#!/usr/bin/env ruby

# Simple script to test manual transfer for order 40
# Run this in Rails console: rails runner test_manual_transfer.rb

puts 'Testing manual transfer for order 40...'

begin
  order = Order.find(40)
  puts "Found order #{order.id} with status: #{order.status}"

  if order.status != Order::RECEIVED
    puts "Order is not in RECEIVED status. Current status: #{order.status}"
    exit 1
  end

  if order.transfers.any?
    puts "Order already has #{order.transfers.count} transfers:"
    order.transfers.each do |transfer|
      puts "  - Transfer #{transfer.id}: #{transfer.recipient.name} (#{transfer.amount} GHS) - #{transfer.status}"
    end
    exit 1
  end

  puts 'Initiating transfers...'
  transfer_service = OrderTransferService.new(order)
  transfers = transfer_service.initiate_transfers

  puts "Successfully created #{transfers.count} transfers:"
  transfers.each do |transfer|
    puts "  - Transfer #{transfer.id}: #{transfer.recipient.name} (#{transfer.amount} GHS) - #{transfer.status}"
    puts "    Paystack Transfer Code: #{transfer.paystack_transfer_code}"
  end
rescue ActiveRecord::RecordNotFound
  puts 'Order 40 not found'
  exit 1
rescue StandardError => e
  puts "Error: #{e.message}"
  puts e.backtrace.join("\n")
  exit 1
end

puts 'Manual transfer test completed successfully!'
