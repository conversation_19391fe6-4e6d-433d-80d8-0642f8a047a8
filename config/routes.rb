Rails.application.routes.draw do
  devise_for :customers, path: '', path_names: {
    sign_in: 'customer/login',
    sign_out: 'customer/logout',
    registration: 'customer/signup',
    password: 'customer/password'
  },
  controllers: {
    sessions: 'customers/sessions',
    registrations: 'customers/registrations',
    passwords: 'customers/passwords'
  }

  devise_for :admins, path: '', path_names: {
    sign_in: 'admin/login',
    sign_out: 'admin/logout',
    registration: 'admin/signup',
    password: 'admin/password'
  },
  controllers: {
    sessions: 'admins/sessions',
    registrations: 'admins/registrations',
    passwords: 'admins/passwords'
  }

  devise_for :vendors, path: '', path_names: {
    sign_in: 'vendor/login',
    sign_out: 'vendor/logout',
    registration: 'vendor/signup',
    password: 'vendor/password'
  },
  controllers: {
    sessions: 'vendors/sessions',
    registrations: 'vendors/registrations',
    passwords: 'vendors/passwords'
  }

  devise_for :riders, path: '', path_names: {
    sign_in: 'rider/login',
    sign_out: 'rider/logout',
    registration: 'rider/signup',
    password: 'rider/password'
  },
  controllers: {
    sessions: 'riders/sessions',
    registrations: 'riders/registrations',
    passwords: 'riders/passwords'
  }

  resources :customers, only: [:index, :show, :update] do
    resources :carts, only: [:create, :index, :update, :destroy] do
      collection do
        delete 'clear', to: 'carts#clear'
      end
    end
    resources :orders, only: [:index, :create, :show , :update] do
      member do
        # Customer confirms delivery and initiates payment
        post 'confirm_delivery', to: 'order_deliveries#confirm'
      end
    end

    # Customer statistics and order history
    member do
      get 'statistics', to: 'customers#statistics'
    end
  end

  # Customer complaints and suggestions
  get '/customers/:customer_id/complaints', to: 'complaints#index', as: 'customer_complaints'
  get '/customers/:customer_id/suggestions', to: 'suggestions#index', as: 'customer_suggestions'

  # Order delivery routes (keeping for backward compatibility)
  resources :orders, only: [] do
    member do
      # Customer confirms delivery and initiates payment
      post 'confirm_delivery', to: 'order_deliveries#confirm'
      # Debug endpoint for transfer issues
      get 'transfer_debug', to: 'orders#transfer_debug'
      # Manual trigger for transfers (for orders missed due to Sidekiq not running)
      post 'manual_transfer', to: 'orders#manual_transfer'
    end
  end

  resources :riders, only: [:index, :show, :update] do
    resources :orders, only: [:index, :update]
    resources :payouts, only: [:index]
    post 'create_paystack_recipient', to: 'paystack_recipients#create_for_rider'
    member do
      get 'statistics', to: 'riders#statistics'
    end
  end

  resources :vendors, only: [:index, :update, :show] do
    resources :orders, only: [:index , :update]
    resources :foods, only: [:index, :show, :create, :update, :destroy]
    resources :payouts, only: [:index]
    post 'create_paystack_recipient', to: 'paystack_recipients#create_for_vendor'
    member do
      get 'statistics', to: 'vendors#statistics'
    end
  end

  resources :admins, only: [:index, :show, :update]

  # Admin routes
  namespace :admin do
    # Dashboard
    get 'dashboard', to: 'dashboard#index'

    # Orders management
    resources :orders, only: [:index, :show]

    # Payouts management
    resources :payouts, only: [:index]

    # Service fees management
    resources :service_fees, only: [:index, :update] do
      collection do
        get 'vendors'
        get 'riders'
      end
    end

    # Vendors management
    resources :vendors, only: [:index, :show] do
      member do
        patch 'approve'
        patch 'reject'
      end
      collection do
        get 'pending'
        get 'statistics'
      end
    end

    # Riders management
    resources :riders, only: [:index, :show] do
      member do
        patch 'approve'
        patch 'reject'
      end
      collection do
        get 'pending'
        get 'statistics'
      end
    end

    # Customers management
    resources :customers, only: [:index, :show] do
      collection do
        get 'statistics'
      end
    end

    # Categories management
    resources :categories

    # Complaints management
    resources :complaints, only: [:index, :show, :update] do
      collection do
        get 'statistics'
      end
    end

    # Suggestions management
    resources :suggestions, only: [:index, :show, :update] do
      collection do
        get 'statistics'
      end
    end
  end

  # Complaints and Suggestions routes
  resources :complaints, only: [:create]
  resources :suggestions, only: [:create]

  post '/paystack/webhook', to: 'paystack_webhooks#create'

  # Push notifications
  resources :push_subscriptions, only: [:create] do
    collection do
      get 'vapid_public_key'
    end
  end
  delete 'push_subscriptions', to: 'push_subscriptions#destroy'

  # mount ActionCable.server => '/cable'


  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Defines the root path route ("/")
  root to: proc { [200, {}, ["OK"]] }
  # root "posts#index"
end
