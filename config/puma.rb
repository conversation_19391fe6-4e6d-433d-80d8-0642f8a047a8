# This configuration file will be evaluated by <PERSON><PERSON>. The top-level methods that
# are invoked here are part of Puma's configuration DSL. For more information
# about methods provided by the DSL, see https://puma.io/puma/Puma/DSL.html.

# Puma can serve each request in a thread from an internal thread pool.
# The `threads` method setting takes two numbers: a minimum and maximum.
# Any libraries that use thread pools should be configured to match
# the maximum value specified for Puma. Default is set to 5 threads for minimum
# and maximum; this matches the default thread size of Active Record.
max_threads_count = ENV.fetch("RAILS_MAX_THREADS") { 5 }
min_threads_count = ENV.fetch("RAILS_MIN_THREADS") { max_threads_count }
threads min_threads_count, max_threads_count

# Specifies that the worker count should equal the number of processors in production.
# Worker processes for production (uncomment for multi-worker setup)
if ENV["RAILS_ENV"] == "production"
  require "concurrent-ruby"
  worker_count = Integer(ENV.fetch("WEB_CONCURRENCY") { [Concurrent.physical_processor_count, 2].min })
  workers worker_count if worker_count > 1
  
  # Worker-specific settings
  preload_app!
  
  # Worker forking callbacks
  on_worker_boot do
    # Worker specific setup for Rails 4.1+
    ActiveRecord::Base.establish_connection if defined?(ActiveRecord)
  end
  
  before_fork do
    # Close database connections before forking
    ActiveRecord::Base.connection_pool.disconnect! if defined?(ActiveRecord)
  end
end

# Binding configuration
if ENV["RAILS_ENV"] == "production"
  # Use Unix socket for production (better performance with Nginx)
  bind "unix:///var/www/EaseFood-Backend/tmp/sockets/puma.sock"
  
  # Optional: Also listen on TCP for direct access (useful for debugging)
  # bind "tcp://127.0.0.1:3000"
else
  # Development binding
  port ENV.fetch("PORT") { 3000 }
  bind "tcp://127.0.0.1:#{ENV.fetch('PORT') { 3000 }}"
end

# Specifies the `port` that Puma will listen on to receive requests; default is 3000.
# port ENV.fetch("PORT") { 3000 }

# Specifies the `environment` that Puma will run in.
environment ENV.fetch("RAILS_ENV") { "development" }

# Specifies the `pidfile` that Puma will use.
pidfile ENV.fetch("PIDFILE") { "tmp/pids/server.pid" }

# Logging
if ENV["RAILS_ENV"] == "production"
  # Production logging
  stdout_redirect 'log/puma.stdout.log', 'log/puma.stderr.log', true
  
  # Log level
  quiet false
else
  # Development - more verbose
  quiet false
end

# Specifies the `worker_timeout` threshold that Puma will use to wait before
# terminating a worker in development environments.
worker_timeout 3600 if ENV.fetch("RAILS_ENV", "development") == "development"

# Production-specific optimizations
if ENV["RAILS_ENV"] == "production"
  # Faster worker boot
  prune_bundler
  
  # Memory and performance tuning
  worker_shutdown_timeout 30
  worker_boot_timeout 30
  
  # Graceful shutdown
  on_restart do
    puts "Puma is restarting..."
  end
end

# Allow puma to be restarted by `bin/rails restart` command.
plugin :tmp_restart
