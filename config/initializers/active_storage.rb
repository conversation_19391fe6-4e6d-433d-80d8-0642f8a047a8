Rails.application.config.active_storage.direct_upload = true

# Set variant processor to mini_magick for better performance
Rails.application.config.active_storage.variant_processor = :mini_magick

# Set queue for Active Storage analysis
Rails.application.config.active_storage.queues.analysis = :active_storage_analysis

# Set queue for Active Storage purge
Rails.application.config.active_storage.queues.purge = :active_storage_purge

# Optimize Active Storage URLs
Rails.application.config.active_storage.resolve_model_to_route = :rails_storage_proxy
