# Configure WebPush with VAPID keys
# These keys should be generated and stored securely in credentials.yml.enc
# To generate VAPID keys, run:
# vapid_key = WebPush.generate_key
# puts "VAPID public key: #{vapid_key.public_key}"
# puts "VAPID private key: #{vapid_key.private_key}"

# Note: Some versions of the web-push gem don't have the configure method
# Instead, we'll set the VAPID details directly in the PushNotificationService
