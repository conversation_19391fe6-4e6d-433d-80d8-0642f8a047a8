class Rack::Attack
  # Throttle all requests by IP (60 requests per minute)
  throttle('req/ip', limit: 300, period: 5.minutes) do |req|
    req.ip
  end

  # Throttle login attempts by IP (5 requests per minute)
  throttle('logins/ip', limit: 5, period: 1.minute) do |req|
    if req.path.include?('/login') && req.post?
      req.ip
    end
  end

  # Throttle API requests by IP (120 requests per minute)
  throttle('api/ip', limit: 120, period: 1.minute) do |req|
    if req.path.start_with?('/admin/')
      req.ip
    end
  end

  # Allow all requests from localhost
  safelist('allow from localhost') do |req|
    # Requests are allowed if the return value is truthy
    '127.0.0.1' == req.ip || '::1' == req.ip
  end

  # Custom response for throttled requests
  self.throttled_responder = lambda do |env|
    retry_after = (env['rack.attack.match_data'] || {})[:period]
    [
      429,
      {'Content-Type' => 'application/json', 'Retry-After' => retry_after.to_s},
      [{ error: "Throttle limit reached. Retry after #{retry_after} seconds." }.to_json]
    ]
  end
end
