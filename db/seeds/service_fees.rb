# Initialize service fees
puts "Creating service fee configurations..."

# Vendor fee percentage (5%)
ServiceFeeConfig.set(
  ServiceFeeConfig::VENDOR_FEE_PERCENTAGE, 
  5.0, 
  'Percentage fee charged to vendors on each food item'
)

# Rider within Dunkwa fee (2 Ghana cedis)
ServiceFeeConfig.set(
  ServiceFeeConfig::RIDER_WITHIN_DUNKWA_FEE, 
  2.0, 
  'Fixed fee in Ghana cedis charged to riders for deliveries within Dunkwa'
)

# Rider outside Dunkwa fee (4 Ghana cedis)
ServiceFeeConfig.set(
  ServiceFeeConfig::RIDER_OUTSIDE_DUNKWA_FEE, 
  4.0, 
  'Fixed fee in Ghana cedis charged to riders for deliveries outside Dunkwa'
)

# Platform fee percentage (1%)
ServiceFeeConfig.set(
  ServiceFeeConfig::PLATFORM_FEE_PERCENTAGE, 
  1.0, 
  'Percentage fee charged to the platform on each order'
)

puts "Service fee configurations created successfully!"
