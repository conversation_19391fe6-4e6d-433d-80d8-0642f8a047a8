# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).
#
# Example:
#
#   ["Action", "Comedy", "Drama", "Horror"].each do |genre_name|
#     MovieGenre.find_or_create_by!(name: genre_name)
#   end
# db/seeds.rb

require 'faker'

# Seed Admins
5.times do
  Admin.create!(
    name: Faker::Name.name,
    phone: Faker::PhoneNumber.cell_phone_in_e164,
    username: Faker::Internet.username,
    role: %w[admin superadmin].sample,
    email: Faker::Internet.email,
    password: 'password',
    password_confirmation: 'password'
  )
end

# Seed Customers
5.times do
  Customer.create!(
    name: Faker::Name.name,
    phone: Faker::PhoneNumber.cell_phone_in_e164,
    address: Faker::Address.full_address,
    username: Faker::Internet.username,
    role: 'customer',
    email: Faker::Internet.email,
    password: 'password',
    password_confirmation: 'password'
  )
end

# Seed Vendors
5.times do
  vendor = Vendor.create!(
    name: Faker::Company.name,
    address: Faker::Address.full_address,
    phone: Faker::PhoneNumber.cell_phone_in_e164,
    operation_time: Faker::Time.backward(days: 14, period: :morning),
    closing_time: Faker::Time.forward(days: 14, period: :evening),
    vendor_description: Faker::Lorem.paragraph,
    username: Faker::Internet.username,
    role: 'vendor',
    email: Faker::Internet.email,
    password: 'password',
    password_confirmation: 'password',
    digital_address: Faker::Address.zip_code,
    ghana_card_number: Faker::Number.number(digits: 10)
  )

  # Attach images to Vendor
  vendor.front_ghana_card.attach(io: File.open('/home/<USER>/Desktop/front_ghana_card.jpg'), filename: 'front_ghana_card.jpg')
  vendor.back_ghana_card.attach(io: File.open('/home/<USER>/Desktop/back_ghana_card.jpg'), filename: 'back_ghana_card.jpg')
  vendor.health_certificate_pic.attach(io: File.open('/home/<USER>/Desktop/health_certificate_pic.jpg'), filename: 'health_certificate_pic.jpg')
  vendor.food_certificate_pic.attach(io: File.open('/home/<USER>/Desktop/food_certificate_pic.jpg'), filename: 'food_certificate_pic.jpg')
  vendor.operation_license_pic.attach(io: File.open('/home/<USER>/Desktop/operation_license_pic.jpg'), filename: 'operation_license_pic.jpg')
end

# Seed Foods
5.times do
  food = Food.create!(
    name: Faker::Food.dish,
    description: Faker::Food.description,
    price: Faker::Commerce.price(range: 5..100),
    vendor: Vendor.order('RANDOM()').first
  )

  # Attach food image
  food.food_image.attach(io: File.open('/home/<USER>/Desktop/food_image.jpg'), filename: 'food_image.jpg')
end

# Seed Riders
5.times do
  rider = Rider.create!(
    name: Faker::Name.name,
    phone: Faker::PhoneNumber.cell_phone_in_e164,
    vehicle_details: "#{Faker::Vehicle.make_and_model}, #{Faker::Vehicle.license_plate}",
    username: Faker::Internet.username,
    role: 'rider',
    email: Faker::Internet.email,
    password: 'password',
    password_confirmation: 'password',
    delivery_price: Faker::Commerce.price(range: 10..50),
    rider_status: %w[available busy offline].sample,
    dob: Faker::Date.birthday(min_age: 18, max_age: 65),
    drivers_license_number: Faker::DrivingLicence.british_driving_licence,
    ghana_card_number: Faker::Number.number(digits: 10),
    vehicle_registration_number: Faker::Vehicle.license_plate
  )

  # Attach images to Rider
  rider.avatar.attach(io: File.open('/home/<USER>/Desktop/avatar.jpg'), filename: 'avatar.jpg')
  rider.front_drivers_license.attach(io: File.open('/home/<USER>/Desktop/front_drivers_license.jpg'), filename: 'front_drivers_license.jpg')
  rider.back_drivers_license.attach(io: File.open('/home/<USER>/Desktop/back_drivers_license.jpg'), filename: 'back_drivers_license.jpg')
  rider.front_ghana_card.attach(io: File.open('/home/<USER>/Desktop/front_ghana_card.jpg'), filename: 'front_ghana_card.jpg')
  rider.back_ghana_card.attach(io: File.open('/home/<USER>/Desktop/back_ghana_card.jpg'), filename: 'back_ghana_card.jpg')
  rider.moto_pic.attach(io: File.open('/home/<USER>/Desktop/moto_pic.jpg'), filename: 'moto_pic.jpg')
  rider.vehicle_registration_pic.attach(io: File.open('/home/<USER>/Desktop/vehicle_registration_pic.jpg'), filename: 'vehicle_registration_pic.jpg')
end

# Seed Orders
5.times do
  Order.create!(
    customer: Customer.order('RANDOM()').first,
    rider: Rider.order('RANDOM()').first,
    order_date: Faker::Time.backward(days: 30),
    status: %w[pending confirmed delivered canceled].sample,
    total_price: Faker::Commerce.price(range: 50..500),
    delivery_address: Faker::Address.full_address,
    food_ids: Food.pluck(:id).sample(3),
    quantities: Array.new(3) { rand(1..5) }
  )
end

# Seed Carts
5.times do
  Cart.create!(
    food: Food.order('RANDOM()').first,
    quantity: rand(1..5),
    customer: Customer.order('RANDOM()').first
  )
end

# Create categories
categories = ['Desserts', 'Alcohols', 'Snack', 'Rice']
categories.each do |category_name|
  Category.find_or_create_by(name: category_name)
end

# Seed Complaints
5.times do
  Complaint.create!(
    customer: Customer.order('RANDOM()').first,
    name: Faker::Name.name,
    email: Faker::Internet.email,
    order_number: "EF-#{Faker::Number.number(digits: 8)}",
    category: %w[order delivery food app payment other].sample,
    complaint: Faker::Lorem.paragraph(sentence_count: 3),
    status: %w[pending under_review resolved rejected].sample
  )
end

# Seed Suggestions
5.times do
  Suggestion.create!(
    customer: Customer.order('RANDOM()').first,
    name: Faker::Name.name,
    email: Faker::Internet.email,
    category: %w[menu delivery app pricing timing other].sample,
    suggestion: Faker::Lorem.paragraph(sentence_count: 3),
    rating: rand(1..5),
    status: %w[pending under_review implemented rejected].sample
  )
end

# Load service fees
require_relative 'seeds/service_fees'
