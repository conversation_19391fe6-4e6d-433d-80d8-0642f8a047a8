# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.1].define(version: 2025_08_07_215441) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "admins", force: :cascade do |t|
    t.string "name"
    t.string "phone"
    t.string "username"
    t.string "role"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.index ["email"], name: "index_admins_on_email", unique: true
    t.index ["reset_password_token"], name: "index_admins_on_reset_password_token", unique: true
  end

  create_table "carts", force: :cascade do |t|
    t.bigint "food_id", null: false
    t.integer "quantity"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "customer_id", null: false
    t.integer "selected_price_index", default: 0
    t.index ["customer_id"], name: "index_carts_on_customer_id"
    t.index ["food_id"], name: "index_carts_on_food_id"
    t.index ["selected_price_index"], name: "index_carts_on_selected_price_index"
  end

  create_table "categories", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_categories_on_name"
  end

  create_table "complaints", force: :cascade do |t|
    t.bigint "customer_id"
    t.string "name", null: false
    t.string "email", null: false
    t.string "order_number", null: false
    t.string "category", null: false
    t.text "complaint", null: false
    t.string "status", default: "pending", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["customer_id"], name: "index_complaints_on_customer_id"
  end

  create_table "customers", force: :cascade do |t|
    t.string "name"
    t.string "phone"
    t.string "address"
    t.string "username"
    t.string "role"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.index ["created_at"], name: "index_customers_on_created_at"
    t.index ["email"], name: "index_customers_on_email", unique: true
    t.index ["reset_password_token"], name: "index_customers_on_reset_password_token", unique: true
  end

  create_table "foods", force: :cascade do |t|
    t.string "name"
    t.string "description"
    t.bigint "vendor_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "prices"
    t.index ["created_at"], name: "index_foods_on_created_at"
    t.index ["name"], name: "index_foods_on_name"
    t.index ["vendor_id"], name: "index_foods_on_vendor_id"
  end

  create_table "jwt_blacklists", force: :cascade do |t|
    t.string "jti"
    t.datetime "exp"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["jti"], name: "index_jwt_blacklists_on_jti"
  end

  create_table "order_groups", force: :cascade do |t|
    t.bigint "customer_id", null: false
    t.string "payment_reference"
    t.decimal "total_amount"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["customer_id"], name: "index_order_groups_on_customer_id"
  end

  create_table "orders", force: :cascade do |t|
    t.bigint "customer_id", null: false
    t.bigint "rider_id", null: false
    t.string "status"
    t.float "total_price"
    t.text "delivery_address"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "food_ids", default: [], array: true
    t.integer "quantities", default: [], array: true
    t.float "delivery_price"
    t.float "donation"
    t.bigint "order_group_id"
    t.integer "price_indexes", default: [], array: true
    t.index ["created_at"], name: "index_orders_on_created_at"
    t.index ["customer_id"], name: "index_orders_on_customer_id"
    t.index ["food_ids"], name: "index_orders_on_food_ids", using: :gin
    t.index ["order_group_id"], name: "index_orders_on_order_group_id"
    t.index ["price_indexes"], name: "index_orders_on_price_indexes", using: :gin
    t.index ["rider_id"], name: "index_orders_on_rider_id"
    t.index ["status"], name: "index_orders_on_status"
  end

  create_table "payments", force: :cascade do |t|
    t.bigint "order_id", null: false
    t.string "transaction_id"
    t.string "status"
    t.decimal "amount"
    t.string "reference"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["order_id"], name: "index_payments_on_order_id"
    t.index ["reference"], name: "index_payments_on_reference", unique: true
  end

  create_table "push_subscriptions", force: :cascade do |t|
    t.string "endpoint", null: false
    t.string "p256dh", null: false
    t.string "auth", null: false
    t.string "user_type", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["endpoint"], name: "index_push_subscriptions_on_endpoint", unique: true
    t.index ["user_type", "user_id"], name: "index_push_subscriptions_on_user"
  end

  create_table "recipients", force: :cascade do |t|
    t.string "recipient_name"
    t.string "recipient_phone"
    t.string "recipient_address"
    t.bigint "order_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["order_id"], name: "index_recipients_on_order_id"
  end

  create_table "riders", force: :cascade do |t|
    t.string "name"
    t.string "phone"
    t.string "vehicle_details"
    t.string "username"
    t.string "role"
    t.float "within_dunkwa_price"
    t.string "rider_status"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.date "dob"
    t.string "drivers_license_number"
    t.string "ghana_card_number"
    t.string "vehicle_registration_number"
    t.string "validation_status", default: "not-verified", null: false
    t.float "outside_dunkwa_price"
    t.string "address"
    t.string "account_name"
    t.string "mobile_money_provider"
    t.string "paystack_recipient_code"
    t.string "mobile_money_number"
    t.index ["created_at"], name: "index_riders_on_created_at"
    t.index ["email"], name: "index_riders_on_email", unique: true
    t.index ["reset_password_token"], name: "index_riders_on_reset_password_token", unique: true
    t.index ["rider_status"], name: "index_riders_on_rider_status"
    t.index ["validation_status"], name: "index_riders_on_validation_status"
  end

  create_table "service_fee_configs", force: :cascade do |t|
    t.string "key", null: false
    t.decimal "value", precision: 10, scale: 2, null: false
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["key"], name: "index_service_fee_configs_on_key", unique: true
  end

  create_table "suggestions", force: :cascade do |t|
    t.bigint "customer_id"
    t.string "name", null: false
    t.string "email", null: false
    t.string "category", null: false
    t.text "suggestion", null: false
    t.integer "rating", null: false
    t.string "status", default: "pending", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["customer_id"], name: "index_suggestions_on_customer_id"
  end

  create_table "transfers", force: :cascade do |t|
    t.string "recipient_type"
    t.integer "recipient_id"
    t.bigint "order_id", null: false
    t.decimal "amount", precision: 10, scale: 2, null: false
    t.decimal "service_fee", precision: 10, scale: 2, null: false
    t.string "status", default: "pending"
    t.string "paystack_transfer_code"
    t.string "paystack_reference"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_at"], name: "index_transfers_on_created_at"
    t.index ["order_id", "recipient_type", "recipient_id"], name: "unique_transfer_per_order_recipient", unique: true
    t.index ["order_id"], name: "index_transfers_on_order_id"
    t.index ["paystack_reference"], name: "index_transfers_on_paystack_reference", unique: true
    t.index ["paystack_transfer_code"], name: "index_transfers_on_paystack_transfer_code", unique: true
    t.index ["recipient_type", "recipient_id"], name: "index_transfers_on_recipient_type_and_recipient_id"
    t.index ["status"], name: "index_transfers_on_status"
  end

  create_table "vendor_categories", force: :cascade do |t|
    t.bigint "vendor_id", null: false
    t.bigint "category_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["category_id"], name: "index_vendor_categories_on_category_id"
    t.index ["vendor_id", "category_id"], name: "index_vendor_categories_on_vendor_id_and_category_id", unique: true
    t.index ["vendor_id"], name: "index_vendor_categories_on_vendor_id"
  end

  create_table "vendors", force: :cascade do |t|
    t.string "name"
    t.string "address"
    t.string "phone"
    t.text "vendor_description"
    t.string "username"
    t.string "role"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.string "digital_address"
    t.string "ghana_card_number"
    t.string "validation_status", default: "not-verified", null: false
    t.string "account_name"
    t.string "mobile_money_provider"
    t.string "paystack_recipient_code"
    t.string "mobile_money_number"
    t.time "operation_time"
    t.time "closing_time"
    t.text "category_names", default: [], null: false, array: true
    t.index ["created_at"], name: "index_vendors_on_created_at"
    t.index ["email"], name: "index_vendors_on_email", unique: true
    t.index ["reset_password_token"], name: "index_vendors_on_reset_password_token", unique: true
    t.index ["validation_status"], name: "index_vendors_on_validation_status"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "carts", "foods"
  add_foreign_key "complaints", "customers"
  add_foreign_key "foods", "vendors"
  add_foreign_key "order_groups", "customers"
  add_foreign_key "orders", "customers"
  add_foreign_key "orders", "order_groups"
  add_foreign_key "orders", "riders"
  add_foreign_key "payments", "orders"
  add_foreign_key "recipients", "orders"
  add_foreign_key "suggestions", "customers"
  add_foreign_key "transfers", "orders"
  add_foreign_key "vendor_categories", "categories"
  add_foreign_key "vendor_categories", "vendors"
end
