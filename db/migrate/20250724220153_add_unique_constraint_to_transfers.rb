class AddUniqueConstraintToTransfers < ActiveRecord::Migration[7.1]
  def up
    # Add unique index to prevent duplicate transfers for the same order and recipient
    add_index :transfers, [:order_id, :recipient_type, :recipient_id],
              unique: true,
              name: 'unique_transfer_per_order_recipient'
  end

  def down
    remove_index :transfers, name: 'unique_transfer_per_order_recipient'
  end
end
