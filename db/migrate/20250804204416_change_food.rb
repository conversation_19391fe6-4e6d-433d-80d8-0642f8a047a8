class ChangeFood < ActiveRecord::Migration[7.1]
  def up
    # Add new prices column as text to store JSON array
    add_column :foods, :prices, :text

    # Migrate existing price data to prices array
    Food.find_each do |food|
      if food.price.present?
        # Convert single price to array format
        food.update_column(:prices, [food.price].to_json)
      end
    end

    # Remove the old price column
    remove_column :foods, :price
  end

  def down
    # Add back the price column
    add_column :foods, :price, :float

    # Migrate array prices back to single price (take first price)
    Food.find_each do |food|
      if food.prices.present?
        prices_array = JSON.parse(food.prices)
        food.update_column(:price, prices_array.first) if prices_array.any?
      end
    end

    # Remove the prices column
    remove_column :foods, :prices
  end
end
