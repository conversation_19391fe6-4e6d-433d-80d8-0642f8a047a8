#ifndef EXTCONF_H
#define EXTCONF_H
#define OPENSSL_SUPPRESS_DEPRECATED 1
#define HAVE_RB_IO_DESCRIPTOR 1
#define HAVE_RB_IO_MAYBE_WAIT 1
#define HAVE_RB_IO_TIMEOUT 1
#define HAVE_OPENSSL_SSL_H 1
#define HAVE_ENGINE_LOAD_DYNAMIC 1
#define HAVE_ENGINE_LOAD_CRYPTODEV 1
#define HAVE_I2D_RE_X509_TBS 1
#define HAVE_OPAQUE_OPENSSL 1
#define HAVE_EVP_MD_CTX_NEW 1
#define HAVE_EVP_MD_CTX_FREE 1
#define HAVE_EVP_MD_CTX_PKEY_CTX 1
#define HAVE_X509_STORE_GET_EX_DATA 1
#define HAVE_X509_STORE_SET_EX_DATA 1
#define HAVE_X509_STORE_GET_EX_NEW_INDEX 1
#define HAVE_X509_CRL_GET0_SIGNATURE 1
#define HAVE_X509_REQ_GET0_SIGNATURE 1
#define HAVE_X509_REVOKED_GET0_SERIALNUMBER 1
#define HAVE_X509_REVOKED_GET0_REVOCATIONDATE 1
#define HAVE_X509_GET0_TBS_SIGALG 1
#define HAVE_X509_STORE_CTX_GET0_UNTRUSTED 1
#define HAVE_X509_STORE_CTX_GET0_CERT 1
#define HAVE_X509_STORE_CTX_GET0_CHAIN 1
#define HAVE_OCSP_SINGLERESP_GET0_ID 1
#define HAVE_SSL_CTX_GET_CIPHERS 1
#define HAVE_X509_UP_REF 1
#define HAVE_X509_CRL_UP_REF 1
#define HAVE_X509_STORE_UP_REF 1
#define HAVE_SSL_SESSION_UP_REF 1
#define HAVE_EVP_PKEY_UP_REF 1
#define HAVE_SSL_CTX_SET_MIN_PROTO_VERSION 1
#define HAVE_SSL_CTX_GET_SECURITY_LEVEL 1
#define HAVE_X509_GET0_NOTBEFORE 1
#define HAVE_SSL_SESSION_GET_PROTOCOL_VERSION 1
#define HAVE_TS_STATUS_INFO_GET0_STATUS 1
#define HAVE_TS_STATUS_INFO_GET0_TEXT 1
#define HAVE_TS_STATUS_INFO_GET0_FAILURE_INFO 1
#define HAVE_TS_VERIFY_CTS_SET_CERTS 1
#define HAVE_TS_VERIFY_CTX_SET_STORE 1
#define HAVE_TS_VERIFY_CTX_ADD_FLAGS 1
#define HAVE_TS_RESP_CTX_SET_TIME_CB 1
#define HAVE_EVP_PBE_SCRYPT 1
#define HAVE_SSL_CTX_SET_POST_HANDSHAKE_AUTH 1
#define HAVE_X509_STORE_GET0_PARAM 1
#define HAVE_EVP_PKEY_CHECK 1
#define HAVE_EVP_PKEY_NEW_RAW_PRIVATE_KEY 1
#define HAVE_SSL_CTX_SET_CIPHERSUITES 1
#define HAVE_SSL_SET0_TMP_DH_PKEY 1
#define HAVE_ERR_GET_ERROR_ALL 1
#define HAVE_TS_VERIFY_CTX_SET_CERTS 1
#define HAVE_SSL_CTX_LOAD_VERIFY_FILE 1
#define HAVE_BN_CHECK_PRIME 1
#define HAVE_EVP_MD_CTX_GET0_MD 1
#define HAVE_EVP_MD_CTX_GET_PKEY_CTX 1
#define HAVE_EVP_PKEY_EQ 1
#define HAVE_EVP_PKEY_DUP 1
#endif
