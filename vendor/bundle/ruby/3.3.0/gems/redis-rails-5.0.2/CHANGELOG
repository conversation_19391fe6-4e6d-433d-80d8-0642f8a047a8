# Changelog

## 5.0.1

Breaking changes

- None

Added

- None

Fixed

- Use non-prerelease versions of dependencies

## 5.0.0

Breaking changes

- None

Added

- Begin testing against ruby 2.1.2, 2.2, and 2.3
- Bump dependencies to support Rails 5

Fixed

- Various documentation improvements

## 4.0.0

Not documented yet. Contributions welcome.

## 3.2.4

Not documented yet. Contributions welcome.

## 3.2.3

Not documented yet. Contributions welcome.

## 3.2.2

Not documented yet. Contributions welcome.

## 3.2.1

Not documented yet. Contributions welcome.

## 3.1.5

Not documented yet. Contributions welcome.

## 3.1.4

Not documented yet. Contributions welcome.

## 3.1.3

Not documented yet. Contributions welcome.
