=== 1.13.0 (2024-06-13)

* Define Erubi.h as a module function (jeremyevans)

* Add erubi/capture_block, supporting capturing block output via standard <%= and <%== tags (jeremyevans)

=== 1.12.0 (2022-12-22)

* Use erb/escape for faster html escaping if available (jeremyevans)

* Default :freeze_template_literals option to false if running with --enable-frozen-string-literal (casperisfine) (#35)

=== 1.11.0 (2022-08-02)

* Support :freeze_template_literals option for configuring whether to add .freeze to template literal strings (casperisfine) (#33)

* Support :chain_appends option for chaining appends to the buffer variable (casperisfine, jeremyevans) (#32)

* Avoid unnecessary defined? usage on Ruby 3+ when using the :ensure option (jeremyevans)

=== 1.10.0 (2020-11-13)

* Improve template parsing, mostly by reducing allocations (jeremyevans)

* Do not ship tests in the gem, reducing gem size about 20% (jeremyevans)

* Support :literal_prefix and :literal_postfix options for how to output literal tags (e.g. <%% code %>) (jaredcwhite) (#26, #27)

=== 1.9.0 (2019-09-25)

* Change default :bufvar from 'String.new' to '::String.new' to work with BasicObject (jeremyevans)

=== 1.8.0 (2018-12-18)

* Support :yield_returns_buffer option in capture_end for always returning the (potentially modified) buffer in <%|= tags (evanleck) (#15)

=== 1.7.1 (2018-03-05)

* Make whitespace handling for <%# %> tags more compatible with Erubis (jeremyevans) (#14)

=== 1.7.0 (2017-10-09)

* Fix escaping in erubi/capture_end, the setting was previously inverted (jeremyevans) (#10)

=== 1.6.1 (2017-06-27)

* Fix usage on newer versions of JRuby 9.1 (jeremyevans)

=== 1.6.0 (2017-02-27)

* Use cgi/escape if available for 6x faster HTML escaping (k0kubun, jeremyevans) (#4)

=== 1.5.0 (2017-01-26)

* Drop tilt/erubi file, as tilt now ships with Erubi support (jeremyevans)

* Drop erubi/capture file, Erubi::CaptureEngine support (jeremyevans)

=== 1.4.0 (2017-01-20)

* Allow postambles to depend on internal state of engine (jeremyevans)

* Allow overriding of behavior for <%= and <%== tags to depend on which indicator was used (jeremyevans)

* Make whitespace handling for <% %> tags more compatible with Erubis for subclasses overriding add_text (jeremyevans)

=== 1.3.0 (2016-12-29)

* Support :capture=>:explicit option in tilt support to use Erubi::CaptureEndEngine (jeremyevans)

* Add erubi/capture_end containing Erubi::CaptureEndEngine, allowing <%|= and <%|== for opening capture tags, and <%| for closing capture tags (jeremyevans)

=== 1.2.1 (2016-11-21)

* Don't automatically freeze template text strings on ruby 1.9 or 2.0 (jeremyevans)

=== 1.2.0 (2016-11-21)

* Engine#src now returns a frozen string (jeremyevans)

* Automatically freeze template text strings on ruby 2.1+, reducing garbage generated (jeremyevans)

* Allow overriding of behavior for <%= and <%== tags (ujifgc) (#1)

=== 1.1.0 (2016-11-14)

* Add :ensure option to supporting restoring bufvar to original value (jeremyevans)

* Don't have tilt support require erb (jeremyevans)

* Support :engine_class option in tilt support to override engine class used (jeremyevans)

* Support :capture option in tilt support to use Erubi::CaptureEngine (jeremyevans)

* Add erubi/capture file containing Erubi::CaptureEngine, allowing <%|= and <%|== for capture (and escaping) blocks in templates (jeremyevans)

* Raise ArgumentError if template source code contains indicators matched by regexp but not handled (jeremyevans)

* Add :bufval option to support arbitrary buffer values (jeremyevans)

* Add :regexp option to specify regexp used for scanning (jeremyevans)

* Add :src option to specify initial template source (jeremyevans)

=== 1.0.0 (2016-11-10)

* Initial Public Release
