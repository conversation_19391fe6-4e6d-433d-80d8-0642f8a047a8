# frozen_string_literal: true

# This file is auto-generated. Instead of editing this file, please
# add MIMEs to data/custom.xml or lib/marcel/mime_type/definitions.rb.

module Marcel
  # @private
  # :nodoc:
  EXTENSIONS = {
    '123' => 'application/vnd.lotus-1-2-3',
    '3dml' => 'text/vnd.in3d.3dml',
    '3fr' => 'image/x-raw-hasselblad',
    '3g2' => 'video/3gpp2',
    '3gp' => 'video/3gpp',
    '4th' => 'text/x-forth',
    '7z' => 'application/x-7z-compressed',
    'a' => 'application/x-archive',
    'aab' => 'application/x-authorware-bin',
    'aac' => 'audio/x-aac',
    'aam' => 'application/x-authorware-map',
    'aart' => 'text/plain',
    'aas' => 'application/x-authorware-seg',
    'abw' => 'application/x-abiword',
    'ac' => 'text/plain',
    'ac3' => 'audio/ac3',
    'acc' => 'application/vnd.americandynamics.acc',
    'ace' => 'application/x-ace-compressed',
    'acfm' => 'application/x-font-adobe-metric',
    'acu' => 'application/vnd.acucobol',
    'acutc' => 'application/vnd.acucorp',
    'ad' => 'text/x-asciidoc',
    'ad.txt' => 'text/x-asciidoc',
    'ada' => 'text/x-ada',
    'adb' => 'text/x-ada',
    'adoc' => 'text/x-asciidoc',
    'adoc.txt' => 'text/x-asciidoc',
    'adp' => 'audio/adpcm',
    'ads' => 'text/x-ada',
    'aep' => 'application/vnd.adobe.aftereffects.project',
    'aet' => 'application/vnd.adobe.aftereffects.template',
    'afm' => 'application/x-font-adobe-metric',
    'afp' => 'application/vnd.ibm.modcap',
    'ai' => 'application/illustrator',
    'aif' => 'audio/x-aiff',
    'aifc' => 'audio/x-aiff',
    'aiff' => 'audio/x-aiff',
    'air' => 'application/vnd.adobe.air-application-installer-package+zip',
    'aj' => 'text/x-aspectj',
    'al' => 'text/x-perl',
    'am' => 'text/plain',
    'amfm' => 'application/x-font-adobe-metric',
    'ami' => 'application/vnd.amiga.ami',
    'amr' => 'audio/amr',
    'anpa' => 'text/vnd.iptc.anpa',
    'apk' => 'application/vnd.android.package-archive',
    'applescript' => 'text/x-applescript',
    'application' => 'application/x-ms-application',
    'apr' => 'application/vnd.lotus-approach',
    'apt' => 'text/plain',
    'ar' => 'application/x-archive',
    'arc' => 'application/x-internet-archive',
    'arj' => 'application/x-arj',
    'arw' => 'image/x-raw-sony',
    'as' => 'text/x-actionscript',
    'asc' => 'application/pgp-signature',
    'asciidoc' => 'text/x-asciidoc',
    'asf' => 'video/x-ms-asf',
    'asice' => 'application/vnd.etsi.asic-e+zip',
    'asics' => 'application/vnd.etsi.asic-s+zip',
    'asm' => 'text/x-assembly',
    'asnd' => 'audio/vnd.adobe.soundbooth',
    'aso' => 'application/vnd.accpac.simply.aso',
    'asp' => 'text/asp',
    'aspx' => 'text/aspdotnet',
    'asx' => 'application/x-ms-asx',
    'atc' => 'application/vnd.acucorp',
    'atom' => 'application/atom+xml',
    'atomcat' => 'application/atomcat+xml',
    'atomsvc' => 'application/atomsvc+xml',
    'atx' => 'application/vnd.antix.game-component',
    'au' => 'audio/basic',
    'avi' => 'video/x-msvideo',
    'avif' => 'image/avif',
    'aw' => 'application/applixware',
    'awk' => 'text/x-awk',
    'axx' => 'application/x-axcrypt',
    'azf' => 'application/vnd.airzip.filesecure.azf',
    'azs' => 'application/vnd.airzip.filesecure.azs',
    'azw' => 'application/vnd.amazon.ebook',
    'bas' => 'text/x-basic',
    'bash' => 'application/x-sh',
    'bat' => 'application/x-bat',
    'bau' => 'application/vnd.openofficeorg.autotext',
    'bay' => 'image/x-raw-casio',
    'bcpio' => 'application/x-bcpio',
    'bdf' => 'application/x-font-bdf',
    'bdm' => 'application/vnd.syncml.dm+wbxml',
    'bh2' => 'application/vnd.fujitsu.oasysprs',
    'bib' => 'application/x-bibtex-text-file',
    'bibtex' => 'application/x-bibtex-text-file',
    'bin' => 'application/octet-stream',
    'bmi' => 'application/vnd.bmi',
    'bmp' => 'image/bmp',
    'book' => 'application/vnd.framemaker',
    'box' => 'application/vnd.previewsystems.box',
    'boz' => 'application/x-bzip2',
    'bpg' => 'image/x-bpg',
    'bpk' => 'application/octet-stream',
    'bpm' => 'application/bizagi-modeler',
    'br' => 'application/x-brotli',
    'brotli' => 'application/x-brotli',
    'bsh' => 'text/plain',
    'btif' => 'image/prs.btif',
    'bz' => 'application/x-bzip',
    'bz2' => 'application/x-bzip2',
    'c' => 'text/x-c++src',
    'c++' => 'text/x-c++src',
    'c4d' => 'application/vnd.clonk.c4group',
    'c4f' => 'application/vnd.clonk.c4group',
    'c4g' => 'application/vnd.clonk.c4group',
    'c4p' => 'application/vnd.clonk.c4group',
    'c4u' => 'application/vnd.clonk.c4group',
    'cab' => 'application/vnd.ms-cab-compressed',
    'caf' => 'audio/x-caf',
    'cap' => 'application/vnd.tcpdump.pcap',
    'car' => 'application/vnd.curl.car',
    'cat' => 'application/vnd.ms-pki.seccat',
    'cbl' => 'text/x-cobol',
    'cbor' => 'application/cbor',
    'cc' => 'text/x-c++src',
    'cct' => 'application/x-director',
    'ccxml' => 'application/ccxml+xml',
    'cdbcmsg' => 'application/vnd.contact.cmsg',
    'cdf' => 'application/x-netcdf',
    'cdkey' => 'application/vnd.mediastation.cdkey',
    'cdr' => 'application/coreldraw',
    'cdx' => 'chemical/x-cdx',
    'cdxml' => 'application/vnd.chemdraw+xml',
    'cdy' => 'application/vnd.cinderella',
    'cer' => 'application/pkix-cert',
    'cfc' => 'text/x-coldfusion',
    'cfg' => 'text/x-config',
    'cfm' => 'text/x-coldfusion',
    'cfml' => 'text/x-coldfusion',
    'cgi' => 'text/x-cgi',
    'cgm' => 'image/cgm',
    'chat' => 'application/x-chat',
    'chm' => 'application/vnd.ms-htmlhelp',
    'chrt' => 'application/vnd.kde.kchart',
    'cif' => 'chemical/x-cif',
    'cii' => 'application/vnd.anser-web-certificate-issue-initiation',
    'cil' => 'application/vnd.ms-artgalry',
    'cl' => 'text/x-common-lisp',
    'cla' => 'application/vnd.claymore',
    'class' => 'application/java-vm',
    'classpath' => 'text/plain',
    'clj' => 'text/x-clojure',
    'clkk' => 'application/vnd.crick.clicker.keyboard',
    'clkp' => 'application/vnd.crick.clicker.palette',
    'clkt' => 'application/vnd.crick.clicker.template',
    'clkw' => 'application/vnd.crick.clicker.wordbank',
    'clkx' => 'application/vnd.crick.clicker',
    'clp' => 'application/x-msclip',
    'cls' => 'text/x-vbasic',
    'cmc' => 'application/vnd.cosmocaller',
    'cmd' => 'application/x-bat',
    'cmdf' => 'chemical/x-cmdf',
    'cml' => 'chemical/x-cml',
    'cmp' => 'application/vnd.yellowriver-custom-menu',
    'cmx' => 'image/x-cmx',
    'cnd' => 'text/plain',
    'cob' => 'text/x-cobol',
    'cod' => 'application/vnd.rim.cod',
    'coffee' => 'text/x-coffeescript',
    'com' => 'application/x-msdownload',
    'conf' => 'text/x-config',
    'config' => 'text/x-config',
    'cpio' => 'application/x-cpio',
    'cpp' => 'text/x-c++src',
    'cpt' => 'application/mac-compactpro',
    'cr2' => 'image/x-raw-canon',
    'crd' => 'application/x-mscardfile',
    'crl' => 'application/pkix-crl',
    'crt' => 'application/x-x509-cert',
    'crw' => 'image/x-raw-canon',
    'crx' => 'application/x-chrome-package',
    'cs' => 'text/x-csharp',
    'csh' => 'application/x-csh',
    'csml' => 'chemical/x-csml',
    'csp' => 'application/vnd.commonspace',
    'css' => 'text/css',
    'cst' => 'application/x-director',
    'csv' => 'text/csv',
    'cu' => 'application/cu-seeme',
    'curl' => 'text/vnd.curl',
    'cwiki' => 'text/plain',
    'cwk' => 'application/x-appleworks',
    'cww' => 'application/prs.cww',
    'cxt' => 'application/x-director',
    'cxx' => 'text/x-c++src',
    'd' => 'text/x-d',
    'daf' => 'application/vnd.mobius.daf',
    'data' => 'text/plain',
    'dataless' => 'application/vnd.fdsn.seed',
    'davmount' => 'application/davmount+xml',
    'dbase' => 'application/x-dbf',
    'dbase3' => 'application/x-dbf',
    'dbf' => 'application/x-dbf',
    'dcl' => 'text/plain',
    'dcr' => 'application/x-director',
    'dcs' => 'image/x-raw-kodak',
    'dcurl' => 'text/vnd.curl.dcurl',
    'dcx' => 'image/vnd.zbrush.dcx',
    'dd2' => 'application/vnd.oma.dd2+xml',
    'ddd' => 'application/vnd.fujixerox.ddd',
    'deb' => 'application/x-debian-package',
    'def' => 'text/plain',
    'deploy' => 'application/octet-stream',
    'der' => 'application/x-x509-cert;format=der',
    'dex' => 'application/x-dex',
    'dfac' => 'application/vnd.dreamfactory',
    'dib' => 'image/bmp',
    'dif' => 'application/dif+xml',
    'diff' => 'text/x-diff',
    'dir' => 'application/x-director',
    'dis' => 'application/vnd.mobius.dis',
    'dist' => 'application/octet-stream',
    'distz' => 'application/octet-stream',
    'dita' => 'application/dita+xml;format=topic',
    'ditamap' => 'application/dita+xml;format=map',
    'ditaval' => 'application/dita+xml;format=val',
    'djv' => 'image/vnd.djvu',
    'djvu' => 'image/vnd.djvu',
    'dll' => 'application/x-msdownload',
    'dmg' => 'application/x-apple-diskimage',
    'dmp' => 'application/vnd.tcpdump.pcap',
    'dms' => 'application/octet-stream',
    'dna' => 'application/vnd.dna',
    'dng' => 'image/x-raw-adobe',
    'do' => 'application/x-stata-do',
    'doc' => 'application/msword',
    'docm' => 'application/vnd.ms-word.document.macroenabled.12',
    'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'dot' => 'application/msword',
    'dotm' => 'application/vnd.ms-word.template.macroenabled.12',
    'dotx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.template',
    'dp' => 'application/vnd.osgi.dp',
    'dpg' => 'application/vnd.dpgraph',
    'dpr' => 'text/x-pascal',
    'dpx' => 'image/x-dpx',
    'drc' => 'video/x-dirac',
    'drf' => 'image/x-raw-kodak',
    'dsc' => 'text/prs.lines.tag',
    'dsp' => 'text/plain',
    'dsw' => 'text/plain',
    'dta' => 'application/x-stata-dta',
    'dtb' => 'application/x-dtbook+xml',
    'dtd' => 'application/xml-dtd',
    'dts' => 'audio/vnd.dts',
    'dtshd' => 'audio/vnd.dts.hd',
    'dump' => 'application/octet-stream',
    'dvi' => 'application/x-dvi',
    'dwf' => 'model/vnd.dwf',
    'dwfx' => 'model/vnd.dwfx+xps',
    'dwg' => 'image/vnd.dwg',
    'dxb' => 'image/vnd.dxb',
    'dxf' => 'image/vnd.dxf',
    'dxp' => 'application/vnd.spotfire.dxp',
    'dxr' => 'application/x-director',
    'e' => 'text/x-eiffel',
    'ear' => 'application/x-tika-java-enterprise-archive',
    'ecelp4800' => 'audio/vnd.nuera.ecelp4800',
    'ecelp7470' => 'audio/vnd.nuera.ecelp7470',
    'ecelp9600' => 'audio/vnd.nuera.ecelp9600',
    'ecma' => 'application/ecmascript',
    'edm' => 'application/vnd.novadigm.edm',
    'edx' => 'application/vnd.novadigm.edx',
    'efif' => 'application/vnd.picsel',
    'egrm' => 'text/plain',
    'ei6' => 'application/vnd.pg.osasli',
    'el' => 'text/x-emacs-lisp',
    'elc' => 'application/octet-stream',
    'emf' => 'image/emf',
    'eml' => 'message/rfc822',
    'emlx' => 'message/x-emlx',
    'emma' => 'application/emma+xml',
    'emz' => 'image/x-emf-compressed',
    'enr' => 'application/x-endnote-refer',
    'ent' => 'text/plain',
    'enw' => 'application/x-endnote-refer',
    'eol' => 'audio/vnd.digital-winds',
    'eot' => 'application/vnd.ms-fontobject',
    'eps' => 'application/postscript',
    'epsf' => 'application/postscript',
    'epsi' => 'application/postscript',
    'epub' => 'application/epub+zip',
    'erf' => 'image/x-raw-epson',
    'erl' => 'text/x-erlang',
    'es3' => 'application/vnd.eszigno3+xml',
    'esf' => 'application/vnd.epson.esf',
    'et3' => 'application/vnd.eszigno3+xml',
    'etx' => 'text/x-setext',
    'exe' => 'application/x-dosexec',
    'exp' => 'text/x-expect',
    'exr' => 'image/aces',
    'ext' => 'application/vnd.novadigm.ext',
    'ez' => 'application/andrew-inset',
    'ez2' => 'application/vnd.ezpix-album',
    'ez3' => 'application/vnd.ezpix-package',
    'f' => 'text/x-fortran',
    'f4v' => 'video/x-f4v',
    'f77' => 'text/x-fortran',
    'f90' => 'text/x-fortran',
    'fb2' => 'application/x-fictionbook+xml',
    'fbs' => 'image/vnd.fastbidsheet',
    'fdf' => 'application/vnd.fdf',
    'fe_launch' => 'application/vnd.denovo.fcselayout-link',
    'fff' => 'image/x-raw-imacon',
    'fg5' => 'application/vnd.fujitsu.oasysgp',
    'fgd' => 'application/x-director',
    'fh' => 'image/x-freehand',
    'fh10' => 'image/x-freehand',
    'fh11' => 'image/x-freehand',
    'fh12' => 'image/x-freehand',
    'fh4' => 'image/x-freehand',
    'fh40' => 'image/x-freehand',
    'fh5' => 'image/x-freehand',
    'fh50' => 'image/x-freehand',
    'fh7' => 'image/x-freehand',
    'fh8' => 'image/x-freehand',
    'fh9' => 'image/x-freehand',
    'fhc' => 'image/x-freehand',
    'fig' => 'application/x-xfig',
    'fit' => 'application/fits',
    'fits' => 'application/fits',
    'flac' => 'audio/x-flac',
    'flc' => 'video/x-flc',
    'fli' => 'video/x-fli',
    'flo' => 'application/vnd.micrografx.flo',
    'flv' => 'video/x-flv',
    'flw' => 'application/vnd.kde.kivio',
    'flx' => 'text/vnd.fmi.flexstor',
    'fly' => 'text/vnd.fly',
    'fm' => 'application/vnd.framemaker',
    'fn' => 'text/plain',
    'fnc' => 'application/vnd.frogans.fnc',
    'fo' => 'application/xslfo+xml',
    'fodp' => 'application/vnd.oasis.opendocument.flat.presentation',
    'fods' => 'application/vnd.oasis.opendocument.flat.spreadsheet',
    'fodt' => 'application/vnd.oasis.opendocument.flat.text',
    'for' => 'text/x-fortran',
    'fp7' => 'application/x-filemaker',
    'fpx' => 'image/vnd.fpx',
    'frame' => 'application/vnd.framemaker',
    'frm' => 'text/x-vbasic',
    'fsc' => 'application/vnd.fsc.weblaunch',
    'fst' => 'image/vnd.fst',
    'ft' => 'text/plain',
    'ft10' => 'image/x-freehand',
    'ft11' => 'image/x-freehand',
    'ft12' => 'image/x-freehand',
    'ft7' => 'image/x-freehand',
    'ft8' => 'image/x-freehand',
    'ft9' => 'image/x-freehand',
    'ftc' => 'application/vnd.fluxtime.clip',
    'fti' => 'application/vnd.anser-web-funds-transfer-initiation',
    'fts' => 'application/fits',
    'fv' => 'text/plain',
    'fvt' => 'video/vnd.fvt',
    'fzs' => 'application/vnd.fuzzysheet',
    'g' => 'text/plain',
    'g3' => 'image/g3fax',
    'gac' => 'application/vnd.groove-account',
    'gdl' => 'model/vnd.gdl',
    'geo' => 'application/vnd.dynageo',
    'gex' => 'application/vnd.geometry-explorer',
    'ggb' => 'application/vnd.geogebra.file',
    'ggt' => 'application/vnd.geogebra.tool',
    'ghf' => 'application/vnd.groove-help',
    'gif' => 'image/gif',
    'gim' => 'application/vnd.groove-identity-message',
    'gmx' => 'application/vnd.gmx',
    'gnucash' => 'application/x-gnucash',
    'gnumeric' => 'application/x-gnumeric',
    'go' => 'text/x-go',
    'gph' => 'application/vnd.flographit',
    'gqf' => 'application/vnd.grafeq',
    'gqs' => 'application/vnd.grafeq',
    'gram' => 'application/srgs',
    'grb' => 'application/x-grib',
    'grb1' => 'application/x-grib',
    'grb2' => 'application/x-grib',
    'gre' => 'application/vnd.geometry-explorer',
    'grm' => 'text/plain',
    'groovy' => 'text/x-groovy',
    'grv' => 'application/vnd.groove-injector',
    'grxml' => 'application/srgs+xml',
    'gsf' => 'application/x-font-ghostscript',
    'gtar' => 'application/x-gtar',
    'gtm' => 'application/vnd.groove-tool-message',
    'gtw' => 'model/vnd.gtw',
    'gv' => 'text/vnd.graphviz',
    'gz' => 'application/gzip',
    'h' => 'text/x-c++hdr',
    'h++' => 'text/x-c++hdr',
    'h261' => 'video/h261',
    'h263' => 'video/h263',
    'h264' => 'video/h264',
    'h5' => 'application/x-hdf',
    'haml' => 'text/x-haml',
    'handlers' => 'text/plain',
    'hbci' => 'application/vnd.hbci',
    'hdf' => 'application/x-hdf',
    'hdr' => 'application/envi.hdr',
    'he5' => 'application/x-hdf',
    'heic' => 'image/heic',
    'heif' => 'image/heif',
    'hfa' => 'application/x-erdas-hfa',
    'hh' => 'text/x-c++hdr',
    'hlp' => 'application/winhlp',
    'hp' => 'text/x-c++hdr',
    'hpgl' => 'application/vnd.hp-hpgl',
    'hpid' => 'application/vnd.hp-hpid',
    'hpp' => 'text/x-c++hdr',
    'hprof' => 'application/vnd.java.hprof ',
    'hprof.txt' => 'application/vnd.java.hprof.text',
    'hps' => 'application/vnd.hp-hps',
    'hqx' => 'application/mac-binhex40',
    'hs' => 'text/x-haskell',
    'htc' => 'text/plain',
    'htke' => 'application/vnd.kenameaapp',
    'htm' => 'text/html',
    'html' => 'text/html',
    'hvd' => 'application/vnd.yamaha.hv-dic',
    'hvp' => 'application/vnd.yamaha.hv-voice',
    'hvs' => 'application/vnd.yamaha.hv-script',
    'hx' => 'text/x-haxe',
    'hxx' => 'text/x-c++hdr',
    'i3' => 'text/x-modula',
    'ibooks' => 'application/x-ibooks+zip',
    'icb' => 'image/x-tga',
    'icc' => 'application/vnd.iccprofile',
    'ice' => 'x-conference/x-cooltalk',
    'icm' => 'application/vnd.iccprofile',
    'icns' => 'image/icns',
    'ico' => 'image/vnd.microsoft.icon',
    'ics' => 'text/calendar',
    'idl' => 'text/x-idl',
    'ief' => 'image/ief',
    'ifb' => 'text/calendar',
    'ifm' => 'application/vnd.shana.informed.formdata',
    'ig' => 'text/x-modula',
    'iges' => 'model/iges',
    'igl' => 'application/vnd.igloader',
    'igs' => 'model/iges',
    'igx' => 'application/vnd.micrografx.igx',
    'ihtml' => 'text/plain',
    'iif' => 'application/vnd.shana.informed.interchange',
    'iiq' => 'image/x-raw-phaseone',
    'imp' => 'application/vnd.accpac.simply.imp',
    'ims' => 'application/vnd.ms-ims',
    'in' => 'text/plain',
    'indd' => 'application/x-adobe-indesign',
    'ini' => 'text/x-ini',
    'inx' => 'application/x-adobe-indesign-interchange',
    'ipa' => 'application/x-itunes-ipa',
    'ipk' => 'application/vnd.shana.informed.package',
    'irm' => 'application/vnd.ibm.rights-management',
    'irp' => 'application/vnd.irepository.package+xml',
    'iso' => 'application/x-iso9660-image',
    'iso19139' => 'text/iso19139+xml',
    'itk' => 'text/x-tcl',
    'itp' => 'application/vnd.shana.informed.formtemplate',
    'ivp' => 'application/vnd.immervision-ivp',
    'ivu' => 'application/vnd.immervision-ivu',
    'j2c' => 'image/x-jp2-codestream',
    'jad' => 'text/vnd.sun.j2me.app-descriptor',
    'jam' => 'application/vnd.jam',
    'jar' => 'application/java-archive',
    'java' => 'text/x-java-source',
    'jb2' => 'image/x-jbig2',
    'jbig2' => 'image/x-jbig2',
    'jfi' => 'image/jpeg',
    'jfif' => 'image/jpeg',
    'jif' => 'image/jpeg',
    'jisp' => 'application/vnd.jisp',
    'jl' => 'text/x-common-lisp',
    'jlt' => 'application/vnd.hp-jlyt',
    'jmx' => 'text/plain',
    'jng' => 'video/x-jng',
    'jnilib' => 'application/x-java-jnilib',
    'jnlp' => 'application/x-java-jnlp-file',
    'joda' => 'application/vnd.joost.joda-archive',
    'jp2' => 'image/jp2',
    'jpe' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'jpf' => 'image/jpx',
    'jpg' => 'image/jpeg',
    'jpgm' => 'image/jpm',
    'jpgv' => 'video/jpeg',
    'jpm' => 'image/jpm',
    'js' => 'application/javascript',
    'json' => 'application/json',
    'jsp' => 'text/x-jsp',
    'junit' => 'text/plain',
    'jx' => 'text/plain',
    'k25' => 'image/x-raw-kodak',
    'kar' => 'audio/midi',
    'karbon' => 'application/vnd.kde.karbon',
    'kdc' => 'image/x-raw-kodak',
    'key' => 'application/vnd.apple.keynote',
    'kfo' => 'application/vnd.kde.kformula',
    'kia' => 'application/vnd.kidspiration',
    'kil' => 'application/x-killustrator',
    'kml' => 'application/vnd.google-earth.kml+xml',
    'kmz' => 'application/vnd.google-earth.kmz',
    'kne' => 'application/vnd.kinar',
    'knp' => 'application/vnd.kinar',
    'kon' => 'application/vnd.kde.kontour',
    'kpr' => 'application/vnd.kde.kpresenter',
    'kpt' => 'application/vnd.kde.kpresenter',
    'ksp' => 'application/vnd.kde.kspread',
    'ktr' => 'application/vnd.kahootz',
    'ktz' => 'application/vnd.kahootz',
    'kwd' => 'application/vnd.kde.kword',
    'kwt' => 'application/vnd.kde.kword',
    'l' => 'text/x-lex',
    'latex' => 'application/x-latex',
    'lbd' => 'application/vnd.llamagraphics.life-balance.desktop',
    'lbe' => 'application/vnd.llamagraphics.life-balance.exchange+xml',
    'les' => 'application/vnd.hhe.lesson-player',
    'less' => 'text/x-less',
    'lha' => 'application/octet-stream',
    'lhs' => 'text/x-haskell',
    'link66' => 'application/vnd.route66.link66+xml',
    'lisp' => 'text/x-common-lisp',
    'list' => 'text/plain',
    'list3820' => 'application/vnd.ibm.modcap',
    'listafp' => 'application/vnd.ibm.modcap',
    'log' => 'text/x-log',
    'lostxml' => 'application/lost+xml',
    'lrf' => 'application/octet-stream',
    'lrm' => 'application/vnd.ms-lrm',
    'lsp' => 'text/x-common-lisp',
    'ltf' => 'application/vnd.frogans.ltf',
    'lua' => 'text/x-lua',
    'lvp' => 'audio/vnd.lucent.voice',
    'lwp' => 'application/vnd.lotus-wordpro',
    'lz' => 'application/x-lzip',
    'lz4' => 'application/x-lz4',
    'lzh' => 'application/octet-stream',
    'lzma' => 'application/x-lzma',
    'm' => 'text/x-objcsrc',
    'm13' => 'application/x-msmediaview',
    'm14' => 'application/x-msmediaview',
    'm1v' => 'video/mpeg',
    'm2a' => 'audio/mpeg',
    'm2v' => 'video/mpeg',
    'm3' => 'text/x-modula',
    'm3a' => 'audio/mpeg',
    'm3u' => 'audio/x-mpegurl',
    'm3u8' => 'application/vnd.apple.mpegurl',
    'm4' => 'text/plain',
    'm4a' => 'audio/mp4',
    'm4b' => 'audio/mp4',
    'm4s' => 'video/iso.segment',
    'm4u' => 'video/vnd.mpegurl',
    'm4v' => 'video/x-m4v',
    'ma' => 'application/mathematica',
    'mag' => 'application/vnd.ecowin.chart',
    'maker' => 'application/vnd.framemaker',
    'man' => 'text/troff',
    'manifest' => 'text/plain',
    'markdown' => 'text/x-web-markdown',
    'mat' => 'application/x-matlab-data',
    'mathml' => 'application/mathml+xml',
    'mb' => 'application/mathematica',
    'mbk' => 'application/vnd.mobius.mbk',
    'mbox' => 'application/mbox',
    'mc1' => 'application/vnd.medcalcdata',
    'mcd' => 'application/vnd.mcd',
    'mcurl' => 'text/vnd.curl.mcurl',
    'md' => 'text/x-web-markdown',
    'mdb' => 'application/x-msaccess',
    'mdi' => 'image/vnd.ms-modi',
    'mdo' => 'text/plain',
    'mdtext' => 'text/x-web-markdown',
    'me' => 'text/troff',
    'mef' => 'image/x-raw-mamiya',
    'memgraph' => 'application/x-memgraph',
    'mesh' => 'model/mesh',
    'meta' => 'text/plain',
    'mf' => 'text/plain',
    'mfm' => 'application/vnd.mfmp',
    'mg' => 'text/x-modula',
    'mgz' => 'application/vnd.proteus.magazine',
    'mht' => 'multipart/related',
    'mhtml' => 'multipart/related',
    'mid' => 'audio/midi',
    'midi' => 'audio/midi',
    'mif' => 'application/vnd.mif',
    'mime' => 'message/rfc822',
    'mj2' => 'video/mj2',
    'mjp2' => 'video/mj2',
    'mka' => 'audio/x-matroska',
    'mkd' => 'text/x-web-markdown',
    'mkv' => 'video/x-matroska',
    'ml' => 'text/x-ml',
    'mli' => 'text/x-ocaml',
    'mlp' => 'application/vnd.dolby.mlp',
    'mmap' => 'application/vnd.mindjet.mindmanager',
    'mmas' => 'application/vnd.mindjet.mindmanager',
    'mmat' => 'application/vnd.mindjet.mindmanager',
    'mmd' => 'application/vnd.chipnuts.karaoke-mmd',
    'mmf' => 'application/vnd.smaf',
    'mmmp' => 'application/vnd.mindjet.mindmanager',
    'mmp' => 'application/vnd.mindjet.mindmanager',
    'mmpt' => 'application/vnd.mindjet.mindmanager',
    'mmr' => 'image/vnd.fujixerox.edmics-mmr',
    'mng' => 'video/x-mng',
    'mny' => 'application/x-msmoney',
    'mobi' => 'application/x-mobipocket-ebook',
    'mod' => 'audio/x-mod',
    'mos' => 'image/x-raw-leaf',
    'mov' => 'video/quicktime',
    'movie' => 'video/x-sgi-movie',
    'mp2' => 'audio/mpeg',
    'mp2a' => 'audio/mpeg',
    'mp3' => 'audio/mpeg',
    'mp4' => 'video/mp4',
    'mp4a' => 'audio/mp4',
    'mp4s' => 'application/mp4',
    'mp4v' => 'video/mp4',
    'mpc' => 'application/vnd.mophun.certificate',
    'mpd' => 'application/dash+xml',
    'mpe' => 'video/mpeg',
    'mpeg' => 'video/mpeg',
    'mpg' => 'video/mpeg',
    'mpg4' => 'video/mp4',
    'mpga' => 'audio/mpeg',
    'mpkg' => 'application/vnd.apple.installer+xml',
    'mpm' => 'application/vnd.blueice.multipass',
    'mpn' => 'application/vnd.mophun.application',
    'mpp' => 'application/vnd.ms-project',
    'mpt' => 'application/vnd.ms-project',
    'mpx' => 'application/x-project',
    'mpy' => 'application/vnd.ibm.minipay',
    'mqy' => 'application/vnd.mobius.mqy',
    'mrc' => 'application/marc',
    'mrw' => 'image/x-raw-minolta',
    'ms' => 'text/troff',
    'mscml' => 'application/mediaservercontrol+xml',
    'mseed' => 'application/vnd.fdsn.mseed',
    'mseq' => 'application/vnd.mseq',
    'msf' => 'application/vnd.epson.msf',
    'msg' => 'application/vnd.ms-outlook',
    'msh' => 'model/mesh',
    'msi' => 'application/x-ms-installer',
    'msl' => 'application/vnd.mobius.msl',
    'msp' => 'application/x-ms-installer',
    'mst' => 'application/x-ms-installer',
    'msty' => 'application/vnd.muvee.style',
    'mts' => 'model/vnd.mts',
    'mus' => 'application/vnd.musician',
    'musicxml' => 'application/vnd.recordare.musicxml+xml',
    'mvb' => 'application/x-msmediaview',
    'mwf' => 'application/vnd.mfer',
    'mxf' => 'application/mxf',
    'mxl' => 'application/vnd.recordare.musicxml',
    'mxml' => 'application/xv+xml',
    'mxs' => 'application/vnd.triscape.mxs',
    'mxu' => 'video/vnd.mpegurl',
    'myd' => 'application/x-mysql-misam-data',
    'myi' => 'application/x-mysql-misam-compressed-index',
    'n-gage' => 'application/vnd.nokia.n-gage.symbian.install',
    'n3' => 'text/plain',
    'nar' => 'application/vnd.iptc.g2.newsmessage+xml',
    'nb' => 'application/mathematica',
    'nc' => 'application/x-netcdf',
    'ncx' => 'application/x-dtbncx+xml',
    'nef' => 'image/x-raw-nikon',
    'ngdat' => 'application/vnd.nokia.n-gage.data',
    'nitf' => 'image/nitf',
    'nlu' => 'application/vnd.neurolanguage.nlu',
    'nml' => 'application/vnd.enliven',
    'nnd' => 'application/vnd.noblenet-directory',
    'nns' => 'application/vnd.noblenet-sealer',
    'nnw' => 'application/vnd.noblenet-web',
    'npx' => 'image/vnd.net-fpx',
    'nroff' => 'text/troff',
    'nrw' => 'image/x-raw-nikon',
    'nsf' => 'application/vnd.lotus-notes',
    'ntf' => 'image/nitf',
    'numbers' => 'application/vnd.apple.numbers',
    'oa2' => 'application/vnd.fujitsu.oasys2',
    'oa3' => 'application/vnd.fujitsu.oasys3',
    'oas' => 'application/vnd.fujitsu.oasys',
    'obd' => 'application/x-msbinder',
    'ocaml' => 'text/x-ocaml',
    'oda' => 'application/oda',
    'odb' => 'application/vnd.oasis.opendocument.base',
    'odc' => 'application/vnd.oasis.opendocument.chart',
    'odf' => 'application/vnd.oasis.opendocument.formula',
    'odft' => 'application/vnd.oasis.opendocument.formula-template',
    'odg' => 'application/vnd.oasis.opendocument.graphics',
    'odi' => 'application/vnd.oasis.opendocument.image',
    'odp' => 'application/vnd.oasis.opendocument.presentation',
    'ods' => 'application/vnd.oasis.opendocument.spreadsheet',
    'odt' => 'application/vnd.oasis.opendocument.text',
    'oga' => 'audio/ogg',
    'ogg' => 'audio/vorbis',
    'ogm' => 'video/x-ogm',
    'ogv' => 'video/ogg',
    'ogx' => 'application/ogg',
    'one' => 'application/onenote;format=one',
    'onepkg' => 'application/onenote; format=package',
    'onetmp' => 'application/onenote',
    'onetoc' => 'application/onenote;format=onetoc2',
    'onetoc2' => 'application/onenote;format=onetoc2',
    'opf' => 'application/oebps-package+xml',
    'oprc' => 'application/vnd.palm',
    'opus' => 'audio/opus',
    'orf' => 'image/x-raw-olympus',
    'org' => 'application/vnd.lotus-organizer',
    'osf' => 'application/vnd.yamaha.openscoreformat',
    'osfpvg' => 'application/vnd.yamaha.openscoreformat.osfpvg+xml',
    'ost' => 'application/vnd.ms-outlook-pst',
    'otc' => 'application/vnd.oasis.opendocument.chart-template',
    'otf' => 'application/x-font-otf',
    'otg' => 'application/vnd.oasis.opendocument.graphics-template',
    'oth' => 'application/vnd.oasis.opendocument.text-web',
    'oti' => 'application/vnd.oasis.opendocument.image-template',
    'otm' => 'application/vnd.oasis.opendocument.text-master',
    'otp' => 'application/vnd.oasis.opendocument.presentation-template',
    'ots' => 'application/vnd.oasis.opendocument.spreadsheet-template',
    'ott' => 'application/vnd.oasis.opendocument.text-template',
    'owl' => 'application/rdf+xml',
    'oxps' => 'application/vnd.ms-xpsdocument',
    'oxt' => 'application/vnd.openofficeorg.extension',
    'p' => 'text/x-pascal',
    'p10' => 'application/pkcs10',
    'p12' => 'application/x-pkcs12',
    'p7b' => 'application/x-pkcs7-certificates',
    'p7c' => 'application/pkcs7-mime',
    'p7m' => 'application/pkcs7-mime',
    'p7r' => 'application/x-pkcs7-certreqresp',
    'p7s' => 'application/pkcs7-signature',
    'pack' => 'application/x-java-pack200',
    'pages' => 'application/vnd.apple.pages',
    'parquet' => 'application/x-parquet',
    'pas' => 'text/x-pascal',
    'patch' => 'text/x-diff',
    'pbd' => 'application/vnd.powerbuilder6',
    'pbm' => 'image/x-portable-bitmap',
    'pcap' => 'application/vnd.tcpdump.pcap',
    'pcf' => 'application/x-font-pcf',
    'pcl' => 'application/vnd.hp-pcl',
    'pclxl' => 'application/vnd.hp-pclxl',
    'pct' => 'image/x-pict',
    'pcurl' => 'application/vnd.curl.pcurl',
    'pcx' => 'image/vnd.zbrush.pcx',
    'pdb' => 'chemical/x-pdb',
    'pdf' => 'application/pdf',
    'pef' => 'image/x-raw-pentax',
    'pem' => 'application/x-x509-cert;format=pem',
    'pen' => 'text/plain',
    'perl' => 'text/x-perl',
    'pfa' => 'application/x-font-type1',
    'pfb' => 'application/x-font-type1',
    'pfm' => 'application/x-font-printer-metric',
    'pfr' => 'application/font-tdpfr',
    'pfx' => 'application/x-pkcs12',
    'pgm' => 'image/x-portable-graymap',
    'pgn' => 'application/x-chess-pgn',
    'pgp' => 'application/pgp-encrypted',
    'php' => 'text/x-php',
    'php3' => 'text/x-php',
    'php4' => 'text/x-php',
    'pic' => 'image/x-pict',
    'pict' => 'image/x-pict',
    'pkg' => 'application/octet-stream',
    'pki' => 'application/pkixcmp',
    'pkipath' => 'application/pkix-pkipath',
    'pl' => 'text/x-perl',
    'plb' => 'application/vnd.3gpp.pic-bw-large',
    'plc' => 'application/vnd.mobius.plc',
    'plf' => 'application/vnd.pocketlearn',
    'pls' => 'application/pls+xml',
    'pm' => 'text/x-perl',
    'pml' => 'application/vnd.ctc-posml',
    'png' => 'image/png',
    'pnm' => 'image/x-portable-anymap',
    'pod' => 'text/plain',
    'pom' => 'text/plain',
    'portpkg' => 'application/vnd.macports.portpkg',
    'pot' => 'application/vnd.ms-powerpoint',
    'potm' => 'application/vnd.ms-powerpoint.template.macroenabled.12',
    'potx' => 'application/vnd.openxmlformats-officedocument.presentationml.template',
    'pp' => 'text/x-pascal',
    'ppa' => 'application/vnd.ms-powerpoint',
    'ppam' => 'application/vnd.ms-powerpoint.addin.macroenabled.12',
    'ppd' => 'application/vnd.cups-ppd',
    'ppj' => 'image/vnd.adobe.premiere',
    'ppm' => 'image/x-portable-pixmap',
    'pps' => 'application/vnd.ms-powerpoint',
    'ppsm' => 'application/vnd.ms-powerpoint.slideshow.macroenabled.12',
    'ppsx' => 'application/vnd.openxmlformats-officedocument.presentationml.slideshow',
    'ppt' => 'application/vnd.ms-powerpoint',
    'pptm' => 'application/vnd.ms-powerpoint.presentation.macroenabled.12',
    'pptx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'ppz' => 'application/vnd.ms-powerpoint',
    'pqa' => 'application/vnd.palm',
    'prc' => 'application/x-mobipocket-ebook',
    'pre' => 'application/vnd.lotus-freelance',
    'prf' => 'application/pics-rules',
    'pro' => 'text/x-prolog',
    'project' => 'text/plain',
    'properties' => 'text/x-java-properties',
    'prt' => 'application/x-prt',
    'ps' => 'application/postscript',
    'psb' => 'application/vnd.3gpp.pic-bw-small',
    'psd' => 'image/vnd.adobe.photoshop',
    'psf' => 'application/x-font-linux-psf',
    'pst' => 'application/vnd.ms-outlook-pst',
    'ptid' => 'application/vnd.pvi.ptid1',
    'ptx' => 'image/x-raw-pentax',
    'pub' => 'application/x-mspublisher',
    'pvb' => 'application/vnd.3gpp.pic-bw-var',
    'pwn' => 'application/vnd.3m.post-it-notes',
    'pxn' => 'image/x-raw-logitech',
    'py' => 'text/x-python',
    'pya' => 'audio/vnd.ms-playready.media.pya',
    'pyv' => 'video/vnd.ms-playready.media.pyv',
    'qam' => 'application/vnd.epson.quickanime',
    'qbo' => 'application/vnd.intu.qbo',
    'qfx' => 'application/vnd.intu.qfx',
    'qps' => 'application/vnd.publishare-delta-tree',
    'qpw' => 'application/x-quattro-pro',
    'qt' => 'video/quicktime',
    'qwd' => 'application/vnd.quark.quarkxpress',
    'qwt' => 'application/vnd.quark.quarkxpress',
    'qxb' => 'application/vnd.quark.quarkxpress',
    'qxd' => 'application/vnd.quark.quarkxpress',
    'qxl' => 'application/vnd.quark.quarkxpress',
    'qxt' => 'application/vnd.quark.quarkxpress',
    'r' => 'text/x-rsrc',
    'r3d' => 'image/x-raw-red',
    'ra' => 'audio/x-pn-realaudio',
    'raf' => 'image/x-raw-fuji',
    'ram' => 'audio/x-pn-realaudio',
    'rar' => 'application/x-rar-compressed',
    'ras' => 'image/x-cmu-raster',
    'raw' => 'image/x-raw-panasonic',
    'rb' => 'text/x-ruby',
    'rcprofile' => 'application/vnd.ipunplugged.rcprofile',
    'rdf' => 'application/rdf+xml',
    'rdz' => 'application/vnd.data-vision.rdz',
    'rep' => 'application/vnd.businessobjects',
    'res' => 'application/x-dtbresource+xml',
    'rest' => 'text/x-rst',
    'restx' => 'text/x-rst',
    'rexx' => 'text/x-rexx',
    'rgb' => 'image/x-rgb',
    'rif' => 'application/reginfo+xml',
    'rl' => 'application/resource-lists+xml',
    'rlc' => 'image/vnd.fujixerox.edmics-rlc',
    'rld' => 'application/resource-lists-diff+xml',
    'rm' => 'application/vnd.rn-realmedia',
    'rmi' => 'audio/midi',
    'rmp' => 'audio/x-pn-realaudio-plugin',
    'rms' => 'application/vnd.jcp.javame.midlet-rms',
    'rnc' => 'application/relax-ng-compact-syntax',
    'rng' => 'text/plain',
    'rnx' => 'text/plain',
    'roff' => 'text/troff',
    'roles' => 'text/plain',
    'rpm' => 'application/x-rpm',
    'rpss' => 'application/vnd.nokia.radio-presets',
    'rpst' => 'application/vnd.nokia.radio-preset',
    'rq' => 'application/sparql-query',
    'rs' => 'application/rls-services+xml',
    'rsd' => 'application/rsd+xml',
    'rss' => 'application/rss+xml',
    'rst' => 'text/x-rst',
    'rtf' => 'application/rtf',
    'rtx' => 'text/richtext',
    'rw2' => 'image/x-raw-panasonic',
    'rwz' => 'image/x-raw-rawzor',
    's' => 'text/x-assembly',
    's7m' => 'application/x-sas-dmdb',
    'sa7' => 'application/x-sas-access',
    'saf' => 'application/vnd.yamaha.smaf-audio',
    'sas' => 'application/x-sas',
    'sas7bacs' => 'application/x-sas-access',
    'sas7baud' => 'application/x-sas-audit',
    'sas7bbak' => 'application/x-sas-backup',
    'sas7bcat' => 'application/x-sas-catalog',
    'sas7bdat' => 'application/x-sas-data',
    'sas7bdmd' => 'application/x-sas-dmdb',
    'sas7bfdb' => 'application/x-sas-fdb',
    'sas7bitm' => 'application/x-sas-itemstor',
    'sas7bmdb' => 'application/x-sas-mddb',
    'sas7bndx' => 'application/x-sas-data-index',
    'sas7bpgm' => 'application/x-sas-program-data',
    'sas7bput' => 'application/x-sas-putility',
    'sas7butl' => 'application/x-sas-utility',
    'sas7bvew' => 'application/x-sas-view',
    'sass' => 'text/x-sass',
    'sbml' => 'application/sbml+xml',
    'sc' => 'application/vnd.ibm.secure-container',
    'sc7' => 'application/x-sas-catalog',
    'scala' => 'text/x-scala',
    'scd' => 'application/x-msschedule',
    'schemas' => 'text/plain',
    'scm' => 'text/x-scheme',
    'scq' => 'application/scvp-cv-request',
    'scs' => 'application/scvp-cv-response',
    'scss' => 'text/x-scss',
    'scurl' => 'text/vnd.curl.scurl',
    'sd2' => 'application/x-sas-data-v6',
    'sd7' => 'application/x-sas-data',
    'sda' => 'application/vnd.stardivision.draw',
    'sdc' => 'application/vnd.stardivision.calc',
    'sdd' => 'application/vnd.stardivision.impress',
    'sdkd' => 'application/vnd.solent.sdkm+xml',
    'sdkm' => 'application/vnd.solent.sdkm+xml',
    'sdp' => 'application/sdp',
    'sdw' => 'application/vnd.stardivision.writer',
    'sed' => 'text/x-sed',
    'see' => 'application/vnd.seemail',
    'seed' => 'application/vnd.fdsn.seed',
    'sema' => 'application/vnd.sema',
    'semd' => 'application/vnd.semd',
    'semf' => 'application/vnd.semf',
    'ser' => 'application/java-serialized-object',
    'setpay' => 'application/set-payment-initiation',
    'setreg' => 'application/set-registration-initiation',
    'sf7' => 'application/x-sas-fdb',
    'sfd-hdstx' => 'application/vnd.hydrostatix.sof-data',
    'sfdu' => 'application/x-sfdu',
    'sfs' => 'application/vnd.spotfire.sfs',
    'sgl' => 'application/vnd.stardivision.writer-global',
    'sgm' => 'text/sgml',
    'sgml' => 'text/sgml',
    'sh' => 'application/x-sh',
    'shar' => 'application/x-shar',
    'shf' => 'application/shf+xml',
    'shp' => 'application/x-shapefile',
    'shw' => 'application/x-corelpresentations',
    'si7' => 'application/x-sas-data-index',
    'sig' => 'application/pgp-signature',
    'silo' => 'model/mesh',
    'sis' => 'application/vnd.symbian.install',
    'sisx' => 'application/vnd.symbian.install',
    'sit' => 'application/x-stuffit',
    'sitx' => 'application/x-stuffitx',
    'skd' => 'application/vnd.koan',
    'skm' => 'application/vnd.koan',
    'skp' => 'application/vnd.koan',
    'skt' => 'application/vnd.koan',
    'sldasm' => 'application/sldworks',
    'slddrw' => 'application/sldworks',
    'sldm' => 'application/vnd.ms-powerpoint.slide.macroenabled.12',
    'sldprt' => 'application/sldworks',
    'sldx' => 'application/vnd.openxmlformats-officedocument.presentationml.slide',
    'slt' => 'application/vnd.epson.salt',
    'sm7' => 'application/x-sas-mddb',
    'smf' => 'application/vnd.stardivision.math',
    'smi' => 'application/smil+xml',
    'smil' => 'application/smil+xml',
    'sml' => 'application/smil+xml',
    'snd' => 'audio/basic',
    'snf' => 'application/x-font-snf',
    'so' => 'application/octet-stream',
    'sp7' => 'application/x-sas-putility',
    'spc' => 'application/x-pkcs7-certificates',
    'spf' => 'application/vnd.yamaha.smaf-phrase',
    'spl' => 'application/x-futuresplash',
    'spot' => 'text/vnd.in3d.spot',
    'spp' => 'application/scvp-vp-response',
    'spq' => 'application/scvp-vp-request',
    'spx' => 'audio/speex',
    'sql' => 'text/x-sql',
    'sr2' => 'image/x-raw-sony',
    'sr7' => 'application/x-sas-itemstor',
    'src' => 'application/x-wais-source',
    'srf' => 'image/x-raw-sony',
    'srl' => 'application/sereal',
    'srx' => 'application/sparql-results+xml',
    'ss7' => 'application/x-sas-program-data',
    'sse' => 'application/vnd.kodak-descriptor',
    'ssf' => 'application/vnd.epson.ssf',
    'ssml' => 'application/ssml+xml',
    'st' => 'text/x-stsrc',
    'st7' => 'application/x-sas-audit',
    'stc' => 'application/vnd.sun.xml.calc.template',
    'std' => 'application/vnd.sun.xml.draw.template',
    'stf' => 'application/vnd.wt.stf',
    'sti' => 'application/vnd.sun.xml.impress.template',
    'stk' => 'application/hyperstudio',
    'stl' => 'application/vnd.ms-pki.stl',
    'str' => 'application/vnd.pg.format',
    'stw' => 'application/vnd.sun.xml.writer.template',
    'stx' => 'application/x-sas-transport',
    'su7' => 'application/x-sas-utility',
    'sus' => 'application/vnd.sus-calendar',
    'susp' => 'application/vnd.sus-calendar',
    'sv4cpio' => 'application/x-sv4cpio',
    'sv4crc' => 'application/x-sv4crc',
    'sv7' => 'application/x-sas-view',
    'svd' => 'application/vnd.svd',
    'svg' => 'image/svg+xml',
    'svgz' => 'image/svg+xml',
    'swa' => 'application/x-director',
    'swf' => 'application/x-shockwave-flash',
    'swi' => 'application/vnd.arastra.swi',
    'sxc' => 'application/vnd.sun.xml.calc',
    'sxd' => 'application/vnd.sun.xml.draw',
    'sxg' => 'application/vnd.sun.xml.writer.global',
    'sxi' => 'application/vnd.sun.xml.impress',
    'sxm' => 'application/vnd.sun.xml.math',
    'sxw' => 'application/vnd.sun.xml.writer',
    'sz' => 'application/x-snappy-framed',
    't' => 'text/troff',
    'tao' => 'application/vnd.tao.intent-module-archive',
    'tar' => 'application/x-tar',
    'tbz' => 'application/x-bzip',
    'tbz2' => 'application/x-bzip2',
    'tcap' => 'application/vnd.3gpp2.tcap',
    'tcl' => 'text/x-tcl',
    'tcsh' => 'application/x-csh',
    'teacher' => 'application/vnd.smart.teacher',
    'tex' => 'application/x-tex',
    'texi' => 'application/x-texinfo',
    'texinfo' => 'application/x-texinfo',
    'text' => 'text/plain',
    'tfm' => 'application/x-tex-tfm',
    'tga' => 'image/x-tga',
    'tgz' => 'application/gzip',
    'thmx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'tif' => 'image/tiff',
    'tiff' => 'image/tiff',
    'tk' => 'text/x-tcl',
    'tld' => 'text/plain',
    'tmo' => 'application/vnd.tmobile-livetv',
    'toast' => 'application/x-roxio-toast',
    'torrent' => 'application/x-bittorrent',
    'tpl' => 'application/vnd.groove-tool-template',
    'tpt' => 'application/vnd.trid.tpt',
    'tr' => 'text/troff',
    'tra' => 'application/vnd.trueapp',
    'trm' => 'application/x-msterminal',
    'tsd' => 'application/timestamped-data',
    'tsv' => 'text/tab-separated-values',
    'ttc' => 'application/x-font-ttf',
    'ttf' => 'application/x-font-ttf',
    'twd' => 'application/vnd.simtech-mindmapper',
    'twds' => 'application/vnd.simtech-mindmapper',
    'txd' => 'application/vnd.genomatix.tuxedo',
    'txf' => 'application/vnd.mobius.txf',
    'txt' => 'text/plain',
    'types' => 'text/plain',
    'u32' => 'application/x-authorware-bin',
    'uc2' => 'application/x-uc2-compressed',
    'udeb' => 'application/x-debian-package',
    'ufd' => 'application/vnd.ufdl',
    'ufdl' => 'application/vnd.ufdl',
    'umj' => 'application/vnd.umajin',
    'unityweb' => 'application/vnd.unity',
    'uoml' => 'application/vnd.uoml+xml',
    'uri' => 'text/uri-list',
    'uris' => 'text/uri-list',
    'urls' => 'text/uri-list',
    'ustar' => 'application/x-ustar',
    'utz' => 'application/vnd.uiq.theme',
    'uu' => 'text/x-uuencode',
    'v' => 'text/x-verilog',
    'vb' => 'text/x-vbdotnet',
    'vbs' => 'text/x-vbscript',
    'vcd' => 'application/x-cdlink',
    'vcf' => 'text/x-vcard',
    'vcg' => 'application/vnd.groove-vcard',
    'vcs' => 'text/x-vcalendar',
    'vcx' => 'application/vnd.vcx',
    'vda' => 'image/x-tga',
    'vhd' => 'text/x-vhdl',
    'vhdl' => 'text/x-vhdl',
    'vis' => 'application/vnd.visionary',
    'viv' => 'video/vnd.vivo',
    'vm' => 'text/plain',
    'vmdk' => 'application/x-vmdk',
    'vor' => 'application/x-staroffice-template',
    'vox' => 'application/x-authorware-bin',
    'vrml' => 'model/vrml',
    'vsd' => 'application/vnd.visio',
    'vsdm' => 'application/vnd.ms-visio.drawing.macroEnabled.12',
    'vsdx' => 'application/vnd.ms-visio.drawing',
    'vsf' => 'application/vnd.vsf',
    'vsl' => 'text/plain',
    'vss' => 'application/vnd.visio',
    'vssm' => 'application/vnd.ms-visio.stencil.macroEnabled.12',
    'vssx' => 'application/vnd.ms-visio.stencil',
    'vst' => 'application/vnd.visio',
    'vstm' => 'application/vnd.ms-visio.template.macroEnabled.12',
    'vstx' => 'application/vnd.ms-visio.template',
    'vsw' => 'application/vnd.visio',
    'vtt' => 'text/vtt',
    'vtu' => 'model/vnd.vtu',
    'vxml' => 'application/voicexml+xml',
    'w3d' => 'application/x-director',
    'w60' => 'application/vnd.wordperfect',
    'wad' => 'application/x-doom',
    'war' => 'application/x-tika-java-web-archive',
    'warc' => 'application/warc',
    'wasm' => 'application/wasm',
    'wav' => 'audio/vnd.wave',
    'wax' => 'audio/x-ms-wax',
    'wb1' => 'application/x-quattro-pro',
    'wb2' => 'application/x-quattro-pro',
    'wb3' => 'application/x-quattro-pro',
    'wbmp' => 'image/vnd.wap.wbmp',
    'wbs' => 'application/vnd.criticaltools.wbs+xml',
    'wbxml' => 'application/vnd.wap.wbxml',
    'wcm' => 'application/vnd.ms-works',
    'wdb' => 'application/vnd.ms-works',
    'webarchive' => 'application/x-webarchive',
    'webm' => 'video/webm',
    'webp' => 'image/webp',
    'wk1' => 'application/vnd.lotus-1-2-3',
    'wk2' => 'application/vnd.lotus-1-2-3',
    'wk3' => 'application/vnd.lotus-1-2-3',
    'wk4' => 'application/vnd.lotus-1-2-3',
    'wkq' => 'application/x-quattro-pro',
    'wks' => 'application/vnd.ms-works',
    'wl' => 'application/vnd.wolfram.wl',
    'wm' => 'video/x-ms-wm',
    'wma' => 'audio/x-ms-wma',
    'wmd' => 'application/x-ms-wmd',
    'wmf' => 'image/wmf',
    'wml' => 'text/vnd.wap.wml',
    'wmlc' => 'application/vnd.wap.wmlc',
    'wmls' => 'text/vnd.wap.wmlscript',
    'wmlsc' => 'application/vnd.wap.wmlscriptc',
    'wmv' => 'video/x-ms-wmv',
    'wmx' => 'video/x-ms-wmx',
    'wmz' => 'application/x-ms-wmz',
    'woff' => 'font/woff',
    'woff2' => 'font/woff2',
    'wp' => 'application/vnd.wordperfect',
    'wp5' => 'application/vnd.wordperfect',
    'wp6' => 'application/vnd.wordperfect',
    'wp61' => 'application/vnd.wordperfect',
    'wpd' => 'application/vnd.wordperfect',
    'wpl' => 'application/vnd.ms-wpl',
    'wps' => 'application/vnd.ms-works',
    'wpt' => 'application/vnd.wordperfect',
    'wq1' => 'application/x-quattro-pro',
    'wq2' => 'application/x-quattro-pro',
    'wqd' => 'application/vnd.wqd',
    'wri' => 'application/x-mswrite',
    'wrl' => 'model/vrml',
    'wsdd' => 'text/plain',
    'wsdl' => 'application/wsdl+xml',
    'wspolicy' => 'application/wspolicy+xml',
    'wtb' => 'application/vnd.webturbo',
    'wvx' => 'video/x-ms-wvx',
    'x32' => 'application/x-authorware-bin',
    'x3d' => 'application/vnd.hzn-3d-crossword',
    'x3f' => 'image/x-raw-sigma',
    'xap' => 'application/x-silverlight-app',
    'xar' => 'application/vnd.xara',
    'xargs' => 'text/plain',
    'xbap' => 'application/x-ms-xbap',
    'xbd' => 'application/vnd.fujixerox.docuworks.binder',
    'xbm' => 'image/x-xbitmap',
    'xcat' => 'text/plain',
    'xcf' => 'image/x-xcf',
    'xconf' => 'text/x-config',
    'xdm' => 'application/vnd.syncml.dm+xml',
    'xdp' => 'application/vnd.adobe.xdp+xml',
    'xdw' => 'application/vnd.fujixerox.docuworks',
    'xegrm' => 'text/plain',
    'xenc' => 'application/xenc+xml',
    'xer' => 'application/patch-ops-error+xml',
    'xfdf' => 'application/vnd.adobe.xfdf',
    'xfdl' => 'application/vnd.xfdl',
    'xgrm' => 'text/plain',
    'xht' => 'application/xhtml+xml',
    'xhtml' => 'application/xhtml+xml',
    'xhtml2' => 'application/xhtml+xml',
    'xhvml' => 'application/xv+xml',
    'xif' => 'image/vnd.xiff',
    'xla' => 'application/vnd.ms-excel',
    'xlam' => 'application/vnd.ms-excel.addin.macroenabled.12',
    'xlc' => 'application/vnd.ms-excel',
    'xld' => 'application/vnd.ms-excel',
    'xlex' => 'text/plain',
    'xlf' => 'application/x-xliff+xml',
    'xliff' => 'application/x-xliff+xml',
    'xll' => 'application/vnd.ms-excel',
    'xlm' => 'application/vnd.ms-excel',
    'xlog' => 'text/plain',
    'xlr' => 'application/x-tika-msworks-spreadsheet',
    'xls' => 'application/vnd.ms-excel',
    'xlsb' => 'application/vnd.ms-excel.sheet.binary.macroenabled.12',
    'xlsm' => 'application/vnd.ms-excel.sheet.macroenabled.12',
    'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'xlt' => 'application/vnd.ms-excel',
    'xltm' => 'application/vnd.ms-excel.template.macroenabled.12',
    'xltx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.template',
    'xlw' => 'application/vnd.ms-excel',
    'xlz' => 'application/x-xliff+zip',
    'xmap' => 'text/plain',
    'xmind' => 'application/x-xmind',
    'xml' => 'application/xml',
    'xmp' => 'application/rdf+xml',
    'xo' => 'application/vnd.olpc-sugar',
    'xop' => 'application/xop+xml',
    'xpi' => 'application/x-xpinstall',
    'xpm' => 'image/x-xpixmap',
    'xport' => 'application/x-sas-xport',
    'xpr' => 'application/vnd.is-xpr',
    'xps' => 'application/vnd.ms-xpsdocument',
    'xpt' => 'application/x-sas-xport',
    'xpw' => 'application/vnd.intercon.formnet',
    'xpx' => 'application/vnd.intercon.formnet',
    'xq' => 'application/xquery',
    'xquery' => 'application/xquery',
    'xroles' => 'text/plain',
    'xsamples' => 'text/plain',
    'xsd' => 'application/xml',
    'xsl' => 'application/xml',
    'xslfo' => 'application/xslfo+xml',
    'xslt' => 'application/xslt+xml',
    'xsm' => 'application/vnd.syncml+xml',
    'xsp' => 'text/plain',
    'xspf' => 'application/xspf+xml',
    'xtest' => 'text/plain',
    'xul' => 'application/vnd.mozilla.xul+xml',
    'xvm' => 'application/xv+xml',
    'xvml' => 'application/xv+xml',
    'xwd' => 'image/x-xwindowdump',
    'xweb' => 'text/plain',
    'xwelcome' => 'text/plain',
    'xyz' => 'chemical/x-xyz',
    'xz' => 'application/x-xz',
    'y' => 'text/x-yacc',
    'yaml' => 'text/x-yaml',
    'z' => 'application/x-compress',
    'zaz' => 'application/vnd.zzazz.deck+xml',
    'zip' => 'application/zip',
    'zir' => 'application/vnd.zul',
    'zirz' => 'application/vnd.zul',
    'zmm' => 'application/vnd.handheld-entertainment+xml',
    'zoo' => 'application/x-zoo',
    'zstd' => 'application/zstd',
  }
  # @private
  # :nodoc:
  TYPE_EXTS = {
    'application/andrew-inset' => %w(ez),
    'application/applixware' => %w(aw),
    'application/atom+xml' => %w(atom),
    'application/atomcat+xml' => %w(atomcat),
    'application/atomsvc+xml' => %w(atomsvc),
    'application/bizagi-modeler' => %w(bpm), # BizAgi Process Modeler
    'application/cbor' => %w(cbor), # Concise Binary Object Representation container
    'application/ccxml+xml' => %w(ccxml),
    'application/coreldraw' => %w(cdr), # des: CorelDraw X4 and newer
    'application/cu-seeme' => %w(cu),
    'application/dash+xml' => %w(mpd),
    'application/davmount+xml' => %w(davmount),
    'application/dif+xml' => %w(dif),
    'application/dita+xml;format=map' => %w(ditamap), # DITA Map
    'application/dita+xml;format=topic' => %w(dita), # DITA Topic
    'application/dita+xml;format=val' => %w(ditaval), # DITA Conditional Processing Profile
    'application/ecmascript' => %w(ecma),
    'application/emma+xml' => %w(emma),
    'application/envi.hdr' => %w(hdr),
    'application/epub+zip' => %w(epub), # Electronic Publication
    'application/fits' => %w(fits fit fts), # Flexible Image Transport System
    'application/font-tdpfr' => %w(pfr),
    'application/gzip' => %w(gz tgz), # Gzip Compressed Archive
    'application/hyperstudio' => %w(stk),
    'application/illustrator' => %w(ai), # Adobe Illustrator Artwork
    'application/java-archive' => %w(jar), # Java Archive
    'application/java-serialized-object' => %w(ser),
    'application/java-vm' => %w(class), # Java Class File
    'application/javascript' => %w(js), # JavaScript Source Code
    'application/json' => %w(json),
    'application/lost+xml' => %w(lostxml),
    'application/mac-binhex40' => %w(hqx),
    'application/mac-compactpro' => %w(cpt),
    'application/marc' => %w(mrc),
    'application/mathematica' => %w(ma nb mb), # Wolfram Mathematica
    'application/mathml+xml' => %w(mathml),
    'application/mbox' => %w(mbox),
    'application/mediaservercontrol+xml' => %w(mscml),
    'application/mp4' => %w(mp4s), # MP4 container format
    'application/msword' => %w(doc dot), # Microsoft Word Document
    'application/mxf' => %w(mxf),
    'application/octet-stream' => %w(bin dms lha lrf lzh so dist distz pkg bpk dump elc deploy),
    'application/oda' => %w(oda),
    'application/oebps-package+xml' => %w(opf),
    'application/ogg' => %w(ogx),
    'application/onenote' => %w(onetmp),
    'application/onenote; format=package' => %w(onepkg), # OneNote Package
    'application/onenote;format=one' => %w(one),
    'application/onenote;format=onetoc2' => %w(onetoc onetoc2), # OneNote Table of Contents
    'application/patch-ops-error+xml' => %w(xer),
    'application/pdf' => %w(pdf), # Portable Document Format
    'application/pgp-encrypted' => %w(pgp),
    'application/pgp-signature' => %w(asc sig),
    'application/pics-rules' => %w(prf),
    'application/pkcs10' => %w(p10),
    'application/pkcs7-mime' => %w(p7m p7c),
    'application/pkcs7-signature' => %w(p7s),
    'application/pkix-cert' => %w(cer),
    'application/pkix-crl' => %w(crl),
    'application/pkix-pkipath' => %w(pkipath),
    'application/pkixcmp' => %w(pki),
    'application/pls+xml' => %w(pls),
    'application/postscript' => %w(ps eps epsf epsi), # PostScript
    'application/prs.cww' => %w(cww),
    'application/rdf+xml' => %w(rdf owl xmp), # XML syntax for RDF graphs
    'application/reginfo+xml' => %w(rif),
    'application/relax-ng-compact-syntax' => %w(rnc),
    'application/resource-lists+xml' => %w(rl),
    'application/resource-lists-diff+xml' => %w(rld),
    'application/rls-services+xml' => %w(rs),
    'application/rsd+xml' => %w(rsd),
    'application/rss+xml' => %w(rss),
    'application/rtf' => %w(rtf), # Rich Text Format File
    'application/sbml+xml' => %w(sbml),
    'application/scvp-cv-request' => %w(scq),
    'application/scvp-cv-response' => %w(scs),
    'application/scvp-vp-request' => %w(spq),
    'application/scvp-vp-response' => %w(spp),
    'application/sdp' => %w(sdp),
    'application/sereal' => %w(srl), # Sereal binary serialization format
    'application/set-payment-initiation' => %w(setpay),
    'application/set-registration-initiation' => %w(setreg),
    'application/shf+xml' => %w(shf),
    'application/sldworks' => %w(sldprt sldasm slddrw), # SolidWorks CAD program
    'application/smil+xml' => %w(smi smil sml), # SMIL Multimedia
    'application/sparql-query' => %w(rq),
    'application/sparql-results+xml' => %w(srx),
    'application/srgs' => %w(gram),
    'application/srgs+xml' => %w(grxml),
    'application/ssml+xml' => %w(ssml),
    'application/timestamped-data' => %w(tsd),
    'application/vnd.3gpp.pic-bw-large' => %w(plb),
    'application/vnd.3gpp.pic-bw-small' => %w(psb),
    'application/vnd.3gpp.pic-bw-var' => %w(pvb),
    'application/vnd.3gpp2.tcap' => %w(tcap),
    'application/vnd.3m.post-it-notes' => %w(pwn),
    'application/vnd.accpac.simply.aso' => %w(aso),
    'application/vnd.accpac.simply.imp' => %w(imp),
    'application/vnd.acucobol' => %w(acu),
    'application/vnd.acucorp' => %w(atc acutc),
    'application/vnd.adobe.aftereffects.project' => %w(aep),
    'application/vnd.adobe.aftereffects.template' => %w(aet),
    'application/vnd.adobe.air-application-installer-package+zip' => %w(air),
    'application/vnd.adobe.xdp+xml' => %w(xdp),
    'application/vnd.adobe.xfdf' => %w(xfdf),
    'application/vnd.airzip.filesecure.azf' => %w(azf),
    'application/vnd.airzip.filesecure.azs' => %w(azs),
    'application/vnd.amazon.ebook' => %w(azw),
    'application/vnd.americandynamics.acc' => %w(acc),
    'application/vnd.amiga.ami' => %w(ami),
    'application/vnd.android.package-archive' => %w(apk),
    'application/vnd.anser-web-certificate-issue-initiation' => %w(cii),
    'application/vnd.anser-web-funds-transfer-initiation' => %w(fti),
    'application/vnd.antix.game-component' => %w(atx),
    'application/vnd.apple.installer+xml' => %w(mpkg),
    'application/vnd.apple.keynote' => %w(key),
    'application/vnd.apple.mpegurl' => %w(m3u8),
    'application/vnd.apple.numbers' => %w(numbers),
    'application/vnd.apple.pages' => %w(pages),
    'application/vnd.arastra.swi' => %w(swi),
    'application/vnd.blueice.multipass' => %w(mpm),
    'application/vnd.bmi' => %w(bmi),
    'application/vnd.businessobjects' => %w(rep),
    'application/vnd.chemdraw+xml' => %w(cdxml),
    'application/vnd.chipnuts.karaoke-mmd' => %w(mmd),
    'application/vnd.cinderella' => %w(cdy),
    'application/vnd.claymore' => %w(cla),
    'application/vnd.clonk.c4group' => %w(c4g c4d c4f c4p c4u),
    'application/vnd.commonspace' => %w(csp),
    'application/vnd.contact.cmsg' => %w(cdbcmsg),
    'application/vnd.cosmocaller' => %w(cmc),
    'application/vnd.crick.clicker' => %w(clkx),
    'application/vnd.crick.clicker.keyboard' => %w(clkk),
    'application/vnd.crick.clicker.palette' => %w(clkp),
    'application/vnd.crick.clicker.template' => %w(clkt),
    'application/vnd.crick.clicker.wordbank' => %w(clkw),
    'application/vnd.criticaltools.wbs+xml' => %w(wbs),
    'application/vnd.ctc-posml' => %w(pml),
    'application/vnd.cups-ppd' => %w(ppd),
    'application/vnd.curl.car' => %w(car),
    'application/vnd.curl.pcurl' => %w(pcurl),
    'application/vnd.data-vision.rdz' => %w(rdz),
    'application/vnd.denovo.fcselayout-link' => %w(fe_launch),
    'application/vnd.dna' => %w(dna),
    'application/vnd.dolby.mlp' => %w(mlp),
    'application/vnd.dpgraph' => %w(dpg),
    'application/vnd.dreamfactory' => %w(dfac),
    'application/vnd.dynageo' => %w(geo),
    'application/vnd.ecowin.chart' => %w(mag),
    'application/vnd.enliven' => %w(nml),
    'application/vnd.epson.esf' => %w(esf),
    'application/vnd.epson.msf' => %w(msf),
    'application/vnd.epson.quickanime' => %w(qam),
    'application/vnd.epson.salt' => %w(slt),
    'application/vnd.epson.ssf' => %w(ssf),
    'application/vnd.eszigno3+xml' => %w(es3 et3),
    'application/vnd.etsi.asic-e+zip' => %w(asice), # Extended Associated Signature Container
    'application/vnd.etsi.asic-s+zip' => %w(asics), # Simple Associated Signature Container
    'application/vnd.ezpix-album' => %w(ez2),
    'application/vnd.ezpix-package' => %w(ez3),
    'application/vnd.fdf' => %w(fdf), # Forms Data Format
    'application/vnd.fdsn.mseed' => %w(mseed),
    'application/vnd.fdsn.seed' => %w(seed dataless),
    'application/vnd.flographit' => %w(gph),
    'application/vnd.fluxtime.clip' => %w(ftc),
    'application/vnd.framemaker' => %w(fm frame maker book),
    'application/vnd.frogans.fnc' => %w(fnc),
    'application/vnd.frogans.ltf' => %w(ltf),
    'application/vnd.fsc.weblaunch' => %w(fsc),
    'application/vnd.fujitsu.oasys' => %w(oas),
    'application/vnd.fujitsu.oasys2' => %w(oa2),
    'application/vnd.fujitsu.oasys3' => %w(oa3),
    'application/vnd.fujitsu.oasysgp' => %w(fg5),
    'application/vnd.fujitsu.oasysprs' => %w(bh2),
    'application/vnd.fujixerox.ddd' => %w(ddd),
    'application/vnd.fujixerox.docuworks' => %w(xdw),
    'application/vnd.fujixerox.docuworks.binder' => %w(xbd),
    'application/vnd.fuzzysheet' => %w(fzs),
    'application/vnd.genomatix.tuxedo' => %w(txd),
    'application/vnd.geogebra.file' => %w(ggb),
    'application/vnd.geogebra.tool' => %w(ggt),
    'application/vnd.geometry-explorer' => %w(gex gre),
    'application/vnd.gmx' => %w(gmx),
    'application/vnd.google-earth.kml+xml' => %w(kml), # Keyhole Markup Language
    'application/vnd.google-earth.kmz' => %w(kmz),
    'application/vnd.grafeq' => %w(gqf gqs),
    'application/vnd.groove-account' => %w(gac),
    'application/vnd.groove-help' => %w(ghf),
    'application/vnd.groove-identity-message' => %w(gim),
    'application/vnd.groove-injector' => %w(grv),
    'application/vnd.groove-tool-message' => %w(gtm),
    'application/vnd.groove-tool-template' => %w(tpl),
    'application/vnd.groove-vcard' => %w(vcg),
    'application/vnd.handheld-entertainment+xml' => %w(zmm),
    'application/vnd.hbci' => %w(hbci),
    'application/vnd.hhe.lesson-player' => %w(les),
    'application/vnd.hp-hpgl' => %w(hpgl),
    'application/vnd.hp-hpid' => %w(hpid),
    'application/vnd.hp-hps' => %w(hps),
    'application/vnd.hp-jlyt' => %w(jlt),
    'application/vnd.hp-pcl' => %w(pcl),
    'application/vnd.hp-pclxl' => %w(pclxl),
    'application/vnd.hydrostatix.sof-data' => %w(sfd-hdstx),
    'application/vnd.hzn-3d-crossword' => %w(x3d),
    'application/vnd.ibm.minipay' => %w(mpy),
    'application/vnd.ibm.modcap' => %w(afp listafp list3820),
    'application/vnd.ibm.rights-management' => %w(irm),
    'application/vnd.ibm.secure-container' => %w(sc),
    'application/vnd.iccprofile' => %w(icc icm),
    'application/vnd.igloader' => %w(igl),
    'application/vnd.immervision-ivp' => %w(ivp),
    'application/vnd.immervision-ivu' => %w(ivu),
    'application/vnd.intercon.formnet' => %w(xpw xpx),
    'application/vnd.intu.qbo' => %w(qbo),
    'application/vnd.intu.qfx' => %w(qfx),
    'application/vnd.iptc.g2.newsmessage+xml' => %w(nar), # XML syntax for IPTC NewsMessages
    'application/vnd.ipunplugged.rcprofile' => %w(rcprofile),
    'application/vnd.irepository.package+xml' => %w(irp),
    'application/vnd.is-xpr' => %w(xpr),
    'application/vnd.jam' => %w(jam),
    'application/vnd.java.hprof ' => %w(hprof), # Java hprof text file
    'application/vnd.java.hprof.text' => %w(hprof.txt), # Java hprof text file
    'application/vnd.jcp.javame.midlet-rms' => %w(rms),
    'application/vnd.jisp' => %w(jisp),
    'application/vnd.joost.joda-archive' => %w(joda),
    'application/vnd.kahootz' => %w(ktz ktr),
    'application/vnd.kde.karbon' => %w(karbon),
    'application/vnd.kde.kchart' => %w(chrt), # KChart File
    'application/vnd.kde.kformula' => %w(kfo),
    'application/vnd.kde.kivio' => %w(flw),
    'application/vnd.kde.kontour' => %w(kon),
    'application/vnd.kde.kpresenter' => %w(kpr kpt), # KPresenter File
    'application/vnd.kde.kspread' => %w(ksp), # KSpread File
    'application/vnd.kde.kword' => %w(kwd kwt), # KWord File
    'application/vnd.kenameaapp' => %w(htke),
    'application/vnd.kidspiration' => %w(kia),
    'application/vnd.kinar' => %w(kne knp),
    'application/vnd.koan' => %w(skp skd skt skm), # SSEYO Koan File
    'application/vnd.kodak-descriptor' => %w(sse),
    'application/vnd.llamagraphics.life-balance.desktop' => %w(lbd),
    'application/vnd.llamagraphics.life-balance.exchange+xml' => %w(lbe),
    'application/vnd.lotus-1-2-3' => %w(wk1 wk2 wk3 wk4 123), # Lotus 1-2-3
    'application/vnd.lotus-1-2-3;version=2' => %w(wk1 wk2), # Lotus 1-2-3, version 2
    'application/vnd.lotus-1-2-3;version=3' => %w(wk3), # Lotus 1-2-3, version 3
    'application/vnd.lotus-1-2-3;version=4' => %w(wk4), # Lotus 1-2-3, version 4-5
    'application/vnd.lotus-1-2-3;version=97+9.x' => %w(123), # Lotus 1-2-3, version 97/9.x
    'application/vnd.lotus-approach' => %w(apr),
    'application/vnd.lotus-freelance' => %w(pre),
    'application/vnd.lotus-notes' => %w(nsf),
    'application/vnd.lotus-organizer' => %w(org),
    'application/vnd.lotus-wordpro' => %w(lwp),
    'application/vnd.macports.portpkg' => %w(portpkg),
    'application/vnd.mcd' => %w(mcd),
    'application/vnd.medcalcdata' => %w(mc1),
    'application/vnd.mediastation.cdkey' => %w(cdkey),
    'application/vnd.mfer' => %w(mwf),
    'application/vnd.mfmp' => %w(mfm),
    'application/vnd.micrografx.flo' => %w(flo),
    'application/vnd.micrografx.igx' => %w(igx),
    'application/vnd.mif' => %w(mif), # FrameMaker Interchange Format
    'application/vnd.mindjet.mindmanager' => %w(mmp mmap mmpt mmat mmmp mmas), # MindManager
    'application/vnd.mobius.daf' => %w(daf),
    'application/vnd.mobius.dis' => %w(dis),
    'application/vnd.mobius.mbk' => %w(mbk),
    'application/vnd.mobius.mqy' => %w(mqy),
    'application/vnd.mobius.msl' => %w(msl),
    'application/vnd.mobius.plc' => %w(plc),
    'application/vnd.mobius.txf' => %w(txf),
    'application/vnd.mophun.application' => %w(mpn),
    'application/vnd.mophun.certificate' => %w(mpc),
    'application/vnd.mozilla.xul+xml' => %w(xul),
    'application/vnd.ms-artgalry' => %w(cil),
    'application/vnd.ms-cab-compressed' => %w(cab),
    'application/vnd.ms-excel' => %w(xls xlm xla xlc xlt xlw xll xld), # Microsoft Excel Spreadsheet
    'application/vnd.ms-excel.addin.macroenabled.12' => %w(xlam), # Office Open XML Workbook Add-in (macro-enabled)
    'application/vnd.ms-excel.sheet.binary.macroenabled.12' => %w(xlsb), # Microsoft Excel 2007 Binary Spreadsheet
    'application/vnd.ms-excel.sheet.macroenabled.12' => %w(xlsm), # Office Open XML Workbook (macro-enabled)
    'application/vnd.ms-excel.template.macroenabled.12' => %w(xltm), # Office Open XML Workbook Template (macro-enabled)
    'application/vnd.ms-fontobject' => %w(eot),
    'application/vnd.ms-htmlhelp' => %w(chm),
    'application/vnd.ms-ims' => %w(ims),
    'application/vnd.ms-lrm' => %w(lrm),
    'application/vnd.ms-outlook' => %w(msg), # Microsoft Outlook Message
    'application/vnd.ms-outlook-pst' => %w(pst ost), # Outlook Personal Folders File Format
    'application/vnd.ms-pki.seccat' => %w(cat),
    'application/vnd.ms-pki.stl' => %w(stl),
    'application/vnd.ms-powerpoint' => %w(ppt ppz pps pot ppa), # Microsoft Powerpoint Presentation
    'application/vnd.ms-powerpoint.addin.macroenabled.12' => %w(ppam), # Office Open XML Presentation Add-in (macro-enabled)
    'application/vnd.ms-powerpoint.presentation.macroenabled.12' => %w(pptm), # Office Open XML Presentation (macro-enabled)
    'application/vnd.ms-powerpoint.slide.macroenabled.12' => %w(sldm),
    'application/vnd.ms-powerpoint.slideshow.macroenabled.12' => %w(ppsm), # Office Open XML Presentation Slideshow (macro-enabled)
    'application/vnd.ms-powerpoint.template.macroenabled.12' => %w(potm),
    'application/vnd.ms-project' => %w(mpp mpt),
    'application/vnd.ms-visio.drawing' => %w(vsdx), # Office Open XML Visio Drawing (macro-free)
    'application/vnd.ms-visio.drawing.macroEnabled.12' => %w(vsdm), # Office Open XML Visio Drawing (macro-enabled)
    'application/vnd.ms-visio.stencil' => %w(vssx), # Office Open XML Visio Stencil (macro-free)
    'application/vnd.ms-visio.stencil.macroEnabled.12' => %w(vssm), # Office Open XML Visio Stencil (macro-enabled)
    'application/vnd.ms-visio.template' => %w(vstx), # Office Open XML Visio Template (macro-free)
    'application/vnd.ms-visio.template.macroEnabled.12' => %w(vstm), # Office Open XML Visio Template (macro-enabled)
    'application/vnd.ms-word.document.macroenabled.12' => %w(docm), # Office Open XML Document (macro-enabled)
    'application/vnd.ms-word.template.macroenabled.12' => %w(dotm), # Office Open XML Document Template (macro-enabled)
    'application/vnd.ms-works' => %w(wps wks wcm wdb),
    'application/vnd.ms-wpl' => %w(wpl),
    'application/vnd.ms-xpsdocument' => %w(xps oxps), # Open XML Paper Specification
    'application/vnd.mseq' => %w(mseq),
    'application/vnd.musician' => %w(mus),
    'application/vnd.muvee.style' => %w(msty),
    'application/vnd.neurolanguage.nlu' => %w(nlu),
    'application/vnd.noblenet-directory' => %w(nnd),
    'application/vnd.noblenet-sealer' => %w(nns),
    'application/vnd.noblenet-web' => %w(nnw),
    'application/vnd.nokia.n-gage.data' => %w(ngdat),
    'application/vnd.nokia.n-gage.symbian.install' => %w(n-gage),
    'application/vnd.nokia.radio-preset' => %w(rpst),
    'application/vnd.nokia.radio-presets' => %w(rpss),
    'application/vnd.novadigm.edm' => %w(edm),
    'application/vnd.novadigm.edx' => %w(edx),
    'application/vnd.novadigm.ext' => %w(ext),
    'application/vnd.oasis.opendocument.base' => %w(odb),
    'application/vnd.oasis.opendocument.chart' => %w(odc), # OpenDocument v1.0: Chart document
    'application/vnd.oasis.opendocument.chart-template' => %w(otc), # OpenDocument v1.0: Chart document used as template
    'application/vnd.oasis.opendocument.flat.presentation' => %w(fodp), # OpenDocument v1.0: Flat Presentation document
    'application/vnd.oasis.opendocument.flat.spreadsheet' => %w(fods), # OpenDocument v1.0: Flat Spreadsheet document
    'application/vnd.oasis.opendocument.flat.text' => %w(fodt), # OpenDocument v1.0: Flat Text document
    'application/vnd.oasis.opendocument.formula' => %w(odf), # OpenDocument v1.0: Formula document
    'application/vnd.oasis.opendocument.formula-template' => %w(odft), # OpenDocument v1.0: Formula document used as template
    'application/vnd.oasis.opendocument.graphics' => %w(odg), # OpenDocument v1.0: Graphics document (Drawing)
    'application/vnd.oasis.opendocument.graphics-template' => %w(otg), # OpenDocument v1.0: Graphics document used as template
    'application/vnd.oasis.opendocument.image' => %w(odi), # OpenDocument v1.0: Image document
    'application/vnd.oasis.opendocument.image-template' => %w(oti), # OpenDocument v1.0: Image document used as template
    'application/vnd.oasis.opendocument.presentation' => %w(odp), # OpenDocument v1.0: Presentation document
    'application/vnd.oasis.opendocument.presentation-template' => %w(otp), # OpenDocument v1.0: Presentation document used as template
    'application/vnd.oasis.opendocument.spreadsheet' => %w(ods), # OpenDocument v1.0: Spreadsheet document
    'application/vnd.oasis.opendocument.spreadsheet-template' => %w(ots), # OpenDocument v1.0: Spreadsheet document used as template
    'application/vnd.oasis.opendocument.text' => %w(odt), # OpenDocument v1.0: Text document
    'application/vnd.oasis.opendocument.text-master' => %w(otm), # OpenDocument v1.0: Global Text document
    'application/vnd.oasis.opendocument.text-template' => %w(ott), # OpenDocument v1.0: Text document used as template
    'application/vnd.oasis.opendocument.text-web' => %w(oth), # OpenDocument v1.0: Text document used as template for HTML documents
    'application/vnd.olpc-sugar' => %w(xo),
    'application/vnd.oma.dd2+xml' => %w(dd2),
    'application/vnd.openofficeorg.autotext' => %w(bau),
    'application/vnd.openofficeorg.extension' => %w(oxt),
    'application/vnd.openxmlformats-officedocument.presentationml.presentation' => %w(pptx thmx), # Office Open XML Presentation
    'application/vnd.openxmlformats-officedocument.presentationml.slide' => %w(sldx),
    'application/vnd.openxmlformats-officedocument.presentationml.slideshow' => %w(ppsx), # Office Open XML Presentation Slideshow
    'application/vnd.openxmlformats-officedocument.presentationml.template' => %w(potx), # Office Open XML Presentation Template
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => %w(xlsx), # Office Open XML Workbook
    'application/vnd.openxmlformats-officedocument.spreadsheetml.template' => %w(xltx), # Office Open XML Workbook Template
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => %w(docx), # Office Open XML Document
    'application/vnd.openxmlformats-officedocument.wordprocessingml.template' => %w(dotx), # Office Open XML Document Template
    'application/vnd.osgi.dp' => %w(dp),
    'application/vnd.palm' => %w(pqa oprc),
    'application/vnd.pg.format' => %w(str),
    'application/vnd.pg.osasli' => %w(ei6),
    'application/vnd.picsel' => %w(efif),
    'application/vnd.pocketlearn' => %w(plf),
    'application/vnd.powerbuilder6' => %w(pbd),
    'application/vnd.previewsystems.box' => %w(box),
    'application/vnd.proteus.magazine' => %w(mgz),
    'application/vnd.publishare-delta-tree' => %w(qps),
    'application/vnd.pvi.ptid1' => %w(ptid),
    'application/vnd.quark.quarkxpress' => %w(qxd qxt qwd qwt qxl qxb),
    'application/vnd.recordare.musicxml' => %w(mxl),
    'application/vnd.recordare.musicxml+xml' => %w(musicxml),
    'application/vnd.rim.cod' => %w(cod),
    'application/vnd.rn-realmedia' => %w(rm),
    'application/vnd.route66.link66+xml' => %w(link66),
    'application/vnd.seemail' => %w(see),
    'application/vnd.sema' => %w(sema),
    'application/vnd.semd' => %w(semd),
    'application/vnd.semf' => %w(semf),
    'application/vnd.shana.informed.formdata' => %w(ifm),
    'application/vnd.shana.informed.formtemplate' => %w(itp),
    'application/vnd.shana.informed.interchange' => %w(iif),
    'application/vnd.shana.informed.package' => %w(ipk),
    'application/vnd.simtech-mindmapper' => %w(twd twds),
    'application/vnd.smaf' => %w(mmf),
    'application/vnd.smart.teacher' => %w(teacher),
    'application/vnd.solent.sdkm+xml' => %w(sdkm sdkd),
    'application/vnd.spotfire.dxp' => %w(dxp),
    'application/vnd.spotfire.sfs' => %w(sfs),
    'application/vnd.stardivision.calc' => %w(sdc),
    'application/vnd.stardivision.draw' => %w(sda),
    'application/vnd.stardivision.impress' => %w(sdd),
    'application/vnd.stardivision.math' => %w(smf),
    'application/vnd.stardivision.writer' => %w(sdw),
    'application/vnd.stardivision.writer-global' => %w(sgl),
    'application/vnd.sun.xml.calc' => %w(sxc),
    'application/vnd.sun.xml.calc.template' => %w(stc),
    'application/vnd.sun.xml.draw' => %w(sxd),
    'application/vnd.sun.xml.draw.template' => %w(std),
    'application/vnd.sun.xml.impress' => %w(sxi),
    'application/vnd.sun.xml.impress.template' => %w(sti),
    'application/vnd.sun.xml.math' => %w(sxm),
    'application/vnd.sun.xml.writer' => %w(sxw), # OpenOffice v1.0: Writer Document
    'application/vnd.sun.xml.writer.global' => %w(sxg),
    'application/vnd.sun.xml.writer.template' => %w(stw),
    'application/vnd.sus-calendar' => %w(sus susp),
    'application/vnd.svd' => %w(svd),
    'application/vnd.symbian.install' => %w(sis sisx),
    'application/vnd.syncml+xml' => %w(xsm),
    'application/vnd.syncml.dm+wbxml' => %w(bdm),
    'application/vnd.syncml.dm+xml' => %w(xdm),
    'application/vnd.tao.intent-module-archive' => %w(tao),
    'application/vnd.tcpdump.pcap' => %w(pcap cap dmp), # TCPDump pcap packet capture
    'application/vnd.tmobile-livetv' => %w(tmo),
    'application/vnd.trid.tpt' => %w(tpt),
    'application/vnd.triscape.mxs' => %w(mxs),
    'application/vnd.trueapp' => %w(tra),
    'application/vnd.ufdl' => %w(ufd ufdl),
    'application/vnd.uiq.theme' => %w(utz),
    'application/vnd.umajin' => %w(umj),
    'application/vnd.unity' => %w(unityweb),
    'application/vnd.uoml+xml' => %w(uoml),
    'application/vnd.vcx' => %w(vcx),
    'application/vnd.visio' => %w(vsd vst vss vsw), # Microsoft Visio Diagram
    'application/vnd.visionary' => %w(vis),
    'application/vnd.vsf' => %w(vsf),
    'application/vnd.wap.wbxml' => %w(wbxml),
    'application/vnd.wap.wmlc' => %w(wmlc), # Compiled WML Document
    'application/vnd.wap.wmlscriptc' => %w(wmlsc), # Compiled WML Script
    'application/vnd.webturbo' => %w(wtb),
    'application/vnd.wolfram.wl' => %w(wl), # Wolfram Language
    'application/vnd.wordperfect' => %w(wpd wp wp5 wp6 w60 wp61 wpt), # WordPerfect - Corel Word Processing
    'application/vnd.wqd' => %w(wqd),
    'application/vnd.wt.stf' => %w(stf),
    'application/vnd.xara' => %w(xar),
    'application/vnd.xfdl' => %w(xfdl),
    'application/vnd.yamaha.hv-dic' => %w(hvd),
    'application/vnd.yamaha.hv-script' => %w(hvs),
    'application/vnd.yamaha.hv-voice' => %w(hvp),
    'application/vnd.yamaha.openscoreformat' => %w(osf),
    'application/vnd.yamaha.openscoreformat.osfpvg+xml' => %w(osfpvg),
    'application/vnd.yamaha.smaf-audio' => %w(saf),
    'application/vnd.yamaha.smaf-phrase' => %w(spf),
    'application/vnd.yellowriver-custom-menu' => %w(cmp),
    'application/vnd.zul' => %w(zir zirz),
    'application/vnd.zzazz.deck+xml' => %w(zaz),
    'application/voicexml+xml' => %w(vxml),
    'application/warc' => %w(warc), # WARC
    'application/wasm' => %w(wasm), # Web Assembly
    'application/winhlp' => %w(hlp),
    'application/wsdl+xml' => %w(wsdl),
    'application/wspolicy+xml' => %w(wspolicy),
    'application/x-7z-compressed' => %w(7z), # 7-zip archive
    'application/x-abiword' => %w(abw),
    'application/x-ace-compressed' => %w(ace),
    'application/x-adobe-indesign' => %w(indd), # Adobe InDesign document
    'application/x-adobe-indesign-interchange' => %w(inx), # Adobe InDesign Interchange format
    'application/x-apple-diskimage' => %w(dmg),
    'application/x-appleworks' => %w(cwk),
    'application/x-archive' => %w(ar a),
    'application/x-arj' => %w(arj),
    'application/x-authorware-bin' => %w(aab x32 u32 vox),
    'application/x-authorware-map' => %w(aam),
    'application/x-authorware-seg' => %w(aas),
    'application/x-axcrypt' => %w(axx), # AxCrypt
    'application/x-bat' => %w(bat cmd), # Windows Batch / Command File
    'application/x-bcpio' => %w(bcpio),
    'application/x-bibtex-text-file' => %w(bib bibtex),
    'application/x-bittorrent' => %w(torrent),
    'application/x-brotli' => %w(br brotli),
    'application/x-bzip' => %w(bz tbz),
    'application/x-bzip2' => %w(bz2 tbz2 boz), # Bzip 2 UNIX Compressed File
    'application/x-cdlink' => %w(vcd), # Virtual CD-ROM CD Image File
    'application/x-chat' => %w(chat),
    'application/x-chess-pgn' => %w(pgn),
    'application/x-chrome-package' => %w(crx), # Chrome Extension Package
    'application/x-compress' => %w(z),
    'application/x-corelpresentations' => %w(shw),
    'application/x-cpio' => %w(cpio), # UNIX CPIO Archive
    'application/x-csh' => %w(csh tcsh),
    'application/x-dbf' => %w(dbf dbase dbase3),
    'application/x-debian-package' => %w(deb udeb),
    'application/x-dex' => %w(dex), # Dalvik Executable Format
    'application/x-director' => %w(dir dcr dxr cst cct cxt w3d fgd swa), # Shockwave Movie
    'application/x-doom' => %w(wad),
    'application/x-dosexec' => %w(exe), # DOS/Windows executable (EXE)
    'application/x-dtbncx+xml' => %w(ncx),
    'application/x-dtbook+xml' => %w(dtb),
    'application/x-dtbresource+xml' => %w(res),
    'application/x-dvi' => %w(dvi), # TeX Device Independent Document
    'application/x-elc' => %w(elc), # Emacs Lisp bytecode
    'application/x-endnote-refer' => %w(enw enr),
    'application/x-erdas-hfa' => %w(hfa),
    'application/x-fictionbook+xml' => %w(fb2), # FictionBook document
    'application/x-filemaker' => %w(fp7), # FileMaker Pro 7
    'application/x-font-adobe-metric' => %w(afm acfm amfm), # Adobe Font Metric
    'application/x-font-bdf' => %w(bdf),
    'application/x-font-ghostscript' => %w(gsf),
    'application/x-font-linux-psf' => %w(psf),
    'application/x-font-otf' => %w(otf), # OpenType Font
    'application/x-font-pcf' => %w(pcf),
    'application/x-font-printer-metric' => %w(pfm), # Printer Font Metric
    'application/x-font-snf' => %w(snf),
    'application/x-font-ttf' => %w(ttf ttc), # TrueType Font
    'application/x-font-type1' => %w(pfa pfb),
    'application/x-futuresplash' => %w(spl), # Macromedia FutureSplash File
    'application/x-gnucash' => %w(gnucash),
    'application/x-gnumeric' => %w(gnumeric),
    'application/x-grib' => %w(grb grb1 grb2), # General Regularly-distributed Information in Binary form
    'application/x-gtar' => %w(gtar), # GNU tar Compressed File Archive (GNU Tape Archive)
    'application/x-hdf' => %w(hdf he5 h5), # Hierarchical Data Format File
    'application/x-ibooks+zip' => %w(ibooks), # Apple iBooks Author publication format
    'application/x-internet-archive' => %w(arc), # ARC
    'application/x-iso9660-image' => %w(iso), # ISO 9660 CD-ROM filesystem data
    'application/x-itunes-ipa' => %w(ipa), # Apple iOS IPA AppStore file
    'application/x-java-jnilib' => %w(jnilib), # Java Native Library for OSX
    'application/x-java-jnlp-file' => %w(jnlp),
    'application/x-java-pack200' => %w(pack),
    'application/x-killustrator' => %w(kil), # KIllustrator File
    'application/x-latex' => %w(latex), # LaTeX Source Document
    'application/x-lz4' => %w(lz4), # Second match Legacy Frame
    'application/x-lzip' => %w(lz), # Lzip (LZMA) compressed archive
    'application/x-lzma' => %w(lzma), # LZMA compressed archive
    'application/x-matlab-data' => %w(mat),
    'application/x-memgraph' => %w(memgraph), # Apple Xcode Memgraph
    'application/x-mobipocket-ebook' => %w(prc mobi), # Mobipocket Ebook
    'application/x-ms-application' => %w(application),
    'application/x-ms-asx' => %w(asx), # Windows Media Metafile
    'application/x-ms-installer' => %w(msi msp mst), # Microsoft Windows Installer
    'application/x-ms-wmd' => %w(wmd),
    'application/x-ms-wmz' => %w(wmz),
    'application/x-ms-xbap' => %w(xbap),
    'application/x-msaccess' => %w(mdb),
    'application/x-msbinder' => %w(obd),
    'application/x-mscardfile' => %w(crd),
    'application/x-msclip' => %w(clp),
    'application/x-msdownload' => %w(dll com),
    'application/x-msmediaview' => %w(mvb m13 m14),
    'application/x-msmoney' => %w(mny),
    'application/x-mspublisher' => %w(pub),
    'application/x-msschedule' => %w(scd),
    'application/x-msterminal' => %w(trm),
    'application/x-mswrite' => %w(wri),
    'application/x-mysql-misam-compressed-index' => %w(myi), # MySQL MISAM Compressed Index
    'application/x-mysql-misam-data' => %w(myd), # MySQL MISAM Data
    'application/x-netcdf' => %w(nc cdf),
    'application/x-parquet' => %w(parquet),
    'application/x-pkcs12' => %w(p12 pfx),
    'application/x-pkcs7-certificates' => %w(p7b spc),
    'application/x-pkcs7-certreqresp' => %w(p7r),
    'application/x-project' => %w(mpx),
    'application/x-prt' => %w(prt),
    'application/x-quattro-pro' => %w(wq1 wq2 wkq qpw wb1 wb2 wb3), #        Quattro Pro - Corel Spreadsheet (part of WordPerfect Office suite)     
    'application/x-quattro-pro;version=1+5' => %w(wb1), # Quattro Pro for Windows, version 1, 5
    'application/x-quattro-pro;version=1-4' => %w(wq1 wkq), # Quattro Pro for DOS, version 1-4
    'application/x-quattro-pro;version=5' => %w(wq2 wkq), # Quattro Pro for DOS, version 5
    'application/x-quattro-pro;version=6' => %w(wb2), # Quattro Pro for Windows, version 6
    'application/x-rar-compressed' => %w(rar), # RAR archive
    'application/x-roxio-toast' => %w(toast),
    'application/x-rpm' => %w(rpm), # RedHat Package Manager
    'application/x-sas' => %w(sas), # SAS Program
    'application/x-sas-access' => %w(sa7 sas7bacs), # SAS Access Descriptor
    'application/x-sas-audit' => %w(st7 sas7baud), # SAS Audit
    'application/x-sas-backup' => %w(sas7bbak), # SAS Backup
    'application/x-sas-catalog' => %w(sc7 sas7bcat), # SAS Catalog
    'application/x-sas-data' => %w(sd7 sas7bdat), # SAS Data Set
    'application/x-sas-data-index' => %w(si7 sas7bndx), # SAS Data Set Index
    'application/x-sas-data-v6' => %w(sd2), # SAS v6 Data Set
    'application/x-sas-dmdb' => %w(s7m sas7bdmd), # SAS DMDB Data Mining Database File
    'application/x-sas-fdb' => %w(sf7 sas7bfdb), # SAS FDB Consolidation Database File
    'application/x-sas-itemstor' => %w(sr7 sas7bitm), # SAS Item Store (ItemStor) File
    'application/x-sas-mddb' => %w(sm7 sas7bmdb), # SAS MDDB Multi-Dimensional Database File
    'application/x-sas-program-data' => %w(ss7 sas7bpgm), # SAS Stored Program (DATA Step)
    'application/x-sas-putility' => %w(sp7 sas7bput), # SAS Permanent Utility
    'application/x-sas-transport' => %w(stx), # SAS Transport File
    'application/x-sas-utility' => %w(su7 sas7butl), # SAS Utility
    'application/x-sas-view' => %w(sv7 sas7bvew), # SAS Data Set View
    'application/x-sas-xport' => %w(xpt xport), # SAS XPORT Transfer File
    'application/x-sfdu' => %w(sfdu), # Standard Formatted Data Units (SFDUs) data
    'application/x-sh' => %w(sh bash), # UNIX/LINUX Shell Script
    'application/x-shapefile' => %w(shp), # ESRI Shapefiles
    'application/x-shar' => %w(shar),
    'application/x-shockwave-flash' => %w(swf), # Adobe Flash
    'application/x-silverlight-app' => %w(xap),
    'application/x-snappy-framed' => %w(sz), # Snappy Framed
    'application/x-staroffice-template' => %w(vor),
    'application/x-stata-do' => %w(do), # Stata DTA Script
    'application/x-stata-dta' => %w(dta), # Stata DTA Dataset
    'application/x-stuffit' => %w(sit),
    'application/x-stuffitx' => %w(sitx),
    'application/x-sv4cpio' => %w(sv4cpio),
    'application/x-sv4crc' => %w(sv4crc),
    'application/x-tar' => %w(tar),
    'application/x-tex' => %w(tex), # TeX Source
    'application/x-tex-tfm' => %w(tfm),
    'application/x-texinfo' => %w(texinfo texi),
    'application/x-tika-java-enterprise-archive' => %w(ear),
    'application/x-tika-java-web-archive' => %w(war),
    'application/x-tika-msworks-spreadsheet' => %w(xlr),
    'application/x-uc2-compressed' => %w(uc2),
    'application/x-ustar' => %w(ustar),
    'application/x-vmdk' => %w(vmdk), # Virtual Disk Format
    'application/x-wais-source' => %w(src),
    'application/x-webarchive' => %w(webarchive),
    'application/x-x509-cert' => %w(crt),
    'application/x-x509-cert;format=der' => %w(der),
    'application/x-x509-cert;format=pem' => %w(pem),
    'application/x-xfig' => %w(fig),
    'application/x-xliff+xml' => %w(xlf xliff), # XLIFF 1.2 document
    'application/x-xliff+zip' => %w(xlz), # XLZ Archive
    'application/x-xmind' => %w(xmind), # XMind Pro
    'application/x-xpinstall' => %w(xpi),
    'application/x-xz' => %w(xz),
    'application/x-zoo' => %w(zoo),
    'application/xenc+xml' => %w(xenc),
    'application/xhtml+xml' => %w(xhtml xhtml2 xht),
    'application/xml' => %w(xml xsl xsd), # Extensible Markup Language
    'application/xml-dtd' => %w(dtd), # XML Document Type Definition
    'application/xop+xml' => %w(xop),
    'application/xquery' => %w(xq xquery), # XQuery source code
    'application/xslfo+xml' => %w(xslfo fo), # XSL Format
    'application/xslt+xml' => %w(xslt), # XSL Transformations
    'application/xspf+xml' => %w(xspf), # XML Shareable Playlist Format
    'application/xv+xml' => %w(mxml xhvml xvml xvm),
    'application/zip' => %w(zip), # Compressed Archive File
    'application/zstd' => %w(zstd), # https://tools.ietf.org/id/draft-kucherawy-dispatch-zstd-01.html
    'audio/ac3' => %w(ac3), # Dolby Digital Audio Compression File
    'audio/adpcm' => %w(adp),
    'audio/amr' => %w(amr),
    'audio/basic' => %w(au snd), # uLaw/AU Audio File
    'audio/midi' => %w(mid midi kar rmi), # Musical Instrument Digital Interface
    'audio/mp4' => %w(mp4a m4a m4b),
    'audio/mpeg' => %w(mpga mp2 mp2a mp3 m2a m3a), # MPEG-1 Audio Layer 3
    'audio/ogg' => %w(oga), # Ogg Vorbis Audio
    'audio/opus' => %w(opus), # Ogg Opus Codec Compressed WAV File
    'audio/speex' => %w(spx), # Ogg Speex Codec Compressed WAV File
    'audio/vnd.adobe.soundbooth' => %w(asnd),
    'audio/vnd.digital-winds' => %w(eol),
    'audio/vnd.dts' => %w(dts),
    'audio/vnd.dts.hd' => %w(dtshd),
    'audio/vnd.lucent.voice' => %w(lvp),
    'audio/vnd.ms-playready.media.pya' => %w(pya),
    'audio/vnd.nuera.ecelp4800' => %w(ecelp4800),
    'audio/vnd.nuera.ecelp7470' => %w(ecelp7470),
    'audio/vnd.nuera.ecelp9600' => %w(ecelp9600),
    'audio/vnd.wave' => %w(wav),
    'audio/vorbis' => %w(ogg), # Ogg Vorbis Codec Compressed WAV File
    'audio/x-aac' => %w(aac),
    'audio/x-aiff' => %w(aif aiff aifc), # Audio Interchange File Format
    'audio/x-caf' => %w(caf), # com.apple.coreaudio-format
    'audio/x-flac' => %w(flac), # Free Lossless Audio Codec
    'audio/x-matroska' => %w(mka),
    'audio/x-mod' => %w(mod),
    'audio/x-mpegurl' => %w(m3u), # MP3 Playlist File
    'audio/x-ms-wax' => %w(wax),
    'audio/x-ms-wma' => %w(wma),
    'audio/x-pn-realaudio' => %w(ram ra), # Real Audio
    'audio/x-pn-realaudio-plugin' => %w(rmp), # RealMedia Player Plug-in
    'chemical/x-cdx' => %w(cdx),
    'chemical/x-cif' => %w(cif),
    'chemical/x-cmdf' => %w(cmdf),
    'chemical/x-cml' => %w(cml),
    'chemical/x-csml' => %w(csml),
    'chemical/x-pdb' => %w(pdb), # Brookhaven Protein Databank File
    'chemical/x-xyz' => %w(xyz),
    'font/woff' => %w(woff),
    'font/woff2' => %w(woff2),
    'image/aces' => %w(exr), # ACES Image Container File
    'image/avif' => %w(avif), # AV1 Image File
    'image/bmp' => %w(bmp dib), # Windows bitmap
    'image/cgm' => %w(cgm), # Computer Graphics Metafile
    'image/emf' => %w(emf), # Enhanced Metafile
    'image/g3fax' => %w(g3),
    'image/gif' => %w(gif), # Graphics Interchange Format
    'image/heic' => %w(heic),
    'image/heif' => %w(heif),
    'image/icns' => %w(icns), # Apple Icon Image Format
    'image/ief' => %w(ief),
    'image/jp2' => %w(jp2), # JPEG 2000 Part 1 (JP2)
    'image/jpeg' => %w(jpg jpeg jpe jif jfif jfi), # Joint Photographic Experts Group
    'image/jpm' => %w(jpm jpgm), # JPEG 2000 Part 6 (JPM)
    'image/jpx' => %w(jpf), # JPEG 2000 Part 2 (JPX)
    'image/nitf' => %w(ntf nitf),
    'image/png' => %w(png), # Portable Network Graphics
    'image/prs.btif' => %w(btif),
    'image/svg+xml' => %w(svg svgz), # Scalable Vector Graphics
    'image/tiff' => %w(tiff tif), # Tagged Image File Format
    'image/vnd.adobe.photoshop' => %w(psd), # Photoshop Image
    'image/vnd.adobe.premiere' => %w(ppj),
    'image/vnd.djvu' => %w(djvu djv),
    'image/vnd.dwg' => %w(dwg), # AutoCad Drawing
    'image/vnd.dxb' => %w(dxb), # AutoCAD DXF simplified Binary
    'image/vnd.dxf' => %w(dxf), # AutoCAD DXF
    'image/vnd.fastbidsheet' => %w(fbs),
    'image/vnd.fpx' => %w(fpx),
    'image/vnd.fst' => %w(fst),
    'image/vnd.fujixerox.edmics-mmr' => %w(mmr),
    'image/vnd.fujixerox.edmics-rlc' => %w(rlc),
    'image/vnd.microsoft.icon' => %w(ico),
    'image/vnd.ms-modi' => %w(mdi), # Microsoft Document Imaging
    'image/vnd.net-fpx' => %w(npx),
    'image/vnd.wap.wbmp' => %w(wbmp), # Wireless Bitmap File Format
    'image/vnd.xiff' => %w(xif),
    'image/vnd.zbrush.dcx' => %w(dcx), # ZSoft Multi-Page Paintbrush
    'image/vnd.zbrush.pcx' => %w(pcx), # ZSoft Paintbrush PiCture eXchange
    'image/webp' => %w(webp),
    'image/wmf' => %w(wmf), # Windows Metafile
    'image/x-bpg' => %w(bpg), # Better Portable Graphics
    'image/x-cmu-raster' => %w(ras),
    'image/x-cmx' => %w(cmx),
    'image/x-dpx' => %w(dpx), # Digital Picture Exchange from SMPTE
    'image/x-emf-compressed' => %w(emz), # Compressed Enhanced Metafile
    'image/x-freehand' => %w(fh fhc fh4 fh40 fh5 fh50 fh7 fh8 fh9 fh10 fh11 fh12 ft7 ft8 ft9 ft10 ft11 ft12), # FreeHand image
    'image/x-jbig2' => %w(jb2 jbig2), #        A lossless image compression standard from the       Joint Bi-level Image Experts Group.     
    'image/x-jp2-codestream' => %w(j2c), # JPEG 2000 Codestream
    'image/x-pict' => %w(pic pct pict), # Apple Macintosh QuickDraw/PICT Format
    'image/x-portable-anymap' => %w(pnm), # Portable Any Map
    'image/x-portable-bitmap' => %w(pbm), # Portable Bit Map
    'image/x-portable-graymap' => %w(pgm), # Portable Graymap Graphic
    'image/x-portable-pixmap' => %w(ppm), # UNIX Portable Bitmap Graphic
    'image/x-raw-adobe' => %w(dng), # Adobe Digital Negative
    'image/x-raw-canon' => %w(crw cr2), # Canon raw image
    'image/x-raw-casio' => %w(bay), # Casio raw image
    'image/x-raw-epson' => %w(erf), # Epson raw image
    'image/x-raw-fuji' => %w(raf), # Fuji raw image
    'image/x-raw-hasselblad' => %w(3fr), # Hasselblad raw image
    'image/x-raw-imacon' => %w(fff), # Imacon raw image
    'image/x-raw-kodak' => %w(k25 kdc dcs drf), # Kodak raw image
    'image/x-raw-leaf' => %w(mos), # Leaf raw image
    'image/x-raw-logitech' => %w(pxn), # Logitech raw image
    'image/x-raw-mamiya' => %w(mef), # Mamiya raw image
    'image/x-raw-minolta' => %w(mrw), # Minolta raw image
    'image/x-raw-nikon' => %w(nef nrw), # Nikon raw image
    'image/x-raw-olympus' => %w(orf), # Olympus raw image
    'image/x-raw-panasonic' => %w(raw rw2), # Panasonic raw image
    'image/x-raw-pentax' => %w(ptx pef), # Pentax raw image
    'image/x-raw-phaseone' => %w(iiq), # Phase One raw image
    'image/x-raw-rawzor' => %w(rwz), # Rawzor raw image
    'image/x-raw-red' => %w(r3d), # Red raw image
    'image/x-raw-sigma' => %w(x3f), # Sigma raw image
    'image/x-raw-sony' => %w(arw srf sr2), # Sony raw image
    'image/x-rgb' => %w(rgb), # Silicon Graphics RGB Bitmap
    'image/x-tga' => %w(tga icb vda), # Targa image data
    'image/x-xbitmap' => %w(xbm),
    'image/x-xcf' => %w(xcf), # GIMP Image File
    'image/x-xpixmap' => %w(xpm),
    'image/x-xwindowdump' => %w(xwd), # X Windows Dump
    'message/rfc822' => %w(eml mime),
    'message/x-emlx' => %w(emlx),
    'model/iges' => %w(igs iges), # Initial Graphics Exchange Specification Format
    'model/mesh' => %w(msh mesh silo),
    'model/vnd.dwf' => %w(dwf), # AutoCAD Design Web Format
    'model/vnd.dwfx+xps' => %w(dwfx), # AutoCAD Design Web Format
    'model/vnd.gdl' => %w(gdl),
    'model/vnd.gtw' => %w(gtw),
    'model/vnd.mts' => %w(mts),
    'model/vnd.vtu' => %w(vtu),
    'model/vrml' => %w(wrl vrml),
    'multipart/related' => %w(mht mhtml), # MIME Encapsulation of Aggregate HTML Documents
    'text/asp' => %w(asp), # Active Server Page
    'text/aspdotnet' => %w(aspx), # ASP .NET
    'text/calendar' => %w(ics ifb),
    'text/css' => %w(css), # Cascading Style Sheet
    'text/csv' => %w(csv),
    'text/html' => %w(html htm), # HyperText Markup Language
    'text/iso19139+xml' => %w(iso19139),
    'text/plain' => %w(txt text def list in aart ac am apt bsh classpath cnd cwiki data dcl dsp dsw egrm ent ft fn fv grm g handlers htc ihtml jmx junit jx manifest m4 mf mf meta mdo n3 pen pod pom project rng rnx roles schemas tld types vm vsl wsdd xargs xcat xegrm xgrm xlex xlog xmap xroles xsamples xsp xtest xweb xwelcome),
    'text/prs.lines.tag' => %w(dsc),
    'text/richtext' => %w(rtx),
    'text/sgml' => %w(sgml sgm),
    'text/tab-separated-values' => %w(tsv),
    'text/troff' => %w(t tr roff nroff man me ms), # Roff/nroff/troff/groff Unformatted Manual Page (UNIX)
    'text/uri-list' => %w(uri uris urls),
    'text/vnd.curl' => %w(curl),
    'text/vnd.curl.dcurl' => %w(dcurl),
    'text/vnd.curl.mcurl' => %w(mcurl),
    'text/vnd.curl.scurl' => %w(scurl),
    'text/vnd.fly' => %w(fly),
    'text/vnd.fmi.flexstor' => %w(flx),
    'text/vnd.graphviz' => %w(gv), # Graphviz Graph Visualization Software
    'text/vnd.in3d.3dml' => %w(3dml),
    'text/vnd.in3d.spot' => %w(spot),
    'text/vnd.iptc.anpa' => %w(anpa), # American Newspaper Publishers Association Wire Feeds
    'text/vnd.sun.j2me.app-descriptor' => %w(jad),
    'text/vnd.wap.wml' => %w(wml),
    'text/vnd.wap.wmlscript' => %w(wmls), # WML Script
    'text/vtt' => %w(vtt), # Web Video Text Tracks Format
    'text/x-actionscript' => %w(as), # ActionScript source code
    'text/x-ada' => %w(ada adb ads), # Ada source code
    'text/x-applescript' => %w(applescript), # AppleScript source code
    'text/x-asciidoc' => %w(asciidoc adoc ad ad.txt adoc.txt), # Asciidoc source code
    'text/x-aspectj' => %w(aj), # AspectJ source code
    'text/x-assembly' => %w(s s asm), # Assembler source code
    'text/x-awk' => %w(awk), # AWK script
    'text/x-basic' => %w(bas bas bas), # Basic source code
    'text/x-c++hdr' => %w(hpp hxx hh h h++ hp hpp), # C++ source code header
    'text/x-c++src' => %w(cpp cxx cc c c++ cpp), # C++ source code
    'text/x-cgi' => %w(cgi), # CGI script
    'text/x-chdr' => %w(h), # C source code header
    'text/x-clojure' => %w(clj), # Clojure source code
    'text/x-cobol' => %w(cbl cbl cbl cob cob cob), # COBOL source code
    'text/x-coffeescript' => %w(coffee), # CoffeeScript source code
    'text/x-coldfusion' => %w(cfm cfml cfc), # ColdFusion source code
    'text/x-common-lisp' => %w(cl jl lisp lsp), # Common Lisp source code
    'text/x-config' => %w(config conf cfg xconf),
    'text/x-csharp' => %w(cs), # C# source code
    'text/x-csrc' => %w(c), # C source code
    'text/x-d' => %w(d), # D source code
    'text/x-diff' => %w(diff patch),
    'text/x-eiffel' => %w(e), # Eiffel source code
    'text/x-emacs-lisp' => %w(el), # Emacs Lisp source code
    'text/x-erlang' => %w(erl), # Erlang source code
    'text/x-expect' => %w(exp), # Expect Script
    'text/x-forth' => %w(4th), # Forth source code
    'text/x-fortran' => %w(f f for f77 f90), # Fortran source code
    'text/x-go' => %w(go), # Go source code
    'text/x-groovy' => %w(groovy), # Groovy source code
    'text/x-haml' => %w(haml), # HAML source code
    'text/x-haskell' => %w(hs lhs), # Haskell source code
    'text/x-haxe' => %w(hx), # Haxe source code
    'text/x-idl' => %w(idl), # Inteface Definition Language
    'text/x-ini' => %w(ini), # Configuration file
    'text/x-java-properties' => %w(properties), # Java Properties
    'text/x-java-source' => %w(java), # Java source code
    'text/x-jsp' => %w(jsp), # Java Server Page
    'text/x-less' => %w(less), # LESS source code
    'text/x-lex' => %w(l), # Lex/Flex source code
    'text/x-log' => %w(log), # application log
    'text/x-lua' => %w(lua), # Lua source code
    'text/x-ml' => %w(ml), # ML source code
    'text/x-modula' => %w(m3 i3 mg ig), # Modula source code
    'text/x-objcsrc' => %w(m), # Objective-C source code
    'text/x-ocaml' => %w(ocaml mli), # Ocaml source code
    'text/x-pascal' => %w(p pp pas pas dpr), # Pascal source code
    'text/x-perl' => %w(pl pm al perl), # Perl script
    'text/x-php' => %w(php php3 php4), # PHP script
    'text/x-prolog' => %w(pro), # Prolog source code
    'text/x-python' => %w(py), # Python script
    'text/x-rexx' => %w(rexx), # Rexx source code
    'text/x-rsrc' => %w(r), # R source code
    'text/x-rst' => %w(rest rst restx), # reStructuredText source code
    'text/x-ruby' => %w(rb), # Ruby source code
    'text/x-sass' => %w(sass),
    'text/x-scala' => %w(scala), # Scala source code
    'text/x-scheme' => %w(scm), # Scheme source code
    'text/x-scss' => %w(scss),
    'text/x-sed' => %w(sed), # Sed code
    'text/x-setext' => %w(etx),
    'text/x-sql' => %w(sql), # SQL code
    'text/x-stsrc' => %w(st), # Smalltalk source code
    'text/x-tcl' => %w(itk tcl tk), # Tcl script
    'text/x-uuencode' => %w(uu),
    'text/x-vbasic' => %w(cls cls cls frm frm frm), # Visual basic source code
    'text/x-vbdotnet' => %w(vb), # VB.NET source code
    'text/x-vbscript' => %w(vbs), # VBScript source code
    'text/x-vcalendar' => %w(vcs),
    'text/x-vcard' => %w(vcf),
    'text/x-verilog' => %w(v), # Verilog source code
    'text/x-vhdl' => %w(vhd vhdl), # VHDL source code
    'text/x-web-markdown' => %w(md mdtext mkd markdown), # Markdown source code
    'text/x-yacc' => %w(y), # Yacc/Bison source code
    'text/x-yaml' => %w(yaml), # YAML source code
    'video/3gpp' => %w(3gp),
    'video/3gpp2' => %w(3g2),
    'video/h261' => %w(h261),
    'video/h263' => %w(h263),
    'video/h264' => %w(h264),
    'video/iso.segment' => %w(m4s),
    'video/jpeg' => %w(jpgv),
    'video/mj2' => %w(mj2 mjp2), # JPEG 2000 Part 3 (Motion JPEG, MJ2)
    'video/mp4' => %w(mp4 mp4v mpg4),
    'video/mpeg' => %w(mpeg mpg mpe m1v m2v), # MPEG Movie Clip
    'video/ogg' => %w(ogv), # Ogg Vorbis Video
    'video/quicktime' => %w(qt mov), # QuickTime Video
    'video/vnd.fvt' => %w(fvt),
    'video/vnd.mpegurl' => %w(mxu m4u),
    'video/vnd.ms-playready.media.pyv' => %w(pyv),
    'video/vnd.vivo' => %w(viv),
    'video/webm' => %w(webm),
    'video/x-dirac' => %w(drc), # Ogg Packaged Dirac Video
    'video/x-f4v' => %w(f4v),
    'video/x-flc' => %w(flc),
    'video/x-fli' => %w(fli),
    'video/x-flv' => %w(flv),
    'video/x-jng' => %w(jng),
    'video/x-m4v' => %w(m4v),
    'video/x-matroska' => %w(mkv),
    'video/x-mng' => %w(mng),
    'video/x-ms-asf' => %w(asf),
    'video/x-ms-wm' => %w(wm),
    'video/x-ms-wmv' => %w(wmv),
    'video/x-ms-wmx' => %w(wmx),
    'video/x-ms-wvx' => %w(wvx),
    'video/x-msvideo' => %w(avi), # Audio Video Interleave File
    'video/x-ogm' => %w(ogm), # Ogg Packaged OGM Video
    'video/x-sgi-movie' => %w(movie),
    'x-conference/x-cooltalk' => %w(ice), # Cooltalk Audio
  }
  TYPE_PARENTS = {
    'application/bizagi-modeler' => %w(application/zip),
    'application/dash+xml' => %w(application/xml),
    'application/dif+xml' => %w(application/xml),
    'application/dita+xml;format=map' => %w(application/dita+xml),
    'application/dita+xml;format=topic' => %w(application/dita+xml),
    'application/dita+xml;format=val' => %w(application/dita+xml),
    'application/illustrator' => %w(application/postscript),
    'application/java-archive' => %w(application/zip),
    'application/javascript' => %w(text/plain),
    'application/json' => %w(application/javascript),
    'application/mathematica' => %w(text/plain),
    'application/mbox' => %w(text/x-tika-text-based-message),
    'application/mp4' => %w(application/quicktime),
    'application/msword' => %w(application/x-tika-msoffice),
    'application/onenote; format=package' => %w(application/vnd.ms-cab-compressed),
    'application/onenote;format=one' => %w(application/onenote),
    'application/onenote;format=onetoc2' => %w(application/onenote),
    'application/rdf+xml' => %w(application/xml),
    'application/relax-ng-compact-syntax' => %w(text/plain),
    'application/rtf' => %w(text/plain),
    'application/sldworks' => %w(application/x-tika-msoffice),
    'application/smil+xml' => %w(application/xml),
    'application/vnd.adobe.xdp+xml' => %w(application/xml),
    'application/vnd.adobe.xfdf' => %w(application/xml),
    'application/vnd.android.package-archive' => %w(application/java-archive),
    'application/vnd.apple.keynote' => %w(application/vnd.apple.iwork),
    'application/vnd.apple.numbers' => %w(application/vnd.apple.iwork),
    'application/vnd.apple.pages' => %w(application/vnd.apple.iwork),
    'application/vnd.etsi.asic-e+zip' => %w(application/zip),
    'application/vnd.etsi.asic-s+zip' => %w(application/zip),
    'application/vnd.google-earth.kml+xml' => %w(application/xml),
    'application/vnd.google-earth.kmz' => %w(application/zip),
    'application/vnd.iptc.g2.newsmessage+xml' => %w(application/xml),
    'application/vnd.java.hprof.text' => %w(text/plain),
    'application/vnd.lotus-1-2-3;version=2' => %w(application/vnd.lotus-1-2-3),
    'application/vnd.lotus-1-2-3;version=3' => %w(application/vnd.lotus-1-2-3),
    'application/vnd.lotus-1-2-3;version=4' => %w(application/vnd.lotus-1-2-3),
    'application/vnd.lotus-1-2-3;version=97+9.x' => %w(application/vnd.lotus-1-2-3),
    'application/vnd.mindjet.mindmanager' => %w(application/zip),
    'application/vnd.ms-excel' => %w(application/x-tika-msoffice),
    'application/vnd.ms-excel.addin.macroenabled.12' => %w(application/x-tika-ooxml),
    'application/vnd.ms-excel.sheet.binary.macroenabled.12' => %w(application/x-tika-ooxml),
    'application/vnd.ms-excel.sheet.macroenabled.12' => %w(application/x-tika-ooxml),
    'application/vnd.ms-excel.template.macroenabled.12' => %w(application/x-tika-ooxml),
    'application/vnd.ms-outlook' => %w(application/x-tika-msoffice),
    'application/vnd.ms-powerpoint' => %w(application/x-tika-msoffice),
    'application/vnd.ms-powerpoint.addin.macroenabled.12' => %w(application/x-tika-ooxml),
    'application/vnd.ms-powerpoint.presentation.macroenabled.12' => %w(application/x-tika-ooxml),
    'application/vnd.ms-powerpoint.slide.macroenabled.12' => %w(application/x-tika-ooxml),
    'application/vnd.ms-powerpoint.slideshow.macroenabled.12' => %w(application/x-tika-ooxml),
    'application/vnd.ms-powerpoint.template.macroenabled.12' => %w(application/x-tika-ooxml),
    'application/vnd.ms-project' => %w(application/x-tika-msoffice),
    'application/vnd.ms-visio.drawing' => %w(application/x-tika-visio-ooxml),
    'application/vnd.ms-visio.drawing.macroEnabled.12' => %w(application/x-tika-visio-ooxml),
    'application/vnd.ms-visio.stencil' => %w(application/x-tika-visio-ooxml),
    'application/vnd.ms-visio.stencil.macroEnabled.12' => %w(application/x-tika-visio-ooxml),
    'application/vnd.ms-visio.template' => %w(application/x-tika-visio-ooxml),
    'application/vnd.ms-visio.template.macroEnabled.12' => %w(application/x-tika-visio-ooxml),
    'application/vnd.ms-word.document.macroenabled.12' => %w(application/x-tika-ooxml),
    'application/vnd.ms-word.template.macroenabled.12' => %w(application/x-tika-ooxml),
    'application/vnd.ms-works' => %w(application/x-tika-msoffice),
    'application/vnd.ms-xpsdocument' => %w(application/x-tika-ooxml),
    'application/vnd.oasis.opendocument.flat.presentation' => %w(application/vnd.oasis.opendocument.tika.flat.document),
    'application/vnd.oasis.opendocument.flat.spreadsheet' => %w(application/vnd.oasis.opendocument.tika.flat.document),
    'application/vnd.oasis.opendocument.flat.text' => %w(application/vnd.oasis.opendocument.tika.flat.document),
    'application/vnd.oasis.opendocument.formula' => %w(application/zip),
    'application/vnd.openofficeorg.autotext' => %w(application/zip),
    'application/vnd.openxmlformats-officedocument.presentationml.presentation' => %w(application/x-tika-ooxml),
    'application/vnd.openxmlformats-officedocument.presentationml.slide' => %w(application/x-tika-ooxml),
    'application/vnd.openxmlformats-officedocument.presentationml.slideshow' => %w(application/x-tika-ooxml),
    'application/vnd.openxmlformats-officedocument.presentationml.template' => %w(application/x-tika-ooxml),
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => %w(application/x-tika-ooxml),
    'application/vnd.openxmlformats-officedocument.spreadsheetml.template' => %w(application/x-tika-ooxml),
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => %w(application/x-tika-ooxml),
    'application/vnd.openxmlformats-officedocument.wordprocessingml.template' => %w(application/x-tika-ooxml),
    'application/vnd.stardivision.calc' => %w(application/x-tika-staroffice),
    'application/vnd.stardivision.draw' => %w(application/x-tika-staroffice),
    'application/vnd.stardivision.impress' => %w(application/x-tika-staroffice),
    'application/vnd.stardivision.writer' => %w(application/x-tika-staroffice),
    'application/vnd.visio' => %w(application/x-tika-msoffice),
    'application/vnd.wolfram.wl' => %w(application/mathematica),
    'application/x-adobe-indesign-interchange' => %w(application/xml),
    'application/x-bat' => %w(text/plain),
    'application/x-bibtex-text-file' => %w(text/plain),
    'application/x-bzip2' => %w(application/x-bzip),
    'application/x-corelpresentations' => %w(application/x-tika-msoffice),
    'application/x-debian-package' => %w(application/x-archive),
    'application/x-dosexec' => %w(application/x-msdownload),
    'application/x-fictionbook+xml' => %w(application/xml),
    'application/x-gtar' => %w(application/x-tar),
    'application/x-ibooks+zip' => %w(application/epub+zip),
    'application/x-itunes-ipa' => %w(application/zip),
    'application/x-latex' => %w(application/x-tex),
    'application/x-memgraph' => %w(application/x-bplist),
    'application/x-ms-asx' => %w(application/xml),
    'application/x-ms-installer' => %w(application/x-tika-msoffice),
    'application/x-ms-wmz' => %w(application/gzip),
    'application/x-mspublisher' => %w(application/x-tika-msoffice),
    'application/x-mysql-misam-compressed-index' => %w(application/x-mysql-db),
    'application/x-mysql-misam-data' => %w(application/x-mysql-db),
    'application/x-project' => %w(text/plain),
    'application/x-quattro-pro' => %w(application/x-tika-msoffice),
    'application/x-quattro-pro;version=1+5' => %w(application/x-quattro-pro),
    'application/x-quattro-pro;version=1-4' => %w(application/x-quattro-pro),
    'application/x-quattro-pro;version=5' => %w(application/x-quattro-pro),
    'application/x-quattro-pro;version=6' => %w(application/x-quattro-pro),
    'application/x-roxio-toast' => %w(application/x-iso9660-image),
    'application/x-sas' => %w(text/plain),
    'application/x-sfdu' => %w(text/plain),
    'application/x-sh' => %w(text/plain),
    'application/x-staroffice-template' => %w(application/x-tika-staroffice),
    'application/x-tex' => %w(text/plain),
    'application/x-tika-java-enterprise-archive' => %w(application/java-archive),
    'application/x-tika-java-web-archive' => %w(application/java-archive),
    'application/x-tika-msworks-spreadsheet' => %w(application/vnd.ms-excel),
    'application/x-webarchive' => %w(application/x-bplist),
    'application/x-x509-cert;format=der' => %w(application/x-x509-cert),
    'application/x-x509-cert;format=pem' => %w(application/x-x509-cert),
    'application/x-xliff+xml' => %w(application/xml),
    'application/x-xliff+zip' => %w(application/zip),
    'application/x-xmind' => %w(application/zip),
    'application/xml' => %w(text/plain),
    'application/xml-dtd' => %w(text/plain),
    'application/xquery' => %w(text/plain),
    'audio/mp4' => %w(application/quicktime),
    'audio/ogg' => %w(application/ogg),
    'audio/opus' => %w(audio/ogg),
    'audio/speex' => %w(audio/ogg),
    'audio/vorbis' => %w(audio/ogg),
    'audio/x-matroska' => %w(application/x-matroska),
    'audio/x-ms-wma' => %w(video/x-ms-asf),
    'image/jp2' => %w(image/x-jp2-container),
    'image/jpm' => %w(image/x-jp2-container),
    'image/jpx' => %w(image/x-jp2-container),
    'image/svg+xml' => %w(application/xml),
    'image/vnd.adobe.premiere' => %w(application/xml),
    'image/x-emf-compressed' => %w(application/gzip),
    'image/x-portable-bitmap' => %w(image/x-portable-anymap),
    'image/x-portable-graymap' => %w(image/x-portable-anymap),
    'image/x-portable-pixmap' => %w(image/x-portable-anymap),
    'image/x-xbitmap' => %w(text/x-c),
    'message/rfc822' => %w(text/x-tika-text-based-message),
    'message/x-emlx' => %w(text/x-tika-text-based-message),
    'model/vnd.dwfx+xps' => %w(application/x-tika-ooxml),
    'multipart/related' => %w(message/rfc822),
    'text/asp' => %w(text/plain),
    'text/aspdotnet' => %w(text/plain),
    'text/calendar' => %w(text/plain),
    'text/css' => %w(text/plain),
    'text/csv' => %w(text/plain),
    'text/iso19139+xml' => %w(application/xml),
    'text/vnd.graphviz' => %w(text/plain),
    'text/vtt' => %w(text/plain),
    'text/x-actionscript' => %w(text/plain),
    'text/x-ada' => %w(text/plain),
    'text/x-applescript' => %w(text/plain),
    'text/x-asciidoc' => %w(text/plain),
    'text/x-aspectj' => %w(text/plain),
    'text/x-assembly' => %w(text/plain),
    'text/x-awk' => %w(text/plain),
    'text/x-basic' => %w(text/plain),
    'text/x-c++hdr' => %w(text/plain),
    'text/x-c++src' => %w(text/plain),
    'text/x-cgi' => %w(text/plain),
    'text/x-chdr' => %w(text/plain),
    'text/x-clojure' => %w(text/plain),
    'text/x-cobol' => %w(text/plain),
    'text/x-coffeescript' => %w(text/plain),
    'text/x-coldfusion' => %w(text/plain),
    'text/x-common-lisp' => %w(text/plain),
    'text/x-config' => %w(text/plain),
    'text/x-csharp' => %w(text/plain),
    'text/x-csrc' => %w(text/plain),
    'text/x-d' => %w(text/plain),
    'text/x-diff' => %w(text/plain),
    'text/x-eiffel' => %w(text/plain),
    'text/x-emacs-lisp' => %w(text/plain),
    'text/x-erlang' => %w(text/plain),
    'text/x-expect' => %w(text/plain),
    'text/x-forth' => %w(text/plain),
    'text/x-fortran' => %w(text/plain),
    'text/x-go' => %w(text/plain),
    'text/x-groovy' => %w(text/plain),
    'text/x-haml' => %w(text/plain),
    'text/x-haskell' => %w(text/plain),
    'text/x-haxe' => %w(text/plain),
    'text/x-idl' => %w(text/plain),
    'text/x-ini' => %w(text/plain),
    'text/x-java-properties' => %w(text/plain),
    'text/x-java-source' => %w(text/plain),
    'text/x-jsp' => %w(text/plain),
    'text/x-less' => %w(text/plain),
    'text/x-lex' => %w(text/plain),
    'text/x-log' => %w(text/plain),
    'text/x-lua' => %w(text/plain),
    'text/x-ml' => %w(text/plain),
    'text/x-modula' => %w(text/plain),
    'text/x-objcsrc' => %w(text/plain),
    'text/x-ocaml' => %w(text/plain),
    'text/x-pascal' => %w(text/plain),
    'text/x-perl' => %w(text/plain),
    'text/x-php' => %w(text/plain),
    'text/x-prolog' => %w(text/plain),
    'text/x-python' => %w(text/plain),
    'text/x-rexx' => %w(text/plain),
    'text/x-rsrc' => %w(text/plain),
    'text/x-rst' => %w(text/plain),
    'text/x-ruby' => %w(text/plain),
    'text/x-sass' => %w(text/plain),
    'text/x-scala' => %w(text/plain),
    'text/x-scheme' => %w(text/plain),
    'text/x-scss' => %w(text/plain),
    'text/x-sed' => %w(text/plain),
    'text/x-setext' => %w(text/plain),
    'text/x-sql' => %w(text/plain),
    'text/x-stsrc' => %w(text/plain),
    'text/x-tcl' => %w(text/plain),
    'text/x-vbasic' => %w(text/x-basic),
    'text/x-vbdotnet' => %w(text/x-vbasic),
    'text/x-vbscript' => %w(text/x-vbasic),
    'text/x-vcalendar' => %w(text/plain),
    'text/x-vcard' => %w(text/plain),
    'text/x-verilog' => %w(text/plain),
    'text/x-vhdl' => %w(text/plain),
    'text/x-web-markdown' => %w(text/plain),
    'text/x-yacc' => %w(text/plain),
    'text/x-yaml' => %w(text/plain),
    'video/iso.segment' => %w(video/quicktime),
    'video/mj2' => %w(image/x-jp2-container),
    'video/mp4' => %w(video/quicktime),
    'video/ogg' => %w(application/ogg),
    'video/quicktime' => %w(application/quicktime),
    'video/webm' => %w(application/x-matroska),
    'video/x-dirac' => %w(video/ogg),
    'video/x-m4v' => %w(video/mp4),
    'video/x-matroska' => %w(application/x-matroska),
    'video/x-ms-wmv' => %w(video/x-ms-asf),
    'video/x-ogm' => %w(video/ogg),
  }
  b = Hash.new { |h, k| h[k] = k.b.freeze }
  # @private
  # :nodoc:
  MAGIC = [
    ['image/jpeg', [[0, b["\377\330\377"]]]],
    ['image/png', [[0, b["\211PNG\r\n\032\n"]]]],
    ['image/gif', [[0, b['GIF87a']], [0, b['GIF89a']]]],
    ['image/tiff', [[0, b["MM\000*"]], [0, b["II*\000"]], [0, b["MM\000+"]]]],
    ['image/bmp', [[0, b['BM'], [[26, b["\001\000"], [[28, b["\000\000"]], [28, b["\001\000"]], [28, b["\004\000"]], [28, b["\b\000"]], [28, b["\020\000"]], [28, b["\030\000"]], [28, b[" \000"]]]]]]]],
    ['image/vnd.adobe.photoshop', [[0, b["8BPS\000\001"]], [0, b["8BPS\000\002"]]]],
    ['image/webp', [[0, b['RIFF'], [[8, b['WEBP']]]]]],
    ['text/html', [[0..64, b['<!DOCTYPE HTML']], [0..64, b['<!DOCTYPE html']], [0..64, b['<!doctype HTML']], [0..64, b['<!doctype html']], [0..64, b['<HEAD']], [0..64, b['<head']], [0..64, b['<TITLE']], [0..64, b['<title']], [0..64, b['<HTML']], [0, b['<BODY']], [0, b['<body']], [0, b['<DIV']], [0, b['<div']], [0, b['<TITLE']], [0, b['<title']], [0, b['<h1']], [0, b['<H1']], [0..128, b['<html']]]],
    ['image/svg+xml', [[0..4096, b['<svg']]]],
    ['video/x-msvideo', [[0, b['RIFF'], [[8, b['AVI ']]]], [8, b['AVI ']]]],
    ['video/x-ms-wmv', [[0..8192, b['Windows Media Video']], [0..8192, b['VC-1 Advanced Profile']], [0..8192, b['wmv2']]]],
    ['video/mp4', [[4, b['ftypmp41']], [4, b['ftypmp42']]]],
    ['audio/mp4', [[4, b['ftypM4A ']], [4, b['ftypM4B ']], [4, b['ftypF4A ']], [4, b['ftypF4B ']]]],
    ['video/quicktime', [[4, b["moov\000"]], [4, b["mdat\000"]], [4, b["free\000"]], [4, b["skip\000"]], [4, b["pnot\000"]], [4, b['ftyp']], [0, b["\000\000\000\bwide"]]]],
    ['video/mpeg', [[0, b["\000\000\001\263"]], [0, b["\000\000\001\272"]]]],
    ['video/webm', [[0, b["\032E\337\243"], [[4..4096, b["B\202"], [[4..4096, b['webm'], [[4..4096, b['V_VP8']], [4..4096, b['V_VP9']]]]]]]]]],
    ['video/x-matroska', [[0, b["\032E\337\243\223B\202\210matroska"]]]],
    ['video/x-flv', [[0, b['FLV']]]],
    ['audio/mpeg', [[0, b["\377\362"]], [0, b["\377\363"]], [0, b["\377\364"]], [0, b["\377\365"]], [0, b["\377\366"]], [0, b["\377\367"]], [0, b["\377\372"]], [0, b["\377\373"]], [0, b["\377\374"]], [0, b["\377\375"]], [0, b["\377\377"]], [0, b['ID3']]]],
    ['application/pdf', [[0, b['%PDF-']], [0, b["\357\273\277%PDF-"]]]],
    ['application/msword', [[2080, b['Microsoft Word 6.0 Document']], [2080, b['Documento Microsoft Word 6']], [2112, b['MSWordDoc']], [0, b["1\276\000\000"]], [0, b['PO^Q`']], [0, b["\3767\000#"]], [0, b["\333\245-\000\000\000"]], [0, b["\224\246."]], [0..8, b["\320\317\021\340\241\261\032\341"], [[1152..4096, b["W\000o\000r\000d\000D\000o\000c\000u\000m\000e\000n\000t"]]]]]],
    ['application/vnd.openxmlformats-officedocument.wordprocessingml.document', [[0, b["PK\003\004"], [[30..65536, b['[Content_Types].xml'], [[0..4096, b['word/']]]], [30, b['_rels/.rels'], [[0..4096, b['word/']]]]]]]],
    ['application/vnd.ms-powerpoint', [[0..8, b["\320\317\021\340\241\261\032\341"], [[1152..4096, b["P\000o\000w\000e\000r\000P\000o\000i\000n\000t\000 D\000o\000c\000u\000m\000e\000n\000t"]]]]]],
    ['application/vnd.openxmlformats-officedocument.presentationml.presentation', [[0, b["PK\003\004"], [[30..65536, b['[Content_Types].xml'], [[0..4096, b['ppt/']]]], [30, b['_rels/.rels'], [[0..4096, b['ppt/']]]]]]]],
    ['application/vnd.ms-excel', [[2080, b['Microsoft Excel 5.0 Worksheet']], [2080, b['Foglio di lavoro Microsoft Exce']], [2114, b['Biff5']], [2121, b['Biff5']], [0..8, b["\320\317\021\340\241\261\032\341"], [[1152..4096, b["W\000o\000r\000k\000b\000o\000o\000k"]]]]]],
    ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', [[0, b["PK\003\004"], [[30..65536, b['[Content_Types].xml'], [[0..4096, b['xl/']]]], [30, b['_rels/.rels'], [[0..4096, b['xl/']]]]]]]],
    ['application/x-dbf', [[0, b["(?s)^[\\\\002\\\\003\\\\060\\\\061\\\\062\\\\103\\\\143\\\\203\\\\213\\\\313\\\\365\\\\345\\\\373].[\\\\001-\\\\014][\\\\001-\\\\037].{4}(?:.[^\\\\000]|[\\\\101-\\\\377].)(?:[^\\\\000\\\\001].|.[^\\\\000]).{31}(?<=[\\\\000][^\\\\000]{0,10})[A-Z@+]"]]]],
    ['image/x-tga', [[1, b["\001\001\000\000"], [[8, b[".*[\\\\124\\\\122\\\\125\\\\105\\\\126\\\\111\\\\123\\\\111\\\\117\\\\116\\\\055\\\\130\\\\106\\\\111\\\\114\\\\105\\\\056\\\\000]"]]]], [1, b["\000\002\000\000"], [[8, b[".*[\\\\124\\\\122\\\\125\\\\105\\\\126\\\\111\\\\123\\\\111\\\\117\\\\116\\\\055\\\\130\\\\106\\\\111\\\\114\\\\105\\\\056\\\\000]"]]]], [1, b["\000\003\000\000"], [[8, b[".*[\\\\124\\\\122\\\\125\\\\105\\\\126\\\\111\\\\123\\\\111\\\\117\\\\116\\\\055\\\\130\\\\106\\\\111\\\\114\\\\105\\\\056\\\\000]"]]]]]],
    ['application/x-endnote-refer', [[0..50, b['%A '], [[0..1000, b["\n%D "], [[0..1000, b["\n%T "]]]]]]]],
    ['application/x-ms-owner', [[0, b["(?s)^([\\\\005-\\\\017])[\\\\000\\\\040-\\\\176]{10}.{43}\\\\1\\000"]]]],
    ['application/mbox', [[0, b['From '], [[32..256, b["\nFrom: "]], [32..256, b["\nDate: "]], [32..256, b["\nSubject: "]], [32..256, b["\nDelivered-To: "]], [32..256, b["\nReceived: by "]], [32..256, b["\nReceived: via "]], [32..256, b["\nReceived: from "]], [32..256, b["\nMime-Version: "]], [32..256, b["\nX-"], [[32..8192, b["\nFrom: "]], [32..8192, b["\nDate: "]], [32..8192, b["\nSubject: "]], [32..8192, b["\nDelivered-To: "]], [32..8192, b["\nReceived: by "]], [32..8192, b["\nReceived: via "]], [32..8192, b["\nReceived: from "]], [32..8192, b["\nMime-Version: "]]]]]]]],
    ['application/x-bplist', [[0, b["bplist\000\000"]], [0, b["bplist\000\001"]], [0, b["bplist@\000"]], [0, b['bplist00']], [0, b['bplist01']], [0, b['bplist10']], [0, b['bplist15']], [0, b['bplist16']]]],
    ['application/x-ms-nls', [[0, b["(?s)^\\\\015.{51}\\\\014\\\\000\\\\015\\\\000\\\\016"]], [0, b["(?s)^\\\\104\\\\103.\\\\001"]]]],
    ['message/x-emlx', [[2..9, b["\nRelay-Version:"]], [2..9, b["\n#! rnews"]], [2..9, b["\nN#! rnews"]], [2..9, b["\nForward to"]], [2..9, b["\nPipe to"]], [2..9, b["\nReturn-Path:"]], [2..9, b["\nFrom:"]], [2..9, b["\nReceived:"]], [2..9, b["\nMessage-ID:"]], [2..9, b["\nDate:"]]]],
    ['application/cbor', [[0, b["\331\331\367"]]]],
    ['application/coreldraw', [[0, b['RIFF'], [[8, b['CDR']], [8, b['cdr']], [8, b['DES']], [8, b['des']]]]]],
    ['application/vnd.etsi.asic-e+zip', [[0, b["PK\003\004"], [[30, b['mimetypeapplication/vnd.etsi.asic-e+zip']]]]]],
    ['application/vnd.etsi.asic-s+zip', [[0, b["PK\003\004"], [[30, b['mimetypeapplication/vnd.etsi.asic-s+zip']]]]]],
    ['application/vnd.ms-excel.sheet.2', [[0, b["\t\000\004\000"], [[4, b["\000\000\020\000"]], [4, b["\000\000 \000"]], [4, b["\000\000@\000"]]]]]],
    ['application/vnd.ms-excel.sheet.3', [[0, b["\t\002\006\000"], [[4, b["\000\000\020\000"]], [4, b["\000\000 \000"]], [4, b["\000\000@\000"]]]]]],
    ['application/vnd.ms-excel.sheet.4', [[0, b["\t\004\006\000"], [[4, b["\000\000\020\000"]], [4, b["\000\000 \000"]], [4, b["\000\000@\000"]]]]]],
    ['application/vnd.ms-excel.workspace.3', [[0, b["\t\002\006\000"], [[4, b["\000\000\000\001"]]]]]],
    ['application/vnd.ms-excel.workspace.4', [[0, b["\t\004\006\000"], [[4, b["\000\000\000\001"]]]]]],
    ['application/x-axcrypt', [[0, b["\300\271\a.O\223\361F\240\025y,\241\331\350!"], [[17, b["\000\000\000\002"]]]]]],
    ['application/x-berkeley-db;format=btree;version=2', [[12, b["b1\005\000"], [[16, b["\006\000\000\000"]]]], [12, b["\000\0051b"], [[16, b["\000\000\000\006"]]]], [12, b["b1\005\000"], [[16, b["\006\000\000\000"]]]]]],
    ['application/x-berkeley-db;format=btree;version=3', [[12, b["b1\005\000"], [[16, b["\b\000\000\000"]]]], [12, b["\000\0051b"], [[16, b["\000\000\000\b"]]]], [12, b["b1\005\000"], [[16, b["\b\000\000\000"]]]]]],
    ['application/x-berkeley-db;format=btree;version=4', [[12, b["b1\005\000"], [[16, b["\t\000\000\000"]]]], [12, b["\000\0051b"], [[16, b["\000\000\000\t"]]]], [12, b["b1\005\000"], [[16, b["\t\000\000\000"]]]]]],
    ['application/x-berkeley-db;format=hash;version=2', [[12, b["a\025\006\000"], [[16, b["\005\000\000\000"]]]], [12, b["\000\006\025a"], [[16, b["\000\000\000\005"]]]], [12, b["a\025\006\000"], [[16, b["\005\000\000\000"]]]]]],
    ['application/x-berkeley-db;format=hash;version=3', [[12, b["a\025\006\000"], [[16, b["\a\000\000\000"]]]], [12, b["\000\006\025a"], [[16, b["\000\000\000\a"]]]], [12, b["a\025\006\000"], [[16, b["\a\000\000\000"]]]]]],
    ['application/x-berkeley-db;format=hash;version=4', [[12, b["a\025\006\000"], [[16, b["\b\000\000\000"]]]], [12, b["\000\006\025a"], [[16, b["\000\000\000\b"]]]], [12, b["a\025\006\000"], [[16, b["\b\000\000\000"]]]]]],
    ['application/x-berkeley-db;format=hash;version=5', [[12, b["a\025\006\000"], [[16, b["\t\000\000\000"]]]], [12, b["\000\006\025a"], [[16, b["\000\000\000\t"]]]], [12, b["a\025\006\000"], [[16, b["\t\000\000\000"]]]]]],
    ['application/x-bplist', [[0, b['bplist']]]],
    ['application/x-debian-package', [[0, b["!<arch>\ndebian-binary"]], [0, b["!<arch>\ndebian-split"]]]],
    ['application/x-font-type1', [[0, b["\200\001"], [[4, b["\000\000%!PS-AdobeFont"]]]], [0, b['%!PS-AdobeFont-1.0']]]],
    ['application/x-internet-archive', [[0, b['filedesc://']]]],
    ['application/x-lz4', [[0, b["\004\"M\030"]], [0, b["\002!L\030"]]]],
    ['application/x-mobipocket-ebook', [[0..60, b['BOOKMOBI']]]],
    ['application/x-msaccess', [[0, b["\000\001\000\000Stan"]]]],
    ['application/x-msdownload;format=pe-arm7', [[128, b["pe\000\000"], [[132, b["\304\001"]]]], [240, b["pe\000\000"], [[244, b["\304\001"]]]]]],
    ['application/x-msdownload;format=pe-armLE', [[128, b["pe\000\000"], [[132, b["\300\001"]]]], [240, b["pe\000\000"], [[244, b["\300\001"]]]]]],
    ['application/x-msdownload;format=pe-itanium', [[128, b["PE\000\000"], [[132, b["\000\002"]]]], [240, b["PE\000\000"], [[244, b["\000\002"]]]]]],
    ['application/x-msdownload;format=pe32', [[128, b["PE\000\000"], [[132, b["L\001"]]]], [240, b["PE\000\000"], [[244, b["L\001"]]]]]],
    ['application/x-msdownload;format=pe64', [[128, b["PE\000\000"], [[132, b["d\206"]]]], [240, b["PE\000\000"], [[244, b["d\206"]]]]]],
    ['application/x-msmoney', [[0, b["\000\001\000\000MSISAM Database"]]]],
    ['application/x-rar-compressed;version=4', [[0, b["Rar!\032\a\000"]]]],
    ['application/x-rar-compressed;version=5', [[0, b["Rar!\032\a\001\000"]]]],
    ['application/x-shapefile', [[0, b["\000\000'\n"]]]],
    ['application/x-stata-dta;version=10', [[0, b['<stata_dta><header><release>114</release>']]]],
    ['application/x-stata-dta;version=12', [[0, b['<stata_dta><header><release>115</release>']]]],
    ['application/x-stata-dta;version=13', [[0, b['<stata_dta><header><release>117</release>']]]],
    ['application/x-stata-dta;version=14', [[0, b['<stata_dta><header><release>118</release>']]]],
    ['application/x-stata-dta;version=8', [[0, b['<stata_dta><header><release>113</release>']]]],
    ['application/x-tika-msworks-spreadsheet', [[0..8, b["\320\317\021\340\241\261\032\341"], [[1152..4096, b["W\000k\000s\000S\000S\000W\000o\000r\000k\000B\000o\000o\000k"]]]]]],
    ['audio/opus', [[0, b['OggS'], [[29, b['pusHead']]]]]],
    ['audio/speex', [[0, b['OggS'], [[29, b['peex   ']]]]]],
    ['audio/vorbis', [[0, b['OggS'], [[29, b['vorbis']]]]]],
    ['audio/x-caf', [[0, b["caff\000\000"]], [0, b["caff\000\001"]], [0, b["caff\000\002"]], [0, b["caff@\000"]], [0, b["caff\200\000"]]]],
    ['audio/x-oggflac', [[0, b['OggS'], [[29, b['LAC']]]]]],
    ['audio/x-oggpcm', [[0, b['OggS'], [[29, b['CM     ']]]]]],
    ['image/avif', [[4, b['ftypavif']]]],
    ['image/heic', [[4, b['ftypheic']], [4, b['ftypheix']]]],
    ['image/heic-sequence', [[4, b['ftyphevc']], [4, b['ftyphevx']]]],
    ['image/heif', [[4, b['ftypmif1']]]],
    ['image/heif-sequence', [[4, b['ftypmsf1']]]],
    ['message/news', [[0, b['Path:']], [0, b['Xref:']]]],
    ['model/vnd.dwf;version=2', [[0, b['(DWF V00.22)']]]],
    ['model/vnd.dwf;version=5', [[0, b['(DWF V00.55)']]]],
    ['model/vnd.dwf;version=6', [[0, b['(DWF V06.'], [[11, b[')PK']]]]]],
    ['multipart/related', [[0, b['From: <Saved by Windows Internet Explorer 8>']], [0, b["From: \"Saved by Internet Explorer 11\""]], [0, b['MIME-Version: 1.0'], [[16..512, b["\nContent-Type: multipart/related"]]]]]],
    ['video/3gpp', [[4, b['ftyp3ge6']], [4, b['ftyp3ge7']], [4, b['ftyp3gg6']], [4, b['ftyp3gp1']], [4, b['ftyp3gp2']], [4, b['ftyp3gp3']], [4, b['ftyp3gp4']], [4, b['ftyp3gp5']], [4, b['ftyp3gp6']], [4, b['ftyp3gs7']]]],
    ['video/3gpp2', [[4, b['ftyp3g2a']], [4, b['ftyp3g2b']], [4, b['ftyp3g2c']]]],
    ['video/daala', [[0, b['OggS'], [[29, b['daala']]]]]],
    ['video/theora', [[0, b['OggS'], [[29, b['theora']]]]]],
    ['video/x-dirac', [[0, b['OggS'], [[29, b['BCD']]]]]],
    ['video/x-m4v', [[4, b['ftypM4V ']], [4, b['ftypM4VH']], [4, b['ftypM4VP']]]],
    ['video/x-oggrgb', [[0, b['OggS'], [[29, b['RGB']]]]]],
    ['video/x-ogguvs', [[0, b['OggS'], [[29, b['VS ']]]]]],
    ['video/x-oggyuv', [[0, b['OggS'], [[29, b['YUV']]]]]],
    ['video/x-ogm', [[0, b['OggS'], [[29, b['ideo']]]]]],
    ['application/x-msdownload;format=pe', [[0, b['MZ'], [[128, b["PE\000\000"]], [176, b["PE\000\000"]], [208, b["PE\000\000"]], [240, b["PE\000\000"]]]]]],
    ['application/applefile', [[0, b["\000\005\026\000"]]]],
    ['application/dash+xml', [[0, b['<MPD']]]],
    ['application/dicom', [[128, b['DICM']]]],
    ['application/epub+zip', [[0, b["PK\003\004"], [[30, b['mimetypeapplication/epub+zip']]]]]],
    ['application/fits', [[0, b['SIMPLE  =                    T']], [0, b['SIMPLE  =                T']]]],
    ['application/javascript', [[0, b['/* jQuery ']], [0, b['/*! jQuery ']], [0, b['/*!'], [[4..8, b['* jQuery ']]]], [0, b['(function(e,undefined){']], [0, b['!function(window,undefined){']], [0, b['/*  Prototype JavaScript ']], [0, b['var Prototype={']], [0, b['function $w(t){']], [0, b['/** @license React']], [0, b['/**'], [[4..8, b['* React ']]]]]],
    ['application/mac-binhex40', [[11, b['must be converted with BinHex']]]],
    ['application/mathematica', [[0, b['(**']], [0, b['(* ']]]],
    ['application/msword', [[0..8, b["\320\317\021\340\241\261\032\341"], [[546, b['jbjb']], [546, b['bjbj']]]]]],
    ['application/msword2', [[0, b["\233\245"]], [0, b["\333\245"]]]],
    ['application/msword5', [[0, b["\3767"]]]],
    ['application/octet-stream', [[10, b['# This is a shell archive']], [0, b["\037\036"]], [0, b["\037\037"]], [0, b["\377\037"]], [0, b["\377\037"]], [0, b["\005\313"]]]],
    ['application/ogg', [[0, b['OggS']]]],
    ['application/onenote;format=one', [[0, b["\344R\\{"], [[4, b["\214\330"], [[6, b["\247M"], [[8, b['0xAEB15378D02996D3']]]]]]]]]],
    ['application/onenote;format=onetoc2', [[0, b["\241/\377C"], [[4, b["\331\357"], [[6, b['vL'], [[8, b['0x9EE210EA5722765F']]]]]]]]]],
    ['application/pkcs7-signature', [[0, b['-----BEGIN PKCS7']], [0, b['0x3080'], [[0, b["\006\t*\206H\206\367\r\001\a"], [[11, b["\240"]]]]]], [0, b['0x3081'], [[0, b["\006\t*\206H\206\367\r\001\a"], [[11, b["\240"]]]]]], [0, b['0x3082'], [[0, b["\006\t*\206H\206\367\r\001\a"], [[11, b["\240"]]]]]], [0, b['0x3083'], [[0, b["\006\t*\206H\206\367\r\001\a"], [[11, b["\240"]]]]]], [0, b['0x3084'], [[0, b["\006\t*\206H\206\367\r\001\a"], [[11, b["\240"]]]]]]]],
    ['application/postscript', [[0, b['%!']], [0, b["\004%!"]], [0, b["\305\320\323\306"]], [0, b['%!PS-Adobe-3.0 EPSF-3.0']]]],
    ['application/rtf', [[0, b["{\\rtf"]]]],
    ['application/sereal;version=1', [[0, b['=srl']]]],
    ['application/sereal;version=2', [[0, b['=srl']]]],
    ['application/sereal;version=3', [[0, b["=\363rl"]]]],
    ['application/timestamped-data', [[0, b["0\200\006\v*\206H\206\367"]]]],
    ['application/vnd.apple.mpegurl', [[0, b['#EXTM3U']]]],
    ['application/vnd.digilite.prolights', [[0, b["\177\fD+"]]]],
    ['application/vnd.fdf', [[0, b['%FDF-']]]],
    ['application/vnd.java.hprof ', [[0, b["JAVA PROFILE \\\\d\\\\.\\\\d\\\\.\\\\d\\\\u0000"]]]],
    ['application/vnd.java.hprof.text', [[0, b["JAVA PROFILE \\\\d\\\\.\\\\d\\\\.\\\\d,"]]]],
    ['application/vnd.lotus-1-2-3;version=1', [[0, b["\000\000\002\000\004\004"]]]],
    ['application/vnd.lotus-1-2-3;version=2', [[0, b["\000\000\002\000\006\004\006\000\b\000"]]]],
    ['application/vnd.lotus-1-2-3;version=3', [[0, b["\000\000\032\000\000\020\004\000"]]]],
    ['application/vnd.lotus-1-2-3;version=4', [[0, b["\000\000\032\000\002\020\004\000"]]]],
    ['application/vnd.lotus-1-2-3;version=97+9.x', [[0, b["\000\000\032\000\003\020\004\000"]]]],
    ['application/vnd.lotus-wordpro', [[0, b["WordPro\000"]], [0, b["WordPro\r\373"]]]],
    ['application/vnd.mif', [[0, b['<MakerFile']], [0, b['<MIFFile']], [0, b['<MakerDictionary']], [0, b['<MakerScreenFont']], [0, b['<MML']], [0, b['<Book']], [0, b['<Maker']]]],
    ['application/vnd.ms-cab-compressed', [[0, b["MSCF\000\000\000\000"]]]],
    ['application/vnd.ms-cab-compressed', [[0, b['MSCF']]]],
    ['application/vnd.ms-htmlhelp', [[0, b['ITSF']]]],
    ['application/vnd.ms-outlook-pst', [[0, b['!BDN'], [[8, b['SM']]]]]],
    ['application/vnd.ms-tnef', [[0, b["x\237>\""]]]],
    ['application/vnd.ms-works', [[0..8, b["\320\317\021\340\241\261\032\341"], [[1152..4096, b["M\000a\000t\000O\000S\000T"]]]]]],
    ['application/vnd.oasis.opendocument.chart', [[0, b['PK'], [[30, b['mimetypeapplication/vnd.oasis.opendocument.chart']]]]]],
    ['application/vnd.oasis.opendocument.chart-template', [[0, b['PK'], [[30, b['mimetypeapplication/vnd.oasis.opendocument.chart-template']]]]]],
    ['application/vnd.oasis.opendocument.formula', [[0, b['PK'], [[30, b['mimetypeapplication/vnd.oasis.opendocument.formula']]]]]],
    ['application/vnd.oasis.opendocument.formula-template', [[0, b['PK'], [[30, b['mimetypeapplication/vnd.oasis.opendocument.formula-template']]]]]],
    ['application/vnd.oasis.opendocument.graphics', [[0, b['PK'], [[30, b['mimetypeapplication/vnd.oasis.opendocument.graphics']]]]]],
    ['application/vnd.oasis.opendocument.graphics-template', [[0, b['PK'], [[30, b['mimetypeapplication/vnd.oasis.opendocument.graphics-template']]]]]],
    ['application/vnd.oasis.opendocument.image', [[0, b['PK'], [[30, b['mimetypeapplication/vnd.oasis.opendocument.image']]]]]],
    ['application/vnd.oasis.opendocument.image-template', [[0, b['PK'], [[30, b['mimetypeapplication/vnd.oasis.opendocument.image-template']]]]]],
    ['application/vnd.oasis.opendocument.presentation', [[0, b['PK'], [[30, b['mimetypeapplication/vnd.oasis.opendocument.presentation']]]]]],
    ['application/vnd.oasis.opendocument.presentation-template', [[0, b['PK'], [[30, b['mimetypeapplication/vnd.oasis.opendocument.presentation-template']]]]]],
    ['application/vnd.oasis.opendocument.spreadsheet', [[0, b['PK'], [[30, b['mimetypeapplication/vnd.oasis.opendocument.spreadsheet']]]]]],
    ['application/vnd.oasis.opendocument.spreadsheet-template', [[0, b['PK'], [[30, b['mimetypeapplication/vnd.oasis.opendocument.spreadsheet-template']]]]]],
    ['application/vnd.oasis.opendocument.text', [[0, b['PK'], [[30, b['mimetypeapplication/vnd.oasis.opendocument.text']]]]]],
    ['application/vnd.oasis.opendocument.text-master', [[0, b['PK'], [[30, b['mimetypeapplication/vnd.oasis.opendocument.text-master']]]]]],
    ['application/vnd.oasis.opendocument.text-template', [[0, b['PK'], [[30, b['mimetypeapplication/vnd.oasis.opendocument.text-template']]]]]],
    ['application/vnd.oasis.opendocument.text-web', [[0, b['PK'], [[30, b['mimetypeapplication/vnd.oasis.opendocument.text-web']]]]]],
    ['application/vnd.rn-realmedia', [[0, b['.RMF']]]],
    ['application/vnd.stardivision.calc', [[0..8, b["\320\317\021\340\241\261\032\341"], [[2048..2207, b['StarCalc']]]]]],
    ['application/vnd.stardivision.draw', [[0..8, b["\320\317\021\340\241\261\032\341"], [[2048..2207, b['StarDraw']]]]]],
    ['application/vnd.stardivision.impress', [[0..8, b["\320\317\021\340\241\261\032\341"], [[2048..2207, b['StarImpress']]]]]],
    ['application/vnd.stardivision.writer', [[0..8, b["\320\317\021\340\241\261\032\341"], [[2048..2207, b['StarWriter']]]]]],
    ['application/vnd.sun.xml.writer', [[0, b['PK'], [[30, b['mimetypeapplication/vnd.sun.xml.writer']]]]]],
    ['application/vnd.symbian.install', [[8, b["\031\004\000\020"]]]],
    ['application/vnd.tcpdump.pcap', [[0, b["\241\262\303\324"]], [0, b["\324\303\262\241"]]]],
    ['application/vnd.wolfram.wl', [[0, b['#!/usr/bin/env wolframscript']]]],
    ['application/vnd.wordperfect', [[0, b['application/vnd.wordperfect;']]]],
    ['application/vnd.wordperfect;version=4.2', [[0, b["\313\n\001"], [[5, b["\313"]]]]]],
    ['application/vnd.wordperfect;version=5.0', [[0, b["\377WPC"], [[10, b["\000\000"]]]]]],
    ['application/vnd.wordperfect;version=5.1', [[0, b["\377WPC"], [[10, b["\000\001"]]]]]],
    ['application/vnd.wordperfect;version=6.x', [[0, b["\377WPC"], [[10, b["\002\001"]]]]]],
    ['application/vnd.xara', [[0, b['xar!']]]],
    ['application/warc', [[0, b['WARC/']]]],
    ['application/wasm', [[0, b["\000asm"]], [0, b["msa\000"]]]],
    ['application/x-7z-compressed', [[0..1, b['7z'], [[2..5, b["\274\257'\034"]]]]]],
    ['application/x-adobe-indesign', [[0, b["\006\006\355\365\330\035F\345\2751\357\347\376t\267\035"]]]],
    ['application/x-adobe-indesign-interchange', [[0..100, b['<?aid']]]],
    ['application/x-archive', [[0, b['=<ar>']], [0, b["!<arch>\n"]]]],
    ['application/x-arj', [[0, b["`\352"]]]],
    ['application/x-bat', [[0, b['@echo off']], [0, b['rem ']]]],
    ['application/x-berkeley-db;format=btree', [[0, b["b1\005\000"]], [0, b["\000\0051b"]], [0, b["b1\005\000"]], [12, b["b1\005\000"]], [12, b["\000\0051b"]], [12, b["b1\005\000"]]]],
    ['application/x-berkeley-db;format=hash', [[0, b["a\025\006\000"]], [0, b["\000\006\025a"]], [0, b["a\025\006\000"]], [12, b["a\025\006\000"]], [12, b["\000\006\025a"]], [12, b["a\025\006\000"]]]],
    ['application/x-berkeley-db;format=log', [[12, b["\210\t\004\000"]], [12, b["\210\t\004\000"]], [12, b["\000\004\t\210"]]]],
    ['application/x-berkeley-db;format=queue', [[12, b["S\"\004\000"]], [12, b["\000\004\"S"]], [12, b["S\"\004\000"]]]],
    ['application/x-bibtex-text-file', [[0, b['% BibTeX `']], [73, b['%%%  ']], [0, b['% BibTeX standard bibliography ']], [73, b['%%%  @BibTeX-style-file{']], [0, b['@article{']], [0, b['@book{']], [0, b['@inbook{']], [0, b['@incollection{']], [0, b['@inproceedings{']], [0, b['@manual{']], [0, b['@misc{']], [0, b['@preamble{']], [0, b['@phdthesis{']], [0, b['@string{']], [0, b['@techreport{']], [0, b['@unpublished{']]]],
    ['application/x-bittorrent', [[0, b['d8:announce']]]],
    ['application/x-chrome-package', [[0, b['Cr24']]]],
    ['application/x-compress', [[0, b["\037\235"]]]],
    ['application/x-coredump', [[0, b["\177ELF"], [[16, b["\004\000"]], [16, b["\000\004"]]]]]],
    ['application/x-cpio', [[0, b["\307q"]], [0, b["q\307"]], [0, b['070707']], [0, b['070701']], [0, b['070702']]]],
    ['application/x-dex', [[0, b["dex\n"], [[7, b["\000"]]]]]],
    ['application/x-dvi', [[0, b["\367\002"]], [0, b["\367\002"]], [14, b["\e TeX output "]]]],
    ['application/x-elc', [[0, b["\n("]], [0, b[";ELC\023\000\000\000"]]]],
    ['application/x-elf', [[0, b["\177ELF"]]]],
    ['application/x-erdas-hfa', [[0, b['EHFA_HEADER_TAG']]]],
    ['application/x-executable', [[0, b["\177ELF"], [[16, b["\002\000"]], [16, b["\000\002"]]]]]],
    ['application/x-filemaker', [[14, b["\300HBAM7"], [[525, b["HBAM2101OCT99\301\002H\aPro 7.0\300\300"]]]]]],
    ['application/x-foxmail', [[0, b["\020\020\020\020\020\020\020\021\021\021\021\021\021S"]]]],
    ['application/x-gnumeric', [[39, b['=<gmr:Workbook']]]],
    ['application/x-grib', [[0, b['GRIB']]]],
    ['application/x-gtar', [[257, b["ustar  \000"]]]],
    ['application/x-hdf', [[0, b["\016\003\023\001"]], [0, b["\211HDF\r\n\032"]]]],
    ['application/x-hwp', [[0, b['HWP Document File V']]]],
    ['application/x-ibooks+zip', [[0, b["PK\003\004"], [[30, b['mimetypeapplication/x-ibooks+zip']]]]]],
    ['application/x-isatab', [[1, b['Source Name']]]],
    ['application/x-isatab-assay', [[1, b['Sample Name']]]],
    ['application/x-isatab-investigation', [[0, b['ONTOLOGY SOURCE REFERENCE']]]],
    ['application/x-iso9660-image', [[32769, b['CD001']], [34817, b['CD001']], [36865, b['CD001']]]],
    ['application/x-java-jnilib', [[0, b["\312\376\272\276"], [[4096, b["\376\355\372\316"]], [4096, b["\376\355\372\317"]], [4096, b["\316\372\355\376"]], [4096, b["\317\372\355\376"]]]]]],
    ['application/x-kdelnk', [[0, b['[KDE Desktop Entry]']], [0, b['# KDE Config File']]]],
    ['application/x-latex', [[0, b['% -*-latex-*-']]]],
    ['application/x-lha', [[2, b['-lzs-']], [2, b['-lh -']], [2, b['-lhd-']], [2, b['-lh2-']], [2, b['-lh3-']], [2, b['-lh4-']], [2, b['-lh5-']], [2, b['-lh6-']], [2, b['-lh7-']]]],
    ['application/x-lharc', [[2, b['-lh0-']], [2, b['-lh1-']], [2, b['-lz4-']], [2, b['-lz5-']]]],
    ['application/x-lzip', [[0, b['LZIP']]]],
    ['application/x-matlab-data', [[0, b['MATLAB']]]],
    ['application/x-msdownload', [[0, b['MZ']]]],
    ['application/x-mswrite', [[0, b["1\276\000\000"]], [0, b["2\276\000\000"]]]],
    ['application/x-netcdf', [[0, b["CDF\001"]], [0, b["CDF\002"]], [0, b["CDF\001"]]]],
    ['application/x-object', [[0, b["\177ELF"], [[16, b["\001\000"]], [16, b["\000\001"]]]]]],
    ['application/x-ole-storage', [[0..8, b["\320\317\021\340\241\261\032\341"]]]],
    ['application/x-parquet', [[0, b['PAR1']]]],
    ['application/x-project', [[0, b['MPX,Microsoft Project for Windows,']]]],
    ['application/x-prt', [[8, b['0M3C']]]],
    ['application/x-quattro-pro;version=1+5', [[0, b["\000\000\002\000\001\020"]]]],
    ['application/x-quattro-pro;version=1-4', [[0, b["\000\000\002\000 Q"]]]],
    ['application/x-quattro-pro;version=5', [[0, b["\000\000\002\000!Q"]]]],
    ['application/x-quattro-pro;version=6', [[0, b["\000\000\002\000\002\020"]]]],
    ['application/x-rar-compressed', [[0, b['Rar!']], [0, b["Rar!\032"]]]],
    ['application/x-rpm', [[0, b["\355\253\356\333"]]]],
    ['application/x-sc', [[38, b['Spreadsheet']]]],
    ['application/x-sh', [[0, b['#!/']], [0, b['#! /']], [0, b["#!\t/"]], [0, b["eval \"exec"]]]],
    ['application/x-sharedlib', [[0, b["\177ELF"], [[16, b["\003\000"]], [16, b["\000\003"]]]]]],
    ['application/x-shockwave-flash', [[0, b['FWS']], [0, b['CWS']]]],
    ['application/x-snappy-framed', [[0, b['sNaPpY']]]],
    ['application/x-sqlite3', [[0, b["SQLite format 3\000"]]]],
    ['application/x-stata-dta', [[0, b['<stata_dta><header><release>']]]],
    ['application/x-stuffit', [[0, b['StuffIt']]]],
    ['application/x-tex', [[0, b["\\input"]], [0, b["\\section"]], [0, b["\\setlength"]], [0, b["\\documentstyle"]], [0, b["\\chapter"]], [0, b["\\documentclass"]], [0, b["\\relax"]], [0, b["\\contentsline"]]]],
    ['application/x-texinfo', [[0, b["\\input texinfo"]]]],
    ['application/x-tika-ooxml', [[0, b["PK\003\004"], [[30, b['[Content_Types].xml']], [30, b['_rels/.rels']]]]]],
    ['application/x-uc2-compressed', [[0, b["UC2\032"]]]],
    ['application/x-vhd', [[0, b['conectix']]]],
    ['application/x-x509-cert;format=der', []],
    ['application/x-x509-cert;format=pem', [[0, b['-----BEGIN CERTIFICATE-----']]]],
    ['application/x-x509-dsa-parameters', [[0, b['-----BEGIN DSA PARAMETERS-----']]]],
    ['application/x-x509-ec-parameters', [[0, b['-----BEGIN EC PARAMETERS-----']]]],
    ['application/x-x509-key;format=pem', [[0, b['-----BEGIN PRIVATE KEY-----']], [0, b['-----BEGIN PUBLIC KEY-----']], [0, b['-----BEGIN KEY-----']], [0, b['-----BEGIN RSA KEY-----']], [0, b['-----BEGIN RSA PRIVATE KEY-----']], [0, b['-----BEGIN DSA KEY-----']], [0, b['-----BEGIN DSA PRIVATE KEY-----']]]],
    ['application/x-xz', [[0, b["\3757zXZ\000"]]]],
    ['application/x-zoo', [[20, b["\334\247\304\375"]]]],
    ['application/xml', [[0, b['<?xml']], [0, b['<?XML']], [0, b["\357\273\277<?xml"]], [0, b["\377\376<\000?\000x\000m\000l\000"]], [0, b["\376\377\000<\000?\000x\000m\000l"]]]],
    ['application/zip', [[0, b["PK\003\004"]], [0, b["PK\005\006"]], [0, b["PK\a\b"]]]],
    ['application/zstd', [[0, b["(\265/\375"]]]],
    ['audio/ac3', [[0, b["\vw"]]]],
    ['audio/amr-wb', [[0, b["#!AMR-WB\n"]]]],
    ['audio/eac3', [[0, b["\vw"]]]],
    ['audio/prs.sid', [[0, b['PSID']]]],
    ['audio/webm', [[0, b["\032E\337\243"], [[4..4096, b["B\202"], [[4..4096, b['webm'], [[4..4096, b['A_VORBIS']], [4..4096, b['A_OPUS']]]]]]]]]],
    ['audio/x-flac', [[0, b['fLaC']]]],
    ['audio/x-mod', [[0, b['Extended Module:']], [21, b['BMOD2STM']], [1080, b['M.K.']], [1080, b['M!K!']], [1080, b['FLT4']], [1080, b['FLT8']], [1080, b['4CHN']], [1080, b['6CHN']], [1080, b['8CHN']], [1080, b['CD81']], [1080, b['OKTA']], [1080, b['16CN']], [1080, b['32CN']], [0, b['IMPM']]]],
    ['audio/x-mpegurl', [[0, b["#EXTM3U\r\n"]]]],
    ['audio/x-ms-wma', [[0..8192, b['Windows Media Audio']]]],
    ['audio/x-pn-realaudio', [[0, b[".ra\375"]]]],
    ['chemical/x-cdx', [[0, b['VjCD0100']]]],
    ['font/woff', [[0, b['wOFF']]]],
    ['font/woff2', [[0, b['wOF2']]]],
    ['image/aces', [[0, b["v/1\001\002\000\000\000"]], [0, b["v/1\001\002\004\000\000"]]]],
    ['image/cgm', [[0, b['BEGMF']]]],
    ['image/emf', [[0, b["\001\000\000\000"], [[40, b[' EMF']]]]]],
    ['image/fits', [[0, b['SIMPLE  =  ']]]],
    ['image/heic', [[4, b['ftypheic']], [4, b['ftypheix']]]],
    ['image/heic-sequence', [[4, b['ftyphevc']], [4, b['ftyphevx']]]],
    ['image/heif', [[4, b['ftypmif1']]]],
    ['image/heif-sequence', [[4, b['ftypmsf1']]]],
    ['image/icns', [[0, b['icns']]]],
    ['image/jp2', [[0, b["\000\000\000\fjP  \r\n\207\n"], [[20, b['jp2 ']]]]]],
    ['image/jpm', [[0, b["\000\000\000\fjP  \r\n\207\n"], [[20, b['jpm ']]]]]],
    ['image/jpx', [[0, b["\000\000\000\fjP  \r\n\207\n"], [[20, b['jpx ']]]]]],
    ['image/nitf', [[0, b['NITF01.10']], [0, b['NITF02.000']], [0, b['NITF02.100']]]],
    ['image/vnd.djvu', [[0, b['AT&TFORM']]]],
    ['image/vnd.dwg', [[0, b['MC0.0']], [0, b['AC1.2']], [0, b['AC1.40']], [0, b['AC1.50']], [0, b['AC2.10']], [0, b['AC2.21']], [0, b['AC2.22']]]],
    ['image/vnd.dxb', [[0, b["AutoCAD DXB 1.0\r\n0x1A00"]]]],
    ['image/vnd.dxf;format=ascii', [[0..3, b["0\\r\\nSECTION\\r\\n"], [[12..18, b["2\\r\\nHEADER\\r\\n"]]]]]],
    ['image/vnd.dxf;format=binary', [[0, b["AutoCAD Binary DXF\r\n0x1A00"]]]],
    ['image/vnd.microsoft.icon', [[0, b["BA(\000\000\000.\000\000\000\000\000\000\000"]], [0, b["\000\000\001\000"]]]],
    ['image/vnd.ms-modi', [[0, b["EP*\000"]]]],
    ['image/vnd.zbrush.dcx', [[0, b["\261h\336:"]]]],
    ['image/wmf', [[0, b["\327\315\306\232\000\000"]], [0, b["\001\000\t\000\000\003"]]]],
    ['image/x-bpg', [[0, b["BPG\373"]]]],
    ['image/x-dpx', [[0, b['SDPX']], [0, b['XPDS']]]],
    ['image/x-freehand', [[0, b['AGD2']], [0, b['AGD3']], [0, b['AGD4']], [0..24, b['FreeHand10']], [0..24, b['FreeHand11']], [0..24, b['FreeHand12']]]],
    ['image/x-jbig2', [[0, b["\227JB2\r\n\032\n"]]]],
    ['image/x-jp2-container', [[0, b["\000\000\000\fjP  \r\n\207\n"]]]],
    ['image/x-niff', [[0, b['IIN1']]]],
    ['image/x-pict', [[522, b["\000\021\002\377\f\000"]]]],
    ['image/x-portable-bitmap', [[0, b['P1']], [0, b['P4']]]],
    ['image/x-portable-graymap', [[0, b['P2']], [0, b['P5']], [0, b["P5\n"]]]],
    ['image/x-portable-pixmap', [[0, b['P3']], [0, b['P6']], [0, b['P7']], [0, b["P4\n"]]]],
    ['image/x-raw-olympus', [[0, b['IIRO']]]],
    ['image/x-rgb', [[0, b["\001\332\001\001\000\003"]]]],
    ['image/x-xbitmap', [[0, b['/* XPM']]]],
    ['image/x-xcf', [[0, b['gimp xcf ']]]],
    ['message/news', [[0, b['Article']]]],
    ['message/rfc822', [[0, b['Relay-Version:']], [0, b['#! rnews']], [0, b['N#! rnews']], [0, b['Forward to']], [0, b['Pipe to']], [0, b['Return-Path:']], [0, b['Message-ID:']], [0, b['X-Mailer:']], [0, b['X-Notes-Item:'], [[0..8192, b['Message-ID:']]]], [0, nil, [[0, b['Date:']], [0, b['Delivered-To:']], [0, b['From:']], [0, b['Message-ID:']], [0, b['MIME-Version:']], [0, b['Received:']], [0, b['Relay-Version:']], [0, b['Return-Path:']], [0, b['Sent:']], [0, b['Status:']], [0, b['User-Agent:']], [0, b['X-Mailer:']], [0, b['X-Originating-IP:']], [0..1024, b["\nDate:"]], [0..1024, b["\nDelivered-To:"]], [0..1024, b["\nFrom:"]], [0..1024, b["\nMIME-Version:"]], [0..1024, b["\nReceived:"]], [0..1024, b["\nRelay-Version:"]], [0..1024, b["\nReturn-Path:"]], [0..1024, b["\nSent:"]], [0..1024, b["\nStatus:"]], [0..1024, b["\nSubject:"]], [0..1024, b["\nTo:"]], [0..1024, b["\nUser-Agent:"]], [0..1024, b["\nX-Mailer:"]], [0..1024, b["\nX-Originating-IP:"]]]], [0, b['(X|DKIM|ARC)-'], [[0..8192, b["\nDate:"]], [0..8192, b["\nDelivered-To:"]], [0..8192, b["\nFrom:"]], [0..8192, b["\nMessage-ID:"]], [0..8192, b["\nMIME-Version:"]], [0..8192, b["\nReceived:"]], [0..8192, b["\nRelay-Version:"]], [0..8192, b["\nReturn-Path:"]], [0..8192, b["\nStatus:"]], [0..8192, b["\nUser-Agent:"]], [0..8192, b["\nX-Mailer:"]], [0..8192, b["\nX-Originating-IP:"]]]]]],
    ['model/vnd.dwf', [[0, b['(DWF V'], [[8, b['.'], [[11, b[')']]]]]]]],
    ['multipart/appledouble', [[0, b["\000\005\026\a"]]]],
    ['text/calendar', [[0, b['BEGIN:VCALENDAR'], [[15..30, b['VERSION:2.0']]]]]],
    ['text/troff', [[0, b[".\\\""]], [0, b["'\\\""]], [0, b["'.\\\""]], [0, b["\\\""]], [0, b["'''"]]]],
    ['text/vnd.graphviz', [[0, b["(?s)^\\\\s*(?:strict\\\\s+)?(?:di)?graph\\\\b"]], [0, b["(?s)^(?:\\\\s*//[^\\\\n]*\\n){1,10}\\\\s*(?:strict\\\\s+)?(?:di)?graph\\\\b"]], [0, b["(?s)^\\\\s*/\\\\*.{0,1024}?\\\\*/\\\\s*(?:strict\\\\s+)?(?:di)?graph\\\\b"]]]],
    ['text/vnd.iptc.anpa', [[0, b["\026\026\001"]]]],
    ['text/x-awk', [[0, b['#!/bin/gawk']], [0, b['#! /bin/gawk']], [0, b['#!/usr/bin/gawk']], [0, b['#! /usr/bin/gawk']], [0, b['#!/usr/local/bin/gawk']], [0, b['#! /usr/local/bin/gawk']], [0, b['#!/bin/awk']], [0, b['#! /bin/awk']], [0, b['#!/usr/bin/awk']], [0, b['#! /usr/bin/awk']]]],
    ['text/x-diff', [[0, b['diff ']], [0, b['*** ']], [0, b['Only in ']], [0, b['Common subdirectories: ']], [0, b['Index:']]]],
    ['text/x-jsp', [[0, b['<%@']], [0, b['<%--']]]],
    ['text/x-matlab', [[0, b['function [']]]],
    ['text/x-perl', [[0, b["eval \"exec /usr/local/bin/perl"]], [0, b['#!/bin/perl']], [0, b['#!/bin/env perl']], [0, b['#!/usr/bin/perl']], [0, b['#!/usr/local/bin/perl']]]],
    ['text/x-php', [[0, b['<?php']]]],
    ['text/x-python', [[0, b['#!/bin/python']], [0, b['#! /bin/python']], [0, b["eval \"exec /bin/python"]], [0, b['#!/usr/bin/python']], [0, b['#! /usr/bin/python']], [0, b["eval \"exec /usr/bin/python"]], [0, b['#!/usr/local/bin/python']], [0, b['#! /usr/local/bin/python']], [0, b["eval \"exec /usr/local/bin/python"]], [1, b['/bin/env python']]]],
    ['text/x-vcalendar', [[0, b['BEGIN:VCALENDAR'], [[15..30, b['VERSION:1.0']]]]]],
    ['text/x-vcard', [[0, b['BEGIN:VCARD']]]],
    ['video/mj2', [[0, b["\000\000\000\fjP  \r\n\207\n"], [[20, b['mjp2']]]]]],
    ['video/x-jng', [[0, b["\213JNG"]]]],
    ['video/x-mng', [[0, b["\212MNG"]]]],
    ['video/x-ms-asf', [[0, b["0&\262u"]]]],
    ['video/x-sgi-movie', [[0, b["MOVI\000"]], [0, b["MOVI\001"]], [0, b["MOVI\002"]], [0, b["MOVI\376"]], [0, b["MOVI\377"]]]],
    ['application/gzip', [[0, b["\037\213"]], [0, b["\037\213"]]]],
    ['application/zlib', [[0, b["x\001"]], [0, b['x^']], [0, b["x\234"]], [0, b["x\332"]]]],
    ['application/java-vm', [[0, b["\312\376\272\276"]]]],
    ['application/vnd.wordperfect', [[0, b["\377WPC"]]]],
    ['application/x-bzip', [[0, b['BZh']]]],
    ['application/x-bzip2', [[0, b['BZh91']]]],
    ['application/x-font-adobe-metric', [[0, b['StartFontMetrics']]]],
    ['application/x-font-printer-metric', [[0, b["\000\001"], [[4, b["\000\000Copyr"]]]]]],
    ['application/x-font-ttf', [[0, b["\000\001\000\000"]]]],
    ['application/x-matroska', [[0, b["\032E\337\243"]]]],
    ['application/x-mysql-misam-compressed-index', [[0, b["\376\376\006"]], [0, b["\376\376\a"]]]],
    ['application/x-mysql-misam-index', [[0, b["\376\376\003"]], [0, b["\376\376\005"]]]],
    ['application/x-mysql-table-definition', [[0, b["\376\001\a"]], [0, b["\376\001\b"]], [0, b["\376\001\t"]], [0, b["\376\001\n"]], [0, b["\376\001\v"]], [0, b["\376\001\f"]]]],
    ['application/x-sas-data', [[84, b['SAS FILE']]]],
    ['application/x-sas-data-v6', [[0, b['SAS     6.']], [0, b['SAS     7.']], [0, b['SAS     8.0']], [0, b['SAS     9.0']]]],
    ['application/x-sas-xport', [[0, b['HEADER RECORD*******LIBRARY HEADER RECORD!!!!!!!']]]],
    ['application/x-stata-dta', [[0, b['<stata_dta>']]]],
    ['application/x-tar', [[257, b["ustar\000"]]]],
    ['application/x-tika-msoffice', [[0..8, b["\320\317\021\340\241\261\032\341"]]]],
    ['application/x-x509-key;format=der', []],
    ['application/xhtml+xml', [[0..8192, b['<html xmlns=']]]],
    ['audio/ac3', [[0, b["\vw"]]]],
    ['audio/amr', [[0, b["#!AMR\n"]], [0, b['#!AMR']]]],
    ['image/vnd.zbrush.pcx', [[0, b["\n"], [[1, b["\000"]], [1, b["\002"]], [1, b["\003"]], [1, b["\004"]], [1, b["\005"]]]]]],
    ['message/rfc822', [[0..1000, b["\nMessage-ID:"]]]],
    ['text/vtt', [[0, b["WEBVTT\r"]], [0, b["WEBVTT\n"]], [0, b['0xfeff'], [[2, b["WEBVTT\r"]]]], [0, b['0xfeff'], [[2, b["WEBVTT\n"]]]], [0, b["WEBVTT FILE\r"]], [0, b["WEBVTT FILE\n"]]]],
    ['text/x-matlab', [[0, b["function [a-zA-Z][A-Za-z0-9_]{0,62}\\\\s*="]]]],
    ['text/x-matlab', [[0, b["function [a-zA-Z][A-Za-z0-9_]{0,62}[\\\\r\\\\n]"]]]],
    ['application/inf', [[0, b['[version]']], [0, b['[strings]']]]],
    ['application/x-bibtex-text-file', [[0, b['%'], [[2..128, b["\n@article{"]], [2..128, b["\n@book{"]], [2..128, b["\n@inbook{"]], [2..128, b["\n@incollection{"]], [2..128, b["\n@inproceedings{"]], [2..128, b["\n@manual{"]], [2..128, b["\n@misc{"]], [2..128, b["\n@preamble{"]], [2..128, b["\n@phdthesis{"]], [2..128, b["\n@string{"]], [2..128, b["\n@techreport{"]], [2..128, b["\n@unpublished{"]]]]]],
    ['application/xml', [[0, b['<!--']]]],
    ['text/vtt', [[0, b['WEBVTT '], [[10..50, b["\n\n"]]]], [0, b['WEBVTT '], [[10..50, b["\r\r"]]]], [0, b['WEBVTT '], [[10..50, b["\r\n\r\n"]]]]]],
    ['text/x-chdr', [[0, b['#ifndef ']]]],
    ['text/x-csrc', [[0, b['#include ']]]],
    ['image/x-jp2-codestream', [[0, b["\377O\377Q"]]]],
    ['text/x-matlab', [[0, b['%'], [[2..120, b["\n%"]]]], [0, b['%'], [[2..120, b["\r%"]]]], [0, b['%%']]]],
    ['application/pdf', [[1..512, b['%PDF-1.']], [1..512, b['%PDF-2.']]]],
    ['audio/basic', [[0, b['.snd'], [[12, b["\000\000\000\001"]], [12, b["\000\000\000\002"]], [12, b["\000\000\000\003"]], [12, b["\000\000\000\004"]], [12, b["\000\000\000\005"]], [12, b["\000\000\000\006"]], [12, b["\000\000\000\a"]]]], [0, b[".snd\000\000\000"]]]],
    ['audio/midi', [[0, b['MThd']]]],
    ['audio/vnd.wave', [[0, b['RIFF'], [[8, b['WAVE']]]]]],
    ['audio/x-adpcm', [[0, b['.snd'], [[12, b["\000\000\000\027"]]]]]],
    ['audio/x-aiff', [[0, b['FORM'], [[8, b['AIFF']]]], [0, b['FORM'], [[8, b['AIFC']]]], [0, b['FORM'], [[8, b['8SVX']]]], [0, b["FORM\000"]]]],
    ['audio/x-dec-adpcm', [[0, b["\000ds."], [[12, b["\000\000\000\027"]]]]]],
    ['audio/x-dec-basic', [[0, b["\000ds."], [[12, b["\000\000\000\001"]], [12, b["\000\000\000\002"]], [12, b["\000\000\000\003"]], [12, b["\000\000\000\004"]], [12, b["\000\000\000\005"]], [12, b["\000\000\000\006"]], [12, b["\000\000\000\a"]]]]]],
    ['text/html', [[128..8192, b['<html']]]],
    ['text/plain', [[0, b['This is TeX,']], [0, b['This is METAFONT,']], [0, b['/*']], [0, b['//']], [0, b[';;']], [0, b["\376\377"]], [0, b["\377\376"]], [0, b["\357\273\277"]]]],
    ['text/x-makefile', [[0, b['# Makefile.in generated by']], [0, b['#!make']], [0, b['#!/usr/bin/make']], [0, b['#!/usr/local/bin/make']], [0, b['#!/usr/bin/env make']]]],
  ]
end
