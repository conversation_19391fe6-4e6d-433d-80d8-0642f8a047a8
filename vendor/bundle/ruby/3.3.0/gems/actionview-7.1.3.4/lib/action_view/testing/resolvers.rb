# frozen_string_literal: true

require "action_view/template/resolver"

module ActionView # :nodoc:
  # Use FixtureResolver in your tests to simulate the presence of files on the
  # file system. This is used internally by Rails' own test suite, and is
  # useful for testing extensions that have no way of knowing what the file
  # system will look like at runtime.
  class FixtureResolver < FileSystemResolver
    def initialize(hash = {})
      super("")
      @hash = hash
      @path = ""
    end

    def data
      @hash
    end

    def to_s
      @hash.keys.join(", ")
    end

    private
      def template_glob(glob)
        @hash.keys.filter_map do |path|
          "/#{path}" if File.fnmatch(glob, path)
        end
      end

      def source_for_template(template)
        @hash[template.from(1)]
      end
  end

  class NullResolver < Resolver
    def find_templates(name, prefix, partial, details, locals = [])
      path = TemplatePath.build(name, prefix, partial)
      handler = ActionView::Template::Handlers::Raw
      [ActionView::Template.new("Template generated by <PERSON>ull Resolver", path.virtual, handler, virtual_path: path.virtual, format: nil, variant: nil, locals: locals)]
    end
  end
end
