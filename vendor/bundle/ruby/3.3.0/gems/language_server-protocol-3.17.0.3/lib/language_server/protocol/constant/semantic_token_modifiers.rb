module LanguageServer
  module Protocol
    module Constant
      module SemanticTokenModifiers
        DECLARATION = 'declaration'
        DEFINITION = 'definition'
        READONLY = 'readonly'
        STATIC = 'static'
        DEPRECATED = 'deprecated'
        ABSTRACT = 'abstract'
        ASYNC = 'async'
        MODIFICATION = 'modification'
        DOCUMENTATION = 'documentation'
        DEFAULT_LIBRARY = 'defaultLibrary'
      end
    end
  end
end
