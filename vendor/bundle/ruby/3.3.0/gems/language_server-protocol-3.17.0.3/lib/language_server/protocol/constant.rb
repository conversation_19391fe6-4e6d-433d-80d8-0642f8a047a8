module LanguageServer
  module Protocol
    module Constant
      autoload :<PERSON><PERSON><PERSON><PERSON><PERSON>, "language_server/protocol/constant/code_action_kind"
      autoload :CodeAction<PERSON><PERSON><PERSON><PERSON><PERSON>, "language_server/protocol/constant/code_action_trigger_kind"
      autoload :CompletionItemKind, "language_server/protocol/constant/completion_item_kind"
      autoload :Comple<PERSON><PERSON><PERSON><PERSON>ag, "language_server/protocol/constant/completion_item_tag"
      autoload :CompletionTriggerKind, "language_server/protocol/constant/completion_trigger_kind"
      autoload :DiagnosticSeverity, "language_server/protocol/constant/diagnostic_severity"
      autoload :DiagnosticTag, "language_server/protocol/constant/diagnostic_tag"
      autoload :DocumentDiagnosticReportKind, "language_server/protocol/constant/document_diagnostic_report_kind"
      autoload :Document<PERSON>ighlight<PERSON><PERSON>, "language_server/protocol/constant/document_highlight_kind"
      autoload :ErrorC<PERSON>, "language_server/protocol/constant/error_codes"
      autoload :FailureHandlingKind, "language_server/protocol/constant/failure_handling_kind"
      autoload :<PERSON><PERSON><PERSON>e<PERSON>ype, "language_server/protocol/constant/file_change_type"
      autoload :FileOperation<PERSON><PERSON><PERSON><PERSON><PERSON>, "language_server/protocol/constant/file_operation_pattern_kind"
      autoload :Folding<PERSON>ang<PERSON><PERSON><PERSON>, "language_server/protocol/constant/folding_range_kind"
      autoload :InitializeErrorCodes, "language_server/protocol/constant/initialize_error_codes"
      autoload :InlayHintKind, "language_server/protocol/constant/inlay_hint_kind"
      autoload :InsertTextFormat, "language_server/protocol/constant/insert_text_format"
      autoload :InsertTextMode, "language_server/protocol/constant/insert_text_mode"
      autoload :MarkupKind, "language_server/protocol/constant/markup_kind"
      autoload :MessageType, "language_server/protocol/constant/message_type"
      autoload :MonikerKind, "language_server/protocol/constant/moniker_kind"
      autoload :NotebookCellKind, "language_server/protocol/constant/notebook_cell_kind"
      autoload :PositionEncodingKind, "language_server/protocol/constant/position_encoding_kind"
      autoload :PrepareSupportDefaultBehavior, "language_server/protocol/constant/prepare_support_default_behavior"
      autoload :ResourceOperationKind, "language_server/protocol/constant/resource_operation_kind"
      autoload :SemanticTokenModifiers, "language_server/protocol/constant/semantic_token_modifiers"
      autoload :SemanticTokenTypes, "language_server/protocol/constant/semantic_token_types"
      autoload :SignatureHelpTriggerKind, "language_server/protocol/constant/signature_help_trigger_kind"
      autoload :SymbolKind, "language_server/protocol/constant/symbol_kind"
      autoload :SymbolTag, "language_server/protocol/constant/symbol_tag"
      autoload :TextDocumentSaveReason, "language_server/protocol/constant/text_document_save_reason"
      autoload :TextDocumentSyncKind, "language_server/protocol/constant/text_document_sync_kind"
      autoload :TokenFormat, "language_server/protocol/constant/token_format"
      autoload :UniquenessLevel, "language_server/protocol/constant/uniqueness_level"
      autoload :WatchKind, "language_server/protocol/constant/watch_kind"

      require "language_server/protocol/constant/code_action_kind"
      require "language_server/protocol/constant/code_action_trigger_kind"
      require "language_server/protocol/constant/completion_item_kind"
      require "language_server/protocol/constant/completion_item_tag"
      require "language_server/protocol/constant/completion_trigger_kind"
      require "language_server/protocol/constant/diagnostic_severity"
      require "language_server/protocol/constant/diagnostic_tag"
      require "language_server/protocol/constant/document_diagnostic_report_kind"
      require "language_server/protocol/constant/document_highlight_kind"
      require "language_server/protocol/constant/error_codes"
      require "language_server/protocol/constant/failure_handling_kind"
      require "language_server/protocol/constant/file_change_type"
      require "language_server/protocol/constant/file_operation_pattern_kind"
      require "language_server/protocol/constant/folding_range_kind"
      require "language_server/protocol/constant/initialize_error_codes"
      require "language_server/protocol/constant/inlay_hint_kind"
      require "language_server/protocol/constant/insert_text_format"
      require "language_server/protocol/constant/insert_text_mode"
      require "language_server/protocol/constant/markup_kind"
      require "language_server/protocol/constant/message_type"
      require "language_server/protocol/constant/moniker_kind"
      require "language_server/protocol/constant/notebook_cell_kind"
      require "language_server/protocol/constant/position_encoding_kind"
      require "language_server/protocol/constant/prepare_support_default_behavior"
      require "language_server/protocol/constant/resource_operation_kind"
      require "language_server/protocol/constant/semantic_token_modifiers"
      require "language_server/protocol/constant/semantic_token_types"
      require "language_server/protocol/constant/signature_help_trigger_kind"
      require "language_server/protocol/constant/symbol_kind"
      require "language_server/protocol/constant/symbol_tag"
      require "language_server/protocol/constant/text_document_save_reason"
      require "language_server/protocol/constant/text_document_sync_kind"
      require "language_server/protocol/constant/token_format"
      require "language_server/protocol/constant/uniqueness_level"
      require "language_server/protocol/constant/watch_kind"
    end
  end
end
