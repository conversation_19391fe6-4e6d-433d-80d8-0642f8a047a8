module LanguageServer
  module Protocol
    module Constant
      #
      # A symbol kind.
      #
      module SymbolKind
        FILE = 1
        MODULE = 2
        NAMESPACE = 3
        PACKAGE = 4
        CLASS = 5
        METHOD = 6
        PROPERTY = 7
        FIELD = 8
        CONSTRUCTOR = 9
        ENUM = 10
        INTERFACE = 11
        FUNCTION = 12
        VARIABLE = 13
        CONSTANT = 14
        STRING = 15
        NUMBER = 16
        BOOLEAN = 17
        ARRAY = 18
        OBJECT = 19
        KEY = 20
        NULL = 21
        ENUM_MEMBER = 22
        STRUCT = 23
        EVENT = 24
        OPERATOR = 25
        TYPE_PARAMETER = 26
      end
    end
  end
end
