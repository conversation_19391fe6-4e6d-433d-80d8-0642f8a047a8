module LanguageServer
  module Protocol
    module Interface
      autoload :AnnotatedTextEdit, "language_server/protocol/interface/annotated_text_edit"
      autoload :ApplyWorkspaceEditParams, "language_server/protocol/interface/apply_workspace_edit_params"
      autoload :ApplyWorkspaceEditResult, "language_server/protocol/interface/apply_workspace_edit_result"
      autoload :CallHierarchyClientCapabilities, "language_server/protocol/interface/call_hierarchy_client_capabilities"
      autoload :CallHierarchyIncomingCall, "language_server/protocol/interface/call_hierarchy_incoming_call"
      autoload :CallHierarchyIncomingCallsParams, "language_server/protocol/interface/call_hierarchy_incoming_calls_params"
      autoload :CallHierarchyItem, "language_server/protocol/interface/call_hierarchy_item"
      autoload :CallHierarchyOptions, "language_server/protocol/interface/call_hierarchy_options"
      autoload :CallHierarchyOutgoingCall, "language_server/protocol/interface/call_hierarchy_outgoing_call"
      autoload :CallHierarchyOutgoingCallsParams, "language_server/protocol/interface/call_hierarchy_outgoing_calls_params"
      autoload :CallHierarchyPrepareParams, "language_server/protocol/interface/call_hierarchy_prepare_params"
      autoload :CallHierarchyRegistrationOptions, "language_server/protocol/interface/call_hierarchy_registration_options"
      autoload :CancelParams, "language_server/protocol/interface/cancel_params"
      autoload :ChangeAnnotation, "language_server/protocol/interface/change_annotation"
      autoload :ClientCapabilities, "language_server/protocol/interface/client_capabilities"
      autoload :CodeAction, "language_server/protocol/interface/code_action"
      autoload :CodeActionClientCapabilities, "language_server/protocol/interface/code_action_client_capabilities"
      autoload :CodeActionContext, "language_server/protocol/interface/code_action_context"
      autoload :CodeActionOptions, "language_server/protocol/interface/code_action_options"
      autoload :CodeActionParams, "language_server/protocol/interface/code_action_params"
      autoload :CodeActionRegistrationOptions, "language_server/protocol/interface/code_action_registration_options"
      autoload :CodeDescription, "language_server/protocol/interface/code_description"
      autoload :CodeLens, "language_server/protocol/interface/code_lens"
      autoload :CodeLensClientCapabilities, "language_server/protocol/interface/code_lens_client_capabilities"
      autoload :CodeLensOptions, "language_server/protocol/interface/code_lens_options"
      autoload :CodeLensParams, "language_server/protocol/interface/code_lens_params"
      autoload :CodeLensRegistrationOptions, "language_server/protocol/interface/code_lens_registration_options"
      autoload :CodeLensWorkspaceClientCapabilities, "language_server/protocol/interface/code_lens_workspace_client_capabilities"
      autoload :Color, "language_server/protocol/interface/color"
      autoload :ColorInformation, "language_server/protocol/interface/color_information"
      autoload :ColorPresentation, "language_server/protocol/interface/color_presentation"
      autoload :ColorPresentationParams, "language_server/protocol/interface/color_presentation_params"
      autoload :Command, "language_server/protocol/interface/command"
      autoload :CompletionClientCapabilities, "language_server/protocol/interface/completion_client_capabilities"
      autoload :CompletionContext, "language_server/protocol/interface/completion_context"
      autoload :CompletionItem, "language_server/protocol/interface/completion_item"
      autoload :CompletionItemLabelDetails, "language_server/protocol/interface/completion_item_label_details"
      autoload :CompletionList, "language_server/protocol/interface/completion_list"
      autoload :CompletionOptions, "language_server/protocol/interface/completion_options"
      autoload :CompletionParams, "language_server/protocol/interface/completion_params"
      autoload :CompletionRegistrationOptions, "language_server/protocol/interface/completion_registration_options"
      autoload :ConfigurationItem, "language_server/protocol/interface/configuration_item"
      autoload :ConfigurationParams, "language_server/protocol/interface/configuration_params"
      autoload :CreateFile, "language_server/protocol/interface/create_file"
      autoload :CreateFileOptions, "language_server/protocol/interface/create_file_options"
      autoload :CreateFilesParams, "language_server/protocol/interface/create_files_params"
      autoload :DeclarationClientCapabilities, "language_server/protocol/interface/declaration_client_capabilities"
      autoload :DeclarationOptions, "language_server/protocol/interface/declaration_options"
      autoload :DeclarationParams, "language_server/protocol/interface/declaration_params"
      autoload :DeclarationRegistrationOptions, "language_server/protocol/interface/declaration_registration_options"
      autoload :DefinitionClientCapabilities, "language_server/protocol/interface/definition_client_capabilities"
      autoload :DefinitionOptions, "language_server/protocol/interface/definition_options"
      autoload :DefinitionParams, "language_server/protocol/interface/definition_params"
      autoload :DefinitionRegistrationOptions, "language_server/protocol/interface/definition_registration_options"
      autoload :DeleteFile, "language_server/protocol/interface/delete_file"
      autoload :DeleteFileOptions, "language_server/protocol/interface/delete_file_options"
      autoload :DeleteFilesParams, "language_server/protocol/interface/delete_files_params"
      autoload :Diagnostic, "language_server/protocol/interface/diagnostic"
      autoload :DiagnosticClientCapabilities, "language_server/protocol/interface/diagnostic_client_capabilities"
      autoload :DiagnosticOptions, "language_server/protocol/interface/diagnostic_options"
      autoload :DiagnosticRegistrationOptions, "language_server/protocol/interface/diagnostic_registration_options"
      autoload :DiagnosticRelatedInformation, "language_server/protocol/interface/diagnostic_related_information"
      autoload :DiagnosticServerCancellationData, "language_server/protocol/interface/diagnostic_server_cancellation_data"
      autoload :DiagnosticWorkspaceClientCapabilities, "language_server/protocol/interface/diagnostic_workspace_client_capabilities"
      autoload :DidChangeConfigurationClientCapabilities, "language_server/protocol/interface/did_change_configuration_client_capabilities"
      autoload :DidChangeConfigurationParams, "language_server/protocol/interface/did_change_configuration_params"
      autoload :DidChangeNotebookDocumentParams, "language_server/protocol/interface/did_change_notebook_document_params"
      autoload :DidChangeTextDocumentParams, "language_server/protocol/interface/did_change_text_document_params"
      autoload :DidChangeWatchedFilesClientCapabilities, "language_server/protocol/interface/did_change_watched_files_client_capabilities"
      autoload :DidChangeWatchedFilesParams, "language_server/protocol/interface/did_change_watched_files_params"
      autoload :DidChangeWatchedFilesRegistrationOptions, "language_server/protocol/interface/did_change_watched_files_registration_options"
      autoload :DidChangeWorkspaceFoldersParams, "language_server/protocol/interface/did_change_workspace_folders_params"
      autoload :DidCloseNotebookDocumentParams, "language_server/protocol/interface/did_close_notebook_document_params"
      autoload :DidCloseTextDocumentParams, "language_server/protocol/interface/did_close_text_document_params"
      autoload :DidOpenNotebookDocumentParams, "language_server/protocol/interface/did_open_notebook_document_params"
      autoload :DidOpenTextDocumentParams, "language_server/protocol/interface/did_open_text_document_params"
      autoload :DidSaveNotebookDocumentParams, "language_server/protocol/interface/did_save_notebook_document_params"
      autoload :DidSaveTextDocumentParams, "language_server/protocol/interface/did_save_text_document_params"
      autoload :DocumentColorClientCapabilities, "language_server/protocol/interface/document_color_client_capabilities"
      autoload :DocumentColorOptions, "language_server/protocol/interface/document_color_options"
      autoload :DocumentColorParams, "language_server/protocol/interface/document_color_params"
      autoload :DocumentColorRegistrationOptions, "language_server/protocol/interface/document_color_registration_options"
      autoload :DocumentDiagnosticParams, "language_server/protocol/interface/document_diagnostic_params"
      autoload :DocumentDiagnosticReportPartialResult, "language_server/protocol/interface/document_diagnostic_report_partial_result"
      autoload :DocumentFilter, "language_server/protocol/interface/document_filter"
      autoload :DocumentFormattingClientCapabilities, "language_server/protocol/interface/document_formatting_client_capabilities"
      autoload :DocumentFormattingOptions, "language_server/protocol/interface/document_formatting_options"
      autoload :DocumentFormattingParams, "language_server/protocol/interface/document_formatting_params"
      autoload :DocumentFormattingRegistrationOptions, "language_server/protocol/interface/document_formatting_registration_options"
      autoload :DocumentHighlight, "language_server/protocol/interface/document_highlight"
      autoload :DocumentHighlightClientCapabilities, "language_server/protocol/interface/document_highlight_client_capabilities"
      autoload :DocumentHighlightOptions, "language_server/protocol/interface/document_highlight_options"
      autoload :DocumentHighlightParams, "language_server/protocol/interface/document_highlight_params"
      autoload :DocumentHighlightRegistrationOptions, "language_server/protocol/interface/document_highlight_registration_options"
      autoload :DocumentLink, "language_server/protocol/interface/document_link"
      autoload :DocumentLinkClientCapabilities, "language_server/protocol/interface/document_link_client_capabilities"
      autoload :DocumentLinkOptions, "language_server/protocol/interface/document_link_options"
      autoload :DocumentLinkParams, "language_server/protocol/interface/document_link_params"
      autoload :DocumentLinkRegistrationOptions, "language_server/protocol/interface/document_link_registration_options"
      autoload :DocumentOnTypeFormattingClientCapabilities, "language_server/protocol/interface/document_on_type_formatting_client_capabilities"
      autoload :DocumentOnTypeFormattingOptions, "language_server/protocol/interface/document_on_type_formatting_options"
      autoload :DocumentOnTypeFormattingParams, "language_server/protocol/interface/document_on_type_formatting_params"
      autoload :DocumentOnTypeFormattingRegistrationOptions, "language_server/protocol/interface/document_on_type_formatting_registration_options"
      autoload :DocumentRangeFormattingClientCapabilities, "language_server/protocol/interface/document_range_formatting_client_capabilities"
      autoload :DocumentRangeFormattingOptions, "language_server/protocol/interface/document_range_formatting_options"
      autoload :DocumentRangeFormattingParams, "language_server/protocol/interface/document_range_formatting_params"
      autoload :DocumentRangeFormattingRegistrationOptions, "language_server/protocol/interface/document_range_formatting_registration_options"
      autoload :DocumentSymbol, "language_server/protocol/interface/document_symbol"
      autoload :DocumentSymbolClientCapabilities, "language_server/protocol/interface/document_symbol_client_capabilities"
      autoload :DocumentSymbolOptions, "language_server/protocol/interface/document_symbol_options"
      autoload :DocumentSymbolParams, "language_server/protocol/interface/document_symbol_params"
      autoload :DocumentSymbolRegistrationOptions, "language_server/protocol/interface/document_symbol_registration_options"
      autoload :ExecuteCommandClientCapabilities, "language_server/protocol/interface/execute_command_client_capabilities"
      autoload :ExecuteCommandOptions, "language_server/protocol/interface/execute_command_options"
      autoload :ExecuteCommandParams, "language_server/protocol/interface/execute_command_params"
      autoload :ExecuteCommandRegistrationOptions, "language_server/protocol/interface/execute_command_registration_options"
      autoload :ExecutionSummary, "language_server/protocol/interface/execution_summary"
      autoload :FileCreate, "language_server/protocol/interface/file_create"
      autoload :FileDelete, "language_server/protocol/interface/file_delete"
      autoload :FileEvent, "language_server/protocol/interface/file_event"
      autoload :FileOperationFilter, "language_server/protocol/interface/file_operation_filter"
      autoload :FileOperationPattern, "language_server/protocol/interface/file_operation_pattern"
      autoload :FileOperationPatternOptions, "language_server/protocol/interface/file_operation_pattern_options"
      autoload :FileOperationRegistrationOptions, "language_server/protocol/interface/file_operation_registration_options"
      autoload :FileRename, "language_server/protocol/interface/file_rename"
      autoload :FileSystemWatcher, "language_server/protocol/interface/file_system_watcher"
      autoload :FoldingRange, "language_server/protocol/interface/folding_range"
      autoload :FoldingRangeClientCapabilities, "language_server/protocol/interface/folding_range_client_capabilities"
      autoload :FoldingRangeOptions, "language_server/protocol/interface/folding_range_options"
      autoload :FoldingRangeParams, "language_server/protocol/interface/folding_range_params"
      autoload :FoldingRangeRegistrationOptions, "language_server/protocol/interface/folding_range_registration_options"
      autoload :FormattingOptions, "language_server/protocol/interface/formatting_options"
      autoload :FullDocumentDiagnosticReport, "language_server/protocol/interface/full_document_diagnostic_report"
      autoload :Hover, "language_server/protocol/interface/hover"
      autoload :HoverClientCapabilities, "language_server/protocol/interface/hover_client_capabilities"
      autoload :HoverOptions, "language_server/protocol/interface/hover_options"
      autoload :HoverParams, "language_server/protocol/interface/hover_params"
      autoload :HoverParams, "language_server/protocol/interface/hover_params"
      autoload :HoverRegistrationOptions, "language_server/protocol/interface/hover_registration_options"
      autoload :HoverResult, "language_server/protocol/interface/hover_result"
      autoload :ImplementationClientCapabilities, "language_server/protocol/interface/implementation_client_capabilities"
      autoload :ImplementationOptions, "language_server/protocol/interface/implementation_options"
      autoload :ImplementationParams, "language_server/protocol/interface/implementation_params"
      autoload :ImplementationRegistrationOptions, "language_server/protocol/interface/implementation_registration_options"
      autoload :InitializeError, "language_server/protocol/interface/initialize_error"
      autoload :InitializeParams, "language_server/protocol/interface/initialize_params"
      autoload :InitializeResult, "language_server/protocol/interface/initialize_result"
      autoload :InitializedParams, "language_server/protocol/interface/initialized_params"
      autoload :InlayHint, "language_server/protocol/interface/inlay_hint"
      autoload :InlayHintClientCapabilities, "language_server/protocol/interface/inlay_hint_client_capabilities"
      autoload :InlayHintLabelPart, "language_server/protocol/interface/inlay_hint_label_part"
      autoload :InlayHintOptions, "language_server/protocol/interface/inlay_hint_options"
      autoload :InlayHintParams, "language_server/protocol/interface/inlay_hint_params"
      autoload :InlayHintRegistrationOptions, "language_server/protocol/interface/inlay_hint_registration_options"
      autoload :InlayHintWorkspaceClientCapabilities, "language_server/protocol/interface/inlay_hint_workspace_client_capabilities"
      autoload :InlineValueClientCapabilities, "language_server/protocol/interface/inline_value_client_capabilities"
      autoload :InlineValueContext, "language_server/protocol/interface/inline_value_context"
      autoload :InlineValueEvaluatableExpression, "language_server/protocol/interface/inline_value_evaluatable_expression"
      autoload :InlineValueOptions, "language_server/protocol/interface/inline_value_options"
      autoload :InlineValueParams, "language_server/protocol/interface/inline_value_params"
      autoload :InlineValueRegistrationOptions, "language_server/protocol/interface/inline_value_registration_options"
      autoload :InlineValueText, "language_server/protocol/interface/inline_value_text"
      autoload :InlineValueVariableLookup, "language_server/protocol/interface/inline_value_variable_lookup"
      autoload :InlineValueWorkspaceClientCapabilities, "language_server/protocol/interface/inline_value_workspace_client_capabilities"
      autoload :InsertReplaceEdit, "language_server/protocol/interface/insert_replace_edit"
      autoload :LinkedEditingRangeClientCapabilities, "language_server/protocol/interface/linked_editing_range_client_capabilities"
      autoload :LinkedEditingRangeOptions, "language_server/protocol/interface/linked_editing_range_options"
      autoload :LinkedEditingRangeParams, "language_server/protocol/interface/linked_editing_range_params"
      autoload :LinkedEditingRangeRegistrationOptions, "language_server/protocol/interface/linked_editing_range_registration_options"
      autoload :LinkedEditingRanges, "language_server/protocol/interface/linked_editing_ranges"
      autoload :Location, "language_server/protocol/interface/location"
      autoload :LocationLink, "language_server/protocol/interface/location_link"
      autoload :LogMessageParams, "language_server/protocol/interface/log_message_params"
      autoload :LogTraceParams, "language_server/protocol/interface/log_trace_params"
      autoload :MarkupContent, "language_server/protocol/interface/markup_content"
      autoload :Message, "language_server/protocol/interface/message"
      autoload :MessageActionItem, "language_server/protocol/interface/message_action_item"
      autoload :Moniker, "language_server/protocol/interface/moniker"
      autoload :MonikerClientCapabilities, "language_server/protocol/interface/moniker_client_capabilities"
      autoload :MonikerOptions, "language_server/protocol/interface/moniker_options"
      autoload :MonikerParams, "language_server/protocol/interface/moniker_params"
      autoload :MonikerRegistrationOptions, "language_server/protocol/interface/moniker_registration_options"
      autoload :NotebookCell, "language_server/protocol/interface/notebook_cell"
      autoload :NotebookCellArrayChange, "language_server/protocol/interface/notebook_cell_array_change"
      autoload :NotebookCellTextDocumentFilter, "language_server/protocol/interface/notebook_cell_text_document_filter"
      autoload :NotebookDocument, "language_server/protocol/interface/notebook_document"
      autoload :NotebookDocumentChangeEvent, "language_server/protocol/interface/notebook_document_change_event"
      autoload :NotebookDocumentClientCapabilities, "language_server/protocol/interface/notebook_document_client_capabilities"
      autoload :NotebookDocumentFilter, "language_server/protocol/interface/notebook_document_filter"
      autoload :NotebookDocumentIdentifier, "language_server/protocol/interface/notebook_document_identifier"
      autoload :NotebookDocumentSyncClientCapabilities, "language_server/protocol/interface/notebook_document_sync_client_capabilities"
      autoload :NotebookDocumentSyncOptions, "language_server/protocol/interface/notebook_document_sync_options"
      autoload :NotebookDocumentSyncRegistrationOptions, "language_server/protocol/interface/notebook_document_sync_registration_options"
      autoload :NotificationMessage, "language_server/protocol/interface/notification_message"
      autoload :OptionalVersionedTextDocumentIdentifier, "language_server/protocol/interface/optional_versioned_text_document_identifier"
      autoload :ParameterInformation, "language_server/protocol/interface/parameter_information"
      autoload :PartialResultParams, "language_server/protocol/interface/partial_result_params"
      autoload :Position, "language_server/protocol/interface/position"
      autoload :PrepareRenameParams, "language_server/protocol/interface/prepare_rename_params"
      autoload :PreviousResultId, "language_server/protocol/interface/previous_result_id"
      autoload :ProgressParams, "language_server/protocol/interface/progress_params"
      autoload :PublishDiagnosticsClientCapabilities, "language_server/protocol/interface/publish_diagnostics_client_capabilities"
      autoload :PublishDiagnosticsParams, "language_server/protocol/interface/publish_diagnostics_params"
      autoload :Range, "language_server/protocol/interface/range"
      autoload :ReferenceClientCapabilities, "language_server/protocol/interface/reference_client_capabilities"
      autoload :ReferenceContext, "language_server/protocol/interface/reference_context"
      autoload :ReferenceOptions, "language_server/protocol/interface/reference_options"
      autoload :ReferenceParams, "language_server/protocol/interface/reference_params"
      autoload :ReferenceRegistrationOptions, "language_server/protocol/interface/reference_registration_options"
      autoload :Registration, "language_server/protocol/interface/registration"
      autoload :RegistrationParams, "language_server/protocol/interface/registration_params"
      autoload :RegularExpressionsClientCapabilities, "language_server/protocol/interface/regular_expressions_client_capabilities"
      autoload :RelatedFullDocumentDiagnosticReport, "language_server/protocol/interface/related_full_document_diagnostic_report"
      autoload :RelatedUnchangedDocumentDiagnosticReport, "language_server/protocol/interface/related_unchanged_document_diagnostic_report"
      autoload :RelativePattern, "language_server/protocol/interface/relative_pattern"
      autoload :RenameClientCapabilities, "language_server/protocol/interface/rename_client_capabilities"
      autoload :RenameFile, "language_server/protocol/interface/rename_file"
      autoload :RenameFileOptions, "language_server/protocol/interface/rename_file_options"
      autoload :RenameFilesParams, "language_server/protocol/interface/rename_files_params"
      autoload :RenameOptions, "language_server/protocol/interface/rename_options"
      autoload :RenameParams, "language_server/protocol/interface/rename_params"
      autoload :RenameRegistrationOptions, "language_server/protocol/interface/rename_registration_options"
      autoload :RequestMessage, "language_server/protocol/interface/request_message"
      autoload :ResponseError, "language_server/protocol/interface/response_error"
      autoload :ResponseMessage, "language_server/protocol/interface/response_message"
      autoload :SaveOptions, "language_server/protocol/interface/save_options"
      autoload :SelectionRange, "language_server/protocol/interface/selection_range"
      autoload :SelectionRangeClientCapabilities, "language_server/protocol/interface/selection_range_client_capabilities"
      autoload :SelectionRangeOptions, "language_server/protocol/interface/selection_range_options"
      autoload :SelectionRangeParams, "language_server/protocol/interface/selection_range_params"
      autoload :SelectionRangeRegistrationOptions, "language_server/protocol/interface/selection_range_registration_options"
      autoload :SemanticTokens, "language_server/protocol/interface/semantic_tokens"
      autoload :SemanticTokensClientCapabilities, "language_server/protocol/interface/semantic_tokens_client_capabilities"
      autoload :SemanticTokensDelta, "language_server/protocol/interface/semantic_tokens_delta"
      autoload :SemanticTokensDeltaParams, "language_server/protocol/interface/semantic_tokens_delta_params"
      autoload :SemanticTokensDeltaPartialResult, "language_server/protocol/interface/semantic_tokens_delta_partial_result"
      autoload :SemanticTokensEdit, "language_server/protocol/interface/semantic_tokens_edit"
      autoload :SemanticTokensLegend, "language_server/protocol/interface/semantic_tokens_legend"
      autoload :SemanticTokensOptions, "language_server/protocol/interface/semantic_tokens_options"
      autoload :SemanticTokensParams, "language_server/protocol/interface/semantic_tokens_params"
      autoload :SemanticTokensPartialResult, "language_server/protocol/interface/semantic_tokens_partial_result"
      autoload :SemanticTokensRangeParams, "language_server/protocol/interface/semantic_tokens_range_params"
      autoload :SemanticTokensRegistrationOptions, "language_server/protocol/interface/semantic_tokens_registration_options"
      autoload :SemanticTokensWorkspaceClientCapabilities, "language_server/protocol/interface/semantic_tokens_workspace_client_capabilities"
      autoload :ServerCapabilities, "language_server/protocol/interface/server_capabilities"
      autoload :SetTraceParams, "language_server/protocol/interface/set_trace_params"
      autoload :ShowDocumentClientCapabilities, "language_server/protocol/interface/show_document_client_capabilities"
      autoload :ShowDocumentParams, "language_server/protocol/interface/show_document_params"
      autoload :ShowDocumentResult, "language_server/protocol/interface/show_document_result"
      autoload :ShowMessageParams, "language_server/protocol/interface/show_message_params"
      autoload :ShowMessageRequestClientCapabilities, "language_server/protocol/interface/show_message_request_client_capabilities"
      autoload :ShowMessageRequestParams, "language_server/protocol/interface/show_message_request_params"
      autoload :SignatureHelp, "language_server/protocol/interface/signature_help"
      autoload :SignatureHelpClientCapabilities, "language_server/protocol/interface/signature_help_client_capabilities"
      autoload :SignatureHelpContext, "language_server/protocol/interface/signature_help_context"
      autoload :SignatureHelpOptions, "language_server/protocol/interface/signature_help_options"
      autoload :SignatureHelpParams, "language_server/protocol/interface/signature_help_params"
      autoload :SignatureHelpRegistrationOptions, "language_server/protocol/interface/signature_help_registration_options"
      autoload :SignatureInformation, "language_server/protocol/interface/signature_information"
      autoload :StaticRegistrationOptions, "language_server/protocol/interface/static_registration_options"
      autoload :SymbolInformation, "language_server/protocol/interface/symbol_information"
      autoload :TextDocumentChangeRegistrationOptions, "language_server/protocol/interface/text_document_change_registration_options"
      autoload :TextDocumentClientCapabilities, "language_server/protocol/interface/text_document_client_capabilities"
      autoload :TextDocumentContentChangeEvent, "language_server/protocol/interface/text_document_content_change_event"
      autoload :TextDocumentEdit, "language_server/protocol/interface/text_document_edit"
      autoload :TextDocumentIdentifier, "language_server/protocol/interface/text_document_identifier"
      autoload :TextDocumentItem, "language_server/protocol/interface/text_document_item"
      autoload :TextDocumentPositionParams, "language_server/protocol/interface/text_document_position_params"
      autoload :TextDocumentRegistrationOptions, "language_server/protocol/interface/text_document_registration_options"
      autoload :TextDocumentSaveRegistrationOptions, "language_server/protocol/interface/text_document_save_registration_options"
      autoload :TextDocumentSyncClientCapabilities, "language_server/protocol/interface/text_document_sync_client_capabilities"
      autoload :TextDocumentSyncOptions, "language_server/protocol/interface/text_document_sync_options"
      autoload :TextDocumentSyncOptions, "language_server/protocol/interface/text_document_sync_options"
      autoload :TextEdit, "language_server/protocol/interface/text_edit"
      autoload :TypeDefinitionClientCapabilities, "language_server/protocol/interface/type_definition_client_capabilities"
      autoload :TypeDefinitionOptions, "language_server/protocol/interface/type_definition_options"
      autoload :TypeDefinitionParams, "language_server/protocol/interface/type_definition_params"
      autoload :TypeDefinitionRegistrationOptions, "language_server/protocol/interface/type_definition_registration_options"
      autoload :TypeHierarchyItem, "language_server/protocol/interface/type_hierarchy_item"
      autoload :TypeHierarchyOptions, "language_server/protocol/interface/type_hierarchy_options"
      autoload :TypeHierarchyPrepareParams, "language_server/protocol/interface/type_hierarchy_prepare_params"
      autoload :TypeHierarchyRegistrationOptions, "language_server/protocol/interface/type_hierarchy_registration_options"
      autoload :TypeHierarchySubtypesParams, "language_server/protocol/interface/type_hierarchy_subtypes_params"
      autoload :TypeHierarchySupertypesParams, "language_server/protocol/interface/type_hierarchy_supertypes_params"
      autoload :UnchangedDocumentDiagnosticReport, "language_server/protocol/interface/unchanged_document_diagnostic_report"
      autoload :Unregistration, "language_server/protocol/interface/unregistration"
      autoload :UnregistrationParams, "language_server/protocol/interface/unregistration_params"
      autoload :VersionedNotebookDocumentIdentifier, "language_server/protocol/interface/versioned_notebook_document_identifier"
      autoload :VersionedTextDocumentIdentifier, "language_server/protocol/interface/versioned_text_document_identifier"
      autoload :WillSaveTextDocumentParams, "language_server/protocol/interface/will_save_text_document_params"
      autoload :WorkDoneProgressBegin, "language_server/protocol/interface/work_done_progress_begin"
      autoload :WorkDoneProgressCancelParams, "language_server/protocol/interface/work_done_progress_cancel_params"
      autoload :WorkDoneProgressCreateParams, "language_server/protocol/interface/work_done_progress_create_params"
      autoload :WorkDoneProgressEnd, "language_server/protocol/interface/work_done_progress_end"
      autoload :WorkDoneProgressOptions, "language_server/protocol/interface/work_done_progress_options"
      autoload :WorkDoneProgressParams, "language_server/protocol/interface/work_done_progress_params"
      autoload :WorkDoneProgressReport, "language_server/protocol/interface/work_done_progress_report"
      autoload :WorkspaceDiagnosticParams, "language_server/protocol/interface/workspace_diagnostic_params"
      autoload :WorkspaceDiagnosticReport, "language_server/protocol/interface/workspace_diagnostic_report"
      autoload :WorkspaceDiagnosticReportPartialResult, "language_server/protocol/interface/workspace_diagnostic_report_partial_result"
      autoload :WorkspaceEdit, "language_server/protocol/interface/workspace_edit"
      autoload :WorkspaceEditClientCapabilities, "language_server/protocol/interface/workspace_edit_client_capabilities"
      autoload :WorkspaceFolder, "language_server/protocol/interface/workspace_folder"
      autoload :WorkspaceFoldersChangeEvent, "language_server/protocol/interface/workspace_folders_change_event"
      autoload :WorkspaceFoldersServerCapabilities, "language_server/protocol/interface/workspace_folders_server_capabilities"
      autoload :WorkspaceFullDocumentDiagnosticReport, "language_server/protocol/interface/workspace_full_document_diagnostic_report"
      autoload :WorkspaceSymbol, "language_server/protocol/interface/workspace_symbol"
      autoload :WorkspaceSymbolClientCapabilities, "language_server/protocol/interface/workspace_symbol_client_capabilities"
      autoload :WorkspaceSymbolOptions, "language_server/protocol/interface/workspace_symbol_options"
      autoload :WorkspaceSymbolParams, "language_server/protocol/interface/workspace_symbol_params"
      autoload :WorkspaceSymbolRegistrationOptions, "language_server/protocol/interface/workspace_symbol_registration_options"
      autoload :WorkspaceUnchangedDocumentDiagnosticReport, "language_server/protocol/interface/workspace_unchanged_document_diagnostic_report"

      require "language_server/protocol/interface/annotated_text_edit"
      require "language_server/protocol/interface/apply_workspace_edit_params"
      require "language_server/protocol/interface/apply_workspace_edit_result"
      require "language_server/protocol/interface/call_hierarchy_client_capabilities"
      require "language_server/protocol/interface/call_hierarchy_incoming_call"
      require "language_server/protocol/interface/call_hierarchy_incoming_calls_params"
      require "language_server/protocol/interface/call_hierarchy_item"
      require "language_server/protocol/interface/call_hierarchy_options"
      require "language_server/protocol/interface/call_hierarchy_outgoing_call"
      require "language_server/protocol/interface/call_hierarchy_outgoing_calls_params"
      require "language_server/protocol/interface/call_hierarchy_prepare_params"
      require "language_server/protocol/interface/call_hierarchy_registration_options"
      require "language_server/protocol/interface/cancel_params"
      require "language_server/protocol/interface/change_annotation"
      require "language_server/protocol/interface/client_capabilities"
      require "language_server/protocol/interface/code_action"
      require "language_server/protocol/interface/code_action_client_capabilities"
      require "language_server/protocol/interface/code_action_context"
      require "language_server/protocol/interface/code_action_options"
      require "language_server/protocol/interface/code_action_params"
      require "language_server/protocol/interface/code_action_registration_options"
      require "language_server/protocol/interface/code_description"
      require "language_server/protocol/interface/code_lens"
      require "language_server/protocol/interface/code_lens_client_capabilities"
      require "language_server/protocol/interface/code_lens_options"
      require "language_server/protocol/interface/code_lens_params"
      require "language_server/protocol/interface/code_lens_registration_options"
      require "language_server/protocol/interface/code_lens_workspace_client_capabilities"
      require "language_server/protocol/interface/color"
      require "language_server/protocol/interface/color_information"
      require "language_server/protocol/interface/color_presentation"
      require "language_server/protocol/interface/color_presentation_params"
      require "language_server/protocol/interface/command"
      require "language_server/protocol/interface/completion_client_capabilities"
      require "language_server/protocol/interface/completion_context"
      require "language_server/protocol/interface/completion_item"
      require "language_server/protocol/interface/completion_item_label_details"
      require "language_server/protocol/interface/completion_list"
      require "language_server/protocol/interface/completion_options"
      require "language_server/protocol/interface/completion_params"
      require "language_server/protocol/interface/completion_registration_options"
      require "language_server/protocol/interface/configuration_item"
      require "language_server/protocol/interface/configuration_params"
      require "language_server/protocol/interface/create_file"
      require "language_server/protocol/interface/create_file_options"
      require "language_server/protocol/interface/create_files_params"
      require "language_server/protocol/interface/declaration_client_capabilities"
      require "language_server/protocol/interface/declaration_options"
      require "language_server/protocol/interface/declaration_params"
      require "language_server/protocol/interface/declaration_registration_options"
      require "language_server/protocol/interface/definition_client_capabilities"
      require "language_server/protocol/interface/definition_options"
      require "language_server/protocol/interface/definition_params"
      require "language_server/protocol/interface/definition_registration_options"
      require "language_server/protocol/interface/delete_file"
      require "language_server/protocol/interface/delete_file_options"
      require "language_server/protocol/interface/delete_files_params"
      require "language_server/protocol/interface/diagnostic"
      require "language_server/protocol/interface/diagnostic_client_capabilities"
      require "language_server/protocol/interface/diagnostic_options"
      require "language_server/protocol/interface/diagnostic_registration_options"
      require "language_server/protocol/interface/diagnostic_related_information"
      require "language_server/protocol/interface/diagnostic_server_cancellation_data"
      require "language_server/protocol/interface/diagnostic_workspace_client_capabilities"
      require "language_server/protocol/interface/did_change_configuration_client_capabilities"
      require "language_server/protocol/interface/did_change_configuration_params"
      require "language_server/protocol/interface/did_change_notebook_document_params"
      require "language_server/protocol/interface/did_change_text_document_params"
      require "language_server/protocol/interface/did_change_watched_files_client_capabilities"
      require "language_server/protocol/interface/did_change_watched_files_params"
      require "language_server/protocol/interface/did_change_watched_files_registration_options"
      require "language_server/protocol/interface/did_change_workspace_folders_params"
      require "language_server/protocol/interface/did_close_notebook_document_params"
      require "language_server/protocol/interface/did_close_text_document_params"
      require "language_server/protocol/interface/did_open_notebook_document_params"
      require "language_server/protocol/interface/did_open_text_document_params"
      require "language_server/protocol/interface/did_save_notebook_document_params"
      require "language_server/protocol/interface/did_save_text_document_params"
      require "language_server/protocol/interface/document_color_client_capabilities"
      require "language_server/protocol/interface/document_color_options"
      require "language_server/protocol/interface/document_color_params"
      require "language_server/protocol/interface/document_color_registration_options"
      require "language_server/protocol/interface/document_diagnostic_params"
      require "language_server/protocol/interface/document_diagnostic_report_partial_result"
      require "language_server/protocol/interface/document_filter"
      require "language_server/protocol/interface/document_formatting_client_capabilities"
      require "language_server/protocol/interface/document_formatting_options"
      require "language_server/protocol/interface/document_formatting_params"
      require "language_server/protocol/interface/document_formatting_registration_options"
      require "language_server/protocol/interface/document_highlight"
      require "language_server/protocol/interface/document_highlight_client_capabilities"
      require "language_server/protocol/interface/document_highlight_options"
      require "language_server/protocol/interface/document_highlight_params"
      require "language_server/protocol/interface/document_highlight_registration_options"
      require "language_server/protocol/interface/document_link"
      require "language_server/protocol/interface/document_link_client_capabilities"
      require "language_server/protocol/interface/document_link_options"
      require "language_server/protocol/interface/document_link_params"
      require "language_server/protocol/interface/document_link_registration_options"
      require "language_server/protocol/interface/document_on_type_formatting_client_capabilities"
      require "language_server/protocol/interface/document_on_type_formatting_options"
      require "language_server/protocol/interface/document_on_type_formatting_params"
      require "language_server/protocol/interface/document_on_type_formatting_registration_options"
      require "language_server/protocol/interface/document_range_formatting_client_capabilities"
      require "language_server/protocol/interface/document_range_formatting_options"
      require "language_server/protocol/interface/document_range_formatting_params"
      require "language_server/protocol/interface/document_range_formatting_registration_options"
      require "language_server/protocol/interface/document_symbol"
      require "language_server/protocol/interface/document_symbol_client_capabilities"
      require "language_server/protocol/interface/document_symbol_options"
      require "language_server/protocol/interface/document_symbol_params"
      require "language_server/protocol/interface/document_symbol_registration_options"
      require "language_server/protocol/interface/execute_command_client_capabilities"
      require "language_server/protocol/interface/execute_command_options"
      require "language_server/protocol/interface/execute_command_params"
      require "language_server/protocol/interface/execute_command_registration_options"
      require "language_server/protocol/interface/execution_summary"
      require "language_server/protocol/interface/file_create"
      require "language_server/protocol/interface/file_delete"
      require "language_server/protocol/interface/file_event"
      require "language_server/protocol/interface/file_operation_filter"
      require "language_server/protocol/interface/file_operation_pattern"
      require "language_server/protocol/interface/file_operation_pattern_options"
      require "language_server/protocol/interface/file_operation_registration_options"
      require "language_server/protocol/interface/file_rename"
      require "language_server/protocol/interface/file_system_watcher"
      require "language_server/protocol/interface/folding_range"
      require "language_server/protocol/interface/folding_range_client_capabilities"
      require "language_server/protocol/interface/folding_range_options"
      require "language_server/protocol/interface/folding_range_params"
      require "language_server/protocol/interface/folding_range_registration_options"
      require "language_server/protocol/interface/formatting_options"
      require "language_server/protocol/interface/full_document_diagnostic_report"
      require "language_server/protocol/interface/hover"
      require "language_server/protocol/interface/hover_client_capabilities"
      require "language_server/protocol/interface/hover_options"
      require "language_server/protocol/interface/hover_params"
      require "language_server/protocol/interface/hover_params"
      require "language_server/protocol/interface/hover_registration_options"
      require "language_server/protocol/interface/hover_result"
      require "language_server/protocol/interface/implementation_client_capabilities"
      require "language_server/protocol/interface/implementation_options"
      require "language_server/protocol/interface/implementation_params"
      require "language_server/protocol/interface/implementation_registration_options"
      require "language_server/protocol/interface/initialize_error"
      require "language_server/protocol/interface/initialize_params"
      require "language_server/protocol/interface/initialize_result"
      require "language_server/protocol/interface/initialized_params"
      require "language_server/protocol/interface/inlay_hint"
      require "language_server/protocol/interface/inlay_hint_client_capabilities"
      require "language_server/protocol/interface/inlay_hint_label_part"
      require "language_server/protocol/interface/inlay_hint_options"
      require "language_server/protocol/interface/inlay_hint_params"
      require "language_server/protocol/interface/inlay_hint_registration_options"
      require "language_server/protocol/interface/inlay_hint_workspace_client_capabilities"
      require "language_server/protocol/interface/inline_value_client_capabilities"
      require "language_server/protocol/interface/inline_value_context"
      require "language_server/protocol/interface/inline_value_evaluatable_expression"
      require "language_server/protocol/interface/inline_value_options"
      require "language_server/protocol/interface/inline_value_params"
      require "language_server/protocol/interface/inline_value_registration_options"
      require "language_server/protocol/interface/inline_value_text"
      require "language_server/protocol/interface/inline_value_variable_lookup"
      require "language_server/protocol/interface/inline_value_workspace_client_capabilities"
      require "language_server/protocol/interface/insert_replace_edit"
      require "language_server/protocol/interface/linked_editing_range_client_capabilities"
      require "language_server/protocol/interface/linked_editing_range_options"
      require "language_server/protocol/interface/linked_editing_range_params"
      require "language_server/protocol/interface/linked_editing_range_registration_options"
      require "language_server/protocol/interface/linked_editing_ranges"
      require "language_server/protocol/interface/location"
      require "language_server/protocol/interface/location_link"
      require "language_server/protocol/interface/log_message_params"
      require "language_server/protocol/interface/log_trace_params"
      require "language_server/protocol/interface/markup_content"
      require "language_server/protocol/interface/message"
      require "language_server/protocol/interface/message_action_item"
      require "language_server/protocol/interface/moniker"
      require "language_server/protocol/interface/moniker_client_capabilities"
      require "language_server/protocol/interface/moniker_options"
      require "language_server/protocol/interface/moniker_params"
      require "language_server/protocol/interface/moniker_registration_options"
      require "language_server/protocol/interface/notebook_cell"
      require "language_server/protocol/interface/notebook_cell_array_change"
      require "language_server/protocol/interface/notebook_cell_text_document_filter"
      require "language_server/protocol/interface/notebook_document"
      require "language_server/protocol/interface/notebook_document_change_event"
      require "language_server/protocol/interface/notebook_document_client_capabilities"
      require "language_server/protocol/interface/notebook_document_filter"
      require "language_server/protocol/interface/notebook_document_identifier"
      require "language_server/protocol/interface/notebook_document_sync_client_capabilities"
      require "language_server/protocol/interface/notebook_document_sync_options"
      require "language_server/protocol/interface/notebook_document_sync_registration_options"
      require "language_server/protocol/interface/notification_message"
      require "language_server/protocol/interface/optional_versioned_text_document_identifier"
      require "language_server/protocol/interface/parameter_information"
      require "language_server/protocol/interface/partial_result_params"
      require "language_server/protocol/interface/position"
      require "language_server/protocol/interface/prepare_rename_params"
      require "language_server/protocol/interface/previous_result_id"
      require "language_server/protocol/interface/progress_params"
      require "language_server/protocol/interface/publish_diagnostics_client_capabilities"
      require "language_server/protocol/interface/publish_diagnostics_params"
      require "language_server/protocol/interface/range"
      require "language_server/protocol/interface/reference_client_capabilities"
      require "language_server/protocol/interface/reference_context"
      require "language_server/protocol/interface/reference_options"
      require "language_server/protocol/interface/reference_params"
      require "language_server/protocol/interface/reference_registration_options"
      require "language_server/protocol/interface/registration"
      require "language_server/protocol/interface/registration_params"
      require "language_server/protocol/interface/regular_expressions_client_capabilities"
      require "language_server/protocol/interface/related_full_document_diagnostic_report"
      require "language_server/protocol/interface/related_unchanged_document_diagnostic_report"
      require "language_server/protocol/interface/relative_pattern"
      require "language_server/protocol/interface/rename_client_capabilities"
      require "language_server/protocol/interface/rename_file"
      require "language_server/protocol/interface/rename_file_options"
      require "language_server/protocol/interface/rename_files_params"
      require "language_server/protocol/interface/rename_options"
      require "language_server/protocol/interface/rename_params"
      require "language_server/protocol/interface/rename_registration_options"
      require "language_server/protocol/interface/request_message"
      require "language_server/protocol/interface/response_error"
      require "language_server/protocol/interface/response_message"
      require "language_server/protocol/interface/save_options"
      require "language_server/protocol/interface/selection_range"
      require "language_server/protocol/interface/selection_range_client_capabilities"
      require "language_server/protocol/interface/selection_range_options"
      require "language_server/protocol/interface/selection_range_params"
      require "language_server/protocol/interface/selection_range_registration_options"
      require "language_server/protocol/interface/semantic_tokens"
      require "language_server/protocol/interface/semantic_tokens_client_capabilities"
      require "language_server/protocol/interface/semantic_tokens_delta"
      require "language_server/protocol/interface/semantic_tokens_delta_params"
      require "language_server/protocol/interface/semantic_tokens_delta_partial_result"
      require "language_server/protocol/interface/semantic_tokens_edit"
      require "language_server/protocol/interface/semantic_tokens_legend"
      require "language_server/protocol/interface/semantic_tokens_options"
      require "language_server/protocol/interface/semantic_tokens_params"
      require "language_server/protocol/interface/semantic_tokens_partial_result"
      require "language_server/protocol/interface/semantic_tokens_range_params"
      require "language_server/protocol/interface/semantic_tokens_registration_options"
      require "language_server/protocol/interface/semantic_tokens_workspace_client_capabilities"
      require "language_server/protocol/interface/server_capabilities"
      require "language_server/protocol/interface/set_trace_params"
      require "language_server/protocol/interface/show_document_client_capabilities"
      require "language_server/protocol/interface/show_document_params"
      require "language_server/protocol/interface/show_document_result"
      require "language_server/protocol/interface/show_message_params"
      require "language_server/protocol/interface/show_message_request_client_capabilities"
      require "language_server/protocol/interface/show_message_request_params"
      require "language_server/protocol/interface/signature_help"
      require "language_server/protocol/interface/signature_help_client_capabilities"
      require "language_server/protocol/interface/signature_help_context"
      require "language_server/protocol/interface/signature_help_options"
      require "language_server/protocol/interface/signature_help_params"
      require "language_server/protocol/interface/signature_help_registration_options"
      require "language_server/protocol/interface/signature_information"
      require "language_server/protocol/interface/static_registration_options"
      require "language_server/protocol/interface/symbol_information"
      require "language_server/protocol/interface/text_document_change_registration_options"
      require "language_server/protocol/interface/text_document_client_capabilities"
      require "language_server/protocol/interface/text_document_content_change_event"
      require "language_server/protocol/interface/text_document_edit"
      require "language_server/protocol/interface/text_document_identifier"
      require "language_server/protocol/interface/text_document_item"
      require "language_server/protocol/interface/text_document_position_params"
      require "language_server/protocol/interface/text_document_registration_options"
      require "language_server/protocol/interface/text_document_save_registration_options"
      require "language_server/protocol/interface/text_document_sync_client_capabilities"
      require "language_server/protocol/interface/text_document_sync_options"
      require "language_server/protocol/interface/text_document_sync_options"
      require "language_server/protocol/interface/text_edit"
      require "language_server/protocol/interface/type_definition_client_capabilities"
      require "language_server/protocol/interface/type_definition_options"
      require "language_server/protocol/interface/type_definition_params"
      require "language_server/protocol/interface/type_definition_registration_options"
      require "language_server/protocol/interface/type_hierarchy_item"
      require "language_server/protocol/interface/type_hierarchy_options"
      require "language_server/protocol/interface/type_hierarchy_prepare_params"
      require "language_server/protocol/interface/type_hierarchy_registration_options"
      require "language_server/protocol/interface/type_hierarchy_subtypes_params"
      require "language_server/protocol/interface/type_hierarchy_supertypes_params"
      require "language_server/protocol/interface/unchanged_document_diagnostic_report"
      require "language_server/protocol/interface/unregistration"
      require "language_server/protocol/interface/unregistration_params"
      require "language_server/protocol/interface/versioned_notebook_document_identifier"
      require "language_server/protocol/interface/versioned_text_document_identifier"
      require "language_server/protocol/interface/will_save_text_document_params"
      require "language_server/protocol/interface/work_done_progress_begin"
      require "language_server/protocol/interface/work_done_progress_cancel_params"
      require "language_server/protocol/interface/work_done_progress_create_params"
      require "language_server/protocol/interface/work_done_progress_end"
      require "language_server/protocol/interface/work_done_progress_options"
      require "language_server/protocol/interface/work_done_progress_params"
      require "language_server/protocol/interface/work_done_progress_report"
      require "language_server/protocol/interface/workspace_diagnostic_params"
      require "language_server/protocol/interface/workspace_diagnostic_report"
      require "language_server/protocol/interface/workspace_diagnostic_report_partial_result"
      require "language_server/protocol/interface/workspace_edit"
      require "language_server/protocol/interface/workspace_edit_client_capabilities"
      require "language_server/protocol/interface/workspace_folder"
      require "language_server/protocol/interface/workspace_folders_change_event"
      require "language_server/protocol/interface/workspace_folders_server_capabilities"
      require "language_server/protocol/interface/workspace_full_document_diagnostic_report"
      require "language_server/protocol/interface/workspace_symbol"
      require "language_server/protocol/interface/workspace_symbol_client_capabilities"
      require "language_server/protocol/interface/workspace_symbol_options"
      require "language_server/protocol/interface/workspace_symbol_params"
      require "language_server/protocol/interface/workspace_symbol_registration_options"
      require "language_server/protocol/interface/workspace_unchanged_document_diagnostic_report"
    end
  end
end
