en:
  faker:
    drone:
      name:
        - 'DJ<PERSON> Ma<PERSON> Air 2'
        - '<PERSON><PERSON>'
        - 'DJ<PERSON> Ma<PERSON> 2 Pro'
        - '<PERSON><PERSON>'
        - 'DJ<PERSON> <PERSON>'
        - 'DJI Phantom 4 RTK'
        - 'DJI Phantom 4 Pro'
        - 'DJI Inspire 2'
        - 'DJI Matrice 300 RTK'
        - 'DJI Matrice 600 Pro'
        - 'DJI Agras T16'
        - 'Parrot ANAFI Thermal'
        - 'Yuneec H520 RTK'
        - 'Yuneec H520'
        - 'Yuneec Typhoon H3'
        - 'Yun<PERSON>c Typhoon H Plus'
        - 'SenseFly eBee X'
        - 'SenseFly eBee SQ'
        - 'SenseFly eBee Plus'
        - 'SenseFly eBee Classic'
        - 'FreeFly Alta X'
        - 'FreeFly Alta Pro'
        - 'FreeFly Alta 8'
        - 'FlyAbility Elios 2'
        - 'FlyAbility Elios'
        - 'Autel Evo II Pro 6K'
        - 'Autel Evo II 8K'
        - 'Delair UX 11'
        - 'Delair UX AG'
        - 'Delair DT26E LiDAR'
        - 'Delair DT26E Surveillance'
        - 'Delair DT26E Tactical'
        - 'Delair DT26E Open Payload'
      weight: '### g'
      max_ascent_speed: '# m/s'
      max_descent_speed: '# m/s'
      flight_time: '## min'
      max_altitude: '#### m'
      max_flight_distance: '#### m'
      max_speed: '## m/s'
      max_wind_resistance: '##.# m/s'
      max_angular_velocity: '##°/s'
      max_tilt_angle: '##°'
      operating_temperature: '##°-###°F'
      battery_capacity:
        - '3### mAh'
        - '2### mAh'
      battery_voltage: '##.#V'
      battery_type:
        - 'LiPo 4S'
        - 'LiPo 3S'
        - 'Li-Polymer'
      battery_weight: '### g'
      charging_temperature: '##°-###°F'
      max_charging_power: '##W'
      iso:
        - '100-3200'
        - '100-6400'
      max_resolution: '##MP'
      photo_format:
        - 'JPEG'
        - 'PNG'
        - 'TIF'
      video_format:
        - 'MP4'
        - 'FLV'
        - 'MOV'
      max_shutter_speed:
        - '1'
        - '2'
        - '4'
        - '8'
        - '15'
        - '30'
        - '60'
      min_shutter_speed:
        - '1/8000'
        - '1/4000'
        - '1/2000'
        - '1/1000'
        - '1/500'
        - '1/250'
        - '1/125'
        - '1/60'
        - '1/30'
        - '1/15'
        - '1/8'
        - '1/4'
        - '1/2'
      shutter_speed_units:
        - 's'
