# https://stripe.com/docs/testing
en:
  faker:
    stripe:
      valid_cards:
        visa:          "****************"
        visa_debit:    "****************"
        mc:            "****************"
        mc_2_series:   "2223003122003222"
        mc_debit:      "****************"
        mc_prepaid:    "****************"
        amex:          "***************"
        amex_2:        "***************"
        discover:      "****************"
        discover_2:    "****************"
        diners_club:   "3056930009020004"
        diners_club_2: "36227206271667"
        jcb:           "3566002020360505"
      valid_tokens:
        visa:          "tok_visa"
        visa_debit:    "tok_visa_debit"
        mc:            "tok_mastercard"
        mc_debit:      "tok_mastercard_debit"
        mc_prepaid:    "tok_mastercard_prepaid"
        amex:          "tok_amex"
        discover:      "tok_discover"
        diners_club:   "tok_diners"
        jcb:           "tok_jcb"
      invalid_cards:
        addressZipFail:         "****************"  # The address_line1_check and address_zip_check verifications fail. If your account is blocking payments that fail ZIP code validation, the charge is declined.
        addressFail:            "****************"  # Charge succeeds but the address_line1_check verification fails.
        zipFail:                "****************"  # The address_zip_check verification fails. If your account is blocking payments that fail ZIP code validation, the charge is declined.
        addressZipUnavailable:  "****************"  # Charge succeeds but the address_zip_check and address_line1_check verifications are both unavailable.
        cvcFail:                "****************"  # If a CVC number is provided, the cvc_check fails. If your account is blocking payments that fail CVC code validation, the charge is declined.
        customerChargeFail:     "****************"  # Attaching this card to a Customer object succeeds, but attempts to charge the customer fail.
        successWithReview:      "****************"  # Charge succeeds with a risk_level of elevated and placed into review.
        declineCard:            "****************"  # Charge is declined with a card_declined code.
        declineFraudulentCard:  "****************"  # Results in a charge with a risk level of highest. The charge is blocked as it's considered fraudulent.
        declineIncorrectCvc:    "****************"  # Charge is declined with an incorrect_cvc code.
        declineExpired:         "****************"  # Charge is declined with an expired_card code.
        declineProcessingError: "****************"  # Charge is declined with a processing_error code.
        declineIncorrectNumber: "****************"  # Charge is declined with an incorrect_number code as the card number fails the Luhn check.
