
sv:
  faker:
    address:
      city_prefix: [<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Öster, Aling, A<PERSON>, <PERSON><PERSON>, <PERSON>, B<PERSON>, Bå, Ek, En, Esk, Fal, Gäv, Göte, Ha, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Kung, K<PERSON>, Lyck, Ny]
      city_suffix: [stad, land, sås, ås, holm, tuna, sta, berg, löv, borg, mora, hamn, fors, köping, by, hult, torp, fred, vik]
      country: [Ryssland, Kanada, Kina, USA, Brasilien, Australien, Indien, Argentina, Kazakstan, Algeriet, DR Kongo, Danmark, Färöarna, Grönland, Saudiarabien, Mexiko, Indonesien, Sudan, Libyen, Iran, Mongoliet, Peru, Tchad, Niger, Angola, Mali, Sydafrika, Colombia, Etiopien, Bolivia, Mauretanien, Egypten, Tanzania, Nigeria, Venezuela, Namibia, Pakistan, Moçambique, Turkiet, Chile, Zambia, Marocko, Västsahara, Burma, Afghanistan, Somalia, Centralafrikanska republiken, Sydsudan, Ukraina, Botswana, Madagaskar, Kenya, Frankrike, Franska Guyana, Jemen, Thailand, Spanien, Turkmenistan, Kamerun, Papua Nya Guinea, Sverige, Uzbekistan, Irak, Paraguay, Zimbabwe, Japan, Tyskland, Kongo, Finland, Malaysia, Vietnam, Norge, Svalbard, Jan Mayen, Elfenbenskusten, Polen, Italien, Filippinerna, Ecuador, Burkina Faso, Nya Zeeland, Gabon, Guinea, Storbritannien, Ghana, Rumänien, Laos, Uganda, Guyana, Oman, Vitryssland, Kirgizistan, Senegal, Syrien, Kambodja, Uruguay, Tunisien, Surinam, Nepal, Bangladesh, Tadzjikistan, Grekland, Nicaragua, Eritrea, Nordkorea, Malawi, Benin, Honduras, Liberia, Bulgarien, Kuba, Guatemala, Island, Sydkorea, Ungern, Portugal, Jordanien, Serbien, Azerbajdzjan, Österrike, Förenade Arabemiraten, Tjeckien, Panama, Sierra Leone, Irland, Georgien, Sri Lanka, Litauen, Lettland, Togo, Kroatien, Bosnien och Hercegovina, Costa Rica, Slovakien, Dominikanska republiken, Bhutan, Estland, Danmark, Färöarna, Grönland, Nederländerna, Schweiz, Guinea-Bissau, Taiwan, Moldavien, Belgien, Lesotho, Armenien, Albanien, Salomonöarna, Ekvatorialguinea, Burundi, Haiti, Rwanda, Makedonien, Djibouti, Belize, Israel, El Salvador, Slovenien, Fiji, Kuwait, Swaziland, Timor-Leste, Montenegro, Bahamas, Vanuatu, Qatar, Gambia, Jamaica, Kosovo, Libanon, Cypern, Brunei, Trinidad och Tobago, Kap Verde, Samoa, Luxemburg, Komorerna, Mauritius, São Tomé och Príncipe, Kiribati, Dominica, Tonga, Mikronesiens federerade stater, Singapore, Bahrain, Saint Lucia, Andorra, Palau, Seychellerna, Antigua och Barbuda, Barbados, Saint Vincent och Grenadinerna, Grenada, Malta, Maldiverna, Saint Kitts och Nevis, Marshallöarna, Liechtenstein, San Marino, Tuvalu, Nauru, Monaco, Vatikanstaten]

      common_street_suffix: ["s Väg", "s Gata"]
      street_prefix: [Västra, Östra, Norra, Södra, Övre, Undre]
      street_root: [Björk, Järnvägs, Ring, Skol, Skogs, Ny, Gran, Idrotts, Stor, Kyrk, Industri, Park, Strand, Skol, Trädgård, Ängs, Kyrko, Villa, Ek, Kvarn, Stations, Back, Furu, Gen, Fabriks, Åker, Bäck, Asp]
      street_suffix: [vägen, gatan, gränden, gärdet, allén]
      state: [Blekinge, Dalarna, Gotland, Gävleborg, Göteborg, Halland, Jämtland, Jönköping, Kalmar, Kronoberg, Norrbotten, Skaraborg, Skåne, Stockholm, Södermanland, Uppsala, Värmland, Västerbotten, Västernorrland, Västmanland, Älvsborg, Örebro, Östergötland]

      city:
        - "#{city_prefix}#{city_suffix}"
      street_name:
        - "#{street_root}#{street_suffix}"
        - "#{street_prefix} #{street_root}#{street_suffix}"
        - "#{Name.first_name}#{common_street_suffix}"
        - "#{Name.last_name}#{common_street_suffix}"
      postcode: ['#####']
      building_number: ['###', '##', '#']
      secondary_address: ['Lgh. ###', 'Hus ###']
      street_address:
        - "#{street_name} #{building_number}"
      full_address:
        - "#{street_address}, #{postcode} #{city}"
      default_country: [Sverige]
      default_country_code: ["SE"]

    company:
      suffix: ["Gruppen", "AB", "HB", "Group", "Investment", "Kommanditbolag", "Aktiebolag"]
      name:
        - "#{Name.last_name} #{suffix}"
        - "#{Name.last_name}-#{Name.last_name}"
        - "#{Name.last_name}, #{Name.last_name} #{suffix}"

    internet:
      domain_suffix: [se, nu, info, com, org]

    name:
      female_first_name: [Maria, Anna, Margareta, Elisabeth, Eva, Birgitta, Kristina, Karin, Elisabet, Marie, Åsa, Hjördis, Ingegärd]
      male_first_name: [Erik, Lars, Karl, Anders, Per, Johan, Nils, Lennart, Emil, Hans, Jörgen, Göran, Håkan, Kåre]
      last_name: [Johansson, Andersson, Karlsson, Nilsson, Eriksson, Larsson, Olsson, Persson, Svensson, Gustafsson, Åslund, Östlund, Änglund]
      prefix: [civ.ek., civ.ing., ekon.dr, ekon. mag., ekon. kand., fil.dr, fil.lic., fil.kand., fil.mag., jur. kand., jur.utr.kand., jur.lic., jur.dr, med.dr, med.lic., med.kand., odont.kand., odont.lic., odont.dr, pol.kand., pol.mag., pol.dr, tekn.dr, tekn.lic., teol.kand., teol.lic., teol.dr]
      name_with_middle:
        - "#{prefix} #{first_name} #{last_name} #{last_name}"
        - "#{first_name} #{last_name} #{last_name}"
        - "#{first_name} #{last_name} #{last_name}"
        - "#{first_name} #{last_name} #{last_name}"
        - "#{first_name} #{last_name} #{last_name}"
      first_name:
        - "#{female_first_name}"
        - "#{male_first_name}"

      title:
        descriptor: [Lead, Senior, Direct, Corporate, Dynamic, Future, Product, National, Regional, District, Central, Global, Customer, Investor, Dynamic, International, Legacy, Forward, Internal, Human, Chief, Principal]
        level: [Solutions, Program, Brand, Security, Research, Marketing, Directives, Implementation, Integration, Functionality, Response, Paradigm, Tactics, Identity, Markets, Group, Division, Applications, Optimization, Operations, Infrastructure, Intranet, Communications, Web, Branding, Quality, Assurance, Mobility, Accounts, Data, Creative, Configuration, Accountability, Interactions, Factors, Usability, Metrics]
        job: [Supervisor, Associate, Executive, Liaison, Officer, Manager, Engineer, Specialist, Director, Coordinator, Administrator, Architect, Analyst, Designer, Planner, Orchestrator, Technician, Developer, Producer, Consultant, Assistant, Facilitator, Agent, Representative, Strategist]
      name:
        - "#{first_name} #{last_name}"
        - "#{prefix} #{first_name} #{last_name}"

    phone_number:
      country_code:
        - '46'
      formats: ['####-#####', '####-######']
    cell_phone:
      formats:
        - '070-###-####'
        - '076-###-####'
        - '073-###-####'

    commerce:
      color: [vit, silver, grå, svart, röd, grön, blå, gul, lila, indigo, guld, brun, rosa, purpur, korall]
      department: ["Böcker", "Filmer", "Musik", "Spel", "Elektronik", "Datorer", "Hem", "Trädgård", "Verktyg", "Livsmedel", "Hälsa", "Skönhet", "Leksaker", "Klädsel", "Skor", "Smycken", "Sport"]
      product_name:
        adjective: [Liten, Ergonomisk, Robust, Intelligent, Söt, Otrolig, Fantastisk, Praktisk, Slimmad, Grym, Enorm, Mediokra, Synergistic, Tung, Lätt, Aerodynamisk, Tålig]
        material: [Stål, Metall, Trä, Betong, Plast, Bomull, Granit, Gummi, Latex, Läder, Silke, Ull, Linne, Marmor, Järn, Brons, Koppar, Aluminium, Papper]
        product: [Stol, Bil, Dator, Handskar, Pants, Shirt, Table, Shoes, Hat, Plate, Kniv, Flaska, Coat, Lampa, Tangentbord, Bag, Bänk, Klocka, Titta, Plånbok]

    team:
      suffix: [IF, FF, BK, HK, AIF, SK, FC, SK, BoIS, FK, BIS, FIF, IK]
      name:
        - "#{Address.city} #{suffix}"

    bank:
      name: ["Swedbank", "Handelsbanken", "SEB", "Nordea", "Danske Bank","Skandiabanken","Länsförsäkringar Bank", "Sparbanken Syd", "ICA Banken", "Forex Bank", "Resurs Bank", "Avanza Bank", "Nordnet Bank", "SBAB", "Landshypotek Bank"]

    sport:
      summer_olympics: # Source https://sok.se/idrotter.html?ityp=Alla&iseason=Sommar&numberOfHitsToShow=48
        - Badminton
        - Baseboll/Softboll
        - Basketboll
        - Bordtennis
        - Boxning
        - Brottning
        - Bågskytte
        - Cricket
        - Cykel
        - Danssport
        - Dragkamp
        - Flaggfotboll
        - Fotboll
        - Friidrott
        - Fäktning
        - Golf
        - Gymnastik
        - Handboll
        - Judo
        - Kanot
        - Karate
        - Kickboxning
        - Lacrosse
        - Landhockey
        - Modern femkamp
        - Muay Thai
        - Padel
        - Ridsport
        - Rodd
        - Rugby
        - Segling
        - Simidrott
        - Skateboard
        - Skyttesport
        - Sportklättring
        - Squash
        - Surfing
        - Taekwondo
        - Tennis
        - Teqball
        - Triathlon
        - Tyngdlyftning
        - Vattenskidor
        - Volleyboll
      winter_olympics: # Source https://sok.se/idrotter.html?ityp=Alla&iseason=Vinter&numberOfHitsToShow=48
        - Alpint
        - Backhoppning
        - Curling
        - Freeskiing
        - Freestyle
        - Ishockey
        - Konståkning
        - Kälksport
        - Längdskidåkning
        - Nordisk kombination
        - Skidskytte
        - Skimo
        - Skridsko
        - Snowboard
      summer_paralympics: # Source https://www.paralympics.se/paralympics/paralympics/idrotter/sommar
        - Badminton
        - Boccia
        - Bordtennis
        - Bågskytte
        - Bänkpress
        - Cykel
        - Fotboll 5-a-side
        - Friidrott
        - Fäktning
        - Goalball
        - Judo
        - Kanot
        - Ridsport
        - Rodd
        - Rullstolsbasket
        - Rullstolsrugby
        - Rullstolstennis
        - Simning
        - Sittande volleyboll
        - Sportskytte
        - Taekwondo
        - Triathlon
      winter_paralympics: # Source https://www.paralympics.se/paralympics/paralympics/idrotter/vinter
        - Alpin skidåkning
        - Längdskidor
        - Paraishockey
        - Rullstolscurling
        - Skidskytte
        - Snowboard
