en:
  faker:
    app:
      name: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>t<PERSON>', '<PERSON><PERSON>-Zap', 'Y-<PERSON><PERSON>', '<PERSON>res<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Biodex', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Zoolab', '<PERSON>in', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', 'Tin', 'It', 'Home Ing', '<PERSON>ami<PERSON>', '<PERSON>ing', 'Konklab', 'Alpha', 'La<PERSON>ux', 'Voyatouch', '<PERSON>zap', '<PERSON><PERSON><PERSON>', '<PERSON>aam-Dox', 'Sub-Ex', 'Quo Lux', '<PERSON><PERSON>', '<PERSON><PERSON>osanza<PERSON>', '<PERSON><PERSON>', 'Hatity', 'Tempsoft', 'Overhold', 'Fixflex', 'Konklux', 'Zontrax', 'Tampflex', 'Span', '<PERSON>fix', '<PERSON>co<PERSON>', '<PERSON><PERSON>', '<PERSON>x San', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Y-find', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Vagram', 'Aerified', 'Pannier', 'Asoka', 'Regrant', 'Wrapsafe', 'Prodder', 'Bytecard', 'Bitchip', 'Veribet', 'Gembucket', 'Cardguard', 'Bitwolf', 'Cardify', 'Domainer', 'Flowdesk', 'Flexidy']
      version: ['0.#.#', '0.##', '#.##', '#.#', '#.#.#']
      author:
        - "#{Name.name}"
        - "#{Company.name}"
