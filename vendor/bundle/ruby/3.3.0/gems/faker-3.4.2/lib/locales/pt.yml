pt:
  faker:
    address:
      city_name:
        - Abrantes
        - Agualva-Cacém
        - Águeda
        - Albergaria-a-Velha
        - Albufeira
        - Alcácer do Sal
        - Alcobaça
        - Alfena
        - Almada
        - Almeirim
        - Alverca do Ribatejo
        - Amadora
        - Amarante
        - Amora
        - Anadia
        - Angra do Heroísmo
        - Aveiro
        - Barcelos
        - Barreiro
        - Beja
        - Borba
        - Braga
        - Bragança
        - Caldas da Rainha
        - Câmara de Lobos
        - Caniço
        - Cantanhede
        - Cartaxo
        - Castelo Branco
        - Chaves
        - Coimbra
        - Costa da Caparica
        - Covilhã
        - Elvas
        - Entroncamento
        - Ermesinde
        - Esmoriz
        - Espinho
        - Esposende
        - Estarreja
        - Estremoz
        - Évora
        - Fafe
        - Faro
        - Fátima
        - Felgueiras
        - Figueira da Foz
        - Fiães
        - Freamunde
        - Funchal
        - Fundão
        - Gafanha da Nazaré
        - Gandra
        - Gondomar
        - Gouveia
        - Guarda
        - Guimarães
        - Horta
        - Ílhavo
        - Lagoa
        - Lagoa
        - Lagos
        - Lamego
        - Leiria
        - Lisboa
        - Lixa
        - Loulé
        - Loures
        - Lourosa
        - Macedo de Cavaleiros
        - Machico
        - Maia
        - Mangualde
        - <PERSON>
        - Marinha Grande
        - Matosinhos
        - Mealhada
        - Mêda
        - Miranda do Douro / Miranda de l Douro
        - Mirandela
        - Montemor-o-Novo
        - Montijo
        - Moura
        - Odivelas
        - Olhão da Restauração
        - Oliveira de Azeméis
        - Oliveira do Bairro
        - Oliveira do Hospital
        - Ourém
        - Ovar
        - Paços de Ferreira
        - Paredes
        - Penafiel
        - Peniche
        - Peso da Régua
        - Pinhel
        - Pombal
        - Ponta Delgada
        - Ponte de Sor
        - Portalegre
        - Portimão
        - Porto
        - Póvoa de Santa Iria
        - Póvoa de Varzim
        - Praia da Vitória
        - Quarteira
        - Queluz
        - Rebordosa
        - Reguengos de Monsaraz
        - Ribeira Grande
        - Rio Maior
        - Rio Tinto
        - Sabugal
        - Sacavém
        - Samora Correia
        - Santa Comba Dão
        - Santa Cruz
        - Santa Maria da Feira
        - Santana
        - Santarém
        - Santiago do Cacém
        - Santo Tirso
        - São João da Madeira
        - São Mamede de Infesta
        - São Pedro do Sul
        - Lordelo
        - Seia
        - Seixal
        - Senhora da Hora
        - Serpa
        - Setúbal
        - Silves
        - Sines
        - Tarouca
        - Tavira
        - Tomar
        - Tondela
        - Torres Novas
        - Torres Vedras
        - Trancoso
        - Trofa
        - Valbom
        - Vale de Cambra
        - Valença
        - Valongo
        - Valpaços
        - Vendas Novas
        - Viana do Castelo
        - Vila Baleira
        - Vila do Conde
        - Vila Franca de Xira
        - Vila Nova de Famalicão
        - Vila Nova de Foz Côa
        - Vila Nova de Gaia
        - Vila Nova de Santo André
        - Vila Real
        - Vila Real de Santo António
        - Viseu
        - Vizela
      city_prefix: []
      city_suffix: []
      city:
        - "#{city_name}"
      country:
        - Afeganistão
        - Albânia
        - Algéria
        - Samoa
        - Andorra
        - Angola
        - Anguilla
        - Antigua and Barbada
        - Argentina
        - Armênia
        - Aruba
        - Austrália
        - Áustria
        - Alzerbajão
        - Bahamas
        - Barém
        - Bangladesh
        - Barbado
        - Belgrado
        - Bélgica
        - Belize
        - Benin
        - Bermuda
        - Bhutan
        - Bolívia
        - Bôsnia
        - Botuasuna
        - Bouvetoia
        - Brasil
        - Arquipélago de Chagos
        - Ilhas Virgens
        - Brunei
        - Bulgária
        - Burkina Faso
        - Burundi
        - Cambójia
        - Camarões
        - Canadá
        - Cabo Verde
        - Ilhas Caiman
        - República da África Central
        - Chad
        - Chile
        - China
        - Ilhas Natal
        - Ilhas Cocos
        - Colômbia
        - Comoros
        - Congo
        - Ilhas Cook
        - Costa Rica
        - Costa do Marfim
        - Croácia
        - Cuba
        - Cyprus
        - República Tcheca
        - Dinamarca
        - Djibouti
        - Dominica
        - República Dominicana
        - Equador
        - Egito
        - El Salvador
        - Guiné Equatorial
        - Eritrea
        - Estônia
        - Etiópia
        - Ilhas Faroe
        - Malvinas
        - Fiji
        - Finlândia
        - França
        - Guiné Francesa
        - Polinésia Francesa
        - Gabão
        - Gâmbia
        - Georgia
        - Alemanha
        - Gana
        - Gibraltar
        - Grécia
        - Groelândia
        - Granada
        - Guadalupe
        - Guano
        - Guatemala
        - Guernsey
        - Guiné
        - Guiné-Bissau
        - Guiana
        - Haiti
        - Heard Island and McDonald Islands
        - Vaticano
        - Honduras
        - Hong Kong
        - Hungria
        - Iceland
        - Índia
        - Indonésia
        - Irã
        - Iraque
        - Irlanda
        - Ilha de Man
        - Israel
        - Itália
        - Jamaica
        - Japão
        - Jersey
        - Jordânia
        - Cazaquistão
        - Quênia
        - Kiribati
        - Coreia do Norte
        - Coreia do Sul
        - Kuwait
        - Kyrgyz Republic
        - República Democrática de Lao People
        - Latvia
        - Líbano
        - Lesotho
        - Libéria
        - Libyan Arab Jamahiriya
        - Liechtenstein
        - Lituânia
        - Luxemburgo
        - Macao
        - Macedônia
        - Madagascar
        - Malawi
        - Malásia
        - Maldives
        - Mali
        - Malta
        - Ilhas Marshall
        - Martinica
        - Mauritânia
        - Mauritius
        - Mayotte
        - México
        - Micronésia
        - Moldova
        - Mônaco
        - Mongólia
        - Montenegro
        - Montserrat
        - Marrocos
        - Moçambique
        - Myanmar
        - Namibia
        - Nauru
        - Nepal
        - Antilhas Holandesas
        - Holanda
        - Nova Caledonia
        - Nova Zelândia
        - Nicarágua
        - Nigéria
        - Niue
        - Ilha Norfolk
        - Northern Mariana Islands
        - Noruega
        - Oman
        - Paquistão
        - Palau
        - Território da Palestina
        - Panamá
        - Nova Guiné Papua
        - Paraguai
        - Peru
        - Filipinas
        - Polônia
        - Portugal
        - Puerto Rico
        - Qatar
        - Romênia
        - Rússia
        - Ruanda
        - São Bartolomeu
        - Santa Helena
        - Santa Lúcia
        - Saint Martin
        - Saint Pierre and Miquelon
        - Saint Vincent and the Grenadines
        - Samoa
        - San Marino
        - Sao Tomé e Príncipe
        - Arábia Saudita
        - Senegal
        - Sérvia
        - Seychelles
        - Serra Leoa
        - Singapura
        - Eslováquia
        - Eslovênia
        - Ilhas Salomão
        - Somália
        - África do Sul
        - South Georgia and the South Sandwich Islands
        - Spanha
        - Sri Lanka
        - Sudão
        - Suriname
        - Svalbard & Jan Mayen Islands
        - Swaziland
        - Suécia
        - Suíça
        - Síria
        - Taiwan
        - Tajiquistão
        - Tanzânia
        - Tailândia
        - Timor-Leste
        - Togo
        - Tokelau
        - Tonga
        - Trinidá e Tobago
        - Tunísia
        - Turquia
        - Turcomenistão
        - Turks and Caicos Islands
        - Tuvalu
        - Uganda
        - Ucrânia
        - Emirados Árabes Unidos
        - Reino Unido
        - Estados Unidos da América
        - Estados Unidos das Ilhas Virgens
        - Uruguai
        - Uzbequistão
        - Vanuatu
        - Venezuela
        - Vietnã
        - Wallis and Futuna
        - Sahara
        - Yemen
        - Zâmbia
        - Zimbábue
      building_number:
        - "#####"
        - "####"
        - "###"
      street_suffix:
        - Rua
        - Avenida
        - Travessa
        - Ponté
        - Alameda
        - Marginal
        - Viela
        - Rodovia
      secondary_address:
        - 'Apto. ###'
        - 'Sobrado ##'
        - 'Casa #'
        - 'Lote ##'
        - 'Quadra ##'
      postcode:
        - "####"
      state:
        - Lisboa
        - Leiria
        - Santarém
        - Setúbal
        - Beja
        - Faro
        - Évora
        - Portalegre
        - Castelo Branco
        - Guarda
        - Coimbra
        - Aveiro
        - Viseu
        - Bragança
        - Vila Real
        - Porto
        - Braga
        - Viana do Castelo
      default_country:
        - Portugal
      default_country_code:
        - PT
    company:
      suffix:
        - S.A.
        - LTDA
        - e Associados
        - Comércio
        - EIRELI
      name:
        - "#{Name.last_name} #{suffix}"
        - "#{Name.last_name}-#{Name.last_name}"
        - "#{Name.last_name}, #{Name.last_name} e #{Name.last_name}"
    internet:
      domain_suffix:
        - pt
        - com
        - biz
        - info
        - net
        - org
    lorem:
      words:
        - alias
        - consequatur
        - aut
        - perferendis
        - sit
        - voluptatem
        - accusantium
        - doloremque
        - aperiam
        - eaque
        - ipsa
        - quae
        - ab
        - illo
        - inventore
        - veritatis
        - et
        - quasi
        - architecto
        - beatae
        - vitae
        - dicta
        - sunt
        - explicabo
        - aspernatur
        - aut
        - odit
        - aut
        - fugit
        - sed
        - quia
        - consequuntur
        - magni
        - dolores
        - eos
        - qui
        - ratione
        - voluptatem
        - sequi
        - nesciunt
        - neque
        - dolorem
        - ipsum
        - quia
        - dolor
        - sit
        - amet
        - consectetur
        - adipisci
        - velit
        - sed
        - quia
        - non
        - numquam
        - eius
        - modi
        - tempora
        - incidunt
        - ut
        - labore
        - et
        - dolore
        - magnam
        - aliquam
        - quaerat
        - voluptatem
        - ut
        - enim
        - ad
        - minima
        - veniam
        - quis
        - nostrum
        - exercitationem
        - ullam
        - corporis
        - nemo
        - enim
        - ipsam
        - voluptatem
        - quia
        - voluptas
        - sit
        - suscipit
        - laboriosam
        - nisi
        - ut
        - aliquid
        - ex
        - ea
        - commodi
        - consequatur
        - quis
        - autem
        - vel
        - eum
        - iure
        - reprehenderit
        - qui
        - in
        - ea
        - voluptate
        - velit
        - esse
        - quam
        - nihil
        - molestiae
        - et
        - iusto
        - odio
        - dignissimos
        - ducimus
        - qui
        - blanditiis
        - praesentium
        - laudantium
        - totam
        - rem
        - voluptatum
        - deleniti
        - atque
        - corrupti
        - quos
        - dolores
        - et
        - quas
        - molestias
        - excepturi
        - sint
        - occaecati
        - cupiditate
        - non
        - provident
        - sed
        - ut
        - perspiciatis
        - unde
        - omnis
        - iste
        - natus
        - error
        - similique
        - sunt
        - in
        - culpa
        - qui
        - officia
        - deserunt
        - mollitia
        - animi
        - id
        - est
        - laborum
        - et
        - dolorum
        - fuga
        - et
        - harum
        - quidem
        - rerum
        - facilis
        - est
        - et
        - expedita
        - distinctio
        - nam
        - libero
        - tempore
        - cum
        - soluta
        - nobis
        - est
        - eligendi
        - optio
        - cumque
        - nihil
        - impedit
        - quo
        - porro
        - quisquam
        - est
        - qui
        - minus
        - id
        - quod
        - maxime
        - placeat
        - facere
        - possimus
        - omnis
        - voluptas
        - assumenda
        - est
        - omnis
        - dolor
        - repellendus
        - temporibus
        - autem
        - quibusdam
        - et
        - aut
        - consequatur
        - vel
        - illum
        - qui
        - dolorem
        - eum
        - fugiat
        - quo
        - voluptas
        - nulla
        - pariatur
        - at
        - vero
        - eos
        - et
        - accusamus
        - officiis
        - debitis
        - aut
        - rerum
        - necessitatibus
        - saepe
        - eveniet
        - ut
        - et
        - voluptates
        - repudiandae
        - sint
        - et
        - molestiae
        - non
        - recusandae
        - itaque
        - earum
        - rerum
        - hic
        - tenetur
        - a
        - sapiente
        - delectus
        - ut
        - aut
        - reiciendis
        - voluptatibus
        - maiores
        - doloribus
        - asperiores
        - repellat
    name:
      first_name:
        - Águeda
        - Amélia
        - Ângela
        - Alessandro
        - Alessandra
        - Alexandre
        - Aline
        - Antônio
        - Breno
        - Bruna
        - Carlos
        - Carla
        - Célia
        - Cecília
        - César
        - Danilo
        - Dalila
        - Deneval
        - Eduardo
        - Eduarda
        - Esther
        - Elísio
        - Fábio
        - Fabrício
        - Fabrícia
        - Félix
        - Felícia
        - Feliciano
        - Frederico
        - Fabiano
        - Gustavo
        - Guilherme
        - Gúbio
        - Heitor
        - Hélio
        - Hugo
        - Isabel
        - Isabela
        - Ígor
        - João
        - Joana
        - Júlio César
        - Júlio
        - Júlia
        - Janaína
        - Karla
        - Kléber
        - Lucas
        - Lorena
        - Lorraine
        - Larissa
        - Ladislau
        - Marcos
        - Meire
        - Marcelo
        - Marcela
        - Margarida
        - Mércia
        - Márcia
        - Marli
        - Morgana
        - Maria
        - Norberto
        - Natália
        - Nataniel
        - Núbia
        - Ofélia
        - Paulo
        - Paula
        - Pablo
        - Pedro
        - Raul
        - Rafael
        - Rafaela
        - Ricardo
        - Roberto
        - Roberta
        - Sílvia
        - Sílvia
        - Silas
        - Simão
        - Suélen
        - Sara
        - Salvador
        - Sirineu
        - Talita
        - Tertuliano
        - Vicente
        - Víctor
        - Vitória
        - Yango
        - Yago
        - Yuri
        - Washington
        - Warley
      last_name:
        - Araújo
        - D'cruze
        - Estéves
        - Silva
        - Souza
        - Carvalho
        - Santos
        - Reis
        - Xavier
        - Franco
        - Braga
        - Macedo
        - Batista
        - Barros
        - Moraes
        - Costa
        - Pereira
        - Carvalho
        - Melo
        - Lemos
        - Saraiva
        - Nogueira
        - Oliveira
        - Martins
        - Moreira
        - Albuquerque
      prefix:
        - Sr.
        - Sra.
        - Srta.
        - Dr.
        - Eng.
      suffix:
        - Jr.
        - Neto
        - Filho
      name_with_middle:
        - "#{prefix} #{first_name} #{last_name} #{last_name}"
        - "#{first_name} #{last_name} #{last_name} #{suffix}"
        - "#{first_name} #{last_name} #{last_name}"
        - "#{first_name} #{last_name} #{last_name}"
        - "#{first_name} #{last_name} #{last_name}"
        - "#{first_name} #{last_name} #{last_name}"
    phone_number:
      country_code:
        - '351'
      formats:
        - "(##) ###-####"
        - "(##) ###-####"
        - '884 ###-###'
