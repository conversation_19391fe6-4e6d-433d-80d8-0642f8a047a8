pl:
  faker:
    address:
      country: [Afganistan, Albania, Algieria, Andora, Angola, Antigua i Barbuda, Arabia Saudyjska, Argentyna, Armenia, Australia, Austria, Azerbejdżan, Bahamy, Bahrajn, Bangladesz, Barbados, Belgia, Belize, Benin, Bhutan, Białoruś, Birma, Boliwia, Sucre, Bośnia i Hercegowina, Botswana, Brazylia, Brunei, Bułgaria, Burkina Faso, Burundi, Chile, Chiny, Chorwacja, Cypr, Czad, Czarnogóra, Czechy, Dania, Demokratyczna Republika Konga, Dominika, Dominikana, Dżibuti, Egipt, Ekwador, Erytrea, Estonia, Etiopia, Fidżi, Filipiny, Finlandia, Francja, Gabon, Gambia, Ghana, Grecja, Grenada, Gruzja, Gujana, Gwatemala, Gwinea, Gwinea Bissau, Gwinea Równikowa, Haiti, Hiszpania, Holandia, Haga, Honduras, Indie, Indonezja, Irak, Iran, Irlandia, Islandia, Izrael, Jamajka, Japonia, Jemen, Jordania, Kambodża, Kamerun, Kanada, Katar, Kazachstan, Kenia, Kirgistan, Kiribati, Kolumbia, Komory, Kongo, Korea Południowa, Korea Północna, Kostaryka, Kuba, Kuwejt, Laos, Lesotho, Liban, Liberia, Libia, Liechtenstein, Litwa, Luksemburg, Łotwa, Macedonia, Madagaskar, Malawi, Malediwy, Malezja, Mali, Malta, Maroko, Mauretania, Mauritius, Meksyk, Mikronezja, Mołdawia, Monako, Mongolia, Mozambik, Namibia, Nauru, Nepal, Niemcy, Niger, Nigeria, Nikaragua, Norwegia, Nowa Zelandia, Oman, Pakistan, Palau, Panama, Papua-Nowa Gwinea, Paragwaj, Peru, Polska, Portugalia, Republika Południowej Afryki, Republika Środkowoafrykańska, Republika Zielonego Przylądka, Rosja, Rumunia, Rwanda, Saint Kitts i Nevis, Saint Lucia, Saint Vincent i Grenadyny, Salwador, Samoa, San Marino, Senegal, Serbia, Seszele, Sierra Leone, Singapur, Słowacja, Słowenia, Somalia, Sri Lanka, Stany Zjednoczone, Suazi, Sudan, Sudan Południowy, Surinam, Syria, Szwajcaria, Szwecja, Tadżykistan, Tajlandia, Tanzania, Timor Wschodni, Togo, Tonga, Trynidad i Tobago, Tunezja, Turcja, Turkmenistan, Tuvalu, Funafuti, Uganda, Ukraina, Urugwaj, Uzbekistan, Vanuatu, Watykan, Wenezuela, Węgry, Wielka Brytania, Wietnam, Włochy, Wybrzeże Kości Słoniowej, Wyspy Marshalla, Wyspy Salomona, Wyspy Świętego Tomasza i Książęca, Zambia, Zimbabwe, Zjednoczone Emiraty Arabskie]
      building_number: ['#####', '####', '###']
      street_prefix: ['ul.', 'al.']
      secondary_address: ['Apt. ###', 'Suite ###']

      postcode: ['##-###']
      state: [Dolnośląskie, Kujawsko-pomorskie, Lubelskie, Lubuskie, Łódzkie, Małopolskie, Mazowieckie, Opolskie, Podkarpackie, Podlaskie, Pomorskie, Śląskie, Świętokrzyskie, Warmińsko-mazurskie, Wielkopolskie, Zachodniopomorskie]
      state_abbr: [DŚ, KP, LB, LS, ŁD, MP, MZ, OP, PK, PL, PM, ŚL, ŚK, WM, WP, ZP]
      city_name: [Aleksandrów Kujawski, Aleksandrów Łódzki, Alwernia, Andrychów, Annopol, Augustów, Babimost, Baborów, Baranów Sandomierski, Barcin, Barczewo, Bardo, Barlinek, Bartoszyce, Barwice, Bełchatów, Bełżyce, Będzin, Biała, Biała Piska, Biała Podlaska, Biała Rawska, Białobrzegi, Białogard, Biały Bór, Białystok, Biecz, Bielawa, Bielsk Podlaski, Bielsko-Biała, Bieruń, Bierutów, Bieżuń, Biłgoraj, Biskupiec, Bisztynek, Blachownia, Błaszki, Błażowa, Błonie, Bobolice, Bobowa, Bochnia, Bodzentyn, Bogatynia, Boguchwała, Boguszów-Gorce, Bojanowo, Bolesławiec, Bolków, Borek Wielkopolski, Borne Sulinowo, Braniewo, Brańsk, Brodnica, Brok, Brusy, Brwinów, Brzeg, Brzeg Dolny, Brzesko, Brzeszcze, Brześć Kujawski, Brzeziny, Brzostek, Brzozów, Buk, Bukowno, Busko-Zdrój, Bychawa, Byczyna, Bydgoszcz, Bystrzyca Kłodzka, Bytom, Bytom Odrzański, Bytów, Cedynia, Chełm, Chełmek, Chełmno, Chełmża, Chęciny, Chmielnik, Chocianów, Chociwel, Chodecz, Chodzież, Chojna, Chojnice, Chojnów, Choroszcz, Chorzele, Chorzów, Choszczno, Chrzanów, Ciechanowiec, Ciechanów, Ciechocinek, Cieszanów, Cieszyn, Ciężkowice, Cybinka, Czaplinek, Czarna Białostocka, Czarna Woda, Czarne, Czarnków, Czchów, Czechowice-Dziedzice, Czeladź, Czempiń, Czerniejewo, Czersk, Czerwieńsk, Czerwionka-Leszczyny, Częstochowa, Człopa, Człuchów, Czyżew, Ćmielów, Daleszyce, Darłowo, Dąbie, Dąbrowa Białostocka, Dąbrowa Górnicza, Dąbrowa Tarnowska, Debrzno, Dębica, Dęblin, Dębno, Dobczyce, Dobiegniew, Dobra (powiat łobeski), Dobra (powiat turecki), Dobre Miasto, Dobrodzień, Dobrzany, Dobrzyń nad Wisłą, Dolsk, Drawno, Drawsko Pomorskie, Drezdenko, Drobin, Drohiczyn, Drzewica, Dukla, Duszniki-Zdrój, Dynów, Działdowo, Działoszyce, Działoszyn, Dzierzgoń, Dzierżoniów, Dziwnów, Elbląg, Ełk, Frampol, Frombork, Garwolin, Gąbin, Gdańsk, Gdynia, Giżycko, Glinojeck, Gliwice, Głogów, Głogów Małopolski, Głogówek, Głowno, Głubczyce, Głuchołazy, Głuszyca, Gniew, Gniewkowo, Gniezno, Gogolin, Golczewo, Goleniów, Golina, Golub-Dobrzyń, Gołańcz, Gołdap, Goniądz, Gorlice, Gorzów Śląski, Gorzów Wielkopolski, Gostynin, Gostyń, Gościno, Gozdnica, Góra, Góra Kalwaria, Górowo Iławeckie, Górzno, Grabów nad Prosną, Grajewo, Grodków, Grodzisk Mazowiecki, Grodzisk Wielkopolski, Grójec, Grudziądz, Grybów, Gryfice, Gryfino, Gryfów Śląski, Gubin, Hajnówka, Halinów, Hel, Hrubieszów, Iława, Iłowa, Iłża, Imielin, Inowrocław, Ińsko, Iwonicz-Zdrój, Izbica Kujawska, Jabłonowo Pomorskie, Janikowo, Janowiec Wielkopolski, Janów Lubelski, Jarocin, Jarosław, Jasień, Jasło, Jastarnia, Jastrowie, Jastrzębie-Zdrój, Jawor, Jaworzno, Jaworzyna Śląska, Jedlicze, Jedlina-Zdrój, Jedwabne, Jelcz-Laskowice, Jelenia Góra, Jeziorany, Jędrzejów, Jordanów, Józefów (powiat biłgorajski), Józefów (powiat otwocki), Jutrosin, Kalety, Kalisz, Kalisz Pomorski, Kalwaria Zebrzydowska, Kałuszyn, Kamienna Góra, Kamień Krajeński, Kamień Pomorski, Kamieńsk, Kańczuga, Karczew, Kargowa, Karlino, Karpacz, Kartuzy, Katowice, Kazimierz Dolny, Kazimierza Wielka, Kąty Wrocławskie, Kcynia, Kędzierzyn-Koźle, Kępice, Kępno, Kętrzyn, Kęty, Kielce, Kietrz, Kisielice, Kleczew, Kleszczele, Kluczbork, Kłecko, Kłobuck, Kłodawa, Kłodzko, Knurów, Knyszyn, Kobylin, Kobyłka, Kock, Kolbuszowa, Kolno, Kolonowskie, Koluszki, Kołaczyce, Koło, Kołobrzeg, Koniecpol, Konin, Konstancin-Jeziorna, Konstantynów Łódzki, Końskie, Koprzywnica, Korfantów, Koronowo, Korsze, Kosów Lacki, Kostrzyn, Kostrzyn nad Odrą, Koszalin, Kościan, Kościerzyna, Kowal, Kowalewo Pomorskie, Kowary, Koziegłowy, Kozienice, Koźmin Wielkopolski, Kożuchów, Kórnik, Krajenka, Kraków, Krapkowice, Krasnobród, Krasnystaw, Kraśnik, Krobia, Krosno, Krosno Odrzańskie, Krośniewice, Krotoszyn, Kruszwica, Krynica Morska, Krynica-Zdrój, Krynki, Krzanowice, Krzepice, Krzeszowice, Krzywiń, Krzyż Wielkopolski, Książ Wielkopolski, Kudowa-Zdrój, Kunów, Kutno, Kuźnia Raciborska, Kwidzyn, Lądek-Zdrój, Legionowo, Legnica, Lesko, Leszno, Leśna, Leśnica, Lewin Brzeski, Leżajsk, Lębork, Lędziny, Libiąż, Lidzbark, Lidzbark Warmiński, Limanowa, Lipiany, Lipno, Lipsk, Lipsko, Lubaczów, Lubań, Lubartów, Lubawa, Lubawka, Lubień Kujawski, Lubin, Lublin, Lubliniec, Lubniewice, Lubomierz, Luboń, Lubraniec, Lubsko, Lwówek, Lwówek Śląski, Łabiszyn, Łańcut, Łapy, Łasin, Łask, Łaskarzew, Łaszczów, Łaziska Górne, Łazy, Łeba, Łęczna, Łęczyca, Łęknica, Łobez, Łobżenica, Łochów, Łomianki, Łomża, Łosice, Łowicz, Łódź, Łuków, Maków Mazowiecki, Maków Podhalański, Malbork, Małogoszcz, Małomice, Margonin, Marki, Maszewo, Miasteczko Śląskie, Miastko, Michałowo, Miechów, Miejska Górka, Mielec, Mieroszów, Mieszkowice, Międzybórz, Międzychód, Międzylesie, Międzyrzec Podlaski, Międzyrzecz, Międzyzdroje, Mikołajki, Mikołów, Mikstat, Milanówek, Milicz, Miłakowo, Miłomłyn, Miłosław, Mińsk Mazowiecki, Mirosławiec, Mirsk, Mława, Młynary, Mogielnica, Mogilno, Mońki, Morąg, Mordy, Moryń, Mosina, Mrągowo, Mrocza, Mszana Dolna, Mszczonów, Murowana Goślina, Muszyna, Mysłowice, Myszków, Myszyniec, Myślenice, Myślibórz, Nakło nad Notecią, Nałęczów, Namysłów, Narol, Nasielsk, Nekla, Nidzica, Niemcza, Niemodlin, Niepołomice, Nieszawa, Nisko, Nowa Dęba, Nowa Ruda, Nowa Sarzyna, Nowa Sól, Nowe, Nowe Brzesko, Nowe Miasteczko, Nowe Miasto Lubawskie, Nowe Miasto nad Pilicą, Nowe Skalmierzyce, Nowe Warpno, Nowogard, Nowogrodziec, Nowogród, Nowogród Bobrzański, Nowy Dwór Gdański, Nowy Dwór Mazowiecki, Nowy Sącz, Nowy Staw, Nowy Targ, Nowy Tomyśl, Nowy Wiśnicz, Nysa, Oborniki, Oborniki Śląskie, Obrzycko, Odolanów, Ogrodzieniec, Okonek, Olecko, Olesno, Oleszyce, Oleśnica, Olkusz, Olsztyn, Olsztynek, Olszyna, Oława, Opalenica, Opatów, Opoczno, Opole, Opole Lubelskie, Orneta, Orzesze, Orzysz, Osieczna, Osiek, Ostrołęka, Ostroróg, Ostrowiec Świętokrzyski, Ostróda, Ostrów Lubelski, Ostrów Mazowiecka, Ostrów Wielkopolski, Ostrzeszów, Ośno Lubuskie, Oświęcim, Otmuchów, Otwock, Ozimek, Ozorków, Ożarów, Ożarów Mazowiecki, Pabianice, Paczków, Pajęczno, Pakość, Parczew, Pasłęk, Pasym, Pelplin, Pełczyce, Piaseczno, Piaski, Piastów, Piechowice, Piekary Śląskie, Pieniężno, Pieńsk, Pieszyce, Pilawa, Pilica, Pilzno, Piła, Piława Górna, Pińczów, Pionki, Piotrków Kujawski, Piotrków Trybunalski, Pisz, Piwniczna-Zdrój, Pleszew, Płock, Płońsk, Płoty, Pniewy, Pobiedziska, Poddębice, Podkowa Leśna, Pogorzela, Polanica-Zdrój, Polanów, Police, Polkowice, Połaniec, Połczyn-Zdrój, Poniatowa, Poniec, Poręba, Poznań, Prabuty, Praszka, Prochowice, Proszowice, Prószków, Pruchnik, Prudnik, Prusice, Pruszcz Gdański, Pruszków, Przasnysz, Przecław, Przedbórz, Przedecz, Przemków, Przemyśl, Przeworsk, Przysucha, Pszczyna, Pszów, Puck, Puławy, Pułtusk, Puszczykowo, Pyrzyce, Pyskowice, Pyzdry, Rabka-Zdrój, Raciąż, Racibórz, Radków, Radlin, Radłów, Radom, Radomsko, Radomyśl Wielki, Radymno, Radziejów, Radzionków, Radzymin, Radzyń Chełmiński, Radzyń Podlaski, Rajgród, Rakoniewice, Raszków, Rawa Mazowiecka, Rawicz, Recz, Reda, Rejowiec Fabryczny, Resko, Reszel, Rogoźno, Ropczyce, Różan, Ruciane-Nida, Ruda Śląska, Rudnik nad Sanem, Rumia, Rybnik, Rychwał, Rydułtowy, Rydzyna, Ryglice, Ryki, Rymanów, Ryn, Rypin, Rzepin, Rzeszów, Rzgów, Sandomierz, Sanok, Sejny, Serock, Sędziszów, Sędziszów Małopolski, Sępopol, Sępólno Krajeńskie, Sianów, Siechnice, Siedlce, Siemianowice Śląskie, Siemiatycze, Sieniawa, Sieradz, Sieraków, Sierpc, Siewierz, Skalbmierz, Skała, Skarszewy, Skaryszew, Skarżysko-Kamienna, Skawina, Skępe, Skierniewice, Skoczów, Skoki, Skórcz, Skwierzyna, Sława, Sławków, Sławno, Słomniki, Słubice, Słupca, Słupsk, Sobótka, Sochaczew, Sokołów Małopolski, Sokołów Podlaski, Sokółka, Solec Kujawski, Sompolno, Sopot, Sosnowiec, Sośnicowice, Stalowa Wola, Starachowice, Stargard Szczeciński, Starogard Gdański, Stary Sącz, Staszów, Stawiski, Stawiszyn, Stąporków, Stęszew, Stoczek Łukowski, Stronie Śląskie, Strumień, Stryków, Strzegom, Strzelce Krajeńskie, Strzelce Opolskie, Strzelin, Strzelno, Strzyżów, Sucha Beskidzka, Suchań, Suchedniów, Suchowola, Sulechów, Sulejów, Sulejówek, Sulęcin, Sulmierzyce, Sułkowice, Supraśl, Suraż, Susz, Suwałki, Swarzędz, Syców, Szadek, Szamocin, Szamotuły, Szczawnica, Szczawno-Zdrój, Szczebrzeszyn, Szczecin, Szczecinek, Szczekociny, Szczucin, Szczuczyn, Szczyrk, Szczytna, Szczytno, Szepietowo, Szklarska Poręba, Szlichtyngowa, Szprotawa, Sztum, Szubin, Szydłowiec, Ścinawa, Ślesin, Śmigiel, Śrem, Środa Śląska, Środa Wielkopolska, Świątniki Górne, Świdnica, Świdnik, Świdwin, Świebodzice, Świebodzin, Świecie, Świeradów-Zdrój, Świerzawa, Świętochłowice, Świnoujście, Tarczyn, Tarnobrzeg, Tarnogród, Tarnowskie Góry, Tarnów, Tczew, Terespol, Tłuszcz, Tolkmicko, Tomaszów Lubelski, Tomaszów Mazowiecki, Toruń, Torzym, Toszek, Trzcianka, Trzciel, Trzcińsko-Zdrój, Trzebiatów, Trzebinia, Trzebnica, Trzemeszno, Tuchola, Tuchów, Tuczno, Tuliszków, Turek, Tuszyn, Twardogóra, Tychowo, Tychy, Tyczyn, Tykocin, Tyszowce, Ujazd, Ujście, Ulanów, Uniejów, Ustka, Ustroń, Ustrzyki Dolne, Wadowice, Wałbrzych, Wałcz, Warka, Warszawa, Warta, Wasilków, Wąbrzeźno, Wąchock, Wągrowiec, Wąsosz, Wejherowo, Węgliniec, Węgorzewo, Węgorzyno, Węgrów, Wiązów, Wieleń, Wielichowo, Wieliczka, Wieluń, Wieruszów, Więcbork, Wilamowice, Wisła, Witkowo, Witnica, Wleń, Władysławowo, Włocławek, Włodawa, Włoszczowa, Wodzisław Śląski, Wojcieszów, Wojkowice, Wojnicz, Wolbórz, Wolbrom, Wolin, Wolsztyn, Wołczyn, Wołomin, Wołów, Woźniki, Wrocław, Wronki, Września, Wschowa, Wyrzysk, Wysoka, Wysokie Mazowieckie, Wyszków, Wyszogród, Wyśmierzyce, Zabłudów, Zabrze, Zagórów, Zagórz, Zakliczyn, Zakopane, Zakroczym, Zalewo, Zambrów, Zamość, Zator, Zawadzkie, Zawichost, Zawidów, Zawiercie, Ząbki, Ząbkowice Śląskie, Zbąszynek, Zbąszyń, Zduny, Zduńska Wola, Zdzieszowice, Zelów, Zgierz, Zgorzelec, Zielona Góra, Zielonka, Ziębice, Złocieniec, Złoczew, Złotoryja, Złotów, Złoty Stok, Zwierzyniec, Zwoleń, Żabno, Żagań, Żarki, Żarów, Żary, Żelechów, Żerków, Żmigród, Żnin, Żory, Żukowo, Żuromin, Żychlin, Żyrardów, Żywiec]
      city:
        - "#{city_name}"
      street_name:
        - "#{street_prefix} #{Name.last_name}"
      street_address:
        - "#{street_name} #{building_number}"
      default_country: [Polska]
      default_country_code: ["PL"]

    coin:
      flip:
        - Orzeł
        - Reszka

    company:
      suffix: [S.A., sp. z o.o., sp. j., sp.p., sp. k., S.K.A.]
      # Buzzword wordlist from http://www.1728.com/buzzword.htm
      buzzwords:
        - ["Adaptive", "Advanced", "Ameliorated", "Assimilated", "Automated", "Balanced", "Business-focused", "Centralized", "Cloned", "Compatible", "Configurable", "Cross-group", "Cross-platform", "Customer-focused", "Customizable", "Decentralized", "De-engineered", "Devolved", "Digitized", "Distributed", "Diverse", "Down-sized", "Enhanced", "Enterprise-wide", "Ergonomic", "Exclusive", "Expanded", "Extended", "Face to face", "Focused", "Front-line", "Fully-configurable", "Function-based", "Fundamental", "Future-proofed", "Grass-roots", "Horizontal", "Implemented", "Innovative", "Integrated", "Intuitive", "Inverse", "Managed", "Mandatory", "Monitored", "Multi-channelled", "Multi-lateral", "Multi-layered", "Multi-tiered", "Networked", "Object-based", "Open-architected", "Open-source", "Operative", "Optimized", "Optional", "Organic", "Organized", "Persevering", "Persistent", "Phased", "Polarised", "Pre-emptive", "Proactive", "Profit-focused", "Profound", "Programmable", "Progressive", "Public-key", "Quality-focused", "Reactive", "Realigned", "Re-contextualized", "Re-engineered", "Reduced", "Reverse-engineered", "Right-sized", "Robust", "Seamless", "Secured", "Self-enabling", "Sharable", "Stand-alone", "Streamlined", "Switchable", "Synchronised", "Synergistic", "Synergized", "Team-oriented", "Total", "Triple-buffered", "Universal", "Up-sized", "Upgradable", "User-centric", "User-friendly", "Versatile", "Virtual", "Visionary", "Vision-oriented"]
        - ["24 hour", "24/7", "3rd generation", "4th generation", "5th generation", "6th generation", "actuating", "analyzing", "asymmetric", "asynchronous", "attitude-oriented", "background", "bandwidth-monitored", "bi-directional", "bifurcated", "bottom-line", "clear-thinking", "client-driven", "client-server", "coherent", "cohesive", "composite", "context-sensitive", "contextually-based", "content-based", "dedicated", "demand-driven", "didactic", "directional", "discrete", "disintermediate", "dynamic", "eco-centric", "empowering", "encompassing", "even-keeled", "executive", "explicit", "exuding", "fault-tolerant", "foreground", "fresh-thinking", "full-range", "global", "grid-enabled", "heuristic", "high-level", "holistic", "homogeneous", "human-resource", "hybrid", "impactful", "incremental", "intangible", "interactive", "intermediate", "leading edge", "local", "logistical", "maximized", "methodical", "mission-critical", "mobile", "modular", "motivating", "multimedia", "multi-state", "multi-tasking", "national", "needs-based", "neutral", "next generation", "non-volatile", "object-oriented", "optimal", "optimizing", "radical", "real-time", "reciprocal", "regional", "responsive", "scalable", "secondary", "solution-oriented", "stable", "static", "systematic", "systemic", "system-worthy", "tangible", "tertiary", "transitional", "uniform", "upward-trending", "user-facing", "value-added", "web-enabled", "well-modulated", "zero administration", "zero defect", "zero tolerance"]
        - ["ability", "access", "adapter", "algorithm", "alliance", "analyzer", "application", "approach", "architecture", "archive", "artificial intelligence", "array", "attitude", "benchmark", "budgetary management", "capability", "capacity", "challenge", "circuit", "collaboration", "complexity", "concept", "conglomeration", "contingency", "core", "customer loyalty", "database", "data-warehouse", "definition", "emulation", "encoding", "encryption", "extranet", "firmware", "flexibility", "focus group", "forecast", "frame", "framework", "function", "functionalities", "Graphic Interface", "groupware", "Graphical User Interface", "hardware", "help-desk", "hierarchy", "hub", "implementation", "info-mediaries", "infrastructure", "initiative", "installation", "instruction set", "interface", "internet solution", "intranet", "knowledge user", "knowledge base", "local area network", "leverage", "matrices", "matrix", "methodology", "middleware", "migration", "model", "moderator", "monitoring", "moratorium", "neural-net", "open architecture", "open system", "orchestration", "paradigm", "parallelism", "policy", "portal", "pricing structure", "process improvement", "product", "productivity", "project", "projection", "protocol", "secured line", "service-desk", "software", "solution", "standardization", "strategy", "structure", "success", "superstructure", "support", "synergy", "system engine", "task-force", "throughput", "time-frame", "toolset", "utilisation", "website", "workforce"]
      # BS wordlist from http://dack.com/web/bullshit.html
      bs:
        - ["implement", "utilize", "integrate", "streamline", "optimize", "evolve", "transform", "embrace", "enable", "orchestrate", "leverage", "reinvent", "aggregate", "architect", "enhance", "incentivize", "morph", "empower", "envisioneer", "monetize", "harness", "facilitate", "seize", "disintermediate", "synergize", "strategize", "deploy", "brand", "grow", "target", "syndicate", "synthesize", "deliver", "mesh", "incubate", "engage", "maximize", "benchmark", "expedite", "reintermediate", "whiteboard", "visualize", "repurpose", "innovate", "scale", "unleash", "drive", "extend", "engineer", "revolutionize", "generate", "exploit", "transition", "e-enable", "iterate", "cultivate", "matrix", "productize", "redefine", "recontextualize"]
        - ["clicks-and-mortar", "value-added", "vertical", "proactive", "robust", "revolutionary", "scalable", "leading-edge", "innovative", "intuitive", "strategic", "e-business", "mission-critical", "sticky", "one-to-one", "24/7", "end-to-end", "global", "B2B", "B2C", "granular", "frictionless", "virtual", "viral", "dynamic", "24/365", "best-of-breed", "killer", "magnetic", "bleeding-edge", "web-enabled", "interactive", "dot-com", "sexy", "back-end", "real-time", "efficient", "front-end", "distributed", "seamless", "extensible", "turn-key", "world-class", "open-source", "cross-platform", "cross-media", "synergistic", "bricks-and-clicks", "out-of-the-box", "enterprise", "integrated", "impactful", "wireless", "transparent", "next-generation", "cutting-edge", "user-centric", "visionary", "customized", "ubiquitous", "plug-and-play", "collaborative", "compelling", "holistic", "rich"]
        - ["synergies", "web-readiness", "paradigms", "markets", "partnerships", "infrastructures", "platforms", "initiatives", "channels", "eyeballs", "communities", "ROI", "solutions", "e-tailers", "e-services", "action-items", "portals", "niches", "technologies", "content", "vortals", "supply-chains", "convergence", "relationships", "architectures", "interfaces", "e-markets", "e-commerce", "systems", "bandwidth", "infomediaries", "models", "mindshare", "deliverables", "users", "schemas", "networks", "applications", "metrics", "e-business", "functionalities", "experiences", "web services", "methodologies"]
      name:
        - "#{Name.last_name} #{suffix}"
        - "#{Name.last_name}-#{Name.last_name}"
        - "#{Name.last_name}, #{Name.last_name} and #{Name.last_name}"

    internet:
      domain_suffix: [com, pl, com.pl, net, org]

    lorem:
      words: [alias, consequatur, aut, perferendis, sit, voluptatem, accusantium, doloremque, aperiam, eaque, ipsa, quae, ab, illo, inventore, veritatis, et, quasi, architecto, beatae, vitae, dicta, sunt, explicabo, aspernatur, aut, odit, aut, fugit, sed, quia, consequuntur, magni, dolores, eos, qui, ratione, voluptatem, sequi, nesciunt, neque, dolorem, ipsum, quia, dolor, sit, amet, consectetur, adipisci, velit, sed, quia, non, numquam, eius, modi, tempora, incidunt, ut, labore, et, dolore, magnam, aliquam, quaerat, voluptatem, ut, enim, ad, minima, veniam, quis, nostrum, exercitationem, ullam, corporis, nemo, enim, ipsam, voluptatem, quia, voluptas, sit, suscipit, laboriosam, nisi, ut, aliquid, ex, ea, commodi, consequatur, quis, autem, vel, eum, iure, reprehenderit, qui, in, ea, voluptate, velit, esse, quam, nihil, molestiae, et, iusto, odio, dignissimos, ducimus, qui, blanditiis, praesentium, laudantium, totam, rem, voluptatum, deleniti, atque, corrupti, quos, dolores, et, quas, molestias, excepturi, sint, occaecati, cupiditate, non, provident, sed, ut, perspiciatis, unde, omnis, iste, natus, error, similique, sunt, in, culpa, qui, officia, deserunt, mollitia, animi, id, est, laborum, et, dolorum, fuga, et, harum, quidem, rerum, facilis, est, et, expedita, distinctio, nam, libero, tempore, cum, soluta, nobis, est, eligendi, optio, cumque, nihil, impedit, quo, porro, quisquam, est, qui, minus, id, quod, maxime, placeat, facere, possimus, omnis, voluptas, assumenda, est, omnis, dolor, repellendus, temporibus, autem, quibusdam, et, aut, consequatur, vel, illum, qui, dolorem, eum, fugiat, quo, voluptas, nulla, pariatur, at, vero, eos, et, accusamus, officiis, debitis, aut, rerum, necessitatibus, saepe, eveniet, ut, et, voluptates, repudiandae, sint, et, molestiae, non, recusandae, itaque, earum, rerum, hic, tenetur, a, sapiente, delectus, ut, aut, reiciendis, voluptatibus, maiores, doloribus, asperiores, repellat]
      supplemental: [abbas, abduco, abeo, abscido, absconditus, absens, absorbeo, absque, abstergo, absum, abundans, abutor, accedo, accendo, acceptus, accipio, accommodo, accusator, acer, acerbitas, acervus, acidus, acies, acquiro, acsi, adamo, adaugeo, addo, adduco, ademptio, adeo, adeptio, adfectus, adfero, adficio, adflicto, adhaero, adhuc, adicio, adimpleo, adinventitias, adipiscor, adiuvo, administratio, admiratio, admitto, admoneo, admoveo, adnuo, adopto, adsidue, adstringo, adsuesco, adsum, adulatio, adulescens, adultus, aduro, advenio, adversus, advoco, aedificium, aeger, aegre, aegrotatio, aegrus, aeneus, aequitas, aequus, aer, aestas, aestivus, aestus, aetas, aeternus, ager, aggero, aggredior, agnitio, agnosco, ago, ait, aiunt, alienus, alii, alioqui, aliqua, alius, allatus, alo, alter, altus, alveus, amaritudo, ambitus, ambulo, amicitia, amiculum, amissio, amita, amitto, amo, amor, amoveo, amplexus, amplitudo, amplus, ancilla, angelus, angulus, angustus, animadverto, animi, animus, annus, anser, ante, antea, antepono, antiquus, aperio, aperte, apostolus, apparatus, appello, appono, appositus, approbo, apto, aptus, apud, aqua, ara, aranea, arbitro, arbor, arbustum, arca, arceo, arcesso, arcus, argentum, argumentum, arguo, arma, armarium, armo, aro, ars, articulus, artificiose, arto, arx, ascisco, ascit, asper, aspicio, asporto, assentator, astrum, atavus, ater, atqui, atrocitas, atrox, attero, attollo, attonbitus, auctor, auctus, audacia, audax, audentia, audeo, audio, auditor, aufero, aureus, auris, aurum, aut, autem, autus, auxilium, avaritia, avarus, aveho, averto, avoco, baiulus, balbus, barba, bardus, basium, beatus, bellicus, bellum, bene, beneficium, benevolentia, benigne, bestia, bibo, bis, blandior, bonus, bos, brevis, cado, caecus, caelestis, caelum, calamitas, calcar, calco, calculus, callide, campana, candidus, canis, canonicus, canto, capillus, capio, capitulus, capto, caput, carbo, carcer, careo, caries, cariosus, caritas, carmen, carpo, carus, casso, caste, casus, catena, caterva, cattus, cauda, causa, caute, caveo, cavus, cedo, celebrer, celer, celo, cena, cenaculum, ceno, censura, centum, cerno, cernuus, certe, certo, certus, cervus, cetera, charisma, chirographum, cibo, cibus, cicuta, cilicium, cimentarius, ciminatio, cinis, circumvenio, cito, civis, civitas, clam, clamo, claro, clarus, claudeo, claustrum, clementia, clibanus, coadunatio, coaegresco, coepi, coerceo, cogito, cognatus, cognomen, cogo, cohaero, cohibeo, cohors, colligo, colloco, collum, colo, color, coma, combibo, comburo, comedo, comes, cometes, comis, comitatus, commemoro, comminor, commodo, communis, comparo, compello, complectus, compono, comprehendo, comptus, conatus, concedo, concido, conculco, condico, conduco, confero, confido, conforto, confugo, congregatio, conicio, coniecto, conitor, coniuratio, conor, conqueror, conscendo, conservo, considero, conspergo, constans, consuasor, contabesco, contego, contigo, contra, conturbo, conventus, convoco, copia, copiose, cornu, corona, corpus, correptius, corrigo, corroboro, corrumpo, coruscus, cotidie, crapula, cras, crastinus, creator, creber, crebro, credo, creo, creptio, crepusculum, cresco, creta, cribro, crinis, cruciamentum, crudelis, cruentus, crur, crustulum, crux, cubicularis, cubitum, cubo, cui, cuius, culpa, culpo, cultellus, cultura, cum, cunabula, cunae, cunctatio, cupiditas, cupio, cuppedia, cupressus, cur, cura, curatio, curia, curiositas, curis, curo, curriculum, currus, cursim, curso, cursus, curto, curtus, curvo, curvus, custodia, damnatio, damno, dapifer, debeo, debilito, decens, decerno, decet, decimus, decipio, decor, decretum, decumbo, dedecor, dedico, deduco, defaeco, defendo, defero, defessus, defetiscor, deficio, defigo, defleo, defluo, defungo, degenero, degero, degusto, deinde, delectatio, delego, deleo, delibero, delicate, delinquo, deludo, demens, demergo, demitto, demo, demonstro, demoror, demulceo, demum, denego, denique, dens, denuncio, denuo, deorsum, depereo, depono, depopulo, deporto, depraedor, deprecator, deprimo, depromo, depulso, deputo, derelinquo, derideo, deripio, desidero, desino, desipio, desolo, desparatus, despecto, despirmatio, infit, inflammatio,  paens, patior, patria, patrocinor, patruus, pauci, paulatim, pauper, pax, peccatus, pecco, pecto, pectus, pecunia, pecus, peior, pel, ocer, socius, sodalitas, sol, soleo, solio, solitudo, solium, sollers, sollicito, solum, solus, solutio, solvo, somniculosus, somnus, sonitus, sono, sophismata, sopor, sordeo, sortitus, spargo, speciosus, spectaculum, speculum, sperno, spero, spes, spiculum, spiritus, spoliatio, sponte, stabilis, statim, statua, stella, stillicidium, stipes, stips, sto, strenuus, strues, studio, stultus, suadeo, suasoria, sub, subito, subiungo, sublime, subnecto, subseco, substantia, subvenio, succedo, succurro, sufficio, suffoco, suffragium, suggero, sui, sulum, sum, summa, summisse, summopere, sumo, sumptus, supellex, super, suppellex, supplanto, suppono, supra, surculus, surgo, sursum, suscipio, suspendo, sustineo, suus, synagoga, tabella, tabernus, tabesco, tabgo, tabula, taceo, tactus, taedium, talio, talis, talus, tam, tamdiu, tamen, tametsi, tamisium, tamquam, tandem, tantillus, tantum, tardus, tego, temeritas, temperantia, templum, temptatio, tempus, tenax, tendo, teneo, tener, tenuis, tenus, tepesco, tepidus, ter, terebro, teres, terga, tergeo, tergiversatio, tergo, tergum, termes, terminatio, tero, terra, terreo, territo, terror, tersus, tertius, testimonium, texo, textilis, textor, textus, thalassinus, theatrum, theca, thema, theologus, thermae, thesaurus, thesis, thorax, thymbra, thymum, tibi, timidus, timor, titulus, tolero, tollo, tondeo, tonsor, torqueo, torrens, tot, totidem, toties, totus, tracto, trado, traho, trans, tredecim, tremo, trepide, tres, tribuo, tricesimus, triduana, triginta, tripudio, tristis, triumphus, trucido, truculenter, tubineus, tui, tum, tumultus, tunc, turba, turbo, turpe, turpis, tutamen, tutis, tyrannus, uberrime, ubi, ulciscor, ullus, ulterius, ultio, ultra, umbra, umerus, umquam, una, unde, undique, universe, unus, urbanus, urbs, uredo, usitas, usque, ustilo, ustulo, usus, uter, uterque, utilis, utique, utor, utpote, utrimque, utroque, utrum, uxor, vaco, vacuus, vado, vae, valde, valens, valeo, valetudo, validus, vallum, vapulus, varietas, varius, vehemens, vel, velociter, velum, velut, venia, venio, ventito, ventosus, ventus, venustas, ver, verbera, verbum, vere, verecundia, vereor, vergo, veritas, vero, versus, verto, verumtamen, verus, vesco, vesica, vesper, vespillo, vester, vestigium, vestrum, vetus, via, vicinus, vicissitudo, victoria, victus, videlicet, video, viduata, viduo, vigilo, vigor, vilicus, vilis, vilitas, villa, vinco, vinculum, vindico, vinitor, vinum, vir, virga, virgo, viridis, viriliter, virtus, vis, viscus, vita, vitiosus, vitium, vito, vivo, vix, vobis, vociferor, voco, volaticus, volo, volubilis, voluntarius, volup, volutabrum, volva, vomer, vomica, vomito, vorago, vorax, voro, vos, votum, voveo, vox, vulariter, vulgaris, vulgivagus, vulgo, vulgus, vulnero, vulnus, vulpes, vulticulus, vultuosus, xiphias]

    name:
      first_name: [Aaron, Abraham, Adam, Adrian, Atanazy, Agaton, Alan, Albert, Aleksander, Aleksy, Alfred, Alwar, Ambroży, Anatol, Andrzej, Antoni, Apollinary, Apollo, Arkady, Arkadiusz, Archibald, Arystarch, Arnold, Arseniusz, Artur, August, Baldwin, Bazyli, Benedykt, Beniamin, Bernard, Bertrand, Bertram, Borys, Brajan, Bruno, Cezary, Cecyliusz, Karol, Krystian, Krzysztof, Klarencjusz, Klaudiusz, Klemens, Konrad, Konstanty, Konstantyn, Kornel, Korneliusz, Korneli, Cyryl, Cyrus, Damian, Daniel, Dariusz, Dawid, Dionizy, Demetriusz, Dominik, Donald, Dorian, Edgar, Edmund, Edward, Edwin, Efrem, Efraim, Eliasz, Eleazar, Emil, Emanuel, Erast, Ernest, Eugeniusz, Eustracjusz, Fabian, Feliks, Florian, Franciszek, Fryderyk, Gabriel, Gedeon, Galfryd, Jerzy, Gerald, Gerazym, Gilbert, Gonsalwy, Grzegorz, Gwido, Harald, Henryk, Herbert, Herman, Hilary, Horacy, Hubert, Hugo, Ignacy, Igor, Hilarion, Innocenty, Hipolit, Ireneusz, Erwin, Izaak, Izajasz, Izydor, Jakub, Jeremi, Jeremiasz, Hieronim, Gerald, Joachim, Jan, Janusz, Jonatan, Józef, Jozue, Julian, Juliusz, Justyn, Kalistrat, Kazimierz, Wawrzyniec, Laurenty, Laurencjusz, Łazarz, Leon, Leonard, Leonid, Leon, Ludwik, Łukasz, Lucjan, Magnus, Makary, Marceli, Marek, Marcin, Mateusz, Maurycy, Maksym, Maksymilian, Michał, Miron, Modest, Mojżesz, Natan, Natanael, Nazariusz, Nazary, Nestor, Mikołaj, Nikodem, Olaf, Oleg, Oliwier, Onufry, Orestes, Oskar, Ansgary, Osmund, Pankracy, Pantaleon, Patryk, Patrycjusz, Patrycy, Paweł, Piotr, Filemon, Filip, Platon, Polikarp, Porfiry, Porfiriusz, Prokles, Prokul, Prokop, Kwintyn, Randolf, Rafał, Rajmund, Reginald, Rajnold, Ryszard, Robert, Roderyk, Roger, Roland, Roman, Romeo, Reginald, Rudolf, Samson, Samuel, Salwator, Sebastian, Serafin, Sergiusz, Seweryn, Zygmunt, Sylwester, Szymon, Salomon, Spirydion, Stanisław, Szczepan, Stefan, Terencjusz, Teodor, Tomasz, Tymoteusz, Tobiasz, Walenty, Walentyn, Walerian, Walery, Wiktor, Wincenty, Witalis, Włodzimierz, Władysław, Błażej, Walter, Walgierz, Wacław, Wilfryd, Wilhelm, Ksawery, Ksenofont, Jerzy, Zachariasz, Zachary, Ada, Adelajda, Agata, Agnieszka, Agrypina, Aida, Aleksandra, Alicja, Alina, Amanda, Anastazja, Angela, Andżelika, Angelina, Anna, Hanna, Antonina, Ariadna, Aurora, Barbara, Beatrycze, Berta, Brygida, Kamila, Karolina, Karolina, Kornelia, Katarzyna, Cecylia, Karolina, Chloe, Krystyna, Klara, Klaudia, Klementyna, Konstancja, Koralia, Daria, Diana, Dina, Dorota, Edyta, Eleonora, Eliza, Elżbieta, Izabela, Elwira, Emilia, Estera, Eudoksja, Eudokia, Eugenia, Ewa, Ewelina, Ferdynanda, Florencja, Franciszka, Gabriela, Gertruda, Gloria, Gracja, Jadwiga, Helena, Henryka, Nadzieja, Ida, Ilona, Helena, Irena, Irma, Izabela, Izolda, Jakubina, Joanna, Janina, Żaneta, Joanna, Ginewra, Józefina, Judyta, Julia, Julia, Julita, Justyna, Kira, Cyra, Kleopatra, Larysa, Laura, Laurencja, Laurentyna, Lea, Leila, Eleonora, Liliana, Lilianna, Lilia, Lilla, Liza, Eliza, Laura, Ludwika, Luiza, Łucja, Lucja, Lidia, Amabela, Magdalena, Malwina, Małgorzata, Greta, Marianna, Maryna, Marta, Martyna, Maria, Matylda, Maja, Maja, Melania, Michalina, Monika, Nadzieja, Noemi, Natalia, Nikola, Nina, Olga, Olimpia, Oliwia, Ofelia, Patrycja, Paula, Pelagia, Penelopa, Filipa, Paulina, Rachela, Rebeka, Regina, Renata, Rozalia, Róża, Roksana, Rufina, Ruta, Sabina, Sara, Serafina, Sybilla, Sylwia, Zofia, Stella, Stefania, Zuzanna, Tamara, Tacjana, Tekla, Teodora, Teresa, Walentyna, Waleria, Wanesa, Wiara, Weronika, Wiktoria, Wirginia, Bibiana, Bibianna, Wanda, Wilhelmina, Ksawera, Ksenia, Zoe]
      last_name: [Adamczak, Adamczyk, Adamek, Adamiak, Adamiec, Adamowicz, Adamski, Adamus, Aleksandrowicz, Andrzejczak, Andrzejewski, Antczak, Augustyn, Augustyniak, Bagiński, Balcerzak, Banach, Banasiak, Banasik, Banaś, Baran, Baranowski, Barański, Bartczak, Bartkowiak, Bartnik, Bartosik, Bednarczyk, Bednarek, Bednarski, Bednarz, Białas, Białek, Białkowski, Bielak, Bielawski, Bielecki, Bielski, Bieniek, Biernacki, Biernat, Bieńkowski, Bilski, Bober, Bochenek, Bogucki, Bogusz, Borek, Borkowski, Borowiec, Borowski, Bożek, Broda, Brzeziński, Brzozowski, Buczek, Buczkowski, Buczyński, Budziński, Budzyński, Bujak, Bukowski, Burzyński, Bąk, Bąkowski, Błaszczak, Błaszczyk, Cebula, Chmiel, Chmielewski, Chmura, Chojnacki, Chojnowski, Cholewa, Chrzanowski, Chudzik, Cichocki, Cichoń, Cichy, Ciesielski, Cieśla, Cieślak, Cieślik, Ciszewski, Cybulski, Cygan, Czaja, Czajka, Czajkowski, Czapla, Czarnecki, Czech, Czechowski, Czekaj, Czerniak, Czerwiński, Czyż, Czyżewski, Dec, Dobosz, Dobrowolski, Dobrzyński, Domagała, Domański, Dominiak, Drabik, Drozd, Drozdowski, Drzewiecki, Dróżdż, Dubiel, Duda, Dudek, Dudziak, Dudzik, Dudziński, Duszyński, Dziedzic, Dziuba, Dąbek, Dąbkowski, Dąbrowski, Dębowski, Dębski, Długosz, Falkowski, Fijałkowski, Filipek, Filipiak, Filipowicz, Flak, Flis, Florczak, Florek, Frankowski, Frąckowiak, Frączek, Frątczak, Furman, Gadomski, Gajda, Gajewski, Gaweł, Gawlik, Gawron, Gawroński, Gałka, Gałązka, Gil, Godlewski, Golec, Gołąb, Gołębiewski, Gołębiowski, Grabowski, Graczyk, Grochowski, Grudzień, Gruszczyński, Gruszka, Grzegorczyk, Grzelak, Grzesiak, Grzesik, Grześkowiak, Grzyb, Grzybowski, Grzywacz, Gutowski, Guzik, Gwóźdź, Góra, Góral, Górecki, Górka, Górniak, Górny, Górski, Gąsior, Gąsiorowski, Głogowski, Głowacki, Głąb, Hajduk, Herman, Iwański, Izdebski, Jabłoński, Jackowski, Jagielski, Jagiełło, Jagodziński, Jakubiak, Jakubowski, Janas, Janiak, Janicki, Janik, Janiszewski, Jankowiak, Jankowski, Janowski, Janus, Janusz, Januszewski, Jaros, Jarosz, Jarząbek, Jasiński, Jastrzębski, Jaworski, Jaśkiewicz, Jezierski, Jurek, Jurkiewicz, Jurkowski, Juszczak, Jóźwiak, Jóźwik, Jędrzejczak, Jędrzejczyk, Jędrzejewski, Kacprzak, Kaczmarczyk, Kaczmarek, Kaczmarski, Kaczor, Kaczorowski, Kaczyński, Kaleta, Kalinowski, Kalisz, Kamiński, Kania, Kaniewski, Kapusta, Karaś, Karczewski, Karpiński, Karwowski, Kasperek, Kasprzak, Kasprzyk, Kaszuba, Kawa, Kawecki, Kałuża, Kaźmierczak, Kiełbasa, Kisiel, Kita, Klimczak, Klimek, Kmiecik, Kmieć, Knapik, Kobus, Kogut, Kolasa, Komorowski, Konieczna, Konieczny, Konopka, Kopczyński, Koper, Kopeć, Korzeniowski, Kos, Kosiński, Kosowski, Kostecki, Kostrzewa, Kot, Kotowski, Kowal, Kowalczuk, Kowalczyk, Kowalewski, Kowalik, Kowalski, Koza, Kozak, Kozieł, Kozioł, Kozłowski, Kołakowski, Kołodziej, Kołodziejczyk, Kołodziejski, Krajewski, Krakowiak, Krawczyk, Krawiec, Kruk, Krukowski, Krupa, Krupiński, Kruszewski, Krysiak, Krzemiński, Krzyżanowski, Król, Królikowski, Książek, Kubacki, Kubiak, Kubica, Kubicki, Kubik, Kuc, Kucharczyk, Kucharski, Kuchta, Kuciński, Kuczyński, Kujawa, Kujawski, Kula, Kulesza, Kulig, Kulik, Kuliński, Kurek, Kurowski, Kuś, Kwaśniewski, Kwiatkowski, Kwiecień, Kwieciński, Kędzierski, Kędziora, Kępa, Kłos, Kłosowski, Lach, Laskowski, Lasota, Lech, Lenart, Lesiak, Leszczyński, Lewandowski, Lewicki, Leśniak, Leśniewski, Lipiński, Lipka, Lipski, Lis, Lisiecki, Lisowski, Maciejewski, Maciąg, Mackiewicz, Madej, Maj, Majcher, Majchrzak, Majewski, Majka, Makowski, Malec, Malicki, Malinowski, Maliszewski, Marchewka, Marciniak, Marcinkowski, Marczak, Marek, Markiewicz, Markowski, Marszałek, Marzec, Masłowski, Matusiak, Matuszak, Matuszewski, Matysiak, Mazur, Mazurek, Mazurkiewicz, Maćkowiak, Małecki, Małek, Maślanka, Michalak, Michalczyk, Michalik, Michalski, Michałek, Michałowski, Mielczarek, Mierzejewski, Mika, Mikołajczak, Mikołajczyk, Mikulski, Milczarek, Milewski, Miller, Misiak, Misztal, Miśkiewicz, Modzelewski, Molenda, Morawski, Motyka, Mroczek, Mroczkowski, Mrozek, Mróz, Mucha, Murawski, Musiał, Muszyński, Młynarczyk, Napierała, Nawrocki, Nawrot, Niedziela, Niedzielski, Niedźwiecki, Niemczyk, Niemiec, Niewiadomski, Noga, Nowacki, Nowaczyk, Nowak, Nowakowski, Nowicki, Nowiński, Olczak, Olejniczak, Olejnik, Olszewski, Orzechowski, Orłowski, Osiński, Ossowski, Ostrowski, Owczarek, Paczkowski, Pająk, Pakuła, Paluch, Panek, Partyka, Pasternak, Paszkowski, Pawelec, Pawlak, Pawlicki, Pawlik, Pawlikowski, Pawłowski, Pałka, Piasecki, Piechota, Piekarski, Pietras, Pietruszka, Pietrzak, Pietrzyk, Pilarski, Pilch, Piotrowicz, Piotrowski, Piwowarczyk, Piórkowski, Piątek, Piątkowski, Piłat, Pluta, Podgórski, Polak, Popławski, Porębski, Prokop, Prus, Przybylski, Przybysz, Przybył, Przybyła, Ptak, Puchalski, Pytel, Płonka, Raczyński, Radecki, Radomski, Rak, Rakowski, Ratajczak, Robak, Rogala, Rogalski, Rogowski, Rojek, Romanowski, Rosa, Rosiak, Rosiński, Ruciński, Rudnicki, Rudziński, Rudzki, Rusin, Rutkowski, Rybak, Rybarczyk, Rybicki, Rzepka, Różański, Różycki, Sadowski, Sawicki, Serafin, Siedlecki, Sienkiewicz, Sieradzki, Sikora, Sikorski, Sitek, Siwek, Skalski, Skiba, Skibiński, Skoczylas, Skowron, Skowronek, Skowroński, Skrzypczak, Skrzypek, Skóra, Smoliński, Sobczak, Sobczyk, Sobieraj, Sobolewski, Socha, Sochacki, Sokołowski, Sokół, Sosnowski, Sowa, Sowiński, Sołtys, Sołtysiak, Sroka, Stachowiak, Stachowicz, Stachura, Stachurski, Stanek, Staniszewski, Stanisławski, Stankiewicz, Stasiak, Staszewski, Stawicki, Stec, Stefaniak, Stefański, Stelmach, Stolarczyk, Stolarski, Strzelczyk, Strzelecki, Stępień, Stępniak, Surma, Suski, Szafrański, Szatkowski, Szczepaniak, Szczepanik, Szczepański, Szczerba, Szcześniak, Szczygieł, Szczęsna, Szczęsny, Szeląg, Szewczyk, Szostak, Szulc, Szwarc, Szwed, Szydłowski, Szymański, Szymczak, Szymczyk, Szymkowiak, Szyszka, Sławiński, Słowik, Słowiński, Tarnowski, Tkaczyk, Tokarski, Tomala, Tomaszewski, Tomczak, Tomczyk, Tracz, Trojanowski, Trzciński, Trzeciak, Turek, Twardowski, Urban, Urbanek, Urbaniak, Urbanowicz, Urbańczyk, Urbański, Walczak, Walkowiak, Warchoł, Wasiak, Wasilewski, Wawrzyniak, Wesołowski, Wieczorek, Wierzbicki, Wilczek, Wilczyński, Wilk, Winiarski, Witczak, Witek, Witkowski, Wiącek, Więcek, Więckowski, Wiśniewski, Wnuk, Wojciechowski, Wojtas, Wojtasik, Wojtczak, Wojtkowiak, Wolak, Woliński, Wolny, Wolski, Woś, Woźniak, Wrona, Wroński, Wróbel, Wróblewski, Wypych, Wysocki, Wyszyński, Wójcicki, Wójcik, Wójtowicz, Wąsik, Węgrzyn, Włodarczyk, Włodarski, Zaborowski, Zabłocki, Zagórski, Zając, Zajączkowski, Zakrzewski, Zalewski, Zaremba, Zarzycki, Zaręba, Zawada, Zawadzki, Zdunek, Zieliński, Zielonka, Ziółkowski, Zięba, Ziętek, Zwoliński, Zych, Zygmunt, Łapiński, Łuczak, Łukasiewicz, Łukasik, Łukaszewski, Śliwa, Śliwiński, Ślusarczyk, Świderski, Świerczyński, Świątek, Żak, Żebrowski, Żmuda, Żuk, Żukowski, Żurawski, Żurek, Żyła]
      prefix: [Pan, Pani]
      title:
        descriptor: [Lead, Senior, Direct, Corporate, Dynamic, Future, Product, National, Regional, District, Central, Global, Customer, Investor, Dynamic, International, Legacy, Forward, Internal, Human, Chief, Principal]
        level: [Solutions, Program, Brand, Security, Research, Marketing, Directives, Implementation, Integration, Functionality, Response, Paradigm, Tactics, Identity, Markets, Group, Division, Applications, Optimization, Operations, Infrastructure, Intranet, Communications, Web, Branding, Quality, Assurance, Mobility, Accounts, Data, Creative, Configuration, Accountability, Interactions, Factors, Usability, Metrics]
        job: [Supervisor, Associate, Executive, Liaison, Officer, Manager, Engineer, Specialist, Director, Coordinator, Administrator, Architect, Analyst, Designer, Planner, Orchestrator, Technician, Developer, Producer, Consultant, Assistant, Facilitator, Agent, Representative, Strategist]
      name:
        - "#{prefix} #{first_name} #{last_name}"
        - "#{first_name} #{last_name}"
        - "#{first_name} #{last_name}"
        - "#{first_name} #{last_name}"
        - "#{first_name} #{last_name}"
        - "#{first_name} #{last_name}"
      name_with_middle:
        - "#{prefix} #{first_name} #{last_name} #{last_name}"
        - "#{first_name} #{last_name} #{last_name}"
        - "#{first_name} #{last_name} #{last_name}"
        - "#{first_name} #{last_name} #{last_name}"
        - "#{first_name} #{last_name} #{last_name}"

    phone_number:
      formats: ['12-###-##-##', '13-###-##-##', '14-###-##-##', '15-###-##-##', '16-###-##-##', '17-###-##-##', '18-###-##-##', '22-###-##-##', '23-###-##-##', '24-###-##-##', '25-###-##-##', '29-###-##-##', '32-###-##-##', '33-###-##-##', '34-###-##-##', '41-###-##-##', '42-###-##-##', '43-###-##-##', '44-###-##-##', '46-###-##-##', '48-###-##-##', '52-###-##-##', '54-###-##-##', '55-###-##-##', '56-###-##-##', '58-###-##-##', '59-###-##-##', '61-###-##-##', '62-###-##-##', '63-###-##-##', '65-###-##-##', '67-###-##-##', '68-###-##-##', '71-###-##-##', '74-###-##-##', '75-###-##-##', '76-###-##-##', '77-###-##-##', '81-###-##-##', '82-###-##-##', '83-###-##-##', '84-###-##-##', '85-###-##-##', '86-###-##-##', '87-###-##-##', '89-###-##-##', '91-###-##-##', '94-###-##-##', '95-###-##-##']
    cell_phone:
      formats: ['50-###-##-##', '51-###-##-##', '53-###-##-##', '57-###-##-##', '60-###-##-##', '66-###-##-##', '69-###-##-##', '72-###-##-##', '73-###-##-##', '78-###-##-##', '79-###-##-##', '88-###-##-##']
