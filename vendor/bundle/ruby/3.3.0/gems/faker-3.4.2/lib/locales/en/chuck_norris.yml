en:
  faker:
    chuck_norris:
      fact: [
        "All arrays <PERSON> declares are of infinite size, because <PERSON> knows no bounds.",
        "<PERSON> doesn't have disk latency because the hard drive knows to hurry the hell up.",
        "All browsers support the hex definitions #chuck and #norris for the colors black and blue.",
        "<PERSON> can't test for equality because he has no equal.",
        "<PERSON> doesn't need garbage collection because he doesn't call .Dispose(), he calls .Drop<PERSON>ick().",
        "<PERSON>'s first program was kill -9.", "<PERSON> burst the dot com bubble.",
        "<PERSON> writes code that optimizes itself.",
        "<PERSON> can write infinite recursion functions... and have them return.",
        "<PERSON> can solve the Towers of Hanoi in one move.",
        "The only pattern <PERSON> knows is God Object.",
        "<PERSON> finished World of Warcraft.",
        "Project managers never ask <PERSON> for estimations... ever.",
        "<PERSON> doesn't use web standards as the web will conform to him.",
        "\"It works on my machine\" always holds true for <PERSON>.",
        "Whiteboards are white because <PERSON> scared them that way.",
        "<PERSON>'s beard can type 140 wpm.",
        "<PERSON> can unit test an entire application with a single assert.",
        "<PERSON> doesn't bug hunt, as that signifies a probability of failure. He goes bug killing.",
        "<PERSON>'s keyboard doesn't have a Ctrl key because nothing controls <PERSON>.",
        "<PERSON> doesn't need a debugger, he just stares down the bug until the code confesses.",
        "<PERSON> can access private methods.",
        "<PERSON> <PERSON> can instantiate an abstract class.",
        "<PERSON> <PERSON> doesn't need to know about class factory pattern. He can instantiate interfaces.",
        "The class object inherits from <PERSON> <PERSON>.",
        "For <PERSON> <PERSON>, NP-Hard = O(1).",
        "<PERSON> <PERSON> knows the last digit of <PERSON><PERSON>.",
        "<PERSON> <PERSON> can divide by zero.",
        "<PERSON> <PERSON> doesn't get compiler errors,
        the language changes itself to accommodate Chuck Norris.",
        "The programs that Chuck Norris writes don't have version numbers because he only writes them once. If a user reports a bug or has a feature request they don't live to see the sun set.",
        "Chuck Norris doesn't believe in floating point numbers because they can't be typed on his binary keyboard.",
        "Chuck Norris solved the Travelling Salesman problem in O(1) time.",
        "Chuck Norris never gets a syntax error. Instead, the language gets a DoesNotConformToChuck error.",
        "No statement can catch the ChuckNorrisException.",
        "Chuck Norris doesn't program with a keyboard. He stares the computer down until it does what he wants.",
        "Chuck Norris doesn't pair program.",
        "Chuck Norris can write multi-threaded applications with a single thread.",
        "There is no Esc key on Chuck Norris' keyboard, because no one escapes Chuck Norris.",
        "Chuck Norris doesn't delete files, he blows them away.",
        "Chuck Norris can binary search unsorted data.",
        "Chuck Norris breaks RSA 128-bit encrypted codes in milliseconds.",
        "Chuck Norris went out of an infinite loop.",
        "Chuck Norris can read all encrypted data, because nothing can hide from Chuck Norris.",
        "Chuck Norris hosting is 101% uptime guaranteed.",
        "When a bug sees Chuck Norris, it flees screaming in terror, and then immediately self-destructs to avoid being roundhouse-kicked.",
        "Chuck Norris rewrote the Google search engine from scratch.",
        "Chuck Norris doesn't need the cloud to scale his applications, he uses his laptop.",
        "Chuck Norris can access the DB from the UI.",
        "Chuck Norris' protocol design method has no status, requests or responses, only commands.",
        "Chuck Norris' programs occupy 150% of CPU, even when they are not executing.",
        "Chuck Norris can spawn threads that complete before they are started.",
        "Chuck Norris programs do not accept input.",
        "Chuck Norris doesn't need an OS.",
        "Chuck Norris can compile syntax errors.",
        "Chuck Norris compresses his files by doing a flying round house kick to the hard drive.",
        "Chuck Norris doesn't use a computer because a computer does everything slower than Chuck Norris.",
        "You don't disable the Chuck Norris plug-in, it disables you.",
        "Chuck Norris doesn't need a java compiler, he goes straight to .war",
        "Chuck Norris can use GOTO as much as he wants to. Telling him otherwise is considered harmful.",
        "There is nothing regular about Chuck Norris' expressions.",
        "Quantum cryptography does not work on Chuck Norris. When something is being observed by Chuck it stays in the same state until he's finished.",
        "There is no need to try catching Chuck Norris' exceptions for recovery; every single throw he does is fatal.",
        "Chuck Norris' beard is immutable.",
        "Chuck Norris' preferred IDE is hexedit.",
        "Chuck Norris is immutable. If something's going to change, it's going to have to be the rest of the universe.",
        "Chuck Norris' addition operator doesn't commute; it teleports to where he needs it to be.",
        "Anonymous methods and anonymous types are really all called Chuck Norris. They just don't like to boast.",
        "Chuck Norris doesn't have performance bottlenecks. He just makes the universe wait its turn.",
        "Chuck Norris does not use exceptions when programming. He has not been able to identify any of his code that is not exceptional.",
        "When Chuck Norris' code fails to compile the compiler apologises.",
        "Chuck Norris does not use revision control software. None of his code has ever needed revision.",
        "Chuck Norris can recite π. Backwards.",
        "When Chuck Norris points to null, null quakes in fear.",
        "Chuck Norris has root access to your system.",
        "When Chuck Norris gives a method an argument, the method loses.",
        "Chuck Norris' keyboard doesn't have a F1 key, the computer asks him for help.",
        "When Chuck Norris presses Ctrl+Alt+Delete, worldwide computer restart is initiated."
      ]
