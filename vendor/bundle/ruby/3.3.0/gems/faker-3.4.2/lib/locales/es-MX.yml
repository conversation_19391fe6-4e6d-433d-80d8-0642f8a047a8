# encoding: utf-8
es-MX:
  faker:
    address:
      city_prefix: ""
      city_suffix: ""
      country: [Afganistán, Albania, Alemania, Andorra, Angola, Anguila, Antigua y Barbuda, Antártida, Arabia Saudita, Argelia, Argentina, Armenia, Aruba, Australia, Austria, Autoridad Nacional Palestina, Azerbaiyán, Bahamas, Bangladés, Barbados, Baréin, Belice, Benín, Bermudas, Bielorrusia, Birmania, Bolivia, Bonaire, San Eustaquio y Saba, Bosnia y Herzegovina, Botsuana, Brasil, Brunéi, Bulgaria, Burkina Faso, Burundi, Bután, Bélgica, Cabo Verde, Camboya, Camerún, Canadá, Chad, Chile, China, Chipre, Ciudad del Vaticano, Colombia, Comoras, Corea del Norte, Corea del Sur, Costa Rica, Costa de Marfil, Croacia, Cuba, Curaçao, Dinamarca, Dominica, Ecuador, Egipto, El Salvador, Emiratos Árabes Unidos, Eritrea, Eslovaquia, Eslovenia, España, Estados Unidos, Estonia, Etiopía, Filipinas, Finlandia, Fiyi, Francia, Gabón, Gambia, Georgia, Ghana, Gibraltar, Granada, Grecia, Groenlandia, Guadalupe, Guam, Guatemala, Guayana Francesa, Guernsey, Guinea, Guinea Ecuatorial, Guinea-Bissau, Guyana, Haití, Honduras, Hong Kong, Hungría, India, Indonesia, Irak, Irlanda, Irán, Isla Bouvet, Isla de Man, Isla de Navidad, Islandia, Islas Caimán, Islas Cocos, Islas Cook, Islas Faroe, Islas Georgias del Sur y Sandwich del Sur, Islas Heard y McDonald, Islas Malvinas, Islas Marianas del Norte, Islas Marshall, Islas Pitcairn, Islas Salomón, Islas Turcas y Caicos, Islas Vírgenes Británicas, Islas Vírgenes de los Estados Unidos, Islas ultramarinas de Estados Unidos, Israel, Italia, Jamaica, Japón, Jersey, Jordania, Kazajistán, Kenia, Kirguistán, Kiribati, Kuwait, Laos, Lesoto, Letonia, Liberia, Libia, Liechtenstein, Lituania, Luxemburgo, Líbano, Macao, Madagascar, Malasia, Malaui, Maldivas, Malta, Malí, Marruecos, Martinica, Mauricio, Mauritania, Mayotte, Micronesia, Moldavia, Mongolia, Montenegro, Montserrat, Mozambique, México, Mónaco, Namibia, Nauru, Nepal, Nicaragua, Nigeria, Niue, Norfolk, Noruega, Nueva Caledonia, Nueva Zelanda, Níger, Omán, Pakistán, Palaos, Panamá, Papúa Nueva Guinea, Paraguay, Países Bajos, Perú, Polinesia Francesa, Polonia, Portugal, Qatar, Reino Unido, Rep. Dem. del Congo, República Centroafricana, República Checa, República Dominicana, República de Macedonia, República del Congo, Reunión, Ruanda, Rumania, Rusia, Samoa, Samoa Estadounidense, San Bartolomé, San Cristóbal y Nieves, San Marino, San Martín, San Martín (parte holandesa), San Pedro y Miquelón, San Vicente y las Granadinas, Santa Helena, A. y T., Santa Lucía, Santo Tomé y Príncipe, Senegal, Serbia, Seychelles, Sierra Leona, Singapur, Siria, Somalia, Sri Lanka, Suazilandia, Sudáfrica, Sudán, Sudán del Sur, Suecia, Suiza, Surinam, Svalbard y Jan Mayen, Sáhara Occidental, Tailandia, Taiwán, Tanzania, Tayikistán, Territorio Británico del Océano Índico, Territorios Australes Franceses, Timor Oriental, Togo, Tokelau, Tonga, Trinidad y Tobago, Turkmenistán, Turquía, Tuvalu, Túnez, Ucrania, Uganda, Uruguay, Uzbekistán, Vanuatu, Venezuela, Vietnam, Wallis y Futuna, Yemen, Yibuti, Zambia, Zimbabue, Åland]
      building_number: ['S/N', '#', '##', '###', '####', '#####']
      municipality: [Abasolo, Acapulco, Actopan, Acuña, Aguascalientes, Ahome, Ahuacatlán, Ahuacuotzingo, Ahuatlan, Alaquines, Altar, Amatlán de Cañas, Angostura, Apan, Apaseo el Alto, Apizaco, Apodaca, Aporo, Aquila, Armería, Arroyo Seco, Arteaga, Asientos, Atenco, Atenguillo, Atolinga, Atzalan, Ayapango, Azcapotzalco, Bacadehuachi, Bacalar, Bacanora, Badiraguato, Benito Juárez, Brise, Buenaventura, Burgos, Bustamante, Cabo Corrientes, Caborca, Calakmul, Calera, Calnali, Calvillo, Camargo, Campeche, Canatlán, Cancún, Candelaria, Cantamayec, Carbo, Carmen, Catorce, Celaya, Centla, Centro, Cerritos, Chalchihuites, Champotón, Chanal, Chapulhuacán, Chapultepec, Chavinda, Chiautempan, Chichiquila, Chicoasen, Chietla, Chihuahua, Chimalhuacán, Choix, Citlaltepetl, Ciudad Madero, Ciudad Valles, Coatzacoalcos, Cocula, Colima, Comala, Comondú, Comonfort, Compostela, Copainala, Copalillo, Copanatoyac, Coquimatlán, Coronango, Cosio, Coyame del Sotol, Coyoacán, Cozumel, Cualac, Cuatro Ciénegas, Cuauhtémoc, Cucurpe, Cuencame, Cuernavaca, Culiacán, Cunduacán, Cárdenas, Doctor Mora, Durango, Dzidzantun, Ecatepec, El Fuerte, El Llano, El Mante, El Marqués, El Naranjo, El Salvador, Emiliano Zapata, Ensenada, Epazoyucan, Epitacio Huerta, Erongaricuaro, Escarcega, Esperanza, Ezequiel Montes, Galeana, Genaro Codina, General Escobedo, González, Gral. Zaragoza, Guachochi, Guadalajara, Guadalupe, Guanacevi, Guerrero, Gómez Palacio, Hecelchakan, Hermosillo, Hocaba, Hopelchen, Huajicori, Hualahuises, Huanimaro, Huayacocotla, Huejucar, Hueyotlipan, Huimilpan, Huixquilucan, Hunucma, Iliatenco, Inde, Irapuato, Irimbo, Isla Mujeres, Ixil, Ixtapaluca, Ixtlahuacán, Ixtlan de Juárez, Ixtlán del Río, Iztacalco, Jalpa de Méndez, Jalpan de Serra, Jamapa, Jesús María, Jiutepec, Jonuta, Juárez, Kaua, La Huerta, La Independencia, La Paz, La Yesca, Las Margaritas, Lazaro Cárdenas, Lerdo, León, Lolotla, Loreto, Los Cabos, Luvianos, López, Magdalena, Manzanillo, Marin, Matamoros, Matehuala, Mazapil, Mazatepec, Mazatlan, Mazatlán, Mecatlan, Melchor Ocampo, Metepec, Metztitlán, Mexicali, Mezquital, Mier y Noriega, Miguel Hidalgo, Milpa Alta, Minatitlán, Miquihuana, Moctezuma, Monclova, Monte Escobedo, Montemorelos, Monterrey, Morelia, Morelos, Moroleón, Mulege, Méndez, Mérida, Nacajuca, Nadadores, Naucalpan, Naupan, Nava, Navolato, Nazareno Etla, Nazas, Nezahualcóyotl, Nombre de Dios, Nuevo Laredo, Nuevo Zoquiapam, Ocampo, Ojinaga, Othón P. Blanco, Oxkutzcab, Palizada, Panuco, Paracuaro, Patzcuaro, Pedro Escobedo, Penjamo, Pinal de Amoles, Poncitlan, Progreso, Puebla, Puente de Ixtla, Queretaro, Querétaro, Rayones, Reynosa, Rincón de Romos, Romita, Rosario, Salamanca, Salinas Victoria, Saltillo, Salto de Agua, San Blas, San Buenaventura, San Carlos, San Ignacio, San Juan, San Juan del Río, San Lorenzo, San Luis Potosí, San Marcos, San Miguel Yotao, San Nicolas, San Nicolás de los Garza, Santa Catarina, Santiago Nuyoo, Santiago Tenango, Saric, Sinaloa, Singuilucan, Solidaridad, Soteapan, Suchiate, Susticacán, Tacotalpa, Tahdziu, Tamazunchale, Tancanhuitz, Tapachula, Tapilula, Taxco de Alarcón, Teapa, Tecamac, Tecate, Tecomán, Tecuala, Temosachic, Tenabo, Tenango del Aire, Tenosique, Teocaltiche, Tepalcingo, Tepezala, Tepic, Tepoztlan, Tetecala, Tetepango, Tijuana, Tlacuilotepec, Tlalchapa, Tlalnepantla, Tlalpan, Tlaquepaque, Tlayacapan, Tocatlan, Tochimilco, Tocumbo, Tolimán, Toluca, Tonalá, Tonatico, Torreón, Totolac, Tulum, Tuxpan, Tuxtla, Umán, Unión Hidalgo, Uruachi, Valladolid, Vanegas, Veracruz, Villa Victoria, Villa de Álvarez, Villa del Carbón, Villahermosa, Xalapa, Xalisco, Xaloztoc, Xico, Xicohtzinco, Xochihuehuetlán, Xochimilco, Xochistlahuaca, Xochitepec, Yauhquemehcan, Yecapixtla, Yogana, Zacatecas, Zacatepec, Zapopan, Zaragoza, Zongolica, Álvaro Obregón]
      street_prefix: [Arroyo, Avenida, Bajada, Barranco, Calle, Camino, Carretera, Conjunto, Entrada, Escalinata, Explanada, Glorieta, Grupo, Huerta, Jardines, Lago, Manzana, Mercado, Monte, Muelle, Parque, Pasaje, Paseo, Plaza, Privada, Prolongación, Quinta, Rampa, Rincón, Salida, Sector, Subida, Vía]
      secondary_address: ['Apartamento ##', 'Departamento ###', 'Depto. ###', 'Interior ###', 'Interior ?#', 'Int. #','Piso #', 'Piso ##', '#ª Planta', 'Planta alta', 'Planta baja']
      postcode: ['#####']
      state: [Aguascalientes, Baja California, Baja California Sur, Campeche, Coahuila, Colima, Chiapas, Chihuahua, Ciudad de México, Durango, Guanajuato, Guerrero, Hidalgo, Jalisco, México, Michoacán, Morelos, Nayarit, Nuevo León, Oaxaca, Puebla, Querétaro, Quintana Roo, San Luis Potosí, Sinaloa, Sonora, Tabasco, Tamaulipas, Tlaxcala, Veracruz, Yucatán, Zacatecas]
      state_abbr: [AGU, BCN, BCS, CAM, CHP, CHH, COA, COL, DIF, DUR, GUA, GRO, HID, JAL, MEX, MIC, MOR, NAY, NLE, OAX, PUE, QUE, ROO, SLP, SIN, SON, TAB, TAM, TLA, VER, YUC, ZAC]
      time_zone: [África/Argel, África/Cairo, África/Casablanca, África/Harare, África/Johannesburgo, África/Monrovia, África/Nairobi, América/Argentina/Buenos_Aires, América/Bogotá, América/Caracas, América/Chicago, América/Chihuahua, América/Ciudad_de_México, América/Denver, América/Guatemala, América/Guyana, América/Halifax, América/Indiana/Indianapolis, América/Juneau, América/La_Paz, América/Lima, América/Los_Angeles, América/Mazatlán, América/Monterrey, América/Nueva_York, América/Nuuk, América/Phoenix, América/Regina, América/San_Juan_de_Terranova, América/Santiago, América/São_Paulo, América/Tijuana, Asia/Almatý, Asia/Bagdad, Asia/Bakú, Asia/Bangkok, Asia/Calcuta, Asia/Chongqing, Asia/Colombo, Asia/Daca, Asia/Ekaterimburgo, Asia/Ereván, Asia/Hong_Kong, Asia/Irkutsk, Asia/Jerusalén, Asia/Kabul, Asia/Kamchatka, Asia/Karachi, Asia/Katmandú, Asia/Krasnoyarsk, Asia/Kuala_Lumpur, Asia/Kuwait, Asia/Magadán, Asia/Mascate, Asia/Novosibirsk, Asia/Rangún, Asia/Riad, Asia/Seúl, Asia/Shanghai, Asia/Singapur, Asia/Taipéi, Asia/Taskent, Asia/Teherán, Asia/Tiflis, Asia/Tokio, Asia/Ulán_Bator, Asia/Urumchi, Asia/Vladivostok, Asia/Yakarta, Asia/Yakutsk, Atlantic/Azores, Atlantic/Cabo_Verde, Atlantic/Georgia_Del_Sur, Australia/Adelaida, Australia/Brisbane, Australia/Darwin, Australia/Hobart, Australia/Melbourne, Australia/Perth, Australia/Sydney, Etc/UTC, Europa/Atenas, Europa/Belgrado, Europa/Berlín, Europa/Bratislava, Europa/Bruselas, Europa/Bucarest, Europa/Budapest, Europa/Copenhague, Europa/Dublín, Europa/Estambul, Europa/Estocolmo, Europa/Helsinki, Europa/Kiev, Europa/Lisboa, Europa/Liubliana, Europa/Londres, Europa/Madrid, Europa/Minsk, Europa/Moscú, Europa/Paris, Europa/Praga, Europa/Riga, Europa/Roma, Europa/Sarajevo, Europa/Skopie, Europa/Sofía, Europa/Tallin, Europa/Viena, Europa/Vilna, Europa/Warsaw Europa/Zagreb, Europa/Ámsterdam, Pacífico/Apia, Pacífico/Auckland, Pacífico/Fakaofo, Pacífico/Fiji, Pacífico/Guaján, Pacífico/Honolulú, Pacífico/Islas_Midway, Pacífico/Majuro, Pacífico/Numea, Pacífico/Pago_Pago, Pacífico/Puerto_Moresby, Pacífico/Tongatapu]
      city:
        - "#{municipality}"
      city_with_state:
        - "#{municipality}, #{state}"
      street_name:
        - "#{street_prefix} #{Name.first_name}"
        - "#{street_prefix} #{Name.first_name} #{Name.last_name}"
        - "#{street_prefix} #{state}"
        - "#{street_prefix} #{municipality}"
      street_address:
        - "#{street_name} #{building_number}"
        - "#{street_name} #{building_number} #{secondary_address}"
      default_country: [México]
      default_country_code: ["MX"]

    company:
      suffix: [S.A., S.A. de C.V., S.R.L, S.A.B., S.C.]
      prefix: [Grupo, Sociedad, Grupo Financiero, Colegio, Fondo]
      name:
        - "#{Name.last_name} #{suffix}"
        - "#{prefix} #{Name.last_name} #{suffix}"
        - "#{Name.last_name} y #{Name.last_name} #{suffix}"
        - "#{Name.last_name} #{Name.last_name} #{suffix}"
        - "#{Name.last_name}, #{Name.last_name} y #{Name.last_name} Asociados #{suffix}"

      university:
            suffix: [Norte, del Norte, Occidental, Oeste, Del Sur, Sur, Oriental, Oriente, de Graduados, de Administración]
            prefix: [Universidad, Instituto, Academia, Colegio]
            name:
              - "#{University.prefix} #{Name.last_name} #{University.suffix}"
              - "#{University.prefix} #{Name.last_name}"
              - "#{University.prefix} #{Address.state} #{University.suffix}"

    internet:
      domain_suffix: [com, com.mx, mx, info, me, org, org.mx]

    name:
      first_name: [José Luis, Juan, Francisco, José, Antonio, Jesús, Miguel Ángel, Pedro, Alejandro, Manuel, Juan Carlos, Roberto, Jorge, Carlos, Fernando, Ricardo, Miguel, Javier, Martín, Rafael, Raúl, Arturo, Daniel, Eduardo, Enrique, Mario, José Antonio, Sergio, Gerardo, Salvador, Marco Antonio, Alfredo, David, Armando, Alberto, Luis, Óscar, Ramón, Guillermo, Rubén, Jaime, Felipe, Julio César, Andrés, Pablo, Ángel, Gabriel, Héctor, Alfonso, José Guadalupe, Agustín, Ignacio, Víctor, Rogelio, Gustavo, Ernesto, Rodolfo, Luis Alberto, Gilberto, Vicente, Juan Antonio, Tomás, Israel, César, Adrián, Ismael, Santiago, Humberto, Gregorio, Joel, Esteban, José Alfredo, Nicolás, Omar, Moisés, Félix, Lorenzo, Samuel, Carlos Alberto, José Angel, Ramiro, Abel, Jorge Luis, Marcos, Mario Alberto, Rodrigo, Edgar, Isidro, José Alberto, Leonardo, Benjamín, Jorge Alberto, Julio, Raymundo, Víctor Hugo, Saúl, Benito, José Juan, Rigoberto, Hugo,  Guadalupe, María, Margarita, Verónica, María Elena, Josefina, Leticia, Teresa, Patricia, Rosa, Martha, Rosa María, Alicia, Yolanda, Francisca, Silvia, Elizabeth, Gloria, Ana María, Gabriela, Alejandra, María Luisa, María de Lourdes, Adriana, Araceli, Antonia, Lucía, Carmen, Irma, Claudia, Beatriz, Isabel, Laura, Maribel, Graciela, Virginia, Catalina, Esperanza, Angélica, Maricela, Cecilia, Susana, Cristina, Julia, Concepción, Victoria, Ofelia, Rocío, Carolina, Raquel, Petra, Lorena, Reyna, Sandra, Paula, Guillermina, Sara, Elvira, Manuela, Marisol, Mónica, Erika, Celia, Luz María, Irene, Magdalena, Estela, Ángela, Rosario, Esther, Eva, Norma, Aurora, Socorro, Consuelo, Lidia, Bertha, Sofía, Dolores, Elena, Rosalba, Liliana, Andrea, Adela, Mariana, Fabiola, Karina, Martina, Marcela, Miriam, Mercedes, Marina, Amalia, Olivia, Angelina, Sonia, Agustina, Edith, Lilia, Micaela]
      last_name: [Hernández, García, Martínez, López, González, Rodríguez, Pérez, Sánchez, Ramírez, Cruz, Flores, Gómez, Morales, Vázquez, Reyes, Jímenez, Torres, Díaz, Gutiérrez, Mendoza, Ruiz, Aguilar, Ortiz, Castillo, Moreno, Romero, Álvarez, Chávez, Rivera, Juárez, Ramos, Méndez, Domínguez, Herrera, Medina, Vargas, Castro, Guzmán, Velázquez, Muñoz, Rojas, Contreras, Salazar, Luna, de la Cruz, Ortega, Guerrero, Santiago, Estrada, Bautista, Cortés, Soto, Alvarado, Espinoza, Lara, Ávila, Ríos, Cervantes, Silva, Delgado, Vega, Márquez, Sandoval, Fernández, León, Carrillo, Mejía, Solís, Núñez, Rosas, Valdez, Ibarra, Campos, Santos, Camacho, Peña, Maldonado, Navarro, Rosales, Acosta, Miranda, Trejo, Cabrera, Valencia, Nava, Castañeda, Pacheco, Robles, Molina, Rangel, Fuentes, Huerta, Meza, Aguirre, Cárdenas, Orozco, Padilla, Espinosa, Ayala, Salas, Valenzuela, Zúñiga, Ochoa, Salinas, Mora, Tapia, Serrano, Durán, Olvera, Macías, Zamora, Calderón, Arellano, Suárez, Barrera, Zavala, Villegas, Gallegos, Lozano, Galván, Figueroa, Beltrán, Franco, Villanueva, Sosa, Montes, Andrade, Velasco, Arias, Marín, Corona, Garza, Ponce, Esquivel, Pineda, Alonso, Palacios, Antonio, Vásquez, Trujillo, Cortez, Rocha, Rubio, Bernal, Benítez, Escobar, Villa, Galindo, Cuevas, Bravo, Cano, Osorio, Mata, Carmona, Montoya, de Jesús, Enríquez, Cisneros, Rivas, Parra, Reséndiz, Téllez, Zárate, Salgado, de la Rosa, Vera, Tovar, Arroyo, Córdova, Leyva, Quintero, Becerra, Quiroz, Barajas, Ávalos, Peralta, Román, Esparza, Murillo, Guevara, Olivares, Félix, de León, Castellanos, Villarreal, Villalobos, Lugo, Ángeles, Montiel, Segura, Magaña, Saucedo, Gallardo, Mercado, Navarrete, Reyna, Paredes, Dávila, Leal, Guerra, Saldaña, Guillén, Santana, Uribe, Monroy, Piña, Yáñez, Nieto, Islas, Granados, Escobedo, Zapata, Caballero, del Ángel, Solano, Barron, Zepeda, Acevedo, Arriaga, Barrios, Mondragón, Galicia, Godínez, Ojeda, Duarte, Alfaro, Medrano, Rico, Aguilera, Gil, Ventura, Balderas, Arredondo, Coronado, Escamilla, Nájera, Palma, Amador, Blanco, Ocampo, Garduño, Barragán, Gámez, Francisco, Meléndez, Carbajal, Hurtado, Carrasco, Bonilla, Correa, Sierra, Anaya, Carranza, Romo, Valdés, Armenta, Alcántara, Escalante, Arreola, Quezada, Alarcón, Gaytán, Rentería, Vidal, Báez, de los Santos, Toledo, Colín, May, Carrera, Jaramillo, Santillán, Valle, Varela, Arenas, Rendón, Treviño, Venegas, Soriano, Zaragoza, Morán, Áviles, Aranda, Lira, Quintana, Arteaga, Valadez, Cordero, Sotelo, de la Torre, Muñiz, Hidalgo, Cázares, Covarrubias, Zamudio, Ordoñez, Aparicio, Baltazar, Gálvez, Madrigal]
      prefix: [Sr., Sra., Srita., Dr., Ing.]
      suffix: [Jr., Sr., I, II, III, IV, V]
      title:
        descriptor: [Supervisor, Asociado, Ejecutivo, Relacciones, Oficial, Gerente, Ingeniero, Especialista, Director, Coordinador, Administrador, Arquitecto, Analista, Diseñador, Planificador, Técnico, Funcionario, Desarrollador, Productor, Consultor, Asistente, Facilitador, Agente, Representante, Estratega]
        level: [de, para]
        job: [Soluciones, Programa, Marca, Seguridad, Investigación, Marketing, Normas, Implementación, Integración, Funcionalidad, Respuesta, Paradigma, Tácticas, Identidad, Mercados, Grupo, División, Aplicaciones, Optimización, Operaciones, Infraestructura, Tecnologías de Información, Comunicaciones, Web, Calidad, Seguro, Mobilidad, Cuentas, Datos, Creativo, Configuración, Contabilidad, Interacciones, Factores, Usabilidad, Métricas, Departamento, Región, Supervisión, Planeación]
      name:
        - "#{prefix} #{first_name} #{last_name} #{last_name}"
        - "#{first_name} #{last_name} #{last_name}"
      name_with_middle:
        - "#{prefix} #{first_name} #{last_name} #{last_name}"
        - "#{first_name} #{last_name} #{last_name} #{suffix}"
        - "#{first_name} #{last_name} #{last_name}"
        - "#{first_name} #{last_name} #{last_name}"
        - "#{first_name} #{last_name} #{last_name}"
        - "#{first_name} #{last_name} #{last_name}"

    phone_number:
      lada_dos: ["33", "55", "81"]
      lada_tres: ["222", "223", "224", "225", "226", "227", "228", "229", "231", "232", "233", "235", "236", "237", "238", "241", "243", "244", "245", "246", "247", "248", "249", "271", "272", "273", "274", "275", "276", "278", "279", "281", "282", "283", "284", "285", "287", "288", "294", "296", "297", "311", "312", "313", "314", "315", "316", "317", "319", "321", "322", "323", "324", "325", "326", "327", "328", "329", "341", "342", "343", "344", "345", "346", "347", "348", "349", "351", "352", "353", "354", "355", "356", "357", "358", "359", "371", "372", "373", "374", "375", "376", "377", "378", "381", "382", "383", "384", "385", "386", "387", "388", "389", "391", "392", "393", "394", "395", "411", "412", "413", "414", "415", "417", "418", "419", "421", "422", "423", "424", "425", "426", "427", "428", "429", "431", "432", "433", "434", "435", "436", "437", "438", "441", "442", "443", "444", "445", "447", "448", "449", "451", "452", "453", "454", "455", "456", "457", "458", "459", "461", "462", "463", "464", "465", "466", "467", "468", "469", "471", "472", "473", "474", "475", "476", "477", "478", "481", "482", "483", "485", "486", "487", "488", "489", "492", "493", "494", "495", "496", "498", "499", "588", "591", "592", "593", "594", "595", "596", "597", "599", "612", "613", "614", "615", "616", "618", "621", "622", "623", "624", "625", "626", "627", "628", "629", "631", "632", "633", "634", "635", "636", "637", "638", "639", "641", "642", "643", "644", "645", "646", "647", "648", "649", "651", "652", "653", "656", "658", "659", "661", "662", "664", "665", "667", "668", "669", "671", "672", "673", "674", "675", "676", "677", "686", "687", "694", "695", "696", "697", "698", "711", "712", "713", "714", "715", "716", "717", "718", "719", "721", "722", "723", "724", "725", "726", "727", "728", "731", "732", "733", "734", "735", "736", "737", "738", "739", "741", "742", "743", "744", "745", "746", "747", "748", "749", "751", "753", "754", "755", "756", "757", "758", "759", "761", "762", "763", "764", "765", "766", "767", "768", "769", "771", "772", "773", "774", "775", "776", "777", "778", "779", "781", "782", "783", "784", "785", "786", "789", "791", "797", "821", "823", "824", "825", "826", "828", "829", "831", "832", "833", "834", "835", "836", "841", "842", "844", "845", "846", "861", "862", "864", "866", "867", "868", "869", "871", "872", "873", "877", "878", "891", "892", "894", "897", "899", "913", "914", "916", "917", "918", "919", "921", "922", "923", "924", "932", "933", "934", "936", "937", "938", "951", "953", "954", "958", "961", "962", "963", "964", "965", "966", "967", "968", "969", "971", "972", "981", "982", "983", "984", "985", "986", "987", "988", "991", "992", "993", "994", "995", "996", "997", "998", "999"]
      formats:
        - "#{PhoneNumber.lada_dos} #### ####"
        - "#{PhoneNumber.lada_tres} ### ####"
        - "(#{PhoneNumber.lada_dos}) #### ####"
        - "(#{PhoneNumber.lada_tres}) ### ####"
        - "#{PhoneNumber.lada_dos}-####-####"
        - "#{PhoneNumber.lada_tres}-###-####"
    cell_phone:
      formats:
        - "#{PhoneNumber.lada_dos} #### ####"
        - "#{PhoneNumber.lada_tres} ### ####"
        - "(#{PhoneNumber.lada_dos}) #### ####"
        - "(#{PhoneNumber.lada_tres}) ### ####"
        - "#{PhoneNumber.lada_dos}-####-####"
        - "#{PhoneNumber.lada_tres}-###-####"
        - "044 #{PhoneNumber.lada_dos} #### ####"
        - "044 #{PhoneNumber.lada_tres} ### ####"
        - "044 (#{PhoneNumber.lada_dos}) #### ####"
        - "044 (#{PhoneNumber.lada_tres}) ### ####"
        - "044 #{PhoneNumber.lada_dos}-####-####"
        - "044 #{PhoneNumber.lada_tres}-###-####"
    subscription:
      plans: ["Prueba gratuita", "Basico", "Starter", "Essential", "Estudiante", Bronze", "Standard", "Silver", "Gold", "Platinum", "Profesional", "Business", "Diamond", "Premium"]
      statuses: ["Activo", "Parado", "Bloqueado", "Pendiente"]
      payment_methods: ["Tarjeta de credito", "Tarjeta de débito", "Paypal", "Efectivo", "Transferencia de dinero", "Bitcoins", "Cheque", "Apple Pay", "Google Pay", "WeChat Pay", "Alipay", "Visa Checkout"]
      subscription_terms: ["Diaria", "Semanal", "Mensual", "Anual", "Bienal", "Trienal", "Quinquenal", "De por vida"]
      payment_terms: ["Pago por adelantado", "Mensual", "Anual", "Suscripción completa"]
    finance:
      vat_number:
        MX: /^([A-ZÑ]{3,4})([0-9]{2})(0[1-9]|1[0-2])(0[1-9]|1[0-9]|2[0-9]|3[0-1])[A-Z0-9]{3}$/
