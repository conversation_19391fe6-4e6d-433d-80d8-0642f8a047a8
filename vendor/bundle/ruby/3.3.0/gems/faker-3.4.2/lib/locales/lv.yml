lv:
  faker:
    address:
      postcode: ["LV-####"]
      building_number: ["####", "###", "##"]
      street_suffix: ["iela"]
      state: [Kurz<PERSON>e, Rīga, Latgale, Zemgale, Vidzeme]
      city_name: [<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>e, Baldonas, Baltinava, Bērzbeķe, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Biķernieki, <PERSON><PERSON>st<PERSON>, Bikstu Stac<PERSON>, Bitenieki, Brak<PERSON>, <PERSON><PERSON><PERSON>, Briģi, <PERSON><PERSON><PERSON><PERSON>, Burtnieki, Carnikava, Ce<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ervon<PERSON>, Cibla, Dalbe, Degumuiž<PERSON>, De<PERSON>e, Dienvid<PERSON>, Dim<PERSON>, Divezeri, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ērģeme, Ērgļi, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Līksna, Litene, Līvi, Lociki, Ļukas, Lūžņa, Maļinova, Mālpils, Mārupe, Mazbiksti, Mazirbe, Medumi, Melnsils, Mērsrags, Mežinieki, Mežotne, Miķeļtornis, Miltiņi, Mūrmuiža, Naudīte, Naukšēni, Nereta, Nīca, Nīcgale, Nītaure, Ņivņiki, Ozolnieki, Parūķis, Pasiene, Patkule, Penkule, Pilsrundāle, Piņķi, Pitrags, Priekuļi, Reģi, Riebiņi, Ropaži, Rosme, Rožupe Parish, Rucava, Rugāji, Sala, Saliena, Saunags, Saurieši, Sīkrags, Sīpele, Skaistkalne, Šķibe, Skrīveri, Skrudaliena, Skujaine, Slagūne, Smārde, Stalbe, Stapriņi, Straupe, Tabore, Tārgale, Tetele, Ulbroka, Upeslejas, Vabole, Vaide, Vaiņode, Vālodzes, Vandzene, Vecpiebalga, Vecumnieki, Vecvārkava, Višķi, Zaķumuiža, Zaļesje, Zālīte, Zaube, Zebrene, Zelmeņi, Ziedlejas, Zorģi]
      city:
        - "#{city_name}"
      street_name:
        - "#{Name.last_name} #{street_suffix}"
      street_address:
        - "#{Name.last_name} #{street_suffix} #{building_number}"

    company:
      suffix: [SIA, AS]
      # Buzzword wordlist from http://www.1728.com/buzzword.htm
      buzzwords:
        - ["Adaptive", "Advanced", "Ameliorated", "Assimilated", "Automated", "Balanced", "Business-focused", "Centralized", "Cloned", "Compatible", "Configurable", "Cross-group", "Cross-platform", "Customer-focused", "Customizable", "Decentralized", "De-engineered", "Devolved", "Digitized", "Distributed", "Diverse", "Down-sized", "Enhanced", "Enterprise-wide", "Ergonomic", "Exclusive", "Expanded", "Extended", "Face to face", "Focused", "Front-line", "Fully-configurable", "Function-based", "Fundamental", "Future-proofed", "Grass-roots", "Horizontal", "Implemented", "Innovative", "Integrated", "Intuitive", "Inverse", "Managed", "Mandatory", "Monitored", "Multi-channelled", "Multi-lateral", "Multi-layered", "Multi-tiered", "Networked", "Object-based", "Open-architected", "Open-source", "Operative", "Optimized", "Optional", "Organic", "Organized", "Persevering", "Persistent", "Phased", "Polarised", "Pre-emptive", "Proactive", "Profit-focused", "Profound", "Programmable", "Progressive", "Public-key", "Quality-focused", "Reactive", "Realigned", "Re-contextualized", "Re-engineered", "Reduced", "Reverse-engineered", "Right-sized", "Robust", "Seamless", "Secured", "Self-enabling", "Sharable", "Stand-alone", "Streamlined", "Switchable", "Synchronised", "Synergistic", "Synergized", "Team-oriented", "Total", "Triple-buffered", "Universal", "Up-sized", "Upgradable", "User-centric", "User-friendly", "Versatile", "Virtual", "Visionary", "Vision-oriented"]
        - ["24 hour", "24/7", "3rd generation", "4th generation", "5th generation", "6th generation", "actuating", "analyzing", "asymmetric", "asynchronous", "attitude-oriented", "background", "bandwidth-monitored", "bi-directional", "bifurcated", "bottom-line", "clear-thinking", "client-driven", "client-server", "coherent", "cohesive", "composite", "context-sensitive", "contextually-based", "content-based", "dedicated", "demand-driven", "didactic", "directional", "discrete", "disintermediate", "dynamic", "eco-centric", "empowering", "encompassing", "even-keeled", "executive", "explicit", "exuding", "fault-tolerant", "foreground", "fresh-thinking", "full-range", "global", "grid-enabled", "heuristic", "high-level", "holistic", "homogeneous", "human-resource", "hybrid", "impactful", "incremental", "intangible", "interactive", "intermediate", "leading edge", "local", "logistical", "maximized", "methodical", "mission-critical", "mobile", "modular", "motivating", "multimedia", "multi-state", "multi-tasking", "national", "needs-based", "neutral", "next generation", "non-volatile", "object-oriented", "optimal", "optimizing", "radical", "real-time", "reciprocal", "regional", "responsive", "scalable", "secondary", "solution-oriented", "stable", "static", "systematic", "systemic", "system-worthy", "tangible", "tertiary", "transitional", "uniform", "upward-trending", "user-facing", "value-added", "web-enabled", "well-modulated", "zero administration", "zero defect", "zero tolerance"]
        - ["ability", "access", "adapter", "algorithm", "alliance", "analyzer", "application", "approach", "architecture", "archive", "artificial intelligence", "array", "attitude", "benchmark", "budgetary management", "capability", "capacity", "challenge", "circuit", "collaboration", "complexity", "concept", "conglomeration", "contingency", "core", "customer loyalty", "database", "data-warehouse", "definition", "emulation", "encoding", "encryption", "extranet", "firmware", "flexibility", "focus group", "forecast", "frame", "framework", "function", "functionalities", "Graphic Interface", "groupware", "Graphical User Interface", "hardware", "help-desk", "hierarchy", "hub", "implementation", "info-mediaries", "infrastructure", "initiative", "installation", "instruction set", "interface", "internet solution", "intranet", "knowledge user", "knowledge base", "local area network", "leverage", "matrices", "matrix", "methodology", "middleware", "migration", "model", "moderator", "monitoring", "moratorium", "neural-net", "open architecture", "open system", "orchestration", "paradigm", "parallelism", "policy", "portal", "pricing structure", "process improvement", "product", "productivity", "project", "projection", "protocol", "secured line", "service-desk", "software", "solution", "standardization", "strategy", "structure", "success", "superstructure", "support", "synergy", "system engine", "task-force", "throughput", "time-frame", "toolset", "utilisation", "website", "workforce"]
      # BS wordlist from http://dack.com/web/bullshit.html
      bs:
        - ["implement", "utilize", "integrate", "streamline", "optimize", "evolve", "transform", "embrace", "enable", "orchestrate", "leverage", "reinvent", "aggregate", "architect", "enhance", "incentivize", "morph", "empower", "envisioneer", "monetize", "harness", "facilitate", "seize", "disintermediate", "synergize", "strategize", "deploy", "brand", "grow", "target", "syndicate", "synthesize", "deliver", "mesh", "incubate", "engage", "maximize", "benchmark", "expedite", "reintermediate", "whiteboard", "visualize", "repurpose", "innovate", "scale", "unleash", "drive", "extend", "engineer", "revolutionize", "generate", "exploit", "transition", "e-enable", "iterate", "cultivate", "matrix", "productize", "redefine", "recontextualize"]
        - ["clicks-and-mortar", "value-added", "vertical", "proactive", "robust", "revolutionary", "scalable", "leading-edge", "innovative", "intuitive", "strategic", "e-business", "mission-critical", "sticky", "one-to-one", "24/7", "end-to-end", "global", "B2B", "B2C", "granular", "frictionless", "virtual", "viral", "dynamic", "24/365", "best-of-breed", "killer", "magnetic", "bleeding-edge", "web-enabled", "interactive", "dot-com", "sexy", "back-end", "real-time", "efficient", "front-end", "distributed", "seamless", "extensible", "turn-key", "world-class", "open-source", "cross-platform", "cross-media", "synergistic", "bricks-and-clicks", "out-of-the-box", "enterprise", "integrated", "impactful", "wireless", "transparent", "next-generation", "cutting-edge", "user-centric", "visionary", "customized", "ubiquitous", "plug-and-play", "collaborative", "compelling", "holistic", "rich"]
        - ["synergies", "web-readiness", "paradigms", "markets", "partnerships", "infrastructures", "platforms", "initiatives", "channels", "eyeballs", "communities", "ROI", "solutions", "e-tailers", "e-services", "action-items", "portals", "niches", "technologies", "content", "vortals", "supply-chains", "convergence", "relationships", "architectures", "interfaces", "e-markets", "e-commerce", "systems", "bandwidth", "infomediaries", "models", "mindshare", "deliverables", "users", "schemas", "networks", "applications", "metrics", "e-business", "functionalities", "experiences", "web services", "methodologies"]
      name:
        - "#{Name.last_name} #{suffix}"
        - "#{suffix} #{Name.last_name}"

    internet:
      domain_suffix: [com, lv, net, org]

    name:
      first_name: [Agate, Agita, Agnese, Agra, Agrita, Aiga, Aija, Aina, Aira, Airita, Aiva, Aivita, Aleksandra, Aleksandrīna, Alina, Alise, Alla, Alma, Alvīna, Alvīne, Alīna, Amanda, Amālija, Anastasija, Ance, Anda, Andra, Andžela, Anete, Anita, Anna, Annija, Antonija, Antoņina, Antra, Anžela, Anželika, Arnita, Arta, Astra, Astrīda, Ausma, Austra, Baba, Baiba, Beatrise, Benita, Betija, Beāte, Biruta, Brigita, Broņislava, Dace, Dagmāra, Dagnija, Daiga, Daina, Dainuvīte, Dana, Daniela, Dina, Dita, Diāna, Doroteja, Dzidra, Dzintra, Dārta, Eda, Edīte, Egija, Egita, Eiženija, Elena, Eleonora, Elga, Elita, Elizabete, Elvīra, Elza, Elēna, Elīna, Elīza, Emma, Emīlija, Enija, Estere, Eva, Evelīna, Evija, Evita, Gaida, Gaļina, Genovefa, Ginta, Gita, Grieta, Guna, Gundega, Gunita, Gunta, Helēna, Ieva, Ilga, Ilona, Iluta, Ilva, Ilze, Ilzīte, Ina, Indra, Inesa, Inese, Ineta, Inga, Ingrīda, Inguna, Ingūna, Inita, Inna, Inta, Ināra, Irma, Irēna, Irīna, Iveta, Jadviga, Jana, Jeļena, Jolanta, Judīte, Justīne, Juta, Jūlija, Karina, Karlīna, Karolīna, Karīna, Katarīna, Katrīna, Keita, Kintija, Kitija, Klaudija, Krista, Kristina, Kristiāna, Kristīna, Kristīn1e, Ksenija, Laila, Laima, Laimdota, Larisa, Lauma, Laura, Lavīze, Lelde, Lidija, Liene, Lienīte, Liesma, Ligita, Lija, Lilija, Lilita, Linda, Liāna, Lolita, Ludmila, Luīze, Lāsma, Līga, Līna, Līva, Līvija, Lūcija, Madara, Maiga, Maija, Maira, Mairita, Margarita, Margita, Margrieta, Marija, Marika, Marina, Marita, Marta, Maruta, Megija, Milda, Mirdza, Modra, Modrīte, Monika, Monta, Mudīte, Māra, Mārīte, Adrians, Adriāns, Agnis, Agris, Aigars, Ainars, Ainis, Ainārs, Aivars, Aivis, Alberts, Aleksanders, Aleksandras, Aleksandrs, Aleksejs, Aleksis, Alfons, Alfrēds, Aloizs, Alvis, Anatolijs, Andis, Andrejs, Andris, Andžejs, Anrijs, Ansis, Antons, Arkādijs, Armands, Armīns, Arnis, Arnolds, Artis, Arturs, Artūrs, Arvis, Arvīds, Atis, Augusts, Austris, Boriss, Brencis, Bruno, Cefanja, Dagnis, Dainis, Daniels, Didzis, Dmitrijs, Dzintars, Dāvids, Dāvis, Džeimss, Džons, Džordžs, Edgars, Edijs, Edmunds, Eduards, Edvards, Edvīns, Egils, Egons, Einārs, Eižens, Elmārs, Elvijs, Elvis, Elviss, Elīza, Emīls, Endijs, Ernests, Ervīns, Felikss, Filips, Fjodors, Francis, Fricis, Fridrihs, Fēlikss, Fīlips, Gatis, Georgs, Gintars, Gintauts, Gints, Gothards, Gundars, Guntars, Guntis, Gunārs, Gustavs, Gusts, Gvido, Hanss, Haralds, Harijs, Helmuts, Henrihs, Henrijs, Henriks, Herberts, Hermanis, Ignats, Igors, Ilgonis, Ilgvars, Ilmārs, Ilvars, Imants, Indriķis, Indulis, Ingars, Ingus, Intars, Ints, Inārs, Ivans, Ivars, Ivo, Jans, Jevgeņijs, Johans, Jozua, Juris, Jurģis, Jānis, Jāzeps, Jēkabs, Jūlijs, Kaspars, Kazimirs, Kirils, Klāvs, Konstantīns, Kristaps, Kristers, Kristiāns, Krists, Krišjānis, Krišs, Kārlis, Laimdots, Laimonis, Lauris, Leonards, Leons, Leonīds, Linards, Ludis, Ludvigs, Lūks, Madars, Maigonis, Maikls, Mairis, Maksims, Mareks, Marks, Markuss, Matejs, Matīss, Miervaldis, Mihaels, Mihails, Miks, Mikus, Miķelis, Modris, Mozus, Mārcis, Māris, Mārtiņš, Nauris, Niklāvs, Nikolajs, Niks, Normunds, Ojārs, Olafs, Olivers, Olivjē, Oskars, Osvalds, Oto, Oļegs, Paulis, Pauls, Pjotrs, Pāvels, Pāvils, Pēteris, Raimonds, Raitis, Raivis, Raivo, Ralfs, Reinholds]
      last_name: [Āboliņš, Ābols, Alksnis, Annina, Antons, Arājs, Balodis, Bartulis, Baumanis, Bertāns, Bertholds, Bērziņš, Briedis, Dārziņš, Dukurs, Džeriņš, Eglītis, Ezergailis, Freimanis, Godmanis, Gulbis, Helmanis, Irbe, Jansons, Jaunzeme, Kalniņš, Krūmiņa, Krūmiņš, Kulda, Lācis, Liepa, Liepiņš, Matīss, Mezhlauk, Mucenieks, Muižnieks, Novickis, Ozoliņš, Ozols, Pelšs, Pētersons, Rozītis, Rubenis, Šics, Sirmais, Skrastiņš, Tīruma, Ulmanis, Vanags, Vasiļjevs, Vilks, Vītoliņš, Vītols, Zariņš]
      title:
        descriptor: [Lead, Senior, Direct, Corporate, Dynamic, Future, Product, National, Regional, District, Central, Global, Customer, Investor, Dynamic, International, Legacy, Forward, Internal, Human, Chief, Principal]
        level: [Solutions, Program, Brand, Security, Research, Marketing, Directives, Implementation, Integration, Functionality, Response, Paradigm, Tactics, Identity, Markets, Group, Division, Applications, Optimization, Operations, Infrastructure, Intranet, Communications, Web, Branding, Quality, Assurance, Mobility, Accounts, Data, Creative, Configuration, Accountability, Interactions, Factors, Usability, Metrics]
        job: [Supervisor, Associate, Executive, Liaison, Officer, Manager, Engineer, Specialist, Director, Coordinator, Administrator, Architect, Analyst, Designer, Planner, Orchestrator, Technician, Developer, Producer, Consultant, Assistant, Facilitator, Agent, Representative, Strategist]
      name:
        - "#{first_name} #{last_name}"
        - "#{first_name} #{last_name}"
        - "#{first_name} #{last_name}"
        - "#{first_name} #{last_name}"
        - "#{first_name} #{last_name}"
      name_with_middle:
        - "#{first_name} #{last_name} #{last_name}"

    phone_number:
      formats: ["5# ### ###", "6# ### ###", "7# ### ###"]
    cell_phone:
      formats: ["2# ### ###"]
