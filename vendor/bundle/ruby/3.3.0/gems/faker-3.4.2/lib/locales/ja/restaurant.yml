ja:
  faker:
    restaurant:
      name_prefix: ["ビッグ", "ゴールデン", "ハングリー", "シルバー", "レッド", "ブルー", "グリーン", "オレンジ", "イエロー"]
      name_suffix: ["ベーカリー", "バー", "焼肉", "ブラッスリー", "バーガー", "カフェ", "コーヒー", "ピッツア", "鉄板焼き", "鮨", "ラーメン", "居酒屋"]
      name:
        - "#{name_suffix} #{Name.last_name}"
        - "#{name_suffix} #{name_prefix}#{Name.last_name}"
        - "#{name_suffix} #{name_prefix}"
        - "#{type} #{Name.last_name}"
      type: ["懐石", "割烹", "精進料理", "京料理", "寿司", "ふぐ", "かに", "すっぽん", "あんこう", "天ぷら", "とんかつ", "串揚げ", "からあげ", "そば", "うどん", "うなぎ", "焼鳥", "すき焼き", "しゃぶしゃぶ", "おでん", "お好み焼き", "もんじゃ焼き", "たこ焼き", "牛丼", "ステーキ", "ハンバーグ", "鉄板焼き", "パスタ", "ピザ", "ハンバーガー", "洋食", "フレンチ", "ビストロ", "イタリアン", "中華料理", "韓国料理", "タイ料理", "メキシコ料理", "カレー", "焼肉", "居酒屋", "ダイニングバー", "ファミレス", "ラーメン", "カフェ", "喫茶店", "パン", "洋菓子", "和菓子", "バー"]
