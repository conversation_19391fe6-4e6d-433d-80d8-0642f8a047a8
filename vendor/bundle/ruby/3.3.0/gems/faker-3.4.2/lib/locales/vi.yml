# encoding: UTF-8
vi:
  faker:
    address:
      city_root: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>nh", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>",
                  "<PERSON><PERSON><PERSON>", "TP Hải Phòng", "<PERSON>ò<PERSON> B<PERSON>", "<PERSON><PERSON><PERSON>ê<PERSON>", "<PERSON> Châu", "<PERSON>à<PERSON> Cai", "<PERSON>ạng Sơn", "Nam Định",
                  "Ninh Bình", "<PERSON><PERSON> Thọ", "<PERSON><PERSON><PERSON><PERSON> Ninh", "Sơn La", "<PERSON>hái Bình", "<PERSON>h<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Vĩnh Phúc",
                  "<PERSON>ên Bá<PERSON>", "TP Đà Nẵng", "Bình Định", "Đắk Lắk", "Đắk Nông", "<PERSON><PERSON> Lai", "<PERSON><PERSON> Tĩnh", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>",
                  "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> B<PERSON>nh", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Thừa Thiên Huế",
                  "TP TP. Hồ <PERSON><PERSON>", "<PERSON> Giang", "<PERSON>à Rịa Vũng Tàu", "Bạc <PERSON>êu", "Bến Tre", "Bình Dương", "<PERSON><PERSON>nh <PERSON>ướ<PERSON>",
                  "Bình Thuận", "<PERSON>à Mau", "T<PERSON> <PERSON>ầ<PERSON> T<PERSON>ơ", "Đồ<PERSON> Na<PERSON>", "<PERSON>ồ<PERSON> Tháp", "<PERSON><PERSON><PERSON> Giang", "<PERSON><PERSON>n Giang", "Lâm Đồng",
                  "Long An", "Ninh Thuận", "Sóc Trăng", "Tây Ninh", "Tiền Giang", "Trà Vinh", "Vĩnh Long"]
      city:
        - "#{city_root}"
      postcode: /[A-PR-UWYZ0-9][A-HK-Y0-9][AEHMNPRTVXY0-9]?[ABEHMNPRVWXY0-9]? {1,2}[0-9][ABD-HJLN-UW-Z]{2}/
      county: ["Avon", "Bedfordshire", "Berkshire", "Borders", "Buckinghamshire", "Cambridgeshire", "Central", 
              "Cheshire", "Cleveland", "Clwyd", "Cornwall", "County Antrim", "County Armagh", "County Down",
              "County Fermanagh", "County Londonderry", "County Tyrone", "Cumbria", "Derbyshire", "Devon",
              "Dorset", "Dumfries and Galloway", "Durham", "Dyfed", "East Sussex", "Essex", "Fife", "Gloucestershire",
              "Grampian", "Greater Manchester", "Gwent", "Gwynedd County", "Hampshire", "Herefordshire", "Hertfordshire",
              "Highlands and Islands", "Humberside", "Isle of Wight", "Kent", "Lancashire", "Leicestershire", "Lincolnshire",
              "Lothian", "Merseyside", "Mid Glamorgan", "Norfolk", "North Yorkshire", "Northamptonshire", "Northumberland",
              "Nottinghamshire", "Oxfordshire", "Powys", "Rutland", "Shropshire", "Somerset", "South Glamorgan", "South Yorkshire",
              "Staffordshire", "Strathclyde", "Suffolk", "Surrey", "Tayside", "Tyne and Wear", "Việt Nam", "Warwickshire",
              "West Glamorgan", "West Midlands", "West Sussex", "West Yorkshire", "Wiltshire", "Worcestershire"]
      default_country: ["Việt Nam"]
      default_country_code: ["VN"]
    internet:
      domain_suffix: [com, net, info, vn, com.vn]
    phone_number:
      formats: ['01#### #####', '01### ######', '01#1 ### ####', '011# ### ####', '02# #### ####', '03## ### ####', '055 #### ####', '056 #### ####', '0800 ### ####', '08## ### ####', '09## ### ####', '016977 ####', '01### #####', '0500 ######', '0800 ######']
    cell_phone:
      formats: ['012# ### ####', '0162 ### ####', '0163 ### ####', '0164 ### ####', '0165 ### ####', '0166 ### ####',
                '0167 ### ####', '0168 ### ####', '0169 ### ####', '0186 ### ####', '0188 ### ####', '0199 ### ####',
                '0866 ### ###', '0868 ### ###', '0869 ### ###', '088# ### ###', '09## ### ###']
    name:
      first_name: ["Phạm", "Nguyễn", "Trần", "Lê", "Lý", "Hoàng", "Phan", "Vũ", "Tăng",
                   "Đặng", "Bùi", "Đỗ", "Hồ", "Ngô", "Dương", "Đào", "Đoàn", "Vương",
                   "Trịnh", "Đinh", "Lâm", "Phùng", "Mai", "Tô", "Trương", "Hà"]
      last_name: ["Nam", "Trung", "Thanh", "Thị", "Văn", "Dương", "Tăng", "Quốc", "Như", 
                 "Phạm", "Nguyễn", "Trần", "Lê", "Lý", "Hoàng", "Phan", "Vũ", "Tăng",
                 "Đặng", "Bùi", "Đỗ", "Hồ", "Ngô", "Dương", "Đào", "Đoàn", "Vương",
                 "Trịnh", "Đinh", "Lâm", "Phùng", "Mai", "Tô", "Trương", "Hà",
                 "Vinh", "Nhung", "Hòa", "Tiến", "Tâm", "Bửu", "Loan", "Hiền", "Hải",
                 "Vân", "Kha", "Minh", "Nhân", "Triệu", "Tuân", "Hữu", "Đức", "Phú", "Khoa",
                 "Thắng", "Sơn", "Dung", "Tú", "Trinh", "Thảo", "Sa", "Kim", "Long", "Thi",
                 "Cường", "Ngọc", "Sinh", "Khang", "Phong", "Thắm", "Thu", "Thủy", "Nhàn"]
      name:
        - "#{first_name} #{last_name}"
        - "#{first_name} #{last_name} #{last_name}"
        - "#{first_name} #{last_name} #{last_name} #{last_name}"
      name_with_middle:
        - "#{first_name} #{last_name} #{last_name}"

    company:
      prefix: ["Công ty", "Cty TNHH", "Cty", "Cửa hàng", "Trung tâm", "Chi nhánh"]
      name:
        - "#{prefix} #{Name.last_name}"
    lorem:
      words: ["đã", "đang", "ừ", "ờ", "á", "không", "biết", "gì", "hết", "đâu", "nha",
              "thế", "thì", "là", "đánh", "đá", "đập", "phá", "viết", "vẽ", "tô", "thuê",
              "mướn", "mượn", "mua", "một", "hai", "ba", "bốn", "năm", "sáu", "bảy", "tám",
              "chín", "mười", "thôi", "việc", "nghỉ", "làm", "nhà", "cửa", "xe", "đạp", "ác",
              "độc", "khoảng", "khoan", "thuyền", "tàu", "bè", "lầu", "xanh", "đỏ", "tím", "vàng",
              "kim", "chỉ", "khâu", "may", "vá", "em", "anh", "yêu", "thương", "thích", "con", "cái",
              "bàn", "ghế", "tủ", "quần", "áo", "nón", "dép", "giày", "lỗi", "được", "ghét", "giết", 
              "chết", "hết", "tôi", "bạn", "tui", "trời", "trăng", "mây", "gió", "máy", "hàng", "hóa",
              "leo", "núi", "bơi", "biển", "chìm", "xuồng", "nước", "ngọt", "ruộng", "đồng", "quê", "hương"]
              
