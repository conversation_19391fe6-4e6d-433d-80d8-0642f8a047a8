en-SG:
  faker:
    name:
      male_first_name: &1
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON><PERSON>-<PERSON><PERSON>
        - <PERSON>
        - <PERSON><PERSON>
        - <PERSON><PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON><PERSON>n
        - <PERSON>
        - <PERSON>
        - <PERSON><PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON><PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON><PERSON>
        - <PERSON><PERSON>
        - <PERSON><PERSON>
        - <PERSON><PERSON>
        - <PERSON><PERSON>
        - <PERSON><PERSON>
        - <PERSON>
        - <PERSON>w <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON><PERSON>
        - <PERSON><PERSON>
        - <PERSON>
        - Wing <PERSON>u
        - E<PERSON>
        - Yu <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON><PERSON>
        - <PERSON><PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON><PERSON>
        - <PERSON>
        - Wee <PERSON>
        - Wee <PERSON>
        - Yu <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON><PERSON>
        - <PERSON><PERSON>
        - <PERSON><PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON><PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON><PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON><PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON><PERSON>
        - <PERSON>
        - <PERSON><PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON><PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON><PERSON>
        - <PERSON>
        - <PERSON>a Woei
        - <PERSON> Heng
        - Shao <PERSON>uan
      last_name:
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - Ong
        - <PERSON>
        - <PERSON>h
        - <PERSON>a
        - <PERSON>
        - <PERSON>h
        - Teo
        - <PERSON>
        - <PERSON>o
        - <PERSON>y
        - <PERSON>
        - <PERSON>
        - Toh
        - Sim
        - Chong
        - <PERSON>a
        - Fong
        - <PERSON>g
        - <PERSON>
        - Ou
        - Li
        - <PERSON>h
        - <PERSON>an
        - <PERSON>i
        - Sim
        - Choo
        - Goy
        - <PERSON>ua
        - Thio
        - <PERSON>
        - <PERSON>
        - <PERSON>hoo
        - Wee
        - <PERSON>k
        - <PERSON>
        - Soh
        - <PERSON>
        - Liew
        - Ko
        - Oh
        - Peh
        - Lam
        - Au
        - Seah
        - Boey
        - Lau
        - Pang
        - Lye
        - Quah
        - Yong
        - Lui
        - Lum
        - Seow
        - Loh
        - Chew
        - Mok
        - Lew
        - Chee
        - Loo
        - Gn
        - Tang
        - Yap
        - Wan
        - Yee
        - Yip
        - Tey
        - Ow
        - Liu
        - Tham
        - See
        - Woo
        - Heng
        - Leow
        - Chen
        - Foo
        - Poh
      female_first_name:
        - Xiu Yi
        - Wai Teng
        - Sing Yee
        - Jing Yi
        - Jia Yee
        - Jia Xuan
        - Shu En
        - Pei Ying
        - Pei Yu
        - Pih Foung
        - Li-ann
        - Shi Xuan
        - Yi Xuan
        - Shu En
        - Yi Xin
        - Hui Juan
        - Yu En
        - Yihui
        - Xin Yi
        - Yun Xuan
        - Xuan Xuan
        - Cheuk Ying
        - Shiqi
        - Yujin
        - Wee Xin
        - Jing Xuan
        - Huishan
        - Yi Ting
        - Wei Xuan
        - Shi Ning
        - Zi Shan
        - Jing Ning
        - Lee Shyin
        - Yi Ning
        - Enyi
        - Siying
        - Ruitong
        - Rui Xuan
        - Siyun
        - Xi Xuan
        - Shuwei
        - Jie Ying
        - Hui Jie
        - Xuan Na
        - Sze Han
        - Rou'en
        - Wei Xuan
        - Kaiyi
        - An Xuan
        - Enxuan
        - Yu Xuan
        - Qi Qi
        - Yutong
        - Jia En
        - Chee En
        - Ruining
        - Lee Ying
        - Yu Qi
        - Ke Xuan
        - Teo Xinyu
        - Xin Yee
        - Xuan Ling
        - Zhi Yi
        - Yan Tong
        - En Qi
        - Yi Ting
        - Yanling
        - Sining
        - Yixuan
        - Zu'er
        - Ke Xuan
        - Ying Le
        - Qinyi
        - Li Min
        - Yi Ling
        - Xu Tong
        - Ser Shyann
        - Teng Ee
        - Miao Yun
        - Yng Qi
        - Xuan Yi
        - Yi Shan
        - Rui Tian
        - Ruishan
        - Jia Xuan
        - Kai Le
        - Le Xuan
        - Yu Tong
        - Kai Qi
        - Xuan Rong
        - Wen Xin
        - Si Xuan
        - Ying Xin
        - Tong En
        - Xinhui
        - Qingyi
        - En Hui
        - Yunwen
        - Zi Xuan
        - Kai En
        - Ann Ting
        - Yu En
        - Yu Xin
        - Ting Loh
        - Jia Yi
        - Min Wen
        - Jia Jia
        - Ke Xin
        - Yuxuan
        - Xin Ling
        - Lizi
        - Tschi-xuan
        - Yu Chen
        - Yi Lea
        - Ziyu
        - Tay Ting
        - Yingbi
        - See-yi
        - Fang'en
        - Chze Xuan
        - Xue Ying
        - Wenyan
        - Zi Yuan
        - Bei'en
        - Yuxi
        - Rei En
        - Yitong
        - Kaiting
        - Jing Xuan
        - Shu Wen
        - Wenxuan
        - Hui Xuan
        - Wan Ying
        - Rui-han
        - Weining
        - Jia'en
        - Hann-yi
        - Cze En
        - Zhiyu
        - Yen Yee
        - Ling Xuan
        - Si Ying
      male_english_name:
        - Leon
        - Bryan
        - Jack
        - Stephen
        - Andy
        - Jerome
        - Ian
        - Desmond
        - Lucas
        - Morgan
        - Keith
        - Ivan
        - Gavin
        - Winson
        - Raynor
        - Ryan
        - Kenson
        - Benjamin
        - Benny
        - Eugene
        - Melvin
        - Shawn
        - Aaron
        - Justin
        - Emmanuel
        - Steven
        - Joshua
        - Terence
        - Darren
        - Daniel
        - Aloysius
        - John
        - Jeremy
        - Wilson
        - Dave
        - Vincent
        - Ryan
        - Sebastian
        - Edward
        - Daryl
        - Eddy
        - William
        - Jason
        - Nicholas
        - Brian
        - Sean
        - Calvin
        - Russell
        - Raphael
        - Kenneth
        - Angus
        - James
        - Dennis
        - Mark
        - Jedd
        - Sherman
        - Marvin
        - Edmund
        - Henry
        - Kevin
        - Vernon
        - Benedict
        - Brendan
        - Gilbert
        - Josh
        - Jay
        - Winston
        - Nicholas
        - Eric
        - Daren
        - Nelson
        - Xavier
        - Glen
        - Gabriel
        - Matthew
        - Tristan
      female_english_name:
        - Alicia
        - Caitlin
        - Denise
        - Emerald
        - Erin
        - Jocelyn
        - Levene
        - Rosaline
        - Victoria
        - Amy
        - Angelyn
        - Chloe
        - Erin
        - Isabel
        - Jolene
        - Natalyn
        - Rachael
        - Rishi
        - Valerie
        - Anastasia
        - Andrea
        - Carina
        - Celeste
        - Flo
        - Janessa
        - Joeunn
        - Mabel
        - Riya
        - Samantha
        - Tricia
        - Aurelia
        - Chanel
        - Colette
        - Fynn
        - Gwyneth
        - Josephine
        - Keisha
        - Rachael
        - Sarah
        - Sharlene
        - Val
        - Charlotte
        - Chloe
        - Danielle
        - Gabrielle
        - Glory
        - Isabel
        - Kyra
        - Marilyn
        - Raine
        - Sophie
        - Beatrice
        - Cassia
        - Cheralyn
        - Christy
        - Dilys
        - Glynis
        - Isabelle
        - Megan
        - Shannen
        - Tisha
        - Tricia
        - Victoria
        - Bethley
        - Catherine
        - Claire
        - Clarissa
        - Eleanor
        - Isabelle
        - Megan
        - Mikayla
        - Renee
        - Steffi
        - Vera
        - Zoe
        - Alanna
        - Alyssa
        - Angeline
        - Anya
        - Ciara
        - Clare
        - Isabella
        - Jeanette
        - Kaelyn
        - Kate
        - Megan
        - Nieve
        - Shannel
        - Valerie
        - Anastasia
        - Ariel
        - Gwenn
        - Janine
        - Kara
        - Kate
        - Katelyn
        - Natalie
        - Natally
        - Samantha
        - Shannon
        - Tiffany
        - Arielle
        - Ashley
        - Claire
        - Jovi
        - Kimi
        - Vil
        - Alicia
        - Caroline
        - Chanell
        - Elizabeth
        - Heidi
        - Megan
        - Nericcia
        - Sharmaine
        - Amelia
        - Caitlyn
        - Elisha
        - Rachel
        - Rannel
        - Rianne
        - Andrea
        - Celeste
        - Chantelle
        - Emma
        - Heidi
        - Joey
        - Khloe
        - Maegin
        - Mayenne
        - Regina
        - Anna
        - Cherie
        - Christie
        - Janelle
        - Jenell
        - Johannah
        - Leah
        - Marissa
        - Arissa
        - Evangelyn
        - Faith
        - Phobe
        - Rebecca
        - Regina
        - Cindy
        - Karen
        - Jess
      first_name: *1
      name:
        - "#{last_name} #{male_first_name}"
        - "#{male_english_name} #{last_name} #{male_first_name}"
        - "#{last_name} #{female_first_name}"
        - "#{female_english_name} #{last_name} #{female_first_name}"
      name_with_middle:
        - "#{first_name} #{last_name} #{last_name}"
    address:
      postcode:
        - "######"
      building_number:
        - "#"
        - "##"
        - "###"
      streets:
        - Tampines
        - Hougang
        - Sims
        - Bukit Timah
        - Jurong West
        - Teck Whye
        - Choa Chu Kang North
        - Woodlands
        - Sembawang
        - Ah Soo
        - Paya Lebar
        - Serangoon
        - Lor Lew Lian
        - Woodlands
        - Geyland
        - Clementi
        - Bukit Merah
        - Tanglin
      street_name:
        - "#{streets} St #{building_number}"
        - "#{streets} Ave #{building_number}"
        - "#{streets} Road"
      street_address:
        - "#{building_number} #{street_name}"
      default_country:
        - Singapore
      default_country_code:
        - SG
    phone_number:
      country_code:
        - '65'
      formats:
        - '6### ####'
        - '9### ####'
        - '8### ####'
    vehicle:
      license_plate: S??####
