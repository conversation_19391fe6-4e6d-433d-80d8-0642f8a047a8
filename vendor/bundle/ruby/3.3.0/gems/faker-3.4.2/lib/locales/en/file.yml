en:
  faker:
    file:
      extension: ["flac", "mp3", "wav", "bmp", "gif", "jpeg", "jpg", "png", "tiff", "css", "csv", "html", "js", "json", "txt", "mp4", "avi", "mov", "webm", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "odt", "ods", "odp", "pages", "numbers", "key", "pdf"]
      mime_type:
        application: ["application/atom+xml", "application/ecmascript", "application/EDI-X12", "application/EDIFACT", "application/json", "application/javascript", "application/ogg", "application/pdf", "application/postscript", "application/rdf+xml", "application/rss+xml", "application/soap+xml", "application/font-woff", "application/xhtml+xml", "application/xml-dtd", "application/xop+xml", "application/zip", "application/gzip"]
        audio: ["audio/basic", "audio/L24", "audio/mp4", "audio/mpeg", "audio/ogg", "audio/vorbis", "audio/vnd.rn-realaudio", "audio/vnd.wave", "audio/webm"]
        image: ["image/gif", "image/jpeg", "image/pjpeg", "image/png", "image/svg+xml", "image/tiff", "image/vnd.microsoft.icon"]
        message: ["message/http", "message/imdn+xml", "message/partial", "message/rfc822"]
        model: ["model/example", "model/iges", "model/mesh", "model/vrml", "model/x3d+binary", "model/x3d+vrml", "model/x3d+xml"]
        multipart: ["multipart/mixed", "multipart/alternative", "multipart/related", "multipart/form-data", "multipart/signed", "multipart/encrypted"]
        text: ["text/cmd", "text/css", "text/csv", "text/html", "text/javascript", "text/plain", "text/vcard", "text/xml"]
        video: ["video/mpeg", "video/mp4", "video/ogg", "video/quicktime", "video/webm", "video/x-matroska", "video/x-ms-wmv", "video/x-flv"]
