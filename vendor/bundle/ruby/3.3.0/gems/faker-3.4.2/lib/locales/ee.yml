ee:
  faker:
    invoice:
      reference:
        # '#' means the place where check digit is placed
        # Source: http://www.pangaliit.ee/en/settlements-and-standards/reference-number-of-the-invoice
        check_digit_method: method_731
        pattern: '\d{1,19}#'
    address:
      postcode: ["#####"]
      building_number: ["####", "###", "##"]
      street_suffix: ["tee", "maantee", "tänav", "põik"]
      state: [Harjumaa, Tartumaa, Ida-Virumaa, Pärnumaa, Lääne-Virumaa, Viljandimaa, Raplamaa, Võrumaa, Saaremaa, Jõgevamaa, Järvamaa, Valgamaa, Põlvamaa, Läänemaa, Hiiumaa]
      city_name: [Tallinn, Tartu, Narva, Pärnu, Kohtla-Järve, Viljandi, Rakvere, Maardu, Sillamäe, Kuressaare, Valga, Võru, Haapsalu, J<PERSON><PERSON>, Ke<PERSON>, P<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, K<PERSON>, Põltsama<PERSON>, O<PERSON>ää, Ki<PERSON>i<PERSON>, Sin<PERSON>, Paldisk<PERSON>, <PERSON>sal<PERSON>, <PERSON>la, <PERSON>rksi-Nuia, Kunda, Püssi, Narva-J<PERSON>esuu, T<PERSON>rva, Loksa, Lihula, Ab<PERSON>-Palu<PERSON>, Võhma, Mustvee, <PERSON>llaste, Mõisaküla]
      city:
        - "#{city_name}"
      street_name:
        - "#{Name.last_name} #{street_suffix}"
      street_address:
        - "#{Name.last_name} #{street_suffix} #{building_number}"

    company:
      suffix: [AS, OÜ]
      # Buzzword wordlist from http://www.1728.com/buzzword.htm
      buzzwords:
        - ["Adaptive", "Advanced", "Ameliorated", "Assimilated", "Automated", "Balanced", "Business-focused", "Centralized", "Cloned", "Compatible", "Configurable", "Cross-group", "Cross-platform", "Customer-focused", "Customizable", "Decentralized", "De-engineered", "Devolved", "Digitized", "Distributed", "Diverse", "Down-sized", "Enhanced", "Enterprise-wide", "Ergonomic", "Exclusive", "Expanded", "Extended", "Face to face", "Focused", "Front-line", "Fully-configurable", "Function-based", "Fundamental", "Future-proofed", "Grass-roots", "Horizontal", "Implemented", "Innovative", "Integrated", "Intuitive", "Inverse", "Managed", "Mandatory", "Monitored", "Multi-channelled", "Multi-lateral", "Multi-layered", "Multi-tiered", "Networked", "Object-based", "Open-architected", "Open-source", "Operative", "Optimized", "Optional", "Organic", "Organized", "Persevering", "Persistent", "Phased", "Polarised", "Pre-emptive", "Proactive", "Profit-focused", "Profound", "Programmable", "Progressive", "Public-key", "Quality-focused", "Reactive", "Realigned", "Re-contextualized", "Re-engineered", "Reduced", "Reverse-engineered", "Right-sized", "Robust", "Seamless", "Secured", "Self-enabling", "Sharable", "Stand-alone", "Streamlined", "Switchable", "Synchronised", "Synergistic", "Synergized", "Team-oriented", "Total", "Triple-buffered", "Universal", "Up-sized", "Upgradable", "User-centric", "User-friendly", "Versatile", "Virtual", "Visionary", "Vision-oriented"]
        - ["24 hour", "24/7", "3rd generation", "4th generation", "5th generation", "6th generation", "actuating", "analyzing", "asymmetric", "asynchronous", "attitude-oriented", "background", "bandwidth-monitored", "bi-directional", "bifurcated", "bottom-line", "clear-thinking", "client-driven", "client-server", "coherent", "cohesive", "composite", "context-sensitive", "contextually-based", "content-based", "dedicated", "demand-driven", "didactic", "directional", "discrete", "disintermediate", "dynamic", "eco-centric", "empowering", "encompassing", "even-keeled", "executive", "explicit", "exuding", "fault-tolerant", "foreground", "fresh-thinking", "full-range", "global", "grid-enabled", "heuristic", "high-level", "holistic", "homogeneous", "human-resource", "hybrid", "impactful", "incremental", "intangible", "interactive", "intermediate", "leading edge", "local", "logistical", "maximized", "methodical", "mission-critical", "mobile", "modular", "motivating", "multimedia", "multi-state", "multi-tasking", "national", "needs-based", "neutral", "next generation", "non-volatile", "object-oriented", "optimal", "optimizing", "radical", "real-time", "reciprocal", "regional", "responsive", "scalable", "secondary", "solution-oriented", "stable", "static", "systematic", "systemic", "system-worthy", "tangible", "tertiary", "transitional", "uniform", "upward-trending", "user-facing", "value-added", "web-enabled", "well-modulated", "zero administration", "zero defect", "zero tolerance"]
        - ["ability", "access", "adapter", "algorithm", "alliance", "analyzer", "application", "approach", "architecture", "archive", "artificial intelligence", "array", "attitude", "benchmark", "budgetary management", "capability", "capacity", "challenge", "circuit", "collaboration", "complexity", "concept", "conglomeration", "contingency", "core", "customer loyalty", "database", "data-warehouse", "definition", "emulation", "encoding", "encryption", "extranet", "firmware", "flexibility", "focus group", "forecast", "frame", "framework", "function", "functionalities", "Graphic Interface", "groupware", "Graphical User Interface", "hardware", "help-desk", "hierarchy", "hub", "implementation", "info-mediaries", "infrastructure", "initiative", "installation", "instruction set", "interface", "internet solution", "intranet", "knowledge user", "knowledge base", "local area network", "leverage", "matrices", "matrix", "methodology", "middleware", "migration", "model", "moderator", "monitoring", "moratorium", "neural-net", "open architecture", "open system", "orchestration", "paradigm", "parallelism", "policy", "portal", "pricing structure", "process improvement", "product", "productivity", "project", "projection", "protocol", "secured line", "service-desk", "software", "solution", "standardization", "strategy", "structure", "success", "superstructure", "support", "synergy", "system engine", "task-force", "throughput", "time-frame", "toolset", "utilisation", "website", "workforce"]
      # BS wordlist from http://dack.com/web/bullshit.html
      bs:
        - ["implement", "utilize", "integrate", "streamline", "optimize", "evolve", "transform", "embrace", "enable", "orchestrate", "leverage", "reinvent", "aggregate", "architect", "enhance", "incentivize", "morph", "empower", "envisioneer", "monetize", "harness", "facilitate", "seize", "disintermediate", "synergize", "strategize", "deploy", "brand", "grow", "target", "syndicate", "synthesize", "deliver", "mesh", "incubate", "engage", "maximize", "benchmark", "expedite", "reintermediate", "whiteboard", "visualize", "repurpose", "innovate", "scale", "unleash", "drive", "extend", "engineer", "revolutionize", "generate", "exploit", "transition", "e-enable", "iterate", "cultivate", "matrix", "productize", "redefine", "recontextualize"]
        - ["clicks-and-mortar", "value-added", "vertical", "proactive", "robust", "revolutionary", "scalable", "leading-edge", "innovative", "intuitive", "strategic", "e-business", "mission-critical", "sticky", "one-to-one", "24/7", "end-to-end", "global", "B2B", "B2C", "granular", "frictionless", "virtual", "viral", "dynamic", "24/365", "best-of-breed", "killer", "magnetic", "bleeding-edge", "web-enabled", "interactive", "dot-com", "sexy", "back-end", "real-time", "efficient", "front-end", "distributed", "seamless", "extensible", "turn-key", "world-class", "open-source", "cross-platform", "cross-media", "synergistic", "bricks-and-clicks", "out-of-the-box", "enterprise", "integrated", "impactful", "wireless", "transparent", "next-generation", "cutting-edge", "user-centric", "visionary", "customized", "ubiquitous", "plug-and-play", "collaborative", "compelling", "holistic", "rich"]
        - ["synergies", "web-readiness", "paradigms", "markets", "partnerships", "infrastructures", "platforms", "initiatives", "channels", "eyeballs", "communities", "ROI", "solutions", "e-tailers", "e-services", "action-items", "portals", "niches", "technologies", "content", "vortals", "supply-chains", "convergence", "relationships", "architectures", "interfaces", "e-markets", "e-commerce", "systems", "bandwidth", "infomediaries", "models", "mindshare", "deliverables", "users", "schemas", "networks", "applications", "metrics", "e-business", "functionalities", "experiences", "web services", "methodologies"]
      name:
        - "#{Name.last_name} #{suffix}"
        - "#{suffix} #{Name.last_name}"

    internet:
      domain_suffix: [com, com.ee, ee, net, org]

    name:
      first_name: [Aabel, Aabraham, Aadu, Aare, Aarne, Aaron, Aavo, Ago, Agu, Ahti, Ahto, Aigar, Ain, Aivar, Aivo, Aksel, Alar, Alari, Albert, Aleks, Aleksander, Allan, Allar, Alo, Alvar, Ando, Andre, Andreas, Andres, Andrus, Anti, Anton, Ants, Ardi, Ardo, Arno, Arnold, Arti, Artur, Arved, Arvi, Arvo, Asko, August, Edgar, Eduard, Eedo, Eedu, Eerik, Eero, Egert, Egon, Einar, Eino, Elias, Elmar, Elmo, Endel, Endrik, Enn, Enno, Ergo, Erik, Erki, Erko, Erlend, Ermo, Evald, Evert, Feliks, Georg, Gert, Gunnar, Gustav, Hannes, Hans, Hardi, Harri, Heigo, Heiki, Heino, Heiti, Hendrik, Henn, Henri, Hillar, Iisak, Illar, Illimar, Ilmar, Ilmo, Imre, Indrek, Innar, Ivar, Ivari, Ivo, Jaagup, Jaak, Jaakob, Jaan, Jaanus, Jakob, Janar, Janek, Janno, Jass, Joel, Johannes, Joona, Joonas, Joonatan, Joosep, Juhan, Juho, Jukk, Juss, Jürgen, Jüri, Jürjo, Kaarel, Kaido, Kajar, Kalev, Kaljo, Kalju, Kalle, Kardo, Karl, Kaspar, Kaupo, Kaur, Kauri, Ken, Kermo, Kert, Kevin, Koit, Kristen, Kristian, Kristjan, Kristo, Kristofer, Kuldar, Kunnar, Kusta, Kustas, Kustav, Küllo, Laur, Lauri, Leevi, Leho, Lembit, Lennart, Luukas, Maanus, Maarjo, Madi, Madis, Magnus, Maido, Mairo, Mait, Marek, Margo, Margus, Mario, Mark, Marko, Markus, Marten, Marti, Martin, Mati, Matteus, Mattias, Meelis, Meeme, Mehis, Mihkel, Miikael, Mikk, Märt, Neeme, Nigul, Nigulas, Niilo, Nuut, Olari, Olavi, Olev, Oliver, Oskar, Ott, Otto, Paavo, Paul, Paulus, Peep, Aada, Aasa, Aet, Age, Agnes, Aile, Aili, Aino, Ainu, Aire, Airi, Aita, Aive, Aleksandra, Alina, Andra, Anete, Angela, Angelika, Ann, Anna, Anne, Anneli, Anni, Annika, Anu, Asta, Astrid, Auli, Aune, Aurelia, Ave, Berit, Birgit, Brita, Dagmar, Diana, Ebe, Eda, Eeva, Eevi, Egle, Eha, Eili, Eliisa, Eliisabet, Eliise, Elina, Elis, Elle, Ellen, Elo, Elvi, Emma, Endla, Ene, Eneli, Epp, Ere, Erika, Esta, Ester, Eva, Eve, Evelin, Gerli, Greete, Grete, Hanna, Hedi, Heidi, Heili, Helbe, Hele, Helen, Helena, Heleri, Helga, Helgi, Heli, Helina, Helju, Helle, Helmi, Helve, Hiie, Hilda, Hilja, Hille, Iiris, Ille, Ilme, Ilona, Imbi, Ines, Inga, Inge, Ingel, Ingrid, Inna, Irene, Iris, Irja, Irma, Ivi, Ivika, Jaana, Jaanika, Jana, Jane, Janne, Johanna, Juta, Kaarin, Kadi, Kadri, Kai, Kaia, Kaidi, Kaie, Kaili, Kairi, Kaisa, Kaja, Karin, Karina, Karmen, Karoliina, Karoliine, Karolin, Katariina, Kati, Katre, Katri, Katriin, Katrin, Keiu, Kelli, Kerli, Kersti, Kerstin, Kertu, Kirke, Koidu, Krista, Kristel, Kristen, Kristi, Kristiina, Kristin, Kristina, Kärt, Kätlin, Külli, Küllike, Lagle, Laine, Laura, Lea, Leelo, Lehte, Leida, Leili, Lembi, Lemme, Lii, Liia, Liidia, Liilia, Liina, Liis, Liisa, Liisbet, Liisi, Liivi, Lilian, Lilli, Linda, Luule, Lüüdia, Maarika, Maarja, Made, Madli, Mai, Maia, Maie, Maila, Maimu, Maire, Malle, Mare, Maret, Margareeta, Margaret, Marge, Margit, Margot, Mari, Mari-Liis]
      last_name: [Aare, Aasmäe, Aavik, Allik, Alver, Antson, Eenpalu, Eskola, Härma, Hõbe, Ilves, Ivask, Jänes, Järvekülg, Jääger, Jõgi, Kaasik, Kaljurand, Kangur, Kasak, Kask, Kesküla, Kivi, Koppel, Korjus, Kross, Kukk, Kuuse, Kuusik, Kuusk, Käbin, Kärner, Kõiv, Kütt, Laar, Laur, Leok, Lepik, Levandi, Linna, Luik, Lõhmus, Lõoke, Made, Mark, Masing, Meri, Mädamürk, Mägi, Männik, Naissoo, Nurme, Nurmsalu, Nõmmik, Olesk, Orav, Poska, Puhvel, Pärn, Pääsuke, Raag, Raud, Rebane, Reinsalu, Rootare, Rüütli, Saar, Salumäe, Savisaar, Sepp, Sisask, Soosaar, Taaramae, Tamm, Tammik, Toome, Tätte, Vaher, Vaino, Valdma, Valk, Vesik, Viiding, Vitsut, Välbe, Väljas, Västrik, Võsu, Wahl, Õunapuu, Öpik]
      title:
        descriptor: [Lead, Senior, Direct, Corporate, Dynamic, Future, Product, National, Regional, District, Central, Global, Customer, Investor, Dynamic, International, Legacy, Forward, Internal, Human, Chief, Principal]
        level: [Solutions, Program, Brand, Security, Research, Marketing, Directives, Implementation, Integration, Functionality, Response, Paradigm, Tactics, Identity, Markets, Group, Division, Applications, Optimization, Operations, Infrastructure, Intranet, Communications, Web, Branding, Quality, Assurance, Mobility, Accounts, Data, Creative, Configuration, Accountability, Interactions, Factors, Usability, Metrics]
        job: [Supervisor, Associate, Executive, Liaison, Officer, Manager, Engineer, Specialist, Director, Coordinator, Administrator, Architect, Analyst, Designer, Planner, Orchestrator, Technician, Developer, Producer, Consultant, Assistant, Facilitator, Agent, Representative, Strategist]
      name:
        - "#{first_name} #{last_name}"
        - "#{first_name} #{last_name}"
        - "#{first_name} #{last_name}"
        - "#{first_name} #{last_name}"
        - "#{first_name} #{last_name}"
      name_with_middle:
        - "#{first_name} #{last_name} #{last_name}"

    phone_number:
      formats: ["32# ####", "33# ####", "35# ####", "38# ####", "39# ####", "43# ####", "44# ####", "45# ####", "46# ####", "47# ####", "48# ####", "61# ####", "62# ####", "63# ####", "64# ####", "65# ####", "66# ####", "67# ####", "68# ####", "69# ####", "7## ####", "71# ####", "72# ####", "73# ####", "74# ####", "75# ####", "76# ####", "77# ####", "78# ####", "79# ####",  "88# ####"]
    cell_phone:
      formats: ["5### ####", "5## ####", "81## ####", "82## ####", "83## ####", "84## ####"]
