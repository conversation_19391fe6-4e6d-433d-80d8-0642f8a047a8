en-au-ocker:
  faker:
    name:
      first_name:
        - <PERSON><PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - Sofia
        - Ella
        - <PERSON>
        - <PERSON>
        - Sienna
        - Mia+3
        - Grace
        - Emma
        - Ivy
        - Layla
        - Abigail
        - <PERSON>la
        - <PERSON>
        - <PERSON><PERSON>
        - Lucy
        - <PERSON><PERSON>
        - <PERSON>
        - <PERSON>
        - Alice
        - Georgia
        - Maya
        - <PERSON>
        - Audrey
        - <PERSON>
        - <PERSON>
        - Chelsea
        - Mila
        - Holly
        - Indiana
        - <PERSON><PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON><PERSON>
        - Jasmine
        - Imogen
        - <PERSON><PERSON>
        - Pheobe
        - Eva
        - <PERSON>
        - <PERSON>
        - <PERSON><PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - Lachlan
        - Noah
        - Liam
        - Alexander
        - Max
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - Oscar
        - Benjamin
        - Aiden
        - Mason
        - Samuel
        - <PERSON>
        - Levi
        - Riley
        - Harrison
        - Ryan
        - Henry
        - Jacob
        - Joshua
        - Leo
        - Zach
        - Harry
        - Hunter
        - Flynn
        - Archie
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - Blake
        - Archer
        - Ashton
        - Sebastian
        - Zachery
        - Lincoln
        - Mitchell
        - Luca
        - Nathan
        - Kai
        - Connor
        - Tom
        - Nigel
        - Matt
        - Sean
      last_name:
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - White
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - Nguyen
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - Lee
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>
        - <PERSON>ch
        - <PERSON>
        - <PERSON>
        - <PERSON><PERSON><PERSON>ne
      ocker_first_name:
        - <PERSON><PERSON>
        - <PERSON><PERSON>
        - <PERSON><PERSON>
        - <PERSON><PERSON>
        - <PERSON><PERSON>
        - <PERSON>ha<PERSON>
        - <PERSON>azza
        - Charl
        - Darl
    company:
      suffix:
        - Pty Ltd
        - and Sons
        - Corp
        - Group
        - Brothers
        - Partners
    internet:
      domain_suffix:
        - com.au
        - com
        - net.au
        - net
        - org.au
        - org
    address:
      street_root:
        - Ramsay Street
        - Bonnie Doon
        - Cavill Avenue
        - Queen Street
      street_name:
        - "#{street_root}"
      city_prefix:
        - Bondi
        - Burleigh Heads
        - Carlton
        - Fitzroy
        - Fremantle
        - Glenelg
        - Manly
        - Noosa
        - Stones Corner
        - St Kilda
        - Surry Hills
        - Yarra Valley
      city:
        - "#{city_prefix}"
      state_abbr:
        - NSW
        - QLD
        - NT
        - SA
        - WA
        - TAS
        - ACT
        - VIC
      region:
        - South East Queensland
        - Wide Bay Burnett
        - Margaret River
        - Port Pirie
        - Gippsland
        - Elizabeth
        - Barossa
      state:
        - New South Wales
        - Queensland
        - Northern Territory
        - South Australia
        - Western Australia
        - Tasmania
        - Australian Capital Territory
        - Victoria
      postcode:
        - 0###
        - 2###
        - 3###
        - 4###
        - 5###
        - 6###
        - 7###
      building_number:
        - "####"
        - "###"
        - "##"
      street_suffix:
        - Avenue
        - Boulevard
        - Circle
        - Circuit
        - Court
        - Crescent
        - Crest
        - Drive
        - Estate Dr
        - Grove
        - Hill
        - Island
        - Junction
        - Knoll
        - Lane
        - Loop
        - Mall
        - Manor
        - Meadow
        - Mews
        - Parade
        - Parkway
        - Pass
        - Place
        - Plaza
        - Ridge
        - Road
        - Run
        - Square
        - Station St
        - Street
        - Summit
        - Terrace
        - Track
        - Trail
        - View Rd
        - Way
      default_country:
        - Australia
      default_country_code:
        - AU
    phone_number:
      country_code:
        - '61'
      formats:
        - '0# #### ####'
        - "# #### ####"
        - '4## ### ###'
    cell_phone:
      formats:
        - 04##-###-###
        - "(0) 4##-###-###"
        - '04## ### ###'
        - 04########
        - '04## ## ## ##'
