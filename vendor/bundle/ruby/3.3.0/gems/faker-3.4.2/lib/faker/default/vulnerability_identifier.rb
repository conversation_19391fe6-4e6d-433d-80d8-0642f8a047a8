# frozen_string_literal: true

module Faker
  class VulnerabilityIdentifier < Base
    class << self
      ##
      # Produces a Common Vulnerabilities and Exposures (CVE) identifier.
      #
      # @param year [Integer] The year-part of the CVE identifier (defaults to the current year)
      # @return [String]
      #
      # @example
      #   Faker::VulnerabilityIdentifier.cve #=> "CVE-2021-1337"
      #   Faker::VulnerabilityIdentifier.cve(year: 1999) #=> "CVE-1999-0523"
      #
      # @faker.version next
      def cve(year: ::Date.today.year)
        index = rand_in_range(1, 99_999).to_s.rjust(4, '0')
        "CVE-#{year}-#{index}"
      end
    end
  end
end
