# frozen_string_literal: true

module Faker
  class TvShows
    class <PERSON><PERSON><PERSON> < Base
      class << self
        ##
        # Produces a quote from <PERSON>.
        #
        # @return [String]
        #
        # @example
        #   Faker::TvShows::<PERSON><PERSON><PERSON>.quote
        #     #=> "I am <PERSON>, always."
        #
        # @faker.version 1.9.0
        def quote
          fetch('michael_scott.quotes')
        end
      end
    end
  end
end
