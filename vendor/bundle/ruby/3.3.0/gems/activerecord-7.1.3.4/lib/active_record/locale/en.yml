en:
  # Attributes names common to most models
  #attributes:
    #created_at: "Created at"
    #updated_at: "Updated at"

  # Default error messages
  errors:
    messages:
      required: "must exist"
      taken: "has already been taken"

  # Active Record models configuration
  activerecord:
    errors:
      messages:
        record_invalid: "Validation failed: %{errors}"
        restrict_dependent_destroy:
          has_one: "Cannot delete record because a dependent %{record} exists"
          has_many: "Cannot delete record because dependent %{record} exist"
        # Append your own errors here or at the model/attributes scope.

      # You can define own errors for models or model attributes.
      # The values :model, :attribute and :value are always available for interpolation.
      #
      # For example,
      #   models:
      #     user:
      #       blank: "This is a custom blank message for %{model}: %{attribute}"
      #       attributes:
      #         login:
      #           blank: "This is a custom blank message for User login"
      # Will define custom blank validation message for User model and
      # custom blank validation message for login attribute of User model.
      #models:

    # Translate model names. Used in Model.human_name().
    #models:
      # For example,
      #   user: "Dude"
      # will translate User model name to "Dude"

    # Translate model attribute names. Used in Model.human_attribute_name(attribute).
    #attributes:
      # For example,
      #   user:
      #     login: "Handle"
      # will translate User attribute "login" as "Handle"
