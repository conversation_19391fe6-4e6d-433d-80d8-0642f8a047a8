.gemtest
BSDL
Contributors.rdoc
History.rdoc
LICENSE
Manifest.txt
POSTGRES
README-OS_X.rdoc
README-Windows.rdoc
README.ja.rdoc
README.rdoc
Rakefile
Rakefile.cross
ext/errorcodes.def
ext/errorcodes.rb
ext/errorcodes.txt
ext/extconf.rb
ext/gvl_wrappers.c
ext/gvl_wrappers.h
ext/pg.c
ext/pg.h
ext/pg_binary_decoder.c
ext/pg_binary_encoder.c
ext/pg_coder.c
ext/pg_connection.c
ext/pg_copy_coder.c
ext/pg_errors.c
ext/pg_record_coder.c
ext/pg_result.c
ext/pg_text_decoder.c
ext/pg_text_encoder.c
ext/pg_tuple.c
ext/pg_type_map.c
ext/pg_type_map_all_strings.c
ext/pg_type_map_by_class.c
ext/pg_type_map_by_column.c
ext/pg_type_map_by_mri_type.c
ext/pg_type_map_by_oid.c
ext/pg_type_map_in_ruby.c
ext/pg_util.c
ext/pg_util.h
ext/vc/pg.sln
ext/vc/pg_18/pg.vcproj
ext/vc/pg_19/pg_19.vcproj
lib/pg.rb
lib/pg/basic_type_mapping.rb
lib/pg/binary_decoder.rb
lib/pg/coder.rb
lib/pg/connection.rb
lib/pg/constants.rb
lib/pg/exceptions.rb
lib/pg/result.rb
lib/pg/text_decoder.rb
lib/pg/text_encoder.rb
lib/pg/tuple.rb
lib/pg/type_map_by_column.rb
spec/data/expected_trace.out
spec/data/random_binary_data
spec/helpers.rb
spec/pg/basic_type_mapping_spec.rb
spec/pg/connection_spec.rb
spec/pg/connection_sync_spec.rb
spec/pg/result_spec.rb
spec/pg/tuple_spec.rb
spec/pg/type_map_by_class_spec.rb
spec/pg/type_map_by_column_spec.rb
spec/pg/type_map_by_mri_type_spec.rb
spec/pg/type_map_by_oid_spec.rb
spec/pg/type_map_in_ruby_spec.rb
spec/pg/type_map_spec.rb
spec/pg/type_spec.rb
spec/pg_spec.rb
