/*
 * ext/errorcodes.def - Definition of error classes.
 *
 * WARNING: This file is autogenerated. Please edit ext/errorcodes.rb !
 *
 */


{
  VALUE klass = define_error_class( "SqlStatementNotYetComplete", NULL );
  register_error_class( "03000", klass );
  register_error_class( "03", klass );
}
{
  VALUE klass = define_error_class( "ConnectionException", NULL );
  register_error_class( "08000", klass );
  register_error_class( "08", klass );
}
{
  VALUE klass = define_error_class( "ConnectionDoesNotExist", "08" );
  register_error_class( "08003", klass );
}
{
  VALUE klass = define_error_class( "ConnectionFailure", "08" );
  register_error_class( "08006", klass );
}
{
  VALUE klass = define_error_class( "SqlclientUnableToEstablishSqlconnection", "08" );
  register_error_class( "08001", klass );
}
{
  VALUE klass = define_error_class( "SqlserverRejectedEstablishmentOfSqlconnection", "08" );
  register_error_class( "08004", klass );
}
{
  VALUE klass = define_error_class( "TransactionResolutionUnknown", "08" );
  register_error_class( "08007", klass );
}
{
  VALUE klass = define_error_class( "ProtocolViolation", "08" );
  register_error_class( "08P01", klass );
}
{
  VALUE klass = define_error_class( "TriggeredActionException", NULL );
  register_error_class( "09000", klass );
  register_error_class( "09", klass );
}
{
  VALUE klass = define_error_class( "FeatureNotSupported", NULL );
  register_error_class( "0A000", klass );
  register_error_class( "0A", klass );
}
{
  VALUE klass = define_error_class( "InvalidTransactionInitiation", NULL );
  register_error_class( "0B000", klass );
  register_error_class( "0B", klass );
}
{
  VALUE klass = define_error_class( "LocatorException", NULL );
  register_error_class( "0F000", klass );
  register_error_class( "0F", klass );
}
{
  VALUE klass = define_error_class( "LEInvalidSpecification", "0F" );
  register_error_class( "0F001", klass );
}
{
  VALUE klass = define_error_class( "InvalidGrantor", NULL );
  register_error_class( "0L000", klass );
  register_error_class( "0L", klass );
}
{
  VALUE klass = define_error_class( "InvalidGrantOperation", "0L" );
  register_error_class( "0LP01", klass );
}
{
  VALUE klass = define_error_class( "InvalidRoleSpecification", NULL );
  register_error_class( "0P000", klass );
  register_error_class( "0P", klass );
}
{
  VALUE klass = define_error_class( "DiagnosticsException", NULL );
  register_error_class( "0Z000", klass );
  register_error_class( "0Z", klass );
}
{
  VALUE klass = define_error_class( "StackedDiagnosticsAccessedWithoutActiveHandler", "0Z" );
  register_error_class( "0Z002", klass );
}
{
  VALUE klass = define_error_class( "CaseNotFound", NULL );
  register_error_class( "20000", klass );
  register_error_class( "20", klass );
}
{
  VALUE klass = define_error_class( "CardinalityViolation", NULL );
  register_error_class( "21000", klass );
  register_error_class( "21", klass );
}
{
  VALUE klass = define_error_class( "DataException", NULL );
  register_error_class( "22000", klass );
  register_error_class( "22", klass );
}
{
  VALUE klass = define_error_class( "ArraySubscriptError", "22" );
  register_error_class( "2202E", klass );
}
{
  VALUE klass = define_error_class( "CharacterNotInRepertoire", "22" );
  register_error_class( "22021", klass );
}
{
  VALUE klass = define_error_class( "DatetimeFieldOverflow", "22" );
  register_error_class( "22008", klass );
}
{
  VALUE klass = define_error_class( "DivisionByZero", "22" );
  register_error_class( "22012", klass );
}
{
  VALUE klass = define_error_class( "ErrorInAssignment", "22" );
  register_error_class( "22005", klass );
}
{
  VALUE klass = define_error_class( "EscapeCharacterConflict", "22" );
  register_error_class( "2200B", klass );
}
{
  VALUE klass = define_error_class( "IndicatorOverflow", "22" );
  register_error_class( "22022", klass );
}
{
  VALUE klass = define_error_class( "IntervalFieldOverflow", "22" );
  register_error_class( "22015", klass );
}
{
  VALUE klass = define_error_class( "InvalidArgumentForLog", "22" );
  register_error_class( "2201E", klass );
}
{
  VALUE klass = define_error_class( "InvalidArgumentForNtile", "22" );
  register_error_class( "22014", klass );
}
{
  VALUE klass = define_error_class( "InvalidArgumentForNthValue", "22" );
  register_error_class( "22016", klass );
}
{
  VALUE klass = define_error_class( "InvalidArgumentForPowerFunction", "22" );
  register_error_class( "2201F", klass );
}
{
  VALUE klass = define_error_class( "InvalidArgumentForWidthBucketFunction", "22" );
  register_error_class( "2201G", klass );
}
{
  VALUE klass = define_error_class( "InvalidCharacterValueForCast", "22" );
  register_error_class( "22018", klass );
}
{
  VALUE klass = define_error_class( "InvalidDatetimeFormat", "22" );
  register_error_class( "22007", klass );
}
{
  VALUE klass = define_error_class( "InvalidEscapeCharacter", "22" );
  register_error_class( "22019", klass );
}
{
  VALUE klass = define_error_class( "InvalidEscapeOctet", "22" );
  register_error_class( "2200D", klass );
}
{
  VALUE klass = define_error_class( "InvalidEscapeSequence", "22" );
  register_error_class( "22025", klass );
}
{
  VALUE klass = define_error_class( "NonstandardUseOfEscapeCharacter", "22" );
  register_error_class( "22P06", klass );
}
{
  VALUE klass = define_error_class( "InvalidIndicatorParameterValue", "22" );
  register_error_class( "22010", klass );
}
{
  VALUE klass = define_error_class( "InvalidParameterValue", "22" );
  register_error_class( "22023", klass );
}
{
  VALUE klass = define_error_class( "InvalidPrecedingOrFollowingSize", "22" );
  register_error_class( "22013", klass );
}
{
  VALUE klass = define_error_class( "InvalidRegularExpression", "22" );
  register_error_class( "2201B", klass );
}
{
  VALUE klass = define_error_class( "InvalidRowCountInLimitClause", "22" );
  register_error_class( "2201W", klass );
}
{
  VALUE klass = define_error_class( "InvalidRowCountInResultOffsetClause", "22" );
  register_error_class( "2201X", klass );
}
{
  VALUE klass = define_error_class( "InvalidTablesampleArgument", "22" );
  register_error_class( "2202H", klass );
}
{
  VALUE klass = define_error_class( "InvalidTablesampleRepeat", "22" );
  register_error_class( "2202G", klass );
}
{
  VALUE klass = define_error_class( "InvalidTimeZoneDisplacementValue", "22" );
  register_error_class( "22009", klass );
}
{
  VALUE klass = define_error_class( "InvalidUseOfEscapeCharacter", "22" );
  register_error_class( "2200C", klass );
}
{
  VALUE klass = define_error_class( "MostSpecificTypeMismatch", "22" );
  register_error_class( "2200G", klass );
}
{
  VALUE klass = define_error_class( "NullValueNotAllowed", "22" );
  register_error_class( "22004", klass );
}
{
  VALUE klass = define_error_class( "NullValueNoIndicatorParameter", "22" );
  register_error_class( "22002", klass );
}
{
  VALUE klass = define_error_class( "NumericValueOutOfRange", "22" );
  register_error_class( "22003", klass );
}
{
  VALUE klass = define_error_class( "SequenceGeneratorLimitExceeded", "22" );
  register_error_class( "2200H", klass );
}
{
  VALUE klass = define_error_class( "StringDataLengthMismatch", "22" );
  register_error_class( "22026", klass );
}
{
  VALUE klass = define_error_class( "StringDataRightTruncation", "22" );
  register_error_class( "22001", klass );
}
{
  VALUE klass = define_error_class( "SubstringError", "22" );
  register_error_class( "22011", klass );
}
{
  VALUE klass = define_error_class( "TrimError", "22" );
  register_error_class( "22027", klass );
}
{
  VALUE klass = define_error_class( "UnterminatedCString", "22" );
  register_error_class( "22024", klass );
}
{
  VALUE klass = define_error_class( "ZeroLengthCharacterString", "22" );
  register_error_class( "2200F", klass );
}
{
  VALUE klass = define_error_class( "FloatingPointException", "22" );
  register_error_class( "22P01", klass );
}
{
  VALUE klass = define_error_class( "InvalidTextRepresentation", "22" );
  register_error_class( "22P02", klass );
}
{
  VALUE klass = define_error_class( "InvalidBinaryRepresentation", "22" );
  register_error_class( "22P03", klass );
}
{
  VALUE klass = define_error_class( "BadCopyFileFormat", "22" );
  register_error_class( "22P04", klass );
}
{
  VALUE klass = define_error_class( "UntranslatableCharacter", "22" );
  register_error_class( "22P05", klass );
}
{
  VALUE klass = define_error_class( "NotAnXmlDocument", "22" );
  register_error_class( "2200L", klass );
}
{
  VALUE klass = define_error_class( "InvalidXmlDocument", "22" );
  register_error_class( "2200M", klass );
}
{
  VALUE klass = define_error_class( "InvalidXmlContent", "22" );
  register_error_class( "2200N", klass );
}
{
  VALUE klass = define_error_class( "InvalidXmlComment", "22" );
  register_error_class( "2200S", klass );
}
{
  VALUE klass = define_error_class( "InvalidXmlProcessingInstruction", "22" );
  register_error_class( "2200T", klass );
}
{
  VALUE klass = define_error_class( "DuplicateJsonObjectKeyValue", "22" );
  register_error_class( "22030", klass );
}
{
  VALUE klass = define_error_class( "InvalidArgumentForSqlJsonDatetimeFunction", "22" );
  register_error_class( "22031", klass );
}
{
  VALUE klass = define_error_class( "InvalidJsonText", "22" );
  register_error_class( "22032", klass );
}
{
  VALUE klass = define_error_class( "InvalidSqlJsonSubscript", "22" );
  register_error_class( "22033", klass );
}
{
  VALUE klass = define_error_class( "MoreThanOneSqlJsonItem", "22" );
  register_error_class( "22034", klass );
}
{
  VALUE klass = define_error_class( "NoSqlJsonItem", "22" );
  register_error_class( "22035", klass );
}
{
  VALUE klass = define_error_class( "NonNumericSqlJsonItem", "22" );
  register_error_class( "22036", klass );
}
{
  VALUE klass = define_error_class( "NonUniqueKeysInAJsonObject", "22" );
  register_error_class( "22037", klass );
}
{
  VALUE klass = define_error_class( "SingletonSqlJsonItemRequired", "22" );
  register_error_class( "22038", klass );
}
{
  VALUE klass = define_error_class( "SqlJsonArrayNotFound", "22" );
  register_error_class( "22039", klass );
}
{
  VALUE klass = define_error_class( "SqlJsonMemberNotFound", "22" );
  register_error_class( "2203A", klass );
}
{
  VALUE klass = define_error_class( "SqlJsonNumberNotFound", "22" );
  register_error_class( "2203B", klass );
}
{
  VALUE klass = define_error_class( "SqlJsonObjectNotFound", "22" );
  register_error_class( "2203C", klass );
}
{
  VALUE klass = define_error_class( "TooManyJsonArrayElements", "22" );
  register_error_class( "2203D", klass );
}
{
  VALUE klass = define_error_class( "TooManyJsonObjectMembers", "22" );
  register_error_class( "2203E", klass );
}
{
  VALUE klass = define_error_class( "SqlJsonScalarRequired", "22" );
  register_error_class( "2203F", klass );
}
{
  VALUE klass = define_error_class( "SqlJsonItemCannotBeCastToTargetType", "22" );
  register_error_class( "2203G", klass );
}
{
  VALUE klass = define_error_class( "IntegrityConstraintViolation", NULL );
  register_error_class( "23000", klass );
  register_error_class( "23", klass );
}
{
  VALUE klass = define_error_class( "RestrictViolation", "23" );
  register_error_class( "23001", klass );
}
{
  VALUE klass = define_error_class( "NotNullViolation", "23" );
  register_error_class( "23502", klass );
}
{
  VALUE klass = define_error_class( "ForeignKeyViolation", "23" );
  register_error_class( "23503", klass );
}
{
  VALUE klass = define_error_class( "UniqueViolation", "23" );
  register_error_class( "23505", klass );
}
{
  VALUE klass = define_error_class( "CheckViolation", "23" );
  register_error_class( "23514", klass );
}
{
  VALUE klass = define_error_class( "ExclusionViolation", "23" );
  register_error_class( "23P01", klass );
}
{
  VALUE klass = define_error_class( "InvalidCursorState", NULL );
  register_error_class( "24000", klass );
  register_error_class( "24", klass );
}
{
  VALUE klass = define_error_class( "InvalidTransactionState", NULL );
  register_error_class( "25000", klass );
  register_error_class( "25", klass );
}
{
  VALUE klass = define_error_class( "ActiveSqlTransaction", "25" );
  register_error_class( "25001", klass );
}
{
  VALUE klass = define_error_class( "BranchTransactionAlreadyActive", "25" );
  register_error_class( "25002", klass );
}
{
  VALUE klass = define_error_class( "HeldCursorRequiresSameIsolationLevel", "25" );
  register_error_class( "25008", klass );
}
{
  VALUE klass = define_error_class( "InappropriateAccessModeForBranchTransaction", "25" );
  register_error_class( "25003", klass );
}
{
  VALUE klass = define_error_class( "InappropriateIsolationLevelForBranchTransaction", "25" );
  register_error_class( "25004", klass );
}
{
  VALUE klass = define_error_class( "NoActiveSqlTransactionForBranchTransaction", "25" );
  register_error_class( "25005", klass );
}
{
  VALUE klass = define_error_class( "ReadOnlySqlTransaction", "25" );
  register_error_class( "25006", klass );
}
{
  VALUE klass = define_error_class( "SchemaAndDataStatementMixingNotSupported", "25" );
  register_error_class( "25007", klass );
}
{
  VALUE klass = define_error_class( "NoActiveSqlTransaction", "25" );
  register_error_class( "25P01", klass );
}
{
  VALUE klass = define_error_class( "InFailedSqlTransaction", "25" );
  register_error_class( "25P02", klass );
}
{
  VALUE klass = define_error_class( "IdleInTransactionSessionTimeout", "25" );
  register_error_class( "25P03", klass );
}
{
  VALUE klass = define_error_class( "InvalidSqlStatementName", NULL );
  register_error_class( "26000", klass );
  register_error_class( "26", klass );
}
{
  VALUE klass = define_error_class( "TriggeredDataChangeViolation", NULL );
  register_error_class( "27000", klass );
  register_error_class( "27", klass );
}
{
  VALUE klass = define_error_class( "InvalidAuthorizationSpecification", NULL );
  register_error_class( "28000", klass );
  register_error_class( "28", klass );
}
{
  VALUE klass = define_error_class( "InvalidPassword", "28" );
  register_error_class( "28P01", klass );
}
{
  VALUE klass = define_error_class( "DependentPrivilegeDescriptorsStillExist", NULL );
  register_error_class( "2B000", klass );
  register_error_class( "2B", klass );
}
{
  VALUE klass = define_error_class( "DependentObjectsStillExist", "2B" );
  register_error_class( "2BP01", klass );
}
{
  VALUE klass = define_error_class( "InvalidTransactionTermination", NULL );
  register_error_class( "2D000", klass );
  register_error_class( "2D", klass );
}
{
  VALUE klass = define_error_class( "SqlRoutineException", NULL );
  register_error_class( "2F000", klass );
  register_error_class( "2F", klass );
}
{
  VALUE klass = define_error_class( "SREFunctionExecutedNoReturnStatement", "2F" );
  register_error_class( "2F005", klass );
}
{
  VALUE klass = define_error_class( "SREModifyingSqlDataNotPermitted", "2F" );
  register_error_class( "2F002", klass );
}
{
  VALUE klass = define_error_class( "SREProhibitedSqlStatementAttempted", "2F" );
  register_error_class( "2F003", klass );
}
{
  VALUE klass = define_error_class( "SREReadingSqlDataNotPermitted", "2F" );
  register_error_class( "2F004", klass );
}
{
  VALUE klass = define_error_class( "InvalidCursorName", NULL );
  register_error_class( "34000", klass );
  register_error_class( "34", klass );
}
{
  VALUE klass = define_error_class( "ExternalRoutineException", NULL );
  register_error_class( "38000", klass );
  register_error_class( "38", klass );
}
{
  VALUE klass = define_error_class( "EREContainingSqlNotPermitted", "38" );
  register_error_class( "38001", klass );
}
{
  VALUE klass = define_error_class( "EREModifyingSqlDataNotPermitted", "38" );
  register_error_class( "38002", klass );
}
{
  VALUE klass = define_error_class( "EREProhibitedSqlStatementAttempted", "38" );
  register_error_class( "38003", klass );
}
{
  VALUE klass = define_error_class( "EREReadingSqlDataNotPermitted", "38" );
  register_error_class( "38004", klass );
}
{
  VALUE klass = define_error_class( "ExternalRoutineInvocationException", NULL );
  register_error_class( "39000", klass );
  register_error_class( "39", klass );
}
{
  VALUE klass = define_error_class( "ERIEInvalidSqlstateReturned", "39" );
  register_error_class( "39001", klass );
}
{
  VALUE klass = define_error_class( "ERIENullValueNotAllowed", "39" );
  register_error_class( "39004", klass );
}
{
  VALUE klass = define_error_class( "ERIETriggerProtocolViolated", "39" );
  register_error_class( "39P01", klass );
}
{
  VALUE klass = define_error_class( "ERIESrfProtocolViolated", "39" );
  register_error_class( "39P02", klass );
}
{
  VALUE klass = define_error_class( "ERIEEventTriggerProtocolViolated", "39" );
  register_error_class( "39P03", klass );
}
{
  VALUE klass = define_error_class( "SavepointException", NULL );
  register_error_class( "3B000", klass );
  register_error_class( "3B", klass );
}
{
  VALUE klass = define_error_class( "SEInvalidSpecification", "3B" );
  register_error_class( "3B001", klass );
}
{
  VALUE klass = define_error_class( "InvalidCatalogName", NULL );
  register_error_class( "3D000", klass );
  register_error_class( "3D", klass );
}
{
  VALUE klass = define_error_class( "InvalidSchemaName", NULL );
  register_error_class( "3F000", klass );
  register_error_class( "3F", klass );
}
{
  VALUE klass = define_error_class( "TransactionRollback", NULL );
  register_error_class( "40000", klass );
  register_error_class( "40", klass );
}
{
  VALUE klass = define_error_class( "TRIntegrityConstraintViolation", "40" );
  register_error_class( "40002", klass );
}
{
  VALUE klass = define_error_class( "TRSerializationFailure", "40" );
  register_error_class( "40001", klass );
}
{
  VALUE klass = define_error_class( "TRStatementCompletionUnknown", "40" );
  register_error_class( "40003", klass );
}
{
  VALUE klass = define_error_class( "TRDeadlockDetected", "40" );
  register_error_class( "40P01", klass );
}
{
  VALUE klass = define_error_class( "SyntaxErrorOrAccessRuleViolation", NULL );
  register_error_class( "42000", klass );
  register_error_class( "42", klass );
}
{
  VALUE klass = define_error_class( "SyntaxError", "42" );
  register_error_class( "42601", klass );
}
{
  VALUE klass = define_error_class( "InsufficientPrivilege", "42" );
  register_error_class( "42501", klass );
}
{
  VALUE klass = define_error_class( "CannotCoerce", "42" );
  register_error_class( "42846", klass );
}
{
  VALUE klass = define_error_class( "GroupingError", "42" );
  register_error_class( "42803", klass );
}
{
  VALUE klass = define_error_class( "WindowingError", "42" );
  register_error_class( "42P20", klass );
}
{
  VALUE klass = define_error_class( "InvalidRecursion", "42" );
  register_error_class( "42P19", klass );
}
{
  VALUE klass = define_error_class( "InvalidForeignKey", "42" );
  register_error_class( "42830", klass );
}
{
  VALUE klass = define_error_class( "InvalidName", "42" );
  register_error_class( "42602", klass );
}
{
  VALUE klass = define_error_class( "NameTooLong", "42" );
  register_error_class( "42622", klass );
}
{
  VALUE klass = define_error_class( "ReservedName", "42" );
  register_error_class( "42939", klass );
}
{
  VALUE klass = define_error_class( "DatatypeMismatch", "42" );
  register_error_class( "42804", klass );
}
{
  VALUE klass = define_error_class( "IndeterminateDatatype", "42" );
  register_error_class( "42P18", klass );
}
{
  VALUE klass = define_error_class( "CollationMismatch", "42" );
  register_error_class( "42P21", klass );
}
{
  VALUE klass = define_error_class( "IndeterminateCollation", "42" );
  register_error_class( "42P22", klass );
}
{
  VALUE klass = define_error_class( "WrongObjectType", "42" );
  register_error_class( "42809", klass );
}
{
  VALUE klass = define_error_class( "GeneratedAlways", "42" );
  register_error_class( "428C9", klass );
}
{
  VALUE klass = define_error_class( "UndefinedColumn", "42" );
  register_error_class( "42703", klass );
}
{
  VALUE klass = define_error_class( "UndefinedFunction", "42" );
  register_error_class( "42883", klass );
}
{
  VALUE klass = define_error_class( "UndefinedTable", "42" );
  register_error_class( "42P01", klass );
}
{
  VALUE klass = define_error_class( "UndefinedParameter", "42" );
  register_error_class( "42P02", klass );
}
{
  VALUE klass = define_error_class( "UndefinedObject", "42" );
  register_error_class( "42704", klass );
}
{
  VALUE klass = define_error_class( "DuplicateColumn", "42" );
  register_error_class( "42701", klass );
}
{
  VALUE klass = define_error_class( "DuplicateCursor", "42" );
  register_error_class( "42P03", klass );
}
{
  VALUE klass = define_error_class( "DuplicateDatabase", "42" );
  register_error_class( "42P04", klass );
}
{
  VALUE klass = define_error_class( "DuplicateFunction", "42" );
  register_error_class( "42723", klass );
}
{
  VALUE klass = define_error_class( "DuplicatePstatement", "42" );
  register_error_class( "42P05", klass );
}
{
  VALUE klass = define_error_class( "DuplicateSchema", "42" );
  register_error_class( "42P06", klass );
}
{
  VALUE klass = define_error_class( "DuplicateTable", "42" );
  register_error_class( "42P07", klass );
}
{
  VALUE klass = define_error_class( "DuplicateAlias", "42" );
  register_error_class( "42712", klass );
}
{
  VALUE klass = define_error_class( "DuplicateObject", "42" );
  register_error_class( "42710", klass );
}
{
  VALUE klass = define_error_class( "AmbiguousColumn", "42" );
  register_error_class( "42702", klass );
}
{
  VALUE klass = define_error_class( "AmbiguousFunction", "42" );
  register_error_class( "42725", klass );
}
{
  VALUE klass = define_error_class( "AmbiguousParameter", "42" );
  register_error_class( "42P08", klass );
}
{
  VALUE klass = define_error_class( "AmbiguousAlias", "42" );
  register_error_class( "42P09", klass );
}
{
  VALUE klass = define_error_class( "InvalidColumnReference", "42" );
  register_error_class( "42P10", klass );
}
{
  VALUE klass = define_error_class( "InvalidColumnDefinition", "42" );
  register_error_class( "42611", klass );
}
{
  VALUE klass = define_error_class( "InvalidCursorDefinition", "42" );
  register_error_class( "42P11", klass );
}
{
  VALUE klass = define_error_class( "InvalidDatabaseDefinition", "42" );
  register_error_class( "42P12", klass );
}
{
  VALUE klass = define_error_class( "InvalidFunctionDefinition", "42" );
  register_error_class( "42P13", klass );
}
{
  VALUE klass = define_error_class( "InvalidPstatementDefinition", "42" );
  register_error_class( "42P14", klass );
}
{
  VALUE klass = define_error_class( "InvalidSchemaDefinition", "42" );
  register_error_class( "42P15", klass );
}
{
  VALUE klass = define_error_class( "InvalidTableDefinition", "42" );
  register_error_class( "42P16", klass );
}
{
  VALUE klass = define_error_class( "InvalidObjectDefinition", "42" );
  register_error_class( "42P17", klass );
}
{
  VALUE klass = define_error_class( "WithCheckOptionViolation", NULL );
  register_error_class( "44000", klass );
  register_error_class( "44", klass );
}
{
  VALUE klass = define_error_class( "InsufficientResources", NULL );
  register_error_class( "53000", klass );
  register_error_class( "53", klass );
}
{
  VALUE klass = define_error_class( "DiskFull", "53" );
  register_error_class( "53100", klass );
}
{
  VALUE klass = define_error_class( "OutOfMemory", "53" );
  register_error_class( "53200", klass );
}
{
  VALUE klass = define_error_class( "TooManyConnections", "53" );
  register_error_class( "53300", klass );
}
{
  VALUE klass = define_error_class( "ConfigurationLimitExceeded", "53" );
  register_error_class( "53400", klass );
}
{
  VALUE klass = define_error_class( "ProgramLimitExceeded", NULL );
  register_error_class( "54000", klass );
  register_error_class( "54", klass );
}
{
  VALUE klass = define_error_class( "StatementTooComplex", "54" );
  register_error_class( "54001", klass );
}
{
  VALUE klass = define_error_class( "TooManyColumns", "54" );
  register_error_class( "54011", klass );
}
{
  VALUE klass = define_error_class( "TooManyArguments", "54" );
  register_error_class( "54023", klass );
}
{
  VALUE klass = define_error_class( "ObjectNotInPrerequisiteState", NULL );
  register_error_class( "55000", klass );
  register_error_class( "55", klass );
}
{
  VALUE klass = define_error_class( "ObjectInUse", "55" );
  register_error_class( "55006", klass );
}
{
  VALUE klass = define_error_class( "CantChangeRuntimeParam", "55" );
  register_error_class( "55P02", klass );
}
{
  VALUE klass = define_error_class( "LockNotAvailable", "55" );
  register_error_class( "55P03", klass );
}
{
  VALUE klass = define_error_class( "UnsafeNewEnumValueUsage", "55" );
  register_error_class( "55P04", klass );
}
{
  VALUE klass = define_error_class( "OperatorIntervention", NULL );
  register_error_class( "57000", klass );
  register_error_class( "57", klass );
}
{
  VALUE klass = define_error_class( "QueryCanceled", "57" );
  register_error_class( "57014", klass );
}
{
  VALUE klass = define_error_class( "AdminShutdown", "57" );
  register_error_class( "57P01", klass );
}
{
  VALUE klass = define_error_class( "CrashShutdown", "57" );
  register_error_class( "57P02", klass );
}
{
  VALUE klass = define_error_class( "CannotConnectNow", "57" );
  register_error_class( "57P03", klass );
}
{
  VALUE klass = define_error_class( "DatabaseDropped", "57" );
  register_error_class( "57P04", klass );
}
{
  VALUE klass = define_error_class( "IdleSessionTimeout", "57" );
  register_error_class( "57P05", klass );
}
{
  VALUE klass = define_error_class( "SystemError", NULL );
  register_error_class( "58000", klass );
  register_error_class( "58", klass );
}
{
  VALUE klass = define_error_class( "IoError", "58" );
  register_error_class( "58030", klass );
}
{
  VALUE klass = define_error_class( "UndefinedFile", "58" );
  register_error_class( "58P01", klass );
}
{
  VALUE klass = define_error_class( "DuplicateFile", "58" );
  register_error_class( "58P02", klass );
}
{
  VALUE klass = define_error_class( "SnapshotTooOld", NULL );
  register_error_class( "72000", klass );
  register_error_class( "72", klass );
}
{
  VALUE klass = define_error_class( "ConfigFileError", NULL );
  register_error_class( "F0000", klass );
  register_error_class( "F0", klass );
}
{
  VALUE klass = define_error_class( "LockFileExists", "F0" );
  register_error_class( "F0001", klass );
}
{
  VALUE klass = define_error_class( "FdwError", NULL );
  register_error_class( "HV000", klass );
  register_error_class( "HV", klass );
}
{
  VALUE klass = define_error_class( "FdwColumnNameNotFound", "HV" );
  register_error_class( "HV005", klass );
}
{
  VALUE klass = define_error_class( "FdwDynamicParameterValueNeeded", "HV" );
  register_error_class( "HV002", klass );
}
{
  VALUE klass = define_error_class( "FdwFunctionSequenceError", "HV" );
  register_error_class( "HV010", klass );
}
{
  VALUE klass = define_error_class( "FdwInconsistentDescriptorInformation", "HV" );
  register_error_class( "HV021", klass );
}
{
  VALUE klass = define_error_class( "FdwInvalidAttributeValue", "HV" );
  register_error_class( "HV024", klass );
}
{
  VALUE klass = define_error_class( "FdwInvalidColumnName", "HV" );
  register_error_class( "HV007", klass );
}
{
  VALUE klass = define_error_class( "FdwInvalidColumnNumber", "HV" );
  register_error_class( "HV008", klass );
}
{
  VALUE klass = define_error_class( "FdwInvalidDataType", "HV" );
  register_error_class( "HV004", klass );
}
{
  VALUE klass = define_error_class( "FdwInvalidDataTypeDescriptors", "HV" );
  register_error_class( "HV006", klass );
}
{
  VALUE klass = define_error_class( "FdwInvalidDescriptorFieldIdentifier", "HV" );
  register_error_class( "HV091", klass );
}
{
  VALUE klass = define_error_class( "FdwInvalidHandle", "HV" );
  register_error_class( "HV00B", klass );
}
{
  VALUE klass = define_error_class( "FdwInvalidOptionIndex", "HV" );
  register_error_class( "HV00C", klass );
}
{
  VALUE klass = define_error_class( "FdwInvalidOptionName", "HV" );
  register_error_class( "HV00D", klass );
}
{
  VALUE klass = define_error_class( "FdwInvalidStringLengthOrBufferLength", "HV" );
  register_error_class( "HV090", klass );
}
{
  VALUE klass = define_error_class( "FdwInvalidStringFormat", "HV" );
  register_error_class( "HV00A", klass );
}
{
  VALUE klass = define_error_class( "FdwInvalidUseOfNullPointer", "HV" );
  register_error_class( "HV009", klass );
}
{
  VALUE klass = define_error_class( "FdwTooManyHandles", "HV" );
  register_error_class( "HV014", klass );
}
{
  VALUE klass = define_error_class( "FdwOutOfMemory", "HV" );
  register_error_class( "HV001", klass );
}
{
  VALUE klass = define_error_class( "FdwNoSchemas", "HV" );
  register_error_class( "HV00P", klass );
}
{
  VALUE klass = define_error_class( "FdwOptionNameNotFound", "HV" );
  register_error_class( "HV00J", klass );
}
{
  VALUE klass = define_error_class( "FdwReplyHandle", "HV" );
  register_error_class( "HV00K", klass );
}
{
  VALUE klass = define_error_class( "FdwSchemaNotFound", "HV" );
  register_error_class( "HV00Q", klass );
}
{
  VALUE klass = define_error_class( "FdwTableNotFound", "HV" );
  register_error_class( "HV00R", klass );
}
{
  VALUE klass = define_error_class( "FdwUnableToCreateExecution", "HV" );
  register_error_class( "HV00L", klass );
}
{
  VALUE klass = define_error_class( "FdwUnableToCreateReply", "HV" );
  register_error_class( "HV00M", klass );
}
{
  VALUE klass = define_error_class( "FdwUnableToEstablishConnection", "HV" );
  register_error_class( "HV00N", klass );
}
{
  VALUE klass = define_error_class( "PlpgsqlError", NULL );
  register_error_class( "P0000", klass );
  register_error_class( "P0", klass );
}
{
  VALUE klass = define_error_class( "RaiseException", "P0" );
  register_error_class( "P0001", klass );
}
{
  VALUE klass = define_error_class( "NoDataFound", "P0" );
  register_error_class( "P0002", klass );
}
{
  VALUE klass = define_error_class( "TooManyRows", "P0" );
  register_error_class( "P0003", klass );
}
{
  VALUE klass = define_error_class( "AssertFailure", "P0" );
  register_error_class( "P0004", klass );
}
{
  VALUE klass = define_error_class( "InternalError", NULL );
  register_error_class( "XX000", klass );
  register_error_class( "XX", klass );
}
{
  VALUE klass = define_error_class( "DataCorrupted", "XX" );
  register_error_class( "XX001", klass );
}
{
  VALUE klass = define_error_class( "IndexCorrupted", "XX" );
  register_error_class( "XX002", klass );
}
