= Compiling 'pg' on MS Windows

In order to build this extension on MS Windows you will need a couple things.

First, a compiler. For the one click installer this means you should use
the DevKit or the compiler that comes with cygwin if you're building on that
platform.

If you've built <PERSON> yourself, you should use the same compiler to build
this library that you used to build Ruby.

Second, PostgreSQL. Be sure you installed it with the development header
files if you installed it using the standard PostgreSQL installer for
Windows. If you didn't, you can run the installer again, select "modify",
and then select the 'development headers' option to install them.

I recommend making sure that 'pg_config.exe' is in your PATH. The PostgreSQL
installer for Windows does not necessarily update your PATH when it installs
itself, so you may need to do this manually. This isn't strictly necessary,
however.

In order to build ruby-pg, just run 'rake'. If the pg_config.exe executable
is not in your PATH, you'll need to explicitly point ruby-pg to where your
PostgreSQL headers and libraries are with something like this:

	rake --with-pg-dir=c:/progra~1/postgr~1/8.3

Adjust your path accordingly. BE SURE TO USE THE SHORT PATH NAMES! If you
try to use a path with spaces in it, the nmake.exe program will choke.


== Building binary 'pg' gems for MS Windows

Binary gems for windows can be built on Linux, OS-X and even on Windows
with the help of docker. This is how regular windows gems are built for
rubygems.org .

To do this, install boot2docker {on Windows}[https://github.com/boot2docker/windows-installer/releases]
or {on OS X}[https://github.com/boot2docker/osx-installer/releases] and make
sure it is started. A native Docker installation is best on Linux.

Then run:

	rake gem:windows

This will download a docker image suited for building windows gems, and it
will download and build OpenSSL and PostgreSQL. Finally the gem is built
containing binaries for all supported ruby versions.


== Reporting Problems

If you have any problems you can submit them via {the project's
issue-tracker}[https://github.com/ged/ruby-pg/issues]. And submit questions, problems, or
solutions, so that it can be improved.

