# -*- ruby -*-

require 'date'
require 'rubygems'
require 'hoe'
require 'pp'

Hoe.spec 'ruby-pg' do
	self.developer '<PERSON>', '<EMAIL>'
	self.dependency 'pg', '~> 0'
	self.spec_extras[:date] = Date.parse( '2008/01/30' )

	line = '-' * 75
	msg = paragraphs_of( 'README.txt', 3..-1 )
	msg.unshift( line )
	msg.push( line )

	self.spec_extras[:post_install_message] = msg.join( "\n\n" ) + "\n"
end

# vim: syntax=ruby
