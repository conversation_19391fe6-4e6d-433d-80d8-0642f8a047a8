= Action Mailer -- Easy email delivery and testing

Action Mailer is a framework for designing email service layers. These layers
are used to consolidate code for sending out forgotten passwords, welcome
wishes on signup, invoices for billing, and any other use case that requires
a written notification to either a person or another system.

Action Mailer is in essence a wrapper around Action Controller and the
Mail gem.  It provides a way to make emails using templates in the same
way that Action Controller renders views using templates.

Additionally, an Action Mailer class can be used to process incoming email,
such as allowing a blog to accept new posts from an email (which could even
have been sent from a phone).

You can read more about Action Mailer in the {Action Mailer Basics}[https://guides.rubyonrails.org/action_mailer_basics.html] guide.

== Sending emails

The framework works by initializing any instance variables you want to be
available in the email template, followed by a call to +mail+ to deliver
the email.

This can be as simple as:

  class Notifier < ActionMailer::Base
    default from: '<EMAIL>'

    def welcome(recipient)
      @recipient = recipient
      mail(to: recipient,
           subject: "[Signed up] Welcome #{recipient}")
    end
  end

The body of the email is created by using an Action View template (regular
ERB) that has the instance variables that are declared in the mailer action.

So the corresponding body template for the method above could look like this:

  Hello there,

  Mr. <%= @recipient %>

  Thank you for signing up!

If the recipient was given as "<EMAIL>", the email
generated would look like this:

  Date: Mon, 25 Jan 2010 22:48:09 +1100
  From: <EMAIL>
  To: <EMAIL>
  Message-ID: <<EMAIL>>
  Subject: [Signed up] Welcome <EMAIL>
  Mime-Version: 1.0
  Content-Type: text/plain;
  	charset="US-ASCII";
  Content-Transfer-Encoding: 7bit

  Hello there,

  Mr. <EMAIL>

  Thank you for signing up!

In order to send mails, you simply call the method and then call +deliver_now+ on the return value.

Calling the method returns a Mail Message object:

  message = Notifier.welcome("<EMAIL>")   # => Returns a Mail::Message object
  message.deliver_now                                    # => delivers the email

Or you can just chain the methods together like:

  Notifier.welcome("<EMAIL>").deliver_now # Creates the email and sends it immediately

== Setting defaults

It is possible to set default values that will be used in every method in your
Action Mailer class. To implement this functionality, you just call the public
class method +default+ which you get for free from ActionMailer::Base.
This method accepts a Hash as the parameter. You can use any of the headers,
email messages have, like +:from+ as the key. You can also pass in a string as
the key, like "Content-Type", but Action Mailer does this out of the box for you,
so you won't need to worry about that. Finally, it is also possible to pass in a
Proc that will get evaluated when it is needed.

Note that every value you set with this method will get overwritten if you use the
same key in your mailer method.

Example:

  class AuthenticationMailer < ActionMailer::Base
    default from: "<EMAIL>", subject: Proc.new { "E-mail was generated at #{Time.now}" }
    .....
  end

== Configuration

The Base class has the full list of configuration options. Here's an example:

  ActionMailer::Base.smtp_settings = {
    address:        'smtp.yourserver.com', # default: localhost
    port:           '25',                  # default: 25
    user_name:      'user',
    password:       'pass',
    authentication: :plain                 # :plain, :login or :cram_md5
  }


== Download and installation

The latest version of Action Mailer can be installed with RubyGems:

  $ gem install actionmailer

Source code can be downloaded as part of the \Rails project on GitHub:

* https://github.com/rails/rails/tree/main/actionmailer


== License

Action Mailer is released under the MIT license:

* https://opensource.org/licenses/MIT


== Support

API documentation is at

* https://api.rubyonrails.org

Bug reports for the Ruby on \Rails project can be filed here:

* https://github.com/rails/rails/issues

Feature requests should be discussed on the rails-core mailing list here:

* https://discuss.rubyonrails.org/c/rubyonrails-core
