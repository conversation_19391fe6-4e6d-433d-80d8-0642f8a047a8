Tasks on this page:

- {New Document}[#label-New+Document]
  - {Task: Create an Empty Document}[#label-Task-3A+Create+an+Empty+Document]
  - {Task: Parse a String into a New Document}[#label-Task-3A+Parse+a+String+into+a+New+Document]
  - {Task: Parse an IO Stream into a New Document}[#label-Task-3A+Parse+an+IO+Stream+into+a+New+Document]
  - {Task: Create a Document from an Existing Document}[#label-Task-3A+Create+a+Document+from+an+Existing+Document]
  - {Task: Clone a Document}[#label-Task-3A+Clone+a+Document]
- {Document Type}[#label-Document+Type]
  - {Task: Get the Document Type}[#label-Task-3A+Get+the+Document+Type]
  - {Task: Set the Document Type}[#label-Task-3A+Set+the+Document+Type]
- {XML Declaration}[#label-XML+Declaration]
  - {Task: Get the XML Declaration}[#label-Task-3A+Get+the+XML+Declaration]
  - {Task: Set the XML Declaration}[#label-Task-3A+Set+the+XML+Declaration]
- {Children}[#label-Children]
  - {Task: Add an Element Child}[#label-Task-3A+Add+an+Element+Child]
  - {Task: Add a Non-Element Child}[#label-Task-3A+Add+a+Non-Element+Child]
- {Writing}[#label-Writing]
  - {Task: Write to $stdout}[#label-Task-3A+Write+to+-24stdout]
  - {Task: Write to IO Stream}[#label-Task-3A+Write+to+IO+Stream]
  - {Task: Write with No Indentation}[#label-Task-3A+Write+with+No+Indentation]
  - {Task: Write with Specified Indentation}[#label-Task-3A+Write+with+Specified+Indentation]
- {Querying}[#label-Querying]
  - {Task: Get the Document}[#label-Task-3A+Get+the+Document]
  - {Task: Get the Encoding}[#label-Task-3A+Get+the+Encoding]
  - {Task: Get the Node Type}[#label-Task-3A+Get+the+Node+Type]
  - {Task: Get the Root Element}[#label-Task-3A+Get+the+Root+Element]
  - {Task: Determine Whether Stand-Alone}[#label-Task-3A+Determine+Whether+Stand-Alone]
  - {Task: Get the Version}[#label-Task-3A+Get+the+Version]

