Tasks on this page:

- {Queries}[#label-Queries]
  - {Task: Get the Count of Children}[#label-Task-3A+Get+the+Count+of+Children]
  - {Task: Get the Child at a Given Index}[#label-Task-3A+Get+the+Child+at+a+Given+Index]
  - {Task: Get the Index of a Given Child}[#label-Task-3A+Get+the+Index+of+a+Given+Child]
  - {Task: Get the Children}[#label-Task-3A+Get+the+Children]
  - {Task: Determine Whether the Node is a Parent}[#label-Task-3A+Determine+Whether+the+Node+is+a+Parent]
- {Additions}[#label-Additions]
  - {Task: Add a Child at the Beginning}[#label-Task-3A+Add+a+Child+at+the+Beginning]
  - {Task: Add a Child at the End}[#label-Task-3A+Add+a+Child+at+the+End]
  - {Task: Replace a Child with Another Child}[#label-Task-3A+Replace+a+Child+with+Another+Child]
  - {Task: Replace Multiple Children with Another Child}[#label-Task-3A+Replace+Multiple+Children+with+Another+Child]
  - {Task: Insert Child Before a Given Child}[#label-Task-3A+Insert+Child+Before+a+Given+Child]
  - {Task: Insert Child After a Given Child}[#label-Task-3A+Insert+Child+After+a+Given+Child]
- {Deletions}[#label-Deletions]
  - {Task: Remove a Given Child}[#label-Task-3A+Remove+a+Given+Child]
  - {Task: Remove the Child at a Specified Offset}[#label-Task-3A+Remove+the+Child+at+a+Specified+Offset]
  - {Task: Remove Children That Meet Specified Criteria}[#label-Task-3A+Remove+Children+That+Meet+Specified+Criteria]
- {Iterations}[#label-Iterations]
  - {Task: Iterate Over Children}[#label-Task-3A+Iterate+Over+Children]
  - {Task: Iterate Over Child Indexes}[#label-Task-3A+Iterate+Over+Child+Indexes]
- {Clones}[#label-Clones]
  - {Task: Clone Deeply}[#label-Task-3A+Clone+Deeply]

