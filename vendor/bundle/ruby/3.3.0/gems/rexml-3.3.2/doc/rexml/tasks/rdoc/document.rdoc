== Class Document

Class Document has methods from its superclasses and included modules;
see:

- {Tasks for Element}[element_rdoc.html].
- {Tasks for Parent}[parent_rdoc.html].
- {Tasks for Child}[child_rdoc.html].
- {Tasks for Node}[node_rdoc.html].
- {Module Enumerable}[https://docs.ruby-lang.org/en/master/Enumerable.html].

:include: ../tocs/document_toc.rdoc

=== New Document

==== Task: Create an Empty Document

Use method {Document::new}[../../../../REXML/Document.html#method-c-new]
to create an empty document.

  d = REXML::Document.new

==== Task: Parse a \String into a New Document

Use method {Document::new}[../../../../REXML/Document.html#method-c-new]
to parse an XML string into a new document:

  xml_string = '<root><a/>text<b/>more<c/></root>'
  d = REXML::Document.new(xml_string)
  d.root # => <root> ... </>

==== Task: Parse an \IO Stream into a New Document

Use method {Document::new}[../../../../REXML/Document.html#method-c-new]
to parse an XML \IO stream into a new document:

  xml_string = '<root><a/>text<b/>more<c/></root>'
  File.write('t.xml', xml_string)
  d = File.open('t.xml', 'r') do |file|
    REXML::Document.new(file)
  end
  d.root # => <root> ... </>

==== Task: Create a Document from an Existing Document

Use method {Document::new}[../../../../REXML/Document.html#method-c-new]
to create a document from an existing document.
The context and attributes are copied to the new document,
but not the children:

  xml_string = '<root><a/>text<b/>more<c/></root>'
  d = REXML::Document.new(xml_string)
  d.children    # => [<root> ... </>]
  d.context = {raw: :all, compress_whitespace: :all}
  d.add_attributes({'bar' => 0, 'baz' => 1})
  d1 = REXML::Document.new(d)
  d1.context    # => {:raw=>:all, :compress_whitespace=>:all}
  d1.attributes # => {"bar"=>bar='0', "baz"=>baz='1'}
  d1.children   # => []

==== Task: Clone a Document

Use method {Document#clone}[../../../../REXML/Document.html#method-i-clone]
to clone a document.
The context and attributes are copied to the new document,
but not the children:

  xml_string = '<root><a/>text<b/>more<c/></root>'
  d = REXML::Document.new(xml_string)
  d.children    # => [<root> ... </>]
  d.context = {raw: :all, compress_whitespace: :all}
  d.add_attributes({'bar' => 0, 'baz' => 1})
  d1 = d.clone  # => < bar='0' baz='1'/>
  d1.context    # => {:raw=>:all, :compress_whitespace=>:all}
  d1.attributes # => {"bar"=>bar='0', "baz"=>baz='1'}
  d1.children   # => []

=== Document Type

==== Task: Get the Document Type

Use method {Document#doctype}[../../../../REXML/Document.html#method-i-doctype]
to get the document type:

  d = REXML::Document.new('<!DOCTYPE document SYSTEM "subjects.dtd">')
  d.doctype.class # => REXML::DocType
  d = REXML::Document.new('')
  d.doctype.class # => nil

==== Task: Set the Document Type

Use method {document#add}[../../../../REXML/Document.html#method-i-add]
to add or replace the document type:

  d = REXML::Document.new('')
  d.doctype.class # => nil
  d.add(REXML::DocType.new('foo'))
  d.doctype.class # => REXML::DocType

=== XML Declaration

==== Task: Get the XML Declaration

Use method {document#xml_decl}[../../../../REXML/Document.html#method-i-xml_decl]
to get the XML declaration:

  d = REXML::Document.new('<!DOCTYPE document SYSTEM "subjects.dtd">')
  d.xml_decl.class # => REXML::XMLDecl
  d.xml_decl       # => <?xml ... ?>
  d = REXML::Document.new('')
  d.xml_decl.class # => REXML::XMLDecl
  d.xml_decl       # => <?xml ... ?>

==== Task: Set the XML Declaration

Use method {document#add}[../../../../REXML/Document.html#method-i-add]
to replace the XML declaration:

  d = REXML::Document.new('<!DOCTYPE document SYSTEM "subjects.dtd">')
  d.add(REXML::XMLDecl.new)

=== Children

==== Task: Add an Element Child

Use method
{document#add_element}[../../../../REXML/Document.html#method-i-add_element]
to add an element to the document:

  d = REXML::Document.new('')
  d.add_element(REXML::Element.new('root'))
  d.children # => [<root/>]

==== Task: Add a Non-Element Child

Use method
{document#add}[../../../../REXML/Document.html#method-i-add]
to add a non-element to the document:

  xml_string = '<root><a/>text<b/>more<c/></root>'
  d = REXML::Document.new(xml_string)
  d.add(REXML::Text.new('foo'))
  d.children # => [<root> ... </>, "foo"]

=== Writing

==== Task: Write to $stdout

Use method
{document#write}[../../../../REXML/Document.html#method-i-write]
to write the document to <tt>$stdout</tt>:

  xml_string = '<root><a/>text<b/>more<c/></root>'
  d = REXML::Document.new(xml_string)
  d.write

Output:

  <root><a/>text<b/>more<c/></root>

==== Task: Write to IO Stream

Use method
{document#write}[../../../../REXML/Document.html#method-i-write]
to write the document to <tt>$stdout</tt>:

  xml_string = '<root><a/>text<b/>more<c/></root>'
  d = REXML::Document.new(xml_string)
  File.open('t.xml', 'w') do |file|
    d.write(file)
  end
  p File.read('t.xml')

Output:

  "<root><a/>text<b/>more<c/></root>"

==== Task: Write with No Indentation

Use method
{document#write}[../../../../REXML/Document.html#method-i-write]
to write the document with no indentation:

  xml_string = '<root><a><b><c></c></b></a></root>'
  d = REXML::Document.new(xml_string)
  d.write({indent: 0})

Output:

  <root>
  <a>
  <b>
  <c/>
  </b>
  </a>
  </root>

==== Task: Write with Specified Indentation

Use method
{document#write}[../../../../REXML/Document.html#method-i-write]
to write the document with a specified indentation:

  xml_string = '<root><a><b><c></c></b></a></root>'
  d = REXML::Document.new(xml_string)
  d.write({indent: 2})

Output:

  <root>
    <a>
      <b>
        <c/>
      </b>
    </a>
  </root>

=== Querying

==== Task: Get the Document

Use method
{document#document}[../../../../REXML/Document.html#method-i-document]
to get the document (+self+); overrides <tt>Element#document</tt>:

  xml_string = '<root><a><b><c></c></b></a></root>'
  d = REXML::Document.new(xml_string)
  d.document == d # => true

==== Task: Get the Encoding

Use method
{document#document}[../../../../REXML/Document.html#method-i-document]
to get the document (+self+); overrides <tt>Element#document</tt>:

  xml_string = '<root><a><b><c></c></b></a></root>'
  d = REXML::Document.new(xml_string)
  d.encoding # => "UTF-8"

==== Task: Get the Node Type

Use method
{document#node_type}[../../../../REXML/Document.html#method-i-node_type]
to get the node type (+:document+); overrides <tt>Element#node_type</tt>:

  xml_string = '<root><a><b><c></c></b></a></root>'
  d = REXML::Document.new(xml_string)
  d.node_type # => :document

==== Task: Get the Root Element

Use method
{document#root}[../../../../REXML/Document.html#method-i-root]
to get the root element:

  xml_string = '<root><a><b><c></c></b></a></root>'
  d = REXML::Document.new(xml_string)
  d.root # => <root> ... </>

==== Task: Determine Whether Stand-Alone

Use method
{document#stand_alone?}[../../../../REXML/Document.html#method-i-stand_alone-3F]
to get the stand-alone value:

  d = REXML::Document.new('<?xml standalone="yes"?>')
  d.stand_alone? # => "yes"

==== Task: Get the Version

Use method
{document#version}[../../../../REXML/Document.html#method-i-version]
to get the version:

  d = REXML::Document.new('<?xml version="2.0" encoding="UTF-8"?>')
  d.version # => "2.0"
