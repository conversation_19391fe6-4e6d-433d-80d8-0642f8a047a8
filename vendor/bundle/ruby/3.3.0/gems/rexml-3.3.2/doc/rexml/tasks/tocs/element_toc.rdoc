Tasks on this page:

- {New Element}[#label-New+Element]
  - {Task: Create a Default Element}[#label-Task-3A+Create+a+Default+Element]
  - {Task: Create a Named Element}[#label-Task-3A+Create+a+Named+Element]
  - {Task: Create an Element with Name and Parent}[#label-Task-3A+Create+an+Element+with+Name+and+Parent]
  - {Task: Create an Element with Name, Parent, and Context}[#label-Task-3A+Create+an+Element+with+Name-2C+Parent-2C+and+Context]
  - {Task: Create a Shallow Clone}[#label-Task-3A+Create+a+Shallow+Clone]
- {Attributes}[#label-Attributes]
  - {Task: Create and Add an Attribute}[#label-Task-3A+Create+and+Add+an+Attribute]
  - {Task: Add an Existing Attribute}[#label-Task-3A+Add+an+Existing+Attribute]
  - {Task: Add Multiple Attributes from a Hash}[#label-Task-3A+Add+Multiple+Attributes+from+a+Hash]
  - {Task: Add Multiple Attributes from an Array}[#label-Task-3A+Add+Multiple+Attributes+from+an+Array]
  - {Task: Retrieve the Value for an Attribute Name}[#label-Task-3A+Retrieve+the+Value+for+an+Attribute+Name]
  - {Task: Retrieve the Attribute Value for a Name and Namespace}[#label-Task-3A+Retrieve+the+Attribute+Value+for+a+Name+and+Namespace]
  - {Task: Delete an Attribute}[#label-Task-3A+Delete+an+Attribute]
  - {Task: Determine Whether the Element Has Attributes}[#label-Task-3A+Determine+Whether+the+Element+Has+Attributes]
- {Children}[#label-Children]
  - {Task: Create and Add an Element}[#label-Task-3A+Create+and+Add+an+Element]
  - {Task: Add an Existing Element}[#label-Task-3A+Add+an+Existing+Element]
  - {Task: Create and Add an Element with Attributes}[#label-Task-3A+Create+and+Add+an+Element+with+Attributes]
  - {Task: Add an Existing Element with Added Attributes}[#label-Task-3A+Add+an+Existing+Element+with+Added+Attributes]
  - {Task: Delete a Specified Element}[#label-Task-3A+Delete+a+Specified+Element]
  - {Task: Delete an Element by Index}[#label-Task-3A+Delete+an+Element+by+Index]
  - {Task: Delete an Element by XPath}[#label-Task-3A+Delete+an+Element+by+XPath]
  - {Task: Determine Whether Element Children}[#label-Task-3A+Determine+Whether+Element+Children]
  - {Task: Get Element Descendants by XPath}[#label-Task-3A+Get+Element+Descendants+by+XPath]
  - {Task: Get Next Element Sibling}[#label-Task-3A+Get+Next+Element+Sibling]
  - {Task: Get Previous Element Sibling}[#label-Task-3A+Get+Previous+Element+Sibling]
  - {Task: Add a Text Node}[#label-Task-3A+Add+a+Text+Node]
  - {Task: Replace the First Text Node}[#label-Task-3A+Replace+the+First+Text+Node]
  - {Task: Remove the First Text Node}[#label-Task-3A+Remove+the+First+Text+Node]
  - {Task: Retrieve the First Text Node}[#label-Task-3A+Retrieve+the+First+Text+Node]
  - {Task: Retrieve a Specific Text Node}[#label-Task-3A+Retrieve+a+Specific+Text+Node]
  - {Task: Determine Whether the Element has Text Nodes}[#label-Task-3A+Determine+Whether+the+Element+has+Text+Nodes]
  - {Task: Get the Child at a Given Index}[#label-Task-3A+Get+the+Child+at+a+Given+Index]
  - {Task: Get All CDATA Children}[#label-Task-3A+Get+All+CDATA+Children]
  - {Task: Get All Comment Children}[#label-Task-3A+Get+All+Comment+Children]
  - {Task: Get All Processing Instruction Children}[#label-Task-3A+Get+All+Processing+Instruction+Children]
  - {Task: Get All Text Children}[#label-Task-3A+Get+All+Text+Children]
- {Namespaces}[#label-Namespaces]
  - {Task: Add a Namespace}[#label-Task-3A+Add+a+Namespace]
  - {Task: Delete the Default Namespace}[#label-Task-3A+Delete+the+Default+Namespace]
  - {Task: Delete a Specific Namespace}[#label-Task-3A+Delete+a+Specific+Namespace]
  - {Task: Get a Namespace URI}[#label-Task-3A+Get+a+Namespace+URI]
  - {Task: Retrieve Namespaces}[#label-Task-3A+Retrieve+Namespaces]
  - {Task: Retrieve Namespace Prefixes}[#label-Task-3A+Retrieve+Namespace+Prefixes]
- {Iteration}[#label-Iteration]
  - {Task: Iterate Over Elements}[#label-Task-3A+Iterate+Over+Elements]
  - {Task: Iterate Over Elements Having a Specified Attribute}[#label-Task-3A+Iterate+Over+Elements+Having+a+Specified+Attribute]
  - {Task: Iterate Over Elements Having a Specified Attribute and Value}[#label-Task-3A+Iterate+Over+Elements+Having+a+Specified+Attribute+and+Value]
  - {Task: Iterate Over Elements Having Specified Text}[#label-Task-3A+Iterate+Over+Elements+Having+Specified+Text]
- {Context}[#label-Context]
- {Other Getters}[#label-Other+Getters]

