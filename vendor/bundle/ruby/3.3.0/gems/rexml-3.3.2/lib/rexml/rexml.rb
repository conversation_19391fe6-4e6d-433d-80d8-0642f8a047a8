# -*- coding: utf-8 -*-
# frozen_string_literal: false
#
# \Module \REXML provides classes and methods for parsing,
# editing, and generating XML.
#
# == Implementation
#
# \REXML:
# - Is pure Ruby.
# - Provides tree, stream, SAX2, pull, and lightweight APIs.
# - Conforms to {XML version 1.0}[https://www.w3.org/TR/REC-xml/].
# - Fully implements {XPath version 1.0}[http://www.w3c.org/tr/xpath].
# - Is {non-validating}[https://www.w3.org/TR/xml/].
# - Passes 100% of the non-validating {Oasis tests}[http://www.oasis-open.org/committees/xml-conformance/xml-test-suite.shtml].
#
# == In a Hurry?
#
# If you're somewhat familiar with XML
# and have a particular task in mind,
# you may want to see {the tasks pages}[doc/rexml/tasks/tocs/master_toc_rdoc.html].
#
# == API
#
# Among the most important classes for using \REXML are:
# - REXML::Document.
# - REXML::Element.
#
# There's also an {REXML tutorial}[doc/rexml/tutorial_rdoc.html].
#
module REXML
  COPYRIGHT = "Copyright © 2001-2008 Sean Russell <<EMAIL>>"
  DATE = "2008/019"
  VERSION = "3.3.2"
  REVISION = ""

  Copyright = COPYRIGHT
  Version = VERSION
end
