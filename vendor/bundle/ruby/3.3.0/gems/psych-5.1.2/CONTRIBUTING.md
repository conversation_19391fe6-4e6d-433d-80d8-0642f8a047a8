## How to contribute to Psych

Full details [here](https://bugs.ruby-lang.org/projects/ruby/wiki/HowToContribute)

#### **Did you find a bug?**

* **Do not open an issue if the bug is a security vulnerability
  in Psych**, instead refer to our [security policy](https://www.ruby-lang.org/en/security/) and email [here](<EMAIL>).

* **Ensure the bug was not already reported** by searching on ruby-core, the ruby github repo and on Psych's github repo. More info [here](https://bugs.ruby-lang.org/projects/ruby/wiki/HowToReport)

* If you're unable to find an open issue addressing the problem, [open a new one](https://bugs.ruby-lang.org/). Be sure to include a **title and clear description**, as much relevant information as possible, and a **code sample** or an **executable test case** demonstrating the expected behavior that is not occurring.

#### **Did you write a patch that fixes a bug?**

* Open a new GitHub pull request with the patch for small fixes. Anything larger look [here](https://bugs.ruby-lang.org/projects/ruby/wiki/HowToContribute); submit a Feature.

* Ensure you clearly describe the problem and solution. Include the relevant ticket/issue number if applicable.

Psych is a volunteer effort. We encourage you to pitch in and [join the team](https://github.com/ruby/psych/contributors)!

Thanks! :heart: :heart: :heart:

The Psych Team
