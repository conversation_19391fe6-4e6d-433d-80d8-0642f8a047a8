# frozen_string_literal: true

#--
# This file is generated from RFC3454, by rake.  Don't edit directly.
#++

module Net::IMAP::StringPrep

  module Tables

    # Unassigned code points in Unicode 3.2 \StringPrep\[\"A.1\"]
    IN_A_1 = /\p{^AGE=3.2}/.freeze

    # Commonly mapped to nothing \StringPrep\[\"B.1\"]
    IN_B_1 = /[\u{00ad 034f 1806 2060 feff}\u{180b}-\u{180d}\u{200b}-\u{200d}\u{fe00}-\u{fe0f}]/.freeze

    # Mapping for case-folding used with NFKC \StringPrep\[\"B.2\"]
    IN_B_2 = /[\u{00b5 0100 0102 0104 0106 0108 010a 010c 010e 0110 0112 0114 0116 0118 011a 011c 011e 0120 0122 0124 0126 0128 012a 012c 012e 0130 0132 0134 0136 0139 013b 013d 013f 0141 0143 0145 0147 014c 014e 0150 0152 0154 0156 0158 015a 015c 015e 0160 0162 0164 0166 0168 016a 016c 016e 0170 0172 0174 0176 017b 017d 017f 0184 01a2 01a4 01a9 01ac 01b5 01bc 01cd 01cf 01d1 01d3 01d5 01d7 01d9 01db 01de 01e0 01e2 01e4 01e6 01e8 01ea 01ec 01ee 01f4 01fa 01fc 01fe 0200 0202 0204 0206 0208 020a 020c 020e 0210 0212 0214 0216 0218 021a 021c 021e 0220 0222 0224 0226 0228 022a 022c 022e 0230 0232 0345 037a 0386 038c 03b0 03c2 03d8 03da 03dc 03de 03e0 03e2 03e4 03e6 03e8 03ea 03ec 03ee 0460 0462 0464 0466 0468 046a 046c 046e 0470 0472 0474 0476 0478 047a 047c 047e 0480 048a 048c 048e 0490 0492 0494 0496 0498 049a 049c 049e 04a0 04a2 04a4 04a6 04a8 04aa 04ac 04ae 04b0 04b2 04b4 04b6 04b8 04ba 04bc 04be 04c1 04c3 04c5 04c7 04c9 04cb 04cd 04d0 04d2 04d4 04d6 04d8 04da 04dc 04de 04e0 04e2 04e4 04e6 04e8 04ea 04ec 04ee 04f0 04f2 04f4 04f8 0500 0502 0504 0506 0508 050a 050c 050e 0587 1e00 1e02 1e04 1e06 1e08 1e0a 1e0c 1e0e 1e10 1e12 1e14 1e16 1e18 1e1a 1e1c 1e1e 1e20 1e22 1e24 1e26 1e28 1e2a 1e2c 1e2e 1e30 1e32 1e34 1e36 1e38 1e3a 1e3c 1e3e 1e40 1e42 1e44 1e46 1e48 1e4a 1e4c 1e4e 1e50 1e52 1e54 1e56 1e58 1e5a 1e5c 1e5e 1e60 1e62 1e64 1e66 1e68 1e6a 1e6c 1e6e 1e70 1e72 1e74 1e76 1e78 1e7a 1e7c 1e7e 1e80 1e82 1e84 1e86 1e88 1e8a 1e8c 1e8e 1e90 1e92 1e94 1ea0 1ea2 1ea4 1ea6 1ea8 1eaa 1eac 1eae 1eb0 1eb2 1eb4 1eb6 1eb8 1eba 1ebc 1ebe 1ec0 1ec2 1ec4 1ec6 1ec8 1eca 1ecc 1ece 1ed0 1ed2 1ed4 1ed6 1ed8 1eda 1edc 1ede 1ee0 1ee2 1ee4 1ee6 1ee8 1eea 1eec 1eee 1ef0 1ef2 1ef4 1ef6 1ef8 1f50 1f52 1f54 1f56 1f59 1f5b 1f5d 1f5f 1fbe 20a8 2107 2109 2124 2126 2128 2133 2145 3371 3373 3375 33c3 33cb 33d7 1d49c 1d4a2 1d546 1d6d3 1d70d 1d747 1d781 1d7bb}\u{0041}-\u{005a}\u{00c0}-\u{00d6}\u{00d8}-\u{00df}\u{0149}-\u{014a}\u{0178}-\u{0179}\u{0181}-\u{0182}\u{0186}-\u{0187}\u{0189}-\u{018b}\u{018e}-\u{0191}\u{0193}-\u{0194}\u{0196}-\u{0198}\u{019c}-\u{019d}\u{019f}-\u{01a0}\u{01a6}-\u{01a7}\u{01ae}-\u{01af}\u{01b1}-\u{01b3}\u{01b7}-\u{01b8}\u{01c4}-\u{01c5}\u{01c7}-\u{01c8}\u{01ca}-\u{01cb}\u{01f0}-\u{01f2}\u{01f6}-\u{01f8}\u{0388}-\u{038a}\u{038e}-\u{03a1}\u{03a3}-\u{03ab}\u{03d0}-\u{03d6}\u{03f0}-\u{03f2}\u{03f4}-\u{03f5}\u{0400}-\u{042f}\u{0531}-\u{0556}\u{1e96}-\u{1e9b}\u{1f08}-\u{1f0f}\u{1f18}-\u{1f1d}\u{1f28}-\u{1f2f}\u{1f38}-\u{1f3f}\u{1f48}-\u{1f4d}\u{1f68}-\u{1f6f}\u{1f80}-\u{1faf}\u{1fb2}-\u{1fb4}\u{1fb6}-\u{1fbc}\u{1fc2}-\u{1fc4}\u{1fc6}-\u{1fcc}\u{1fd2}-\u{1fd3}\u{1fd6}-\u{1fdb}\u{1fe2}-\u{1fe4}\u{1fe6}-\u{1fec}\u{1ff2}-\u{1ff4}\u{1ff6}-\u{1ffc}\u{2102}-\u{2103}\u{210b}-\u{210d}\u{2110}-\u{2112}\u{2115}-\u{2116}\u{2119}-\u{211d}\u{2120}-\u{2122}\u{212a}-\u{212d}\u{2130}-\u{2131}\u{213e}-\u{213f}\u{2160}-\u{216f}\u{24b6}-\u{24cf}\u{3380}-\u{3387}\u{338a}-\u{338c}\u{3390}-\u{3394}\u{33a9}-\u{33ac}\u{33b4}-\u{33c1}\u{33c6}-\u{33c9}\u{33cd}-\u{33ce}\u{33d9}-\u{33da}\u{33dc}-\u{33dd}\u{fb00}-\u{fb06}\u{fb13}-\u{fb17}\u{ff21}-\u{ff3a}\u{10400}-\u{10425}\u{1d400}-\u{1d419}\u{1d434}-\u{1d44d}\u{1d468}-\u{1d481}\u{1d49e}-\u{1d49f}\u{1d4a5}-\u{1d4a6}\u{1d4a9}-\u{1d4ac}\u{1d4ae}-\u{1d4b5}\u{1d4d0}-\u{1d4e9}\u{1d504}-\u{1d505}\u{1d507}-\u{1d50a}\u{1d50d}-\u{1d514}\u{1d516}-\u{1d51c}\u{1d538}-\u{1d539}\u{1d53b}-\u{1d53e}\u{1d540}-\u{1d544}\u{1d54a}-\u{1d550}\u{1d56c}-\u{1d585}\u{1d5a0}-\u{1d5b9}\u{1d5d4}-\u{1d5ed}\u{1d608}-\u{1d621}\u{1d63c}-\u{1d655}\u{1d670}-\u{1d689}\u{1d6a8}-\u{1d6c0}\u{1d6e2}-\u{1d6fa}\u{1d71c}-\u{1d734}\u{1d756}-\u{1d76e}\u{1d790}-\u{1d7a8}]/.freeze

    # Mapping for case-folding used with no normalization \StringPrep\[\"B.3\"]
    IN_B_3 = /[\u{00b5 0100 0102 0104 0106 0108 010a 010c 010e 0110 0112 0114 0116 0118 011a 011c 011e 0120 0122 0124 0126 0128 012a 012c 012e 0130 0132 0134 0136 0139 013b 013d 013f 0141 0143 0145 0147 014c 014e 0150 0152 0154 0156 0158 015a 015c 015e 0160 0162 0164 0166 0168 016a 016c 016e 0170 0172 0174 0176 017b 017d 017f 0184 01a2 01a4 01a9 01ac 01b5 01bc 01cd 01cf 01d1 01d3 01d5 01d7 01d9 01db 01de 01e0 01e2 01e4 01e6 01e8 01ea 01ec 01ee 01f4 01fa 01fc 01fe 0200 0202 0204 0206 0208 020a 020c 020e 0210 0212 0214 0216 0218 021a 021c 021e 0220 0222 0224 0226 0228 022a 022c 022e 0230 0232 0345 0386 038c 03b0 03c2 03d8 03da 03dc 03de 03e0 03e2 03e4 03e6 03e8 03ea 03ec 03ee 0460 0462 0464 0466 0468 046a 046c 046e 0470 0472 0474 0476 0478 047a 047c 047e 0480 048a 048c 048e 0490 0492 0494 0496 0498 049a 049c 049e 04a0 04a2 04a4 04a6 04a8 04aa 04ac 04ae 04b0 04b2 04b4 04b6 04b8 04ba 04bc 04be 04c1 04c3 04c5 04c7 04c9 04cb 04cd 04d0 04d2 04d4 04d6 04d8 04da 04dc 04de 04e0 04e2 04e4 04e6 04e8 04ea 04ec 04ee 04f0 04f2 04f4 04f8 0500 0502 0504 0506 0508 050a 050c 050e 0587 1e00 1e02 1e04 1e06 1e08 1e0a 1e0c 1e0e 1e10 1e12 1e14 1e16 1e18 1e1a 1e1c 1e1e 1e20 1e22 1e24 1e26 1e28 1e2a 1e2c 1e2e 1e30 1e32 1e34 1e36 1e38 1e3a 1e3c 1e3e 1e40 1e42 1e44 1e46 1e48 1e4a 1e4c 1e4e 1e50 1e52 1e54 1e56 1e58 1e5a 1e5c 1e5e 1e60 1e62 1e64 1e66 1e68 1e6a 1e6c 1e6e 1e70 1e72 1e74 1e76 1e78 1e7a 1e7c 1e7e 1e80 1e82 1e84 1e86 1e88 1e8a 1e8c 1e8e 1e90 1e92 1e94 1ea0 1ea2 1ea4 1ea6 1ea8 1eaa 1eac 1eae 1eb0 1eb2 1eb4 1eb6 1eb8 1eba 1ebc 1ebe 1ec0 1ec2 1ec4 1ec6 1ec8 1eca 1ecc 1ece 1ed0 1ed2 1ed4 1ed6 1ed8 1eda 1edc 1ede 1ee0 1ee2 1ee4 1ee6 1ee8 1eea 1eec 1eee 1ef0 1ef2 1ef4 1ef6 1ef8 1f50 1f52 1f54 1f56 1f59 1f5b 1f5d 1f5f 1fbe 2126}\u{0041}-\u{005a}\u{00c0}-\u{00d6}\u{00d8}-\u{00df}\u{0149}-\u{014a}\u{0178}-\u{0179}\u{0181}-\u{0182}\u{0186}-\u{0187}\u{0189}-\u{018b}\u{018e}-\u{0191}\u{0193}-\u{0194}\u{0196}-\u{0198}\u{019c}-\u{019d}\u{019f}-\u{01a0}\u{01a6}-\u{01a7}\u{01ae}-\u{01af}\u{01b1}-\u{01b3}\u{01b7}-\u{01b8}\u{01c4}-\u{01c5}\u{01c7}-\u{01c8}\u{01ca}-\u{01cb}\u{01f0}-\u{01f2}\u{01f6}-\u{01f8}\u{0388}-\u{038a}\u{038e}-\u{03a1}\u{03a3}-\u{03ab}\u{03d0}-\u{03d1}\u{03d5}-\u{03d6}\u{03f0}-\u{03f2}\u{03f4}-\u{03f5}\u{0400}-\u{042f}\u{0531}-\u{0556}\u{1e96}-\u{1e9b}\u{1f08}-\u{1f0f}\u{1f18}-\u{1f1d}\u{1f28}-\u{1f2f}\u{1f38}-\u{1f3f}\u{1f48}-\u{1f4d}\u{1f68}-\u{1f6f}\u{1f80}-\u{1faf}\u{1fb2}-\u{1fb4}\u{1fb6}-\u{1fbc}\u{1fc2}-\u{1fc4}\u{1fc6}-\u{1fcc}\u{1fd2}-\u{1fd3}\u{1fd6}-\u{1fdb}\u{1fe2}-\u{1fe4}\u{1fe6}-\u{1fec}\u{1ff2}-\u{1ff4}\u{1ff6}-\u{1ffc}\u{212a}-\u{212b}\u{2160}-\u{216f}\u{24b6}-\u{24cf}\u{fb00}-\u{fb06}\u{fb13}-\u{fb17}\u{ff21}-\u{ff3a}\u{10400}-\u{10425}]/.freeze

    # Replacements for IN_B.1
    MAP_B_1 = "".freeze

    # Replacements for IN_B.2
    MAP_B_2 = {"A"=>"a", "B"=>"b", "C"=>"c", "D"=>"d", "E"=>"e", "F"=>"f", "G"=>"g", "H"=>"h", "I"=>"i", "J"=>"j", "K"=>"k", "L"=>"l", "M"=>"m", "N"=>"n", "O"=>"o", "P"=>"p", "Q"=>"q", "R"=>"r", "S"=>"s", "T"=>"t", "U"=>"u", "V"=>"v", "W"=>"w", "X"=>"x", "Y"=>"y", "Z"=>"z", "µ"=>"μ", "À"=>"à", "Á"=>"á", "Â"=>"â", "Ã"=>"ã", "Ä"=>"ä", "Å"=>"å", "Æ"=>"æ", "Ç"=>"ç", "È"=>"è", "É"=>"é", "Ê"=>"ê", "Ë"=>"ë", "Ì"=>"ì", "Í"=>"í", "Î"=>"î", "Ï"=>"ï", "Ð"=>"ð", "Ñ"=>"ñ", "Ò"=>"ò", "Ó"=>"ó", "Ô"=>"ô", "Õ"=>"õ", "Ö"=>"ö", "Ø"=>"ø", "Ù"=>"ù", "Ú"=>"ú", "Û"=>"û", "Ü"=>"ü", "Ý"=>"ý", "Þ"=>"þ", "ß"=>"ss", "Ā"=>"ā", "Ă"=>"ă", "Ą"=>"ą", "Ć"=>"ć", "Ĉ"=>"ĉ", "Ċ"=>"ċ", "Č"=>"č", "Ď"=>"ď", "Đ"=>"đ", "Ē"=>"ē", "Ĕ"=>"ĕ", "Ė"=>"ė", "Ę"=>"ę", "Ě"=>"ě", "Ĝ"=>"ĝ", "Ğ"=>"ğ", "Ġ"=>"ġ", "Ģ"=>"ģ", "Ĥ"=>"ĥ", "Ħ"=>"ħ", "Ĩ"=>"ĩ", "Ī"=>"ī", "Ĭ"=>"ĭ", "Į"=>"į", "İ"=>"i̇", "Ĳ"=>"ĳ", "Ĵ"=>"ĵ", "Ķ"=>"ķ", "Ĺ"=>"ĺ", "Ļ"=>"ļ", "Ľ"=>"ľ", "Ŀ"=>"ŀ", "Ł"=>"ł", "Ń"=>"ń", "Ņ"=>"ņ", "Ň"=>"ň", "ŉ"=>"ʼn", "Ŋ"=>"ŋ", "Ō"=>"ō", "Ŏ"=>"ŏ", "Ő"=>"ő", "Œ"=>"œ", "Ŕ"=>"ŕ", "Ŗ"=>"ŗ", "Ř"=>"ř", "Ś"=>"ś", "Ŝ"=>"ŝ", "Ş"=>"ş", "Š"=>"š", "Ţ"=>"ţ", "Ť"=>"ť", "Ŧ"=>"ŧ", "Ũ"=>"ũ", "Ū"=>"ū", "Ŭ"=>"ŭ", "Ů"=>"ů", "Ű"=>"ű", "Ų"=>"ų", "Ŵ"=>"ŵ", "Ŷ"=>"ŷ", "Ÿ"=>"ÿ", "Ź"=>"ź", "Ż"=>"ż", "Ž"=>"ž", "ſ"=>"s", "Ɓ"=>"ɓ", "Ƃ"=>"ƃ", "Ƅ"=>"ƅ", "Ɔ"=>"ɔ", "Ƈ"=>"ƈ", "Ɖ"=>"ɖ", "Ɗ"=>"ɗ", "Ƌ"=>"ƌ", "Ǝ"=>"ǝ", "Ə"=>"ə", "Ɛ"=>"ɛ", "Ƒ"=>"ƒ", "Ɠ"=>"ɠ", "Ɣ"=>"ɣ", "Ɩ"=>"ɩ", "Ɨ"=>"ɨ", "Ƙ"=>"ƙ", "Ɯ"=>"ɯ", "Ɲ"=>"ɲ", "Ɵ"=>"ɵ", "Ơ"=>"ơ", "Ƣ"=>"ƣ", "Ƥ"=>"ƥ", "Ʀ"=>"ʀ", "Ƨ"=>"ƨ", "Ʃ"=>"ʃ", "Ƭ"=>"ƭ", "Ʈ"=>"ʈ", "Ư"=>"ư", "Ʊ"=>"ʊ", "Ʋ"=>"ʋ", "Ƴ"=>"ƴ", "Ƶ"=>"ƶ", "Ʒ"=>"ʒ", "Ƹ"=>"ƹ", "Ƽ"=>"ƽ", "Ǆ"=>"ǆ", "ǅ"=>"ǆ", "Ǉ"=>"ǉ", "ǈ"=>"ǉ", "Ǌ"=>"ǌ", "ǋ"=>"ǌ", "Ǎ"=>"ǎ", "Ǐ"=>"ǐ", "Ǒ"=>"ǒ", "Ǔ"=>"ǔ", "Ǖ"=>"ǖ", "Ǘ"=>"ǘ", "Ǚ"=>"ǚ", "Ǜ"=>"ǜ", "Ǟ"=>"ǟ", "Ǡ"=>"ǡ", "Ǣ"=>"ǣ", "Ǥ"=>"ǥ", "Ǧ"=>"ǧ", "Ǩ"=>"ǩ", "Ǫ"=>"ǫ", "Ǭ"=>"ǭ", "Ǯ"=>"ǯ", "ǰ"=>"ǰ", "Ǳ"=>"ǳ", "ǲ"=>"ǳ", "Ǵ"=>"ǵ", "Ƕ"=>"ƕ", "Ƿ"=>"ƿ", "Ǹ"=>"ǹ", "Ǻ"=>"ǻ", "Ǽ"=>"ǽ", "Ǿ"=>"ǿ", "Ȁ"=>"ȁ", "Ȃ"=>"ȃ", "Ȅ"=>"ȅ", "Ȇ"=>"ȇ", "Ȉ"=>"ȉ", "Ȋ"=>"ȋ", "Ȍ"=>"ȍ", "Ȏ"=>"ȏ", "Ȑ"=>"ȑ", "Ȓ"=>"ȓ", "Ȕ"=>"ȕ", "Ȗ"=>"ȗ", "Ș"=>"ș", "Ț"=>"ț", "Ȝ"=>"ȝ", "Ȟ"=>"ȟ", "Ƞ"=>"ƞ", "Ȣ"=>"ȣ", "Ȥ"=>"ȥ", "Ȧ"=>"ȧ", "Ȩ"=>"ȩ", "Ȫ"=>"ȫ", "Ȭ"=>"ȭ", "Ȯ"=>"ȯ", "Ȱ"=>"ȱ", "Ȳ"=>"ȳ", "ͅ"=>"ι", "ͺ"=>" ι", "Ά"=>"ά", "Έ"=>"έ", "Ή"=>"ή", "Ί"=>"ί", "Ό"=>"ό", "Ύ"=>"ύ", "Ώ"=>"ώ", "ΐ"=>"ΐ", "Α"=>"α", "Β"=>"β", "Γ"=>"γ", "Δ"=>"δ", "Ε"=>"ε", "Ζ"=>"ζ", "Η"=>"η", "Θ"=>"θ", "Ι"=>"ι", "Κ"=>"κ", "Λ"=>"λ", "Μ"=>"μ", "Ν"=>"ν", "Ξ"=>"ξ", "Ο"=>"ο", "Π"=>"π", "Ρ"=>"ρ", "Σ"=>"σ", "Τ"=>"τ", "Υ"=>"υ", "Φ"=>"φ", "Χ"=>"χ", "Ψ"=>"ψ", "Ω"=>"ω", "Ϊ"=>"ϊ", "Ϋ"=>"ϋ", "ΰ"=>"ΰ", "ς"=>"σ", "ϐ"=>"β", "ϑ"=>"θ", "ϒ"=>"υ", "ϓ"=>"ύ", "ϔ"=>"ϋ", "ϕ"=>"φ", "ϖ"=>"π", "Ϙ"=>"ϙ", "Ϛ"=>"ϛ", "Ϝ"=>"ϝ", "Ϟ"=>"ϟ", "Ϡ"=>"ϡ", "Ϣ"=>"ϣ", "Ϥ"=>"ϥ", "Ϧ"=>"ϧ", "Ϩ"=>"ϩ", "Ϫ"=>"ϫ", "Ϭ"=>"ϭ", "Ϯ"=>"ϯ", "ϰ"=>"κ", "ϱ"=>"ρ", "ϲ"=>"σ", "ϴ"=>"θ", "ϵ"=>"ε", "Ѐ"=>"ѐ", "Ё"=>"ё", "Ђ"=>"ђ", "Ѓ"=>"ѓ", "Є"=>"є", "Ѕ"=>"ѕ", "І"=>"і", "Ї"=>"ї", "Ј"=>"ј", "Љ"=>"љ", "Њ"=>"њ", "Ћ"=>"ћ", "Ќ"=>"ќ", "Ѝ"=>"ѝ", "Ў"=>"ў", "Џ"=>"џ", "А"=>"а", "Б"=>"б", "В"=>"в", "Г"=>"г", "Д"=>"д", "Е"=>"е", "Ж"=>"ж", "З"=>"з", "И"=>"и", "Й"=>"й", "К"=>"к", "Л"=>"л", "М"=>"м", "Н"=>"н", "О"=>"о", "П"=>"п", "Р"=>"р", "С"=>"с", "Т"=>"т", "У"=>"у", "Ф"=>"ф", "Х"=>"х", "Ц"=>"ц", "Ч"=>"ч", "Ш"=>"ш", "Щ"=>"щ", "Ъ"=>"ъ", "Ы"=>"ы", "Ь"=>"ь", "Э"=>"э", "Ю"=>"ю", "Я"=>"я", "Ѡ"=>"ѡ", "Ѣ"=>"ѣ", "Ѥ"=>"ѥ", "Ѧ"=>"ѧ", "Ѩ"=>"ѩ", "Ѫ"=>"ѫ", "Ѭ"=>"ѭ", "Ѯ"=>"ѯ", "Ѱ"=>"ѱ", "Ѳ"=>"ѳ", "Ѵ"=>"ѵ", "Ѷ"=>"ѷ", "Ѹ"=>"ѹ", "Ѻ"=>"ѻ", "Ѽ"=>"ѽ", "Ѿ"=>"ѿ", "Ҁ"=>"ҁ", "Ҋ"=>"ҋ", "Ҍ"=>"ҍ", "Ҏ"=>"ҏ", "Ґ"=>"ґ", "Ғ"=>"ғ", "Ҕ"=>"ҕ", "Җ"=>"җ", "Ҙ"=>"ҙ", "Қ"=>"қ", "Ҝ"=>"ҝ", "Ҟ"=>"ҟ", "Ҡ"=>"ҡ", "Ң"=>"ң", "Ҥ"=>"ҥ", "Ҧ"=>"ҧ", "Ҩ"=>"ҩ", "Ҫ"=>"ҫ", "Ҭ"=>"ҭ", "Ү"=>"ү", "Ұ"=>"ұ", "Ҳ"=>"ҳ", "Ҵ"=>"ҵ", "Ҷ"=>"ҷ", "Ҹ"=>"ҹ", "Һ"=>"һ", "Ҽ"=>"ҽ", "Ҿ"=>"ҿ", "Ӂ"=>"ӂ", "Ӄ"=>"ӄ", "Ӆ"=>"ӆ", "Ӈ"=>"ӈ", "Ӊ"=>"ӊ", "Ӌ"=>"ӌ", "Ӎ"=>"ӎ", "Ӑ"=>"ӑ", "Ӓ"=>"ӓ", "Ӕ"=>"ӕ", "Ӗ"=>"ӗ", "Ә"=>"ә", "Ӛ"=>"ӛ", "Ӝ"=>"ӝ", "Ӟ"=>"ӟ", "Ӡ"=>"ӡ", "Ӣ"=>"ӣ", "Ӥ"=>"ӥ", "Ӧ"=>"ӧ", "Ө"=>"ө", "Ӫ"=>"ӫ", "Ӭ"=>"ӭ", "Ӯ"=>"ӯ", "Ӱ"=>"ӱ", "Ӳ"=>"ӳ", "Ӵ"=>"ӵ", "Ӹ"=>"ӹ", "Ԁ"=>"ԁ", "Ԃ"=>"ԃ", "Ԅ"=>"ԅ", "Ԇ"=>"ԇ", "Ԉ"=>"ԉ", "Ԋ"=>"ԋ", "Ԍ"=>"ԍ", "Ԏ"=>"ԏ", "Ա"=>"ա", "Բ"=>"բ", "Գ"=>"գ", "Դ"=>"դ", "Ե"=>"ե", "Զ"=>"զ", "Է"=>"է", "Ը"=>"ը", "Թ"=>"թ", "Ժ"=>"ժ", "Ի"=>"ի", "Լ"=>"լ", "Խ"=>"խ", "Ծ"=>"ծ", "Կ"=>"կ", "Հ"=>"հ", "Ձ"=>"ձ", "Ղ"=>"ղ", "Ճ"=>"ճ", "Մ"=>"մ", "Յ"=>"յ", "Ն"=>"ն", "Շ"=>"շ", "Ո"=>"ո", "Չ"=>"չ", "Պ"=>"պ", "Ջ"=>"ջ", "Ռ"=>"ռ", "Ս"=>"ս", "Վ"=>"վ", "Տ"=>"տ", "Ր"=>"ր", "Ց"=>"ց", "Ւ"=>"ւ", "Փ"=>"փ", "Ք"=>"ք", "Օ"=>"օ", "Ֆ"=>"ֆ", "և"=>"եւ", "Ḁ"=>"ḁ", "Ḃ"=>"ḃ", "Ḅ"=>"ḅ", "Ḇ"=>"ḇ", "Ḉ"=>"ḉ", "Ḋ"=>"ḋ", "Ḍ"=>"ḍ", "Ḏ"=>"ḏ", "Ḑ"=>"ḑ", "Ḓ"=>"ḓ", "Ḕ"=>"ḕ", "Ḗ"=>"ḗ", "Ḙ"=>"ḙ", "Ḛ"=>"ḛ", "Ḝ"=>"ḝ", "Ḟ"=>"ḟ", "Ḡ"=>"ḡ", "Ḣ"=>"ḣ", "Ḥ"=>"ḥ", "Ḧ"=>"ḧ", "Ḩ"=>"ḩ", "Ḫ"=>"ḫ", "Ḭ"=>"ḭ", "Ḯ"=>"ḯ", "Ḱ"=>"ḱ", "Ḳ"=>"ḳ", "Ḵ"=>"ḵ", "Ḷ"=>"ḷ", "Ḹ"=>"ḹ", "Ḻ"=>"ḻ", "Ḽ"=>"ḽ", "Ḿ"=>"ḿ", "Ṁ"=>"ṁ", "Ṃ"=>"ṃ", "Ṅ"=>"ṅ", "Ṇ"=>"ṇ", "Ṉ"=>"ṉ", "Ṋ"=>"ṋ", "Ṍ"=>"ṍ", "Ṏ"=>"ṏ", "Ṑ"=>"ṑ", "Ṓ"=>"ṓ", "Ṕ"=>"ṕ", "Ṗ"=>"ṗ", "Ṙ"=>"ṙ", "Ṛ"=>"ṛ", "Ṝ"=>"ṝ", "Ṟ"=>"ṟ", "Ṡ"=>"ṡ", "Ṣ"=>"ṣ", "Ṥ"=>"ṥ", "Ṧ"=>"ṧ", "Ṩ"=>"ṩ", "Ṫ"=>"ṫ", "Ṭ"=>"ṭ", "Ṯ"=>"ṯ", "Ṱ"=>"ṱ", "Ṳ"=>"ṳ", "Ṵ"=>"ṵ", "Ṷ"=>"ṷ", "Ṹ"=>"ṹ", "Ṻ"=>"ṻ", "Ṽ"=>"ṽ", "Ṿ"=>"ṿ", "Ẁ"=>"ẁ", "Ẃ"=>"ẃ", "Ẅ"=>"ẅ", "Ẇ"=>"ẇ", "Ẉ"=>"ẉ", "Ẋ"=>"ẋ", "Ẍ"=>"ẍ", "Ẏ"=>"ẏ", "Ẑ"=>"ẑ", "Ẓ"=>"ẓ", "Ẕ"=>"ẕ", "ẖ"=>"ẖ", "ẗ"=>"ẗ", "ẘ"=>"ẘ", "ẙ"=>"ẙ", "ẚ"=>"aʾ", "ẛ"=>"ṡ", "Ạ"=>"ạ", "Ả"=>"ả", "Ấ"=>"ấ", "Ầ"=>"ầ", "Ẩ"=>"ẩ", "Ẫ"=>"ẫ", "Ậ"=>"ậ", "Ắ"=>"ắ", "Ằ"=>"ằ", "Ẳ"=>"ẳ", "Ẵ"=>"ẵ", "Ặ"=>"ặ", "Ẹ"=>"ẹ", "Ẻ"=>"ẻ", "Ẽ"=>"ẽ", "Ế"=>"ế", "Ề"=>"ề", "Ể"=>"ể", "Ễ"=>"ễ", "Ệ"=>"ệ", "Ỉ"=>"ỉ", "Ị"=>"ị", "Ọ"=>"ọ", "Ỏ"=>"ỏ", "Ố"=>"ố", "Ồ"=>"ồ", "Ổ"=>"ổ", "Ỗ"=>"ỗ", "Ộ"=>"ộ", "Ớ"=>"ớ", "Ờ"=>"ờ", "Ở"=>"ở", "Ỡ"=>"ỡ", "Ợ"=>"ợ", "Ụ"=>"ụ", "Ủ"=>"ủ", "Ứ"=>"ứ", "Ừ"=>"ừ", "Ử"=>"ử", "Ữ"=>"ữ", "Ự"=>"ự", "Ỳ"=>"ỳ", "Ỵ"=>"ỵ", "Ỷ"=>"ỷ", "Ỹ"=>"ỹ", "Ἀ"=>"ἀ", "Ἁ"=>"ἁ", "Ἂ"=>"ἂ", "Ἃ"=>"ἃ", "Ἄ"=>"ἄ", "Ἅ"=>"ἅ", "Ἆ"=>"ἆ", "Ἇ"=>"ἇ", "Ἐ"=>"ἐ", "Ἑ"=>"ἑ", "Ἒ"=>"ἒ", "Ἓ"=>"ἓ", "Ἔ"=>"ἔ", "Ἕ"=>"ἕ", "Ἠ"=>"ἠ", "Ἡ"=>"ἡ", "Ἢ"=>"ἢ", "Ἣ"=>"ἣ", "Ἤ"=>"ἤ", "Ἥ"=>"ἥ", "Ἦ"=>"ἦ", "Ἧ"=>"ἧ", "Ἰ"=>"ἰ", "Ἱ"=>"ἱ", "Ἲ"=>"ἲ", "Ἳ"=>"ἳ", "Ἴ"=>"ἴ", "Ἵ"=>"ἵ", "Ἶ"=>"ἶ", "Ἷ"=>"ἷ", "Ὀ"=>"ὀ", "Ὁ"=>"ὁ", "Ὂ"=>"ὂ", "Ὃ"=>"ὃ", "Ὄ"=>"ὄ", "Ὅ"=>"ὅ", "ὐ"=>"ὐ", "ὒ"=>"ὒ", "ὔ"=>"ὔ", "ὖ"=>"ὖ", "Ὑ"=>"ὑ", "Ὓ"=>"ὓ", "Ὕ"=>"ὕ", "Ὗ"=>"ὗ", "Ὠ"=>"ὠ", "Ὡ"=>"ὡ", "Ὢ"=>"ὢ", "Ὣ"=>"ὣ", "Ὤ"=>"ὤ", "Ὥ"=>"ὥ", "Ὦ"=>"ὦ", "Ὧ"=>"ὧ", "ᾀ"=>"ἀι", "ᾁ"=>"ἁι", "ᾂ"=>"ἂι", "ᾃ"=>"ἃι", "ᾄ"=>"ἄι", "ᾅ"=>"ἅι", "ᾆ"=>"ἆι", "ᾇ"=>"ἇι", "ᾈ"=>"ἀι", "ᾉ"=>"ἁι", "ᾊ"=>"ἂι", "ᾋ"=>"ἃι", "ᾌ"=>"ἄι", "ᾍ"=>"ἅι", "ᾎ"=>"ἆι", "ᾏ"=>"ἇι", "ᾐ"=>"ἠι", "ᾑ"=>"ἡι", "ᾒ"=>"ἢι", "ᾓ"=>"ἣι", "ᾔ"=>"ἤι", "ᾕ"=>"ἥι", "ᾖ"=>"ἦι", "ᾗ"=>"ἧι", "ᾘ"=>"ἠι", "ᾙ"=>"ἡι", "ᾚ"=>"ἢι", "ᾛ"=>"ἣι", "ᾜ"=>"ἤι", "ᾝ"=>"ἥι", "ᾞ"=>"ἦι", "ᾟ"=>"ἧι", "ᾠ"=>"ὠι", "ᾡ"=>"ὡι", "ᾢ"=>"ὢι", "ᾣ"=>"ὣι", "ᾤ"=>"ὤι", "ᾥ"=>"ὥι", "ᾦ"=>"ὦι", "ᾧ"=>"ὧι", "ᾨ"=>"ὠι", "ᾩ"=>"ὡι", "ᾪ"=>"ὢι", "ᾫ"=>"ὣι", "ᾬ"=>"ὤι", "ᾭ"=>"ὥι", "ᾮ"=>"ὦι", "ᾯ"=>"ὧι", "ᾲ"=>"ὰι", "ᾳ"=>"αι", "ᾴ"=>"άι", "ᾶ"=>"ᾶ", "ᾷ"=>"ᾶι", "Ᾰ"=>"ᾰ", "Ᾱ"=>"ᾱ", "Ὰ"=>"ὰ", "Ά"=>"ά", "ᾼ"=>"αι", "ι"=>"ι", "ῂ"=>"ὴι", "ῃ"=>"ηι", "ῄ"=>"ήι", "ῆ"=>"ῆ", "ῇ"=>"ῆι", "Ὲ"=>"ὲ", "Έ"=>"έ", "Ὴ"=>"ὴ", "Ή"=>"ή", "ῌ"=>"ηι", "ῒ"=>"ῒ", "ΐ"=>"ΐ", "ῖ"=>"ῖ", "ῗ"=>"ῗ", "Ῐ"=>"ῐ", "Ῑ"=>"ῑ", "Ὶ"=>"ὶ", "Ί"=>"ί", "ῢ"=>"ῢ", "ΰ"=>"ΰ", "ῤ"=>"ῤ", "ῦ"=>"ῦ", "ῧ"=>"ῧ", "Ῠ"=>"ῠ", "Ῡ"=>"ῡ", "Ὺ"=>"ὺ", "Ύ"=>"ύ", "Ῥ"=>"ῥ", "ῲ"=>"ὼι", "ῳ"=>"ωι", "ῴ"=>"ώι", "ῶ"=>"ῶ", "ῷ"=>"ῶι", "Ὸ"=>"ὸ", "Ό"=>"ό", "Ὼ"=>"ὼ", "Ώ"=>"ώ", "ῼ"=>"ωι", "₨"=>"rs", "ℂ"=>"c", "℃"=>"°c", "ℇ"=>"ɛ", "℉"=>"°f", "ℋ"=>"h", "ℌ"=>"h", "ℍ"=>"h", "ℐ"=>"i", "ℑ"=>"i", "ℒ"=>"l", "ℕ"=>"n", "№"=>"no", "ℙ"=>"p", "ℚ"=>"q", "ℛ"=>"r", "ℜ"=>"r", "ℝ"=>"r", "℠"=>"sm", "℡"=>"tel", "™"=>"tm", "ℤ"=>"z", "Ω"=>"ω", "ℨ"=>"z", "K"=>"k", "Å"=>"å", "ℬ"=>"b", "ℭ"=>"c", "ℰ"=>"e", "ℱ"=>"f", "ℳ"=>"m", "ℾ"=>"γ", "ℿ"=>"π", "ⅅ"=>"d", "Ⅰ"=>"ⅰ", "Ⅱ"=>"ⅱ", "Ⅲ"=>"ⅲ", "Ⅳ"=>"ⅳ", "Ⅴ"=>"ⅴ", "Ⅵ"=>"ⅵ", "Ⅶ"=>"ⅶ", "Ⅷ"=>"ⅷ", "Ⅸ"=>"ⅸ", "Ⅹ"=>"ⅹ", "Ⅺ"=>"ⅺ", "Ⅻ"=>"ⅻ", "Ⅼ"=>"ⅼ", "Ⅽ"=>"ⅽ", "Ⅾ"=>"ⅾ", "Ⅿ"=>"ⅿ", "Ⓐ"=>"ⓐ", "Ⓑ"=>"ⓑ", "Ⓒ"=>"ⓒ", "Ⓓ"=>"ⓓ", "Ⓔ"=>"ⓔ", "Ⓕ"=>"ⓕ", "Ⓖ"=>"ⓖ", "Ⓗ"=>"ⓗ", "Ⓘ"=>"ⓘ", "Ⓙ"=>"ⓙ", "Ⓚ"=>"ⓚ", "Ⓛ"=>"ⓛ", "Ⓜ"=>"ⓜ", "Ⓝ"=>"ⓝ", "Ⓞ"=>"ⓞ", "Ⓟ"=>"ⓟ", "Ⓠ"=>"ⓠ", "Ⓡ"=>"ⓡ", "Ⓢ"=>"ⓢ", "Ⓣ"=>"ⓣ", "Ⓤ"=>"ⓤ", "Ⓥ"=>"ⓥ", "Ⓦ"=>"ⓦ", "Ⓧ"=>"ⓧ", "Ⓨ"=>"ⓨ", "Ⓩ"=>"ⓩ", "㍱"=>"hpa", "㍳"=>"au", "㍵"=>"ov", "㎀"=>"pa", "㎁"=>"na", "㎂"=>"μa", "㎃"=>"ma", "㎄"=>"ka", "㎅"=>"kb", "㎆"=>"mb", "㎇"=>"gb", "㎊"=>"pf", "㎋"=>"nf", "㎌"=>"μf", "㎐"=>"hz", "㎑"=>"khz", "㎒"=>"mhz", "㎓"=>"ghz", "㎔"=>"thz", "㎩"=>"pa", "㎪"=>"kpa", "㎫"=>"mpa", "㎬"=>"gpa", "㎴"=>"pv", "㎵"=>"nv", "㎶"=>"μv", "㎷"=>"mv", "㎸"=>"kv", "㎹"=>"mv", "㎺"=>"pw", "㎻"=>"nw", "㎼"=>"μw", "㎽"=>"mw", "㎾"=>"kw", "㎿"=>"mw", "㏀"=>"kω", "㏁"=>"mω", "㏃"=>"bq", "㏆"=>"c∕kg", "㏇"=>"co.", "㏈"=>"db", "㏉"=>"gy", "㏋"=>"hp", "㏍"=>"kk", "㏎"=>"km", "㏗"=>"ph", "㏙"=>"ppm", "㏚"=>"pr", "㏜"=>"sv", "㏝"=>"wb", "ﬀ"=>"ff", "ﬁ"=>"fi", "ﬂ"=>"fl", "ﬃ"=>"ffi", "ﬄ"=>"ffl", "ﬅ"=>"st", "ﬆ"=>"st", "ﬓ"=>"մն", "ﬔ"=>"մե", "ﬕ"=>"մի", "ﬖ"=>"վն", "ﬗ"=>"մխ", "Ａ"=>"ａ", "Ｂ"=>"ｂ", "Ｃ"=>"ｃ", "Ｄ"=>"ｄ", "Ｅ"=>"ｅ", "Ｆ"=>"ｆ", "Ｇ"=>"ｇ", "Ｈ"=>"ｈ", "Ｉ"=>"ｉ", "Ｊ"=>"ｊ", "Ｋ"=>"ｋ", "Ｌ"=>"ｌ", "Ｍ"=>"ｍ", "Ｎ"=>"ｎ", "Ｏ"=>"ｏ", "Ｐ"=>"ｐ", "Ｑ"=>"ｑ", "Ｒ"=>"ｒ", "Ｓ"=>"ｓ", "Ｔ"=>"ｔ", "Ｕ"=>"ｕ", "Ｖ"=>"ｖ", "Ｗ"=>"ｗ", "Ｘ"=>"ｘ", "Ｙ"=>"ｙ", "Ｚ"=>"ｚ", "𐐀"=>"𐐨", "𐐁"=>"𐐩", "𐐂"=>"𐐪", "𐐃"=>"𐐫", "𐐄"=>"𐐬", "𐐅"=>"𐐭", "𐐆"=>"𐐮", "𐐇"=>"𐐯", "𐐈"=>"𐐰", "𐐉"=>"𐐱", "𐐊"=>"𐐲", "𐐋"=>"𐐳", "𐐌"=>"𐐴", "𐐍"=>"𐐵", "𐐎"=>"𐐶", "𐐏"=>"𐐷", "𐐐"=>"𐐸", "𐐑"=>"𐐹", "𐐒"=>"𐐺", "𐐓"=>"𐐻", "𐐔"=>"𐐼", "𐐕"=>"𐐽", "𐐖"=>"𐐾", "𐐗"=>"𐐿", "𐐘"=>"𐑀", "𐐙"=>"𐑁", "𐐚"=>"𐑂", "𐐛"=>"𐑃", "𐐜"=>"𐑄", "𐐝"=>"𐑅", "𐐞"=>"𐑆", "𐐟"=>"𐑇", "𐐠"=>"𐑈", "𐐡"=>"𐑉", "𐐢"=>"𐑊", "𐐣"=>"𐑋", "𐐤"=>"𐑌", "𐐥"=>"𐑍", "𝐀"=>"a", "𝐁"=>"b", "𝐂"=>"c", "𝐃"=>"d", "𝐄"=>"e", "𝐅"=>"f", "𝐆"=>"g", "𝐇"=>"h", "𝐈"=>"i", "𝐉"=>"j", "𝐊"=>"k", "𝐋"=>"l", "𝐌"=>"m", "𝐍"=>"n", "𝐎"=>"o", "𝐏"=>"p", "𝐐"=>"q", "𝐑"=>"r", "𝐒"=>"s", "𝐓"=>"t", "𝐔"=>"u", "𝐕"=>"v", "𝐖"=>"w", "𝐗"=>"x", "𝐘"=>"y", "𝐙"=>"z", "𝐴"=>"a", "𝐵"=>"b", "𝐶"=>"c", "𝐷"=>"d", "𝐸"=>"e", "𝐹"=>"f", "𝐺"=>"g", "𝐻"=>"h", "𝐼"=>"i", "𝐽"=>"j", "𝐾"=>"k", "𝐿"=>"l", "𝑀"=>"m", "𝑁"=>"n", "𝑂"=>"o", "𝑃"=>"p", "𝑄"=>"q", "𝑅"=>"r", "𝑆"=>"s", "𝑇"=>"t", "𝑈"=>"u", "𝑉"=>"v", "𝑊"=>"w", "𝑋"=>"x", "𝑌"=>"y", "𝑍"=>"z", "𝑨"=>"a", "𝑩"=>"b", "𝑪"=>"c", "𝑫"=>"d", "𝑬"=>"e", "𝑭"=>"f", "𝑮"=>"g", "𝑯"=>"h", "𝑰"=>"i", "𝑱"=>"j", "𝑲"=>"k", "𝑳"=>"l", "𝑴"=>"m", "𝑵"=>"n", "𝑶"=>"o", "𝑷"=>"p", "𝑸"=>"q", "𝑹"=>"r", "𝑺"=>"s", "𝑻"=>"t", "𝑼"=>"u", "𝑽"=>"v", "𝑾"=>"w", "𝑿"=>"x", "𝒀"=>"y", "𝒁"=>"z", "𝒜"=>"a", "𝒞"=>"c", "𝒟"=>"d", "𝒢"=>"g", "𝒥"=>"j", "𝒦"=>"k", "𝒩"=>"n", "𝒪"=>"o", "𝒫"=>"p", "𝒬"=>"q", "𝒮"=>"s", "𝒯"=>"t", "𝒰"=>"u", "𝒱"=>"v", "𝒲"=>"w", "𝒳"=>"x", "𝒴"=>"y", "𝒵"=>"z", "𝓐"=>"a", "𝓑"=>"b", "𝓒"=>"c", "𝓓"=>"d", "𝓔"=>"e", "𝓕"=>"f", "𝓖"=>"g", "𝓗"=>"h", "𝓘"=>"i", "𝓙"=>"j", "𝓚"=>"k", "𝓛"=>"l", "𝓜"=>"m", "𝓝"=>"n", "𝓞"=>"o", "𝓟"=>"p", "𝓠"=>"q", "𝓡"=>"r", "𝓢"=>"s", "𝓣"=>"t", "𝓤"=>"u", "𝓥"=>"v", "𝓦"=>"w", "𝓧"=>"x", "𝓨"=>"y", "𝓩"=>"z", "𝔄"=>"a", "𝔅"=>"b", "𝔇"=>"d", "𝔈"=>"e", "𝔉"=>"f", "𝔊"=>"g", "𝔍"=>"j", "𝔎"=>"k", "𝔏"=>"l", "𝔐"=>"m", "𝔑"=>"n", "𝔒"=>"o", "𝔓"=>"p", "𝔔"=>"q", "𝔖"=>"s", "𝔗"=>"t", "𝔘"=>"u", "𝔙"=>"v", "𝔚"=>"w", "𝔛"=>"x", "𝔜"=>"y", "𝔸"=>"a", "𝔹"=>"b", "𝔻"=>"d", "𝔼"=>"e", "𝔽"=>"f", "𝔾"=>"g", "𝕀"=>"i", "𝕁"=>"j", "𝕂"=>"k", "𝕃"=>"l", "𝕄"=>"m", "𝕆"=>"o", "𝕊"=>"s", "𝕋"=>"t", "𝕌"=>"u", "𝕍"=>"v", "𝕎"=>"w", "𝕏"=>"x", "𝕐"=>"y", "𝕬"=>"a", "𝕭"=>"b", "𝕮"=>"c", "𝕯"=>"d", "𝕰"=>"e", "𝕱"=>"f", "𝕲"=>"g", "𝕳"=>"h", "𝕴"=>"i", "𝕵"=>"j", "𝕶"=>"k", "𝕷"=>"l", "𝕸"=>"m", "𝕹"=>"n", "𝕺"=>"o", "𝕻"=>"p", "𝕼"=>"q", "𝕽"=>"r", "𝕾"=>"s", "𝕿"=>"t", "𝖀"=>"u", "𝖁"=>"v", "𝖂"=>"w", "𝖃"=>"x", "𝖄"=>"y", "𝖅"=>"z", "𝖠"=>"a", "𝖡"=>"b", "𝖢"=>"c", "𝖣"=>"d", "𝖤"=>"e", "𝖥"=>"f", "𝖦"=>"g", "𝖧"=>"h", "𝖨"=>"i", "𝖩"=>"j", "𝖪"=>"k", "𝖫"=>"l", "𝖬"=>"m", "𝖭"=>"n", "𝖮"=>"o", "𝖯"=>"p", "𝖰"=>"q", "𝖱"=>"r", "𝖲"=>"s", "𝖳"=>"t", "𝖴"=>"u", "𝖵"=>"v", "𝖶"=>"w", "𝖷"=>"x", "𝖸"=>"y", "𝖹"=>"z", "𝗔"=>"a", "𝗕"=>"b", "𝗖"=>"c", "𝗗"=>"d", "𝗘"=>"e", "𝗙"=>"f", "𝗚"=>"g", "𝗛"=>"h", "𝗜"=>"i", "𝗝"=>"j", "𝗞"=>"k", "𝗟"=>"l", "𝗠"=>"m", "𝗡"=>"n", "𝗢"=>"o", "𝗣"=>"p", "𝗤"=>"q", "𝗥"=>"r", "𝗦"=>"s", "𝗧"=>"t", "𝗨"=>"u", "𝗩"=>"v", "𝗪"=>"w", "𝗫"=>"x", "𝗬"=>"y", "𝗭"=>"z", "𝘈"=>"a", "𝘉"=>"b", "𝘊"=>"c", "𝘋"=>"d", "𝘌"=>"e", "𝘍"=>"f", "𝘎"=>"g", "𝘏"=>"h", "𝘐"=>"i", "𝘑"=>"j", "𝘒"=>"k", "𝘓"=>"l", "𝘔"=>"m", "𝘕"=>"n", "𝘖"=>"o", "𝘗"=>"p", "𝘘"=>"q", "𝘙"=>"r", "𝘚"=>"s", "𝘛"=>"t", "𝘜"=>"u", "𝘝"=>"v", "𝘞"=>"w", "𝘟"=>"x", "𝘠"=>"y", "𝘡"=>"z", "𝘼"=>"a", "𝘽"=>"b", "𝘾"=>"c", "𝘿"=>"d", "𝙀"=>"e", "𝙁"=>"f", "𝙂"=>"g", "𝙃"=>"h", "𝙄"=>"i", "𝙅"=>"j", "𝙆"=>"k", "𝙇"=>"l", "𝙈"=>"m", "𝙉"=>"n", "𝙊"=>"o", "𝙋"=>"p", "𝙌"=>"q", "𝙍"=>"r", "𝙎"=>"s", "𝙏"=>"t", "𝙐"=>"u", "𝙑"=>"v", "𝙒"=>"w", "𝙓"=>"x", "𝙔"=>"y", "𝙕"=>"z", "𝙰"=>"a", "𝙱"=>"b", "𝙲"=>"c", "𝙳"=>"d", "𝙴"=>"e", "𝙵"=>"f", "𝙶"=>"g", "𝙷"=>"h", "𝙸"=>"i", "𝙹"=>"j", "𝙺"=>"k", "𝙻"=>"l", "𝙼"=>"m", "𝙽"=>"n", "𝙾"=>"o", "𝙿"=>"p", "𝚀"=>"q", "𝚁"=>"r", "𝚂"=>"s", "𝚃"=>"t", "𝚄"=>"u", "𝚅"=>"v", "𝚆"=>"w", "𝚇"=>"x", "𝚈"=>"y", "𝚉"=>"z", "𝚨"=>"α", "𝚩"=>"β", "𝚪"=>"γ", "𝚫"=>"δ", "𝚬"=>"ε", "𝚭"=>"ζ", "𝚮"=>"η", "𝚯"=>"θ", "𝚰"=>"ι", "𝚱"=>"κ", "𝚲"=>"λ", "𝚳"=>"μ", "𝚴"=>"ν", "𝚵"=>"ξ", "𝚶"=>"ο", "𝚷"=>"π", "𝚸"=>"ρ", "𝚹"=>"θ", "𝚺"=>"σ", "𝚻"=>"τ", "𝚼"=>"υ", "𝚽"=>"φ", "𝚾"=>"χ", "𝚿"=>"ψ", "𝛀"=>"ω", "𝛓"=>"σ", "𝛢"=>"α", "𝛣"=>"β", "𝛤"=>"γ", "𝛥"=>"δ", "𝛦"=>"ε", "𝛧"=>"ζ", "𝛨"=>"η", "𝛩"=>"θ", "𝛪"=>"ι", "𝛫"=>"κ", "𝛬"=>"λ", "𝛭"=>"μ", "𝛮"=>"ν", "𝛯"=>"ξ", "𝛰"=>"ο", "𝛱"=>"π", "𝛲"=>"ρ", "𝛳"=>"θ", "𝛴"=>"σ", "𝛵"=>"τ", "𝛶"=>"υ", "𝛷"=>"φ", "𝛸"=>"χ", "𝛹"=>"ψ", "𝛺"=>"ω", "𝜍"=>"σ", "𝜜"=>"α", "𝜝"=>"β", "𝜞"=>"γ", "𝜟"=>"δ", "𝜠"=>"ε", "𝜡"=>"ζ", "𝜢"=>"η", "𝜣"=>"θ", "𝜤"=>"ι", "𝜥"=>"κ", "𝜦"=>"λ", "𝜧"=>"μ", "𝜨"=>"ν", "𝜩"=>"ξ", "𝜪"=>"ο", "𝜫"=>"π", "𝜬"=>"ρ", "𝜭"=>"θ", "𝜮"=>"σ", "𝜯"=>"τ", "𝜰"=>"υ", "𝜱"=>"φ", "𝜲"=>"χ", "𝜳"=>"ψ", "𝜴"=>"ω", "𝝇"=>"σ", "𝝖"=>"α", "𝝗"=>"β", "𝝘"=>"γ", "𝝙"=>"δ", "𝝚"=>"ε", "𝝛"=>"ζ", "𝝜"=>"η", "𝝝"=>"θ", "𝝞"=>"ι", "𝝟"=>"κ", "𝝠"=>"λ", "𝝡"=>"μ", "𝝢"=>"ν", "𝝣"=>"ξ", "𝝤"=>"ο", "𝝥"=>"π", "𝝦"=>"ρ", "𝝧"=>"θ", "𝝨"=>"σ", "𝝩"=>"τ", "𝝪"=>"υ", "𝝫"=>"φ", "𝝬"=>"χ", "𝝭"=>"ψ", "𝝮"=>"ω", "𝞁"=>"σ", "𝞐"=>"α", "𝞑"=>"β", "𝞒"=>"γ", "𝞓"=>"δ", "𝞔"=>"ε", "𝞕"=>"ζ", "𝞖"=>"η", "𝞗"=>"θ", "𝞘"=>"ι", "𝞙"=>"κ", "𝞚"=>"λ", "𝞛"=>"μ", "𝞜"=>"ν", "𝞝"=>"ξ", "𝞞"=>"ο", "𝞟"=>"π", "𝞠"=>"ρ", "𝞡"=>"θ", "𝞢"=>"σ", "𝞣"=>"τ", "𝞤"=>"υ", "𝞥"=>"φ", "𝞦"=>"χ", "𝞧"=>"ψ", "𝞨"=>"ω", "𝞻"=>"σ"}.freeze

    # Replacements for IN_B.3
    MAP_B_3 = {"A"=>"a", "B"=>"b", "C"=>"c", "D"=>"d", "E"=>"e", "F"=>"f", "G"=>"g", "H"=>"h", "I"=>"i", "J"=>"j", "K"=>"k", "L"=>"l", "M"=>"m", "N"=>"n", "O"=>"o", "P"=>"p", "Q"=>"q", "R"=>"r", "S"=>"s", "T"=>"t", "U"=>"u", "V"=>"v", "W"=>"w", "X"=>"x", "Y"=>"y", "Z"=>"z", "µ"=>"μ", "À"=>"à", "Á"=>"á", "Â"=>"â", "Ã"=>"ã", "Ä"=>"ä", "Å"=>"å", "Æ"=>"æ", "Ç"=>"ç", "È"=>"è", "É"=>"é", "Ê"=>"ê", "Ë"=>"ë", "Ì"=>"ì", "Í"=>"í", "Î"=>"î", "Ï"=>"ï", "Ð"=>"ð", "Ñ"=>"ñ", "Ò"=>"ò", "Ó"=>"ó", "Ô"=>"ô", "Õ"=>"õ", "Ö"=>"ö", "Ø"=>"ø", "Ù"=>"ù", "Ú"=>"ú", "Û"=>"û", "Ü"=>"ü", "Ý"=>"ý", "Þ"=>"þ", "ß"=>"ss", "Ā"=>"ā", "Ă"=>"ă", "Ą"=>"ą", "Ć"=>"ć", "Ĉ"=>"ĉ", "Ċ"=>"ċ", "Č"=>"č", "Ď"=>"ď", "Đ"=>"đ", "Ē"=>"ē", "Ĕ"=>"ĕ", "Ė"=>"ė", "Ę"=>"ę", "Ě"=>"ě", "Ĝ"=>"ĝ", "Ğ"=>"ğ", "Ġ"=>"ġ", "Ģ"=>"ģ", "Ĥ"=>"ĥ", "Ħ"=>"ħ", "Ĩ"=>"ĩ", "Ī"=>"ī", "Ĭ"=>"ĭ", "Į"=>"į", "İ"=>"i̇", "Ĳ"=>"ĳ", "Ĵ"=>"ĵ", "Ķ"=>"ķ", "Ĺ"=>"ĺ", "Ļ"=>"ļ", "Ľ"=>"ľ", "Ŀ"=>"ŀ", "Ł"=>"ł", "Ń"=>"ń", "Ņ"=>"ņ", "Ň"=>"ň", "ŉ"=>"ʼn", "Ŋ"=>"ŋ", "Ō"=>"ō", "Ŏ"=>"ŏ", "Ő"=>"ő", "Œ"=>"œ", "Ŕ"=>"ŕ", "Ŗ"=>"ŗ", "Ř"=>"ř", "Ś"=>"ś", "Ŝ"=>"ŝ", "Ş"=>"ş", "Š"=>"š", "Ţ"=>"ţ", "Ť"=>"ť", "Ŧ"=>"ŧ", "Ũ"=>"ũ", "Ū"=>"ū", "Ŭ"=>"ŭ", "Ů"=>"ů", "Ű"=>"ű", "Ų"=>"ų", "Ŵ"=>"ŵ", "Ŷ"=>"ŷ", "Ÿ"=>"ÿ", "Ź"=>"ź", "Ż"=>"ż", "Ž"=>"ž", "ſ"=>"s", "Ɓ"=>"ɓ", "Ƃ"=>"ƃ", "Ƅ"=>"ƅ", "Ɔ"=>"ɔ", "Ƈ"=>"ƈ", "Ɖ"=>"ɖ", "Ɗ"=>"ɗ", "Ƌ"=>"ƌ", "Ǝ"=>"ǝ", "Ə"=>"ə", "Ɛ"=>"ɛ", "Ƒ"=>"ƒ", "Ɠ"=>"ɠ", "Ɣ"=>"ɣ", "Ɩ"=>"ɩ", "Ɨ"=>"ɨ", "Ƙ"=>"ƙ", "Ɯ"=>"ɯ", "Ɲ"=>"ɲ", "Ɵ"=>"ɵ", "Ơ"=>"ơ", "Ƣ"=>"ƣ", "Ƥ"=>"ƥ", "Ʀ"=>"ʀ", "Ƨ"=>"ƨ", "Ʃ"=>"ʃ", "Ƭ"=>"ƭ", "Ʈ"=>"ʈ", "Ư"=>"ư", "Ʊ"=>"ʊ", "Ʋ"=>"ʋ", "Ƴ"=>"ƴ", "Ƶ"=>"ƶ", "Ʒ"=>"ʒ", "Ƹ"=>"ƹ", "Ƽ"=>"ƽ", "Ǆ"=>"ǆ", "ǅ"=>"ǆ", "Ǉ"=>"ǉ", "ǈ"=>"ǉ", "Ǌ"=>"ǌ", "ǋ"=>"ǌ", "Ǎ"=>"ǎ", "Ǐ"=>"ǐ", "Ǒ"=>"ǒ", "Ǔ"=>"ǔ", "Ǖ"=>"ǖ", "Ǘ"=>"ǘ", "Ǚ"=>"ǚ", "Ǜ"=>"ǜ", "Ǟ"=>"ǟ", "Ǡ"=>"ǡ", "Ǣ"=>"ǣ", "Ǥ"=>"ǥ", "Ǧ"=>"ǧ", "Ǩ"=>"ǩ", "Ǫ"=>"ǫ", "Ǭ"=>"ǭ", "Ǯ"=>"ǯ", "ǰ"=>"ǰ", "Ǳ"=>"ǳ", "ǲ"=>"ǳ", "Ǵ"=>"ǵ", "Ƕ"=>"ƕ", "Ƿ"=>"ƿ", "Ǹ"=>"ǹ", "Ǻ"=>"ǻ", "Ǽ"=>"ǽ", "Ǿ"=>"ǿ", "Ȁ"=>"ȁ", "Ȃ"=>"ȃ", "Ȅ"=>"ȅ", "Ȇ"=>"ȇ", "Ȉ"=>"ȉ", "Ȋ"=>"ȋ", "Ȍ"=>"ȍ", "Ȏ"=>"ȏ", "Ȑ"=>"ȑ", "Ȓ"=>"ȓ", "Ȕ"=>"ȕ", "Ȗ"=>"ȗ", "Ș"=>"ș", "Ț"=>"ț", "Ȝ"=>"ȝ", "Ȟ"=>"ȟ", "Ƞ"=>"ƞ", "Ȣ"=>"ȣ", "Ȥ"=>"ȥ", "Ȧ"=>"ȧ", "Ȩ"=>"ȩ", "Ȫ"=>"ȫ", "Ȭ"=>"ȭ", "Ȯ"=>"ȯ", "Ȱ"=>"ȱ", "Ȳ"=>"ȳ", "ͅ"=>"ι", "Ά"=>"ά", "Έ"=>"έ", "Ή"=>"ή", "Ί"=>"ί", "Ό"=>"ό", "Ύ"=>"ύ", "Ώ"=>"ώ", "ΐ"=>"ΐ", "Α"=>"α", "Β"=>"β", "Γ"=>"γ", "Δ"=>"δ", "Ε"=>"ε", "Ζ"=>"ζ", "Η"=>"η", "Θ"=>"θ", "Ι"=>"ι", "Κ"=>"κ", "Λ"=>"λ", "Μ"=>"μ", "Ν"=>"ν", "Ξ"=>"ξ", "Ο"=>"ο", "Π"=>"π", "Ρ"=>"ρ", "Σ"=>"σ", "Τ"=>"τ", "Υ"=>"υ", "Φ"=>"φ", "Χ"=>"χ", "Ψ"=>"ψ", "Ω"=>"ω", "Ϊ"=>"ϊ", "Ϋ"=>"ϋ", "ΰ"=>"ΰ", "ς"=>"σ", "ϐ"=>"β", "ϑ"=>"θ", "ϕ"=>"φ", "ϖ"=>"π", "Ϙ"=>"ϙ", "Ϛ"=>"ϛ", "Ϝ"=>"ϝ", "Ϟ"=>"ϟ", "Ϡ"=>"ϡ", "Ϣ"=>"ϣ", "Ϥ"=>"ϥ", "Ϧ"=>"ϧ", "Ϩ"=>"ϩ", "Ϫ"=>"ϫ", "Ϭ"=>"ϭ", "Ϯ"=>"ϯ", "ϰ"=>"κ", "ϱ"=>"ρ", "ϲ"=>"σ", "ϴ"=>"θ", "ϵ"=>"ε", "Ѐ"=>"ѐ", "Ё"=>"ё", "Ђ"=>"ђ", "Ѓ"=>"ѓ", "Є"=>"є", "Ѕ"=>"ѕ", "І"=>"і", "Ї"=>"ї", "Ј"=>"ј", "Љ"=>"љ", "Њ"=>"њ", "Ћ"=>"ћ", "Ќ"=>"ќ", "Ѝ"=>"ѝ", "Ў"=>"ў", "Џ"=>"џ", "А"=>"а", "Б"=>"б", "В"=>"в", "Г"=>"г", "Д"=>"д", "Е"=>"е", "Ж"=>"ж", "З"=>"з", "И"=>"и", "Й"=>"й", "К"=>"к", "Л"=>"л", "М"=>"м", "Н"=>"н", "О"=>"о", "П"=>"п", "Р"=>"р", "С"=>"с", "Т"=>"т", "У"=>"у", "Ф"=>"ф", "Х"=>"х", "Ц"=>"ц", "Ч"=>"ч", "Ш"=>"ш", "Щ"=>"щ", "Ъ"=>"ъ", "Ы"=>"ы", "Ь"=>"ь", "Э"=>"э", "Ю"=>"ю", "Я"=>"я", "Ѡ"=>"ѡ", "Ѣ"=>"ѣ", "Ѥ"=>"ѥ", "Ѧ"=>"ѧ", "Ѩ"=>"ѩ", "Ѫ"=>"ѫ", "Ѭ"=>"ѭ", "Ѯ"=>"ѯ", "Ѱ"=>"ѱ", "Ѳ"=>"ѳ", "Ѵ"=>"ѵ", "Ѷ"=>"ѷ", "Ѹ"=>"ѹ", "Ѻ"=>"ѻ", "Ѽ"=>"ѽ", "Ѿ"=>"ѿ", "Ҁ"=>"ҁ", "Ҋ"=>"ҋ", "Ҍ"=>"ҍ", "Ҏ"=>"ҏ", "Ґ"=>"ґ", "Ғ"=>"ғ", "Ҕ"=>"ҕ", "Җ"=>"җ", "Ҙ"=>"ҙ", "Қ"=>"қ", "Ҝ"=>"ҝ", "Ҟ"=>"ҟ", "Ҡ"=>"ҡ", "Ң"=>"ң", "Ҥ"=>"ҥ", "Ҧ"=>"ҧ", "Ҩ"=>"ҩ", "Ҫ"=>"ҫ", "Ҭ"=>"ҭ", "Ү"=>"ү", "Ұ"=>"ұ", "Ҳ"=>"ҳ", "Ҵ"=>"ҵ", "Ҷ"=>"ҷ", "Ҹ"=>"ҹ", "Һ"=>"һ", "Ҽ"=>"ҽ", "Ҿ"=>"ҿ", "Ӂ"=>"ӂ", "Ӄ"=>"ӄ", "Ӆ"=>"ӆ", "Ӈ"=>"ӈ", "Ӊ"=>"ӊ", "Ӌ"=>"ӌ", "Ӎ"=>"ӎ", "Ӑ"=>"ӑ", "Ӓ"=>"ӓ", "Ӕ"=>"ӕ", "Ӗ"=>"ӗ", "Ә"=>"ә", "Ӛ"=>"ӛ", "Ӝ"=>"ӝ", "Ӟ"=>"ӟ", "Ӡ"=>"ӡ", "Ӣ"=>"ӣ", "Ӥ"=>"ӥ", "Ӧ"=>"ӧ", "Ө"=>"ө", "Ӫ"=>"ӫ", "Ӭ"=>"ӭ", "Ӯ"=>"ӯ", "Ӱ"=>"ӱ", "Ӳ"=>"ӳ", "Ӵ"=>"ӵ", "Ӹ"=>"ӹ", "Ԁ"=>"ԁ", "Ԃ"=>"ԃ", "Ԅ"=>"ԅ", "Ԇ"=>"ԇ", "Ԉ"=>"ԉ", "Ԋ"=>"ԋ", "Ԍ"=>"ԍ", "Ԏ"=>"ԏ", "Ա"=>"ա", "Բ"=>"բ", "Գ"=>"գ", "Դ"=>"դ", "Ե"=>"ե", "Զ"=>"զ", "Է"=>"է", "Ը"=>"ը", "Թ"=>"թ", "Ժ"=>"ժ", "Ի"=>"ի", "Լ"=>"լ", "Խ"=>"խ", "Ծ"=>"ծ", "Կ"=>"կ", "Հ"=>"հ", "Ձ"=>"ձ", "Ղ"=>"ղ", "Ճ"=>"ճ", "Մ"=>"մ", "Յ"=>"յ", "Ն"=>"ն", "Շ"=>"շ", "Ո"=>"ո", "Չ"=>"չ", "Պ"=>"պ", "Ջ"=>"ջ", "Ռ"=>"ռ", "Ս"=>"ս", "Վ"=>"վ", "Տ"=>"տ", "Ր"=>"ր", "Ց"=>"ց", "Ւ"=>"ւ", "Փ"=>"փ", "Ք"=>"ք", "Օ"=>"օ", "Ֆ"=>"ֆ", "և"=>"եւ", "Ḁ"=>"ḁ", "Ḃ"=>"ḃ", "Ḅ"=>"ḅ", "Ḇ"=>"ḇ", "Ḉ"=>"ḉ", "Ḋ"=>"ḋ", "Ḍ"=>"ḍ", "Ḏ"=>"ḏ", "Ḑ"=>"ḑ", "Ḓ"=>"ḓ", "Ḕ"=>"ḕ", "Ḗ"=>"ḗ", "Ḙ"=>"ḙ", "Ḛ"=>"ḛ", "Ḝ"=>"ḝ", "Ḟ"=>"ḟ", "Ḡ"=>"ḡ", "Ḣ"=>"ḣ", "Ḥ"=>"ḥ", "Ḧ"=>"ḧ", "Ḩ"=>"ḩ", "Ḫ"=>"ḫ", "Ḭ"=>"ḭ", "Ḯ"=>"ḯ", "Ḱ"=>"ḱ", "Ḳ"=>"ḳ", "Ḵ"=>"ḵ", "Ḷ"=>"ḷ", "Ḹ"=>"ḹ", "Ḻ"=>"ḻ", "Ḽ"=>"ḽ", "Ḿ"=>"ḿ", "Ṁ"=>"ṁ", "Ṃ"=>"ṃ", "Ṅ"=>"ṅ", "Ṇ"=>"ṇ", "Ṉ"=>"ṉ", "Ṋ"=>"ṋ", "Ṍ"=>"ṍ", "Ṏ"=>"ṏ", "Ṑ"=>"ṑ", "Ṓ"=>"ṓ", "Ṕ"=>"ṕ", "Ṗ"=>"ṗ", "Ṙ"=>"ṙ", "Ṛ"=>"ṛ", "Ṝ"=>"ṝ", "Ṟ"=>"ṟ", "Ṡ"=>"ṡ", "Ṣ"=>"ṣ", "Ṥ"=>"ṥ", "Ṧ"=>"ṧ", "Ṩ"=>"ṩ", "Ṫ"=>"ṫ", "Ṭ"=>"ṭ", "Ṯ"=>"ṯ", "Ṱ"=>"ṱ", "Ṳ"=>"ṳ", "Ṵ"=>"ṵ", "Ṷ"=>"ṷ", "Ṹ"=>"ṹ", "Ṻ"=>"ṻ", "Ṽ"=>"ṽ", "Ṿ"=>"ṿ", "Ẁ"=>"ẁ", "Ẃ"=>"ẃ", "Ẅ"=>"ẅ", "Ẇ"=>"ẇ", "Ẉ"=>"ẉ", "Ẋ"=>"ẋ", "Ẍ"=>"ẍ", "Ẏ"=>"ẏ", "Ẑ"=>"ẑ", "Ẓ"=>"ẓ", "Ẕ"=>"ẕ", "ẖ"=>"ẖ", "ẗ"=>"ẗ", "ẘ"=>"ẘ", "ẙ"=>"ẙ", "ẚ"=>"aʾ", "ẛ"=>"ṡ", "Ạ"=>"ạ", "Ả"=>"ả", "Ấ"=>"ấ", "Ầ"=>"ầ", "Ẩ"=>"ẩ", "Ẫ"=>"ẫ", "Ậ"=>"ậ", "Ắ"=>"ắ", "Ằ"=>"ằ", "Ẳ"=>"ẳ", "Ẵ"=>"ẵ", "Ặ"=>"ặ", "Ẹ"=>"ẹ", "Ẻ"=>"ẻ", "Ẽ"=>"ẽ", "Ế"=>"ế", "Ề"=>"ề", "Ể"=>"ể", "Ễ"=>"ễ", "Ệ"=>"ệ", "Ỉ"=>"ỉ", "Ị"=>"ị", "Ọ"=>"ọ", "Ỏ"=>"ỏ", "Ố"=>"ố", "Ồ"=>"ồ", "Ổ"=>"ổ", "Ỗ"=>"ỗ", "Ộ"=>"ộ", "Ớ"=>"ớ", "Ờ"=>"ờ", "Ở"=>"ở", "Ỡ"=>"ỡ", "Ợ"=>"ợ", "Ụ"=>"ụ", "Ủ"=>"ủ", "Ứ"=>"ứ", "Ừ"=>"ừ", "Ử"=>"ử", "Ữ"=>"ữ", "Ự"=>"ự", "Ỳ"=>"ỳ", "Ỵ"=>"ỵ", "Ỷ"=>"ỷ", "Ỹ"=>"ỹ", "Ἀ"=>"ἀ", "Ἁ"=>"ἁ", "Ἂ"=>"ἂ", "Ἃ"=>"ἃ", "Ἄ"=>"ἄ", "Ἅ"=>"ἅ", "Ἆ"=>"ἆ", "Ἇ"=>"ἇ", "Ἐ"=>"ἐ", "Ἑ"=>"ἑ", "Ἒ"=>"ἒ", "Ἓ"=>"ἓ", "Ἔ"=>"ἔ", "Ἕ"=>"ἕ", "Ἠ"=>"ἠ", "Ἡ"=>"ἡ", "Ἢ"=>"ἢ", "Ἣ"=>"ἣ", "Ἤ"=>"ἤ", "Ἥ"=>"ἥ", "Ἦ"=>"ἦ", "Ἧ"=>"ἧ", "Ἰ"=>"ἰ", "Ἱ"=>"ἱ", "Ἲ"=>"ἲ", "Ἳ"=>"ἳ", "Ἴ"=>"ἴ", "Ἵ"=>"ἵ", "Ἶ"=>"ἶ", "Ἷ"=>"ἷ", "Ὀ"=>"ὀ", "Ὁ"=>"ὁ", "Ὂ"=>"ὂ", "Ὃ"=>"ὃ", "Ὄ"=>"ὄ", "Ὅ"=>"ὅ", "ὐ"=>"ὐ", "ὒ"=>"ὒ", "ὔ"=>"ὔ", "ὖ"=>"ὖ", "Ὑ"=>"ὑ", "Ὓ"=>"ὓ", "Ὕ"=>"ὕ", "Ὗ"=>"ὗ", "Ὠ"=>"ὠ", "Ὡ"=>"ὡ", "Ὢ"=>"ὢ", "Ὣ"=>"ὣ", "Ὤ"=>"ὤ", "Ὥ"=>"ὥ", "Ὦ"=>"ὦ", "Ὧ"=>"ὧ", "ᾀ"=>"ἀι", "ᾁ"=>"ἁι", "ᾂ"=>"ἂι", "ᾃ"=>"ἃι", "ᾄ"=>"ἄι", "ᾅ"=>"ἅι", "ᾆ"=>"ἆι", "ᾇ"=>"ἇι", "ᾈ"=>"ἀι", "ᾉ"=>"ἁι", "ᾊ"=>"ἂι", "ᾋ"=>"ἃι", "ᾌ"=>"ἄι", "ᾍ"=>"ἅι", "ᾎ"=>"ἆι", "ᾏ"=>"ἇι", "ᾐ"=>"ἠι", "ᾑ"=>"ἡι", "ᾒ"=>"ἢι", "ᾓ"=>"ἣι", "ᾔ"=>"ἤι", "ᾕ"=>"ἥι", "ᾖ"=>"ἦι", "ᾗ"=>"ἧι", "ᾘ"=>"ἠι", "ᾙ"=>"ἡι", "ᾚ"=>"ἢι", "ᾛ"=>"ἣι", "ᾜ"=>"ἤι", "ᾝ"=>"ἥι", "ᾞ"=>"ἦι", "ᾟ"=>"ἧι", "ᾠ"=>"ὠι", "ᾡ"=>"ὡι", "ᾢ"=>"ὢι", "ᾣ"=>"ὣι", "ᾤ"=>"ὤι", "ᾥ"=>"ὥι", "ᾦ"=>"ὦι", "ᾧ"=>"ὧι", "ᾨ"=>"ὠι", "ᾩ"=>"ὡι", "ᾪ"=>"ὢι", "ᾫ"=>"ὣι", "ᾬ"=>"ὤι", "ᾭ"=>"ὥι", "ᾮ"=>"ὦι", "ᾯ"=>"ὧι", "ᾲ"=>"ὰι", "ᾳ"=>"αι", "ᾴ"=>"άι", "ᾶ"=>"ᾶ", "ᾷ"=>"ᾶι", "Ᾰ"=>"ᾰ", "Ᾱ"=>"ᾱ", "Ὰ"=>"ὰ", "Ά"=>"ά", "ᾼ"=>"αι", "ι"=>"ι", "ῂ"=>"ὴι", "ῃ"=>"ηι", "ῄ"=>"ήι", "ῆ"=>"ῆ", "ῇ"=>"ῆι", "Ὲ"=>"ὲ", "Έ"=>"έ", "Ὴ"=>"ὴ", "Ή"=>"ή", "ῌ"=>"ηι", "ῒ"=>"ῒ", "ΐ"=>"ΐ", "ῖ"=>"ῖ", "ῗ"=>"ῗ", "Ῐ"=>"ῐ", "Ῑ"=>"ῑ", "Ὶ"=>"ὶ", "Ί"=>"ί", "ῢ"=>"ῢ", "ΰ"=>"ΰ", "ῤ"=>"ῤ", "ῦ"=>"ῦ", "ῧ"=>"ῧ", "Ῠ"=>"ῠ", "Ῡ"=>"ῡ", "Ὺ"=>"ὺ", "Ύ"=>"ύ", "Ῥ"=>"ῥ", "ῲ"=>"ὼι", "ῳ"=>"ωι", "ῴ"=>"ώι", "ῶ"=>"ῶ", "ῷ"=>"ῶι", "Ὸ"=>"ὸ", "Ό"=>"ό", "Ὼ"=>"ὼ", "Ώ"=>"ώ", "ῼ"=>"ωι", "Ω"=>"ω", "K"=>"k", "Å"=>"å", "Ⅰ"=>"ⅰ", "Ⅱ"=>"ⅱ", "Ⅲ"=>"ⅲ", "Ⅳ"=>"ⅳ", "Ⅴ"=>"ⅴ", "Ⅵ"=>"ⅵ", "Ⅶ"=>"ⅶ", "Ⅷ"=>"ⅷ", "Ⅸ"=>"ⅸ", "Ⅹ"=>"ⅹ", "Ⅺ"=>"ⅺ", "Ⅻ"=>"ⅻ", "Ⅼ"=>"ⅼ", "Ⅽ"=>"ⅽ", "Ⅾ"=>"ⅾ", "Ⅿ"=>"ⅿ", "Ⓐ"=>"ⓐ", "Ⓑ"=>"ⓑ", "Ⓒ"=>"ⓒ", "Ⓓ"=>"ⓓ", "Ⓔ"=>"ⓔ", "Ⓕ"=>"ⓕ", "Ⓖ"=>"ⓖ", "Ⓗ"=>"ⓗ", "Ⓘ"=>"ⓘ", "Ⓙ"=>"ⓙ", "Ⓚ"=>"ⓚ", "Ⓛ"=>"ⓛ", "Ⓜ"=>"ⓜ", "Ⓝ"=>"ⓝ", "Ⓞ"=>"ⓞ", "Ⓟ"=>"ⓟ", "Ⓠ"=>"ⓠ", "Ⓡ"=>"ⓡ", "Ⓢ"=>"ⓢ", "Ⓣ"=>"ⓣ", "Ⓤ"=>"ⓤ", "Ⓥ"=>"ⓥ", "Ⓦ"=>"ⓦ", "Ⓧ"=>"ⓧ", "Ⓨ"=>"ⓨ", "Ⓩ"=>"ⓩ", "ﬀ"=>"ff", "ﬁ"=>"fi", "ﬂ"=>"fl", "ﬃ"=>"ffi", "ﬄ"=>"ffl", "ﬅ"=>"st", "ﬆ"=>"st", "ﬓ"=>"մն", "ﬔ"=>"մե", "ﬕ"=>"մի", "ﬖ"=>"վն", "ﬗ"=>"մխ", "Ａ"=>"ａ", "Ｂ"=>"ｂ", "Ｃ"=>"ｃ", "Ｄ"=>"ｄ", "Ｅ"=>"ｅ", "Ｆ"=>"ｆ", "Ｇ"=>"ｇ", "Ｈ"=>"ｈ", "Ｉ"=>"ｉ", "Ｊ"=>"ｊ", "Ｋ"=>"ｋ", "Ｌ"=>"ｌ", "Ｍ"=>"ｍ", "Ｎ"=>"ｎ", "Ｏ"=>"ｏ", "Ｐ"=>"ｐ", "Ｑ"=>"ｑ", "Ｒ"=>"ｒ", "Ｓ"=>"ｓ", "Ｔ"=>"ｔ", "Ｕ"=>"ｕ", "Ｖ"=>"ｖ", "Ｗ"=>"ｗ", "Ｘ"=>"ｘ", "Ｙ"=>"ｙ", "Ｚ"=>"ｚ", "𐐀"=>"𐐨", "𐐁"=>"𐐩", "𐐂"=>"𐐪", "𐐃"=>"𐐫", "𐐄"=>"𐐬", "𐐅"=>"𐐭", "𐐆"=>"𐐮", "𐐇"=>"𐐯", "𐐈"=>"𐐰", "𐐉"=>"𐐱", "𐐊"=>"𐐲", "𐐋"=>"𐐳", "𐐌"=>"𐐴", "𐐍"=>"𐐵", "𐐎"=>"𐐶", "𐐏"=>"𐐷", "𐐐"=>"𐐸", "𐐑"=>"𐐹", "𐐒"=>"𐐺", "𐐓"=>"𐐻", "𐐔"=>"𐐼", "𐐕"=>"𐐽", "𐐖"=>"𐐾", "𐐗"=>"𐐿", "𐐘"=>"𐑀", "𐐙"=>"𐑁", "𐐚"=>"𐑂", "𐐛"=>"𐑃", "𐐜"=>"𐑄", "𐐝"=>"𐑅", "𐐞"=>"𐑆", "𐐟"=>"𐑇", "𐐠"=>"𐑈", "𐐡"=>"𐑉", "𐐢"=>"𐑊", "𐐣"=>"𐑋", "𐐤"=>"𐑌", "𐐥"=>"𐑍"}.freeze

    # ASCII space characters \StringPrep\[\"C.1.1\"]
    IN_C_1_1 = / /.freeze

    # Non-ASCII space characters \StringPrep\[\"C.1.2\"]
    IN_C_1_2 = /[\u200b\p{Zs}&&[^ ]]/.freeze

    # ASCII control characters \StringPrep\[\"C.2.1\"]
    IN_C_2_1 = /[\x00-\x1f\x7f]/.freeze

    # Non-ASCII control characters \StringPrep\[\"C.2.2\"]
    IN_C_2_2 = /[\u{06dd 070f 180e feff}\u{0080}-\u{009f}\u{200c}-\u{200d}\u{2028}-\u{2029}\u{2060}-\u{2063}\u{206a}-\u{206f}\u{fff9}-\u{fffc}\u{1d173}-\u{1d17a}]/.freeze

    # Private use \StringPrep\[\"C.3\"]
    IN_C_3 = /\p{private use}/.freeze

    # Non-character code points \StringPrep\[\"C.4\"]
    IN_C_4 = /\p{noncharacter code point}/.freeze

    # Surrogate codes \StringPrep\[\"C.5\"]
    IN_C_5 = /\p{surrogate}/.freeze

    # Inappropriate for plain text \StringPrep\[\"C.6\"]
    IN_C_6 = /[\p{in specials}&&\p{AGE=3.2}&&\p{^NChar}]/.freeze

    # Inappropriate for canonical representation \StringPrep\[\"C.7\"]
    IN_C_7 = /[\p{in ideographic description characters}&&\p{AGE=3.2}]/.freeze

    # Change display properties or are deprecated \StringPrep\[\"C.8\"]
    IN_C_8 = /[\u{0340}-\u{0341}\u{200e}-\u{200f}\u{202a}-\u{202e}\u{206a}-\u{206f}]/.freeze

    # Tagging characters \StringPrep\[\"C.9\"]
    IN_C_9 = /[\p{in Tags}&&\p{AGE=3.2}]/.freeze

    # Characters with bidirectional property "R" or "AL" \StringPrep\[\"D.1\"]
    IN_D_1 = /[\u{05be 05c0 05c3 061b 061f 06dd 0710 07b1 200f fb1d fb3e}\u{05d0}-\u{05ea}\u{05f0}-\u{05f4}\u{0621}-\u{063a}\u{0640}-\u{064a}\u{066d}-\u{066f}\u{0671}-\u{06d5}\u{06e5}-\u{06e6}\u{06fa}-\u{06fe}\u{0700}-\u{070d}\u{0712}-\u{072c}\u{0780}-\u{07a5}\u{fb1f}-\u{fb28}\u{fb2a}-\u{fb36}\u{fb38}-\u{fb3c}\u{fb40}-\u{fb41}\u{fb43}-\u{fb44}\u{fb46}-\u{fbb1}\u{fbd3}-\u{fd3d}\u{fd50}-\u{fd8f}\u{fd92}-\u{fdc7}\u{fdf0}-\u{fdfc}\u{fe70}-\u{fe74}\u{fe76}-\u{fefc}]/.freeze

    # Used to check req3 of bidirectional checks
    # Matches the negation of the D.1 table
    IN_D_1_NEGATED = /[^\u{05be 05c0 05c3 061b 061f 06dd 0710 07b1 200f fb1d fb3e}\u{05d0}-\u{05ea}\u{05f0}-\u{05f4}\u{0621}-\u{063a}\u{0640}-\u{064a}\u{066d}-\u{066f}\u{0671}-\u{06d5}\u{06e5}-\u{06e6}\u{06fa}-\u{06fe}\u{0700}-\u{070d}\u{0712}-\u{072c}\u{0780}-\u{07a5}\u{fb1f}-\u{fb28}\u{fb2a}-\u{fb36}\u{fb38}-\u{fb3c}\u{fb40}-\u{fb41}\u{fb43}-\u{fb44}\u{fb46}-\u{fbb1}\u{fbd3}-\u{fd3d}\u{fd50}-\u{fd8f}\u{fd92}-\u{fdc7}\u{fdf0}-\u{fdfc}\u{fe70}-\u{fe74}\u{fe76}-\u{fefc}]/.freeze

    # Characters with bidirectional property "L" \StringPrep\[\"D.2\"]
    IN_D_2 = /[\u{00aa 00b5 00ba 02ee 037a 0386 038c 0589 0903 0950 09b2 09d7 0a5e 0a83 0a8d 0ac9 0ad0 0ae0 0b40 0b57 0b83 0b9c 0bd7 0cbe 0cde 0d57 0dbd 0e84 0e8a 0e8d 0ea5 0ea7 0ebd 0ec6 0f36 0f38 0f7f 0f85 0fcf 102c 1031 1038 10fb 1248 1258 1288 12b0 12c0 1310 17dc 1f59 1f5b 1f5d 1fbe 200e 2071 207f 2102 2107 2115 2124 2126 2128 2395 1d4a2 1d4bb 1d546}\u{0041}-\u{005a}\u{0061}-\u{007a}\u{00c0}-\u{00d6}\u{00d8}-\u{00f6}\u{00f8}-\u{0220}\u{0222}-\u{0233}\u{0250}-\u{02ad}\u{02b0}-\u{02b8}\u{02bb}-\u{02c1}\u{02d0}-\u{02d1}\u{02e0}-\u{02e4}\u{0388}-\u{038a}\u{038e}-\u{03a1}\u{03a3}-\u{03ce}\u{03d0}-\u{03f5}\u{0400}-\u{0482}\u{048a}-\u{04ce}\u{04d0}-\u{04f5}\u{04f8}-\u{04f9}\u{0500}-\u{050f}\u{0531}-\u{0556}\u{0559}-\u{055f}\u{0561}-\u{0587}\u{0905}-\u{0939}\u{093d}-\u{0940}\u{0949}-\u{094c}\u{0958}-\u{0961}\u{0964}-\u{0970}\u{0982}-\u{0983}\u{0985}-\u{098c}\u{098f}-\u{0990}\u{0993}-\u{09a8}\u{09aa}-\u{09b0}\u{09b6}-\u{09b9}\u{09be}-\u{09c0}\u{09c7}-\u{09c8}\u{09cb}-\u{09cc}\u{09dc}-\u{09dd}\u{09df}-\u{09e1}\u{09e6}-\u{09f1}\u{09f4}-\u{09fa}\u{0a05}-\u{0a0a}\u{0a0f}-\u{0a10}\u{0a13}-\u{0a28}\u{0a2a}-\u{0a30}\u{0a32}-\u{0a33}\u{0a35}-\u{0a36}\u{0a38}-\u{0a39}\u{0a3e}-\u{0a40}\u{0a59}-\u{0a5c}\u{0a66}-\u{0a6f}\u{0a72}-\u{0a74}\u{0a85}-\u{0a8b}\u{0a8f}-\u{0a91}\u{0a93}-\u{0aa8}\u{0aaa}-\u{0ab0}\u{0ab2}-\u{0ab3}\u{0ab5}-\u{0ab9}\u{0abd}-\u{0ac0}\u{0acb}-\u{0acc}\u{0ae6}-\u{0aef}\u{0b02}-\u{0b03}\u{0b05}-\u{0b0c}\u{0b0f}-\u{0b10}\u{0b13}-\u{0b28}\u{0b2a}-\u{0b30}\u{0b32}-\u{0b33}\u{0b36}-\u{0b39}\u{0b3d}-\u{0b3e}\u{0b47}-\u{0b48}\u{0b4b}-\u{0b4c}\u{0b5c}-\u{0b5d}\u{0b5f}-\u{0b61}\u{0b66}-\u{0b70}\u{0b85}-\u{0b8a}\u{0b8e}-\u{0b90}\u{0b92}-\u{0b95}\u{0b99}-\u{0b9a}\u{0b9e}-\u{0b9f}\u{0ba3}-\u{0ba4}\u{0ba8}-\u{0baa}\u{0bae}-\u{0bb5}\u{0bb7}-\u{0bb9}\u{0bbe}-\u{0bbf}\u{0bc1}-\u{0bc2}\u{0bc6}-\u{0bc8}\u{0bca}-\u{0bcc}\u{0be7}-\u{0bf2}\u{0c01}-\u{0c03}\u{0c05}-\u{0c0c}\u{0c0e}-\u{0c10}\u{0c12}-\u{0c28}\u{0c2a}-\u{0c33}\u{0c35}-\u{0c39}\u{0c41}-\u{0c44}\u{0c60}-\u{0c61}\u{0c66}-\u{0c6f}\u{0c82}-\u{0c83}\u{0c85}-\u{0c8c}\u{0c8e}-\u{0c90}\u{0c92}-\u{0ca8}\u{0caa}-\u{0cb3}\u{0cb5}-\u{0cb9}\u{0cc0}-\u{0cc4}\u{0cc7}-\u{0cc8}\u{0cca}-\u{0ccb}\u{0cd5}-\u{0cd6}\u{0ce0}-\u{0ce1}\u{0ce6}-\u{0cef}\u{0d02}-\u{0d03}\u{0d05}-\u{0d0c}\u{0d0e}-\u{0d10}\u{0d12}-\u{0d28}\u{0d2a}-\u{0d39}\u{0d3e}-\u{0d40}\u{0d46}-\u{0d48}\u{0d4a}-\u{0d4c}\u{0d60}-\u{0d61}\u{0d66}-\u{0d6f}\u{0d82}-\u{0d83}\u{0d85}-\u{0d96}\u{0d9a}-\u{0db1}\u{0db3}-\u{0dbb}\u{0dc0}-\u{0dc6}\u{0dcf}-\u{0dd1}\u{0dd8}-\u{0ddf}\u{0df2}-\u{0df4}\u{0e01}-\u{0e30}\u{0e32}-\u{0e33}\u{0e40}-\u{0e46}\u{0e4f}-\u{0e5b}\u{0e81}-\u{0e82}\u{0e87}-\u{0e88}\u{0e94}-\u{0e97}\u{0e99}-\u{0e9f}\u{0ea1}-\u{0ea3}\u{0eaa}-\u{0eab}\u{0ead}-\u{0eb0}\u{0eb2}-\u{0eb3}\u{0ec0}-\u{0ec4}\u{0ed0}-\u{0ed9}\u{0edc}-\u{0edd}\u{0f00}-\u{0f17}\u{0f1a}-\u{0f34}\u{0f3e}-\u{0f47}\u{0f49}-\u{0f6a}\u{0f88}-\u{0f8b}\u{0fbe}-\u{0fc5}\u{0fc7}-\u{0fcc}\u{1000}-\u{1021}\u{1023}-\u{1027}\u{1029}-\u{102a}\u{1040}-\u{1057}\u{10a0}-\u{10c5}\u{10d0}-\u{10f8}\u{1100}-\u{1159}\u{115f}-\u{11a2}\u{11a8}-\u{11f9}\u{1200}-\u{1206}\u{1208}-\u{1246}\u{124a}-\u{124d}\u{1250}-\u{1256}\u{125a}-\u{125d}\u{1260}-\u{1286}\u{128a}-\u{128d}\u{1290}-\u{12ae}\u{12b2}-\u{12b5}\u{12b8}-\u{12be}\u{12c2}-\u{12c5}\u{12c8}-\u{12ce}\u{12d0}-\u{12d6}\u{12d8}-\u{12ee}\u{12f0}-\u{130e}\u{1312}-\u{1315}\u{1318}-\u{131e}\u{1320}-\u{1346}\u{1348}-\u{135a}\u{1361}-\u{137c}\u{13a0}-\u{13f4}\u{1401}-\u{1676}\u{1681}-\u{169a}\u{16a0}-\u{16f0}\u{1700}-\u{170c}\u{170e}-\u{1711}\u{1720}-\u{1731}\u{1735}-\u{1736}\u{1740}-\u{1751}\u{1760}-\u{176c}\u{176e}-\u{1770}\u{1780}-\u{17b6}\u{17be}-\u{17c5}\u{17c7}-\u{17c8}\u{17d4}-\u{17da}\u{17e0}-\u{17e9}\u{1810}-\u{1819}\u{1820}-\u{1877}\u{1880}-\u{18a8}\u{1e00}-\u{1e9b}\u{1ea0}-\u{1ef9}\u{1f00}-\u{1f15}\u{1f18}-\u{1f1d}\u{1f20}-\u{1f45}\u{1f48}-\u{1f4d}\u{1f50}-\u{1f57}\u{1f5f}-\u{1f7d}\u{1f80}-\u{1fb4}\u{1fb6}-\u{1fbc}\u{1fc2}-\u{1fc4}\u{1fc6}-\u{1fcc}\u{1fd0}-\u{1fd3}\u{1fd6}-\u{1fdb}\u{1fe0}-\u{1fec}\u{1ff2}-\u{1ff4}\u{1ff6}-\u{1ffc}\u{210a}-\u{2113}\u{2119}-\u{211d}\u{212a}-\u{212d}\u{212f}-\u{2131}\u{2133}-\u{2139}\u{213d}-\u{213f}\u{2145}-\u{2149}\u{2160}-\u{2183}\u{2336}-\u{237a}\u{249c}-\u{24e9}\u{3005}-\u{3007}\u{3021}-\u{3029}\u{3031}-\u{3035}\u{3038}-\u{303c}\u{3041}-\u{3096}\u{309d}-\u{309f}\u{30a1}-\u{30fa}\u{30fc}-\u{30ff}\u{3105}-\u{312c}\u{3131}-\u{318e}\u{3190}-\u{31b7}\u{31f0}-\u{321c}\u{3220}-\u{3243}\u{3260}-\u{327b}\u{327f}-\u{32b0}\u{32c0}-\u{32cb}\u{32d0}-\u{32fe}\u{3300}-\u{3376}\u{337b}-\u{33dd}\u{33e0}-\u{33fe}\u{3400}-\u{4db5}\u{4e00}-\u{9fa5}\u{a000}-\u{a48c}\u{ac00}-\u{d7a3}\u{e000}-\u{fa2d}\u{fa30}-\u{fa6a}\u{fb00}-\u{fb06}\u{fb13}-\u{fb17}\u{ff21}-\u{ff3a}\u{ff41}-\u{ff5a}\u{ff66}-\u{ffbe}\u{ffc2}-\u{ffc7}\u{ffca}-\u{ffcf}\u{ffd2}-\u{ffd7}\u{ffda}-\u{ffdc}\u{10300}-\u{1031e}\u{10320}-\u{10323}\u{10330}-\u{1034a}\u{10400}-\u{10425}\u{10428}-\u{1044d}\u{1d000}-\u{1d0f5}\u{1d100}-\u{1d126}\u{1d12a}-\u{1d166}\u{1d16a}-\u{1d172}\u{1d183}-\u{1d184}\u{1d18c}-\u{1d1a9}\u{1d1ae}-\u{1d1dd}\u{1d400}-\u{1d454}\u{1d456}-\u{1d49c}\u{1d49e}-\u{1d49f}\u{1d4a5}-\u{1d4a6}\u{1d4a9}-\u{1d4ac}\u{1d4ae}-\u{1d4b9}\u{1d4bd}-\u{1d4c0}\u{1d4c2}-\u{1d4c3}\u{1d4c5}-\u{1d505}\u{1d507}-\u{1d50a}\u{1d50d}-\u{1d514}\u{1d516}-\u{1d51c}\u{1d51e}-\u{1d539}\u{1d53b}-\u{1d53e}\u{1d540}-\u{1d544}\u{1d54a}-\u{1d550}\u{1d552}-\u{1d6a3}\u{1d6a8}-\u{1d7c9}\u{20000}-\u{2a6d6}\u{2f800}-\u{2fa1d}\u{f0000}-\u{ffffd}\u{100000}-\u{10fffd}\p{Cs}]/.freeze

    BIDI_DESC_REQ2 = "A string with RandALCat characters must not contain LCat characters."

    # Bidirectional Characters [StringPrep, §6], Requirement 2
    # >>>
    #   If a string contains any RandALCat character, the string MUST NOT
    #   contain any LCat character.
    BIDI_FAILS_REQ2 = /(?m-ix:(?-mix:[\u{05be 05c0 05c3 061b 061f 06dd 0710 07b1 200f fb1d fb3e}\u{05d0}-\u{05ea}\u{05f0}-\u{05f4}\u{0621}-\u{063a}\u{0640}-\u{064a}\u{066d}-\u{066f}\u{0671}-\u{06d5}\u{06e5}-\u{06e6}\u{06fa}-\u{06fe}\u{0700}-\u{070d}\u{0712}-\u{072c}\u{0780}-\u{07a5}\u{fb1f}-\u{fb28}\u{fb2a}-\u{fb36}\u{fb38}-\u{fb3c}\u{fb40}-\u{fb41}\u{fb43}-\u{fb44}\u{fb46}-\u{fbb1}\u{fbd3}-\u{fd3d}\u{fd50}-\u{fd8f}\u{fd92}-\u{fdc7}\u{fdf0}-\u{fdfc}\u{fe70}-\u{fe74}\u{fe76}-\u{fefc}]).*?(?-mix:[\u{00aa 00b5 00ba 02ee 037a 0386 038c 0589 0903 0950 09b2 09d7 0a5e 0a83 0a8d 0ac9 0ad0 0ae0 0b40 0b57 0b83 0b9c 0bd7 0cbe 0cde 0d57 0dbd 0e84 0e8a 0e8d 0ea5 0ea7 0ebd 0ec6 0f36 0f38 0f7f 0f85 0fcf 102c 1031 1038 10fb 1248 1258 1288 12b0 12c0 1310 17dc 1f59 1f5b 1f5d 1fbe 200e 2071 207f 2102 2107 2115 2124 2126 2128 2395 1d4a2 1d4bb 1d546}\u{0041}-\u{005a}\u{0061}-\u{007a}\u{00c0}-\u{00d6}\u{00d8}-\u{00f6}\u{00f8}-\u{0220}\u{0222}-\u{0233}\u{0250}-\u{02ad}\u{02b0}-\u{02b8}\u{02bb}-\u{02c1}\u{02d0}-\u{02d1}\u{02e0}-\u{02e4}\u{0388}-\u{038a}\u{038e}-\u{03a1}\u{03a3}-\u{03ce}\u{03d0}-\u{03f5}\u{0400}-\u{0482}\u{048a}-\u{04ce}\u{04d0}-\u{04f5}\u{04f8}-\u{04f9}\u{0500}-\u{050f}\u{0531}-\u{0556}\u{0559}-\u{055f}\u{0561}-\u{0587}\u{0905}-\u{0939}\u{093d}-\u{0940}\u{0949}-\u{094c}\u{0958}-\u{0961}\u{0964}-\u{0970}\u{0982}-\u{0983}\u{0985}-\u{098c}\u{098f}-\u{0990}\u{0993}-\u{09a8}\u{09aa}-\u{09b0}\u{09b6}-\u{09b9}\u{09be}-\u{09c0}\u{09c7}-\u{09c8}\u{09cb}-\u{09cc}\u{09dc}-\u{09dd}\u{09df}-\u{09e1}\u{09e6}-\u{09f1}\u{09f4}-\u{09fa}\u{0a05}-\u{0a0a}\u{0a0f}-\u{0a10}\u{0a13}-\u{0a28}\u{0a2a}-\u{0a30}\u{0a32}-\u{0a33}\u{0a35}-\u{0a36}\u{0a38}-\u{0a39}\u{0a3e}-\u{0a40}\u{0a59}-\u{0a5c}\u{0a66}-\u{0a6f}\u{0a72}-\u{0a74}\u{0a85}-\u{0a8b}\u{0a8f}-\u{0a91}\u{0a93}-\u{0aa8}\u{0aaa}-\u{0ab0}\u{0ab2}-\u{0ab3}\u{0ab5}-\u{0ab9}\u{0abd}-\u{0ac0}\u{0acb}-\u{0acc}\u{0ae6}-\u{0aef}\u{0b02}-\u{0b03}\u{0b05}-\u{0b0c}\u{0b0f}-\u{0b10}\u{0b13}-\u{0b28}\u{0b2a}-\u{0b30}\u{0b32}-\u{0b33}\u{0b36}-\u{0b39}\u{0b3d}-\u{0b3e}\u{0b47}-\u{0b48}\u{0b4b}-\u{0b4c}\u{0b5c}-\u{0b5d}\u{0b5f}-\u{0b61}\u{0b66}-\u{0b70}\u{0b85}-\u{0b8a}\u{0b8e}-\u{0b90}\u{0b92}-\u{0b95}\u{0b99}-\u{0b9a}\u{0b9e}-\u{0b9f}\u{0ba3}-\u{0ba4}\u{0ba8}-\u{0baa}\u{0bae}-\u{0bb5}\u{0bb7}-\u{0bb9}\u{0bbe}-\u{0bbf}\u{0bc1}-\u{0bc2}\u{0bc6}-\u{0bc8}\u{0bca}-\u{0bcc}\u{0be7}-\u{0bf2}\u{0c01}-\u{0c03}\u{0c05}-\u{0c0c}\u{0c0e}-\u{0c10}\u{0c12}-\u{0c28}\u{0c2a}-\u{0c33}\u{0c35}-\u{0c39}\u{0c41}-\u{0c44}\u{0c60}-\u{0c61}\u{0c66}-\u{0c6f}\u{0c82}-\u{0c83}\u{0c85}-\u{0c8c}\u{0c8e}-\u{0c90}\u{0c92}-\u{0ca8}\u{0caa}-\u{0cb3}\u{0cb5}-\u{0cb9}\u{0cc0}-\u{0cc4}\u{0cc7}-\u{0cc8}\u{0cca}-\u{0ccb}\u{0cd5}-\u{0cd6}\u{0ce0}-\u{0ce1}\u{0ce6}-\u{0cef}\u{0d02}-\u{0d03}\u{0d05}-\u{0d0c}\u{0d0e}-\u{0d10}\u{0d12}-\u{0d28}\u{0d2a}-\u{0d39}\u{0d3e}-\u{0d40}\u{0d46}-\u{0d48}\u{0d4a}-\u{0d4c}\u{0d60}-\u{0d61}\u{0d66}-\u{0d6f}\u{0d82}-\u{0d83}\u{0d85}-\u{0d96}\u{0d9a}-\u{0db1}\u{0db3}-\u{0dbb}\u{0dc0}-\u{0dc6}\u{0dcf}-\u{0dd1}\u{0dd8}-\u{0ddf}\u{0df2}-\u{0df4}\u{0e01}-\u{0e30}\u{0e32}-\u{0e33}\u{0e40}-\u{0e46}\u{0e4f}-\u{0e5b}\u{0e81}-\u{0e82}\u{0e87}-\u{0e88}\u{0e94}-\u{0e97}\u{0e99}-\u{0e9f}\u{0ea1}-\u{0ea3}\u{0eaa}-\u{0eab}\u{0ead}-\u{0eb0}\u{0eb2}-\u{0eb3}\u{0ec0}-\u{0ec4}\u{0ed0}-\u{0ed9}\u{0edc}-\u{0edd}\u{0f00}-\u{0f17}\u{0f1a}-\u{0f34}\u{0f3e}-\u{0f47}\u{0f49}-\u{0f6a}\u{0f88}-\u{0f8b}\u{0fbe}-\u{0fc5}\u{0fc7}-\u{0fcc}\u{1000}-\u{1021}\u{1023}-\u{1027}\u{1029}-\u{102a}\u{1040}-\u{1057}\u{10a0}-\u{10c5}\u{10d0}-\u{10f8}\u{1100}-\u{1159}\u{115f}-\u{11a2}\u{11a8}-\u{11f9}\u{1200}-\u{1206}\u{1208}-\u{1246}\u{124a}-\u{124d}\u{1250}-\u{1256}\u{125a}-\u{125d}\u{1260}-\u{1286}\u{128a}-\u{128d}\u{1290}-\u{12ae}\u{12b2}-\u{12b5}\u{12b8}-\u{12be}\u{12c2}-\u{12c5}\u{12c8}-\u{12ce}\u{12d0}-\u{12d6}\u{12d8}-\u{12ee}\u{12f0}-\u{130e}\u{1312}-\u{1315}\u{1318}-\u{131e}\u{1320}-\u{1346}\u{1348}-\u{135a}\u{1361}-\u{137c}\u{13a0}-\u{13f4}\u{1401}-\u{1676}\u{1681}-\u{169a}\u{16a0}-\u{16f0}\u{1700}-\u{170c}\u{170e}-\u{1711}\u{1720}-\u{1731}\u{1735}-\u{1736}\u{1740}-\u{1751}\u{1760}-\u{176c}\u{176e}-\u{1770}\u{1780}-\u{17b6}\u{17be}-\u{17c5}\u{17c7}-\u{17c8}\u{17d4}-\u{17da}\u{17e0}-\u{17e9}\u{1810}-\u{1819}\u{1820}-\u{1877}\u{1880}-\u{18a8}\u{1e00}-\u{1e9b}\u{1ea0}-\u{1ef9}\u{1f00}-\u{1f15}\u{1f18}-\u{1f1d}\u{1f20}-\u{1f45}\u{1f48}-\u{1f4d}\u{1f50}-\u{1f57}\u{1f5f}-\u{1f7d}\u{1f80}-\u{1fb4}\u{1fb6}-\u{1fbc}\u{1fc2}-\u{1fc4}\u{1fc6}-\u{1fcc}\u{1fd0}-\u{1fd3}\u{1fd6}-\u{1fdb}\u{1fe0}-\u{1fec}\u{1ff2}-\u{1ff4}\u{1ff6}-\u{1ffc}\u{210a}-\u{2113}\u{2119}-\u{211d}\u{212a}-\u{212d}\u{212f}-\u{2131}\u{2133}-\u{2139}\u{213d}-\u{213f}\u{2145}-\u{2149}\u{2160}-\u{2183}\u{2336}-\u{237a}\u{249c}-\u{24e9}\u{3005}-\u{3007}\u{3021}-\u{3029}\u{3031}-\u{3035}\u{3038}-\u{303c}\u{3041}-\u{3096}\u{309d}-\u{309f}\u{30a1}-\u{30fa}\u{30fc}-\u{30ff}\u{3105}-\u{312c}\u{3131}-\u{318e}\u{3190}-\u{31b7}\u{31f0}-\u{321c}\u{3220}-\u{3243}\u{3260}-\u{327b}\u{327f}-\u{32b0}\u{32c0}-\u{32cb}\u{32d0}-\u{32fe}\u{3300}-\u{3376}\u{337b}-\u{33dd}\u{33e0}-\u{33fe}\u{3400}-\u{4db5}\u{4e00}-\u{9fa5}\u{a000}-\u{a48c}\u{ac00}-\u{d7a3}\u{e000}-\u{fa2d}\u{fa30}-\u{fa6a}\u{fb00}-\u{fb06}\u{fb13}-\u{fb17}\u{ff21}-\u{ff3a}\u{ff41}-\u{ff5a}\u{ff66}-\u{ffbe}\u{ffc2}-\u{ffc7}\u{ffca}-\u{ffcf}\u{ffd2}-\u{ffd7}\u{ffda}-\u{ffdc}\u{10300}-\u{1031e}\u{10320}-\u{10323}\u{10330}-\u{1034a}\u{10400}-\u{10425}\u{10428}-\u{1044d}\u{1d000}-\u{1d0f5}\u{1d100}-\u{1d126}\u{1d12a}-\u{1d166}\u{1d16a}-\u{1d172}\u{1d183}-\u{1d184}\u{1d18c}-\u{1d1a9}\u{1d1ae}-\u{1d1dd}\u{1d400}-\u{1d454}\u{1d456}-\u{1d49c}\u{1d49e}-\u{1d49f}\u{1d4a5}-\u{1d4a6}\u{1d4a9}-\u{1d4ac}\u{1d4ae}-\u{1d4b9}\u{1d4bd}-\u{1d4c0}\u{1d4c2}-\u{1d4c3}\u{1d4c5}-\u{1d505}\u{1d507}-\u{1d50a}\u{1d50d}-\u{1d514}\u{1d516}-\u{1d51c}\u{1d51e}-\u{1d539}\u{1d53b}-\u{1d53e}\u{1d540}-\u{1d544}\u{1d54a}-\u{1d550}\u{1d552}-\u{1d6a3}\u{1d6a8}-\u{1d7c9}\u{20000}-\u{2a6d6}\u{2f800}-\u{2fa1d}\u{f0000}-\u{ffffd}\u{100000}-\u{10fffd}\p{Cs}]))|(?m-ix:(?-mix:[\u{00aa 00b5 00ba 02ee 037a 0386 038c 0589 0903 0950 09b2 09d7 0a5e 0a83 0a8d 0ac9 0ad0 0ae0 0b40 0b57 0b83 0b9c 0bd7 0cbe 0cde 0d57 0dbd 0e84 0e8a 0e8d 0ea5 0ea7 0ebd 0ec6 0f36 0f38 0f7f 0f85 0fcf 102c 1031 1038 10fb 1248 1258 1288 12b0 12c0 1310 17dc 1f59 1f5b 1f5d 1fbe 200e 2071 207f 2102 2107 2115 2124 2126 2128 2395 1d4a2 1d4bb 1d546}\u{0041}-\u{005a}\u{0061}-\u{007a}\u{00c0}-\u{00d6}\u{00d8}-\u{00f6}\u{00f8}-\u{0220}\u{0222}-\u{0233}\u{0250}-\u{02ad}\u{02b0}-\u{02b8}\u{02bb}-\u{02c1}\u{02d0}-\u{02d1}\u{02e0}-\u{02e4}\u{0388}-\u{038a}\u{038e}-\u{03a1}\u{03a3}-\u{03ce}\u{03d0}-\u{03f5}\u{0400}-\u{0482}\u{048a}-\u{04ce}\u{04d0}-\u{04f5}\u{04f8}-\u{04f9}\u{0500}-\u{050f}\u{0531}-\u{0556}\u{0559}-\u{055f}\u{0561}-\u{0587}\u{0905}-\u{0939}\u{093d}-\u{0940}\u{0949}-\u{094c}\u{0958}-\u{0961}\u{0964}-\u{0970}\u{0982}-\u{0983}\u{0985}-\u{098c}\u{098f}-\u{0990}\u{0993}-\u{09a8}\u{09aa}-\u{09b0}\u{09b6}-\u{09b9}\u{09be}-\u{09c0}\u{09c7}-\u{09c8}\u{09cb}-\u{09cc}\u{09dc}-\u{09dd}\u{09df}-\u{09e1}\u{09e6}-\u{09f1}\u{09f4}-\u{09fa}\u{0a05}-\u{0a0a}\u{0a0f}-\u{0a10}\u{0a13}-\u{0a28}\u{0a2a}-\u{0a30}\u{0a32}-\u{0a33}\u{0a35}-\u{0a36}\u{0a38}-\u{0a39}\u{0a3e}-\u{0a40}\u{0a59}-\u{0a5c}\u{0a66}-\u{0a6f}\u{0a72}-\u{0a74}\u{0a85}-\u{0a8b}\u{0a8f}-\u{0a91}\u{0a93}-\u{0aa8}\u{0aaa}-\u{0ab0}\u{0ab2}-\u{0ab3}\u{0ab5}-\u{0ab9}\u{0abd}-\u{0ac0}\u{0acb}-\u{0acc}\u{0ae6}-\u{0aef}\u{0b02}-\u{0b03}\u{0b05}-\u{0b0c}\u{0b0f}-\u{0b10}\u{0b13}-\u{0b28}\u{0b2a}-\u{0b30}\u{0b32}-\u{0b33}\u{0b36}-\u{0b39}\u{0b3d}-\u{0b3e}\u{0b47}-\u{0b48}\u{0b4b}-\u{0b4c}\u{0b5c}-\u{0b5d}\u{0b5f}-\u{0b61}\u{0b66}-\u{0b70}\u{0b85}-\u{0b8a}\u{0b8e}-\u{0b90}\u{0b92}-\u{0b95}\u{0b99}-\u{0b9a}\u{0b9e}-\u{0b9f}\u{0ba3}-\u{0ba4}\u{0ba8}-\u{0baa}\u{0bae}-\u{0bb5}\u{0bb7}-\u{0bb9}\u{0bbe}-\u{0bbf}\u{0bc1}-\u{0bc2}\u{0bc6}-\u{0bc8}\u{0bca}-\u{0bcc}\u{0be7}-\u{0bf2}\u{0c01}-\u{0c03}\u{0c05}-\u{0c0c}\u{0c0e}-\u{0c10}\u{0c12}-\u{0c28}\u{0c2a}-\u{0c33}\u{0c35}-\u{0c39}\u{0c41}-\u{0c44}\u{0c60}-\u{0c61}\u{0c66}-\u{0c6f}\u{0c82}-\u{0c83}\u{0c85}-\u{0c8c}\u{0c8e}-\u{0c90}\u{0c92}-\u{0ca8}\u{0caa}-\u{0cb3}\u{0cb5}-\u{0cb9}\u{0cc0}-\u{0cc4}\u{0cc7}-\u{0cc8}\u{0cca}-\u{0ccb}\u{0cd5}-\u{0cd6}\u{0ce0}-\u{0ce1}\u{0ce6}-\u{0cef}\u{0d02}-\u{0d03}\u{0d05}-\u{0d0c}\u{0d0e}-\u{0d10}\u{0d12}-\u{0d28}\u{0d2a}-\u{0d39}\u{0d3e}-\u{0d40}\u{0d46}-\u{0d48}\u{0d4a}-\u{0d4c}\u{0d60}-\u{0d61}\u{0d66}-\u{0d6f}\u{0d82}-\u{0d83}\u{0d85}-\u{0d96}\u{0d9a}-\u{0db1}\u{0db3}-\u{0dbb}\u{0dc0}-\u{0dc6}\u{0dcf}-\u{0dd1}\u{0dd8}-\u{0ddf}\u{0df2}-\u{0df4}\u{0e01}-\u{0e30}\u{0e32}-\u{0e33}\u{0e40}-\u{0e46}\u{0e4f}-\u{0e5b}\u{0e81}-\u{0e82}\u{0e87}-\u{0e88}\u{0e94}-\u{0e97}\u{0e99}-\u{0e9f}\u{0ea1}-\u{0ea3}\u{0eaa}-\u{0eab}\u{0ead}-\u{0eb0}\u{0eb2}-\u{0eb3}\u{0ec0}-\u{0ec4}\u{0ed0}-\u{0ed9}\u{0edc}-\u{0edd}\u{0f00}-\u{0f17}\u{0f1a}-\u{0f34}\u{0f3e}-\u{0f47}\u{0f49}-\u{0f6a}\u{0f88}-\u{0f8b}\u{0fbe}-\u{0fc5}\u{0fc7}-\u{0fcc}\u{1000}-\u{1021}\u{1023}-\u{1027}\u{1029}-\u{102a}\u{1040}-\u{1057}\u{10a0}-\u{10c5}\u{10d0}-\u{10f8}\u{1100}-\u{1159}\u{115f}-\u{11a2}\u{11a8}-\u{11f9}\u{1200}-\u{1206}\u{1208}-\u{1246}\u{124a}-\u{124d}\u{1250}-\u{1256}\u{125a}-\u{125d}\u{1260}-\u{1286}\u{128a}-\u{128d}\u{1290}-\u{12ae}\u{12b2}-\u{12b5}\u{12b8}-\u{12be}\u{12c2}-\u{12c5}\u{12c8}-\u{12ce}\u{12d0}-\u{12d6}\u{12d8}-\u{12ee}\u{12f0}-\u{130e}\u{1312}-\u{1315}\u{1318}-\u{131e}\u{1320}-\u{1346}\u{1348}-\u{135a}\u{1361}-\u{137c}\u{13a0}-\u{13f4}\u{1401}-\u{1676}\u{1681}-\u{169a}\u{16a0}-\u{16f0}\u{1700}-\u{170c}\u{170e}-\u{1711}\u{1720}-\u{1731}\u{1735}-\u{1736}\u{1740}-\u{1751}\u{1760}-\u{176c}\u{176e}-\u{1770}\u{1780}-\u{17b6}\u{17be}-\u{17c5}\u{17c7}-\u{17c8}\u{17d4}-\u{17da}\u{17e0}-\u{17e9}\u{1810}-\u{1819}\u{1820}-\u{1877}\u{1880}-\u{18a8}\u{1e00}-\u{1e9b}\u{1ea0}-\u{1ef9}\u{1f00}-\u{1f15}\u{1f18}-\u{1f1d}\u{1f20}-\u{1f45}\u{1f48}-\u{1f4d}\u{1f50}-\u{1f57}\u{1f5f}-\u{1f7d}\u{1f80}-\u{1fb4}\u{1fb6}-\u{1fbc}\u{1fc2}-\u{1fc4}\u{1fc6}-\u{1fcc}\u{1fd0}-\u{1fd3}\u{1fd6}-\u{1fdb}\u{1fe0}-\u{1fec}\u{1ff2}-\u{1ff4}\u{1ff6}-\u{1ffc}\u{210a}-\u{2113}\u{2119}-\u{211d}\u{212a}-\u{212d}\u{212f}-\u{2131}\u{2133}-\u{2139}\u{213d}-\u{213f}\u{2145}-\u{2149}\u{2160}-\u{2183}\u{2336}-\u{237a}\u{249c}-\u{24e9}\u{3005}-\u{3007}\u{3021}-\u{3029}\u{3031}-\u{3035}\u{3038}-\u{303c}\u{3041}-\u{3096}\u{309d}-\u{309f}\u{30a1}-\u{30fa}\u{30fc}-\u{30ff}\u{3105}-\u{312c}\u{3131}-\u{318e}\u{3190}-\u{31b7}\u{31f0}-\u{321c}\u{3220}-\u{3243}\u{3260}-\u{327b}\u{327f}-\u{32b0}\u{32c0}-\u{32cb}\u{32d0}-\u{32fe}\u{3300}-\u{3376}\u{337b}-\u{33dd}\u{33e0}-\u{33fe}\u{3400}-\u{4db5}\u{4e00}-\u{9fa5}\u{a000}-\u{a48c}\u{ac00}-\u{d7a3}\u{e000}-\u{fa2d}\u{fa30}-\u{fa6a}\u{fb00}-\u{fb06}\u{fb13}-\u{fb17}\u{ff21}-\u{ff3a}\u{ff41}-\u{ff5a}\u{ff66}-\u{ffbe}\u{ffc2}-\u{ffc7}\u{ffca}-\u{ffcf}\u{ffd2}-\u{ffd7}\u{ffda}-\u{ffdc}\u{10300}-\u{1031e}\u{10320}-\u{10323}\u{10330}-\u{1034a}\u{10400}-\u{10425}\u{10428}-\u{1044d}\u{1d000}-\u{1d0f5}\u{1d100}-\u{1d126}\u{1d12a}-\u{1d166}\u{1d16a}-\u{1d172}\u{1d183}-\u{1d184}\u{1d18c}-\u{1d1a9}\u{1d1ae}-\u{1d1dd}\u{1d400}-\u{1d454}\u{1d456}-\u{1d49c}\u{1d49e}-\u{1d49f}\u{1d4a5}-\u{1d4a6}\u{1d4a9}-\u{1d4ac}\u{1d4ae}-\u{1d4b9}\u{1d4bd}-\u{1d4c0}\u{1d4c2}-\u{1d4c3}\u{1d4c5}-\u{1d505}\u{1d507}-\u{1d50a}\u{1d50d}-\u{1d514}\u{1d516}-\u{1d51c}\u{1d51e}-\u{1d539}\u{1d53b}-\u{1d53e}\u{1d540}-\u{1d544}\u{1d54a}-\u{1d550}\u{1d552}-\u{1d6a3}\u{1d6a8}-\u{1d7c9}\u{20000}-\u{2a6d6}\u{2f800}-\u{2fa1d}\u{f0000}-\u{ffffd}\u{100000}-\u{10fffd}\p{Cs}]).*?(?-mix:[\u{05be 05c0 05c3 061b 061f 06dd 0710 07b1 200f fb1d fb3e}\u{05d0}-\u{05ea}\u{05f0}-\u{05f4}\u{0621}-\u{063a}\u{0640}-\u{064a}\u{066d}-\u{066f}\u{0671}-\u{06d5}\u{06e5}-\u{06e6}\u{06fa}-\u{06fe}\u{0700}-\u{070d}\u{0712}-\u{072c}\u{0780}-\u{07a5}\u{fb1f}-\u{fb28}\u{fb2a}-\u{fb36}\u{fb38}-\u{fb3c}\u{fb40}-\u{fb41}\u{fb43}-\u{fb44}\u{fb46}-\u{fbb1}\u{fbd3}-\u{fd3d}\u{fd50}-\u{fd8f}\u{fd92}-\u{fdc7}\u{fdf0}-\u{fdfc}\u{fe70}-\u{fe74}\u{fe76}-\u{fefc}]))/.freeze

    BIDI_DESC_REQ3 = "A string with RandALCat characters must start and end with RandALCat characters."

    # Bidirectional Characters [StringPrep, §6], Requirement 3
    # >>>
    #   If a string contains any RandALCat character, a RandALCat
    #   character MUST be the first character of the string, and a
    #   RandALCat character MUST be the last character of the string.
    BIDI_FAILS_REQ3 = /(?m-ix:\A(?-mix:[^\u{05be 05c0 05c3 061b 061f 06dd 0710 07b1 200f fb1d fb3e}\u{05d0}-\u{05ea}\u{05f0}-\u{05f4}\u{0621}-\u{063a}\u{0640}-\u{064a}\u{066d}-\u{066f}\u{0671}-\u{06d5}\u{06e5}-\u{06e6}\u{06fa}-\u{06fe}\u{0700}-\u{070d}\u{0712}-\u{072c}\u{0780}-\u{07a5}\u{fb1f}-\u{fb28}\u{fb2a}-\u{fb36}\u{fb38}-\u{fb3c}\u{fb40}-\u{fb41}\u{fb43}-\u{fb44}\u{fb46}-\u{fbb1}\u{fbd3}-\u{fd3d}\u{fd50}-\u{fd8f}\u{fd92}-\u{fdc7}\u{fdf0}-\u{fdfc}\u{fe70}-\u{fe74}\u{fe76}-\u{fefc}]).*?(?-mix:[\u{05be 05c0 05c3 061b 061f 06dd 0710 07b1 200f fb1d fb3e}\u{05d0}-\u{05ea}\u{05f0}-\u{05f4}\u{0621}-\u{063a}\u{0640}-\u{064a}\u{066d}-\u{066f}\u{0671}-\u{06d5}\u{06e5}-\u{06e6}\u{06fa}-\u{06fe}\u{0700}-\u{070d}\u{0712}-\u{072c}\u{0780}-\u{07a5}\u{fb1f}-\u{fb28}\u{fb2a}-\u{fb36}\u{fb38}-\u{fb3c}\u{fb40}-\u{fb41}\u{fb43}-\u{fb44}\u{fb46}-\u{fbb1}\u{fbd3}-\u{fd3d}\u{fd50}-\u{fd8f}\u{fd92}-\u{fdc7}\u{fdf0}-\u{fdfc}\u{fe70}-\u{fe74}\u{fe76}-\u{fefc}]))|(?m-ix:(?-mix:[\u{05be 05c0 05c3 061b 061f 06dd 0710 07b1 200f fb1d fb3e}\u{05d0}-\u{05ea}\u{05f0}-\u{05f4}\u{0621}-\u{063a}\u{0640}-\u{064a}\u{066d}-\u{066f}\u{0671}-\u{06d5}\u{06e5}-\u{06e6}\u{06fa}-\u{06fe}\u{0700}-\u{070d}\u{0712}-\u{072c}\u{0780}-\u{07a5}\u{fb1f}-\u{fb28}\u{fb2a}-\u{fb36}\u{fb38}-\u{fb3c}\u{fb40}-\u{fb41}\u{fb43}-\u{fb44}\u{fb46}-\u{fbb1}\u{fbd3}-\u{fd3d}\u{fd50}-\u{fd8f}\u{fd92}-\u{fdc7}\u{fdf0}-\u{fdfc}\u{fe70}-\u{fe74}\u{fe76}-\u{fefc}]).*?(?-mix:[^\u{05be 05c0 05c3 061b 061f 06dd 0710 07b1 200f fb1d fb3e}\u{05d0}-\u{05ea}\u{05f0}-\u{05f4}\u{0621}-\u{063a}\u{0640}-\u{064a}\u{066d}-\u{066f}\u{0671}-\u{06d5}\u{06e5}-\u{06e6}\u{06fa}-\u{06fe}\u{0700}-\u{070d}\u{0712}-\u{072c}\u{0780}-\u{07a5}\u{fb1f}-\u{fb28}\u{fb2a}-\u{fb36}\u{fb38}-\u{fb3c}\u{fb40}-\u{fb41}\u{fb43}-\u{fb44}\u{fb46}-\u{fbb1}\u{fbd3}-\u{fd3d}\u{fd50}-\u{fd8f}\u{fd92}-\u{fdc7}\u{fdf0}-\u{fdfc}\u{fe70}-\u{fe74}\u{fe76}-\u{fefc}])\z)/.freeze

    # Bidirectional Characters [StringPrep, §6]
    BIDI_FAILURE = /(?-mix:(?m-ix:(?-mix:[\u{05be 05c0 05c3 061b 061f 06dd 0710 07b1 200f fb1d fb3e}\u{05d0}-\u{05ea}\u{05f0}-\u{05f4}\u{0621}-\u{063a}\u{0640}-\u{064a}\u{066d}-\u{066f}\u{0671}-\u{06d5}\u{06e5}-\u{06e6}\u{06fa}-\u{06fe}\u{0700}-\u{070d}\u{0712}-\u{072c}\u{0780}-\u{07a5}\u{fb1f}-\u{fb28}\u{fb2a}-\u{fb36}\u{fb38}-\u{fb3c}\u{fb40}-\u{fb41}\u{fb43}-\u{fb44}\u{fb46}-\u{fbb1}\u{fbd3}-\u{fd3d}\u{fd50}-\u{fd8f}\u{fd92}-\u{fdc7}\u{fdf0}-\u{fdfc}\u{fe70}-\u{fe74}\u{fe76}-\u{fefc}]).*?(?-mix:[\u{00aa 00b5 00ba 02ee 037a 0386 038c 0589 0903 0950 09b2 09d7 0a5e 0a83 0a8d 0ac9 0ad0 0ae0 0b40 0b57 0b83 0b9c 0bd7 0cbe 0cde 0d57 0dbd 0e84 0e8a 0e8d 0ea5 0ea7 0ebd 0ec6 0f36 0f38 0f7f 0f85 0fcf 102c 1031 1038 10fb 1248 1258 1288 12b0 12c0 1310 17dc 1f59 1f5b 1f5d 1fbe 200e 2071 207f 2102 2107 2115 2124 2126 2128 2395 1d4a2 1d4bb 1d546}\u{0041}-\u{005a}\u{0061}-\u{007a}\u{00c0}-\u{00d6}\u{00d8}-\u{00f6}\u{00f8}-\u{0220}\u{0222}-\u{0233}\u{0250}-\u{02ad}\u{02b0}-\u{02b8}\u{02bb}-\u{02c1}\u{02d0}-\u{02d1}\u{02e0}-\u{02e4}\u{0388}-\u{038a}\u{038e}-\u{03a1}\u{03a3}-\u{03ce}\u{03d0}-\u{03f5}\u{0400}-\u{0482}\u{048a}-\u{04ce}\u{04d0}-\u{04f5}\u{04f8}-\u{04f9}\u{0500}-\u{050f}\u{0531}-\u{0556}\u{0559}-\u{055f}\u{0561}-\u{0587}\u{0905}-\u{0939}\u{093d}-\u{0940}\u{0949}-\u{094c}\u{0958}-\u{0961}\u{0964}-\u{0970}\u{0982}-\u{0983}\u{0985}-\u{098c}\u{098f}-\u{0990}\u{0993}-\u{09a8}\u{09aa}-\u{09b0}\u{09b6}-\u{09b9}\u{09be}-\u{09c0}\u{09c7}-\u{09c8}\u{09cb}-\u{09cc}\u{09dc}-\u{09dd}\u{09df}-\u{09e1}\u{09e6}-\u{09f1}\u{09f4}-\u{09fa}\u{0a05}-\u{0a0a}\u{0a0f}-\u{0a10}\u{0a13}-\u{0a28}\u{0a2a}-\u{0a30}\u{0a32}-\u{0a33}\u{0a35}-\u{0a36}\u{0a38}-\u{0a39}\u{0a3e}-\u{0a40}\u{0a59}-\u{0a5c}\u{0a66}-\u{0a6f}\u{0a72}-\u{0a74}\u{0a85}-\u{0a8b}\u{0a8f}-\u{0a91}\u{0a93}-\u{0aa8}\u{0aaa}-\u{0ab0}\u{0ab2}-\u{0ab3}\u{0ab5}-\u{0ab9}\u{0abd}-\u{0ac0}\u{0acb}-\u{0acc}\u{0ae6}-\u{0aef}\u{0b02}-\u{0b03}\u{0b05}-\u{0b0c}\u{0b0f}-\u{0b10}\u{0b13}-\u{0b28}\u{0b2a}-\u{0b30}\u{0b32}-\u{0b33}\u{0b36}-\u{0b39}\u{0b3d}-\u{0b3e}\u{0b47}-\u{0b48}\u{0b4b}-\u{0b4c}\u{0b5c}-\u{0b5d}\u{0b5f}-\u{0b61}\u{0b66}-\u{0b70}\u{0b85}-\u{0b8a}\u{0b8e}-\u{0b90}\u{0b92}-\u{0b95}\u{0b99}-\u{0b9a}\u{0b9e}-\u{0b9f}\u{0ba3}-\u{0ba4}\u{0ba8}-\u{0baa}\u{0bae}-\u{0bb5}\u{0bb7}-\u{0bb9}\u{0bbe}-\u{0bbf}\u{0bc1}-\u{0bc2}\u{0bc6}-\u{0bc8}\u{0bca}-\u{0bcc}\u{0be7}-\u{0bf2}\u{0c01}-\u{0c03}\u{0c05}-\u{0c0c}\u{0c0e}-\u{0c10}\u{0c12}-\u{0c28}\u{0c2a}-\u{0c33}\u{0c35}-\u{0c39}\u{0c41}-\u{0c44}\u{0c60}-\u{0c61}\u{0c66}-\u{0c6f}\u{0c82}-\u{0c83}\u{0c85}-\u{0c8c}\u{0c8e}-\u{0c90}\u{0c92}-\u{0ca8}\u{0caa}-\u{0cb3}\u{0cb5}-\u{0cb9}\u{0cc0}-\u{0cc4}\u{0cc7}-\u{0cc8}\u{0cca}-\u{0ccb}\u{0cd5}-\u{0cd6}\u{0ce0}-\u{0ce1}\u{0ce6}-\u{0cef}\u{0d02}-\u{0d03}\u{0d05}-\u{0d0c}\u{0d0e}-\u{0d10}\u{0d12}-\u{0d28}\u{0d2a}-\u{0d39}\u{0d3e}-\u{0d40}\u{0d46}-\u{0d48}\u{0d4a}-\u{0d4c}\u{0d60}-\u{0d61}\u{0d66}-\u{0d6f}\u{0d82}-\u{0d83}\u{0d85}-\u{0d96}\u{0d9a}-\u{0db1}\u{0db3}-\u{0dbb}\u{0dc0}-\u{0dc6}\u{0dcf}-\u{0dd1}\u{0dd8}-\u{0ddf}\u{0df2}-\u{0df4}\u{0e01}-\u{0e30}\u{0e32}-\u{0e33}\u{0e40}-\u{0e46}\u{0e4f}-\u{0e5b}\u{0e81}-\u{0e82}\u{0e87}-\u{0e88}\u{0e94}-\u{0e97}\u{0e99}-\u{0e9f}\u{0ea1}-\u{0ea3}\u{0eaa}-\u{0eab}\u{0ead}-\u{0eb0}\u{0eb2}-\u{0eb3}\u{0ec0}-\u{0ec4}\u{0ed0}-\u{0ed9}\u{0edc}-\u{0edd}\u{0f00}-\u{0f17}\u{0f1a}-\u{0f34}\u{0f3e}-\u{0f47}\u{0f49}-\u{0f6a}\u{0f88}-\u{0f8b}\u{0fbe}-\u{0fc5}\u{0fc7}-\u{0fcc}\u{1000}-\u{1021}\u{1023}-\u{1027}\u{1029}-\u{102a}\u{1040}-\u{1057}\u{10a0}-\u{10c5}\u{10d0}-\u{10f8}\u{1100}-\u{1159}\u{115f}-\u{11a2}\u{11a8}-\u{11f9}\u{1200}-\u{1206}\u{1208}-\u{1246}\u{124a}-\u{124d}\u{1250}-\u{1256}\u{125a}-\u{125d}\u{1260}-\u{1286}\u{128a}-\u{128d}\u{1290}-\u{12ae}\u{12b2}-\u{12b5}\u{12b8}-\u{12be}\u{12c2}-\u{12c5}\u{12c8}-\u{12ce}\u{12d0}-\u{12d6}\u{12d8}-\u{12ee}\u{12f0}-\u{130e}\u{1312}-\u{1315}\u{1318}-\u{131e}\u{1320}-\u{1346}\u{1348}-\u{135a}\u{1361}-\u{137c}\u{13a0}-\u{13f4}\u{1401}-\u{1676}\u{1681}-\u{169a}\u{16a0}-\u{16f0}\u{1700}-\u{170c}\u{170e}-\u{1711}\u{1720}-\u{1731}\u{1735}-\u{1736}\u{1740}-\u{1751}\u{1760}-\u{176c}\u{176e}-\u{1770}\u{1780}-\u{17b6}\u{17be}-\u{17c5}\u{17c7}-\u{17c8}\u{17d4}-\u{17da}\u{17e0}-\u{17e9}\u{1810}-\u{1819}\u{1820}-\u{1877}\u{1880}-\u{18a8}\u{1e00}-\u{1e9b}\u{1ea0}-\u{1ef9}\u{1f00}-\u{1f15}\u{1f18}-\u{1f1d}\u{1f20}-\u{1f45}\u{1f48}-\u{1f4d}\u{1f50}-\u{1f57}\u{1f5f}-\u{1f7d}\u{1f80}-\u{1fb4}\u{1fb6}-\u{1fbc}\u{1fc2}-\u{1fc4}\u{1fc6}-\u{1fcc}\u{1fd0}-\u{1fd3}\u{1fd6}-\u{1fdb}\u{1fe0}-\u{1fec}\u{1ff2}-\u{1ff4}\u{1ff6}-\u{1ffc}\u{210a}-\u{2113}\u{2119}-\u{211d}\u{212a}-\u{212d}\u{212f}-\u{2131}\u{2133}-\u{2139}\u{213d}-\u{213f}\u{2145}-\u{2149}\u{2160}-\u{2183}\u{2336}-\u{237a}\u{249c}-\u{24e9}\u{3005}-\u{3007}\u{3021}-\u{3029}\u{3031}-\u{3035}\u{3038}-\u{303c}\u{3041}-\u{3096}\u{309d}-\u{309f}\u{30a1}-\u{30fa}\u{30fc}-\u{30ff}\u{3105}-\u{312c}\u{3131}-\u{318e}\u{3190}-\u{31b7}\u{31f0}-\u{321c}\u{3220}-\u{3243}\u{3260}-\u{327b}\u{327f}-\u{32b0}\u{32c0}-\u{32cb}\u{32d0}-\u{32fe}\u{3300}-\u{3376}\u{337b}-\u{33dd}\u{33e0}-\u{33fe}\u{3400}-\u{4db5}\u{4e00}-\u{9fa5}\u{a000}-\u{a48c}\u{ac00}-\u{d7a3}\u{e000}-\u{fa2d}\u{fa30}-\u{fa6a}\u{fb00}-\u{fb06}\u{fb13}-\u{fb17}\u{ff21}-\u{ff3a}\u{ff41}-\u{ff5a}\u{ff66}-\u{ffbe}\u{ffc2}-\u{ffc7}\u{ffca}-\u{ffcf}\u{ffd2}-\u{ffd7}\u{ffda}-\u{ffdc}\u{10300}-\u{1031e}\u{10320}-\u{10323}\u{10330}-\u{1034a}\u{10400}-\u{10425}\u{10428}-\u{1044d}\u{1d000}-\u{1d0f5}\u{1d100}-\u{1d126}\u{1d12a}-\u{1d166}\u{1d16a}-\u{1d172}\u{1d183}-\u{1d184}\u{1d18c}-\u{1d1a9}\u{1d1ae}-\u{1d1dd}\u{1d400}-\u{1d454}\u{1d456}-\u{1d49c}\u{1d49e}-\u{1d49f}\u{1d4a5}-\u{1d4a6}\u{1d4a9}-\u{1d4ac}\u{1d4ae}-\u{1d4b9}\u{1d4bd}-\u{1d4c0}\u{1d4c2}-\u{1d4c3}\u{1d4c5}-\u{1d505}\u{1d507}-\u{1d50a}\u{1d50d}-\u{1d514}\u{1d516}-\u{1d51c}\u{1d51e}-\u{1d539}\u{1d53b}-\u{1d53e}\u{1d540}-\u{1d544}\u{1d54a}-\u{1d550}\u{1d552}-\u{1d6a3}\u{1d6a8}-\u{1d7c9}\u{20000}-\u{2a6d6}\u{2f800}-\u{2fa1d}\u{f0000}-\u{ffffd}\u{100000}-\u{10fffd}\p{Cs}]))|(?m-ix:(?-mix:[\u{00aa 00b5 00ba 02ee 037a 0386 038c 0589 0903 0950 09b2 09d7 0a5e 0a83 0a8d 0ac9 0ad0 0ae0 0b40 0b57 0b83 0b9c 0bd7 0cbe 0cde 0d57 0dbd 0e84 0e8a 0e8d 0ea5 0ea7 0ebd 0ec6 0f36 0f38 0f7f 0f85 0fcf 102c 1031 1038 10fb 1248 1258 1288 12b0 12c0 1310 17dc 1f59 1f5b 1f5d 1fbe 200e 2071 207f 2102 2107 2115 2124 2126 2128 2395 1d4a2 1d4bb 1d546}\u{0041}-\u{005a}\u{0061}-\u{007a}\u{00c0}-\u{00d6}\u{00d8}-\u{00f6}\u{00f8}-\u{0220}\u{0222}-\u{0233}\u{0250}-\u{02ad}\u{02b0}-\u{02b8}\u{02bb}-\u{02c1}\u{02d0}-\u{02d1}\u{02e0}-\u{02e4}\u{0388}-\u{038a}\u{038e}-\u{03a1}\u{03a3}-\u{03ce}\u{03d0}-\u{03f5}\u{0400}-\u{0482}\u{048a}-\u{04ce}\u{04d0}-\u{04f5}\u{04f8}-\u{04f9}\u{0500}-\u{050f}\u{0531}-\u{0556}\u{0559}-\u{055f}\u{0561}-\u{0587}\u{0905}-\u{0939}\u{093d}-\u{0940}\u{0949}-\u{094c}\u{0958}-\u{0961}\u{0964}-\u{0970}\u{0982}-\u{0983}\u{0985}-\u{098c}\u{098f}-\u{0990}\u{0993}-\u{09a8}\u{09aa}-\u{09b0}\u{09b6}-\u{09b9}\u{09be}-\u{09c0}\u{09c7}-\u{09c8}\u{09cb}-\u{09cc}\u{09dc}-\u{09dd}\u{09df}-\u{09e1}\u{09e6}-\u{09f1}\u{09f4}-\u{09fa}\u{0a05}-\u{0a0a}\u{0a0f}-\u{0a10}\u{0a13}-\u{0a28}\u{0a2a}-\u{0a30}\u{0a32}-\u{0a33}\u{0a35}-\u{0a36}\u{0a38}-\u{0a39}\u{0a3e}-\u{0a40}\u{0a59}-\u{0a5c}\u{0a66}-\u{0a6f}\u{0a72}-\u{0a74}\u{0a85}-\u{0a8b}\u{0a8f}-\u{0a91}\u{0a93}-\u{0aa8}\u{0aaa}-\u{0ab0}\u{0ab2}-\u{0ab3}\u{0ab5}-\u{0ab9}\u{0abd}-\u{0ac0}\u{0acb}-\u{0acc}\u{0ae6}-\u{0aef}\u{0b02}-\u{0b03}\u{0b05}-\u{0b0c}\u{0b0f}-\u{0b10}\u{0b13}-\u{0b28}\u{0b2a}-\u{0b30}\u{0b32}-\u{0b33}\u{0b36}-\u{0b39}\u{0b3d}-\u{0b3e}\u{0b47}-\u{0b48}\u{0b4b}-\u{0b4c}\u{0b5c}-\u{0b5d}\u{0b5f}-\u{0b61}\u{0b66}-\u{0b70}\u{0b85}-\u{0b8a}\u{0b8e}-\u{0b90}\u{0b92}-\u{0b95}\u{0b99}-\u{0b9a}\u{0b9e}-\u{0b9f}\u{0ba3}-\u{0ba4}\u{0ba8}-\u{0baa}\u{0bae}-\u{0bb5}\u{0bb7}-\u{0bb9}\u{0bbe}-\u{0bbf}\u{0bc1}-\u{0bc2}\u{0bc6}-\u{0bc8}\u{0bca}-\u{0bcc}\u{0be7}-\u{0bf2}\u{0c01}-\u{0c03}\u{0c05}-\u{0c0c}\u{0c0e}-\u{0c10}\u{0c12}-\u{0c28}\u{0c2a}-\u{0c33}\u{0c35}-\u{0c39}\u{0c41}-\u{0c44}\u{0c60}-\u{0c61}\u{0c66}-\u{0c6f}\u{0c82}-\u{0c83}\u{0c85}-\u{0c8c}\u{0c8e}-\u{0c90}\u{0c92}-\u{0ca8}\u{0caa}-\u{0cb3}\u{0cb5}-\u{0cb9}\u{0cc0}-\u{0cc4}\u{0cc7}-\u{0cc8}\u{0cca}-\u{0ccb}\u{0cd5}-\u{0cd6}\u{0ce0}-\u{0ce1}\u{0ce6}-\u{0cef}\u{0d02}-\u{0d03}\u{0d05}-\u{0d0c}\u{0d0e}-\u{0d10}\u{0d12}-\u{0d28}\u{0d2a}-\u{0d39}\u{0d3e}-\u{0d40}\u{0d46}-\u{0d48}\u{0d4a}-\u{0d4c}\u{0d60}-\u{0d61}\u{0d66}-\u{0d6f}\u{0d82}-\u{0d83}\u{0d85}-\u{0d96}\u{0d9a}-\u{0db1}\u{0db3}-\u{0dbb}\u{0dc0}-\u{0dc6}\u{0dcf}-\u{0dd1}\u{0dd8}-\u{0ddf}\u{0df2}-\u{0df4}\u{0e01}-\u{0e30}\u{0e32}-\u{0e33}\u{0e40}-\u{0e46}\u{0e4f}-\u{0e5b}\u{0e81}-\u{0e82}\u{0e87}-\u{0e88}\u{0e94}-\u{0e97}\u{0e99}-\u{0e9f}\u{0ea1}-\u{0ea3}\u{0eaa}-\u{0eab}\u{0ead}-\u{0eb0}\u{0eb2}-\u{0eb3}\u{0ec0}-\u{0ec4}\u{0ed0}-\u{0ed9}\u{0edc}-\u{0edd}\u{0f00}-\u{0f17}\u{0f1a}-\u{0f34}\u{0f3e}-\u{0f47}\u{0f49}-\u{0f6a}\u{0f88}-\u{0f8b}\u{0fbe}-\u{0fc5}\u{0fc7}-\u{0fcc}\u{1000}-\u{1021}\u{1023}-\u{1027}\u{1029}-\u{102a}\u{1040}-\u{1057}\u{10a0}-\u{10c5}\u{10d0}-\u{10f8}\u{1100}-\u{1159}\u{115f}-\u{11a2}\u{11a8}-\u{11f9}\u{1200}-\u{1206}\u{1208}-\u{1246}\u{124a}-\u{124d}\u{1250}-\u{1256}\u{125a}-\u{125d}\u{1260}-\u{1286}\u{128a}-\u{128d}\u{1290}-\u{12ae}\u{12b2}-\u{12b5}\u{12b8}-\u{12be}\u{12c2}-\u{12c5}\u{12c8}-\u{12ce}\u{12d0}-\u{12d6}\u{12d8}-\u{12ee}\u{12f0}-\u{130e}\u{1312}-\u{1315}\u{1318}-\u{131e}\u{1320}-\u{1346}\u{1348}-\u{135a}\u{1361}-\u{137c}\u{13a0}-\u{13f4}\u{1401}-\u{1676}\u{1681}-\u{169a}\u{16a0}-\u{16f0}\u{1700}-\u{170c}\u{170e}-\u{1711}\u{1720}-\u{1731}\u{1735}-\u{1736}\u{1740}-\u{1751}\u{1760}-\u{176c}\u{176e}-\u{1770}\u{1780}-\u{17b6}\u{17be}-\u{17c5}\u{17c7}-\u{17c8}\u{17d4}-\u{17da}\u{17e0}-\u{17e9}\u{1810}-\u{1819}\u{1820}-\u{1877}\u{1880}-\u{18a8}\u{1e00}-\u{1e9b}\u{1ea0}-\u{1ef9}\u{1f00}-\u{1f15}\u{1f18}-\u{1f1d}\u{1f20}-\u{1f45}\u{1f48}-\u{1f4d}\u{1f50}-\u{1f57}\u{1f5f}-\u{1f7d}\u{1f80}-\u{1fb4}\u{1fb6}-\u{1fbc}\u{1fc2}-\u{1fc4}\u{1fc6}-\u{1fcc}\u{1fd0}-\u{1fd3}\u{1fd6}-\u{1fdb}\u{1fe0}-\u{1fec}\u{1ff2}-\u{1ff4}\u{1ff6}-\u{1ffc}\u{210a}-\u{2113}\u{2119}-\u{211d}\u{212a}-\u{212d}\u{212f}-\u{2131}\u{2133}-\u{2139}\u{213d}-\u{213f}\u{2145}-\u{2149}\u{2160}-\u{2183}\u{2336}-\u{237a}\u{249c}-\u{24e9}\u{3005}-\u{3007}\u{3021}-\u{3029}\u{3031}-\u{3035}\u{3038}-\u{303c}\u{3041}-\u{3096}\u{309d}-\u{309f}\u{30a1}-\u{30fa}\u{30fc}-\u{30ff}\u{3105}-\u{312c}\u{3131}-\u{318e}\u{3190}-\u{31b7}\u{31f0}-\u{321c}\u{3220}-\u{3243}\u{3260}-\u{327b}\u{327f}-\u{32b0}\u{32c0}-\u{32cb}\u{32d0}-\u{32fe}\u{3300}-\u{3376}\u{337b}-\u{33dd}\u{33e0}-\u{33fe}\u{3400}-\u{4db5}\u{4e00}-\u{9fa5}\u{a000}-\u{a48c}\u{ac00}-\u{d7a3}\u{e000}-\u{fa2d}\u{fa30}-\u{fa6a}\u{fb00}-\u{fb06}\u{fb13}-\u{fb17}\u{ff21}-\u{ff3a}\u{ff41}-\u{ff5a}\u{ff66}-\u{ffbe}\u{ffc2}-\u{ffc7}\u{ffca}-\u{ffcf}\u{ffd2}-\u{ffd7}\u{ffda}-\u{ffdc}\u{10300}-\u{1031e}\u{10320}-\u{10323}\u{10330}-\u{1034a}\u{10400}-\u{10425}\u{10428}-\u{1044d}\u{1d000}-\u{1d0f5}\u{1d100}-\u{1d126}\u{1d12a}-\u{1d166}\u{1d16a}-\u{1d172}\u{1d183}-\u{1d184}\u{1d18c}-\u{1d1a9}\u{1d1ae}-\u{1d1dd}\u{1d400}-\u{1d454}\u{1d456}-\u{1d49c}\u{1d49e}-\u{1d49f}\u{1d4a5}-\u{1d4a6}\u{1d4a9}-\u{1d4ac}\u{1d4ae}-\u{1d4b9}\u{1d4bd}-\u{1d4c0}\u{1d4c2}-\u{1d4c3}\u{1d4c5}-\u{1d505}\u{1d507}-\u{1d50a}\u{1d50d}-\u{1d514}\u{1d516}-\u{1d51c}\u{1d51e}-\u{1d539}\u{1d53b}-\u{1d53e}\u{1d540}-\u{1d544}\u{1d54a}-\u{1d550}\u{1d552}-\u{1d6a3}\u{1d6a8}-\u{1d7c9}\u{20000}-\u{2a6d6}\u{2f800}-\u{2fa1d}\u{f0000}-\u{ffffd}\u{100000}-\u{10fffd}\p{Cs}]).*?(?-mix:[\u{05be 05c0 05c3 061b 061f 06dd 0710 07b1 200f fb1d fb3e}\u{05d0}-\u{05ea}\u{05f0}-\u{05f4}\u{0621}-\u{063a}\u{0640}-\u{064a}\u{066d}-\u{066f}\u{0671}-\u{06d5}\u{06e5}-\u{06e6}\u{06fa}-\u{06fe}\u{0700}-\u{070d}\u{0712}-\u{072c}\u{0780}-\u{07a5}\u{fb1f}-\u{fb28}\u{fb2a}-\u{fb36}\u{fb38}-\u{fb3c}\u{fb40}-\u{fb41}\u{fb43}-\u{fb44}\u{fb46}-\u{fbb1}\u{fbd3}-\u{fd3d}\u{fd50}-\u{fd8f}\u{fd92}-\u{fdc7}\u{fdf0}-\u{fdfc}\u{fe70}-\u{fe74}\u{fe76}-\u{fefc}])))|(?-mix:(?m-ix:\A(?-mix:[^\u{05be 05c0 05c3 061b 061f 06dd 0710 07b1 200f fb1d fb3e}\u{05d0}-\u{05ea}\u{05f0}-\u{05f4}\u{0621}-\u{063a}\u{0640}-\u{064a}\u{066d}-\u{066f}\u{0671}-\u{06d5}\u{06e5}-\u{06e6}\u{06fa}-\u{06fe}\u{0700}-\u{070d}\u{0712}-\u{072c}\u{0780}-\u{07a5}\u{fb1f}-\u{fb28}\u{fb2a}-\u{fb36}\u{fb38}-\u{fb3c}\u{fb40}-\u{fb41}\u{fb43}-\u{fb44}\u{fb46}-\u{fbb1}\u{fbd3}-\u{fd3d}\u{fd50}-\u{fd8f}\u{fd92}-\u{fdc7}\u{fdf0}-\u{fdfc}\u{fe70}-\u{fe74}\u{fe76}-\u{fefc}]).*?(?-mix:[\u{05be 05c0 05c3 061b 061f 06dd 0710 07b1 200f fb1d fb3e}\u{05d0}-\u{05ea}\u{05f0}-\u{05f4}\u{0621}-\u{063a}\u{0640}-\u{064a}\u{066d}-\u{066f}\u{0671}-\u{06d5}\u{06e5}-\u{06e6}\u{06fa}-\u{06fe}\u{0700}-\u{070d}\u{0712}-\u{072c}\u{0780}-\u{07a5}\u{fb1f}-\u{fb28}\u{fb2a}-\u{fb36}\u{fb38}-\u{fb3c}\u{fb40}-\u{fb41}\u{fb43}-\u{fb44}\u{fb46}-\u{fbb1}\u{fbd3}-\u{fd3d}\u{fd50}-\u{fd8f}\u{fd92}-\u{fdc7}\u{fdf0}-\u{fdfc}\u{fe70}-\u{fe74}\u{fe76}-\u{fefc}]))|(?m-ix:(?-mix:[\u{05be 05c0 05c3 061b 061f 06dd 0710 07b1 200f fb1d fb3e}\u{05d0}-\u{05ea}\u{05f0}-\u{05f4}\u{0621}-\u{063a}\u{0640}-\u{064a}\u{066d}-\u{066f}\u{0671}-\u{06d5}\u{06e5}-\u{06e6}\u{06fa}-\u{06fe}\u{0700}-\u{070d}\u{0712}-\u{072c}\u{0780}-\u{07a5}\u{fb1f}-\u{fb28}\u{fb2a}-\u{fb36}\u{fb38}-\u{fb3c}\u{fb40}-\u{fb41}\u{fb43}-\u{fb44}\u{fb46}-\u{fbb1}\u{fbd3}-\u{fd3d}\u{fd50}-\u{fd8f}\u{fd92}-\u{fdc7}\u{fdf0}-\u{fdfc}\u{fe70}-\u{fe74}\u{fe76}-\u{fefc}]).*?(?-mix:[^\u{05be 05c0 05c3 061b 061f 06dd 0710 07b1 200f fb1d fb3e}\u{05d0}-\u{05ea}\u{05f0}-\u{05f4}\u{0621}-\u{063a}\u{0640}-\u{064a}\u{066d}-\u{066f}\u{0671}-\u{06d5}\u{06e5}-\u{06e6}\u{06fa}-\u{06fe}\u{0700}-\u{070d}\u{0712}-\u{072c}\u{0780}-\u{07a5}\u{fb1f}-\u{fb28}\u{fb2a}-\u{fb36}\u{fb38}-\u{fb3c}\u{fb40}-\u{fb41}\u{fb43}-\u{fb44}\u{fb46}-\u{fbb1}\u{fbd3}-\u{fd3d}\u{fd50}-\u{fd8f}\u{fd92}-\u{fdc7}\u{fdf0}-\u{fdfc}\u{fe70}-\u{fe74}\u{fe76}-\u{fefc}])\z))/.freeze

    # Names of each codepoint table in the RFC-3454 appendices
    TITLES = {
      "A.1" => "Unassigned code points in Unicode 3.2",
      "B.1" => "Commonly mapped to nothing",
      "B.2" => "Mapping for case-folding used with NFKC",
      "B.3" => "Mapping for case-folding used with no normalization",
      "C.1" => "Space characters",
      "C.1.1" => "ASCII space characters",
      "C.1.2" => "Non-ASCII space characters",
      "C.2" => "Control characters",
      "C.2.1" => "ASCII control characters",
      "C.2.2" => "Non-ASCII control characters",
      "C.3" => "Private use",
      "C.4" => "Non-character code points",
      "C.5" => "Surrogate codes",
      "C.6" => "Inappropriate for plain text",
      "C.7" => "Inappropriate for canonical representation",
      "C.8" => "Change display properties or are deprecated",
      "C.9" => "Tagging characters",
      "D.1" => "Characters with bidirectional property \"R\" or \"AL\"",
      "D.2" => "Characters with bidirectional property \"L\"",
    }.freeze

    # Regexps matching each codepoint table in the RFC-3454 appendices
    REGEXPS = {
      "A.1" => IN_A_1,
      "B.1" => IN_B_1,
      "B.2" => IN_B_2,
      "B.3" => IN_B_3,
      "C.1.1" => IN_C_1_1,
      "C.1.2" => IN_C_1_2,
      "C.2.1" => IN_C_2_1,
      "C.2.2" => IN_C_2_2,
      "C.3" => IN_C_3,
      "C.4" => IN_C_4,
      "C.5" => IN_C_5,
      "C.6" => IN_C_6,
      "C.7" => IN_C_7,
      "C.8" => IN_C_8,
      "C.9" => IN_C_9,
      "D.1" => IN_D_1,
      "D.2" => IN_D_2,
    }.freeze

    MAPPINGS = {
      "B.1" => [IN_B_1, MAP_B_1].freeze,
      "B.2" => [IN_B_2, MAP_B_2].freeze,
      "B.3" => [IN_B_3, MAP_B_3].freeze,
    }.freeze

  end
end
