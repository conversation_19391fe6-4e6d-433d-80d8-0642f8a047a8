# Rackup

`rackup` provides a command line interface for running a Rack-compatible application.

[![Development Status](https://github.com/rack/rackup/workflows/Test/badge.svg)](https://github.com/rack/rackup/actions?workflow=Test)

## Installation

``` bash
$ gem install rackup
```

## Usage

In a directory with your `config.ru` simply run the command:

``` bash
$ rackup
```

Your application should now be available locally, typically `http://localhost:9292`.

## Contributing

We welcome contributions to this project.

1.  Fork it.
2.  Create your feature branch (`git checkout -b my-new-feature`).
3.  Commit your changes (`git commit -am 'Add some feature'`).
4.  Push to the branch (`git push origin my-new-feature`).
5.  Create new Pull Request.
