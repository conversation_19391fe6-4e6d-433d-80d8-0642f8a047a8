{"name": "@rails/actiontext", "version": "7.1.3-4", "description": "Edit and display rich text in Rails applications", "module": "app/assets/javascripts/actiontext.esm.js", "main": "app/assets/javascripts/actiontext.js", "files": ["app/assets/javascripts/*.js"], "homepage": "https://rubyonrails.org/", "repository": {"type": "git", "url": "git+https://github.com/rails/rails.git"}, "bugs": {"url": "https://github.com/rails/rails/issues"}, "author": "37signals LLC", "contributors": ["<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "license": "MIT", "dependencies": {"@rails/activestorage": ">= 7.1.0-alpha"}, "peerDependencies": {"trix": "^2.0.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^19.0.1", "@rollup/plugin-node-resolve": "^11.0.1", "rollup": "^2.35.1", "trix": "^2.0.0"}, "scripts": {"build": "rollup --config rollup.config.js"}}