# encoding: utf-8
# frozen_string_literal: true
require 'mail/fields/common_address_field'

module Mail
  # = Sender Field
  #
  # The Sender field inherits sender Structured<PERSON><PERSON> and handles the Sender: header
  # field in the email.
  #
  # Sending sender to a mail message will instantiate a Mail::Field object that
  # has a SenderField as its field type.  This includes all Mail::CommonAddress
  # module instance metods.
  #
  # Only one Sender field can appear in a header, though it can have multiple
  # addresses and groups of addresses.
  #
  # == Examples:
  #
  #  mail = Mail.new
  #  mail.sender = '<PERSON><PERSON> <<EMAIL>>'
  #  mail.sender    #=> '<EMAIL>'
  #  mail[:sender]  #=> '#<Mail::Field:0x180e5e8 @field=#<Mail::SenderField:0x180e1c4
  #  mail['sender'] #=> '#<Mail::Field:0x180e5e8 @field=#<Mail::SenderField:0x180e1c4
  #  mail['Sender'] #=> '#<Mail::Field:0x180e5e8 @field=#<Mail::SenderField:0x180e1c4
  #
  #  mail[:sender].encoded   #=> "Sender: <PERSON><PERSON> <<EMAIL>>\r\n"
  #  mail[:sender].decoded   #=> '<PERSON>l <PERSON>dsaar <<EMAIL>>'
  #  mail[:sender].addresses #=> ['<EMAIL>']
  # <AUTHOR> <EMAIL>']
  class SenderField < CommonAddressField #:nodoc:
    NAME = 'Sender'

    def self.singular?
      true
    end

    def default
      address
    end

    def addresses
      Array(super.first)
    end
  end
end
