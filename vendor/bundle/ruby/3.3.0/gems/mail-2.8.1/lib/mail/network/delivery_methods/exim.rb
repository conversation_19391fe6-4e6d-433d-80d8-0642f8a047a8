# frozen_string_literal: true

module Mail
  # A delivery method implementation which sends via exim.
  #
  # To use this, first find out where the exim binary is on your computer,
  # if you are on a mac or unix box, it is usually in /usr/sbin/exim, this will
  # be your exim location.
  #
  #   Mail.defaults do
  #     delivery_method :exim
  #   end
  #
  # Or if your exim binary is not at '/usr/sbin/exim'
  #
  #   Mail.defaults do
  #     delivery_method :exim, :location => '/absolute/path/to/your/exim'
  #   end
  #
  # Then just deliver the email as normal:
  #
  #   Mail.deliver do
  #     to '<EMAIL>'
  #     from '<EMAIL>'
  #     subject 'testing exim'
  #     body 'testing exim'
  #   end
  #
  # Or by calling deliver on a Mail message
  #
  #   mail = Mail.new do
  #     to '<EMAIL>'
  #     from '<EMAIL>'
  #     subject 'testing exim'
  #     body 'testing exim'
  #   end
  #
  #   mail.deliver!
  class Exim < Sendmail
    DEFAULTS = {
      :location   => '/usr/sbin/exim',
      :arguments  => %w[ -i -t ]
    }

    # Uses -t option to extract recipients from the message.
    def destinations_for(envelope)
      nil
    end
  end
end
