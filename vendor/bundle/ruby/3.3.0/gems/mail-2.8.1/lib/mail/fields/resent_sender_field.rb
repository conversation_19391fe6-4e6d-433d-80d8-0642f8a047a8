# encoding: utf-8
# frozen_string_literal: true
require 'mail/fields/common_address_field'

module Mail
  # = Resent-Sender Field
  #
  # The Resent-Sender field inherits resent-sender Structured<PERSON><PERSON> and handles the Resent-Sender: header
  # field in the email.
  #
  # Sending resent_sender to a mail message will instantiate a Mail::Field object that
  # has a ResentSenderField as its field type.  This includes all Mail::CommonAddress
  # module instance metods.
  #
  # Only one Resent-Sender field can appear in a header, though it can have multiple
  # addresses and groups of addresses.
  #
  # == Examples:
  #
  #  mail = Mail.new
  #  mail.resent_sender = '<PERSON><PERSON> <<EMAIL>>, <EMAIL>'
  #  mail.resent_sender    #=> ['<EMAIL>']
  #  mail[:resent_sender]  #=> '#<Mail::Field:0x180e5e8 @field=#<Mail::ResentSenderField:0x180e1c4
  #  mail['resent-sender'] #=> '#<Mail::Field:0x180e5e8 @field=#<Mail::ResentSenderField:0x180e1c4
  #  mail['Resent-Sender'] #=> '#<Mail::Field:0x180e5e8 @field=#<Mail::ResentSenderField:0x180e1c4
  #
  # <AUTHOR> <EMAIL>, <EMAIL>'
  #  mail.resent_sender.addresses #=> ['<EMAIL>', '<EMAIL>']
  # <AUTHOR> <EMAIL>', '<EMAIL>']
  class ResentSenderField < CommonAddressField #:nodoc:
    NAME = 'Resent-Sender'
  end
end
