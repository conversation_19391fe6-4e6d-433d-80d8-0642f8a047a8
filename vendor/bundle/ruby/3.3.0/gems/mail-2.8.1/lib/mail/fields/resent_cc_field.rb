# encoding: utf-8
# frozen_string_literal: true
require 'mail/fields/common_address_field'

module Mail
  # = Resent-Cc Field
  #
  # The Resent-Cc field inherits resent-cc StructuredField and handles the Resent-Cc: header
  # field in the email.
  #
  # Sending resent_cc to a mail message will instantiate a Mail::Field object that
  # has a ResentCcField as its field type.  This includes all Mail::CommonAddress
  # module instance metods.
  #
  # Only one Resent-Cc field can appear in a header, though it can have multiple
  # addresses and groups of addresses.
  #
  # == Examples:
  #
  #  mail = Mail.new
  #  mail.resent_cc = '<PERSON><PERSON> <<EMAIL>>, <EMAIL>'
  #  mail.resent_cc    #=> ['<EMAIL>', '<EMAIL>']
  #  mail[:resent_cc]  #=> '#<Mail::Field:0x180e5e8 @field=#<Mail::ResentCcField:0x180e1c4
  #  mail['resent-cc'] #=> '#<Mail::Field:0x180e5e8 @field=#<Mail::ResentCcField:0x180e1c4
  #  mail['Resent-Cc'] #=> '#<Mail::Field:0x180e5e8 @field=#<Mail::ResentCcField:0x180e1c4
  #
  # <AUTHOR> <EMAIL>, <EMAIL>\r\n'
  # <AUTHOR> <EMAIL>, <EMAIL>'
  #  mail[:resent_cc].addresses #=> ['<EMAIL>', '<EMAIL>']
  # <AUTHOR> <EMAIL>', '<EMAIL>']
  class ResentCcField < CommonAddressField #:nodoc:
    NAME = 'Resent-Cc'
  end
end
