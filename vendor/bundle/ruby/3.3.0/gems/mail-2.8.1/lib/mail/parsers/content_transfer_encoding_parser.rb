
# frozen_string_literal: true
require "mail/utilities"
require "mail/parser_tools"

begin
  original_verbose, $VERBOSE = $VERBOSE, nil

  module Mail::Parsers
    module ContentTransferEncodingParser
      extend Mail::ParserTools

      ContentTransferEncodingStruct = Struct.new(:encoding, :error)

      class << self
        attr_accessor :_trans_keys
        private :_trans_keys, :_trans_keys=
      end
      self._trans_keys = [
        0, 0, 9, 126, 10, 10,
        9, 32, 10, 10, 9,
        32, 10, 10, 9, 32,
        9, 126, 1, 244, 1, 244,
        10, 10, 9, 32, 0,
        244, 128, 191, 160, 191,
        128, 191, 128, 159, 144, 191,
        128, 191, 128, 143, 9,
        126, 9, 59, 9, 59,
        9, 40, 9, 40, 0, 0,
        0,
      ]

      class << self
        attr_accessor :_key_spans
        private :_key_spans, :_key_spans=
      end
      self._key_spans = [
        0, 118, 1, 24, 1, 24, 1, 24,
        118, 244, 244, 1, 24, 245, 64, 32,
        64, 32, 48, 64, 16, 118, 51, 51,
        32, 32, 0,
      ]

      class << self
        attr_accessor :_index_offsets
        private :_index_offsets, :_index_offsets=
      end
      self._index_offsets = [
        0, 0, 119, 121, 146, 148, 173, 175,
        200, 319, 564, 809, 811, 836, 1082, 1147,
        1180, 1245, 1278, 1327, 1392, 1409, 1528, 1580,
        1632, 1665, 1698,
      ]

      class << self
        attr_accessor :_indicies
        private :_indicies, :_indicies=
      end
      self._indicies = [
        0, 1, 1, 1, 2, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 0,
        3, 3, 3, 3, 3, 3, 3, 4,
        1, 3, 3, 3, 3, 3, 1, 3,
        3, 3, 3, 3, 3, 3, 3, 3,
        3, 1, 1, 1, 1, 1, 1, 1,
        3, 3, 3, 3, 3, 3, 3, 3,
        3, 3, 3, 3, 3, 3, 3, 3,
        3, 3, 3, 3, 3, 3, 3, 3,
        3, 3, 1, 1, 1, 3, 3, 3,
        3, 3, 3, 3, 3, 3, 3, 3,
        3, 3, 3, 3, 3, 3, 3, 3,
        3, 3, 3, 3, 3, 3, 3, 3,
        3, 3, 3, 3, 3, 3, 1, 5,
        1, 0, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        0, 1, 6, 1, 7, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 7, 1, 8, 1, 9,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 9, 1,
        10, 1, 1, 1, 11, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 10,
        12, 12, 12, 12, 12, 12, 12, 13,
        1, 12, 12, 12, 12, 12, 1, 12,
        12, 12, 12, 12, 12, 12, 12, 12,
        12, 1, 1, 1, 1, 1, 1, 1,
        12, 12, 12, 12, 12, 12, 12, 12,
        12, 12, 12, 12, 12, 12, 12, 12,
        12, 12, 12, 12, 12, 12, 12, 12,
        12, 12, 1, 1, 1, 12, 12, 12,
        12, 12, 12, 12, 12, 12, 12, 12,
        12, 12, 12, 12, 12, 12, 12, 12,
        12, 12, 12, 12, 12, 12, 12, 12,
        12, 12, 12, 12, 12, 12, 1, 14,
        14, 14, 14, 14, 14, 14, 14, 14,
        1, 14, 14, 15, 14, 14, 14, 14,
        14, 14, 14, 14, 14, 14, 14, 14,
        14, 14, 14, 14, 14, 14, 14, 14,
        14, 14, 14, 14, 14, 14, 16, 17,
        14, 14, 14, 14, 14, 14, 14, 14,
        14, 14, 14, 14, 14, 14, 14, 14,
        14, 14, 14, 14, 14, 14, 14, 14,
        14, 14, 14, 14, 14, 14, 14, 14,
        14, 14, 14, 14, 14, 14, 14, 14,
        14, 14, 14, 14, 14, 14, 14, 14,
        14, 14, 18, 14, 14, 14, 14, 14,
        14, 14, 14, 14, 14, 14, 14, 14,
        14, 14, 14, 14, 14, 14, 14, 14,
        14, 14, 14, 14, 14, 14, 14, 14,
        14, 14, 14, 14, 14, 14, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        19, 19, 19, 19, 19, 19, 19, 19,
        19, 19, 19, 19, 19, 19, 19, 19,
        19, 19, 19, 19, 19, 19, 19, 19,
        19, 19, 19, 19, 19, 19, 20, 21,
        21, 21, 21, 21, 21, 21, 21, 21,
        21, 21, 21, 22, 21, 21, 23, 24,
        24, 24, 25, 1, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 1, 26, 26,
        27, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 28, 29, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 30,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 31, 31, 31,
        31, 31, 31, 31, 31, 31, 31, 31,
        31, 31, 31, 31, 31, 31, 31, 31,
        31, 31, 31, 31, 31, 31, 31, 31,
        31, 31, 31, 32, 33, 33, 33, 33,
        33, 33, 33, 33, 33, 33, 33, 33,
        34, 33, 33, 35, 36, 36, 36, 37,
        1, 38, 1, 26, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 26, 1, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 31, 31,
        31, 31, 31, 31, 31, 31, 31, 31,
        31, 31, 31, 31, 31, 31, 31, 31,
        31, 31, 31, 31, 31, 31, 31, 31,
        31, 31, 31, 31, 32, 33, 33, 33,
        33, 33, 33, 33, 33, 33, 33, 33,
        33, 34, 33, 33, 35, 36, 36, 36,
        37, 1, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 26, 26, 26, 26, 26, 26,
        26, 26, 1, 31, 31, 31, 31, 31,
        31, 31, 31, 31, 31, 31, 31, 31,
        31, 31, 31, 31, 31, 31, 31, 31,
        31, 31, 31, 31, 31, 31, 31, 31,
        31, 31, 31, 1, 31, 31, 31, 31,
        31, 31, 31, 31, 31, 31, 31, 31,
        31, 31, 31, 31, 31, 31, 31, 31,
        31, 31, 31, 31, 31, 31, 31, 31,
        31, 31, 31, 31, 31, 31, 31, 31,
        31, 31, 31, 31, 31, 31, 31, 31,
        31, 31, 31, 31, 31, 31, 31, 31,
        31, 31, 31, 31, 31, 31, 31, 31,
        31, 31, 31, 31, 1, 31, 31, 31,
        31, 31, 31, 31, 31, 31, 31, 31,
        31, 31, 31, 31, 31, 31, 31, 31,
        31, 31, 31, 31, 31, 31, 31, 31,
        31, 31, 31, 31, 31, 1, 33, 33,
        33, 33, 33, 33, 33, 33, 33, 33,
        33, 33, 33, 33, 33, 33, 33, 33,
        33, 33, 33, 33, 33, 33, 33, 33,
        33, 33, 33, 33, 33, 33, 33, 33,
        33, 33, 33, 33, 33, 33, 33, 33,
        33, 33, 33, 33, 33, 33, 1, 33,
        33, 33, 33, 33, 33, 33, 33, 33,
        33, 33, 33, 33, 33, 33, 33, 33,
        33, 33, 33, 33, 33, 33, 33, 33,
        33, 33, 33, 33, 33, 33, 33, 33,
        33, 33, 33, 33, 33, 33, 33, 33,
        33, 33, 33, 33, 33, 33, 33, 33,
        33, 33, 33, 33, 33, 33, 33, 33,
        33, 33, 33, 33, 33, 33, 33, 1,
        33, 33, 33, 33, 33, 33, 33, 33,
        33, 33, 33, 33, 33, 33, 33, 33,
        1, 39, 1, 1, 1, 40, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        39, 41, 41, 41, 41, 41, 41, 41,
        42, 1, 41, 41, 41, 41, 41, 1,
        41, 41, 41, 41, 41, 41, 41, 41,
        41, 41, 1, 43, 1, 1, 1, 1,
        1, 41, 41, 41, 41, 41, 41, 41,
        41, 41, 41, 41, 41, 41, 41, 41,
        41, 41, 41, 41, 41, 41, 41, 41,
        41, 41, 41, 1, 1, 1, 41, 41,
        41, 41, 41, 41, 41, 41, 41, 41,
        41, 41, 41, 41, 41, 41, 41, 41,
        41, 41, 41, 41, 41, 41, 41, 41,
        41, 41, 41, 41, 41, 41, 41, 1,
        7, 1, 1, 1, 44, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 7,
        1, 1, 1, 1, 1, 1, 1, 45,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 9, 1, 46, 1, 1, 1,
        47, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 46, 1, 1, 1, 1,
        1, 1, 1, 48, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 49, 1,
        9, 1, 1, 1, 50, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 9,
        1, 1, 1, 1, 1, 1, 1, 51,
        1, 49, 1, 1, 1, 52, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        49, 1, 1, 1, 1, 1, 1, 1,
        53, 1, 1, 0,
      ]

      class << self
        attr_accessor :_trans_targs
        private :_trans_targs, :_trans_targs=
      end
      self._trans_targs = [
        1, 0, 2, 21, 8, 3, 5, 22,
        7, 24, 1, 2, 21, 8, 10, 11,
        10, 26, 13, 14, 15, 16, 17, 18,
        19, 20, 10, 11, 10, 26, 13, 14,
        15, 16, 17, 18, 19, 20, 12, 22,
        4, 21, 23, 24, 4, 23, 22, 4,
        23, 24, 6, 25, 6, 25,
      ]

      class << self
        attr_accessor :_trans_actions
        private :_trans_actions, :_trans_actions=
      end
      self._trans_actions = [
        0, 0, 0, 1, 2, 0, 0, 0,
        0, 0, 3, 3, 4, 5, 6, 6,
        7, 8, 6, 6, 6, 6, 6, 6,
        6, 6, 0, 0, 2, 9, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 10,
        10, 0, 11, 10, 0, 2, 3, 3,
        5, 3, 0, 2, 3, 5,
      ]

      class << self
        attr_accessor :_eof_actions
        private :_eof_actions, :_eof_actions=
      end
      self._eof_actions = [
        0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 10, 0, 3,
        0, 3, 0,
      ]

      class << self
        attr_accessor :start
      end
      self.start = 1
      class << self
        attr_accessor :first_final
      end
      self.first_final = 21
      class << self
        attr_accessor :error
      end
      self.error = 0

      class << self
        attr_accessor :en_comment_tail
      end
      self.en_comment_tail = 9
      class << self
        attr_accessor :en_main
      end
      self.en_main = 1

      def self.parse(data)
        data = data.dup.force_encoding(Encoding::ASCII_8BIT) if data.respond_to?(:force_encoding)

        content_transfer_encoding = ContentTransferEncodingStruct.new("")
        return content_transfer_encoding if Mail::Utilities.blank?(data)

        # Parser state
        encoding_s = nil

        # 5.1 Variables Used by Ragel
        p = 0
        eof = pe = data.length
        stack = []

        begin
          p ||= 0
          pe ||= data.length
          cs = start
          top = 0
        end

        begin
          testEof = false
          _slen, _trans, _keys, _inds, _acts, _nacts = nil
          _goto_level = 0
          _resume = 10
          _eof_trans = 15
          _again = 20
          _test_eof = 30
          _out = 40
          while true
            if _goto_level <= 0
              if p == pe
                _goto_level = _test_eof
                next
              end
              if cs == 0
                _goto_level = _out
                next
              end
            end
            if _goto_level <= _resume
              _keys = cs << 1
              _inds = _index_offsets[cs]
              _slen = _key_spans[cs]
              _wide = data[p].ord
              _trans = if (_slen > 0 &&
                           _trans_keys[_keys] <= _wide &&
                           _wide <= _trans_keys[_keys + 1])
                         _indicies[_inds + _wide - _trans_keys[_keys]]
                       else
                         _indicies[_inds + _slen]
                       end
              cs = _trans_targs[_trans]
              if _trans_actions[_trans] != 0
                case _trans_actions[_trans]
                when 1
                  begin
                    encoding_s = p
                  end
                when 10
                  begin
                    content_transfer_encoding.encoding = chars(data, encoding_s, p - 1).downcase
                  end
                when 3
                  begin
                  end
                when 6
                  begin
                  end
                when 2
                  begin
                    begin
                      stack[top] = cs
                      top += 1
                      cs = 9
                      _goto_level = _again
                      next
                    end
                  end
                when 9
                  begin
                    begin
                      top -= 1
                      cs = stack[top]
                      _goto_level = _again
                      next
                    end
                  end
                when 11
                  begin
                    content_transfer_encoding.encoding = chars(data, encoding_s, p - 1).downcase
                  end
                  begin
                    begin
                      stack[top] = cs
                      top += 1
                      cs = 9
                      _goto_level = _again
                      next
                    end
                  end
                when 4
                  begin
                  end
                  begin
                    encoding_s = p
                  end
                when 5
                  begin
                  end
                  begin
                    begin
                      stack[top] = cs
                      top += 1
                      cs = 9
                      _goto_level = _again
                      next
                    end
                  end
                when 7
                  begin
                  end
                  begin
                    begin
                      stack[top] = cs
                      top += 1
                      cs = 9
                      _goto_level = _again
                      next
                    end
                  end
                when 8
                  begin
                  end
                  begin
                    begin
                      top -= 1
                      cs = stack[top]
                      _goto_level = _again
                      next
                    end
                  end
                end
              end
            end
            if _goto_level <= _again
              if cs == 0
                _goto_level = _out
                next
              end
              p += 1
              if p != pe
                _goto_level = _resume
                next
              end
            end
            if _goto_level <= _test_eof
              if p == eof
                case _eof_actions[cs]
                when 10
                  begin
                    content_transfer_encoding.encoding = chars(data, encoding_s, p - 1).downcase
                  end
                when 3
                  begin
                  end
                end
              end
            end
            if _goto_level <= _out
              break
            end
          end
        end

        if p != eof || cs < 21
          raise Mail::Field::IncompleteParseError.new(Mail::ContentTransferEncodingElement, data, p)
        end

        content_transfer_encoding
      end
    end
  end
ensure
  $VERBOSE = original_verbose
end
