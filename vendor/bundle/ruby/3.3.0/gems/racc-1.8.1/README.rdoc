= Racc

* https://github.com/ruby/racc

== DESCRIPTION:

  Racc is an LALR(1) parser generator.
  It is written in Ruby itself, and generates Ruby program.

== Requirement

  *  Ruby 2.5 or later.

== Installation

  gem install:

    $ gem install racc

== Testing Racc

  Racc comes with simple calculator. To compile this, on shell:

      $ racc -o calc calc.y

  This process costs few seconds (or less). Then type:

      $ ruby calc

  ... Does it work?
  For details of Racc, see HTML documents placed under 'doc/en/'
  and sample grammar files under 'sample/'.

== Release flow

* Update VERSION number of these files
  * <code>RACC_VERSION</code> in "ext/racc/com/headius/racc/Cparse.java"
  * <code>VERSION</code> in "lib/racc/info.rb"
* Release as a gem by <code>rake release</code> with CRuby and JRuby because Racc gem provides 2 packages
* Create new release on {GitHub}[https://github.com/ruby/racc/releases]

== License

  Racc is distributed under the same terms of ruby.
  (see the file COPYING). Note that you do NOT need to follow
  ruby license for your own parser (racc outputs).
  You can distribute those files under any licenses you want.


== Bug Reports

  Any kind of bug report is welcome.
  If you find a bug of Racc, please report an issue at 
  https://github.com/ruby/racc/issues. Your grammar file,
  debug output generated by "racc -g", are helpful.


                                                      Minero Aoki
                                              <EMAIL>
                                            http://i.loveruby.net
