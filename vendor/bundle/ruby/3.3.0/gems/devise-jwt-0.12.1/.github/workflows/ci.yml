name: CI 

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        ruby-version: ['3.0', '3.1', '3.2', '3.3', ruby-head]

    steps:
    - uses: actions/checkout@v4
    - name: Set up Ruby ${{ matrix.ruby-version }}
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: ${{ matrix.ruby-version }}
        bundler-cache: true # 'bundle install' and cache
    - name: Run specs 
      run: |
        bundle exec rspec
