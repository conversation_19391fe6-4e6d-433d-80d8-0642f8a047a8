inherit_from: .rubocop_todo.yml

# Offense count: 963
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
Style/StringLiterals:
  Enabled: false

# Offense count: 327
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, EnforcedStyleForEmptyBraces, SupportedStyles.
Style/SpaceInsideHashLiteralBraces:
  Enabled: false

# Offense count: 33
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles, EnforcedStyleForEmptyBraces, SpaceBeforeBlockParameters.
Style/SpaceInsideBlockBraces:
  Enabled: false

# Offense count: 1
# Cop supports --auto-correct.
Style/SpaceBeforeSemicolon:
  Enabled: false

# Offense count: 20
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
Style/SignalException:
  Enabled: false

# Offense count: 1
# Configuration parameters: Methods.
Style/SingleLineBlockParams:
  Enabled: false

# Offense count: 6
# Cop supports --auto-correct.
Style/PerlBackrefs:
  Enabled: false

# Offense count: 2
# Cop supports --auto-correct.
# Configuration parameters: AllowAsExpressionSeparator.
Style/Semicolon:
  Enabled: false

# Offense count: 77
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
Style/BracesAroundHashParameters:
  Enabled: false

# Offense count: 36
Style/Documentation:
  Enabled: false

# Offense count: 6
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles, AllowInnerSlashes.
Style/RegexpLiteral:
  Enabled: false

# Offense count: 5
# Cop supports --auto-correct.
Style/NumericLiterals:
  MinDigits: 6

# Offense count: 4
# Cop supports --auto-correct.
Lint/UnusedMethodArgument:
  Enabled: false

# Offense count: 11
# Cop supports --auto-correct.
Lint/UnusedBlockArgument:
  Enabled: false

# Offense count: 1
Lint/Void:
  Enabled: false

# Offense count: 22
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
Style/IndentHash:
  Enabled: false

# Offense count: 7
# Configuration parameters: MinBodyLength.
Style/GuardClause:
  Enabled: false
