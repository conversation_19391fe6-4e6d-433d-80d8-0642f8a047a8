@media screen, projection {
	/*
	Copyright (c) 2007, Yahoo! Inc. All rights reserved.
	Code licensed under the BSD License:
	http://developer.yahoo.net/yui/license.txt
	version: 2.2.0
	*/
	body {font:13px arial,helvetica,clean,sans-serif;*font-size:small;*font:x-small;}table {font-size:inherit;font:100%;}select, input, textarea {font:99% arial,helvetica,clean,sans-serif;}pre, code {font:115% monospace;*font-size:100%;}body * {line-height:1.22em;}
	body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,form,fieldset,input,textarea,p,blockquote,th,td{margin:0;padding:0;}table{border-collapse:collapse;border-spacing:0;}fieldset,img{border:0;}address,caption,cite,code,dfn,em,strong,th,var{font-style:normal;font-weight:normal;}/*ol,ul {list-style:none;}*/caption,th {text-align:left;}h1,h2,h3,h4,h5,h6{font-size:100%;font-weight:normal;}q:before,q:after{content:'';}abbr,acronym {border:0;}
	/* end of yahoo reset and fonts */

	body 										{color:#333; background:#4b1a1a; line-height:1.3;}
	p												{margin:0 0 20px;}
	a												{color:#4b1a1a;}
	a:hover									{text-decoration:none;}
	strong									{font-weight:bold;}
	em											{font-style:italics;}
	h1,h2,h3,h4,h5,h6				{font-weight:bold;}
	h1											{font-size:197%; margin:30px 0; color:#4b1a1a;}
	h2											{font-size:174%; margin:20px 0; color:#b8111a;}
	h3											{font-size:152%; margin:10px 0;}
	h4											{font-size:129%; margin:10px 0;}
	pre 										{background:#eee; margin:0 0 20px; padding:20px; border:1px solid #ccc; font-size:100%; overflow:auto;}
	code										{font-size:100%; margin:0; padding:0;}
	ul, ol									{margin:10px 0 10px 25px;}
	ol li										{margin:0 0 10px;}





	div#wrapper 							{background:#fff; width:560px; margin:0 auto; padding:20px; border:10px solid #bc8c46; border-width:0 10px;}
	div#header								{position:relative; border-bottom:1px dotted; margin:0 0 10px; padding:0 0 10px;}
	div#header p							{margin:0; padding:0;}
	div#header h1							{margin:0; padding:0;}
	ul#nav										{position:absolute; top:0; right:0; list-style:none; margin:0; padding:0;}
	ul#nav li									{display:inline; padding:0 0 0 5px;}
	ul#nav li a								{}
	div#content								{}
	div#footer								{margin:40px 0 0; border-top:1px dotted; padding:10px 0 0;}






}