# This configuration was generated by `rubocop --auto-gen-config`
# on 2015-04-24 07:22:28 +0200 using RuboCop version 0.30.0.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

# Offense count: 33
Lint/AmbiguousRegexpLiteral:
  Enabled: false

# Offense count: 1
# Configuration parameters: AlignWith, SupportedStyles.
Lint/EndAlignment:
  Enabled: false

# Offense count: 1
Lint/SuppressedException:
  Enabled: false

# Offense count: 5
Lint/UselessAssignment:
  Enabled: false

# Offense count: 23
Metrics/AbcSize:
  Max: 86

# Offense count: 1
# Configuration parameters: CountComments.
Metrics/ClassLength:
  Max: 285

# Offense count: 8
Metrics/CyclomaticComplexity:
  Max: 17

# Offense count: 332
# Configuration parameters: AllowURI, URISchemes.
Metrics/LineLength:
  Max: 266

# Offense count: 17
# Configuration parameters: CountComments.
Metrics/MethodLength:
  Max: 39

# Offense count: 8
Metrics/PerceivedComplexity:
  Max: 20

# Offense count: 1
Style/AccessorMethodName:
  Enabled: false

# Offense count: 1
Style/AsciiComments:
  Enabled: false

# Offense count: 14
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles, ProceduralMethods, FunctionalMethods, IgnoredMethods.
Style/BlockDelimiters:
  Enabled: false

# Offense count: 2
Style/CaseEquality:
  Enabled: false

# Offense count: 3
# Configuration parameters: IndentWhenRelativeTo, SupportedStyles, IndentOneStep.
Style/CaseIndentation:
  Enabled: false

# Offense count: 4
# Configuration parameters: EnforcedStyle, SupportedStyles.
Style/ClassAndModuleChildren:
  Enabled: false

# Offense count: 7
Style/ConstantName:
  Enabled: false

# Offense count: 2
Style/EachWithObject:
  Enabled: false

# Offense count: 2
# Cop supports --auto-correct.
Style/ElseAlignment:
  Enabled: false

# Offense count: 3
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
Style/FirstParameterIndentation:
  Enabled: false

# Offense count: 2
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles, UseHashRocketsWithSymbolValues.
Style/HashSyntax:
  Enabled: false

# Offense count: 7
# Cop supports --auto-correct.
# Configuration parameters: MaxLineLength.
Style/IfUnlessModifier:
  Enabled: false

# Offense count: 11
# Cop supports --auto-correct.
Style/Lambda:
  Enabled: false

# Offense count: 1
# Configuration parameters: EnforcedStyle, MinBodyLength, SupportedStyles.
Style/Next:
  Enabled: false

# Offense count: 2
# Configuration parameters: EnforcedStyle, SupportedStyles.
Style/RaiseArgs:
  Enabled: false
