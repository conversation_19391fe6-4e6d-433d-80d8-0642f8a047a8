name: CI
on: [push, pull_request]
jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        ruby:
          - 2.7
          - "3.0" # Quoted, to avoid YAML float 3.0 interplated to "3"
          - 3.1
          - 3.2
          - 3.3
    steps:
      - name: Check out repository code
        uses: actions/checkout@v4
      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: ${{ matrix.ruby }}
          bundler-cache: true # Run "bundle install", and cache the result automatically.
      - name: Run Rake
        run: bundle exec rake
