# Contributing

* Contributions will not be accepted without tests.
* Please post unconfirmed bugs to the mailing list first: https://groups.google.com/forum/#!forum/httparty-gem
* Don't change the version. The maintainers will handle that when they release.
* Always provide as much information and reproducibility as possible when filing an issue or submitting a pull request.

## Workflow

* Fork the project.
* Run `bundle`
* Run `bundle exec rake`
* Make your feature addition or bug fix.
* Add tests for it. This is important so I don't break it in a future version unintentionally.
* Run `bundle exec rake` (No, REALLY :))
* Commit, do not mess with rakefile, version, or history. (if you want to have your own version, that is fine but bump version in a commit by itself in another branch so I can ignore when I pull)
* Send me a pull request. Bonus points for topic branches.

## Help and Docs

* https://groups.google.com/forum/#!forum/httparty-gem
* http://stackoverflow.com/questions/tagged/httparty
* http://rdoc.info/projects/jnunemaker/httparty
