# frozen_string_literal: true
#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.8.0
# from Racc grammar file "block_parser.ry".
#

###### racc/parser.rb begin
unless $".find {|p| p.end_with?('/racc/parser.rb')}
$".push "#{__dir__}/racc/parser.rb"
#--
# Copyright (c) 1999-2006 Minero Aoki
#
# This program is free software.
# You can distribute/modify this program under the same terms of ruby.
#
# As a special exception, when this code is copied by Racc
# into a Racc output file, you may use that output file
# without restriction.
#++

unless $".find {|p| p.end_with?('/racc/info.rb')}
$".push "#{__dir__}/racc/info.rb"

module Racc
  VERSION   = '1.8.0'
  Version = VERSION
  Copyright = 'Copyright (c) 1999-2006 Minero Aoki'
end

end


module Racc
  class ParseError < StandardError; end
end
unless defined?(::ParseError)
  ParseError = Racc::ParseError # :nodoc:
end

# Racc is a LALR(1) parser generator.
# It is written in Ruby itself, and generates Ruby programs.
#
# == Command-line Reference
#
#     racc [-o<var>filename</var>] [--output-file=<var>filename</var>]
#          [-e<var>rubypath</var>] [--executable=<var>rubypath</var>]
#          [-v] [--verbose]
#          [-O<var>filename</var>] [--log-file=<var>filename</var>]
#          [-g] [--debug]
#          [-E] [--embedded]
#          [-l] [--no-line-convert]
#          [-c] [--line-convert-all]
#          [-a] [--no-omit-actions]
#          [-C] [--check-only]
#          [-S] [--output-status]
#          [--version] [--copyright] [--help] <var>grammarfile</var>
#
# [+grammarfile+]
#   Racc grammar file. Any extension is permitted.
# [-o+outfile+, --output-file=+outfile+]
#   A filename for output. default is <+filename+>.tab.rb
# [-O+filename+, --log-file=+filename+]
#   Place logging output in file +filename+.
#   Default log file name is <+filename+>.output.
# [-e+rubypath+, --executable=+rubypath+]
#   output executable file(mode 755). where +path+ is the Ruby interpreter.
# [-v, --verbose]
#   verbose mode. create +filename+.output file, like yacc's y.output file.
# [-g, --debug]
#   add debug code to parser class. To display debugging information,
#   use this '-g' option and set @yydebug true in parser class.
# [-E, --embedded]
#   Output parser which doesn't need runtime files (racc/parser.rb).
# [-F, --frozen]
#   Output parser which declares frozen_string_literals: true
# [-C, --check-only]
#   Check syntax of racc grammar file and quit.
# [-S, --output-status]
#   Print messages time to time while compiling.
# [-l, --no-line-convert]
#   turns off line number converting.
# [-c, --line-convert-all]
#   Convert line number of actions, inner, header and footer.
# [-a, --no-omit-actions]
#   Call all actions, even if an action is empty.
# [--version]
#   print Racc version and quit.
# [--copyright]
#   Print copyright and quit.
# [--help]
#   Print usage and quit.
#
# == Generating Parser Using Racc
#
# To compile Racc grammar file, simply type:
#
#   $ racc parse.y
#
# This creates Ruby script file "parse.tab.y". The -o option can change the output filename.
#
# == Writing A Racc Grammar File
#
# If you want your own parser, you have to write a grammar file.
# A grammar file contains the name of your parser class, grammar for the parser,
# user code, and anything else.
# When writing a grammar file, yacc's knowledge is helpful.
# If you have not used yacc before, Racc is not too difficult.
#
# Here's an example Racc grammar file.
#
#   class Calcparser
#   rule
#     target: exp { print val[0] }
#
#     exp: exp '+' exp
#        | exp '*' exp
#        | '(' exp ')'
#        | NUMBER
#   end
#
# Racc grammar files resemble yacc files.
# But (of course), this is Ruby code.
# yacc's $$ is the 'result', $0, $1... is
# an array called 'val', and $-1, $-2... is an array called '_values'.
#
# See the {Grammar File Reference}[rdoc-ref:lib/racc/rdoc/grammar.en.rdoc] for
# more information on grammar files.
#
# == Parser
#
# Then you must prepare the parse entry method. There are two types of
# parse methods in Racc, Racc::Parser#do_parse and Racc::Parser#yyparse
#
# Racc::Parser#do_parse is simple.
#
# It's yyparse() of yacc, and Racc::Parser#next_token is yylex().
# This method must returns an array like [TOKENSYMBOL, ITS_VALUE].
# EOF is [false, false].
# (TOKENSYMBOL is a Ruby symbol (taken from String#intern) by default.
# If you want to change this, see the grammar reference.
#
# Racc::Parser#yyparse is little complicated, but useful.
# It does not use Racc::Parser#next_token, instead it gets tokens from any iterator.
#
# For example, <code>yyparse(obj, :scan)</code> causes
# calling +obj#scan+, and you can return tokens by yielding them from +obj#scan+.
#
# == Debugging
#
# When debugging, "-v" or/and the "-g" option is helpful.
#
# "-v" creates verbose log file (.output).
# "-g" creates a "Verbose Parser".
# Verbose Parser prints the internal status when parsing.
# But it's _not_ automatic.
# You must use -g option and set +@yydebug+ to +true+ in order to get output.
# -g option only creates the verbose parser.
#
# === Racc reported syntax error.
#
# Isn't there too many "end"?
# grammar of racc file is changed in v0.10.
#
# Racc does not use '%' mark, while yacc uses huge number of '%' marks..
#
# === Racc reported "XXXX conflicts".
#
# Try "racc -v xxxx.y".
# It causes producing racc's internal log file, xxxx.output.
#
# === Generated parsers does not work correctly
#
# Try "racc -g xxxx.y".
# This command let racc generate "debugging parser".
# Then set @yydebug=true in your parser.
# It produces a working log of your parser.
#
# == Re-distributing Racc runtime
#
# A parser, which is created by Racc, requires the Racc runtime module;
# racc/parser.rb.
#
# Ruby 1.8.x comes with Racc runtime module,
# you need NOT distribute Racc runtime files.
#
# If you want to include the Racc runtime module with your parser.
# This can be done by using '-E' option:
#
#   $ racc -E -omyparser.rb myparser.y
#
# This command creates myparser.rb which `includes' Racc runtime.
# Only you must do is to distribute your parser file (myparser.rb).
#
# Note: parser.rb is ruby license, but your parser is not.
# Your own parser is completely yours.
module Racc

  unless defined?(Racc_No_Extensions)
    Racc_No_Extensions = false # :nodoc:
  end

  class Parser

    Racc_Runtime_Version = ::Racc::VERSION
    Racc_Runtime_Core_Version_R = ::Racc::VERSION

    begin
      if Object.const_defined?(:RUBY_ENGINE) and RUBY_ENGINE == 'jruby'
        require 'jruby'
        require 'racc/cparse-jruby.jar'
        com.headius.racc.Cparse.new.load(JRuby.runtime, false)
      else
        require 'racc/cparse'
      end

      unless new.respond_to?(:_racc_do_parse_c, true)
        raise LoadError, 'old cparse.so'
      end
      if Racc_No_Extensions
        raise LoadError, 'selecting ruby version of racc runtime core'
      end

      Racc_Main_Parsing_Routine    = :_racc_do_parse_c # :nodoc:
      Racc_YY_Parse_Method         = :_racc_yyparse_c # :nodoc:
      Racc_Runtime_Core_Version    = Racc_Runtime_Core_Version_C # :nodoc:
      Racc_Runtime_Type            = 'c' # :nodoc:
    rescue LoadError
      Racc_Main_Parsing_Routine    = :_racc_do_parse_rb
      Racc_YY_Parse_Method         = :_racc_yyparse_rb
      Racc_Runtime_Core_Version    = Racc_Runtime_Core_Version_R
      Racc_Runtime_Type            = 'ruby'
    end

    def Parser.racc_runtime_type # :nodoc:
      Racc_Runtime_Type
    end

    def _racc_setup
      @yydebug = false unless self.class::Racc_debug_parser
      @yydebug = false unless defined?(@yydebug)
      if @yydebug
        @racc_debug_out = $stderr unless defined?(@racc_debug_out)
        @racc_debug_out ||= $stderr
      end
      arg = self.class::Racc_arg
      arg[13] = true if arg.size < 14
      arg
    end

    def _racc_init_sysvars
      @racc_state  = [0]
      @racc_tstack = []
      @racc_vstack = []

      @racc_t = nil
      @racc_val = nil

      @racc_read_next = true

      @racc_user_yyerror = false
      @racc_error_status = 0
    end

    # The entry point of the parser. This method is used with #next_token.
    # If Racc wants to get token (and its value), calls next_token.
    #
    # Example:
    #     def parse
    #       @q = [[1,1],
    #             [2,2],
    #             [3,3],
    #             [false, '$']]
    #       do_parse
    #     end
    #
    #     def next_token
    #       @q.shift
    #     end
    class_eval <<~RUBY, __FILE__, __LINE__ + 1
    def do_parse
      #{Racc_Main_Parsing_Routine}(_racc_setup(), false)
    end
    RUBY

    # The method to fetch next token.
    # If you use #do_parse method, you must implement #next_token.
    #
    # The format of return value is [TOKEN_SYMBOL, VALUE].
    # +token-symbol+ is represented by Ruby's symbol by default, e.g. :IDENT
    # for 'IDENT'.  ";" (String) for ';'.
    #
    # The final symbol (End of file) must be false.
    def next_token
      raise NotImplementedError, "#{self.class}\#next_token is not defined"
    end

    def _racc_do_parse_rb(arg, in_debug)
      action_table, action_check, action_default, action_pointer,
      _,            _,            _,              _,
      _,            _,            token_table,    * = arg

      _racc_init_sysvars
      tok = act = i = nil

      catch(:racc_end_parse) {
        while true
          if i = action_pointer[@racc_state[-1]]
            if @racc_read_next
              if @racc_t != 0   # not EOF
                tok, @racc_val = next_token()
                unless tok      # EOF
                  @racc_t = 0
                else
                  @racc_t = (token_table[tok] or 1)   # error token
                end
                racc_read_token(@racc_t, tok, @racc_val) if @yydebug
                @racc_read_next = false
              end
            end
            i += @racc_t
            unless i >= 0 and
                   act = action_table[i] and
                   action_check[i] == @racc_state[-1]
              act = action_default[@racc_state[-1]]
            end
          else
            act = action_default[@racc_state[-1]]
          end
          while act = _racc_evalact(act, arg)
            ;
          end
        end
      }
    end

    # Another entry point for the parser.
    # If you use this method, you must implement RECEIVER#METHOD_ID method.
    #
    # RECEIVER#METHOD_ID is a method to get next token.
    # It must 'yield' the token, which format is [TOKEN-SYMBOL, VALUE].
    class_eval <<~RUBY, __FILE__, __LINE__ + 1
    def yyparse(recv, mid)
      #{Racc_YY_Parse_Method}(recv, mid, _racc_setup(), false)
    end
    RUBY

    def _racc_yyparse_rb(recv, mid, arg, c_debug)
      action_table, action_check, action_default, action_pointer,
      _,            _,            _,              _,
      _,            _,            token_table,    * = arg

      _racc_init_sysvars

      catch(:racc_end_parse) {
        until i = action_pointer[@racc_state[-1]]
          while act = _racc_evalact(action_default[@racc_state[-1]], arg)
            ;
          end
        end
        recv.__send__(mid) do |tok, val|
          unless tok
            @racc_t = 0
          else
            @racc_t = (token_table[tok] or 1)   # error token
          end
          @racc_val = val
          @racc_read_next = false

          i += @racc_t
          unless i >= 0 and
                 act = action_table[i] and
                 action_check[i] == @racc_state[-1]
            act = action_default[@racc_state[-1]]
          end
          while act = _racc_evalact(act, arg)
            ;
          end

          while !(i = action_pointer[@racc_state[-1]]) ||
                ! @racc_read_next ||
                @racc_t == 0  # $
            unless i and i += @racc_t and
                   i >= 0 and
                   act = action_table[i] and
                   action_check[i] == @racc_state[-1]
              act = action_default[@racc_state[-1]]
            end
            while act = _racc_evalact(act, arg)
              ;
            end
          end
        end
      }
    end

    ###
    ### common
    ###

    def _racc_evalact(act, arg)
      action_table, action_check, _, action_pointer,
      _,            _,            _, _,
      _,            _,            _, shift_n,
      reduce_n,     * = arg
      nerr = 0   # tmp

      if act > 0 and act < shift_n
        #
        # shift
        #
        if @racc_error_status > 0
          @racc_error_status -= 1 unless @racc_t <= 1 # error token or EOF
        end
        @racc_vstack.push @racc_val
        @racc_state.push act
        @racc_read_next = true
        if @yydebug
          @racc_tstack.push @racc_t
          racc_shift @racc_t, @racc_tstack, @racc_vstack
        end

      elsif act < 0 and act > -reduce_n
        #
        # reduce
        #
        code = catch(:racc_jump) {
          @racc_state.push _racc_do_reduce(arg, act)
          false
        }
        if code
          case code
          when 1 # yyerror
            @racc_user_yyerror = true   # user_yyerror
            return -reduce_n
          when 2 # yyaccept
            return shift_n
          else
            raise '[Racc Bug] unknown jump code'
          end
        end

      elsif act == shift_n
        #
        # accept
        #
        racc_accept if @yydebug
        throw :racc_end_parse, @racc_vstack[0]

      elsif act == -reduce_n
        #
        # error
        #
        case @racc_error_status
        when 0
          unless arg[21]    # user_yyerror
            nerr += 1
            on_error @racc_t, @racc_val, @racc_vstack
          end
        when 3
          if @racc_t == 0   # is $
            # We're at EOF, and another error occurred immediately after
            # attempting auto-recovery
            throw :racc_end_parse, nil
          end
          @racc_read_next = true
        end
        @racc_user_yyerror = false
        @racc_error_status = 3
        while true
          if i = action_pointer[@racc_state[-1]]
            i += 1   # error token
            if  i >= 0 and
                (act = action_table[i]) and
                action_check[i] == @racc_state[-1]
              break
            end
          end
          throw :racc_end_parse, nil if @racc_state.size <= 1
          @racc_state.pop
          @racc_vstack.pop
          if @yydebug
            @racc_tstack.pop
            racc_e_pop @racc_state, @racc_tstack, @racc_vstack
          end
        end
        return act

      else
        raise "[Racc Bug] unknown action #{act.inspect}"
      end

      racc_next_state(@racc_state[-1], @racc_state) if @yydebug

      nil
    end

    def _racc_do_reduce(arg, act)
      _,          _,            _,            _,
      goto_table, goto_check,   goto_default, goto_pointer,
      nt_base,    reduce_table, _,            _,
      _,          use_result,   * = arg

      state = @racc_state
      vstack = @racc_vstack
      tstack = @racc_tstack

      i = act * -3
      len       = reduce_table[i]
      reduce_to = reduce_table[i+1]
      method_id = reduce_table[i+2]
      void_array = []

      tmp_t = tstack[-len, len] if @yydebug
      tmp_v = vstack[-len, len]
      tstack[-len, len] = void_array if @yydebug
      vstack[-len, len] = void_array
      state[-len, len]  = void_array

      # tstack must be updated AFTER method call
      if use_result
        vstack.push __send__(method_id, tmp_v, vstack, tmp_v[0])
      else
        vstack.push __send__(method_id, tmp_v, vstack)
      end
      tstack.push reduce_to

      racc_reduce(tmp_t, reduce_to, tstack, vstack) if @yydebug

      k1 = reduce_to - nt_base
      if i = goto_pointer[k1]
        i += state[-1]
        if i >= 0 and (curstate = goto_table[i]) and goto_check[i] == k1
          return curstate
        end
      end
      goto_default[k1]
    end

    # This method is called when a parse error is found.
    #
    # ERROR_TOKEN_ID is an internal ID of token which caused error.
    # You can get string representation of this ID by calling
    # #token_to_str.
    #
    # ERROR_VALUE is a value of error token.
    #
    # value_stack is a stack of symbol values.
    # DO NOT MODIFY this object.
    #
    # This method raises ParseError by default.
    #
    # If this method returns, parsers enter "error recovering mode".
    def on_error(t, val, vstack)
      raise ParseError, sprintf("parse error on value %s (%s)",
                                val.inspect, token_to_str(t) || '?')
    end

    # Enter error recovering mode.
    # This method does not call #on_error.
    def yyerror
      throw :racc_jump, 1
    end

    # Exit parser.
    # Return value is +Symbol_Value_Stack[0]+.
    def yyaccept
      throw :racc_jump, 2
    end

    # Leave error recovering mode.
    def yyerrok
      @racc_error_status = 0
    end

    # For debugging output
    def racc_read_token(t, tok, val)
      @racc_debug_out.print 'read    '
      @racc_debug_out.print tok.inspect, '(', racc_token2str(t), ') '
      @racc_debug_out.puts val.inspect
      @racc_debug_out.puts
    end

    def racc_shift(tok, tstack, vstack)
      @racc_debug_out.puts "shift   #{racc_token2str tok}"
      racc_print_stacks tstack, vstack
      @racc_debug_out.puts
    end

    def racc_reduce(toks, sim, tstack, vstack)
      out = @racc_debug_out
      out.print 'reduce '
      if toks.empty?
        out.print ' <none>'
      else
        toks.each {|t| out.print ' ', racc_token2str(t) }
      end
      out.puts " --> #{racc_token2str(sim)}"
      racc_print_stacks tstack, vstack
      @racc_debug_out.puts
    end

    def racc_accept
      @racc_debug_out.puts 'accept'
      @racc_debug_out.puts
    end

    def racc_e_pop(state, tstack, vstack)
      @racc_debug_out.puts 'error recovering mode: pop token'
      racc_print_states state
      racc_print_stacks tstack, vstack
      @racc_debug_out.puts
    end

    def racc_next_state(curstate, state)
      @racc_debug_out.puts  "goto    #{curstate}"
      racc_print_states state
      @racc_debug_out.puts
    end

    def racc_print_stacks(t, v)
      out = @racc_debug_out
      out.print '        ['
      t.each_index do |i|
        out.print ' (', racc_token2str(t[i]), ' ', v[i].inspect, ')'
      end
      out.puts ' ]'
    end

    def racc_print_states(s)
      out = @racc_debug_out
      out.print '        ['
      s.each {|st| out.print ' ', st }
      out.puts ' ]'
    end

    def racc_token2str(tok)
      self.class::Racc_token_to_s_table[tok] or
          raise "[Racc Bug] can't convert token #{tok} to string"
    end

    # Convert internal ID of token symbol to the string.
    def token_to_str(t)
      self.class::Racc_token_to_s_table[t]
    end

  end

end

end
###### racc/parser.rb end

class RDoc::RD

##
# RD format parser for headings, paragraphs, lists, verbatim sections that
# exist as blocks.

class BlockParser < Racc::Parser


# :stopdoc:

MARK_TO_LEVEL = {
  '='    => 1,
  '=='   => 2,
  '==='  => 3,
  '====' => 4,
  '+'    => 5,
  '++'   => 6,
}

# :startdoc:

##
# Footnotes for this document

attr_reader :footnotes

##
# Labels for items in this document

attr_reader :labels

##
# Path to find included files in

attr_accessor :include_path

##
# Creates a new RDoc::RD::BlockParser.  Use #parse to parse an rd-format
# document.

def initialize
  @inline_parser = RDoc::RD::InlineParser.new self
  @include_path = []

  # for testing
  @footnotes = []
  @labels    = {}
end

##
# Parses +src+ and returns an RDoc::Markup::Document.

def parse src
  @src = src
  @src.push false

  @footnotes = []
  @labels    = {}

  # @i: index(line no.) of src
  @i = 0

  # stack for current indentation
  @indent_stack = []

  # how indented.
  @current_indent = @indent_stack.join("")

  # RDoc::RD::BlockParser for tmp src
  @subparser = nil

  # which part is in now
  @in_part = nil
  @part_content = []

  @in_verbatim = false

  @yydebug = true

  document = do_parse

  unless @footnotes.empty? then
    blankline = document.parts.pop

    document.parts << RDoc::Markup::Rule.new(1)
    document.parts.concat @footnotes

    document.parts.push blankline
  end

  document
end

##
# Returns the next token from the document

def next_token # :nodoc:
  # preprocessing
  # if it is not in RD part
  # => method
  while @in_part != "rd"
    line = @src[@i]
    @i += 1 # next line

    case line
    # src end
    when false
      return [false, false]
    # RD part begin
    when /^=begin\s*(?:\bRD\b.*)?\s*$/
      if @in_part # if in non-RD part
        @part_content.push(line)
      else
        @in_part = "rd"
        return [:WHITELINE, "=begin\n"] # <= for textblockand
      end
    # non-RD part begin
    when /^=begin\s+(\w+)/
      part = $1
=begin # not imported to RDoc
      if @in_part # if in non-RD part
        @part_content.push(line)
      else
        @in_part = part if @tree.filter[part] # if filter exists
#  p "BEGIN_PART: #{@in_part}" # DEBUG
      end
=end
      @in_part = part
    # non-RD part end
    when /^=end(?:$|[\s\0\C-d\C-z])/
      if @in_part # if in non-RD part
=begin # not imported to RDoc
#  p "END_PART: #{@in_part}" # DEBUG
        # make Part-in object
        part = RDoc::RD::Part.new(@part_content.join(""), @tree, "r")
        @part_content.clear
        # call filter, part_out is output(Part object)
        part_out = @tree.filter[@in_part].call(part)

        if @tree.filter[@in_part].mode == :rd # if output is RD formatted
          subtree = parse_subtree(part_out.to_a)
        else # if output is target formatted
          basename = Tempfile.create(["rdtmp", ".#{@in_part}"], @tree.tmp_dir) do |tmpfile|
            tmpfile.print(part_out)
            File.basename(tmpfile.path)
          end
          subtree = parse_subtree(["=begin\n", "<<< #{basename}\n", "=end\n"])
        end
        @in_part = nil
        return [:SUBTREE, subtree]
=end
      end
    else
=begin # not imported to RDoc
      if @in_part # if in non-RD part
        @part_content.push(line)
      end
=end
    end
  end

  @current_indent = @indent_stack.join("")
  line = @src[@i]
  case line
  when false
    if_current_indent_equal("") do
      [false, false]
    end
  when /^=end/
    if_current_indent_equal("") do
      @in_part = nil
      [:WHITELINE, "=end"] # MUST CHANGE??
    end
  when /^\s*$/
    @i += 1 # next line
    return [:WHITELINE, ':WHITELINE']
  when /^\#/  # comment line
    @i += 1 # next line
    self.next_token()
  when /^(={1,4})(?!=)\s*(?=\S)/, /^(\+{1,2})(?!\+)\s*(?=\S)/
    rest = $'                    # '
    rest.strip!
    mark = $1
    if_current_indent_equal("") do
      return [:HEADLINE, [MARK_TO_LEVEL[mark], rest]]
    end
  when /^<<<\s*(\S+)/
    file = $1
    if_current_indent_equal("") do
      suffix = file[-3 .. -1]
      if suffix == ".rd" or suffix == ".rb"
        subtree = parse_subtree(get_included(file))
        [:SUBTREE, subtree]
      else
        [:INCLUDE, file]
      end
    end
  when /^(\s*)\*(\s*)/
    rest = $'                   # '
    newIndent = $2
    if_current_indent_equal($1) do
      if @in_verbatim
        [:STRINGLINE, line]
      else
        @indent_stack.push("\s" + newIndent)
        [:ITEMLISTLINE, rest]
      end
    end
  when /^(\s*)(\(\d+\))(\s*)/
    rest = $'                     # '
    mark = $2
    newIndent = $3
    if_current_indent_equal($1) do
      if @in_verbatim
        [:STRINGLINE, line]
      else
        @indent_stack.push("\s" * mark.size + newIndent)
        [:ENUMLISTLINE, rest]
      end
    end
  when /^(\s*):(\s*)/
    rest = $'                    # '
    newIndent = $2
    if_current_indent_equal($1) do
      if @in_verbatim
        [:STRINGLINE, line]
      else
        @indent_stack.push("\s#{$2}")
        [:DESCLISTLINE, rest]
      end
    end
  when /^(\s*)---(?!-|\s*$)/
    indent = $1
    rest = $'
    /\s*/ === rest
    term = $'
    new_indent = $&
    if_current_indent_equal(indent) do
      if @in_verbatim
        [:STRINGLINE, line]
      else
        @indent_stack.push("\s\s\s" + new_indent)
        [:METHODLISTLINE, term]
      end
    end
  when /^(\s*)/
    if_current_indent_equal($1) do
      [:STRINGLINE, line]
    end
  else
    raise "[BUG] parsing error may occurred."
  end
end

##
# Yields to the given block if +indent+ matches the current indent, otherwise
# an indentation token is processed.

def if_current_indent_equal(indent)
  indent = indent.sub(/\t/, "\s" * 8)
  if @current_indent == indent
    @i += 1 # next line
    yield
  elsif indent.index(@current_indent) == 0
    @indent_stack.push(indent[@current_indent.size .. -1])
    [:INDENT, ":INDENT"]
  else
    @indent_stack.pop
    [:DEDENT, ":DEDENT"]
  end
end
private :if_current_indent_equal

##
# Cuts off excess whitespace in +src+

def cut_off(src)
  ret = []
  whiteline_buf = []

  line = src.shift
  /^\s*/ =~ line

  indent = Regexp.quote($&)
  ret.push($')

  while line = src.shift
    if /^(\s*)$/ =~ line
      whiteline_buf.push(line)
    elsif /^#{indent}/ =~ line
      unless whiteline_buf.empty?
        ret.concat(whiteline_buf)
        whiteline_buf.clear
      end
      ret.push($')
    else
      raise "[BUG]: probably Parser Error while cutting off.\n"
    end
  end
  ret
end
private :cut_off

def set_term_to_element(parent, term)
#  parent.set_term_under_document_struct(term, @tree.document_struct)
  parent.set_term_without_document_struct(term)
end
private :set_term_to_element

##
# Raises a ParseError when invalid formatting is found

def on_error(et, ev, _values)
  prv, cur, nxt = format_line_num(@i, @i+1, @i+2)

  raise ParseError, <<Msg

RD syntax error: line #{@i+1}:
  #{prv}  |#{@src[@i-1].chomp}
  #{cur}=>|#{@src[@i].chomp}
  #{nxt}  |#{@src[@i+1].chomp}

Msg
end

##
# Current line number

def line_index
  @i
end

##
# Parses subtree +src+

def parse_subtree src
  @subparser ||= RDoc::RD::BlockParser.new

  @subparser.parse src
end
private :parse_subtree

##
# Retrieves the content for +file+ from the include_path

def get_included(file)
  included = []

  @include_path.each do |dir|
    file_name = File.join dir, file

    if File.exist? file_name then
      included = File.readlines file_name
      break
    end
  end

  included
end
private :get_included

##
# Formats line numbers +line_numbers+ prettily

def format_line_num(*line_numbers)
  width = line_numbers.collect{|i| i.to_s.length }.max
  line_numbers.collect{|i| sprintf("%#{width}d", i) }
end
private :format_line_num

##
# Retrieves the content of +values+ as a single String

def content values
 values.map { |value| value.content }.join
end

##
# Creates a paragraph for +value+

def paragraph value
  content = cut_off(value).join(' ').rstrip
  contents = @inline_parser.parse content

  RDoc::Markup::Paragraph.new(*contents)
end

##
# Adds footnote +content+ to the document

def add_footnote content
  index = @footnotes.length / 2 + 1

  footmark_link = "{^#{index}}[rdoc-label:footmark-#{index}:foottext-#{index}]"

  @footnotes << RDoc::Markup::Paragraph.new(footmark_link, ' ', *content)
  @footnotes << RDoc::Markup::BlankLine.new

  index
end

##
# Adds label +label+ to the document

def add_label label
  @labels[label] = true

  label
end

# :stopdoc:

##### State transition tables begin ###

racc_action_table = [
    34,    35,    30,    33,    40,    34,    35,    30,    33,    40,
    65,    34,    35,    30,    33,    14,    73,    36,    38,    34,
    15,    88,    34,    35,    30,    33,    14,     9,    10,    11,
    12,    15,    34,    35,    30,    33,    14,     9,    10,    11,
    12,    15,    34,    35,    30,    33,    35,    47,    30,    54,
    33,    15,    34,    35,    30,    33,    54,    47,    14,    14,
    59,    15,    34,    35,    30,    33,    14,    73,    67,    76,
    77,    15,    34,    35,    30,    33,    14,    73,    54,    81,
    38,    15,    34,    35,    30,    33,    14,    73,    38,    40,
    83,    15,    34,    35,    30,    33,    14,    73,   nil,   nil,
   nil,    15,    34,    35,    30,    33,    14,    73,   nil,   nil,
   nil,    15,    34,    35,    30,    33,    14,    73,   nil,   nil,
   nil,    15,    34,    35,    30,    33,    14,    73,   nil,   nil,
   nil,    15,    34,    35,    30,    33,    14,    73,   nil,   nil,
   nil,    15,    34,    35,    30,    33,    14,    73,    61,    63,
   nil,    15,    14,    62,    60,    61,    63,    79,    61,    63,
    62,    87,   nil,    62,    34,    35,    30,    33 ]

racc_action_check = [
    41,    41,    41,    41,    41,    15,    15,    15,    15,    15,
    41,    86,    86,    86,    86,    86,    86,     1,    13,    22,
    86,    86,     0,     0,     0,     0,     0,     0,     0,     0,
     0,     0,     2,     2,     2,     2,     2,     2,     2,     2,
     2,     2,    24,    24,    24,    24,    25,    24,    28,    30,
    31,    24,    27,    27,    27,    27,    33,    27,    34,    35,
    36,    27,    45,    45,    45,    45,    45,    45,    44,    49,
    51,    45,    46,    46,    46,    46,    46,    46,    54,    56,
    57,    46,    47,    47,    47,    47,    47,    47,    58,    62,
    66,    47,    68,    68,    68,    68,    68,    68,   nil,   nil,
   nil,    68,    74,    74,    74,    74,    74,    74,   nil,   nil,
   nil,    74,    75,    75,    75,    75,    75,    75,   nil,   nil,
   nil,    75,    78,    78,    78,    78,    78,    78,   nil,   nil,
   nil,    78,    79,    79,    79,    79,    79,    79,   nil,   nil,
   nil,    79,    85,    85,    85,    85,    85,    85,    39,    39,
   nil,    85,    52,    39,    39,    82,    82,    52,    64,    64,
    82,    82,   nil,    64,    20,    20,    20,    20 ]

racc_action_pointer = [
    19,    17,    29,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    11,   nil,     2,   nil,   nil,   nil,   nil,
   161,   nil,    16,   nil,    39,    42,   nil,    49,    43,   nil,
    41,    44,   nil,    48,    51,    52,    60,   nil,   nil,   141,
   nil,    -3,   nil,   nil,    55,    59,    69,    79,   nil,    56,
   nil,    57,   145,   nil,    70,   nil,    66,    73,    81,   nil,
   nil,   nil,    82,   nil,   151,   nil,    77,   nil,    89,   nil,
   nil,   nil,   nil,   nil,    99,   109,   nil,   nil,   119,   129,
   nil,   nil,   148,   nil,   nil,   139,     8,   nil,   nil ]

racc_action_default = [
    -2,   -73,    -1,    -4,    -5,    -6,    -7,    -8,    -9,   -10,
   -11,   -12,   -13,   -14,   -16,   -73,   -23,   -24,   -25,   -26,
   -27,   -31,   -32,   -34,   -72,   -36,   -38,   -72,   -40,   -42,
   -59,   -44,   -46,   -59,   -63,   -65,   -73,    -3,   -15,   -73,
   -22,   -73,   -30,   -33,   -73,   -69,   -70,   -71,   -37,   -73,
   -41,   -73,   -51,   -58,   -61,   -45,   -73,   -62,   -64,    89,
   -17,   -19,   -73,   -21,   -18,   -28,   -73,   -35,   -66,   -53,
   -54,   -55,   -56,   -57,   -67,   -68,   -39,   -43,   -49,   -73,
   -60,   -47,   -73,   -29,   -52,   -48,   -73,   -20,   -50 ]

racc_goto_table = [
     4,    39,     4,    68,    74,    75,     5,     6,     5,     6,
    44,    42,    51,    49,     3,    56,    37,    57,    58,     1,
     2,    66,    84,    41,    43,    48,    50,    64,    84,    84,
    45,    46,    42,    45,    46,    55,    85,    86,    80,    84,
    84,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    82,   nil,
   nil,   nil,    78 ]

racc_goto_check = [
     4,    10,     4,    31,    31,    31,     5,     6,     5,     6,
    21,    12,    27,    21,     3,    27,     3,     9,     9,     1,
     2,    11,    32,    17,    19,    23,    26,    10,    32,    32,
     5,     6,    12,     5,     6,    29,    31,    31,    33,    32,
    32,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    10,   nil,
   nil,   nil,     4 ]

racc_goto_pointer = [
   nil,    19,    20,    14,     0,     6,     7,   nil,   nil,   -17,
   -14,   -20,    -9,   nil,   nil,   nil,   nil,     8,   nil,     2,
   nil,   -14,   nil,     0,   nil,   nil,    -2,   -18,   nil,     4,
   nil,   -42,   -46,   -16 ]

racc_goto_default = [
   nil,   nil,   nil,   nil,    70,    71,    72,     7,     8,    13,
   nil,   nil,    21,    16,    17,    18,    19,    20,    22,    23,
    24,   nil,    25,    26,    27,    28,    29,   nil,    31,    32,
    52,   nil,    69,    53 ]

racc_reduce_table = [
  0, 0, :racc_error,
  1, 15, :_reduce_1,
  0, 15, :_reduce_2,
  2, 16, :_reduce_3,
  1, 16, :_reduce_4,
  1, 17, :_reduce_5,
  1, 17, :_reduce_6,
  1, 17, :_reduce_none,
  1, 17, :_reduce_8,
  1, 17, :_reduce_9,
  1, 17, :_reduce_10,
  1, 17, :_reduce_11,
  1, 21, :_reduce_12,
  1, 22, :_reduce_13,
  1, 18, :_reduce_14,
  2, 23, :_reduce_15,
  1, 23, :_reduce_16,
  3, 19, :_reduce_17,
  1, 25, :_reduce_18,
  2, 24, :_reduce_19,
  4, 24, :_reduce_20,
  2, 24, :_reduce_21,
  1, 24, :_reduce_22,
  1, 26, :_reduce_none,
  1, 26, :_reduce_none,
  1, 26, :_reduce_none,
  1, 26, :_reduce_none,
  1, 20, :_reduce_27,
  3, 20, :_reduce_28,
  4, 20, :_reduce_29,
  2, 31, :_reduce_30,
  1, 31, :_reduce_31,
  1, 27, :_reduce_32,
  2, 32, :_reduce_33,
  1, 32, :_reduce_34,
  3, 33, :_reduce_35,
  1, 28, :_reduce_36,
  2, 36, :_reduce_37,
  1, 36, :_reduce_38,
  3, 37, :_reduce_39,
  1, 29, :_reduce_40,
  2, 39, :_reduce_41,
  1, 39, :_reduce_42,
  3, 40, :_reduce_43,
  1, 30, :_reduce_44,
  2, 42, :_reduce_45,
  1, 42, :_reduce_46,
  3, 43, :_reduce_47,
  3, 41, :_reduce_48,
  2, 41, :_reduce_49,
  4, 41, :_reduce_50,
  1, 41, :_reduce_51,
  2, 45, :_reduce_52,
  1, 45, :_reduce_none,
  1, 46, :_reduce_54,
  1, 46, :_reduce_55,
  1, 46, :_reduce_none,
  1, 46, :_reduce_57,
  1, 44, :_reduce_none,
  0, 44, :_reduce_none,
  2, 47, :_reduce_none,
  1, 47, :_reduce_none,
  2, 34, :_reduce_62,
  1, 34, :_reduce_63,
  2, 38, :_reduce_64,
  1, 38, :_reduce_65,
  2, 35, :_reduce_66,
  2, 35, :_reduce_67,
  2, 35, :_reduce_68,
  1, 35, :_reduce_69,
  1, 35, :_reduce_none,
  1, 35, :_reduce_71,
  0, 35, :_reduce_72 ]

racc_reduce_n = 73

racc_shift_n = 89

racc_token_table = {
  false => 0,
  :error => 1,
  :DUMMY => 2,
  :ITEMLISTLINE => 3,
  :ENUMLISTLINE => 4,
  :DESCLISTLINE => 5,
  :METHODLISTLINE => 6,
  :STRINGLINE => 7,
  :WHITELINE => 8,
  :SUBTREE => 9,
  :HEADLINE => 10,
  :INCLUDE => 11,
  :INDENT => 12,
  :DEDENT => 13 }

racc_nt_base = 14

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]
Ractor.make_shareable(Racc_arg) if defined?(Ractor)

Racc_token_to_s_table = [
  "$end",
  "error",
  "DUMMY",
  "ITEMLISTLINE",
  "ENUMLISTLINE",
  "DESCLISTLINE",
  "METHODLISTLINE",
  "STRINGLINE",
  "WHITELINE",
  "SUBTREE",
  "HEADLINE",
  "INCLUDE",
  "INDENT",
  "DEDENT",
  "$start",
  "document",
  "blocks",
  "block",
  "textblock",
  "verbatim",
  "lists",
  "headline",
  "include",
  "textblockcontent",
  "verbatimcontent",
  "verbatim_after_lists",
  "list",
  "itemlist",
  "enumlist",
  "desclist",
  "methodlist",
  "lists2",
  "itemlistitems",
  "itemlistitem",
  "first_textblock_in_itemlist",
  "other_blocks_in_list",
  "enumlistitems",
  "enumlistitem",
  "first_textblock_in_enumlist",
  "desclistitems",
  "desclistitem",
  "description_part",
  "methodlistitems",
  "methodlistitem",
  "whitelines",
  "blocks_in_list",
  "block_in_list",
  "whitelines2" ]
Ractor.make_shareable(Racc_token_to_s_table) if defined?(Ractor)

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

def _reduce_1(val, _values, result)
 result = RDoc::Markup::Document.new(*val[0])
    result
end

def _reduce_2(val, _values, result)
 raise ParseError, "file empty"
    result
end

def _reduce_3(val, _values, result)
 result = val[0].concat val[1]
    result
end

def _reduce_4(val, _values, result)
 result = val[0]
    result
end

def _reduce_5(val, _values, result)
 result = val
    result
end

def _reduce_6(val, _values, result)
 result = val
    result
end

# reduce 7 omitted

def _reduce_8(val, _values, result)
 result = val
    result
end

def _reduce_9(val, _values, result)
 result = val
    result
end

def _reduce_10(val, _values, result)
 result = [RDoc::Markup::BlankLine.new]
    result
end

def _reduce_11(val, _values, result)
 result = val[0].parts
    result
end

def _reduce_12(val, _values, result)
      # val[0] is like [level, title]
      title = @inline_parser.parse(val[0][1])
      result = RDoc::Markup::Heading.new(val[0][0], title)

    result
end

def _reduce_13(val, _values, result)
      result = RDoc::Markup::Include.new val[0], @include_path

    result
end

def _reduce_14(val, _values, result)
      # val[0] is Array of String
      result = paragraph val[0]

    result
end

def _reduce_15(val, _values, result)
 result << val[1].rstrip
    result
end

def _reduce_16(val, _values, result)
 result = [val[0].rstrip]
    result
end

def _reduce_17(val, _values, result)
      # val[1] is Array of String
      content = cut_off val[1]
      result = RDoc::Markup::Verbatim.new(*content)

      # imform to lexer.
      @in_verbatim = false

    result
end

def _reduce_18(val, _values, result)
      # val[0] is Array of String
      content = cut_off val[0]
      result = RDoc::Markup::Verbatim.new(*content)

      # imform to lexer.
      @in_verbatim = false

    result
end

def _reduce_19(val, _values, result)
      result << val[1]

    result
end

def _reduce_20(val, _values, result)
      result.concat val[2]

    result
end

def _reduce_21(val, _values, result)
      result << "\n"

    result
end

def _reduce_22(val, _values, result)
      result = val
      # inform to lexer.
      @in_verbatim = true

    result
end

# reduce 23 omitted

# reduce 24 omitted

# reduce 25 omitted

# reduce 26 omitted

def _reduce_27(val, _values, result)
      result = val[0]

    result
end

def _reduce_28(val, _values, result)
      result = val[1]

    result
end

def _reduce_29(val, _values, result)
      result = val[1].push(val[2])

    result
end

def _reduce_30(val, _values, result)
 result = val[0] << val[1]
    result
end

def _reduce_31(val, _values, result)
 result = [val[0]]
    result
end

def _reduce_32(val, _values, result)
      result = RDoc::Markup::List.new :BULLET, *val[0]

    result
end

def _reduce_33(val, _values, result)
 result.push(val[1])
    result
end

def _reduce_34(val, _values, result)
 result = val
    result
end

def _reduce_35(val, _values, result)
      result = RDoc::Markup::ListItem.new nil, val[0], *val[1]

    result
end

def _reduce_36(val, _values, result)
      result = RDoc::Markup::List.new :NUMBER, *val[0]

    result
end

def _reduce_37(val, _values, result)
 result.push(val[1])
    result
end

def _reduce_38(val, _values, result)
 result = val
    result
end

def _reduce_39(val, _values, result)
      result = RDoc::Markup::ListItem.new nil, val[0], *val[1]

    result
end

def _reduce_40(val, _values, result)
      result = RDoc::Markup::List.new :NOTE, *val[0]

    result
end

def _reduce_41(val, _values, result)
 result.push(val[1])
    result
end

def _reduce_42(val, _values, result)
 result = val
    result
end

def _reduce_43(val, _values, result)
      term = @inline_parser.parse val[0].strip

      result = RDoc::Markup::ListItem.new term, *val[1]

    result
end

def _reduce_44(val, _values, result)
      result = RDoc::Markup::List.new :LABEL, *val[0]

    result
end

def _reduce_45(val, _values, result)
 result.push(val[1])
    result
end

def _reduce_46(val, _values, result)
 result = val
    result
end

def _reduce_47(val, _values, result)
      result = RDoc::Markup::ListItem.new "<tt>#{val[0].strip}</tt>", *val[1]

    result
end

def _reduce_48(val, _values, result)
      result = [val[1]].concat(val[2])

    result
end

def _reduce_49(val, _values, result)
      result = [val[1]]

    result
end

def _reduce_50(val, _values, result)
      result = val[2]

    result
end

def _reduce_51(val, _values, result)
      result = []

    result
end

def _reduce_52(val, _values, result)
 result.concat val[1]
    result
end

# reduce 53 omitted

def _reduce_54(val, _values, result)
 result = val
    result
end

def _reduce_55(val, _values, result)
 result = val
    result
end

# reduce 56 omitted

def _reduce_57(val, _values, result)
 result = []
    result
end

# reduce 58 omitted

# reduce 59 omitted

# reduce 60 omitted

# reduce 61 omitted

def _reduce_62(val, _values, result)
      result = paragraph [val[0]].concat(val[1])

    result
end

def _reduce_63(val, _values, result)
      result = paragraph [val[0]]

    result
end

def _reduce_64(val, _values, result)
      result = paragraph [val[0]].concat(val[1])

    result
end

def _reduce_65(val, _values, result)
      result = paragraph [val[0]]

    result
end

def _reduce_66(val, _values, result)
      result = [val[0]].concat(val[1])

    result
end

def _reduce_67(val, _values, result)
 result.concat val[1]
    result
end

def _reduce_68(val, _values, result)
 result = val[1]
    result
end

def _reduce_69(val, _values, result)
 result = val
    result
end

# reduce 70 omitted

def _reduce_71(val, _values, result)
 result = []
    result
end

def _reduce_72(val, _values, result)
 result = []
    result
end

def _reduce_none(val, _values, result)
  val[0]
end

end   # class BlockParser

end
