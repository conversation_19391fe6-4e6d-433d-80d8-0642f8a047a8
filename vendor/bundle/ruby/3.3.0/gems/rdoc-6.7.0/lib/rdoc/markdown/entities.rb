# frozen_string_literal: true
##
# HTML entity name map for RDoc::Markdown

RDoc::Markdown::HTML_ENTITIES = {
  "AElig" => [0x000C6],
  "AMP" => [0x00026],
  "Aacute" => [0x000C1],
  "Abreve" => [0x00102],
  "Acirc" => [0x000C2],
  "Acy" => [0x00410],
  "Afr" => [0x1D504],
  "Agrave" => [0x000C0],
  "Alpha" => [0x00391],
  "Amacr" => [0x00100],
  "And" => [0x02A53],
  "Aogon" => [0x00104],
  "Aopf" => [0x1D538],
  "ApplyFunction" => [0x02061],
  "Aring" => [0x000C5],
  "Ascr" => [0x1D49C],
  "Assign" => [0x02254],
  "Atilde" => [0x000C3],
  "Auml" => [0x000C4],
  "Backslash" => [0x02216],
  "Barv" => [0x02AE7],
  "Barwed" => [0x02306],
  "Bcy" => [0x00411],
  "Because" => [0x02235],
  "Bernoullis" => [0x0212C],
  "Beta" => [0x00392],
  "Bfr" => [0x1D505],
  "Bopf" => [0x1D539],
  "Breve" => [0x002D8],
  "Bscr" => [0x0212C],
  "Bumpeq" => [0x0224E],
  "CHcy" => [0x00427],
  "COPY" => [0x000A9],
  "Cacute" => [0x00106],
  "Cap" => [0x022D2],
  "CapitalDifferentialD" => [0x02145],
  "Cayleys" => [0x0212D],
  "Ccaron" => [0x0010C],
  "Ccedil" => [0x000C7],
  "Ccirc" => [0x00108],
  "Cconint" => [0x02230],
  "Cdot" => [0x0010A],
  "Cedilla" => [0x000B8],
  "CenterDot" => [0x000B7],
  "Cfr" => [0x0212D],
  "Chi" => [0x003A7],
  "CircleDot" => [0x02299],
  "CircleMinus" => [0x02296],
  "CirclePlus" => [0x02295],
  "CircleTimes" => [0x02297],
  "ClockwiseContourIntegral" => [0x02232],
  "CloseCurlyDoubleQuote" => [0x0201D],
  "CloseCurlyQuote" => [0x02019],
  "Colon" => [0x02237],
  "Colone" => [0x02A74],
  "Congruent" => [0x02261],
  "Conint" => [0x0222F],
  "ContourIntegral" => [0x0222E],
  "Copf" => [0x02102],
  "Coproduct" => [0x02210],
  "CounterClockwiseContourIntegral" => [0x02233],
  "Cross" => [0x02A2F],
  "Cscr" => [0x1D49E],
  "Cup" => [0x022D3],
  "CupCap" => [0x0224D],
  "DD" => [0x02145],
  "DDotrahd" => [0x02911],
  "DJcy" => [0x00402],
  "DScy" => [0x00405],
  "DZcy" => [0x0040F],
  "Dagger" => [0x02021],
  "Darr" => [0x021A1],
  "Dashv" => [0x02AE4],
  "Dcaron" => [0x0010E],
  "Dcy" => [0x00414],
  "Del" => [0x02207],
  "Delta" => [0x00394],
  "Dfr" => [0x1D507],
  "DiacriticalAcute" => [0x000B4],
  "DiacriticalDot" => [0x002D9],
  "DiacriticalDoubleAcute" => [0x002DD],
  "DiacriticalGrave" => [0x00060],
  "DiacriticalTilde" => [0x002DC],
  "Diamond" => [0x022C4],
  "DifferentialD" => [0x02146],
  "Dopf" => [0x1D53B],
  "Dot" => [0x000A8],
  "DotDot" => [0x020DC],
  "DotEqual" => [0x02250],
  "DoubleContourIntegral" => [0x0222F],
  "DoubleDot" => [0x000A8],
  "DoubleDownArrow" => [0x021D3],
  "DoubleLeftArrow" => [0x021D0],
  "DoubleLeftRightArrow" => [0x021D4],
  "DoubleLeftTee" => [0x02AE4],
  "DoubleLongLeftArrow" => [0x027F8],
  "DoubleLongLeftRightArrow" => [0x027FA],
  "DoubleLongRightArrow" => [0x027F9],
  "DoubleRightArrow" => [0x021D2],
  "DoubleRightTee" => [0x022A8],
  "DoubleUpArrow" => [0x021D1],
  "DoubleUpDownArrow" => [0x021D5],
  "DoubleVerticalBar" => [0x02225],
  "DownArrow" => [0x02193],
  "DownArrowBar" => [0x02913],
  "DownArrowUpArrow" => [0x021F5],
  "DownBreve" => [0x00311],
  "DownLeftRightVector" => [0x02950],
  "DownLeftTeeVector" => [0x0295E],
  "DownLeftVector" => [0x021BD],
  "DownLeftVectorBar" => [0x02956],
  "DownRightTeeVector" => [0x0295F],
  "DownRightVector" => [0x021C1],
  "DownRightVectorBar" => [0x02957],
  "DownTee" => [0x022A4],
  "DownTeeArrow" => [0x021A7],
  "Downarrow" => [0x021D3],
  "Dscr" => [0x1D49F],
  "Dstrok" => [0x00110],
  "ENG" => [0x0014A],
  "ETH" => [0x000D0],
  "Eacute" => [0x000C9],
  "Ecaron" => [0x0011A],
  "Ecirc" => [0x000CA],
  "Ecy" => [0x0042D],
  "Edot" => [0x00116],
  "Efr" => [0x1D508],
  "Egrave" => [0x000C8],
  "Element" => [0x02208],
  "Emacr" => [0x00112],
  "EmptySmallSquare" => [0x025FB],
  "EmptyVerySmallSquare" => [0x025AB],
  "Eogon" => [0x00118],
  "Eopf" => [0x1D53C],
  "Epsilon" => [0x00395],
  "Equal" => [0x02A75],
  "EqualTilde" => [0x02242],
  "Equilibrium" => [0x021CC],
  "Escr" => [0x02130],
  "Esim" => [0x02A73],
  "Eta" => [0x00397],
  "Euml" => [0x000CB],
  "Exists" => [0x02203],
  "ExponentialE" => [0x02147],
  "Fcy" => [0x00424],
  "Ffr" => [0x1D509],
  "FilledSmallSquare" => [0x025FC],
  "FilledVerySmallSquare" => [0x025AA],
  "Fopf" => [0x1D53D],
  "ForAll" => [0x02200],
  "Fouriertrf" => [0x02131],
  "Fscr" => [0x02131],
  "GJcy" => [0x00403],
  "GT" => [0x0003E],
  "Gamma" => [0x00393],
  "Gammad" => [0x003DC],
  "Gbreve" => [0x0011E],
  "Gcedil" => [0x00122],
  "Gcirc" => [0x0011C],
  "Gcy" => [0x00413],
  "Gdot" => [0x00120],
  "Gfr" => [0x1D50A],
  "Gg" => [0x022D9],
  "Gopf" => [0x1D53E],
  "GreaterEqual" => [0x02265],
  "GreaterEqualLess" => [0x022DB],
  "GreaterFullEqual" => [0x02267],
  "GreaterGreater" => [0x02AA2],
  "GreaterLess" => [0x02277],
  "GreaterSlantEqual" => [0x02A7E],
  "GreaterTilde" => [0x02273],
  "Gscr" => [0x1D4A2],
  "Gt" => [0x0226B],
  "HARDcy" => [0x0042A],
  "Hacek" => [0x002C7],
  "Hat" => [0x0005E],
  "Hcirc" => [0x00124],
  "Hfr" => [0x0210C],
  "HilbertSpace" => [0x0210B],
  "Hopf" => [0x0210D],
  "HorizontalLine" => [0x02500],
  "Hscr" => [0x0210B],
  "Hstrok" => [0x00126],
  "HumpDownHump" => [0x0224E],
  "HumpEqual" => [0x0224F],
  "IEcy" => [0x00415],
  "IJlig" => [0x00132],
  "IOcy" => [0x00401],
  "Iacute" => [0x000CD],
  "Icirc" => [0x000CE],
  "Icy" => [0x00418],
  "Idot" => [0x00130],
  "Ifr" => [0x02111],
  "Igrave" => [0x000CC],
  "Im" => [0x02111],
  "Imacr" => [0x0012A],
  "ImaginaryI" => [0x02148],
  "Implies" => [0x021D2],
  "Int" => [0x0222C],
  "Integral" => [0x0222B],
  "Intersection" => [0x022C2],
  "InvisibleComma" => [0x02063],
  "InvisibleTimes" => [0x02062],
  "Iogon" => [0x0012E],
  "Iopf" => [0x1D540],
  "Iota" => [0x00399],
  "Iscr" => [0x02110],
  "Itilde" => [0x00128],
  "Iukcy" => [0x00406],
  "Iuml" => [0x000CF],
  "Jcirc" => [0x00134],
  "Jcy" => [0x00419],
  "Jfr" => [0x1D50D],
  "Jopf" => [0x1D541],
  "Jscr" => [0x1D4A5],
  "Jsercy" => [0x00408],
  "Jukcy" => [0x00404],
  "KHcy" => [0x00425],
  "KJcy" => [0x0040C],
  "Kappa" => [0x0039A],
  "Kcedil" => [0x00136],
  "Kcy" => [0x0041A],
  "Kfr" => [0x1D50E],
  "Kopf" => [0x1D542],
  "Kscr" => [0x1D4A6],
  "LJcy" => [0x00409],
  "LT" => [0x0003C],
  "Lacute" => [0x00139],
  "Lambda" => [0x0039B],
  "Lang" => [0x027EA],
  "Laplacetrf" => [0x02112],
  "Larr" => [0x0219E],
  "Lcaron" => [0x0013D],
  "Lcedil" => [0x0013B],
  "Lcy" => [0x0041B],
  "LeftAngleBracket" => [0x027E8],
  "LeftArrow" => [0x02190],
  "LeftArrowBar" => [0x021E4],
  "LeftArrowRightArrow" => [0x021C6],
  "LeftCeiling" => [0x02308],
  "LeftDoubleBracket" => [0x027E6],
  "LeftDownTeeVector" => [0x02961],
  "LeftDownVector" => [0x021C3],
  "LeftDownVectorBar" => [0x02959],
  "LeftFloor" => [0x0230A],
  "LeftRightArrow" => [0x02194],
  "LeftRightVector" => [0x0294E],
  "LeftTee" => [0x022A3],
  "LeftTeeArrow" => [0x021A4],
  "LeftTeeVector" => [0x0295A],
  "LeftTriangle" => [0x022B2],
  "LeftTriangleBar" => [0x029CF],
  "LeftTriangleEqual" => [0x022B4],
  "LeftUpDownVector" => [0x02951],
  "LeftUpTeeVector" => [0x02960],
  "LeftUpVector" => [0x021BF],
  "LeftUpVectorBar" => [0x02958],
  "LeftVector" => [0x021BC],
  "LeftVectorBar" => [0x02952],
  "Leftarrow" => [0x021D0],
  "Leftrightarrow" => [0x021D4],
  "LessEqualGreater" => [0x022DA],
  "LessFullEqual" => [0x02266],
  "LessGreater" => [0x02276],
  "LessLess" => [0x02AA1],
  "LessSlantEqual" => [0x02A7D],
  "LessTilde" => [0x02272],
  "Lfr" => [0x1D50F],
  "Ll" => [0x022D8],
  "Lleftarrow" => [0x021DA],
  "Lmidot" => [0x0013F],
  "LongLeftArrow" => [0x027F5],
  "LongLeftRightArrow" => [0x027F7],
  "LongRightArrow" => [0x027F6],
  "Longleftarrow" => [0x027F8],
  "Longleftrightarrow" => [0x027FA],
  "Longrightarrow" => [0x027F9],
  "Lopf" => [0x1D543],
  "LowerLeftArrow" => [0x02199],
  "LowerRightArrow" => [0x02198],
  "Lscr" => [0x02112],
  "Lsh" => [0x021B0],
  "Lstrok" => [0x00141],
  "Lt" => [0x0226A],
  "Map" => [0x02905],
  "Mcy" => [0x0041C],
  "MediumSpace" => [0x0205F],
  "Mellintrf" => [0x02133],
  "Mfr" => [0x1D510],
  "MinusPlus" => [0x02213],
  "Mopf" => [0x1D544],
  "Mscr" => [0x02133],
  "Mu" => [0x0039C],
  "NJcy" => [0x0040A],
  "Nacute" => [0x00143],
  "Ncaron" => [0x00147],
  "Ncedil" => [0x00145],
  "Ncy" => [0x0041D],
  "NegativeMediumSpace" => [0x0200B],
  "NegativeThickSpace" => [0x0200B],
  "NegativeThinSpace" => [0x0200B],
  "NegativeVeryThinSpace" => [0x0200B],
  "NestedGreaterGreater" => [0x0226B],
  "NestedLessLess" => [0x0226A],
  "NewLine" => [0x0000A],
  "Nfr" => [0x1D511],
  "NoBreak" => [0x02060],
  "NonBreakingSpace" => [0x000A0],
  "Nopf" => [0x02115],
  "Not" => [0x02AEC],
  "NotCongruent" => [0x02262],
  "NotCupCap" => [0x0226D],
  "NotDoubleVerticalBar" => [0x02226],
  "NotElement" => [0x02209],
  "NotEqual" => [0x02260],
  "NotEqualTilde" => [0x02242, 0x00338],
  "NotExists" => [0x02204],
  "NotGreater" => [0x0226F],
  "NotGreaterEqual" => [0x02271],
  "NotGreaterFullEqual" => [0x02267, 0x00338],
  "NotGreaterGreater" => [0x0226B, 0x00338],
  "NotGreaterLess" => [0x02279],
  "NotGreaterSlantEqual" => [0x02A7E, 0x00338],
  "NotGreaterTilde" => [0x02275],
  "NotHumpDownHump" => [0x0224E, 0x00338],
  "NotHumpEqual" => [0x0224F, 0x00338],
  "NotLeftTriangle" => [0x022EA],
  "NotLeftTriangleBar" => [0x029CF, 0x00338],
  "NotLeftTriangleEqual" => [0x022EC],
  "NotLess" => [0x0226E],
  "NotLessEqual" => [0x02270],
  "NotLessGreater" => [0x02278],
  "NotLessLess" => [0x0226A, 0x00338],
  "NotLessSlantEqual" => [0x02A7D, 0x00338],
  "NotLessTilde" => [0x02274],
  "NotNestedGreaterGreater" => [0x02AA2, 0x00338],
  "NotNestedLessLess" => [0x02AA1, 0x00338],
  "NotPrecedes" => [0x02280],
  "NotPrecedesEqual" => [0x02AAF, 0x00338],
  "NotPrecedesSlantEqual" => [0x022E0],
  "NotReverseElement" => [0x0220C],
  "NotRightTriangle" => [0x022EB],
  "NotRightTriangleBar" => [0x029D0, 0x00338],
  "NotRightTriangleEqual" => [0x022ED],
  "NotSquareSubset" => [0x0228F, 0x00338],
  "NotSquareSubsetEqual" => [0x022E2],
  "NotSquareSuperset" => [0x02290, 0x00338],
  "NotSquareSupersetEqual" => [0x022E3],
  "NotSubset" => [0x02282, 0x020D2],
  "NotSubsetEqual" => [0x02288],
  "NotSucceeds" => [0x02281],
  "NotSucceedsEqual" => [0x02AB0, 0x00338],
  "NotSucceedsSlantEqual" => [0x022E1],
  "NotSucceedsTilde" => [0x0227F, 0x00338],
  "NotSuperset" => [0x02283, 0x020D2],
  "NotSupersetEqual" => [0x02289],
  "NotTilde" => [0x02241],
  "NotTildeEqual" => [0x02244],
  "NotTildeFullEqual" => [0x02247],
  "NotTildeTilde" => [0x02249],
  "NotVerticalBar" => [0x02224],
  "Nscr" => [0x1D4A9],
  "Ntilde" => [0x000D1],
  "Nu" => [0x0039D],
  "OElig" => [0x00152],
  "Oacute" => [0x000D3],
  "Ocirc" => [0x000D4],
  "Ocy" => [0x0041E],
  "Odblac" => [0x00150],
  "Ofr" => [0x1D512],
  "Ograve" => [0x000D2],
  "Omacr" => [0x0014C],
  "Omega" => [0x003A9],
  "Omicron" => [0x0039F],
  "Oopf" => [0x1D546],
  "OpenCurlyDoubleQuote" => [0x0201C],
  "OpenCurlyQuote" => [0x02018],
  "Or" => [0x02A54],
  "Oscr" => [0x1D4AA],
  "Oslash" => [0x000D8],
  "Otilde" => [0x000D5],
  "Otimes" => [0x02A37],
  "Ouml" => [0x000D6],
  "OverBar" => [0x0203E],
  "OverBrace" => [0x023DE],
  "OverBracket" => [0x023B4],
  "OverParenthesis" => [0x023DC],
  "PartialD" => [0x02202],
  "Pcy" => [0x0041F],
  "Pfr" => [0x1D513],
  "Phi" => [0x003A6],
  "Pi" => [0x003A0],
  "PlusMinus" => [0x000B1],
  "Poincareplane" => [0x0210C],
  "Popf" => [0x02119],
  "Pr" => [0x02ABB],
  "Precedes" => [0x0227A],
  "PrecedesEqual" => [0x02AAF],
  "PrecedesSlantEqual" => [0x0227C],
  "PrecedesTilde" => [0x0227E],
  "Prime" => [0x02033],
  "Product" => [0x0220F],
  "Proportion" => [0x02237],
  "Proportional" => [0x0221D],
  "Pscr" => [0x1D4AB],
  "Psi" => [0x003A8],
  "QUOT" => [0x00022],
  "Qfr" => [0x1D514],
  "Qopf" => [0x0211A],
  "Qscr" => [0x1D4AC],
  "RBarr" => [0x02910],
  "REG" => [0x000AE],
  "Racute" => [0x00154],
  "Rang" => [0x027EB],
  "Rarr" => [0x021A0],
  "Rarrtl" => [0x02916],
  "Rcaron" => [0x00158],
  "Rcedil" => [0x00156],
  "Rcy" => [0x00420],
  "Re" => [0x0211C],
  "ReverseElement" => [0x0220B],
  "ReverseEquilibrium" => [0x021CB],
  "ReverseUpEquilibrium" => [0x0296F],
  "Rfr" => [0x0211C],
  "Rho" => [0x003A1],
  "RightAngleBracket" => [0x027E9],
  "RightArrow" => [0x02192],
  "RightArrowBar" => [0x021E5],
  "RightArrowLeftArrow" => [0x021C4],
  "RightCeiling" => [0x02309],
  "RightDoubleBracket" => [0x027E7],
  "RightDownTeeVector" => [0x0295D],
  "RightDownVector" => [0x021C2],
  "RightDownVectorBar" => [0x02955],
  "RightFloor" => [0x0230B],
  "RightTee" => [0x022A2],
  "RightTeeArrow" => [0x021A6],
  "RightTeeVector" => [0x0295B],
  "RightTriangle" => [0x022B3],
  "RightTriangleBar" => [0x029D0],
  "RightTriangleEqual" => [0x022B5],
  "RightUpDownVector" => [0x0294F],
  "RightUpTeeVector" => [0x0295C],
  "RightUpVector" => [0x021BE],
  "RightUpVectorBar" => [0x02954],
  "RightVector" => [0x021C0],
  "RightVectorBar" => [0x02953],
  "Rightarrow" => [0x021D2],
  "Ropf" => [0x0211D],
  "RoundImplies" => [0x02970],
  "Rrightarrow" => [0x021DB],
  "Rscr" => [0x0211B],
  "Rsh" => [0x021B1],
  "RuleDelayed" => [0x029F4],
  "SHCHcy" => [0x00429],
  "SHcy" => [0x00428],
  "SOFTcy" => [0x0042C],
  "Sacute" => [0x0015A],
  "Sc" => [0x02ABC],
  "Scaron" => [0x00160],
  "Scedil" => [0x0015E],
  "Scirc" => [0x0015C],
  "Scy" => [0x00421],
  "Sfr" => [0x1D516],
  "ShortDownArrow" => [0x02193],
  "ShortLeftArrow" => [0x02190],
  "ShortRightArrow" => [0x02192],
  "ShortUpArrow" => [0x02191],
  "Sigma" => [0x003A3],
  "SmallCircle" => [0x02218],
  "Sopf" => [0x1D54A],
  "Sqrt" => [0x0221A],
  "Square" => [0x025A1],
  "SquareIntersection" => [0x02293],
  "SquareSubset" => [0x0228F],
  "SquareSubsetEqual" => [0x02291],
  "SquareSuperset" => [0x02290],
  "SquareSupersetEqual" => [0x02292],
  "SquareUnion" => [0x02294],
  "Sscr" => [0x1D4AE],
  "Star" => [0x022C6],
  "Sub" => [0x022D0],
  "Subset" => [0x022D0],
  "SubsetEqual" => [0x02286],
  "Succeeds" => [0x0227B],
  "SucceedsEqual" => [0x02AB0],
  "SucceedsSlantEqual" => [0x0227D],
  "SucceedsTilde" => [0x0227F],
  "SuchThat" => [0x0220B],
  "Sum" => [0x02211],
  "Sup" => [0x022D1],
  "Superset" => [0x02283],
  "SupersetEqual" => [0x02287],
  "Supset" => [0x022D1],
  "THORN" => [0x000DE],
  "TRADE" => [0x02122],
  "TSHcy" => [0x0040B],
  "TScy" => [0x00426],
  "Tab" => [0x00009],
  "Tau" => [0x003A4],
  "Tcaron" => [0x00164],
  "Tcedil" => [0x00162],
  "Tcy" => [0x00422],
  "Tfr" => [0x1D517],
  "Therefore" => [0x02234],
  "Theta" => [0x00398],
  "ThickSpace" => [0x0205F, 0x0200A],
  "ThinSpace" => [0x02009],
  "Tilde" => [0x0223C],
  "TildeEqual" => [0x02243],
  "TildeFullEqual" => [0x02245],
  "TildeTilde" => [0x02248],
  "Topf" => [0x1D54B],
  "TripleDot" => [0x020DB],
  "Tscr" => [0x1D4AF],
  "Tstrok" => [0x00166],
  "Uacute" => [0x000DA],
  "Uarr" => [0x0219F],
  "Uarrocir" => [0x02949],
  "Ubrcy" => [0x0040E],
  "Ubreve" => [0x0016C],
  "Ucirc" => [0x000DB],
  "Ucy" => [0x00423],
  "Udblac" => [0x00170],
  "Ufr" => [0x1D518],
  "Ugrave" => [0x000D9],
  "Umacr" => [0x0016A],
  "UnderBar" => [0x0005F],
  "UnderBrace" => [0x023DF],
  "UnderBracket" => [0x023B5],
  "UnderParenthesis" => [0x023DD],
  "Union" => [0x022C3],
  "UnionPlus" => [0x0228E],
  "Uogon" => [0x00172],
  "Uopf" => [0x1D54C],
  "UpArrow" => [0x02191],
  "UpArrowBar" => [0x02912],
  "UpArrowDownArrow" => [0x021C5],
  "UpDownArrow" => [0x02195],
  "UpEquilibrium" => [0x0296E],
  "UpTee" => [0x022A5],
  "UpTeeArrow" => [0x021A5],
  "Uparrow" => [0x021D1],
  "Updownarrow" => [0x021D5],
  "UpperLeftArrow" => [0x02196],
  "UpperRightArrow" => [0x02197],
  "Upsi" => [0x003D2],
  "Upsilon" => [0x003A5],
  "Uring" => [0x0016E],
  "Uscr" => [0x1D4B0],
  "Utilde" => [0x00168],
  "Uuml" => [0x000DC],
  "VDash" => [0x022AB],
  "Vbar" => [0x02AEB],
  "Vcy" => [0x00412],
  "Vdash" => [0x022A9],
  "Vdashl" => [0x02AE6],
  "Vee" => [0x022C1],
  "Verbar" => [0x02016],
  "Vert" => [0x02016],
  "VerticalBar" => [0x02223],
  "VerticalLine" => [0x0007C],
  "VerticalSeparator" => [0x02758],
  "VerticalTilde" => [0x02240],
  "VeryThinSpace" => [0x0200A],
  "Vfr" => [0x1D519],
  "Vopf" => [0x1D54D],
  "Vscr" => [0x1D4B1],
  "Vvdash" => [0x022AA],
  "Wcirc" => [0x00174],
  "Wedge" => [0x022C0],
  "Wfr" => [0x1D51A],
  "Wopf" => [0x1D54E],
  "Wscr" => [0x1D4B2],
  "Xfr" => [0x1D51B],
  "Xi" => [0x0039E],
  "Xopf" => [0x1D54F],
  "Xscr" => [0x1D4B3],
  "YAcy" => [0x0042F],
  "YIcy" => [0x00407],
  "YUcy" => [0x0042E],
  "Yacute" => [0x000DD],
  "Ycirc" => [0x00176],
  "Ycy" => [0x0042B],
  "Yfr" => [0x1D51C],
  "Yopf" => [0x1D550],
  "Yscr" => [0x1D4B4],
  "Yuml" => [0x00178],
  "ZHcy" => [0x00416],
  "Zacute" => [0x00179],
  "Zcaron" => [0x0017D],
  "Zcy" => [0x00417],
  "Zdot" => [0x0017B],
  "ZeroWidthSpace" => [0x0200B],
  "Zeta" => [0x00396],
  "Zfr" => [0x02128],
  "Zopf" => [0x02124],
  "Zscr" => [0x1D4B5],
  "aacute" => [0x000E1],
  "abreve" => [0x00103],
  "ac" => [0x0223E],
  "acE" => [0x0223E, 0x00333],
  "acd" => [0x0223F],
  "acirc" => [0x000E2],
  "acute" => [0x000B4],
  "acy" => [0x00430],
  "aelig" => [0x000E6],
  "af" => [0x02061],
  "afr" => [0x1D51E],
  "agrave" => [0x000E0],
  "alefsym" => [0x02135],
  "aleph" => [0x02135],
  "alpha" => [0x003B1],
  "amacr" => [0x00101],
  "amalg" => [0x02A3F],
  "amp" => [0x00026],
  "and" => [0x02227],
  "andand" => [0x02A55],
  "andd" => [0x02A5C],
  "andslope" => [0x02A58],
  "andv" => [0x02A5A],
  "ang" => [0x02220],
  "ange" => [0x029A4],
  "angle" => [0x02220],
  "angmsd" => [0x02221],
  "angmsdaa" => [0x029A8],
  "angmsdab" => [0x029A9],
  "angmsdac" => [0x029AA],
  "angmsdad" => [0x029AB],
  "angmsdae" => [0x029AC],
  "angmsdaf" => [0x029AD],
  "angmsdag" => [0x029AE],
  "angmsdah" => [0x029AF],
  "angrt" => [0x0221F],
  "angrtvb" => [0x022BE],
  "angrtvbd" => [0x0299D],
  "angsph" => [0x02222],
  "angst" => [0x000C5],
  "angzarr" => [0x0237C],
  "aogon" => [0x00105],
  "aopf" => [0x1D552],
  "ap" => [0x02248],
  "apE" => [0x02A70],
  "apacir" => [0x02A6F],
  "ape" => [0x0224A],
  "apid" => [0x0224B],
  "apos" => [0x00027],
  "approx" => [0x02248],
  "approxeq" => [0x0224A],
  "aring" => [0x000E5],
  "ascr" => [0x1D4B6],
  "ast" => [0x0002A],
  "asymp" => [0x02248],
  "asympeq" => [0x0224D],
  "atilde" => [0x000E3],
  "auml" => [0x000E4],
  "awconint" => [0x02233],
  "awint" => [0x02A11],
  "bNot" => [0x02AED],
  "backcong" => [0x0224C],
  "backepsilon" => [0x003F6],
  "backprime" => [0x02035],
  "backsim" => [0x0223D],
  "backsimeq" => [0x022CD],
  "barvee" => [0x022BD],
  "barwed" => [0x02305],
  "barwedge" => [0x02305],
  "bbrk" => [0x023B5],
  "bbrktbrk" => [0x023B6],
  "bcong" => [0x0224C],
  "bcy" => [0x00431],
  "bdquo" => [0x0201E],
  "becaus" => [0x02235],
  "because" => [0x02235],
  "bemptyv" => [0x029B0],
  "bepsi" => [0x003F6],
  "bernou" => [0x0212C],
  "beta" => [0x003B2],
  "beth" => [0x02136],
  "between" => [0x0226C],
  "bfr" => [0x1D51F],
  "bigcap" => [0x022C2],
  "bigcirc" => [0x025EF],
  "bigcup" => [0x022C3],
  "bigodot" => [0x02A00],
  "bigoplus" => [0x02A01],
  "bigotimes" => [0x02A02],
  "bigsqcup" => [0x02A06],
  "bigstar" => [0x02605],
  "bigtriangledown" => [0x025BD],
  "bigtriangleup" => [0x025B3],
  "biguplus" => [0x02A04],
  "bigvee" => [0x022C1],
  "bigwedge" => [0x022C0],
  "bkarow" => [0x0290D],
  "blacklozenge" => [0x029EB],
  "blacksquare" => [0x025AA],
  "blacktriangle" => [0x025B4],
  "blacktriangledown" => [0x025BE],
  "blacktriangleleft" => [0x025C2],
  "blacktriangleright" => [0x025B8],
  "blank" => [0x02423],
  "blk12" => [0x02592],
  "blk14" => [0x02591],
  "blk34" => [0x02593],
  "block" => [0x02588],
  "bne" => [0x0003D, 0x020E5],
  "bnequiv" => [0x02261, 0x020E5],
  "bnot" => [0x02310],
  "bopf" => [0x1D553],
  "bot" => [0x022A5],
  "bottom" => [0x022A5],
  "bowtie" => [0x022C8],
  "boxDL" => [0x02557],
  "boxDR" => [0x02554],
  "boxDl" => [0x02556],
  "boxDr" => [0x02553],
  "boxH" => [0x02550],
  "boxHD" => [0x02566],
  "boxHU" => [0x02569],
  "boxHd" => [0x02564],
  "boxHu" => [0x02567],
  "boxUL" => [0x0255D],
  "boxUR" => [0x0255A],
  "boxUl" => [0x0255C],
  "boxUr" => [0x02559],
  "boxV" => [0x02551],
  "boxVH" => [0x0256C],
  "boxVL" => [0x02563],
  "boxVR" => [0x02560],
  "boxVh" => [0x0256B],
  "boxVl" => [0x02562],
  "boxVr" => [0x0255F],
  "boxbox" => [0x029C9],
  "boxdL" => [0x02555],
  "boxdR" => [0x02552],
  "boxdl" => [0x02510],
  "boxdr" => [0x0250C],
  "boxh" => [0x02500],
  "boxhD" => [0x02565],
  "boxhU" => [0x02568],
  "boxhd" => [0x0252C],
  "boxhu" => [0x02534],
  "boxminus" => [0x0229F],
  "boxplus" => [0x0229E],
  "boxtimes" => [0x022A0],
  "boxuL" => [0x0255B],
  "boxuR" => [0x02558],
  "boxul" => [0x02518],
  "boxur" => [0x02514],
  "boxv" => [0x02502],
  "boxvH" => [0x0256A],
  "boxvL" => [0x02561],
  "boxvR" => [0x0255E],
  "boxvh" => [0x0253C],
  "boxvl" => [0x02524],
  "boxvr" => [0x0251C],
  "bprime" => [0x02035],
  "breve" => [0x002D8],
  "brvbar" => [0x000A6],
  "bscr" => [0x1D4B7],
  "bsemi" => [0x0204F],
  "bsim" => [0x0223D],
  "bsime" => [0x022CD],
  "bsol" => [0x0005C],
  "bsolb" => [0x029C5],
  "bsolhsub" => [0x027C8],
  "bull" => [0x02022],
  "bullet" => [0x02022],
  "bump" => [0x0224E],
  "bumpE" => [0x02AAE],
  "bumpe" => [0x0224F],
  "bumpeq" => [0x0224F],
  "cacute" => [0x00107],
  "cap" => [0x02229],
  "capand" => [0x02A44],
  "capbrcup" => [0x02A49],
  "capcap" => [0x02A4B],
  "capcup" => [0x02A47],
  "capdot" => [0x02A40],
  "caps" => [0x02229, 0x0FE00],
  "caret" => [0x02041],
  "caron" => [0x002C7],
  "ccaps" => [0x02A4D],
  "ccaron" => [0x0010D],
  "ccedil" => [0x000E7],
  "ccirc" => [0x00109],
  "ccups" => [0x02A4C],
  "ccupssm" => [0x02A50],
  "cdot" => [0x0010B],
  "cedil" => [0x000B8],
  "cemptyv" => [0x029B2],
  "cent" => [0x000A2],
  "centerdot" => [0x000B7],
  "cfr" => [0x1D520],
  "chcy" => [0x00447],
  "check" => [0x02713],
  "checkmark" => [0x02713],
  "chi" => [0x003C7],
  "cir" => [0x025CB],
  "cirE" => [0x029C3],
  "circ" => [0x002C6],
  "circeq" => [0x02257],
  "circlearrowleft" => [0x021BA],
  "circlearrowright" => [0x021BB],
  "circledR" => [0x000AE],
  "circledS" => [0x024C8],
  "circledast" => [0x0229B],
  "circledcirc" => [0x0229A],
  "circleddash" => [0x0229D],
  "cire" => [0x02257],
  "cirfnint" => [0x02A10],
  "cirmid" => [0x02AEF],
  "cirscir" => [0x029C2],
  "clubs" => [0x02663],
  "clubsuit" => [0x02663],
  "colon" => [0x0003A],
  "colone" => [0x02254],
  "coloneq" => [0x02254],
  "comma" => [0x0002C],
  "commat" => [0x00040],
  "comp" => [0x02201],
  "compfn" => [0x02218],
  "complement" => [0x02201],
  "complexes" => [0x02102],
  "cong" => [0x02245],
  "congdot" => [0x02A6D],
  "conint" => [0x0222E],
  "copf" => [0x1D554],
  "coprod" => [0x02210],
  "copy" => [0x000A9],
  "copysr" => [0x02117],
  "crarr" => [0x021B5],
  "cross" => [0x02717],
  "cscr" => [0x1D4B8],
  "csub" => [0x02ACF],
  "csube" => [0x02AD1],
  "csup" => [0x02AD0],
  "csupe" => [0x02AD2],
  "ctdot" => [0x022EF],
  "cudarrl" => [0x02938],
  "cudarrr" => [0x02935],
  "cuepr" => [0x022DE],
  "cuesc" => [0x022DF],
  "cularr" => [0x021B6],
  "cularrp" => [0x0293D],
  "cup" => [0x0222A],
  "cupbrcap" => [0x02A48],
  "cupcap" => [0x02A46],
  "cupcup" => [0x02A4A],
  "cupdot" => [0x0228D],
  "cupor" => [0x02A45],
  "cups" => [0x0222A, 0x0FE00],
  "curarr" => [0x021B7],
  "curarrm" => [0x0293C],
  "curlyeqprec" => [0x022DE],
  "curlyeqsucc" => [0x022DF],
  "curlyvee" => [0x022CE],
  "curlywedge" => [0x022CF],
  "curren" => [0x000A4],
  "curvearrowleft" => [0x021B6],
  "curvearrowright" => [0x021B7],
  "cuvee" => [0x022CE],
  "cuwed" => [0x022CF],
  "cwconint" => [0x02232],
  "cwint" => [0x02231],
  "cylcty" => [0x0232D],
  "dArr" => [0x021D3],
  "dHar" => [0x02965],
  "dagger" => [0x02020],
  "daleth" => [0x02138],
  "darr" => [0x02193],
  "dash" => [0x02010],
  "dashv" => [0x022A3],
  "dbkarow" => [0x0290F],
  "dblac" => [0x002DD],
  "dcaron" => [0x0010F],
  "dcy" => [0x00434],
  "dd" => [0x02146],
  "ddagger" => [0x02021],
  "ddarr" => [0x021CA],
  "ddotseq" => [0x02A77],
  "deg" => [0x000B0],
  "delta" => [0x003B4],
  "demptyv" => [0x029B1],
  "dfisht" => [0x0297F],
  "dfr" => [0x1D521],
  "dharl" => [0x021C3],
  "dharr" => [0x021C2],
  "diam" => [0x022C4],
  "diamond" => [0x022C4],
  "diamondsuit" => [0x02666],
  "diams" => [0x02666],
  "die" => [0x000A8],
  "digamma" => [0x003DD],
  "disin" => [0x022F2],
  "div" => [0x000F7],
  "divide" => [0x000F7],
  "divideontimes" => [0x022C7],
  "divonx" => [0x022C7],
  "djcy" => [0x00452],
  "dlcorn" => [0x0231E],
  "dlcrop" => [0x0230D],
  "dollar" => [0x00024],
  "dopf" => [0x1D555],
  "dot" => [0x002D9],
  "doteq" => [0x02250],
  "doteqdot" => [0x02251],
  "dotminus" => [0x02238],
  "dotplus" => [0x02214],
  "dotsquare" => [0x022A1],
  "doublebarwedge" => [0x02306],
  "downarrow" => [0x02193],
  "downdownarrows" => [0x021CA],
  "downharpoonleft" => [0x021C3],
  "downharpoonright" => [0x021C2],
  "drbkarow" => [0x02910],
  "drcorn" => [0x0231F],
  "drcrop" => [0x0230C],
  "dscr" => [0x1D4B9],
  "dscy" => [0x00455],
  "dsol" => [0x029F6],
  "dstrok" => [0x00111],
  "dtdot" => [0x022F1],
  "dtri" => [0x025BF],
  "dtrif" => [0x025BE],
  "duarr" => [0x021F5],
  "duhar" => [0x0296F],
  "dwangle" => [0x029A6],
  "dzcy" => [0x0045F],
  "dzigrarr" => [0x027FF],
  "eDDot" => [0x02A77],
  "eDot" => [0x02251],
  "eacute" => [0x000E9],
  "easter" => [0x02A6E],
  "ecaron" => [0x0011B],
  "ecir" => [0x02256],
  "ecirc" => [0x000EA],
  "ecolon" => [0x02255],
  "ecy" => [0x0044D],
  "edot" => [0x00117],
  "ee" => [0x02147],
  "efDot" => [0x02252],
  "efr" => [0x1D522],
  "eg" => [0x02A9A],
  "egrave" => [0x000E8],
  "egs" => [0x02A96],
  "egsdot" => [0x02A98],
  "el" => [0x02A99],
  "elinters" => [0x023E7],
  "ell" => [0x02113],
  "els" => [0x02A95],
  "elsdot" => [0x02A97],
  "emacr" => [0x00113],
  "empty" => [0x02205],
  "emptyset" => [0x02205],
  "emptyv" => [0x02205],
  "emsp" => [0x02003],
  "emsp13" => [0x02004],
  "emsp14" => [0x02005],
  "eng" => [0x0014B],
  "ensp" => [0x02002],
  "eogon" => [0x00119],
  "eopf" => [0x1D556],
  "epar" => [0x022D5],
  "eparsl" => [0x029E3],
  "eplus" => [0x02A71],
  "epsi" => [0x003B5],
  "epsilon" => [0x003B5],
  "epsiv" => [0x003F5],
  "eqcirc" => [0x02256],
  "eqcolon" => [0x02255],
  "eqsim" => [0x02242],
  "eqslantgtr" => [0x02A96],
  "eqslantless" => [0x02A95],
  "equals" => [0x0003D],
  "equest" => [0x0225F],
  "equiv" => [0x02261],
  "equivDD" => [0x02A78],
  "eqvparsl" => [0x029E5],
  "erDot" => [0x02253],
  "erarr" => [0x02971],
  "escr" => [0x0212F],
  "esdot" => [0x02250],
  "esim" => [0x02242],
  "eta" => [0x003B7],
  "eth" => [0x000F0],
  "euml" => [0x000EB],
  "euro" => [0x020AC],
  "excl" => [0x00021],
  "exist" => [0x02203],
  "expectation" => [0x02130],
  "exponentiale" => [0x02147],
  "fallingdotseq" => [0x02252],
  "fcy" => [0x00444],
  "female" => [0x02640],
  "ffilig" => [0x0FB03],
  "fflig" => [0x0FB00],
  "ffllig" => [0x0FB04],
  "ffr" => [0x1D523],
  "filig" => [0x0FB01],
  "fjlig" => [0x00066, 0x0006A],
  "flat" => [0x0266D],
  "fllig" => [0x0FB02],
  "fltns" => [0x025B1],
  "fnof" => [0x00192],
  "fopf" => [0x1D557],
  "forall" => [0x02200],
  "fork" => [0x022D4],
  "forkv" => [0x02AD9],
  "fpartint" => [0x02A0D],
  "frac12" => [0x000BD],
  "frac13" => [0x02153],
  "frac14" => [0x000BC],
  "frac15" => [0x02155],
  "frac16" => [0x02159],
  "frac18" => [0x0215B],
  "frac23" => [0x02154],
  "frac25" => [0x02156],
  "frac34" => [0x000BE],
  "frac35" => [0x02157],
  "frac38" => [0x0215C],
  "frac45" => [0x02158],
  "frac56" => [0x0215A],
  "frac58" => [0x0215D],
  "frac78" => [0x0215E],
  "frasl" => [0x02044],
  "frown" => [0x02322],
  "fscr" => [0x1D4BB],
  "gE" => [0x02267],
  "gEl" => [0x02A8C],
  "gacute" => [0x001F5],
  "gamma" => [0x003B3],
  "gammad" => [0x003DD],
  "gap" => [0x02A86],
  "gbreve" => [0x0011F],
  "gcirc" => [0x0011D],
  "gcy" => [0x00433],
  "gdot" => [0x00121],
  "ge" => [0x02265],
  "gel" => [0x022DB],
  "geq" => [0x02265],
  "geqq" => [0x02267],
  "geqslant" => [0x02A7E],
  "ges" => [0x02A7E],
  "gescc" => [0x02AA9],
  "gesdot" => [0x02A80],
  "gesdoto" => [0x02A82],
  "gesdotol" => [0x02A84],
  "gesl" => [0x022DB, 0x0FE00],
  "gesles" => [0x02A94],
  "gfr" => [0x1D524],
  "gg" => [0x0226B],
  "ggg" => [0x022D9],
  "gimel" => [0x02137],
  "gjcy" => [0x00453],
  "gl" => [0x02277],
  "glE" => [0x02A92],
  "gla" => [0x02AA5],
  "glj" => [0x02AA4],
  "gnE" => [0x02269],
  "gnap" => [0x02A8A],
  "gnapprox" => [0x02A8A],
  "gne" => [0x02A88],
  "gneq" => [0x02A88],
  "gneqq" => [0x02269],
  "gnsim" => [0x022E7],
  "gopf" => [0x1D558],
  "grave" => [0x00060],
  "gscr" => [0x0210A],
  "gsim" => [0x02273],
  "gsime" => [0x02A8E],
  "gsiml" => [0x02A90],
  "gt" => [0x0003E],
  "gtcc" => [0x02AA7],
  "gtcir" => [0x02A7A],
  "gtdot" => [0x022D7],
  "gtlPar" => [0x02995],
  "gtquest" => [0x02A7C],
  "gtrapprox" => [0x02A86],
  "gtrarr" => [0x02978],
  "gtrdot" => [0x022D7],
  "gtreqless" => [0x022DB],
  "gtreqqless" => [0x02A8C],
  "gtrless" => [0x02277],
  "gtrsim" => [0x02273],
  "gvertneqq" => [0x02269, 0x0FE00],
  "gvnE" => [0x02269, 0x0FE00],
  "hArr" => [0x021D4],
  "hairsp" => [0x0200A],
  "half" => [0x000BD],
  "hamilt" => [0x0210B],
  "hardcy" => [0x0044A],
  "harr" => [0x02194],
  "harrcir" => [0x02948],
  "harrw" => [0x021AD],
  "hbar" => [0x0210F],
  "hcirc" => [0x00125],
  "hearts" => [0x02665],
  "heartsuit" => [0x02665],
  "hellip" => [0x02026],
  "hercon" => [0x022B9],
  "hfr" => [0x1D525],
  "hksearow" => [0x02925],
  "hkswarow" => [0x02926],
  "hoarr" => [0x021FF],
  "homtht" => [0x0223B],
  "hookleftarrow" => [0x021A9],
  "hookrightarrow" => [0x021AA],
  "hopf" => [0x1D559],
  "horbar" => [0x02015],
  "hscr" => [0x1D4BD],
  "hslash" => [0x0210F],
  "hstrok" => [0x00127],
  "hybull" => [0x02043],
  "hyphen" => [0x02010],
  "iacute" => [0x000ED],
  "ic" => [0x02063],
  "icirc" => [0x000EE],
  "icy" => [0x00438],
  "iecy" => [0x00435],
  "iexcl" => [0x000A1],
  "iff" => [0x021D4],
  "ifr" => [0x1D526],
  "igrave" => [0x000EC],
  "ii" => [0x02148],
  "iiiint" => [0x02A0C],
  "iiint" => [0x0222D],
  "iinfin" => [0x029DC],
  "iiota" => [0x02129],
  "ijlig" => [0x00133],
  "imacr" => [0x0012B],
  "image" => [0x02111],
  "imagline" => [0x02110],
  "imagpart" => [0x02111],
  "imath" => [0x00131],
  "imof" => [0x022B7],
  "imped" => [0x001B5],
  "in" => [0x02208],
  "incare" => [0x02105],
  "infin" => [0x0221E],
  "infintie" => [0x029DD],
  "inodot" => [0x00131],
  "int" => [0x0222B],
  "intcal" => [0x022BA],
  "integers" => [0x02124],
  "intercal" => [0x022BA],
  "intlarhk" => [0x02A17],
  "intprod" => [0x02A3C],
  "iocy" => [0x00451],
  "iogon" => [0x0012F],
  "iopf" => [0x1D55A],
  "iota" => [0x003B9],
  "iprod" => [0x02A3C],
  "iquest" => [0x000BF],
  "iscr" => [0x1D4BE],
  "isin" => [0x02208],
  "isinE" => [0x022F9],
  "isindot" => [0x022F5],
  "isins" => [0x022F4],
  "isinsv" => [0x022F3],
  "isinv" => [0x02208],
  "it" => [0x02062],
  "itilde" => [0x00129],
  "iukcy" => [0x00456],
  "iuml" => [0x000EF],
  "jcirc" => [0x00135],
  "jcy" => [0x00439],
  "jfr" => [0x1D527],
  "jmath" => [0x00237],
  "jopf" => [0x1D55B],
  "jscr" => [0x1D4BF],
  "jsercy" => [0x00458],
  "jukcy" => [0x00454],
  "kappa" => [0x003BA],
  "kappav" => [0x003F0],
  "kcedil" => [0x00137],
  "kcy" => [0x0043A],
  "kfr" => [0x1D528],
  "kgreen" => [0x00138],
  "khcy" => [0x00445],
  "kjcy" => [0x0045C],
  "kopf" => [0x1D55C],
  "kscr" => [0x1D4C0],
  "lAarr" => [0x021DA],
  "lArr" => [0x021D0],
  "lAtail" => [0x0291B],
  "lBarr" => [0x0290E],
  "lE" => [0x02266],
  "lEg" => [0x02A8B],
  "lHar" => [0x02962],
  "lacute" => [0x0013A],
  "laemptyv" => [0x029B4],
  "lagran" => [0x02112],
  "lambda" => [0x003BB],
  "lang" => [0x027E8],
  "langd" => [0x02991],
  "langle" => [0x027E8],
  "lap" => [0x02A85],
  "laquo" => [0x000AB],
  "larr" => [0x02190],
  "larrb" => [0x021E4],
  "larrbfs" => [0x0291F],
  "larrfs" => [0x0291D],
  "larrhk" => [0x021A9],
  "larrlp" => [0x021AB],
  "larrpl" => [0x02939],
  "larrsim" => [0x02973],
  "larrtl" => [0x021A2],
  "lat" => [0x02AAB],
  "latail" => [0x02919],
  "late" => [0x02AAD],
  "lates" => [0x02AAD, 0x0FE00],
  "lbarr" => [0x0290C],
  "lbbrk" => [0x02772],
  "lbrace" => [0x0007B],
  "lbrack" => [0x0005B],
  "lbrke" => [0x0298B],
  "lbrksld" => [0x0298F],
  "lbrkslu" => [0x0298D],
  "lcaron" => [0x0013E],
  "lcedil" => [0x0013C],
  "lceil" => [0x02308],
  "lcub" => [0x0007B],
  "lcy" => [0x0043B],
  "ldca" => [0x02936],
  "ldquo" => [0x0201C],
  "ldquor" => [0x0201E],
  "ldrdhar" => [0x02967],
  "ldrushar" => [0x0294B],
  "ldsh" => [0x021B2],
  "le" => [0x02264],
  "leftarrow" => [0x02190],
  "leftarrowtail" => [0x021A2],
  "leftharpoondown" => [0x021BD],
  "leftharpoonup" => [0x021BC],
  "leftleftarrows" => [0x021C7],
  "leftrightarrow" => [0x02194],
  "leftrightarrows" => [0x021C6],
  "leftrightharpoons" => [0x021CB],
  "leftrightsquigarrow" => [0x021AD],
  "leftthreetimes" => [0x022CB],
  "leg" => [0x022DA],
  "leq" => [0x02264],
  "leqq" => [0x02266],
  "leqslant" => [0x02A7D],
  "les" => [0x02A7D],
  "lescc" => [0x02AA8],
  "lesdot" => [0x02A7F],
  "lesdoto" => [0x02A81],
  "lesdotor" => [0x02A83],
  "lesg" => [0x022DA, 0x0FE00],
  "lesges" => [0x02A93],
  "lessapprox" => [0x02A85],
  "lessdot" => [0x022D6],
  "lesseqgtr" => [0x022DA],
  "lesseqqgtr" => [0x02A8B],
  "lessgtr" => [0x02276],
  "lesssim" => [0x02272],
  "lfisht" => [0x0297C],
  "lfloor" => [0x0230A],
  "lfr" => [0x1D529],
  "lg" => [0x02276],
  "lgE" => [0x02A91],
  "lhard" => [0x021BD],
  "lharu" => [0x021BC],
  "lharul" => [0x0296A],
  "lhblk" => [0x02584],
  "ljcy" => [0x00459],
  "ll" => [0x0226A],
  "llarr" => [0x021C7],
  "llcorner" => [0x0231E],
  "llhard" => [0x0296B],
  "lltri" => [0x025FA],
  "lmidot" => [0x00140],
  "lmoust" => [0x023B0],
  "lmoustache" => [0x023B0],
  "lnE" => [0x02268],
  "lnap" => [0x02A89],
  "lnapprox" => [0x02A89],
  "lne" => [0x02A87],
  "lneq" => [0x02A87],
  "lneqq" => [0x02268],
  "lnsim" => [0x022E6],
  "loang" => [0x027EC],
  "loarr" => [0x021FD],
  "lobrk" => [0x027E6],
  "longleftarrow" => [0x027F5],
  "longleftrightarrow" => [0x027F7],
  "longmapsto" => [0x027FC],
  "longrightarrow" => [0x027F6],
  "looparrowleft" => [0x021AB],
  "looparrowright" => [0x021AC],
  "lopar" => [0x02985],
  "lopf" => [0x1D55D],
  "loplus" => [0x02A2D],
  "lotimes" => [0x02A34],
  "lowast" => [0x02217],
  "lowbar" => [0x0005F],
  "loz" => [0x025CA],
  "lozenge" => [0x025CA],
  "lozf" => [0x029EB],
  "lpar" => [0x00028],
  "lparlt" => [0x02993],
  "lrarr" => [0x021C6],
  "lrcorner" => [0x0231F],
  "lrhar" => [0x021CB],
  "lrhard" => [0x0296D],
  "lrm" => [0x0200E],
  "lrtri" => [0x022BF],
  "lsaquo" => [0x02039],
  "lscr" => [0x1D4C1],
  "lsh" => [0x021B0],
  "lsim" => [0x02272],
  "lsime" => [0x02A8D],
  "lsimg" => [0x02A8F],
  "lsqb" => [0x0005B],
  "lsquo" => [0x02018],
  "lsquor" => [0x0201A],
  "lstrok" => [0x00142],
  "lt" => [0x0003C],
  "ltcc" => [0x02AA6],
  "ltcir" => [0x02A79],
  "ltdot" => [0x022D6],
  "lthree" => [0x022CB],
  "ltimes" => [0x022C9],
  "ltlarr" => [0x02976],
  "ltquest" => [0x02A7B],
  "ltrPar" => [0x02996],
  "ltri" => [0x025C3],
  "ltrie" => [0x022B4],
  "ltrif" => [0x025C2],
  "lurdshar" => [0x0294A],
  "luruhar" => [0x02966],
  "lvertneqq" => [0x02268, 0x0FE00],
  "lvnE" => [0x02268, 0x0FE00],
  "mDDot" => [0x0223A],
  "macr" => [0x000AF],
  "male" => [0x02642],
  "malt" => [0x02720],
  "maltese" => [0x02720],
  "map" => [0x021A6],
  "mapsto" => [0x021A6],
  "mapstodown" => [0x021A7],
  "mapstoleft" => [0x021A4],
  "mapstoup" => [0x021A5],
  "marker" => [0x025AE],
  "mcomma" => [0x02A29],
  "mcy" => [0x0043C],
  "mdash" => [0x02014],
  "measuredangle" => [0x02221],
  "mfr" => [0x1D52A],
  "mho" => [0x02127],
  "micro" => [0x000B5],
  "mid" => [0x02223],
  "midast" => [0x0002A],
  "midcir" => [0x02AF0],
  "middot" => [0x000B7],
  "minus" => [0x02212],
  "minusb" => [0x0229F],
  "minusd" => [0x02238],
  "minusdu" => [0x02A2A],
  "mlcp" => [0x02ADB],
  "mldr" => [0x02026],
  "mnplus" => [0x02213],
  "models" => [0x022A7],
  "mopf" => [0x1D55E],
  "mp" => [0x02213],
  "mscr" => [0x1D4C2],
  "mstpos" => [0x0223E],
  "mu" => [0x003BC],
  "multimap" => [0x022B8],
  "mumap" => [0x022B8],
  "nGg" => [0x022D9, 0x00338],
  "nGt" => [0x0226B, 0x020D2],
  "nGtv" => [0x0226B, 0x00338],
  "nLeftarrow" => [0x021CD],
  "nLeftrightarrow" => [0x021CE],
  "nLl" => [0x022D8, 0x00338],
  "nLt" => [0x0226A, 0x020D2],
  "nLtv" => [0x0226A, 0x00338],
  "nRightarrow" => [0x021CF],
  "nVDash" => [0x022AF],
  "nVdash" => [0x022AE],
  "nabla" => [0x02207],
  "nacute" => [0x00144],
  "nang" => [0x02220, 0x020D2],
  "nap" => [0x02249],
  "napE" => [0x02A70, 0x00338],
  "napid" => [0x0224B, 0x00338],
  "napos" => [0x00149],
  "napprox" => [0x02249],
  "natur" => [0x0266E],
  "natural" => [0x0266E],
  "naturals" => [0x02115],
  "nbsp" => [0x000A0],
  "nbump" => [0x0224E, 0x00338],
  "nbumpe" => [0x0224F, 0x00338],
  "ncap" => [0x02A43],
  "ncaron" => [0x00148],
  "ncedil" => [0x00146],
  "ncong" => [0x02247],
  "ncongdot" => [0x02A6D, 0x00338],
  "ncup" => [0x02A42],
  "ncy" => [0x0043D],
  "ndash" => [0x02013],
  "ne" => [0x02260],
  "neArr" => [0x021D7],
  "nearhk" => [0x02924],
  "nearr" => [0x02197],
  "nearrow" => [0x02197],
  "nedot" => [0x02250, 0x00338],
  "nequiv" => [0x02262],
  "nesear" => [0x02928],
  "nesim" => [0x02242, 0x00338],
  "nexist" => [0x02204],
  "nexists" => [0x02204],
  "nfr" => [0x1D52B],
  "ngE" => [0x02267, 0x00338],
  "nge" => [0x02271],
  "ngeq" => [0x02271],
  "ngeqq" => [0x02267, 0x00338],
  "ngeqslant" => [0x02A7E, 0x00338],
  "nges" => [0x02A7E, 0x00338],
  "ngsim" => [0x02275],
  "ngt" => [0x0226F],
  "ngtr" => [0x0226F],
  "nhArr" => [0x021CE],
  "nharr" => [0x021AE],
  "nhpar" => [0x02AF2],
  "ni" => [0x0220B],
  "nis" => [0x022FC],
  "nisd" => [0x022FA],
  "niv" => [0x0220B],
  "njcy" => [0x0045A],
  "nlArr" => [0x021CD],
  "nlE" => [0x02266, 0x00338],
  "nlarr" => [0x0219A],
  "nldr" => [0x02025],
  "nle" => [0x02270],
  "nleftarrow" => [0x0219A],
  "nleftrightarrow" => [0x021AE],
  "nleq" => [0x02270],
  "nleqq" => [0x02266, 0x00338],
  "nleqslant" => [0x02A7D, 0x00338],
  "nles" => [0x02A7D, 0x00338],
  "nless" => [0x0226E],
  "nlsim" => [0x02274],
  "nlt" => [0x0226E],
  "nltri" => [0x022EA],
  "nltrie" => [0x022EC],
  "nmid" => [0x02224],
  "nopf" => [0x1D55F],
  "not" => [0x000AC],
  "notin" => [0x02209],
  "notinE" => [0x022F9, 0x00338],
  "notindot" => [0x022F5, 0x00338],
  "notinva" => [0x02209],
  "notinvb" => [0x022F7],
  "notinvc" => [0x022F6],
  "notni" => [0x0220C],
  "notniva" => [0x0220C],
  "notnivb" => [0x022FE],
  "notnivc" => [0x022FD],
  "npar" => [0x02226],
  "nparallel" => [0x02226],
  "nparsl" => [0x02AFD, 0x020E5],
  "npart" => [0x02202, 0x00338],
  "npolint" => [0x02A14],
  "npr" => [0x02280],
  "nprcue" => [0x022E0],
  "npre" => [0x02AAF, 0x00338],
  "nprec" => [0x02280],
  "npreceq" => [0x02AAF, 0x00338],
  "nrArr" => [0x021CF],
  "nrarr" => [0x0219B],
  "nrarrc" => [0x02933, 0x00338],
  "nrarrw" => [0x0219D, 0x00338],
  "nrightarrow" => [0x0219B],
  "nrtri" => [0x022EB],
  "nrtrie" => [0x022ED],
  "nsc" => [0x02281],
  "nsccue" => [0x022E1],
  "nsce" => [0x02AB0, 0x00338],
  "nscr" => [0x1D4C3],
  "nshortmid" => [0x02224],
  "nshortparallel" => [0x02226],
  "nsim" => [0x02241],
  "nsime" => [0x02244],
  "nsimeq" => [0x02244],
  "nsmid" => [0x02224],
  "nspar" => [0x02226],
  "nsqsube" => [0x022E2],
  "nsqsupe" => [0x022E3],
  "nsub" => [0x02284],
  "nsubE" => [0x02AC5, 0x00338],
  "nsube" => [0x02288],
  "nsubset" => [0x02282, 0x020D2],
  "nsubseteq" => [0x02288],
  "nsubseteqq" => [0x02AC5, 0x00338],
  "nsucc" => [0x02281],
  "nsucceq" => [0x02AB0, 0x00338],
  "nsup" => [0x02285],
  "nsupE" => [0x02AC6, 0x00338],
  "nsupe" => [0x02289],
  "nsupset" => [0x02283, 0x020D2],
  "nsupseteq" => [0x02289],
  "nsupseteqq" => [0x02AC6, 0x00338],
  "ntgl" => [0x02279],
  "ntilde" => [0x000F1],
  "ntlg" => [0x02278],
  "ntriangleleft" => [0x022EA],
  "ntrianglelefteq" => [0x022EC],
  "ntriangleright" => [0x022EB],
  "ntrianglerighteq" => [0x022ED],
  "nu" => [0x003BD],
  "num" => [0x00023],
  "numero" => [0x02116],
  "numsp" => [0x02007],
  "nvDash" => [0x022AD],
  "nvHarr" => [0x02904],
  "nvap" => [0x0224D, 0x020D2],
  "nvdash" => [0x022AC],
  "nvge" => [0x02265, 0x020D2],
  "nvgt" => [0x0003E, 0x020D2],
  "nvinfin" => [0x029DE],
  "nvlArr" => [0x02902],
  "nvle" => [0x02264, 0x020D2],
  "nvlt" => [0x0003C, 0x020D2],
  "nvltrie" => [0x022B4, 0x020D2],
  "nvrArr" => [0x02903],
  "nvrtrie" => [0x022B5, 0x020D2],
  "nvsim" => [0x0223C, 0x020D2],
  "nwArr" => [0x021D6],
  "nwarhk" => [0x02923],
  "nwarr" => [0x02196],
  "nwarrow" => [0x02196],
  "nwnear" => [0x02927],
  "oS" => [0x024C8],
  "oacute" => [0x000F3],
  "oast" => [0x0229B],
  "ocir" => [0x0229A],
  "ocirc" => [0x000F4],
  "ocy" => [0x0043E],
  "odash" => [0x0229D],
  "odblac" => [0x00151],
  "odiv" => [0x02A38],
  "odot" => [0x02299],
  "odsold" => [0x029BC],
  "oelig" => [0x00153],
  "ofcir" => [0x029BF],
  "ofr" => [0x1D52C],
  "ogon" => [0x002DB],
  "ograve" => [0x000F2],
  "ogt" => [0x029C1],
  "ohbar" => [0x029B5],
  "ohm" => [0x003A9],
  "oint" => [0x0222E],
  "olarr" => [0x021BA],
  "olcir" => [0x029BE],
  "olcross" => [0x029BB],
  "oline" => [0x0203E],
  "olt" => [0x029C0],
  "omacr" => [0x0014D],
  "omega" => [0x003C9],
  "omicron" => [0x003BF],
  "omid" => [0x029B6],
  "ominus" => [0x02296],
  "oopf" => [0x1D560],
  "opar" => [0x029B7],
  "operp" => [0x029B9],
  "oplus" => [0x02295],
  "or" => [0x02228],
  "orarr" => [0x021BB],
  "ord" => [0x02A5D],
  "order" => [0x02134],
  "orderof" => [0x02134],
  "ordf" => [0x000AA],
  "ordm" => [0x000BA],
  "origof" => [0x022B6],
  "oror" => [0x02A56],
  "orslope" => [0x02A57],
  "orv" => [0x02A5B],
  "oscr" => [0x02134],
  "oslash" => [0x000F8],
  "osol" => [0x02298],
  "otilde" => [0x000F5],
  "otimes" => [0x02297],
  "otimesas" => [0x02A36],
  "ouml" => [0x000F6],
  "ovbar" => [0x0233D],
  "par" => [0x02225],
  "para" => [0x000B6],
  "parallel" => [0x02225],
  "parsim" => [0x02AF3],
  "parsl" => [0x02AFD],
  "part" => [0x02202],
  "pcy" => [0x0043F],
  "percnt" => [0x00025],
  "period" => [0x0002E],
  "permil" => [0x02030],
  "perp" => [0x022A5],
  "pertenk" => [0x02031],
  "pfr" => [0x1D52D],
  "phi" => [0x003C6],
  "phiv" => [0x003D5],
  "phmmat" => [0x02133],
  "phone" => [0x0260E],
  "pi" => [0x003C0],
  "pitchfork" => [0x022D4],
  "piv" => [0x003D6],
  "planck" => [0x0210F],
  "planckh" => [0x0210E],
  "plankv" => [0x0210F],
  "plus" => [0x0002B],
  "plusacir" => [0x02A23],
  "plusb" => [0x0229E],
  "pluscir" => [0x02A22],
  "plusdo" => [0x02214],
  "plusdu" => [0x02A25],
  "pluse" => [0x02A72],
  "plusmn" => [0x000B1],
  "plussim" => [0x02A26],
  "plustwo" => [0x02A27],
  "pm" => [0x000B1],
  "pointint" => [0x02A15],
  "popf" => [0x1D561],
  "pound" => [0x000A3],
  "pr" => [0x0227A],
  "prE" => [0x02AB3],
  "prap" => [0x02AB7],
  "prcue" => [0x0227C],
  "pre" => [0x02AAF],
  "prec" => [0x0227A],
  "precapprox" => [0x02AB7],
  "preccurlyeq" => [0x0227C],
  "preceq" => [0x02AAF],
  "precnapprox" => [0x02AB9],
  "precneqq" => [0x02AB5],
  "precnsim" => [0x022E8],
  "precsim" => [0x0227E],
  "prime" => [0x02032],
  "primes" => [0x02119],
  "prnE" => [0x02AB5],
  "prnap" => [0x02AB9],
  "prnsim" => [0x022E8],
  "prod" => [0x0220F],
  "profalar" => [0x0232E],
  "profline" => [0x02312],
  "profsurf" => [0x02313],
  "prop" => [0x0221D],
  "propto" => [0x0221D],
  "prsim" => [0x0227E],
  "prurel" => [0x022B0],
  "pscr" => [0x1D4C5],
  "psi" => [0x003C8],
  "puncsp" => [0x02008],
  "qfr" => [0x1D52E],
  "qint" => [0x02A0C],
  "qopf" => [0x1D562],
  "qprime" => [0x02057],
  "qscr" => [0x1D4C6],
  "quaternions" => [0x0210D],
  "quatint" => [0x02A16],
  "quest" => [0x0003F],
  "questeq" => [0x0225F],
  "quot" => [0x00022],
  "rAarr" => [0x021DB],
  "rArr" => [0x021D2],
  "rAtail" => [0x0291C],
  "rBarr" => [0x0290F],
  "rHar" => [0x02964],
  "race" => [0x0223D, 0x00331],
  "racute" => [0x00155],
  "radic" => [0x0221A],
  "raemptyv" => [0x029B3],
  "rang" => [0x027E9],
  "rangd" => [0x02992],
  "range" => [0x029A5],
  "rangle" => [0x027E9],
  "raquo" => [0x000BB],
  "rarr" => [0x02192],
  "rarrap" => [0x02975],
  "rarrb" => [0x021E5],
  "rarrbfs" => [0x02920],
  "rarrc" => [0x02933],
  "rarrfs" => [0x0291E],
  "rarrhk" => [0x021AA],
  "rarrlp" => [0x021AC],
  "rarrpl" => [0x02945],
  "rarrsim" => [0x02974],
  "rarrtl" => [0x021A3],
  "rarrw" => [0x0219D],
  "ratail" => [0x0291A],
  "ratio" => [0x02236],
  "rationals" => [0x0211A],
  "rbarr" => [0x0290D],
  "rbbrk" => [0x02773],
  "rbrace" => [0x0007D],
  "rbrack" => [0x0005D],
  "rbrke" => [0x0298C],
  "rbrksld" => [0x0298E],
  "rbrkslu" => [0x02990],
  "rcaron" => [0x00159],
  "rcedil" => [0x00157],
  "rceil" => [0x02309],
  "rcub" => [0x0007D],
  "rcy" => [0x00440],
  "rdca" => [0x02937],
  "rdldhar" => [0x02969],
  "rdquo" => [0x0201D],
  "rdquor" => [0x0201D],
  "rdsh" => [0x021B3],
  "real" => [0x0211C],
  "realine" => [0x0211B],
  "realpart" => [0x0211C],
  "reals" => [0x0211D],
  "rect" => [0x025AD],
  "reg" => [0x000AE],
  "rfisht" => [0x0297D],
  "rfloor" => [0x0230B],
  "rfr" => [0x1D52F],
  "rhard" => [0x021C1],
  "rharu" => [0x021C0],
  "rharul" => [0x0296C],
  "rho" => [0x003C1],
  "rhov" => [0x003F1],
  "rightarrow" => [0x02192],
  "rightarrowtail" => [0x021A3],
  "rightharpoondown" => [0x021C1],
  "rightharpoonup" => [0x021C0],
  "rightleftarrows" => [0x021C4],
  "rightleftharpoons" => [0x021CC],
  "rightrightarrows" => [0x021C9],
  "rightsquigarrow" => [0x0219D],
  "rightthreetimes" => [0x022CC],
  "ring" => [0x002DA],
  "risingdotseq" => [0x02253],
  "rlarr" => [0x021C4],
  "rlhar" => [0x021CC],
  "rlm" => [0x0200F],
  "rmoust" => [0x023B1],
  "rmoustache" => [0x023B1],
  "rnmid" => [0x02AEE],
  "roang" => [0x027ED],
  "roarr" => [0x021FE],
  "robrk" => [0x027E7],
  "ropar" => [0x02986],
  "ropf" => [0x1D563],
  "roplus" => [0x02A2E],
  "rotimes" => [0x02A35],
  "rpar" => [0x00029],
  "rpargt" => [0x02994],
  "rppolint" => [0x02A12],
  "rrarr" => [0x021C9],
  "rsaquo" => [0x0203A],
  "rscr" => [0x1D4C7],
  "rsh" => [0x021B1],
  "rsqb" => [0x0005D],
  "rsquo" => [0x02019],
  "rsquor" => [0x02019],
  "rthree" => [0x022CC],
  "rtimes" => [0x022CA],
  "rtri" => [0x025B9],
  "rtrie" => [0x022B5],
  "rtrif" => [0x025B8],
  "rtriltri" => [0x029CE],
  "ruluhar" => [0x02968],
  "rx" => [0x0211E],
  "sacute" => [0x0015B],
  "sbquo" => [0x0201A],
  "sc" => [0x0227B],
  "scE" => [0x02AB4],
  "scap" => [0x02AB8],
  "scaron" => [0x00161],
  "sccue" => [0x0227D],
  "sce" => [0x02AB0],
  "scedil" => [0x0015F],
  "scirc" => [0x0015D],
  "scnE" => [0x02AB6],
  "scnap" => [0x02ABA],
  "scnsim" => [0x022E9],
  "scpolint" => [0x02A13],
  "scsim" => [0x0227F],
  "scy" => [0x00441],
  "sdot" => [0x022C5],
  "sdotb" => [0x022A1],
  "sdote" => [0x02A66],
  "seArr" => [0x021D8],
  "searhk" => [0x02925],
  "searr" => [0x02198],
  "searrow" => [0x02198],
  "sect" => [0x000A7],
  "semi" => [0x0003B],
  "seswar" => [0x02929],
  "setminus" => [0x02216],
  "setmn" => [0x02216],
  "sext" => [0x02736],
  "sfr" => [0x1D530],
  "sfrown" => [0x02322],
  "sharp" => [0x0266F],
  "shchcy" => [0x00449],
  "shcy" => [0x00448],
  "shortmid" => [0x02223],
  "shortparallel" => [0x02225],
  "shy" => [0x000AD],
  "sigma" => [0x003C3],
  "sigmaf" => [0x003C2],
  "sigmav" => [0x003C2],
  "sim" => [0x0223C],
  "simdot" => [0x02A6A],
  "sime" => [0x02243],
  "simeq" => [0x02243],
  "simg" => [0x02A9E],
  "simgE" => [0x02AA0],
  "siml" => [0x02A9D],
  "simlE" => [0x02A9F],
  "simne" => [0x02246],
  "simplus" => [0x02A24],
  "simrarr" => [0x02972],
  "slarr" => [0x02190],
  "smallsetminus" => [0x02216],
  "smashp" => [0x02A33],
  "smeparsl" => [0x029E4],
  "smid" => [0x02223],
  "smile" => [0x02323],
  "smt" => [0x02AAA],
  "smte" => [0x02AAC],
  "smtes" => [0x02AAC, 0x0FE00],
  "softcy" => [0x0044C],
  "sol" => [0x0002F],
  "solb" => [0x029C4],
  "solbar" => [0x0233F],
  "sopf" => [0x1D564],
  "spades" => [0x02660],
  "spadesuit" => [0x02660],
  "spar" => [0x02225],
  "sqcap" => [0x02293],
  "sqcaps" => [0x02293, 0x0FE00],
  "sqcup" => [0x02294],
  "sqcups" => [0x02294, 0x0FE00],
  "sqsub" => [0x0228F],
  "sqsube" => [0x02291],
  "sqsubset" => [0x0228F],
  "sqsubseteq" => [0x02291],
  "sqsup" => [0x02290],
  "sqsupe" => [0x02292],
  "sqsupset" => [0x02290],
  "sqsupseteq" => [0x02292],
  "squ" => [0x025A1],
  "square" => [0x025A1],
  "squarf" => [0x025AA],
  "squf" => [0x025AA],
  "srarr" => [0x02192],
  "sscr" => [0x1D4C8],
  "ssetmn" => [0x02216],
  "ssmile" => [0x02323],
  "sstarf" => [0x022C6],
  "star" => [0x02606],
  "starf" => [0x02605],
  "straightepsilon" => [0x003F5],
  "straightphi" => [0x003D5],
  "strns" => [0x000AF],
  "sub" => [0x02282],
  "subE" => [0x02AC5],
  "subdot" => [0x02ABD],
  "sube" => [0x02286],
  "subedot" => [0x02AC3],
  "submult" => [0x02AC1],
  "subnE" => [0x02ACB],
  "subne" => [0x0228A],
  "subplus" => [0x02ABF],
  "subrarr" => [0x02979],
  "subset" => [0x02282],
  "subseteq" => [0x02286],
  "subseteqq" => [0x02AC5],
  "subsetneq" => [0x0228A],
  "subsetneqq" => [0x02ACB],
  "subsim" => [0x02AC7],
  "subsub" => [0x02AD5],
  "subsup" => [0x02AD3],
  "succ" => [0x0227B],
  "succapprox" => [0x02AB8],
  "succcurlyeq" => [0x0227D],
  "succeq" => [0x02AB0],
  "succnapprox" => [0x02ABA],
  "succneqq" => [0x02AB6],
  "succnsim" => [0x022E9],
  "succsim" => [0x0227F],
  "sum" => [0x02211],
  "sung" => [0x0266A],
  "sup" => [0x02283],
  "sup1" => [0x000B9],
  "sup2" => [0x000B2],
  "sup3" => [0x000B3],
  "supE" => [0x02AC6],
  "supdot" => [0x02ABE],
  "supdsub" => [0x02AD8],
  "supe" => [0x02287],
  "supedot" => [0x02AC4],
  "suphsol" => [0x027C9],
  "suphsub" => [0x02AD7],
  "suplarr" => [0x0297B],
  "supmult" => [0x02AC2],
  "supnE" => [0x02ACC],
  "supne" => [0x0228B],
  "supplus" => [0x02AC0],
  "supset" => [0x02283],
  "supseteq" => [0x02287],
  "supseteqq" => [0x02AC6],
  "supsetneq" => [0x0228B],
  "supsetneqq" => [0x02ACC],
  "supsim" => [0x02AC8],
  "supsub" => [0x02AD4],
  "supsup" => [0x02AD6],
  "swArr" => [0x021D9],
  "swarhk" => [0x02926],
  "swarr" => [0x02199],
  "swarrow" => [0x02199],
  "swnwar" => [0x0292A],
  "szlig" => [0x000DF],
  "target" => [0x02316],
  "tau" => [0x003C4],
  "tbrk" => [0x023B4],
  "tcaron" => [0x00165],
  "tcedil" => [0x00163],
  "tcy" => [0x00442],
  "tdot" => [0x020DB],
  "telrec" => [0x02315],
  "tfr" => [0x1D531],
  "there4" => [0x02234],
  "therefore" => [0x02234],
  "theta" => [0x003B8],
  "thetasym" => [0x003D1],
  "thetav" => [0x003D1],
  "thickapprox" => [0x02248],
  "thicksim" => [0x0223C],
  "thinsp" => [0x02009],
  "thkap" => [0x02248],
  "thksim" => [0x0223C],
  "thorn" => [0x000FE],
  "tilde" => [0x002DC],
  "times" => [0x000D7],
  "timesb" => [0x022A0],
  "timesbar" => [0x02A31],
  "timesd" => [0x02A30],
  "tint" => [0x0222D],
  "toea" => [0x02928],
  "top" => [0x022A4],
  "topbot" => [0x02336],
  "topcir" => [0x02AF1],
  "topf" => [0x1D565],
  "topfork" => [0x02ADA],
  "tosa" => [0x02929],
  "tprime" => [0x02034],
  "trade" => [0x02122],
  "triangle" => [0x025B5],
  "triangledown" => [0x025BF],
  "triangleleft" => [0x025C3],
  "trianglelefteq" => [0x022B4],
  "triangleq" => [0x0225C],
  "triangleright" => [0x025B9],
  "trianglerighteq" => [0x022B5],
  "tridot" => [0x025EC],
  "trie" => [0x0225C],
  "triminus" => [0x02A3A],
  "triplus" => [0x02A39],
  "trisb" => [0x029CD],
  "tritime" => [0x02A3B],
  "trpezium" => [0x023E2],
  "tscr" => [0x1D4C9],
  "tscy" => [0x00446],
  "tshcy" => [0x0045B],
  "tstrok" => [0x00167],
  "twixt" => [0x0226C],
  "twoheadleftarrow" => [0x0219E],
  "twoheadrightarrow" => [0x021A0],
  "uArr" => [0x021D1],
  "uHar" => [0x02963],
  "uacute" => [0x000FA],
  "uarr" => [0x02191],
  "ubrcy" => [0x0045E],
  "ubreve" => [0x0016D],
  "ucirc" => [0x000FB],
  "ucy" => [0x00443],
  "udarr" => [0x021C5],
  "udblac" => [0x00171],
  "udhar" => [0x0296E],
  "ufisht" => [0x0297E],
  "ufr" => [0x1D532],
  "ugrave" => [0x000F9],
  "uharl" => [0x021BF],
  "uharr" => [0x021BE],
  "uhblk" => [0x02580],
  "ulcorn" => [0x0231C],
  "ulcorner" => [0x0231C],
  "ulcrop" => [0x0230F],
  "ultri" => [0x025F8],
  "umacr" => [0x0016B],
  "uml" => [0x000A8],
  "uogon" => [0x00173],
  "uopf" => [0x1D566],
  "uparrow" => [0x02191],
  "updownarrow" => [0x02195],
  "upharpoonleft" => [0x021BF],
  "upharpoonright" => [0x021BE],
  "uplus" => [0x0228E],
  "upsi" => [0x003C5],
  "upsih" => [0x003D2],
  "upsilon" => [0x003C5],
  "upuparrows" => [0x021C8],
  "urcorn" => [0x0231D],
  "urcorner" => [0x0231D],
  "urcrop" => [0x0230E],
  "uring" => [0x0016F],
  "urtri" => [0x025F9],
  "uscr" => [0x1D4CA],
  "utdot" => [0x022F0],
  "utilde" => [0x00169],
  "utri" => [0x025B5],
  "utrif" => [0x025B4],
  "uuarr" => [0x021C8],
  "uuml" => [0x000FC],
  "uwangle" => [0x029A7],
  "vArr" => [0x021D5],
  "vBar" => [0x02AE8],
  "vBarv" => [0x02AE9],
  "vDash" => [0x022A8],
  "vangrt" => [0x0299C],
  "varepsilon" => [0x003F5],
  "varkappa" => [0x003F0],
  "varnothing" => [0x02205],
  "varphi" => [0x003D5],
  "varpi" => [0x003D6],
  "varpropto" => [0x0221D],
  "varr" => [0x02195],
  "varrho" => [0x003F1],
  "varsigma" => [0x003C2],
  "varsubsetneq" => [0x0228A, 0x0FE00],
  "varsubsetneqq" => [0x02ACB, 0x0FE00],
  "varsupsetneq" => [0x0228B, 0x0FE00],
  "varsupsetneqq" => [0x02ACC, 0x0FE00],
  "vartheta" => [0x003D1],
  "vartriangleleft" => [0x022B2],
  "vartriangleright" => [0x022B3],
  "vcy" => [0x00432],
  "vdash" => [0x022A2],
  "vee" => [0x02228],
  "veebar" => [0x022BB],
  "veeeq" => [0x0225A],
  "vellip" => [0x022EE],
  "verbar" => [0x0007C],
  "vert" => [0x0007C],
  "vfr" => [0x1D533],
  "vltri" => [0x022B2],
  "vnsub" => [0x02282, 0x020D2],
  "vnsup" => [0x02283, 0x020D2],
  "vopf" => [0x1D567],
  "vprop" => [0x0221D],
  "vrtri" => [0x022B3],
  "vscr" => [0x1D4CB],
  "vsubnE" => [0x02ACB, 0x0FE00],
  "vsubne" => [0x0228A, 0x0FE00],
  "vsupnE" => [0x02ACC, 0x0FE00],
  "vsupne" => [0x0228B, 0x0FE00],
  "vzigzag" => [0x0299A],
  "wcirc" => [0x00175],
  "wedbar" => [0x02A5F],
  "wedge" => [0x02227],
  "wedgeq" => [0x02259],
  "weierp" => [0x02118],
  "wfr" => [0x1D534],
  "wopf" => [0x1D568],
  "wp" => [0x02118],
  "wr" => [0x02240],
  "wreath" => [0x02240],
  "wscr" => [0x1D4CC],
  "xcap" => [0x022C2],
  "xcirc" => [0x025EF],
  "xcup" => [0x022C3],
  "xdtri" => [0x025BD],
  "xfr" => [0x1D535],
  "xhArr" => [0x027FA],
  "xharr" => [0x027F7],
  "xi" => [0x003BE],
  "xlArr" => [0x027F8],
  "xlarr" => [0x027F5],
  "xmap" => [0x027FC],
  "xnis" => [0x022FB],
  "xodot" => [0x02A00],
  "xopf" => [0x1D569],
  "xoplus" => [0x02A01],
  "xotime" => [0x02A02],
  "xrArr" => [0x027F9],
  "xrarr" => [0x027F6],
  "xscr" => [0x1D4CD],
  "xsqcup" => [0x02A06],
  "xuplus" => [0x02A04],
  "xutri" => [0x025B3],
  "xvee" => [0x022C1],
  "xwedge" => [0x022C0],
  "yacute" => [0x000FD],
  "yacy" => [0x0044F],
  "ycirc" => [0x00177],
  "ycy" => [0x0044B],
  "yen" => [0x000A5],
  "yfr" => [0x1D536],
  "yicy" => [0x00457],
  "yopf" => [0x1D56A],
  "yscr" => [0x1D4CE],
  "yucy" => [0x0044E],
  "yuml" => [0x000FF],
  "zacute" => [0x0017A],
  "zcaron" => [0x0017E],
  "zcy" => [0x00437],
  "zdot" => [0x0017C],
  "zeetrf" => [0x02128],
  "zeta" => [0x003B6],
  "zfr" => [0x1D537],
  "zhcy" => [0x00436],
  "zigrarr" => [0x021DD],
  "zopf" => [0x1D56B],
  "zscr" => [0x1D4CF],
  "zwj" => [0x0200D],
  "zwnj" => [0x0200C],
}
