class InlineParser

  preclow
    nonassoc EX_LOW
    left QUOTE
         BAR
         SLASH
         BACK_SLASH
         URL
         OTHER
         REF_OPEN
         FOOTNOTE_OPEN
         FOOTNOTE_CLOSE
    nonassoc EX_HIGH
  prechigh

  token EM_OPEN
        EM_CLOSE
        CODE_OPEN
        CODE_CLOSE
        VAR_OPEN
        VAR_CLOSE
        KBD_OPEN
        KBD_CLOSE
        INDEX_OPEN
        INDEX_CLOSE
        REF_OPEN
        REF_CLOSE
        FOOTNOTE_OPEN
        FOOTNOTE_CLOSE
        VERB_OPEN
        VERB_CLOSE
        BAR
        QUOTE
        SLASH
        BACK_SLASH
        URL
        OTHER
        EX_LOW
        EX_HIGH

  rule
    content : elements
            ;

    elements : elements element { result.append val[1] }
             | element          { result = val[0] }
             ;

    element : emphasis
            | code
            | var
            | keyboard
            | index
            | reference
            | footnote
            | verb
            | normal_str_ele
            ;

    emphasis : EM_OPEN content EM_CLOSE {
      content = val[1]
      result = inline "<em>#{content}</em>", content
    }
    ;

    code : CODE_OPEN content CODE_CLOSE {
      content = val[1]
      result = inline "<code>#{content}</code>", content
    }
    ;

    var : VAR_OPEN content VAR_CLOSE {
      content = val[1]
      result = inline "+#{content}+", content
    }
    ;

    keyboard : KBD_OPEN content KBD_CLOSE {
      content = val[1]
      result = inline "<tt>#{content}</tt>", content
    }
    ;

    index : INDEX_OPEN content INDEX_CLOSE {
      label = val[1]
      @block_parser.add_label label.reference
      result = "<span id=\"label-#{label}\">#{label}</span>"
    }
    ;

# Reference
# ((<subst|filename/element_label>))

    reference : REF_OPEN substitute ref_label REF_CLOSE {
      result = "{#{val[1]}}[#{val[2].join}]"
    } | REF_OPEN ref_label2 REF_CLOSE {
      scheme, inline = val[1]

      result = "{#{inline}}[#{scheme}#{inline.reference}]"
    }
    ;

    # result is scheme and reference
    ref_label : URL ref_url_strings {
      result = [nil, inline(val[1])]
    } | filename element_label {
      result = [
        'rdoc-label:',
        inline("#{val[0].reference}/#{val[1].reference}")
      ]
    } | element_label {
      result = ['rdoc-label:', val[0].reference]
    } | filename {
      result = ['rdoc-label:', "#{val[0].reference}/"]
    }
    ;

    # result is reference type and value
    ref_label2 : URL ref_url_strings {
      result = [nil, inline(val[1])]
    } | filename element_label2 {
      result = [
        'rdoc-label:',
        inline("#{val[0].reference}/#{val[1].reference}")
      ]
    } | element_label2 {
      result = ['rdoc-label:', val[0]]
    } | filename {
      ref = val[0].reference
      result = ['rdoc-label:', inline(ref, "#{ref}/")]
    }
    ;

    substitute : ref_subst_content BAR
               | QUOTE ref_subst_content_q QUOTE BAR { result = val[1] }
               | QUOTE ref_subst_strings_q QUOTE BAR { result = val[1] }
               ;

    filename : ref_subst_strings_first SLASH {
      result = inline val[0]
    } | QUOTE ref_subst_strings_q QUOTE SLASH {
      result = inline "\"#{val[1]}\""
    }
    ;

    # when substitute part exists
    element_label : ref_subst_strings_first {
      result = inline val[0]
    } | QUOTE ref_subst_strings_q QUOTE {
      result = inline "\"#{val[1]}\""
    }
    ;

    # when substitute part doesn't exist
    # in this case, element label can contain Inlines
    element_label2 : ref_subst_content
                   | QUOTE ref_subst_content_q QUOTE { result = val[1] }
                   | QUOTE ref_subst_strings_q QUOTE { result = inline val[1] }
                   ;

    ref_subst_content : ref_subst_ele2 ref_subst_eles {
      result = val[0].append val[1]
    } | ref_subst_str_ele_first ref_subst_eles {
      result = val[0].append val[1]
    } | ref_subst_str_ele_first {
      result = val[0]
    } | ref_subst_ele2 {
      result = inline val[0]
    }
    ;

    ref_subst_content_q : ref_subst_eles_q
                        ;

    ref_subst_eles : ref_subst_eles ref_subst_ele {
      result = val[0].append val[1]
    } | ref_subst_ele {
      result = inline val[0]
    }
    ;

    ref_subst_eles_q : ref_subst_eles_q ref_subst_ele_q {
      result = val[0].append val[1]
    } | ref_subst_ele_q {
      result = val[0]
    }
    ;

    ref_subst_ele2 : emphasis
                   | code
                   | var
                   | keyboard
                   | index
                   | verb
                   ;

    ref_subst_ele : ref_subst_ele2
                  | ref_subst_str_ele
                  ;

    ref_subst_ele_q : ref_subst_ele2
                    | ref_subst_str_ele_q
                    ;

    ref_subst_str_ele : ref_subst_strings = EX_LOW {
      result = val[0]
    }
    ;

    ref_subst_str_ele_first : ref_subst_strings_first {
      result = inline val[0]
    }
    ;

    ref_subst_str_ele_q : ref_subst_strings_q = EX_LOW {
      result = inline val[0]
    }
    ;

    ref_subst_strings : ref_subst_strings ref_subst_string3 { result << val[1] }
                      | ref_subst_string3
                      ;

    # if it is first element of substitute, it can't contain URL on head.
    ref_subst_strings_first : ref_subst_string ref_subst_strings = EX_HIGH {
      result << val[1]
    } | ref_subst_string = EX_LOW
    ;

    ref_subst_strings_q : ref_subst_strings_q ref_subst_string_q {
      result << val[1]
    } | ref_subst_string_q
    ;

    ref_subst_string : OTHER
                     | BACK_SLASH
                     | REF_OPEN
                     | FOOTNOTE_OPEN
                     | FOOTNOTE_CLOSE
                     ;

    ref_subst_string2 : ref_subst_string
                      | URL
                      ;

    ref_subst_string3 : ref_subst_string2
                      | QUOTE
                      ;

    ref_subst_string_q : ref_subst_string2
                       | BAR
                       | SLASH
                       ;

# end subst 

# string in url
    ref_url_strings : ref_url_strings ref_url_string { result << val[1] }
                    | ref_url_string
                    ;

    ref_url_string : OTHER
                   | BACK_SLASH BACK_SLASH
                   | URL
                   | SLASH
                   | BAR
                   | QUOTE
                   | EM_OPEN
                   | EM_CLOSE
                   | CODE_OPEN
                   | CODE_CLOSE
                   | VAR_OPEN
                   | VAR_CLOSE
                   | KBD_OPEN
                   | KBD_CLOSE
                   | INDEX_OPEN
                   | INDEX_CLOSE
                   | REF_OPEN
                   | FOOTNOTE_OPEN
                   | FOOTNOTE_CLOSE
                   | VERB_OPEN
                   | VERB_CLOSE
                   ;

# end url
# end Reference

    footnote : FOOTNOTE_OPEN content FOOTNOTE_CLOSE {
      index = @block_parser.add_footnote val[1].rdoc
      result = "{*#{index}}[rdoc-label:foottext-#{index}:footmark-#{index}]"
    }
    ;

    verb : VERB_OPEN verb_strings VERB_CLOSE {
      result = inline "<tt>#{val[1]}</tt>", val[1]
    }
    ;

    # normal string
    # OTHER, QUOTE, BAR, SLASH, BACK_SLASH, URL
    normal_string : OTHER
                  | QUOTE
                  | BAR
                  | SLASH
                  | BACK_SLASH
                  | URL
                  ;

    normal_strings : normal_strings normal_string { result << val[1] }
                   | normal_string
                   ;

    normal_str_ele : normal_strings = EX_LOW {
      result = inline val[0]
    }
    ;

    # in verb
    verb_string : verb_normal_string
                | BACK_SLASH verb_normal_string { result = val[1] }
                | BACK_SLASH VERB_CLOSE         { result = val[1] }
                | BACK_SLASH BACK_SLASH         { result = val[1] }
                ;

    verb_normal_string : OTHER
                       | QUOTE
                       | BAR
                       | SLASH
                       | EM_OPEN
                       | EM_CLOSE
                       | CODE_OPEN
                       | CODE_CLOSE
                       | VAR_OPEN
                       | VAR_CLOSE
                       | KBD_OPEN 
                       | KBD_CLOSE
                       | INDEX_OPEN
                       | INDEX_CLOSE
                       | REF_OPEN 
                       | REF_CLOSE
                       | FOOTNOTE_OPEN 
                       | FOOTNOTE_CLOSE 
                       | VERB_OPEN
                       | URL
                       ;

    verb_strings : verb_strings verb_string { result << val[1] }
                 | verb_string
                 ; 

/*     verb_str_ele : verb_strings
 *     ;
 */
end

---- inner

# :stopdoc:

EM_OPEN = '((*'
EM_OPEN_RE = /\A#{Regexp.quote(EM_OPEN)}/
EM_CLOSE = '*))'
EM_CLOSE_RE = /\A#{Regexp.quote(EM_CLOSE)}/
CODE_OPEN = '(({'
CODE_OPEN_RE = /\A#{Regexp.quote(CODE_OPEN)}/
CODE_CLOSE = '}))'
CODE_CLOSE_RE = /\A#{Regexp.quote(CODE_CLOSE)}/
VAR_OPEN = '((|'
VAR_OPEN_RE = /\A#{Regexp.quote(VAR_OPEN)}/
VAR_CLOSE = '|))'
VAR_CLOSE_RE = /\A#{Regexp.quote(VAR_CLOSE)}/
KBD_OPEN = '((%'
KBD_OPEN_RE = /\A#{Regexp.quote(KBD_OPEN)}/
KBD_CLOSE = '%))'
KBD_CLOSE_RE = /\A#{Regexp.quote(KBD_CLOSE)}/
INDEX_OPEN = '((:'
INDEX_OPEN_RE = /\A#{Regexp.quote(INDEX_OPEN)}/
INDEX_CLOSE = ':))'
INDEX_CLOSE_RE = /\A#{Regexp.quote(INDEX_CLOSE)}/
REF_OPEN = '((<'
REF_OPEN_RE = /\A#{Regexp.quote(REF_OPEN)}/
REF_CLOSE = '>))'
REF_CLOSE_RE = /\A#{Regexp.quote(REF_CLOSE)}/
FOOTNOTE_OPEN = '((-'
FOOTNOTE_OPEN_RE = /\A#{Regexp.quote(FOOTNOTE_OPEN)}/
FOOTNOTE_CLOSE = '-))'
FOOTNOTE_CLOSE_RE = /\A#{Regexp.quote(FOOTNOTE_CLOSE)}/
VERB_OPEN = "(('"
VERB_OPEN_RE = /\A#{Regexp.quote(VERB_OPEN)}/
VERB_CLOSE = "'))"
VERB_CLOSE_RE = /\A#{Regexp.quote(VERB_CLOSE)}/

BAR = "|"
BAR_RE = /\A#{Regexp.quote(BAR)}/
QUOTE = '"'
QUOTE_RE = /\A#{Regexp.quote(QUOTE)}/
SLASH = "/"
SLASH_RE = /\A#{Regexp.quote(SLASH)}/
BACK_SLASH = "\\"
BACK_SLASH_RE = /\A#{Regexp.quote(BACK_SLASH)}/
URL = "URL:"
URL_RE = /\A#{Regexp.quote(URL)}/

other_re_mode = Regexp::EXTENDED
other_re_mode |= Regexp::MULTILINE

OTHER_RE = Regexp.new(
  "\\A.+?(?=#{Regexp.quote(EM_OPEN)}|#{Regexp.quote(EM_CLOSE)}|
              #{Regexp.quote(CODE_OPEN)}|#{Regexp.quote(CODE_CLOSE)}|
              #{Regexp.quote(VAR_OPEN)}|#{Regexp.quote(VAR_CLOSE)}|
              #{Regexp.quote(KBD_OPEN)}|#{Regexp.quote(KBD_CLOSE)}|
              #{Regexp.quote(INDEX_OPEN)}|#{Regexp.quote(INDEX_CLOSE)}|
              #{Regexp.quote(REF_OPEN)}|#{Regexp.quote(REF_CLOSE)}|
            #{Regexp.quote(FOOTNOTE_OPEN)}|#{Regexp.quote(FOOTNOTE_CLOSE)}|
              #{Regexp.quote(VERB_OPEN)}|#{Regexp.quote(VERB_CLOSE)}|
              #{Regexp.quote(BAR)}|
              #{Regexp.quote(QUOTE)}|
              #{Regexp.quote(SLASH)}|
              #{Regexp.quote(BACK_SLASH)}|
              #{Regexp.quote(URL)})", other_re_mode)

# :startdoc:

##
# Creates a new parser for inline markup in the rd format.  The +block_parser+
# is used to for footnotes and labels in the inline text.

def initialize block_parser
  @block_parser = block_parser
end

##
# Parses the +inline+ text from RD format into RDoc format.

def parse inline
  @inline = inline
  @src = StringScanner.new inline
  @pre = "".dup
  @yydebug = true
  do_parse.to_s
end

##
# Returns the next token from the inline text

def next_token
  return [false, false] if @src.eos?
#  p @src.rest if @yydebug
  if ret = @src.scan(EM_OPEN_RE)
    @pre << ret
    [:EM_OPEN, ret]
  elsif ret = @src.scan(EM_CLOSE_RE)
    @pre << ret
    [:EM_CLOSE, ret]
  elsif ret = @src.scan(CODE_OPEN_RE)
    @pre << ret
    [:CODE_OPEN, ret]
  elsif ret = @src.scan(CODE_CLOSE_RE)
    @pre << ret
    [:CODE_CLOSE, ret]
  elsif ret = @src.scan(VAR_OPEN_RE)
    @pre << ret
    [:VAR_OPEN, ret]
  elsif ret = @src.scan(VAR_CLOSE_RE)
    @pre << ret
    [:VAR_CLOSE, ret]
  elsif ret = @src.scan(KBD_OPEN_RE)
    @pre << ret
    [:KBD_OPEN, ret]
  elsif ret = @src.scan(KBD_CLOSE_RE)
    @pre << ret
    [:KBD_CLOSE, ret]
  elsif ret = @src.scan(INDEX_OPEN_RE)
    @pre << ret
    [:INDEX_OPEN, ret]
  elsif ret = @src.scan(INDEX_CLOSE_RE)
    @pre << ret
    [:INDEX_CLOSE, ret]
  elsif ret = @src.scan(REF_OPEN_RE)
    @pre << ret
    [:REF_OPEN, ret]
  elsif ret = @src.scan(REF_CLOSE_RE)
    @pre << ret
    [:REF_CLOSE, ret]
  elsif ret = @src.scan(FOOTNOTE_OPEN_RE)
    @pre << ret
    [:FOOTNOTE_OPEN, ret]
  elsif ret = @src.scan(FOOTNOTE_CLOSE_RE)
    @pre << ret
    [:FOOTNOTE_CLOSE, ret]
  elsif ret = @src.scan(VERB_OPEN_RE)
    @pre << ret
    [:VERB_OPEN, ret]
  elsif ret = @src.scan(VERB_CLOSE_RE)
    @pre << ret
    [:VERB_CLOSE, ret]
  elsif ret = @src.scan(BAR_RE)
    @pre << ret
    [:BAR, ret]
  elsif ret = @src.scan(QUOTE_RE)
    @pre << ret
    [:QUOTE, ret]
  elsif ret = @src.scan(SLASH_RE)
    @pre << ret
    [:SLASH, ret]
  elsif ret = @src.scan(BACK_SLASH_RE)
    @pre << ret
    [:BACK_SLASH, ret]
  elsif ret = @src.scan(URL_RE)
    @pre << ret
    [:URL, ret]
  elsif ret = @src.scan(OTHER_RE)
    @pre << ret
    [:OTHER, ret]
  else
    ret = @src.rest
    @pre << ret
    @src.terminate
    [:OTHER, ret]
  end
end

##
# Raises a ParseError when invalid formatting is found

def on_error(et, ev, values)
  lines_of_rest = @src.rest.lines.to_a.length
  prev_words = prev_words_on_error(ev)
  at = 4 + prev_words.length

  message = <<-MSG
RD syntax error: line #{@block_parser.line_index - lines_of_rest}:
...#{prev_words} #{(ev||'')} #{next_words_on_error()} ...
  MSG

  message << " " * at + "^" * (ev ? ev.length : 0) + "\n"
  raise ParseError, message
end

##
# Returns words before the error

def prev_words_on_error(ev)
  pre = @pre
  if ev and /#{Regexp.quote(ev)}$/ =~ pre
    pre = $`
  end
  last_line(pre)
end

##
# Returns the last line of +src+

def last_line(src)
  if n = src.rindex("\n")
    src[(n+1) .. -1]
  else
    src
  end
end
private :last_line

##
# Returns words following an error

def next_words_on_error
  if n = @src.rest.index("\n")
    @src.rest[0 .. (n-1)]
  else
    @src.rest
  end
end

##
# Creates a new RDoc::RD::Inline for the +rdoc+ markup and the raw +reference+

def inline rdoc, reference = rdoc
  RDoc::RD::Inline.new rdoc, reference
end

# :stopdoc:
---- header
require 'strscan'

class RDoc::RD

##
# RD format parser for inline markup such as emphasis, links, footnotes, etc.

---- footer
end

