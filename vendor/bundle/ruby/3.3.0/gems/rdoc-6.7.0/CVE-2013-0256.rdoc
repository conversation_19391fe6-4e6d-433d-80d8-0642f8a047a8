= RDoc 2.3.0 through 3.12 XSS Exploit

RDoc documentation generated by rdoc 2.3.0 through rdoc 3.12 and prereleases up
to rdoc 4.0.0.preview2.1 are vulnerable to an XSS exploit.  This exploit may
lead to cookie disclosure to third parties.

The exploit exists in darkfish.js which is copied from the RDoc install
location to the generated documentation.

RDoc is a static documentation generation tool.  Patching the library itself
is insufficient to correct this exploit.  Those hosting rdoc documentation will
need to apply the following patch.  If applied while ignoring whitespace, this
patch will correct all affected versions:

  diff --git darkfish.js darkfish.js
  index 4be722f..f26fd45 100644
  --- darkfish.js
  +++ darkfish.js
  @@ -109,13 +109,15 @@ function hookSearch() {
   function highlightTarget( anchor ) {
     console.debug( "Highlighting target '%s'.", anchor );
   
  -  $("a[name=" + anchor + "]").each( function() {
  -    if ( !$(this).parent().parent().hasClass('target-section') ) {
  -      console.debug( "Wrapping the target-section" );
  -      $('div.method-detail').unwrap( 'div.target-section' );
  -      $(this).parent().wrap( '<div class="target-section"></div>' );
  -    } else {
  -      console.debug( "Already wrapped." );
  +  $("a[name]").each( function() {
  +    if ( $(this).attr("name") == anchor ) {
  +      if ( !$(this).parent().parent().hasClass('target-section') ) {
  +        console.debug( "Wrapping the target-section" );
  +        $('div.method-detail').unwrap( 'div.target-section' );
  +        $(this).parent().wrap( '<div class="target-section"></div>' );
  +      } else {
  +        console.debug( "Already wrapped." );
  +      }
       }
     });
   };

RDoc 3.9.5, 3.12.1 and RDoc 4.0.0.rc.2 and newer are not vulnerable to this
exploit.

This exploit was discovered by Evgeny Ermakov <<EMAIL>>.

This vulnerability has been assigned the CVE identifier CVE-2013-0256.

