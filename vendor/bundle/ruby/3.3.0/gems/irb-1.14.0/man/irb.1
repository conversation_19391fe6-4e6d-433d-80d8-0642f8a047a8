.\"Ruby is copyrighted by <PERSON><PERSON><PERSON> <<EMAIL>>.
.Dd August 11, 2019
.Dt IRB \&1 "Ruby Programmer's Reference Guide"
.Os UNIX
.Sh NAME
.Nm irb
.Nd Interactive Ruby Shell
.Sh SYNOPSIS
.Nm
.Op Fl -version
.Op Fl dfUw
.Op Fl I Ar directory
.Op Fl r Ar library
.Op Fl E Ar external Ns Op : Ns Ar internal
.Op Fl W Ns Op Ar level
.Op Fl - Ns Oo no Oc Ns inspect
.Op Fl - Ns Oo no Oc Ns multiline
.Op Fl - Ns Oo no Oc Ns singleline
.Op Fl - Ns Oo no Oc Ns echo
.Op Fl - Ns Oo no Oc Ns colorize
.Op Fl - Ns Oo no Oc Ns autocomplete
.Op Fl - Ns Oo no Oc Ns verbose
.Op Fl -prompt Ar mode
.Op Fl -prompt-mode Ar mode
.Op Fl -inf-ruby-mode
.Op Fl -simple-prompt
.Op Fl -noprompt
.Op Fl -tracer
.Op Fl -back-trace-limit Ar n
.Op Fl -
.Op program_file
.Op argument ...
.Pp
.Sh DESCRIPTION
.Nm
is the REPL(read-eval-print loop) environment for Ruby programs.
.Pp
.Sh OPTIONS
.Bl -tag -width "1234567890123" -compact
.Pp
.It Fl -version
Prints the version of
.Nm .
.Pp
.It Fl E Ar external Ns Op : Ns Ar internal
.It Fl -encoding Ar external Ns Op : Ns Ar internal
Same as `ruby -E' .
Specifies the default value(s) for external encodings and internal encoding. Values should be separated with colon (:).
.Pp
You can omit the one for internal encodings, then the value
.Pf ( Li "Encoding.default_internal" ) will be nil.
.Pp
.It Fl I Ar path
Same as `ruby -I' .
Specifies
.Li $LOAD_PATH
directory
.Pp
.It Fl U
Same as `ruby -U' .
Sets the default value for internal encodings
.Pf ( Li "Encoding.default_internal" ) to UTF-8.
.Pp
.It Fl d
Same as `ruby -d' .
Sets
.Li $DEBUG
to true.
.Pp
.It Fl f
Suppresses read of
.Pa ~/.irbrc .
.Pp
.It Fl w
Same as `ruby -w' .
.Pp
.Pp
.It Fl W
Same as `ruby -W' .
.Pp
.It Fl h
.It Fl -help
Prints a summary of the options.
.Pp
.It Fl r Ar library
Same as `ruby -r'.
Causes irb to load the library using require.
.Pp
.It Fl -inspect
Uses `inspect' for output (default except for bc mode)
.Pp
.It Fl -noinspect
Doesn't use inspect for output
.Pp
.It Fl -multiline
Uses multiline editor module.
.Pp
.It Fl -nomultiline
Doesn't use multiline editor module.
.Pp
.It Fl -singleline
Uses singleline editor module.
.Pp
.It Fl -nosingleline
Doesn't use singleline editor module.
.Pp
.Pp
.It Fl -extra-doc-dir
Add an extra doc dir for the doc dialog.
.Pp
.Pp
.It Fl -echo
Show result (default).
.Pp
.It Fl -noecho
Don't show result.
.Pp
.Pp
.It Fl -echo-on-assignment
Show result on assignment.
.Pp
.It Fl -noecho-on-assignment
Don't show result on assignment.
.Pp
.It Fl -truncate-echo-on-assignment
Show truncated result on assignment (default).
.Pp
.Pp
.It Fl -colorize
Use colorization.
.Pp
.It Fl -nocolorize
Don't use colorization.
.Pp
.Pp
.It Fl -autocomplete
Use autocompletion.
.Pp
.It Fl -noautocomplete
Don't use autocompletion.
.Pp
.Pp
.It Fl -regexp-completor
Use regexp based completion.
.Pp
.It Fl -type-completor
Use type based completion.
.Pp
.Pp
.It Fl -verbose
Show details.
.Pp
.It Fl -noverbose
Don't show details.
.Pp
.It Fl -prompt Ar mode
.It Fl -prompt-mode Ar mode
Switch prompt mode. Pre-defined prompt modes are
`default', `simple', `xmp' and `inf-ruby'.
.Pp
.It Fl -inf-ruby-mode
Uses prompt appropriate for inf-ruby-mode on emacs.
Suppresses --multiline and --singleline.
.Pp
.It Fl -simple-prompt
Makes prompts simple.
.Pp
.It Fl -noprompt
No prompt mode.
.Pp
.It Fl -tracer
Displays trace for each execution of commands.
.Pp
.It Fl -back-trace-limit Ar n
Displays backtrace top
.Ar n
and tail
.Ar n Ns .
The default value is 16.
.El
.Pp
.Sh ENVIRONMENT
.Bl -tag -compact -width "XDG_CONFIG_HOME"
.It Ev IRB_LANG
The locale used for
.Nm .
.Pp
.It Ev IRBRC
The path to the personal initialization file.
.Pp
.It Ev XDG_CONFIG_HOME
.Nm
respects XDG_CONFIG_HOME. If this is set, load
.Pa $XDG_CONFIG_HOME/irb/irbrc
as a personal initialization file.
.Pp
.El
.Pp
Also
.Nm
depends on same variables as
.Xr ruby 1 .
.Pp
.Sh FILES
.Bl -tag -compact
.It Pa ~/.irbrc
Personal irb initialization. If
.Ev IRBRC
is set, read
.Pa $IRBRC
instead. If
.Ev IRBRC
is not set and
.Ev XDG_CONFIG_HOME
is set,
.Pa $XDG_CONFIG_HOME/irb/irbrc
is loaded.
.Pp
.El
.Pp
.Sh EXAMPLES
.Dl % irb
.Dl irb(main):001:0> Ic 1 + 1
.Dl 2
.Dl irb(main):002:0> Ic def t(x)
.Dl irb(main):003:1> Ic   x + 1
.Dl irb(main):004:1> Ic end
.Dl => :t
.Dl irb(main):005:0> Ic t(3)
.Dl => 4
.Dl irb(main):006:0> Ic if t(3) == 4
.Dl irb(main):007:1> Ic p :ok
.Dl irb(main):008:1> Ic end
.Dl :ok
.Dl => :ok
.Dl irb(main):009:0> Ic quit
.Dl %
.Pp
.Sh SEE ALSO
.Xr ruby 1 .
.Pp
.Sh REPORTING BUGS
.Bl -bullet
.It
Security vulnerabilities should be reported via an email to
.Mt <EMAIL> .
Reported problems will be published after being fixed.
.Pp
.It
Other bugs and feature requests can be reported via the
Ruby Issue Tracking System
.Pq Lk https://bugs.ruby-lang.org/ .
Do not report security vulnerabilities
via this system because it publishes the vulnerabilities immediately.
.El
.Sh AUTHORS
Written by Keiju ISHITSUKA.
