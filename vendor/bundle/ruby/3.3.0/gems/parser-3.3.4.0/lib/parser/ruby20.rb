# -*- encoding:utf-8; warn-indent:false; frozen_string_literal: true  -*-
#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.7.3
# from Racc grammar file "ruby20.y".
#

require 'racc/parser.rb'


require_relative '../parser'

module Parser
  class Ruby20 < Parser::Base


  def version
    20
  end

  def default_encoding
    Encoding::UTF_8
  end

  def local_push
    @static_env.extend_static
    @lexer.cmdarg.push(false)
    @lexer.cond.push(false)
  end

  def local_pop
    @static_env.unextend
    @lexer.cmdarg.pop
    @lexer.cond.pop
  end
##### State transition tables begin ###

racc_action_table = [
  -476,     5,    74,    75,    71,     9,    57,  -476,  -476,  -476,
    63,    64,  -476,  -476,  -476,    67,  -476,    65,    66,    68,
    30,    31,    72,    73,  -476,   267,  -476,  -476,  -476,    29,
    28,    27,   100,    99,   101,   102,  -476,  -476,    19,  -476,
  -476,  -476,  -476,  -476,     8,    45,     7,    10,   104,   103,
   105,    94,    56,    96,    95,    97,   -98,    98,   106,   107,
   556,    92,    93,    42,    43,    41,  -476,  -476,  -476,  -476,
  -476,  -476,  -476,  -476,  -476,  -476,  -476,  -476,  -476,  -476,
   267,   -99,  -476,  -476,  -476,    40,  -476,  -476,    33,  -106,
  -476,    58,    59,  -476,  -476,    60,  -476,    35,  -476,   267,
  -476,    44,  -476,  -476,   267,  -476,  -476,  -476,  -476,  -476,
    20,  -476,   266,  -476,   216,    91,    81,    84,    85,   577,
    86,    88,    87,    89,  -105,   108,   789,  -476,    82,    90,
  -476,  -476,  -476,  -476,  -479,  -476,    62,  -476,    83,  -476,
  -101,  -479,  -479,  -479,   262,   535,  -479,  -479,  -479,   579,
  -479,   120,   535,  -103,   112,   217,   535,  -286,  -479,   111,
  -479,  -479,  -479,  -100,   614,   -89,   555,   266,   262,  -490,
  -479,  -479,   535,  -479,  -479,  -479,  -479,  -479,   535,  -101,
  -103,  -100,  -102,  -102,   690,   576,   266,   112,   259,   112,
   -90,   266,   111,  -491,   111,   260,   205,  -286,   -97,   613,
  -479,  -479,  -479,  -479,  -479,  -479,  -479,  -479,  -479,  -479,
  -479,  -479,  -479,  -479,   206,   578,  -479,  -479,  -479,  -476,
  -479,  -479,   -98,   -92,  -479,   441,  -476,  -479,  -479,   512,
  -479,   690,  -479,   -96,  -479,   788,  -479,  -479,   745,  -479,
  -479,  -479,  -479,  -479,  -289,  -479,   -99,  -479,  -568,   -92,
   690,  -289,  -289,  -289,   213,   214,   746,  -289,  -289,  -568,
  -289,  -479,   -94,   112,  -479,  -479,  -479,  -479,   111,  -479,
   112,  -479,   -91,  -479,   112,   111,  -104,   820,  -106,   111,
  -289,  -289,  -105,  -289,  -289,  -289,  -289,  -289,   112,   -92,
   112,  -101,   -93,   111,   -94,   111,   112,  -101,  -103,  -100,
  -102,   111,  -101,  -103,  -100,  -102,   207,   213,   214,  -103,
  -289,  -289,  -289,  -289,  -289,  -289,  -289,  -289,  -289,  -289,
  -289,  -289,  -289,  -289,   213,   214,  -289,  -289,  -289,   -92,
   597,   212,   -92,   211,  -289,   112,   838,  -289,   -92,   -84,
   111,  -569,  -289,   614,  -289,   -70,  -289,  -289,   217,  -289,
  -289,  -289,  -289,  -289,   112,  -289,  -572,  -289,   256,   111,
   -94,   213,   214,  -572,  -572,  -572,  -490,   614,  -491,  -572,
  -572,  -289,  -572,   525,  -289,  -289,   524,   -95,   613,  -289,
   -97,  -572,   614,  -104,  -565,   -95,  -106,  -479,   525,  -100,
   314,   527,  -572,  -572,  -479,  -572,  -572,  -572,  -572,  -572,
   -94,   112,   613,   -94,   512,   780,   111,   541,   807,   -94,
   666,   665,   664,   -89,   667,   -90,   564,   613,   217,   -98,
  -566,   -99,  -572,  -572,  -572,  -572,  -572,  -572,  -572,  -572,
  -572,  -572,  -572,  -572,  -572,  -572,  -476,   209,  -572,  -572,
  -572,   564,   598,  -476,   210,   315,  -572,   217,  -565,  -572,
  -572,   -96,  -476,   208,  -572,   217,  -572,  -105,  -572,  -572,
   384,  -572,  -572,  -572,  -572,  -572,   564,  -572,  -572,  -572,
   592,  -565,  -479,   112,   525,   213,   214,   527,   111,  -479,
   566,   565,   562,  -572,  -566,  -572,  -572,  -572,  -479,   -93,
   885,  -572,  -572,  -572,  -572,  -102,  -486,  -572,  -572,  -572,
   547,  -572,  -572,  -486,   548,   566,   565,  -566,   397,  -572,
  -572,  -572,  -572,  -572,  -568,   722,   525,   -92,  -572,   527,
   440,  -572,  -572,  -101,  -572,  -572,  -572,  -572,  -572,   442,
   566,   565,   807,   593,   666,   665,   664,  -572,   667,   564,
   724,  -485,  -487,  -572,   564,   112,   316,   317,  -485,  -487,
   111,  -572,  -572,  -572,  -572,  -572,  -572,  -572,  -572,  -572,
  -572,  -572,  -572,  -572,  -572,   507,   508,  -572,  -572,  -572,
  -484,   747,  -572,   564,   443,  -572,   731,  -484,  -572,  -572,
   -94,  -572,  -414,  -572,   112,  -572,  -103,  -572,  -572,   111,
  -572,  -572,  -572,  -572,  -572,  -572,  -572,  -572,  -572,   564,
   213,   214,  -572,   566,   565,   567,   564,  -568,   566,   565,
   569,  -572,  -572,   216,   981,  -572,  -572,  -572,  -572,  -289,
  -572,   474,  -572,   -91,  -102,   483,  -289,  -289,  -289,  -100,
  -572,  -289,  -289,  -289,  -414,  -289,  -333,   566,   565,   571,
   485,  -414,  -481,  -333,  -482,  -289,  -289,  -289,   394,  -481,
  -414,  -482,  -333,   396,   395,  -289,  -289,   487,  -289,  -289,
  -289,  -289,  -289,   566,   565,   575,   437,   494,  -488,  -414,
   566,   565,   580,   438,   807,  -488,   666,   665,   664,   -69,
   667,   217,   439,   497,  -488,  -289,  -289,  -289,  -289,  -289,
  -289,  -289,  -289,  -289,  -289,  -289,  -289,  -289,  -289,   237,
   673,  -289,  -289,  -289,   498,   748,  -289,  -483,   505,  -289,
   675,   803,  -289,  -289,  -483,  -289,   271,  -289,   217,  -289,
   806,  -289,  -289,   762,  -289,  -289,  -289,  -289,  -289,   262,
  -289,   234,  -289,   675,   259,   236,   235,   112,   232,   233,
   509,   260,   111,   683,   682,   513,  -289,   514,   676,  -289,
  -289,  -289,  -289,   237,  -289,   217,  -289,   528,  -104,   288,
    74,    75,    71,     9,    57,   529,   683,   682,    63,    64,
   487,   676,    81,    67,   541,    65,    66,    68,    30,    31,
    72,    73,   217,   386,    82,   966,   545,    29,    28,    27,
   100,    99,   101,   102,    83,   546,    19,   213,   214,   815,
   780,   602,     8,    45,   290,    10,   104,   103,   105,    94,
    56,    96,    95,    97,   581,    98,   106,   107,   584,    92,
    93,    42,    43,    41,   237,   241,   246,   247,   248,   243,
   245,   253,   254,   249,   250,  -489,   230,   231,   503,  -263,
   251,   252,  -489,    40,   586,   504,   292,   683,   682,    58,
    59,  -489,   217,    60,   502,    35,   234,   590,   240,    44,
   236,   235,   591,   232,   233,   244,   242,   238,    20,   239,
   815,   780,   262,    91,    81,    84,    85,  -279,    86,    88,
    87,    89,   601,   237,  -279,   515,    82,    90,   604,   255,
  -280,  -240,   516,  -279,    62,   237,    83,  -280,  -280,  -280,
   237,   439,  -280,  -280,  -280,   807,  -280,   666,   665,   664,
   237,   667,   237,   217,   217,   234,  -280,  -280,  -280,   236,
   235,   543,   232,   233,   217,   -84,  -280,  -280,   544,  -280,
  -280,  -280,  -280,  -280,   632,   217,   518,   542,   643,   237,
   649,   807,   803,   666,   665,   664,   650,   667,   716,   717,
   652,   806,   718,   106,   107,   686,  -280,  -280,  -280,  -280,
  -280,  -280,  -280,  -280,  -280,  -280,  -280,  -280,  -280,  -280,
   237,   234,  -280,  -280,  -280,   236,   235,  -280,   803,   541,
  -280,   693,   721,  -280,  -280,   725,  -280,   943,  -280,   726,
  -280,  -264,  -280,  -280,   732,  -280,  -280,  -280,  -280,  -280,
   474,  -280,   234,  -280,   474,   217,   236,   235,   551,   750,
   914,   256,   666,   665,   664,   550,   667,  -280,   485,  -290,
  -280,  -280,  -280,  -280,   552,  -280,  -290,  -280,     5,    74,
    75,    71,     9,    57,   487,  -290,  -290,    63,    64,   773,
   643,   217,    67,  -290,    65,    66,    68,    30,    31,    72,
    73,   262,  -290,   262,   643,   237,    29,    28,    27,   100,
    99,   101,   102,   237,   914,    19,   666,   665,   664,   780,
   667,     8,    45,     7,    10,   104,   103,   105,    94,    56,
    96,    95,    97,   217,    98,   106,   107,   791,    92,    93,
    42,    43,    41,   237,   241,   246,   247,   248,   243,   245,
   253,   254,   249,   250,  -289,  -591,  -591,   551,   794,   251,
   252,  -289,    40,   795,   917,    33,  -569,   797,    58,    59,
  -289,   799,    60,   552,    35,   234,   801,   240,    44,   236,
   235,   810,   232,   233,   244,   242,   238,    20,   239,   811,
   812,   780,    91,    81,    84,    85,   819,    86,    88,    87,
    89,   217,   217,   828,  -265,    82,    90,   288,    74,    75,
    71,     9,    57,    62,   837,    83,    63,    64,   840,   794,
   843,    67,   845,    65,    66,    68,    30,    31,    72,    73,
   115,   116,   117,   118,   119,    29,    28,    27,   100,    99,
   101,   102,   847,   849,    19,   115,   116,   117,   118,   119,
     8,    45,   290,    10,   104,   103,   105,    94,    56,    96,
    95,    97,   217,    98,   106,   107,   851,    92,    93,    42,
    43,    41,   237,   241,   246,   247,   248,   243,   245,   253,
   254,   249,   250,  -289,  -591,  -591,   551,   852,   251,   252,
  -289,    40,   855,   917,    33,  -569,   857,    58,    59,  -289,
   858,    60,   552,    35,   234,   643,   240,    44,   236,   235,
   860,   232,   233,   244,   242,   238,    20,   239,  -263,   864,
   866,    91,    81,    84,    85,   217,    86,    88,    87,    89,
   883,   217,   887,   889,    82,    90,   288,    74,    75,    71,
     9,    57,    62,   895,    83,    63,    64,   898,   217,   901,
    67,  -266,    65,    66,    68,    30,    31,    72,    73,   115,
   116,   117,   118,   119,    29,    28,    27,   100,    99,   101,
   102,   911,   807,    19,   666,   665,   664,   918,   667,     8,
    45,   290,    10,   104,   103,   105,    94,    56,    96,    95,
    97,   919,    98,   106,   107,   930,    92,    93,    42,    43,
    41,   237,  -591,  -591,  -591,  -591,   243,   245,  -488,   803,
  -591,  -591,  -489,   794,   932,  -488,   934,   251,   252,  -489,
    40,   936,   938,    33,  -488,   938,    58,    59,  -489,   217,
    60,   944,    35,   234,   947,   240,    44,   236,   235,   948,
   232,   233,   244,   242,   238,    20,   239,   953,   794,   956,
    91,    81,    84,    85,   958,    86,    88,    87,    89,   960,
   962,   962,   973,    82,    90,   288,    74,    75,    71,     9,
    57,    62,   974,    83,    63,    64,   975,   983,  -569,    67,
  -568,    65,    66,    68,    30,    31,    72,    73,   649,   998,
   938,   938,   938,    29,    28,    27,   100,    99,   101,   102,
  1003,   807,    19,   666,   665,   664,   983,   667,     8,    45,
   290,    10,   104,   103,   105,    94,    56,    96,    95,    97,
  1006,    98,   106,   107,  1007,    92,    93,    42,    43,    41,
   237,   807,  1008,   666,   665,   664,   962,   667,   803,   807,
   962,   666,   665,   664,   971,   667,   251,   252,   962,    40,
   217,   972,   292,   983,   938,    58,    59,   983,   962,    60,
   970,    35,   234,   nil,   240,    44,   236,   235,   803,   232,
   233,   nil,   nil,   238,    20,   239,   803,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   288,    74,    75,    71,     9,    57,
    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,
   nil,   nil,    29,    28,    27,   100,    99,   101,   102,   nil,
   807,    19,   666,   665,   664,   nil,   667,     8,    45,   290,
    10,   104,   103,   105,    94,    56,    96,    95,    97,   nil,
    98,   106,   107,  -279,    92,    93,    42,    43,    41,   237,
  -279,  -290,   nil,   nil,   nil,   nil,   nil,   803,  -290,  -279,
   nil,   nil,   nil,   nil,   nil,   251,   252,  -290,    40,   nil,
   nil,   292,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
    35,   234,   nil,   240,    44,   236,   235,   nil,   232,   233,
   nil,   nil,   238,    20,   239,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   288,    74,    75,    71,     9,    57,    62,
   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,
   nil,    29,    28,    27,   100,    99,   101,   102,   nil,   nil,
    19,   nil,   nil,   nil,   nil,   587,     8,    45,   290,    10,
   104,   103,   105,    94,    56,    96,    95,    97,   nil,    98,
   106,   107,   nil,    92,    93,    42,    43,    41,   237,   241,
   246,   247,   248,   243,   245,   253,   254,   249,   250,  -289,
   230,   231,   nil,   nil,   251,   252,  -289,    40,   nil,   nil,
    33,  -569,   nil,    58,    59,  -289,   nil,    60,   nil,    35,
   234,   nil,   240,    44,   236,   235,   nil,   232,   233,   244,
   242,   238,    20,   239,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   255,  -573,   nil,   nil,   nil,    62,   nil,
    83,  -573,  -573,  -573,   nil,   nil,  -573,  -573,  -573,   237,
  -573,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -573,
  -573,  -573,  -573,   nil,   nil,   251,   252,   nil,   nil,   nil,
  -573,  -573,   nil,  -573,  -573,  -573,  -573,  -573,   nil,   nil,
   nil,   234,   nil,   240,   nil,   236,   235,   nil,   232,   233,
   nil,   nil,   238,   nil,   239,   nil,   nil,   nil,   nil,   nil,
  -573,  -573,  -573,  -573,  -573,  -573,  -573,  -573,  -573,  -573,
  -573,  -573,  -573,  -573,   nil,   nil,  -573,  -573,  -573,   nil,
   nil,  -573,   nil,   nil,  -573,   nil,   nil,  -573,  -573,   nil,
  -573,   nil,  -573,   nil,  -573,   nil,  -573,  -573,   nil,  -573,
  -573,  -573,  -573,  -573,   nil,  -573,  -573,  -573,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,  -573,   nil,   nil,  -573,  -573,  -573,  -573,  -574,  -573,
   nil,  -573,   nil,   nil,   nil,  -574,  -574,  -574,   nil,   nil,
  -574,  -574,  -574,   237,  -574,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,  -574,  -574,  -574,  -574,   nil,   nil,   251,
   252,   nil,   nil,   nil,  -574,  -574,   nil,  -574,  -574,  -574,
  -574,  -574,   nil,   nil,   nil,   234,   nil,   240,   nil,   236,
   235,   nil,   232,   233,   nil,   nil,   238,   nil,   239,   nil,
   nil,   nil,   nil,   nil,  -574,  -574,  -574,  -574,  -574,  -574,
  -574,  -574,  -574,  -574,  -574,  -574,  -574,  -574,   nil,   nil,
  -574,  -574,  -574,   nil,   nil,  -574,   nil,   nil,  -574,   nil,
   nil,  -574,  -574,   nil,  -574,   nil,  -574,   nil,  -574,   nil,
  -574,  -574,   nil,  -574,  -574,  -574,  -574,  -574,   nil,  -574,
  -574,  -574,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,  -574,   nil,   nil,  -574,  -574,
  -574,  -574,   nil,  -574,   nil,  -574,     5,    74,    75,    71,
     9,    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,    30,    31,    72,    73,   nil,
   nil,   nil,   nil,   nil,    29,    28,    27,   100,    99,   101,
   102,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,
    45,     7,    10,   104,   103,   105,    94,    56,    96,    95,
    97,   nil,    98,   106,   107,   nil,    92,    93,    42,    43,
    41,   237,  -591,  -591,  -591,  -591,   243,   245,   nil,   nil,
  -591,  -591,   nil,   nil,   nil,   nil,   nil,   251,   252,   nil,
    40,   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,    35,   234,   nil,   240,    44,   236,   235,   nil,
   232,   233,   244,   242,   238,    20,   239,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   288,    74,    75,    71,     9,
    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   100,    99,   101,   102,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   290,    10,   104,   103,   105,    94,    56,    96,    95,    97,
   nil,    98,   106,   107,   nil,    92,    93,    42,    43,    41,
   237,  -591,  -591,  -591,  -591,   243,   245,   nil,   nil,  -591,
  -591,   nil,   nil,   nil,   nil,   nil,   251,   252,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   234,   nil,   240,    44,   236,   235,   nil,   232,
   233,   244,   242,   238,    20,   239,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   288,    74,    75,    71,     9,    57,
    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,
   nil,   nil,    29,    28,    27,   100,    99,   101,   102,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,   290,
    10,   104,   103,   105,    94,    56,    96,    95,    97,   nil,
    98,   106,   107,   nil,    92,    93,    42,    43,    41,   237,
  -591,  -591,  -591,  -591,   243,   245,   nil,   nil,  -591,  -591,
   nil,   nil,   nil,   nil,   nil,   251,   252,   nil,    40,   nil,
   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
    35,   234,   nil,   240,    44,   236,   235,   nil,   232,   233,
   244,   242,   238,    20,   239,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   288,    74,    75,    71,     9,    57,    62,
   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,
   nil,    29,    28,    27,   100,    99,   101,   102,   nil,   nil,
    19,   nil,   nil,   nil,   nil,   nil,     8,    45,   290,    10,
   104,   103,   105,    94,    56,    96,    95,    97,   nil,    98,
   106,   107,   nil,    92,    93,    42,    43,    41,   237,  -591,
  -591,  -591,  -591,   243,   245,   nil,   nil,  -591,  -591,   nil,
   nil,   nil,   nil,   nil,   251,   252,   nil,    40,   nil,   nil,
    33,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,    35,
   234,   nil,   240,    44,   236,   235,   nil,   232,   233,   244,
   242,   238,    20,   239,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   288,    74,    75,    71,     9,    57,    62,   nil,
    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,
    29,    28,    27,   100,    99,   101,   102,   nil,   nil,    19,
   nil,   nil,   nil,   nil,   nil,     8,    45,   290,    10,   104,
   103,   105,    94,    56,    96,    95,    97,   nil,    98,   106,
   107,   nil,    92,    93,    42,    43,    41,   237,  -591,  -591,
  -591,  -591,   243,   245,   nil,   nil,  -591,  -591,   nil,   nil,
   nil,   nil,   nil,   251,   252,   nil,    40,   nil,   nil,    33,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,    35,   234,
   nil,   240,    44,   236,   235,   nil,   232,   233,   244,   242,
   238,    20,   239,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   288,    74,    75,    71,     9,    57,    62,   nil,    83,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,
    28,    27,   100,    99,   101,   102,   nil,   nil,    19,   nil,
   nil,   nil,   nil,   nil,     8,    45,   290,    10,   104,   103,
   105,    94,    56,    96,    95,    97,   nil,    98,   106,   107,
   nil,    92,    93,    42,    43,    41,   237,   241,   246,   247,
   248,   243,   245,   nil,   nil,   249,   250,   nil,   nil,   nil,
   nil,   nil,   251,   252,   nil,    40,   nil,   nil,    33,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,    35,   234,   nil,
   240,    44,   236,   235,   nil,   232,   233,   244,   242,   238,
    20,   239,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   288,    74,    75,    71,     9,    57,    62,   nil,    83,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,
    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,
    27,   100,    99,   101,   102,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,     8,    45,   290,    10,   104,   103,   105,
    94,    56,    96,    95,    97,   nil,    98,   106,   107,   nil,
    92,    93,    42,    43,    41,   237,   241,   246,   247,   248,
   243,   245,   253,   nil,   249,   250,   nil,   nil,   nil,   nil,
   nil,   251,   252,   nil,    40,   nil,   nil,    33,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,    35,   234,   nil,   240,
    44,   236,   235,   nil,   232,   233,   244,   242,   238,    20,
   239,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   288,
    74,    75,    71,     9,    57,    62,   nil,    83,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   100,    99,   101,   102,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,     8,    45,   290,    10,   104,   103,   105,    94,
    56,    96,    95,    97,   nil,    98,   106,   107,   nil,    92,
    93,    42,    43,    41,   237,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   251,   252,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,    35,   234,   nil,   240,    44,
   236,   235,   nil,   232,   233,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   288,    74,
    75,    71,     9,    57,    62,   nil,    83,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   100,
    99,   101,   102,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   290,    10,   104,   103,   105,    94,    56,
    96,    95,    97,   nil,    98,   106,   107,   nil,    92,    93,
    42,    43,    41,   237,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   251,
   252,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   234,   nil,   240,    44,   236,
   235,   nil,   232,   233,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   288,    74,    75,
    71,     9,    57,    62,   nil,    83,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,    30,    31,    72,    73,
   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   100,    99,
   101,   102,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,
     8,    45,   290,    10,   104,   103,   105,    94,    56,    96,
    95,    97,   nil,    98,   106,   107,   nil,    92,    93,    42,
    43,    41,   237,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   251,   252,
   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,    35,   234,   nil,   nil,    44,   236,   235,
   nil,   232,   233,   nil,   nil,   nil,    20,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   288,    74,    75,    71,
     9,    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,    30,    31,    72,    73,   nil,
   nil,   nil,   nil,   nil,    29,    28,    27,   100,    99,   101,
   102,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,
    45,   290,    10,   104,   103,   105,    94,    56,    96,    95,
    97,   nil,    98,   106,   107,   nil,    92,    93,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    40,   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,    35,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   288,    74,    75,    71,     9,
    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   100,    99,   101,   102,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   290,    10,   104,   103,   105,    94,    56,    96,    95,    97,
   nil,    98,   106,   107,   nil,    92,    93,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   288,    74,    75,    71,     9,    57,
    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,
   nil,   nil,    29,    28,    27,   100,    99,   101,   102,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,   290,
    10,   104,   103,   105,    94,    56,    96,    95,    97,   nil,
    98,   106,   107,   nil,    92,    93,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
    35,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   288,    74,    75,    71,     9,    57,    62,
   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,
   nil,    29,    28,    27,   100,    99,   101,   102,   nil,   nil,
    19,   nil,   nil,   nil,   nil,   nil,     8,    45,   290,    10,
   104,   103,   105,    94,    56,    96,    95,    97,   nil,    98,
   106,   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
    33,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,    35,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   288,    74,    75,    71,     9,    57,    62,   nil,
    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,
    29,    28,    27,   100,    99,   101,   102,   nil,   nil,    19,
   nil,   nil,   nil,   nil,   nil,     8,    45,   290,    10,   104,
   103,   105,    94,    56,    96,    95,    97,   nil,    98,   106,
   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,    33,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,    35,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    20,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   288,    74,    75,    71,     9,    57,    62,   nil,    83,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,
    28,    27,   100,    99,   101,   102,   nil,   nil,    19,   nil,
   nil,   nil,   nil,   nil,     8,    45,   290,    10,   104,   103,
   105,    94,    56,    96,    95,    97,   nil,    98,   106,   107,
   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,    33,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,    35,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    20,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   288,    74,    75,    71,     9,    57,    62,   nil,    83,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,
    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,
    27,   100,    99,   101,   102,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,     8,    45,   290,    10,   104,   103,   105,
    94,    56,    96,    95,    97,   nil,    98,   106,   107,   nil,
    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    40,   nil,   nil,    33,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,    35,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   288,
    74,    75,    71,     9,    57,    62,   nil,    83,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   100,    99,   101,   102,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,     8,    45,   290,    10,   104,   103,   105,    94,
    56,    96,    95,    97,   nil,    98,   106,   107,   nil,    92,
    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,    35,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   288,    74,
    75,    71,     9,    57,    62,   nil,    83,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   100,
    99,   101,   102,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   290,    10,   104,   103,   105,    94,    56,
    96,    95,    97,   nil,    98,   106,   107,   nil,    92,    93,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   288,    74,    75,
    71,     9,    57,    62,   nil,    83,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,    30,    31,    72,    73,
   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   100,    99,
   101,   102,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,
     8,    45,   290,    10,   104,   103,   105,    94,    56,    96,
    95,    97,   nil,    98,   106,   107,   nil,    92,    93,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,    35,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   288,    74,    75,    71,
     9,    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,    30,    31,    72,    73,   nil,
   nil,   nil,   nil,   nil,    29,    28,    27,   100,    99,   101,
   102,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,
    45,   290,    10,   104,   103,   105,    94,    56,    96,    95,
    97,   nil,    98,   106,   107,   nil,    92,    93,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    40,   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,    35,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   288,    74,    75,    71,     9,
    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   100,    99,   101,   102,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   290,    10,   104,   103,   105,    94,    56,    96,    95,    97,
   nil,    98,   106,   107,   nil,    92,    93,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   288,    74,    75,    71,     9,    57,
    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,
   nil,   nil,    29,    28,    27,   100,    99,   101,   102,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,   290,
    10,   104,   103,   105,    94,    56,    96,    95,    97,   nil,
    98,   106,   107,   nil,    92,    93,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
    35,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,    74,    75,    71,     9,    57,    62,
   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,
   nil,    29,    28,    27,   100,    99,   101,   102,   nil,   nil,
    19,   nil,   nil,   nil,   nil,   nil,     8,    45,     7,    10,
   104,   103,   105,    94,    56,    96,    95,    97,   nil,    98,
   106,   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
    33,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,    35,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,
    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,
    29,    28,    27,   100,    99,   101,   102,   nil,   nil,   229,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,
   103,   105,    94,    56,    96,    95,    97,   nil,    98,   106,
   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   227,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,    83,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,
    28,    27,   100,    99,   101,   102,   nil,   nil,   229,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,   103,
   105,    94,    56,    96,    95,    97,   282,    98,   106,   107,
   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   280,   nil,   278,
   nil,    44,   nil,   nil,   283,   nil,   nil,   nil,   nil,   nil,
   227,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,    74,    75,    71,   nil,    57,    62,   nil,    83,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,
    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,
    27,   100,    99,   101,   102,   nil,   nil,   229,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,   103,   105,
    94,    56,    96,    95,    97,   282,    98,   106,   107,   nil,
    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   280,   nil,   278,   nil,
    44,   nil,   nil,   283,   nil,   nil,   nil,   nil,   nil,   227,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
    74,    75,    71,   nil,    57,    62,   nil,    83,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   100,    99,   101,   102,   nil,   nil,   229,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   104,   103,   105,    94,
    56,    96,    95,    97,   282,    98,   106,   107,   nil,    92,
    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   280,   nil,   278,   nil,    44,
   nil,   nil,   283,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,    74,
    75,    71,   nil,    57,    62,   nil,    83,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   307,   308,    72,
    73,   nil,   nil,   nil,   nil,   nil,   303,   304,   310,   100,
    99,   101,   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,
   nil,   nil,   305,   nil,   nil,   104,   103,   105,    94,    56,
    96,    95,    97,   nil,    98,   106,   107,   nil,    92,    93,
   nil,   nil,   311,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   301,   nil,   nil,   297,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   296,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,    74,    75,
    71,   nil,    57,    62,   nil,    83,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   307,   308,    72,    73,
   nil,   nil,   nil,   nil,   nil,   303,   304,   310,   100,    99,
   101,   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,   602,
   nil,   305,   nil,   nil,   104,   103,   105,    94,    56,    96,
    95,    97,   nil,    98,   106,   107,   nil,    92,    93,   nil,
   nil,   311,   237,   241,   246,   247,   248,   243,   245,   253,
   254,   249,   250,   nil,   230,   231,   nil,   nil,   251,   252,
   nil,   301,   nil,   nil,   228,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   234,   nil,   240,   nil,   236,   235,
   nil,   232,   233,   244,   242,   238,   nil,   239,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   255,   nil,   313,
   nil,   nil,    62,   nil,    83,    74,    75,    71,   nil,    57,
   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   307,   308,    72,    73,   nil,   nil,   nil,
   nil,   nil,   303,   304,   310,   100,    99,   101,   102,   nil,
   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   104,   103,   105,    94,    56,    96,    95,    97,   nil,
    98,   106,   107,   nil,    92,    93,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,
   nil,   228,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,    74,    75,    71,   nil,    57,    62,
   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   307,   308,    72,    73,   nil,   nil,   nil,   nil,
   nil,   303,   304,   310,   100,    99,   101,   102,   nil,   nil,
   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   104,   103,   105,    94,    56,    96,    95,    97,   nil,    98,
   106,   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,
   228,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,
    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   307,   308,    72,    73,   nil,   nil,   nil,   nil,   nil,
   303,   304,   310,   100,    99,   101,   102,   nil,   nil,   229,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,
   103,   105,    94,    56,    96,    95,    97,   nil,    98,   106,
   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   227,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,    83,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   307,   308,    72,    73,   nil,   nil,   nil,   nil,   nil,   303,
   304,   310,   100,    99,   101,   102,   nil,   nil,   229,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,   103,
   105,    94,    56,    96,    95,    97,   282,    98,   106,   107,
   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   280,   nil,   nil,
   nil,    44,   nil,   nil,   283,   nil,   nil,   nil,   nil,   nil,
   227,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,    74,    75,    71,   nil,    57,    62,   nil,    83,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   307,
   308,    72,    73,   nil,   nil,   nil,   nil,   nil,   303,   304,
   310,   100,    99,   101,   102,   nil,   nil,   229,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,   103,   105,
    94,    56,    96,    95,    97,   282,    98,   106,   107,   nil,
    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   283,   nil,   nil,   nil,   nil,   nil,   227,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
    74,    75,    71,   nil,    57,    62,   nil,    83,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   100,    99,   101,   102,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   104,   103,   105,    94,
    56,    96,    95,    97,   nil,    98,   106,   107,   nil,    92,
    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,    74,
    75,    71,   nil,    57,    62,   nil,    83,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   100,
    99,   101,   102,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   104,   103,   105,    94,    56,
    96,    95,    97,   nil,    98,   106,   107,   nil,    92,    93,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,    74,    75,
    71,   nil,    57,    62,   nil,    83,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,    30,    31,    72,    73,
   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   100,    99,
   101,   102,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   104,   103,   105,    94,    56,    96,
    95,    97,   nil,    98,   106,   107,   nil,    92,    93,    42,
    43,    41,   237,   241,   246,   247,   248,   243,   245,   253,
   254,   249,   250,   nil,   230,   231,   nil,   nil,   251,   252,
   nil,   222,   nil,   nil,   228,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   234,   nil,   240,    44,   236,   235,
   nil,   232,   233,   244,   242,   238,    20,   239,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   112,   255,   nil,   nil,
   nil,   111,    62,   nil,    83,    74,    75,    71,   nil,    57,
   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   307,   308,    72,    73,   nil,   nil,   nil,
   nil,   nil,   303,   304,   310,   100,    99,   101,   102,   nil,
   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,   305,   nil,
   nil,   104,   103,   105,    94,    56,    96,    95,    97,   nil,
    98,   106,   107,   nil,    92,    93,   nil,   nil,   311,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   346,   nil,
   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
    35,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,    74,    75,    71,   nil,    57,    62,
   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   307,   308,    72,    73,   nil,   nil,   nil,   nil,
   nil,   303,   304,   310,   100,    99,   101,   102,   nil,   nil,
   229,   nil,   nil,   nil,   nil,   nil,   nil,   305,   nil,   nil,
   104,   103,   105,   351,    56,    96,    95,   352,   nil,    98,
   106,   107,   nil,    92,    93,   nil,   nil,   311,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   358,   nil,   nil,   353,   nil,   nil,
   228,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,
    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   307,   308,    72,    73,   nil,   nil,   nil,   nil,   nil,
   303,   304,   310,   100,    99,   101,   102,   nil,   nil,   229,
   nil,   nil,   nil,   nil,   nil,   nil,   305,   nil,   nil,   104,
   103,   105,   351,    56,    96,    95,   352,   nil,    98,   106,
   107,   nil,    92,    93,   nil,   nil,   311,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   353,   nil,   nil,   228,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,    74,    75,    71,     9,    57,    62,   nil,    83,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,
    28,    27,   100,    99,   101,   102,   nil,   nil,    19,   nil,
   nil,   nil,   nil,   nil,     8,    45,     7,    10,   104,   103,
   105,    94,    56,    96,    95,    97,   nil,    98,   106,   107,
   nil,    92,    93,    42,    43,    41,   237,   241,   246,   247,
   248,   243,   245,   253,   254,   249,   250,   nil,   230,   231,
   nil,   nil,   251,   252,   nil,    40,   nil,   nil,    33,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,    35,   234,   nil,
   240,    44,   236,   235,   nil,   232,   233,   244,   242,   238,
    20,   239,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   255,   nil,   nil,   nil,   386,    62,   nil,    83,    74,
    75,    71,   nil,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   100,
    99,   101,   102,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   104,   103,   105,    94,    56,
    96,    95,    97,   nil,    98,   106,   107,   nil,    92,    93,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,    74,    75,
    71,   nil,    57,    62,   nil,    83,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,    30,    31,    72,    73,
   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   100,    99,
   101,   102,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   104,   103,   105,    94,    56,    96,
    95,    97,   nil,    98,   106,   107,   nil,    92,    93,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   222,   nil,   nil,   228,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,    74,    75,    71,
   nil,    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,    30,    31,    72,    73,   nil,
   nil,   nil,   nil,   nil,    29,    28,    27,   100,    99,   101,
   102,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   104,   103,   105,    94,    56,    96,    95,
    97,   nil,    98,   106,   107,   nil,    92,    93,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   222,   nil,   nil,   228,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,    74,    75,    71,   nil,
    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   100,    99,   101,   102,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   104,   103,   105,    94,    56,    96,    95,    97,
   nil,    98,   106,   107,   nil,    92,    93,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,
   nil,   nil,   228,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,    74,    75,    71,     9,    57,
    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,
   nil,   nil,    29,    28,    27,   100,    99,   101,   102,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,   nil,
    10,   104,   103,   105,    94,    56,    96,    95,    97,   nil,
    98,   106,   107,   nil,    92,    93,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
    35,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,    74,    75,    71,   nil,    57,    62,
   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,
   nil,    29,    28,    27,   100,    99,   101,   102,   nil,   nil,
   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   104,   103,   105,    94,    56,    96,    95,    97,   nil,    98,
   106,   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,
   228,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   402,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,
    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,
    29,    28,    27,   100,    99,   101,   102,   nil,   nil,   229,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,
   103,   105,    94,    56,    96,    95,    97,   nil,    98,   106,
   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   227,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,    83,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,
    28,    27,   100,    99,   101,   102,   nil,   nil,   229,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,   103,
   105,    94,    56,    96,    95,    97,   282,    98,   106,   107,
   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   280,   nil,   278,
   nil,    44,   nil,   nil,   283,   nil,   nil,   nil,   nil,   nil,
   227,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,    74,    75,    71,   nil,    57,    62,   nil,    83,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,
    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,
    27,   100,    99,   101,   102,   nil,   nil,   229,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,   103,   105,
    94,    56,    96,    95,    97,   nil,    98,   106,   107,   nil,
    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
    74,    75,    71,   nil,    57,    62,   nil,    83,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   100,    99,   101,   102,   nil,   nil,   229,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   104,   103,   105,    94,
    56,    96,    95,    97,   nil,    98,   106,   107,   nil,    92,
    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   402,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,    74,
    75,    71,   nil,    57,    62,   nil,    83,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   100,
    99,   101,   102,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   104,   103,   105,    94,    56,
    96,    95,    97,   nil,    98,   106,   107,   nil,    92,    93,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,    74,    75,
    71,   nil,    57,    62,   nil,    83,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,    30,    31,    72,    73,
   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   100,    99,
   101,   102,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   104,   103,   105,    94,    56,    96,
    95,    97,   nil,    98,   106,   107,   nil,    92,    93,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   222,   nil,   nil,   228,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,    74,    75,    71,
   nil,    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,    30,    31,    72,    73,   nil,
   nil,   nil,   nil,   nil,    29,    28,    27,   100,    99,   101,
   102,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   104,   103,   105,    94,    56,    96,    95,
    97,   nil,    98,   106,   107,   nil,    92,    93,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   222,   nil,   nil,   228,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,    74,    75,    71,   nil,
    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   100,    99,   101,   102,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   104,   103,   105,    94,    56,    96,    95,    97,
   nil,    98,   106,   107,   nil,    92,    93,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,
   nil,   nil,   228,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   217,    74,    75,    71,   nil,    57,
    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   307,   308,    72,    73,   nil,   nil,   nil,
   nil,   nil,   303,   304,   310,   100,    99,   101,   102,   nil,
   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   104,   103,   105,    94,    56,    96,    95,    97,   nil,
    98,   106,   107,   nil,    92,    93,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,
   nil,   228,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,    74,    75,    71,   nil,    57,    62,
   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   307,   308,    72,    73,   nil,   nil,   nil,   nil,
   nil,   303,   304,   310,   100,    99,   101,   102,   nil,   nil,
   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   104,   103,   105,    94,    56,    96,    95,    97,   nil,    98,
   106,   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,
   228,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,
    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   307,   308,    72,    73,   nil,   nil,   nil,   nil,   nil,
   303,   304,   310,   100,    99,   101,   102,   nil,   nil,   229,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,
   103,   105,    94,    56,    96,    95,    97,   nil,    98,   106,
   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   227,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,    83,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   307,   308,    72,    73,   nil,   nil,   nil,   nil,   nil,   303,
   304,   310,   100,    99,   101,   102,   nil,   nil,   229,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,   103,
   105,    94,    56,    96,    95,    97,   nil,    98,   106,   107,
   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   227,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,    74,    75,    71,   nil,    57,    62,   nil,    83,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   307,
   308,    72,    73,   nil,   nil,   nil,   nil,   nil,   303,   304,
   310,   100,    99,   101,   102,   nil,   nil,   229,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,   103,   105,
    94,    56,    96,    95,    97,   nil,    98,   106,   107,   nil,
    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
    74,    75,    71,   nil,    57,    62,   nil,    83,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   307,   308,
    72,    73,   nil,   nil,   nil,   nil,   nil,   303,   304,   310,
   100,    99,   101,   102,   nil,   nil,   229,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   104,   103,   105,    94,
    56,    96,    95,    97,   nil,    98,   106,   107,   nil,    92,
    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,    74,
    75,    71,   nil,    57,    62,   nil,    83,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   307,   308,    72,
    73,   nil,   nil,   nil,   nil,   nil,   303,   304,   310,   100,
    99,   101,   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   104,   103,   105,    94,    56,
    96,    95,    97,   nil,    98,   106,   107,   nil,    92,    93,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,    74,    75,
    71,   nil,    57,    62,   nil,    83,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   307,   308,    72,    73,
   nil,   nil,   nil,   nil,   nil,   303,   304,   310,   100,    99,
   101,   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   104,   103,   105,    94,    56,    96,
    95,    97,   nil,    98,   106,   107,   nil,    92,    93,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   222,   nil,   nil,   228,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,    74,    75,    71,
   nil,    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   307,   308,    72,    73,   nil,
   nil,   nil,   nil,   nil,   303,   304,   310,   100,    99,   101,
   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   104,   103,   105,    94,    56,    96,    95,
    97,   nil,    98,   106,   107,   nil,    92,    93,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   222,   nil,   nil,   228,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,    74,    75,    71,   nil,
    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   307,   308,    72,    73,   nil,   nil,
   nil,   nil,   nil,   303,   304,   310,   100,    99,   101,   102,
   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   104,   103,   105,    94,    56,    96,    95,    97,
   nil,    98,   106,   107,   nil,    92,    93,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,
   nil,   nil,   228,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,    74,    75,    71,   nil,    57,
    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   307,   308,    72,    73,   nil,   nil,   nil,
   nil,   nil,   303,   304,   310,   100,    99,   101,   102,   nil,
   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   104,   103,   105,    94,    56,    96,    95,    97,   nil,
    98,   106,   107,   nil,    92,    93,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,
   nil,   228,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,    74,    75,    71,   nil,    57,    62,
   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   307,   308,    72,    73,   nil,   nil,   nil,   nil,
   nil,   303,   304,   310,   100,    99,   101,   102,   nil,   nil,
   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   104,   103,   105,    94,    56,    96,    95,    97,   nil,    98,
   106,   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,
   228,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,
    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   307,   308,    72,    73,   nil,   nil,   nil,   nil,   nil,
   303,   304,   310,   100,    99,   101,   102,   nil,   nil,   229,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,
   103,   105,    94,    56,    96,    95,    97,   nil,    98,   106,
   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   227,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,    83,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   307,   308,    72,    73,   nil,   nil,   nil,   nil,   nil,   303,
   304,   310,   100,    99,   101,   102,   nil,   nil,   229,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,   103,
   105,    94,    56,    96,    95,    97,   nil,    98,   106,   107,
   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   227,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,    74,    75,    71,   nil,    57,    62,   nil,    83,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   307,
   308,    72,    73,   nil,   nil,   nil,   nil,   nil,   303,   304,
   310,   100,    99,   101,   102,   nil,   nil,   229,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,   103,   105,
    94,    56,    96,    95,    97,   nil,    98,   106,   107,   nil,
    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
    74,    75,    71,   nil,    57,    62,   nil,    83,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   307,   308,
    72,    73,   nil,   nil,   nil,   nil,   nil,   303,   304,   310,
   100,    99,   101,   102,   nil,   nil,   229,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   104,   103,   105,    94,
    56,    96,    95,    97,   nil,    98,   106,   107,   nil,    92,
    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,    74,
    75,    71,   nil,    57,    62,   nil,    83,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   307,   308,    72,
    73,   nil,   nil,   nil,   nil,   nil,   303,   304,   310,   100,
    99,   101,   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   104,   103,   105,    94,    56,
    96,    95,    97,   nil,    98,   106,   107,   nil,    92,    93,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,    74,    75,
    71,   nil,    57,    62,   nil,    83,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   307,   308,    72,    73,
   nil,   nil,   nil,   nil,   nil,   303,   304,   310,   100,    99,
   101,   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   104,   103,   105,    94,    56,    96,
    95,    97,   nil,    98,   106,   107,   nil,    92,    93,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   222,   nil,   nil,   228,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,    74,    75,    71,
   nil,    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   307,   308,    72,    73,   nil,
   nil,   nil,   nil,   nil,   303,   304,   310,   100,    99,   101,
   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   104,   103,   105,    94,    56,    96,    95,
    97,   nil,    98,   106,   107,   nil,    92,    93,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   222,   nil,   nil,   228,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,    74,    75,    71,   nil,
    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   307,   308,    72,    73,   nil,   nil,
   nil,   nil,   nil,   303,   304,   310,   100,    99,   101,   102,
   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   104,   103,   105,    94,    56,    96,    95,    97,
   nil,    98,   106,   107,   nil,    92,    93,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,
   nil,   nil,   228,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,    74,    75,    71,   nil,    57,
    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   307,   308,    72,    73,   nil,   nil,   nil,
   nil,   nil,   303,   304,   310,   100,    99,   101,   102,   nil,
   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   104,   103,   105,    94,    56,    96,    95,    97,   nil,
    98,   106,   107,   nil,    92,    93,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,
   nil,   228,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,    74,    75,    71,   nil,    57,    62,
   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   307,   308,    72,    73,   nil,   nil,   nil,   nil,
   nil,   303,   304,   310,   100,    99,   101,   102,   nil,   nil,
   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   104,   103,   105,    94,    56,    96,    95,    97,   nil,    98,
   106,   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,
   228,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,
    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   307,   308,    72,    73,   nil,   nil,   nil,   nil,   nil,
   303,   304,   310,   100,    99,   101,   102,   nil,   nil,   229,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,
   103,   105,    94,    56,    96,    95,    97,   nil,    98,   106,
   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   227,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,    83,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   307,   308,    72,    73,   nil,   nil,   nil,   nil,   nil,   303,
   304,   310,   100,    99,   101,   102,   nil,   nil,   229,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,   103,
   105,    94,    56,    96,    95,    97,   nil,    98,   106,   107,
   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   227,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,    74,    75,    71,   nil,    57,    62,   nil,    83,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   307,
   308,    72,    73,   nil,   nil,   nil,   nil,   nil,   303,   304,
   310,   100,    99,   101,   102,   nil,   nil,   229,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,   103,   105,
    94,    56,    96,    95,    97,   nil,    98,   106,   107,   nil,
    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
    74,    75,    71,   nil,    57,    62,   nil,    83,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   307,   308,
    72,    73,   nil,   nil,   nil,   nil,   nil,   303,   304,   310,
   100,    99,   101,   102,   nil,   nil,   229,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   104,   103,   105,    94,
    56,    96,    95,    97,   nil,    98,   106,   107,   nil,    92,
    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,    74,
    75,    71,   nil,    57,    62,   nil,    83,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   307,   308,    72,
    73,   nil,   nil,   nil,   nil,   nil,   303,   304,   310,   100,
    99,   101,   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   104,   103,   105,    94,    56,
    96,    95,    97,   nil,    98,   106,   107,   nil,    92,    93,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,    74,    75,
    71,   nil,    57,    62,   nil,    83,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,    30,    31,    72,    73,
   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   100,    99,
   101,   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   104,   103,   105,    94,    56,    96,
    95,    97,   282,    98,   106,   107,   nil,    92,    93,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   222,   nil,   nil,   228,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   280,   nil,   278,   nil,    44,   nil,   nil,
   283,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,    74,    75,    71,
   nil,    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,    30,    31,    72,    73,   nil,
   nil,   nil,   nil,   nil,    29,    28,    27,   100,    99,   101,
   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   104,   103,   105,    94,    56,    96,    95,
    97,   282,    98,   106,   107,   nil,    92,    93,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   222,   nil,   nil,   228,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   280,   nil,   278,   nil,    44,   nil,   nil,   283,
   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,    74,    75,    71,   nil,
    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   100,    99,   101,   102,
   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   104,   103,   105,    94,    56,    96,    95,    97,
   282,    98,   106,   107,   nil,    92,    93,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,
   nil,   nil,   228,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   280,   nil,   278,   nil,    44,   nil,   nil,   283,   nil,
   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   217,    74,    75,    71,   nil,    57,
    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   307,   308,    72,    73,   nil,   nil,   nil,
   nil,   nil,   303,   304,   310,   100,    99,   101,   102,   nil,
   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   104,   103,   105,    94,    56,    96,    95,    97,   nil,
    98,   106,   107,   nil,    92,    93,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,
   nil,   228,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,    74,    75,    71,   nil,    57,    62,
   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   307,   308,    72,    73,   nil,   nil,   nil,   nil,
   nil,   303,   304,   310,   100,    99,   101,   102,   nil,   nil,
   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   104,   103,   105,    94,    56,    96,    95,    97,   nil,    98,
   106,   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,
   228,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,
    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   307,   308,    72,    73,   nil,   nil,   nil,   nil,   nil,
   303,   304,   310,   100,    99,   101,   102,   nil,   nil,   229,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,
   103,   105,    94,    56,    96,    95,    97,   nil,    98,   106,
   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   227,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,    83,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   307,   308,    72,    73,   nil,   nil,   nil,   nil,   nil,   303,
   304,   310,   100,    99,   101,   102,   nil,   nil,   229,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,   103,
   105,    94,    56,    96,    95,    97,   nil,    98,   106,   107,
   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   227,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,    74,    75,    71,     9,    57,    62,   nil,    83,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,
    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,
    27,   100,    99,   101,   102,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,     8,    45,   nil,    10,   104,   103,   105,
    94,    56,    96,    95,    97,   nil,    98,   106,   107,   nil,
    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    40,   nil,   nil,    33,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,    35,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
    74,    75,    71,   nil,    57,    62,   nil,    83,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   307,   308,
    72,    73,   nil,   nil,   nil,   nil,   nil,   303,   304,   310,
   100,    99,   101,   102,   nil,   nil,   229,   nil,   nil,   nil,
   nil,   nil,   nil,   305,   nil,   nil,   104,   103,   105,    94,
    56,    96,    95,    97,   nil,    98,   106,   107,   nil,    92,
    93,   nil,   nil,   311,   237,   241,   246,   247,   248,   243,
   245,   253,   254,   249,   250,   nil,   230,   231,   nil,   nil,
   251,   252,   nil,   301,   nil,   nil,   228,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   234,   nil,   240,   nil,
   236,   235,   nil,   232,   233,   244,   242,   238,   nil,   239,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   255,
   nil,   500,   nil,   nil,    62,   nil,    83,    74,    75,    71,
   nil,    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   307,   308,    72,    73,   nil,
   nil,   nil,   nil,   nil,   303,   304,   310,   100,    99,   101,
   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,
   305,   nil,   nil,   104,   103,   105,    94,    56,    96,    95,
    97,   nil,    98,   106,   107,   nil,    92,    93,   nil,   nil,
   311,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   301,   nil,   nil,   297,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,    74,    75,    71,   nil,
    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   307,   308,    72,    73,   nil,   nil,
   nil,   nil,   nil,   303,   304,   310,   100,    99,   101,   102,
   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   104,   103,   105,    94,    56,    96,    95,    97,
   nil,    98,   106,   107,   nil,    92,    93,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,
   nil,   nil,   228,   518,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,    74,    75,    71,   nil,    57,
    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,
   nil,   nil,    29,    28,    27,   100,    99,   101,   102,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   104,   103,   105,    94,    56,    96,    95,    97,   nil,
    98,   106,   107,   nil,    92,    93,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,
   nil,   228,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,    74,    75,    71,   nil,    57,    62,
   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,
   nil,    29,    28,    27,   100,    99,   101,   102,   nil,   nil,
    19,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   104,   103,   105,    94,    56,    96,    95,    97,   nil,    98,
   106,   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,
   228,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,
    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,
    29,    28,    27,   100,    99,   101,   102,   nil,   nil,    19,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,
   103,   105,    94,    56,    96,    95,    97,   nil,    98,   106,
   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    20,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,    83,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,
    28,    27,   100,    99,   101,   102,   nil,   nil,    19,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,   103,
   105,    94,    56,    96,    95,    97,   nil,    98,   106,   107,
   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    20,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,    74,    75,    71,   nil,    57,    62,   nil,    83,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,
    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,
    27,   100,    99,   101,   102,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,   103,   105,
    94,    56,    96,    95,    97,   nil,    98,   106,   107,   nil,
    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
    74,    75,    71,   nil,    57,    62,   nil,    83,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   307,   308,
    72,    73,   nil,   nil,   nil,   nil,   nil,   303,   304,   310,
   100,    99,   101,   102,   nil,   nil,   229,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   104,   103,   105,    94,
    56,    96,    95,    97,   nil,    98,   106,   107,   nil,    92,
    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,    74,
    75,    71,   nil,    57,    62,   nil,    83,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   100,
    99,   101,   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   104,   103,   105,    94,    56,
    96,    95,    97,   282,    98,   106,   107,   nil,    92,    93,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   280,   nil,   278,   nil,    44,   nil,
   nil,   283,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,    74,    75,
    71,   nil,    57,    62,   nil,    83,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   307,   308,    72,    73,
   nil,   nil,   nil,   nil,   nil,   303,   304,   310,   100,    99,
   101,   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   104,   103,   105,    94,    56,    96,
    95,    97,   nil,    98,   106,   107,   nil,    92,    93,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   222,   nil,   nil,   228,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,    74,    75,    71,
   nil,    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   307,   308,    72,    73,   nil,
   nil,   nil,   nil,   nil,   303,   304,   310,   100,    99,   101,
   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   104,   103,   105,    94,    56,    96,    95,
    97,   nil,    98,   106,   107,   nil,    92,    93,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   222,   nil,   nil,   228,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,    74,    75,    71,   nil,
    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   307,   308,    72,    73,   nil,   nil,
   nil,   nil,   nil,   303,   304,   310,   100,    99,   101,   102,
   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   104,   103,   105,    94,    56,    96,    95,    97,
   nil,    98,   106,   107,   nil,    92,    93,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,
   nil,   nil,   228,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,    74,    75,    71,   nil,    57,
    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   307,   308,    72,    73,   nil,   nil,   nil,
   nil,   nil,   303,   304,   310,   100,    99,   101,   102,   nil,
   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   104,   103,   105,    94,    56,    96,    95,    97,   282,
    98,   106,   107,   nil,    92,    93,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,
   nil,   228,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   626,   nil,   278,   nil,    44,   nil,   nil,   283,   nil,   nil,
   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,    74,    75,    71,   nil,    57,    62,
   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   307,   308,    72,    73,   nil,   nil,   nil,   nil,
   nil,   303,   304,   310,   100,    99,   101,   102,   nil,   nil,
   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   104,   103,   105,    94,    56,    96,    95,    97,   282,    98,
   106,   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,
   228,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   278,   nil,    44,   nil,   nil,   283,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,
    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   307,   308,    72,    73,   nil,   nil,   nil,   nil,   nil,
   303,   304,   310,   100,    99,   101,   102,   nil,   nil,   229,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,
   103,   105,    94,    56,    96,    95,    97,   nil,    98,   106,
   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   227,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,    74,    75,    71,     9,    57,    62,   nil,    83,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,
    28,    27,   100,    99,   101,   102,   nil,   nil,    19,   nil,
   nil,   nil,   nil,   nil,     8,    45,   290,    10,   104,   103,
   105,    94,    56,    96,    95,    97,   nil,    98,   106,   107,
   nil,    92,    93,    42,    43,    41,   237,   241,   246,   247,
   248,   243,   245,   253,   254,   249,   250,   nil,   230,   231,
   nil,   nil,   251,   252,   nil,    40,   nil,   nil,    33,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,    35,   234,   nil,
   240,    44,   236,   235,   nil,   232,   233,   244,   242,   238,
    20,   239,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   217,   255,   nil,   nil,   nil,   386,    62,   nil,    83,    74,
    75,    71,   nil,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   307,   308,    72,
    73,   nil,   nil,   nil,   nil,   nil,   303,   304,   310,   100,
    99,   101,   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,
   nil,   nil,   305,   nil,   nil,   104,   103,   105,    94,    56,
    96,    95,    97,   nil,    98,   106,   107,   nil,    92,    93,
   nil,   nil,   311,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   301,   nil,   nil,   297,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,    74,    75,
    71,   nil,    57,    62,   nil,    83,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,    30,    31,    72,    73,
   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   100,    99,
   101,   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   104,   103,   105,    94,    56,    96,
    95,    97,   282,    98,   106,   107,   nil,    92,    93,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   222,   nil,   nil,   228,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   280,   nil,   278,   nil,    44,   nil,   nil,
   283,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,    74,    75,    71,
   nil,    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   307,   308,    72,    73,   nil,
   nil,   nil,   nil,   nil,   303,   304,   310,   100,    99,   101,
   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,
   305,   nil,   nil,   104,   103,   105,    94,    56,    96,    95,
    97,   nil,    98,   106,   107,   nil,    92,    93,   nil,   nil,
   311,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   301,   nil,   nil,   297,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,    74,    75,    71,   nil,
    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   307,   308,    72,    73,   nil,   nil,
   nil,   nil,   nil,   303,   304,   310,   100,    99,   101,   102,
   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   104,   103,   105,    94,    56,    96,    95,    97,
   nil,    98,   106,   107,   nil,    92,    93,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,
   nil,   nil,   228,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,    74,    75,    71,   nil,    57,
    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   307,   308,    72,    73,   nil,   nil,   nil,
   nil,   nil,   303,   304,   310,   100,    99,   101,   102,   nil,
   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   104,   103,   105,    94,    56,    96,    95,    97,   nil,
    98,   106,   107,   nil,    92,    93,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,
   nil,   228,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,    74,    75,    71,   nil,    57,    62,
   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   307,   308,    72,    73,   nil,   nil,   nil,   nil,
   nil,   303,   304,   310,   100,    99,   101,   102,   nil,   nil,
   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   104,   103,   105,    94,    56,    96,    95,    97,   nil,    98,
   106,   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,
   228,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,
    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,
    29,    28,    27,   100,    99,   101,   102,   nil,   nil,    19,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,
   103,   105,    94,    56,    96,    95,    97,   nil,    98,   106,
   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    20,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,    83,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   307,   308,    72,    73,   nil,   nil,   nil,   nil,   nil,   303,
   304,   310,   100,    99,   101,   102,   nil,   nil,   229,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,   103,
   105,    94,    56,    96,    95,    97,   282,    98,   106,   107,
   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   626,   nil,   nil,
   nil,    44,   nil,   nil,   283,   nil,   nil,   nil,   nil,   nil,
   227,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,    74,    75,    71,   nil,    57,    62,   nil,    83,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   307,
   308,    72,    73,   nil,   nil,   nil,   nil,   nil,   303,   304,
   310,   100,    99,   101,   102,   nil,   nil,   229,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,   103,   105,
    94,    56,    96,    95,    97,   282,    98,   106,   107,   nil,
    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   283,   nil,   nil,   nil,   nil,   nil,   227,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
    74,    75,    71,   nil,    57,    62,   nil,    83,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   307,   308,
    72,    73,   nil,   nil,   nil,   nil,   nil,   303,   304,   310,
   100,    99,   101,   102,   nil,   nil,   229,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   104,   103,   105,    94,
    56,    96,    95,    97,   nil,    98,   106,   107,   nil,    92,
    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   280,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,    74,
    75,    71,   nil,    57,    62,   nil,    83,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   100,
    99,   101,   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   104,   103,   105,    94,    56,
    96,    95,    97,   282,    98,   106,   107,   nil,    92,    93,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   280,   nil,   278,   nil,    44,   nil,
   nil,   283,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,    74,    75,
    71,   nil,    57,    62,   nil,    83,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,    30,    31,    72,    73,
   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   100,    99,
   101,   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   104,   103,   105,    94,    56,    96,
    95,    97,   282,    98,   106,   107,   nil,    92,    93,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   222,   nil,   nil,   228,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   280,   nil,   278,   nil,    44,   nil,   nil,
   283,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,    74,    75,    71,
   nil,    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   307,   308,    72,    73,   nil,
   nil,   nil,   nil,   nil,   303,   304,   310,   100,    99,   101,
   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   104,   103,   105,    94,    56,    96,    95,
    97,   nil,    98,   106,   107,   nil,    92,    93,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   222,   nil,   nil,   228,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   729,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,    74,    75,    71,   nil,
    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   307,   308,    72,    73,   nil,   nil,
   nil,   nil,   nil,   303,   304,   310,   100,    99,   101,   102,
   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   104,   103,   105,    94,    56,    96,    95,    97,
   nil,    98,   106,   107,   nil,    92,    93,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,
   nil,   nil,   228,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,    74,    75,    71,   nil,    57,
    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   307,   308,    72,    73,   nil,   nil,   nil,
   nil,   nil,   303,   304,   310,   100,    99,   101,   102,   nil,
   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   104,   103,   105,    94,    56,    96,    95,    97,   282,
    98,   106,   107,   nil,    92,    93,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,
   nil,   228,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   626,   nil,   278,   nil,    44,   nil,   nil,   283,   nil,   nil,
   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,    74,    75,    71,   nil,    57,    62,
   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   307,   308,    72,    73,   nil,   nil,   nil,   nil,
   nil,   303,   304,   310,   100,    99,   101,   102,   nil,   nil,
   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   104,   103,   105,    94,    56,    96,    95,    97,   282,    98,
   106,   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,
   228,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   278,   nil,    44,   nil,   nil,   283,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,
    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,
    29,    28,    27,   100,    99,   101,   102,   nil,   nil,   229,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,
   103,   105,    94,    56,    96,    95,    97,   nil,    98,   106,
   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   227,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,    83,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,
    28,    27,   100,    99,   101,   102,   nil,   nil,   229,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,   103,
   105,    94,    56,    96,    95,    97,   nil,    98,   106,   107,
   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   227,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,    74,    75,    71,   nil,    57,    62,   nil,    83,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,
    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,
    27,   100,    99,   101,   102,   nil,   nil,   229,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,   103,   105,
    94,    56,    96,    95,    97,   nil,    98,   106,   107,   nil,
    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
    74,    75,    71,   nil,    57,    62,   nil,    83,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   100,    99,   101,   102,   nil,   nil,   229,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   104,   103,   105,    94,
    56,    96,    95,    97,   nil,    98,   106,   107,   nil,    92,
    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,    74,
    75,    71,   nil,    57,    62,   nil,    83,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   100,
    99,   101,   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   104,   103,   105,    94,    56,
    96,    95,    97,   nil,    98,   106,   107,   nil,    92,    93,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,    74,    75,
    71,   nil,    57,    62,   nil,    83,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   307,   308,    72,    73,
   nil,   nil,   nil,   nil,   nil,   303,   304,   310,   100,    99,
   101,   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   104,   103,   105,    94,    56,    96,
    95,    97,   nil,    98,   106,   107,   nil,    92,    93,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   222,   nil,   nil,   228,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,    74,    75,    71,
   nil,    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   307,   308,    72,    73,   nil,
   nil,   nil,   nil,   nil,   303,   304,   310,   100,    99,   101,
   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   104,   103,   105,    94,    56,    96,    95,
    97,   nil,    98,   106,   107,   nil,    92,    93,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   222,   nil,   nil,   228,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,    74,    75,    71,   nil,
    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   307,   308,    72,    73,   nil,   nil,
   nil,   nil,   nil,   303,   304,   310,   100,    99,   101,   102,
   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,   305,
   nil,   nil,   104,   103,   105,    94,    56,    96,    95,    97,
   nil,    98,   106,   107,   nil,    92,    93,   nil,   nil,   311,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   301,
   nil,   nil,   297,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,    74,    75,    71,   nil,    57,
    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   307,   308,    72,    73,   nil,   nil,   nil,
   nil,   nil,   303,   304,   310,   100,    99,   101,   102,   nil,
   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,   305,   nil,
   nil,   104,   103,   105,    94,    56,    96,    95,    97,   nil,
    98,   106,   107,   nil,    92,    93,   nil,   nil,   311,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   301,   nil,
   nil,   297,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,    74,    75,    71,   nil,    57,    62,
   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   307,   308,    72,    73,   nil,   nil,   nil,   nil,
   nil,   303,   304,   310,   100,    99,   101,   102,   nil,   nil,
   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   104,   103,   105,    94,    56,    96,    95,    97,   nil,    98,
   106,   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,
   228,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   402,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,
    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   307,   308,    72,    73,   nil,   nil,   nil,   nil,   nil,
   303,   304,   310,   100,    99,   101,   102,   nil,   nil,   229,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,
   103,   105,    94,    56,    96,    95,    97,   nil,    98,   106,
   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   227,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,    83,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,
    28,    27,   100,    99,   101,   102,   nil,   nil,    19,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,   103,
   105,    94,    56,    96,    95,    97,   nil,    98,   106,   107,
   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    20,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,    74,    75,    71,   nil,    57,    62,   nil,    83,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,
    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,
    27,   100,    99,   101,   102,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,   103,   105,
    94,    56,    96,    95,    97,   nil,    98,   106,   107,   nil,
    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
    74,    75,    71,   nil,    57,    62,   nil,    83,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   307,   308,
    72,    73,   nil,   nil,   nil,   nil,   nil,   303,   304,   310,
   100,    99,   101,   102,   nil,   nil,   229,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   104,   103,   105,    94,
    56,    96,    95,    97,   nil,    98,   106,   107,   nil,    92,
    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,    74,
    75,    71,   nil,    57,    62,   nil,    83,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   100,
    99,   101,   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   104,   103,   105,    94,    56,
    96,    95,    97,   nil,    98,   106,   107,   nil,    92,    93,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,    74,    75,
    71,   nil,    57,    62,   nil,    83,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   307,   308,    72,    73,
   nil,   nil,   nil,   nil,   nil,   303,   304,   310,   100,    99,
   101,   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   104,   103,   105,    94,    56,    96,
    95,    97,   nil,    98,   106,   107,   nil,    92,    93,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   222,   nil,   nil,   228,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,    74,    75,    71,
   nil,    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   307,   308,    72,    73,   nil,
   nil,   nil,   nil,   nil,   303,   304,   310,   100,    99,   101,
   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   104,   103,   105,    94,    56,    96,    95,
    97,   nil,    98,   106,   107,   nil,    92,    93,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   222,   nil,   nil,   228,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,    74,    75,    71,   nil,
    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   307,   308,    72,    73,   nil,   nil,
   nil,   nil,   nil,   303,   304,   310,   100,    99,   101,   102,
   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   104,   103,   105,    94,    56,    96,    95,    97,
   nil,    98,   106,   107,   nil,    92,    93,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,
   nil,   nil,   228,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,    74,    75,    71,   nil,    57,
    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   307,   308,    72,    73,   nil,   nil,   nil,
   nil,   nil,   303,   304,   310,   100,    99,   101,   102,   nil,
   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   104,   103,   105,    94,    56,    96,    95,    97,   nil,
    98,   106,   107,   nil,    92,    93,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,
   nil,   228,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,    74,    75,    71,   nil,    57,    62,
   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   307,   308,    72,    73,   nil,   nil,   nil,   nil,
   nil,   303,   304,   310,   100,    99,   101,   102,   nil,   nil,
   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   104,   103,   105,    94,    56,    96,    95,    97,   nil,    98,
   106,   107,   nil,    92,    93,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,
   228,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,
    83,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   307,   308,    72,    73,   nil,   nil,   nil,   nil,   nil,
   303,   304,   310,   100,    99,   101,   102,   nil,   nil,   229,
   nil,   nil,   nil,   nil,   nil,   nil,   305,   nil,   nil,   104,
   103,   105,    94,    56,    96,    95,    97,   nil,    98,   106,
   107,   nil,    92,    93,   nil,   nil,   311,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   854,   nil,   nil,   228,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,    74,    75,    71,   nil,    57,    62,   nil,    83,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   307,   308,    72,    73,   nil,   nil,   nil,   nil,   nil,   303,
   304,   310,   100,    99,   101,   102,   nil,   nil,   229,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,   103,
   105,    94,    56,    96,    95,    97,   nil,    98,   106,   107,
   nil,    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   227,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,    74,    75,    71,   nil,    57,    62,   nil,    83,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,
    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,
    27,   100,    99,   101,   102,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   104,   103,   105,
    94,    56,    96,    95,    97,   nil,    98,   106,   107,   nil,
    92,    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
    74,    75,    71,   nil,    57,    62,   nil,    83,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   307,   308,
    72,    73,   nil,   nil,   nil,   nil,   nil,   303,   304,   310,
   100,    99,   101,   102,   nil,   nil,   229,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   104,   103,   105,    94,
    56,    96,    95,    97,   nil,    98,   106,   107,   nil,    92,
    93,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   626,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,    74,
    75,    71,   nil,    57,    62,   nil,    83,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   307,   308,    72,
    73,   nil,   nil,   nil,   nil,   nil,   303,   304,   310,   100,
    99,   101,   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   104,   103,   105,    94,    56,
    96,    95,    97,   282,    98,   106,   107,   nil,    92,    93,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   222,   nil,   nil,   228,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   278,   nil,    44,   nil,
   nil,   283,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,    74,    75,
    71,   nil,    57,    62,   nil,    83,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   307,   308,    72,    73,
   nil,   nil,   nil,   nil,   nil,   303,   304,   310,   100,    99,
   101,   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   104,   103,   105,    94,    56,    96,
    95,    97,   nil,    98,   106,   107,   nil,    92,    93,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   222,   nil,   nil,   228,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,    74,    75,    71,
   nil,    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   307,   308,    72,    73,   nil,
   nil,   nil,   nil,   nil,   303,   304,   310,   100,    99,   101,
   102,   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,
   305,   nil,   nil,   104,   103,   105,    94,    56,    96,    95,
    97,   nil,    98,   106,   107,   nil,    92,    93,   nil,   nil,
   311,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   854,   nil,   nil,   228,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,    74,    75,    71,   nil,
    57,    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   307,   308,    72,    73,   nil,   nil,
   nil,   nil,   nil,   303,   304,   310,   100,    99,   101,   102,
   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,   305,
   nil,   nil,   104,   103,   105,    94,    56,    96,    95,    97,
   nil,    98,   106,   107,   nil,    92,    93,   nil,   nil,   311,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   925,
   nil,   nil,   228,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,    74,    75,    71,   nil,    57,
    62,   nil,    83,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,
   nil,   nil,    29,    28,    27,   100,    99,   101,   102,   nil,
   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   104,   103,   105,    94,    56,    96,    95,    97,   282,
    98,   106,   107,   nil,    92,    93,    42,    43,    41,   237,
   241,   246,   247,   248,   243,   245,   253,   254,   249,   250,
   nil,   230,   231,   nil,   nil,   251,   252,   nil,   222,   nil,
   nil,   228,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   280,   234,   278,   240,    44,   236,   235,   283,   232,   233,
   244,   242,   238,   227,   239,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   255,  -413,   nil,   nil,   nil,    62,
   nil,    83,  -413,  -413,  -413,   nil,   nil,  -413,  -413,  -413,
   nil,  -413,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
  -413,  -413,  -413,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,  -413,  -413,   nil,  -413,  -413,  -413,  -413,  -413,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,  -413,  -413,  -413,  -413,  -413,  -413,  -413,  -413,  -413,
  -413,  -413,  -413,  -413,  -413,   nil,   nil,  -413,  -413,  -413,
   nil,   nil,  -413,   nil,   262,  -413,   nil,   nil,  -413,  -413,
   nil,  -413,   nil,  -413,   nil,  -413,   nil,  -413,  -413,   nil,
  -413,  -413,  -413,  -413,  -413,  -296,  -413,  -413,  -413,   nil,
   nil,   nil,  -296,  -296,  -296,   nil,   nil,  -296,  -296,  -296,
   nil,  -296,  -413,   nil,   nil,  -413,  -413,   nil,  -413,   nil,
  -413,  -296,  -296,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,  -296,  -296,   nil,  -296,  -296,  -296,  -296,  -296,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,  -296,  -296,  -296,  -296,  -296,  -296,  -296,  -296,  -296,
  -296,  -296,  -296,  -296,  -296,   nil,   nil,  -296,  -296,  -296,
   nil,   nil,  -296,   nil,   271,  -296,   nil,   nil,  -296,  -296,
   nil,  -296,   nil,  -296,   nil,  -296,   nil,  -296,  -296,   nil,
  -296,  -296,  -296,  -296,  -296,   nil,  -296,  -246,  -296,   nil,
   nil,   nil,   nil,   nil,  -246,  -246,  -246,   nil,   nil,  -246,
  -246,  -246,  -296,  -246,   nil,  -296,  -296,   nil,  -296,   nil,
  -296,   nil,  -246,  -246,  -246,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,  -246,  -246,   nil,  -246,  -246,  -246,  -246,
  -246,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,  -246,  -246,  -246,  -246,  -246,  -246,  -246,
  -246,  -246,  -246,  -246,  -246,  -246,  -246,   nil,   nil,  -246,
  -246,  -246,   nil,   nil,  -246,   nil,   262,  -246,   nil,   nil,
  -246,  -246,   nil,  -246,   nil,  -246,   nil,  -246,   nil,  -246,
  -246,   nil,  -246,  -246,  -246,  -246,  -246,   nil,  -246,  -246,
  -246,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,  -246,   nil,  -246,  -246,  -246,   nil,
  -246,   nil,  -246,  -246,  -246,  -246,   nil,   nil,  -246,  -246,
  -246,   761,  -246,   666,   665,   664,   762,   667,   nil,   nil,
   nil,  -246,  -246,   nil,   nil,   nil,   675,   nil,   nil,   nil,
   nil,   nil,  -246,  -246,   nil,  -246,  -246,  -246,  -246,  -246,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   671,   nil,
   669,   nil,   666,   665,   664,   673,   667,   680,   679,   683,
   682,   nil,   nil,   nil,   676,   675,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,  -246,   nil,   nil,   nil,   nil,   nil,   nil,
  -246,   nil,   nil,   nil,   nil,   262,  -246,   671,   655,   nil,
   217,   nil,   nil,   nil,   nil,   nil,   680,   679,   683,   682,
   nil,   nil,   nil,   676,   nil,   nil,   nil,   nil,  -246,  -246,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,  -246,   nil,   nil,  -246,   nil,   nil,   nil,
   nil,  -246,   172,   183,   173,   196,   169,   189,   179,   178,
   199,   200,   194,   177,   176,   171,   197,   201,   202,   181,
   170,   184,   188,   190,   182,   175,   nil,   nil,   nil,   191,
   198,   193,   192,   185,   195,   180,   168,   187,   186,   nil,
   nil,   nil,   nil,   nil,   167,   174,   165,   166,   162,   163,
   164,   123,   125,   122,   nil,   124,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   156,   157,   nil,   153,   135,   136,   137,
   144,   141,   143,   nil,   nil,   138,   139,   nil,   nil,   nil,
   158,   159,   145,   146,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   150,   149,   nil,
   134,   155,   152,   151,   160,   147,   148,   142,   140,   132,
   154,   133,   nil,   nil,   161,    91,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    90,
   172,   183,   173,   196,   169,   189,   179,   178,   199,   200,
   194,   177,   176,   171,   197,   201,   202,   181,   170,   184,
   188,   190,   182,   175,   nil,   nil,   nil,   191,   198,   193,
   192,   185,   195,   180,   168,   187,   186,   nil,   nil,   nil,
   nil,   nil,   167,   174,   165,   166,   162,   163,   164,   123,
   125,   nil,   nil,   124,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   156,   157,   nil,   153,   135,   136,   137,   144,   141,
   143,   nil,   nil,   138,   139,   nil,   nil,   nil,   158,   159,
   145,   146,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   150,   149,   nil,   134,   155,
   152,   151,   160,   147,   148,   142,   140,   132,   154,   133,
   nil,   nil,   161,    91,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    90,   172,   183,
   173,   196,   169,   189,   179,   178,   199,   200,   194,   177,
   176,   171,   197,   201,   202,   181,   170,   184,   188,   190,
   182,   175,   nil,   nil,   nil,   191,   198,   193,   192,   185,
   195,   180,   168,   187,   186,   nil,   nil,   nil,   nil,   nil,
   167,   174,   165,   166,   162,   163,   164,   123,   125,   nil,
   nil,   124,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   156,
   157,   nil,   153,   135,   136,   137,   144,   141,   143,   nil,
   nil,   138,   139,   nil,   nil,   nil,   158,   159,   145,   146,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   150,   149,   nil,   134,   155,   152,   151,
   160,   147,   148,   142,   140,   132,   154,   133,   nil,   nil,
   161,    91,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    90,   172,   183,   173,   196,
   169,   189,   179,   178,   199,   200,   194,   177,   176,   171,
   197,   201,   202,   181,   170,   184,   188,   190,   182,   175,
   nil,   nil,   nil,   191,   198,   193,   192,   185,   195,   180,
   168,   187,   186,   nil,   nil,   nil,   nil,   nil,   167,   174,
   165,   166,   162,   163,   164,   123,   125,   nil,   nil,   124,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   156,   157,   nil,
   153,   135,   136,   137,   144,   141,   143,   nil,   nil,   138,
   139,   nil,   nil,   nil,   158,   159,   145,   146,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   150,   149,   nil,   134,   155,   152,   151,   160,   147,
   148,   142,   140,   132,   154,   133,   nil,   nil,   161,    91,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    90,   172,   183,   173,   196,   169,   189,
   179,   178,   199,   200,   194,   177,   176,   171,   197,   201,
   202,   181,   170,   184,   188,   190,   182,   175,   nil,   nil,
   nil,   191,   198,   193,   369,   368,   370,   367,   168,   187,
   186,   nil,   nil,   nil,   nil,   nil,   167,   174,   165,   166,
   364,   365,   366,   362,   125,    96,    95,   363,   nil,    98,
   nil,   nil,   nil,   nil,   nil,   156,   157,   nil,   153,   135,
   136,   137,   144,   141,   143,   nil,   nil,   138,   139,   nil,
   nil,   nil,   158,   159,   145,   146,   nil,   nil,   nil,   nil,
   nil,   374,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   150,
   149,   nil,   134,   155,   152,   151,   160,   147,   148,   142,
   140,   132,   154,   133,   nil,   nil,   161,   172,   183,   173,
   196,   169,   189,   179,   178,   199,   200,   194,   177,   176,
   171,   197,   201,   202,   181,   170,   184,   188,   190,   182,
   175,   nil,   nil,   nil,   191,   198,   193,   192,   185,   195,
   180,   168,   187,   186,   nil,   nil,   nil,   nil,   nil,   167,
   174,   165,   166,   162,   163,   164,   123,   125,   nil,   nil,
   124,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   156,   157,
   nil,   153,   135,   136,   137,   144,   141,   143,   nil,   nil,
   138,   139,   nil,   nil,   nil,   158,   159,   145,   146,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   150,   149,   nil,   134,   155,   152,   151,   160,
   147,   148,   142,   140,   132,   154,   133,   nil,   nil,   161,
   237,   241,   246,   247,   248,   243,   245,   253,   254,   249,
   250,   nil,   230,   231,   nil,   nil,   251,   252,   nil,   nil,
   nil,   nil,   nil,   669,   nil,   666,   665,   664,   673,   667,
   nil,   nil,   234,   nil,   240,   nil,   236,   235,   675,   232,
   233,   244,   242,   238,   nil,   239,   237,   241,   246,   247,
   248,   243,   245,   253,   254,   249,   250,   nil,   230,   231,
   671,   711,   251,   252,   nil,   255,   nil,   nil,   nil,   680,
   679,   683,   682,   nil,   nil,   nil,   676,   nil,   234,   nil,
   240,   nil,   236,   235,   nil,   232,   233,   244,   242,   238,
   nil,   239,   237,   241,   246,   247,   248,   243,   245,   253,
   254,   249,   250,   nil,   230,   231,   nil,   nil,   251,   252,
   nil,   255,   669,   nil,   666,   665,   664,   673,   667,   nil,
   nil,   nil,   nil,   nil,   234,   nil,   240,   675,   236,   235,
   nil,   232,   233,   244,   242,   238,   nil,   239,   237,   241,
   246,   247,   248,   243,   245,   253,   254,   249,   250,   671,
   230,   231,   nil,   nil,   251,   252,   nil,   255,   680,   679,
   683,   682,   nil,   nil,   nil,   676,   nil,   nil,   nil,   nil,
   234,   nil,   240,   nil,   236,   235,   nil,   232,   233,   244,
   242,   238,   nil,   239,   237,   241,   246,   247,   248,   243,
   245,   253,   254,   249,   250,   nil,   230,   231,   nil,   nil,
   251,   252,   nil,   255,   669,   nil,   666,   665,   664,   673,
   667,   nil,   nil,   nil,   nil,   nil,   234,   nil,   240,   675,
   236,   235,   nil,   232,   233,   244,   242,   238,   nil,   239,
   237,   241,   246,   247,   248,   243,   245,   253,   254,   249,
   250,   671,   230,   231,   nil,   nil,   251,   252,   nil,   255,
   680,   679,   683,   682,   nil,   nil,   nil,   676,   nil,   nil,
   nil,   nil,   234,   nil,   240,   nil,   236,   235,   nil,   232,
   233,   244,   242,   238,   nil,   239,   237,   241,   246,   247,
   248,   243,   245,   253,   254,   249,   250,   nil,   230,   231,
   nil,   nil,   251,   252,   nil,   255,   669,   nil,   666,   665,
   664,   673,   667,   nil,   nil,   nil,   nil,   nil,   234,   nil,
   240,   675,   236,   235,   nil,   232,   233,   244,   242,   238,
   nil,   239,   237,   241,   246,   247,   248,   243,   245,   253,
   254,   249,   250,   671,   230,   231,   nil,   nil,   251,   252,
   nil,   255,   680,   679,   683,   682,   nil,   nil,   nil,   676,
   nil,   nil,   nil,   nil,   234,   nil,   240,   nil,   236,   235,
   nil,   232,   233,   244,   242,   238,   nil,   239,   237,   241,
   246,   247,   248,   243,   245,   253,   254,   249,   250,   nil,
   230,   231,   nil,   nil,   251,   252,   nil,   255,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   234,   nil,   240,   nil,   236,   235,   nil,   232,   233,   244,
   242,   238,   nil,   239,   nil,   nil,   nil,   nil,   nil,   410,
   414,   nil,   nil,   411,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   156,   157,   255,   153,   135,   136,   137,   144,   141,
   143,   nil,   nil,   138,   139,   nil,   nil,   nil,   158,   159,
   145,   146,   nil,   nil,   nil,   nil,   nil,   262,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   150,   149,   nil,   134,   155,
   152,   151,   160,   147,   148,   142,   140,   132,   154,   133,
   417,   421,   161,   nil,   416,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   156,   157,   nil,   153,   135,   136,   137,   144,
   141,   143,   nil,   nil,   138,   139,   nil,   nil,   nil,   158,
   159,   145,   146,   nil,   nil,   nil,   nil,   nil,   262,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   150,   149,   nil,   134,
   155,   152,   151,   160,   147,   148,   142,   140,   132,   154,
   133,   472,   414,   161,   nil,   473,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   156,   157,   nil,   153,   135,   136,   137,
   144,   141,   143,   nil,   nil,   138,   139,   nil,   nil,   nil,
   158,   159,   145,   146,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   150,   149,   nil,
   134,   155,   152,   151,   160,   147,   148,   142,   140,   132,
   154,   133,   605,   414,   161,   nil,   606,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   156,   157,   nil,   153,   135,   136,
   137,   144,   141,   143,   nil,   nil,   138,   139,   nil,   nil,
   nil,   158,   159,   145,   146,   nil,   nil,   nil,   nil,   nil,
   262,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   150,   149,
   nil,   134,   155,   152,   151,   160,   147,   148,   142,   140,
   132,   154,   133,   607,   421,   161,   nil,   608,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   156,   157,   nil,   153,   135,
   136,   137,   144,   141,   143,   nil,   nil,   138,   139,   nil,
   nil,   nil,   158,   159,   145,   146,   nil,   nil,   nil,   nil,
   nil,   262,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   150,
   149,   nil,   134,   155,   152,   151,   160,   147,   148,   142,
   140,   132,   154,   133,   636,   414,   161,   nil,   637,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   156,   157,   nil,   153,
   135,   136,   137,   144,   141,   143,   nil,   nil,   138,   139,
   nil,   nil,   nil,   158,   159,   145,   146,   nil,   nil,   nil,
   nil,   nil,   262,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   150,   149,   nil,   134,   155,   152,   151,   160,   147,   148,
   142,   140,   132,   154,   133,   639,   421,   161,   nil,   640,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   156,   157,   nil,
   153,   135,   136,   137,   144,   141,   143,   nil,   nil,   138,
   139,   nil,   nil,   nil,   158,   159,   145,   146,   nil,   nil,
   nil,   nil,   nil,   262,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   150,   149,   nil,   134,   155,   152,   151,   160,   147,
   148,   142,   140,   132,   154,   133,   605,   414,   161,   nil,
   606,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   156,   157,
   nil,   153,   135,   136,   137,   144,   141,   143,   nil,   nil,
   138,   139,   nil,   nil,   nil,   158,   159,   145,   146,   nil,
   nil,   nil,   nil,   nil,   262,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   150,   149,   nil,   134,   155,   152,   151,   160,
   147,   148,   142,   140,   132,   154,   133,   607,   421,   161,
   nil,   608,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   156,
   157,   nil,   153,   135,   136,   137,   144,   141,   143,   nil,
   nil,   138,   139,   nil,   nil,   nil,   158,   159,   145,   146,
   nil,   nil,   nil,   nil,   nil,   262,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   150,   149,   nil,   134,   155,   152,   151,
   160,   147,   148,   142,   140,   132,   154,   133,   696,   414,
   161,   nil,   697,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   156,   157,   nil,   153,   135,   136,   137,   144,   141,   143,
   nil,   nil,   138,   139,   nil,   nil,   nil,   158,   159,   145,
   146,   nil,   nil,   nil,   nil,   nil,   262,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   150,   149,   nil,   134,   155,   152,
   151,   160,   147,   148,   142,   140,   132,   154,   133,   698,
   421,   161,   nil,   699,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   156,   157,   nil,   153,   135,   136,   137,   144,   141,
   143,   nil,   nil,   138,   139,   nil,   nil,   nil,   158,   159,
   145,   146,   nil,   nil,   nil,   nil,   nil,   262,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   150,   149,   nil,   134,   155,
   152,   151,   160,   147,   148,   142,   140,   132,   154,   133,
   701,   421,   161,   nil,   702,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   156,   157,   nil,   153,   135,   136,   137,   144,
   141,   143,   nil,   nil,   138,   139,   nil,   nil,   nil,   158,
   159,   145,   146,   nil,   nil,   nil,   nil,   nil,   262,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   150,   149,   nil,   134,
   155,   152,   151,   160,   147,   148,   142,   140,   132,   154,
   133,   472,   414,   161,   nil,   473,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   156,   157,   nil,   153,   135,   136,   137,
   144,   141,   143,   nil,   nil,   138,   139,   nil,   nil,   nil,
   158,   159,   145,   146,   nil,   nil,   nil,   nil,   nil,   262,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   150,   149,   nil,
   134,   155,   152,   151,   160,   147,   148,   142,   140,   132,
   154,   133,   968,   421,   161,   nil,   967,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   156,   157,   nil,   153,   135,   136,
   137,   144,   141,   143,   nil,   nil,   138,   139,   nil,   nil,
   nil,   158,   159,   145,   146,   nil,   nil,   nil,   nil,   nil,
   262,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   150,   149,
   nil,   134,   155,   152,   151,   160,   147,   148,   142,   140,
   132,   154,   133,   994,   414,   161,   nil,   995,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   156,   157,   nil,   153,   135,
   136,   137,   144,   141,   143,   nil,   nil,   138,   139,   nil,
   nil,   nil,   158,   159,   145,   146,   nil,   nil,   nil,   nil,
   nil,   262,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   150,
   149,   nil,   134,   155,   152,   151,   160,   147,   148,   142,
   140,   132,   154,   133,   996,   421,   161,   nil,   997,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   156,   157,   nil,   153,
   135,   136,   137,   144,   141,   143,   nil,   nil,   138,   139,
   nil,   nil,   nil,   158,   159,   145,   146,   nil,   nil,   nil,
   nil,   nil,   262,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   150,   149,   nil,   134,   155,   152,   151,   160,   147,   148,
   142,   140,   132,   154,   133,   nil,   669,   161,   666,   665,
   664,   673,   667,   807,   nil,   666,   665,   664,   673,   667,
   nil,   675,   nil,   nil,   nil,   nil,   nil,   761,   675,   666,
   665,   664,   762,   667,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   675,   671,   nil,   nil,   nil,   nil,   nil,   nil,
   671,   nil,   680,   679,   683,   682,   nil,   nil,   nil,   676,
   nil,   683,   682,   nil,   671,   nil,   676,   nil,   nil,   nil,
   nil,   nil,   nil,   680,   679,   683,   682,   nil,   nil,   761,
   676,   666,   665,   664,   762,   667,   807,   nil,   666,   665,
   664,   762,   667,   nil,   675,   nil,   nil,   nil,   nil,   nil,
   669,   675,   666,   665,   664,   673,   667,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   675,   671,   nil,   nil,   nil,
   nil,   nil,   nil,   671,   nil,   680,   679,   683,   682,   nil,
   nil,   nil,   676,   nil,   683,   682,   nil,   671,   711,   676,
   nil,   nil,   nil,   nil,   nil,   nil,   680,   679,   683,   682,
   nil,   nil,   669,   676,   666,   665,   664,   673,   667,   807,
   nil,   666,   665,   664,   673,   667,   807,   675,   666,   665,
   664,   673,   667,   807,   675,   666,   665,   664,   673,   667,
   nil,   675,   nil,   nil,   nil,   nil,   nil,   nil,   675,   671,
   nil,   nil,   nil,   nil,   nil,   nil,   671,   nil,   680,   679,
   683,   682,   nil,   671,   nil,   676,   nil,   683,   682,   nil,
   671,   nil,   676,   nil,   683,   682,   nil,   nil,   nil,   676,
   nil,   683,   682,   nil,   nil,   761,   676,   666,   665,   664,
   762,   667,   807,   nil,   666,   665,   664,   762,   667,   807,
   675,   666,   665,   664,   762,   667,   807,   675,   666,   665,
   664,   762,   667,   807,   675,   666,   665,   664,   673,   667,
   nil,   675,   671,   nil,   nil,   nil,   nil,   nil,   675,   671,
   nil,   680,   679,   683,   682,   nil,   671,   nil,   676,   nil,
   683,   682,   nil,   671,   nil,   676,   nil,   683,   682,   nil,
   671,   nil,   676,   nil,   683,   682,   nil,   nil,   nil,   676,
   nil,   683,   682,   nil,   nil,   807,   676,   666,   665,   664,
   762,   667,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   675,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   671,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   683,   682,   nil,   nil,   nil,   676 ]

racc_action_check = [
    94,     0,     0,     0,     0,     0,     0,    94,    94,    94,
     0,     0,    94,    94,    94,     0,    94,     0,     0,     0,
     0,     0,     0,     0,    94,    61,    94,    94,    94,     0,
     0,     0,     0,     0,     0,     0,    94,    94,     0,    94,
    94,    94,    94,    94,     0,     0,     0,     0,     0,     0,
     0,     0,     0,     0,     0,     0,   343,     0,     0,     0,
   357,     0,     0,     0,     0,     0,    94,    94,    94,    94,
    94,    94,    94,    94,    94,    94,    94,    94,    94,    94,
    26,   344,    94,    94,    94,     0,    94,    94,     0,   347,
    94,     0,     0,    94,    94,     0,    94,     0,    94,   617,
    94,     0,    94,    94,   306,    94,    94,    94,    94,    94,
     0,    94,    61,    94,    19,     0,     0,     0,     0,   381,
     0,     0,     0,     0,   545,     1,   654,    94,     0,     0,
    94,    94,    94,    94,    97,    94,     0,    94,     0,    94,
   696,    97,    97,    97,    26,   336,    97,    97,    97,   382,
    97,     7,   337,   697,     3,    19,   694,    58,    97,     3,
    97,    97,    97,   822,   470,   343,   357,    26,   306,   220,
    97,    97,   863,    97,    97,    97,    97,    97,   888,   994,
   995,  1013,   996,   698,   537,   381,   617,   286,    24,   357,
   344,   306,   286,   221,   357,    24,    10,    58,   347,   470,
    97,    97,    97,    97,    97,    97,    97,    97,    97,    97,
    97,    97,    97,    97,    12,   382,    97,    97,    97,   362,
    97,    97,   220,   636,    97,   223,   362,    97,    97,   440,
    97,   538,    97,   545,    97,   654,    97,    97,   605,    97,
    97,    97,    97,    97,   416,    97,   221,    97,   698,   696,
   823,   416,   416,   416,   434,   434,   606,   416,   416,   996,
   416,    97,   697,   336,    97,    97,    97,    97,   336,    97,
   337,    97,   822,    97,   694,   337,   699,   694,   223,   694,
   416,   416,   440,   416,   416,   416,   416,   416,   537,   636,
   863,   605,   698,   537,   637,   863,   888,   994,   995,  1013,
   996,   888,   994,   995,  1013,   996,    13,   557,   557,   606,
   416,   416,   416,   416,   416,   416,   416,   416,   416,   416,
   416,   416,   416,   416,   561,   561,   416,   416,   416,   636,
   416,    16,   636,    15,   416,   538,   749,   416,   636,   631,
   538,   699,   416,   478,   416,   631,   416,   416,   434,   416,
   416,   416,   416,   416,   823,   416,   417,   416,    22,   823,
   637,   647,   647,   417,   417,   417,    38,   479,    39,   417,
   417,   416,   417,   327,   416,   416,   327,   416,   478,   416,
    15,   417,   618,   416,   351,   699,    15,   363,   328,   749,
    37,   328,   417,   417,   363,   417,   417,   417,   417,   417,
   637,   557,   479,   637,   315,   893,   557,   893,   806,   637,
   806,   806,   806,    38,   806,    39,   376,   618,   561,    38,
   352,    39,   417,   417,   417,   417,   417,   417,   417,   417,
   417,   417,   417,   417,   417,   417,   351,    14,   417,   417,
   417,   572,   417,   351,    14,    40,   417,    45,   351,   417,
   639,   315,   351,    14,   417,   647,   417,   315,   417,   417,
   108,   417,   417,   417,   417,   417,   574,   417,   417,   417,
   410,   351,   352,   340,   331,    17,    17,   331,   340,   352,
   376,   376,   376,   417,   352,   607,   417,   417,   352,   417,
   806,   417,   607,   607,   607,   417,   364,   607,   607,   607,
   353,   607,   639,   364,   353,   572,   572,   352,   203,   639,
   607,   607,   607,   607,   639,   572,   653,   410,   639,   653,
   222,   607,   607,   410,   607,   607,   607,   607,   607,   224,
   574,   574,   943,   411,   943,   943,   943,   639,   943,   377,
   574,   365,   366,   701,   378,   556,    41,    41,   365,   366,
   556,   607,   607,   607,   607,   607,   607,   607,   607,   607,
   607,   607,   607,   607,   607,   311,   311,   607,   607,   607,
   367,   607,   607,   379,   225,   607,   588,   367,   607,   607,
   411,   607,   775,   607,   712,   607,   411,   607,   607,   712,
   607,   607,   607,   607,   607,   701,   607,   607,   607,   380,
   335,   335,   701,   377,   377,   377,   383,   701,   378,   378,
   378,   701,   607,   229,   943,   607,   607,   607,   607,   608,
   607,   261,   607,   588,   607,   275,   608,   608,   608,   588,
   701,   608,   608,   608,   775,   608,    46,   379,   379,   379,
   276,   775,   368,    46,   369,   608,   608,   608,   122,   368,
   775,   369,    46,   122,   122,   608,   608,   279,   608,   608,
   608,   608,   608,   380,   380,   380,   219,   290,   298,   775,
   383,   383,   383,   219,   671,   298,   671,   671,   671,   291,
   671,   293,   219,   294,   298,   608,   608,   608,   608,   608,
   608,   608,   608,   608,   608,   608,   608,   608,   608,   465,
   791,   608,   608,   608,   295,   608,   608,   370,   301,   608,
   791,   671,   608,   608,   370,   608,   304,   608,   305,   608,
   671,   608,   608,   840,   608,   608,   608,   608,   608,   310,
   608,   465,   608,   840,   372,   465,   465,   826,   465,   465,
   312,   372,   826,   791,   791,   316,   608,   317,   791,   608,
   608,   608,   608,   320,   608,   325,   608,   329,   608,    33,
    33,    33,    33,    33,    33,   330,   840,   840,    33,    33,
   332,   840,    79,    33,   341,    33,    33,    33,    33,    33,
    33,    33,   912,   342,    79,   912,   346,    33,    33,    33,
    33,    33,    33,    33,    79,   348,    33,   521,   521,   685,
   685,   429,    33,    33,    33,    33,    33,    33,    33,    33,
    33,    33,    33,    33,   392,    33,    33,    33,   398,    33,
    33,    33,    33,    33,   429,   429,   429,   429,   429,   429,
   429,   429,   429,   429,   429,   299,   429,   429,   300,   400,
   429,   429,   299,    33,   403,   300,    33,   794,   794,    33,
    33,   299,   406,    33,   300,    33,   429,   408,   429,    33,
   429,   429,   409,   429,   429,   429,   429,   429,    33,   429,
   984,   984,   418,    33,    33,    33,    33,   302,    33,    33,
    33,    33,   426,   466,   302,   318,    33,    33,   436,   429,
    56,   429,   318,   302,    33,   448,    33,    56,    56,    56,
   449,   318,    56,    56,    56,   803,    56,   803,   803,   803,
   450,   803,   451,   476,   480,   466,    56,    56,    56,   466,
   466,   345,   466,   466,   495,   496,    56,    56,   345,    56,
    56,    56,    56,    56,   499,   501,   506,   345,   510,   446,
   519,   883,   803,   883,   883,   883,   520,   883,   565,   565,
   522,   803,   565,   565,   565,   534,    56,    56,    56,    56,
    56,    56,    56,    56,    56,    56,    56,    56,    56,    56,
   447,   446,    56,    56,    56,   446,   446,    56,   883,   539,
    56,   540,   569,    56,    56,   577,    56,   883,    56,   579,
    56,   585,    56,    56,   589,    56,    56,    56,    56,    56,
   594,    56,   447,    56,   599,   609,   447,   447,   355,   611,
   851,   616,   851,   851,   851,   355,   851,    56,   623,   505,
    56,    56,    56,    56,   355,    56,   505,    56,   120,   120,
   120,   120,   120,   120,   625,   505,   548,   120,   120,   630,
   633,   635,   120,   548,   120,   120,   120,   120,   120,   120,
   120,   638,   548,   641,   642,   645,   120,   120,   120,   120,
   120,   120,   120,   646,   966,   120,   966,   966,   966,   648,
   966,   120,   120,   120,   120,   120,   120,   120,   120,   120,
   120,   120,   120,   651,   120,   120,   120,   657,   120,   120,
   120,   120,   120,   444,   444,   444,   444,   444,   444,   444,
   444,   444,   444,   444,   640,   444,   444,   853,   658,   444,
   444,   640,   120,   660,   853,   120,   640,   661,   120,   120,
   640,   662,   120,   853,   120,   444,   669,   444,   120,   444,
   444,   677,   444,   444,   444,   444,   444,   120,   444,   681,
   684,   687,   120,   120,   120,   120,   692,   120,   120,   120,
   120,   695,   704,   709,   728,   120,   120,   205,   205,   205,
   205,   205,   205,   120,   733,   120,   205,   205,   751,   752,
   754,   205,   755,   205,   205,   205,   205,   205,   205,   205,
     6,     6,     6,     6,     6,   205,   205,   205,   205,   205,
   205,   205,   756,   758,   205,   289,   289,   289,   289,   289,
   205,   205,   205,   205,   205,   205,   205,   205,   205,   205,
   205,   205,   759,   205,   205,   205,   760,   205,   205,   205,
   205,   205,   445,   445,   445,   445,   445,   445,   445,   445,
   445,   445,   445,   702,   445,   445,   916,   761,   445,   445,
   702,   205,   765,   916,   205,   702,   769,   205,   205,   702,
   770,   205,   916,   205,   445,   774,   445,   205,   445,   445,
   778,   445,   445,   445,   445,   445,   205,   445,   781,   782,
   785,   205,   205,   205,   205,   790,   205,   205,   205,   205,
   805,   808,   813,   816,   205,   205,   228,   228,   228,   228,
   228,   228,   205,   825,   205,   228,   228,   829,   830,   833,
   228,   834,   228,   228,   228,   228,   228,   228,   228,   493,
   493,   493,   493,   493,   228,   228,   228,   228,   228,   228,
   228,   850,   885,   228,   885,   885,   885,   854,   885,   228,
   228,   228,   228,   228,   228,   228,   228,   228,   228,   228,
   228,   856,   228,   228,   228,   869,   228,   228,   228,   228,
   228,   455,   455,   455,   455,   455,   455,   455,   922,   885,
   455,   455,   923,   870,   874,   922,   875,   455,   455,   923,
   228,   877,   878,   228,   922,   880,   228,   228,   923,   882,
   228,   884,   228,   455,   890,   455,   228,   455,   455,   891,
   455,   455,   455,   455,   455,   228,   455,   897,   902,   905,
   228,   228,   228,   228,   906,   228,   228,   228,   228,   907,
   908,   910,   925,   228,   228,   292,   292,   292,   292,   292,
   292,   228,   928,   228,   292,   292,   929,   945,   967,   292,
   968,   292,   292,   292,   292,   292,   292,   292,   969,   976,
   977,   978,   979,   292,   292,   292,   292,   292,   292,   292,
   980,   944,   292,   944,   944,   944,   982,   944,   292,   292,
   292,   292,   292,   292,   292,   292,   292,   292,   292,   292,
   985,   292,   292,   292,   986,   292,   292,   292,   292,   292,
   456,   981,   987,   981,   981,   981,   988,   981,   944,   983,
   989,   983,   983,   983,   924,   983,   456,   456,   990,   292,
   993,   924,   292,  1004,  1014,   292,   292,  1015,  1016,   292,
   924,   292,   456,   nil,   456,   292,   456,   456,   981,   456,
   456,   nil,   nil,   456,   292,   456,   983,   nil,   nil,   292,
   292,   292,   292,   nil,   292,   292,   292,   292,   nil,   nil,
   nil,   nil,   292,   292,   297,   297,   297,   297,   297,   297,
   292,   nil,   292,   297,   297,   nil,   nil,   nil,   297,   nil,
   297,   297,   297,   297,   297,   297,   297,   nil,   nil,   nil,
   nil,   nil,   297,   297,   297,   297,   297,   297,   297,   nil,
  1003,   297,  1003,  1003,  1003,   nil,  1003,   297,   297,   297,
   297,   297,   297,   297,   297,   297,   297,   297,   297,   nil,
   297,   297,   297,   926,   297,   297,   297,   297,   297,   457,
   926,   973,   nil,   nil,   nil,   nil,   nil,  1003,   973,   926,
   nil,   nil,   nil,   nil,   nil,   457,   457,   973,   297,   nil,
   nil,   297,   nil,   nil,   297,   297,   nil,   nil,   297,   nil,
   297,   457,   nil,   457,   297,   457,   457,   nil,   457,   457,
   nil,   nil,   457,   297,   457,   nil,   nil,   nil,   297,   297,
   297,   297,   nil,   297,   297,   297,   297,   nil,   nil,   nil,
   nil,   297,   297,   323,   323,   323,   323,   323,   323,   297,
   nil,   297,   323,   323,   nil,   nil,   nil,   323,   nil,   323,
   323,   323,   323,   323,   323,   323,   nil,   nil,   nil,   nil,
   nil,   323,   323,   323,   323,   323,   323,   323,   nil,   nil,
   323,   nil,   nil,   nil,   nil,   405,   323,   323,   323,   323,
   323,   323,   323,   323,   323,   323,   323,   323,   nil,   323,
   323,   323,   nil,   323,   323,   323,   323,   323,   405,   405,
   405,   405,   405,   405,   405,   405,   405,   405,   405,   997,
   405,   405,   nil,   nil,   405,   405,   997,   323,   nil,   nil,
   323,   997,   nil,   323,   323,   997,   nil,   323,   nil,   323,
   405,   nil,   405,   323,   405,   405,   nil,   405,   405,   405,
   405,   405,   323,   405,   nil,   nil,   nil,   323,   323,   323,
   323,   nil,   323,   323,   323,   323,   nil,   nil,   nil,   nil,
   323,   323,   nil,   405,   421,   nil,   nil,   nil,   323,   nil,
   323,   421,   421,   421,   nil,   nil,   421,   421,   421,   458,
   421,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   421,
   421,   421,   421,   nil,   nil,   458,   458,   nil,   nil,   nil,
   421,   421,   nil,   421,   421,   421,   421,   421,   nil,   nil,
   nil,   458,   nil,   458,   nil,   458,   458,   nil,   458,   458,
   nil,   nil,   458,   nil,   458,   nil,   nil,   nil,   nil,   nil,
   421,   421,   421,   421,   421,   421,   421,   421,   421,   421,
   421,   421,   421,   421,   nil,   nil,   421,   421,   421,   nil,
   nil,   421,   nil,   nil,   421,   nil,   nil,   421,   421,   nil,
   421,   nil,   421,   nil,   421,   nil,   421,   421,   nil,   421,
   421,   421,   421,   421,   nil,   421,   421,   421,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   421,   nil,   nil,   421,   421,   421,   421,   422,   421,
   nil,   421,   nil,   nil,   nil,   422,   422,   422,   nil,   nil,
   422,   422,   422,   459,   422,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   422,   422,   422,   422,   nil,   nil,   459,
   459,   nil,   nil,   nil,   422,   422,   nil,   422,   422,   422,
   422,   422,   nil,   nil,   nil,   459,   nil,   459,   nil,   459,
   459,   nil,   459,   459,   nil,   nil,   459,   nil,   459,   nil,
   nil,   nil,   nil,   nil,   422,   422,   422,   422,   422,   422,
   422,   422,   422,   422,   422,   422,   422,   422,   nil,   nil,
   422,   422,   422,   nil,   nil,   422,   nil,   nil,   422,   nil,
   nil,   422,   422,   nil,   422,   nil,   422,   nil,   422,   nil,
   422,   422,   nil,   422,   422,   422,   422,   422,   nil,   422,
   422,   422,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   422,   nil,   nil,   422,   422,
   422,   422,   nil,   422,   nil,   422,   494,   494,   494,   494,
   494,   494,   nil,   nil,   nil,   494,   494,   nil,   nil,   nil,
   494,   nil,   494,   494,   494,   494,   494,   494,   494,   nil,
   nil,   nil,   nil,   nil,   494,   494,   494,   494,   494,   494,
   494,   nil,   nil,   494,   nil,   nil,   nil,   nil,   nil,   494,
   494,   494,   494,   494,   494,   494,   494,   494,   494,   494,
   494,   nil,   494,   494,   494,   nil,   494,   494,   494,   494,
   494,   460,   460,   460,   460,   460,   460,   460,   nil,   nil,
   460,   460,   nil,   nil,   nil,   nil,   nil,   460,   460,   nil,
   494,   nil,   nil,   494,   nil,   nil,   494,   494,   nil,   nil,
   494,   nil,   494,   460,   nil,   460,   494,   460,   460,   nil,
   460,   460,   460,   460,   460,   494,   460,   nil,   nil,   nil,
   494,   494,   494,   494,   nil,   494,   494,   494,   494,   nil,
   nil,   nil,   nil,   494,   494,   533,   533,   533,   533,   533,
   533,   494,   nil,   494,   533,   533,   nil,   nil,   nil,   533,
   nil,   533,   533,   533,   533,   533,   533,   533,   nil,   nil,
   nil,   nil,   nil,   533,   533,   533,   533,   533,   533,   533,
   nil,   nil,   533,   nil,   nil,   nil,   nil,   nil,   533,   533,
   533,   533,   533,   533,   533,   533,   533,   533,   533,   533,
   nil,   533,   533,   533,   nil,   533,   533,   533,   533,   533,
   461,   461,   461,   461,   461,   461,   461,   nil,   nil,   461,
   461,   nil,   nil,   nil,   nil,   nil,   461,   461,   nil,   533,
   nil,   nil,   533,   nil,   nil,   533,   533,   nil,   nil,   533,
   nil,   533,   461,   nil,   461,   533,   461,   461,   nil,   461,
   461,   461,   461,   461,   533,   461,   nil,   nil,   nil,   533,
   533,   533,   533,   nil,   533,   533,   533,   533,   nil,   nil,
   nil,   nil,   533,   533,   536,   536,   536,   536,   536,   536,
   533,   nil,   533,   536,   536,   nil,   nil,   nil,   536,   nil,
   536,   536,   536,   536,   536,   536,   536,   nil,   nil,   nil,
   nil,   nil,   536,   536,   536,   536,   536,   536,   536,   nil,
   nil,   536,   nil,   nil,   nil,   nil,   nil,   536,   536,   536,
   536,   536,   536,   536,   536,   536,   536,   536,   536,   nil,
   536,   536,   536,   nil,   536,   536,   536,   536,   536,   462,
   462,   462,   462,   462,   462,   462,   nil,   nil,   462,   462,
   nil,   nil,   nil,   nil,   nil,   462,   462,   nil,   536,   nil,
   nil,   536,   nil,   nil,   536,   536,   nil,   nil,   536,   nil,
   536,   462,   nil,   462,   536,   462,   462,   nil,   462,   462,
   462,   462,   462,   536,   462,   nil,   nil,   nil,   536,   536,
   536,   536,   nil,   536,   536,   536,   536,   nil,   nil,   nil,
   nil,   536,   536,   558,   558,   558,   558,   558,   558,   536,
   nil,   536,   558,   558,   nil,   nil,   nil,   558,   nil,   558,
   558,   558,   558,   558,   558,   558,   nil,   nil,   nil,   nil,
   nil,   558,   558,   558,   558,   558,   558,   558,   nil,   nil,
   558,   nil,   nil,   nil,   nil,   nil,   558,   558,   558,   558,
   558,   558,   558,   558,   558,   558,   558,   558,   nil,   558,
   558,   558,   nil,   558,   558,   558,   558,   558,   463,   463,
   463,   463,   463,   463,   463,   nil,   nil,   463,   463,   nil,
   nil,   nil,   nil,   nil,   463,   463,   nil,   558,   nil,   nil,
   558,   nil,   nil,   558,   558,   nil,   nil,   558,   nil,   558,
   463,   nil,   463,   558,   463,   463,   nil,   463,   463,   463,
   463,   463,   558,   463,   nil,   nil,   nil,   558,   558,   558,
   558,   nil,   558,   558,   558,   558,   nil,   nil,   nil,   nil,
   558,   558,   615,   615,   615,   615,   615,   615,   558,   nil,
   558,   615,   615,   nil,   nil,   nil,   615,   nil,   615,   615,
   615,   615,   615,   615,   615,   nil,   nil,   nil,   nil,   nil,
   615,   615,   615,   615,   615,   615,   615,   nil,   nil,   615,
   nil,   nil,   nil,   nil,   nil,   615,   615,   615,   615,   615,
   615,   615,   615,   615,   615,   615,   615,   nil,   615,   615,
   615,   nil,   615,   615,   615,   615,   615,   464,   464,   464,
   464,   464,   464,   464,   nil,   nil,   464,   464,   nil,   nil,
   nil,   nil,   nil,   464,   464,   nil,   615,   nil,   nil,   615,
   nil,   nil,   615,   615,   nil,   nil,   615,   nil,   615,   464,
   nil,   464,   615,   464,   464,   nil,   464,   464,   464,   464,
   464,   615,   464,   nil,   nil,   nil,   615,   615,   615,   615,
   nil,   615,   615,   615,   615,   nil,   nil,   nil,   nil,   615,
   615,   620,   620,   620,   620,   620,   620,   615,   nil,   615,
   620,   620,   nil,   nil,   nil,   620,   nil,   620,   620,   620,
   620,   620,   620,   620,   nil,   nil,   nil,   nil,   nil,   620,
   620,   620,   620,   620,   620,   620,   nil,   nil,   620,   nil,
   nil,   nil,   nil,   nil,   620,   620,   620,   620,   620,   620,
   620,   620,   620,   620,   620,   620,   nil,   620,   620,   620,
   nil,   620,   620,   620,   620,   620,   467,   467,   467,   467,
   467,   467,   467,   nil,   nil,   467,   467,   nil,   nil,   nil,
   nil,   nil,   467,   467,   nil,   620,   nil,   nil,   620,   nil,
   nil,   620,   620,   nil,   nil,   620,   nil,   620,   467,   nil,
   467,   620,   467,   467,   nil,   467,   467,   467,   467,   467,
   620,   467,   nil,   nil,   nil,   620,   620,   620,   620,   nil,
   620,   620,   620,   620,   nil,   nil,   nil,   nil,   620,   620,
   621,   621,   621,   621,   621,   621,   620,   nil,   620,   621,
   621,   nil,   nil,   nil,   621,   nil,   621,   621,   621,   621,
   621,   621,   621,   nil,   nil,   nil,   nil,   nil,   621,   621,
   621,   621,   621,   621,   621,   nil,   nil,   621,   nil,   nil,
   nil,   nil,   nil,   621,   621,   621,   621,   621,   621,   621,
   621,   621,   621,   621,   621,   nil,   621,   621,   621,   nil,
   621,   621,   621,   621,   621,   468,   468,   468,   468,   468,
   468,   468,   468,   nil,   468,   468,   nil,   nil,   nil,   nil,
   nil,   468,   468,   nil,   621,   nil,   nil,   621,   nil,   nil,
   621,   621,   nil,   nil,   621,   nil,   621,   468,   nil,   468,
   621,   468,   468,   nil,   468,   468,   468,   468,   468,   621,
   468,   nil,   nil,   nil,   621,   621,   621,   621,   nil,   621,
   621,   621,   621,   nil,   nil,   nil,   nil,   621,   621,   705,
   705,   705,   705,   705,   705,   621,   nil,   621,   705,   705,
   nil,   nil,   nil,   705,   nil,   705,   705,   705,   705,   705,
   705,   705,   nil,   nil,   nil,   nil,   nil,   705,   705,   705,
   705,   705,   705,   705,   nil,   nil,   705,   nil,   nil,   nil,
   nil,   nil,   705,   705,   705,   705,   705,   705,   705,   705,
   705,   705,   705,   705,   nil,   705,   705,   705,   nil,   705,
   705,   705,   705,   705,   452,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   452,   452,   nil,   705,   nil,   nil,   705,   nil,   nil,   705,
   705,   nil,   nil,   705,   nil,   705,   452,   nil,   452,   705,
   452,   452,   nil,   452,   452,   nil,   nil,   nil,   705,   nil,
   nil,   nil,   nil,   705,   705,   705,   705,   nil,   705,   705,
   705,   705,   nil,   nil,   nil,   nil,   705,   705,   710,   710,
   710,   710,   710,   710,   705,   nil,   705,   710,   710,   nil,
   nil,   nil,   710,   nil,   710,   710,   710,   710,   710,   710,
   710,   nil,   nil,   nil,   nil,   nil,   710,   710,   710,   710,
   710,   710,   710,   nil,   nil,   710,   nil,   nil,   nil,   nil,
   nil,   710,   710,   710,   710,   710,   710,   710,   710,   710,
   710,   710,   710,   nil,   710,   710,   710,   nil,   710,   710,
   710,   710,   710,   453,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   453,
   453,   nil,   710,   nil,   nil,   710,   nil,   nil,   710,   710,
   nil,   nil,   710,   nil,   710,   453,   nil,   453,   710,   453,
   453,   nil,   453,   453,   nil,   nil,   nil,   710,   nil,   nil,
   nil,   nil,   710,   710,   710,   710,   nil,   710,   710,   710,
   710,   nil,   nil,   nil,   nil,   710,   710,   720,   720,   720,
   720,   720,   720,   710,   nil,   710,   720,   720,   nil,   nil,
   nil,   720,   nil,   720,   720,   720,   720,   720,   720,   720,
   nil,   nil,   nil,   nil,   nil,   720,   720,   720,   720,   720,
   720,   720,   nil,   nil,   720,   nil,   nil,   nil,   nil,   nil,
   720,   720,   720,   720,   720,   720,   720,   720,   720,   720,
   720,   720,   nil,   720,   720,   720,   nil,   720,   720,   720,
   720,   720,   454,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   454,   454,
   nil,   720,   nil,   nil,   720,   nil,   nil,   720,   720,   nil,
   nil,   720,   nil,   720,   454,   nil,   nil,   720,   454,   454,
   nil,   454,   454,   nil,   nil,   nil,   720,   nil,   nil,   nil,
   nil,   720,   720,   720,   720,   nil,   720,   720,   720,   720,
   nil,   nil,   nil,   nil,   720,   720,   768,   768,   768,   768,
   768,   768,   720,   nil,   720,   768,   768,   nil,   nil,   nil,
   768,   nil,   768,   768,   768,   768,   768,   768,   768,   nil,
   nil,   nil,   nil,   nil,   768,   768,   768,   768,   768,   768,
   768,   nil,   nil,   768,   nil,   nil,   nil,   nil,   nil,   768,
   768,   768,   768,   768,   768,   768,   768,   768,   768,   768,
   768,   nil,   768,   768,   768,   nil,   768,   768,   768,   768,
   768,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   768,   nil,   nil,   768,   nil,   nil,   768,   768,   nil,   nil,
   768,   nil,   768,   nil,   nil,   nil,   768,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   768,   nil,   nil,   nil,   nil,
   768,   768,   768,   768,   nil,   768,   768,   768,   768,   nil,
   nil,   nil,   nil,   768,   768,   780,   780,   780,   780,   780,
   780,   768,   nil,   768,   780,   780,   nil,   nil,   nil,   780,
   nil,   780,   780,   780,   780,   780,   780,   780,   nil,   nil,
   nil,   nil,   nil,   780,   780,   780,   780,   780,   780,   780,
   nil,   nil,   780,   nil,   nil,   nil,   nil,   nil,   780,   780,
   780,   780,   780,   780,   780,   780,   780,   780,   780,   780,
   nil,   780,   780,   780,   nil,   780,   780,   780,   780,   780,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   780,
   nil,   nil,   780,   nil,   nil,   780,   780,   nil,   nil,   780,
   nil,   780,   nil,   nil,   nil,   780,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   780,   nil,   nil,   nil,   nil,   780,
   780,   780,   780,   nil,   780,   780,   780,   780,   nil,   nil,
   nil,   nil,   780,   780,   817,   817,   817,   817,   817,   817,
   780,   nil,   780,   817,   817,   nil,   nil,   nil,   817,   nil,
   817,   817,   817,   817,   817,   817,   817,   nil,   nil,   nil,
   nil,   nil,   817,   817,   817,   817,   817,   817,   817,   nil,
   nil,   817,   nil,   nil,   nil,   nil,   nil,   817,   817,   817,
   817,   817,   817,   817,   817,   817,   817,   817,   817,   nil,
   817,   817,   817,   nil,   817,   817,   817,   817,   817,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   817,   nil,
   nil,   817,   nil,   nil,   817,   817,   nil,   nil,   817,   nil,
   817,   nil,   nil,   nil,   817,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   817,   nil,   nil,   nil,   nil,   817,   817,
   817,   817,   nil,   817,   817,   817,   817,   nil,   nil,   nil,
   nil,   817,   817,   818,   818,   818,   818,   818,   818,   817,
   nil,   817,   818,   818,   nil,   nil,   nil,   818,   nil,   818,
   818,   818,   818,   818,   818,   818,   nil,   nil,   nil,   nil,
   nil,   818,   818,   818,   818,   818,   818,   818,   nil,   nil,
   818,   nil,   nil,   nil,   nil,   nil,   818,   818,   818,   818,
   818,   818,   818,   818,   818,   818,   818,   818,   nil,   818,
   818,   818,   nil,   818,   818,   818,   818,   818,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   818,   nil,   nil,
   818,   nil,   nil,   818,   818,   nil,   nil,   818,   nil,   818,
   nil,   nil,   nil,   818,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   818,   nil,   nil,   nil,   nil,   818,   818,   818,
   818,   nil,   818,   818,   818,   818,   nil,   nil,   nil,   nil,
   818,   818,   821,   821,   821,   821,   821,   821,   818,   nil,
   818,   821,   821,   nil,   nil,   nil,   821,   nil,   821,   821,
   821,   821,   821,   821,   821,   nil,   nil,   nil,   nil,   nil,
   821,   821,   821,   821,   821,   821,   821,   nil,   nil,   821,
   nil,   nil,   nil,   nil,   nil,   821,   821,   821,   821,   821,
   821,   821,   821,   821,   821,   821,   821,   nil,   821,   821,
   821,   nil,   821,   821,   821,   821,   821,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   821,   nil,   nil,   821,
   nil,   nil,   821,   821,   nil,   nil,   821,   nil,   821,   nil,
   nil,   nil,   821,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   821,   nil,   nil,   nil,   nil,   821,   821,   821,   821,
   nil,   821,   821,   821,   821,   nil,   nil,   nil,   nil,   821,
   821,   827,   827,   827,   827,   827,   827,   821,   nil,   821,
   827,   827,   nil,   nil,   nil,   827,   nil,   827,   827,   827,
   827,   827,   827,   827,   nil,   nil,   nil,   nil,   nil,   827,
   827,   827,   827,   827,   827,   827,   nil,   nil,   827,   nil,
   nil,   nil,   nil,   nil,   827,   827,   827,   827,   827,   827,
   827,   827,   827,   827,   827,   827,   nil,   827,   827,   827,
   nil,   827,   827,   827,   827,   827,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   827,   nil,   nil,   827,   nil,
   nil,   827,   827,   nil,   nil,   827,   nil,   827,   nil,   nil,
   nil,   827,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   827,   nil,   nil,   nil,   nil,   827,   827,   827,   827,   nil,
   827,   827,   827,   827,   nil,   nil,   nil,   nil,   827,   827,
   860,   860,   860,   860,   860,   860,   827,   nil,   827,   860,
   860,   nil,   nil,   nil,   860,   nil,   860,   860,   860,   860,
   860,   860,   860,   nil,   nil,   nil,   nil,   nil,   860,   860,
   860,   860,   860,   860,   860,   nil,   nil,   860,   nil,   nil,
   nil,   nil,   nil,   860,   860,   860,   860,   860,   860,   860,
   860,   860,   860,   860,   860,   nil,   860,   860,   860,   nil,
   860,   860,   860,   860,   860,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   860,   nil,   nil,   860,   nil,   nil,
   860,   860,   nil,   nil,   860,   nil,   860,   nil,   nil,   nil,
   860,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   860,
   nil,   nil,   nil,   nil,   860,   860,   860,   860,   nil,   860,
   860,   860,   860,   nil,   nil,   nil,   nil,   860,   860,   867,
   867,   867,   867,   867,   867,   860,   nil,   860,   867,   867,
   nil,   nil,   nil,   867,   nil,   867,   867,   867,   867,   867,
   867,   867,   nil,   nil,   nil,   nil,   nil,   867,   867,   867,
   867,   867,   867,   867,   nil,   nil,   867,   nil,   nil,   nil,
   nil,   nil,   867,   867,   867,   867,   867,   867,   867,   867,
   867,   867,   867,   867,   nil,   867,   867,   867,   nil,   867,
   867,   867,   867,   867,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   867,   nil,   nil,   867,   nil,   nil,   867,
   867,   nil,   nil,   867,   nil,   867,   nil,   nil,   nil,   867,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   867,   nil,
   nil,   nil,   nil,   867,   867,   867,   867,   nil,   867,   867,
   867,   867,   nil,   nil,   nil,   nil,   867,   867,   868,   868,
   868,   868,   868,   868,   867,   nil,   867,   868,   868,   nil,
   nil,   nil,   868,   nil,   868,   868,   868,   868,   868,   868,
   868,   nil,   nil,   nil,   nil,   nil,   868,   868,   868,   868,
   868,   868,   868,   nil,   nil,   868,   nil,   nil,   nil,   nil,
   nil,   868,   868,   868,   868,   868,   868,   868,   868,   868,
   868,   868,   868,   nil,   868,   868,   868,   nil,   868,   868,
   868,   868,   868,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   868,   nil,   nil,   868,   nil,   nil,   868,   868,
   nil,   nil,   868,   nil,   868,   nil,   nil,   nil,   868,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   868,   nil,   nil,
   nil,   nil,   868,   868,   868,   868,   nil,   868,   868,   868,
   868,   nil,   nil,   nil,   nil,   868,   868,   921,   921,   921,
   921,   921,   921,   868,   nil,   868,   921,   921,   nil,   nil,
   nil,   921,   nil,   921,   921,   921,   921,   921,   921,   921,
   nil,   nil,   nil,   nil,   nil,   921,   921,   921,   921,   921,
   921,   921,   nil,   nil,   921,   nil,   nil,   nil,   nil,   nil,
   921,   921,   921,   921,   921,   921,   921,   921,   921,   921,
   921,   921,   nil,   921,   921,   921,   nil,   921,   921,   921,
   921,   921,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   921,   nil,   nil,   921,   nil,   nil,   921,   921,   nil,
   nil,   921,   nil,   921,   nil,   nil,   nil,   921,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   921,   nil,   nil,   nil,
   nil,   921,   921,   921,   921,   nil,   921,   921,   921,   921,
   nil,   nil,   nil,   nil,   921,   921,   946,   946,   946,   946,
   946,   946,   921,   nil,   921,   946,   946,   nil,   nil,   nil,
   946,   nil,   946,   946,   946,   946,   946,   946,   946,   nil,
   nil,   nil,   nil,   nil,   946,   946,   946,   946,   946,   946,
   946,   nil,   nil,   946,   nil,   nil,   nil,   nil,   nil,   946,
   946,   946,   946,   946,   946,   946,   946,   946,   946,   946,
   946,   nil,   946,   946,   946,   nil,   946,   946,   946,   946,
   946,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   946,   nil,   nil,   946,   nil,   nil,   946,   946,   nil,   nil,
   946,   nil,   946,   nil,   nil,   nil,   946,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   946,   nil,   nil,   nil,   nil,
   946,   946,   946,   946,   nil,   946,   946,   946,   946,   nil,
   nil,   nil,   nil,   946,   946,   952,   952,   952,   952,   952,
   952,   946,   nil,   946,   952,   952,   nil,   nil,   nil,   952,
   nil,   952,   952,   952,   952,   952,   952,   952,   nil,   nil,
   nil,   nil,   nil,   952,   952,   952,   952,   952,   952,   952,
   nil,   nil,   952,   nil,   nil,   nil,   nil,   nil,   952,   952,
   952,   952,   952,   952,   952,   952,   952,   952,   952,   952,
   nil,   952,   952,   952,   nil,   952,   952,   952,   952,   952,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   952,
   nil,   nil,   952,   nil,   nil,   952,   952,   nil,   nil,   952,
   nil,   952,   nil,   nil,   nil,   952,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   952,   nil,   nil,   nil,   nil,   952,
   952,   952,   952,   nil,   952,   952,   952,   952,   nil,   nil,
   nil,   nil,   952,   952,   954,   954,   954,   954,   954,   954,
   952,   nil,   952,   954,   954,   nil,   nil,   nil,   954,   nil,
   954,   954,   954,   954,   954,   954,   954,   nil,   nil,   nil,
   nil,   nil,   954,   954,   954,   954,   954,   954,   954,   nil,
   nil,   954,   nil,   nil,   nil,   nil,   nil,   954,   954,   954,
   954,   954,   954,   954,   954,   954,   954,   954,   954,   nil,
   954,   954,   954,   nil,   954,   954,   954,   954,   954,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   954,   nil,
   nil,   954,   nil,   nil,   954,   954,   nil,   nil,   954,   nil,
   954,   nil,   nil,   nil,   954,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   954,   nil,   nil,   nil,   nil,   954,   954,
   954,   954,   nil,   954,   954,   954,   954,   nil,   nil,   nil,
   nil,   954,   954,   nil,     5,     5,     5,     5,     5,   954,
   nil,   954,     5,     5,   nil,   nil,   nil,     5,   nil,     5,
     5,     5,     5,     5,     5,     5,   nil,   nil,   nil,   nil,
   nil,     5,     5,     5,     5,     5,     5,     5,   nil,   nil,
     5,   nil,   nil,   nil,   nil,   nil,     5,     5,     5,     5,
     5,     5,     5,     5,     5,     5,     5,     5,   nil,     5,
     5,     5,   nil,     5,     5,     5,     5,     5,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,     5,   nil,   nil,
     5,   nil,   nil,     5,     5,   nil,   nil,     5,   nil,     5,
   nil,   nil,   nil,     5,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,     5,   nil,   nil,   nil,   nil,     5,     5,     5,
     5,   nil,     5,     5,     5,     5,   nil,   nil,   nil,   nil,
     5,     5,   nil,    20,    20,    20,   nil,    20,     5,   nil,
     5,    20,    20,   nil,   nil,   nil,    20,   nil,    20,    20,
    20,    20,    20,    20,    20,   nil,   nil,   nil,   nil,   nil,
    20,    20,    20,    20,    20,    20,    20,   nil,   nil,    20,
   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,    20,
    20,    20,    20,    20,    20,    20,    20,   nil,    20,    20,
    20,   nil,    20,    20,    20,    20,    20,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,    20,
   nil,   nil,    20,    20,   nil,   nil,    20,   nil,   nil,   nil,
   nil,   nil,    20,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    20,   nil,   nil,   nil,   nil,    20,    20,    20,    20,
   nil,    20,    20,    20,    20,   nil,   nil,   nil,   nil,    20,
    20,   nil,    29,    29,    29,   nil,    29,    20,   nil,    20,
    29,    29,   nil,   nil,   nil,    29,   nil,    29,    29,    29,
    29,    29,    29,    29,   nil,   nil,   nil,   nil,   nil,    29,
    29,    29,    29,    29,    29,    29,   nil,   nil,    29,   nil,
   nil,   nil,   nil,   nil,   nil,    29,   nil,   nil,    29,    29,
    29,    29,    29,    29,    29,    29,    29,    29,    29,    29,
   nil,    29,    29,    29,    29,    29,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    29,   nil,   nil,    29,   nil,
   nil,    29,    29,   nil,   nil,    29,   nil,    29,   nil,    29,
   nil,    29,   nil,   nil,    29,   nil,   nil,   nil,   nil,   nil,
    29,   nil,   nil,   nil,   nil,    29,    29,    29,    29,   nil,
    29,    29,    29,    29,   nil,   nil,   nil,   nil,    29,    29,
   nil,    30,    30,    30,   nil,    30,    29,   nil,    29,    30,
    30,   nil,   nil,   nil,    30,   nil,    30,    30,    30,    30,
    30,    30,    30,   nil,   nil,   nil,   nil,   nil,    30,    30,
    30,    30,    30,    30,    30,   nil,   nil,    30,   nil,   nil,
   nil,   nil,   nil,   nil,    30,   nil,   nil,    30,    30,    30,
    30,    30,    30,    30,    30,    30,    30,    30,    30,   nil,
    30,    30,    30,    30,    30,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    30,   nil,   nil,    30,   nil,   nil,
    30,    30,   nil,   nil,    30,   nil,    30,   nil,    30,   nil,
    30,   nil,   nil,    30,   nil,   nil,   nil,   nil,   nil,    30,
   nil,   nil,   nil,   nil,    30,    30,    30,    30,   nil,    30,
    30,    30,    30,   nil,   nil,   nil,   nil,    30,    30,   nil,
    31,    31,    31,   nil,    31,    30,   nil,    30,    31,    31,
   nil,   nil,   nil,    31,   nil,    31,    31,    31,    31,    31,
    31,    31,   nil,   nil,   nil,   nil,   nil,    31,    31,    31,
    31,    31,    31,    31,   nil,   nil,    31,   nil,   nil,   nil,
   nil,   nil,   nil,    31,   nil,   nil,    31,    31,    31,    31,
    31,    31,    31,    31,    31,    31,    31,    31,   nil,    31,
    31,    31,    31,    31,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    31,   nil,   nil,    31,   nil,   nil,    31,
    31,   nil,   nil,    31,   nil,    31,   nil,    31,   nil,    31,
   nil,   nil,    31,   nil,   nil,   nil,   nil,   nil,    31,   nil,
   nil,   nil,   nil,    31,    31,    31,    31,   nil,    31,    31,
    31,    31,   nil,   nil,   nil,   nil,    31,    31,   nil,    34,
    34,    34,   nil,    34,    31,   nil,    31,    34,    34,   nil,
   nil,   nil,    34,   nil,    34,    34,    34,    34,    34,    34,
    34,   nil,   nil,   nil,   nil,   nil,    34,    34,    34,    34,
    34,    34,    34,   nil,   nil,    34,   nil,   nil,   nil,   nil,
   nil,   nil,    34,   nil,   nil,    34,    34,    34,    34,    34,
    34,    34,    34,   nil,    34,    34,    34,   nil,    34,    34,
   nil,   nil,    34,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    34,   nil,   nil,    34,   nil,   nil,    34,    34,
   nil,   nil,    34,   nil,    34,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    34,    34,    34,    34,   nil,    34,    34,    34,
    34,   nil,   nil,   nil,   nil,    34,    34,   nil,    35,    35,
    35,   nil,    35,    34,   nil,    34,    35,    35,   nil,   nil,
   nil,    35,   nil,    35,    35,    35,    35,    35,    35,    35,
   nil,   nil,   nil,   nil,   nil,    35,    35,    35,    35,    35,
    35,    35,   nil,   nil,    35,   nil,   nil,   nil,   nil,   610,
   nil,    35,   nil,   nil,    35,    35,    35,    35,    35,    35,
    35,    35,   nil,    35,    35,    35,   nil,    35,    35,   nil,
   nil,    35,   610,   610,   610,   610,   610,   610,   610,   610,
   610,   610,   610,   nil,   610,   610,   nil,   nil,   610,   610,
   nil,    35,   nil,   nil,    35,   nil,   nil,    35,    35,   nil,
   nil,    35,   nil,   nil,   610,   nil,   610,   nil,   610,   610,
   nil,   610,   610,   610,   610,   610,   nil,   610,   nil,   nil,
   nil,    35,    35,    35,    35,   nil,    35,    35,    35,    35,
   nil,   nil,   nil,   nil,    35,    35,   nil,   610,   nil,    35,
   nil,   nil,    35,   nil,    35,    42,    42,    42,   nil,    42,
   nil,   nil,   nil,    42,    42,   nil,   nil,   nil,    42,   nil,
    42,    42,    42,    42,    42,    42,    42,   nil,   nil,   nil,
   nil,   nil,    42,    42,    42,    42,    42,    42,    42,   nil,
   nil,    42,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    42,    42,    42,    42,    42,    42,    42,    42,   nil,
    42,    42,    42,   nil,    42,    42,    42,    42,    42,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    42,   nil,   nil,    42,    42,   nil,   nil,    42,   nil,
   nil,   nil,   nil,   nil,    42,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,   nil,   nil,    42,    42,
    42,    42,   nil,    42,    42,    42,    42,   nil,   nil,   nil,
   nil,    42,    42,   nil,    43,    43,    43,   nil,    43,    42,
   nil,    42,    43,    43,   nil,   nil,   nil,    43,   nil,    43,
    43,    43,    43,    43,    43,    43,   nil,   nil,   nil,   nil,
   nil,    43,    43,    43,    43,    43,    43,    43,   nil,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,
    43,    43,    43,    43,    43,    43,    43,    43,   nil,    43,
    43,    43,   nil,    43,    43,    43,    43,    43,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,
    43,   nil,   nil,    43,    43,   nil,   nil,    43,   nil,   nil,
   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    43,   nil,   nil,   nil,   nil,    43,    43,    43,
    43,   nil,    43,    43,    43,    43,   nil,   nil,   nil,   nil,
    43,    43,   nil,    44,    44,    44,   nil,    44,    43,   nil,
    43,    44,    44,   nil,   nil,   nil,    44,   nil,    44,    44,
    44,    44,    44,    44,    44,   nil,   nil,   nil,   nil,   nil,
    44,    44,    44,    44,    44,    44,    44,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,    44,
    44,    44,    44,    44,    44,    44,    44,   nil,    44,    44,
    44,   nil,    44,    44,    44,    44,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,    44,
   nil,   nil,    44,    44,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,    44,    44,    44,    44,
   nil,    44,    44,    44,    44,   nil,   nil,   nil,   nil,    44,
    44,   nil,    59,    59,    59,   nil,    59,    44,   nil,    44,
    59,    59,   nil,   nil,   nil,    59,   nil,    59,    59,    59,
    59,    59,    59,    59,   nil,   nil,   nil,   nil,   nil,    59,
    59,    59,    59,    59,    59,    59,   nil,   nil,    59,   nil,
   nil,   nil,   nil,   nil,   nil,    59,   nil,   nil,    59,    59,
    59,    59,    59,    59,    59,    59,    59,    59,    59,    59,
   nil,    59,    59,    59,    59,    59,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    59,   nil,   nil,    59,   nil,
   nil,    59,    59,   nil,   nil,    59,   nil,    59,   nil,   nil,
   nil,    59,   nil,   nil,    59,   nil,   nil,   nil,   nil,   nil,
    59,   nil,   nil,   nil,   nil,    59,    59,    59,    59,   nil,
    59,    59,    59,    59,   nil,   nil,   nil,   nil,    59,    59,
   nil,    60,    60,    60,   nil,    60,    59,   nil,    59,    60,
    60,   nil,   nil,   nil,    60,   nil,    60,    60,    60,    60,
    60,    60,    60,   nil,   nil,   nil,   nil,   nil,    60,    60,
    60,    60,    60,    60,    60,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,   nil,    60,   nil,   nil,    60,    60,    60,
    60,    60,    60,    60,    60,    60,    60,    60,    60,   nil,
    60,    60,    60,    60,    60,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    60,   nil,   nil,    60,   nil,   nil,
    60,    60,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    60,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    60,
   nil,   nil,   nil,   nil,    60,    60,    60,    60,   nil,    60,
    60,    60,    60,   nil,   nil,   nil,   nil,    60,    60,   nil,
    63,    63,    63,   nil,    63,    60,   nil,    60,    63,    63,
   nil,   nil,   nil,    63,   nil,    63,    63,    63,    63,    63,
    63,    63,   nil,   nil,   nil,   nil,   nil,    63,    63,    63,
    63,    63,    63,    63,   nil,   nil,    63,   nil,   nil,   nil,
   nil,   nil,   nil,    63,   nil,   nil,    63,    63,    63,    63,
    63,    63,    63,    63,   nil,    63,    63,    63,   nil,    63,
    63,    63,    63,    63,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    63,   nil,   nil,    63,   nil,   nil,    63,
    63,   nil,   nil,    63,   nil,   nil,   nil,   nil,   nil,    63,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    63,   nil,
   nil,   nil,   nil,    63,    63,    63,    63,   nil,    63,    63,
    63,    63,   nil,   nil,   nil,   nil,    63,    63,   nil,    64,
    64,    64,   nil,    64,    63,   nil,    63,    64,    64,   nil,
   nil,   nil,    64,   nil,    64,    64,    64,    64,    64,    64,
    64,   nil,   nil,   nil,   nil,   nil,    64,    64,    64,    64,
    64,    64,    64,   nil,   nil,    64,   nil,   nil,   nil,   nil,
   nil,   nil,    64,   nil,   nil,    64,    64,    64,    64,    64,
    64,    64,    64,   nil,    64,    64,    64,   nil,    64,    64,
    64,    64,    64,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    64,   nil,   nil,    64,   nil,   nil,    64,    64,
   nil,   nil,    64,   nil,   nil,   nil,   nil,   nil,    64,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    64,   nil,   nil,
   nil,   nil,    64,    64,    64,    64,   nil,    64,    64,    64,
    64,   nil,   nil,   nil,   nil,    64,    64,   nil,    67,    67,
    67,   nil,    67,    64,   nil,    64,    67,    67,   nil,   nil,
   nil,    67,   nil,    67,    67,    67,    67,    67,    67,    67,
   nil,   nil,   nil,   nil,   nil,    67,    67,    67,    67,    67,
    67,    67,   nil,   nil,    67,   nil,   nil,   nil,   nil,   nil,
   nil,    67,   nil,   nil,    67,    67,    67,    67,    67,    67,
    67,    67,   nil,    67,    67,    67,   nil,    67,    67,    67,
    67,    67,    21,    21,    21,    21,    21,    21,    21,    21,
    21,    21,    21,   nil,    21,    21,   nil,   nil,    21,    21,
   nil,    67,   nil,   nil,    67,   nil,   nil,    67,    67,   nil,
   nil,    67,   nil,   nil,    21,   nil,    21,    67,    21,    21,
   nil,    21,    21,    21,    21,    21,    67,    21,   nil,   nil,
   nil,    67,    67,    67,    67,   nil,    67,    67,    67,    67,
   nil,   nil,   nil,   nil,    67,    67,    67,    21,   nil,   nil,
   nil,    67,    67,   nil,    67,    68,    68,    68,   nil,    68,
   nil,   nil,   nil,    68,    68,   nil,   nil,   nil,    68,   nil,
    68,    68,    68,    68,    68,    68,    68,   nil,   nil,   nil,
   nil,   nil,    68,    68,    68,    68,    68,    68,    68,   nil,
   nil,    68,   nil,   nil,   nil,   nil,   nil,   nil,    68,   nil,
   nil,    68,    68,    68,    68,    68,    68,    68,    68,   nil,
    68,    68,    68,   nil,    68,    68,   nil,   nil,    68,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    68,   nil,
   nil,    68,   nil,   nil,    68,    68,   nil,   nil,    68,   nil,
    68,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    68,    68,
    68,    68,   nil,    68,    68,    68,    68,   nil,   nil,   nil,
   nil,    68,    68,   nil,    69,    69,    69,   nil,    69,    68,
   nil,    68,    69,    69,   nil,   nil,   nil,    69,   nil,    69,
    69,    69,    69,    69,    69,    69,   nil,   nil,   nil,   nil,
   nil,    69,    69,    69,    69,    69,    69,    69,   nil,   nil,
    69,   nil,   nil,   nil,   nil,   nil,   nil,    69,   nil,   nil,
    69,    69,    69,    69,    69,    69,    69,    69,   nil,    69,
    69,    69,   nil,    69,    69,   nil,   nil,    69,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    69,   nil,   nil,    69,   nil,   nil,
    69,   nil,   nil,    69,    69,   nil,   nil,    69,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    69,    69,    69,
    69,   nil,    69,    69,    69,    69,   nil,   nil,   nil,   nil,
    69,    69,   nil,    70,    70,    70,   nil,    70,    69,   nil,
    69,    70,    70,   nil,   nil,   nil,    70,   nil,    70,    70,
    70,    70,    70,    70,    70,   nil,   nil,   nil,   nil,   nil,
    70,    70,    70,    70,    70,    70,    70,   nil,   nil,    70,
   nil,   nil,   nil,   nil,   nil,   nil,    70,   nil,   nil,    70,
    70,    70,    70,    70,    70,    70,    70,   nil,    70,    70,
    70,   nil,    70,    70,   nil,   nil,    70,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    70,   nil,   nil,    70,
   nil,   nil,    70,    70,   nil,   nil,    70,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    70,    70,    70,    70,
   nil,    70,    70,    70,    70,   nil,   nil,   nil,   nil,    70,
    70,   nil,   110,   110,   110,   110,   110,    70,   nil,    70,
   110,   110,   nil,   nil,   nil,   110,   nil,   110,   110,   110,
   110,   110,   110,   110,   nil,   nil,   nil,   nil,   nil,   110,
   110,   110,   110,   110,   110,   110,   nil,   nil,   110,   nil,
   nil,   nil,   nil,   nil,   110,   110,   110,   110,   110,   110,
   110,   110,   110,   110,   110,   110,   nil,   110,   110,   110,
   nil,   110,   110,   110,   110,   110,   273,   273,   273,   273,
   273,   273,   273,   273,   273,   273,   273,   nil,   273,   273,
   nil,   nil,   273,   273,   nil,   110,   nil,   nil,   110,   nil,
   nil,   110,   110,   nil,   nil,   110,   nil,   110,   273,   nil,
   273,   110,   273,   273,   nil,   273,   273,   273,   273,   273,
   110,   273,   nil,   nil,   nil,   110,   110,   110,   110,   nil,
   110,   110,   110,   110,   nil,   nil,   nil,   nil,   110,   110,
   nil,   273,   nil,   nil,   nil,   110,   110,   nil,   110,   115,
   115,   115,   nil,   115,   nil,   nil,   nil,   115,   115,   nil,
   nil,   nil,   115,   nil,   115,   115,   115,   115,   115,   115,
   115,   nil,   nil,   nil,   nil,   nil,   115,   115,   115,   115,
   115,   115,   115,   nil,   nil,   115,   nil,   nil,   nil,   nil,
   nil,   nil,   115,   nil,   nil,   115,   115,   115,   115,   115,
   115,   115,   115,   nil,   115,   115,   115,   nil,   115,   115,
   115,   115,   115,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   115,   nil,   nil,   115,   nil,   nil,   115,   115,
   nil,   nil,   115,   nil,   nil,   nil,   nil,   nil,   115,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   115,   nil,   nil,
   nil,   nil,   115,   115,   115,   115,   nil,   115,   115,   115,
   115,   nil,   nil,   nil,   nil,   115,   115,   nil,   116,   116,
   116,   nil,   116,   115,   nil,   115,   116,   116,   nil,   nil,
   nil,   116,   nil,   116,   116,   116,   116,   116,   116,   116,
   nil,   nil,   nil,   nil,   nil,   116,   116,   116,   116,   116,
   116,   116,   nil,   nil,   116,   nil,   nil,   nil,   nil,   nil,
   nil,   116,   nil,   nil,   116,   116,   116,   116,   116,   116,
   116,   116,   nil,   116,   116,   116,   nil,   116,   116,   116,
   116,   116,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   116,   nil,   nil,   116,   nil,   nil,   116,   116,   nil,
   nil,   116,   nil,   nil,   nil,   nil,   nil,   116,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   116,   nil,   nil,   nil,
   nil,   116,   116,   116,   116,   nil,   116,   116,   116,   116,
   nil,   nil,   nil,   nil,   116,   116,   nil,   117,   117,   117,
   nil,   117,   116,   nil,   116,   117,   117,   nil,   nil,   nil,
   117,   nil,   117,   117,   117,   117,   117,   117,   117,   nil,
   nil,   nil,   nil,   nil,   117,   117,   117,   117,   117,   117,
   117,   nil,   nil,   117,   nil,   nil,   nil,   nil,   nil,   nil,
   117,   nil,   nil,   117,   117,   117,   117,   117,   117,   117,
   117,   nil,   117,   117,   117,   nil,   117,   117,   117,   117,
   117,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   117,   nil,   nil,   117,   nil,   nil,   117,   117,   nil,   nil,
   117,   nil,   nil,   nil,   nil,   nil,   117,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   117,   nil,   nil,   nil,   nil,
   117,   117,   117,   117,   nil,   117,   117,   117,   117,   nil,
   nil,   nil,   nil,   117,   117,   nil,   118,   118,   118,   nil,
   118,   117,   nil,   117,   118,   118,   nil,   nil,   nil,   118,
   nil,   118,   118,   118,   118,   118,   118,   118,   nil,   nil,
   nil,   nil,   nil,   118,   118,   118,   118,   118,   118,   118,
   nil,   nil,   118,   nil,   nil,   nil,   nil,   nil,   nil,   118,
   nil,   nil,   118,   118,   118,   118,   118,   118,   118,   118,
   nil,   118,   118,   118,   nil,   118,   118,   118,   118,   118,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   118,
   nil,   nil,   118,   nil,   nil,   118,   118,   nil,   nil,   118,
   nil,   nil,   nil,   nil,   nil,   118,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   118,   nil,   nil,   nil,   nil,   118,
   118,   118,   118,   nil,   118,   118,   118,   118,   nil,   nil,
   nil,   nil,   118,   118,   nil,   119,   119,   119,   119,   119,
   118,   nil,   118,   119,   119,   nil,   nil,   nil,   119,   nil,
   119,   119,   119,   119,   119,   119,   119,   nil,   nil,   nil,
   nil,   nil,   119,   119,   119,   119,   119,   119,   119,   nil,
   nil,   119,   nil,   nil,   nil,   nil,   nil,   119,   119,   nil,
   119,   119,   119,   119,   119,   119,   119,   119,   119,   nil,
   119,   119,   119,   nil,   119,   119,   119,   119,   119,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   119,   nil,
   nil,   119,   nil,   nil,   119,   119,   nil,   nil,   119,   nil,
   119,   nil,   nil,   nil,   119,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   119,   nil,   nil,   nil,   nil,   119,   119,
   119,   119,   nil,   119,   119,   119,   119,   nil,   nil,   nil,
   nil,   119,   119,   nil,   206,   206,   206,   nil,   206,   119,
   nil,   119,   206,   206,   nil,   nil,   nil,   206,   nil,   206,
   206,   206,   206,   206,   206,   206,   nil,   nil,   nil,   nil,
   nil,   206,   206,   206,   206,   206,   206,   206,   nil,   nil,
   206,   nil,   nil,   nil,   nil,   nil,   nil,   206,   nil,   nil,
   206,   206,   206,   206,   206,   206,   206,   206,   nil,   206,
   206,   206,   nil,   206,   206,   206,   206,   206,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   206,   nil,   nil,
   206,   nil,   nil,   206,   206,   nil,   nil,   206,   nil,   206,
   nil,   nil,   nil,   206,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   206,   nil,   nil,   nil,   nil,   206,   206,   206,
   206,   nil,   206,   206,   206,   206,   nil,   nil,   nil,   nil,
   206,   206,   nil,   207,   207,   207,   nil,   207,   206,   nil,
   206,   207,   207,   nil,   nil,   nil,   207,   nil,   207,   207,
   207,   207,   207,   207,   207,   nil,   nil,   nil,   nil,   nil,
   207,   207,   207,   207,   207,   207,   207,   nil,   nil,   207,
   nil,   nil,   nil,   nil,   nil,   nil,   207,   nil,   nil,   207,
   207,   207,   207,   207,   207,   207,   207,   nil,   207,   207,
   207,   nil,   207,   207,   207,   207,   207,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   207,   nil,   nil,   207,
   nil,   nil,   207,   207,   nil,   nil,   207,   nil,   nil,   nil,
   nil,   nil,   207,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   207,   nil,   nil,   nil,   nil,   207,   207,   207,   207,
   nil,   207,   207,   207,   207,   nil,   nil,   nil,   nil,   207,
   207,   nil,   208,   208,   208,   nil,   208,   207,   nil,   207,
   208,   208,   nil,   nil,   nil,   208,   nil,   208,   208,   208,
   208,   208,   208,   208,   nil,   nil,   nil,   nil,   nil,   208,
   208,   208,   208,   208,   208,   208,   nil,   nil,   208,   nil,
   nil,   nil,   nil,   nil,   nil,   208,   nil,   nil,   208,   208,
   208,   208,   208,   208,   208,   208,   208,   208,   208,   208,
   nil,   208,   208,   208,   208,   208,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   208,   nil,   nil,   208,   nil,
   nil,   208,   208,   nil,   nil,   208,   nil,   208,   nil,   208,
   nil,   208,   nil,   nil,   208,   nil,   nil,   nil,   nil,   nil,
   208,   nil,   nil,   nil,   nil,   208,   208,   208,   208,   nil,
   208,   208,   208,   208,   nil,   nil,   nil,   nil,   208,   208,
   nil,   211,   211,   211,   nil,   211,   208,   nil,   208,   211,
   211,   nil,   nil,   nil,   211,   nil,   211,   211,   211,   211,
   211,   211,   211,   nil,   nil,   nil,   nil,   nil,   211,   211,
   211,   211,   211,   211,   211,   nil,   nil,   211,   nil,   nil,
   nil,   nil,   nil,   nil,   211,   nil,   nil,   211,   211,   211,
   211,   211,   211,   211,   211,   nil,   211,   211,   211,   nil,
   211,   211,   211,   211,   211,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   211,   nil,   nil,   211,   nil,   nil,
   211,   211,   nil,   nil,   211,   nil,   nil,   nil,   nil,   nil,
   211,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   211,
   nil,   nil,   nil,   nil,   211,   211,   211,   211,   nil,   211,
   211,   211,   211,   nil,   nil,   nil,   nil,   211,   211,   nil,
   212,   212,   212,   nil,   212,   211,   nil,   211,   212,   212,
   nil,   nil,   nil,   212,   nil,   212,   212,   212,   212,   212,
   212,   212,   nil,   nil,   nil,   nil,   nil,   212,   212,   212,
   212,   212,   212,   212,   nil,   nil,   212,   nil,   nil,   nil,
   nil,   nil,   nil,   212,   nil,   nil,   212,   212,   212,   212,
   212,   212,   212,   212,   nil,   212,   212,   212,   nil,   212,
   212,   212,   212,   212,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   212,   nil,   nil,   212,   nil,   nil,   212,
   212,   nil,   nil,   212,   nil,   212,   nil,   nil,   nil,   212,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   212,   nil,
   nil,   nil,   nil,   212,   212,   212,   212,   nil,   212,   212,
   212,   212,   nil,   nil,   nil,   nil,   212,   212,   nil,   213,
   213,   213,   nil,   213,   212,   nil,   212,   213,   213,   nil,
   nil,   nil,   213,   nil,   213,   213,   213,   213,   213,   213,
   213,   nil,   nil,   nil,   nil,   nil,   213,   213,   213,   213,
   213,   213,   213,   nil,   nil,   213,   nil,   nil,   nil,   nil,
   nil,   nil,   213,   nil,   nil,   213,   213,   213,   213,   213,
   213,   213,   213,   nil,   213,   213,   213,   nil,   213,   213,
   213,   213,   213,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   213,   nil,   nil,   213,   nil,   nil,   213,   213,
   nil,   nil,   213,   nil,   nil,   nil,   nil,   nil,   213,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,   nil,
   nil,   nil,   213,   213,   213,   213,   nil,   213,   213,   213,
   213,   nil,   nil,   nil,   nil,   213,   213,   nil,   214,   214,
   214,   nil,   214,   213,   nil,   213,   214,   214,   nil,   nil,
   nil,   214,   nil,   214,   214,   214,   214,   214,   214,   214,
   nil,   nil,   nil,   nil,   nil,   214,   214,   214,   214,   214,
   214,   214,   nil,   nil,   214,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   214,   214,   214,   214,   214,   214,
   214,   214,   nil,   214,   214,   214,   nil,   214,   214,   214,
   214,   214,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   214,   nil,   nil,   214,   214,   nil,
   nil,   214,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   nil,
   nil,   214,   214,   214,   214,   nil,   214,   214,   214,   214,
   nil,   nil,   nil,   nil,   214,   214,   nil,   215,   215,   215,
   nil,   215,   214,   nil,   214,   215,   215,   nil,   nil,   nil,
   215,   nil,   215,   215,   215,   215,   215,   215,   215,   nil,
   nil,   nil,   nil,   nil,   215,   215,   215,   215,   215,   215,
   215,   nil,   nil,   215,   nil,   nil,   nil,   nil,   nil,   nil,
   215,   nil,   nil,   215,   215,   215,   215,   215,   215,   215,
   215,   nil,   215,   215,   215,   nil,   215,   215,   215,   215,
   215,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   215,   nil,   nil,   215,   nil,   nil,   215,   215,   nil,   nil,
   215,   nil,   nil,   nil,   nil,   nil,   215,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   215,   nil,   nil,   nil,   nil,
   215,   215,   215,   215,   nil,   215,   215,   215,   215,   nil,
   nil,   nil,   nil,   215,   215,   nil,   216,   216,   216,   nil,
   216,   215,   nil,   215,   216,   216,   nil,   nil,   nil,   216,
   nil,   216,   216,   216,   216,   216,   216,   216,   nil,   nil,
   nil,   nil,   nil,   216,   216,   216,   216,   216,   216,   216,
   nil,   nil,   216,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   216,   216,   216,   216,   216,   216,   216,   216,
   nil,   216,   216,   216,   nil,   216,   216,   216,   216,   216,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   216,   nil,   nil,   216,   216,   nil,   nil,   216,
   nil,   nil,   nil,   nil,   nil,   216,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   nil,   nil,   216,
   216,   216,   216,   nil,   216,   216,   216,   216,   nil,   nil,
   nil,   nil,   216,   216,   216,   227,   227,   227,   nil,   227,
   216,   nil,   216,   227,   227,   nil,   nil,   nil,   227,   nil,
   227,   227,   227,   227,   227,   227,   227,   nil,   nil,   nil,
   nil,   nil,   227,   227,   227,   227,   227,   227,   227,   nil,
   nil,   227,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   227,   227,   227,   227,   227,   227,   227,   227,   nil,
   227,   227,   227,   nil,   227,   227,   227,   227,   227,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   227,   nil,   nil,   227,   227,   nil,   nil,   227,   nil,
   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,   227,   227,
   227,   227,   nil,   227,   227,   227,   227,   nil,   nil,   nil,
   nil,   227,   227,   nil,   230,   230,   230,   nil,   230,   227,
   nil,   227,   230,   230,   nil,   nil,   nil,   230,   nil,   230,
   230,   230,   230,   230,   230,   230,   nil,   nil,   nil,   nil,
   nil,   230,   230,   230,   230,   230,   230,   230,   nil,   nil,
   230,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   230,   230,   230,   230,   230,   230,   230,   230,   nil,   230,
   230,   230,   nil,   230,   230,   230,   230,   230,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   230,   nil,   nil,   230,   230,   nil,   nil,   230,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   nil,   nil,   230,   230,   230,
   230,   nil,   230,   230,   230,   230,   nil,   nil,   nil,   nil,
   230,   230,   nil,   231,   231,   231,   nil,   231,   230,   nil,
   230,   231,   231,   nil,   nil,   nil,   231,   nil,   231,   231,
   231,   231,   231,   231,   231,   nil,   nil,   nil,   nil,   nil,
   231,   231,   231,   231,   231,   231,   231,   nil,   nil,   231,
   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   231,
   231,   231,   231,   231,   231,   231,   231,   nil,   231,   231,
   231,   nil,   231,   231,   231,   231,   231,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   231,
   nil,   nil,   231,   231,   nil,   nil,   231,   nil,   nil,   nil,
   nil,   nil,   231,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   231,   nil,   nil,   nil,   nil,   231,   231,   231,   231,
   nil,   231,   231,   231,   231,   nil,   nil,   nil,   nil,   231,
   231,   nil,   232,   232,   232,   nil,   232,   231,   nil,   231,
   232,   232,   nil,   nil,   nil,   232,   nil,   232,   232,   232,
   232,   232,   232,   232,   nil,   nil,   nil,   nil,   nil,   232,
   232,   232,   232,   232,   232,   232,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,   232,   232,
   232,   232,   232,   232,   232,   232,   nil,   232,   232,   232,
   nil,   232,   232,   232,   232,   232,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,   232,   nil,
   nil,   232,   232,   nil,   nil,   232,   nil,   nil,   nil,   nil,
   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   232,   nil,   nil,   nil,   nil,   232,   232,   232,   232,   nil,
   232,   232,   232,   232,   nil,   nil,   nil,   nil,   232,   232,
   nil,   233,   233,   233,   nil,   233,   232,   nil,   232,   233,
   233,   nil,   nil,   nil,   233,   nil,   233,   233,   233,   233,
   233,   233,   233,   nil,   nil,   nil,   nil,   nil,   233,   233,
   233,   233,   233,   233,   233,   nil,   nil,   233,   nil,   nil,
   nil,   nil,   nil,   nil,   233,   nil,   nil,   233,   233,   233,
   233,   233,   233,   233,   233,   nil,   233,   233,   233,   nil,
   233,   233,   233,   233,   233,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   233,   nil,   nil,   233,   nil,   nil,
   233,   233,   nil,   nil,   233,   nil,   nil,   nil,   nil,   nil,
   233,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   233,
   nil,   nil,   nil,   nil,   233,   233,   233,   233,   nil,   233,
   233,   233,   233,   nil,   nil,   nil,   nil,   233,   233,   nil,
   234,   234,   234,   nil,   234,   233,   nil,   233,   234,   234,
   nil,   nil,   nil,   234,   nil,   234,   234,   234,   234,   234,
   234,   234,   nil,   nil,   nil,   nil,   nil,   234,   234,   234,
   234,   234,   234,   234,   nil,   nil,   234,   nil,   nil,   nil,
   nil,   nil,   nil,   234,   nil,   nil,   234,   234,   234,   234,
   234,   234,   234,   234,   nil,   234,   234,   234,   nil,   234,
   234,   234,   234,   234,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   234,   nil,   nil,   234,   nil,   nil,   234,
   234,   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   234,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   234,   nil,
   nil,   nil,   nil,   234,   234,   234,   234,   nil,   234,   234,
   234,   234,   nil,   nil,   nil,   nil,   234,   234,   nil,   235,
   235,   235,   nil,   235,   234,   nil,   234,   235,   235,   nil,
   nil,   nil,   235,   nil,   235,   235,   235,   235,   235,   235,
   235,   nil,   nil,   nil,   nil,   nil,   235,   235,   235,   235,
   235,   235,   235,   nil,   nil,   235,   nil,   nil,   nil,   nil,
   nil,   nil,   235,   nil,   nil,   235,   235,   235,   235,   235,
   235,   235,   235,   nil,   235,   235,   235,   nil,   235,   235,
   235,   235,   235,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   235,   nil,   nil,   235,   nil,   nil,   235,   235,
   nil,   nil,   235,   nil,   nil,   nil,   nil,   nil,   235,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,
   nil,   nil,   235,   235,   235,   235,   nil,   235,   235,   235,
   235,   nil,   nil,   nil,   nil,   235,   235,   nil,   236,   236,
   236,   nil,   236,   235,   nil,   235,   236,   236,   nil,   nil,
   nil,   236,   nil,   236,   236,   236,   236,   236,   236,   236,
   nil,   nil,   nil,   nil,   nil,   236,   236,   236,   236,   236,
   236,   236,   nil,   nil,   236,   nil,   nil,   nil,   nil,   nil,
   nil,   236,   nil,   nil,   236,   236,   236,   236,   236,   236,
   236,   236,   nil,   236,   236,   236,   nil,   236,   236,   236,
   236,   236,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   236,   nil,   nil,   236,   nil,   nil,   236,   236,   nil,
   nil,   236,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,
   nil,   236,   236,   236,   236,   nil,   236,   236,   236,   236,
   nil,   nil,   nil,   nil,   236,   236,   nil,   237,   237,   237,
   nil,   237,   236,   nil,   236,   237,   237,   nil,   nil,   nil,
   237,   nil,   237,   237,   237,   237,   237,   237,   237,   nil,
   nil,   nil,   nil,   nil,   237,   237,   237,   237,   237,   237,
   237,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,
   237,   nil,   nil,   237,   237,   237,   237,   237,   237,   237,
   237,   nil,   237,   237,   237,   nil,   237,   237,   237,   237,
   237,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   237,   nil,   nil,   237,   nil,   nil,   237,   237,   nil,   nil,
   237,   nil,   nil,   nil,   nil,   nil,   237,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   237,   nil,   nil,   nil,   nil,
   237,   237,   237,   237,   nil,   237,   237,   237,   237,   nil,
   nil,   nil,   nil,   237,   237,   nil,   238,   238,   238,   nil,
   238,   237,   nil,   237,   238,   238,   nil,   nil,   nil,   238,
   nil,   238,   238,   238,   238,   238,   238,   238,   nil,   nil,
   nil,   nil,   nil,   238,   238,   238,   238,   238,   238,   238,
   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,   238,
   nil,   nil,   238,   238,   238,   238,   238,   238,   238,   238,
   nil,   238,   238,   238,   nil,   238,   238,   238,   238,   238,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   238,
   nil,   nil,   238,   nil,   nil,   238,   238,   nil,   nil,   238,
   nil,   nil,   nil,   nil,   nil,   238,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   238,   nil,   nil,   nil,   nil,   238,
   238,   238,   238,   nil,   238,   238,   238,   238,   nil,   nil,
   nil,   nil,   238,   238,   nil,   239,   239,   239,   nil,   239,
   238,   nil,   238,   239,   239,   nil,   nil,   nil,   239,   nil,
   239,   239,   239,   239,   239,   239,   239,   nil,   nil,   nil,
   nil,   nil,   239,   239,   239,   239,   239,   239,   239,   nil,
   nil,   239,   nil,   nil,   nil,   nil,   nil,   nil,   239,   nil,
   nil,   239,   239,   239,   239,   239,   239,   239,   239,   nil,
   239,   239,   239,   nil,   239,   239,   239,   239,   239,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   239,   nil,
   nil,   239,   nil,   nil,   239,   239,   nil,   nil,   239,   nil,
   nil,   nil,   nil,   nil,   239,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   239,   nil,   nil,   nil,   nil,   239,   239,
   239,   239,   nil,   239,   239,   239,   239,   nil,   nil,   nil,
   nil,   239,   239,   nil,   240,   240,   240,   nil,   240,   239,
   nil,   239,   240,   240,   nil,   nil,   nil,   240,   nil,   240,
   240,   240,   240,   240,   240,   240,   nil,   nil,   nil,   nil,
   nil,   240,   240,   240,   240,   240,   240,   240,   nil,   nil,
   240,   nil,   nil,   nil,   nil,   nil,   nil,   240,   nil,   nil,
   240,   240,   240,   240,   240,   240,   240,   240,   nil,   240,
   240,   240,   nil,   240,   240,   240,   240,   240,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   240,   nil,   nil,
   240,   nil,   nil,   240,   240,   nil,   nil,   240,   nil,   nil,
   nil,   nil,   nil,   240,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   240,   nil,   nil,   nil,   nil,   240,   240,   240,
   240,   nil,   240,   240,   240,   240,   nil,   nil,   nil,   nil,
   240,   240,   nil,   241,   241,   241,   nil,   241,   240,   nil,
   240,   241,   241,   nil,   nil,   nil,   241,   nil,   241,   241,
   241,   241,   241,   241,   241,   nil,   nil,   nil,   nil,   nil,
   241,   241,   241,   241,   241,   241,   241,   nil,   nil,   241,
   nil,   nil,   nil,   nil,   nil,   nil,   241,   nil,   nil,   241,
   241,   241,   241,   241,   241,   241,   241,   nil,   241,   241,
   241,   nil,   241,   241,   241,   241,   241,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   241,   nil,   nil,   241,
   nil,   nil,   241,   241,   nil,   nil,   241,   nil,   nil,   nil,
   nil,   nil,   241,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   241,   nil,   nil,   nil,   nil,   241,   241,   241,   241,
   nil,   241,   241,   241,   241,   nil,   nil,   nil,   nil,   241,
   241,   nil,   242,   242,   242,   nil,   242,   241,   nil,   241,
   242,   242,   nil,   nil,   nil,   242,   nil,   242,   242,   242,
   242,   242,   242,   242,   nil,   nil,   nil,   nil,   nil,   242,
   242,   242,   242,   242,   242,   242,   nil,   nil,   242,   nil,
   nil,   nil,   nil,   nil,   nil,   242,   nil,   nil,   242,   242,
   242,   242,   242,   242,   242,   242,   nil,   242,   242,   242,
   nil,   242,   242,   242,   242,   242,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   242,   nil,   nil,   242,   nil,
   nil,   242,   242,   nil,   nil,   242,   nil,   nil,   nil,   nil,
   nil,   242,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   242,   nil,   nil,   nil,   nil,   242,   242,   242,   242,   nil,
   242,   242,   242,   242,   nil,   nil,   nil,   nil,   242,   242,
   nil,   243,   243,   243,   nil,   243,   242,   nil,   242,   243,
   243,   nil,   nil,   nil,   243,   nil,   243,   243,   243,   243,
   243,   243,   243,   nil,   nil,   nil,   nil,   nil,   243,   243,
   243,   243,   243,   243,   243,   nil,   nil,   243,   nil,   nil,
   nil,   nil,   nil,   nil,   243,   nil,   nil,   243,   243,   243,
   243,   243,   243,   243,   243,   nil,   243,   243,   243,   nil,
   243,   243,   243,   243,   243,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   243,   nil,   nil,   243,   nil,   nil,
   243,   243,   nil,   nil,   243,   nil,   nil,   nil,   nil,   nil,
   243,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   243,
   nil,   nil,   nil,   nil,   243,   243,   243,   243,   nil,   243,
   243,   243,   243,   nil,   nil,   nil,   nil,   243,   243,   nil,
   244,   244,   244,   nil,   244,   243,   nil,   243,   244,   244,
   nil,   nil,   nil,   244,   nil,   244,   244,   244,   244,   244,
   244,   244,   nil,   nil,   nil,   nil,   nil,   244,   244,   244,
   244,   244,   244,   244,   nil,   nil,   244,   nil,   nil,   nil,
   nil,   nil,   nil,   244,   nil,   nil,   244,   244,   244,   244,
   244,   244,   244,   244,   nil,   244,   244,   244,   nil,   244,
   244,   244,   244,   244,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   244,   nil,   nil,   244,   nil,   nil,   244,
   244,   nil,   nil,   244,   nil,   nil,   nil,   nil,   nil,   244,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   244,   nil,
   nil,   nil,   nil,   244,   244,   244,   244,   nil,   244,   244,
   244,   244,   nil,   nil,   nil,   nil,   244,   244,   nil,   245,
   245,   245,   nil,   245,   244,   nil,   244,   245,   245,   nil,
   nil,   nil,   245,   nil,   245,   245,   245,   245,   245,   245,
   245,   nil,   nil,   nil,   nil,   nil,   245,   245,   245,   245,
   245,   245,   245,   nil,   nil,   245,   nil,   nil,   nil,   nil,
   nil,   nil,   245,   nil,   nil,   245,   245,   245,   245,   245,
   245,   245,   245,   nil,   245,   245,   245,   nil,   245,   245,
   245,   245,   245,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   245,   nil,   nil,   245,   nil,   nil,   245,   245,
   nil,   nil,   245,   nil,   nil,   nil,   nil,   nil,   245,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   245,   nil,   nil,
   nil,   nil,   245,   245,   245,   245,   nil,   245,   245,   245,
   245,   nil,   nil,   nil,   nil,   245,   245,   nil,   246,   246,
   246,   nil,   246,   245,   nil,   245,   246,   246,   nil,   nil,
   nil,   246,   nil,   246,   246,   246,   246,   246,   246,   246,
   nil,   nil,   nil,   nil,   nil,   246,   246,   246,   246,   246,
   246,   246,   nil,   nil,   246,   nil,   nil,   nil,   nil,   nil,
   nil,   246,   nil,   nil,   246,   246,   246,   246,   246,   246,
   246,   246,   nil,   246,   246,   246,   nil,   246,   246,   246,
   246,   246,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   246,   nil,   nil,   246,   nil,   nil,   246,   246,   nil,
   nil,   246,   nil,   nil,   nil,   nil,   nil,   246,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   246,   nil,   nil,   nil,
   nil,   246,   246,   246,   246,   nil,   246,   246,   246,   246,
   nil,   nil,   nil,   nil,   246,   246,   nil,   247,   247,   247,
   nil,   247,   246,   nil,   246,   247,   247,   nil,   nil,   nil,
   247,   nil,   247,   247,   247,   247,   247,   247,   247,   nil,
   nil,   nil,   nil,   nil,   247,   247,   247,   247,   247,   247,
   247,   nil,   nil,   247,   nil,   nil,   nil,   nil,   nil,   nil,
   247,   nil,   nil,   247,   247,   247,   247,   247,   247,   247,
   247,   nil,   247,   247,   247,   nil,   247,   247,   247,   247,
   247,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   247,   nil,   nil,   247,   nil,   nil,   247,   247,   nil,   nil,
   247,   nil,   nil,   nil,   nil,   nil,   247,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   247,   nil,   nil,   nil,   nil,
   247,   247,   247,   247,   nil,   247,   247,   247,   247,   nil,
   nil,   nil,   nil,   247,   247,   nil,   248,   248,   248,   nil,
   248,   247,   nil,   247,   248,   248,   nil,   nil,   nil,   248,
   nil,   248,   248,   248,   248,   248,   248,   248,   nil,   nil,
   nil,   nil,   nil,   248,   248,   248,   248,   248,   248,   248,
   nil,   nil,   248,   nil,   nil,   nil,   nil,   nil,   nil,   248,
   nil,   nil,   248,   248,   248,   248,   248,   248,   248,   248,
   nil,   248,   248,   248,   nil,   248,   248,   248,   248,   248,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   248,
   nil,   nil,   248,   nil,   nil,   248,   248,   nil,   nil,   248,
   nil,   nil,   nil,   nil,   nil,   248,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   248,   nil,   nil,   nil,   nil,   248,
   248,   248,   248,   nil,   248,   248,   248,   248,   nil,   nil,
   nil,   nil,   248,   248,   nil,   249,   249,   249,   nil,   249,
   248,   nil,   248,   249,   249,   nil,   nil,   nil,   249,   nil,
   249,   249,   249,   249,   249,   249,   249,   nil,   nil,   nil,
   nil,   nil,   249,   249,   249,   249,   249,   249,   249,   nil,
   nil,   249,   nil,   nil,   nil,   nil,   nil,   nil,   249,   nil,
   nil,   249,   249,   249,   249,   249,   249,   249,   249,   nil,
   249,   249,   249,   nil,   249,   249,   249,   249,   249,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   249,   nil,
   nil,   249,   nil,   nil,   249,   249,   nil,   nil,   249,   nil,
   nil,   nil,   nil,   nil,   249,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   249,   nil,   nil,   nil,   nil,   249,   249,
   249,   249,   nil,   249,   249,   249,   249,   nil,   nil,   nil,
   nil,   249,   249,   nil,   250,   250,   250,   nil,   250,   249,
   nil,   249,   250,   250,   nil,   nil,   nil,   250,   nil,   250,
   250,   250,   250,   250,   250,   250,   nil,   nil,   nil,   nil,
   nil,   250,   250,   250,   250,   250,   250,   250,   nil,   nil,
   250,   nil,   nil,   nil,   nil,   nil,   nil,   250,   nil,   nil,
   250,   250,   250,   250,   250,   250,   250,   250,   nil,   250,
   250,   250,   nil,   250,   250,   250,   250,   250,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   250,   nil,   nil,
   250,   nil,   nil,   250,   250,   nil,   nil,   250,   nil,   nil,
   nil,   nil,   nil,   250,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   250,   nil,   nil,   nil,   nil,   250,   250,   250,
   250,   nil,   250,   250,   250,   250,   nil,   nil,   nil,   nil,
   250,   250,   nil,   251,   251,   251,   nil,   251,   250,   nil,
   250,   251,   251,   nil,   nil,   nil,   251,   nil,   251,   251,
   251,   251,   251,   251,   251,   nil,   nil,   nil,   nil,   nil,
   251,   251,   251,   251,   251,   251,   251,   nil,   nil,   251,
   nil,   nil,   nil,   nil,   nil,   nil,   251,   nil,   nil,   251,
   251,   251,   251,   251,   251,   251,   251,   nil,   251,   251,
   251,   nil,   251,   251,   251,   251,   251,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   251,   nil,   nil,   251,
   nil,   nil,   251,   251,   nil,   nil,   251,   nil,   nil,   nil,
   nil,   nil,   251,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   251,   nil,   nil,   nil,   nil,   251,   251,   251,   251,
   nil,   251,   251,   251,   251,   nil,   nil,   nil,   nil,   251,
   251,   nil,   252,   252,   252,   nil,   252,   251,   nil,   251,
   252,   252,   nil,   nil,   nil,   252,   nil,   252,   252,   252,
   252,   252,   252,   252,   nil,   nil,   nil,   nil,   nil,   252,
   252,   252,   252,   252,   252,   252,   nil,   nil,   252,   nil,
   nil,   nil,   nil,   nil,   nil,   252,   nil,   nil,   252,   252,
   252,   252,   252,   252,   252,   252,   nil,   252,   252,   252,
   nil,   252,   252,   252,   252,   252,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   252,   nil,   nil,   252,   nil,
   nil,   252,   252,   nil,   nil,   252,   nil,   nil,   nil,   nil,
   nil,   252,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   252,   nil,   nil,   nil,   nil,   252,   252,   252,   252,   nil,
   252,   252,   252,   252,   nil,   nil,   nil,   nil,   252,   252,
   nil,   253,   253,   253,   nil,   253,   252,   nil,   252,   253,
   253,   nil,   nil,   nil,   253,   nil,   253,   253,   253,   253,
   253,   253,   253,   nil,   nil,   nil,   nil,   nil,   253,   253,
   253,   253,   253,   253,   253,   nil,   nil,   253,   nil,   nil,
   nil,   nil,   nil,   nil,   253,   nil,   nil,   253,   253,   253,
   253,   253,   253,   253,   253,   nil,   253,   253,   253,   nil,
   253,   253,   253,   253,   253,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   253,   nil,   nil,   253,   nil,   nil,
   253,   253,   nil,   nil,   253,   nil,   nil,   nil,   nil,   nil,
   253,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   253,
   nil,   nil,   nil,   nil,   253,   253,   253,   253,   nil,   253,
   253,   253,   253,   nil,   nil,   nil,   nil,   253,   253,   nil,
   254,   254,   254,   nil,   254,   253,   nil,   253,   254,   254,
   nil,   nil,   nil,   254,   nil,   254,   254,   254,   254,   254,
   254,   254,   nil,   nil,   nil,   nil,   nil,   254,   254,   254,
   254,   254,   254,   254,   nil,   nil,   254,   nil,   nil,   nil,
   nil,   nil,   nil,   254,   nil,   nil,   254,   254,   254,   254,
   254,   254,   254,   254,   nil,   254,   254,   254,   nil,   254,
   254,   254,   254,   254,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   254,   nil,   nil,   254,   nil,   nil,   254,
   254,   nil,   nil,   254,   nil,   nil,   nil,   nil,   nil,   254,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   254,   nil,
   nil,   nil,   nil,   254,   254,   254,   254,   nil,   254,   254,
   254,   254,   nil,   nil,   nil,   nil,   254,   254,   nil,   255,
   255,   255,   nil,   255,   254,   nil,   254,   255,   255,   nil,
   nil,   nil,   255,   nil,   255,   255,   255,   255,   255,   255,
   255,   nil,   nil,   nil,   nil,   nil,   255,   255,   255,   255,
   255,   255,   255,   nil,   nil,   255,   nil,   nil,   nil,   nil,
   nil,   nil,   255,   nil,   nil,   255,   255,   255,   255,   255,
   255,   255,   255,   nil,   255,   255,   255,   nil,   255,   255,
   255,   255,   255,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   255,   nil,   nil,   255,   nil,   nil,   255,   255,
   nil,   nil,   255,   nil,   nil,   nil,   nil,   nil,   255,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   255,   nil,   nil,
   nil,   nil,   255,   255,   255,   255,   nil,   255,   255,   255,
   255,   nil,   nil,   nil,   nil,   255,   255,   nil,   262,   262,
   262,   nil,   262,   255,   nil,   255,   262,   262,   nil,   nil,
   nil,   262,   nil,   262,   262,   262,   262,   262,   262,   262,
   nil,   nil,   nil,   nil,   nil,   262,   262,   262,   262,   262,
   262,   262,   nil,   nil,   262,   nil,   nil,   nil,   nil,   nil,
   nil,   262,   nil,   nil,   262,   262,   262,   262,   262,   262,
   262,   262,   262,   262,   262,   262,   nil,   262,   262,   262,
   262,   262,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   262,   nil,   nil,   262,   nil,   nil,   262,   262,   nil,
   nil,   262,   nil,   262,   nil,   262,   nil,   262,   nil,   nil,
   262,   nil,   nil,   nil,   nil,   nil,   262,   nil,   nil,   nil,
   nil,   262,   262,   262,   262,   nil,   262,   262,   262,   262,
   nil,   nil,   nil,   nil,   262,   262,   nil,   263,   263,   263,
   nil,   263,   262,   nil,   262,   263,   263,   nil,   nil,   nil,
   263,   nil,   263,   263,   263,   263,   263,   263,   263,   nil,
   nil,   nil,   nil,   nil,   263,   263,   263,   263,   263,   263,
   263,   nil,   nil,   263,   nil,   nil,   nil,   nil,   nil,   nil,
   263,   nil,   nil,   263,   263,   263,   263,   263,   263,   263,
   263,   263,   263,   263,   263,   nil,   263,   263,   263,   263,
   263,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   263,   nil,   nil,   263,   nil,   nil,   263,   263,   nil,   nil,
   263,   nil,   263,   nil,   263,   nil,   263,   nil,   nil,   263,
   nil,   nil,   nil,   nil,   nil,   263,   nil,   nil,   nil,   nil,
   263,   263,   263,   263,   nil,   263,   263,   263,   263,   nil,
   nil,   nil,   nil,   263,   263,   nil,   271,   271,   271,   nil,
   271,   263,   nil,   263,   271,   271,   nil,   nil,   nil,   271,
   nil,   271,   271,   271,   271,   271,   271,   271,   nil,   nil,
   nil,   nil,   nil,   271,   271,   271,   271,   271,   271,   271,
   nil,   nil,   271,   nil,   nil,   nil,   nil,   nil,   nil,   271,
   nil,   nil,   271,   271,   271,   271,   271,   271,   271,   271,
   271,   271,   271,   271,   nil,   271,   271,   271,   271,   271,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   271,
   nil,   nil,   271,   nil,   nil,   271,   271,   nil,   nil,   271,
   nil,   271,   nil,   271,   nil,   271,   nil,   nil,   271,   nil,
   nil,   nil,   nil,   nil,   271,   nil,   nil,   nil,   nil,   271,
   271,   271,   271,   nil,   271,   271,   271,   271,   nil,   nil,
   nil,   nil,   271,   271,   271,   278,   278,   278,   nil,   278,
   271,   nil,   271,   278,   278,   nil,   nil,   nil,   278,   nil,
   278,   278,   278,   278,   278,   278,   278,   nil,   nil,   nil,
   nil,   nil,   278,   278,   278,   278,   278,   278,   278,   nil,
   nil,   278,   nil,   nil,   nil,   nil,   nil,   nil,   278,   nil,
   nil,   278,   278,   278,   278,   278,   278,   278,   278,   nil,
   278,   278,   278,   nil,   278,   278,   278,   278,   278,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   278,   nil,
   nil,   278,   nil,   nil,   278,   278,   nil,   nil,   278,   nil,
   nil,   nil,   nil,   nil,   278,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   278,   nil,   nil,   nil,   nil,   278,   278,
   278,   278,   nil,   278,   278,   278,   278,   nil,   nil,   nil,
   nil,   278,   278,   nil,   280,   280,   280,   nil,   280,   278,
   nil,   278,   280,   280,   nil,   nil,   nil,   280,   nil,   280,
   280,   280,   280,   280,   280,   280,   nil,   nil,   nil,   nil,
   nil,   280,   280,   280,   280,   280,   280,   280,   nil,   nil,
   280,   nil,   nil,   nil,   nil,   nil,   nil,   280,   nil,   nil,
   280,   280,   280,   280,   280,   280,   280,   280,   nil,   280,
   280,   280,   nil,   280,   280,   280,   280,   280,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   280,   nil,   nil,
   280,   nil,   nil,   280,   280,   nil,   nil,   280,   nil,   nil,
   nil,   nil,   nil,   280,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   280,   nil,   nil,   nil,   nil,   280,   280,   280,
   280,   nil,   280,   280,   280,   280,   nil,   nil,   nil,   nil,
   280,   280,   nil,   282,   282,   282,   nil,   282,   280,   nil,
   280,   282,   282,   nil,   nil,   nil,   282,   nil,   282,   282,
   282,   282,   282,   282,   282,   nil,   nil,   nil,   nil,   nil,
   282,   282,   282,   282,   282,   282,   282,   nil,   nil,   282,
   nil,   nil,   nil,   nil,   nil,   nil,   282,   nil,   nil,   282,
   282,   282,   282,   282,   282,   282,   282,   nil,   282,   282,
   282,   nil,   282,   282,   282,   282,   282,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   282,   nil,   nil,   282,
   nil,   nil,   282,   282,   nil,   nil,   282,   nil,   nil,   nil,
   nil,   nil,   282,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   282,   nil,   nil,   nil,   nil,   282,   282,   282,   282,
   nil,   282,   282,   282,   282,   nil,   nil,   nil,   nil,   282,
   282,   nil,   283,   283,   283,   nil,   283,   282,   nil,   282,
   283,   283,   nil,   nil,   nil,   283,   nil,   283,   283,   283,
   283,   283,   283,   283,   nil,   nil,   nil,   nil,   nil,   283,
   283,   283,   283,   283,   283,   283,   nil,   nil,   283,   nil,
   nil,   nil,   nil,   nil,   nil,   283,   nil,   nil,   283,   283,
   283,   283,   283,   283,   283,   283,   nil,   283,   283,   283,
   nil,   283,   283,   283,   283,   283,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   283,   nil,   nil,   283,   nil,
   nil,   283,   283,   nil,   nil,   283,   nil,   nil,   nil,   nil,
   nil,   283,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   283,   nil,   nil,   nil,   nil,   283,   283,   283,   283,   nil,
   283,   283,   283,   283,   nil,   nil,   nil,   nil,   283,   283,
   nil,   288,   288,   288,   288,   288,   283,   nil,   283,   288,
   288,   nil,   nil,   nil,   288,   nil,   288,   288,   288,   288,
   288,   288,   288,   nil,   nil,   nil,   nil,   nil,   288,   288,
   288,   288,   288,   288,   288,   nil,   nil,   288,   nil,   nil,
   nil,   nil,   nil,   288,   288,   nil,   288,   288,   288,   288,
   288,   288,   288,   288,   288,   nil,   288,   288,   288,   nil,
   288,   288,   288,   288,   288,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   288,   nil,   nil,   288,   nil,   nil,
   288,   288,   nil,   nil,   288,   nil,   288,   nil,   nil,   nil,
   288,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   288,
   nil,   nil,   nil,   nil,   288,   288,   288,   288,   nil,   288,
   288,   288,   288,   nil,   nil,   nil,   nil,   288,   288,   nil,
   296,   296,   296,   nil,   296,   288,   nil,   288,   296,   296,
   nil,   nil,   nil,   296,   nil,   296,   296,   296,   296,   296,
   296,   296,   nil,   nil,   nil,   nil,   nil,   296,   296,   296,
   296,   296,   296,   296,   nil,   nil,   296,   nil,   nil,   nil,
   nil,   nil,   nil,   296,   nil,   nil,   296,   296,   296,   296,
   296,   296,   296,   296,   nil,   296,   296,   296,   nil,   296,
   296,   nil,   nil,   296,   424,   424,   424,   424,   424,   424,
   424,   424,   424,   424,   424,   nil,   424,   424,   nil,   nil,
   424,   424,   nil,   296,   nil,   nil,   296,   nil,   nil,   296,
   296,   nil,   nil,   296,   nil,   nil,   424,   nil,   424,   nil,
   424,   424,   nil,   424,   424,   424,   424,   424,   nil,   424,
   nil,   nil,   nil,   296,   296,   296,   296,   nil,   296,   296,
   296,   296,   nil,   nil,   nil,   nil,   296,   296,   nil,   424,
   nil,   296,   nil,   nil,   296,   nil,   296,   313,   313,   313,
   nil,   313,   nil,   nil,   nil,   313,   313,   nil,   nil,   nil,
   313,   nil,   313,   313,   313,   313,   313,   313,   313,   nil,
   nil,   nil,   nil,   nil,   313,   313,   313,   313,   313,   313,
   313,   nil,   nil,   313,   nil,   nil,   nil,   nil,   nil,   nil,
   313,   nil,   nil,   313,   313,   313,   313,   313,   313,   313,
   313,   nil,   313,   313,   313,   nil,   313,   313,   nil,   nil,
   313,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   313,   nil,   nil,   313,   nil,   nil,   313,   313,   nil,   nil,
   313,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   313,   313,   313,   313,   nil,   313,   313,   313,   313,   nil,
   nil,   nil,   nil,   313,   313,   nil,   322,   322,   322,   nil,
   322,   313,   nil,   313,   322,   322,   nil,   nil,   nil,   322,
   nil,   322,   322,   322,   322,   322,   322,   322,   nil,   nil,
   nil,   nil,   nil,   322,   322,   322,   322,   322,   322,   322,
   nil,   nil,   322,   nil,   nil,   nil,   nil,   nil,   nil,   322,
   nil,   nil,   322,   322,   322,   322,   322,   322,   322,   322,
   nil,   322,   322,   322,   nil,   322,   322,   322,   322,   322,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   322,
   nil,   nil,   322,   322,   nil,   322,   322,   nil,   nil,   322,
   nil,   nil,   nil,   nil,   nil,   322,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   322,   nil,   nil,   nil,   nil,   322,
   322,   322,   322,   nil,   322,   322,   322,   322,   nil,   nil,
   nil,   nil,   322,   322,   nil,   324,   324,   324,   nil,   324,
   322,   nil,   322,   324,   324,   nil,   nil,   nil,   324,   nil,
   324,   324,   324,   324,   324,   324,   324,   nil,   nil,   nil,
   nil,   nil,   324,   324,   324,   324,   324,   324,   324,   nil,
   nil,   324,   nil,   nil,   nil,   nil,   nil,   nil,   324,   nil,
   nil,   324,   324,   324,   324,   324,   324,   324,   324,   nil,
   324,   324,   324,   nil,   324,   324,   324,   324,   324,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   324,   nil,
   nil,   324,   nil,   nil,   324,   324,   nil,   nil,   324,   nil,
   nil,   nil,   nil,   nil,   324,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   324,   nil,   nil,   nil,   nil,   324,   324,
   324,   324,   nil,   324,   324,   324,   324,   nil,   nil,   nil,
   nil,   324,   324,   nil,   338,   338,   338,   nil,   338,   324,
   nil,   324,   338,   338,   nil,   nil,   nil,   338,   nil,   338,
   338,   338,   338,   338,   338,   338,   nil,   nil,   nil,   nil,
   nil,   338,   338,   338,   338,   338,   338,   338,   nil,   nil,
   338,   nil,   nil,   nil,   nil,   nil,   nil,   338,   nil,   nil,
   338,   338,   338,   338,   338,   338,   338,   338,   nil,   338,
   338,   338,   nil,   338,   338,   338,   338,   338,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   338,   nil,   nil,
   338,   nil,   nil,   338,   338,   nil,   nil,   338,   nil,   nil,
   nil,   nil,   nil,   338,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   338,   nil,   nil,   nil,   nil,   338,   338,   338,
   338,   nil,   338,   338,   338,   338,   nil,   nil,   nil,   nil,
   338,   338,   nil,   339,   339,   339,   nil,   339,   338,   nil,
   338,   339,   339,   nil,   nil,   nil,   339,   nil,   339,   339,
   339,   339,   339,   339,   339,   nil,   nil,   nil,   nil,   nil,
   339,   339,   339,   339,   339,   339,   339,   nil,   nil,   339,
   nil,   nil,   nil,   nil,   nil,   nil,   339,   nil,   nil,   339,
   339,   339,   339,   339,   339,   339,   339,   nil,   339,   339,
   339,   nil,   339,   339,   339,   339,   339,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   339,   nil,   nil,   339,
   nil,   nil,   339,   339,   nil,   nil,   339,   nil,   nil,   nil,
   nil,   nil,   339,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   339,   nil,   nil,   nil,   nil,   339,   339,   339,   339,
   nil,   339,   339,   339,   339,   nil,   nil,   nil,   nil,   339,
   339,   nil,   358,   358,   358,   nil,   358,   339,   nil,   339,
   358,   358,   nil,   nil,   nil,   358,   nil,   358,   358,   358,
   358,   358,   358,   358,   nil,   nil,   nil,   nil,   nil,   358,
   358,   358,   358,   358,   358,   358,   nil,   nil,   358,   nil,
   nil,   nil,   nil,   nil,   nil,   358,   nil,   nil,   358,   358,
   358,   358,   358,   358,   358,   358,   nil,   358,   358,   358,
   nil,   358,   358,   358,   358,   358,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   358,   nil,   nil,   358,   nil,
   nil,   358,   358,   nil,   nil,   358,   nil,   nil,   nil,   nil,
   nil,   358,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   358,   nil,   nil,   nil,   nil,   358,   358,   358,   358,   nil,
   358,   358,   358,   358,   nil,   nil,   nil,   nil,   358,   358,
   nil,   374,   374,   374,   nil,   374,   358,   nil,   358,   374,
   374,   nil,   nil,   nil,   374,   nil,   374,   374,   374,   374,
   374,   374,   374,   nil,   nil,   nil,   nil,   nil,   374,   374,
   374,   374,   374,   374,   374,   nil,   nil,   374,   nil,   nil,
   nil,   nil,   nil,   nil,   374,   nil,   nil,   374,   374,   374,
   374,   374,   374,   374,   374,   nil,   374,   374,   374,   nil,
   374,   374,   374,   374,   374,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   374,   nil,   nil,   374,   nil,   nil,
   374,   374,   nil,   nil,   374,   nil,   nil,   nil,   nil,   nil,
   374,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   374,
   nil,   nil,   nil,   nil,   374,   374,   374,   374,   nil,   374,
   374,   374,   374,   nil,   nil,   nil,   nil,   374,   374,   nil,
   402,   402,   402,   nil,   402,   374,   nil,   374,   402,   402,
   nil,   nil,   nil,   402,   nil,   402,   402,   402,   402,   402,
   402,   402,   nil,   nil,   nil,   nil,   nil,   402,   402,   402,
   402,   402,   402,   402,   nil,   nil,   402,   nil,   nil,   nil,
   nil,   nil,   nil,   402,   nil,   nil,   402,   402,   402,   402,
   402,   402,   402,   402,   nil,   402,   402,   402,   nil,   402,
   402,   402,   402,   402,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   402,   nil,   nil,   402,   nil,   nil,   402,
   402,   nil,   nil,   402,   nil,   nil,   nil,   nil,   nil,   402,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   402,   nil,
   nil,   nil,   nil,   402,   402,   402,   402,   nil,   402,   402,
   402,   402,   nil,   nil,   nil,   nil,   402,   402,   nil,   439,
   439,   439,   nil,   439,   402,   nil,   402,   439,   439,   nil,
   nil,   nil,   439,   nil,   439,   439,   439,   439,   439,   439,
   439,   nil,   nil,   nil,   nil,   nil,   439,   439,   439,   439,
   439,   439,   439,   nil,   nil,   439,   nil,   nil,   nil,   nil,
   nil,   nil,   439,   nil,   nil,   439,   439,   439,   439,   439,
   439,   439,   439,   439,   439,   439,   439,   nil,   439,   439,
   439,   439,   439,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   439,   nil,   nil,   439,   nil,   nil,   439,   439,
   nil,   nil,   439,   nil,   439,   nil,   439,   nil,   439,   nil,
   nil,   439,   nil,   nil,   nil,   nil,   nil,   439,   nil,   nil,
   nil,   nil,   439,   439,   439,   439,   nil,   439,   439,   439,
   439,   nil,   nil,   nil,   nil,   439,   439,   nil,   441,   441,
   441,   nil,   441,   439,   nil,   439,   441,   441,   nil,   nil,
   nil,   441,   nil,   441,   441,   441,   441,   441,   441,   441,
   nil,   nil,   nil,   nil,   nil,   441,   441,   441,   441,   441,
   441,   441,   nil,   nil,   441,   nil,   nil,   nil,   nil,   nil,
   nil,   441,   nil,   nil,   441,   441,   441,   441,   441,   441,
   441,   441,   nil,   441,   441,   441,   nil,   441,   441,   441,
   441,   441,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   441,   nil,   nil,   441,   nil,   nil,   441,   441,   nil,
   nil,   441,   nil,   nil,   nil,   nil,   nil,   441,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   441,   nil,   nil,   nil,
   nil,   441,   441,   441,   441,   nil,   441,   441,   441,   441,
   nil,   nil,   nil,   nil,   441,   441,   nil,   442,   442,   442,
   nil,   442,   441,   nil,   441,   442,   442,   nil,   nil,   nil,
   442,   nil,   442,   442,   442,   442,   442,   442,   442,   nil,
   nil,   nil,   nil,   nil,   442,   442,   442,   442,   442,   442,
   442,   nil,   nil,   442,   nil,   nil,   nil,   nil,   nil,   nil,
   442,   nil,   nil,   442,   442,   442,   442,   442,   442,   442,
   442,   nil,   442,   442,   442,   nil,   442,   442,   442,   442,
   442,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   442,   nil,   nil,   442,   nil,   nil,   442,   442,   nil,   nil,
   442,   nil,   nil,   nil,   nil,   nil,   442,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   442,   nil,   nil,   nil,   nil,
   442,   442,   442,   442,   nil,   442,   442,   442,   442,   nil,
   nil,   nil,   nil,   442,   442,   nil,   443,   443,   443,   nil,
   443,   442,   nil,   442,   443,   443,   nil,   nil,   nil,   443,
   nil,   443,   443,   443,   443,   443,   443,   443,   nil,   nil,
   nil,   nil,   nil,   443,   443,   443,   443,   443,   443,   443,
   nil,   nil,   443,   nil,   nil,   nil,   nil,   nil,   nil,   443,
   nil,   nil,   443,   443,   443,   443,   443,   443,   443,   443,
   nil,   443,   443,   443,   nil,   443,   443,   443,   443,   443,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   443,
   nil,   nil,   443,   nil,   nil,   443,   443,   nil,   nil,   443,
   nil,   nil,   nil,   nil,   nil,   443,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   443,   nil,   nil,   nil,   nil,   443,
   443,   443,   443,   nil,   443,   443,   443,   443,   nil,   nil,
   nil,   nil,   443,   443,   nil,   483,   483,   483,   nil,   483,
   443,   nil,   443,   483,   483,   nil,   nil,   nil,   483,   nil,
   483,   483,   483,   483,   483,   483,   483,   nil,   nil,   nil,
   nil,   nil,   483,   483,   483,   483,   483,   483,   483,   nil,
   nil,   483,   nil,   nil,   nil,   nil,   nil,   nil,   483,   nil,
   nil,   483,   483,   483,   483,   483,   483,   483,   483,   483,
   483,   483,   483,   nil,   483,   483,   483,   483,   483,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   483,   nil,
   nil,   483,   nil,   nil,   483,   483,   nil,   nil,   483,   nil,
   483,   nil,   483,   nil,   483,   nil,   nil,   483,   nil,   nil,
   nil,   nil,   nil,   483,   nil,   nil,   nil,   nil,   483,   483,
   483,   483,   nil,   483,   483,   483,   483,   nil,   nil,   nil,
   nil,   483,   483,   nil,   485,   485,   485,   nil,   485,   483,
   nil,   483,   485,   485,   nil,   nil,   nil,   485,   nil,   485,
   485,   485,   485,   485,   485,   485,   nil,   nil,   nil,   nil,
   nil,   485,   485,   485,   485,   485,   485,   485,   nil,   nil,
   485,   nil,   nil,   nil,   nil,   nil,   nil,   485,   nil,   nil,
   485,   485,   485,   485,   485,   485,   485,   485,   485,   485,
   485,   485,   nil,   485,   485,   485,   485,   485,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   485,   nil,   nil,
   485,   nil,   nil,   485,   485,   nil,   nil,   485,   nil,   nil,
   nil,   485,   nil,   485,   nil,   nil,   485,   nil,   nil,   nil,
   nil,   nil,   485,   nil,   nil,   nil,   nil,   485,   485,   485,
   485,   nil,   485,   485,   485,   485,   nil,   nil,   nil,   nil,
   485,   485,   nil,   487,   487,   487,   nil,   487,   485,   nil,
   485,   487,   487,   nil,   nil,   nil,   487,   nil,   487,   487,
   487,   487,   487,   487,   487,   nil,   nil,   nil,   nil,   nil,
   487,   487,   487,   487,   487,   487,   487,   nil,   nil,   487,
   nil,   nil,   nil,   nil,   nil,   nil,   487,   nil,   nil,   487,
   487,   487,   487,   487,   487,   487,   487,   nil,   487,   487,
   487,   nil,   487,   487,   487,   487,   487,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   487,   nil,   nil,   487,
   nil,   nil,   487,   487,   nil,   nil,   487,   nil,   nil,   nil,
   nil,   nil,   487,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   487,   nil,   nil,   nil,   nil,   487,   487,   487,   487,
   nil,   487,   487,   487,   487,   nil,   nil,   nil,   nil,   487,
   487,   nil,   492,   492,   492,   492,   492,   487,   nil,   487,
   492,   492,   nil,   nil,   nil,   492,   nil,   492,   492,   492,
   492,   492,   492,   492,   nil,   nil,   nil,   nil,   nil,   492,
   492,   492,   492,   492,   492,   492,   nil,   nil,   492,   nil,
   nil,   nil,   nil,   nil,   492,   492,   492,   492,   492,   492,
   492,   492,   492,   492,   492,   492,   nil,   492,   492,   492,
   nil,   492,   492,   492,   492,   492,   469,   469,   469,   469,
   469,   469,   469,   469,   469,   469,   469,   nil,   469,   469,
   nil,   nil,   469,   469,   nil,   492,   nil,   nil,   492,   nil,
   nil,   492,   492,   nil,   nil,   492,   nil,   492,   469,   nil,
   469,   492,   469,   469,   nil,   469,   469,   469,   469,   469,
   492,   469,   nil,   nil,   nil,   492,   492,   492,   492,   nil,
   492,   492,   492,   492,   nil,   nil,   nil,   nil,   492,   492,
   469,   469,   nil,   nil,   nil,   492,   492,   nil,   492,   500,
   500,   500,   nil,   500,   nil,   nil,   nil,   500,   500,   nil,
   nil,   nil,   500,   nil,   500,   500,   500,   500,   500,   500,
   500,   nil,   nil,   nil,   nil,   nil,   500,   500,   500,   500,
   500,   500,   500,   nil,   nil,   500,   nil,   nil,   nil,   nil,
   nil,   nil,   500,   nil,   nil,   500,   500,   500,   500,   500,
   500,   500,   500,   nil,   500,   500,   500,   nil,   500,   500,
   nil,   nil,   500,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   500,   nil,   nil,   500,   nil,   nil,   500,   500,
   nil,   nil,   500,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   500,   500,   500,   500,   nil,   500,   500,   500,
   500,   nil,   nil,   nil,   nil,   500,   500,   nil,   502,   502,
   502,   nil,   502,   500,   nil,   500,   502,   502,   nil,   nil,
   nil,   502,   nil,   502,   502,   502,   502,   502,   502,   502,
   nil,   nil,   nil,   nil,   nil,   502,   502,   502,   502,   502,
   502,   502,   nil,   nil,   502,   nil,   nil,   nil,   nil,   nil,
   nil,   502,   nil,   nil,   502,   502,   502,   502,   502,   502,
   502,   502,   502,   502,   502,   502,   nil,   502,   502,   502,
   502,   502,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   502,   nil,   nil,   502,   nil,   nil,   502,   502,   nil,
   nil,   502,   nil,   502,   nil,   502,   nil,   502,   nil,   nil,
   502,   nil,   nil,   nil,   nil,   nil,   502,   nil,   nil,   nil,
   nil,   502,   502,   502,   502,   nil,   502,   502,   502,   502,
   nil,   nil,   nil,   nil,   502,   502,   nil,   509,   509,   509,
   nil,   509,   502,   nil,   502,   509,   509,   nil,   nil,   nil,
   509,   nil,   509,   509,   509,   509,   509,   509,   509,   nil,
   nil,   nil,   nil,   nil,   509,   509,   509,   509,   509,   509,
   509,   nil,   nil,   509,   nil,   nil,   nil,   nil,   nil,   nil,
   509,   nil,   nil,   509,   509,   509,   509,   509,   509,   509,
   509,   nil,   509,   509,   509,   nil,   509,   509,   nil,   nil,
   509,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   509,   nil,   nil,   509,   nil,   nil,   509,   509,   nil,   nil,
   509,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   509,   509,   509,   509,   nil,   509,   509,   509,   509,   nil,
   nil,   nil,   nil,   509,   509,   nil,   512,   512,   512,   nil,
   512,   509,   nil,   509,   512,   512,   nil,   nil,   nil,   512,
   nil,   512,   512,   512,   512,   512,   512,   512,   nil,   nil,
   nil,   nil,   nil,   512,   512,   512,   512,   512,   512,   512,
   nil,   nil,   512,   nil,   nil,   nil,   nil,   nil,   nil,   512,
   nil,   nil,   512,   512,   512,   512,   512,   512,   512,   512,
   nil,   512,   512,   512,   nil,   512,   512,   512,   512,   512,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   512,
   nil,   nil,   512,   nil,   nil,   512,   512,   nil,   nil,   512,
   nil,   nil,   nil,   nil,   nil,   512,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   512,   nil,   nil,   nil,   nil,   512,
   512,   512,   512,   nil,   512,   512,   512,   512,   nil,   nil,
   nil,   nil,   512,   512,   nil,   513,   513,   513,   nil,   513,
   512,   nil,   512,   513,   513,   nil,   nil,   nil,   513,   nil,
   513,   513,   513,   513,   513,   513,   513,   nil,   nil,   nil,
   nil,   nil,   513,   513,   513,   513,   513,   513,   513,   nil,
   nil,   513,   nil,   nil,   nil,   nil,   nil,   nil,   513,   nil,
   nil,   513,   513,   513,   513,   513,   513,   513,   513,   nil,
   513,   513,   513,   nil,   513,   513,   513,   513,   513,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   513,   nil,
   nil,   513,   nil,   nil,   513,   513,   nil,   nil,   513,   nil,
   nil,   nil,   nil,   nil,   513,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   513,   nil,   nil,   nil,   nil,   513,   513,
   513,   513,   nil,   513,   513,   513,   513,   nil,   nil,   nil,
   nil,   513,   513,   nil,   514,   514,   514,   nil,   514,   513,
   nil,   513,   514,   514,   nil,   nil,   nil,   514,   nil,   514,
   514,   514,   514,   514,   514,   514,   nil,   nil,   nil,   nil,
   nil,   514,   514,   514,   514,   514,   514,   514,   nil,   nil,
   514,   nil,   nil,   nil,   nil,   nil,   nil,   514,   nil,   nil,
   514,   514,   514,   514,   514,   514,   514,   514,   nil,   514,
   514,   514,   nil,   514,   514,   514,   514,   514,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   514,   nil,   nil,
   514,   nil,   nil,   514,   514,   nil,   nil,   514,   nil,   nil,
   nil,   nil,   nil,   514,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   514,   nil,   nil,   nil,   nil,   514,   514,   514,
   514,   nil,   514,   514,   514,   514,   nil,   nil,   nil,   nil,
   514,   514,   nil,   518,   518,   518,   nil,   518,   514,   nil,
   514,   518,   518,   nil,   nil,   nil,   518,   nil,   518,   518,
   518,   518,   518,   518,   518,   nil,   nil,   nil,   nil,   nil,
   518,   518,   518,   518,   518,   518,   518,   nil,   nil,   518,
   nil,   nil,   nil,   nil,   nil,   nil,   518,   nil,   nil,   518,
   518,   518,   518,   518,   518,   518,   518,   nil,   518,   518,
   518,   nil,   518,   518,   518,   518,   518,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   518,   nil,   nil,   518,
   nil,   nil,   518,   518,   nil,   nil,   518,   nil,   nil,   nil,
   nil,   nil,   518,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   518,   nil,   nil,   nil,   nil,   518,   518,   518,   518,
   nil,   518,   518,   518,   518,   nil,   nil,   nil,   nil,   518,
   518,   nil,   524,   524,   524,   nil,   524,   518,   nil,   518,
   524,   524,   nil,   nil,   nil,   524,   nil,   524,   524,   524,
   524,   524,   524,   524,   nil,   nil,   nil,   nil,   nil,   524,
   524,   524,   524,   524,   524,   524,   nil,   nil,   524,   nil,
   nil,   nil,   nil,   nil,   nil,   524,   nil,   nil,   524,   524,
   524,   524,   524,   524,   524,   524,   524,   524,   524,   524,
   nil,   524,   524,   524,   524,   524,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   524,   nil,   nil,   524,   nil,
   nil,   524,   524,   nil,   nil,   524,   nil,   524,   nil,   nil,
   nil,   524,   nil,   nil,   524,   nil,   nil,   nil,   nil,   nil,
   524,   nil,   nil,   nil,   nil,   524,   524,   524,   524,   nil,
   524,   524,   524,   524,   nil,   nil,   nil,   nil,   524,   524,
   nil,   527,   527,   527,   nil,   527,   524,   nil,   524,   527,
   527,   nil,   nil,   nil,   527,   nil,   527,   527,   527,   527,
   527,   527,   527,   nil,   nil,   nil,   nil,   nil,   527,   527,
   527,   527,   527,   527,   527,   nil,   nil,   527,   nil,   nil,
   nil,   nil,   nil,   nil,   527,   nil,   nil,   527,   527,   527,
   527,   527,   527,   527,   527,   527,   527,   527,   527,   nil,
   527,   527,   527,   527,   527,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   527,   nil,   nil,   527,   nil,   nil,
   527,   527,   nil,   nil,   527,   nil,   nil,   nil,   nil,   nil,
   527,   nil,   nil,   527,   nil,   nil,   nil,   nil,   nil,   527,
   nil,   nil,   nil,   nil,   527,   527,   527,   527,   nil,   527,
   527,   527,   527,   nil,   nil,   nil,   nil,   527,   527,   nil,
   541,   541,   541,   nil,   541,   527,   nil,   527,   541,   541,
   nil,   nil,   nil,   541,   nil,   541,   541,   541,   541,   541,
   541,   541,   nil,   nil,   nil,   nil,   nil,   541,   541,   541,
   541,   541,   541,   541,   nil,   nil,   541,   nil,   nil,   nil,
   nil,   nil,   nil,   541,   nil,   nil,   541,   541,   541,   541,
   541,   541,   541,   541,   nil,   541,   541,   541,   nil,   541,
   541,   541,   541,   541,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   541,   nil,   nil,   541,   nil,   nil,   541,
   541,   nil,   nil,   541,   nil,   541,   nil,   nil,   nil,   541,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   541,   nil,
   nil,   nil,   nil,   541,   541,   541,   541,   nil,   541,   541,
   541,   541,   nil,   nil,   nil,   nil,   541,   541,   nil,   542,
   542,   542,   nil,   542,   541,   nil,   541,   542,   542,   nil,
   nil,   nil,   542,   nil,   542,   542,   542,   542,   542,   542,
   542,   nil,   nil,   nil,   nil,   nil,   542,   542,   542,   542,
   542,   542,   542,   nil,   nil,   542,   nil,   nil,   nil,   nil,
   nil,   nil,   542,   nil,   nil,   542,   542,   542,   542,   542,
   542,   542,   542,   542,   542,   542,   542,   nil,   542,   542,
   542,   542,   542,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   542,   nil,   nil,   542,   nil,   nil,   542,   542,
   nil,   nil,   542,   nil,   542,   nil,   542,   nil,   542,   nil,
   nil,   542,   nil,   nil,   nil,   nil,   nil,   542,   nil,   nil,
   nil,   nil,   542,   542,   542,   542,   nil,   542,   542,   542,
   542,   nil,   nil,   nil,   nil,   542,   542,   nil,   552,   552,
   552,   nil,   552,   542,   nil,   542,   552,   552,   nil,   nil,
   nil,   552,   nil,   552,   552,   552,   552,   552,   552,   552,
   nil,   nil,   nil,   nil,   nil,   552,   552,   552,   552,   552,
   552,   552,   nil,   nil,   552,   nil,   nil,   nil,   nil,   nil,
   nil,   552,   nil,   nil,   552,   552,   552,   552,   552,   552,
   552,   552,   552,   552,   552,   552,   nil,   552,   552,   552,
   552,   552,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   552,   nil,   nil,   552,   nil,   nil,   552,   552,   nil,
   nil,   552,   nil,   552,   nil,   552,   nil,   552,   nil,   nil,
   552,   nil,   nil,   nil,   nil,   nil,   552,   nil,   nil,   nil,
   nil,   552,   552,   552,   552,   nil,   552,   552,   552,   552,
   nil,   nil,   nil,   nil,   552,   552,   nil,   586,   586,   586,
   nil,   586,   552,   nil,   552,   586,   586,   nil,   nil,   nil,
   586,   nil,   586,   586,   586,   586,   586,   586,   586,   nil,
   nil,   nil,   nil,   nil,   586,   586,   586,   586,   586,   586,
   586,   nil,   nil,   586,   nil,   nil,   nil,   nil,   nil,   nil,
   586,   nil,   nil,   586,   586,   586,   586,   586,   586,   586,
   586,   nil,   586,   586,   586,   nil,   586,   586,   586,   586,
   586,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   586,   nil,   nil,   586,   nil,   nil,   586,   586,   nil,   nil,
   586,   nil,   586,   nil,   nil,   nil,   586,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   586,   nil,   nil,   nil,   nil,
   586,   586,   586,   586,   nil,   586,   586,   586,   586,   nil,
   nil,   nil,   nil,   586,   586,   nil,   587,   587,   587,   nil,
   587,   586,   nil,   586,   587,   587,   nil,   nil,   nil,   587,
   nil,   587,   587,   587,   587,   587,   587,   587,   nil,   nil,
   nil,   nil,   nil,   587,   587,   587,   587,   587,   587,   587,
   nil,   nil,   587,   nil,   nil,   nil,   nil,   nil,   nil,   587,
   nil,   nil,   587,   587,   587,   587,   587,   587,   587,   587,
   nil,   587,   587,   587,   nil,   587,   587,   587,   587,   587,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   587,
   nil,   nil,   587,   nil,   nil,   587,   587,   nil,   nil,   587,
   nil,   nil,   nil,   nil,   nil,   587,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   587,   nil,   nil,   nil,   nil,   587,
   587,   587,   587,   nil,   587,   587,   587,   587,   nil,   nil,
   nil,   nil,   587,   587,   nil,   590,   590,   590,   nil,   590,
   587,   nil,   587,   590,   590,   nil,   nil,   nil,   590,   nil,
   590,   590,   590,   590,   590,   590,   590,   nil,   nil,   nil,
   nil,   nil,   590,   590,   590,   590,   590,   590,   590,   nil,
   nil,   590,   nil,   nil,   nil,   nil,   nil,   nil,   590,   nil,
   nil,   590,   590,   590,   590,   590,   590,   590,   590,   590,
   590,   590,   590,   nil,   590,   590,   590,   590,   590,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   590,   nil,
   nil,   590,   nil,   nil,   590,   590,   nil,   nil,   590,   nil,
   590,   nil,   590,   nil,   590,   nil,   nil,   590,   nil,   nil,
   nil,   nil,   nil,   590,   nil,   nil,   nil,   nil,   590,   590,
   590,   590,   nil,   590,   590,   590,   590,   nil,   nil,   nil,
   nil,   590,   590,   nil,   591,   591,   591,   nil,   591,   590,
   nil,   590,   591,   591,   nil,   nil,   nil,   591,   nil,   591,
   591,   591,   591,   591,   591,   591,   nil,   nil,   nil,   nil,
   nil,   591,   591,   591,   591,   591,   591,   591,   nil,   nil,
   591,   nil,   nil,   nil,   nil,   nil,   nil,   591,   nil,   nil,
   591,   591,   591,   591,   591,   591,   591,   591,   591,   591,
   591,   591,   nil,   591,   591,   591,   591,   591,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   591,   nil,   nil,
   591,   nil,   nil,   591,   591,   nil,   nil,   591,   nil,   nil,
   nil,   591,   nil,   591,   nil,   nil,   591,   nil,   nil,   nil,
   nil,   nil,   591,   nil,   nil,   nil,   nil,   591,   591,   591,
   591,   nil,   591,   591,   591,   591,   nil,   nil,   nil,   nil,
   591,   591,   nil,   592,   592,   592,   nil,   592,   591,   nil,
   591,   592,   592,   nil,   nil,   nil,   592,   nil,   592,   592,
   592,   592,   592,   592,   592,   nil,   nil,   nil,   nil,   nil,
   592,   592,   592,   592,   592,   592,   592,   nil,   nil,   592,
   nil,   nil,   nil,   nil,   nil,   nil,   592,   nil,   nil,   592,
   592,   592,   592,   592,   592,   592,   592,   nil,   592,   592,
   592,   nil,   592,   592,   592,   592,   592,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   592,   nil,   nil,   592,
   nil,   nil,   592,   592,   nil,   nil,   592,   nil,   nil,   nil,
   nil,   nil,   592,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   592,   nil,   nil,   nil,   nil,   592,   592,   592,   592,
   nil,   592,   592,   592,   592,   nil,   nil,   nil,   nil,   592,
   592,   nil,   593,   593,   593,   nil,   593,   592,   nil,   592,
   593,   593,   nil,   nil,   nil,   593,   nil,   593,   593,   593,
   593,   593,   593,   593,   nil,   nil,   nil,   nil,   nil,   593,
   593,   593,   593,   593,   593,   593,   nil,   nil,   593,   nil,
   nil,   nil,   nil,   nil,   nil,   593,   nil,   nil,   593,   593,
   593,   593,   593,   593,   593,   593,   nil,   593,   593,   593,
   nil,   593,   593,   593,   593,   593,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   593,   nil,   nil,   593,   nil,
   nil,   593,   593,   nil,   nil,   593,   nil,   nil,   nil,   nil,
   nil,   593,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   593,   nil,   nil,   nil,   nil,   593,   593,   593,   593,   nil,
   593,   593,   593,   593,   nil,   nil,   nil,   nil,   593,   593,
   nil,   597,   597,   597,   nil,   597,   593,   nil,   593,   597,
   597,   nil,   nil,   nil,   597,   nil,   597,   597,   597,   597,
   597,   597,   597,   nil,   nil,   nil,   nil,   nil,   597,   597,
   597,   597,   597,   597,   597,   nil,   nil,   597,   nil,   nil,
   nil,   nil,   nil,   nil,   597,   nil,   nil,   597,   597,   597,
   597,   597,   597,   597,   597,   nil,   597,   597,   597,   nil,
   597,   597,   597,   597,   597,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   597,   nil,   nil,   597,   nil,   nil,
   597,   597,   nil,   nil,   597,   nil,   nil,   nil,   nil,   nil,
   597,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   597,
   nil,   nil,   nil,   nil,   597,   597,   597,   597,   nil,   597,
   597,   597,   597,   nil,   nil,   nil,   nil,   597,   597,   nil,
   598,   598,   598,   nil,   598,   597,   nil,   597,   598,   598,
   nil,   nil,   nil,   598,   nil,   598,   598,   598,   598,   598,
   598,   598,   nil,   nil,   nil,   nil,   nil,   598,   598,   598,
   598,   598,   598,   598,   nil,   nil,   598,   nil,   nil,   nil,
   nil,   nil,   nil,   598,   nil,   nil,   598,   598,   598,   598,
   598,   598,   598,   598,   nil,   598,   598,   598,   nil,   598,
   598,   598,   598,   598,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   598,   nil,   nil,   598,   nil,   nil,   598,
   598,   nil,   nil,   598,   nil,   nil,   nil,   nil,   nil,   598,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   598,   nil,
   nil,   nil,   nil,   598,   598,   598,   598,   nil,   598,   598,
   598,   598,   nil,   nil,   nil,   nil,   598,   598,   nil,   601,
   601,   601,   nil,   601,   598,   nil,   598,   601,   601,   nil,
   nil,   nil,   601,   nil,   601,   601,   601,   601,   601,   601,
   601,   nil,   nil,   nil,   nil,   nil,   601,   601,   601,   601,
   601,   601,   601,   nil,   nil,   601,   nil,   nil,   nil,   nil,
   nil,   nil,   601,   nil,   nil,   601,   601,   601,   601,   601,
   601,   601,   601,   nil,   601,   601,   601,   nil,   601,   601,
   601,   601,   601,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   601,   nil,   nil,   601,   nil,   nil,   601,   601,
   nil,   nil,   601,   nil,   nil,   nil,   nil,   nil,   601,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   601,   nil,   nil,
   nil,   nil,   601,   601,   601,   601,   nil,   601,   601,   601,
   601,   nil,   nil,   nil,   nil,   601,   601,   nil,   602,   602,
   602,   nil,   602,   601,   nil,   601,   602,   602,   nil,   nil,
   nil,   602,   nil,   602,   602,   602,   602,   602,   602,   602,
   nil,   nil,   nil,   nil,   nil,   602,   602,   602,   602,   602,
   602,   602,   nil,   nil,   602,   nil,   nil,   nil,   nil,   nil,
   nil,   602,   nil,   nil,   602,   602,   602,   602,   602,   602,
   602,   602,   nil,   602,   602,   602,   nil,   602,   602,   602,
   602,   602,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   602,   nil,   nil,   602,   nil,   nil,   602,   602,   nil,
   nil,   602,   nil,   nil,   nil,   nil,   nil,   602,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   602,   nil,   nil,   nil,
   nil,   602,   602,   602,   602,   nil,   602,   602,   602,   602,
   nil,   nil,   nil,   nil,   602,   602,   nil,   626,   626,   626,
   nil,   626,   602,   nil,   602,   626,   626,   nil,   nil,   nil,
   626,   nil,   626,   626,   626,   626,   626,   626,   626,   nil,
   nil,   nil,   nil,   nil,   626,   626,   626,   626,   626,   626,
   626,   nil,   nil,   626,   nil,   nil,   nil,   nil,   nil,   nil,
   626,   nil,   nil,   626,   626,   626,   626,   626,   626,   626,
   626,   nil,   626,   626,   626,   nil,   626,   626,   626,   626,
   626,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   626,   nil,   nil,   626,   nil,   nil,   626,   626,   nil,   nil,
   626,   nil,   nil,   nil,   nil,   nil,   626,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   626,   nil,   nil,   nil,   nil,
   626,   626,   626,   626,   nil,   626,   626,   626,   626,   nil,
   nil,   nil,   nil,   626,   626,   nil,   632,   632,   632,   nil,
   632,   626,   nil,   626,   632,   632,   nil,   nil,   nil,   632,
   nil,   632,   632,   632,   632,   632,   632,   632,   nil,   nil,
   nil,   nil,   nil,   632,   632,   632,   632,   632,   632,   632,
   nil,   nil,   632,   nil,   nil,   nil,   nil,   nil,   nil,   632,
   nil,   nil,   632,   632,   632,   632,   632,   632,   632,   632,
   nil,   632,   632,   632,   nil,   632,   632,   nil,   nil,   632,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   632,
   nil,   nil,   632,   nil,   nil,   632,   632,   nil,   nil,   632,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   632,
   632,   632,   632,   nil,   632,   632,   632,   632,   nil,   nil,
   nil,   nil,   632,   632,   nil,   643,   643,   643,   nil,   643,
   632,   nil,   632,   643,   643,   nil,   nil,   nil,   643,   nil,
   643,   643,   643,   643,   643,   643,   643,   nil,   nil,   nil,
   nil,   nil,   643,   643,   643,   643,   643,   643,   643,   nil,
   nil,   643,   nil,   nil,   nil,   nil,   nil,   nil,   643,   nil,
   nil,   643,   643,   643,   643,   643,   643,   643,   643,   nil,
   643,   643,   643,   nil,   643,   643,   nil,   nil,   643,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   643,   nil,
   nil,   643,   nil,   nil,   643,   643,   nil,   nil,   643,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   643,   643,
   643,   643,   nil,   643,   643,   643,   643,   nil,   nil,   nil,
   nil,   643,   643,   nil,   649,   649,   649,   nil,   649,   643,
   nil,   643,   649,   649,   nil,   nil,   nil,   649,   nil,   649,
   649,   649,   649,   649,   649,   649,   nil,   nil,   nil,   nil,
   nil,   649,   649,   649,   649,   649,   649,   649,   nil,   nil,
   649,   nil,   nil,   nil,   nil,   nil,   nil,   649,   nil,   nil,
   649,   649,   649,   649,   649,   649,   649,   649,   nil,   649,
   649,   649,   nil,   649,   649,   649,   649,   649,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   649,   nil,   nil,
   649,   nil,   nil,   649,   649,   nil,   nil,   649,   nil,   649,
   nil,   nil,   nil,   649,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   649,   nil,   nil,   nil,   nil,   649,   649,   649,
   649,   nil,   649,   649,   649,   649,   nil,   nil,   nil,   nil,
   649,   649,   nil,   673,   673,   673,   nil,   673,   649,   nil,
   649,   673,   673,   nil,   nil,   nil,   673,   nil,   673,   673,
   673,   673,   673,   673,   673,   nil,   nil,   nil,   nil,   nil,
   673,   673,   673,   673,   673,   673,   673,   nil,   nil,   673,
   nil,   nil,   nil,   nil,   nil,   nil,   673,   nil,   nil,   673,
   673,   673,   673,   673,   673,   673,   673,   nil,   673,   673,
   673,   nil,   673,   673,   673,   673,   673,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   673,   nil,   nil,   673,
   nil,   nil,   673,   673,   nil,   nil,   673,   nil,   nil,   nil,
   nil,   nil,   673,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   673,   nil,   nil,   nil,   nil,   673,   673,   673,   673,
   nil,   673,   673,   673,   673,   nil,   nil,   nil,   nil,   673,
   673,   nil,   700,   700,   700,   nil,   700,   673,   nil,   673,
   700,   700,   nil,   nil,   nil,   700,   nil,   700,   700,   700,
   700,   700,   700,   700,   nil,   nil,   nil,   nil,   nil,   700,
   700,   700,   700,   700,   700,   700,   nil,   nil,   700,   nil,
   nil,   nil,   nil,   nil,   nil,   700,   nil,   nil,   700,   700,
   700,   700,   700,   700,   700,   700,   nil,   700,   700,   700,
   nil,   700,   700,   700,   700,   700,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   700,   nil,   nil,   700,   nil,
   nil,   700,   700,   nil,   nil,   700,   nil,   nil,   nil,   nil,
   nil,   700,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   700,   nil,   nil,   nil,   nil,   700,   700,   700,   700,   nil,
   700,   700,   700,   700,   nil,   nil,   nil,   nil,   700,   700,
   nil,   706,   706,   706,   nil,   706,   700,   nil,   700,   706,
   706,   nil,   nil,   nil,   706,   nil,   706,   706,   706,   706,
   706,   706,   706,   nil,   nil,   nil,   nil,   nil,   706,   706,
   706,   706,   706,   706,   706,   nil,   nil,   706,   nil,   nil,
   nil,   nil,   nil,   nil,   706,   nil,   nil,   706,   706,   706,
   706,   706,   706,   706,   706,   nil,   706,   706,   706,   nil,
   706,   706,   706,   706,   706,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   706,   nil,   nil,   706,   nil,   nil,
   706,   706,   nil,   nil,   706,   nil,   nil,   nil,   nil,   nil,
   706,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   706,
   nil,   nil,   nil,   nil,   706,   706,   706,   706,   nil,   706,
   706,   706,   706,   nil,   nil,   nil,   nil,   706,   706,   nil,
   729,   729,   729,   nil,   729,   706,   nil,   706,   729,   729,
   nil,   nil,   nil,   729,   nil,   729,   729,   729,   729,   729,
   729,   729,   nil,   nil,   nil,   nil,   nil,   729,   729,   729,
   729,   729,   729,   729,   nil,   nil,   729,   nil,   nil,   nil,
   nil,   nil,   nil,   729,   nil,   nil,   729,   729,   729,   729,
   729,   729,   729,   729,   nil,   729,   729,   729,   nil,   729,
   729,   729,   729,   729,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   729,   nil,   nil,   729,   nil,   nil,   729,
   729,   nil,   nil,   729,   nil,   nil,   nil,   nil,   nil,   729,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   729,   nil,
   nil,   nil,   nil,   729,   729,   729,   729,   nil,   729,   729,
   729,   729,   nil,   nil,   nil,   nil,   729,   729,   nil,   731,
   731,   731,   nil,   731,   729,   nil,   729,   731,   731,   nil,
   nil,   nil,   731,   nil,   731,   731,   731,   731,   731,   731,
   731,   nil,   nil,   nil,   nil,   nil,   731,   731,   731,   731,
   731,   731,   731,   nil,   nil,   731,   nil,   nil,   nil,   nil,
   nil,   nil,   731,   nil,   nil,   731,   731,   731,   731,   731,
   731,   731,   731,   nil,   731,   731,   731,   nil,   731,   731,
   731,   731,   731,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   731,   nil,   nil,   731,   nil,   nil,   731,   731,
   nil,   nil,   731,   nil,   nil,   nil,   nil,   nil,   731,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   731,   nil,   nil,
   nil,   nil,   731,   731,   731,   731,   nil,   731,   731,   731,
   731,   nil,   nil,   nil,   nil,   731,   731,   nil,   745,   745,
   745,   nil,   745,   731,   nil,   731,   745,   745,   nil,   nil,
   nil,   745,   nil,   745,   745,   745,   745,   745,   745,   745,
   nil,   nil,   nil,   nil,   nil,   745,   745,   745,   745,   745,
   745,   745,   nil,   nil,   745,   nil,   nil,   nil,   nil,   nil,
   nil,   745,   nil,   nil,   745,   745,   745,   745,   745,   745,
   745,   745,   nil,   745,   745,   745,   nil,   745,   745,   745,
   745,   745,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   745,   nil,   nil,   745,   nil,   nil,   745,   745,   nil,
   nil,   745,   nil,   nil,   nil,   nil,   nil,   745,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   745,   nil,   nil,   nil,
   nil,   745,   745,   745,   745,   nil,   745,   745,   745,   745,
   nil,   nil,   nil,   nil,   745,   745,   nil,   746,   746,   746,
   nil,   746,   745,   nil,   745,   746,   746,   nil,   nil,   nil,
   746,   nil,   746,   746,   746,   746,   746,   746,   746,   nil,
   nil,   nil,   nil,   nil,   746,   746,   746,   746,   746,   746,
   746,   nil,   nil,   746,   nil,   nil,   nil,   nil,   nil,   nil,
   746,   nil,   nil,   746,   746,   746,   746,   746,   746,   746,
   746,   nil,   746,   746,   746,   nil,   746,   746,   746,   746,
   746,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   746,   nil,   nil,   746,   nil,   nil,   746,   746,   nil,   nil,
   746,   nil,   nil,   nil,   nil,   nil,   746,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   746,   nil,   nil,   nil,   nil,
   746,   746,   746,   746,   nil,   746,   746,   746,   746,   nil,
   nil,   nil,   nil,   746,   746,   nil,   747,   747,   747,   nil,
   747,   746,   nil,   746,   747,   747,   nil,   nil,   nil,   747,
   nil,   747,   747,   747,   747,   747,   747,   747,   nil,   nil,
   nil,   nil,   nil,   747,   747,   747,   747,   747,   747,   747,
   nil,   nil,   747,   nil,   nil,   nil,   nil,   nil,   nil,   747,
   nil,   nil,   747,   747,   747,   747,   747,   747,   747,   747,
   nil,   747,   747,   747,   nil,   747,   747,   747,   747,   747,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   747,
   nil,   nil,   747,   nil,   nil,   747,   747,   nil,   nil,   747,
   nil,   nil,   nil,   nil,   nil,   747,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   747,   nil,   nil,   nil,   nil,   747,
   747,   747,   747,   nil,   747,   747,   747,   747,   nil,   nil,
   nil,   nil,   747,   747,   nil,   748,   748,   748,   nil,   748,
   747,   nil,   747,   748,   748,   nil,   nil,   nil,   748,   nil,
   748,   748,   748,   748,   748,   748,   748,   nil,   nil,   nil,
   nil,   nil,   748,   748,   748,   748,   748,   748,   748,   nil,
   nil,   748,   nil,   nil,   nil,   nil,   nil,   nil,   748,   nil,
   nil,   748,   748,   748,   748,   748,   748,   748,   748,   nil,
   748,   748,   748,   nil,   748,   748,   748,   748,   748,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   748,   nil,
   nil,   748,   nil,   nil,   748,   748,   nil,   nil,   748,   nil,
   nil,   nil,   nil,   nil,   748,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   748,   nil,   nil,   nil,   nil,   748,   748,
   748,   748,   nil,   748,   748,   748,   748,   nil,   nil,   nil,
   nil,   748,   748,   nil,   750,   750,   750,   nil,   750,   748,
   nil,   748,   750,   750,   nil,   nil,   nil,   750,   nil,   750,
   750,   750,   750,   750,   750,   750,   nil,   nil,   nil,   nil,
   nil,   750,   750,   750,   750,   750,   750,   750,   nil,   nil,
   750,   nil,   nil,   nil,   nil,   nil,   nil,   750,   nil,   nil,
   750,   750,   750,   750,   750,   750,   750,   750,   nil,   750,
   750,   750,   nil,   750,   750,   750,   750,   750,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   750,   nil,   nil,
   750,   nil,   nil,   750,   750,   nil,   nil,   750,   nil,   nil,
   nil,   nil,   nil,   750,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   750,   nil,   nil,   nil,   nil,   750,   750,   750,
   750,   nil,   750,   750,   750,   750,   nil,   nil,   nil,   nil,
   750,   750,   nil,   762,   762,   762,   nil,   762,   750,   nil,
   750,   762,   762,   nil,   nil,   nil,   762,   nil,   762,   762,
   762,   762,   762,   762,   762,   nil,   nil,   nil,   nil,   nil,
   762,   762,   762,   762,   762,   762,   762,   nil,   nil,   762,
   nil,   nil,   nil,   nil,   nil,   nil,   762,   nil,   nil,   762,
   762,   762,   762,   762,   762,   762,   762,   nil,   762,   762,
   762,   nil,   762,   762,   nil,   nil,   762,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   762,   nil,   nil,   762,
   nil,   nil,   762,   762,   nil,   nil,   762,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   762,   762,   762,   762,
   nil,   762,   762,   762,   762,   nil,   nil,   nil,   nil,   762,
   762,   nil,   801,   801,   801,   nil,   801,   762,   nil,   762,
   801,   801,   nil,   nil,   nil,   801,   nil,   801,   801,   801,
   801,   801,   801,   801,   nil,   nil,   nil,   nil,   nil,   801,
   801,   801,   801,   801,   801,   801,   nil,   nil,   801,   nil,
   nil,   nil,   nil,   nil,   nil,   801,   nil,   nil,   801,   801,
   801,   801,   801,   801,   801,   801,   nil,   801,   801,   801,
   nil,   801,   801,   801,   801,   801,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   801,   nil,   nil,   801,   nil,
   nil,   801,   801,   nil,   nil,   801,   nil,   nil,   nil,   nil,
   nil,   801,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   801,   nil,   nil,   nil,   nil,   801,   801,   801,   801,   nil,
   801,   801,   801,   801,   nil,   nil,   nil,   nil,   801,   801,
   nil,   815,   815,   815,   nil,   815,   801,   nil,   801,   815,
   815,   nil,   nil,   nil,   815,   nil,   815,   815,   815,   815,
   815,   815,   815,   nil,   nil,   nil,   nil,   nil,   815,   815,
   815,   815,   815,   815,   815,   nil,   nil,   815,   nil,   nil,
   nil,   nil,   nil,   nil,   815,   nil,   nil,   815,   815,   815,
   815,   815,   815,   815,   815,   nil,   815,   815,   815,   nil,
   815,   815,   815,   815,   815,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   815,   nil,   nil,   815,   nil,   nil,
   815,   815,   nil,   nil,   815,   nil,   nil,   nil,   nil,   nil,
   815,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   815,
   nil,   nil,   nil,   nil,   815,   815,   815,   815,   nil,   815,
   815,   815,   815,   nil,   nil,   nil,   nil,   815,   815,   nil,
   820,   820,   820,   nil,   820,   815,   nil,   815,   820,   820,
   nil,   nil,   nil,   820,   nil,   820,   820,   820,   820,   820,
   820,   820,   nil,   nil,   nil,   nil,   nil,   820,   820,   820,
   820,   820,   820,   820,   nil,   nil,   820,   nil,   nil,   nil,
   nil,   nil,   nil,   820,   nil,   nil,   820,   820,   820,   820,
   820,   820,   820,   820,   nil,   820,   820,   820,   nil,   820,
   820,   820,   820,   820,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   820,   nil,   nil,   820,   nil,   nil,   820,
   820,   nil,   nil,   820,   nil,   820,   nil,   nil,   nil,   820,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   820,   nil,
   nil,   nil,   nil,   820,   820,   820,   820,   nil,   820,   820,
   820,   820,   nil,   nil,   nil,   nil,   820,   820,   nil,   837,
   837,   837,   nil,   837,   820,   nil,   820,   837,   837,   nil,
   nil,   nil,   837,   nil,   837,   837,   837,   837,   837,   837,
   837,   nil,   nil,   nil,   nil,   nil,   837,   837,   837,   837,
   837,   837,   837,   nil,   nil,   837,   nil,   nil,   nil,   nil,
   nil,   nil,   837,   nil,   nil,   837,   837,   837,   837,   837,
   837,   837,   837,   837,   837,   837,   837,   nil,   837,   837,
   837,   837,   837,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   837,   nil,   nil,   837,   nil,   nil,   837,   837,
   nil,   nil,   837,   nil,   nil,   nil,   837,   nil,   837,   nil,
   nil,   837,   nil,   nil,   nil,   nil,   nil,   837,   nil,   nil,
   nil,   nil,   837,   837,   837,   837,   nil,   837,   837,   837,
   837,   nil,   nil,   nil,   nil,   837,   837,   nil,   838,   838,
   838,   nil,   838,   837,   nil,   837,   838,   838,   nil,   nil,
   nil,   838,   nil,   838,   838,   838,   838,   838,   838,   838,
   nil,   nil,   nil,   nil,   nil,   838,   838,   838,   838,   838,
   838,   838,   nil,   nil,   838,   nil,   nil,   nil,   nil,   nil,
   nil,   838,   nil,   nil,   838,   838,   838,   838,   838,   838,
   838,   838,   nil,   838,   838,   838,   nil,   838,   838,   838,
   838,   838,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   838,   nil,   nil,   838,   nil,   nil,   838,   838,   nil,
   nil,   838,   nil,   nil,   nil,   nil,   nil,   838,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   838,   nil,   nil,   nil,
   nil,   838,   838,   838,   838,   nil,   838,   838,   838,   838,
   nil,   nil,   nil,   nil,   838,   838,   nil,   852,   852,   852,
   nil,   852,   838,   nil,   838,   852,   852,   nil,   nil,   nil,
   852,   nil,   852,   852,   852,   852,   852,   852,   852,   nil,
   nil,   nil,   nil,   nil,   852,   852,   852,   852,   852,   852,
   852,   nil,   nil,   852,   nil,   nil,   nil,   nil,   nil,   nil,
   852,   nil,   nil,   852,   852,   852,   852,   852,   852,   852,
   852,   nil,   852,   852,   852,   nil,   852,   852,   nil,   nil,
   852,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   852,   nil,   nil,   852,   nil,   nil,   852,   852,   nil,   nil,
   852,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   852,   852,   852,   852,   nil,   852,   852,   852,   852,   nil,
   nil,   nil,   nil,   852,   852,   nil,   864,   864,   864,   nil,
   864,   852,   nil,   852,   864,   864,   nil,   nil,   nil,   864,
   nil,   864,   864,   864,   864,   864,   864,   864,   nil,   nil,
   nil,   nil,   nil,   864,   864,   864,   864,   864,   864,   864,
   nil,   nil,   864,   nil,   nil,   nil,   nil,   nil,   nil,   864,
   nil,   nil,   864,   864,   864,   864,   864,   864,   864,   864,
   nil,   864,   864,   864,   nil,   864,   864,   nil,   nil,   864,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   864,
   nil,   nil,   864,   nil,   nil,   864,   864,   nil,   nil,   864,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   864,
   864,   864,   864,   nil,   864,   864,   864,   864,   nil,   nil,
   nil,   nil,   864,   864,   nil,   970,   970,   970,   nil,   970,
   864,   nil,   864,   970,   970,   nil,   nil,   nil,   970,   nil,
   970,   970,   970,   970,   970,   970,   970,   nil,   nil,   nil,
   nil,   nil,   970,   970,   970,   970,   970,   970,   970,   nil,
   nil,   970,   nil,   nil,   nil,   nil,   nil,   nil,   970,   nil,
   nil,   970,   970,   970,   970,   970,   970,   970,   970,   970,
   970,   970,   970,   nil,   970,   970,   970,   970,   970,   517,
   517,   517,   517,   517,   517,   517,   517,   517,   517,   517,
   nil,   517,   517,   nil,   nil,   517,   517,   nil,   970,   nil,
   nil,   970,   nil,   nil,   970,   970,   nil,   nil,   970,   nil,
   970,   517,   970,   517,   970,   517,   517,   970,   517,   517,
   517,   517,   517,   970,   517,   nil,   nil,   nil,   970,   970,
   970,   970,   nil,   970,   970,   970,   970,   nil,   nil,   nil,
   nil,   970,   970,   nil,   517,    27,   nil,   nil,   nil,   970,
   nil,   970,    27,    27,    27,   nil,   nil,    27,    27,    27,
   nil,    27,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    27,    27,    27,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    27,    27,   nil,    27,    27,    27,    27,    27,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    27,    27,    27,    27,    27,    27,    27,    27,    27,
    27,    27,    27,    27,    27,   nil,   nil,    27,    27,    27,
   nil,   nil,    27,   nil,    27,    27,   nil,   nil,    27,    27,
   nil,    27,   nil,    27,   nil,    27,   nil,    27,    27,   nil,
    27,    27,    27,    27,    27,    28,    27,    27,    27,   nil,
   nil,   nil,    28,    28,    28,   nil,   nil,    28,    28,    28,
   nil,    28,    27,   nil,   nil,    27,    27,   nil,    27,   nil,
    27,    28,    28,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    28,    28,   nil,    28,    28,    28,    28,    28,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    28,    28,    28,    28,    28,    28,    28,    28,    28,
    28,    28,    28,    28,    28,   nil,   nil,    28,    28,    28,
   nil,   nil,    28,   nil,    28,    28,   nil,   nil,    28,    28,
   nil,    28,   nil,    28,   nil,    28,   nil,    28,    28,   nil,
    28,    28,    28,    28,    28,   nil,    28,   412,    28,   nil,
   nil,   nil,   nil,   nil,   412,   412,   412,   nil,   nil,   412,
   412,   412,    28,   412,   nil,    28,    28,   nil,    28,   nil,
    28,   nil,   412,   412,   412,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   412,   412,   nil,   412,   412,   412,   412,
   412,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   412,   412,   412,   412,   412,   412,   412,
   412,   412,   412,   412,   412,   412,   412,   nil,   nil,   412,
   412,   412,   nil,   nil,   412,   nil,   412,   412,   nil,   nil,
   412,   412,   nil,   412,   nil,   412,   nil,   412,   nil,   412,
   412,   nil,   412,   412,   412,   412,   412,   nil,   412,   412,
   412,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   412,   nil,   471,   412,   412,   nil,
   412,   nil,   412,   471,   471,   471,   nil,   nil,   471,   471,
   471,   613,   471,   613,   613,   613,   613,   613,   nil,   nil,
   nil,   471,   471,   nil,   nil,   nil,   613,   nil,   nil,   nil,
   nil,   nil,   471,   471,   nil,   471,   471,   471,   471,   471,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   613,   nil,
   532,   nil,   532,   532,   532,   532,   532,   613,   613,   613,
   613,   nil,   nil,   nil,   613,   532,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   471,   nil,   nil,   nil,   nil,   nil,   nil,
   471,   nil,   nil,   nil,   nil,   471,   471,   532,   532,   nil,
   613,   nil,   nil,   nil,   nil,   nil,   532,   532,   532,   532,
   nil,   nil,   nil,   532,   nil,   nil,   nil,   nil,   471,   471,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   471,   nil,   nil,   471,   nil,   nil,   nil,
   nil,   471,     8,     8,     8,     8,     8,     8,     8,     8,
     8,     8,     8,     8,     8,     8,     8,     8,     8,     8,
     8,     8,     8,     8,     8,     8,   nil,   nil,   nil,     8,
     8,     8,     8,     8,     8,     8,     8,     8,     8,   nil,
   nil,   nil,   nil,   nil,     8,     8,     8,     8,     8,     8,
     8,     8,     8,     8,   nil,     8,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,     8,     8,   nil,     8,     8,     8,     8,
     8,     8,     8,   nil,   nil,     8,     8,   nil,   nil,   nil,
     8,     8,     8,     8,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,     8,     8,   nil,
     8,     8,     8,     8,     8,     8,     8,     8,     8,     8,
     8,     8,   nil,   nil,     8,     8,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,     8,
     9,     9,     9,     9,     9,     9,     9,     9,     9,     9,
     9,     9,     9,     9,     9,     9,     9,     9,     9,     9,
     9,     9,     9,     9,   nil,   nil,   nil,     9,     9,     9,
     9,     9,     9,     9,     9,     9,     9,   nil,   nil,   nil,
   nil,   nil,     9,     9,     9,     9,     9,     9,     9,     9,
     9,   nil,   nil,     9,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,     9,     9,   nil,     9,     9,     9,     9,     9,     9,
     9,   nil,   nil,     9,     9,   nil,   nil,   nil,     9,     9,
     9,     9,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,     9,     9,   nil,     9,     9,
     9,     9,     9,     9,     9,     9,     9,     9,     9,     9,
   nil,   nil,     9,     9,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,     9,   393,   393,
   393,   393,   393,   393,   393,   393,   393,   393,   393,   393,
   393,   393,   393,   393,   393,   393,   393,   393,   393,   393,
   393,   393,   nil,   nil,   nil,   393,   393,   393,   393,   393,
   393,   393,   393,   393,   393,   nil,   nil,   nil,   nil,   nil,
   393,   393,   393,   393,   393,   393,   393,   393,   393,   nil,
   nil,   393,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   393,
   393,   nil,   393,   393,   393,   393,   393,   393,   393,   nil,
   nil,   393,   393,   nil,   nil,   nil,   393,   393,   393,   393,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   393,   393,   nil,   393,   393,   393,   393,
   393,   393,   393,   393,   393,   393,   393,   393,   nil,   nil,
   393,   393,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   393,   583,   583,   583,   583,
   583,   583,   583,   583,   583,   583,   583,   583,   583,   583,
   583,   583,   583,   583,   583,   583,   583,   583,   583,   583,
   nil,   nil,   nil,   583,   583,   583,   583,   583,   583,   583,
   583,   583,   583,   nil,   nil,   nil,   nil,   nil,   583,   583,
   583,   583,   583,   583,   583,   583,   583,   nil,   nil,   583,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   583,   583,   nil,
   583,   583,   583,   583,   583,   583,   583,   nil,   nil,   583,
   583,   nil,   nil,   nil,   583,   583,   583,   583,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   583,   583,   nil,   583,   583,   583,   583,   583,   583,
   583,   583,   583,   583,   583,   583,   nil,   nil,   583,   583,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   583,    71,    71,    71,    71,    71,    71,
    71,    71,    71,    71,    71,    71,    71,    71,    71,    71,
    71,    71,    71,    71,    71,    71,    71,    71,   nil,   nil,
   nil,    71,    71,    71,    71,    71,    71,    71,    71,    71,
    71,   nil,   nil,   nil,   nil,   nil,    71,    71,    71,    71,
    71,    71,    71,    71,    71,    71,    71,    71,   nil,    71,
   nil,   nil,   nil,   nil,   nil,    71,    71,   nil,    71,    71,
    71,    71,    71,    71,    71,   nil,   nil,    71,    71,   nil,
   nil,   nil,    71,    71,    71,    71,   nil,   nil,   nil,   nil,
   nil,    71,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    71,
    71,   nil,    71,    71,    71,    71,    71,    71,    71,    71,
    71,    71,    71,    71,   nil,   nil,    71,   713,   713,   713,
   713,   713,   713,   713,   713,   713,   713,   713,   713,   713,
   713,   713,   713,   713,   713,   713,   713,   713,   713,   713,
   713,   nil,   nil,   nil,   713,   713,   713,   713,   713,   713,
   713,   713,   713,   713,   nil,   nil,   nil,   nil,   nil,   713,
   713,   713,   713,   713,   713,   713,   713,   713,   nil,   nil,
   713,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   713,   713,
   nil,   713,   713,   713,   713,   713,   713,   713,   nil,   nil,
   713,   713,   nil,   nil,   nil,   713,   713,   713,   713,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   713,   713,   nil,   713,   713,   713,   713,   713,
   713,   713,   713,   713,   713,   713,   713,   nil,   nil,   713,
   644,   644,   644,   644,   644,   644,   644,   644,   644,   644,
   644,   nil,   644,   644,   nil,   nil,   644,   644,   nil,   nil,
   nil,   nil,   nil,   559,   nil,   559,   559,   559,   559,   559,
   nil,   nil,   644,   nil,   644,   nil,   644,   644,   559,   644,
   644,   644,   644,   644,   nil,   644,   730,   730,   730,   730,
   730,   730,   730,   730,   730,   730,   730,   nil,   730,   730,
   559,   559,   730,   730,   nil,   644,   nil,   nil,   nil,   559,
   559,   559,   559,   nil,   nil,   nil,   559,   nil,   730,   nil,
   730,   nil,   730,   730,   nil,   730,   730,   730,   730,   730,
   nil,   730,   735,   735,   735,   735,   735,   735,   735,   735,
   735,   735,   735,   nil,   735,   735,   nil,   nil,   735,   735,
   nil,   730,   655,   nil,   655,   655,   655,   655,   655,   nil,
   nil,   nil,   nil,   nil,   735,   nil,   735,   655,   735,   735,
   nil,   735,   735,   735,   735,   735,   nil,   735,   737,   737,
   737,   737,   737,   737,   737,   737,   737,   737,   737,   655,
   737,   737,   nil,   nil,   737,   737,   nil,   735,   655,   655,
   655,   655,   nil,   nil,   nil,   655,   nil,   nil,   nil,   nil,
   737,   nil,   737,   nil,   737,   737,   nil,   737,   737,   737,
   737,   737,   nil,   737,   740,   740,   740,   740,   740,   740,
   740,   740,   740,   740,   740,   nil,   740,   740,   nil,   nil,
   740,   740,   nil,   737,   711,   nil,   711,   711,   711,   711,
   711,   nil,   nil,   nil,   nil,   nil,   740,   nil,   740,   711,
   740,   740,   nil,   740,   740,   740,   740,   740,   nil,   740,
   742,   742,   742,   742,   742,   742,   742,   742,   742,   742,
   742,   711,   742,   742,   nil,   nil,   742,   742,   nil,   740,
   711,   711,   711,   711,   nil,   nil,   nil,   711,   nil,   nil,
   nil,   nil,   742,   nil,   742,   nil,   742,   742,   nil,   742,
   742,   742,   742,   742,   nil,   742,   744,   744,   744,   744,
   744,   744,   744,   744,   744,   744,   744,   nil,   744,   744,
   nil,   nil,   744,   744,   nil,   742,   795,   nil,   795,   795,
   795,   795,   795,   nil,   nil,   nil,   nil,   nil,   744,   nil,
   744,   795,   744,   744,   nil,   744,   744,   744,   744,   744,
   nil,   744,   836,   836,   836,   836,   836,   836,   836,   836,
   836,   836,   836,   795,   836,   836,   nil,   nil,   836,   836,
   nil,   744,   795,   795,   795,   795,   nil,   nil,   nil,   795,
   nil,   nil,   nil,   nil,   836,   nil,   836,   nil,   836,   836,
   nil,   836,   836,   836,   836,   836,   nil,   836,   839,   839,
   839,   839,   839,   839,   839,   839,   839,   839,   839,   nil,
   839,   839,   nil,   nil,   839,   839,   nil,   836,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   839,   nil,   839,   nil,   839,   839,   nil,   839,   839,   839,
   839,   839,   nil,   839,   nil,   nil,   nil,   nil,   nil,   209,
   209,   nil,   nil,   209,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   209,   839,   209,   209,   209,   209,   209,   209,
   209,   nil,   nil,   209,   209,   nil,   nil,   nil,   209,   209,
   209,   209,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   209,   209,   nil,   209,   209,
   209,   209,   209,   209,   209,   209,   209,   209,   209,   209,
   210,   210,   209,   nil,   210,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   210,   210,   nil,   210,   210,   210,   210,   210,
   210,   210,   nil,   nil,   210,   210,   nil,   nil,   nil,   210,
   210,   210,   210,   nil,   nil,   nil,   nil,   nil,   210,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   210,   210,   nil,   210,
   210,   210,   210,   210,   210,   210,   210,   210,   210,   210,
   210,   258,   258,   210,   nil,   258,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   258,   258,   nil,   258,   258,   258,   258,
   258,   258,   258,   nil,   nil,   258,   258,   nil,   nil,   nil,
   258,   258,   258,   258,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   258,   258,   nil,
   258,   258,   258,   258,   258,   258,   258,   258,   258,   258,
   258,   258,   437,   437,   258,   nil,   437,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   437,   437,   nil,   437,   437,   437,
   437,   437,   437,   437,   nil,   nil,   437,   437,   nil,   nil,
   nil,   437,   437,   437,   437,   nil,   nil,   nil,   nil,   nil,
   437,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   437,   437,
   nil,   437,   437,   437,   437,   437,   437,   437,   437,   437,
   437,   437,   437,   438,   438,   437,   nil,   438,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   438,   438,   nil,   438,   438,
   438,   438,   438,   438,   438,   nil,   nil,   438,   438,   nil,
   nil,   nil,   438,   438,   438,   438,   nil,   nil,   nil,   nil,
   nil,   438,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   438,
   438,   nil,   438,   438,   438,   438,   438,   438,   438,   438,
   438,   438,   438,   438,   503,   503,   438,   nil,   503,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   503,   503,   nil,   503,
   503,   503,   503,   503,   503,   503,   nil,   nil,   503,   503,
   nil,   nil,   nil,   503,   503,   503,   503,   nil,   nil,   nil,
   nil,   nil,   503,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   503,   503,   nil,   503,   503,   503,   503,   503,   503,   503,
   503,   503,   503,   503,   503,   504,   504,   503,   nil,   504,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   504,   504,   nil,
   504,   504,   504,   504,   504,   504,   504,   nil,   nil,   504,
   504,   nil,   nil,   nil,   504,   504,   504,   504,   nil,   nil,
   nil,   nil,   nil,   504,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   504,   504,   nil,   504,   504,   504,   504,   504,   504,
   504,   504,   504,   504,   504,   504,   515,   515,   504,   nil,
   515,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   515,   515,
   nil,   515,   515,   515,   515,   515,   515,   515,   nil,   nil,
   515,   515,   nil,   nil,   nil,   515,   515,   515,   515,   nil,
   nil,   nil,   nil,   nil,   515,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   515,   515,   nil,   515,   515,   515,   515,   515,
   515,   515,   515,   515,   515,   515,   515,   516,   516,   515,
   nil,   516,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   516,
   516,   nil,   516,   516,   516,   516,   516,   516,   516,   nil,
   nil,   516,   516,   nil,   nil,   nil,   516,   516,   516,   516,
   nil,   nil,   nil,   nil,   nil,   516,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   516,   516,   nil,   516,   516,   516,   516,
   516,   516,   516,   516,   516,   516,   516,   516,   543,   543,
   516,   nil,   543,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   543,   543,   nil,   543,   543,   543,   543,   543,   543,   543,
   nil,   nil,   543,   543,   nil,   nil,   nil,   543,   543,   543,
   543,   nil,   nil,   nil,   nil,   nil,   543,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   543,   543,   nil,   543,   543,   543,
   543,   543,   543,   543,   543,   543,   543,   543,   543,   544,
   544,   543,   nil,   544,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   544,   544,   nil,   544,   544,   544,   544,   544,   544,
   544,   nil,   nil,   544,   544,   nil,   nil,   nil,   544,   544,
   544,   544,   nil,   nil,   nil,   nil,   nil,   544,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   544,   544,   nil,   544,   544,
   544,   544,   544,   544,   544,   544,   544,   544,   544,   544,
   550,   550,   544,   nil,   550,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   550,   550,   nil,   550,   550,   550,   550,   550,
   550,   550,   nil,   nil,   550,   550,   nil,   nil,   nil,   550,
   550,   550,   550,   nil,   nil,   nil,   nil,   nil,   550,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   550,   550,   nil,   550,
   550,   550,   550,   550,   550,   550,   550,   550,   550,   550,
   550,   551,   551,   550,   nil,   551,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   551,   551,   nil,   551,   551,   551,   551,
   551,   551,   551,   nil,   nil,   551,   551,   nil,   nil,   nil,
   551,   551,   551,   551,   nil,   nil,   nil,   nil,   nil,   551,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   551,   551,   nil,
   551,   551,   551,   551,   551,   551,   551,   551,   551,   551,
   551,   551,   917,   917,   551,   nil,   917,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   917,   917,   nil,   917,   917,   917,
   917,   917,   917,   917,   nil,   nil,   917,   917,   nil,   nil,
   nil,   917,   917,   917,   917,   nil,   nil,   nil,   nil,   nil,
   917,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   917,   917,
   nil,   917,   917,   917,   917,   917,   917,   917,   917,   917,
   917,   917,   917,   971,   971,   917,   nil,   971,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   971,   971,   nil,   971,   971,
   971,   971,   971,   971,   971,   nil,   nil,   971,   971,   nil,
   nil,   nil,   971,   971,   971,   971,   nil,   nil,   nil,   nil,
   nil,   971,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   971,
   971,   nil,   971,   971,   971,   971,   971,   971,   971,   971,
   971,   971,   971,   971,   972,   972,   971,   nil,   972,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   972,   972,   nil,   972,
   972,   972,   972,   972,   972,   972,   nil,   nil,   972,   972,
   nil,   nil,   nil,   972,   972,   972,   972,   nil,   nil,   nil,
   nil,   nil,   972,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   972,   972,   nil,   972,   972,   972,   972,   972,   972,   972,
   972,   972,   972,   972,   972,   nil,   797,   972,   797,   797,
   797,   797,   797,   799,   nil,   799,   799,   799,   799,   799,
   nil,   797,   nil,   nil,   nil,   nil,   nil,   843,   799,   843,
   843,   843,   843,   843,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   843,   797,   nil,   nil,   nil,   nil,   nil,   nil,
   799,   nil,   797,   797,   797,   797,   nil,   nil,   nil,   797,
   nil,   799,   799,   nil,   843,   nil,   799,   nil,   nil,   nil,
   nil,   nil,   nil,   843,   843,   843,   843,   nil,   nil,   845,
   843,   845,   845,   845,   845,   845,   847,   nil,   847,   847,
   847,   847,   847,   nil,   845,   nil,   nil,   nil,   nil,   nil,
   900,   847,   900,   900,   900,   900,   900,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   900,   845,   nil,   nil,   nil,
   nil,   nil,   nil,   847,   nil,   845,   845,   845,   845,   nil,
   nil,   nil,   845,   nil,   847,   847,   nil,   900,   900,   847,
   nil,   nil,   nil,   nil,   nil,   nil,   900,   900,   900,   900,
   nil,   nil,   932,   900,   932,   932,   932,   932,   932,   934,
   nil,   934,   934,   934,   934,   934,   936,   932,   936,   936,
   936,   936,   936,   938,   934,   938,   938,   938,   938,   938,
   nil,   936,   nil,   nil,   nil,   nil,   nil,   nil,   938,   932,
   nil,   nil,   nil,   nil,   nil,   nil,   934,   nil,   932,   932,
   932,   932,   nil,   936,   nil,   932,   nil,   934,   934,   nil,
   938,   nil,   934,   nil,   936,   936,   nil,   nil,   nil,   936,
   nil,   938,   938,   nil,   nil,   956,   938,   956,   956,   956,
   956,   956,   958,   nil,   958,   958,   958,   958,   958,   960,
   956,   960,   960,   960,   960,   960,   962,   958,   962,   962,
   962,   962,   962,   998,   960,   998,   998,   998,   998,   998,
   nil,   962,   956,   nil,   nil,   nil,   nil,   nil,   998,   958,
   nil,   956,   956,   956,   956,   nil,   960,   nil,   956,   nil,
   958,   958,   nil,   962,   nil,   958,   nil,   960,   960,   nil,
   998,   nil,   960,   nil,   962,   962,   nil,   nil,   nil,   962,
   nil,   998,   998,   nil,   nil,  1008,   998,  1008,  1008,  1008,
  1008,  1008,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
  1008,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,  1008,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,  1008,  1008,   nil,   nil,   nil,  1008 ]

racc_action_pointer = [
     0,   125,   nil,    24,   nil,  5042,  1141,    39, 22340, 22468,
    84,   nil,    75,   220,   360,   247,   192,   439,   nil,    25,
  5171,  6856,   331,   nil,   111,   nil,    55, 21855, 21965,  5300,
  5429,  5558,   nil,   758,  5687,  5816,   nil,   257,   280,   282,
   390,   485,  5953,  6082,  6211,   317,   559,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   890,   nil,    67,  6340,
  6469,     0,   nil,  6598,  6727,   nil,   nil,  6856,  6993,  7122,
  7251, 22852,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   656,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,     0,   nil,   nil,   134,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   460,   nil,
  7380,   nil,   nil,   nil,   nil,  7517,  7646,  7775,  7904,  8033,
  1027,   nil,   595,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   375,   nil,  1156,  8162,  8291,  8420, 23448,
 23509,  8549,  8678,  8807,  8936,  9065,  9194,   nil,   nil,   589,
    83,   107,   465,   139,   390,   488,   nil,  9323,  1285,   524,
  9452,  9581,  9710,  9839,  9968, 10097, 10226, 10355, 10484, 10613,
 10742, 10871, 11000, 11129, 11258, 11387, 11516, 11645, 11774, 11903,
 12032, 12161, 12290, 12419, 12548, 12677,   nil,   nil, 23570,   nil,
   nil,   525, 12806, 12935,   nil,   nil,   nil,   nil,   nil,   nil,
   nil, 13064,   nil,  7380,   nil,   492,   507,   nil, 13193,   570,
 13322,   nil, 13451, 13580,   nil,   nil,    57,   nil, 13709,  1156,
   555,   540,  1414,   551,   593,   571, 13838,  1543,   591,   758,
   761,   653,   800,   nil,   627,   588,    79,   nil,   nil,   nil,
   640,   504,   607, 13975,   nil,   318,   679,   681,   808,   nil,
   687,   nil, 14104,  1672, 14233,   625,   nil,   243,   258,   663,
   652,   344,   683,   nil,   nil,   564,   133,   140, 14362, 14491,
   343,   758,   648,    32,    57,   844,   731,    65,   771,   nil,
   nil,   359,   395,   449,   nil,   931,   nil,    59, 14620,   nil,
   nil,   nil,   142,   310,   419,   464,   465,   493,   565,   567,
   630,   nil,   657,   nil, 14749,   nil,   356,   479,   484,   513,
   539,    59,    89,   546,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   701, 22596,   nil,   nil,   nil,   nil,   705,   nil,
   706,   nil, 14878,   711,   nil,  1672,   722,   nil,   724,   729,
   384,   447, 22077,   nil,   nil,   nil,   244,   356,   783,   nil,
   nil,  1804,  1938,   nil, 13838,   nil,   743,   nil,   nil,   758,
   nil,   nil,   nil,   nil,   218,   nil,   798, 23631, 23692, 15007,
   143, 15136, 15265, 15394,  1027,  1156,   873,   904,   829,   834,
   844,   846,  2978,  3107,  3236,  1285,  1414,  1543,  1753,  1887,
  2075,  2204,  2333,  2462,  2591,   633,   817,  2720,  2849, 15910,
    90, 22206,   nil,   nil,   nil,   nil,   783,   nil,   269,   293,
   784,   nil,   nil, 15523,   nil, 15652,   nil, 15781,   nil,   nil,
   nil,   nil, 15910,  1270,  2075,   794,   792,   nil,   nil,   801,
 16047,   805, 16176, 23753, 23814,   942,   847,   nil,   nil, 16305,
   805,   nil, 16434, 16563, 16692, 23875, 23936, 21723, 16821,   933,
   937,   761,   860,   nil, 16950,   nil,   nil, 17079,   nil,   nil,
   nil,   nil, 22209,  2204,   943,   nil,  2333,   158,   205,   963,
   972, 17208, 17337, 23997, 24058,   100,   nil,   nil,   959,   nil,
 24119, 24180, 17466,   nil,   nil,   nil,   415,   271,  2462, 23052,
   nil,   288,   nil,   nil,   nil,   895,   nil,   nil,   nil,   863,
   nil,   nil,   381,   nil,   406,   nil,   nil,   851,   nil,   855,
   nil,   nil,   nil, 22724,   nil,   858, 17595, 17724,   490,   900,
 17853, 17982, 18111, 18240,   904,   nil,   nil, 18369, 18498,   908,
   nil, 18627, 18756,   nil,   nil,   152,   170,   485,   619,   875,
  5816,   877,   nil, 22170,   nil,  2591,   984,    74,   308,   nil,
  2720,  2849,   nil,   885,   nil,   947, 18885,   nil,   nil,   nil,
   926,   206, 19014,   907,   nil,   911,   199,   270,   962,   425,
  1027,   964,   921, 19143, 23014,   989,   997,   325,  1055, 19272,
   nil,   953,   nil,   386,    98, 23141,   nil,   954,   975,   nil,
   980,   984,   988,   nil,   nil,   nil,   nil,   nil,   nil,   987,
   nil,   623,   nil, 19401,   nil,   nil,   nil,  1080,   nil,   nil,
   nil,  1088,   nil,   nil,  1089,   786,   nil,  1127,   nil,   nil,
   nil,   nil,  1137,   nil,   144,  1021,   116,   129,   159,   252,
 19530,   518,  1156,   nil,  1022,  2978, 19659,   nil,   nil,  1144,
  3107, 23233,   454, 22965,   nil,   nil,   nil,   nil,   nil,   nil,
  3236,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  1021, 19788,
 23060, 19917,   nil,  1031,   nil, 23106,   nil, 23152,   nil,   nil,
 23198,   nil, 23244,   nil, 23290, 20046, 20175, 20304, 20433,   250,
 20562,  1035,  1036,   nil,  1037,  1039,  1059,   nil,  1084,  1082,
  1081,  1098, 20691,   nil,   nil,  1233,   nil,   nil,  3365,  1133,
  1241,   nil,   nil,   nil,  1122,   557,   nil,   nil,  1252,   nil,
  3494,  1135,  1182,   nil,   nil,  1180,   nil,   nil,   nil,   nil,
  1145,   644,   nil,   nil,   748, 23325,   nil, 24425,   nil, 24432,
   nil, 20820,   nil,   854,   nil,  1147,   357,   nil,  1151,   nil,
   nil,   nil,   nil,  1273,   nil, 20949,  1274,  3623,  3752,   nil,
 21078,  3881,   139,   224,   nil,  1284,   607,  4010,   nil,  1288,
  1168,   nil,   nil,  1172,  1168,   nil, 23336, 21207, 21336, 23382,
   667,   nil,   nil, 24446,   nil, 24498,   nil, 24505,   nil,   nil,
  1212,   959, 21465,  1030,  1272,   nil,  1228,   nil,   nil,   nil,
  4139,   nil,   nil,   160, 21594,   nil,   nil,  4268,  4397,  1255,
  1230,   nil,   nil,   nil,  1231,  1233,   nil,  1238,  1239,   nil,
  1242,   nil,  1249,   890,  1248,  1271,   nil,   nil,   166,   nil,
  1375,  1380,   nil,   391,   nil,   nil,   nil,  1388,   nil,   nil,
 24519,   nil,  1265,   nil,   nil,  1266,  1271,  1276,  1277,   nil,
  1278,   nil,   652,   nil,   nil,   nil,  1159, 24241,   nil,   nil,
   nil,  4526,  1281,  1285,  1417,  1357,  1526,   nil,  1309,  1417,
   nil,   nil, 24571,   nil, 24578,   nil, 24585,   nil, 24592,   nil,
   nil,   nil,   nil,   481,  1400,  1294,  4655,   nil,   nil,   nil,
   nil,   nil,  4784,   nil,  4913,   nil, 24644,   nil, 24651,   nil,
 24658,   nil, 24665,   nil,   nil,   nil,  1013,  1339,  1341,  1431,
 21723, 24302, 24363,  1534,   nil,   nil,  1306,  1307,  1308,  1309,
  1317,  1430,  1323,  1438,   857,  1461,  1465,  1349,  1353,  1357,
  1365,   nil,   nil,  1370,   167,   168,   170,  1672, 24672,   nil,
   nil,   nil,   nil,  1529,  1370,   nil,   nil,   nil, 24724,   nil,
   nil,   nil,   nil,   169,  1371,  1374,  1375,   nil,   nil ]

racc_action_default = [
    -3,  -591,    -1,  -577,    -4,  -591,    -7,  -591,  -591,  -591,
  -591,   -29,  -591,  -591,  -591,  -279,  -591,   -41,   -44,  -579,
  -591,   -49,   -51,   -52,   -53,   -57,  -258,  -258,  -258,  -293,
  -329,  -330,   -69,   -11,   -73,   -81,   -83,  -591,  -488,  -489,
  -591,  -591,  -591,  -591,  -591,  -579,  -239,  -270,  -271,  -272,
  -273,  -274,  -275,  -276,  -277,  -278,  -567,  -281,  -283,  -590,
  -558,  -301,  -303,  -591,  -591,  -307,  -310,  -577,  -591,  -591,
  -591,  -591,  -331,  -332,  -334,  -335,  -431,  -432,  -433,  -434,
  -435,  -456,  -438,  -439,  -458,  -460,  -443,  -448,  -452,  -454,
  -470,  -458,  -472,  -473,  -565,  -477,  -478,  -566,  -480,  -481,
  -482,  -483,  -484,  -485,  -486,  -487,  -492,  -493,  -591,    -2,
  -578,  -586,  -587,  -588,    -6,  -591,  -591,  -591,  -591,  -591,
    -3,   -17,  -591,  -112,  -113,  -114,  -115,  -116,  -117,  -118,
  -119,  -120,  -124,  -125,  -126,  -127,  -128,  -129,  -130,  -131,
  -132,  -133,  -134,  -135,  -136,  -137,  -138,  -139,  -140,  -141,
  -142,  -143,  -144,  -145,  -146,  -147,  -148,  -149,  -150,  -151,
  -152,  -153,  -154,  -155,  -156,  -157,  -158,  -159,  -160,  -161,
  -162,  -163,  -164,  -165,  -166,  -167,  -168,  -169,  -170,  -171,
  -172,  -173,  -174,  -175,  -176,  -177,  -178,  -179,  -180,  -181,
  -182,  -183,  -184,  -185,  -186,  -187,  -188,  -189,  -190,  -191,
  -192,  -193,  -194,   -22,  -121,   -11,  -591,  -591,  -248,  -591,
  -591,  -591,  -591,  -591,  -591,  -591,  -579,  -580,   -48,  -591,
  -488,  -489,  -591,  -279,  -591,  -591,  -231,  -591,   -11,  -591,
  -591,  -591,  -591,  -591,  -591,  -591,  -591,  -591,  -591,  -591,
  -591,  -591,  -591,  -591,  -591,  -591,  -591,  -591,  -591,  -591,
  -591,  -591,  -591,  -591,  -591,  -591,  -400,  -402,  -591,  -575,
  -576,   -58,  -248,  -591,  -300,  -406,  -415,  -417,   -64,  -412,
   -65,  -579,   -66,  -240,  -253,  -262,  -262,  -257,  -591,  -263,
  -591,  -560,  -591,  -591,   -67,   -68,  -577,   -12,  -591,   -15,
  -591,   -71,   -11,  -579,  -591,   -74,   -77,   -11,   -89,   -90,
  -591,  -591,   -97,  -293,  -296,  -579,  -591,  -329,  -330,  -333,
  -413,  -591,   -79,  -591,   -85,  -290,  -474,  -475,  -591,  -216,
  -217,  -232,  -591,   -11,  -591,  -579,  -241,  -583,  -583,  -591,
  -591,  -583,  -591,  -302,  -392,   -50,  -591,  -591,  -591,  -591,
  -577,  -591,  -578,  -488,  -489,  -591,  -591,  -279,  -591,  -345,
  -346,  -107,  -108,  -591,  -110,  -591,  -279,  -591,  -591,  -488,
  -489,  -322,  -112,  -113,  -154,  -155,  -156,  -172,  -177,  -184,
  -187,  -324,  -591,  -556,  -591,  -436,  -591,  -591,  -591,  -591,
  -591,  -591,  -591,  -591,  1019,    -5,  -589,   -23,   -24,   -25,
   -26,   -27,  -591,  -591,   -19,   -20,   -21,  -122,  -591,   -30,
   -39,   -40,  -591,  -591,   -31,  -197,  -579,  -249,  -262,  -262,
  -568,  -569,  -258,  -410,  -570,  -571,  -569,  -568,  -258,  -409,
  -411,  -570,  -571,   -37,  -205,   -38,  -591,   -42,   -43,  -195,
  -263,   -45,   -46,   -47,  -579,  -299,  -591,  -591,  -591,  -248,
  -290,  -591,  -591,  -591,  -206,  -207,  -208,  -209,  -210,  -211,
  -212,  -213,  -218,  -219,  -220,  -221,  -222,  -223,  -224,  -225,
  -226,  -227,  -228,  -229,  -230,  -233,  -234,  -235,  -236,  -579,
  -381,  -258,  -568,  -569,   -55,   -59,  -579,  -259,  -381,  -381,
  -579,  -295,  -254,  -591,  -255,  -591,  -260,  -591,  -264,  -563,
  -564,   -10,  -578,   -14,    -3,  -579,   -70,  -288,   -86,   -75,
  -591,  -579,  -248,  -591,  -591,   -96,  -591,  -474,  -475,  -591,
   -82,   -87,  -591,  -591,  -591,  -591,  -591,  -237,  -591,  -423,
  -591,  -284,  -591,  -242,  -585,  -584,  -244,  -585,  -291,  -292,
  -559,  -304,  -520,   -11,  -336,  -337,   -11,  -591,  -591,  -591,
  -591,  -591,  -248,  -591,  -591,  -290,  -315,  -107,  -108,  -109,
  -591,  -591,  -248,  -318,  -494,  -495,  -591,  -591,   -11,  -520,
  -326,  -579,  -437,  -457,  -462,  -591,  -464,  -440,  -459,  -591,
  -461,  -442,  -591,  -445,  -591,  -447,  -450,  -591,  -451,  -591,
  -471,    -8,   -18,  -591,   -28,  -269,  -591,  -591,  -414,  -591,
  -250,  -252,  -591,  -591,   -60,  -247,  -407,  -591,  -591,   -62,
  -408,  -591,  -591,  -298,  -581,  -568,  -569,  -568,  -569,  -579,
  -195,  -591,  -382,  -579,  -384,   -11,   -54,  -403,  -381,  -245,
   -11,   -11,  -294,  -262,  -261,  -265,  -591,  -561,  -562,   -13,
  -591,   -72,  -591,   -78,   -84,  -579,  -568,  -569,  -246,   -93,
   -95,  -591,   -80,  -591,  -204,  -214,  -215,  -579,  -590,  -590,
  -282,  -579,  -287,  -583,  -591,  -520,  -395,  -555,  -555,  -503,
  -505,  -505,  -505,  -519,  -521,  -522,  -523,  -524,  -525,  -526,
  -527,  -591,  -529,  -591,  -535,  -537,  -538,  -540,  -545,  -547,
  -548,  -550,  -551,  -552,  -591,  -590,  -338,  -590,  -308,  -339,
  -340,  -311,  -591,  -314,  -591,  -579,  -568,  -569,  -572,  -289,
  -591,  -107,  -108,  -111,  -579,   -11,  -591,  -497,  -320,  -591,
   -11,  -520,  -591,  -591,  -557,  -463,  -466,  -467,  -468,  -469,
   -11,  -441,  -444,  -446,  -449,  -453,  -455,  -123,  -267,  -591,
  -198,  -591,  -582,  -262,   -33,  -200,   -34,  -201,   -61,   -35,
  -203,   -36,  -202,   -63,  -196,  -591,  -591,  -591,  -591,  -414,
  -591,  -555,  -555,  -363,  -365,  -365,  -365,  -380,  -591,  -579,
  -386,  -526,  -591,  -533,  -543,  -591,  -405,  -404,   -11,  -591,
  -591,  -256,  -266,   -16,   -76,   -91,   -88,  -297,  -590,  -343,
   -11,  -424,  -590,  -425,  -426,  -591,  -243,  -393,  -396,  -398,
  -579,  -591,  -501,  -502,  -591,  -591,  -512,  -591,  -515,  -591,
  -517,  -591,  -347,  -591,  -349,  -351,  -358,  -526,  -579,  -531,
  -539,  -549,  -553,  -591,  -341,  -591,  -591,   -11,   -11,  -313,
  -591,   -11,  -414,  -591,  -414,  -591,  -591,   -11,  -323,  -591,
  -579,  -499,  -327,  -591,  -268,   -32,  -199,  -251,  -591,  -238,
  -591,  -361,  -362,  -371,  -373,  -591,  -376,  -591,  -378,  -383,
  -591,  -591,  -591,  -532,  -591,  -401,  -591,  -416,  -418,    -9,
   -11,  -430,  -344,  -591,  -591,  -428,  -285,   -11,   -11,  -591,
  -555,  -536,  -554,  -504,  -505,  -505,  -530,  -505,  -505,  -546,
  -505,  -541,  -579,  -591,  -356,  -591,  -528,  -305,  -591,  -306,
  -591,  -591,  -265,  -590,  -316,  -319,  -496,  -591,  -325,  -498,
  -520,  -465,  -555,  -534,  -364,  -365,  -365,  -365,  -365,  -544,
  -365,  -385,  -579,  -388,  -390,  -391,  -542,  -591,  -290,   -56,
  -429,   -11,   -98,   -99,  -591,  -591,  -106,  -427,  -591,  -591,
  -394,  -500,  -591,  -508,  -591,  -510,  -591,  -513,  -591,  -516,
  -518,  -348,  -350,  -354,  -591,  -359,   -11,  -309,  -312,  -419,
  -420,  -421,   -11,  -321,   -11,  -360,  -591,  -368,  -591,  -370,
  -591,  -374,  -591,  -377,  -379,  -387,  -591,  -289,  -572,  -423,
  -248,  -591,  -591,  -105,  -397,  -399,  -505,  -505,  -505,  -505,
  -352,  -591,  -357,  -591,  -590,  -591,  -591,  -365,  -365,  -365,
  -365,  -389,  -422,  -579,  -568,  -569,  -572,  -104,  -591,  -506,
  -509,  -511,  -514,  -591,  -355,  -342,  -317,  -328,  -591,  -366,
  -369,  -372,  -375,  -414,  -505,  -353,  -365,  -507,  -367 ]

racc_goto_table = [
   215,   406,   295,   265,   269,    14,   333,   126,   126,   326,
    14,   218,   274,   274,   274,   371,   257,   540,   520,     2,
   113,   121,   204,   533,   536,   219,   322,   401,   648,   428,
   484,   412,   418,   425,   219,   219,   219,   258,    14,   300,
   300,   710,   129,   129,   336,   337,   131,   131,   340,   627,
   110,   435,   309,   309,   778,   476,   663,   261,   268,   270,
   475,   624,     6,   624,   752,   813,   312,     6,   219,   219,
   126,   588,   219,   345,   355,   355,   549,   335,   335,   755,
   471,   335,   808,   663,   113,   656,   309,   309,   309,   879,
   909,   627,   913,   816,   758,   109,   387,   388,   389,   390,
   523,   526,     1,   291,   530,   876,   481,   275,   275,   275,
   792,   793,   276,   276,   276,    14,   915,   510,   688,   691,
   219,   219,   219,   219,    14,    14,   756,   293,   496,   335,
   335,   335,   335,   114,   942,   357,   361,   327,   377,   392,
   945,   859,   328,   331,   563,   383,   570,   573,   573,   572,
   574,   629,   203,   876,   350,   627,   393,   615,   618,   341,
   272,   284,   285,   484,   583,   620,   621,   329,   624,   624,
   617,   373,     6,   330,   531,   348,   553,   372,   323,   663,
   324,   391,     6,   651,   325,   334,   413,   338,   817,   339,
   818,   274,   700,   952,   705,   827,   558,   399,   404,   982,
   559,   909,   423,   427,   841,   842,   713,   991,   790,   422,
    14,   219,   219,   219,   882,   692,   219,   219,   219,   219,
   219,   219,   900,   757,   879,   759,   912,   431,   432,   433,
   434,   915,   609,    14,   942,   663,  1004,   654,   385,   787,
   850,   532,   870,   867,   868,   274,   274,   470,   876,   478,
   479,   949,   782,   709,   274,   863,    13,   375,  1015,   412,
   418,    13,   376,   378,   830,   379,   380,   219,   219,   603,
   381,   869,   876,   703,   749,   382,   219,   715,   720,   706,
   874,   511,   871,   265,   403,   903,   506,   269,   nil,    13,
   403,   902,   nil,    14,   752,   635,   752,    14,   752,   950,
   775,   300,    14,   113,   633,   768,   522,   nil,   875,   905,
   877,   619,   nil,   642,   309,   622,   nil,   nil,   300,   537,
   538,   nil,   nil,   931,   nil,   638,   nil,   499,    14,   219,
   631,   309,   nil,   492,   nil,   695,   634,   638,   521,   nil,
   723,   275,   723,   219,   219,   704,   276,   nil,   nil,   275,
   493,   nil,   335,   335,   276,   955,   906,   113,   907,   nil,
   822,   nil,   291,   219,  1005,   638,    13,   291,   nil,   824,
   nil,   nil,   557,   638,   554,    13,    13,   771,   491,   219,
   nil,   821,   954,   nil,   nil,   560,   495,   589,   561,   595,
   nil,   501,   126,   738,   477,   600,   714,   nil,   743,   nil,
   825,   627,   480,   nil,   894,   829,   582,   752,   nil,   752,
   nil,   752,   nil,   752,   413,   624,   nil,   nil,   428,   nil,
   nil,   nil,   274,   nil,   663,   nil,   786,   129,   nil,   nil,
   nil,   131,   539,   nil,   nil,   nil,   774,   422,   nil,   nil,
   nil,   nil,   nil,   594,   219,   976,   nil,   nil,   595,   599,
   611,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   752,
   nil,    13,   nil,   nil,   nil,   nil,   nil,   nil,   511,   987,
   783,   nil,   nil,   nil,   nil,   nil,   nil,   511,   992,   nil,
   413,   nil,   777,   nil,    13,   274,   nil,   771,   nil,   nil,
   nil,   nil,   413,   nil,   nil,   nil,   nil,    14,   nil,    14,
   nil,   nil,   616,   422,   nil,   300,   nil,   219,   nil,   nil,
   nil,   nil,   nil,   630,   300,   422,   nil,   nil,   309,   nil,
   413,   nil,   897,   219,   nil,   274,   nil,   309,   413,   nil,
   nil,   nil,   647,   nil,   nil,   274,   nil,   nil,    14,   nil,
   nil,    14,   nil,   422,    13,   nil,   nil,   219,    13,   422,
   921,   nil,   294,    13,   689,   689,     6,   219,   nil,   nil,
   nil,   nil,   767,    14,   nil,   nil,   623,   nil,   nil,   951,
   nil,   nil,   nil,   707,   708,   946,   nil,   nil,   nil,    13,
   nil,   nil,   126,   734,   736,   nil,   nil,   nil,   739,   741,
   589,   nil,   427,   nil,   760,   nil,   727,   219,   219,   784,
   511,   nil,   219,   219,   nil,   nil,   219,   653,   nil,   nil,
   766,   776,   nil,   nil,   nil,   595,   589,   129,   600,   694,
    14,   131,   nil,   nil,   nil,    14,    14,   nil,   nil,   nil,
   nil,   nil,   785,   nil,   nil,   nil,   nil,   300,   nil,    26,
   nil,   nil,   nil,   886,    26,   nil,   nil,   nil,   300,   986,
   309,   nil,   nil,   753,   nil,   nil,   nil,   832,  1013,    26,
   nil,   309,   nil,   nil,   nil,   899,   nil,   nil,    26,    26,
    26,   nil,    26,   733,   nil,   nil,   589,   nil,   nil,   nil,
   754,   823,   nil,   nil,   nil,   589,   nil,   826,   nil,   796,
   798,   800,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    26,    26,   nil,   219,    26,   802,   nil,   nil,
    14,   219,   126,   nil,   335,    14,   nil,   941,   nil,   nil,
   335,   nil,   835,   nil,   398,    14,   nil,   403,   861,   831,
   nil,   nil,   865,   nil,   nil,   nil,   219,   nil,   nil,   nil,
   760,   nil,   nil,   nil,   nil,   nil,   nil,   294,    13,    26,
    13,   nil,   nil,   nil,    26,    26,    26,    26,    26,    26,
   nil,   nil,   nil,   993,   nil,   nil,   nil,   853,   nil,   nil,
   nil,   760,   nil,    14,   nil,   nil,   nil,   nil,   nil,   nil,
   309,   nil,   nil,   nil,   nil,    14,   nil,   nil,   nil,    13,
   nil,   nil,    13,   638,   nil,   nil,   888,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   294,   nil,   nil,    13,   nil,   294,   nil,   nil,   nil,
   219,   nil,    14,    14,   nil,   nil,    14,   nil,   nil,   335,
    16,   872,    14,   nil,   872,    16,   844,   846,   848,   802,
   689,   nil,   884,   896,    26,    26,    26,    26,   nil,   nil,
    26,    26,    26,    26,    26,    26,   nil,   916,   nil,   nil,
   nil,   nil,   nil,    16,   878,    14,   880,    26,   nil,   924,
   309,    13,    14,    14,   nil,   nil,    13,    13,   nil,   nil,
   872,   nil,   309,   753,   nil,   753,   nil,   753,   nil,   nil,
   nil,   nil,   nil,   965,   nil,   nil,   nil,   nil,   349,   nil,
   nil,    26,    26,   933,   935,   nil,   937,   939,   nil,   940,
    26,   nil,   908,   nil,   910,   nil,   422,   nil,   nil,   802,
   nil,   802,   nil,   nil,   nil,   nil,    14,    26,   nil,   nil,
   nil,    26,   nil,   nil,   nil,   nil,    26,   nil,   nil,   nil,
    16,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   413,    16,
    16,    14,   nil,   274,   nil,   nil,   nil,    14,   nil,    14,
   nil,    13,    26,    26,   nil,   nil,    13,   nil,   nil,   nil,
   nil,   422,   nil,   nil,   589,   219,    13,    26,    26,   980,
   802,    15,   nil,   nil,   nil,   nil,    15,   957,   959,   961,
   963,   nil,   964,   nil,   nil,   nil,   753,    26,   753,   977,
   753,   978,   753,   979,   nil,   999,  1000,  1001,  1002,   nil,
   nil,   nil,   nil,    26,    15,   302,   302,   802,   nil,   802,
   nil,   nil,   nil,   988,    13,   989,   nil,   990,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    16,    13,   nil,   nil,   802,
   nil,   nil,   426,  1017,   nil,   nil,   nil,   nil,   753,   347,
   356,   356,   685,   nil,   nil,   687,   nil,   nil,    16,   nil,
   nil,   nil,   nil,   nil,   nil,  1014,   nil,   nil,   nil,  1009,
  1010,  1011,  1012,    13,    13,  1016,   nil,    13,    26,   nil,
   nil,   nil,   nil,    13,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    15,   nil,   nil,   nil,   nil,   nil,   nil,  1018,   nil,
    15,    15,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    13,   nil,    16,   nil,
   nil,   nil,    16,    13,    13,   nil,   nil,    16,   nil,   nil,
   nil,    26,   nil,    26,   765,   nil,   nil,   nil,   nil,   769,
   770,    26,   nil,   nil,   nil,    38,   nil,   nil,   nil,   nil,
    38,   nil,   nil,    16,   nil,   nil,   nil,    26,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    26,   nil,   nil,    26,   nil,    13,    38,   298,
   298,    26,   nil,   nil,   nil,   nil,    15,   nil,   nil,   nil,
   nil,    26,   nil,   nil,   nil,   nil,   nil,    26,   nil,   nil,
   nil,   nil,    13,   nil,   nil,   nil,   nil,   nil,    13,    15,
    13,   nil,   nil,   343,   359,   359,   359,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    26,    26,   nil,   nil,   nil,    26,    26,   nil,   833,
    26,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    26,    38,   nil,   nil,   nil,    26,
    26,   nil,   nil,   nil,    38,    38,   nil,   nil,   nil,    15,
   nil,   nil,   nil,    15,   nil,   nil,   nil,   302,    15,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   856,   nil,   nil,
   nil,   nil,   nil,   nil,   302,   nil,   nil,   nil,   nil,   862,
   nil,   nil,   nil,   nil,    15,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    16,   nil,    16,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   890,   891,   nil,    26,
   893,   nil,   nil,   nil,    26,    26,   nil,   nil,   nil,    26,
    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,
   nil,   nil,   nil,    16,   nil,   nil,    16,   nil,   nil,   nil,
    26,   nil,   nil,    38,   nil,   nil,   nil,   nil,   nil,   920,
   nil,   nil,   nil,   nil,   nil,   nil,   928,   929,    16,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   426,   nil,    38,   nil,   nil,   nil,    38,   nil,   nil,
   969,   298,    38,   nil,   nil,    16,   nil,   nil,   nil,   nil,
    16,    16,   nil,   nil,    26,   nil,    26,    26,   298,   nil,
    26,   nil,   nil,   nil,   nil,   984,    26,   nil,    38,   nil,
   nil,   985,   nil,    15,   nil,    15,   nil,   nil,   nil,   nil,
   nil,   302,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   302,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,
   nil,   nil,   nil,   nil,   nil,   nil,    26,    26,   nil,   nil,
   nil,   nil,   nil,   nil,    15,   nil,   nil,    15,   nil,   332,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    16,   nil,   nil,   nil,    15,
    16,   nil,   nil,   nil,   nil,   nil,   719,   nil,   nil,   nil,
    16,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    26,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    26,   nil,   nil,   nil,   nil,
   nil,    26,    39,    26,   nil,   nil,    15,    39,    16,   nil,
   nil,    15,    15,   nil,   nil,   nil,   nil,   nil,   nil,    26,
    16,   nil,   nil,   302,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   302,    39,   299,   299,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    38,   nil,    38,
   nil,   nil,   nil,   nil,   nil,   298,   nil,    16,    16,   nil,
   nil,    16,   nil,   nil,   298,   nil,   nil,    16,   nil,   nil,
   344,   360,   360,   360,   nil,   400,   nil,   nil,   nil,   nil,
   nil,   430,   nil,   nil,   nil,   nil,   nil,   nil,    38,   nil,
   nil,    38,   nil,   nil,   nil,   nil,    15,   nil,   nil,   nil,
    16,    15,   nil,   nil,   927,   nil,   nil,    16,    16,   nil,
   nil,    15,    39,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    39,    39,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   486,   nil,   488,
   nil,   489,   490,   356,   nil,   nil,   nil,   nil,   nil,    15,
   nil,    16,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    38,    15,   nil,   nil,   nil,    38,    38,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    16,   298,   nil,   nil,
   nil,   nil,    16,   nil,    16,   nil,   nil,   nil,   298,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    39,    15,    15,
   nil,   nil,    15,   nil,   nil,   nil,   nil,   nil,    15,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    39,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   356,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    15,   nil,   nil,   nil,   926,   nil,   nil,    15,    15,
    38,   nil,   nil,   nil,   nil,    38,   nil,   nil,   nil,   nil,
   nil,   585,   nil,   nil,   nil,    38,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    39,   nil,   nil,   nil,    39,   nil,   nil,   nil,   299,    39,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    15,   nil,   nil,   299,   nil,   359,   nil,   nil,
   nil,   nil,   nil,    38,   nil,    39,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    38,   nil,    15,   nil,   nil,
   nil,   nil,   nil,    15,   nil,    15,   nil,   nil,   nil,   nil,
   nil,   nil,   625,   nil,   332,   nil,   628,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    38,    38,   nil,   nil,    38,   nil,   nil,   nil,
   nil,   nil,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   625,   nil,   nil,   332,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   359,   nil,   nil,
   430,   nil,   nil,   nil,   nil,    38,   nil,   nil,   nil,   922,
   nil,   nil,    38,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   728,   nil,   nil,   nil,   625,
   332,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    38,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    39,   772,    39,   nil,   nil,   nil,
   nil,    38,   299,   nil,   nil,   nil,   nil,    38,   nil,    38,
   nil,   299,   nil,   nil,   nil,   nil,   nil,   nil,   781,   nil,
   nil,   nil,   nil,   226,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   273,   273,   273,    39,   nil,   nil,    39,   nil,
   nil,   nil,   809,   nil,   nil,   319,   320,   321,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    39,   nil,   273,   273,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   834,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    39,   nil,   nil,
   nil,   nil,    39,    39,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   299,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   299,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   881,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   892,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   332,    39,   nil,   273,
   405,   273,    39,   nil,   424,   429,   nil,   nil,   nil,   nil,
   nil,   nil,    39,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   226,   nil,   nil,   444,   445,   446,   447,   448,   449,   450,
   451,   452,   453,   454,   455,   456,   457,   458,   459,   460,
   461,   462,   463,   464,   465,   466,   467,   468,   469,   nil,
   nil,   nil,   nil,   nil,   360,   273,   273,   nil,   nil,   nil,
    39,   nil,   nil,   nil,   273,   nil,   nil,   nil,   nil,   nil,
   nil,   273,    39,   273,   nil,   273,   273,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    39,
    39,   nil,   nil,    39,   nil,   517,   nil,   nil,   nil,    39,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   360,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    39,   nil,   nil,   nil,   923,   nil,   nil,    39,
    39,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   273,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    39,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   273,   nil,   424,   610,   405,   nil,    39,   nil,
   nil,   nil,   nil,   nil,    39,   nil,    39,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   273,   nil,   273,   nil,
   273,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   273,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   644,   645,   646,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   273,   nil,   nil,
   273,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   273,   273,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   273,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   273,
   730,   nil,   nil,   273,   273,   735,   737,   nil,   nil,   nil,
   740,   742,   nil,   nil,   610,   744,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   273,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   273,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   273,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   273,   nil,   836,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   735,   737,
   742,   740,   nil,   839,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   273,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   273,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   273,   836,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   273 ]

racc_goto_check = [
    31,    23,    49,    69,    69,    22,    84,    57,    57,    65,
    22,    20,    33,    33,    33,    56,   138,    90,     8,     2,
    94,    15,    15,    87,    87,    22,    31,    27,    10,    18,
    71,    37,    37,    27,    22,    22,    22,    36,    22,    22,
    22,    96,    60,    60,    17,    17,    61,    61,    17,   175,
     6,    47,    63,    63,    11,    23,   163,    38,    38,    38,
    39,    72,     7,    72,   121,    88,    50,     7,    22,    22,
    57,    24,    22,    22,    22,    22,    54,    29,    29,   127,
    37,    29,   117,   163,    94,   137,    63,    63,    63,   171,
   172,   175,   132,    11,   130,     4,    17,    17,    17,    17,
    67,    67,     1,    45,    67,   167,    47,    66,    66,    66,
   122,   122,    68,    68,    68,    22,   133,    51,    89,    89,
    22,    22,    22,    22,    22,    22,   128,    46,    47,    29,
    29,    29,    29,     5,   115,    55,    55,    66,   152,     2,
   118,    12,    68,    68,   156,   152,   156,   156,   156,   155,
   155,    14,    16,   167,    19,   175,    30,    40,    42,     4,
    44,    44,    44,    71,    62,    40,    40,    64,    72,    72,
    70,    82,     7,    83,    86,    91,    93,    97,    98,   163,
    99,     7,     7,   100,   101,   102,    69,   103,   104,   105,
   106,    33,   107,   108,   109,   110,   111,    20,    20,   118,
   112,   172,    20,    20,   122,   122,   113,   132,   137,    57,
    22,    22,    22,    22,   117,    90,    22,    22,    22,    22,
    22,    22,   114,   119,   171,   125,   131,    29,    29,    29,
    29,   133,    23,    22,   115,   163,   118,   134,     5,   135,
   130,   136,   121,   139,   140,    33,    33,   141,   167,   143,
   144,   145,   146,     8,    33,   147,    21,   150,   118,    37,
    37,    21,   151,   153,   137,   154,   157,    22,    22,    47,
   158,   130,   167,    54,    24,   159,    22,   160,   161,   162,
   166,    49,   168,    69,    66,   169,    31,    69,   nil,    21,
    66,   121,   nil,    22,   121,    23,   121,    22,   121,    11,
    24,    22,    22,    94,    51,    40,    31,   nil,   128,   127,
   128,    47,   nil,    51,    63,    47,   nil,   nil,    22,    17,
    17,   nil,   nil,   122,   nil,    37,   nil,    50,    22,    22,
    47,    63,   nil,     6,   nil,    23,    47,    37,    29,   nil,
   156,    66,   156,    22,    22,    23,    68,   nil,   nil,    66,
     7,   nil,    29,    29,    68,   122,   128,    94,   128,   nil,
    24,   nil,    45,    22,    88,    37,    21,    45,   nil,    24,
   nil,   nil,    29,    37,    94,    21,    21,    71,     4,    22,
   nil,    87,    96,   nil,   nil,    36,    46,    31,    29,    69,
   nil,    46,    57,    39,    44,    69,    47,   nil,    39,   nil,
     8,   175,    44,   nil,    89,     8,    15,   121,   nil,   121,
   nil,   121,   nil,   121,    69,    72,   nil,   nil,    18,   nil,
   nil,   nil,    33,   nil,   163,   nil,    67,    60,   nil,   nil,
   nil,    61,     4,   nil,   nil,   nil,    51,    57,   nil,   nil,
   nil,   nil,   nil,    38,    22,   128,   nil,   nil,    69,    38,
    31,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   121,
   nil,    21,   nil,   nil,   nil,   nil,   nil,   nil,    49,   128,
    27,   nil,   nil,   nil,   nil,   nil,   nil,    49,    10,   nil,
    69,   nil,    47,   nil,    21,    33,   nil,    71,   nil,   nil,
   nil,   nil,    69,   nil,   nil,   nil,   nil,    22,   nil,    22,
   nil,   nil,    38,    57,   nil,    22,   nil,    22,   nil,   nil,
   nil,   nil,   nil,     2,    22,    57,   nil,   nil,    63,   nil,
    69,   nil,     8,    22,   nil,    33,   nil,    63,    69,   nil,
   nil,   nil,    29,   nil,   nil,    33,   nil,   nil,    22,   nil,
   nil,    22,   nil,    57,    21,   nil,   nil,    22,    21,    57,
    87,   nil,     9,    21,    94,    94,     7,    22,   nil,   nil,
   nil,   nil,    84,    22,   nil,   nil,    68,   nil,   nil,    90,
   nil,   nil,   nil,    94,    94,    87,   nil,   nil,   nil,    21,
   nil,   nil,    57,    20,    20,   nil,   nil,   nil,    20,    20,
    31,   nil,    20,   nil,    31,   nil,    15,    22,    22,    65,
    49,   nil,    22,    22,   nil,   nil,    22,    68,   nil,   nil,
   138,    49,   nil,   nil,   nil,    69,    31,    60,    69,    66,
    22,    61,   nil,   nil,   nil,    22,    22,   nil,   nil,   nil,
   nil,   nil,    31,   nil,   nil,   nil,   nil,    22,   nil,    41,
   nil,   nil,   nil,    47,    41,   nil,   nil,   nil,    22,     8,
    63,   nil,   nil,   123,   nil,   nil,   nil,    56,    24,    41,
   nil,    63,   nil,   nil,   nil,    47,   nil,   nil,    41,    41,
    41,   nil,    41,    68,   nil,   nil,    31,   nil,   nil,   nil,
   126,    17,   nil,   nil,   nil,    31,   nil,    17,   nil,   165,
   165,   165,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    41,    41,   nil,    22,    41,   116,   nil,   nil,
    22,    22,    57,   nil,    29,    22,   nil,    47,   nil,   nil,
    29,   nil,    20,   nil,     9,    22,   nil,    66,    65,    94,
   nil,   nil,    65,   nil,   nil,   nil,    22,   nil,   nil,   nil,
    31,   nil,   nil,   nil,   nil,   nil,   nil,     9,    21,    41,
    21,   nil,   nil,   nil,    41,    41,    41,    41,    41,    41,
   nil,   nil,   nil,    23,   nil,   nil,   nil,    22,   nil,   nil,
   nil,    31,   nil,    22,   nil,   nil,   nil,   nil,   nil,   nil,
    63,   nil,   nil,   nil,   nil,    22,   nil,   nil,   nil,    21,
   nil,   nil,    21,    37,   nil,   nil,    17,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,     9,   nil,   nil,    21,   nil,     9,   nil,   nil,   nil,
    22,   nil,    22,    22,   nil,   nil,    22,   nil,   nil,    29,
    26,   123,    22,   nil,   123,    26,   124,   124,   124,   116,
    94,   nil,   116,    94,    41,    41,    41,    41,   nil,   nil,
    41,    41,    41,    41,    41,    41,   nil,    22,   nil,   nil,
   nil,   nil,   nil,    26,   126,    22,   126,    41,   nil,    22,
    63,    21,    22,    22,   nil,   nil,    21,    21,   nil,   nil,
   123,   nil,    63,   123,   nil,   123,   nil,   123,   nil,   nil,
   nil,   nil,   nil,    31,   nil,   nil,   nil,   nil,    26,   nil,
   nil,    41,    41,   165,   165,   nil,   165,   165,   nil,   165,
    41,   nil,   126,   nil,   126,   nil,    57,   nil,   nil,   116,
   nil,   116,   nil,   nil,   nil,   nil,    22,    41,   nil,   nil,
   nil,    41,   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,
    26,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    69,    26,
    26,    22,   nil,    33,   nil,   nil,   nil,    22,   nil,    22,
   nil,    21,    41,    41,   nil,   nil,    21,   nil,   nil,   nil,
   nil,    57,   nil,   nil,    31,    22,    21,    41,    41,   116,
   116,    25,   nil,   nil,   nil,   nil,    25,   124,   124,   124,
   124,   nil,   124,   nil,   nil,   nil,   123,    41,   123,   126,
   123,   126,   123,   126,   nil,   165,   165,   165,   165,   nil,
   nil,   nil,   nil,    41,    25,    25,    25,   116,   nil,   116,
   nil,   nil,   nil,   126,    21,   126,   nil,   126,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    26,    21,   nil,   nil,   116,
   nil,   nil,    26,   165,   nil,   nil,   nil,   nil,   123,    25,
    25,    25,     9,   nil,   nil,     9,   nil,   nil,    26,   nil,
   nil,   nil,   nil,   nil,   nil,   126,   nil,   nil,   nil,   124,
   124,   124,   124,    21,    21,   126,   nil,    21,    41,   nil,
   nil,   nil,   nil,    21,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    25,   nil,   nil,   nil,   nil,   nil,   nil,   124,   nil,
    25,    25,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    21,   nil,    26,   nil,
   nil,   nil,    26,    21,    21,   nil,   nil,    26,   nil,   nil,
   nil,    41,   nil,    41,     9,   nil,   nil,   nil,   nil,     9,
     9,    41,   nil,   nil,   nil,    52,   nil,   nil,   nil,   nil,
    52,   nil,   nil,    26,   nil,   nil,   nil,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    41,   nil,   nil,    41,   nil,    21,    52,    52,
    52,    41,   nil,   nil,   nil,   nil,    25,   nil,   nil,   nil,
   nil,    41,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
   nil,   nil,    21,   nil,   nil,   nil,   nil,   nil,    21,    25,
    21,   nil,   nil,    52,    52,    52,    52,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    41,    41,   nil,   nil,   nil,    41,    41,   nil,     9,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    41,    52,   nil,   nil,   nil,    41,
    41,   nil,   nil,   nil,    52,    52,   nil,   nil,   nil,    25,
   nil,   nil,   nil,    25,   nil,   nil,   nil,    25,    25,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,     9,   nil,   nil,
   nil,   nil,   nil,   nil,    25,   nil,   nil,   nil,   nil,     9,
   nil,   nil,   nil,   nil,    25,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    26,   nil,    26,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,     9,     9,   nil,    41,
     9,   nil,   nil,   nil,    41,    41,   nil,   nil,   nil,    41,
    52,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,   nil,    26,   nil,   nil,    26,   nil,   nil,   nil,
    41,   nil,   nil,    52,   nil,   nil,   nil,   nil,   nil,     9,
   nil,   nil,   nil,   nil,   nil,   nil,     9,     9,    26,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    26,   nil,    52,   nil,   nil,   nil,    52,   nil,   nil,
     9,    52,    52,   nil,   nil,    26,   nil,   nil,   nil,   nil,
    26,    26,   nil,   nil,    41,   nil,    41,    41,    52,   nil,
    41,   nil,   nil,   nil,   nil,     9,    41,   nil,    52,   nil,
   nil,     9,   nil,    25,   nil,    25,   nil,   nil,   nil,   nil,
   nil,    25,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    25,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,    41,    41,   nil,   nil,
   nil,   nil,   nil,   nil,    25,   nil,   nil,    25,   nil,    28,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    26,   nil,   nil,   nil,    25,
    26,   nil,   nil,   nil,   nil,   nil,    25,   nil,   nil,   nil,
    26,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,
   nil,    41,    53,    41,   nil,   nil,    25,    53,    26,   nil,
   nil,    25,    25,   nil,   nil,   nil,   nil,   nil,   nil,    41,
    26,   nil,   nil,    25,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    25,    53,    53,    53,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    52,   nil,    52,
   nil,   nil,   nil,   nil,   nil,    52,   nil,    26,    26,   nil,
   nil,    26,   nil,   nil,    52,   nil,   nil,    26,   nil,   nil,
    53,    53,    53,    53,   nil,    28,   nil,   nil,   nil,   nil,
   nil,    28,   nil,   nil,   nil,   nil,   nil,   nil,    52,   nil,
   nil,    52,   nil,   nil,   nil,   nil,    25,   nil,   nil,   nil,
    26,    25,   nil,   nil,    26,   nil,   nil,    26,    26,   nil,
   nil,    25,    53,    52,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    53,    53,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    28,   nil,    28,
   nil,    28,    28,    25,   nil,   nil,   nil,   nil,   nil,    25,
   nil,    26,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    52,    25,   nil,   nil,   nil,    52,    52,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    26,    52,   nil,   nil,
   nil,   nil,    26,   nil,    26,   nil,   nil,   nil,    52,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    53,    25,    25,
   nil,   nil,    25,   nil,   nil,   nil,   nil,   nil,    25,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    53,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    25,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    25,   nil,   nil,   nil,    25,   nil,   nil,    25,    25,
    52,   nil,   nil,   nil,   nil,    52,   nil,   nil,   nil,   nil,
   nil,    28,   nil,   nil,   nil,    52,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    53,   nil,   nil,   nil,    53,   nil,   nil,   nil,    53,    53,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    25,   nil,   nil,    53,   nil,    52,   nil,   nil,
   nil,   nil,   nil,    52,   nil,    53,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    52,   nil,    25,   nil,   nil,
   nil,   nil,   nil,    25,   nil,    25,   nil,   nil,   nil,   nil,
   nil,   nil,    28,   nil,    28,   nil,    28,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    52,    52,   nil,   nil,    52,   nil,   nil,   nil,
   nil,   nil,    52,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    28,   nil,   nil,    28,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    52,   nil,   nil,
    28,   nil,   nil,   nil,   nil,    52,   nil,   nil,   nil,    52,
   nil,   nil,    52,    52,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    28,   nil,   nil,   nil,    28,
    28,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    52,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    53,    28,    53,   nil,   nil,   nil,
   nil,    52,    53,   nil,   nil,   nil,   nil,    52,   nil,    52,
   nil,    53,   nil,   nil,   nil,   nil,   nil,   nil,    28,   nil,
   nil,   nil,   nil,    32,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    32,    32,    32,    53,   nil,   nil,    53,   nil,
   nil,   nil,    28,   nil,   nil,    32,    32,    32,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    53,   nil,    32,    32,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    28,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    53,   nil,   nil,
   nil,   nil,    53,    53,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    53,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    53,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    28,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    28,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    28,    53,   nil,    32,
    32,    32,    53,   nil,    32,    32,   nil,   nil,   nil,   nil,
   nil,   nil,    53,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    32,   nil,   nil,    32,    32,    32,    32,    32,    32,    32,
    32,    32,    32,    32,    32,    32,    32,    32,    32,    32,
    32,    32,    32,    32,    32,    32,    32,    32,    32,   nil,
   nil,   nil,   nil,   nil,    53,    32,    32,   nil,   nil,   nil,
    53,   nil,   nil,   nil,    32,   nil,   nil,   nil,   nil,   nil,
   nil,    32,    53,    32,   nil,    32,    32,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    53,
    53,   nil,   nil,    53,   nil,    32,   nil,   nil,   nil,    53,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    53,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    53,   nil,   nil,   nil,    53,   nil,   nil,    53,
    53,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    32,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    53,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    32,   nil,    32,    32,    32,   nil,    53,   nil,
   nil,   nil,   nil,   nil,    53,   nil,    53,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    32,   nil,    32,   nil,
    32,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    32,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    32,    32,    32,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    32,   nil,   nil,
    32,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    32,    32,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    32,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    32,
    32,   nil,   nil,    32,    32,    32,    32,   nil,   nil,   nil,
    32,    32,   nil,   nil,    32,    32,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    32,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    32,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    32,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    32,   nil,    32,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    32,    32,
    32,    32,   nil,    32,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    32,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    32,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    32,    32,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    32 ]

racc_goto_pointer = [
   nil,   102,    19,   nil,    92,   128,    47,    62,  -305,   519,
  -491,  -594,  -637,   nil,  -341,    13,   143,   -19,  -183,    86,
    -9,   256,     5,  -207,  -335,   981,   830,  -179,  1459,    14,
    35,   -19,  2093,   -17,   nil,   nil,    13,  -178,    31,  -201,
  -313,   639,  -316,   nil,   131,    70,    94,  -165,   nil,   -32,
    31,  -196,  1145,  1592,  -277,    66,   -56,    -1,   nil,   nil,
    34,    38,  -233,    18,   108,   -50,    78,  -227,    83,   -23,
  -301,  -246,  -422,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   100,   113,   -55,   nil,  -160,  -313,  -620,  -419,
  -324,   107,   nil,  -181,    17,   nil,  -518,   106,   121,   122,
  -338,   126,   123,   122,  -500,   123,  -501,  -354,  -701,  -359,
  -513,  -165,  -171,  -354,  -610,  -749,    36,  -589,  -745,  -390,
   nil,  -549,  -547,    40,    82,  -388,    67,  -534,  -487,   nil,
  -519,  -625,  -759,  -735,  -295,  -415,   -93,  -447,    -6,  -545,
  -545,    -9,   nil,   -17,   -17,  -642,  -397,  -527,   nil,   nil,
   178,   181,    54,   178,   179,  -230,  -232,   179,   182,   186,
  -288,  -288,  -276,  -476,   nil,    29,  -515,  -690,  -509,  -555,
   nil,  -708,  -755,   nil,   nil,  -436 ]

racc_goto_default = [
   nil,   nil,   nil,     3,   nil,     4,   342,   289,   nil,   519,
   nil,   814,   nil,   286,   287,   nil,   nil,   nil,    11,    12,
    18,   225,   318,   nil,   nil,   223,   224,   nil,   279,    17,
   nil,   436,    21,    22,    23,    24,   nil,   641,   nil,   nil,
   nil,   306,   nil,    25,   407,    32,   nil,   nil,    34,    37,
    36,   nil,   220,   221,   354,   nil,   128,   415,   127,   130,
    77,    78,   nil,    46,   nil,   779,   408,   nil,   409,   420,
   596,   482,   277,   263,    47,    48,    49,    50,    51,    52,
    53,    54,    55,   nil,   264,    61,   nil,   nil,   nil,   nil,
   nil,   nil,    69,   nil,   534,    70,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   804,   670,   nil,   805,   904,
   751,   658,   nil,   659,   nil,   nil,   660,   nil,   662,   612,
   nil,   nil,   nil,   668,   nil,   nil,   nil,   712,   nil,   nil,
   nil,   nil,   419,   nil,   nil,   nil,   nil,   nil,    76,    79,
    80,   nil,   nil,   nil,   nil,   nil,   568,   nil,   nil,   nil,
   nil,   nil,   nil,   873,   657,   nil,   661,   672,   674,   763,
   677,   678,   764,   681,   684,   281 ]

racc_reduce_table = [
  0, 0, :racc_error,
  1, 142, :_reduce_none,
  2, 143, :_reduce_2,
  0, 144, :_reduce_3,
  1, 144, :_reduce_4,
  3, 144, :_reduce_5,
  2, 144, :_reduce_6,
  1, 146, :_reduce_none,
  4, 146, :_reduce_8,
  4, 149, :_reduce_9,
  2, 150, :_reduce_10,
  0, 154, :_reduce_11,
  1, 154, :_reduce_12,
  3, 154, :_reduce_13,
  2, 154, :_reduce_14,
  1, 155, :_reduce_none,
  4, 155, :_reduce_16,
  0, 171, :_reduce_17,
  4, 148, :_reduce_18,
  3, 148, :_reduce_19,
  3, 148, :_reduce_20,
  3, 148, :_reduce_21,
  2, 148, :_reduce_22,
  3, 148, :_reduce_23,
  3, 148, :_reduce_24,
  3, 148, :_reduce_25,
  3, 148, :_reduce_26,
  3, 148, :_reduce_27,
  4, 148, :_reduce_28,
  1, 148, :_reduce_none,
  3, 148, :_reduce_30,
  3, 148, :_reduce_31,
  6, 148, :_reduce_32,
  5, 148, :_reduce_33,
  5, 148, :_reduce_34,
  5, 148, :_reduce_35,
  5, 148, :_reduce_36,
  3, 148, :_reduce_37,
  3, 148, :_reduce_38,
  3, 148, :_reduce_39,
  3, 148, :_reduce_40,
  1, 148, :_reduce_none,
  3, 159, :_reduce_42,
  3, 159, :_reduce_43,
  1, 170, :_reduce_none,
  3, 170, :_reduce_45,
  3, 170, :_reduce_46,
  3, 170, :_reduce_47,
  2, 170, :_reduce_48,
  1, 170, :_reduce_none,
  1, 158, :_reduce_none,
  1, 161, :_reduce_none,
  1, 161, :_reduce_none,
  1, 175, :_reduce_none,
  4, 175, :_reduce_54,
  0, 183, :_reduce_55,
  5, 180, :_reduce_56,
  1, 182, :_reduce_none,
  2, 174, :_reduce_58,
  3, 174, :_reduce_59,
  4, 174, :_reduce_60,
  5, 174, :_reduce_61,
  4, 174, :_reduce_62,
  5, 174, :_reduce_63,
  2, 174, :_reduce_64,
  2, 174, :_reduce_65,
  2, 174, :_reduce_66,
  2, 174, :_reduce_67,
  2, 174, :_reduce_68,
  1, 160, :_reduce_69,
  3, 160, :_reduce_70,
  1, 187, :_reduce_71,
  3, 187, :_reduce_72,
  1, 186, :_reduce_none,
  2, 186, :_reduce_74,
  3, 186, :_reduce_75,
  5, 186, :_reduce_76,
  2, 186, :_reduce_77,
  4, 186, :_reduce_78,
  2, 186, :_reduce_79,
  4, 186, :_reduce_80,
  1, 186, :_reduce_81,
  3, 186, :_reduce_82,
  1, 190, :_reduce_none,
  3, 190, :_reduce_84,
  2, 189, :_reduce_85,
  3, 189, :_reduce_86,
  1, 192, :_reduce_87,
  3, 192, :_reduce_88,
  1, 191, :_reduce_89,
  1, 191, :_reduce_90,
  4, 191, :_reduce_91,
  3, 191, :_reduce_92,
  3, 191, :_reduce_93,
  3, 191, :_reduce_94,
  3, 191, :_reduce_95,
  2, 191, :_reduce_96,
  1, 191, :_reduce_97,
  1, 167, :_reduce_98,
  1, 167, :_reduce_99,
  4, 167, :_reduce_100,
  3, 167, :_reduce_101,
  3, 167, :_reduce_102,
  3, 167, :_reduce_103,
  3, 167, :_reduce_104,
  2, 167, :_reduce_105,
  1, 167, :_reduce_106,
  1, 195, :_reduce_107,
  1, 195, :_reduce_none,
  2, 196, :_reduce_109,
  1, 196, :_reduce_110,
  3, 196, :_reduce_111,
  1, 197, :_reduce_none,
  1, 197, :_reduce_none,
  1, 197, :_reduce_none,
  1, 197, :_reduce_none,
  1, 197, :_reduce_none,
  1, 200, :_reduce_117,
  1, 200, :_reduce_none,
  1, 156, :_reduce_none,
  1, 156, :_reduce_none,
  1, 157, :_reduce_121,
  0, 203, :_reduce_122,
  4, 157, :_reduce_123,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  3, 173, :_reduce_195,
  5, 173, :_reduce_196,
  3, 173, :_reduce_197,
  5, 173, :_reduce_198,
  6, 173, :_reduce_199,
  5, 173, :_reduce_200,
  5, 173, :_reduce_201,
  5, 173, :_reduce_202,
  5, 173, :_reduce_203,
  4, 173, :_reduce_204,
  3, 173, :_reduce_205,
  3, 173, :_reduce_206,
  3, 173, :_reduce_207,
  3, 173, :_reduce_208,
  3, 173, :_reduce_209,
  3, 173, :_reduce_210,
  3, 173, :_reduce_211,
  3, 173, :_reduce_212,
  3, 173, :_reduce_213,
  4, 173, :_reduce_214,
  4, 173, :_reduce_215,
  2, 173, :_reduce_216,
  2, 173, :_reduce_217,
  3, 173, :_reduce_218,
  3, 173, :_reduce_219,
  3, 173, :_reduce_220,
  3, 173, :_reduce_221,
  3, 173, :_reduce_222,
  3, 173, :_reduce_223,
  3, 173, :_reduce_224,
  3, 173, :_reduce_225,
  3, 173, :_reduce_226,
  3, 173, :_reduce_227,
  3, 173, :_reduce_228,
  3, 173, :_reduce_229,
  3, 173, :_reduce_230,
  2, 173, :_reduce_231,
  2, 173, :_reduce_232,
  3, 173, :_reduce_233,
  3, 173, :_reduce_234,
  3, 173, :_reduce_235,
  3, 173, :_reduce_236,
  3, 173, :_reduce_237,
  6, 173, :_reduce_238,
  1, 173, :_reduce_none,
  1, 169, :_reduce_none,
  1, 205, :_reduce_none,
  2, 205, :_reduce_none,
  4, 205, :_reduce_243,
  2, 205, :_reduce_244,
  3, 210, :_reduce_245,
  0, 211, :_reduce_246,
  1, 211, :_reduce_none,
  0, 164, :_reduce_248,
  1, 164, :_reduce_none,
  2, 164, :_reduce_none,
  4, 164, :_reduce_251,
  2, 164, :_reduce_252,
  1, 185, :_reduce_253,
  2, 185, :_reduce_254,
  2, 185, :_reduce_255,
  4, 185, :_reduce_256,
  1, 185, :_reduce_257,
  0, 214, :_reduce_258,
  2, 179, :_reduce_259,
  2, 213, :_reduce_260,
  2, 212, :_reduce_261,
  0, 212, :_reduce_262,
  1, 207, :_reduce_263,
  2, 207, :_reduce_264,
  3, 207, :_reduce_265,
  4, 207, :_reduce_266,
  3, 168, :_reduce_267,
  4, 168, :_reduce_268,
  2, 168, :_reduce_269,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_280,
  0, 239, :_reduce_281,
  4, 204, :_reduce_282,
  0, 240, :_reduce_283,
  0, 241, :_reduce_284,
  6, 204, :_reduce_285,
  0, 242, :_reduce_286,
  4, 204, :_reduce_287,
  3, 204, :_reduce_288,
  3, 204, :_reduce_289,
  2, 204, :_reduce_290,
  3, 204, :_reduce_291,
  3, 204, :_reduce_292,
  1, 204, :_reduce_293,
  4, 204, :_reduce_294,
  3, 204, :_reduce_295,
  1, 204, :_reduce_296,
  5, 204, :_reduce_297,
  4, 204, :_reduce_298,
  3, 204, :_reduce_299,
  2, 204, :_reduce_300,
  1, 204, :_reduce_none,
  2, 204, :_reduce_302,
  0, 243, :_reduce_303,
  3, 204, :_reduce_304,
  6, 204, :_reduce_305,
  6, 204, :_reduce_306,
  0, 244, :_reduce_307,
  0, 245, :_reduce_308,
  7, 204, :_reduce_309,
  0, 246, :_reduce_310,
  0, 247, :_reduce_311,
  7, 204, :_reduce_312,
  5, 204, :_reduce_313,
  4, 204, :_reduce_314,
  0, 248, :_reduce_315,
  0, 249, :_reduce_316,
  9, 204, :_reduce_317,
  0, 250, :_reduce_318,
  6, 204, :_reduce_319,
  0, 251, :_reduce_320,
  7, 204, :_reduce_321,
  0, 252, :_reduce_322,
  5, 204, :_reduce_323,
  0, 253, :_reduce_324,
  6, 204, :_reduce_325,
  0, 254, :_reduce_326,
  0, 255, :_reduce_327,
  9, 204, :_reduce_328,
  1, 204, :_reduce_329,
  1, 204, :_reduce_330,
  1, 204, :_reduce_331,
  1, 204, :_reduce_332,
  1, 163, :_reduce_none,
  1, 233, :_reduce_334,
  1, 236, :_reduce_335,
  1, 228, :_reduce_none,
  1, 228, :_reduce_none,
  2, 228, :_reduce_338,
  1, 230, :_reduce_none,
  1, 230, :_reduce_none,
  1, 229, :_reduce_none,
  5, 229, :_reduce_342,
  1, 152, :_reduce_none,
  2, 152, :_reduce_344,
  1, 232, :_reduce_none,
  1, 232, :_reduce_none,
  1, 256, :_reduce_347,
  3, 256, :_reduce_348,
  1, 259, :_reduce_349,
  3, 259, :_reduce_350,
  1, 258, :_reduce_none,
  4, 258, :_reduce_352,
  6, 258, :_reduce_353,
  3, 258, :_reduce_354,
  5, 258, :_reduce_355,
  2, 258, :_reduce_356,
  4, 258, :_reduce_357,
  1, 258, :_reduce_358,
  3, 258, :_reduce_359,
  4, 260, :_reduce_360,
  2, 260, :_reduce_361,
  2, 260, :_reduce_362,
  1, 260, :_reduce_363,
  2, 265, :_reduce_364,
  0, 265, :_reduce_365,
  6, 266, :_reduce_366,
  8, 266, :_reduce_367,
  4, 266, :_reduce_368,
  6, 266, :_reduce_369,
  4, 266, :_reduce_370,
  2, 266, :_reduce_none,
  6, 266, :_reduce_372,
  2, 266, :_reduce_373,
  4, 266, :_reduce_374,
  6, 266, :_reduce_375,
  2, 266, :_reduce_376,
  4, 266, :_reduce_377,
  2, 266, :_reduce_378,
  4, 266, :_reduce_379,
  1, 266, :_reduce_none,
  0, 181, :_reduce_381,
  1, 181, :_reduce_382,
  3, 270, :_reduce_383,
  1, 270, :_reduce_384,
  4, 270, :_reduce_385,
  1, 271, :_reduce_386,
  4, 271, :_reduce_387,
  1, 272, :_reduce_388,
  3, 272, :_reduce_389,
  1, 273, :_reduce_390,
  1, 273, :_reduce_none,
  0, 277, :_reduce_392,
  3, 227, :_reduce_393,
  4, 275, :_reduce_394,
  1, 275, :_reduce_395,
  0, 280, :_reduce_396,
  4, 276, :_reduce_397,
  0, 281, :_reduce_398,
  4, 276, :_reduce_399,
  0, 282, :_reduce_400,
  5, 279, :_reduce_401,
  2, 176, :_reduce_402,
  4, 176, :_reduce_403,
  5, 176, :_reduce_404,
  5, 176, :_reduce_405,
  2, 226, :_reduce_406,
  4, 226, :_reduce_407,
  4, 226, :_reduce_408,
  3, 226, :_reduce_409,
  3, 226, :_reduce_410,
  3, 226, :_reduce_411,
  2, 226, :_reduce_412,
  1, 226, :_reduce_413,
  4, 226, :_reduce_414,
  0, 284, :_reduce_415,
  5, 225, :_reduce_416,
  0, 285, :_reduce_417,
  5, 225, :_reduce_418,
  5, 231, :_reduce_419,
  1, 286, :_reduce_420,
  1, 286, :_reduce_none,
  6, 151, :_reduce_422,
  0, 151, :_reduce_423,
  1, 287, :_reduce_424,
  1, 287, :_reduce_none,
  1, 287, :_reduce_none,
  2, 288, :_reduce_427,
  1, 288, :_reduce_none,
  2, 153, :_reduce_429,
  1, 153, :_reduce_none,
  1, 215, :_reduce_none,
  1, 215, :_reduce_none,
  1, 215, :_reduce_none,
  1, 216, :_reduce_434,
  1, 290, :_reduce_435,
  2, 290, :_reduce_436,
  3, 291, :_reduce_437,
  1, 291, :_reduce_438,
  1, 291, :_reduce_439,
  3, 217, :_reduce_440,
  4, 218, :_reduce_441,
  3, 219, :_reduce_442,
  0, 295, :_reduce_443,
  3, 295, :_reduce_444,
  1, 296, :_reduce_445,
  2, 296, :_reduce_446,
  3, 221, :_reduce_447,
  0, 298, :_reduce_448,
  3, 298, :_reduce_449,
  3, 220, :_reduce_450,
  3, 222, :_reduce_451,
  0, 299, :_reduce_452,
  3, 299, :_reduce_453,
  0, 300, :_reduce_454,
  3, 300, :_reduce_455,
  0, 292, :_reduce_456,
  2, 292, :_reduce_457,
  0, 293, :_reduce_458,
  2, 293, :_reduce_459,
  0, 294, :_reduce_460,
  2, 294, :_reduce_461,
  1, 297, :_reduce_462,
  2, 297, :_reduce_463,
  0, 302, :_reduce_464,
  4, 297, :_reduce_465,
  1, 301, :_reduce_466,
  1, 301, :_reduce_467,
  1, 301, :_reduce_468,
  1, 301, :_reduce_none,
  1, 201, :_reduce_470,
  3, 202, :_reduce_471,
  1, 289, :_reduce_472,
  1, 289, :_reduce_473,
  2, 289, :_reduce_474,
  2, 289, :_reduce_475,
  1, 193, :_reduce_476,
  1, 193, :_reduce_477,
  1, 193, :_reduce_478,
  1, 193, :_reduce_479,
  1, 193, :_reduce_480,
  1, 194, :_reduce_481,
  1, 194, :_reduce_482,
  1, 194, :_reduce_483,
  1, 194, :_reduce_484,
  1, 194, :_reduce_485,
  1, 194, :_reduce_486,
  1, 194, :_reduce_487,
  1, 223, :_reduce_488,
  1, 223, :_reduce_489,
  1, 162, :_reduce_490,
  1, 162, :_reduce_491,
  1, 166, :_reduce_492,
  1, 166, :_reduce_493,
  1, 234, :_reduce_494,
  0, 303, :_reduce_495,
  4, 234, :_reduce_496,
  2, 234, :_reduce_497,
  3, 237, :_reduce_498,
  2, 237, :_reduce_499,
  4, 304, :_reduce_500,
  2, 304, :_reduce_501,
  2, 304, :_reduce_502,
  1, 304, :_reduce_503,
  2, 306, :_reduce_504,
  0, 306, :_reduce_505,
  6, 278, :_reduce_506,
  8, 278, :_reduce_507,
  4, 278, :_reduce_508,
  6, 278, :_reduce_509,
  4, 278, :_reduce_510,
  6, 278, :_reduce_511,
  2, 278, :_reduce_512,
  4, 278, :_reduce_513,
  6, 278, :_reduce_514,
  2, 278, :_reduce_515,
  4, 278, :_reduce_516,
  2, 278, :_reduce_517,
  4, 278, :_reduce_518,
  1, 278, :_reduce_519,
  0, 278, :_reduce_520,
  1, 274, :_reduce_521,
  1, 274, :_reduce_522,
  1, 274, :_reduce_523,
  1, 274, :_reduce_524,
  1, 257, :_reduce_none,
  1, 257, :_reduce_none,
  1, 308, :_reduce_527,
  3, 308, :_reduce_528,
  1, 267, :_reduce_529,
  3, 267, :_reduce_530,
  2, 309, :_reduce_531,
  2, 310, :_reduce_532,
  1, 261, :_reduce_533,
  3, 261, :_reduce_534,
  1, 305, :_reduce_535,
  3, 305, :_reduce_536,
  1, 311, :_reduce_none,
  1, 311, :_reduce_none,
  2, 262, :_reduce_539,
  1, 262, :_reduce_540,
  3, 312, :_reduce_541,
  3, 313, :_reduce_542,
  1, 268, :_reduce_543,
  3, 268, :_reduce_544,
  1, 307, :_reduce_545,
  3, 307, :_reduce_546,
  1, 314, :_reduce_none,
  1, 314, :_reduce_none,
  2, 269, :_reduce_549,
  1, 269, :_reduce_550,
  1, 315, :_reduce_none,
  1, 315, :_reduce_none,
  2, 264, :_reduce_553,
  2, 263, :_reduce_554,
  0, 263, :_reduce_555,
  1, 238, :_reduce_none,
  3, 238, :_reduce_557,
  0, 224, :_reduce_558,
  2, 224, :_reduce_none,
  1, 209, :_reduce_560,
  3, 209, :_reduce_561,
  3, 316, :_reduce_562,
  2, 316, :_reduce_563,
  2, 316, :_reduce_564,
  1, 184, :_reduce_none,
  1, 184, :_reduce_none,
  1, 184, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 283, :_reduce_none,
  1, 283, :_reduce_none,
  1, 283, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  0, 145, :_reduce_none,
  1, 145, :_reduce_none,
  0, 172, :_reduce_none,
  1, 172, :_reduce_none,
  2, 188, :_reduce_581,
  2, 165, :_reduce_582,
  0, 208, :_reduce_none,
  1, 208, :_reduce_none,
  1, 208, :_reduce_none,
  1, 235, :_reduce_586,
  1, 235, :_reduce_none,
  1, 147, :_reduce_none,
  2, 147, :_reduce_none,
  0, 206, :_reduce_590 ]

racc_reduce_n = 591

racc_shift_n = 1019

racc_token_table = {
  false => 0,
  :error => 1,
  :kCLASS => 2,
  :kMODULE => 3,
  :kDEF => 4,
  :kUNDEF => 5,
  :kBEGIN => 6,
  :kRESCUE => 7,
  :kENSURE => 8,
  :kEND => 9,
  :kIF => 10,
  :kUNLESS => 11,
  :kTHEN => 12,
  :kELSIF => 13,
  :kELSE => 14,
  :kCASE => 15,
  :kWHEN => 16,
  :kWHILE => 17,
  :kUNTIL => 18,
  :kFOR => 19,
  :kBREAK => 20,
  :kNEXT => 21,
  :kREDO => 22,
  :kRETRY => 23,
  :kIN => 24,
  :kDO => 25,
  :kDO_COND => 26,
  :kDO_BLOCK => 27,
  :kDO_LAMBDA => 28,
  :kRETURN => 29,
  :kYIELD => 30,
  :kSUPER => 31,
  :kSELF => 32,
  :kNIL => 33,
  :kTRUE => 34,
  :kFALSE => 35,
  :kAND => 36,
  :kOR => 37,
  :kNOT => 38,
  :kIF_MOD => 39,
  :kUNLESS_MOD => 40,
  :kWHILE_MOD => 41,
  :kUNTIL_MOD => 42,
  :kRESCUE_MOD => 43,
  :kALIAS => 44,
  :kDEFINED => 45,
  :klBEGIN => 46,
  :klEND => 47,
  :k__LINE__ => 48,
  :k__FILE__ => 49,
  :k__ENCODING__ => 50,
  :tIDENTIFIER => 51,
  :tFID => 52,
  :tGVAR => 53,
  :tIVAR => 54,
  :tCONSTANT => 55,
  :tLABEL => 56,
  :tCVAR => 57,
  :tNTH_REF => 58,
  :tBACK_REF => 59,
  :tSTRING_CONTENT => 60,
  :tINTEGER => 61,
  :tFLOAT => 62,
  :tUPLUS => 63,
  :tUMINUS => 64,
  :tUNARY_NUM => 65,
  :tPOW => 66,
  :tCMP => 67,
  :tEQ => 68,
  :tEQQ => 69,
  :tNEQ => 70,
  :tGEQ => 71,
  :tLEQ => 72,
  :tANDOP => 73,
  :tOROP => 74,
  :tMATCH => 75,
  :tNMATCH => 76,
  :tDOT => 77,
  :tDOT2 => 78,
  :tDOT3 => 79,
  :tAREF => 80,
  :tASET => 81,
  :tLSHFT => 82,
  :tRSHFT => 83,
  :tCOLON2 => 84,
  :tCOLON3 => 85,
  :tOP_ASGN => 86,
  :tASSOC => 87,
  :tLPAREN => 88,
  :tLPAREN2 => 89,
  :tRPAREN => 90,
  :tLPAREN_ARG => 91,
  :tLBRACK => 92,
  :tLBRACK2 => 93,
  :tRBRACK => 94,
  :tLBRACE => 95,
  :tLBRACE_ARG => 96,
  :tSTAR => 97,
  :tSTAR2 => 98,
  :tAMPER => 99,
  :tAMPER2 => 100,
  :tTILDE => 101,
  :tPERCENT => 102,
  :tDIVIDE => 103,
  :tDSTAR => 104,
  :tPLUS => 105,
  :tMINUS => 106,
  :tLT => 107,
  :tGT => 108,
  :tPIPE => 109,
  :tBANG => 110,
  :tCARET => 111,
  :tLCURLY => 112,
  :tRCURLY => 113,
  :tBACK_REF2 => 114,
  :tSYMBEG => 115,
  :tSTRING_BEG => 116,
  :tXSTRING_BEG => 117,
  :tREGEXP_BEG => 118,
  :tREGEXP_OPT => 119,
  :tWORDS_BEG => 120,
  :tQWORDS_BEG => 121,
  :tSYMBOLS_BEG => 122,
  :tQSYMBOLS_BEG => 123,
  :tSTRING_DBEG => 124,
  :tSTRING_DVAR => 125,
  :tSTRING_END => 126,
  :tSTRING_DEND => 127,
  :tSTRING => 128,
  :tSYMBOL => 129,
  :tNL => 130,
  :tEH => 131,
  :tCOLON => 132,
  :tCOMMA => 133,
  :tSPACE => 134,
  :tSEMI => 135,
  :tLAMBDA => 136,
  :tLAMBEG => 137,
  :tCHARACTER => 138,
  :tEQL => 139,
  :tLOWEST => 140 }

racc_nt_base = 141

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]
Ractor.make_shareable(Racc_arg) if defined?(Ractor)

Racc_token_to_s_table = [
  "$end",
  "error",
  "kCLASS",
  "kMODULE",
  "kDEF",
  "kUNDEF",
  "kBEGIN",
  "kRESCUE",
  "kENSURE",
  "kEND",
  "kIF",
  "kUNLESS",
  "kTHEN",
  "kELSIF",
  "kELSE",
  "kCASE",
  "kWHEN",
  "kWHILE",
  "kUNTIL",
  "kFOR",
  "kBREAK",
  "kNEXT",
  "kREDO",
  "kRETRY",
  "kIN",
  "kDO",
  "kDO_COND",
  "kDO_BLOCK",
  "kDO_LAMBDA",
  "kRETURN",
  "kYIELD",
  "kSUPER",
  "kSELF",
  "kNIL",
  "kTRUE",
  "kFALSE",
  "kAND",
  "kOR",
  "kNOT",
  "kIF_MOD",
  "kUNLESS_MOD",
  "kWHILE_MOD",
  "kUNTIL_MOD",
  "kRESCUE_MOD",
  "kALIAS",
  "kDEFINED",
  "klBEGIN",
  "klEND",
  "k__LINE__",
  "k__FILE__",
  "k__ENCODING__",
  "tIDENTIFIER",
  "tFID",
  "tGVAR",
  "tIVAR",
  "tCONSTANT",
  "tLABEL",
  "tCVAR",
  "tNTH_REF",
  "tBACK_REF",
  "tSTRING_CONTENT",
  "tINTEGER",
  "tFLOAT",
  "tUPLUS",
  "tUMINUS",
  "tUNARY_NUM",
  "tPOW",
  "tCMP",
  "tEQ",
  "tEQQ",
  "tNEQ",
  "tGEQ",
  "tLEQ",
  "tANDOP",
  "tOROP",
  "tMATCH",
  "tNMATCH",
  "tDOT",
  "tDOT2",
  "tDOT3",
  "tAREF",
  "tASET",
  "tLSHFT",
  "tRSHFT",
  "tCOLON2",
  "tCOLON3",
  "tOP_ASGN",
  "tASSOC",
  "tLPAREN",
  "tLPAREN2",
  "tRPAREN",
  "tLPAREN_ARG",
  "tLBRACK",
  "tLBRACK2",
  "tRBRACK",
  "tLBRACE",
  "tLBRACE_ARG",
  "tSTAR",
  "tSTAR2",
  "tAMPER",
  "tAMPER2",
  "tTILDE",
  "tPERCENT",
  "tDIVIDE",
  "tDSTAR",
  "tPLUS",
  "tMINUS",
  "tLT",
  "tGT",
  "tPIPE",
  "tBANG",
  "tCARET",
  "tLCURLY",
  "tRCURLY",
  "tBACK_REF2",
  "tSYMBEG",
  "tSTRING_BEG",
  "tXSTRING_BEG",
  "tREGEXP_BEG",
  "tREGEXP_OPT",
  "tWORDS_BEG",
  "tQWORDS_BEG",
  "tSYMBOLS_BEG",
  "tQSYMBOLS_BEG",
  "tSTRING_DBEG",
  "tSTRING_DVAR",
  "tSTRING_END",
  "tSTRING_DEND",
  "tSTRING",
  "tSYMBOL",
  "tNL",
  "tEH",
  "tCOLON",
  "tCOMMA",
  "tSPACE",
  "tSEMI",
  "tLAMBDA",
  "tLAMBEG",
  "tCHARACTER",
  "tEQL",
  "tLOWEST",
  "$start",
  "program",
  "top_compstmt",
  "top_stmts",
  "opt_terms",
  "top_stmt",
  "terms",
  "stmt",
  "bodystmt",
  "compstmt",
  "opt_rescue",
  "opt_else",
  "opt_ensure",
  "stmts",
  "stmt_or_begin",
  "fitem",
  "undef_list",
  "expr_value",
  "command_asgn",
  "mlhs",
  "command_call",
  "var_lhs",
  "primary_value",
  "opt_call_args",
  "rbracket",
  "backref",
  "lhs",
  "mrhs",
  "arg_value",
  "expr",
  "@1",
  "opt_nl",
  "arg",
  "command",
  "block_command",
  "block_call",
  "dot_or_colon",
  "operation2",
  "command_args",
  "cmd_brace_block",
  "opt_block_param",
  "fcall",
  "@2",
  "operation",
  "call_args",
  "mlhs_basic",
  "mlhs_inner",
  "rparen",
  "mlhs_head",
  "mlhs_item",
  "mlhs_node",
  "mlhs_post",
  "user_variable",
  "keyword_variable",
  "cname",
  "cpath",
  "fname",
  "op",
  "reswords",
  "fsym",
  "symbol",
  "dsym",
  "@3",
  "primary",
  "aref_args",
  "none",
  "args",
  "trailer",
  "assocs",
  "paren_args",
  "opt_paren_args",
  "opt_block_arg",
  "block_arg",
  "@4",
  "literal",
  "strings",
  "xstring",
  "regexp",
  "words",
  "qwords",
  "symbols",
  "qsymbols",
  "var_ref",
  "assoc_list",
  "brace_block",
  "method_call",
  "lambda",
  "then",
  "if_tail",
  "do",
  "case_body",
  "for_var",
  "k_class",
  "superclass",
  "term",
  "k_module",
  "f_arglist",
  "singleton",
  "@5",
  "@6",
  "@7",
  "@8",
  "@9",
  "@10",
  "@11",
  "@12",
  "@13",
  "@14",
  "@15",
  "@16",
  "@17",
  "@18",
  "@19",
  "@20",
  "@21",
  "f_marg",
  "f_norm_arg",
  "f_margs",
  "f_marg_list",
  "block_args_tail",
  "f_block_kwarg",
  "f_kwrest",
  "opt_f_block_arg",
  "f_block_arg",
  "opt_block_args_tail",
  "block_param",
  "f_arg",
  "f_block_optarg",
  "f_rest_arg",
  "block_param_def",
  "opt_bv_decl",
  "bv_decls",
  "bvar",
  "f_bad_arg",
  "f_larglist",
  "lambda_body",
  "@22",
  "f_args",
  "do_block",
  "@23",
  "@24",
  "@25",
  "operation3",
  "@26",
  "@27",
  "cases",
  "exc_list",
  "exc_var",
  "numeric",
  "string",
  "string1",
  "string_contents",
  "xstring_contents",
  "regexp_contents",
  "word_list",
  "word",
  "string_content",
  "symbol_list",
  "qword_list",
  "qsym_list",
  "string_dvar",
  "@28",
  "@29",
  "args_tail",
  "f_kwarg",
  "opt_args_tail",
  "f_optarg",
  "f_arg_item",
  "f_kw",
  "f_block_kw",
  "kwrest_mark",
  "f_opt",
  "f_block_opt",
  "restarg_mark",
  "blkarg_mark",
  "assoc" ]
Ractor.make_shareable(Racc_token_to_s_table) if defined?(Ractor)

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

# reduce 1 omitted

def _reduce_2(val, _values, result)
                      result = @builder.compstmt(val[0])

    result
end

def _reduce_3(val, _values, result)
                      result = []

    result
end

def _reduce_4(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_5(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_6(val, _values, result)
                      result = [ val[1] ]

    result
end

# reduce 7 omitted

def _reduce_8(val, _values, result)
                      result = @builder.preexe(val[0], val[1], val[2], val[3])

    result
end

def _reduce_9(val, _values, result)
                      rescue_bodies     = val[1]
                      else_t,   else_   = val[2]
                      ensure_t, ensure_ = val[3]

                      if rescue_bodies.empty? && !else_t.nil?
                        diagnostic :warning, :useless_else, nil, else_t
                      end

                      result = @builder.begin_body(val[0],
                                  rescue_bodies,
                                  else_t,   else_,
                                  ensure_t, ensure_)

    result
end

def _reduce_10(val, _values, result)
                      result = @builder.compstmt(val[0])

    result
end

def _reduce_11(val, _values, result)
                      result = []

    result
end

def _reduce_12(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_13(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_14(val, _values, result)
                      result = [ val[1] ]

    result
end

# reduce 15 omitted

def _reduce_16(val, _values, result)
                      if @context.in_def
                        diagnostic :error, :begin_in_method, nil, val[0]
                      end

                      result = @builder.preexe(val[0], val[1], val[2], val[3])

    result
end

def _reduce_17(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_18(val, _values, result)
                      result = @builder.alias(val[0], val[1], val[3])

    result
end

def _reduce_19(val, _values, result)
                      result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.gvar(val[2]))

    result
end

def _reduce_20(val, _values, result)
                      result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.back_ref(val[2]))

    result
end

def _reduce_21(val, _values, result)
                      diagnostic :error, :nth_ref_alias, nil, val[2]

    result
end

def _reduce_22(val, _values, result)
                      result = @builder.undef_method(val[0], val[1])

    result
end

def _reduce_23(val, _values, result)
                      result = @builder.condition_mod(val[0], nil,
                                                      val[1], val[2])

    result
end

def _reduce_24(val, _values, result)
                      result = @builder.condition_mod(nil, val[0],
                                                      val[1], val[2])

    result
end

def _reduce_25(val, _values, result)
                      result = @builder.loop_mod(:while, val[0], val[1], val[2])

    result
end

def _reduce_26(val, _values, result)
                      result = @builder.loop_mod(:until, val[0], val[1], val[2])

    result
end

def _reduce_27(val, _values, result)
                      rescue_body = @builder.rescue_body(val[1],
                                        nil, nil, nil,
                                        nil, val[2])

                      result = @builder.begin_body(val[0], [ rescue_body ])

    result
end

def _reduce_28(val, _values, result)
                      result = @builder.postexe(val[0], val[1], val[2], val[3])

    result
end

# reduce 29 omitted

def _reduce_30(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1], val[2])

    result
end

def _reduce_31(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_32(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])

    result
end

def _reduce_33(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_34(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_35(val, _values, result)
                      const  = @builder.const_op_assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))
                      result = @builder.op_assign(const, val[3], val[4])

    result
end

def _reduce_36(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_37(val, _values, result)
                      @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_38(val, _values, result)
                      result = @builder.assign(val[0], val[1],
                                  @builder.array(nil, val[2], nil))

    result
end

def _reduce_39(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1], val[2])

    result
end

def _reduce_40(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1],
                                  @builder.array(nil, val[2], nil))

    result
end

# reduce 41 omitted

def _reduce_42(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

def _reduce_43(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

# reduce 44 omitted

def _reduce_45(val, _values, result)
                      result = @builder.logical_op(:and, val[0], val[1], val[2])

    result
end

def _reduce_46(val, _values, result)
                      result = @builder.logical_op(:or, val[0], val[1], val[2])

    result
end

def _reduce_47(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[2], nil)

    result
end

def _reduce_48(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[1], nil)

    result
end

# reduce 49 omitted

# reduce 50 omitted

# reduce 51 omitted

# reduce 52 omitted

# reduce 53 omitted

def _reduce_54(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
end

def _reduce_55(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_56(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

# reduce 57 omitted

def _reduce_58(val, _values, result)
                      result = @builder.call_method(nil, nil, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_59(val, _values, result)
                      method_call = @builder.call_method(nil, nil, val[0],
                                        nil, val[1], nil)

                      begin_t, args, body, end_t = val[2]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_60(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
end

def _reduce_61(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                        nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_62(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
end

def _reduce_63(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                        nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_64(val, _values, result)
                      result = @builder.keyword_cmd(:super, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_65(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_66(val, _values, result)
                      result = @builder.keyword_cmd(:return, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_67(val, _values, result)
                      result = @builder.keyword_cmd(:break, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_68(val, _values, result)
                      result = @builder.keyword_cmd(:next, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_69(val, _values, result)
                      result = @builder.multi_lhs(nil, val[0], nil)

    result
end

def _reduce_70(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_71(val, _values, result)
                      result = @builder.multi_lhs(nil, val[0], nil)

    result
end

def _reduce_72(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

# reduce 73 omitted

def _reduce_74(val, _values, result)
                      result = val[0].
                                  push(val[1])

    result
end

def _reduce_75(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1], val[2]))

    result
end

def _reduce_76(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1], val[2])).
                                  concat(val[4])

    result
end

def _reduce_77(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1]))

    result
end

def _reduce_78(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1])).
                                  concat(val[3])

    result
end

def _reduce_79(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

def _reduce_80(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]),
                                 *val[3] ]

    result
end

def _reduce_81(val, _values, result)
                      result = [ @builder.splat(val[0]) ]

    result
end

def _reduce_82(val, _values, result)
                      result = [ @builder.splat(val[0]),
                                 *val[2] ]

    result
end

# reduce 83 omitted

def _reduce_84(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_85(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_86(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_87(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_88(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_89(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_90(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_91(val, _values, result)
                      result = @builder.index_asgn(val[0], val[1], val[2], val[3])

    result
end

def _reduce_92(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_93(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_94(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_95(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))

    result
end

def _reduce_96(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))

    result
end

def _reduce_97(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_98(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_99(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_100(val, _values, result)
                      result = @builder.index_asgn(val[0], val[1], val[2], val[3])

    result
end

def _reduce_101(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_102(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_103(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_104(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))

    result
end

def _reduce_105(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))

    result
end

def _reduce_106(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_107(val, _values, result)
                      diagnostic :error, :module_name_const, nil, val[0]

    result
end

# reduce 108 omitted

def _reduce_109(val, _values, result)
                      result = @builder.const_global(val[0], val[1])

    result
end

def _reduce_110(val, _values, result)
                      result = @builder.const(val[0])

    result
end

def _reduce_111(val, _values, result)
                      result = @builder.const_fetch(val[0], val[1], val[2])

    result
end

# reduce 112 omitted

# reduce 113 omitted

# reduce 114 omitted

# reduce 115 omitted

# reduce 116 omitted

def _reduce_117(val, _values, result)
                      result = @builder.symbol_internal(val[0])

    result
end

# reduce 118 omitted

# reduce 119 omitted

# reduce 120 omitted

def _reduce_121(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_122(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_123(val, _values, result)
                      result = val[0] << val[3]

    result
end

# reduce 124 omitted

# reduce 125 omitted

# reduce 126 omitted

# reduce 127 omitted

# reduce 128 omitted

# reduce 129 omitted

# reduce 130 omitted

# reduce 131 omitted

# reduce 132 omitted

# reduce 133 omitted

# reduce 134 omitted

# reduce 135 omitted

# reduce 136 omitted

# reduce 137 omitted

# reduce 138 omitted

# reduce 139 omitted

# reduce 140 omitted

# reduce 141 omitted

# reduce 142 omitted

# reduce 143 omitted

# reduce 144 omitted

# reduce 145 omitted

# reduce 146 omitted

# reduce 147 omitted

# reduce 148 omitted

# reduce 149 omitted

# reduce 150 omitted

# reduce 151 omitted

# reduce 152 omitted

# reduce 153 omitted

# reduce 154 omitted

# reduce 155 omitted

# reduce 156 omitted

# reduce 157 omitted

# reduce 158 omitted

# reduce 159 omitted

# reduce 160 omitted

# reduce 161 omitted

# reduce 162 omitted

# reduce 163 omitted

# reduce 164 omitted

# reduce 165 omitted

# reduce 166 omitted

# reduce 167 omitted

# reduce 168 omitted

# reduce 169 omitted

# reduce 170 omitted

# reduce 171 omitted

# reduce 172 omitted

# reduce 173 omitted

# reduce 174 omitted

# reduce 175 omitted

# reduce 176 omitted

# reduce 177 omitted

# reduce 178 omitted

# reduce 179 omitted

# reduce 180 omitted

# reduce 181 omitted

# reduce 182 omitted

# reduce 183 omitted

# reduce 184 omitted

# reduce 185 omitted

# reduce 186 omitted

# reduce 187 omitted

# reduce 188 omitted

# reduce 189 omitted

# reduce 190 omitted

# reduce 191 omitted

# reduce 192 omitted

# reduce 193 omitted

# reduce 194 omitted

def _reduce_195(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

def _reduce_196(val, _values, result)
                      rescue_body = @builder.rescue_body(val[3],
                                        nil, nil, nil,
                                        nil, val[4])

                      rescue_ = @builder.begin_body(val[2], [ rescue_body ])

                      result  = @builder.assign(val[0], val[1], rescue_)

    result
end

def _reduce_197(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_198(val, _values, result)
                      rescue_body = @builder.rescue_body(val[3],
                                        nil, nil, nil,
                                        nil, val[4])

                      rescue_ = @builder.begin_body(val[2], [ rescue_body ])

                      result = @builder.op_assign(val[0], val[1], rescue_)

    result
end

def _reduce_199(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])

    result
end

def _reduce_200(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_201(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_202(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_203(val, _values, result)
                      const  = @builder.const_op_assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))
                      result = @builder.op_assign(const, val[3], val[4])

    result
end

def _reduce_204(val, _values, result)
                      const  = @builder.const_op_assignable(
                                  @builder.const_global(val[0], val[1]))
                      result = @builder.op_assign(const, val[2], val[3])

    result
end

def _reduce_205(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_206(val, _values, result)
                      result = @builder.range_inclusive(val[0], val[1], val[2])

    result
end

def _reduce_207(val, _values, result)
                      result = @builder.range_exclusive(val[0], val[1], val[2])

    result
end

def _reduce_208(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_209(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_210(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_211(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_212(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_213(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_214(val, _values, result)
                      result = @builder.unary_op(val[0],
                                  @builder.binary_op(
                                    @builder.integer(val[1]),
                                      val[2], val[3]))

    result
end

def _reduce_215(val, _values, result)
                      result = @builder.unary_op(val[0],
                                  @builder.binary_op(
                                    @builder.float(val[1]),
                                      val[2], val[3]))

    result
end

def _reduce_216(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_217(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_218(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_219(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_220(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_221(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_222(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_223(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_224(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_225(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_226(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_227(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_228(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_229(val, _values, result)
                      result = @builder.match_op(val[0], val[1], val[2])

    result
end

def _reduce_230(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_231(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[1], nil)

    result
end

def _reduce_232(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_233(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_234(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_235(val, _values, result)
                      result = @builder.logical_op(:and, val[0], val[1], val[2])

    result
end

def _reduce_236(val, _values, result)
                      result = @builder.logical_op(:or, val[0], val[1], val[2])

    result
end

def _reduce_237(val, _values, result)
                      result = @builder.keyword_cmd(:defined?, val[0], nil, [ val[2] ], nil)

    result
end

def _reduce_238(val, _values, result)
                      result = @builder.ternary(val[0], val[1],
                                                val[2], val[4], val[5])

    result
end

# reduce 239 omitted

# reduce 240 omitted

# reduce 241 omitted

# reduce 242 omitted

def _reduce_243(val, _values, result)
                      result = val[0] << @builder.associate(nil, val[2], nil)

    result
end

def _reduce_244(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]

    result
end

def _reduce_245(val, _values, result)
                      result = val

    result
end

def _reduce_246(val, _values, result)
                      result = [ nil, [], nil ]

    result
end

# reduce 247 omitted

def _reduce_248(val, _values, result)
                      result = []

    result
end

# reduce 249 omitted

# reduce 250 omitted

def _reduce_251(val, _values, result)
                      result = val[0] << @builder.associate(nil, val[2], nil)

    result
end

def _reduce_252(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]

    result
end

def _reduce_253(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_254(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_255(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]
                      result.concat(val[1])

    result
end

def _reduce_256(val, _values, result)
                      assocs = @builder.associate(nil, val[2], nil)
                      result = val[0] << assocs
                      result.concat(val[3])

    result
end

def _reduce_257(val, _values, result)
                      result =  [ val[0] ]

    result
end

def _reduce_258(val, _values, result)
                      result = @lexer.cmdarg.dup
                      @lexer.cmdarg.push(true)

    result
end

def _reduce_259(val, _values, result)
                      @lexer.cmdarg = val[0]

                      result = val[1]

    result
end

def _reduce_260(val, _values, result)
                      result = @builder.block_pass(val[0], val[1])

    result
end

def _reduce_261(val, _values, result)
                      result = [ val[1] ]

    result
end

def _reduce_262(val, _values, result)
                      result = []

    result
end

def _reduce_263(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_264(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

def _reduce_265(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_266(val, _values, result)
                      result = val[0] << @builder.splat(val[2], val[3])

    result
end

def _reduce_267(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_268(val, _values, result)
                      result = val[0] << @builder.splat(val[2], val[3])

    result
end

def _reduce_269(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

# reduce 270 omitted

# reduce 271 omitted

# reduce 272 omitted

# reduce 273 omitted

# reduce 274 omitted

# reduce 275 omitted

# reduce 276 omitted

# reduce 277 omitted

# reduce 278 omitted

# reduce 279 omitted

def _reduce_280(val, _values, result)
                      result = @builder.call_method(nil, nil, val[0])

    result
end

def _reduce_281(val, _values, result)
                      result = @lexer.cmdarg.dup
                      @lexer.cmdarg.clear

    result
end

def _reduce_282(val, _values, result)
                      @lexer.cmdarg = val[1]

                      result = @builder.begin_keyword(val[0], val[2], val[3])

    result
end

def _reduce_283(val, _values, result)
                      result = @lexer.cmdarg.dup
                      @lexer.cmdarg.clear

    result
end

def _reduce_284(val, _values, result)
                      @lexer.state = :expr_endarg

    result
end

def _reduce_285(val, _values, result)
                      @lexer.cmdarg = val[1]

                      result = @builder.begin(val[0], val[2], val[5])

    result
end

def _reduce_286(val, _values, result)
                      @lexer.state = :expr_endarg

    result
end

def _reduce_287(val, _values, result)
                      result = @builder.begin(val[0], nil, val[3])

    result
end

def _reduce_288(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_289(val, _values, result)
                      result = @builder.const_fetch(val[0], val[1], val[2])

    result
end

def _reduce_290(val, _values, result)
                      result = @builder.const_global(val[0], val[1])

    result
end

def _reduce_291(val, _values, result)
                      result = @builder.array(val[0], val[1], val[2])

    result
end

def _reduce_292(val, _values, result)
                      result = @builder.associate(val[0], val[1], val[2])

    result
end

def _reduce_293(val, _values, result)
                      result = @builder.keyword_cmd(:return, val[0])

    result
end

def _reduce_294(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0], val[1], val[2], val[3])

    result
end

def _reduce_295(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0], val[1], [], val[2])

    result
end

def _reduce_296(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0])

    result
end

def _reduce_297(val, _values, result)
                      result = @builder.keyword_cmd(:defined?, val[0],
                                                    val[2], [ val[3] ], val[4])

    result
end

def _reduce_298(val, _values, result)
                      result = @builder.not_op(val[0], val[1], val[2], val[3])

    result
end

def _reduce_299(val, _values, result)
                      result = @builder.not_op(val[0], val[1], nil, val[2])

    result
end

def _reduce_300(val, _values, result)
                      method_call = @builder.call_method(nil, nil, val[0])

                      begin_t, args, body, end_t = val[1]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

# reduce 301 omitted

def _reduce_302(val, _values, result)
                      begin_t, args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, args, body, end_t)

    result
end

def _reduce_303(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_304(val, _values, result)
                      lambda_call = @builder.call_lambda(val[0])

                      args, (begin_t, body, end_t) = val[2]
                      result      = @builder.block(lambda_call,
                                      begin_t, args, body, end_t)

                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_305(val, _values, result)
                      else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  val[3], else_t,
                                                  else_,  val[5])

    result
end

def _reduce_306(val, _values, result)
                      else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  else_,  else_t,
                                                  val[3], val[5])

    result
end

def _reduce_307(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_308(val, _values, result)
                      @lexer.cond.pop

    result
end

def _reduce_309(val, _values, result)
                      result = @builder.loop(:while, val[0], val[2], val[3],
                                             val[5], val[6])

    result
end

def _reduce_310(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_311(val, _values, result)
                      @lexer.cond.pop

    result
end

def _reduce_312(val, _values, result)
                      result = @builder.loop(:until, val[0], val[2], val[3],
                                             val[5], val[6])

    result
end

def _reduce_313(val, _values, result)
                      *when_bodies, (else_t, else_body) = *val[3]

                      result = @builder.case(val[0], val[1],
                                             when_bodies, else_t, else_body,
                                             val[4])

    result
end

def _reduce_314(val, _values, result)
                      *when_bodies, (else_t, else_body) = *val[2]

                      result = @builder.case(val[0], nil,
                                             when_bodies, else_t, else_body,
                                             val[3])

    result
end

def _reduce_315(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_316(val, _values, result)
                      @lexer.cond.pop

    result
end

def _reduce_317(val, _values, result)
                      result = @builder.for(val[0], val[1],
                                            val[2], val[4],
                                            val[5], val[7], val[8])

    result
end

def _reduce_318(val, _values, result)
                      local_push
                      @context.in_class = true

    result
end

def _reduce_319(val, _values, result)
                      k_class, ctx = val[0]
                      if @context.in_def
                        diagnostic :error, :class_in_def, nil, k_class
                      end

                      lt_t, superclass = val[2]
                      result = @builder.def_class(k_class, val[1],
                                                  lt_t, superclass,
                                                  val[4], val[5])

                      local_pop
                      @context.in_class = ctx.in_class

    result
end

def _reduce_320(val, _values, result)
                      @context.in_def = false
                      @context.in_class = false
                      local_push

    result
end

def _reduce_321(val, _values, result)
                      k_class, ctx = val[0]
                      result = @builder.def_sclass(k_class, val[1], val[2],
                                                   val[5], val[6])

                      local_pop
                      @context.in_def = ctx.in_def
                      @context.in_class = ctx.in_class

    result
end

def _reduce_322(val, _values, result)
                      @context.in_class = true
                      local_push

    result
end

def _reduce_323(val, _values, result)
                      k_mod, ctx = val[0]
                      if @context.in_def
                        diagnostic :error, :module_in_def, nil, k_mod
                      end

                      result = @builder.def_module(k_mod, val[1],
                                                   val[3], val[4])

                      local_pop
                      @context.in_class = ctx.in_class

    result
end

def _reduce_324(val, _values, result)
                      local_push
                      result = context.dup
                      @context.in_def = true

    result
end

def _reduce_325(val, _values, result)
                      result = @builder.def_method(val[0], val[1],
                                  val[3], val[4], val[5])

                      local_pop
                      @context.in_def = val[2].in_def

    result
end

def _reduce_326(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_327(val, _values, result)
                      local_push
                      result = context.dup
                      @context.in_def = true

    result
end

def _reduce_328(val, _values, result)
                      result = @builder.def_singleton(val[0], val[1], val[2],
                                  val[4], val[6], val[7], val[8])

                      local_pop
                      @context.in_def = val[5].in_def

    result
end

def _reduce_329(val, _values, result)
                      result = @builder.keyword_cmd(:break, val[0])

    result
end

def _reduce_330(val, _values, result)
                      result = @builder.keyword_cmd(:next, val[0])

    result
end

def _reduce_331(val, _values, result)
                      result = @builder.keyword_cmd(:redo, val[0])

    result
end

def _reduce_332(val, _values, result)
                      result = @builder.keyword_cmd(:retry, val[0])

    result
end

# reduce 333 omitted

def _reduce_334(val, _values, result)
                      result = [ val[0], @context.dup ]

    result
end

def _reduce_335(val, _values, result)
                      result = [ val[0], @context.dup ]

    result
end

# reduce 336 omitted

# reduce 337 omitted

def _reduce_338(val, _values, result)
                      result = val[1]

    result
end

# reduce 339 omitted

# reduce 340 omitted

# reduce 341 omitted

def _reduce_342(val, _values, result)
                      else_t, else_ = val[4]
                      result = [ val[0],
                                 @builder.condition(val[0], val[1], val[2],
                                                    val[3], else_t,
                                                    else_,  nil),
                               ]

    result
end

# reduce 343 omitted

def _reduce_344(val, _values, result)
                      result = val

    result
end

# reduce 345 omitted

# reduce 346 omitted

def _reduce_347(val, _values, result)
                      @static_env.declare val[0][0]

                      result = @builder.arg(val[0])

    result
end

def _reduce_348(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

def _reduce_349(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_350(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 351 omitted

def _reduce_352(val, _values, result)
                      @static_env.declare val[3][0]

                      result = val[0].
                                  push(@builder.restarg(val[2], val[3]))

    result
end

def _reduce_353(val, _values, result)
                      @static_env.declare val[3][0]

                      result = val[0].
                                  push(@builder.restarg(val[2], val[3])).
                                  concat(val[5])

    result
end

def _reduce_354(val, _values, result)
                      result = val[0].
                                  push(@builder.restarg(val[2]))

    result
end

def _reduce_355(val, _values, result)
                      result = val[0].
                                  push(@builder.restarg(val[2])).
                                  concat(val[4])

    result
end

def _reduce_356(val, _values, result)
                      @static_env.declare val[1][0]

                      result = [ @builder.restarg(val[0], val[1]) ]

    result
end

def _reduce_357(val, _values, result)
                      @static_env.declare val[1][0]

                      result = [ @builder.restarg(val[0], val[1]),
                                 *val[3] ]

    result
end

def _reduce_358(val, _values, result)
                      result = [ @builder.restarg(val[0]) ]

    result
end

def _reduce_359(val, _values, result)
                      result = [ @builder.restarg(val[0]),
                                 *val[2] ]

    result
end

def _reduce_360(val, _values, result)
                      result = val[0].concat(val[2]).concat(val[3])

    result
end

def _reduce_361(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_362(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_363(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_364(val, _values, result)
                      result = val[1]

    result
end

def _reduce_365(val, _values, result)
                      result = []

    result
end

def _reduce_366(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_367(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[6]).
                                  concat(val[7])

    result
end

def _reduce_368(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_369(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_370(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

# reduce 371 omitted

def _reduce_372(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_373(val, _values, result)
                      if val[1].empty? && val[0].size == 1
                        result = [@builder.procarg0(val[0][0])]
                      else
                        result = val[0].concat(val[1])
                      end

    result
end

def _reduce_374(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_375(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_376(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_377(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_378(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_379(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

# reduce 380 omitted

def _reduce_381(val, _values, result)
                      result = @builder.args(nil, [], nil)

    result
end

def _reduce_382(val, _values, result)
                      @lexer.state = :expr_value

    result
end

def _reduce_383(val, _values, result)
                      result = @builder.args(val[0], val[1], val[2])

    result
end

def _reduce_384(val, _values, result)
                      result = @builder.args(val[0], [], val[0])

    result
end

def _reduce_385(val, _values, result)
                      result = @builder.args(val[0], val[1].concat(val[2]), val[3])

    result
end

def _reduce_386(val, _values, result)
                      result = []

    result
end

def _reduce_387(val, _values, result)
                      result = val[2]

    result
end

def _reduce_388(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_389(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_390(val, _values, result)
                      @static_env.declare val[0][0]
                      result = @builder.shadowarg(val[0])

    result
end

# reduce 391 omitted

def _reduce_392(val, _values, result)
                      @static_env.extend_dynamic

    result
end

def _reduce_393(val, _values, result)
                      result = [ val[1], val[2] ]

                      @static_env.unextend

    result
end

def _reduce_394(val, _values, result)
                      result = @builder.args(val[0], val[1].concat(val[2]), val[3])

    result
end

def _reduce_395(val, _values, result)
                      result = @builder.args(nil, val[0], nil)

    result
end

def _reduce_396(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_397(val, _values, result)
                      result = [ val[0], val[2], val[3] ]
                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_398(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_399(val, _values, result)
                      result = [ val[0], val[2], val[3] ]
                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_400(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_401(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_402(val, _values, result)
                      begin_t, block_args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, block_args, body, end_t)

    result
end

def _reduce_403(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_404(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                      lparen_t, args, rparen_t)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_405(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                      nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_406(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.call_method(nil, nil, val[0],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_407(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_408(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_409(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2])

    result
end

def _reduce_410(val, _values, result)
                      lparen_t, args, rparen_t = val[2]
                      result = @builder.call_method(val[0], val[1], nil,
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_411(val, _values, result)
                      lparen_t, args, rparen_t = val[2]
                      result = @builder.call_method(val[0], val[1], nil,
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_412(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.keyword_cmd(:super, val[0],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_413(val, _values, result)
                      result = @builder.keyword_cmd(:zsuper, val[0])

    result
end

def _reduce_414(val, _values, result)
                      result = @builder.index(val[0], val[1], val[2], val[3])

    result
end

def _reduce_415(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_416(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_417(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_418(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_419(val, _values, result)
                      result = [ @builder.when(val[0], val[1], val[2], val[3]),
                                 *val[4] ]

    result
end

def _reduce_420(val, _values, result)
                      result = [ val[0] ]

    result
end

# reduce 421 omitted

def _reduce_422(val, _values, result)
                      assoc_t, exc_var = val[2]

                      if val[1]
                        exc_list = @builder.array(nil, val[1], nil)
                      end

                      result = [ @builder.rescue_body(val[0],
                                      exc_list, assoc_t, exc_var,
                                      val[3], val[4]),
                                 *val[5] ]

    result
end

def _reduce_423(val, _values, result)
                      result = []

    result
end

def _reduce_424(val, _values, result)
                      result = [ val[0] ]

    result
end

# reduce 425 omitted

# reduce 426 omitted

def _reduce_427(val, _values, result)
                      result = [ val[0], val[1] ]

    result
end

# reduce 428 omitted

def _reduce_429(val, _values, result)
                      result = [ val[0], val[1] ]

    result
end

# reduce 430 omitted

# reduce 431 omitted

# reduce 432 omitted

# reduce 433 omitted

def _reduce_434(val, _values, result)
                      result = @builder.string_compose(nil, val[0], nil)

    result
end

def _reduce_435(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_436(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_437(val, _values, result)
                      result = @builder.string_compose(val[0], val[1], val[2])

    result
end

def _reduce_438(val, _values, result)
                      result = @builder.string(val[0])

    result
end

def _reduce_439(val, _values, result)
                      result = @builder.character(val[0])

    result
end

def _reduce_440(val, _values, result)
                      result = @builder.xstring_compose(val[0], val[1], val[2])

    result
end

def _reduce_441(val, _values, result)
                      opts   = @builder.regexp_options(val[3])
                      result = @builder.regexp_compose(val[0], val[1], val[2], opts)

    result
end

def _reduce_442(val, _values, result)
                      result = @builder.words_compose(val[0], val[1], val[2])

    result
end

def _reduce_443(val, _values, result)
                      result = []

    result
end

def _reduce_444(val, _values, result)
                      result = val[0] << @builder.word(val[1])

    result
end

def _reduce_445(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_446(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_447(val, _values, result)
                      result = @builder.symbols_compose(val[0], val[1], val[2])

    result
end

def _reduce_448(val, _values, result)
                      result = []

    result
end

def _reduce_449(val, _values, result)
                      result = val[0] << @builder.word(val[1])

    result
end

def _reduce_450(val, _values, result)
                      result = @builder.words_compose(val[0], val[1], val[2])

    result
end

def _reduce_451(val, _values, result)
                      result = @builder.symbols_compose(val[0], val[1], val[2])

    result
end

def _reduce_452(val, _values, result)
                      result = []

    result
end

def _reduce_453(val, _values, result)
                      result = val[0] << @builder.string_internal(val[1])

    result
end

def _reduce_454(val, _values, result)
                      result = []

    result
end

def _reduce_455(val, _values, result)
                      result = val[0] << @builder.symbol_internal(val[1])

    result
end

def _reduce_456(val, _values, result)
                      result = []

    result
end

def _reduce_457(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_458(val, _values, result)
                      result = []

    result
end

def _reduce_459(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_460(val, _values, result)
                      result = []

    result
end

def _reduce_461(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_462(val, _values, result)
                      result = @builder.string_internal(val[0])

    result
end

def _reduce_463(val, _values, result)
                      result = val[1]

    result
end

def _reduce_464(val, _values, result)
                      @lexer.cond.push(false)
                      @lexer.cmdarg.push(false)

    result
end

def _reduce_465(val, _values, result)
                      @lexer.cond.lexpop
                      @lexer.cmdarg.lexpop

                      result = @builder.begin(val[0], val[2], val[3])

    result
end

def _reduce_466(val, _values, result)
                      result = @builder.gvar(val[0])

    result
end

def _reduce_467(val, _values, result)
                      result = @builder.ivar(val[0])

    result
end

def _reduce_468(val, _values, result)
                      result = @builder.cvar(val[0])

    result
end

# reduce 469 omitted

def _reduce_470(val, _values, result)
                      result = @builder.symbol(val[0])

    result
end

def _reduce_471(val, _values, result)
                      result = @builder.symbol_compose(val[0], val[1], val[2])

    result
end

def _reduce_472(val, _values, result)
                      result = @builder.integer(val[0])

    result
end

def _reduce_473(val, _values, result)
                      result = @builder.float(val[0])

    result
end

def _reduce_474(val, _values, result)
                      num = @builder.integer(val[1])
                      if @builder.respond_to? :negate
                        # AST builder interface compatibility
                        result = @builder.negate(val[0], num)
                      else
                        result = @builder.unary_num(val[0], num)
                      end

    result
end

def _reduce_475(val, _values, result)
                      num = @builder.float(val[1])
                      if @builder.respond_to? :negate
                        # AST builder interface compatibility
                        result = @builder.negate(val[0], num)
                      else
                        result = @builder.unary_num(val[0], num)
                      end

    result
end

def _reduce_476(val, _values, result)
                      result = @builder.ident(val[0])

    result
end

def _reduce_477(val, _values, result)
                      result = @builder.ivar(val[0])

    result
end

def _reduce_478(val, _values, result)
                      result = @builder.gvar(val[0])

    result
end

def _reduce_479(val, _values, result)
                      result = @builder.const(val[0])

    result
end

def _reduce_480(val, _values, result)
                      result = @builder.cvar(val[0])

    result
end

def _reduce_481(val, _values, result)
                      result = @builder.nil(val[0])

    result
end

def _reduce_482(val, _values, result)
                      result = @builder.self(val[0])

    result
end

def _reduce_483(val, _values, result)
                      result = @builder.true(val[0])

    result
end

def _reduce_484(val, _values, result)
                      result = @builder.false(val[0])

    result
end

def _reduce_485(val, _values, result)
                      result = @builder.__FILE__(val[0])

    result
end

def _reduce_486(val, _values, result)
                      result = @builder.__LINE__(val[0])

    result
end

def _reduce_487(val, _values, result)
                      result = @builder.__ENCODING__(val[0])

    result
end

def _reduce_488(val, _values, result)
                      result = @builder.accessible(val[0])

    result
end

def _reduce_489(val, _values, result)
                      result = @builder.accessible(val[0])

    result
end

def _reduce_490(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_491(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_492(val, _values, result)
                      result = @builder.nth_ref(val[0])

    result
end

def _reduce_493(val, _values, result)
                      result = @builder.back_ref(val[0])

    result
end

def _reduce_494(val, _values, result)
                      result = nil

    result
end

def _reduce_495(val, _values, result)
                      @lexer.state = :expr_value

    result
end

def _reduce_496(val, _values, result)
                      result = [ val[0], val[2] ]

    result
end

def _reduce_497(val, _values, result)
                      yyerrok
                      result = nil

    result
end

def _reduce_498(val, _values, result)
                      result = @builder.args(val[0], val[1], val[2])

                      @lexer.state = :expr_value

    result
end

def _reduce_499(val, _values, result)
                      result = @builder.args(nil, val[0], nil)

    result
end

def _reduce_500(val, _values, result)
                      result = val[0].concat(val[2]).concat(val[3])

    result
end

def _reduce_501(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_502(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_503(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_504(val, _values, result)
                      result = val[1]

    result
end

def _reduce_505(val, _values, result)
                      result = []

    result
end

def _reduce_506(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_507(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[6]).
                                  concat(val[7])

    result
end

def _reduce_508(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_509(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_510(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_511(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_512(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_513(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_514(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_515(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_516(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_517(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_518(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_519(val, _values, result)
                      result = val[0]

    result
end

def _reduce_520(val, _values, result)
                      result = []

    result
end

def _reduce_521(val, _values, result)
                      diagnostic :error, :argument_const, nil, val[0]

    result
end

def _reduce_522(val, _values, result)
                      diagnostic :error, :argument_ivar, nil, val[0]

    result
end

def _reduce_523(val, _values, result)
                      diagnostic :error, :argument_gvar, nil, val[0]

    result
end

def _reduce_524(val, _values, result)
                      diagnostic :error, :argument_cvar, nil, val[0]

    result
end

# reduce 525 omitted

# reduce 526 omitted

def _reduce_527(val, _values, result)
                      @static_env.declare val[0][0]

                      result = @builder.arg(val[0])

    result
end

def _reduce_528(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

def _reduce_529(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_530(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_531(val, _values, result)
                      check_kwarg_name(val[0])

                      @static_env.declare val[0][0]

                      result = @builder.kwoptarg(val[0], val[1])

    result
end

def _reduce_532(val, _values, result)
                      check_kwarg_name(val[0])

                      @static_env.declare val[0][0]

                      result = @builder.kwoptarg(val[0], val[1])

    result
end

def _reduce_533(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_534(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_535(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_536(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 537 omitted

# reduce 538 omitted

def _reduce_539(val, _values, result)
                      @static_env.declare val[1][0]

                      result = [ @builder.kwrestarg(val[0], val[1]) ]

    result
end

def _reduce_540(val, _values, result)
                      result = [ @builder.kwrestarg(val[0]) ]

    result
end

def _reduce_541(val, _values, result)
                      @static_env.declare val[0][0]

                      result = @builder.optarg(val[0], val[1], val[2])

    result
end

def _reduce_542(val, _values, result)
                      @static_env.declare val[0][0]

                      result = @builder.optarg(val[0], val[1], val[2])

    result
end

def _reduce_543(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_544(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_545(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_546(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 547 omitted

# reduce 548 omitted

def _reduce_549(val, _values, result)
                      @static_env.declare val[1][0]

                      result = [ @builder.restarg(val[0], val[1]) ]

    result
end

def _reduce_550(val, _values, result)
                      result = [ @builder.restarg(val[0]) ]

    result
end

# reduce 551 omitted

# reduce 552 omitted

def _reduce_553(val, _values, result)
                      @static_env.declare val[1][0]

                      result = @builder.blockarg(val[0], val[1])

    result
end

def _reduce_554(val, _values, result)
                      result = [ val[1] ]

    result
end

def _reduce_555(val, _values, result)
                      result = []

    result
end

# reduce 556 omitted

def _reduce_557(val, _values, result)
                      result = val[1]

    result
end

def _reduce_558(val, _values, result)
                      result = []

    result
end

# reduce 559 omitted

def _reduce_560(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_561(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_562(val, _values, result)
                      result = @builder.pair(val[0], val[1], val[2])

    result
end

def _reduce_563(val, _values, result)
                      result = @builder.pair_keyword(val[0], val[1])

    result
end

def _reduce_564(val, _values, result)
                      result = @builder.kwsplat(val[0], val[1])

    result
end

# reduce 565 omitted

# reduce 566 omitted

# reduce 567 omitted

# reduce 568 omitted

# reduce 569 omitted

# reduce 570 omitted

# reduce 571 omitted

# reduce 572 omitted

# reduce 573 omitted

# reduce 574 omitted

# reduce 575 omitted

# reduce 576 omitted

# reduce 577 omitted

# reduce 578 omitted

# reduce 579 omitted

# reduce 580 omitted

def _reduce_581(val, _values, result)
                      result = val[1]

    result
end

def _reduce_582(val, _values, result)
                      result = val[1]

    result
end

# reduce 583 omitted

# reduce 584 omitted

# reduce 585 omitted

def _reduce_586(val, _values, result)
                    yyerrok

    result
end

# reduce 587 omitted

# reduce 588 omitted

# reduce 589 omitted

def _reduce_590(val, _values, result)
                    result = nil

    result
end

def _reduce_none(val, _values, result)
  val[0]
end

  end   # class Ruby20
end   # module Parser
