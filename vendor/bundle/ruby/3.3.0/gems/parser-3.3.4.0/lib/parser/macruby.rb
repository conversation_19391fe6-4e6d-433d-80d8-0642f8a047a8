# -*- encoding:utf-8; warn-indent:false; frozen_string_literal: true  -*-
#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.7.3
# from Racc grammar file "macruby.y".
#

require 'racc/parser.rb'


require_relative '../parser'

module Parser
  class MacRuby < Parser::Base


  def version
    19 # closest released match: v1_9_0_2
  end

  def default_encoding
    Encoding::BINARY
  end

  def local_push
    @static_env.extend_static
    @lexer.cmdarg.push(false)
    @lexer.cond.push(false)
  end

  def local_pop
    @static_env.unextend
    @lexer.cmdarg.pop
    @lexer.cond.pop
  end
##### State transition tables begin ###

racc_action_table = [
  -460,     5,    69,    70,    66,     9,    52,  -460,  -460,  -460,
    58,    59,  -460,  -460,  -460,    62,  -460,    60,    61,    63,
    28,    29,    67,    68,  -460,   257,  -460,  -460,  -460,    27,
    26,    25,    93,    92,    94,    95,  -460,  -460,    18,  -460,
  -460,  -460,  -460,  -460,     8,    42,     7,    10,    97,    96,
    98,    87,    51,    89,    88,    90,   535,    91,    99,   100,
   514,    85,    86,    39,    40,    38,  -460,  -460,  -460,  -460,
  -460,  -460,  -460,  -460,  -460,  -460,  -460,  -460,  -460,  -460,
   101,   468,  -460,  -460,  -460,    37,  -460,  -460,    31,   -93,
  -460,    53,    54,  -460,  -460,    55,  -460,    33,  -460,   556,
  -460,    41,  -460,  -460,  -460,  -460,  -460,  -460,  -460,    19,
  -460,   256,  -460,  -100,    84,    76,    79,    80,   208,    81,
    82,   113,   -99,   736,    77,    83,  -460,  -460,  -460,  -460,
  -463,  -460,    57,  -460,    78,  -460,   759,  -463,  -463,  -463,
  -473,   204,  -463,  -463,  -463,   667,  -463,   526,   514,   -87,
   197,   527,   514,   588,  -463,   209,  -463,  -463,  -463,   -95,
   504,   534,   555,   503,   198,   -97,  -463,  -463,   667,  -463,
  -463,  -463,  -463,  -463,   105,   514,   514,   -95,   -97,   104,
   -94,   105,   205,   206,   -92,   -94,   104,   587,   105,   -93,
  -100,   588,   199,   104,   -85,   419,  -463,  -463,  -463,  -463,
  -463,  -463,  -463,  -463,  -463,  -463,  -463,  -463,  -463,  -463,
   205,   206,  -463,  -463,  -463,   -87,  -463,  -463,   -92,   200,
  -463,   -96,   -89,  -463,  -463,   587,  -463,   -91,  -463,   247,
  -463,   667,  -463,  -463,  -463,  -463,  -463,  -463,  -463,  -286,
  -463,   758,  -463,   300,  -100,   105,  -286,  -286,  -286,   588,
   104,   -87,  -286,  -286,   -87,  -286,  -463,  -463,  -463,  -463,
   -87,  -463,   105,  -463,   -87,  -463,   105,   104,   105,   786,
   -89,   104,   105,   104,   257,  -286,  -286,   104,  -286,  -286,
  -286,  -286,  -286,   587,   301,   -86,   257,   -96,   -89,   105,
   105,   -95,   -97,   -98,   104,   104,   -95,   -97,  -534,   -94,
   209,   631,   209,   520,   -94,  -286,  -286,  -286,  -286,  -286,
  -286,  -286,  -286,  -286,  -286,  -286,  -286,  -286,  -286,   205,
   206,  -286,  -286,  -286,   -89,   573,  -473,   -89,   366,  -286,
   491,   105,  -286,   -89,   545,   -96,   104,  -286,   252,  -286,
   -96,  -286,  -286,  -286,  -286,  -286,  -286,  -286,  -538,  -286,
   252,  -286,  -534,   205,   206,  -538,  -538,  -538,  -535,   767,
   256,  -538,  -538,   719,  -538,  -286,  -286,   588,   -90,   -85,
  -286,   249,   256,  -538,   -98,   -93,  -531,  -404,   250,   -99,
   504,   205,   206,   506,  -538,  -538,  -460,  -538,  -538,  -538,
  -538,  -538,   -88,  -460,   379,   547,   546,   543,   -90,   767,
   775,   587,   647,   646,   645,   651,   648,   768,   545,   209,
   504,   418,   -95,   506,  -538,  -538,  -538,  -538,  -538,  -538,
  -538,  -538,  -538,  -538,  -538,  -538,  -538,  -538,  -460,  -404,
  -538,  -538,  -538,   420,   574,  -460,  -404,   421,  -538,   720,
  -531,  -538,   545,   209,  -460,  -404,  -538,   817,  -538,   208,
  -538,  -538,  -538,  -538,  -538,  -538,  -538,    76,  -538,  -538,
  -538,   545,  -531,  -404,   302,   303,    77,  -532,  -538,   547,
   546,   548,   803,  -538,  -538,  -538,    78,   -88,   850,  -538,
  -538,  -538,  -538,   -96,  -463,  -538,  -538,  -538,   -97,  -538,
   775,  -463,   647,   646,   645,   651,   648,   228,  -538,  -538,
  -538,  -538,   376,   547,   546,   550,  -538,   378,   377,  -538,
  -538,  -470,  -538,  -538,  -538,  -538,  -538,   453,  -470,  -463,
  -538,   -94,   547,   546,   552,   545,  -463,  -538,   545,   225,
  -469,  -532,  -534,   227,   226,  -463,  -538,  -469,   464,  -538,
  -538,  -538,  -538,  -538,  -538,  -538,  -538,  -538,  -538,  -538,
  -538,  -538,  -538,  -532,  -538,  -538,  -538,  -538,  -538,   721,
  -538,   486,   487,  -538,   491,  -538,  -538,  -538,   938,  -538,
  -534,  -538,   466,  -538,  -538,  -538,  -538,  -538,  -538,  -538,
  -538,  -538,   -80,  -538,  -538,  -538,   547,   546,   -66,   547,
   546,   557,  -538,   468,   504,   699,   228,   506,   568,  -538,
  -538,  -538,  -538,  -286,  -538,   569,  -538,   -91,   -96,   707,
  -286,  -286,  -286,   -99,   -65,  -286,  -286,  -286,   732,  -286,
   647,   646,   645,   651,   648,   228,   205,   206,   225,  -286,
  -286,  -286,   227,   226,   223,   224,   205,   206,   209,  -286,
  -286,   -87,  -286,  -286,  -286,  -286,  -286,   -95,   -89,  -471,
   781,   631,   -86,  -468,   -97,   653,  -471,   225,   -94,   476,
  -468,   227,   226,   105,   657,   656,   660,   659,   104,  -286,
  -286,  -286,  -286,  -286,  -286,  -286,  -286,  -286,  -286,  -286,
  -286,  -286,  -286,   477,   202,  -286,  -286,  -286,  -330,   722,
  -286,   203,   484,  -286,   261,  -330,  -286,  -286,   731,  -286,
   201,  -286,   209,  -286,  -330,  -286,  -286,  -286,  -286,  -286,
  -286,  -286,   252,  -286,   488,  -286,   492,   650,  -465,   647,
   646,   645,   651,   648,   493,  -465,  -466,   228,  -467,  -286,
  -286,  -286,  -286,  -466,  -286,  -467,  -286,   499,   -98,   277,
    69,    70,    66,     9,    52,   540,   105,   500,    58,    59,
   507,   104,   541,    62,   653,    60,    61,    63,    28,    29,
    67,    68,   508,   657,   656,   660,   659,    27,    26,    25,
    93,    92,    94,    95,   693,   694,    18,   468,   695,    99,
   100,   562,     8,    42,   520,    10,    97,    96,    98,    87,
    51,    89,    88,    90,   368,    91,    99,   100,   524,    85,
    86,    39,    40,    38,   228,   232,   237,   238,   239,   234,
   236,   244,   245,   240,   241,   415,   221,   222,  -472,   105,
   242,   243,   416,    37,   104,  -472,   279,   105,   525,    53,
    54,   417,   104,    55,  -472,    33,   225,   558,   231,    41,
   227,   226,   223,   224,   235,   233,   229,    19,   230,   781,
   631,   105,    84,    76,    79,    80,   104,    81,    82,   228,
   561,   482,    77,    83,   564,   246,  -281,  -233,   483,  -266,
    57,   209,    78,  -281,  -281,  -281,   252,   481,  -281,  -281,
  -281,   650,  -281,   647,   646,   645,   651,   648,   578,   228,
   228,   225,  -281,  -281,  -281,   227,   226,   223,   224,   228,
   228,   209,  -281,  -281,   209,  -281,  -281,  -281,  -281,  -281,
   775,   209,   647,   646,   645,   651,   648,   -80,   653,   639,
   775,   613,   647,   646,   645,   651,   648,   657,   656,   660,
   659,   209,  -281,  -281,  -281,  -281,  -281,  -281,  -281,  -281,
  -281,  -281,  -281,  -281,  -281,  -281,   497,   771,  -281,  -281,
  -281,  -280,   624,  -281,   631,   209,  -281,   771,  -280,  -281,
  -281,   663,  -281,   520,  -281,   670,  -281,  -280,  -281,  -281,
  -281,  -281,  -281,  -281,  -281,   494,  -281,   698,  -281,   701,
   522,  -267,   495,   708,   453,   453,   209,   523,   724,   466,
   209,   417,  -281,  -281,  -281,  -281,   521,  -281,   743,  -281,
   277,    69,    70,    66,     9,    52,   468,   624,   530,    58,
    59,   209,   252,   252,    62,   529,    60,    61,    63,    28,
    29,    67,    68,   624,   531,   228,   228,   750,    27,    26,
    25,    93,    92,    94,    95,  -266,   775,    18,   647,   646,
   645,   651,   648,     8,    42,   754,    10,    97,    96,    98,
    87,    51,    89,    88,    90,   761,    91,    99,   100,   763,
    85,    86,    39,    40,    38,   775,   766,   647,   646,   645,
   651,   648,   775,   771,   647,   646,   645,   651,   648,  -287,
   769,  -287,   774,   777,    37,   778,  -287,    31,  -287,   631,
    53,    54,   785,   209,    55,  -287,    33,  -287,   209,   794,
    41,   775,   771,   647,   646,   645,   651,   648,    19,   653,
  -268,   774,   805,    84,    76,    79,    80,   807,    81,    82,
   660,   659,   810,    77,    83,     5,    69,    70,    66,     9,
    52,    57,   811,    78,    58,    59,   731,   818,   653,    62,
   209,    60,    61,    63,    28,    29,    67,    68,   824,   660,
   659,   825,   624,    27,    26,    25,    93,    92,    94,    95,
   731,   775,    18,   647,   646,   645,   651,   648,     8,    42,
     7,    10,    97,    96,    98,    87,    51,    89,    88,    90,
   845,    91,    99,   100,   848,    85,    86,    39,    40,    38,
   775,   767,   647,   646,   645,   651,   648,   775,   771,   647,
   646,   645,   651,   648,  -472,   209,  -286,   901,   852,    37,
   854,  -472,    31,  -286,   860,    53,    54,   862,  -535,    55,
  -472,    33,  -286,   209,   865,    41,   775,   771,   647,   646,
   645,   651,   648,    19,   653,  -269,   872,   873,    84,    76,
    79,    80,   876,    81,    82,   660,   659,   878,    77,    83,
   277,    69,    70,    66,     9,    52,    57,   466,    78,    58,
    59,   881,   886,   653,    62,   209,    60,    61,    63,    28,
    29,    67,    68,   890,   660,   659,   893,   895,    27,    26,
    25,    93,    92,    94,    95,   897,   775,    18,   647,   646,
   645,   651,   648,     8,    42,   897,    10,    97,    96,    98,
    87,    51,    89,    88,    90,   209,    91,    99,   100,   902,
    85,    86,    39,    40,    38,   775,   905,   647,   646,   645,
   651,   648,   775,   771,   647,   646,   645,   651,   648,   884,
   906,  -286,  -280,   911,    37,   913,   885,    31,  -286,  -280,
    53,    54,   916,  -535,    55,   883,    33,  -286,  -280,   918,
    41,   775,   771,   647,   646,   645,   651,   648,    19,   653,
   897,   897,   923,    84,    76,    79,    80,   499,    81,    82,
   660,   659,   931,    77,    83,   277,    69,    70,    66,     9,
    52,    57,   932,    78,    58,    59,   940,   466,   653,    62,
   209,    60,    61,    63,    28,    29,    67,    68,   953,   660,
   659,   897,   897,    27,    26,    25,    93,    92,    94,    95,
   897,   815,    18,   647,   646,   645,   957,   648,     8,    42,
   940,    10,    97,    96,    98,    87,    51,    89,    88,    90,
   960,    91,    99,   100,   961,    85,    86,    39,    40,    38,
   815,   530,   647,   646,   645,   963,   648,   775,   922,   647,
   646,   645,   651,   648,  -287,   897,  -286,   531,   897,    37,
   897,  -287,    31,  -286,  -535,    53,    54,  -534,  -535,    55,
  -287,    33,  -286,   940,   897,    41,   775,   940,   647,   646,
   645,   651,   648,    19,   653,   897,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   660,   659,   nil,    77,    83,
   277,    69,    70,    66,     9,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   653,    62,   nil,    60,    61,    63,    28,
    29,    67,    68,   nil,   660,   659,   nil,   nil,    27,    26,
    25,    93,    92,    94,    95,   nil,   nil,    18,   108,   109,
   110,   111,   112,     8,    42,   nil,    10,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   228,   108,   109,   110,   111,
   112,   nil,   775,   nil,   647,   646,   645,   651,   648,   nil,
   nil,   242,   243,   nil,    37,   nil,   nil,   279,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,    33,   225,   nil,   231,
    41,   227,   226,   223,   224,   nil,   nil,   nil,    19,   653,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   660,   659,   nil,    77,    83,   277,    69,    70,    66,     9,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,    28,    29,    67,    68,   108,   109,
   110,   111,   112,    27,    26,    25,    93,    92,    94,    95,
   nil,   nil,    18,   nil,   nil,   nil,   nil,   565,     8,    42,
   nil,    10,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   228,   232,   237,   238,   239,   234,   236,   244,   245,   240,
   241,   nil,   221,   222,   nil,   nil,   242,   243,   nil,    37,
   nil,   nil,   279,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,    33,   225,   nil,   231,    41,   227,   226,   223,   224,
   235,   233,   229,    19,   230,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,   246,  -539,   nil,   nil,   nil,    57,   nil,    78,  -539,
  -539,  -539,   nil,   nil,  -539,  -539,  -539,   650,  -539,   647,
   646,   645,   651,   648,   nil,   nil,   nil,  -539,  -539,  -539,
  -539,   108,   109,   110,   111,   112,   nil,   nil,  -539,  -539,
   nil,  -539,  -539,  -539,  -539,  -539,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   653,   688,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   657,   656,   660,   659,   nil,  -539,  -539,
  -539,  -539,  -539,  -539,  -539,  -539,  -539,  -539,  -539,  -539,
  -539,  -539,   nil,   nil,  -539,  -539,  -539,   nil,   nil,  -539,
   nil,   nil,  -539,   nil,   nil,  -539,  -539,   nil,  -539,   nil,
  -539,   nil,  -539,   nil,  -539,  -539,  -539,  -539,  -539,  -539,
  -539,  -540,  -539,  -539,  -539,   nil,   nil,   nil,  -540,  -540,
  -540,   nil,   nil,  -540,  -540,  -540,   228,  -540,  -539,  -539,
  -539,  -539,   nil,  -539,   nil,  -539,  -540,  -540,  -540,  -540,
   nil,   nil,   242,   243,   nil,   nil,   nil,  -540,  -540,   nil,
  -540,  -540,  -540,  -540,  -540,   nil,   nil,   nil,   225,   nil,
   231,   nil,   227,   226,   223,   224,   nil,   nil,   229,   nil,
   230,   nil,   nil,   nil,   nil,   nil,   nil,  -540,  -540,  -540,
  -540,  -540,  -540,  -540,  -540,  -540,  -540,  -540,  -540,  -540,
  -540,   nil,   nil,  -540,  -540,  -540,   nil,   nil,  -540,   nil,
   nil,  -540,   nil,   nil,  -540,  -540,   nil,  -540,   nil,  -540,
   nil,  -540,   nil,  -540,  -540,  -540,  -540,  -540,  -540,  -540,
   nil,  -540,  -540,  -540,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -540,  -540,  -540,
  -540,   nil,  -540,   nil,  -540,   277,    69,    70,    66,     9,
    52,   nil,   nil,   nil,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,    28,    29,    67,    68,   nil,   nil,
   nil,   nil,   nil,    27,    26,    25,    93,    92,    94,    95,
   nil,   nil,    18,   nil,   nil,   nil,   nil,   nil,     8,    42,
   nil,    10,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   228,   232,   237,   238,   239,   234,   236,   244,   245,   240,
   241,   nil,  -557,  -557,   nil,   nil,   242,   243,   nil,    37,
   nil,   nil,    31,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,    33,   225,   nil,   231,    41,   227,   226,   223,   224,
   235,   233,   229,    19,   230,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   277,    69,    70,    66,     9,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,
    29,    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,
    25,    93,    92,    94,    95,   nil,   nil,    18,   nil,   nil,
   nil,   nil,   nil,     8,    42,   nil,    10,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   228,   232,   237,   238,   239,
   234,   236,   244,   245,   240,   241,   nil,  -557,  -557,   nil,
   nil,   242,   243,   nil,    37,   nil,   nil,    31,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,    33,   225,   nil,   231,
    41,   227,   226,   223,   224,   235,   233,   229,    19,   230,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   277,    69,    70,    66,     9,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,    28,    29,    67,    68,   nil,   nil,
   nil,   nil,   nil,    27,    26,    25,    93,    92,    94,    95,
   nil,   nil,    18,   nil,   nil,   nil,   nil,   nil,     8,    42,
   nil,    10,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   228,  -557,  -557,  -557,  -557,   234,   236,   nil,   nil,  -557,
  -557,   nil,   nil,   nil,   nil,   nil,   242,   243,   nil,    37,
   nil,   nil,    31,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,    33,   225,   nil,   231,    41,   227,   226,   223,   224,
   235,   233,   229,    19,   230,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   277,    69,    70,    66,     9,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,
    29,    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,
    25,    93,    92,    94,    95,   nil,   nil,    18,   nil,   nil,
   nil,   nil,   nil,     8,    42,   nil,    10,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   228,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   242,   243,   nil,    37,   nil,   nil,    31,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,    33,   225,   nil,   231,
    41,   227,   226,   223,   224,   nil,   nil,   229,    19,   230,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   277,    69,    70,    66,     9,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,    28,    29,    67,    68,   nil,   nil,
   nil,   nil,   nil,    27,    26,    25,    93,    92,    94,    95,
   nil,   nil,    18,   nil,   nil,   nil,   nil,   nil,     8,    42,
   nil,    10,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   228,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   242,   243,   nil,    37,
   nil,   nil,    31,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,    33,   225,   nil,   231,    41,   227,   226,   223,   224,
   nil,   nil,   229,    19,   230,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   277,    69,    70,    66,     9,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,
    29,    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,
    25,    93,    92,    94,    95,   nil,   nil,    18,   nil,   nil,
   nil,   nil,   nil,     8,    42,   nil,    10,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   228,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   242,   243,   nil,    37,   nil,   nil,    31,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,    33,   225,   nil,   231,
    41,   227,   226,   223,   224,   nil,   nil,   229,    19,   230,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   277,    69,    70,    66,     9,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,    28,    29,    67,    68,   nil,   nil,
   nil,   nil,   nil,    27,    26,    25,    93,    92,    94,    95,
   nil,   nil,    18,   nil,   nil,   nil,   nil,   nil,     8,    42,
   nil,    10,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   228,  -557,  -557,  -557,  -557,   234,   236,   nil,   nil,  -557,
  -557,   nil,   nil,   nil,   nil,   nil,   242,   243,   nil,    37,
   nil,   nil,    31,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,    33,   225,   nil,   231,    41,   227,   226,   223,   224,
   235,   233,   229,    19,   230,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   277,    69,    70,    66,     9,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,
    29,    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,
    25,    93,    92,    94,    95,   nil,   nil,    18,   nil,   nil,
   nil,   nil,   nil,     8,    42,   nil,    10,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   228,  -557,  -557,  -557,  -557,
   234,   236,   nil,   nil,  -557,  -557,   nil,   nil,   nil,   nil,
   nil,   242,   243,   nil,    37,   nil,   nil,    31,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,    33,   225,   nil,   231,
    41,   227,   226,   223,   224,   235,   233,   229,    19,   230,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   277,    69,    70,    66,     9,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,    28,    29,    67,    68,   nil,   nil,
   nil,   nil,   nil,    27,    26,    25,    93,    92,    94,    95,
   nil,   nil,    18,   nil,   nil,   nil,   nil,   nil,     8,    42,
   nil,    10,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   228,  -557,  -557,  -557,  -557,   234,   236,   nil,   nil,  -557,
  -557,   nil,   nil,   nil,   nil,   nil,   242,   243,   nil,    37,
   nil,   nil,    31,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,    33,   225,   nil,   231,    41,   227,   226,   223,   224,
   235,   233,   229,    19,   230,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   277,    69,    70,    66,     9,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,
    29,    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,
    25,    93,    92,    94,    95,   nil,   nil,    18,   nil,   nil,
   nil,   nil,   nil,     8,    42,   nil,    10,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   228,  -557,  -557,  -557,  -557,
   234,   236,   nil,   nil,  -557,  -557,   nil,   nil,   nil,   nil,
   nil,   242,   243,   nil,    37,   nil,   nil,    31,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,    33,   225,   nil,   231,
    41,   227,   226,   223,   224,   235,   233,   229,    19,   230,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   277,    69,    70,    66,     9,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,    28,    29,    67,    68,   nil,   nil,
   nil,   nil,   nil,    27,    26,    25,    93,    92,    94,    95,
   nil,   nil,    18,   nil,   nil,   nil,   nil,   nil,     8,    42,
   nil,    10,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   228,  -557,  -557,  -557,  -557,   234,   236,   nil,   nil,  -557,
  -557,   nil,   nil,   nil,   nil,   nil,   242,   243,   nil,    37,
   nil,   nil,    31,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,    33,   225,   nil,   231,    41,   227,   226,   223,   224,
   235,   233,   229,    19,   230,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   277,    69,    70,    66,     9,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,
    29,    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,
    25,    93,    92,    94,    95,   nil,   nil,    18,   nil,   nil,
   nil,   nil,   nil,     8,    42,   nil,    10,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   228,   232,   237,   238,   239,
   234,   236,   nil,   nil,   240,   241,   nil,   nil,   nil,   nil,
   nil,   242,   243,   nil,    37,   nil,   nil,    31,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,    33,   225,   nil,   231,
    41,   227,   226,   223,   224,   235,   233,   229,    19,   230,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   277,    69,    70,    66,     9,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,    28,    29,    67,    68,   nil,   nil,
   nil,   nil,   nil,    27,    26,    25,    93,    92,    94,    95,
   nil,   nil,    18,   nil,   nil,   nil,   nil,   nil,     8,    42,
   nil,    10,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   228,   232,   237,   238,   239,   234,   236,   244,   nil,   240,
   241,   nil,   nil,   nil,   nil,   nil,   242,   243,   nil,    37,
   nil,   nil,    31,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,    33,   225,   nil,   231,    41,   227,   226,   223,   224,
   235,   233,   229,    19,   230,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   277,    69,    70,    66,     9,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,
    29,    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,
    25,    93,    92,    94,    95,   nil,   nil,    18,   nil,   nil,
   nil,   nil,   nil,     8,    42,   nil,    10,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   228,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   242,   243,   nil,    37,   nil,   nil,    31,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,    33,   225,   nil,   231,
    41,   227,   226,   223,   224,   nil,   nil,   nil,    19,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   277,    69,    70,    66,     9,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,    28,    29,    67,    68,   nil,   nil,
   nil,   nil,   nil,    27,    26,    25,    93,    92,    94,    95,
   nil,   nil,    18,   nil,   nil,   nil,   nil,   nil,     8,    42,
   nil,    10,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   228,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   242,   243,   nil,    37,
   nil,   nil,    31,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,    33,   225,   nil,   nil,    41,   227,   226,   223,   224,
   nil,   nil,   nil,    19,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   277,    69,    70,    66,     9,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,
    29,    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,
    25,    93,    92,    94,    95,   nil,   nil,    18,   nil,   nil,
   nil,   nil,   nil,     8,    42,   nil,    10,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    37,   nil,   nil,    31,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,    33,   nil,   nil,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    19,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   277,    69,    70,    66,     9,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,    28,    29,    67,    68,   nil,   nil,
   nil,   nil,   nil,    27,    26,    25,    93,    92,    94,    95,
   nil,   nil,    18,   nil,   nil,   nil,   nil,   nil,     8,    42,
   nil,    10,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    37,
   nil,   nil,    31,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,    33,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    19,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   277,    69,    70,    66,     9,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,
    29,    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,
    25,    93,    92,    94,    95,   nil,   nil,    18,   nil,   nil,
   nil,   nil,   nil,     8,    42,   nil,    10,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    37,   nil,   nil,    31,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,    33,   nil,   nil,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    19,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   277,    69,    70,    66,     9,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,    28,    29,    67,    68,   nil,   nil,
   nil,   nil,   nil,    27,    26,    25,    93,    92,    94,    95,
   nil,   nil,    18,   nil,   nil,   nil,   nil,   nil,     8,    42,
   nil,    10,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    37,
   nil,   nil,    31,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,    33,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    19,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   277,    69,    70,    66,     9,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,
    29,    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,
    25,    93,    92,    94,    95,   nil,   nil,    18,   nil,   nil,
   nil,   nil,   nil,     8,    42,   nil,    10,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    37,   nil,   nil,    31,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,    33,   nil,   nil,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    19,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   277,    69,    70,    66,     9,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,    28,    29,    67,    68,   nil,   nil,
   nil,   nil,   nil,    27,    26,    25,    93,    92,    94,    95,
   nil,   nil,    18,   nil,   nil,   nil,   nil,   nil,     8,    42,
   nil,    10,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    37,
   nil,   nil,    31,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,    33,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    19,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   277,    69,    70,    66,     9,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,
    29,    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,
    25,    93,    92,    94,    95,   nil,   nil,    18,   nil,   nil,
   nil,   nil,   nil,     8,    42,   nil,    10,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    37,   nil,   nil,    31,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,    33,   nil,   nil,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    19,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,     9,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,    28,    29,    67,    68,   nil,   nil,
   nil,   nil,   nil,    27,    26,    25,    93,    92,    94,    95,
   nil,   nil,    18,   nil,   nil,   nil,   nil,   nil,     8,    42,
     7,    10,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    37,
   nil,   nil,    31,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,    33,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    19,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,
    29,    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,
    25,    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,
   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,    28,    29,    67,    68,   nil,   nil,
   nil,   nil,   nil,    27,    26,    25,    93,    92,    94,    95,
   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,
   nil,   nil,    97,    96,    98,    87,    51,    89,    88,    90,
   272,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,
   nil,   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,   270,   nil,   268,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,
    29,    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,
    25,    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,
   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   272,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   270,   nil,   268,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,    28,    29,    67,    68,   nil,   nil,
   nil,   nil,   nil,    27,    26,    25,    93,    92,    94,    95,
   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,
   nil,   nil,    97,    96,    98,    87,    51,    89,    88,    90,
   272,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,
   nil,   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,   270,   nil,   268,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,
   294,    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,
   296,    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,
   nil,   nil,   nil,   nil,   291,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,   nil,   650,   297,   647,   646,   645,   651,   648,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   287,   nil,   nil,   284,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   283,   nil,   nil,   nil,
   653,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   657,
   656,   660,   659,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,   293,   294,    67,    68,   nil,   nil,
   nil,   nil,   nil,   289,   290,   296,    93,    92,    94,    95,
   nil,   nil,   220,   nil,   nil,   nil,   nil,   562,   nil,   291,
   nil,   nil,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,   nil,   nil,   297,
   228,   232,   237,   238,   239,   234,   236,   244,   245,   240,
   241,   nil,   221,   222,   nil,   nil,   242,   243,   nil,   287,
   nil,   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,   nil,   225,   nil,   231,   nil,   227,   226,   223,   224,
   235,   233,   229,   nil,   230,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,   246,   nil,   299,   nil,   nil,    57,   nil,    78,    69,
    70,    66,   nil,    52,   nil,   nil,   nil,    58,    59,   nil,
   nil,   nil,    62,   nil,    60,    61,    63,   293,   294,    67,
    68,   nil,   nil,   nil,   nil,   nil,   289,   290,   296,    93,
    92,    94,    95,   nil,   nil,   220,   nil,   nil,   nil,   nil,
   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,    51,
    89,    88,    90,   nil,    91,    99,   100,   nil,    85,    86,
    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,    54,
   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,   nil,
   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,   nil,
   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,    57,
   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,    60,
    61,    63,   293,   294,    67,    68,   nil,   nil,   nil,   nil,
   nil,   289,   290,   296,    93,    92,    94,    95,   nil,   nil,
   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,   nil,
    97,    96,    98,    87,    51,    89,    88,    90,   nil,    91,
    99,   100,   nil,    85,    86,    39,    40,    38,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,   nil,
   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,   nil,
   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   218,   nil,   nil,   nil,   nil,    84,    76,    79,    80,
   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,    69,
    70,    66,   nil,    52,    57,   nil,    78,    58,    59,   nil,
   nil,   nil,    62,   nil,    60,    61,    63,   293,   294,    67,
    68,   nil,   nil,   nil,   nil,   nil,   289,   290,   296,    93,
    92,    94,    95,   nil,   nil,   220,   nil,   nil,   nil,   nil,
   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,    51,
    89,    88,    90,   nil,    91,    99,   100,   nil,    85,    86,
    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,    54,
   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,   nil,
   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,   nil,
   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,    57,
   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,    60,
    61,    63,    28,    29,    67,    68,   nil,   nil,   nil,   nil,
   nil,    27,    26,    25,    93,    92,    94,    95,   nil,   nil,
    18,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,   nil,
    97,    96,    98,    87,    51,    89,    88,    90,   nil,    91,
    99,   100,   nil,    85,    86,    39,    40,    38,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,   nil,
   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,   nil,
   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    19,   nil,   nil,   nil,   nil,    84,    76,    79,    80,
   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,    69,
    70,    66,   nil,    52,    57,   nil,    78,    58,    59,   nil,
   nil,   nil,    62,   nil,    60,    61,    63,   293,   294,    67,
    68,   nil,   nil,   nil,   nil,   nil,   289,   290,   296,    93,
    92,    94,    95,   nil,   nil,   220,   nil,   nil,   nil,   nil,
   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,    51,
    89,    88,    90,   272,    91,    99,   100,   nil,    85,    86,
    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,    54,
   nil,   nil,    55,   nil,   270,   nil,   nil,   nil,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,   nil,
   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,   nil,
   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,    57,
   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,    60,
    61,    63,   293,   294,    67,    68,   nil,   nil,   nil,   nil,
   nil,   289,   290,   296,    93,    92,    94,    95,   nil,   nil,
   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,   nil,
    97,    96,    98,    87,    51,    89,    88,    90,   272,    91,
    99,   100,   nil,    85,    86,    39,    40,    38,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,   nil,
   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,   nil,
   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   218,   nil,   nil,   nil,   nil,    84,    76,    79,    80,
   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,    69,
    70,    66,   nil,    52,    57,   nil,    78,    58,    59,   nil,
   nil,   nil,    62,   nil,    60,    61,    63,    28,    29,    67,
    68,   nil,   nil,   nil,   nil,   nil,    27,    26,    25,    93,
    92,    94,    95,   nil,   nil,    18,   nil,   nil,   nil,   nil,
   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,    51,
    89,    88,    90,   nil,    91,    99,   100,   nil,    85,    86,
    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,    54,
   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    19,   nil,   nil,   nil,
   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,   nil,
   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,    57,
   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,    60,
    61,    63,    28,    29,    67,    68,   nil,   nil,   nil,   nil,
   nil,    27,    26,    25,    93,    92,    94,    95,   nil,   nil,
    18,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,   nil,
    97,    96,    98,    87,    51,    89,    88,    90,   nil,    91,
    99,   100,   nil,    85,    86,    39,    40,    38,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,   nil,
   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,   nil,
   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    19,   nil,   nil,   nil,   nil,    84,    76,    79,    80,
   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,    69,
    70,    66,   nil,    52,    57,   nil,    78,    58,    59,   nil,
   nil,   nil,    62,   nil,    60,    61,    63,    28,    29,    67,
    68,   nil,   nil,   nil,   nil,   nil,    27,    26,    25,    93,
    92,    94,    95,   nil,   nil,    18,   nil,   nil,   nil,   nil,
   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,    51,
    89,    88,    90,   nil,    91,    99,   100,   nil,    85,    86,
    39,    40,    38,   228,   232,   237,   238,   239,   234,   236,
   244,   245,   240,   241,   nil,   221,   222,   nil,   nil,   242,
   243,   nil,   213,   nil,  -233,   219,   nil,   nil,    53,    54,
   nil,   nil,    55,   nil,   nil,   225,   nil,   231,    41,   227,
   226,   223,   224,   235,   233,   229,    19,   230,   nil,   nil,
   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,   nil,
   nil,    77,    83,   105,   246,   nil,  -233,   nil,   104,    57,
   nil,    78,    69,    70,    66,   nil,    52,   nil,   nil,   nil,
    58,    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,
   293,   294,    67,    68,   nil,   nil,   nil,   nil,   nil,   289,
   290,   296,    93,    92,    94,    95,   nil,   nil,   220,   nil,
   nil,   nil,   nil,   nil,   nil,   291,   nil,   nil,    97,    96,
    98,    87,    51,    89,    88,    90,   nil,    91,    99,   100,
   nil,    85,    86,   nil,   650,   297,   647,   646,   645,   651,
   648,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   331,   nil,   nil,    31,   nil,
   nil,    53,    54,   nil,   nil,    55,   nil,    33,   nil,   nil,
   nil,   653,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   657,   656,   660,   659,    84,    76,    79,    80,   nil,    81,
    82,   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,
   nil,    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,
    62,   nil,    60,    61,    63,   293,   294,    67,    68,   nil,
   nil,   nil,   nil,   nil,   289,   290,   296,    93,    92,    94,
    95,   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,
   291,   nil,   nil,    97,    96,    98,   336,    51,    89,    88,
   337,   nil,    91,    99,   100,   nil,    85,    86,   nil,   650,
   297,   647,   646,   645,   651,   648,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   343,   nil,   nil,
   338,   nil,   nil,   219,   nil,   nil,    53,    54,   nil,   nil,
    55,   nil,   nil,   nil,   nil,   nil,   653,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   657,   656,   660,   659,    84,
    76,    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,
    83,   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,
    58,    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,
   293,   294,    67,    68,   nil,   nil,   nil,   nil,   nil,   289,
   290,   296,    93,    92,    94,    95,   nil,   nil,   220,   nil,
   nil,   nil,   nil,   nil,   nil,   291,   nil,   nil,    97,    96,
    98,   336,    51,    89,    88,   337,   nil,    91,    99,   100,
   nil,    85,    86,   nil,   732,   297,   647,   646,   645,   651,
   648,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   338,   nil,   nil,   219,   nil,
   nil,    53,    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,
   nil,   653,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   657,   656,   660,   659,    84,    76,    79,    80,   nil,    81,
    82,   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,
     9,    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,
    62,   nil,    60,    61,    63,    28,    29,    67,    68,   nil,
   nil,   nil,   nil,   nil,    27,    26,    25,    93,    92,    94,
    95,   nil,   nil,    18,   nil,   nil,   nil,   nil,   nil,     8,
    42,     7,    10,    97,    96,    98,    87,    51,    89,    88,
    90,   nil,    91,    99,   100,   nil,    85,    86,    39,    40,
    38,   228,   232,   237,   238,   239,   234,   236,   244,   245,
   240,   241,   nil,   221,   222,   nil,   nil,   242,   243,   nil,
    37,   nil,   nil,    31,   nil,   nil,    53,    54,   nil,   nil,
    55,   nil,    33,   225,   nil,   231,    41,   227,   226,   223,
   224,   235,   233,   229,    19,   230,   nil,   nil,   nil,    84,
    76,    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,
    83,   nil,   246,   nil,   nil,   nil,   368,    57,   nil,    78,
    69,    70,    66,   nil,    52,   nil,   nil,   nil,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,    29,
    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,    25,
    93,    92,    94,    95,   nil,   nil,    18,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,
    51,    89,    88,    90,   nil,    91,    99,   100,   nil,    85,
    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,
    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    19,   nil,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,
    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,
    60,    61,    63,    28,    29,    67,    68,   nil,   nil,   nil,
   nil,   nil,    27,    26,    25,    93,    92,    94,    95,   nil,
   nil,    18,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    97,    96,    98,    87,    51,    89,    88,    90,   nil,
    91,    99,   100,   nil,    85,    86,    39,    40,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,
   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    19,   nil,   nil,   nil,   nil,    84,    76,    79,
    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,
    69,    70,    66,   nil,    52,    57,   nil,    78,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,    29,
    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,    25,
    93,    92,    94,    95,   nil,   nil,    18,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,
    51,    89,    88,    90,   nil,    91,    99,   100,   nil,    85,
    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,
    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    19,   nil,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,
    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,
    60,    61,    63,    28,    29,    67,    68,   nil,   nil,   nil,
   nil,   nil,    27,    26,    25,    93,    92,    94,    95,   nil,
   nil,    18,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    97,    96,    98,    87,    51,    89,    88,    90,   nil,
    91,    99,   100,   nil,    85,    86,    39,    40,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,
   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    19,   nil,   nil,   nil,   nil,    84,    76,    79,
    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,
    69,    70,    66,     9,    52,    57,   nil,    78,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,    29,
    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,    25,
    93,    92,    94,    95,   nil,   nil,    18,   nil,   nil,   nil,
   nil,   nil,     8,    42,   nil,    10,    97,    96,    98,    87,
    51,    89,    88,    90,   nil,    91,    99,   100,   nil,    85,
    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    37,   nil,   nil,    31,   nil,   nil,    53,
    54,   nil,   nil,    55,   nil,    33,   nil,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    19,   nil,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,
    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,
    60,    61,    63,    28,    29,    67,    68,   nil,   nil,   nil,
   nil,   nil,    27,    26,    25,    93,    92,    94,    95,   nil,
   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    97,    96,    98,    87,    51,    89,    88,    90,   nil,
    91,    99,   100,   nil,    85,    86,    39,    40,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,
   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,
   385,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,    79,
    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,
    69,    70,    66,   nil,    52,    57,   nil,    78,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,    29,
    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,    25,
    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,
    51,    89,    88,    90,   nil,    91,    99,   100,   nil,    85,
    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,
    54,   nil,   nil,    55,   nil,   385,   nil,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,
    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,
    60,    61,    63,    28,    29,    67,    68,   nil,   nil,   nil,
   nil,   nil,    27,    26,    25,    93,    92,    94,    95,   nil,
   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    97,    96,    98,    87,    51,    89,    88,    90,   nil,
    91,    99,   100,   nil,    85,    86,    39,    40,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,
   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,    79,
    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,
    69,    70,    66,   nil,    52,    57,   nil,    78,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,    29,
    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,    25,
    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,
    51,    89,    88,    90,   272,    91,    99,   100,   nil,    85,
    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,
    54,   nil,   nil,    55,   nil,   270,   nil,   268,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,
    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,
    60,    61,    63,    28,    29,    67,    68,   nil,   nil,   nil,
   nil,   nil,    27,    26,    25,    93,    92,    94,    95,   nil,
   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    97,    96,    98,    87,    51,    89,    88,    90,   nil,
    91,    99,   100,   nil,    85,    86,    39,    40,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,
   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,    79,
    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,
    69,    70,    66,   nil,    52,    57,   nil,    78,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,    29,
    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,    25,
    93,    92,    94,    95,   nil,   nil,    18,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,
    51,    89,    88,    90,   nil,    91,    99,   100,   nil,    85,
    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,
    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    19,   nil,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,
    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,
    60,    61,    63,    28,    29,    67,    68,   nil,   nil,   nil,
   nil,   nil,    27,    26,    25,    93,    92,    94,    95,   nil,
   nil,    18,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    97,    96,    98,    87,    51,    89,    88,    90,   nil,
    91,    99,   100,   nil,    85,    86,    39,    40,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,
   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    19,   nil,   nil,   nil,   nil,    84,    76,    79,
    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,
    69,    70,    66,   nil,    52,    57,   nil,    78,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,    29,
    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,    25,
    93,    92,    94,    95,   nil,   nil,    18,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,
    51,    89,    88,    90,   nil,    91,    99,   100,   nil,    85,
    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,
    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    19,   nil,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,
    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,
    60,    61,    63,    28,    29,    67,    68,   nil,   nil,   nil,
   nil,   nil,    27,    26,    25,    93,    92,    94,    95,   nil,
   nil,    18,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    97,    96,    98,    87,    51,    89,    88,    90,   nil,
    91,    99,   100,   nil,    85,    86,    39,    40,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,
   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    19,   nil,   nil,   nil,   nil,    84,    76,    79,
    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,   209,
    69,    70,    66,   nil,    52,    57,   nil,    78,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,   294,
    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,   296,
    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,
    51,    89,    88,    90,   nil,    91,    99,   100,   nil,    85,
    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,
    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,
    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,
    60,    61,    63,   293,   294,    67,    68,   nil,   nil,   nil,
   nil,   nil,   289,   290,   296,    93,    92,    94,    95,   nil,
   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    97,    96,    98,    87,    51,    89,    88,    90,   nil,
    91,    99,   100,   nil,    85,    86,    39,    40,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,
   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,    79,
    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,
    69,    70,    66,   nil,    52,    57,   nil,    78,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,   294,
    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,   296,
    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,
    51,    89,    88,    90,   nil,    91,    99,   100,   nil,    85,
    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,
    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,
    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,
    60,    61,    63,   293,   294,    67,    68,   nil,   nil,   nil,
   nil,   nil,   289,   290,   296,    93,    92,    94,    95,   nil,
   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    97,    96,    98,    87,    51,    89,    88,    90,   nil,
    91,    99,   100,   nil,    85,    86,    39,    40,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,
   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,    79,
    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,
    69,    70,    66,   nil,    52,    57,   nil,    78,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,   294,
    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,   296,
    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,
    51,    89,    88,    90,   nil,    91,    99,   100,   nil,    85,
    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,
    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,
    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,
    60,    61,    63,   293,   294,    67,    68,   nil,   nil,   nil,
   nil,   nil,   289,   290,   296,    93,    92,    94,    95,   nil,
   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    97,    96,    98,    87,    51,    89,    88,    90,   nil,
    91,    99,   100,   nil,    85,    86,    39,    40,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,
   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,    79,
    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,
    69,    70,    66,   nil,    52,    57,   nil,    78,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,   294,
    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,   296,
    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,
    51,    89,    88,    90,   nil,    91,    99,   100,   nil,    85,
    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,
    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,
    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,
    60,    61,    63,   293,   294,    67,    68,   nil,   nil,   nil,
   nil,   nil,   289,   290,   296,    93,    92,    94,    95,   nil,
   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    97,    96,    98,    87,    51,    89,    88,    90,   nil,
    91,    99,   100,   nil,    85,    86,    39,    40,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,
   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,    79,
    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,
    69,    70,    66,   nil,    52,    57,   nil,    78,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,   294,
    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,   296,
    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,
    51,    89,    88,    90,   nil,    91,    99,   100,   nil,    85,
    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,
    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,
    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,
    60,    61,    63,   293,   294,    67,    68,   nil,   nil,   nil,
   nil,   nil,   289,   290,   296,    93,    92,    94,    95,   nil,
   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    97,    96,    98,    87,    51,    89,    88,    90,   nil,
    91,    99,   100,   nil,    85,    86,    39,    40,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,
   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,    79,
    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,
    69,    70,    66,   nil,    52,    57,   nil,    78,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,   294,
    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,   296,
    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,
    51,    89,    88,    90,   nil,    91,    99,   100,   nil,    85,
    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,
    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,
    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,
    60,    61,    63,   293,   294,    67,    68,   nil,   nil,   nil,
   nil,   nil,   289,   290,   296,    93,    92,    94,    95,   nil,
   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    97,    96,    98,    87,    51,    89,    88,    90,   nil,
    91,    99,   100,   nil,    85,    86,    39,    40,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,
   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,    79,
    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,
    69,    70,    66,   nil,    52,    57,   nil,    78,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,   294,
    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,   296,
    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,
    51,    89,    88,    90,   nil,    91,    99,   100,   nil,    85,
    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,
    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,
    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,
    60,    61,    63,   293,   294,    67,    68,   nil,   nil,   nil,
   nil,   nil,   289,   290,   296,    93,    92,    94,    95,   nil,
   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    97,    96,    98,    87,    51,    89,    88,    90,   nil,
    91,    99,   100,   nil,    85,    86,    39,    40,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,
   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,    79,
    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,
    69,    70,    66,   nil,    52,    57,   nil,    78,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,   294,
    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,   296,
    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,
    51,    89,    88,    90,   nil,    91,    99,   100,   nil,    85,
    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,
    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,
    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,
    60,    61,    63,   293,   294,    67,    68,   nil,   nil,   nil,
   nil,   nil,   289,   290,   296,    93,    92,    94,    95,   nil,
   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    97,    96,    98,    87,    51,    89,    88,    90,   nil,
    91,    99,   100,   nil,    85,    86,    39,    40,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,
   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,    79,
    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,
    69,    70,    66,   nil,    52,    57,   nil,    78,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,   294,
    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,   296,
    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,
    51,    89,    88,    90,   nil,    91,    99,   100,   nil,    85,
    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,
    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,
    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,
    60,    61,    63,   293,   294,    67,    68,   nil,   nil,   nil,
   nil,   nil,   289,   290,   296,    93,    92,    94,    95,   nil,
   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    97,    96,    98,    87,    51,    89,    88,    90,   nil,
    91,    99,   100,   nil,    85,    86,    39,    40,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,
   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,    79,
    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,
    69,    70,    66,   nil,    52,    57,   nil,    78,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,   294,
    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,   296,
    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,
    51,    89,    88,    90,   nil,    91,    99,   100,   nil,    85,
    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,
    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,
    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,
    60,    61,    63,   293,   294,    67,    68,   nil,   nil,   nil,
   nil,   nil,   289,   290,   296,    93,    92,    94,    95,   nil,
   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    97,    96,    98,    87,    51,    89,    88,    90,   nil,
    91,    99,   100,   nil,    85,    86,    39,    40,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,
   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,    79,
    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,
    69,    70,    66,   nil,    52,    57,   nil,    78,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,   294,
    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,   296,
    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,
    51,    89,    88,    90,   nil,    91,    99,   100,   nil,    85,
    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,
    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,
    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,
    60,    61,    63,   293,   294,    67,    68,   nil,   nil,   nil,
   nil,   nil,   289,   290,   296,    93,    92,    94,    95,   nil,
   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    97,    96,    98,    87,    51,    89,    88,    90,   nil,
    91,    99,   100,   nil,    85,    86,    39,    40,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,
   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,    79,
    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,
    69,    70,    66,   nil,    52,    57,   nil,    78,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,   294,
    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,   296,
    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,
    51,    89,    88,    90,   nil,    91,    99,   100,   nil,    85,
    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,
    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,
    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,
    60,    61,    63,   293,   294,    67,    68,   nil,   nil,   nil,
   nil,   nil,   289,   290,   296,    93,    92,    94,    95,   nil,
   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    97,    96,    98,    87,    51,    89,    88,    90,   nil,
    91,    99,   100,   nil,    85,    86,    39,    40,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,
   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,    79,
    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,
    69,    70,    66,   nil,    52,    57,   nil,    78,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,   294,
    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,   296,
    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,
    51,    89,    88,    90,   nil,    91,    99,   100,   nil,    85,
    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,
    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,
    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,
    60,    61,    63,   293,   294,    67,    68,   nil,   nil,   nil,
   nil,   nil,   289,   290,   296,    93,    92,    94,    95,   nil,
   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    97,    96,    98,    87,    51,    89,    88,    90,   nil,
    91,    99,   100,   nil,    85,    86,    39,    40,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,
   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,    79,
    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,
    69,    70,    66,   nil,    52,    57,   nil,    78,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,   294,
    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,   296,
    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,
    51,    89,    88,    90,   nil,    91,    99,   100,   nil,    85,
    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,
    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,
    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,
    60,    61,    63,    28,    29,    67,    68,   nil,   nil,   nil,
   nil,   nil,    27,    26,    25,    93,    92,    94,    95,   nil,
   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    97,    96,    98,    87,    51,    89,    88,    90,   272,
    91,    99,   100,   nil,    85,    86,    39,    40,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,
   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,
   270,   nil,   268,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,    79,
    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,
    69,    70,    66,   nil,    52,    57,   nil,    78,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,    29,
    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,    25,
    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,
    51,    89,    88,    90,   272,    91,    99,   100,   nil,    85,
    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,   458,
    54,   nil,   nil,    55,   nil,   270,   nil,   268,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,
    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,
    60,    61,    63,    28,    29,    67,    68,   nil,   nil,   nil,
   nil,   nil,    27,    26,    25,    93,    92,    94,    95,   nil,
   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    97,    96,    98,    87,    51,    89,    88,    90,   272,
    91,    99,   100,   nil,    85,    86,    39,    40,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,
   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,
   270,   nil,   268,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,    79,
    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,   209,
    69,    70,    66,   nil,    52,    57,   nil,    78,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,   294,
    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,   296,
    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,
    51,    89,    88,    90,   nil,    91,    99,   100,   nil,    85,
    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,
    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,    52,
    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,
    60,    61,    63,   293,   294,    67,    68,   nil,   nil,   nil,
   nil,   nil,   289,   290,   296,    93,    92,    94,    95,   nil,
   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,    97,    96,    98,    87,    51,    89,    88,    90,   nil,
    91,    99,   100,   nil,    85,    86,    39,    40,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,
   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,    79,
    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,
    69,    70,    66,   nil,    52,    57,   nil,    78,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,   294,
    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,   296,
    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,    87,
    51,    89,    88,    90,   nil,    91,    99,   100,   nil,    85,
    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,    53,
    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,    69,    70,    66,     9,    52,
    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,   nil,
    60,    61,    63,    28,    29,    67,    68,   nil,   nil,   nil,
   nil,   nil,    27,    26,    25,    93,    92,    94,    95,   nil,
   nil,    18,   nil,   nil,   nil,   nil,   nil,     8,    42,   nil,
    10,    97,    96,    98,    87,    51,    89,    88,    90,   nil,
    91,    99,   100,   nil,    85,    86,    39,    40,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    37,   nil,
   nil,    31,   nil,   nil,    53,    54,   nil,   nil,    55,   nil,
    33,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    19,   nil,   nil,   nil,   nil,    84,    76,    79,
    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,   nil,
    69,    70,    66,   nil,    52,    57,   nil,    78,    58,    59,
   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,   294,
    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,   296,
    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,   nil,
   nil,   nil,   nil,   291,   nil,   nil,    97,    96,    98,    87,
    51,    89,    88,    90,   nil,    91,    99,   100,   nil,    85,
    86,   nil,   nil,   297,   228,   232,   237,   238,   239,   234,
   236,   244,   245,   240,   241,   nil,   221,   222,   nil,   nil,
   242,   243,   nil,   287,   nil,   nil,   219,   nil,   nil,    53,
    54,   nil,   nil,    55,   nil,   nil,   225,   nil,   231,   nil,
   227,   226,   223,   224,   235,   233,   229,   nil,   230,   nil,
   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,   nil,
   nil,   nil,    77,    83,   nil,   246,   nil,   479,   nil,   nil,
    57,   nil,    78,    69,    70,    66,   nil,    52,   nil,   nil,
   nil,    58,    59,   nil,   nil,   nil,    62,   nil,    60,    61,
    63,   293,   294,    67,    68,   nil,   nil,   nil,   nil,   nil,
   289,   290,   296,    93,    92,    94,    95,   nil,   nil,   220,
   nil,   nil,   nil,   nil,   nil,   nil,   291,   nil,   nil,    97,
    96,    98,    87,    51,    89,    88,    90,   nil,    91,    99,
   100,   nil,    85,    86,   nil,   732,   297,   647,   646,   645,
   651,   648,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   287,   nil,   nil,   284,
   nil,   nil,    53,    54,   nil,   nil,    55,   nil,   nil,   nil,
   nil,   nil,   653,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   657,   656,   660,   659,    84,    76,    79,    80,   nil,
    81,    82,   nil,   nil,   nil,    77,    83,   nil,    69,    70,
    66,   nil,    52,    57,   nil,    78,    58,    59,   nil,   nil,
   nil,    62,   nil,    60,    61,    63,   293,   294,    67,    68,
   nil,   nil,   nil,   nil,   nil,   289,   290,   296,    93,    92,
    94,    95,   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,
   nil,    42,   nil,   nil,    97,    96,    98,    87,    51,    89,
    88,    90,   nil,    91,    99,   100,   nil,    85,    86,    39,
    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   213,   nil,   nil,   219,   497,   nil,    53,    54,   nil,
   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,
    84,    76,    79,    80,   nil,    81,    82,   nil,   nil,   nil,
    77,    83,   nil,    69,    70,    66,   nil,    52,    57,   nil,
    78,    58,    59,   nil,   nil,   nil,    62,   nil,    60,    61,
    63,    28,    29,    67,    68,   nil,   nil,   nil,   nil,   nil,
    27,    26,    25,    93,    92,    94,    95,   nil,   nil,    18,
   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,
    96,    98,    87,    51,    89,    88,    90,   nil,    91,    99,
   100,   nil,    85,    86,    39,    40,    38,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,
   nil,   nil,    53,    54,   nil,   nil,    55,   nil,   nil,   nil,
   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    19,   nil,   nil,   nil,   nil,    84,    76,    79,    80,   nil,
    81,    82,   nil,   nil,   nil,    77,    83,   nil,    69,    70,
    66,   nil,    52,    57,   nil,    78,    58,    59,   nil,   nil,
   nil,    62,   nil,    60,    61,    63,    28,    29,    67,    68,
   nil,   nil,   nil,   nil,   nil,    27,    26,    25,    93,    92,
    94,    95,   nil,   nil,    18,   nil,   nil,   nil,   nil,   nil,
   nil,    42,   nil,   nil,    97,    96,    98,    87,    51,    89,
    88,    90,   nil,    91,    99,   100,   nil,    85,    86,    39,
    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   213,   nil,   nil,   219,   nil,   nil,    53,    54,   nil,
   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    19,   nil,   nil,   nil,   nil,
    84,    76,    79,    80,   nil,    81,    82,   nil,   nil,   nil,
    77,    83,   nil,    69,    70,    66,   nil,    52,    57,   nil,
    78,    58,    59,   nil,   nil,   nil,    62,   nil,    60,    61,
    63,    28,    29,    67,    68,   nil,   nil,   nil,   nil,   nil,
    27,    26,    25,    93,    92,    94,    95,   nil,   nil,    18,
   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,
    96,    98,    87,    51,    89,    88,    90,   nil,    91,    99,
   100,   nil,    85,    86,    39,    40,    38,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,
   nil,   nil,    53,    54,   nil,   nil,    55,   nil,   nil,   nil,
   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    19,   nil,   nil,   nil,   nil,    84,    76,    79,    80,   nil,
    81,    82,   nil,   nil,   nil,    77,    83,   nil,    69,    70,
    66,   nil,    52,    57,   nil,    78,    58,    59,   nil,   nil,
   nil,    62,   nil,    60,    61,    63,    28,    29,    67,    68,
   nil,   nil,   nil,   nil,   nil,    27,    26,    25,    93,    92,
    94,    95,   nil,   nil,    18,   nil,   nil,   nil,   nil,   nil,
   nil,    42,   nil,   nil,    97,    96,    98,    87,    51,    89,
    88,    90,   nil,    91,    99,   100,   nil,    85,    86,    39,
    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   213,   nil,   nil,   219,   nil,   nil,    53,    54,   nil,
   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    19,   nil,   nil,   nil,   nil,
    84,    76,    79,    80,   nil,    81,    82,   nil,   nil,   nil,
    77,    83,   nil,    69,    70,    66,   nil,    52,    57,   nil,
    78,    58,    59,   nil,   nil,   nil,    62,   nil,    60,    61,
    63,   293,   294,    67,    68,   nil,   nil,   nil,   nil,   nil,
   289,   290,   296,    93,    92,    94,    95,   nil,   nil,   220,
   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,
    96,    98,    87,    51,    89,    88,    90,   nil,    91,    99,
   100,   nil,    85,    86,    39,    40,    38,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,
   nil,   nil,    53,    54,   nil,   nil,    55,   nil,   nil,   nil,
   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   218,   nil,   nil,   nil,   nil,    84,    76,    79,    80,   nil,
    81,    82,   nil,   nil,   nil,    77,    83,   nil,    69,    70,
    66,   nil,    52,    57,   nil,    78,    58,    59,   nil,   nil,
   nil,    62,   nil,    60,    61,    63,    28,    29,    67,    68,
   nil,   nil,   nil,   nil,   nil,    27,    26,    25,    93,    92,
    94,    95,   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,
   nil,    42,   nil,   nil,    97,    96,    98,    87,    51,    89,
    88,    90,   272,    91,    99,   100,   nil,    85,    86,    39,
    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   213,   nil,   nil,   219,   nil,   nil,    53,    54,   nil,
   nil,    55,   nil,   270,   nil,   268,   nil,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,
    84,    76,    79,    80,   nil,    81,    82,   nil,   nil,   nil,
    77,    83,   nil,    69,    70,    66,   nil,    52,    57,   nil,
    78,    58,    59,   nil,   nil,   nil,    62,   nil,    60,    61,
    63,   293,   294,    67,    68,   nil,   nil,   nil,   nil,   nil,
   289,   290,   296,    93,    92,    94,    95,   nil,   nil,   220,
   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,
    96,    98,    87,    51,    89,    88,    90,   nil,    91,    99,
   100,   nil,    85,    86,    39,    40,    38,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,
   nil,   nil,    53,    54,   nil,   nil,    55,   nil,   nil,   nil,
   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   218,   nil,   nil,   nil,   nil,    84,    76,    79,    80,   nil,
    81,    82,   nil,   nil,   nil,    77,    83,   nil,    69,    70,
    66,   nil,    52,    57,   nil,    78,    58,    59,   nil,   nil,
   nil,    62,   nil,    60,    61,    63,   293,   294,    67,    68,
   nil,   nil,   nil,   nil,   nil,   289,   290,   296,    93,    92,
    94,    95,   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,
   nil,    42,   nil,   nil,    97,    96,    98,    87,    51,    89,
    88,    90,   nil,    91,    99,   100,   nil,    85,    86,    39,
    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   213,   nil,   nil,   219,   nil,   nil,    53,    54,   nil,
   nil,    55,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,
    84,    76,    79,    80,   nil,    81,    82,   nil,   nil,   nil,
    77,    83,   nil,    69,    70,    66,   nil,    52,    57,   nil,
    78,    58,    59,   nil,   nil,   nil,    62,   nil,    60,    61,
    63,   293,   294,    67,    68,   nil,   nil,   nil,   nil,   nil,
   289,   290,   296,    93,    92,    94,    95,   nil,   nil,   220,
   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,
    96,    98,    87,    51,    89,    88,    90,   nil,    91,    99,
   100,   nil,    85,    86,    39,    40,    38,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,
   nil,   nil,    53,    54,   nil,   nil,    55,   nil,   nil,   nil,
   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   218,   nil,   nil,   nil,   nil,    84,    76,    79,    80,   nil,
    81,    82,   nil,   nil,   nil,    77,    83,   nil,    69,    70,
    66,   nil,    52,    57,   nil,    78,    58,    59,   nil,   nil,
   nil,    62,   nil,    60,    61,    63,    28,    29,    67,    68,
   nil,   nil,   nil,   nil,   nil,    27,    26,    25,    93,    92,
    94,    95,   nil,   nil,    18,   nil,   nil,   nil,   nil,   nil,
   nil,    42,   nil,   nil,    97,    96,    98,    87,    51,    89,
    88,    90,   272,    91,    99,   100,   nil,    85,    86,    39,
    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   213,   nil,   nil,   219,   nil,   nil,    53,    54,   nil,
   nil,    55,   nil,   nil,   nil,   268,   nil,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    19,   nil,   nil,   nil,   nil,
    84,    76,    79,    80,   nil,    81,    82,   nil,   nil,   nil,
    77,    83,   nil,    69,    70,    66,   nil,    52,    57,   nil,
    78,    58,    59,   nil,   nil,   nil,    62,   nil,    60,    61,
    63,   293,   294,    67,    68,   nil,   nil,   nil,   nil,   nil,
   289,   290,   296,    93,    92,    94,    95,   nil,   nil,   220,
   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,
    96,    98,    87,    51,    89,    88,    90,   272,    91,    99,
   100,   nil,    85,    86,    39,    40,    38,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,
   nil,   nil,    53,    54,   nil,   nil,    55,   nil,   608,   nil,
   268,   nil,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   218,   nil,   nil,   nil,   nil,    84,    76,    79,    80,   nil,
    81,    82,   nil,   nil,   nil,    77,    83,   nil,    69,    70,
    66,   nil,    52,    57,   nil,    78,    58,    59,   nil,   nil,
   nil,    62,   nil,    60,    61,    63,   293,   294,    67,    68,
   nil,   nil,   nil,   nil,   nil,   289,   290,   296,    93,    92,
    94,    95,   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,
   nil,    42,   nil,   nil,    97,    96,    98,    87,    51,    89,
    88,    90,   272,    91,    99,   100,   nil,    85,    86,    39,
    40,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   213,   nil,   nil,   219,   nil,   nil,    53,    54,   nil,
   nil,    55,   nil,   nil,   nil,   268,   nil,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,
    84,    76,    79,    80,   nil,    81,    82,   nil,   nil,   nil,
    77,    83,   nil,    69,    70,    66,   nil,    52,    57,   nil,
    78,    58,    59,   nil,   nil,   nil,    62,   nil,    60,    61,
    63,   293,   294,    67,    68,   nil,   nil,   nil,   nil,   nil,
   289,   290,   296,    93,    92,    94,    95,   nil,   nil,   220,
   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,
    96,    98,    87,    51,    89,    88,    90,   nil,    91,    99,
   100,   nil,    85,    86,    39,    40,    38,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,
   nil,   nil,    53,    54,   nil,   nil,    55,   nil,   nil,   nil,
   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   218,   nil,   nil,   nil,   nil,    84,    76,    79,    80,   nil,
    81,    82,   nil,   nil,   nil,    77,    83,   nil,    69,    70,
    66,     9,    52,    57,   nil,    78,    58,    59,   nil,   nil,
   nil,    62,   nil,    60,    61,    63,    28,    29,    67,    68,
   nil,   nil,   nil,   nil,   nil,    27,    26,    25,    93,    92,
    94,    95,   nil,   nil,    18,   nil,   nil,   nil,   nil,   nil,
     8,    42,   nil,    10,    97,    96,    98,    87,    51,    89,
    88,    90,   nil,    91,    99,   100,   nil,    85,    86,    39,
    40,    38,   228,   232,   237,   238,   239,   234,   236,   244,
   245,   240,   241,   nil,   221,   222,   nil,   nil,   242,   243,
   nil,    37,   nil,   nil,    31,   nil,   nil,    53,    54,   nil,
   nil,    55,   nil,    33,   225,   nil,   231,    41,   227,   226,
   223,   224,   235,   233,   229,    19,   230,   nil,   nil,   nil,
    84,    76,    79,    80,   nil,    81,    82,   nil,   nil,   nil,
    77,    83,   nil,   246,   nil,   nil,   nil,   368,    57,   nil,
    78,    69,    70,    66,   nil,    52,   nil,   nil,   nil,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,
   294,    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,
   296,    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,
   nil,   nil,   nil,   nil,   291,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,   nil,   650,   297,   647,   646,   645,   651,   648,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   287,   nil,   nil,   284,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,
   653,   688,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   657,
   656,   660,   659,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,    28,    29,    67,    68,   nil,   nil,
   nil,   nil,   nil,    27,    26,    25,    93,    92,    94,    95,
   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,
   nil,   nil,    97,    96,    98,    87,    51,    89,    88,    90,
   272,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,
   nil,   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,   270,   nil,   268,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,
   294,    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,
   296,    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,
   nil,   nil,   nil,   nil,   291,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,   nil,   650,   297,   647,   646,   645,   651,   648,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   287,   nil,   nil,   284,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,
   653,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   657,
   656,   660,   659,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,   293,   294,    67,    68,   nil,   nil,
   nil,   nil,   nil,   289,   290,   296,    93,    92,    94,    95,
   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,
   nil,   nil,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,
   nil,   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,
   294,    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,
   296,    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,
   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,   293,   294,    67,    68,   nil,   nil,
   nil,   nil,   nil,   289,   290,   296,    93,    92,    94,    95,
   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,
   nil,   nil,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,
   nil,   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,
    29,    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,
    25,    93,    92,    94,    95,   nil,   nil,    18,   nil,   nil,
   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    19,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,   293,   294,    67,    68,   nil,   nil,
   nil,   nil,   nil,   289,   290,   296,    93,    92,    94,    95,
   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,
   nil,   nil,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,
   nil,   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,   385,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,
   294,    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,
   296,    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,
   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   272,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   608,   nil,   nil,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,   293,   294,    67,    68,   nil,   nil,
   nil,   nil,   nil,   289,   290,   296,    93,    92,    94,    95,
   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,
   nil,   nil,    97,    96,    98,    87,    51,    89,    88,    90,
   272,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,
   nil,   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,
   294,    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,
   296,    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,
   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   270,   nil,   nil,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,    28,    29,    67,    68,   nil,   nil,
   nil,   nil,   nil,    27,    26,    25,    93,    92,    94,    95,
   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,
   nil,   nil,    97,    96,    98,    87,    51,    89,    88,    90,
   272,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,
   nil,   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,   270,   nil,   268,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,
    29,    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,
    25,    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,
   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   272,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   270,   nil,   268,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,    28,    29,    67,    68,   nil,   nil,
   nil,   nil,   nil,    27,    26,    25,    93,    92,    94,    95,
   nil,   nil,    18,   nil,   nil,   nil,   nil,   nil,   nil,    42,
   nil,   nil,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,
   nil,   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    19,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,
   294,    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,
   296,    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,
   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,   293,   294,    67,    68,   nil,   nil,
   nil,   nil,   nil,   289,   290,   296,    93,    92,    94,    95,
   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,
   nil,   nil,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,
   nil,   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,   705,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,
   294,    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,
   296,    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,
   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,    28,    29,    67,    68,   nil,   nil,
   nil,   nil,   nil,    27,    26,    25,    93,    92,    94,    95,
   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,
   nil,   nil,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,
   nil,   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,
    29,    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,
    25,    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,
   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,    28,    29,    67,    68,   nil,   nil,
   nil,   nil,   nil,    27,    26,    25,    93,    92,    94,    95,
   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,
   nil,   nil,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,
   nil,   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,
    29,    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,
    25,    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,
   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,   293,   294,    67,    68,   nil,   nil,
   nil,   nil,   nil,   289,   290,   296,    93,    92,    94,    95,
   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,
   nil,   nil,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,
   nil,   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,
   294,    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,
   296,    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,
   nil,   nil,   nil,   nil,   291,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,   nil,   732,   297,   647,   646,   645,   651,   648,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   287,   nil,   nil,   284,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,
   653,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   657,
   656,   660,   659,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,   293,   294,    67,    68,   nil,   nil,
   nil,   nil,   nil,   289,   290,   296,    93,    92,    94,    95,
   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,   291,
   nil,   nil,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,   nil,   nil,   297,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   287,
   nil,   nil,   284,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,
    29,    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,
    25,    93,    92,    94,    95,   nil,   nil,    18,   nil,   nil,
   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    19,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,   293,   294,    67,    68,   nil,   nil,
   nil,   nil,   nil,   289,   290,   296,    93,    92,    94,    95,
   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,
   nil,   nil,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,
   nil,   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,
    29,    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,
    25,    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,
   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,   293,   294,    67,    68,   nil,   nil,
   nil,   nil,   nil,   289,   290,   296,    93,    92,    94,    95,
   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,
   nil,   nil,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,
   nil,   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,
   294,    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,
   296,    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,
   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,   293,   294,    67,    68,   nil,   nil,
   nil,   nil,   nil,   289,   290,   296,    93,    92,    94,    95,
   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,
   nil,   nil,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,
   nil,   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,
   294,    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,
   296,    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,
   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,   293,   294,    67,    68,   nil,   nil,
   nil,   nil,   nil,   289,   290,   296,    93,    92,    94,    95,
   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,
   nil,   nil,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,
   nil,   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,
   294,    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,
   296,    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,
   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   272,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   270,   nil,   268,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,   293,   294,    67,    68,   nil,   nil,
   nil,   nil,   nil,   289,   290,   296,    93,    92,    94,    95,
   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,
   nil,   nil,    97,    96,    98,    87,    51,    89,    88,    90,
   272,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,
   nil,   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,   270,   nil,   268,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,
   294,    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,
   296,    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,
   nil,   nil,   nil,   nil,   291,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,   nil,   nil,   297,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   831,   nil,   nil,   219,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,   293,   294,    67,    68,   nil,   nil,
   nil,   nil,   nil,   289,   290,   296,    93,    92,    94,    95,
   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,
   nil,   nil,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,
   nil,   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,
    29,    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,
    25,    93,    92,    94,    95,   nil,   nil,    18,   nil,   nil,
   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    19,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,   293,   294,    67,    68,   nil,   nil,
   nil,   nil,   nil,   289,   290,   296,    93,    92,    94,    95,
   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,
   nil,   nil,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,
   nil,   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,   608,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,
   294,    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,
   296,    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,
   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   nil,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,   293,   294,    67,    68,   nil,   nil,
   nil,   nil,   nil,   289,   290,   296,    93,    92,    94,    95,
   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,   291,
   nil,   nil,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,   nil,   nil,   297,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   875,
   nil,   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,   293,
   294,    67,    68,   nil,   nil,   nil,   nil,   nil,   289,   290,
   296,    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,
   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   272,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   213,   nil,   nil,   219,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   608,   nil,   268,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   nil,    69,    70,    66,   nil,
    52,    57,   nil,    78,    58,    59,   nil,   nil,   nil,    62,
   nil,    60,    61,    63,   293,   294,    67,    68,   nil,   nil,
   nil,   nil,   nil,   289,   290,   296,    93,    92,    94,    95,
   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,    42,
   nil,   nil,    97,    96,    98,    87,    51,    89,    88,    90,
   nil,    91,    99,   100,   nil,    85,    86,    39,    40,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,
   nil,   nil,   219,   nil,   nil,    53,    54,   nil,   nil,    55,
   nil,   608,   nil,   268,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,    84,    76,
    79,    80,   nil,    81,    82,   nil,   nil,   nil,    77,    83,
   nil,    69,    70,    66,   nil,    52,    57,   nil,    78,    58,
    59,   nil,   nil,   nil,    62,   nil,    60,    61,    63,    28,
    29,    67,    68,   nil,   nil,   nil,   nil,   nil,    27,    26,
    25,    93,    92,    94,    95,   nil,   nil,   220,   nil,   nil,
   nil,   nil,   nil,   nil,    42,   nil,   nil,    97,    96,    98,
    87,    51,    89,    88,    90,   272,    91,    99,   100,   nil,
    85,    86,    39,    40,    38,   228,   232,   237,   238,   239,
   234,   236,   244,   245,   240,   241,   nil,   221,   222,   nil,
   nil,   242,   243,   nil,   213,   nil,   nil,   219,   nil,   nil,
    53,    54,   nil,   nil,    55,   nil,   270,   225,   268,   231,
    41,   227,   226,   223,   224,   235,   233,   229,   218,   230,
   nil,   nil,   nil,    84,    76,    79,    80,   nil,    81,    82,
   nil,   nil,   nil,    77,    83,   209,   246,  -403,   nil,   nil,
   nil,    57,   nil,    78,  -403,  -403,  -403,   nil,   nil,  -403,
  -403,  -403,   nil,  -403,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,  -403,  -403,  -403,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,  -403,  -403,   nil,  -403,  -403,  -403,  -403,
  -403,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,  -403,  -403,  -403,  -403,  -403,  -403,  -403,
  -403,  -403,  -403,  -403,  -403,  -403,  -403,   nil,   nil,  -403,
  -403,  -403,   nil,   nil,  -403,   nil,   252,  -403,   nil,   nil,
  -403,  -403,   nil,  -403,   nil,  -403,   nil,  -403,   nil,  -403,
  -403,  -403,  -403,  -403,  -403,  -403,  -293,  -403,  -403,  -403,
   nil,   nil,   nil,  -293,  -293,  -293,   nil,   nil,  -293,  -293,
  -293,   nil,  -293,  -403,  -403,   nil,  -403,   nil,  -403,   nil,
   nil,   nil,  -293,  -293,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,  -293,  -293,   nil,  -293,  -293,  -293,  -293,  -293,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,  -293,  -293,  -293,  -293,  -293,  -293,  -293,  -293,
  -293,  -293,  -293,  -293,  -293,  -293,   nil,   nil,  -293,  -293,
  -293,   nil,   nil,  -293,   nil,   261,  -293,   nil,   nil,  -293,
  -293,   nil,  -293,   nil,  -293,   nil,  -293,   nil,  -293,  -293,
  -293,  -293,  -293,  -293,  -293,  -239,  -293,   nil,  -293,   nil,
   nil,   nil,  -239,  -239,  -239,   nil,   nil,  -239,  -239,  -239,
   nil,  -239,  -293,  -293,   nil,  -293,   nil,  -293,   nil,   nil,
  -239,  -239,  -239,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,  -239,  -239,   nil,  -239,  -239,  -239,  -239,  -239,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,  -239,  -239,  -239,  -239,  -239,  -239,  -239,  -239,  -239,
  -239,  -239,  -239,  -239,  -239,   nil,   nil,  -239,  -239,  -239,
   nil,   nil,  -239,   nil,   252,  -239,   nil,   nil,  -239,  -239,
   nil,  -239,   nil,  -239,   nil,  -239,   nil,  -239,  -239,  -239,
  -239,  -239,  -239,  -239,  -239,  -239,  -239,  -239,   nil,   nil,
   nil,  -239,  -239,  -239,   nil,   nil,  -239,  -239,  -239,   nil,
  -239,  -239,  -239,   nil,  -239,   nil,  -239,   nil,   nil,   nil,
  -239,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
  -239,  -239,   nil,  -239,  -239,  -239,  -239,  -239,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -239,
   nil,   nil,   nil,   nil,   nil,   nil,  -239,  -239,  -239,   nil,
   nil,  -239,  -239,  -239,   nil,  -239,   nil,   nil,   nil,   nil,
   nil,  -239,   nil,   nil,   nil,  -239,   nil,   nil,  -239,   nil,
   nil,   nil,   nil,   252,  -239,  -239,  -239,   nil,  -239,  -239,
  -239,  -239,  -239,   nil,   nil,   nil,   nil,   nil,   394,   398,
   nil,   nil,   395,   nil,   nil,   nil,  -239,   nil,   nil,   nil,
   149,   150,   nil,   146,   128,   129,   130,   137,   134,   136,
  -239,   nil,   131,   132,   nil,  -239,  -239,   151,   152,   138,
   139,   nil,   nil,  -239,   nil,   nil,   252,   nil,   252,  -239,
   nil,   nil,   nil,   nil,   143,   142,   nil,   127,   148,   145,
   144,   140,   141,   135,   133,   125,   147,   126,   nil,   nil,
   153,  -239,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,  -239,   nil,   nil,   nil,   nil,
  -239,   164,   175,   165,   188,   161,   181,   171,   170,   191,
   192,   186,   169,   168,   163,   189,   193,   194,   173,   162,
   176,   180,   182,   174,   167,   nil,   nil,   nil,   183,   190,
   185,   184,   177,   187,   172,   160,   179,   178,   nil,   nil,
   nil,   nil,   nil,   159,   166,   157,   158,   154,   155,   156,
   116,   118,   115,   nil,   117,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   149,   150,   nil,   146,   128,   129,   130,   137,
   134,   136,   nil,   nil,   131,   132,   nil,   nil,   nil,   151,
   152,   138,   139,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   143,   142,   nil,   127,
   148,   145,   144,   140,   141,   135,   133,   125,   147,   126,
   nil,   nil,   153,    84,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    83,   164,   175,   165,   188,   161,
   181,   171,   170,   191,   192,   186,   169,   168,   163,   189,
   193,   194,   173,   162,   176,   180,   182,   174,   167,   nil,
   nil,   nil,   183,   190,   185,   184,   177,   187,   172,   160,
   179,   178,   nil,   nil,   nil,   nil,   nil,   159,   166,   157,
   158,   154,   155,   156,   116,   118,   nil,   nil,   117,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   149,   150,   nil,   146,
   128,   129,   130,   137,   134,   136,   nil,   nil,   131,   132,
   nil,   nil,   nil,   151,   152,   138,   139,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   143,   142,   nil,   127,   148,   145,   144,   140,   141,   135,
   133,   125,   147,   126,   nil,   nil,   153,    84,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    83,   164,
   175,   165,   188,   161,   181,   171,   170,   191,   192,   186,
   169,   168,   163,   189,   193,   194,   173,   162,   176,   180,
   182,   174,   167,   nil,   nil,   nil,   183,   190,   185,   184,
   177,   187,   172,   160,   179,   178,   nil,   nil,   nil,   nil,
   nil,   159,   166,   157,   158,   154,   155,   156,   116,   118,
   nil,   nil,   117,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   149,   150,   nil,   146,   128,   129,   130,   137,   134,   136,
   nil,   nil,   131,   132,   nil,   nil,   nil,   151,   152,   138,
   139,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   143,   142,   nil,   127,   148,   145,
   144,   140,   141,   135,   133,   125,   147,   126,   nil,   nil,
   153,    84,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    83,   164,   175,   165,   188,   161,   181,   171,
   170,   191,   192,   186,   169,   168,   163,   189,   193,   194,
   173,   162,   176,   180,   182,   174,   167,   nil,   nil,   nil,
   183,   190,   185,   184,   177,   187,   172,   160,   179,   178,
   nil,   nil,   nil,   nil,   nil,   159,   166,   157,   158,   154,
   155,   156,   116,   118,   nil,   nil,   117,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   149,   150,   nil,   146,   128,   129,
   130,   137,   134,   136,   nil,   nil,   131,   132,   nil,   nil,
   nil,   151,   152,   138,   139,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   143,   142,
   nil,   127,   148,   145,   144,   140,   141,   135,   133,   125,
   147,   126,   nil,   nil,   153,    84,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    83,   164,   175,   165,
   188,   161,   181,   171,   170,   191,   192,   186,   169,   168,
   163,   189,   193,   194,   173,   162,   176,   180,   182,   174,
   167,   nil,   nil,   nil,   183,   190,   185,   353,   352,   354,
   351,   160,   179,   178,   nil,   nil,   nil,   nil,   nil,   159,
   166,   157,   158,   348,   349,   350,   346,   118,    89,    88,
   347,   nil,    91,   nil,   nil,   nil,   nil,   nil,   149,   150,
   nil,   146,   128,   129,   130,   137,   134,   136,   nil,   nil,
   131,   132,   nil,   nil,   nil,   151,   152,   138,   139,   nil,
   nil,   nil,   nil,   nil,   358,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   143,   142,   nil,   127,   148,   145,   144,   140,
   141,   135,   133,   125,   147,   126,   nil,   nil,   153,   164,
   175,   165,   188,   161,   181,   171,   170,   191,   192,   186,
   169,   168,   163,   189,   193,   194,   173,   162,   176,   180,
   182,   174,   167,   nil,   nil,   nil,   183,   190,   185,   184,
   177,   187,   172,   160,   179,   178,   nil,   nil,   nil,   nil,
   nil,   159,   166,   157,   158,   154,   155,   156,   116,   118,
   nil,   nil,   117,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   149,   150,   nil,   146,   128,   129,   130,   137,   134,   136,
   nil,   nil,   131,   132,   nil,   nil,   nil,   151,   152,   138,
   139,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   143,   142,   nil,   127,   148,   145,
   144,   140,   141,   135,   133,   125,   147,   126,   401,   405,
   153,   nil,   400,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   149,   150,   nil,   146,   128,   129,   130,   137,   134,   136,
   nil,   nil,   131,   132,   nil,   nil,   nil,   151,   152,   138,
   139,   nil,   nil,   nil,   nil,   nil,   252,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   143,   142,   nil,   127,   148,   145,
   144,   140,   141,   135,   133,   125,   147,   126,   450,   398,
   153,   nil,   451,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   149,   150,   nil,   146,   128,   129,   130,   137,   134,   136,
   nil,   nil,   131,   132,   nil,   nil,   nil,   151,   152,   138,
   139,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   143,   142,   nil,   127,   148,   145,
   144,   140,   141,   135,   133,   125,   147,   126,   450,   398,
   153,   nil,   451,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   149,   150,   nil,   146,   128,   129,   130,   137,   134,   136,
   nil,   nil,   131,   132,   nil,   nil,   nil,   151,   152,   138,
   139,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   143,   142,   nil,   127,   148,   145,
   144,   140,   141,   135,   133,   125,   147,   126,   579,   398,
   153,   nil,   580,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   149,   150,   nil,   146,   128,   129,   130,   137,   134,   136,
   nil,   nil,   131,   132,   nil,   nil,   nil,   151,   152,   138,
   139,   nil,   nil,   nil,   nil,   nil,   252,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   143,   142,   nil,   127,   148,   145,
   144,   140,   141,   135,   133,   125,   147,   126,   581,   405,
   153,   nil,   582,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   149,   150,   nil,   146,   128,   129,   130,   137,   134,   136,
   nil,   nil,   131,   132,   nil,   nil,   nil,   151,   152,   138,
   139,   nil,   nil,   nil,   nil,   nil,   252,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   143,   142,   nil,   127,   148,   145,
   144,   140,   141,   135,   133,   125,   147,   126,   617,   398,
   153,   nil,   618,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   149,   150,   nil,   146,   128,   129,   130,   137,   134,   136,
   nil,   nil,   131,   132,   nil,   nil,   nil,   151,   152,   138,
   139,   nil,   nil,   nil,   nil,   nil,   252,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   143,   142,   nil,   127,   148,   145,
   144,   140,   141,   135,   133,   125,   147,   126,   620,   405,
   153,   nil,   621,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   149,   150,   nil,   146,   128,   129,   130,   137,   134,   136,
   nil,   nil,   131,   132,   nil,   nil,   nil,   151,   152,   138,
   139,   nil,   nil,   nil,   nil,   nil,   252,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   143,   142,   nil,   127,   148,   145,
   144,   140,   141,   135,   133,   125,   147,   126,   579,   398,
   153,   nil,   580,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   149,   150,   nil,   146,   128,   129,   130,   137,   134,   136,
   nil,   nil,   131,   132,   nil,   nil,   nil,   151,   152,   138,
   139,   nil,   nil,   nil,   nil,   nil,   252,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   143,   142,   nil,   127,   148,   145,
   144,   140,   141,   135,   133,   125,   147,   126,   581,   405,
   153,   nil,   582,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   149,   150,   nil,   146,   128,   129,   130,   137,   134,   136,
   nil,   nil,   131,   132,   nil,   nil,   nil,   151,   152,   138,
   139,   nil,   nil,   nil,   nil,   nil,   252,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   143,   142,   nil,   127,   148,   145,
   144,   140,   141,   135,   133,   125,   147,   126,   673,   398,
   153,   nil,   674,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   149,   150,   nil,   146,   128,   129,   130,   137,   134,   136,
   nil,   nil,   131,   132,   nil,   nil,   nil,   151,   152,   138,
   139,   nil,   nil,   nil,   nil,   nil,   252,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   143,   142,   nil,   127,   148,   145,
   144,   140,   141,   135,   133,   125,   147,   126,   675,   405,
   153,   nil,   676,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   149,   150,   nil,   146,   128,   129,   130,   137,   134,   136,
   nil,   nil,   131,   132,   nil,   nil,   nil,   151,   152,   138,
   139,   nil,   nil,   nil,   nil,   nil,   252,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   143,   142,   nil,   127,   148,   145,
   144,   140,   141,   135,   133,   125,   147,   126,   678,   405,
   153,   nil,   679,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   149,   150,   nil,   146,   128,   129,   130,   137,   134,   136,
   nil,   nil,   131,   132,   nil,   nil,   nil,   151,   152,   138,
   139,   nil,   nil,   nil,   nil,   nil,   252,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   143,   142,   nil,   127,   148,   145,
   144,   140,   141,   135,   133,   125,   147,   126,   450,   398,
   153,   nil,   451,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   149,   150,   nil,   146,   128,   129,   130,   137,   134,   136,
   nil,   nil,   131,   132,   nil,   nil,   nil,   151,   152,   138,
   139,   nil,   nil,   nil,   nil,   nil,   252,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   143,   142,   nil,   127,   148,   145,
   144,   140,   141,   135,   133,   125,   147,   126,   927,   398,
   153,   nil,   928,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   149,   150,   nil,   146,   128,   129,   130,   137,   134,   136,
   nil,   nil,   131,   132,   nil,   nil,   nil,   151,   152,   138,
   139,   nil,   nil,   nil,   nil,   nil,   252,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   143,   142,   nil,   127,   148,   145,
   144,   140,   141,   135,   133,   125,   147,   126,   929,   405,
   153,   nil,   930,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   149,   150,   nil,   146,   128,   129,   130,   137,   134,   136,
   nil,   nil,   131,   132,   nil,   nil,   nil,   151,   152,   138,
   139,   nil,   nil,   nil,   nil,   nil,   252,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   143,   142,   nil,   127,   148,   145,
   144,   140,   141,   135,   133,   125,   147,   126,   949,   405,
   153,   nil,   948,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   149,   150,   nil,   146,   128,   129,   130,   137,   134,   136,
   nil,   nil,   131,   132,   nil,   nil,   nil,   151,   152,   138,
   139,   nil,   nil,   nil,   nil,   nil,   252,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   143,   142,   nil,   127,   148,   145,
   144,   140,   141,   135,   133,   125,   147,   126,   nil,   nil,
   153,   228,   232,   237,   238,   239,   234,   236,   244,   245,
   240,   241,   nil,   221,   222,   nil,   nil,   242,   243,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   231,   nil,   227,   226,   223,
   224,   235,   233,   229,   nil,   230,   nil,   228,   232,   237,
   238,   239,   234,   236,   244,   245,   240,   241,   nil,   221,
   222,   nil,   246,   242,   243,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,
   nil,   231,   nil,   227,   226,   223,   224,   235,   233,   229,
   nil,   230,   nil,   228,   232,   237,   238,   239,   234,   236,
   244,   245,   240,   241,   nil,   221,   222,   nil,   246,   242,
   243,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   231,   nil,   227,
   226,   223,   224,   235,   233,   229,   nil,   230,   nil,   228,
   232,   237,   238,   239,   234,   236,   244,   245,   240,   241,
   nil,   221,   222,   nil,   246,   242,   243,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   225,   nil,   231,   nil,   227,   226,   223,   224,   235,
   233,   229,   nil,   230,   nil,   228,   232,   237,   238,   239,
   234,   236,   244,   245,   240,   241,   nil,   221,   222,   nil,
   246,   242,   243,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   231,
   nil,   227,   226,   223,   224,   235,   233,   229,   nil,   230,
   nil,   228,   232,   237,   238,   239,   234,   236,   244,   245,
   240,   241,   nil,   221,   222,   nil,   246,   242,   243,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   231,   nil,   227,   226,   223,
   224,   235,   233,   229,   nil,   230,   nil,   228,   232,   237,
   238,   239,   234,   236,   244,   245,   240,   241,   nil,   221,
   222,   nil,   246,   242,   243,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,
   nil,   231,   nil,   227,   226,   223,   224,   235,   233,   229,
   nil,   230,   nil,   228,   232,   237,   238,   239,   234,   236,
   244,   245,   240,   241,   nil,   221,   222,   nil,   246,   242,
   243,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   231,   nil,   227,
   226,   223,   224,   235,   233,   229,   nil,   230,   nil,   228,
   232,   237,   238,   239,   234,   236,   244,   245,   240,   241,
   nil,   221,   222,   nil,   246,   242,   243,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   225,   nil,   231,   nil,   227,   226,   223,   224,   235,
   233,   229,   nil,   230,   nil,   228,   232,   237,   238,   239,
   234,   236,   244,   245,   240,   241,   nil,   221,   222,   nil,
   246,   242,   243,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   231,
   nil,   227,   226,   223,   224,   235,   233,   229,   nil,   230,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   246 ]

racc_action_check = [
    87,     0,     0,     0,     0,     0,     0,    87,    87,    87,
     0,     0,    87,    87,    87,     0,    87,     0,     0,     0,
     0,     0,     0,     0,    87,    56,    87,    87,    87,     0,
     0,     0,     0,     0,     0,     0,    87,    87,     0,    87,
    87,    87,    87,    87,     0,     0,     0,     0,     0,     0,
     0,     0,     0,     0,     0,     0,   342,     0,     0,     0,
   322,     0,     0,     0,     0,     0,    87,    87,    87,    87,
    87,    87,    87,    87,    87,    87,    87,    87,    87,    87,
     1,   597,    87,    87,    87,     0,    87,    87,     0,   329,
    87,     0,     0,    87,    87,     0,    87,     0,    87,   364,
    87,     0,    87,    87,    87,    87,    87,    87,    87,     0,
    87,    56,    87,   332,     0,     0,     0,     0,    18,     0,
     0,     7,   524,   597,     0,     0,    87,    87,    87,    87,
    90,    87,     0,    87,     0,    87,   638,    90,    90,    90,
   212,    15,    90,    90,    90,   516,    90,   338,   323,   617,
    10,   338,   671,   448,    90,    18,    90,    90,    90,   673,
   313,   342,   364,   313,    11,   674,    90,    90,   517,    90,
    90,    90,    90,    90,   322,   753,   853,   927,   928,   322,
   788,   342,   536,   536,    15,   951,   342,   448,     3,   212,
    15,   459,    12,     3,   329,   214,    90,    90,    90,    90,
    90,    90,    90,    90,    90,    90,    90,    90,    90,    90,
   412,   412,    90,    90,    90,   617,    90,    90,   332,    13,
    90,   929,   618,    90,    90,   459,    90,   524,    90,    21,
    90,   789,    90,    90,    90,    90,    90,    90,    90,   400,
    90,   638,    90,    35,   214,   516,   400,   400,   400,   460,
   516,   617,   400,   400,   617,   400,    90,    90,    90,    90,
   617,    90,   323,    90,   673,    90,   671,   323,   517,   671,
   674,   671,   536,   517,    24,   400,   400,   536,   400,   400,
   400,   400,   400,   460,    37,   788,   292,   675,   618,   753,
   853,   927,   928,   676,   753,   853,   927,   928,   929,   951,
   412,   858,    42,   858,   951,   400,   400,   400,   400,   400,
   400,   400,   400,   400,   400,   400,   400,   400,   400,   542,
   542,   400,   400,   400,   618,   400,    36,   618,   101,   400,
   418,   789,   400,   618,   360,   929,   789,   400,    24,   400,
   929,   400,   400,   400,   400,   400,   400,   400,   401,   400,
   292,   400,   675,   628,   628,   401,   401,   401,   676,   650,
    24,   401,   401,   579,   401,   400,   400,   594,   400,    36,
   400,    23,   292,   401,   400,    36,   336,   746,    23,   418,
   314,    16,    16,   314,   401,   401,   346,   401,   401,   401,
   401,   401,   675,   346,   195,   360,   360,   360,   676,   732,
   774,   594,   774,   774,   774,   774,   774,   650,   361,   542,
   317,   213,   579,   317,   401,   401,   401,   401,   401,   401,
   401,   401,   401,   401,   401,   401,   401,   401,   336,   746,
   401,   401,   401,   215,   401,   336,   746,   216,   401,   580,
   336,   401,   362,   628,   336,   746,   401,   732,   401,   220,
   401,   401,   401,   401,   401,   401,   401,    74,   401,   401,
   401,   363,   336,   746,    38,    38,    74,   337,   620,   361,
   361,   361,   723,   581,   401,   401,    74,   401,   774,   401,
   581,   581,   581,   401,   347,   581,   581,   581,   580,   581,
   901,   347,   901,   901,   901,   901,   901,   424,   581,   581,
   581,   581,   115,   362,   362,   362,   678,   115,   115,   581,
   581,   348,   581,   581,   581,   581,   581,   251,   348,   337,
   620,   723,   363,   363,   363,   553,   337,   620,   365,   424,
   349,   337,   620,   424,   424,   337,   620,   349,   265,   581,
   581,   581,   581,   581,   581,   581,   581,   581,   581,   581,
   581,   581,   581,   337,   620,   581,   581,   581,   678,   581,
   581,   297,   297,   581,   301,   678,   581,   581,   901,   581,
   678,   581,   266,   581,   678,   581,   581,   581,   581,   581,
   581,   581,   612,   581,   581,   581,   553,   553,   612,   365,
   365,   365,   678,   269,   637,   553,   443,   637,   394,   581,
   581,   581,   581,   582,   581,   395,   581,   301,   581,   566,
   582,   582,   582,   301,   278,   582,   582,   582,   587,   582,
   587,   587,   587,   587,   587,   425,   311,   311,   443,   582,
   582,   582,   443,   443,   443,   443,   321,   321,   280,   582,
   582,   394,   582,   582,   582,   582,   582,   394,   395,   350,
   662,   662,   566,   351,   395,   587,   350,   425,   566,   281,
   351,   425,   425,   275,   587,   587,   587,   587,   275,   582,
   582,   582,   582,   582,   582,   582,   582,   582,   582,   582,
   582,   582,   582,   282,    14,   582,   582,   582,    43,   582,
   582,    14,   287,   582,   290,    43,   582,   582,   587,   582,
    14,   582,   291,   582,    43,   582,   582,   582,   582,   582,
   582,   582,   296,   582,   298,   582,   302,   639,   352,   639,
   639,   639,   639,   639,   303,   352,   353,   306,   354,   582,
   582,   582,   582,   353,   582,   354,   582,   309,   582,    31,
    31,    31,    31,    31,    31,   356,   326,   310,    31,    31,
   315,   326,   356,    31,   639,    31,    31,    31,    31,    31,
    31,    31,   316,   639,   639,   639,   639,    31,    31,    31,
    31,    31,    31,    31,   546,   546,    31,   318,   546,   546,
   546,   383,    31,    31,   327,    31,    31,    31,    31,    31,
    31,    31,    31,    31,   328,    31,    31,    31,   331,    31,
    31,    31,    31,    31,   383,   383,   383,   383,   383,   383,
   383,   383,   383,   383,   383,   211,   383,   383,   285,   535,
   383,   383,   211,    31,   535,   285,    31,   683,   333,    31,
    31,   211,   683,    31,   285,    31,   383,   374,   383,    31,
   383,   383,   383,   383,   383,   383,   383,    31,   383,   941,
   941,   689,    31,    31,    31,    31,   689,    31,    31,   444,
   380,   286,    31,    31,   386,   383,    51,   383,   286,   388,
    31,   392,    31,    51,    51,    51,   402,   286,    51,    51,
    51,   511,    51,   511,   511,   511,   511,   511,   414,   426,
   427,   444,    51,    51,    51,   444,   444,   444,   444,   428,
   429,   455,    51,    51,   461,    51,    51,    51,    51,    51,
   850,   474,   850,   850,   850,   850,   850,   475,   511,   511,
   902,   478,   902,   902,   902,   902,   902,   511,   511,   511,
   511,   480,    51,    51,    51,    51,    51,    51,    51,    51,
    51,    51,    51,    51,    51,    51,   485,   850,    51,    51,
    51,   288,   489,    51,   498,   501,    51,   902,   288,    51,
    51,   513,    51,   518,    51,   519,    51,   288,    51,    51,
    51,    51,    51,    51,    51,   304,    51,   550,    51,   556,
   330,   563,   304,   567,   570,   575,   583,   330,   585,   598,
   600,   304,    51,    51,    51,    51,   330,    51,   605,    51,
    52,    52,    52,    52,    52,    52,   607,   614,   340,    52,
    52,   616,   619,   622,    52,   340,    52,    52,    52,    52,
    52,    52,    52,   623,   340,   626,   627,   629,    52,    52,
    52,    52,    52,    52,    52,   632,   653,    52,   653,   653,
   653,   653,   653,    52,    52,   633,    52,    52,    52,    52,
    52,    52,    52,    52,    52,   641,    52,    52,    52,   642,
    52,    52,    52,    52,    52,   771,   643,   771,   771,   771,
   771,   771,   766,   653,   766,   766,   766,   766,   766,   484,
   651,   527,   653,   658,    52,   661,   484,    52,   527,   664,
    52,    52,   669,   672,    52,   484,    52,   527,   681,   686,
    52,   810,   771,   810,   810,   810,   810,   810,    52,   766,
   704,   771,   725,    52,    52,    52,    52,   726,    52,    52,
   766,   766,   727,    52,    52,   113,   113,   113,   113,   113,
   113,    52,   729,    52,   113,   113,   730,   734,   810,   113,
   739,   113,   113,   113,   113,   113,   113,   113,   740,   810,
   810,   741,   745,   113,   113,   113,   113,   113,   113,   113,
   760,   848,   113,   848,   848,   848,   848,   848,   113,   113,
   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,
   767,   113,   113,   113,   773,   113,   113,   113,   113,   113,
   938,   775,   938,   938,   938,   938,   938,   893,   848,   893,
   893,   893,   893,   893,   829,   776,   621,   848,   779,   113,
   782,   829,   113,   621,   791,   113,   113,   795,   621,   113,
   829,   113,   621,   796,   799,   113,   895,   938,   895,   895,
   895,   895,   895,   113,   893,   800,   812,   813,   113,   113,
   113,   113,   819,   113,   113,   893,   893,   820,   113,   113,
   197,   197,   197,   197,   197,   197,   113,   822,   113,   197,
   197,   826,   831,   895,   197,   836,   197,   197,   197,   197,
   197,   197,   197,   837,   895,   895,   838,   841,   197,   197,
   197,   197,   197,   197,   197,   842,   940,   197,   940,   940,
   940,   940,   940,   197,   197,   844,   197,   197,   197,   197,
   197,   197,   197,   197,   197,   847,   197,   197,   197,   849,
   197,   197,   197,   197,   197,   957,   855,   957,   957,   957,
   957,   957,   897,   940,   897,   897,   897,   897,   897,   830,
   856,   679,   832,   861,   197,   866,   830,   197,   679,   832,
   197,   197,   867,   679,   197,   830,   197,   679,   832,   868,
   197,   916,   957,   916,   916,   916,   916,   916,   197,   897,
   869,   871,   875,   197,   197,   197,   197,   882,   197,   197,
   897,   897,   887,   197,   197,   219,   219,   219,   219,   219,
   219,   197,   888,   197,   219,   219,   903,   924,   916,   219,
   926,   219,   219,   219,   219,   219,   219,   219,   933,   916,
   916,   934,   935,   219,   219,   219,   219,   219,   219,   219,
   936,   731,   219,   731,   731,   731,   937,   731,   219,   219,
   939,   219,   219,   219,   219,   219,   219,   219,   219,   219,
   942,   219,   219,   219,   943,   219,   219,   219,   219,   219,
   873,   874,   873,   873,   873,   944,   873,   918,   874,   918,
   918,   918,   918,   918,   886,   945,   930,   874,   946,   219,
   947,   886,   219,   930,   948,   219,   219,   949,   930,   219,
   886,   219,   930,   958,   967,   219,   953,   968,   953,   953,
   953,   953,   953,   219,   918,   969,   nil,   nil,   219,   219,
   219,   219,   nil,   219,   219,   918,   918,   nil,   219,   219,
   279,   279,   279,   279,   279,   279,   219,   nil,   219,   279,
   279,   nil,   nil,   953,   279,   nil,   279,   279,   279,   279,
   279,   279,   279,   nil,   953,   953,   nil,   nil,   279,   279,
   279,   279,   279,   279,   279,   nil,   nil,   279,     6,     6,
     6,     6,     6,   279,   279,   nil,   279,   279,   279,   279,
   279,   279,   279,   279,   279,   nil,   279,   279,   279,   nil,
   279,   279,   279,   279,   279,   430,   276,   276,   276,   276,
   276,   nil,   963,   nil,   963,   963,   963,   963,   963,   nil,
   nil,   430,   430,   nil,   279,   nil,   nil,   279,   nil,   nil,
   279,   279,   nil,   nil,   279,   nil,   279,   430,   nil,   430,
   279,   430,   430,   430,   430,   nil,   nil,   nil,   279,   963,
   nil,   nil,   nil,   279,   279,   279,   279,   nil,   279,   279,
   963,   963,   nil,   279,   279,   284,   284,   284,   284,   284,
   284,   279,   nil,   279,   284,   284,   nil,   nil,   nil,   284,
   nil,   284,   284,   284,   284,   284,   284,   284,   473,   473,
   473,   473,   473,   284,   284,   284,   284,   284,   284,   284,
   nil,   nil,   284,   nil,   nil,   nil,   nil,   391,   284,   284,
   nil,   284,   284,   284,   284,   284,   284,   284,   284,   284,
   nil,   284,   284,   284,   nil,   284,   284,   284,   284,   284,
   391,   391,   391,   391,   391,   391,   391,   391,   391,   391,
   391,   nil,   391,   391,   nil,   nil,   391,   391,   nil,   284,
   nil,   nil,   284,   nil,   nil,   284,   284,   nil,   nil,   284,
   nil,   284,   391,   nil,   391,   284,   391,   391,   391,   391,
   391,   391,   391,   284,   391,   nil,   nil,   nil,   284,   284,
   284,   284,   nil,   284,   284,   nil,   nil,   nil,   284,   284,
   nil,   391,   405,   nil,   nil,   nil,   284,   nil,   284,   405,
   405,   405,   nil,   nil,   405,   405,   405,   538,   405,   538,
   538,   538,   538,   538,   nil,   nil,   nil,   405,   405,   405,
   405,   611,   611,   611,   611,   611,   nil,   nil,   405,   405,
   nil,   405,   405,   405,   405,   405,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   538,   538,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   538,   538,   538,   538,   nil,   405,   405,
   405,   405,   405,   405,   405,   405,   405,   405,   405,   405,
   405,   405,   nil,   nil,   405,   405,   405,   nil,   nil,   405,
   nil,   nil,   405,   nil,   nil,   405,   405,   nil,   405,   nil,
   405,   nil,   405,   nil,   405,   405,   405,   405,   405,   405,
   405,   406,   405,   405,   405,   nil,   nil,   nil,   406,   406,
   406,   nil,   nil,   406,   406,   406,   434,   406,   405,   405,
   405,   405,   nil,   405,   nil,   405,   406,   406,   406,   406,
   nil,   nil,   434,   434,   nil,   nil,   nil,   406,   406,   nil,
   406,   406,   406,   406,   406,   nil,   nil,   nil,   434,   nil,
   434,   nil,   434,   434,   434,   434,   nil,   nil,   434,   nil,
   434,   nil,   nil,   nil,   nil,   nil,   nil,   406,   406,   406,
   406,   406,   406,   406,   406,   406,   406,   406,   406,   406,
   406,   nil,   nil,   406,   406,   406,   nil,   nil,   406,   nil,
   nil,   406,   nil,   nil,   406,   406,   nil,   406,   nil,   406,
   nil,   406,   nil,   406,   406,   406,   406,   406,   406,   406,
   nil,   406,   406,   406,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   406,   406,   406,
   406,   nil,   406,   nil,   406,   512,   512,   512,   512,   512,
   512,   nil,   nil,   nil,   512,   512,   nil,   nil,   nil,   512,
   nil,   512,   512,   512,   512,   512,   512,   512,   nil,   nil,
   nil,   nil,   nil,   512,   512,   512,   512,   512,   512,   512,
   nil,   nil,   512,   nil,   nil,   nil,   nil,   nil,   512,   512,
   nil,   512,   512,   512,   512,   512,   512,   512,   512,   512,
   nil,   512,   512,   512,   nil,   512,   512,   512,   512,   512,
   422,   422,   422,   422,   422,   422,   422,   422,   422,   422,
   422,   nil,   422,   422,   nil,   nil,   422,   422,   nil,   512,
   nil,   nil,   512,   nil,   nil,   512,   512,   nil,   nil,   512,
   nil,   512,   422,   nil,   422,   512,   422,   422,   422,   422,
   422,   422,   422,   512,   422,   nil,   nil,   nil,   512,   512,
   512,   512,   nil,   512,   512,   nil,   nil,   nil,   512,   512,
   515,   515,   515,   515,   515,   515,   512,   nil,   512,   515,
   515,   nil,   nil,   nil,   515,   nil,   515,   515,   515,   515,
   515,   515,   515,   nil,   nil,   nil,   nil,   nil,   515,   515,
   515,   515,   515,   515,   515,   nil,   nil,   515,   nil,   nil,
   nil,   nil,   nil,   515,   515,   nil,   515,   515,   515,   515,
   515,   515,   515,   515,   515,   nil,   515,   515,   515,   nil,
   515,   515,   515,   515,   515,   423,   423,   423,   423,   423,
   423,   423,   423,   423,   423,   423,   nil,   423,   423,   nil,
   nil,   423,   423,   nil,   515,   nil,   nil,   515,   nil,   nil,
   515,   515,   nil,   nil,   515,   nil,   515,   423,   nil,   423,
   515,   423,   423,   423,   423,   423,   423,   423,   515,   423,
   nil,   nil,   nil,   515,   515,   515,   515,   nil,   515,   515,
   nil,   nil,   nil,   515,   515,   537,   537,   537,   537,   537,
   537,   515,   nil,   515,   537,   537,   nil,   nil,   nil,   537,
   nil,   537,   537,   537,   537,   537,   537,   537,   nil,   nil,
   nil,   nil,   nil,   537,   537,   537,   537,   537,   537,   537,
   nil,   nil,   537,   nil,   nil,   nil,   nil,   nil,   537,   537,
   nil,   537,   537,   537,   537,   537,   537,   537,   537,   537,
   nil,   537,   537,   537,   nil,   537,   537,   537,   537,   537,
   433,   433,   433,   433,   433,   433,   433,   nil,   nil,   433,
   433,   nil,   nil,   nil,   nil,   nil,   433,   433,   nil,   537,
   nil,   nil,   537,   nil,   nil,   537,   537,   nil,   nil,   537,
   nil,   537,   433,   nil,   433,   537,   433,   433,   433,   433,
   433,   433,   433,   537,   433,   nil,   nil,   nil,   537,   537,
   537,   537,   nil,   537,   537,   nil,   nil,   nil,   537,   537,
   589,   589,   589,   589,   589,   589,   537,   nil,   537,   589,
   589,   nil,   nil,   nil,   589,   nil,   589,   589,   589,   589,
   589,   589,   589,   nil,   nil,   nil,   nil,   nil,   589,   589,
   589,   589,   589,   589,   589,   nil,   nil,   589,   nil,   nil,
   nil,   nil,   nil,   589,   589,   nil,   589,   589,   589,   589,
   589,   589,   589,   589,   589,   nil,   589,   589,   589,   nil,
   589,   589,   589,   589,   589,   435,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   435,   435,   nil,   589,   nil,   nil,   589,   nil,   nil,
   589,   589,   nil,   nil,   589,   nil,   589,   435,   nil,   435,
   589,   435,   435,   435,   435,   nil,   nil,   435,   589,   435,
   nil,   nil,   nil,   589,   589,   589,   589,   nil,   589,   589,
   nil,   nil,   nil,   589,   589,   602,   602,   602,   602,   602,
   602,   589,   nil,   589,   602,   602,   nil,   nil,   nil,   602,
   nil,   602,   602,   602,   602,   602,   602,   602,   nil,   nil,
   nil,   nil,   nil,   602,   602,   602,   602,   602,   602,   602,
   nil,   nil,   602,   nil,   nil,   nil,   nil,   nil,   602,   602,
   nil,   602,   602,   602,   602,   602,   602,   602,   602,   602,
   nil,   602,   602,   602,   nil,   602,   602,   602,   602,   602,
   436,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   436,   436,   nil,   602,
   nil,   nil,   602,   nil,   nil,   602,   602,   nil,   nil,   602,
   nil,   602,   436,   nil,   436,   602,   436,   436,   436,   436,
   nil,   nil,   436,   602,   436,   nil,   nil,   nil,   602,   602,
   602,   602,   nil,   602,   602,   nil,   nil,   nil,   602,   602,
   603,   603,   603,   603,   603,   603,   602,   nil,   602,   603,
   603,   nil,   nil,   nil,   603,   nil,   603,   603,   603,   603,
   603,   603,   603,   nil,   nil,   nil,   nil,   nil,   603,   603,
   603,   603,   603,   603,   603,   nil,   nil,   603,   nil,   nil,
   nil,   nil,   nil,   603,   603,   nil,   603,   603,   603,   603,
   603,   603,   603,   603,   603,   nil,   603,   603,   603,   nil,
   603,   603,   603,   603,   603,   437,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   437,   437,   nil,   603,   nil,   nil,   603,   nil,   nil,
   603,   603,   nil,   nil,   603,   nil,   603,   437,   nil,   437,
   603,   437,   437,   437,   437,   nil,   nil,   437,   603,   437,
   nil,   nil,   nil,   603,   603,   603,   603,   nil,   603,   603,
   nil,   nil,   nil,   603,   603,   631,   631,   631,   631,   631,
   631,   603,   nil,   603,   631,   631,   nil,   nil,   nil,   631,
   nil,   631,   631,   631,   631,   631,   631,   631,   nil,   nil,
   nil,   nil,   nil,   631,   631,   631,   631,   631,   631,   631,
   nil,   nil,   631,   nil,   nil,   nil,   nil,   nil,   631,   631,
   nil,   631,   631,   631,   631,   631,   631,   631,   631,   631,
   nil,   631,   631,   631,   nil,   631,   631,   631,   631,   631,
   438,   438,   438,   438,   438,   438,   438,   nil,   nil,   438,
   438,   nil,   nil,   nil,   nil,   nil,   438,   438,   nil,   631,
   nil,   nil,   631,   nil,   nil,   631,   631,   nil,   nil,   631,
   nil,   631,   438,   nil,   438,   631,   438,   438,   438,   438,
   438,   438,   438,   631,   438,   nil,   nil,   nil,   631,   631,
   631,   631,   nil,   631,   631,   nil,   nil,   nil,   631,   631,
   682,   682,   682,   682,   682,   682,   631,   nil,   631,   682,
   682,   nil,   nil,   nil,   682,   nil,   682,   682,   682,   682,
   682,   682,   682,   nil,   nil,   nil,   nil,   nil,   682,   682,
   682,   682,   682,   682,   682,   nil,   nil,   682,   nil,   nil,
   nil,   nil,   nil,   682,   682,   nil,   682,   682,   682,   682,
   682,   682,   682,   682,   682,   nil,   682,   682,   682,   nil,
   682,   682,   682,   682,   682,   439,   439,   439,   439,   439,
   439,   439,   nil,   nil,   439,   439,   nil,   nil,   nil,   nil,
   nil,   439,   439,   nil,   682,   nil,   nil,   682,   nil,   nil,
   682,   682,   nil,   nil,   682,   nil,   682,   439,   nil,   439,
   682,   439,   439,   439,   439,   439,   439,   439,   682,   439,
   nil,   nil,   nil,   682,   682,   682,   682,   nil,   682,   682,
   nil,   nil,   nil,   682,   682,   687,   687,   687,   687,   687,
   687,   682,   nil,   682,   687,   687,   nil,   nil,   nil,   687,
   nil,   687,   687,   687,   687,   687,   687,   687,   nil,   nil,
   nil,   nil,   nil,   687,   687,   687,   687,   687,   687,   687,
   nil,   nil,   687,   nil,   nil,   nil,   nil,   nil,   687,   687,
   nil,   687,   687,   687,   687,   687,   687,   687,   687,   687,
   nil,   687,   687,   687,   nil,   687,   687,   687,   687,   687,
   440,   440,   440,   440,   440,   440,   440,   nil,   nil,   440,
   440,   nil,   nil,   nil,   nil,   nil,   440,   440,   nil,   687,
   nil,   nil,   687,   nil,   nil,   687,   687,   nil,   nil,   687,
   nil,   687,   440,   nil,   440,   687,   440,   440,   440,   440,
   440,   440,   440,   687,   440,   nil,   nil,   nil,   687,   687,
   687,   687,   nil,   687,   687,   nil,   nil,   nil,   687,   687,
   697,   697,   697,   697,   697,   697,   687,   nil,   687,   697,
   697,   nil,   nil,   nil,   697,   nil,   697,   697,   697,   697,
   697,   697,   697,   nil,   nil,   nil,   nil,   nil,   697,   697,
   697,   697,   697,   697,   697,   nil,   nil,   697,   nil,   nil,
   nil,   nil,   nil,   697,   697,   nil,   697,   697,   697,   697,
   697,   697,   697,   697,   697,   nil,   697,   697,   697,   nil,
   697,   697,   697,   697,   697,   441,   441,   441,   441,   441,
   441,   441,   nil,   nil,   441,   441,   nil,   nil,   nil,   nil,
   nil,   441,   441,   nil,   697,   nil,   nil,   697,   nil,   nil,
   697,   697,   nil,   nil,   697,   nil,   697,   441,   nil,   441,
   697,   441,   441,   441,   441,   441,   441,   441,   697,   441,
   nil,   nil,   nil,   697,   697,   697,   697,   nil,   697,   697,
   nil,   nil,   nil,   697,   697,   735,   735,   735,   735,   735,
   735,   697,   nil,   697,   735,   735,   nil,   nil,   nil,   735,
   nil,   735,   735,   735,   735,   735,   735,   735,   nil,   nil,
   nil,   nil,   nil,   735,   735,   735,   735,   735,   735,   735,
   nil,   nil,   735,   nil,   nil,   nil,   nil,   nil,   735,   735,
   nil,   735,   735,   735,   735,   735,   735,   735,   735,   735,
   nil,   735,   735,   735,   nil,   735,   735,   735,   735,   735,
   442,   442,   442,   442,   442,   442,   442,   nil,   nil,   442,
   442,   nil,   nil,   nil,   nil,   nil,   442,   442,   nil,   735,
   nil,   nil,   735,   nil,   nil,   735,   735,   nil,   nil,   735,
   nil,   735,   442,   nil,   442,   735,   442,   442,   442,   442,
   442,   442,   442,   735,   442,   nil,   nil,   nil,   735,   735,
   735,   735,   nil,   735,   735,   nil,   nil,   nil,   735,   735,
   750,   750,   750,   750,   750,   750,   735,   nil,   735,   750,
   750,   nil,   nil,   nil,   750,   nil,   750,   750,   750,   750,
   750,   750,   750,   nil,   nil,   nil,   nil,   nil,   750,   750,
   750,   750,   750,   750,   750,   nil,   nil,   750,   nil,   nil,
   nil,   nil,   nil,   750,   750,   nil,   750,   750,   750,   750,
   750,   750,   750,   750,   750,   nil,   750,   750,   750,   nil,
   750,   750,   750,   750,   750,   445,   445,   445,   445,   445,
   445,   445,   nil,   nil,   445,   445,   nil,   nil,   nil,   nil,
   nil,   445,   445,   nil,   750,   nil,   nil,   750,   nil,   nil,
   750,   750,   nil,   nil,   750,   nil,   750,   445,   nil,   445,
   750,   445,   445,   445,   445,   445,   445,   445,   750,   445,
   nil,   nil,   nil,   750,   750,   750,   750,   nil,   750,   750,
   nil,   nil,   nil,   750,   750,   783,   783,   783,   783,   783,
   783,   750,   nil,   750,   783,   783,   nil,   nil,   nil,   783,
   nil,   783,   783,   783,   783,   783,   783,   783,   nil,   nil,
   nil,   nil,   nil,   783,   783,   783,   783,   783,   783,   783,
   nil,   nil,   783,   nil,   nil,   nil,   nil,   nil,   783,   783,
   nil,   783,   783,   783,   783,   783,   783,   783,   783,   783,
   nil,   783,   783,   783,   nil,   783,   783,   783,   783,   783,
   446,   446,   446,   446,   446,   446,   446,   446,   nil,   446,
   446,   nil,   nil,   nil,   nil,   nil,   446,   446,   nil,   783,
   nil,   nil,   783,   nil,   nil,   783,   783,   nil,   nil,   783,
   nil,   783,   446,   nil,   446,   783,   446,   446,   446,   446,
   446,   446,   446,   783,   446,   nil,   nil,   nil,   783,   783,
   783,   783,   nil,   783,   783,   nil,   nil,   nil,   783,   783,
   784,   784,   784,   784,   784,   784,   783,   nil,   783,   784,
   784,   nil,   nil,   nil,   784,   nil,   784,   784,   784,   784,
   784,   784,   784,   nil,   nil,   nil,   nil,   nil,   784,   784,
   784,   784,   784,   784,   784,   nil,   nil,   784,   nil,   nil,
   nil,   nil,   nil,   784,   784,   nil,   784,   784,   784,   784,
   784,   784,   784,   784,   784,   nil,   784,   784,   784,   nil,
   784,   784,   784,   784,   784,   431,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   431,   431,   nil,   784,   nil,   nil,   784,   nil,   nil,
   784,   784,   nil,   nil,   784,   nil,   784,   431,   nil,   431,
   784,   431,   431,   431,   431,   nil,   nil,   nil,   784,   nil,
   nil,   nil,   nil,   784,   784,   784,   784,   nil,   784,   784,
   nil,   nil,   nil,   784,   784,   787,   787,   787,   787,   787,
   787,   784,   nil,   784,   787,   787,   nil,   nil,   nil,   787,
   nil,   787,   787,   787,   787,   787,   787,   787,   nil,   nil,
   nil,   nil,   nil,   787,   787,   787,   787,   787,   787,   787,
   nil,   nil,   787,   nil,   nil,   nil,   nil,   nil,   787,   787,
   nil,   787,   787,   787,   787,   787,   787,   787,   787,   787,
   nil,   787,   787,   787,   nil,   787,   787,   787,   787,   787,
   432,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   432,   432,   nil,   787,
   nil,   nil,   787,   nil,   nil,   787,   787,   nil,   nil,   787,
   nil,   787,   432,   nil,   nil,   787,   432,   432,   432,   432,
   nil,   nil,   nil,   787,   nil,   nil,   nil,   nil,   787,   787,
   787,   787,   nil,   787,   787,   nil,   nil,   nil,   787,   787,
   793,   793,   793,   793,   793,   793,   787,   nil,   787,   793,
   793,   nil,   nil,   nil,   793,   nil,   793,   793,   793,   793,
   793,   793,   793,   nil,   nil,   nil,   nil,   nil,   793,   793,
   793,   793,   793,   793,   793,   nil,   nil,   793,   nil,   nil,
   nil,   nil,   nil,   793,   793,   nil,   793,   793,   793,   793,
   793,   793,   793,   793,   793,   nil,   793,   793,   793,   nil,
   793,   793,   793,   793,   793,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   793,   nil,   nil,   793,   nil,   nil,
   793,   793,   nil,   nil,   793,   nil,   793,   nil,   nil,   nil,
   793,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   793,   nil,
   nil,   nil,   nil,   793,   793,   793,   793,   nil,   793,   793,
   nil,   nil,   nil,   793,   793,   828,   828,   828,   828,   828,
   828,   793,   nil,   793,   828,   828,   nil,   nil,   nil,   828,
   nil,   828,   828,   828,   828,   828,   828,   828,   nil,   nil,
   nil,   nil,   nil,   828,   828,   828,   828,   828,   828,   828,
   nil,   nil,   828,   nil,   nil,   nil,   nil,   nil,   828,   828,
   nil,   828,   828,   828,   828,   828,   828,   828,   828,   828,
   nil,   828,   828,   828,   nil,   828,   828,   828,   828,   828,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   828,
   nil,   nil,   828,   nil,   nil,   828,   828,   nil,   nil,   828,
   nil,   828,   nil,   nil,   nil,   828,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   828,   nil,   nil,   nil,   nil,   828,   828,
   828,   828,   nil,   828,   828,   nil,   nil,   nil,   828,   828,
   834,   834,   834,   834,   834,   834,   828,   nil,   828,   834,
   834,   nil,   nil,   nil,   834,   nil,   834,   834,   834,   834,
   834,   834,   834,   nil,   nil,   nil,   nil,   nil,   834,   834,
   834,   834,   834,   834,   834,   nil,   nil,   834,   nil,   nil,
   nil,   nil,   nil,   834,   834,   nil,   834,   834,   834,   834,
   834,   834,   834,   834,   834,   nil,   834,   834,   834,   nil,
   834,   834,   834,   834,   834,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   834,   nil,   nil,   834,   nil,   nil,
   834,   834,   nil,   nil,   834,   nil,   834,   nil,   nil,   nil,
   834,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   834,   nil,
   nil,   nil,   nil,   834,   834,   834,   834,   nil,   834,   834,
   nil,   nil,   nil,   834,   834,   835,   835,   835,   835,   835,
   835,   834,   nil,   834,   835,   835,   nil,   nil,   nil,   835,
   nil,   835,   835,   835,   835,   835,   835,   835,   nil,   nil,
   nil,   nil,   nil,   835,   835,   835,   835,   835,   835,   835,
   nil,   nil,   835,   nil,   nil,   nil,   nil,   nil,   835,   835,
   nil,   835,   835,   835,   835,   835,   835,   835,   835,   835,
   nil,   835,   835,   835,   nil,   835,   835,   835,   835,   835,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   835,
   nil,   nil,   835,   nil,   nil,   835,   835,   nil,   nil,   835,
   nil,   835,   nil,   nil,   nil,   835,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   835,   nil,   nil,   nil,   nil,   835,   835,
   835,   835,   nil,   835,   835,   nil,   nil,   nil,   835,   835,
   904,   904,   904,   904,   904,   904,   835,   nil,   835,   904,
   904,   nil,   nil,   nil,   904,   nil,   904,   904,   904,   904,
   904,   904,   904,   nil,   nil,   nil,   nil,   nil,   904,   904,
   904,   904,   904,   904,   904,   nil,   nil,   904,   nil,   nil,
   nil,   nil,   nil,   904,   904,   nil,   904,   904,   904,   904,
   904,   904,   904,   904,   904,   nil,   904,   904,   904,   nil,
   904,   904,   904,   904,   904,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   904,   nil,   nil,   904,   nil,   nil,
   904,   904,   nil,   nil,   904,   nil,   904,   nil,   nil,   nil,
   904,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   904,   nil,
   nil,   nil,   nil,   904,   904,   904,   904,   nil,   904,   904,
   nil,   nil,   nil,   904,   904,   910,   910,   910,   910,   910,
   910,   904,   nil,   904,   910,   910,   nil,   nil,   nil,   910,
   nil,   910,   910,   910,   910,   910,   910,   910,   nil,   nil,
   nil,   nil,   nil,   910,   910,   910,   910,   910,   910,   910,
   nil,   nil,   910,   nil,   nil,   nil,   nil,   nil,   910,   910,
   nil,   910,   910,   910,   910,   910,   910,   910,   910,   910,
   nil,   910,   910,   910,   nil,   910,   910,   910,   910,   910,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   910,
   nil,   nil,   910,   nil,   nil,   910,   910,   nil,   nil,   910,
   nil,   910,   nil,   nil,   nil,   910,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   910,   nil,   nil,   nil,   nil,   910,   910,
   910,   910,   nil,   910,   910,   nil,   nil,   nil,   910,   910,
   912,   912,   912,   912,   912,   912,   910,   nil,   910,   912,
   912,   nil,   nil,   nil,   912,   nil,   912,   912,   912,   912,
   912,   912,   912,   nil,   nil,   nil,   nil,   nil,   912,   912,
   912,   912,   912,   912,   912,   nil,   nil,   912,   nil,   nil,
   nil,   nil,   nil,   912,   912,   nil,   912,   912,   912,   912,
   912,   912,   912,   912,   912,   nil,   912,   912,   912,   nil,
   912,   912,   912,   912,   912,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   912,   nil,   nil,   912,   nil,   nil,
   912,   912,   nil,   nil,   912,   nil,   912,   nil,   nil,   nil,
   912,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   912,   nil,
   nil,   nil,   nil,   912,   912,   912,   912,   nil,   912,   912,
   nil,   nil,   nil,   912,   912,   nil,     5,     5,     5,     5,
     5,   912,   nil,   912,     5,     5,   nil,   nil,   nil,     5,
   nil,     5,     5,     5,     5,     5,     5,     5,   nil,   nil,
   nil,   nil,   nil,     5,     5,     5,     5,     5,     5,     5,
   nil,   nil,     5,   nil,   nil,   nil,   nil,   nil,     5,     5,
     5,     5,     5,     5,     5,     5,     5,     5,     5,     5,
   nil,     5,     5,     5,   nil,     5,     5,     5,     5,     5,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,     5,
   nil,   nil,     5,   nil,   nil,     5,     5,   nil,   nil,     5,
   nil,     5,   nil,   nil,   nil,     5,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,     5,   nil,   nil,   nil,   nil,     5,     5,
     5,     5,   nil,     5,     5,   nil,   nil,   nil,     5,     5,
   nil,    19,    19,    19,   nil,    19,     5,   nil,     5,    19,
    19,   nil,   nil,   nil,    19,   nil,    19,    19,    19,    19,
    19,    19,    19,   nil,   nil,   nil,   nil,   nil,    19,    19,
    19,    19,    19,    19,    19,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,   nil,    19,   nil,   nil,    19,    19,    19,
    19,    19,    19,    19,    19,   nil,    19,    19,    19,   nil,
    19,    19,    19,    19,    19,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    19,   nil,   nil,    19,   nil,   nil,
    19,    19,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,
    19,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    19,   nil,
   nil,   nil,   nil,    19,    19,    19,    19,   nil,    19,    19,
   nil,   nil,   nil,    19,    19,   nil,    27,    27,    27,   nil,
    27,    19,   nil,    19,    27,    27,   nil,   nil,   nil,    27,
   nil,    27,    27,    27,    27,    27,    27,    27,   nil,   nil,
   nil,   nil,   nil,    27,    27,    27,    27,    27,    27,    27,
   nil,   nil,    27,   nil,   nil,   nil,   nil,   nil,   nil,    27,
   nil,   nil,    27,    27,    27,    27,    27,    27,    27,    27,
    27,    27,    27,    27,   nil,    27,    27,    27,    27,    27,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    27,
   nil,   nil,    27,   nil,   nil,    27,    27,   nil,   nil,    27,
   nil,    27,   nil,    27,   nil,    27,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    27,   nil,   nil,   nil,   nil,    27,    27,
    27,    27,   nil,    27,    27,   nil,   nil,   nil,    27,    27,
   nil,    28,    28,    28,   nil,    28,    27,   nil,    27,    28,
    28,   nil,   nil,   nil,    28,   nil,    28,    28,    28,    28,
    28,    28,    28,   nil,   nil,   nil,   nil,   nil,    28,    28,
    28,    28,    28,    28,    28,   nil,   nil,    28,   nil,   nil,
   nil,   nil,   nil,   nil,    28,   nil,   nil,    28,    28,    28,
    28,    28,    28,    28,    28,    28,    28,    28,    28,   nil,
    28,    28,    28,    28,    28,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    28,   nil,   nil,    28,   nil,   nil,
    28,    28,   nil,   nil,    28,   nil,    28,   nil,    28,   nil,
    28,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    28,   nil,
   nil,   nil,   nil,    28,    28,    28,    28,   nil,    28,    28,
   nil,   nil,   nil,    28,    28,   nil,    29,    29,    29,   nil,
    29,    28,   nil,    28,    29,    29,   nil,   nil,   nil,    29,
   nil,    29,    29,    29,    29,    29,    29,    29,   nil,   nil,
   nil,   nil,   nil,    29,    29,    29,    29,    29,    29,    29,
   nil,   nil,    29,   nil,   nil,   nil,   nil,   nil,   nil,    29,
   nil,   nil,    29,    29,    29,    29,    29,    29,    29,    29,
    29,    29,    29,    29,   nil,    29,    29,    29,    29,    29,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    29,
   nil,   nil,    29,   nil,   nil,    29,    29,   nil,   nil,    29,
   nil,    29,   nil,    29,   nil,    29,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    29,   nil,   nil,   nil,   nil,    29,    29,
    29,    29,   nil,    29,    29,   nil,   nil,   nil,    29,    29,
   nil,    32,    32,    32,   nil,    32,    29,   nil,    29,    32,
    32,   nil,   nil,   nil,    32,   nil,    32,    32,    32,    32,
    32,    32,    32,   nil,   nil,   nil,   nil,   nil,    32,    32,
    32,    32,    32,    32,    32,   nil,   nil,    32,   nil,   nil,
   nil,   nil,   nil,   nil,    32,   nil,   nil,    32,    32,    32,
    32,    32,    32,    32,    32,   nil,    32,    32,    32,   nil,
    32,    32,   nil,   688,    32,   688,   688,   688,   688,   688,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    32,   nil,   nil,    32,   nil,   nil,
    32,    32,   nil,   nil,    32,   nil,    32,   nil,   nil,   nil,
   688,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   688,
   688,   688,   688,    32,    32,    32,    32,   nil,    32,    32,
   nil,   nil,   nil,    32,    32,   nil,    33,    33,    33,   nil,
    33,    32,   nil,    32,    33,    33,   nil,   nil,   nil,    33,
   nil,    33,    33,    33,    33,    33,    33,    33,   nil,   nil,
   nil,   nil,   nil,    33,    33,    33,    33,    33,    33,    33,
   nil,   nil,    33,   nil,   nil,   nil,   nil,   584,   nil,    33,
   nil,   nil,    33,    33,    33,    33,    33,    33,    33,    33,
   nil,    33,    33,    33,   nil,    33,    33,   nil,   nil,    33,
   584,   584,   584,   584,   584,   584,   584,   584,   584,   584,
   584,   nil,   584,   584,   nil,   nil,   584,   584,   nil,    33,
   nil,   nil,    33,   nil,   nil,    33,    33,   nil,   nil,    33,
   nil,   nil,   584,   nil,   584,   nil,   584,   584,   584,   584,
   584,   584,   584,   nil,   584,   nil,   nil,   nil,    33,    33,
    33,    33,   nil,    33,    33,   nil,   nil,   nil,    33,    33,
   nil,   584,   nil,    33,   nil,   nil,    33,   nil,    33,    39,
    39,    39,   nil,    39,   nil,   nil,   nil,    39,    39,   nil,
   nil,   nil,    39,   nil,    39,    39,    39,    39,    39,    39,
    39,   nil,   nil,   nil,   nil,   nil,    39,    39,    39,    39,
    39,    39,    39,   nil,   nil,    39,   nil,   nil,   nil,   nil,
   nil,   nil,    39,   nil,   nil,    39,    39,    39,    39,    39,
    39,    39,    39,   nil,    39,    39,    39,   nil,    39,    39,
    39,    39,    39,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    39,   nil,   nil,    39,   nil,   nil,    39,    39,
   nil,   nil,    39,   nil,   nil,   nil,   nil,   nil,    39,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    39,   nil,   nil,   nil,
   nil,    39,    39,    39,    39,   nil,    39,    39,   nil,   nil,
   nil,    39,    39,   nil,    40,    40,    40,   nil,    40,    39,
   nil,    39,    40,    40,   nil,   nil,   nil,    40,   nil,    40,
    40,    40,    40,    40,    40,    40,   nil,   nil,   nil,   nil,
   nil,    40,    40,    40,    40,    40,    40,    40,   nil,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
    40,    40,    40,    40,    40,    40,    40,    40,   nil,    40,
    40,    40,   nil,    40,    40,    40,    40,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
    40,   nil,   nil,    40,    40,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,    40,    40,    40,    40,
   nil,    40,    40,   nil,   nil,   nil,    40,    40,   nil,    41,
    41,    41,   nil,    41,    40,   nil,    40,    41,    41,   nil,
   nil,   nil,    41,   nil,    41,    41,    41,    41,    41,    41,
    41,   nil,   nil,   nil,   nil,   nil,    41,    41,    41,    41,
    41,    41,    41,   nil,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,    41,   nil,   nil,    41,    41,    41,    41,    41,
    41,    41,    41,   nil,    41,    41,    41,   nil,    41,    41,
    41,    41,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    41,   nil,   nil,    41,   nil,   nil,    41,    41,
   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,
   nil,    41,    41,    41,    41,   nil,    41,    41,   nil,   nil,
   nil,    41,    41,   nil,    53,    53,    53,   nil,    53,    41,
   nil,    41,    53,    53,   nil,   nil,   nil,    53,   nil,    53,
    53,    53,    53,    53,    53,    53,   nil,   nil,   nil,   nil,
   nil,    53,    53,    53,    53,    53,    53,    53,   nil,   nil,
    53,   nil,   nil,   nil,   nil,   nil,   nil,    53,   nil,   nil,
    53,    53,    53,    53,    53,    53,    53,    53,   nil,    53,
    53,    53,   nil,    53,    53,    53,    53,    53,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    53,   nil,   nil,
    53,   nil,   nil,    53,    53,   nil,   nil,    53,   nil,   nil,
   nil,   nil,   nil,    53,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    53,   nil,   nil,   nil,   nil,    53,    53,    53,    53,
   nil,    53,    53,   nil,   nil,   nil,    53,    53,   nil,    54,
    54,    54,   nil,    54,    53,   nil,    53,    54,    54,   nil,
   nil,   nil,    54,   nil,    54,    54,    54,    54,    54,    54,
    54,   nil,   nil,   nil,   nil,   nil,    54,    54,    54,    54,
    54,    54,    54,   nil,   nil,    54,   nil,   nil,   nil,   nil,
   nil,   nil,    54,   nil,   nil,    54,    54,    54,    54,    54,
    54,    54,    54,    54,    54,    54,    54,   nil,    54,    54,
    54,    54,    54,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    54,   nil,   nil,    54,   nil,   nil,    54,    54,
   nil,   nil,    54,   nil,    54,   nil,   nil,   nil,    54,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    54,   nil,   nil,   nil,
   nil,    54,    54,    54,    54,   nil,    54,    54,   nil,   nil,
   nil,    54,    54,   nil,    55,    55,    55,   nil,    55,    54,
   nil,    54,    55,    55,   nil,   nil,   nil,    55,   nil,    55,
    55,    55,    55,    55,    55,    55,   nil,   nil,   nil,   nil,
   nil,    55,    55,    55,    55,    55,    55,    55,   nil,   nil,
    55,   nil,   nil,   nil,   nil,   nil,   nil,    55,   nil,   nil,
    55,    55,    55,    55,    55,    55,    55,    55,    55,    55,
    55,    55,   nil,    55,    55,    55,    55,    55,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    55,   nil,   nil,
    55,   nil,   nil,    55,    55,   nil,   nil,    55,   nil,   nil,
   nil,   nil,   nil,    55,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    55,   nil,   nil,   nil,   nil,    55,    55,    55,    55,
   nil,    55,    55,   nil,   nil,   nil,    55,    55,   nil,    58,
    58,    58,   nil,    58,    55,   nil,    55,    58,    58,   nil,
   nil,   nil,    58,   nil,    58,    58,    58,    58,    58,    58,
    58,   nil,   nil,   nil,   nil,   nil,    58,    58,    58,    58,
    58,    58,    58,   nil,   nil,    58,   nil,   nil,   nil,   nil,
   nil,   nil,    58,   nil,   nil,    58,    58,    58,    58,    58,
    58,    58,    58,   nil,    58,    58,    58,   nil,    58,    58,
    58,    58,    58,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    58,   nil,   nil,    58,   nil,   nil,    58,    58,
   nil,   nil,    58,   nil,   nil,   nil,   nil,   nil,    58,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    58,   nil,   nil,   nil,
   nil,    58,    58,    58,    58,   nil,    58,    58,   nil,   nil,
   nil,    58,    58,   nil,    59,    59,    59,   nil,    59,    58,
   nil,    58,    59,    59,   nil,   nil,   nil,    59,   nil,    59,
    59,    59,    59,    59,    59,    59,   nil,   nil,   nil,   nil,
   nil,    59,    59,    59,    59,    59,    59,    59,   nil,   nil,
    59,   nil,   nil,   nil,   nil,   nil,   nil,    59,   nil,   nil,
    59,    59,    59,    59,    59,    59,    59,    59,   nil,    59,
    59,    59,   nil,    59,    59,    59,    59,    59,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    59,   nil,   nil,
    59,   nil,   nil,    59,    59,   nil,   nil,    59,   nil,   nil,
   nil,   nil,   nil,    59,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    59,   nil,   nil,   nil,   nil,    59,    59,    59,    59,
   nil,    59,    59,   nil,   nil,   nil,    59,    59,   nil,    62,
    62,    62,   nil,    62,    59,   nil,    59,    62,    62,   nil,
   nil,   nil,    62,   nil,    62,    62,    62,    62,    62,    62,
    62,   nil,   nil,   nil,   nil,   nil,    62,    62,    62,    62,
    62,    62,    62,   nil,   nil,    62,   nil,   nil,   nil,   nil,
   nil,   nil,    62,   nil,   nil,    62,    62,    62,    62,    62,
    62,    62,    62,   nil,    62,    62,    62,   nil,    62,    62,
    62,    62,    62,   596,   596,   596,   596,   596,   596,   596,
   596,   596,   596,   596,   nil,   596,   596,   nil,   nil,   596,
   596,   nil,    62,   nil,   596,    62,   nil,   nil,    62,    62,
   nil,   nil,    62,   nil,   nil,   596,   nil,   596,    62,   596,
   596,   596,   596,   596,   596,   596,    62,   596,   nil,   nil,
   nil,    62,    62,    62,    62,   nil,    62,    62,   nil,   nil,
   nil,    62,    62,    62,   596,   nil,   596,   nil,    62,    62,
   nil,    62,    63,    63,    63,   nil,    63,   nil,   nil,   nil,
    63,    63,   nil,   nil,   nil,    63,   nil,    63,    63,    63,
    63,    63,    63,    63,   nil,   nil,   nil,   nil,   nil,    63,
    63,    63,    63,    63,    63,    63,   nil,   nil,    63,   nil,
   nil,   nil,   nil,   nil,   nil,    63,   nil,   nil,    63,    63,
    63,    63,    63,    63,    63,    63,   nil,    63,    63,    63,
   nil,    63,    63,   nil,   761,    63,   761,   761,   761,   761,
   761,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    63,   nil,   nil,    63,   nil,
   nil,    63,    63,   nil,   nil,    63,   nil,    63,   nil,   nil,
   nil,   761,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   761,   761,   761,   761,    63,    63,    63,    63,   nil,    63,
    63,   nil,   nil,   nil,    63,    63,   nil,    64,    64,    64,
   nil,    64,    63,   nil,    63,    64,    64,   nil,   nil,   nil,
    64,   nil,    64,    64,    64,    64,    64,    64,    64,   nil,
   nil,   nil,   nil,   nil,    64,    64,    64,    64,    64,    64,
    64,   nil,   nil,    64,   nil,   nil,   nil,   nil,   nil,   nil,
    64,   nil,   nil,    64,    64,    64,    64,    64,    64,    64,
    64,   nil,    64,    64,    64,   nil,    64,    64,   nil,   763,
    64,   763,   763,   763,   763,   763,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    64,   nil,   nil,
    64,   nil,   nil,    64,   nil,   nil,    64,    64,   nil,   nil,
    64,   nil,   nil,   nil,   nil,   nil,   763,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   763,   763,   763,   763,    64,
    64,    64,    64,   nil,    64,    64,   nil,   nil,   nil,    64,
    64,   nil,    65,    65,    65,   nil,    65,    64,   nil,    64,
    65,    65,   nil,   nil,   nil,    65,   nil,    65,    65,    65,
    65,    65,    65,    65,   nil,   nil,   nil,   nil,   nil,    65,
    65,    65,    65,    65,    65,    65,   nil,   nil,    65,   nil,
   nil,   nil,   nil,   nil,   nil,    65,   nil,   nil,    65,    65,
    65,    65,    65,    65,    65,    65,   nil,    65,    65,    65,
   nil,    65,    65,   nil,   805,    65,   805,   805,   805,   805,
   805,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    65,   nil,   nil,    65,   nil,
   nil,    65,    65,   nil,   nil,    65,   nil,   nil,   nil,   nil,
   nil,   805,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   805,   805,   805,   805,    65,    65,    65,    65,   nil,    65,
    65,   nil,   nil,   nil,    65,    65,   nil,   103,   103,   103,
   103,   103,    65,   nil,    65,   103,   103,   nil,   nil,   nil,
   103,   nil,   103,   103,   103,   103,   103,   103,   103,   nil,
   nil,   nil,   nil,   nil,   103,   103,   103,   103,   103,   103,
   103,   nil,   nil,   103,   nil,   nil,   nil,   nil,   nil,   103,
   103,   103,   103,   103,   103,   103,   103,   103,   103,   103,
   103,   nil,   103,   103,   103,   nil,   103,   103,   103,   103,
   103,    20,    20,    20,    20,    20,    20,    20,    20,    20,
    20,    20,   nil,    20,    20,   nil,   nil,    20,    20,   nil,
   103,   nil,   nil,   103,   nil,   nil,   103,   103,   nil,   nil,
   103,   nil,   103,    20,   nil,    20,   103,    20,    20,    20,
    20,    20,    20,    20,   103,    20,   nil,   nil,   nil,   103,
   103,   103,   103,   nil,   103,   103,   nil,   nil,   nil,   103,
   103,   nil,    20,   nil,   nil,   nil,   103,   103,   nil,   103,
   108,   108,   108,   nil,   108,   nil,   nil,   nil,   108,   108,
   nil,   nil,   nil,   108,   nil,   108,   108,   108,   108,   108,
   108,   108,   nil,   nil,   nil,   nil,   nil,   108,   108,   108,
   108,   108,   108,   108,   nil,   nil,   108,   nil,   nil,   nil,
   nil,   nil,   nil,   108,   nil,   nil,   108,   108,   108,   108,
   108,   108,   108,   108,   nil,   108,   108,   108,   nil,   108,
   108,   108,   108,   108,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   108,   nil,   nil,   108,   nil,   nil,   108,
   108,   nil,   nil,   108,   nil,   nil,   nil,   nil,   nil,   108,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   108,   nil,   nil,
   nil,   nil,   108,   108,   108,   108,   nil,   108,   108,   nil,
   nil,   nil,   108,   108,   nil,   109,   109,   109,   nil,   109,
   108,   nil,   108,   109,   109,   nil,   nil,   nil,   109,   nil,
   109,   109,   109,   109,   109,   109,   109,   nil,   nil,   nil,
   nil,   nil,   109,   109,   109,   109,   109,   109,   109,   nil,
   nil,   109,   nil,   nil,   nil,   nil,   nil,   nil,   109,   nil,
   nil,   109,   109,   109,   109,   109,   109,   109,   109,   nil,
   109,   109,   109,   nil,   109,   109,   109,   109,   109,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   109,   nil,
   nil,   109,   nil,   nil,   109,   109,   nil,   nil,   109,   nil,
   nil,   nil,   nil,   nil,   109,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   109,   nil,   nil,   nil,   nil,   109,   109,   109,
   109,   nil,   109,   109,   nil,   nil,   nil,   109,   109,   nil,
   110,   110,   110,   nil,   110,   109,   nil,   109,   110,   110,
   nil,   nil,   nil,   110,   nil,   110,   110,   110,   110,   110,
   110,   110,   nil,   nil,   nil,   nil,   nil,   110,   110,   110,
   110,   110,   110,   110,   nil,   nil,   110,   nil,   nil,   nil,
   nil,   nil,   nil,   110,   nil,   nil,   110,   110,   110,   110,
   110,   110,   110,   110,   nil,   110,   110,   110,   nil,   110,
   110,   110,   110,   110,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   110,   nil,   nil,   110,   nil,   nil,   110,
   110,   nil,   nil,   110,   nil,   nil,   nil,   nil,   nil,   110,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   110,   nil,   nil,
   nil,   nil,   110,   110,   110,   110,   nil,   110,   110,   nil,
   nil,   nil,   110,   110,   nil,   111,   111,   111,   nil,   111,
   110,   nil,   110,   111,   111,   nil,   nil,   nil,   111,   nil,
   111,   111,   111,   111,   111,   111,   111,   nil,   nil,   nil,
   nil,   nil,   111,   111,   111,   111,   111,   111,   111,   nil,
   nil,   111,   nil,   nil,   nil,   nil,   nil,   nil,   111,   nil,
   nil,   111,   111,   111,   111,   111,   111,   111,   111,   nil,
   111,   111,   111,   nil,   111,   111,   111,   111,   111,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   111,   nil,
   nil,   111,   nil,   nil,   111,   111,   nil,   nil,   111,   nil,
   nil,   nil,   nil,   nil,   111,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   111,   nil,   nil,   nil,   nil,   111,   111,   111,
   111,   nil,   111,   111,   nil,   nil,   nil,   111,   111,   nil,
   112,   112,   112,   112,   112,   111,   nil,   111,   112,   112,
   nil,   nil,   nil,   112,   nil,   112,   112,   112,   112,   112,
   112,   112,   nil,   nil,   nil,   nil,   nil,   112,   112,   112,
   112,   112,   112,   112,   nil,   nil,   112,   nil,   nil,   nil,
   nil,   nil,   112,   112,   nil,   112,   112,   112,   112,   112,
   112,   112,   112,   112,   nil,   112,   112,   112,   nil,   112,
   112,   112,   112,   112,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   112,   nil,   nil,   112,   nil,   nil,   112,
   112,   nil,   nil,   112,   nil,   112,   nil,   nil,   nil,   112,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   112,   nil,   nil,
   nil,   nil,   112,   112,   112,   112,   nil,   112,   112,   nil,
   nil,   nil,   112,   112,   nil,   198,   198,   198,   nil,   198,
   112,   nil,   112,   198,   198,   nil,   nil,   nil,   198,   nil,
   198,   198,   198,   198,   198,   198,   198,   nil,   nil,   nil,
   nil,   nil,   198,   198,   198,   198,   198,   198,   198,   nil,
   nil,   198,   nil,   nil,   nil,   nil,   nil,   nil,   198,   nil,
   nil,   198,   198,   198,   198,   198,   198,   198,   198,   nil,
   198,   198,   198,   nil,   198,   198,   198,   198,   198,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   198,   nil,
   nil,   198,   nil,   nil,   198,   198,   nil,   nil,   198,   nil,
   198,   nil,   nil,   nil,   198,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   198,   nil,   nil,   nil,   nil,   198,   198,   198,
   198,   nil,   198,   198,   nil,   nil,   nil,   198,   198,   nil,
   199,   199,   199,   nil,   199,   198,   nil,   198,   199,   199,
   nil,   nil,   nil,   199,   nil,   199,   199,   199,   199,   199,
   199,   199,   nil,   nil,   nil,   nil,   nil,   199,   199,   199,
   199,   199,   199,   199,   nil,   nil,   199,   nil,   nil,   nil,
   nil,   nil,   nil,   199,   nil,   nil,   199,   199,   199,   199,
   199,   199,   199,   199,   nil,   199,   199,   199,   nil,   199,
   199,   199,   199,   199,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   199,   nil,   nil,   199,   nil,   nil,   199,
   199,   nil,   nil,   199,   nil,   199,   nil,   nil,   nil,   199,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   199,   nil,   nil,
   nil,   nil,   199,   199,   199,   199,   nil,   199,   199,   nil,
   nil,   nil,   199,   199,   nil,   200,   200,   200,   nil,   200,
   199,   nil,   199,   200,   200,   nil,   nil,   nil,   200,   nil,
   200,   200,   200,   200,   200,   200,   200,   nil,   nil,   nil,
   nil,   nil,   200,   200,   200,   200,   200,   200,   200,   nil,
   nil,   200,   nil,   nil,   nil,   nil,   nil,   nil,   200,   nil,
   nil,   200,   200,   200,   200,   200,   200,   200,   200,   nil,
   200,   200,   200,   nil,   200,   200,   200,   200,   200,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   200,   nil,
   nil,   200,   nil,   nil,   200,   200,   nil,   nil,   200,   nil,
   nil,   nil,   nil,   nil,   200,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   200,   nil,   nil,   nil,   nil,   200,   200,   200,
   200,   nil,   200,   200,   nil,   nil,   nil,   200,   200,   nil,
   201,   201,   201,   nil,   201,   200,   nil,   200,   201,   201,
   nil,   nil,   nil,   201,   nil,   201,   201,   201,   201,   201,
   201,   201,   nil,   nil,   nil,   nil,   nil,   201,   201,   201,
   201,   201,   201,   201,   nil,   nil,   201,   nil,   nil,   nil,
   nil,   nil,   nil,   201,   nil,   nil,   201,   201,   201,   201,
   201,   201,   201,   201,   201,   201,   201,   201,   nil,   201,
   201,   201,   201,   201,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   201,   nil,   nil,   201,   nil,   nil,   201,
   201,   nil,   nil,   201,   nil,   201,   nil,   201,   nil,   201,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   201,   nil,   nil,
   nil,   nil,   201,   201,   201,   201,   nil,   201,   201,   nil,
   nil,   nil,   201,   201,   nil,   204,   204,   204,   nil,   204,
   201,   nil,   201,   204,   204,   nil,   nil,   nil,   204,   nil,
   204,   204,   204,   204,   204,   204,   204,   nil,   nil,   nil,
   nil,   nil,   204,   204,   204,   204,   204,   204,   204,   nil,
   nil,   204,   nil,   nil,   nil,   nil,   nil,   nil,   204,   nil,
   nil,   204,   204,   204,   204,   204,   204,   204,   204,   nil,
   204,   204,   204,   nil,   204,   204,   204,   204,   204,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   204,   nil,
   nil,   204,   nil,   nil,   204,   204,   nil,   nil,   204,   nil,
   nil,   nil,   nil,   nil,   204,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   204,   nil,   nil,   nil,   nil,   204,   204,   204,
   204,   nil,   204,   204,   nil,   nil,   nil,   204,   204,   nil,
   205,   205,   205,   nil,   205,   204,   nil,   204,   205,   205,
   nil,   nil,   nil,   205,   nil,   205,   205,   205,   205,   205,
   205,   205,   nil,   nil,   nil,   nil,   nil,   205,   205,   205,
   205,   205,   205,   205,   nil,   nil,   205,   nil,   nil,   nil,
   nil,   nil,   nil,   205,   nil,   nil,   205,   205,   205,   205,
   205,   205,   205,   205,   nil,   205,   205,   205,   nil,   205,
   205,   205,   205,   205,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   205,   nil,   nil,   205,   nil,   nil,   205,
   205,   nil,   nil,   205,   nil,   nil,   nil,   nil,   nil,   205,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   205,   nil,   nil,
   nil,   nil,   205,   205,   205,   205,   nil,   205,   205,   nil,
   nil,   nil,   205,   205,   nil,   206,   206,   206,   nil,   206,
   205,   nil,   205,   206,   206,   nil,   nil,   nil,   206,   nil,
   206,   206,   206,   206,   206,   206,   206,   nil,   nil,   nil,
   nil,   nil,   206,   206,   206,   206,   206,   206,   206,   nil,
   nil,   206,   nil,   nil,   nil,   nil,   nil,   nil,   206,   nil,
   nil,   206,   206,   206,   206,   206,   206,   206,   206,   nil,
   206,   206,   206,   nil,   206,   206,   206,   206,   206,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   206,   nil,
   nil,   206,   nil,   nil,   206,   206,   nil,   nil,   206,   nil,
   nil,   nil,   nil,   nil,   206,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   206,   nil,   nil,   nil,   nil,   206,   206,   206,
   206,   nil,   206,   206,   nil,   nil,   nil,   206,   206,   nil,
   207,   207,   207,   nil,   207,   206,   nil,   206,   207,   207,
   nil,   nil,   nil,   207,   nil,   207,   207,   207,   207,   207,
   207,   207,   nil,   nil,   nil,   nil,   nil,   207,   207,   207,
   207,   207,   207,   207,   nil,   nil,   207,   nil,   nil,   nil,
   nil,   nil,   nil,   207,   nil,   nil,   207,   207,   207,   207,
   207,   207,   207,   207,   nil,   207,   207,   207,   nil,   207,
   207,   207,   207,   207,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   207,   nil,   nil,   207,   nil,   nil,   207,
   207,   nil,   nil,   207,   nil,   nil,   nil,   nil,   nil,   207,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   207,   nil,   nil,
   nil,   nil,   207,   207,   207,   207,   nil,   207,   207,   nil,
   nil,   nil,   207,   207,   nil,   208,   208,   208,   nil,   208,
   207,   nil,   207,   208,   208,   nil,   nil,   nil,   208,   nil,
   208,   208,   208,   208,   208,   208,   208,   nil,   nil,   nil,
   nil,   nil,   208,   208,   208,   208,   208,   208,   208,   nil,
   nil,   208,   nil,   nil,   nil,   nil,   nil,   nil,   208,   nil,
   nil,   208,   208,   208,   208,   208,   208,   208,   208,   nil,
   208,   208,   208,   nil,   208,   208,   208,   208,   208,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   208,   nil,
   nil,   208,   nil,   nil,   208,   208,   nil,   nil,   208,   nil,
   nil,   nil,   nil,   nil,   208,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   208,   nil,   nil,   nil,   nil,   208,   208,   208,
   208,   nil,   208,   208,   nil,   nil,   nil,   208,   208,   208,
   218,   218,   218,   nil,   218,   208,   nil,   208,   218,   218,
   nil,   nil,   nil,   218,   nil,   218,   218,   218,   218,   218,
   218,   218,   nil,   nil,   nil,   nil,   nil,   218,   218,   218,
   218,   218,   218,   218,   nil,   nil,   218,   nil,   nil,   nil,
   nil,   nil,   nil,   218,   nil,   nil,   218,   218,   218,   218,
   218,   218,   218,   218,   nil,   218,   218,   218,   nil,   218,
   218,   218,   218,   218,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   218,   nil,   nil,   218,   nil,   nil,   218,
   218,   nil,   nil,   218,   nil,   nil,   nil,   nil,   nil,   218,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,
   nil,   nil,   218,   218,   218,   218,   nil,   218,   218,   nil,
   nil,   nil,   218,   218,   nil,   221,   221,   221,   nil,   221,
   218,   nil,   218,   221,   221,   nil,   nil,   nil,   221,   nil,
   221,   221,   221,   221,   221,   221,   221,   nil,   nil,   nil,
   nil,   nil,   221,   221,   221,   221,   221,   221,   221,   nil,
   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,
   nil,   221,   221,   221,   221,   221,   221,   221,   221,   nil,
   221,   221,   221,   nil,   221,   221,   221,   221,   221,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,
   nil,   221,   nil,   nil,   221,   221,   nil,   nil,   221,   nil,
   nil,   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   221,   221,   221,
   221,   nil,   221,   221,   nil,   nil,   nil,   221,   221,   nil,
   222,   222,   222,   nil,   222,   221,   nil,   221,   222,   222,
   nil,   nil,   nil,   222,   nil,   222,   222,   222,   222,   222,
   222,   222,   nil,   nil,   nil,   nil,   nil,   222,   222,   222,
   222,   222,   222,   222,   nil,   nil,   222,   nil,   nil,   nil,
   nil,   nil,   nil,   222,   nil,   nil,   222,   222,   222,   222,
   222,   222,   222,   222,   nil,   222,   222,   222,   nil,   222,
   222,   222,   222,   222,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   222,   nil,   nil,   222,   nil,   nil,   222,
   222,   nil,   nil,   222,   nil,   nil,   nil,   nil,   nil,   222,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,
   nil,   nil,   222,   222,   222,   222,   nil,   222,   222,   nil,
   nil,   nil,   222,   222,   nil,   223,   223,   223,   nil,   223,
   222,   nil,   222,   223,   223,   nil,   nil,   nil,   223,   nil,
   223,   223,   223,   223,   223,   223,   223,   nil,   nil,   nil,
   nil,   nil,   223,   223,   223,   223,   223,   223,   223,   nil,
   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,   223,   nil,
   nil,   223,   223,   223,   223,   223,   223,   223,   223,   nil,
   223,   223,   223,   nil,   223,   223,   223,   223,   223,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   223,   nil,
   nil,   223,   nil,   nil,   223,   223,   nil,   nil,   223,   nil,
   nil,   nil,   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   223,   223,   223,
   223,   nil,   223,   223,   nil,   nil,   nil,   223,   223,   nil,
   224,   224,   224,   nil,   224,   223,   nil,   223,   224,   224,
   nil,   nil,   nil,   224,   nil,   224,   224,   224,   224,   224,
   224,   224,   nil,   nil,   nil,   nil,   nil,   224,   224,   224,
   224,   224,   224,   224,   nil,   nil,   224,   nil,   nil,   nil,
   nil,   nil,   nil,   224,   nil,   nil,   224,   224,   224,   224,
   224,   224,   224,   224,   nil,   224,   224,   224,   nil,   224,
   224,   224,   224,   224,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   224,   nil,   nil,   224,   nil,   nil,   224,
   224,   nil,   nil,   224,   nil,   nil,   nil,   nil,   nil,   224,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   224,   nil,   nil,
   nil,   nil,   224,   224,   224,   224,   nil,   224,   224,   nil,
   nil,   nil,   224,   224,   nil,   225,   225,   225,   nil,   225,
   224,   nil,   224,   225,   225,   nil,   nil,   nil,   225,   nil,
   225,   225,   225,   225,   225,   225,   225,   nil,   nil,   nil,
   nil,   nil,   225,   225,   225,   225,   225,   225,   225,   nil,
   nil,   225,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   225,   225,   225,   225,   225,   225,   225,   225,   nil,
   225,   225,   225,   nil,   225,   225,   225,   225,   225,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   225,   nil,   nil,   225,   225,   nil,   nil,   225,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   nil,   nil,   225,   225,   225,
   225,   nil,   225,   225,   nil,   nil,   nil,   225,   225,   nil,
   226,   226,   226,   nil,   226,   225,   nil,   225,   226,   226,
   nil,   nil,   nil,   226,   nil,   226,   226,   226,   226,   226,
   226,   226,   nil,   nil,   nil,   nil,   nil,   226,   226,   226,
   226,   226,   226,   226,   nil,   nil,   226,   nil,   nil,   nil,
   nil,   nil,   nil,   226,   nil,   nil,   226,   226,   226,   226,
   226,   226,   226,   226,   nil,   226,   226,   226,   nil,   226,
   226,   226,   226,   226,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   226,   nil,   nil,   226,   nil,   nil,   226,
   226,   nil,   nil,   226,   nil,   nil,   nil,   nil,   nil,   226,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   226,   nil,   nil,
   nil,   nil,   226,   226,   226,   226,   nil,   226,   226,   nil,
   nil,   nil,   226,   226,   nil,   227,   227,   227,   nil,   227,
   226,   nil,   226,   227,   227,   nil,   nil,   nil,   227,   nil,
   227,   227,   227,   227,   227,   227,   227,   nil,   nil,   nil,
   nil,   nil,   227,   227,   227,   227,   227,   227,   227,   nil,
   nil,   227,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   227,   227,   227,   227,   227,   227,   227,   227,   nil,
   227,   227,   227,   nil,   227,   227,   227,   227,   227,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   227,   nil,   nil,   227,   227,   nil,   nil,   227,   nil,
   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   nil,   nil,   227,   227,   227,
   227,   nil,   227,   227,   nil,   nil,   nil,   227,   227,   nil,
   228,   228,   228,   nil,   228,   227,   nil,   227,   228,   228,
   nil,   nil,   nil,   228,   nil,   228,   228,   228,   228,   228,
   228,   228,   nil,   nil,   nil,   nil,   nil,   228,   228,   228,
   228,   228,   228,   228,   nil,   nil,   228,   nil,   nil,   nil,
   nil,   nil,   nil,   228,   nil,   nil,   228,   228,   228,   228,
   228,   228,   228,   228,   nil,   228,   228,   228,   nil,   228,
   228,   228,   228,   228,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   228,   nil,   nil,   228,   nil,   nil,   228,
   228,   nil,   nil,   228,   nil,   nil,   nil,   nil,   nil,   228,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   228,   nil,   nil,
   nil,   nil,   228,   228,   228,   228,   nil,   228,   228,   nil,
   nil,   nil,   228,   228,   nil,   229,   229,   229,   nil,   229,
   228,   nil,   228,   229,   229,   nil,   nil,   nil,   229,   nil,
   229,   229,   229,   229,   229,   229,   229,   nil,   nil,   nil,
   nil,   nil,   229,   229,   229,   229,   229,   229,   229,   nil,
   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,   229,   nil,
   nil,   229,   229,   229,   229,   229,   229,   229,   229,   nil,
   229,   229,   229,   nil,   229,   229,   229,   229,   229,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   229,   nil,
   nil,   229,   nil,   nil,   229,   229,   nil,   nil,   229,   nil,
   nil,   nil,   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   229,   nil,   nil,   nil,   nil,   229,   229,   229,
   229,   nil,   229,   229,   nil,   nil,   nil,   229,   229,   nil,
   230,   230,   230,   nil,   230,   229,   nil,   229,   230,   230,
   nil,   nil,   nil,   230,   nil,   230,   230,   230,   230,   230,
   230,   230,   nil,   nil,   nil,   nil,   nil,   230,   230,   230,
   230,   230,   230,   230,   nil,   nil,   230,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   230,   230,   230,   230,
   230,   230,   230,   230,   nil,   230,   230,   230,   nil,   230,
   230,   230,   230,   230,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   230,   nil,   nil,   230,
   230,   nil,   nil,   230,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   nil,   nil,   230,   230,   230,   230,   nil,   230,   230,   nil,
   nil,   nil,   230,   230,   nil,   231,   231,   231,   nil,   231,
   230,   nil,   230,   231,   231,   nil,   nil,   nil,   231,   nil,
   231,   231,   231,   231,   231,   231,   231,   nil,   nil,   nil,
   nil,   nil,   231,   231,   231,   231,   231,   231,   231,   nil,
   nil,   231,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,
   nil,   231,   231,   231,   231,   231,   231,   231,   231,   nil,
   231,   231,   231,   nil,   231,   231,   231,   231,   231,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,
   nil,   231,   nil,   nil,   231,   231,   nil,   nil,   231,   nil,
   nil,   nil,   nil,   nil,   231,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   231,   nil,   nil,   nil,   nil,   231,   231,   231,
   231,   nil,   231,   231,   nil,   nil,   nil,   231,   231,   nil,
   232,   232,   232,   nil,   232,   231,   nil,   231,   232,   232,
   nil,   nil,   nil,   232,   nil,   232,   232,   232,   232,   232,
   232,   232,   nil,   nil,   nil,   nil,   nil,   232,   232,   232,
   232,   232,   232,   232,   nil,   nil,   232,   nil,   nil,   nil,
   nil,   nil,   nil,   232,   nil,   nil,   232,   232,   232,   232,
   232,   232,   232,   232,   nil,   232,   232,   232,   nil,   232,
   232,   232,   232,   232,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   232,   nil,   nil,   232,   nil,   nil,   232,
   232,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   232,   232,   232,   232,   nil,   232,   232,   nil,
   nil,   nil,   232,   232,   nil,   233,   233,   233,   nil,   233,
   232,   nil,   232,   233,   233,   nil,   nil,   nil,   233,   nil,
   233,   233,   233,   233,   233,   233,   233,   nil,   nil,   nil,
   nil,   nil,   233,   233,   233,   233,   233,   233,   233,   nil,
   nil,   233,   nil,   nil,   nil,   nil,   nil,   nil,   233,   nil,
   nil,   233,   233,   233,   233,   233,   233,   233,   233,   nil,
   233,   233,   233,   nil,   233,   233,   233,   233,   233,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   233,   nil,
   nil,   233,   nil,   nil,   233,   233,   nil,   nil,   233,   nil,
   nil,   nil,   nil,   nil,   233,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   233,   nil,   nil,   nil,   nil,   233,   233,   233,
   233,   nil,   233,   233,   nil,   nil,   nil,   233,   233,   nil,
   234,   234,   234,   nil,   234,   233,   nil,   233,   234,   234,
   nil,   nil,   nil,   234,   nil,   234,   234,   234,   234,   234,
   234,   234,   nil,   nil,   nil,   nil,   nil,   234,   234,   234,
   234,   234,   234,   234,   nil,   nil,   234,   nil,   nil,   nil,
   nil,   nil,   nil,   234,   nil,   nil,   234,   234,   234,   234,
   234,   234,   234,   234,   nil,   234,   234,   234,   nil,   234,
   234,   234,   234,   234,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   234,   nil,   nil,   234,   nil,   nil,   234,
   234,   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   234,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   234,   nil,   nil,
   nil,   nil,   234,   234,   234,   234,   nil,   234,   234,   nil,
   nil,   nil,   234,   234,   nil,   235,   235,   235,   nil,   235,
   234,   nil,   234,   235,   235,   nil,   nil,   nil,   235,   nil,
   235,   235,   235,   235,   235,   235,   235,   nil,   nil,   nil,
   nil,   nil,   235,   235,   235,   235,   235,   235,   235,   nil,
   nil,   235,   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,
   nil,   235,   235,   235,   235,   235,   235,   235,   235,   nil,
   235,   235,   235,   nil,   235,   235,   235,   235,   235,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,
   nil,   235,   nil,   nil,   235,   235,   nil,   nil,   235,   nil,
   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   235,   nil,   nil,   nil,   nil,   235,   235,   235,
   235,   nil,   235,   235,   nil,   nil,   nil,   235,   235,   nil,
   236,   236,   236,   nil,   236,   235,   nil,   235,   236,   236,
   nil,   nil,   nil,   236,   nil,   236,   236,   236,   236,   236,
   236,   236,   nil,   nil,   nil,   nil,   nil,   236,   236,   236,
   236,   236,   236,   236,   nil,   nil,   236,   nil,   nil,   nil,
   nil,   nil,   nil,   236,   nil,   nil,   236,   236,   236,   236,
   236,   236,   236,   236,   nil,   236,   236,   236,   nil,   236,
   236,   236,   236,   236,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   236,   nil,   nil,   236,   nil,   nil,   236,
   236,   nil,   nil,   236,   nil,   nil,   nil,   nil,   nil,   236,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,
   nil,   nil,   236,   236,   236,   236,   nil,   236,   236,   nil,
   nil,   nil,   236,   236,   nil,   237,   237,   237,   nil,   237,
   236,   nil,   236,   237,   237,   nil,   nil,   nil,   237,   nil,
   237,   237,   237,   237,   237,   237,   237,   nil,   nil,   nil,
   nil,   nil,   237,   237,   237,   237,   237,   237,   237,   nil,
   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,   237,   nil,
   nil,   237,   237,   237,   237,   237,   237,   237,   237,   nil,
   237,   237,   237,   nil,   237,   237,   237,   237,   237,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   237,   nil,
   nil,   237,   nil,   nil,   237,   237,   nil,   nil,   237,   nil,
   nil,   nil,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   237,   nil,   nil,   nil,   nil,   237,   237,   237,
   237,   nil,   237,   237,   nil,   nil,   nil,   237,   237,   nil,
   238,   238,   238,   nil,   238,   237,   nil,   237,   238,   238,
   nil,   nil,   nil,   238,   nil,   238,   238,   238,   238,   238,
   238,   238,   nil,   nil,   nil,   nil,   nil,   238,   238,   238,
   238,   238,   238,   238,   nil,   nil,   238,   nil,   nil,   nil,
   nil,   nil,   nil,   238,   nil,   nil,   238,   238,   238,   238,
   238,   238,   238,   238,   nil,   238,   238,   238,   nil,   238,
   238,   238,   238,   238,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   238,   nil,   nil,   238,   nil,   nil,   238,
   238,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   238,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   238,   nil,   nil,
   nil,   nil,   238,   238,   238,   238,   nil,   238,   238,   nil,
   nil,   nil,   238,   238,   nil,   239,   239,   239,   nil,   239,
   238,   nil,   238,   239,   239,   nil,   nil,   nil,   239,   nil,
   239,   239,   239,   239,   239,   239,   239,   nil,   nil,   nil,
   nil,   nil,   239,   239,   239,   239,   239,   239,   239,   nil,
   nil,   239,   nil,   nil,   nil,   nil,   nil,   nil,   239,   nil,
   nil,   239,   239,   239,   239,   239,   239,   239,   239,   nil,
   239,   239,   239,   nil,   239,   239,   239,   239,   239,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   239,   nil,
   nil,   239,   nil,   nil,   239,   239,   nil,   nil,   239,   nil,
   nil,   nil,   nil,   nil,   239,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   239,   nil,   nil,   nil,   nil,   239,   239,   239,
   239,   nil,   239,   239,   nil,   nil,   nil,   239,   239,   nil,
   240,   240,   240,   nil,   240,   239,   nil,   239,   240,   240,
   nil,   nil,   nil,   240,   nil,   240,   240,   240,   240,   240,
   240,   240,   nil,   nil,   nil,   nil,   nil,   240,   240,   240,
   240,   240,   240,   240,   nil,   nil,   240,   nil,   nil,   nil,
   nil,   nil,   nil,   240,   nil,   nil,   240,   240,   240,   240,
   240,   240,   240,   240,   nil,   240,   240,   240,   nil,   240,
   240,   240,   240,   240,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   240,   nil,   nil,   240,   nil,   nil,   240,
   240,   nil,   nil,   240,   nil,   nil,   nil,   nil,   nil,   240,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   240,   nil,   nil,
   nil,   nil,   240,   240,   240,   240,   nil,   240,   240,   nil,
   nil,   nil,   240,   240,   nil,   241,   241,   241,   nil,   241,
   240,   nil,   240,   241,   241,   nil,   nil,   nil,   241,   nil,
   241,   241,   241,   241,   241,   241,   241,   nil,   nil,   nil,
   nil,   nil,   241,   241,   241,   241,   241,   241,   241,   nil,
   nil,   241,   nil,   nil,   nil,   nil,   nil,   nil,   241,   nil,
   nil,   241,   241,   241,   241,   241,   241,   241,   241,   nil,
   241,   241,   241,   nil,   241,   241,   241,   241,   241,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   241,   nil,
   nil,   241,   nil,   nil,   241,   241,   nil,   nil,   241,   nil,
   nil,   nil,   nil,   nil,   241,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   241,   nil,   nil,   nil,   nil,   241,   241,   241,
   241,   nil,   241,   241,   nil,   nil,   nil,   241,   241,   nil,
   242,   242,   242,   nil,   242,   241,   nil,   241,   242,   242,
   nil,   nil,   nil,   242,   nil,   242,   242,   242,   242,   242,
   242,   242,   nil,   nil,   nil,   nil,   nil,   242,   242,   242,
   242,   242,   242,   242,   nil,   nil,   242,   nil,   nil,   nil,
   nil,   nil,   nil,   242,   nil,   nil,   242,   242,   242,   242,
   242,   242,   242,   242,   nil,   242,   242,   242,   nil,   242,
   242,   242,   242,   242,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   242,   nil,   nil,   242,   nil,   nil,   242,
   242,   nil,   nil,   242,   nil,   nil,   nil,   nil,   nil,   242,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   242,   nil,   nil,
   nil,   nil,   242,   242,   242,   242,   nil,   242,   242,   nil,
   nil,   nil,   242,   242,   nil,   243,   243,   243,   nil,   243,
   242,   nil,   242,   243,   243,   nil,   nil,   nil,   243,   nil,
   243,   243,   243,   243,   243,   243,   243,   nil,   nil,   nil,
   nil,   nil,   243,   243,   243,   243,   243,   243,   243,   nil,
   nil,   243,   nil,   nil,   nil,   nil,   nil,   nil,   243,   nil,
   nil,   243,   243,   243,   243,   243,   243,   243,   243,   nil,
   243,   243,   243,   nil,   243,   243,   243,   243,   243,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   243,   nil,
   nil,   243,   nil,   nil,   243,   243,   nil,   nil,   243,   nil,
   nil,   nil,   nil,   nil,   243,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   243,   nil,   nil,   nil,   nil,   243,   243,   243,
   243,   nil,   243,   243,   nil,   nil,   nil,   243,   243,   nil,
   244,   244,   244,   nil,   244,   243,   nil,   243,   244,   244,
   nil,   nil,   nil,   244,   nil,   244,   244,   244,   244,   244,
   244,   244,   nil,   nil,   nil,   nil,   nil,   244,   244,   244,
   244,   244,   244,   244,   nil,   nil,   244,   nil,   nil,   nil,
   nil,   nil,   nil,   244,   nil,   nil,   244,   244,   244,   244,
   244,   244,   244,   244,   nil,   244,   244,   244,   nil,   244,
   244,   244,   244,   244,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   244,   nil,   nil,   244,   nil,   nil,   244,
   244,   nil,   nil,   244,   nil,   nil,   nil,   nil,   nil,   244,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   244,   nil,   nil,
   nil,   nil,   244,   244,   244,   244,   nil,   244,   244,   nil,
   nil,   nil,   244,   244,   nil,   245,   245,   245,   nil,   245,
   244,   nil,   244,   245,   245,   nil,   nil,   nil,   245,   nil,
   245,   245,   245,   245,   245,   245,   245,   nil,   nil,   nil,
   nil,   nil,   245,   245,   245,   245,   245,   245,   245,   nil,
   nil,   245,   nil,   nil,   nil,   nil,   nil,   nil,   245,   nil,
   nil,   245,   245,   245,   245,   245,   245,   245,   245,   nil,
   245,   245,   245,   nil,   245,   245,   245,   245,   245,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   245,   nil,
   nil,   245,   nil,   nil,   245,   245,   nil,   nil,   245,   nil,
   nil,   nil,   nil,   nil,   245,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   245,   nil,   nil,   nil,   nil,   245,   245,   245,
   245,   nil,   245,   245,   nil,   nil,   nil,   245,   245,   nil,
   246,   246,   246,   nil,   246,   245,   nil,   245,   246,   246,
   nil,   nil,   nil,   246,   nil,   246,   246,   246,   246,   246,
   246,   246,   nil,   nil,   nil,   nil,   nil,   246,   246,   246,
   246,   246,   246,   246,   nil,   nil,   246,   nil,   nil,   nil,
   nil,   nil,   nil,   246,   nil,   nil,   246,   246,   246,   246,
   246,   246,   246,   246,   nil,   246,   246,   246,   nil,   246,
   246,   246,   246,   246,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   246,   nil,   nil,   246,   nil,   nil,   246,
   246,   nil,   nil,   246,   nil,   nil,   nil,   nil,   nil,   246,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   246,   nil,   nil,
   nil,   nil,   246,   246,   246,   246,   nil,   246,   246,   nil,
   nil,   nil,   246,   246,   nil,   252,   252,   252,   nil,   252,
   246,   nil,   246,   252,   252,   nil,   nil,   nil,   252,   nil,
   252,   252,   252,   252,   252,   252,   252,   nil,   nil,   nil,
   nil,   nil,   252,   252,   252,   252,   252,   252,   252,   nil,
   nil,   252,   nil,   nil,   nil,   nil,   nil,   nil,   252,   nil,
   nil,   252,   252,   252,   252,   252,   252,   252,   252,   252,
   252,   252,   252,   nil,   252,   252,   252,   252,   252,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   252,   nil,
   nil,   252,   nil,   nil,   252,   252,   nil,   nil,   252,   nil,
   252,   nil,   252,   nil,   252,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   252,   nil,   nil,   nil,   nil,   252,   252,   252,
   252,   nil,   252,   252,   nil,   nil,   nil,   252,   252,   nil,
   253,   253,   253,   nil,   253,   252,   nil,   252,   253,   253,
   nil,   nil,   nil,   253,   nil,   253,   253,   253,   253,   253,
   253,   253,   nil,   nil,   nil,   nil,   nil,   253,   253,   253,
   253,   253,   253,   253,   nil,   nil,   253,   nil,   nil,   nil,
   nil,   nil,   nil,   253,   nil,   nil,   253,   253,   253,   253,
   253,   253,   253,   253,   253,   253,   253,   253,   nil,   253,
   253,   253,   253,   253,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   253,   nil,   nil,   253,   nil,   nil,   253,
   253,   nil,   nil,   253,   nil,   253,   nil,   253,   nil,   253,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   253,   nil,   nil,
   nil,   nil,   253,   253,   253,   253,   nil,   253,   253,   nil,
   nil,   nil,   253,   253,   nil,   261,   261,   261,   nil,   261,
   253,   nil,   253,   261,   261,   nil,   nil,   nil,   261,   nil,
   261,   261,   261,   261,   261,   261,   261,   nil,   nil,   nil,
   nil,   nil,   261,   261,   261,   261,   261,   261,   261,   nil,
   nil,   261,   nil,   nil,   nil,   nil,   nil,   nil,   261,   nil,
   nil,   261,   261,   261,   261,   261,   261,   261,   261,   261,
   261,   261,   261,   nil,   261,   261,   261,   261,   261,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   261,   nil,
   nil,   261,   nil,   nil,   261,   261,   nil,   nil,   261,   nil,
   261,   nil,   261,   nil,   261,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   261,   nil,   nil,   nil,   nil,   261,   261,   261,
   261,   nil,   261,   261,   nil,   nil,   nil,   261,   261,   261,
   268,   268,   268,   nil,   268,   261,   nil,   261,   268,   268,
   nil,   nil,   nil,   268,   nil,   268,   268,   268,   268,   268,
   268,   268,   nil,   nil,   nil,   nil,   nil,   268,   268,   268,
   268,   268,   268,   268,   nil,   nil,   268,   nil,   nil,   nil,
   nil,   nil,   nil,   268,   nil,   nil,   268,   268,   268,   268,
   268,   268,   268,   268,   nil,   268,   268,   268,   nil,   268,
   268,   268,   268,   268,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   268,   nil,   nil,   268,   nil,   nil,   268,
   268,   nil,   nil,   268,   nil,   nil,   nil,   nil,   nil,   268,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   268,   nil,   nil,
   nil,   nil,   268,   268,   268,   268,   nil,   268,   268,   nil,
   nil,   nil,   268,   268,   nil,   270,   270,   270,   nil,   270,
   268,   nil,   268,   270,   270,   nil,   nil,   nil,   270,   nil,
   270,   270,   270,   270,   270,   270,   270,   nil,   nil,   nil,
   nil,   nil,   270,   270,   270,   270,   270,   270,   270,   nil,
   nil,   270,   nil,   nil,   nil,   nil,   nil,   nil,   270,   nil,
   nil,   270,   270,   270,   270,   270,   270,   270,   270,   nil,
   270,   270,   270,   nil,   270,   270,   270,   270,   270,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   270,   nil,
   nil,   270,   nil,   nil,   270,   270,   nil,   nil,   270,   nil,
   nil,   nil,   nil,   nil,   270,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   270,   nil,   nil,   nil,   nil,   270,   270,   270,
   270,   nil,   270,   270,   nil,   nil,   nil,   270,   270,   nil,
   272,   272,   272,   nil,   272,   270,   nil,   270,   272,   272,
   nil,   nil,   nil,   272,   nil,   272,   272,   272,   272,   272,
   272,   272,   nil,   nil,   nil,   nil,   nil,   272,   272,   272,
   272,   272,   272,   272,   nil,   nil,   272,   nil,   nil,   nil,
   nil,   nil,   nil,   272,   nil,   nil,   272,   272,   272,   272,
   272,   272,   272,   272,   nil,   272,   272,   272,   nil,   272,
   272,   272,   272,   272,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   272,   nil,   nil,   272,   nil,   nil,   272,
   272,   nil,   nil,   272,   nil,   nil,   nil,   nil,   nil,   272,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   272,   nil,   nil,
   nil,   nil,   272,   272,   272,   272,   nil,   272,   272,   nil,
   nil,   nil,   272,   272,   nil,   277,   277,   277,   277,   277,
   272,   nil,   272,   277,   277,   nil,   nil,   nil,   277,   nil,
   277,   277,   277,   277,   277,   277,   277,   nil,   nil,   nil,
   nil,   nil,   277,   277,   277,   277,   277,   277,   277,   nil,
   nil,   277,   nil,   nil,   nil,   nil,   nil,   277,   277,   nil,
   277,   277,   277,   277,   277,   277,   277,   277,   277,   nil,
   277,   277,   277,   nil,   277,   277,   277,   277,   277,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   277,   nil,
   nil,   277,   nil,   nil,   277,   277,   nil,   nil,   277,   nil,
   277,   nil,   nil,   nil,   277,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   277,   nil,   nil,   nil,   nil,   277,   277,   277,
   277,   nil,   277,   277,   nil,   nil,   nil,   277,   277,   nil,
   283,   283,   283,   nil,   283,   277,   nil,   277,   283,   283,
   nil,   nil,   nil,   283,   nil,   283,   283,   283,   283,   283,
   283,   283,   nil,   nil,   nil,   nil,   nil,   283,   283,   283,
   283,   283,   283,   283,   nil,   nil,   283,   nil,   nil,   nil,
   nil,   nil,   nil,   283,   nil,   nil,   283,   283,   283,   283,
   283,   283,   283,   283,   nil,   283,   283,   283,   nil,   283,
   283,   nil,   nil,   283,   263,   263,   263,   263,   263,   263,
   263,   263,   263,   263,   263,   nil,   263,   263,   nil,   nil,
   263,   263,   nil,   283,   nil,   nil,   283,   nil,   nil,   283,
   283,   nil,   nil,   283,   nil,   nil,   263,   nil,   263,   nil,
   263,   263,   263,   263,   263,   263,   263,   nil,   263,   nil,
   nil,   nil,   283,   283,   283,   283,   nil,   283,   283,   nil,
   nil,   nil,   283,   283,   nil,   263,   nil,   283,   nil,   nil,
   283,   nil,   283,   299,   299,   299,   nil,   299,   nil,   nil,
   nil,   299,   299,   nil,   nil,   nil,   299,   nil,   299,   299,
   299,   299,   299,   299,   299,   nil,   nil,   nil,   nil,   nil,
   299,   299,   299,   299,   299,   299,   299,   nil,   nil,   299,
   nil,   nil,   nil,   nil,   nil,   nil,   299,   nil,   nil,   299,
   299,   299,   299,   299,   299,   299,   299,   nil,   299,   299,
   299,   nil,   299,   299,   nil,   807,   299,   807,   807,   807,
   807,   807,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   299,   nil,   nil,   299,
   nil,   nil,   299,   299,   nil,   nil,   299,   nil,   nil,   nil,
   nil,   nil,   807,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   807,   807,   807,   807,   299,   299,   299,   299,   nil,
   299,   299,   nil,   nil,   nil,   299,   299,   nil,   308,   308,
   308,   nil,   308,   299,   nil,   299,   308,   308,   nil,   nil,
   nil,   308,   nil,   308,   308,   308,   308,   308,   308,   308,
   nil,   nil,   nil,   nil,   nil,   308,   308,   308,   308,   308,
   308,   308,   nil,   nil,   308,   nil,   nil,   nil,   nil,   nil,
   nil,   308,   nil,   nil,   308,   308,   308,   308,   308,   308,
   308,   308,   nil,   308,   308,   308,   nil,   308,   308,   308,
   308,   308,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   308,   nil,   nil,   308,   308,   nil,   308,   308,   nil,
   nil,   308,   nil,   nil,   nil,   nil,   nil,   308,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   308,   nil,   nil,   nil,   nil,
   308,   308,   308,   308,   nil,   308,   308,   nil,   nil,   nil,
   308,   308,   nil,   324,   324,   324,   nil,   324,   308,   nil,
   308,   324,   324,   nil,   nil,   nil,   324,   nil,   324,   324,
   324,   324,   324,   324,   324,   nil,   nil,   nil,   nil,   nil,
   324,   324,   324,   324,   324,   324,   324,   nil,   nil,   324,
   nil,   nil,   nil,   nil,   nil,   nil,   324,   nil,   nil,   324,
   324,   324,   324,   324,   324,   324,   324,   nil,   324,   324,
   324,   nil,   324,   324,   324,   324,   324,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   324,   nil,   nil,   324,
   nil,   nil,   324,   324,   nil,   nil,   324,   nil,   nil,   nil,
   nil,   nil,   324,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   324,   nil,   nil,   nil,   nil,   324,   324,   324,   324,   nil,
   324,   324,   nil,   nil,   nil,   324,   324,   nil,   325,   325,
   325,   nil,   325,   324,   nil,   324,   325,   325,   nil,   nil,
   nil,   325,   nil,   325,   325,   325,   325,   325,   325,   325,
   nil,   nil,   nil,   nil,   nil,   325,   325,   325,   325,   325,
   325,   325,   nil,   nil,   325,   nil,   nil,   nil,   nil,   nil,
   nil,   325,   nil,   nil,   325,   325,   325,   325,   325,   325,
   325,   325,   nil,   325,   325,   325,   nil,   325,   325,   325,
   325,   325,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   325,   nil,   nil,   325,   nil,   nil,   325,   325,   nil,
   nil,   325,   nil,   nil,   nil,   nil,   nil,   325,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   325,   nil,   nil,   nil,   nil,
   325,   325,   325,   325,   nil,   325,   325,   nil,   nil,   nil,
   325,   325,   nil,   343,   343,   343,   nil,   343,   325,   nil,
   325,   343,   343,   nil,   nil,   nil,   343,   nil,   343,   343,
   343,   343,   343,   343,   343,   nil,   nil,   nil,   nil,   nil,
   343,   343,   343,   343,   343,   343,   343,   nil,   nil,   343,
   nil,   nil,   nil,   nil,   nil,   nil,   343,   nil,   nil,   343,
   343,   343,   343,   343,   343,   343,   343,   nil,   343,   343,
   343,   nil,   343,   343,   343,   343,   343,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   343,   nil,   nil,   343,
   nil,   nil,   343,   343,   nil,   nil,   343,   nil,   nil,   nil,
   nil,   nil,   343,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   343,   nil,   nil,   nil,   nil,   343,   343,   343,   343,   nil,
   343,   343,   nil,   nil,   nil,   343,   343,   nil,   358,   358,
   358,   nil,   358,   343,   nil,   343,   358,   358,   nil,   nil,
   nil,   358,   nil,   358,   358,   358,   358,   358,   358,   358,
   nil,   nil,   nil,   nil,   nil,   358,   358,   358,   358,   358,
   358,   358,   nil,   nil,   358,   nil,   nil,   nil,   nil,   nil,
   nil,   358,   nil,   nil,   358,   358,   358,   358,   358,   358,
   358,   358,   nil,   358,   358,   358,   nil,   358,   358,   358,
   358,   358,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   358,   nil,   nil,   358,   nil,   nil,   358,   358,   nil,
   nil,   358,   nil,   nil,   nil,   nil,   nil,   358,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   358,   nil,   nil,   nil,   nil,
   358,   358,   358,   358,   nil,   358,   358,   nil,   nil,   nil,
   358,   358,   nil,   385,   385,   385,   nil,   385,   358,   nil,
   358,   385,   385,   nil,   nil,   nil,   385,   nil,   385,   385,
   385,   385,   385,   385,   385,   nil,   nil,   nil,   nil,   nil,
   385,   385,   385,   385,   385,   385,   385,   nil,   nil,   385,
   nil,   nil,   nil,   nil,   nil,   nil,   385,   nil,   nil,   385,
   385,   385,   385,   385,   385,   385,   385,   nil,   385,   385,
   385,   nil,   385,   385,   385,   385,   385,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   385,   nil,   nil,   385,
   nil,   nil,   385,   385,   nil,   nil,   385,   nil,   nil,   nil,
   nil,   nil,   385,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   385,   nil,   nil,   nil,   nil,   385,   385,   385,   385,   nil,
   385,   385,   nil,   nil,   nil,   385,   385,   nil,   417,   417,
   417,   nil,   417,   385,   nil,   385,   417,   417,   nil,   nil,
   nil,   417,   nil,   417,   417,   417,   417,   417,   417,   417,
   nil,   nil,   nil,   nil,   nil,   417,   417,   417,   417,   417,
   417,   417,   nil,   nil,   417,   nil,   nil,   nil,   nil,   nil,
   nil,   417,   nil,   nil,   417,   417,   417,   417,   417,   417,
   417,   417,   417,   417,   417,   417,   nil,   417,   417,   417,
   417,   417,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   417,   nil,   nil,   417,   nil,   nil,   417,   417,   nil,
   nil,   417,   nil,   417,   nil,   417,   nil,   417,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   417,   nil,   nil,   nil,   nil,
   417,   417,   417,   417,   nil,   417,   417,   nil,   nil,   nil,
   417,   417,   nil,   419,   419,   419,   nil,   419,   417,   nil,
   417,   419,   419,   nil,   nil,   nil,   419,   nil,   419,   419,
   419,   419,   419,   419,   419,   nil,   nil,   nil,   nil,   nil,
   419,   419,   419,   419,   419,   419,   419,   nil,   nil,   419,
   nil,   nil,   nil,   nil,   nil,   nil,   419,   nil,   nil,   419,
   419,   419,   419,   419,   419,   419,   419,   nil,   419,   419,
   419,   nil,   419,   419,   419,   419,   419,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   419,   nil,   nil,   419,
   nil,   nil,   419,   419,   nil,   nil,   419,   nil,   nil,   nil,
   nil,   nil,   419,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   419,   nil,   nil,   nil,   nil,   419,   419,   419,   419,   nil,
   419,   419,   nil,   nil,   nil,   419,   419,   nil,   420,   420,
   420,   nil,   420,   419,   nil,   419,   420,   420,   nil,   nil,
   nil,   420,   nil,   420,   420,   420,   420,   420,   420,   420,
   nil,   nil,   nil,   nil,   nil,   420,   420,   420,   420,   420,
   420,   420,   nil,   nil,   420,   nil,   nil,   nil,   nil,   nil,
   nil,   420,   nil,   nil,   420,   420,   420,   420,   420,   420,
   420,   420,   nil,   420,   420,   420,   nil,   420,   420,   420,
   420,   420,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   420,   nil,   nil,   420,   nil,   nil,   420,   420,   nil,
   nil,   420,   nil,   nil,   nil,   nil,   nil,   420,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   420,   nil,   nil,   nil,   nil,
   420,   420,   420,   420,   nil,   420,   420,   nil,   nil,   nil,
   420,   420,   nil,   421,   421,   421,   nil,   421,   420,   nil,
   420,   421,   421,   nil,   nil,   nil,   421,   nil,   421,   421,
   421,   421,   421,   421,   421,   nil,   nil,   nil,   nil,   nil,
   421,   421,   421,   421,   421,   421,   421,   nil,   nil,   421,
   nil,   nil,   nil,   nil,   nil,   nil,   421,   nil,   nil,   421,
   421,   421,   421,   421,   421,   421,   421,   nil,   421,   421,
   421,   nil,   421,   421,   421,   421,   421,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   421,   nil,   nil,   421,
   nil,   nil,   421,   421,   nil,   nil,   421,   nil,   nil,   nil,
   nil,   nil,   421,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   421,   nil,   nil,   nil,   nil,   421,   421,   421,   421,   nil,
   421,   421,   nil,   nil,   nil,   421,   421,   nil,   458,   458,
   458,   nil,   458,   421,   nil,   421,   458,   458,   nil,   nil,
   nil,   458,   nil,   458,   458,   458,   458,   458,   458,   458,
   nil,   nil,   nil,   nil,   nil,   458,   458,   458,   458,   458,
   458,   458,   nil,   nil,   458,   nil,   nil,   nil,   nil,   nil,
   nil,   458,   nil,   nil,   458,   458,   458,   458,   458,   458,
   458,   458,   458,   458,   458,   458,   nil,   458,   458,   458,
   458,   458,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   458,   nil,   nil,   458,   nil,   nil,   458,   458,   nil,
   nil,   458,   nil,   nil,   nil,   458,   nil,   458,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   458,   nil,   nil,   nil,   nil,
   458,   458,   458,   458,   nil,   458,   458,   nil,   nil,   nil,
   458,   458,   nil,   464,   464,   464,   nil,   464,   458,   nil,
   458,   464,   464,   nil,   nil,   nil,   464,   nil,   464,   464,
   464,   464,   464,   464,   464,   nil,   nil,   nil,   nil,   nil,
   464,   464,   464,   464,   464,   464,   464,   nil,   nil,   464,
   nil,   nil,   nil,   nil,   nil,   nil,   464,   nil,   nil,   464,
   464,   464,   464,   464,   464,   464,   464,   464,   464,   464,
   464,   nil,   464,   464,   464,   464,   464,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   464,   nil,   nil,   464,
   nil,   nil,   464,   464,   nil,   nil,   464,   nil,   464,   nil,
   464,   nil,   464,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   464,   nil,   nil,   nil,   nil,   464,   464,   464,   464,   nil,
   464,   464,   nil,   nil,   nil,   464,   464,   nil,   466,   466,
   466,   nil,   466,   464,   nil,   464,   466,   466,   nil,   nil,
   nil,   466,   nil,   466,   466,   466,   466,   466,   466,   466,
   nil,   nil,   nil,   nil,   nil,   466,   466,   466,   466,   466,
   466,   466,   nil,   nil,   466,   nil,   nil,   nil,   nil,   nil,
   nil,   466,   nil,   nil,   466,   466,   466,   466,   466,   466,
   466,   466,   466,   466,   466,   466,   nil,   466,   466,   466,
   466,   466,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   466,   nil,   nil,   466,   nil,   nil,   466,   466,   nil,
   nil,   466,   nil,   nil,   nil,   466,   nil,   466,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   466,   nil,   nil,   nil,   nil,
   466,   466,   466,   466,   nil,   466,   466,   nil,   nil,   nil,
   466,   466,   nil,   468,   468,   468,   nil,   468,   466,   nil,
   466,   468,   468,   nil,   nil,   nil,   468,   nil,   468,   468,
   468,   468,   468,   468,   468,   nil,   nil,   nil,   nil,   nil,
   468,   468,   468,   468,   468,   468,   468,   nil,   nil,   468,
   nil,   nil,   nil,   nil,   nil,   nil,   468,   nil,   nil,   468,
   468,   468,   468,   468,   468,   468,   468,   nil,   468,   468,
   468,   nil,   468,   468,   468,   468,   468,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   468,   nil,   nil,   468,
   nil,   nil,   468,   468,   nil,   nil,   468,   nil,   nil,   nil,
   nil,   nil,   468,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   468,   nil,   nil,   nil,   nil,   468,   468,   468,   468,   nil,
   468,   468,   nil,   nil,   nil,   468,   468,   nil,   472,   472,
   472,   472,   472,   468,   nil,   468,   472,   472,   nil,   nil,
   nil,   472,   nil,   472,   472,   472,   472,   472,   472,   472,
   nil,   nil,   nil,   nil,   nil,   472,   472,   472,   472,   472,
   472,   472,   nil,   nil,   472,   nil,   nil,   nil,   nil,   nil,
   472,   472,   nil,   472,   472,   472,   472,   472,   472,   472,
   472,   472,   nil,   472,   472,   472,   nil,   472,   472,   472,
   472,   472,   408,   408,   408,   408,   408,   408,   408,   408,
   408,   408,   408,   nil,   408,   408,   nil,   nil,   408,   408,
   nil,   472,   nil,   nil,   472,   nil,   nil,   472,   472,   nil,
   nil,   472,   nil,   472,   408,   nil,   408,   472,   408,   408,
   408,   408,   408,   408,   408,   472,   408,   nil,   nil,   nil,
   472,   472,   472,   472,   nil,   472,   472,   nil,   nil,   nil,
   472,   472,   nil,   408,   nil,   nil,   nil,   472,   472,   nil,
   472,   479,   479,   479,   nil,   479,   nil,   nil,   nil,   479,
   479,   nil,   nil,   nil,   479,   nil,   479,   479,   479,   479,
   479,   479,   479,   nil,   nil,   nil,   nil,   nil,   479,   479,
   479,   479,   479,   479,   479,   nil,   nil,   479,   nil,   nil,
   nil,   nil,   nil,   nil,   479,   nil,   nil,   479,   479,   479,
   479,   479,   479,   479,   479,   nil,   479,   479,   479,   nil,
   479,   479,   nil,   864,   479,   864,   864,   864,   864,   864,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   479,   nil,   nil,   479,   nil,   nil,
   479,   479,   nil,   nil,   479,   nil,   nil,   nil,   nil,   nil,
   864,   864,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   864,
   864,   864,   864,   479,   479,   479,   479,   nil,   479,   479,
   nil,   nil,   nil,   479,   479,   nil,   481,   481,   481,   nil,
   481,   479,   nil,   479,   481,   481,   nil,   nil,   nil,   481,
   nil,   481,   481,   481,   481,   481,   481,   481,   nil,   nil,
   nil,   nil,   nil,   481,   481,   481,   481,   481,   481,   481,
   nil,   nil,   481,   nil,   nil,   nil,   nil,   nil,   nil,   481,
   nil,   nil,   481,   481,   481,   481,   481,   481,   481,   481,
   481,   481,   481,   481,   nil,   481,   481,   481,   481,   481,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   481,
   nil,   nil,   481,   nil,   nil,   481,   481,   nil,   nil,   481,
   nil,   481,   nil,   481,   nil,   481,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   481,   nil,   nil,   nil,   nil,   481,   481,
   481,   481,   nil,   481,   481,   nil,   nil,   nil,   481,   481,
   nil,   488,   488,   488,   nil,   488,   481,   nil,   481,   488,
   488,   nil,   nil,   nil,   488,   nil,   488,   488,   488,   488,
   488,   488,   488,   nil,   nil,   nil,   nil,   nil,   488,   488,
   488,   488,   488,   488,   488,   nil,   nil,   488,   nil,   nil,
   nil,   nil,   nil,   nil,   488,   nil,   nil,   488,   488,   488,
   488,   488,   488,   488,   488,   nil,   488,   488,   488,   nil,
   488,   488,   nil,   890,   488,   890,   890,   890,   890,   890,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   488,   nil,   nil,   488,   nil,   nil,
   488,   488,   nil,   nil,   488,   nil,   nil,   nil,   nil,   nil,
   890,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   890,
   890,   890,   890,   488,   488,   488,   488,   nil,   488,   488,
   nil,   nil,   nil,   488,   488,   nil,   491,   491,   491,   nil,
   491,   488,   nil,   488,   491,   491,   nil,   nil,   nil,   491,
   nil,   491,   491,   491,   491,   491,   491,   491,   nil,   nil,
   nil,   nil,   nil,   491,   491,   491,   491,   491,   491,   491,
   nil,   nil,   491,   nil,   nil,   nil,   nil,   nil,   nil,   491,
   nil,   nil,   491,   491,   491,   491,   491,   491,   491,   491,
   nil,   491,   491,   491,   nil,   491,   491,   491,   491,   491,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   491,
   nil,   nil,   491,   nil,   nil,   491,   491,   nil,   nil,   491,
   nil,   nil,   nil,   nil,   nil,   491,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   491,   nil,   nil,   nil,   nil,   491,   491,
   491,   491,   nil,   491,   491,   nil,   nil,   nil,   491,   491,
   nil,   492,   492,   492,   nil,   492,   491,   nil,   491,   492,
   492,   nil,   nil,   nil,   492,   nil,   492,   492,   492,   492,
   492,   492,   492,   nil,   nil,   nil,   nil,   nil,   492,   492,
   492,   492,   492,   492,   492,   nil,   nil,   492,   nil,   nil,
   nil,   nil,   nil,   nil,   492,   nil,   nil,   492,   492,   492,
   492,   492,   492,   492,   492,   nil,   492,   492,   492,   nil,
   492,   492,   492,   492,   492,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   492,   nil,   nil,   492,   nil,   nil,
   492,   492,   nil,   nil,   492,   nil,   nil,   nil,   nil,   nil,
   492,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   492,   nil,
   nil,   nil,   nil,   492,   492,   492,   492,   nil,   492,   492,
   nil,   nil,   nil,   492,   492,   nil,   493,   493,   493,   nil,
   493,   492,   nil,   492,   493,   493,   nil,   nil,   nil,   493,
   nil,   493,   493,   493,   493,   493,   493,   493,   nil,   nil,
   nil,   nil,   nil,   493,   493,   493,   493,   493,   493,   493,
   nil,   nil,   493,   nil,   nil,   nil,   nil,   nil,   nil,   493,
   nil,   nil,   493,   493,   493,   493,   493,   493,   493,   493,
   nil,   493,   493,   493,   nil,   493,   493,   493,   493,   493,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   493,
   nil,   nil,   493,   nil,   nil,   493,   493,   nil,   nil,   493,
   nil,   nil,   nil,   nil,   nil,   493,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   493,   nil,   nil,   nil,   nil,   493,   493,
   493,   493,   nil,   493,   493,   nil,   nil,   nil,   493,   493,
   nil,   497,   497,   497,   nil,   497,   493,   nil,   493,   497,
   497,   nil,   nil,   nil,   497,   nil,   497,   497,   497,   497,
   497,   497,   497,   nil,   nil,   nil,   nil,   nil,   497,   497,
   497,   497,   497,   497,   497,   nil,   nil,   497,   nil,   nil,
   nil,   nil,   nil,   nil,   497,   nil,   nil,   497,   497,   497,
   497,   497,   497,   497,   497,   nil,   497,   497,   497,   nil,
   497,   497,   497,   497,   497,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   497,   nil,   nil,   497,   nil,   nil,
   497,   497,   nil,   nil,   497,   nil,   nil,   nil,   nil,   nil,
   497,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   497,   nil,
   nil,   nil,   nil,   497,   497,   497,   497,   nil,   497,   497,
   nil,   nil,   nil,   497,   497,   nil,   499,   499,   499,   nil,
   499,   497,   nil,   497,   499,   499,   nil,   nil,   nil,   499,
   nil,   499,   499,   499,   499,   499,   499,   499,   nil,   nil,
   nil,   nil,   nil,   499,   499,   499,   499,   499,   499,   499,
   nil,   nil,   499,   nil,   nil,   nil,   nil,   nil,   nil,   499,
   nil,   nil,   499,   499,   499,   499,   499,   499,   499,   499,
   nil,   499,   499,   499,   nil,   499,   499,   499,   499,   499,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   499,
   nil,   nil,   499,   nil,   nil,   499,   499,   nil,   nil,   499,
   nil,   499,   nil,   nil,   nil,   499,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   499,   nil,   nil,   nil,   nil,   499,   499,
   499,   499,   nil,   499,   499,   nil,   nil,   nil,   499,   499,
   nil,   503,   503,   503,   nil,   503,   499,   nil,   499,   503,
   503,   nil,   nil,   nil,   503,   nil,   503,   503,   503,   503,
   503,   503,   503,   nil,   nil,   nil,   nil,   nil,   503,   503,
   503,   503,   503,   503,   503,   nil,   nil,   503,   nil,   nil,
   nil,   nil,   nil,   nil,   503,   nil,   nil,   503,   503,   503,
   503,   503,   503,   503,   503,   503,   503,   503,   503,   nil,
   503,   503,   503,   503,   503,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   503,   nil,   nil,   503,   nil,   nil,
   503,   503,   nil,   nil,   503,   nil,   503,   nil,   nil,   nil,
   503,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   503,   nil,
   nil,   nil,   nil,   503,   503,   503,   503,   nil,   503,   503,
   nil,   nil,   nil,   503,   503,   nil,   506,   506,   506,   nil,
   506,   503,   nil,   503,   506,   506,   nil,   nil,   nil,   506,
   nil,   506,   506,   506,   506,   506,   506,   506,   nil,   nil,
   nil,   nil,   nil,   506,   506,   506,   506,   506,   506,   506,
   nil,   nil,   506,   nil,   nil,   nil,   nil,   nil,   nil,   506,
   nil,   nil,   506,   506,   506,   506,   506,   506,   506,   506,
   506,   506,   506,   506,   nil,   506,   506,   506,   506,   506,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   506,
   nil,   nil,   506,   nil,   nil,   506,   506,   nil,   nil,   506,
   nil,   nil,   nil,   nil,   nil,   506,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   506,   nil,   nil,   nil,   nil,   506,   506,
   506,   506,   nil,   506,   506,   nil,   nil,   nil,   506,   506,
   nil,   520,   520,   520,   nil,   520,   506,   nil,   506,   520,
   520,   nil,   nil,   nil,   520,   nil,   520,   520,   520,   520,
   520,   520,   520,   nil,   nil,   nil,   nil,   nil,   520,   520,
   520,   520,   520,   520,   520,   nil,   nil,   520,   nil,   nil,
   nil,   nil,   nil,   nil,   520,   nil,   nil,   520,   520,   520,
   520,   520,   520,   520,   520,   nil,   520,   520,   520,   nil,
   520,   520,   520,   520,   520,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   520,   nil,   nil,   520,   nil,   nil,
   520,   520,   nil,   nil,   520,   nil,   520,   nil,   nil,   nil,
   520,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   520,   nil,
   nil,   nil,   nil,   520,   520,   520,   520,   nil,   520,   520,
   nil,   nil,   nil,   520,   520,   nil,   521,   521,   521,   nil,
   521,   520,   nil,   520,   521,   521,   nil,   nil,   nil,   521,
   nil,   521,   521,   521,   521,   521,   521,   521,   nil,   nil,
   nil,   nil,   nil,   521,   521,   521,   521,   521,   521,   521,
   nil,   nil,   521,   nil,   nil,   nil,   nil,   nil,   nil,   521,
   nil,   nil,   521,   521,   521,   521,   521,   521,   521,   521,
   521,   521,   521,   521,   nil,   521,   521,   521,   521,   521,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   521,
   nil,   nil,   521,   nil,   nil,   521,   521,   nil,   nil,   521,
   nil,   521,   nil,   521,   nil,   521,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   521,   nil,   nil,   nil,   nil,   521,   521,
   521,   521,   nil,   521,   521,   nil,   nil,   nil,   521,   521,
   nil,   531,   531,   531,   nil,   531,   521,   nil,   521,   531,
   531,   nil,   nil,   nil,   531,   nil,   531,   531,   531,   531,
   531,   531,   531,   nil,   nil,   nil,   nil,   nil,   531,   531,
   531,   531,   531,   531,   531,   nil,   nil,   531,   nil,   nil,
   nil,   nil,   nil,   nil,   531,   nil,   nil,   531,   531,   531,
   531,   531,   531,   531,   531,   531,   531,   531,   531,   nil,
   531,   531,   531,   531,   531,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   531,   nil,   nil,   531,   nil,   nil,
   531,   531,   nil,   nil,   531,   nil,   531,   nil,   531,   nil,
   531,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   531,   nil,
   nil,   nil,   nil,   531,   531,   531,   531,   nil,   531,   531,
   nil,   nil,   nil,   531,   531,   nil,   534,   534,   534,   nil,
   534,   531,   nil,   531,   534,   534,   nil,   nil,   nil,   534,
   nil,   534,   534,   534,   534,   534,   534,   534,   nil,   nil,
   nil,   nil,   nil,   534,   534,   534,   534,   534,   534,   534,
   nil,   nil,   534,   nil,   nil,   nil,   nil,   nil,   nil,   534,
   nil,   nil,   534,   534,   534,   534,   534,   534,   534,   534,
   nil,   534,   534,   534,   nil,   534,   534,   534,   534,   534,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   534,
   nil,   nil,   534,   nil,   nil,   534,   534,   nil,   nil,   534,
   nil,   nil,   nil,   nil,   nil,   534,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   534,   nil,   nil,   nil,   nil,   534,   534,
   534,   534,   nil,   534,   534,   nil,   nil,   nil,   534,   534,
   nil,   562,   562,   562,   nil,   562,   534,   nil,   534,   562,
   562,   nil,   nil,   nil,   562,   nil,   562,   562,   562,   562,
   562,   562,   562,   nil,   nil,   nil,   nil,   nil,   562,   562,
   562,   562,   562,   562,   562,   nil,   nil,   562,   nil,   nil,
   nil,   nil,   nil,   nil,   562,   nil,   nil,   562,   562,   562,
   562,   562,   562,   562,   562,   nil,   562,   562,   562,   nil,
   562,   562,   562,   562,   562,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   562,   nil,   nil,   562,   nil,   nil,
   562,   562,   nil,   nil,   562,   nil,   nil,   nil,   nil,   nil,
   562,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   562,   nil,
   nil,   nil,   nil,   562,   562,   562,   562,   nil,   562,   562,
   nil,   nil,   nil,   562,   562,   nil,   564,   564,   564,   nil,
   564,   562,   nil,   562,   564,   564,   nil,   nil,   nil,   564,
   nil,   564,   564,   564,   564,   564,   564,   564,   nil,   nil,
   nil,   nil,   nil,   564,   564,   564,   564,   564,   564,   564,
   nil,   nil,   564,   nil,   nil,   nil,   nil,   nil,   nil,   564,
   nil,   nil,   564,   564,   564,   564,   564,   564,   564,   564,
   nil,   564,   564,   564,   nil,   564,   564,   564,   564,   564,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   564,
   nil,   nil,   564,   nil,   nil,   564,   564,   nil,   nil,   564,
   nil,   564,   nil,   nil,   nil,   564,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   564,   nil,   nil,   nil,   nil,   564,   564,
   564,   564,   nil,   564,   564,   nil,   nil,   nil,   564,   564,
   nil,   565,   565,   565,   nil,   565,   564,   nil,   564,   565,
   565,   nil,   nil,   nil,   565,   nil,   565,   565,   565,   565,
   565,   565,   565,   nil,   nil,   nil,   nil,   nil,   565,   565,
   565,   565,   565,   565,   565,   nil,   nil,   565,   nil,   nil,
   nil,   nil,   nil,   nil,   565,   nil,   nil,   565,   565,   565,
   565,   565,   565,   565,   565,   nil,   565,   565,   565,   nil,
   565,   565,   565,   565,   565,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   565,   nil,   nil,   565,   nil,   nil,
   565,   565,   nil,   nil,   565,   nil,   nil,   nil,   nil,   nil,
   565,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   565,   nil,
   nil,   nil,   nil,   565,   565,   565,   565,   nil,   565,   565,
   nil,   nil,   nil,   565,   565,   nil,   568,   568,   568,   nil,
   568,   565,   nil,   565,   568,   568,   nil,   nil,   nil,   568,
   nil,   568,   568,   568,   568,   568,   568,   568,   nil,   nil,
   nil,   nil,   nil,   568,   568,   568,   568,   568,   568,   568,
   nil,   nil,   568,   nil,   nil,   nil,   nil,   nil,   nil,   568,
   nil,   nil,   568,   568,   568,   568,   568,   568,   568,   568,
   nil,   568,   568,   568,   nil,   568,   568,   568,   568,   568,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   568,
   nil,   nil,   568,   nil,   nil,   568,   568,   nil,   nil,   568,
   nil,   nil,   nil,   nil,   nil,   568,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   568,   nil,   nil,   nil,   nil,   568,   568,
   568,   568,   nil,   568,   568,   nil,   nil,   nil,   568,   568,
   nil,   569,   569,   569,   nil,   569,   568,   nil,   568,   569,
   569,   nil,   nil,   nil,   569,   nil,   569,   569,   569,   569,
   569,   569,   569,   nil,   nil,   nil,   nil,   nil,   569,   569,
   569,   569,   569,   569,   569,   nil,   nil,   569,   nil,   nil,
   nil,   nil,   nil,   nil,   569,   nil,   nil,   569,   569,   569,
   569,   569,   569,   569,   569,   nil,   569,   569,   569,   nil,
   569,   569,   569,   569,   569,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   569,   nil,   nil,   569,   nil,   nil,
   569,   569,   nil,   nil,   569,   nil,   nil,   nil,   nil,   nil,
   569,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   569,   nil,
   nil,   nil,   nil,   569,   569,   569,   569,   nil,   569,   569,
   nil,   nil,   nil,   569,   569,   nil,   573,   573,   573,   nil,
   573,   569,   nil,   569,   573,   573,   nil,   nil,   nil,   573,
   nil,   573,   573,   573,   573,   573,   573,   573,   nil,   nil,
   nil,   nil,   nil,   573,   573,   573,   573,   573,   573,   573,
   nil,   nil,   573,   nil,   nil,   nil,   nil,   nil,   nil,   573,
   nil,   nil,   573,   573,   573,   573,   573,   573,   573,   573,
   nil,   573,   573,   573,   nil,   573,   573,   573,   573,   573,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   573,
   nil,   nil,   573,   nil,   nil,   573,   573,   nil,   nil,   573,
   nil,   nil,   nil,   nil,   nil,   573,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   573,   nil,   nil,   nil,   nil,   573,   573,
   573,   573,   nil,   573,   573,   nil,   nil,   nil,   573,   573,
   nil,   574,   574,   574,   nil,   574,   573,   nil,   573,   574,
   574,   nil,   nil,   nil,   574,   nil,   574,   574,   574,   574,
   574,   574,   574,   nil,   nil,   nil,   nil,   nil,   574,   574,
   574,   574,   574,   574,   574,   nil,   nil,   574,   nil,   nil,
   nil,   nil,   nil,   nil,   574,   nil,   nil,   574,   574,   574,
   574,   574,   574,   574,   574,   nil,   574,   574,   574,   nil,
   574,   574,   574,   574,   574,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   574,   nil,   nil,   574,   nil,   nil,
   574,   574,   nil,   nil,   574,   nil,   nil,   nil,   nil,   nil,
   574,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   574,   nil,
   nil,   nil,   nil,   574,   574,   574,   574,   nil,   574,   574,
   nil,   nil,   nil,   574,   574,   nil,   608,   608,   608,   nil,
   608,   574,   nil,   574,   608,   608,   nil,   nil,   nil,   608,
   nil,   608,   608,   608,   608,   608,   608,   608,   nil,   nil,
   nil,   nil,   nil,   608,   608,   608,   608,   608,   608,   608,
   nil,   nil,   608,   nil,   nil,   nil,   nil,   nil,   nil,   608,
   nil,   nil,   608,   608,   608,   608,   608,   608,   608,   608,
   nil,   608,   608,   608,   nil,   608,   608,   608,   608,   608,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   608,
   nil,   nil,   608,   nil,   nil,   608,   608,   nil,   nil,   608,
   nil,   nil,   nil,   nil,   nil,   608,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   608,   nil,   nil,   nil,   nil,   608,   608,
   608,   608,   nil,   608,   608,   nil,   nil,   nil,   608,   608,
   nil,   613,   613,   613,   nil,   613,   608,   nil,   608,   613,
   613,   nil,   nil,   nil,   613,   nil,   613,   613,   613,   613,
   613,   613,   613,   nil,   nil,   nil,   nil,   nil,   613,   613,
   613,   613,   613,   613,   613,   nil,   nil,   613,   nil,   nil,
   nil,   nil,   nil,   nil,   613,   nil,   nil,   613,   613,   613,
   613,   613,   613,   613,   613,   nil,   613,   613,   613,   nil,
   613,   613,   nil,   913,   613,   913,   913,   913,   913,   913,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   613,   nil,   nil,   613,   nil,   nil,
   613,   613,   nil,   nil,   613,   nil,   nil,   nil,   nil,   nil,
   913,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   913,
   913,   913,   913,   613,   613,   613,   613,   nil,   613,   613,
   nil,   nil,   nil,   613,   613,   nil,   624,   624,   624,   nil,
   624,   613,   nil,   613,   624,   624,   nil,   nil,   nil,   624,
   nil,   624,   624,   624,   624,   624,   624,   624,   nil,   nil,
   nil,   nil,   nil,   624,   624,   624,   624,   624,   624,   624,
   nil,   nil,   624,   nil,   nil,   nil,   nil,   nil,   nil,   624,
   nil,   nil,   624,   624,   624,   624,   624,   624,   624,   624,
   nil,   624,   624,   624,   nil,   624,   624,   nil,   nil,   624,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   624,
   nil,   nil,   624,   nil,   nil,   624,   624,   nil,   nil,   624,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   624,   624,
   624,   624,   nil,   624,   624,   nil,   nil,   nil,   624,   624,
   nil,   677,   677,   677,   nil,   677,   624,   nil,   624,   677,
   677,   nil,   nil,   nil,   677,   nil,   677,   677,   677,   677,
   677,   677,   677,   nil,   nil,   nil,   nil,   nil,   677,   677,
   677,   677,   677,   677,   677,   nil,   nil,   677,   nil,   nil,
   nil,   nil,   nil,   nil,   677,   nil,   nil,   677,   677,   677,
   677,   677,   677,   677,   677,   nil,   677,   677,   677,   nil,
   677,   677,   677,   677,   677,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   677,   nil,   nil,   677,   nil,   nil,
   677,   677,   nil,   nil,   677,   nil,   nil,   nil,   nil,   nil,
   677,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   677,   nil,
   nil,   nil,   nil,   677,   677,   677,   677,   nil,   677,   677,
   nil,   nil,   nil,   677,   677,   nil,   705,   705,   705,   nil,
   705,   677,   nil,   677,   705,   705,   nil,   nil,   nil,   705,
   nil,   705,   705,   705,   705,   705,   705,   705,   nil,   nil,
   nil,   nil,   nil,   705,   705,   705,   705,   705,   705,   705,
   nil,   nil,   705,   nil,   nil,   nil,   nil,   nil,   nil,   705,
   nil,   nil,   705,   705,   705,   705,   705,   705,   705,   705,
   nil,   705,   705,   705,   nil,   705,   705,   705,   705,   705,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   705,
   nil,   nil,   705,   nil,   nil,   705,   705,   nil,   nil,   705,
   nil,   nil,   nil,   nil,   nil,   705,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   705,   nil,   nil,   nil,   nil,   705,   705,
   705,   705,   nil,   705,   705,   nil,   nil,   nil,   705,   705,
   nil,   707,   707,   707,   nil,   707,   705,   nil,   705,   707,
   707,   nil,   nil,   nil,   707,   nil,   707,   707,   707,   707,
   707,   707,   707,   nil,   nil,   nil,   nil,   nil,   707,   707,
   707,   707,   707,   707,   707,   nil,   nil,   707,   nil,   nil,
   nil,   nil,   nil,   nil,   707,   nil,   nil,   707,   707,   707,
   707,   707,   707,   707,   707,   nil,   707,   707,   707,   nil,
   707,   707,   707,   707,   707,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   707,   nil,   nil,   707,   nil,   nil,
   707,   707,   nil,   nil,   707,   nil,   nil,   nil,   nil,   nil,
   707,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   707,   nil,
   nil,   nil,   nil,   707,   707,   707,   707,   nil,   707,   707,
   nil,   nil,   nil,   707,   707,   nil,   719,   719,   719,   nil,
   719,   707,   nil,   707,   719,   719,   nil,   nil,   nil,   719,
   nil,   719,   719,   719,   719,   719,   719,   719,   nil,   nil,
   nil,   nil,   nil,   719,   719,   719,   719,   719,   719,   719,
   nil,   nil,   719,   nil,   nil,   nil,   nil,   nil,   nil,   719,
   nil,   nil,   719,   719,   719,   719,   719,   719,   719,   719,
   nil,   719,   719,   719,   nil,   719,   719,   719,   719,   719,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   719,
   nil,   nil,   719,   nil,   nil,   719,   719,   nil,   nil,   719,
   nil,   nil,   nil,   nil,   nil,   719,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   719,   nil,   nil,   nil,   nil,   719,   719,
   719,   719,   nil,   719,   719,   nil,   nil,   nil,   719,   719,
   nil,   720,   720,   720,   nil,   720,   719,   nil,   719,   720,
   720,   nil,   nil,   nil,   720,   nil,   720,   720,   720,   720,
   720,   720,   720,   nil,   nil,   nil,   nil,   nil,   720,   720,
   720,   720,   720,   720,   720,   nil,   nil,   720,   nil,   nil,
   nil,   nil,   nil,   nil,   720,   nil,   nil,   720,   720,   720,
   720,   720,   720,   720,   720,   nil,   720,   720,   720,   nil,
   720,   720,   720,   720,   720,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   720,   nil,   nil,   720,   nil,   nil,
   720,   720,   nil,   nil,   720,   nil,   nil,   nil,   nil,   nil,
   720,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   720,   nil,
   nil,   nil,   nil,   720,   720,   720,   720,   nil,   720,   720,
   nil,   nil,   nil,   720,   720,   nil,   721,   721,   721,   nil,
   721,   720,   nil,   720,   721,   721,   nil,   nil,   nil,   721,
   nil,   721,   721,   721,   721,   721,   721,   721,   nil,   nil,
   nil,   nil,   nil,   721,   721,   721,   721,   721,   721,   721,
   nil,   nil,   721,   nil,   nil,   nil,   nil,   nil,   nil,   721,
   nil,   nil,   721,   721,   721,   721,   721,   721,   721,   721,
   nil,   721,   721,   721,   nil,   721,   721,   721,   721,   721,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   721,
   nil,   nil,   721,   nil,   nil,   721,   721,   nil,   nil,   721,
   nil,   nil,   nil,   nil,   nil,   721,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   721,   nil,   nil,   nil,   nil,   721,   721,
   721,   721,   nil,   721,   721,   nil,   nil,   nil,   721,   721,
   nil,   722,   722,   722,   nil,   722,   721,   nil,   721,   722,
   722,   nil,   nil,   nil,   722,   nil,   722,   722,   722,   722,
   722,   722,   722,   nil,   nil,   nil,   nil,   nil,   722,   722,
   722,   722,   722,   722,   722,   nil,   nil,   722,   nil,   nil,
   nil,   nil,   nil,   nil,   722,   nil,   nil,   722,   722,   722,
   722,   722,   722,   722,   722,   nil,   722,   722,   722,   nil,
   722,   722,   722,   722,   722,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   722,   nil,   nil,   722,   nil,   nil,
   722,   722,   nil,   nil,   722,   nil,   nil,   nil,   nil,   nil,
   722,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   722,   nil,
   nil,   nil,   nil,   722,   722,   722,   722,   nil,   722,   722,
   nil,   nil,   nil,   722,   722,   nil,   724,   724,   724,   nil,
   724,   722,   nil,   722,   724,   724,   nil,   nil,   nil,   724,
   nil,   724,   724,   724,   724,   724,   724,   724,   nil,   nil,
   nil,   nil,   nil,   724,   724,   724,   724,   724,   724,   724,
   nil,   nil,   724,   nil,   nil,   nil,   nil,   nil,   nil,   724,
   nil,   nil,   724,   724,   724,   724,   724,   724,   724,   724,
   nil,   724,   724,   724,   nil,   724,   724,   724,   724,   724,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   724,
   nil,   nil,   724,   nil,   nil,   724,   724,   nil,   nil,   724,
   nil,   nil,   nil,   nil,   nil,   724,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   724,   nil,   nil,   nil,   nil,   724,   724,
   724,   724,   nil,   724,   724,   nil,   nil,   nil,   724,   724,
   nil,   736,   736,   736,   nil,   736,   724,   nil,   724,   736,
   736,   nil,   nil,   nil,   736,   nil,   736,   736,   736,   736,
   736,   736,   736,   nil,   nil,   nil,   nil,   nil,   736,   736,
   736,   736,   736,   736,   736,   nil,   nil,   736,   nil,   nil,
   nil,   nil,   nil,   nil,   736,   nil,   nil,   736,   736,   736,
   736,   736,   736,   736,   736,   736,   736,   736,   736,   nil,
   736,   736,   736,   736,   736,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   736,   nil,   nil,   736,   nil,   nil,
   736,   736,   nil,   nil,   736,   nil,   736,   nil,   736,   nil,
   736,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   736,   nil,
   nil,   nil,   nil,   736,   736,   736,   736,   nil,   736,   736,
   nil,   nil,   nil,   736,   736,   nil,   743,   743,   743,   nil,
   743,   736,   nil,   736,   743,   743,   nil,   nil,   nil,   743,
   nil,   743,   743,   743,   743,   743,   743,   743,   nil,   nil,
   nil,   nil,   nil,   743,   743,   743,   743,   743,   743,   743,
   nil,   nil,   743,   nil,   nil,   nil,   nil,   nil,   nil,   743,
   nil,   nil,   743,   743,   743,   743,   743,   743,   743,   743,
   743,   743,   743,   743,   nil,   743,   743,   743,   743,   743,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   743,
   nil,   nil,   743,   nil,   nil,   743,   743,   nil,   nil,   743,
   nil,   743,   nil,   743,   nil,   743,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   743,   nil,   nil,   nil,   nil,   743,   743,
   743,   743,   nil,   743,   743,   nil,   nil,   nil,   743,   743,
   nil,   754,   754,   754,   nil,   754,   743,   nil,   743,   754,
   754,   nil,   nil,   nil,   754,   nil,   754,   754,   754,   754,
   754,   754,   754,   nil,   nil,   nil,   nil,   nil,   754,   754,
   754,   754,   754,   754,   754,   nil,   nil,   754,   nil,   nil,
   nil,   nil,   nil,   nil,   754,   nil,   nil,   754,   754,   754,
   754,   754,   754,   754,   754,   nil,   754,   754,   754,   nil,
   754,   754,   nil,   nil,   754,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   754,   nil,   nil,   754,   nil,   nil,
   754,   754,   nil,   nil,   754,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   754,   754,   754,   754,   nil,   754,   754,
   nil,   nil,   nil,   754,   754,   nil,   768,   768,   768,   nil,
   768,   754,   nil,   754,   768,   768,   nil,   nil,   nil,   768,
   nil,   768,   768,   768,   768,   768,   768,   768,   nil,   nil,
   nil,   nil,   nil,   768,   768,   768,   768,   768,   768,   768,
   nil,   nil,   768,   nil,   nil,   nil,   nil,   nil,   nil,   768,
   nil,   nil,   768,   768,   768,   768,   768,   768,   768,   768,
   nil,   768,   768,   768,   nil,   768,   768,   768,   768,   768,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   768,
   nil,   nil,   768,   nil,   nil,   768,   768,   nil,   nil,   768,
   nil,   nil,   nil,   nil,   nil,   768,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   768,   nil,   nil,   nil,   nil,   768,   768,
   768,   768,   nil,   768,   768,   nil,   nil,   nil,   768,   768,
   nil,   781,   781,   781,   nil,   781,   768,   nil,   768,   781,
   781,   nil,   nil,   nil,   781,   nil,   781,   781,   781,   781,
   781,   781,   781,   nil,   nil,   nil,   nil,   nil,   781,   781,
   781,   781,   781,   781,   781,   nil,   nil,   781,   nil,   nil,
   nil,   nil,   nil,   nil,   781,   nil,   nil,   781,   781,   781,
   781,   781,   781,   781,   781,   nil,   781,   781,   781,   nil,
   781,   781,   781,   781,   781,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   781,   nil,   nil,   781,   nil,   nil,
   781,   781,   nil,   nil,   781,   nil,   nil,   nil,   nil,   nil,
   781,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   781,   nil,
   nil,   nil,   nil,   781,   781,   781,   781,   nil,   781,   781,
   nil,   nil,   nil,   781,   781,   nil,   786,   786,   786,   nil,
   786,   781,   nil,   781,   786,   786,   nil,   nil,   nil,   786,
   nil,   786,   786,   786,   786,   786,   786,   786,   nil,   nil,
   nil,   nil,   nil,   786,   786,   786,   786,   786,   786,   786,
   nil,   nil,   786,   nil,   nil,   nil,   nil,   nil,   nil,   786,
   nil,   nil,   786,   786,   786,   786,   786,   786,   786,   786,
   nil,   786,   786,   786,   nil,   786,   786,   786,   786,   786,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   786,
   nil,   nil,   786,   nil,   nil,   786,   786,   nil,   nil,   786,
   nil,   786,   nil,   nil,   nil,   786,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   786,   nil,   nil,   nil,   nil,   786,   786,
   786,   786,   nil,   786,   786,   nil,   nil,   nil,   786,   786,
   nil,   803,   803,   803,   nil,   803,   786,   nil,   786,   803,
   803,   nil,   nil,   nil,   803,   nil,   803,   803,   803,   803,
   803,   803,   803,   nil,   nil,   nil,   nil,   nil,   803,   803,
   803,   803,   803,   803,   803,   nil,   nil,   803,   nil,   nil,
   nil,   nil,   nil,   nil,   803,   nil,   nil,   803,   803,   803,
   803,   803,   803,   803,   803,   nil,   803,   803,   803,   nil,
   803,   803,   803,   803,   803,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   803,   nil,   nil,   803,   nil,   nil,
   803,   803,   nil,   nil,   803,   nil,   nil,   nil,   nil,   nil,
   803,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   803,   nil,
   nil,   nil,   nil,   803,   803,   803,   803,   nil,   803,   803,
   nil,   nil,   nil,   803,   803,   nil,   817,   817,   817,   nil,
   817,   803,   nil,   803,   817,   817,   nil,   nil,   nil,   817,
   nil,   817,   817,   817,   817,   817,   817,   817,   nil,   nil,
   nil,   nil,   nil,   817,   817,   817,   817,   817,   817,   817,
   nil,   nil,   817,   nil,   nil,   nil,   nil,   nil,   nil,   817,
   nil,   nil,   817,   817,   817,   817,   817,   817,   817,   817,
   nil,   817,   817,   817,   nil,   817,   817,   nil,   nil,   817,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   817,
   nil,   nil,   817,   nil,   nil,   817,   817,   nil,   nil,   817,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   817,   817,
   817,   817,   nil,   817,   817,   nil,   nil,   nil,   817,   817,
   nil,   878,   878,   878,   nil,   878,   817,   nil,   817,   878,
   878,   nil,   nil,   nil,   878,   nil,   878,   878,   878,   878,
   878,   878,   878,   nil,   nil,   nil,   nil,   nil,   878,   878,
   878,   878,   878,   878,   878,   nil,   nil,   878,   nil,   nil,
   nil,   nil,   nil,   nil,   878,   nil,   nil,   878,   878,   878,
   878,   878,   878,   878,   878,   878,   878,   878,   878,   nil,
   878,   878,   878,   878,   878,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   878,   nil,   nil,   878,   nil,   nil,
   878,   878,   nil,   nil,   878,   nil,   878,   nil,   878,   nil,
   878,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   878,   nil,
   nil,   nil,   nil,   878,   878,   878,   878,   nil,   878,   878,
   nil,   nil,   nil,   878,   878,   nil,   881,   881,   881,   nil,
   881,   878,   nil,   878,   881,   881,   nil,   nil,   nil,   881,
   nil,   881,   881,   881,   881,   881,   881,   881,   nil,   nil,
   nil,   nil,   nil,   881,   881,   881,   881,   881,   881,   881,
   nil,   nil,   881,   nil,   nil,   nil,   nil,   nil,   nil,   881,
   nil,   nil,   881,   881,   881,   881,   881,   881,   881,   881,
   nil,   881,   881,   881,   nil,   881,   881,   881,   881,   881,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   881,
   nil,   nil,   881,   nil,   nil,   881,   881,   nil,   nil,   881,
   nil,   881,   nil,   881,   nil,   881,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   881,   nil,   nil,   nil,   nil,   881,   881,
   881,   881,   nil,   881,   881,   nil,   nil,   nil,   881,   881,
   nil,   883,   883,   883,   nil,   883,   881,   nil,   881,   883,
   883,   nil,   nil,   nil,   883,   nil,   883,   883,   883,   883,
   883,   883,   883,   nil,   nil,   nil,   nil,   nil,   883,   883,
   883,   883,   883,   883,   883,   nil,   nil,   883,   nil,   nil,
   nil,   nil,   nil,   nil,   883,   nil,   nil,   883,   883,   883,
   883,   883,   883,   883,   883,   883,   883,   883,   883,   nil,
   883,   883,   883,   883,   883,   447,   447,   447,   447,   447,
   447,   447,   447,   447,   447,   447,   nil,   447,   447,   nil,
   nil,   447,   447,   nil,   883,   nil,   nil,   883,   nil,   nil,
   883,   883,   nil,   nil,   883,   nil,   883,   447,   883,   447,
   883,   447,   447,   447,   447,   447,   447,   447,   883,   447,
   nil,   nil,   nil,   883,   883,   883,   883,   nil,   883,   883,
   nil,   nil,   nil,   883,   883,   447,   447,    25,   nil,   nil,
   nil,   883,   nil,   883,    25,    25,    25,   nil,   nil,    25,
    25,    25,   nil,    25,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    25,    25,    25,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    25,    25,   nil,    25,    25,    25,    25,
    25,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    25,    25,    25,    25,    25,    25,    25,
    25,    25,    25,    25,    25,    25,    25,   nil,   nil,    25,
    25,    25,   nil,   nil,    25,   nil,    25,    25,   nil,   nil,
    25,    25,   nil,    25,   nil,    25,   nil,    25,   nil,    25,
    25,    25,    25,    25,    25,    25,    26,    25,    25,    25,
   nil,   nil,   nil,    26,    26,    26,   nil,   nil,    26,    26,
    26,   nil,    26,    25,    25,   nil,    25,   nil,    25,   nil,
   nil,   nil,    26,    26,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    26,    26,   nil,    26,    26,    26,    26,    26,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    26,    26,    26,    26,    26,    26,    26,    26,
    26,    26,    26,    26,    26,    26,   nil,   nil,    26,    26,
    26,   nil,   nil,    26,   nil,    26,    26,   nil,   nil,    26,
    26,   nil,    26,   nil,    26,   nil,    26,   nil,    26,    26,
    26,    26,    26,    26,    26,   396,    26,   nil,    26,   nil,
   nil,   nil,   396,   396,   396,   nil,   nil,   396,   396,   396,
   nil,   396,    26,    26,   nil,    26,   nil,    26,   nil,   nil,
   396,   396,   396,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   396,   396,   nil,   396,   396,   396,   396,   396,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   396,   396,   396,   396,   396,   396,   396,   396,   396,
   396,   396,   396,   396,   396,   nil,   nil,   396,   396,   396,
   nil,   nil,   396,   nil,   396,   396,   nil,   nil,   396,   396,
   nil,   396,   nil,   396,   nil,   396,   nil,   396,   396,   396,
   396,   396,   396,   396,   449,   396,   396,   396,   nil,   nil,
   nil,   449,   449,   449,   nil,   nil,   449,   449,   449,   nil,
   449,   396,   396,   nil,   396,   nil,   396,   nil,   nil,   nil,
   449,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   449,   449,   nil,   449,   449,   449,   449,   449,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   452,
   nil,   nil,   nil,   nil,   nil,   nil,   452,   452,   452,   nil,
   nil,   452,   452,   452,   nil,   452,   nil,   nil,   nil,   nil,
   nil,   449,   nil,   nil,   nil,   452,   nil,   nil,   449,   nil,
   nil,   nil,   nil,   449,   449,   452,   452,   nil,   452,   452,
   452,   452,   452,   nil,   nil,   nil,   nil,   nil,   202,   202,
   nil,   nil,   202,   nil,   nil,   nil,   449,   nil,   nil,   nil,
   202,   202,   nil,   202,   202,   202,   202,   202,   202,   202,
   449,   nil,   202,   202,   nil,   449,   452,   202,   202,   202,
   202,   nil,   nil,   452,   nil,   nil,   202,   nil,   452,   452,
   nil,   nil,   nil,   nil,   202,   202,   nil,   202,   202,   202,
   202,   202,   202,   202,   202,   202,   202,   202,   nil,   nil,
   202,   452,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   452,   nil,   nil,   nil,   nil,
   452,     8,     8,     8,     8,     8,     8,     8,     8,     8,
     8,     8,     8,     8,     8,     8,     8,     8,     8,     8,
     8,     8,     8,     8,     8,   nil,   nil,   nil,     8,     8,
     8,     8,     8,     8,     8,     8,     8,     8,   nil,   nil,
   nil,   nil,   nil,     8,     8,     8,     8,     8,     8,     8,
     8,     8,     8,   nil,     8,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,     8,     8,   nil,     8,     8,     8,     8,     8,
     8,     8,   nil,   nil,     8,     8,   nil,   nil,   nil,     8,
     8,     8,     8,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,     8,     8,   nil,     8,
     8,     8,     8,     8,     8,     8,     8,     8,     8,     8,
   nil,   nil,     8,     8,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,     8,     9,     9,     9,     9,     9,
     9,     9,     9,     9,     9,     9,     9,     9,     9,     9,
     9,     9,     9,     9,     9,     9,     9,     9,     9,   nil,
   nil,   nil,     9,     9,     9,     9,     9,     9,     9,     9,
     9,     9,   nil,   nil,   nil,   nil,   nil,     9,     9,     9,
     9,     9,     9,     9,     9,     9,   nil,   nil,     9,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,     9,     9,   nil,     9,
     9,     9,     9,     9,     9,     9,   nil,   nil,     9,     9,
   nil,   nil,   nil,     9,     9,     9,     9,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
     9,     9,   nil,     9,     9,     9,     9,     9,     9,     9,
     9,     9,     9,     9,   nil,   nil,     9,     9,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,     9,   375,
   375,   375,   375,   375,   375,   375,   375,   375,   375,   375,
   375,   375,   375,   375,   375,   375,   375,   375,   375,   375,
   375,   375,   375,   nil,   nil,   nil,   375,   375,   375,   375,
   375,   375,   375,   375,   375,   375,   nil,   nil,   nil,   nil,
   nil,   375,   375,   375,   375,   375,   375,   375,   375,   375,
   nil,   nil,   375,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   375,   375,   nil,   375,   375,   375,   375,   375,   375,   375,
   nil,   nil,   375,   375,   nil,   nil,   nil,   375,   375,   375,
   375,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   375,   375,   nil,   375,   375,   375,
   375,   375,   375,   375,   375,   375,   375,   375,   nil,   nil,
   375,   375,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   375,   560,   560,   560,   560,   560,   560,   560,
   560,   560,   560,   560,   560,   560,   560,   560,   560,   560,
   560,   560,   560,   560,   560,   560,   560,   nil,   nil,   nil,
   560,   560,   560,   560,   560,   560,   560,   560,   560,   560,
   nil,   nil,   nil,   nil,   nil,   560,   560,   560,   560,   560,
   560,   560,   560,   560,   nil,   nil,   560,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   560,   560,   nil,   560,   560,   560,
   560,   560,   560,   560,   nil,   nil,   560,   560,   nil,   nil,
   nil,   560,   560,   560,   560,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   560,   560,
   nil,   560,   560,   560,   560,   560,   560,   560,   560,   560,
   560,   560,   nil,   nil,   560,   560,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   560,    66,    66,    66,
    66,    66,    66,    66,    66,    66,    66,    66,    66,    66,
    66,    66,    66,    66,    66,    66,    66,    66,    66,    66,
    66,   nil,   nil,   nil,    66,    66,    66,    66,    66,    66,
    66,    66,    66,    66,   nil,   nil,   nil,   nil,   nil,    66,
    66,    66,    66,    66,    66,    66,    66,    66,    66,    66,
    66,   nil,    66,   nil,   nil,   nil,   nil,   nil,    66,    66,
   nil,    66,    66,    66,    66,    66,    66,    66,   nil,   nil,
    66,    66,   nil,   nil,   nil,    66,    66,    66,    66,   nil,
   nil,   nil,   nil,   nil,    66,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    66,    66,   nil,    66,    66,    66,    66,    66,
    66,    66,    66,    66,    66,    66,   nil,   nil,    66,   690,
   690,   690,   690,   690,   690,   690,   690,   690,   690,   690,
   690,   690,   690,   690,   690,   690,   690,   690,   690,   690,
   690,   690,   690,   nil,   nil,   nil,   690,   690,   690,   690,
   690,   690,   690,   690,   690,   690,   nil,   nil,   nil,   nil,
   nil,   690,   690,   690,   690,   690,   690,   690,   690,   690,
   nil,   nil,   690,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   690,   690,   nil,   690,   690,   690,   690,   690,   690,   690,
   nil,   nil,   690,   690,   nil,   nil,   nil,   690,   690,   690,
   690,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   690,   690,   nil,   690,   690,   690,
   690,   690,   690,   690,   690,   690,   690,   690,   203,   203,
   690,   nil,   203,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   203,   203,   nil,   203,   203,   203,   203,   203,   203,   203,
   nil,   nil,   203,   203,   nil,   nil,   nil,   203,   203,   203,
   203,   nil,   nil,   nil,   nil,   nil,   203,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   203,   203,   nil,   203,   203,   203,
   203,   203,   203,   203,   203,   203,   203,   203,   249,   249,
   203,   nil,   249,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   249,   249,   nil,   249,   249,   249,   249,   249,   249,   249,
   nil,   nil,   249,   249,   nil,   nil,   nil,   249,   249,   249,
   249,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   249,   249,   nil,   249,   249,   249,
   249,   249,   249,   249,   249,   249,   249,   249,   250,   250,
   249,   nil,   250,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   250,   250,   nil,   250,   250,   250,   250,   250,   250,   250,
   nil,   nil,   250,   250,   nil,   nil,   nil,   250,   250,   250,
   250,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   250,   250,   nil,   250,   250,   250,
   250,   250,   250,   250,   250,   250,   250,   250,   415,   415,
   250,   nil,   415,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   415,   415,   nil,   415,   415,   415,   415,   415,   415,   415,
   nil,   nil,   415,   415,   nil,   nil,   nil,   415,   415,   415,
   415,   nil,   nil,   nil,   nil,   nil,   415,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   415,   415,   nil,   415,   415,   415,
   415,   415,   415,   415,   415,   415,   415,   415,   416,   416,
   415,   nil,   416,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   416,   416,   nil,   416,   416,   416,   416,   416,   416,   416,
   nil,   nil,   416,   416,   nil,   nil,   nil,   416,   416,   416,
   416,   nil,   nil,   nil,   nil,   nil,   416,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   416,   416,   nil,   416,   416,   416,
   416,   416,   416,   416,   416,   416,   416,   416,   482,   482,
   416,   nil,   482,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   482,   482,   nil,   482,   482,   482,   482,   482,   482,   482,
   nil,   nil,   482,   482,   nil,   nil,   nil,   482,   482,   482,
   482,   nil,   nil,   nil,   nil,   nil,   482,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   482,   482,   nil,   482,   482,   482,
   482,   482,   482,   482,   482,   482,   482,   482,   483,   483,
   482,   nil,   483,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   483,   483,   nil,   483,   483,   483,   483,   483,   483,   483,
   nil,   nil,   483,   483,   nil,   nil,   nil,   483,   483,   483,
   483,   nil,   nil,   nil,   nil,   nil,   483,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   483,   483,   nil,   483,   483,   483,
   483,   483,   483,   483,   483,   483,   483,   483,   494,   494,
   483,   nil,   494,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   494,   494,   nil,   494,   494,   494,   494,   494,   494,   494,
   nil,   nil,   494,   494,   nil,   nil,   nil,   494,   494,   494,
   494,   nil,   nil,   nil,   nil,   nil,   494,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   494,   494,   nil,   494,   494,   494,
   494,   494,   494,   494,   494,   494,   494,   494,   495,   495,
   494,   nil,   495,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   495,   495,   nil,   495,   495,   495,   495,   495,   495,   495,
   nil,   nil,   495,   495,   nil,   nil,   nil,   495,   495,   495,
   495,   nil,   nil,   nil,   nil,   nil,   495,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   495,   495,   nil,   495,   495,   495,
   495,   495,   495,   495,   495,   495,   495,   495,   522,   522,
   495,   nil,   522,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   522,   522,   nil,   522,   522,   522,   522,   522,   522,   522,
   nil,   nil,   522,   522,   nil,   nil,   nil,   522,   522,   522,
   522,   nil,   nil,   nil,   nil,   nil,   522,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   522,   522,   nil,   522,   522,   522,
   522,   522,   522,   522,   522,   522,   522,   522,   523,   523,
   522,   nil,   523,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   523,   523,   nil,   523,   523,   523,   523,   523,   523,   523,
   nil,   nil,   523,   523,   nil,   nil,   nil,   523,   523,   523,
   523,   nil,   nil,   nil,   nil,   nil,   523,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   523,   523,   nil,   523,   523,   523,
   523,   523,   523,   523,   523,   523,   523,   523,   529,   529,
   523,   nil,   529,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   529,   529,   nil,   529,   529,   529,   529,   529,   529,   529,
   nil,   nil,   529,   529,   nil,   nil,   nil,   529,   529,   529,
   529,   nil,   nil,   nil,   nil,   nil,   529,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   529,   529,   nil,   529,   529,   529,
   529,   529,   529,   529,   529,   529,   529,   529,   530,   530,
   529,   nil,   530,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   530,   530,   nil,   530,   530,   530,   530,   530,   530,   530,
   nil,   nil,   530,   530,   nil,   nil,   nil,   530,   530,   530,
   530,   nil,   nil,   nil,   nil,   nil,   530,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   530,   530,   nil,   530,   530,   530,
   530,   530,   530,   530,   530,   530,   530,   530,   884,   884,
   530,   nil,   884,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   884,   884,   nil,   884,   884,   884,   884,   884,   884,   884,
   nil,   nil,   884,   884,   nil,   nil,   nil,   884,   884,   884,
   884,   nil,   nil,   nil,   nil,   nil,   884,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   884,   884,   nil,   884,   884,   884,
   884,   884,   884,   884,   884,   884,   884,   884,   885,   885,
   884,   nil,   885,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   885,   885,   nil,   885,   885,   885,   885,   885,   885,   885,
   nil,   nil,   885,   885,   nil,   nil,   nil,   885,   885,   885,
   885,   nil,   nil,   nil,   nil,   nil,   885,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   885,   885,   nil,   885,   885,   885,
   885,   885,   885,   885,   885,   885,   885,   885,   922,   922,
   885,   nil,   922,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   922,   922,   nil,   922,   922,   922,   922,   922,   922,   922,
   nil,   nil,   922,   922,   nil,   nil,   nil,   922,   922,   922,
   922,   nil,   nil,   nil,   nil,   nil,   922,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   922,   922,   nil,   922,   922,   922,
   922,   922,   922,   922,   922,   922,   922,   922,   nil,   nil,
   922,   496,   496,   496,   496,   496,   496,   496,   496,   496,
   496,   496,   nil,   496,   496,   nil,   nil,   496,   496,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   496,   nil,   496,   nil,   496,   496,   496,
   496,   496,   496,   496,   nil,   496,   nil,   625,   625,   625,
   625,   625,   625,   625,   625,   625,   625,   625,   nil,   625,
   625,   nil,   496,   625,   625,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   625,
   nil,   625,   nil,   625,   625,   625,   625,   625,   625,   625,
   nil,   625,   nil,   703,   703,   703,   703,   703,   703,   703,
   703,   703,   703,   703,   nil,   703,   703,   nil,   625,   703,
   703,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   703,   nil,   703,   nil,   703,
   703,   703,   703,   703,   703,   703,   nil,   703,   nil,   706,
   706,   706,   706,   706,   706,   706,   706,   706,   706,   706,
   nil,   706,   706,   nil,   703,   706,   706,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   706,   nil,   706,   nil,   706,   706,   706,   706,   706,
   706,   706,   nil,   706,   nil,   710,   710,   710,   710,   710,
   710,   710,   710,   710,   710,   710,   nil,   710,   710,   nil,
   706,   710,   710,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   710,   nil,   710,
   nil,   710,   710,   710,   710,   710,   710,   710,   nil,   710,
   nil,   712,   712,   712,   712,   712,   712,   712,   712,   712,
   712,   712,   nil,   712,   712,   nil,   710,   712,   712,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   712,   nil,   712,   nil,   712,   712,   712,
   712,   712,   712,   712,   nil,   712,   nil,   715,   715,   715,
   715,   715,   715,   715,   715,   715,   715,   715,   nil,   715,
   715,   nil,   712,   715,   715,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   715,
   nil,   715,   nil,   715,   715,   715,   715,   715,   715,   715,
   nil,   715,   nil,   717,   717,   717,   717,   717,   717,   717,
   717,   717,   717,   717,   nil,   717,   717,   nil,   715,   717,
   717,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   717,   nil,   717,   nil,   717,
   717,   717,   717,   717,   717,   717,   nil,   717,   nil,   802,
   802,   802,   802,   802,   802,   802,   802,   802,   802,   802,
   nil,   802,   802,   nil,   717,   802,   802,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   802,   nil,   802,   nil,   802,   802,   802,   802,   802,
   802,   802,   nil,   802,   nil,   804,   804,   804,   804,   804,
   804,   804,   804,   804,   804,   804,   nil,   804,   804,   nil,
   802,   804,   804,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   804,   nil,   804,
   nil,   804,   804,   804,   804,   804,   804,   804,   nil,   804,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   804 ]

racc_action_pointer = [
     0,    80,   nil,    62,   nil,  4744,  1499,    10, 21299, 21423,
    39,    29,    57,   133,   607,    55,   345,   nil,    29,  4869,
  7135,   202,   nil,   294,   249, 20787, 20896,  4994,  5119,  5244,
   nil,   738,  5369,  5494,   nil,   114,   240,   229,   403,  5627,
  5752,  5877,   176,   611,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   866,   999,  6002,  6127,  6252,     0,   nil,  6377,  6502,
   nil,   nil,  6627,  6760,  6885,  7010, 21795,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   342,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,     0,   nil,   nil,
   130,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   328,   nil,  7135,   nil,   nil,   nil,   nil,  7268,  7393,
  7518,  7643,  7768,  1124,   nil,   449,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   265,   nil,  1249,  7893,  8018,
  8143,  8268, 21167, 21967,  8393,  8518,  8643,  8768,  8893,   nil,
   nil,   738,    54,   356,   109,   298,   351,   nil,  9018,  1374,
   360,  9143,  9268,  9393,  9518,  9643,  9768,  9893, 10018, 10143,
 10268, 10393, 10518, 10643, 10768, 10893, 11018, 11143, 11268, 11393,
 11518, 11643, 11768, 11893, 12018, 12143, 12268,   nil,   nil, 22027,
 22087,   421, 12393, 12518,   nil,   nil,   nil,   nil,   nil,   nil,
   nil, 12643,   nil, 13268,   nil,   409,   443,   nil, 12768,   506,
 12893,   nil, 13018,   nil,   nil,   537,  1527, 13143,   479,  1499,
   512,   569,   554, 13268,  1624,   741,   784,   637,   874,   nil,
   605,   576,   261,   nil,   nil,   nil,   623,   500,   585, 13401,
   nil,   478,   650,   658,   898,   nil,   661,   nil, 13526,   730,
   738,   590,   nil,    34,   254,   656,   650,   284,   690,   nil,
   nil,   600,    48,   136, 13651, 13776,   620,   768,   663,    65,
   903,   743,    89,   804,   nil,   nil,   351,   442,    96,   nil,
   931,   nil,    55, 13901,   nil,   nil,   309,   407,   434,   453,
   572,   576,   641,   649,   651,   nil,   668,   nil, 14026,   nil,
   274,   348,   382,   401,    39,   468,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   725, 21547,   nil,   nil,   nil,   nil,
   748,   nil,   nil,   738,   nil, 14151,   735,   nil,   740,   nil,
   nil,  1624,   745,   nil,   512,   519, 21005,   nil,   nil,   nil,
   239,   348,   787,   nil,   nil,  1752,  1861,   nil, 15276,   nil,
   nil,   nil,   174,   nil,   798, 22147, 22207, 14276,   244, 14401,
 14526, 14651,  1994,  2119,   431,   559,   823,   824,   833,   834,
  1499,  3619,  3744,  2244,  1810,  2369,  2494,  2619,  2744,  2869,
  2994,  3119,  3244,   530,   793,  3369,  3494, 20659,    79, 21114,
   nil,   nil, 21169,   nil,   nil,   775,   nil,   nil, 14776,   117,
   175,   778,   nil,   nil, 14901,   nil, 15026,   nil, 15151,   nil,
   nil,   nil, 15276,  1609,   785,   788,   nil,   nil,   792, 15409,
   805, 15534, 22267, 22327,  1002,   857,   nil,   nil, 15659,   823,
   nil, 15784, 15909, 16034, 22387, 22447, 22915, 16159,   940, 16284,
   nil,   829,   nil, 16409,   nil,   nil, 16534,   nil,   nil,   nil,
   nil,   830,  1994,   949,   nil,  2119,   119,   142,   947,   956,
 16659, 16784, 22507, 22567,    98,   nil,   nil,  1004,   nil, 22627,
 22687, 16909,   nil,   nil, 17034,   693,   146,  2244,  1716,   nil,
   nil,   nil,   283,   nil,   nil,   nil,   721,   nil,   nil,   nil,
   859,   nil,   nil,   465,   nil,   nil,   849,   nil,   nil,   nil,
 21671,   nil, 17159,   852, 17284, 17409,   523,   889, 17534, 17659,
   888,   nil,   nil, 17784, 17909,   889,   nil,   nil,   nil,   277,
   353,   473,   603,   860,  5494,   860,   nil,   567,   nil,  2369,
   nil,   nil,   nil,   nil,   293,   nil,  6627,    -6,   860,   nil,
   864,   nil,  2494,  2619,   nil,   869,   nil,   919, 18034,   nil,
   nil,  1742,   453, 18159,   878,   nil,   885,   125,   198,   923,
   443,  1129,   924,   894, 18284, 22961,   959,   960,   317,  1019,
   nil,  2744,   906,   958,   nil,   nil,   nil,   468,   108,   666,
   nil,   926,   930,   937,   nil,   nil,   nil,   nil,   nil,   nil,
   272,  1029,   nil,   985,   nil,   nil,   nil,   nil,  1032,   nil,
   nil,  1034,   637,   nil,  1075,   nil,   nil,   nil,   nil,  1083,
   nil,   140,   967,   135,   141,   263,   269, 18409,   481,  1254,
   nil,   972,  2869,   701,   nil,   nil,  1090,  2994,  5382,   725,
 21907,   nil,   nil,   nil,   nil,   nil,   nil,  3119,   nil,   nil,
   nil,   nil,   nil, 23007,   981, 18534, 23053, 18659,   nil,   nil,
 23099,   nil, 23145,   nil,   nil, 23191,   nil, 23237,   nil, 18784,
 18909, 19034, 19159,   386, 19284,   983,   988,   993,   nil,  1024,
  1005,  1360,   312,   nil,  1128,  3244, 19409,   nil,   nil,  1014,
  1036,  1142,   nil, 19534,   nil,  1023,   352,   nil,   nil,   nil,
  3369,   nil,   nil,   163, 19659,   nil,   nil,   nil,   nil,   nil,
  1029,  6773,   nil,  6898,   nil,   nil,  1021,  1129, 19784,   nil,
   nil,  1014,   nil,  1055,   349,  1104,  1079,   nil,   nil,  1199,
   nil, 19909,  1201,  3494,  3619,   nil, 20034,  3744,   156,   205,
   nil,  1205,   nil,  3869,   nil,  1208,  1097,   nil,   nil,  1112,
  1106,   nil, 23283, 20159, 23329,  7023,   nil, 13414,   nil,   nil,
  1050,   nil,  1128,  1108,   nil,   nil,   nil, 20284,   nil,  1130,
  1118,   nil,  1128,   nil,   nil,   nil,  1132,   nil,  3994,  1127,
  1252,  1207,  1255,   nil,  4119,  4244,  1139,  1144,  1147,   nil,
   nil,  1148,  1156,   nil,  1166,   nil,   nil,  1179,  1110,  1180,
   859,   nil,   nil,   164,   nil,  1307,  1321,   nil,   287,   nil,
   nil,  1324,   nil,   nil, 15422,   nil,  1206,  1213,  1220,  1231,
   nil,  1232,   nil,  1389,  1364,  1307,   nil,   nil, 20409,   nil,
   nil, 20534,  1360, 20659, 22747, 22807,  1377,  1260,  1373,   nil,
 15672,   nil,   nil,  1146,   nil,  1175,   nil,  1271,   nil,   nil,
   nil,   439,   869,  1257,  4369,   nil,   nil,   nil,   nil,   nil,
  4494,   nil,  4619, 18172,   nil,   nil,  1300,   nil,  1396,   nil,
   nil,   nil, 22867,   nil,  1258,   nil,  1264,   165,   166,   209,
  1379,   nil,   nil,  1269,  1272,  1273,  1281,  1287,  1139,  1291,
  1235,   836,  1421,  1425,  1316,  1326,  1329,  1331,  1375,  1378,
   nil,   173,   nil,  1425,   nil,   nil,   nil,  1264,  1344,   nil,
   nil,   nil,   nil,  1521,   nil,   nil,   nil,  1345,  1348,  1356,
   nil,   nil ]

racc_action_default = [
    -3,  -557,    -1,  -543,    -4,  -557,    -7,  -557,  -557,  -557,
  -557,  -557,  -557,  -557,  -557,  -280,   -39,   -40,  -545,  -557,
   -45,   -47,   -48,   -49,  -255,  -255,  -255,  -290,  -326,  -327,
   -65,   -11,   -69,   -77,   -79,  -557,  -472,  -557,  -557,  -557,
  -557,  -557,  -545,  -232,  -273,  -274,  -275,  -276,  -277,  -278,
  -279,  -533,   -11,  -557,  -556,  -525,  -298,  -300,  -557,  -557,
  -304,  -307,  -543,  -557,  -557,  -557,  -557,  -328,  -329,  -331,
  -332,  -421,  -422,  -423,  -424,  -425,  -440,  -428,  -429,  -442,
  -444,  -433,  -438,  -454,  -442,  -456,  -457,  -531,  -461,  -462,
  -532,  -464,  -465,  -466,  -467,  -468,  -469,  -470,  -471,  -474,
  -475,  -557,    -2,  -544,  -552,  -553,  -554,    -6,  -557,  -557,
  -557,  -557,  -557,    -3,   -15,  -557,  -106,  -107,  -108,  -109,
  -110,  -111,  -112,  -113,  -114,  -118,  -119,  -120,  -121,  -122,
  -123,  -124,  -125,  -126,  -127,  -128,  -129,  -130,  -131,  -132,
  -133,  -134,  -135,  -136,  -137,  -138,  -139,  -140,  -141,  -142,
  -143,  -144,  -145,  -146,  -147,  -148,  -149,  -150,  -151,  -152,
  -153,  -154,  -155,  -156,  -157,  -158,  -159,  -160,  -161,  -162,
  -163,  -164,  -165,  -166,  -167,  -168,  -169,  -170,  -171,  -172,
  -173,  -174,  -175,  -176,  -177,  -178,  -179,  -180,  -181,  -182,
  -183,  -184,  -185,  -186,  -187,   -20,  -115,   -11,  -557,  -557,
  -557,  -241,  -557,  -557,  -557,  -557,  -557,  -557,  -545,  -546,
   -44,  -557,  -472,  -557,  -280,  -557,  -557,  -224,  -557,   -11,
  -557,  -557,  -557,  -557,  -557,  -557,  -557,  -557,  -557,  -557,
  -557,  -557,  -557,  -557,  -557,  -557,  -557,  -557,  -557,  -557,
  -557,  -557,  -557,  -557,  -557,  -557,  -557,  -391,  -393,  -557,
  -557,   -54,  -241,  -557,  -297,  -396,  -405,  -407,   -60,  -402,
   -61,  -545,   -62,  -233,  -243,  -265,  -265,  -248,  -557,  -266,
  -557,  -527,  -557,   -63,   -64,  -543,   -12,  -557,   -67,   -11,
  -545,  -557,   -70,   -73,   -11,   -85,  -557,  -557,   -92,  -290,
  -293,  -545,  -557,  -326,  -327,  -330,  -403,  -557,   -75,  -557,
   -81,  -287,  -458,  -459,  -557,  -209,  -210,  -225,  -557,  -413,
  -557,  -283,  -234,  -549,  -549,  -557,  -557,  -549,  -557,  -299,
  -383,   -46,  -557,  -557,  -557,  -557,  -543,  -557,  -544,  -472,
  -557,  -557,  -280,  -557,  -342,  -343,  -101,  -102,  -557,  -104,
  -557,  -280,  -557,  -557,  -472,  -319,  -106,  -107,  -147,  -148,
  -149,  -165,  -170,  -177,  -180,  -321,  -557,  -523,  -557,  -426,
  -557,  -557,  -557,  -557,  -557,  -557,   972,    -5,  -555,   -21,
   -22,   -23,   -24,   -25,  -557,  -557,   -17,   -18,   -19,  -116,
  -557,   -27,   -36,  -188,  -266,  -557,  -557,   -28,   -37,   -38,
   -29,  -190,  -545,  -242,  -534,  -535,  -255,  -400,  -536,  -537,
  -535,  -534,  -255,  -399,  -401,  -536,  -537,   -35,  -198,   -41,
   -42,   -43,  -545,  -296,  -557,  -557,  -557,  -241,  -287,  -557,
  -557,  -557,  -199,  -200,  -201,  -202,  -203,  -204,  -205,  -206,
  -211,  -212,  -213,  -214,  -215,  -216,  -217,  -218,  -219,  -220,
  -221,  -222,  -223,  -226,  -227,  -228,  -229,  -545,  -372,  -255,
  -534,  -535,  -255,   -52,   -55,  -545,  -256,  -257,  -258,  -372,
  -372,  -545,  -292,  -244,  -264,  -245,  -264,  -262,  -557,  -267,
  -530,   -10,  -544,   -14,  -545,   -66,  -285,   -82,   -71,  -557,
  -545,  -241,  -557,  -557,   -91,  -557,  -458,  -459,  -557,   -78,
   -83,  -557,  -557,  -557,  -557,  -557,  -230,  -557,  -556,  -556,
  -282,  -545,  -235,  -551,  -550,  -237,  -551,  -288,  -289,  -526,
  -301,  -495,   -11,  -333,  -334,   -11,  -557,  -557,  -557,  -557,
  -557,  -241,  -557,  -557,  -287,  -312,  -101,  -102,  -103,  -557,
  -557,  -241,  -315,  -476,  -557,  -557,  -557,   -11,  -495,  -323,
  -541,  -542,  -545,  -427,  -441,  -446,  -557,  -448,  -430,  -443,
  -557,  -445,  -432,  -557,  -435,  -437,  -557,  -455,    -8,   -16,
  -557,   -26,  -557,  -272,  -557,  -557,  -404,  -557,  -557,  -557,
   -56,  -240,  -397,  -557,  -557,   -58,  -398,  -295,  -547,  -534,
  -535,  -534,  -535,  -545,  -188,  -557,  -373,  -377,  -375,   -11,
   -50,  -394,   -51,  -395,  -372,  -238,   -45,  -557,  -265,  -254,
  -545,  -260,   -11,   -11,  -291,  -265,  -263,  -268,  -557,  -528,
  -529,   -13,   -68,  -557,   -74,   -80,  -545,  -534,  -535,  -239,
   -88,   -90,  -557,   -76,  -557,  -197,  -207,  -208,  -545,  -556,
  -340,   -11,  -414,  -556,  -415,  -416,  -284,  -549,  -557,  -495,
  -386,  -522,  -522,  -522,  -494,  -496,  -497,  -498,  -499,  -500,
  -501,  -557,  -504,  -557,  -506,  -512,  -514,  -515,  -517,  -518,
  -519,  -557,  -556,  -335,  -556,  -305,  -336,  -337,  -308,  -557,
  -311,  -557,  -545,  -534,  -535,  -538,  -286,  -557,  -101,  -102,
  -105,  -545,   -11,  -557,  -478,  -317,  -557,   -11,  -495,  -557,
  -557,  -524,  -447,  -450,  -451,  -452,  -453,   -11,  -431,  -434,
  -436,  -439,  -117,  -189,  -270,  -557,  -191,  -557,  -548,   -31,
  -193,   -32,  -194,   -57,   -33,  -196,   -34,  -195,   -59,  -557,
  -557,  -557,  -557,  -404,  -557,  -522,  -522,  -522,  -371,  -557,
  -377,  -557,  -501,  -510,  -557,   -11,  -557,  -251,  -259,  -545,
  -557,  -557,  -246,  -264,  -269,   -72,   -86,   -84,  -294,    -9,
   -11,  -420,  -341,  -557,  -557,  -418,  -236,  -384,  -387,  -389,
  -377,  -557,  -487,  -557,  -490,  -492,  -557,  -557,  -557,  -503,
  -344,  -557,  -346,  -348,  -355,  -501,  -545,  -516,  -520,  -557,
  -338,  -557,  -557,   -11,   -11,  -310,  -557,   -11,  -404,  -557,
  -404,  -557,  -477,   -11,  -320,  -557,  -545,  -480,  -324,  -557,
  -271,   -30,  -192,  -557,  -231,  -362,  -364,  -557,  -367,  -369,
  -557,  -374,  -557,  -378,  -379,  -381,  -382,  -557,  -392,  -557,
  -265,  -250,  -265,  -261,  -406,  -408,  -265,  -419,   -11,   -93,
  -557,  -557,  -100,  -417,   -11,   -11,  -545,  -522,  -522,  -507,
  -521,  -522,  -522,  -513,  -522,  -502,  -508,  -545,  -557,  -353,
  -557,  -505,  -302,  -557,  -303,  -557,  -557,  -268,  -556,  -313,
  -316,  -557,  -322,  -479,  -495,  -449,  -522,  -522,  -522,  -522,
  -511,  -522,  -376,  -557,  -509,  -557,   -53,  -249,  -264,  -252,
  -247,  -264,  -413,  -241,  -557,  -557,   -99,  -557,  -557,  -385,
  -557,  -483,  -485,  -557,  -488,  -557,  -491,  -557,  -493,  -345,
  -347,  -351,  -557,  -356,   -11,  -306,  -309,  -409,  -410,  -411,
   -11,  -318,   -11,  -557,  -359,  -361,  -557,  -365,  -557,  -368,
  -370,  -380,  -557,  -287,  -265,  -412,  -545,  -534,  -535,  -538,
   -98,  -388,  -390,  -522,  -522,  -522,  -522,  -349,  -557,  -354,
  -557,  -556,  -557,  -557,  -522,  -522,  -522,  -522,  -286,  -538,
  -253,  -404,  -481,  -557,  -484,  -486,  -489,  -557,  -352,  -339,
  -314,  -325,  -357,  -557,  -360,  -363,  -366,  -522,  -350,  -522,
  -482,  -358 ]

racc_goto_table = [
   119,   119,   207,   392,   355,    11,   210,   313,   498,   629,
    11,   314,   317,   519,   687,   310,   489,    13,   454,   566,
   295,   295,    13,   103,   396,   402,   308,   779,   102,   264,
   264,   264,   727,   106,   640,   282,    11,   512,   515,   726,
   609,   463,   465,   114,   196,   278,   644,   312,    13,   382,
   389,   295,   295,   295,   455,     6,   729,    11,   119,   281,
     6,   322,   323,   122,   122,   326,   124,   124,   334,    13,
   298,   449,   452,   644,   502,   505,   280,   528,   509,   814,
   609,   255,   259,   251,   258,   260,   816,   327,   665,   668,
   599,   839,   106,   843,   776,   870,   606,   900,   606,   107,
   262,   273,   274,     2,   544,   361,   551,   554,    11,     1,
   365,   369,   370,   371,   372,   589,   749,    11,    11,   903,
    13,   591,   728,   195,   593,   335,   602,   603,   375,    13,
    13,   342,   345,   594,   560,   839,   315,   601,   456,   600,
   739,   357,   316,   319,   510,   333,   532,   356,   539,   501,
   320,   386,   386,   324,   783,   325,   784,   677,     6,   910,
   682,   793,   760,   537,   538,   690,   864,   373,     6,   730,
   813,   939,   638,   757,   644,   782,   511,   248,   834,   835,
   448,   459,   460,   907,   633,   381,   387,   390,   753,   900,
   359,   407,   360,   362,   363,   406,   614,   367,   553,   812,
   364,   870,    11,   264,   669,   623,   838,   958,   841,   692,
   723,   796,   847,   697,    13,   837,   374,   nil,   nil,   583,
   843,   921,   nil,   644,    11,   380,   968,   839,   816,   836,
   nil,   nil,   nil,   nil,   nil,   nil,    13,   396,   402,   nil,
   nil,   nil,   nil,   746,   nil,   nil,   nil,   281,   nil,   nil,
   867,   nil,   868,   nil,   264,   264,   nil,   866,   nil,   397,
   nil,   735,   nil,   264,   nil,   nil,   nil,   nil,   680,   nil,
   nil,   295,   nil,   nil,   nil,   485,   nil,   nil,   nil,   nil,
   nil,   nil,    11,   616,    11,   nil,   nil,   295,   nil,    11,
   nil,   nil,   nil,   278,    13,   472,    13,   700,   278,   788,
   471,    13,   490,   nil,   619,   106,   959,   281,   790,   nil,
   nil,   nil,   281,   nil,   nil,   nil,   619,   609,   nil,   nil,
   478,   318,   nil,   672,   474,   nil,   457,   516,   517,   480,
   745,   nil,   473,   681,   461,   933,   nil,   713,   nil,   nil,
   912,   nil,   718,   nil,   619,   nil,   nil,   nil,   nil,   255,
   634,   518,   619,   259,   nil,   nil,   106,   nil,   944,   nil,
   nil,   859,   nil,   nil,   nil,   nil,   nil,   119,   821,   908,
   nil,   nil,   533,   nil,   737,   606,   567,   nil,   nil,   nil,
   nil,   742,   nil,   nil,   nil,    14,   787,   nil,   nil,   nil,
    14,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   756,   644,
   nil,   nil,   nil,   nil,   211,   nil,   nil,   nil,   406,   nil,
   559,   nil,   211,   211,   211,   598,    14,   286,   286,   264,
   nil,   605,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   122,   585,   nil,   124,   nil,   nil,   nil,    14,   211,   nil,
   nil,   nil,   nil,   211,   211,   nil,   nil,   211,   330,   340,
   340,   nil,   386,   571,   nil,   570,   nil,   nil,   nil,   576,
   637,   575,   nil,   nil,   384,   388,   nil,   295,   828,   nil,
   nil,   nil,   397,   671,   nil,   406,   295,    11,   nil,   nil,
   nil,   nil,   490,   264,   nil,   nil,   nil,   406,    14,    13,
   nil,   490,   635,   211,   211,   211,   211,    14,    14,   nil,
   686,   nil,   nil,   nil,   nil,   nil,   571,   nil,   590,   571,
   606,   592,   nil,   606,   nil,   406,   nil,    11,   nil,   nil,
    11,   406,   nil,   264,   nil,   nil,   nil,   611,   nil,    13,
   nil,   nil,    13,   264,   467,   nil,   469,   683,   470,   397,
   662,   nil,    11,   664,   909,   nil,   666,   666,   nil,   nil,
   nil,   397,   119,   951,    13,   709,   711,   nil,   nil,   nil,
   714,   716,   nil,   nil,   nil,   684,   685,   567,   904,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   397,
   nil,   925,    14,   211,   211,   211,   211,   397,   nil,   211,
   211,   211,   211,   211,    11,   702,   877,   nil,   879,   nil,
   567,   295,   880,   nil,    14,   nil,    13,    11,    11,    15,
   nil,   nil,   295,   nil,    15,   122,   490,   734,   124,    13,
    13,   nil,   751,   nil,   nil,   nil,   755,   747,   798,   nil,
   740,   741,   nil,   nil,   nil,   nil,    11,   211,   211,   nil,
    15,   288,   288,   nil,   nil,   791,   211,   nil,    13,   nil,
   795,   563,   nil,   nil,   nil,   nil,   567,   nil,   nil,   752,
   nil,    15,    14,   nil,    14,   567,   nil,   nil,   286,    14,
   nil,   nil,   332,   341,   341,   nil,   571,   nil,   nil,   576,
   789,   nil,   119,   nil,   286,   926,   nil,    11,   nil,   820,
   nil,   nil,    11,   822,   801,   nil,   826,   nil,   nil,    13,
   950,   nil,    11,   nil,    13,   nil,   619,   nil,   nil,   211,
   211,   nil,    15,   792,    13,   nil,   nil,   nil,   nil,   797,
   725,    15,    15,   nil,   597,   799,   nil,   nil,   211,   nil,
   607,   nil,   318,   nil,   610,   nil,   nil,   nil,   nil,   nil,
    11,   nil,   295,   211,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    13,   nil,   nil,    11,   861,   nil,   nil,   833,
   nil,   nil,   nil,   819,   nil,   632,   nil,    13,   nil,   607,
   nil,   nil,   318,   770,   nil,   nil,   nil,   nil,   827,   nil,
   nil,   nil,   nil,   nil,   853,   nil,   384,   nil,    11,    11,
   nil,   nil,    11,   nil,   nil,   nil,   nil,   413,    11,   nil,
    13,    13,   211,   nil,    13,   295,    15,   nil,   nil,   nil,
    13,   855,   856,   nil,   nil,   858,   nil,   nil,   nil,   666,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    15,   nil,
   704,   nil,   nil,    11,   nil,   924,   nil,   nil,   nil,    11,
    11,   nil,   nil,   211,   nil,    13,   nil,   nil,   nil,   nil,
   462,    13,    13,   nil,   nil,   nil,   882,    14,   nil,   nil,
   nil,   nil,   887,   888,   286,   nil,   211,   nil,   nil,   475,
   nil,   nil,   nil,   286,   744,   943,   nil,   406,   nil,   nil,
   nil,   nil,   211,   nil,   nil,   264,    15,   nil,    15,   nil,
   nil,   770,   288,    15,   849,   nil,   842,    14,   nil,   844,
    14,   nil,   nil,   nil,   nil,   nil,   211,   nil,   288,    11,
   567,   nil,   nil,   nil,   406,    11,   211,    11,   nil,   211,
   nil,    13,    14,   nil,   nil,   nil,   nil,    13,   nil,    13,
   nil,   nil,   941,   nil,   nil,   nil,   nil,   nil,   942,   nil,
   869,   397,   nil,   871,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   211,   211,   nil,   nil,   311,   211,   211,
   nil,   nil,   321,   321,   nil,   nil,   321,   nil,   770,   nil,
   770,   800,   nil,   nil,    14,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    14,    14,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   286,   nil,
   nil,   577,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   286,
   nil,   nil,   321,   321,   321,   321,    14,   nil,   nil,   nil,
   nil,   937,   770,   934,   nil,   nil,   935,   nil,   936,   nil,
   nil,   nil,   nil,   nil,   846,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   595,   nil,   945,   nil,   nil,   946,
   604,   947,   857,   nil,   nil,   nil,   nil,   nil,   770,   nil,
   770,   nil,   211,   612,   nil,   nil,   nil,    14,   nil,   615,
   nil,   nil,    14,   nil,    24,   nil,   nil,   770,   nil,    24,
   nil,    15,    14,   nil,   nil,   nil,   967,   nil,   288,   nil,
   636,   nil,   211,    24,   nil,   nil,   969,   288,   nil,   nil,
   nil,    24,    24,    24,   nil,    24,   nil,   nil,   nil,   409,
   410,   411,   412,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    14,    15,   nil,   nil,    15,   nil,    24,    24,   nil,   nil,
   nil,   691,    24,    24,   nil,    14,    24,   nil,   nil,   830,
   nil,   nil,   nil,   nil,   607,   nil,    15,   857,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   696,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   211,   nil,    14,    14,
   nil,   nil,    14,   nil,   nil,   nil,   nil,    24,    14,   nil,
   nil,   nil,    24,    24,    24,    24,    24,    24,   nil,   738,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    15,   nil,
   nil,   nil,   874,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    15,    15,    14,   nil,   nil,   nil,   748,   nil,    14,
    14,   nil,   288,   nil,   nil,   nil,   nil,   nil,   321,   321,
   nil,   nil,   nil,   288,   nil,   nil,   nil,   nil,   nil,   nil,
    15,   nil,   nil,   nil,   nil,   nil,   nil,   536,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   542,   nil,   nil,   nil,   nil,   nil,   211,   nil,
   nil,    24,    24,    24,    24,    24,   nil,   nil,    24,    24,
    24,    24,    24,   nil,   nil,   nil,   nil,   nil,   nil,    14,
   nil,    15,   nil,    24,   nil,    14,    15,    14,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    15,   nil,    36,   nil,
   nil,   nil,   nil,    36,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    24,    24,   823,   nil,
   nil,   nil,   nil,   nil,   nil,    24,   nil,   nil,   nil,    36,
   285,   285,   nil,   nil,    15,   nil,   nil,   nil,   nil,   nil,
   nil,    24,   nil,    24,   nil,   nil,   nil,   nil,    24,    15,
    36,   nil,   311,   832,   nil,   851,   nil,   nil,   nil,   nil,
   nil,   329,   344,   344,   344,   nil,   762,   764,   765,   nil,
   nil,   nil,   nil,   nil,   nil,   863,   nil,   nil,   nil,   nil,
   nil,   nil,    15,    15,   nil,   nil,    15,   nil,    24,    24,
   nil,   628,    15,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    36,   nil,   nil,   nil,   nil,   nil,    24,   nil,   nil,
    36,    36,   nil,   nil,   nil,   889,   341,   nil,   nil,   nil,
   nil,   nil,    24,   nil,   nil,   nil,   899,    15,   321,   nil,
   nil,   nil,   nil,    15,    15,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   806,   808,   809,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    24,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    36,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    15,   nil,   nil,   nil,   nil,   nil,    15,
   nil,    15,   nil,   nil,   nil,   nil,   nil,    36,   nil,   nil,
   nil,   nil,    24,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    24,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    24,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    24,   891,   892,   nil,   nil,   894,   896,   nil,   898,
   nil,   321,   nil,   nil,   nil,    36,    24,    36,   nil,    24,
   nil,   285,    36,   nil,   nil,    24,   nil,   nil,   nil,   nil,
   nil,   914,   915,   917,   919,    24,   920,   285,    24,   nil,
   nil,    24,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    24,    24,   nil,   nil,   nil,    24,    24,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    24,   nil,   nil,   nil,   nil,   952,   954,
   955,   956,   nil,   nil,   nil,   nil,    24,    24,   nil,   962,
   964,   965,   966,   nil,   nil,   321,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   970,   nil,   971,    24,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    24,   nil,   nil,   nil,   nil,    24,   nil,   nil,   nil,
   nil,    24,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    24,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    36,    24,   nil,   nil,   nil,   nil,   nil,   285,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   285,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    24,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    36,   nil,   nil,    36,    24,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    36,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    24,   nil,    24,    24,   nil,
   nil,    24,   nil,   nil,   nil,   nil,   nil,    24,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    36,   nil,   nil,
   nil,   nil,    24,   nil,   nil,   nil,   nil,   nil,    24,    24,
    36,    36,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   285,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   285,   nil,   nil,   nil,   nil,   nil,   nil,    36,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   217,   nil,   nil,   nil,   nil,    24,   nil,   nil,
   263,   263,   263,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   305,   306,   307,   nil,   nil,   nil,    24,   nil,
   nil,   nil,   nil,   nil,    24,   nil,    24,   263,   263,   nil,
    36,   nil,   nil,   nil,   nil,    36,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    36,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    36,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    36,   nil,
   nil,   nil,   829,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    36,    36,   nil,   nil,    36,   nil,   nil,   nil,   nil,
   nil,    36,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   344,   nil,   nil,   nil,   nil,
   nil,   383,   263,   391,   263,   nil,    36,   408,   nil,   nil,
   nil,   nil,    36,    36,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   217,   nil,   nil,   422,   423,   424,   425,   426,   427,
   428,   429,   430,   431,   432,   433,   434,   435,   436,   437,
   438,   439,   440,   441,   442,   443,   444,   445,   446,   447,
   nil,   nil,   nil,   nil,   nil,   263,   263,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   263,   nil,   nil,   nil,   nil,   nil,
   nil,   263,   nil,   263,   nil,   263,   nil,   nil,   nil,   nil,
   nil,   nil,    36,   nil,   nil,   nil,   nil,   nil,    36,   nil,
    36,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   496,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   263,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   263,   nil,   408,   584,   391,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   596,   nil,   nil,   nil,   nil,   nil,   263,   nil,   263,
   nil,   263,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   263,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   625,   626,   627,   nil,   nil,   nil,
   nil,   nil,   263,   nil,   nil,   nil,   263,   nil,   nil,   263,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   263,   263,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   263,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   703,   nil,   263,   706,   nil,
   nil,   710,   712,   nil,   nil,   nil,   715,   717,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   263,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   263,   nil,
   802,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   710,   712,   717,   715,   nil,   804,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   263,
   nil,   nil,   nil,   nil,   nil,   nil,   263,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   263,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   263,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   802,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   263,   nil,   nil,   263,   nil,   263 ]

racc_goto_check = [
    52,    52,    29,    22,    51,    17,    18,    61,    10,    11,
    17,    63,    63,    87,    93,     8,    47,    20,    36,    23,
    58,    58,    20,     6,    34,    34,    29,    85,     4,    31,
    31,    31,   117,    91,   128,    45,    17,    84,    84,   116,
   157,    66,    66,    14,    14,    41,   119,    60,    20,    25,
    25,    58,    58,    58,    22,     7,   121,    17,    52,     9,
     7,    16,    16,    55,    55,    16,    56,    56,    17,    20,
    46,    34,    34,   119,    62,    62,    42,    49,    62,   123,
   157,    64,    64,    35,    35,    35,   124,     4,    86,    86,
    67,   152,    91,   153,   112,   154,    67,   110,    67,     5,
    40,    40,    40,     2,   147,   143,   147,   147,    17,     1,
   143,    16,    16,    16,    16,    37,    12,    17,    17,   113,
    20,    65,   119,    15,    65,    19,    37,    37,    28,    20,
    20,    50,    50,    38,    57,   152,    59,    68,    69,    71,
    72,    79,    80,    81,    83,    88,    90,    94,    95,    96,
    97,    61,    61,    98,    99,   100,   101,   102,     7,   103,
   104,   105,   128,   106,   107,   108,   109,     7,     7,   114,
   122,   113,   125,   126,   119,    11,   127,   129,   130,   131,
   132,   134,   135,   136,   137,    18,    18,    18,   138,   110,
   141,    18,   142,   144,   145,    52,    47,     5,   146,   121,
   148,   154,    17,    31,    87,    47,   117,   113,   117,   149,
    23,   128,   112,   150,    20,   151,     2,   nil,   nil,    22,
   153,   123,   nil,   119,    17,     9,   113,   152,   124,   121,
   nil,   nil,   nil,   nil,   nil,   nil,    20,    34,    34,   nil,
   nil,   nil,   nil,    23,   nil,   nil,   nil,     9,   nil,   nil,
   117,   nil,   117,   nil,    31,    31,   nil,   116,   nil,    64,
   nil,    37,   nil,    31,   nil,   nil,   nil,   nil,    49,   nil,
   nil,    58,   nil,   nil,   nil,    29,   nil,   nil,   nil,   nil,
   nil,   nil,    17,    22,    17,   nil,   nil,    58,   nil,    17,
   nil,   nil,   nil,    41,    20,     6,    20,   147,    41,    23,
     4,    20,    45,   nil,    34,    91,    85,     9,    23,   nil,
   nil,   nil,     9,   nil,   nil,   nil,    34,   157,   nil,   nil,
    46,    26,   nil,    22,    42,   nil,    40,    16,    16,    42,
    47,   nil,     7,    22,    40,   117,   nil,    36,   nil,   nil,
    93,   nil,    36,   nil,    34,   nil,   nil,   nil,   nil,    64,
    25,     4,    34,    64,   nil,   nil,    91,   nil,   117,   nil,
   nil,    86,   nil,   nil,   nil,   nil,   nil,    52,    67,    11,
   nil,   nil,    91,   nil,    66,    67,    29,   nil,   nil,   nil,
   nil,    66,   nil,   nil,   nil,    21,    84,   nil,   nil,   nil,
    21,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    62,   119,
   nil,   nil,   nil,   nil,    21,   nil,   nil,   nil,    52,   nil,
    14,   nil,    21,    21,    21,    63,    21,    21,    21,    31,
   nil,    63,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    55,    29,   nil,    56,   nil,   nil,   nil,    21,    21,   nil,
   nil,   nil,   nil,    21,    21,   nil,   nil,    21,    21,    21,
    21,   nil,    61,    64,   nil,    35,   nil,   nil,   nil,    64,
    63,    35,   nil,   nil,    26,    26,   nil,    58,    84,   nil,
   nil,   nil,    64,    61,   nil,    52,    58,    17,   nil,   nil,
   nil,   nil,    45,    31,   nil,   nil,   nil,    52,    21,    20,
   nil,    45,    60,    21,    21,    21,    21,    21,    21,   nil,
     8,   nil,   nil,   nil,   nil,   nil,    64,   nil,    35,    64,
    67,    35,   nil,    67,   nil,    52,   nil,    17,   nil,   nil,
    17,    52,   nil,    31,   nil,   nil,   nil,     7,   nil,    20,
   nil,   nil,    20,    31,    26,   nil,    26,    16,    26,    64,
     9,   nil,    17,     9,    87,   nil,    91,    91,   nil,   nil,
   nil,    64,    52,    23,    20,    18,    18,   nil,   nil,   nil,
    18,    18,   nil,   nil,   nil,    91,    91,    29,    84,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    64,
   nil,    10,    21,    21,    21,    21,    21,    64,   nil,    21,
    21,    21,    21,    21,    17,    14,    66,   nil,    66,   nil,
    29,    58,    66,   nil,    21,   nil,    20,    17,    17,    24,
   nil,   nil,    58,   nil,    24,    55,    45,     9,    56,    20,
    20,   nil,    60,   nil,   nil,   nil,    60,    45,    51,   nil,
     9,     9,   nil,   nil,   nil,   nil,    17,    21,    21,   nil,
    24,    24,    24,   nil,   nil,     8,    21,   nil,    20,   nil,
     8,    26,   nil,   nil,   nil,   nil,    29,   nil,   nil,     9,
   nil,    24,    21,   nil,    21,    29,   nil,   nil,    21,    21,
   nil,   nil,    24,    24,    24,   nil,    64,   nil,   nil,    64,
    16,   nil,    52,   nil,    21,    22,   nil,    17,   nil,    61,
   nil,   nil,    17,    63,    18,   nil,    61,   nil,   nil,    20,
    66,   nil,    17,   nil,    20,   nil,    34,   nil,   nil,    21,
    21,   nil,    24,    91,    20,   nil,   nil,   nil,   nil,    91,
   115,    24,    24,   nil,    26,     9,   nil,   nil,    21,   nil,
    26,   nil,    26,   nil,    26,   nil,   nil,   nil,   nil,   nil,
    17,   nil,    58,    21,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    20,   nil,   nil,    17,     8,   nil,   nil,    17,
   nil,   nil,   nil,     9,   nil,    26,   nil,    20,   nil,    26,
   nil,   nil,    26,   111,   nil,   nil,   nil,   nil,     9,   nil,
   nil,   nil,   nil,   nil,    16,   nil,    26,   nil,    17,    17,
   nil,   nil,    17,   nil,   nil,   nil,   nil,    43,    17,   nil,
    20,    20,    21,   nil,    20,    58,    24,   nil,   nil,   nil,
    20,     9,     9,   nil,   nil,     9,   nil,   nil,   nil,    91,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    24,   nil,
    26,   nil,   nil,    17,   nil,    63,   nil,   nil,   nil,    17,
    17,   nil,   nil,    21,   nil,    20,   nil,   nil,   nil,   nil,
    43,    20,    20,   nil,   nil,   nil,     9,    21,   nil,   nil,
   nil,   nil,     9,     9,    21,   nil,    21,   nil,   nil,    43,
   nil,   nil,   nil,    21,    26,     8,   nil,    52,   nil,   nil,
   nil,   nil,    21,   nil,   nil,    31,    24,   nil,    24,   nil,
   nil,   111,    24,    24,   111,   nil,   115,    21,   nil,   115,
    21,   nil,   nil,   nil,   nil,   nil,    21,   nil,    24,    17,
    29,   nil,   nil,   nil,    52,    17,    21,    17,   nil,    21,
   nil,    20,    21,   nil,   nil,   nil,   nil,    20,   nil,    20,
   nil,   nil,     9,   nil,   nil,   nil,   nil,   nil,     9,   nil,
   115,    64,   nil,   115,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    21,    21,   nil,   nil,    27,    21,    21,
   nil,   nil,    27,    27,   nil,   nil,    27,   nil,   111,   nil,
   111,    26,   nil,   nil,    21,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    21,    21,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    21,   nil,
   nil,    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    21,
   nil,   nil,    27,    27,    27,    27,    21,   nil,   nil,   nil,
   nil,   111,   111,   115,   nil,   nil,   115,   nil,   115,   nil,
   nil,   nil,   nil,   nil,    26,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   115,   nil,   nil,   115,
    43,   115,    26,   nil,   nil,   nil,   nil,   nil,   111,   nil,
   111,   nil,    21,    43,   nil,   nil,   nil,    21,   nil,    43,
   nil,   nil,    21,   nil,    39,   nil,   nil,   111,   nil,    39,
   nil,    24,    21,   nil,   nil,   nil,   115,   nil,    24,   nil,
    43,   nil,    21,    39,   nil,   nil,   115,    24,   nil,   nil,
   nil,    39,    39,    39,   nil,    39,   nil,   nil,   nil,    27,
    27,    27,    27,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    21,    24,   nil,   nil,    24,   nil,    39,    39,   nil,   nil,
   nil,    43,    39,    39,   nil,    21,    39,   nil,   nil,    21,
   nil,   nil,   nil,   nil,    26,   nil,    24,    26,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    24,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    21,   nil,    21,    21,
   nil,   nil,    21,   nil,   nil,   nil,   nil,    39,    21,   nil,
   nil,   nil,    39,    39,    39,    39,    39,    39,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    24,   nil,
   nil,   nil,    21,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    24,    24,    21,   nil,   nil,   nil,    43,   nil,    21,
    21,   nil,    24,   nil,   nil,   nil,   nil,   nil,    27,    27,
   nil,   nil,   nil,    24,   nil,   nil,   nil,   nil,   nil,   nil,
    24,   nil,   nil,   nil,   nil,   nil,   nil,    27,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    27,   nil,   nil,   nil,   nil,   nil,    21,   nil,
   nil,    39,    39,    39,    39,    39,   nil,   nil,    39,    39,
    39,    39,    39,   nil,   nil,   nil,   nil,   nil,   nil,    21,
   nil,    24,   nil,    39,   nil,    21,    24,    21,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    24,   nil,    48,   nil,
   nil,   nil,   nil,    48,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    39,    39,    43,   nil,
   nil,   nil,   nil,   nil,   nil,    39,   nil,   nil,   nil,    48,
    48,    48,   nil,   nil,    24,   nil,   nil,   nil,   nil,   nil,
   nil,    39,   nil,    39,   nil,   nil,   nil,   nil,    39,    24,
    48,   nil,    27,    24,   nil,    43,   nil,   nil,   nil,   nil,
   nil,    48,    48,    48,    48,   nil,   118,   118,   118,   nil,
   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,
   nil,   nil,    24,    24,   nil,   nil,    24,   nil,    39,    39,
   nil,    27,    24,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    48,   nil,   nil,   nil,   nil,   nil,    39,   nil,   nil,
    48,    48,   nil,   nil,   nil,    43,    24,   nil,   nil,   nil,
   nil,   nil,    39,   nil,   nil,   nil,    43,    24,    27,   nil,
   nil,   nil,   nil,    24,    24,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   118,   118,   118,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    39,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    48,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    24,   nil,   nil,   nil,   nil,   nil,    24,
   nil,    24,   nil,   nil,   nil,   nil,   nil,    48,   nil,   nil,
   nil,   nil,    39,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    39,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    39,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    39,   118,   118,   nil,   nil,   118,   118,   nil,   118,
   nil,    27,   nil,   nil,   nil,    48,    39,    48,   nil,    39,
   nil,    48,    48,   nil,   nil,    39,   nil,   nil,   nil,   nil,
   nil,   118,   118,   118,   118,    39,   118,    48,    39,   nil,
   nil,    39,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    39,    39,   nil,   nil,   nil,    39,    39,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    39,   nil,   nil,   nil,   nil,   118,   118,
   118,   118,   nil,   nil,   nil,   nil,    39,    39,   nil,   118,
   118,   118,   118,   nil,   nil,    27,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   118,   nil,   118,    39,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    39,   nil,   nil,   nil,   nil,    39,   nil,   nil,   nil,
   nil,    39,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    39,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    48,    39,   nil,   nil,   nil,   nil,   nil,    48,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    48,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    39,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    48,   nil,   nil,    48,    39,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    48,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    39,   nil,    39,    39,   nil,
   nil,    39,   nil,   nil,   nil,   nil,   nil,    39,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    48,   nil,   nil,
   nil,   nil,    39,   nil,   nil,   nil,   nil,   nil,    39,    39,
    48,    48,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    48,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    48,   nil,   nil,   nil,   nil,   nil,   nil,    48,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    30,   nil,   nil,   nil,   nil,    39,   nil,   nil,
    30,    30,    30,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    30,    30,    30,   nil,   nil,   nil,    39,   nil,
   nil,   nil,   nil,   nil,    39,   nil,    39,    30,    30,   nil,
    48,   nil,   nil,   nil,   nil,    48,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    48,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    48,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    48,   nil,
   nil,   nil,    48,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    48,    48,   nil,   nil,    48,   nil,   nil,   nil,   nil,
   nil,    48,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    48,   nil,   nil,   nil,   nil,
   nil,    30,    30,    30,    30,   nil,    48,    30,   nil,   nil,
   nil,   nil,    48,    48,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    30,   nil,   nil,    30,    30,    30,    30,    30,    30,
    30,    30,    30,    30,    30,    30,    30,    30,    30,    30,
    30,    30,    30,    30,    30,    30,    30,    30,    30,    30,
   nil,   nil,   nil,   nil,   nil,    30,    30,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    30,   nil,   nil,   nil,   nil,   nil,
   nil,    30,   nil,    30,   nil,    30,   nil,   nil,   nil,   nil,
   nil,   nil,    48,   nil,   nil,   nil,   nil,   nil,    48,   nil,
    48,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    30,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    30,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    30,   nil,    30,    30,    30,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    30,   nil,   nil,   nil,   nil,   nil,    30,   nil,    30,
   nil,    30,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    30,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    30,    30,    30,   nil,   nil,   nil,
   nil,   nil,    30,   nil,   nil,   nil,    30,   nil,   nil,    30,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    30,    30,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    30,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    30,   nil,    30,    30,   nil,
   nil,    30,    30,   nil,   nil,   nil,    30,    30,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    30,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    30,   nil,
    30,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    30,    30,    30,    30,   nil,    30,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    30,
   nil,   nil,   nil,   nil,   nil,   nil,    30,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    30,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    30,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    30,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    30,   nil,   nil,    30,   nil,    30 ]

racc_goto_pointer = [
   nil,   109,   103,   nil,    25,    94,    20,    55,   -37,    28,
  -301,  -489,  -513,   nil,    35,   114,     3,     5,   -13,    62,
    17,   385,  -198,  -373,   609,  -149,   266,   904,    14,   -16,
  1933,     2,   nil,   nil,  -178,    59,  -233,  -333,  -320,  1074,
    73,    14,    45,   589,   nil,     3,    37,  -283,  1308,  -261,
    67,   -62,    -8,   nil,   nil,    55,    58,  -245,   -12,    82,
    -7,   -47,  -239,   -43,    57,  -328,  -224,  -368,  -321,  -115,
   nil,  -319,  -461,   nil,   nil,   nil,   nil,   nil,   nil,    75,
    87,    87,   nil,  -176,  -285,  -635,  -428,  -314,    82,   nil,
  -196,    30,   nil,  -524,    81,  -208,  -162,    93,    93,  -511,
    94,  -512,  -368,  -700,  -372,  -524,  -182,  -191,  -374,  -632,
  -751,   120,  -559,  -731,  -418,   133,  -548,  -555,   735,  -465,
   nil,  -531,  -561,  -652,  -645,  -339,  -465,  -144,  -477,   156,
  -580,  -580,   -67,   nil,   -75,   -75,  -675,  -315,  -445,   nil,
   nil,   116,   116,    26,   113,   113,  -165,  -256,   118,  -337,
  -334,  -546,  -670,  -670,  -712,   nil,   nil,  -426 ]

racc_goto_default = [
   nil,   nil,   nil,     3,   nil,     4,   328,   276,   nil,   309,
   nil,   780,   nil,   275,   nil,   nil,   nil,   215,    17,    12,
   216,   304,   nil,   nil,   214,   nil,   269,    16,   nil,   414,
    20,    21,    22,    23,   622,   nil,   nil,   nil,   nil,   292,
   393,    30,   nil,   nil,    32,    35,    34,   nil,   212,   339,
   nil,   121,   399,   120,   123,    72,    73,   nil,    43,   nil,
   630,   265,   nil,   266,   404,   572,   nil,   267,   nil,   nil,
   253,   nil,   nil,    44,    45,    46,    47,    48,    49,    50,
   nil,   254,    56,   nil,   nil,   nil,   nil,   nil,   nil,    64,
   nil,   513,    65,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   772,   652,   nil,   773,   nil,   641,   nil,   643,   nil,   840,
   586,   nil,   nil,   nil,   649,   nil,   nil,   nil,   689,   nil,
   nil,   nil,   nil,   403,   nil,   nil,   nil,   nil,   nil,    71,
    74,    75,   nil,   nil,   nil,   nil,   nil,   549,   nil,   nil,
   nil,   642,   654,   655,   733,   658,   661,   271 ]

racc_reduce_table = [
  0, 0, :racc_error,
  1, 138, :_reduce_none,
  2, 139, :_reduce_2,
  0, 140, :_reduce_3,
  1, 140, :_reduce_4,
  3, 140, :_reduce_5,
  2, 140, :_reduce_6,
  1, 142, :_reduce_none,
  4, 142, :_reduce_8,
  4, 145, :_reduce_9,
  2, 146, :_reduce_10,
  0, 150, :_reduce_11,
  1, 150, :_reduce_12,
  3, 150, :_reduce_13,
  2, 150, :_reduce_14,
  0, 165, :_reduce_15,
  4, 144, :_reduce_16,
  3, 144, :_reduce_17,
  3, 144, :_reduce_18,
  3, 144, :_reduce_19,
  2, 144, :_reduce_20,
  3, 144, :_reduce_21,
  3, 144, :_reduce_22,
  3, 144, :_reduce_23,
  3, 144, :_reduce_24,
  3, 144, :_reduce_25,
  4, 144, :_reduce_26,
  3, 144, :_reduce_27,
  3, 144, :_reduce_28,
  3, 144, :_reduce_29,
  6, 144, :_reduce_30,
  5, 144, :_reduce_31,
  5, 144, :_reduce_32,
  5, 144, :_reduce_33,
  5, 144, :_reduce_34,
  3, 144, :_reduce_35,
  3, 144, :_reduce_36,
  3, 144, :_reduce_37,
  3, 144, :_reduce_38,
  1, 144, :_reduce_none,
  1, 164, :_reduce_none,
  3, 164, :_reduce_41,
  3, 164, :_reduce_42,
  3, 164, :_reduce_43,
  2, 164, :_reduce_44,
  1, 164, :_reduce_none,
  1, 153, :_reduce_none,
  1, 155, :_reduce_none,
  1, 155, :_reduce_none,
  1, 169, :_reduce_none,
  4, 169, :_reduce_50,
  4, 169, :_reduce_51,
  0, 175, :_reduce_52,
  5, 173, :_reduce_53,
  2, 168, :_reduce_54,
  3, 168, :_reduce_55,
  4, 168, :_reduce_56,
  5, 168, :_reduce_57,
  4, 168, :_reduce_58,
  5, 168, :_reduce_59,
  2, 168, :_reduce_60,
  2, 168, :_reduce_61,
  2, 168, :_reduce_62,
  2, 168, :_reduce_63,
  2, 168, :_reduce_64,
  1, 156, :_reduce_65,
  3, 156, :_reduce_66,
  1, 179, :_reduce_67,
  3, 179, :_reduce_68,
  1, 178, :_reduce_none,
  2, 178, :_reduce_70,
  3, 178, :_reduce_71,
  5, 178, :_reduce_72,
  2, 178, :_reduce_73,
  4, 178, :_reduce_74,
  2, 178, :_reduce_75,
  4, 178, :_reduce_76,
  1, 178, :_reduce_77,
  3, 178, :_reduce_78,
  1, 182, :_reduce_none,
  3, 182, :_reduce_80,
  2, 181, :_reduce_81,
  3, 181, :_reduce_82,
  1, 184, :_reduce_83,
  3, 184, :_reduce_84,
  1, 183, :_reduce_85,
  4, 183, :_reduce_86,
  3, 183, :_reduce_87,
  3, 183, :_reduce_88,
  3, 183, :_reduce_89,
  3, 183, :_reduce_90,
  2, 183, :_reduce_91,
  1, 183, :_reduce_92,
  1, 154, :_reduce_93,
  4, 154, :_reduce_94,
  3, 154, :_reduce_95,
  3, 154, :_reduce_96,
  3, 154, :_reduce_97,
  3, 154, :_reduce_98,
  2, 154, :_reduce_99,
  1, 154, :_reduce_100,
  1, 186, :_reduce_101,
  1, 186, :_reduce_none,
  2, 187, :_reduce_103,
  1, 187, :_reduce_104,
  3, 187, :_reduce_105,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 191, :_reduce_111,
  1, 191, :_reduce_none,
  1, 151, :_reduce_none,
  1, 151, :_reduce_none,
  1, 152, :_reduce_115,
  0, 194, :_reduce_116,
  4, 152, :_reduce_117,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  3, 167, :_reduce_188,
  5, 167, :_reduce_189,
  3, 167, :_reduce_190,
  5, 167, :_reduce_191,
  6, 167, :_reduce_192,
  5, 167, :_reduce_193,
  5, 167, :_reduce_194,
  5, 167, :_reduce_195,
  5, 167, :_reduce_196,
  4, 167, :_reduce_197,
  3, 167, :_reduce_198,
  3, 167, :_reduce_199,
  3, 167, :_reduce_200,
  3, 167, :_reduce_201,
  3, 167, :_reduce_202,
  3, 167, :_reduce_203,
  3, 167, :_reduce_204,
  3, 167, :_reduce_205,
  3, 167, :_reduce_206,
  4, 167, :_reduce_207,
  4, 167, :_reduce_208,
  2, 167, :_reduce_209,
  2, 167, :_reduce_210,
  3, 167, :_reduce_211,
  3, 167, :_reduce_212,
  3, 167, :_reduce_213,
  3, 167, :_reduce_214,
  3, 167, :_reduce_215,
  3, 167, :_reduce_216,
  3, 167, :_reduce_217,
  3, 167, :_reduce_218,
  3, 167, :_reduce_219,
  3, 167, :_reduce_220,
  3, 167, :_reduce_221,
  3, 167, :_reduce_222,
  3, 167, :_reduce_223,
  2, 167, :_reduce_224,
  2, 167, :_reduce_225,
  3, 167, :_reduce_226,
  3, 167, :_reduce_227,
  3, 167, :_reduce_228,
  3, 167, :_reduce_229,
  3, 167, :_reduce_230,
  6, 167, :_reduce_231,
  1, 167, :_reduce_none,
  1, 163, :_reduce_none,
  1, 196, :_reduce_none,
  2, 196, :_reduce_none,
  4, 196, :_reduce_236,
  2, 196, :_reduce_237,
  3, 201, :_reduce_238,
  0, 202, :_reduce_239,
  1, 202, :_reduce_none,
  0, 159, :_reduce_241,
  1, 159, :_reduce_none,
  1, 177, :_reduce_243,
  2, 177, :_reduce_244,
  2, 177, :_reduce_245,
  4, 177, :_reduce_246,
  6, 177, :_reduce_247,
  1, 177, :_reduce_248,
  4, 205, :_reduce_249,
  3, 205, :_reduce_250,
  2, 205, :_reduce_251,
  4, 205, :_reduce_252,
  6, 205, :_reduce_253,
  1, 205, :_reduce_254,
  0, 207, :_reduce_255,
  2, 172, :_reduce_256,
  1, 206, :_reduce_257,
  0, 208, :_reduce_258,
  3, 206, :_reduce_259,
  0, 209, :_reduce_260,
  4, 206, :_reduce_261,
  2, 204, :_reduce_262,
  2, 203, :_reduce_263,
  1, 203, :_reduce_264,
  0, 203, :_reduce_265,
  1, 198, :_reduce_266,
  2, 198, :_reduce_267,
  3, 198, :_reduce_268,
  4, 198, :_reduce_269,
  3, 162, :_reduce_270,
  4, 162, :_reduce_271,
  2, 162, :_reduce_272,
  1, 195, :_reduce_none,
  1, 195, :_reduce_none,
  1, 195, :_reduce_none,
  1, 195, :_reduce_none,
  1, 195, :_reduce_none,
  1, 195, :_reduce_none,
  1, 195, :_reduce_none,
  1, 195, :_reduce_none,
  1, 195, :_reduce_281,
  3, 195, :_reduce_282,
  0, 233, :_reduce_283,
  4, 195, :_reduce_284,
  3, 195, :_reduce_285,
  3, 195, :_reduce_286,
  2, 195, :_reduce_287,
  3, 195, :_reduce_288,
  3, 195, :_reduce_289,
  1, 195, :_reduce_290,
  4, 195, :_reduce_291,
  3, 195, :_reduce_292,
  1, 195, :_reduce_293,
  5, 195, :_reduce_294,
  4, 195, :_reduce_295,
  3, 195, :_reduce_296,
  2, 195, :_reduce_297,
  1, 195, :_reduce_none,
  2, 195, :_reduce_299,
  0, 234, :_reduce_300,
  3, 195, :_reduce_301,
  6, 195, :_reduce_302,
  6, 195, :_reduce_303,
  0, 235, :_reduce_304,
  0, 236, :_reduce_305,
  7, 195, :_reduce_306,
  0, 237, :_reduce_307,
  0, 238, :_reduce_308,
  7, 195, :_reduce_309,
  5, 195, :_reduce_310,
  4, 195, :_reduce_311,
  0, 239, :_reduce_312,
  0, 240, :_reduce_313,
  9, 195, :_reduce_314,
  0, 241, :_reduce_315,
  6, 195, :_reduce_316,
  0, 242, :_reduce_317,
  7, 195, :_reduce_318,
  0, 243, :_reduce_319,
  5, 195, :_reduce_320,
  0, 244, :_reduce_321,
  6, 195, :_reduce_322,
  0, 245, :_reduce_323,
  0, 246, :_reduce_324,
  9, 195, :_reduce_325,
  1, 195, :_reduce_326,
  1, 195, :_reduce_327,
  1, 195, :_reduce_328,
  1, 195, :_reduce_329,
  1, 158, :_reduce_none,
  1, 226, :_reduce_331,
  1, 229, :_reduce_332,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  2, 221, :_reduce_335,
  1, 223, :_reduce_none,
  1, 223, :_reduce_none,
  1, 222, :_reduce_none,
  5, 222, :_reduce_339,
  1, 148, :_reduce_none,
  2, 148, :_reduce_341,
  1, 225, :_reduce_none,
  1, 225, :_reduce_none,
  1, 247, :_reduce_none,
  3, 247, :_reduce_345,
  1, 250, :_reduce_346,
  3, 250, :_reduce_347,
  1, 249, :_reduce_none,
  4, 249, :_reduce_349,
  6, 249, :_reduce_350,
  3, 249, :_reduce_351,
  5, 249, :_reduce_352,
  2, 249, :_reduce_353,
  4, 249, :_reduce_354,
  1, 249, :_reduce_355,
  3, 249, :_reduce_356,
  6, 251, :_reduce_357,
  8, 251, :_reduce_358,
  4, 251, :_reduce_359,
  6, 251, :_reduce_360,
  4, 251, :_reduce_361,
  2, 251, :_reduce_none,
  6, 251, :_reduce_363,
  2, 251, :_reduce_364,
  4, 251, :_reduce_365,
  6, 251, :_reduce_366,
  2, 251, :_reduce_367,
  4, 251, :_reduce_368,
  2, 251, :_reduce_369,
  4, 251, :_reduce_370,
  1, 251, :_reduce_371,
  0, 174, :_reduce_372,
  1, 174, :_reduce_373,
  3, 257, :_reduce_374,
  1, 257, :_reduce_375,
  4, 257, :_reduce_376,
  0, 258, :_reduce_377,
  2, 258, :_reduce_378,
  1, 259, :_reduce_379,
  3, 259, :_reduce_380,
  1, 260, :_reduce_381,
  1, 260, :_reduce_none,
  0, 264, :_reduce_383,
  3, 220, :_reduce_384,
  4, 262, :_reduce_385,
  1, 262, :_reduce_386,
  0, 267, :_reduce_387,
  4, 263, :_reduce_388,
  0, 268, :_reduce_389,
  4, 263, :_reduce_390,
  0, 269, :_reduce_391,
  5, 266, :_reduce_392,
  2, 170, :_reduce_393,
  4, 170, :_reduce_394,
  4, 170, :_reduce_395,
  2, 219, :_reduce_396,
  4, 219, :_reduce_397,
  4, 219, :_reduce_398,
  3, 219, :_reduce_399,
  3, 219, :_reduce_400,
  3, 219, :_reduce_401,
  2, 219, :_reduce_402,
  1, 219, :_reduce_403,
  4, 219, :_reduce_404,
  0, 271, :_reduce_405,
  5, 218, :_reduce_406,
  0, 272, :_reduce_407,
  5, 218, :_reduce_408,
  5, 224, :_reduce_409,
  1, 273, :_reduce_410,
  1, 273, :_reduce_none,
  6, 147, :_reduce_412,
  0, 147, :_reduce_413,
  1, 274, :_reduce_414,
  1, 274, :_reduce_none,
  1, 274, :_reduce_none,
  2, 275, :_reduce_417,
  1, 275, :_reduce_none,
  2, 149, :_reduce_419,
  1, 149, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 211, :_reduce_424,
  1, 277, :_reduce_425,
  2, 277, :_reduce_426,
  3, 278, :_reduce_427,
  1, 278, :_reduce_428,
  1, 278, :_reduce_429,
  3, 212, :_reduce_430,
  4, 213, :_reduce_431,
  3, 214, :_reduce_432,
  0, 282, :_reduce_433,
  3, 282, :_reduce_434,
  1, 283, :_reduce_435,
  2, 283, :_reduce_436,
  3, 215, :_reduce_437,
  0, 285, :_reduce_438,
  3, 285, :_reduce_439,
  0, 279, :_reduce_440,
  2, 279, :_reduce_441,
  0, 280, :_reduce_442,
  2, 280, :_reduce_443,
  0, 281, :_reduce_444,
  2, 281, :_reduce_445,
  1, 284, :_reduce_446,
  2, 284, :_reduce_447,
  0, 287, :_reduce_448,
  4, 284, :_reduce_449,
  1, 286, :_reduce_450,
  1, 286, :_reduce_451,
  1, 286, :_reduce_452,
  1, 286, :_reduce_none,
  1, 192, :_reduce_454,
  3, 193, :_reduce_455,
  1, 276, :_reduce_456,
  1, 276, :_reduce_457,
  2, 276, :_reduce_458,
  2, 276, :_reduce_459,
  1, 185, :_reduce_460,
  1, 185, :_reduce_461,
  1, 185, :_reduce_462,
  1, 185, :_reduce_463,
  1, 185, :_reduce_464,
  1, 185, :_reduce_465,
  1, 185, :_reduce_466,
  1, 185, :_reduce_467,
  1, 185, :_reduce_468,
  1, 185, :_reduce_469,
  1, 185, :_reduce_470,
  1, 185, :_reduce_471,
  1, 216, :_reduce_472,
  1, 157, :_reduce_473,
  1, 161, :_reduce_474,
  1, 161, :_reduce_475,
  1, 227, :_reduce_476,
  3, 227, :_reduce_477,
  2, 227, :_reduce_478,
  3, 230, :_reduce_479,
  2, 230, :_reduce_480,
  6, 265, :_reduce_481,
  8, 265, :_reduce_482,
  4, 265, :_reduce_483,
  6, 265, :_reduce_484,
  4, 265, :_reduce_485,
  6, 265, :_reduce_486,
  2, 265, :_reduce_487,
  4, 265, :_reduce_488,
  6, 265, :_reduce_489,
  2, 265, :_reduce_490,
  4, 265, :_reduce_491,
  2, 265, :_reduce_492,
  4, 265, :_reduce_493,
  1, 265, :_reduce_494,
  0, 265, :_reduce_495,
  1, 261, :_reduce_496,
  1, 261, :_reduce_497,
  1, 261, :_reduce_498,
  1, 261, :_reduce_499,
  1, 248, :_reduce_none,
  1, 248, :_reduce_501,
  3, 248, :_reduce_502,
  2, 248, :_reduce_503,
  1, 289, :_reduce_none,
  3, 289, :_reduce_505,
  1, 252, :_reduce_506,
  3, 252, :_reduce_507,
  3, 290, :_reduce_508,
  3, 291, :_reduce_509,
  1, 253, :_reduce_510,
  3, 253, :_reduce_511,
  1, 288, :_reduce_512,
  3, 288, :_reduce_513,
  1, 292, :_reduce_none,
  1, 292, :_reduce_none,
  2, 254, :_reduce_516,
  1, 254, :_reduce_517,
  1, 293, :_reduce_none,
  1, 293, :_reduce_none,
  2, 256, :_reduce_520,
  2, 255, :_reduce_521,
  0, 255, :_reduce_522,
  1, 231, :_reduce_none,
  3, 231, :_reduce_524,
  0, 217, :_reduce_525,
  2, 217, :_reduce_none,
  1, 200, :_reduce_527,
  3, 200, :_reduce_528,
  3, 294, :_reduce_529,
  2, 294, :_reduce_530,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 171, :_reduce_none,
  1, 171, :_reduce_none,
  1, 171, :_reduce_none,
  1, 171, :_reduce_none,
  1, 270, :_reduce_none,
  1, 270, :_reduce_none,
  1, 270, :_reduce_none,
  1, 232, :_reduce_none,
  1, 232, :_reduce_none,
  0, 141, :_reduce_none,
  1, 141, :_reduce_none,
  0, 166, :_reduce_none,
  1, 166, :_reduce_none,
  2, 180, :_reduce_547,
  2, 160, :_reduce_548,
  0, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 228, :_reduce_552,
  1, 228, :_reduce_none,
  1, 143, :_reduce_none,
  2, 143, :_reduce_none,
  0, 197, :_reduce_556 ]

racc_reduce_n = 557

racc_shift_n = 972

racc_token_table = {
  false => 0,
  :error => 1,
  :kCLASS => 2,
  :kMODULE => 3,
  :kDEF => 4,
  :kUNDEF => 5,
  :kBEGIN => 6,
  :kRESCUE => 7,
  :kENSURE => 8,
  :kEND => 9,
  :kIF => 10,
  :kUNLESS => 11,
  :kTHEN => 12,
  :kELSIF => 13,
  :kELSE => 14,
  :kCASE => 15,
  :kWHEN => 16,
  :kWHILE => 17,
  :kUNTIL => 18,
  :kFOR => 19,
  :kBREAK => 20,
  :kNEXT => 21,
  :kREDO => 22,
  :kRETRY => 23,
  :kIN => 24,
  :kDO => 25,
  :kDO_COND => 26,
  :kDO_BLOCK => 27,
  :kDO_LAMBDA => 28,
  :kRETURN => 29,
  :kYIELD => 30,
  :kSUPER => 31,
  :kSELF => 32,
  :kNIL => 33,
  :kTRUE => 34,
  :kFALSE => 35,
  :kAND => 36,
  :kOR => 37,
  :kNOT => 38,
  :kIF_MOD => 39,
  :kUNLESS_MOD => 40,
  :kWHILE_MOD => 41,
  :kUNTIL_MOD => 42,
  :kRESCUE_MOD => 43,
  :kALIAS => 44,
  :kDEFINED => 45,
  :klBEGIN => 46,
  :klEND => 47,
  :k__LINE__ => 48,
  :k__FILE__ => 49,
  :k__ENCODING__ => 50,
  :tIDENTIFIER => 51,
  :tFID => 52,
  :tGVAR => 53,
  :tIVAR => 54,
  :tCONSTANT => 55,
  :tLABEL => 56,
  :tCVAR => 57,
  :tNTH_REF => 58,
  :tBACK_REF => 59,
  :tSTRING_CONTENT => 60,
  :tINTEGER => 61,
  :tFLOAT => 62,
  :tUPLUS => 63,
  :tUMINUS => 64,
  :tUNARY_NUM => 65,
  :tPOW => 66,
  :tCMP => 67,
  :tEQ => 68,
  :tEQQ => 69,
  :tNEQ => 70,
  :tGEQ => 71,
  :tLEQ => 72,
  :tANDOP => 73,
  :tOROP => 74,
  :tMATCH => 75,
  :tNMATCH => 76,
  :tDOT => 77,
  :tDOT2 => 78,
  :tDOT3 => 79,
  :tAREF => 80,
  :tASET => 81,
  :tLSHFT => 82,
  :tRSHFT => 83,
  :tCOLON2 => 84,
  :tCOLON3 => 85,
  :tOP_ASGN => 86,
  :tASSOC => 87,
  :tLPAREN => 88,
  :tLPAREN2 => 89,
  :tRPAREN => 90,
  :tLPAREN_ARG => 91,
  :tLBRACK => 92,
  :tLBRACK2 => 93,
  :tRBRACK => 94,
  :tLBRACE => 95,
  :tLBRACE_ARG => 96,
  :tSTAR => 97,
  :tSTAR2 => 98,
  :tAMPER => 99,
  :tAMPER2 => 100,
  :tTILDE => 101,
  :tPERCENT => 102,
  :tDIVIDE => 103,
  :tPLUS => 104,
  :tMINUS => 105,
  :tLT => 106,
  :tGT => 107,
  :tPIPE => 108,
  :tBANG => 109,
  :tCARET => 110,
  :tLCURLY => 111,
  :tRCURLY => 112,
  :tBACK_REF2 => 113,
  :tSYMBEG => 114,
  :tSTRING_BEG => 115,
  :tXSTRING_BEG => 116,
  :tREGEXP_BEG => 117,
  :tREGEXP_OPT => 118,
  :tWORDS_BEG => 119,
  :tQWORDS_BEG => 120,
  :tSTRING_DBEG => 121,
  :tSTRING_DVAR => 122,
  :tSTRING_END => 123,
  :tSTRING => 124,
  :tSYMBOL => 125,
  :tNL => 126,
  :tEH => 127,
  :tCOLON => 128,
  :tCOMMA => 129,
  :tSPACE => 130,
  :tSEMI => 131,
  :tLAMBDA => 132,
  :tLAMBEG => 133,
  :tCHARACTER => 134,
  :tEQL => 135,
  :tLOWEST => 136 }

racc_nt_base = 137

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]
Ractor.make_shareable(Racc_arg) if defined?(Ractor)

Racc_token_to_s_table = [
  "$end",
  "error",
  "kCLASS",
  "kMODULE",
  "kDEF",
  "kUNDEF",
  "kBEGIN",
  "kRESCUE",
  "kENSURE",
  "kEND",
  "kIF",
  "kUNLESS",
  "kTHEN",
  "kELSIF",
  "kELSE",
  "kCASE",
  "kWHEN",
  "kWHILE",
  "kUNTIL",
  "kFOR",
  "kBREAK",
  "kNEXT",
  "kREDO",
  "kRETRY",
  "kIN",
  "kDO",
  "kDO_COND",
  "kDO_BLOCK",
  "kDO_LAMBDA",
  "kRETURN",
  "kYIELD",
  "kSUPER",
  "kSELF",
  "kNIL",
  "kTRUE",
  "kFALSE",
  "kAND",
  "kOR",
  "kNOT",
  "kIF_MOD",
  "kUNLESS_MOD",
  "kWHILE_MOD",
  "kUNTIL_MOD",
  "kRESCUE_MOD",
  "kALIAS",
  "kDEFINED",
  "klBEGIN",
  "klEND",
  "k__LINE__",
  "k__FILE__",
  "k__ENCODING__",
  "tIDENTIFIER",
  "tFID",
  "tGVAR",
  "tIVAR",
  "tCONSTANT",
  "tLABEL",
  "tCVAR",
  "tNTH_REF",
  "tBACK_REF",
  "tSTRING_CONTENT",
  "tINTEGER",
  "tFLOAT",
  "tUPLUS",
  "tUMINUS",
  "tUNARY_NUM",
  "tPOW",
  "tCMP",
  "tEQ",
  "tEQQ",
  "tNEQ",
  "tGEQ",
  "tLEQ",
  "tANDOP",
  "tOROP",
  "tMATCH",
  "tNMATCH",
  "tDOT",
  "tDOT2",
  "tDOT3",
  "tAREF",
  "tASET",
  "tLSHFT",
  "tRSHFT",
  "tCOLON2",
  "tCOLON3",
  "tOP_ASGN",
  "tASSOC",
  "tLPAREN",
  "tLPAREN2",
  "tRPAREN",
  "tLPAREN_ARG",
  "tLBRACK",
  "tLBRACK2",
  "tRBRACK",
  "tLBRACE",
  "tLBRACE_ARG",
  "tSTAR",
  "tSTAR2",
  "tAMPER",
  "tAMPER2",
  "tTILDE",
  "tPERCENT",
  "tDIVIDE",
  "tPLUS",
  "tMINUS",
  "tLT",
  "tGT",
  "tPIPE",
  "tBANG",
  "tCARET",
  "tLCURLY",
  "tRCURLY",
  "tBACK_REF2",
  "tSYMBEG",
  "tSTRING_BEG",
  "tXSTRING_BEG",
  "tREGEXP_BEG",
  "tREGEXP_OPT",
  "tWORDS_BEG",
  "tQWORDS_BEG",
  "tSTRING_DBEG",
  "tSTRING_DVAR",
  "tSTRING_END",
  "tSTRING",
  "tSYMBOL",
  "tNL",
  "tEH",
  "tCOLON",
  "tCOMMA",
  "tSPACE",
  "tSEMI",
  "tLAMBDA",
  "tLAMBEG",
  "tCHARACTER",
  "tEQL",
  "tLOWEST",
  "$start",
  "program",
  "top_compstmt",
  "top_stmts",
  "opt_terms",
  "top_stmt",
  "terms",
  "stmt",
  "bodystmt",
  "compstmt",
  "opt_rescue",
  "opt_else",
  "opt_ensure",
  "stmts",
  "fitem",
  "undef_list",
  "expr_value",
  "lhs",
  "command_call",
  "mlhs",
  "var_lhs",
  "primary_value",
  "opt_call_args",
  "rbracket",
  "backref",
  "mrhs",
  "arg_value",
  "expr",
  "@1",
  "opt_nl",
  "arg",
  "command",
  "block_command",
  "block_call",
  "operation2",
  "command_args",
  "cmd_brace_block",
  "opt_block_param",
  "@2",
  "operation",
  "call_args",
  "mlhs_basic",
  "mlhs_inner",
  "rparen",
  "mlhs_head",
  "mlhs_item",
  "mlhs_node",
  "mlhs_post",
  "variable",
  "cname",
  "cpath",
  "fname",
  "op",
  "reswords",
  "fsym",
  "symbol",
  "dsym",
  "@3",
  "primary",
  "aref_args",
  "none",
  "args",
  "trailer",
  "assocs",
  "paren_args",
  "opt_paren_args",
  "opt_block_arg",
  "block_arg",
  "call_args2",
  "open_args",
  "@4",
  "@5",
  "@6",
  "literal",
  "strings",
  "xstring",
  "regexp",
  "words",
  "qwords",
  "var_ref",
  "assoc_list",
  "brace_block",
  "method_call",
  "lambda",
  "then",
  "if_tail",
  "do",
  "case_body",
  "for_var",
  "k_class",
  "superclass",
  "term",
  "k_module",
  "f_arglist",
  "singleton",
  "dot_or_colon",
  "@7",
  "@8",
  "@9",
  "@10",
  "@11",
  "@12",
  "@13",
  "@14",
  "@15",
  "@16",
  "@17",
  "@18",
  "@19",
  "@20",
  "f_marg",
  "f_norm_arg",
  "f_margs",
  "f_marg_list",
  "block_param",
  "f_arg",
  "f_block_optarg",
  "f_rest_arg",
  "opt_f_block_arg",
  "f_block_arg",
  "block_param_def",
  "opt_bv_decl",
  "bv_decls",
  "bvar",
  "f_bad_arg",
  "f_larglist",
  "lambda_body",
  "@21",
  "f_args",
  "do_block",
  "@22",
  "@23",
  "@24",
  "operation3",
  "@25",
  "@26",
  "cases",
  "exc_list",
  "exc_var",
  "numeric",
  "string",
  "string1",
  "string_contents",
  "xstring_contents",
  "regexp_contents",
  "word_list",
  "word",
  "string_content",
  "qword_list",
  "string_dvar",
  "@27",
  "f_optarg",
  "f_arg_item",
  "f_opt",
  "f_block_opt",
  "restarg_mark",
  "blkarg_mark",
  "assoc" ]
Ractor.make_shareable(Racc_token_to_s_table) if defined?(Ractor)

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

# reduce 1 omitted

def _reduce_2(val, _values, result)
                      result = @builder.compstmt(val[0])

    result
end

def _reduce_3(val, _values, result)
                      result = []

    result
end

def _reduce_4(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_5(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_6(val, _values, result)
                      result = [ val[1] ]

    result
end

# reduce 7 omitted

def _reduce_8(val, _values, result)
                      result = @builder.preexe(val[0], val[1], val[2], val[3])

    result
end

def _reduce_9(val, _values, result)
                      rescue_bodies     = val[1]
                      else_t,   else_   = val[2]
                      ensure_t, ensure_ = val[3]

                      if rescue_bodies.empty? && !else_t.nil?
                        diagnostic :warning, :useless_else, nil, else_t
                      end

                      result = @builder.begin_body(val[0],
                                  rescue_bodies,
                                  else_t,   else_,
                                  ensure_t, ensure_)

    result
end

def _reduce_10(val, _values, result)
                      result = @builder.compstmt(val[0])

    result
end

def _reduce_11(val, _values, result)
                      result = []

    result
end

def _reduce_12(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_13(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_14(val, _values, result)
                      result = [ val[1] ]

    result
end

def _reduce_15(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_16(val, _values, result)
                      result = @builder.alias(val[0], val[1], val[3])

    result
end

def _reduce_17(val, _values, result)
                      result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.gvar(val[2]))

    result
end

def _reduce_18(val, _values, result)
                      result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.back_ref(val[2]))

    result
end

def _reduce_19(val, _values, result)
                      diagnostic :error, :nth_ref_alias, nil, val[2]

    result
end

def _reduce_20(val, _values, result)
                      result = @builder.undef_method(val[0], val[1])

    result
end

def _reduce_21(val, _values, result)
                      result = @builder.condition_mod(val[0], nil,
                                                      val[1], val[2])

    result
end

def _reduce_22(val, _values, result)
                      result = @builder.condition_mod(nil, val[0],
                                                      val[1], val[2])

    result
end

def _reduce_23(val, _values, result)
                      result = @builder.loop_mod(:while, val[0], val[1], val[2])

    result
end

def _reduce_24(val, _values, result)
                      result = @builder.loop_mod(:until, val[0], val[1], val[2])

    result
end

def _reduce_25(val, _values, result)
                      rescue_body = @builder.rescue_body(val[1],
                                        nil, nil, nil,
                                        nil, val[2])

                      result = @builder.begin_body(val[0], [ rescue_body ])

    result
end

def _reduce_26(val, _values, result)
                      result = @builder.postexe(val[0], val[1], val[2], val[3])

    result
end

def _reduce_27(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

def _reduce_28(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1], val[2])

    result
end

def _reduce_29(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_30(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])

    result
end

def _reduce_31(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_32(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_33(val, _values, result)
                      diagnostic :error, :const_reassignment, nil, val[3]

    result
end

def _reduce_34(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_35(val, _values, result)
                      @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_36(val, _values, result)
                      result = @builder.assign(val[0], val[1],
                                  @builder.array(nil, val[2], nil))

    result
end

def _reduce_37(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1], val[2])

    result
end

def _reduce_38(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1],
                                  @builder.array(nil, val[2], nil))

    result
end

# reduce 39 omitted

# reduce 40 omitted

def _reduce_41(val, _values, result)
                      result = @builder.logical_op(:and, val[0], val[1], val[2])

    result
end

def _reduce_42(val, _values, result)
                      result = @builder.logical_op(:or, val[0], val[1], val[2])

    result
end

def _reduce_43(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[2], nil)

    result
end

def _reduce_44(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[1], nil)

    result
end

# reduce 45 omitted

# reduce 46 omitted

# reduce 47 omitted

# reduce 48 omitted

# reduce 49 omitted

def _reduce_50(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  *val[3])

    result
end

def _reduce_51(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  *val[3])

    result
end

def _reduce_52(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_53(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_54(val, _values, result)
                      result = @builder.call_method(nil, nil, val[0],
                                  *val[1])

    result
end

def _reduce_55(val, _values, result)
                      method_call = @builder.call_method(nil, nil, val[0],
                                        *val[1])

                      begin_t, args, body, end_t = val[2]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_56(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  *val[3])

    result
end

def _reduce_57(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                        *val[3])

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_58(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  *val[3])

    result
end

def _reduce_59(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                        *val[3])

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_60(val, _values, result)
                      result = @builder.keyword_cmd(:super, val[0],
                                  *val[1])

    result
end

def _reduce_61(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0],
                                  *val[1])

    result
end

def _reduce_62(val, _values, result)
                      result = @builder.keyword_cmd(:return, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_63(val, _values, result)
                      result = @builder.keyword_cmd(:break, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_64(val, _values, result)
                      result = @builder.keyword_cmd(:next, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_65(val, _values, result)
                      result = @builder.multi_lhs(nil, val[0], nil)

    result
end

def _reduce_66(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_67(val, _values, result)
                      result = @builder.multi_lhs(nil, val[0], nil)

    result
end

def _reduce_68(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

# reduce 69 omitted

def _reduce_70(val, _values, result)
                      result = val[0].
                                  push(val[1])

    result
end

def _reduce_71(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1], val[2]))

    result
end

def _reduce_72(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1], val[2])).
                                  concat(val[4])

    result
end

def _reduce_73(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1]))

    result
end

def _reduce_74(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1])).
                                  concat(val[3])

    result
end

def _reduce_75(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

def _reduce_76(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]),
                                 *val[3] ]

    result
end

def _reduce_77(val, _values, result)
                      result = [ @builder.splat(val[0]) ]

    result
end

def _reduce_78(val, _values, result)
                      result = [ @builder.splat(val[0]),
                                 *val[2] ]

    result
end

# reduce 79 omitted

def _reduce_80(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_81(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_82(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_83(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_84(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_85(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_86(val, _values, result)
                      result = @builder.index_asgn(val[0], val[1], val[2], val[3])

    result
end

def _reduce_87(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_88(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_89(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_90(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))

    result
end

def _reduce_91(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))

    result
end

def _reduce_92(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_93(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_94(val, _values, result)
                      result = @builder.index_asgn(val[0], val[1], val[2], val[3])

    result
end

def _reduce_95(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_96(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_97(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_98(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))

    result
end

def _reduce_99(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))

    result
end

def _reduce_100(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_101(val, _values, result)
                      diagnostic :error, :module_name_const, nil, val[0]

    result
end

# reduce 102 omitted

def _reduce_103(val, _values, result)
                      result = @builder.const_global(val[0], val[1])

    result
end

def _reduce_104(val, _values, result)
                      result = @builder.const(val[0])

    result
end

def _reduce_105(val, _values, result)
                      result = @builder.const_fetch(val[0], val[1], val[2])

    result
end

# reduce 106 omitted

# reduce 107 omitted

# reduce 108 omitted

# reduce 109 omitted

# reduce 110 omitted

def _reduce_111(val, _values, result)
                      result = @builder.symbol_internal(val[0])

    result
end

# reduce 112 omitted

# reduce 113 omitted

# reduce 114 omitted

def _reduce_115(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_116(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_117(val, _values, result)
                      result = val[0] << val[3]

    result
end

# reduce 118 omitted

# reduce 119 omitted

# reduce 120 omitted

# reduce 121 omitted

# reduce 122 omitted

# reduce 123 omitted

# reduce 124 omitted

# reduce 125 omitted

# reduce 126 omitted

# reduce 127 omitted

# reduce 128 omitted

# reduce 129 omitted

# reduce 130 omitted

# reduce 131 omitted

# reduce 132 omitted

# reduce 133 omitted

# reduce 134 omitted

# reduce 135 omitted

# reduce 136 omitted

# reduce 137 omitted

# reduce 138 omitted

# reduce 139 omitted

# reduce 140 omitted

# reduce 141 omitted

# reduce 142 omitted

# reduce 143 omitted

# reduce 144 omitted

# reduce 145 omitted

# reduce 146 omitted

# reduce 147 omitted

# reduce 148 omitted

# reduce 149 omitted

# reduce 150 omitted

# reduce 151 omitted

# reduce 152 omitted

# reduce 153 omitted

# reduce 154 omitted

# reduce 155 omitted

# reduce 156 omitted

# reduce 157 omitted

# reduce 158 omitted

# reduce 159 omitted

# reduce 160 omitted

# reduce 161 omitted

# reduce 162 omitted

# reduce 163 omitted

# reduce 164 omitted

# reduce 165 omitted

# reduce 166 omitted

# reduce 167 omitted

# reduce 168 omitted

# reduce 169 omitted

# reduce 170 omitted

# reduce 171 omitted

# reduce 172 omitted

# reduce 173 omitted

# reduce 174 omitted

# reduce 175 omitted

# reduce 176 omitted

# reduce 177 omitted

# reduce 178 omitted

# reduce 179 omitted

# reduce 180 omitted

# reduce 181 omitted

# reduce 182 omitted

# reduce 183 omitted

# reduce 184 omitted

# reduce 185 omitted

# reduce 186 omitted

# reduce 187 omitted

def _reduce_188(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

def _reduce_189(val, _values, result)
                      rescue_body = @builder.rescue_body(val[3],
                                        nil, nil, nil,
                                        nil, val[4])

                      rescue_ = @builder.begin_body(val[2], [ rescue_body ])

                      result  = @builder.assign(val[0], val[1], rescue_)

    result
end

def _reduce_190(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_191(val, _values, result)
                      rescue_body = @builder.rescue_body(val[3],
                                        nil, nil, nil,
                                        nil, val[4])

                      rescue_ = @builder.begin_body(val[2], [ rescue_body ])

                      result = @builder.op_assign(val[0], val[1], rescue_)

    result
end

def _reduce_192(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])

    result
end

def _reduce_193(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_194(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_195(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_196(val, _values, result)
                      diagnostic :error, :dynamic_const, nil, val[2], [ val[3] ]

    result
end

def _reduce_197(val, _values, result)
                      diagnostic :error, :dynamic_const, nil, val[1], [ val[2] ]

    result
end

def _reduce_198(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_199(val, _values, result)
                      result = @builder.range_inclusive(val[0], val[1], val[2])

    result
end

def _reduce_200(val, _values, result)
                      result = @builder.range_exclusive(val[0], val[1], val[2])

    result
end

def _reduce_201(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_202(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_203(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_204(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_205(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_206(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_207(val, _values, result)
                      result = @builder.unary_op(val[0],
                                  @builder.binary_op(
                                    @builder.integer(val[1]),
                                      val[2], val[3]))

    result
end

def _reduce_208(val, _values, result)
                      result = @builder.unary_op(val[0],
                                  @builder.binary_op(
                                    @builder.float(val[1]),
                                      val[2], val[3]))

    result
end

def _reduce_209(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_210(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_211(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_212(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_213(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_214(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_215(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_216(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_217(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_218(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_219(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_220(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_221(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_222(val, _values, result)
                      result = @builder.match_op(val[0], val[1], val[2])

    result
end

def _reduce_223(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_224(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[1], nil)

    result
end

def _reduce_225(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_226(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_227(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_228(val, _values, result)
                      result = @builder.logical_op(:and, val[0], val[1], val[2])

    result
end

def _reduce_229(val, _values, result)
                      result = @builder.logical_op(:or, val[0], val[1], val[2])

    result
end

def _reduce_230(val, _values, result)
                      result = @builder.keyword_cmd(:defined?, val[0], nil, [ val[2] ], nil)

    result
end

def _reduce_231(val, _values, result)
                      result = @builder.ternary(val[0], val[1],
                                                val[2], val[4], val[5])

    result
end

# reduce 232 omitted

# reduce 233 omitted

# reduce 234 omitted

# reduce 235 omitted

def _reduce_236(val, _values, result)
                      result = val[0] << @builder.associate(nil, val[2], nil)

    result
end

def _reduce_237(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]

    result
end

def _reduce_238(val, _values, result)
                      result = val

    result
end

def _reduce_239(val, _values, result)
                      result = [ nil, [], nil ]

    result
end

# reduce 240 omitted

def _reduce_241(val, _values, result)
                      result = []

    result
end

# reduce 242 omitted

def _reduce_243(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_244(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_245(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]
                      result.concat(val[1])

    result
end

def _reduce_246(val, _values, result)
                      assocs = @builder.associate(nil, val[2], nil)
                      result = val[0] << assocs
                      result.concat(val[3])

    result
end

def _reduce_247(val, _values, result)
                      val[2][-1] = @builder.objc_varargs(val[2][-1], val[4])
                      assocs = @builder.associate(nil, val[2], nil)
                      result = val[0] << assocs
                      result.concat(val[5])

    result
end

def _reduce_248(val, _values, result)
                      result =  [ val[0] ]

    result
end

def _reduce_249(val, _values, result)
                      result = [ val[0], *val[2].concat(val[3]) ]

    result
end

def _reduce_250(val, _values, result)
                      result = [ val[0], val[2] ]

    result
end

def _reduce_251(val, _values, result)
                      result =  [ @builder.associate(nil, val[0], nil),
                                  *val[1] ]

    result
end

def _reduce_252(val, _values, result)
                      result =  [ val[0],
                                  @builder.associate(nil, val[2], nil),
                                  *val[3] ]

    result
end

def _reduce_253(val, _values, result)
                      result =  [ val[0],
                                  *val[2].
                                    push(@builder.associate(nil, val[4], nil)).
                                    concat(val[5]) ]

    result
end

def _reduce_254(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_255(val, _values, result)
                      result = @lexer.cmdarg.dup
                      @lexer.cmdarg.push(true)

    result
end

def _reduce_256(val, _values, result)
                      @lexer.cmdarg = val[0]

                      result = val[1]

    result
end

def _reduce_257(val, _values, result)
                      result = [ nil, val[0], nil ]

    result
end

def _reduce_258(val, _values, result)
                      @lexer.state = :expr_endarg

    result
end

def _reduce_259(val, _values, result)
                      result = [ val[0], [], val[2] ]

    result
end

def _reduce_260(val, _values, result)
                      @lexer.state = :expr_endarg

    result
end

def _reduce_261(val, _values, result)
                      result = [ val[0], val[1], val[3] ]

    result
end

def _reduce_262(val, _values, result)
                      result = @builder.block_pass(val[0], val[1])

    result
end

def _reduce_263(val, _values, result)
                      result = [ val[1] ]

    result
end

def _reduce_264(val, _values, result)
                      result = []

    result
end

def _reduce_265(val, _values, result)
                      result = []

    result
end

def _reduce_266(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_267(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

def _reduce_268(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_269(val, _values, result)
                      result = val[0] << @builder.splat(val[2], val[3])

    result
end

def _reduce_270(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_271(val, _values, result)
                      result = val[0] << @builder.splat(val[2], val[3])

    result
end

def _reduce_272(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

# reduce 273 omitted

# reduce 274 omitted

# reduce 275 omitted

# reduce 276 omitted

# reduce 277 omitted

# reduce 278 omitted

# reduce 279 omitted

# reduce 280 omitted

def _reduce_281(val, _values, result)
                      result = @builder.call_method(nil, nil, val[0])

    result
end

def _reduce_282(val, _values, result)
                      result = @builder.begin_keyword(val[0], val[1], val[2])

    result
end

def _reduce_283(val, _values, result)
                      @lexer.state = :expr_endarg

    result
end

def _reduce_284(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[3])

    result
end

def _reduce_285(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_286(val, _values, result)
                      result = @builder.const_fetch(val[0], val[1], val[2])

    result
end

def _reduce_287(val, _values, result)
                      result = @builder.const_global(val[0], val[1])

    result
end

def _reduce_288(val, _values, result)
                      result = @builder.array(val[0], val[1], val[2])

    result
end

def _reduce_289(val, _values, result)
                      result = @builder.associate(val[0], val[1], val[2])

    result
end

def _reduce_290(val, _values, result)
                      result = @builder.keyword_cmd(:return, val[0])

    result
end

def _reduce_291(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0], val[1], val[2], val[3])

    result
end

def _reduce_292(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0], val[1], [], val[2])

    result
end

def _reduce_293(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0])

    result
end

def _reduce_294(val, _values, result)
                      result = @builder.keyword_cmd(:defined?, val[0],
                                                    val[2], [ val[3] ], val[4])

    result
end

def _reduce_295(val, _values, result)
                      result = @builder.not_op(val[0], val[1], val[2], val[3])

    result
end

def _reduce_296(val, _values, result)
                      result = @builder.not_op(val[0], val[1], nil, val[2])

    result
end

def _reduce_297(val, _values, result)
                      method_call = @builder.call_method(nil, nil, val[0])

                      begin_t, args, body, end_t = val[1]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

# reduce 298 omitted

def _reduce_299(val, _values, result)
                      begin_t, args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, args, body, end_t)

    result
end

def _reduce_300(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_301(val, _values, result)
                      lambda_call = @builder.call_lambda(val[0])

                      args, (begin_t, body, end_t) = val[2]
                      result      = @builder.block(lambda_call,
                                      begin_t, args, body, end_t)

                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_302(val, _values, result)
                      else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  val[3], else_t,
                                                  else_,  val[5])

    result
end

def _reduce_303(val, _values, result)
                      else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  else_,  else_t,
                                                  val[3], val[5])

    result
end

def _reduce_304(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_305(val, _values, result)
                      @lexer.cond.pop

    result
end

def _reduce_306(val, _values, result)
                      result = @builder.loop(:while, val[0], val[2], val[3],
                                             val[5], val[6])

    result
end

def _reduce_307(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_308(val, _values, result)
                      @lexer.cond.pop

    result
end

def _reduce_309(val, _values, result)
                      result = @builder.loop(:until, val[0], val[2], val[3],
                                             val[5], val[6])

    result
end

def _reduce_310(val, _values, result)
                      *when_bodies, (else_t, else_body) = *val[3]

                      result = @builder.case(val[0], val[1],
                                             when_bodies, else_t, else_body,
                                             val[4])

    result
end

def _reduce_311(val, _values, result)
                      *when_bodies, (else_t, else_body) = *val[2]

                      result = @builder.case(val[0], nil,
                                             when_bodies, else_t, else_body,
                                             val[3])

    result
end

def _reduce_312(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_313(val, _values, result)
                      @lexer.cond.pop

    result
end

def _reduce_314(val, _values, result)
                      result = @builder.for(val[0], val[1],
                                            val[2], val[4],
                                            val[5], val[7], val[8])

    result
end

def _reduce_315(val, _values, result)
                      local_push
                      @context.in_class = true

    result
end

def _reduce_316(val, _values, result)
                      k_class, ctx = val[0]
                      if @context.in_def
                        diagnostic :error, :class_in_def, nil, k_class
                      end

                      lt_t, superclass = val[2]
                      result = @builder.def_class(k_class, val[1],
                                                  lt_t, superclass,
                                                  val[4], val[5])

                      local_pop
                      @context.in_class = ctx.in_class

    result
end

def _reduce_317(val, _values, result)
                      @context.in_def = false
                      @context.in_class = false
                      local_push

    result
end

def _reduce_318(val, _values, result)
                      k_class, ctx = val[0]
                      result = @builder.def_sclass(k_class, val[1], val[2],
                                                   val[5], val[6])

                      local_pop
                      @context.in_def = ctx.in_def
                      @context.in_class = ctx.in_class

    result
end

def _reduce_319(val, _values, result)
                      @context.in_class = true
                      local_push

    result
end

def _reduce_320(val, _values, result)
                      k_mod, ctx = val[0]
                      if @context.in_def
                        diagnostic :error, :module_in_def, nil, k_mod
                      end

                      result = @builder.def_module(k_mod, val[1],
                                                   val[3], val[4])

                      local_pop
                      @context.in_class = ctx.in_class

    result
end

def _reduce_321(val, _values, result)
                      local_push
                      result = context.dup
                      @context.in_def = true

    result
end

def _reduce_322(val, _values, result)
                      result = @builder.def_method(val[0], val[1],
                                  val[3], val[4], val[5])

                      local_pop
                      @context.in_def = val[2].in_def

    result
end

def _reduce_323(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_324(val, _values, result)
                      local_push
                      result = context.dup
                      @context.in_def = true

    result
end

def _reduce_325(val, _values, result)
                      result = @builder.def_singleton(val[0], val[1], val[2],
                                  val[4], val[6], val[7], val[8])

                      local_pop
                      @context.in_def = val[5].in_def

    result
end

def _reduce_326(val, _values, result)
                      result = @builder.keyword_cmd(:break, val[0])

    result
end

def _reduce_327(val, _values, result)
                      result = @builder.keyword_cmd(:next, val[0])

    result
end

def _reduce_328(val, _values, result)
                      result = @builder.keyword_cmd(:redo, val[0])

    result
end

def _reduce_329(val, _values, result)
                      result = @builder.keyword_cmd(:retry, val[0])

    result
end

# reduce 330 omitted

def _reduce_331(val, _values, result)
                      result = [ val[0], @context.dup ]

    result
end

def _reduce_332(val, _values, result)
                      result = [ val[0], @context.dup ]

    result
end

# reduce 333 omitted

# reduce 334 omitted

def _reduce_335(val, _values, result)
                      result = val[1]

    result
end

# reduce 336 omitted

# reduce 337 omitted

# reduce 338 omitted

def _reduce_339(val, _values, result)
                      else_t, else_ = val[4]
                      result = [ val[0],
                                 @builder.condition(val[0], val[1], val[2],
                                                    val[3], else_t,
                                                    else_,  nil),
                               ]

    result
end

# reduce 340 omitted

def _reduce_341(val, _values, result)
                      result = val

    result
end

# reduce 342 omitted

# reduce 343 omitted

# reduce 344 omitted

def _reduce_345(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

def _reduce_346(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_347(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 348 omitted

def _reduce_349(val, _values, result)
                      result = val[0].
                                  push(@builder.objc_restarg(val[2], val[3]))

    result
end

def _reduce_350(val, _values, result)
                      result = val[0].
                                  push(@builder.objc_restarg(val[2], val[3])).
                                  concat(val[5])

    result
end

def _reduce_351(val, _values, result)
                      result = val[0].
                                  push(@builder.objc_restarg(val[2]))

    result
end

def _reduce_352(val, _values, result)
                      result = val[0].
                                  push(@builder.objc_restarg(val[2])).
                                  concat(val[4])

    result
end

def _reduce_353(val, _values, result)
                      result = [ @builder.objc_restarg(val[0], val[1]) ]

    result
end

def _reduce_354(val, _values, result)
                      result = [ @builder.objc_restarg(val[0], val[1]),
                                 *val[3] ]

    result
end

def _reduce_355(val, _values, result)
                      result = [ @builder.objc_restarg(val[0]) ]

    result
end

def _reduce_356(val, _values, result)
                      result = [ @builder.objc_restarg(val[0]),
                                 *val[2] ]

    result
end

def _reduce_357(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_358(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[6]).
                                  concat(val[7])

    result
end

def _reduce_359(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_360(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_361(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

# reduce 362 omitted

def _reduce_363(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_364(val, _values, result)
                      if val[1].empty? && val[0].size == 1
                        result = [@builder.procarg0(val[0][0])]
                      else
                        result = val[0].concat(val[1])
                      end

    result
end

def _reduce_365(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_366(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_367(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_368(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_369(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_370(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_371(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_372(val, _values, result)
                      result = @builder.args(nil, [], nil)

    result
end

def _reduce_373(val, _values, result)
                      @lexer.state = :expr_value

    result
end

def _reduce_374(val, _values, result)
                      result = @builder.args(val[0], val[1], val[2])

    result
end

def _reduce_375(val, _values, result)
                      result = @builder.args(val[0], [], val[0])

    result
end

def _reduce_376(val, _values, result)
                      result = @builder.args(val[0], val[1].concat(val[2]), val[3])

    result
end

def _reduce_377(val, _values, result)
                      result = []

    result
end

def _reduce_378(val, _values, result)
                      result = val[1]

    result
end

def _reduce_379(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_380(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_381(val, _values, result)
                      @static_env.declare val[0][0]
                      result = @builder.shadowarg(val[0])

    result
end

# reduce 382 omitted

def _reduce_383(val, _values, result)
                      @static_env.extend_dynamic

    result
end

def _reduce_384(val, _values, result)
                      result = [ val[1], val[2] ]

                      @static_env.unextend

    result
end

def _reduce_385(val, _values, result)
                      result = @builder.args(val[0], val[1].concat(val[2]), val[3])

    result
end

def _reduce_386(val, _values, result)
                      result = @builder.args(nil, val[0], nil)

    result
end

def _reduce_387(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_388(val, _values, result)
                      result = [ val[0], val[2], val[3] ]
                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_389(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_390(val, _values, result)
                      result = [ val[0], val[2], val[3] ]
                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_391(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_392(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_393(val, _values, result)
                      begin_t, block_args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, block_args, body, end_t)

    result
end

def _reduce_394(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_395(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_396(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.call_method(nil, nil, val[0],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_397(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_398(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_399(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2])

    result
end

def _reduce_400(val, _values, result)
                      lparen_t, args, rparen_t = val[2]
                      result = @builder.call_method(val[0], val[1], nil,
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_401(val, _values, result)
                      lparen_t, args, rparen_t = val[2]
                      result = @builder.call_method(val[0], val[1], nil,
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_402(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.keyword_cmd(:super, val[0],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_403(val, _values, result)
                      result = @builder.keyword_cmd(:zsuper, val[0])

    result
end

def _reduce_404(val, _values, result)
                      result = @builder.index(val[0], val[1], val[2], val[3])

    result
end

def _reduce_405(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_406(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_407(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_408(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_409(val, _values, result)
                      result = [ @builder.when(val[0], val[1], val[2], val[3]),
                                 *val[4] ]

    result
end

def _reduce_410(val, _values, result)
                      result = [ val[0] ]

    result
end

# reduce 411 omitted

def _reduce_412(val, _values, result)
                      assoc_t, exc_var = val[2]

                      if val[1]
                        exc_list = @builder.array(nil, val[1], nil)
                      end

                      result = [ @builder.rescue_body(val[0],
                                      exc_list, assoc_t, exc_var,
                                      val[3], val[4]),
                                 *val[5] ]

    result
end

def _reduce_413(val, _values, result)
                      result = []

    result
end

def _reduce_414(val, _values, result)
                      result = [ val[0] ]

    result
end

# reduce 415 omitted

# reduce 416 omitted

def _reduce_417(val, _values, result)
                      result = [ val[0], val[1] ]

    result
end

# reduce 418 omitted

def _reduce_419(val, _values, result)
                      result = [ val[0], val[1] ]

    result
end

# reduce 420 omitted

# reduce 421 omitted

# reduce 422 omitted

# reduce 423 omitted

def _reduce_424(val, _values, result)
                      result = @builder.string_compose(nil, val[0], nil)

    result
end

def _reduce_425(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_426(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_427(val, _values, result)
                      result = @builder.string_compose(val[0], val[1], val[2])

    result
end

def _reduce_428(val, _values, result)
                      result = @builder.string(val[0])

    result
end

def _reduce_429(val, _values, result)
                      result = @builder.character(val[0])

    result
end

def _reduce_430(val, _values, result)
                      result = @builder.xstring_compose(val[0], val[1], val[2])

    result
end

def _reduce_431(val, _values, result)
                      opts   = @builder.regexp_options(val[3])
                      result = @builder.regexp_compose(val[0], val[1], val[2], opts)

    result
end

def _reduce_432(val, _values, result)
                      result = @builder.words_compose(val[0], val[1], val[2])

    result
end

def _reduce_433(val, _values, result)
                      result = []

    result
end

def _reduce_434(val, _values, result)
                      result = val[0] << @builder.word(val[1])

    result
end

def _reduce_435(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_436(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_437(val, _values, result)
                      result = @builder.words_compose(val[0], val[1], val[2])

    result
end

def _reduce_438(val, _values, result)
                      result = []

    result
end

def _reduce_439(val, _values, result)
                      result = val[0] << @builder.string_internal(val[1])

    result
end

def _reduce_440(val, _values, result)
                      result = []

    result
end

def _reduce_441(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_442(val, _values, result)
                      result = []

    result
end

def _reduce_443(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_444(val, _values, result)
                      result = []

    result
end

def _reduce_445(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_446(val, _values, result)
                      result = @builder.string_internal(val[0])

    result
end

def _reduce_447(val, _values, result)
                      result = val[1]

    result
end

def _reduce_448(val, _values, result)
                      @lexer.cond.push(false)
                      @lexer.cmdarg.push(false)

    result
end

def _reduce_449(val, _values, result)
                      @lexer.cond.lexpop
                      @lexer.cmdarg.lexpop

                      result = @builder.begin(val[0], val[2], val[3])

    result
end

def _reduce_450(val, _values, result)
                      result = @builder.gvar(val[0])

    result
end

def _reduce_451(val, _values, result)
                      result = @builder.ivar(val[0])

    result
end

def _reduce_452(val, _values, result)
                      result = @builder.cvar(val[0])

    result
end

# reduce 453 omitted

def _reduce_454(val, _values, result)
                      result = @builder.symbol(val[0])

    result
end

def _reduce_455(val, _values, result)
                      result = @builder.symbol_compose(val[0], val[1], val[2])

    result
end

def _reduce_456(val, _values, result)
                      result = @builder.integer(val[0])

    result
end

def _reduce_457(val, _values, result)
                      result = @builder.float(val[0])

    result
end

def _reduce_458(val, _values, result)
                      num = @builder.integer(val[1])
                      if @builder.respond_to? :negate
                        # AST builder interface compatibility
                        result = @builder.negate(val[0], num)
                      else
                        result = @builder.unary_num(val[0], num)
                      end

    result
end

def _reduce_459(val, _values, result)
                      num = @builder.float(val[1])
                      if @builder.respond_to? :negate
                        # AST builder interface compatibility
                        result = @builder.negate(val[0], num)
                      else
                        result = @builder.unary_num(val[0], num)
                      end

    result
end

def _reduce_460(val, _values, result)
                      result = @builder.ident(val[0])

    result
end

def _reduce_461(val, _values, result)
                      result = @builder.ivar(val[0])

    result
end

def _reduce_462(val, _values, result)
                      result = @builder.gvar(val[0])

    result
end

def _reduce_463(val, _values, result)
                      result = @builder.const(val[0])

    result
end

def _reduce_464(val, _values, result)
                      result = @builder.cvar(val[0])

    result
end

def _reduce_465(val, _values, result)
                      result = @builder.nil(val[0])

    result
end

def _reduce_466(val, _values, result)
                      result = @builder.self(val[0])

    result
end

def _reduce_467(val, _values, result)
                      result = @builder.true(val[0])

    result
end

def _reduce_468(val, _values, result)
                      result = @builder.false(val[0])

    result
end

def _reduce_469(val, _values, result)
                      result = @builder.__FILE__(val[0])

    result
end

def _reduce_470(val, _values, result)
                      result = @builder.__LINE__(val[0])

    result
end

def _reduce_471(val, _values, result)
                      result = @builder.__ENCODING__(val[0])

    result
end

def _reduce_472(val, _values, result)
                      result = @builder.accessible(val[0])

    result
end

def _reduce_473(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_474(val, _values, result)
                      result = @builder.nth_ref(val[0])

    result
end

def _reduce_475(val, _values, result)
                      result = @builder.back_ref(val[0])

    result
end

def _reduce_476(val, _values, result)
                      result = nil

    result
end

def _reduce_477(val, _values, result)
                      result = [ val[0], val[1] ]

    result
end

def _reduce_478(val, _values, result)
                      yyerrok
                      result = nil

    result
end

def _reduce_479(val, _values, result)
                      result = @builder.args(val[0], val[1], val[2])

                      @lexer.state = :expr_value

    result
end

def _reduce_480(val, _values, result)
                      result = @builder.args(nil, val[0], nil)

    result
end

def _reduce_481(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_482(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[6]).
                                  concat(val[7])

    result
end

def _reduce_483(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_484(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_485(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_486(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_487(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_488(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_489(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_490(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_491(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_492(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_493(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_494(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_495(val, _values, result)
                      result = []

    result
end

def _reduce_496(val, _values, result)
                      diagnostic :error, :argument_const, nil, val[0]

    result
end

def _reduce_497(val, _values, result)
                      diagnostic :error, :argument_ivar, nil, val[0]

    result
end

def _reduce_498(val, _values, result)
                      diagnostic :error, :argument_gvar, nil, val[0]

    result
end

def _reduce_499(val, _values, result)
                      diagnostic :error, :argument_cvar, nil, val[0]

    result
end

# reduce 500 omitted

def _reduce_501(val, _values, result)
                      @static_env.declare val[0][0]

                      result = @builder.arg(val[0])

    result
end

def _reduce_502(val, _values, result)
                      @static_env.declare val[2][0]

                      result = @builder.objc_kwarg(val[0], val[1], val[2])

    result
end

def _reduce_503(val, _values, result)
                      @static_env.declare val[1][0]

                      result = @builder.objc_kwarg(val[0], nil, val[1])

    result
end

# reduce 504 omitted

def _reduce_505(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

def _reduce_506(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_507(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_508(val, _values, result)
                      @static_env.declare val[0][0]

                      result = @builder.optarg(val[0], val[1], val[2])

    result
end

def _reduce_509(val, _values, result)
                      @static_env.declare val[0][0]

                      result = @builder.optarg(val[0], val[1], val[2])

    result
end

def _reduce_510(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_511(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_512(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_513(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 514 omitted

# reduce 515 omitted

def _reduce_516(val, _values, result)
                      @static_env.declare val[1][0]

                      result = [ @builder.restarg(val[0], val[1]) ]

    result
end

def _reduce_517(val, _values, result)
                      result = [ @builder.restarg(val[0]) ]

    result
end

# reduce 518 omitted

# reduce 519 omitted

def _reduce_520(val, _values, result)
                      @static_env.declare val[1][0]

                      result = @builder.blockarg(val[0], val[1])

    result
end

def _reduce_521(val, _values, result)
                      result = [ val[1] ]

    result
end

def _reduce_522(val, _values, result)
                      result = []

    result
end

# reduce 523 omitted

def _reduce_524(val, _values, result)
                      result = val[1]

    result
end

def _reduce_525(val, _values, result)
                      result = []

    result
end

# reduce 526 omitted

def _reduce_527(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_528(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_529(val, _values, result)
                      result = @builder.pair(val[0], val[1], val[2])

    result
end

def _reduce_530(val, _values, result)
                      result = @builder.pair_keyword(val[0], val[1])

    result
end

# reduce 531 omitted

# reduce 532 omitted

# reduce 533 omitted

# reduce 534 omitted

# reduce 535 omitted

# reduce 536 omitted

# reduce 537 omitted

# reduce 538 omitted

# reduce 539 omitted

# reduce 540 omitted

# reduce 541 omitted

# reduce 542 omitted

# reduce 543 omitted

# reduce 544 omitted

# reduce 545 omitted

# reduce 546 omitted

def _reduce_547(val, _values, result)
                      result = val[1]

    result
end

def _reduce_548(val, _values, result)
                      result = val[1]

    result
end

# reduce 549 omitted

# reduce 550 omitted

# reduce 551 omitted

def _reduce_552(val, _values, result)
                    yyerrok

    result
end

# reduce 553 omitted

# reduce 554 omitted

# reduce 555 omitted

def _reduce_556(val, _values, result)
                    result = nil

    result
end

def _reduce_none(val, _values, result)
  val[0]
end

  end   # class MacRuby
end   # module Parser
