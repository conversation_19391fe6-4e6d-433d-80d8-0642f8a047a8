# -*- encoding:utf-8; warn-indent:false; frozen_string_literal: true  -*-
#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.7.3
# from Racc grammar file "rubymotion.y".
#

require 'racc/parser.rb'


require_relative '../parser'

module Parser
  class RubyMotion < Parser::Base


  def version
    19 # closest released match: v1_9_0_2
  end

  def default_encoding
    Encoding::BINARY
  end

  def local_push
    @static_env.extend_static
    @lexer.cmdarg.push(false)
    @lexer.cond.push(false)
  end

  def local_pop
    @static_env.unextend
    @lexer.cmdarg.pop
    @lexer.cond.pop
  end
##### State transition tables begin ###

racc_action_table = [
  -453,     5,    68,    69,    65,     7,    51,  -453,  -453,  -453,
    57,    58,  -453,  -453,  -453,    61,  -453,    59,    60,    62,
    23,    24,    66,    67,  -453,   274,  -453,  -453,  -453,    22,
    28,    27,    92,    91,    93,    94,  -453,  -453,    17,  -453,
  -453,  -453,  -453,  -453,     6,    41,     8,     9,    96,    95,
    97,    86,    50,    88,    87,    89,   530,    90,    98,    99,
   509,    84,    85,    38,    39,    37,  -453,  -453,  -453,  -453,
  -453,  -453,  -453,  -453,  -453,  -453,  -453,  -453,  -453,  -453,
   100,   452,  -453,  -453,  -453,    36,  -453,  -453,    30,   -86,
  -453,    52,    53,  -453,  -453,    54,  -453,    32,  -453,   551,
  -453,    40,  -453,  -453,  -453,  -453,  -453,  -453,  -453,    18,
  -453,   273,  -453,   -93,    83,    75,    78,    79,   195,    80,
    81,   540,   -92,   731,    76,    82,  -453,  -453,  -453,  -453,
  -456,  -453,    56,  -453,    77,  -453,  -453,  -456,  -456,  -456,
  -466,   196,  -456,  -456,  -456,   661,  -456,   197,   509,   661,
   209,   206,   207,   203,  -456,   509,  -456,  -456,  -456,   104,
   267,   529,   550,   751,   103,   -88,  -456,  -456,   -90,  -456,
  -456,  -456,  -456,  -456,   104,   509,   -86,   -93,   509,   103,
   -87,   104,   542,   541,   -92,   -88,   103,   210,   521,   198,
   -86,   693,   522,   417,   -78,   -89,  -456,  -456,  -456,  -456,
  -456,  -456,  -456,  -456,  -456,  -456,  -456,  -456,  -456,  -456,
   104,   204,  -456,  -456,  -456,   103,  -456,  -456,   -85,   -90,
  -456,   -87,   -80,  -456,  -456,   199,  -456,   -84,  -456,   625,
  -456,   515,  -456,  -456,  -456,  -456,  -456,  -456,  -456,  -531,
  -456,   210,  -456,   -93,  -453,   104,  -531,  -531,  -531,   104,
   103,  -453,  -531,  -531,   103,  -531,  -456,  -456,  -456,  -456,
  -527,  -456,   104,  -456,  -531,  -456,  -456,   103,   750,   104,
   -80,   -91,   779,   -82,   103,  -531,  -531,   661,  -531,  -531,
  -531,  -531,  -531,   -89,   499,   -79,   -91,   498,   -80,   104,
   -86,   -93,   104,   248,   103,   -86,   -93,   103,   -92,   -88,
   -81,   -82,  -453,   -92,   -88,  -531,  -531,  -531,  -531,  -531,
  -531,  -531,  -531,  -531,  -531,  -531,  -531,  -531,  -531,   206,
   207,  -531,  -531,  -531,   -80,   568,   298,   -80,   486,  -531,
   760,   540,  -531,   -90,   -80,   -87,  -528,  -531,   -90,  -531,
   -87,  -531,  -531,  -531,  -531,  -531,  -531,  -531,  -279,  -531,
  -531,  -531,   206,   207,   712,  -279,  -279,  -279,   713,   299,
  -527,  -279,  -279,  -528,  -279,  -531,  -531,   -82,   -81,   210,
  -531,   206,   207,   274,  -531,   -89,   -83,   104,   -92,   761,
   274,  -524,   103,   364,  -279,  -279,   205,  -279,  -279,  -279,
  -279,  -279,   542,   541,   538,   499,   499,   -89,   501,   501,
   -91,   540,   -89,   -82,   -88,   -91,   -82,   540,   -90,   210,
   206,   207,   376,   -82,  -279,  -279,  -279,  -279,  -279,  -279,
  -279,  -279,  -279,  -279,  -279,  -279,  -279,  -279,  -466,   -85,
  -279,  -279,  -279,  -453,   571,   416,   -93,   269,  -279,   418,
  -453,  -279,   104,  -525,   269,  -524,  -279,   103,  -279,  -453,
  -279,  -279,  -279,  -279,  -279,  -279,  -279,   540,  -279,   273,
  -279,   210,   542,   541,   543,   540,   273,  -524,   542,   541,
   545,   -78,   583,  -531,  -279,  -279,   760,   -83,   -86,  -279,
  -531,  -531,  -531,  -279,   -91,  -531,  -531,  -531,   796,  -531,
   -73,  -453,   300,   301,   419,  -456,  -456,   -59,  -531,  -531,
  -531,  -531,  -456,  -456,   209,   499,   582,  -525,   501,  -531,
  -531,  -456,  -531,  -531,  -531,  -531,  -531,   448,   542,   541,
   547,   -81,   -79,   450,  -463,   810,   542,   541,   552,  -525,
   583,  -463,   104,   452,   583,   481,   482,   103,   -87,  -531,
  -531,  -531,  -531,  -531,  -531,  -531,  -531,  -531,  -531,  -531,
  -531,  -531,  -531,  -456,  -456,  -531,  -531,  -531,   459,   714,
  -531,   -58,   583,  -531,   582,   210,  -531,  -531,   582,  -531,
   486,  -531,   471,  -531,   472,  -531,  -531,  -531,  -531,  -531,
  -531,  -531,  -463,  -531,  -531,  -531,  -527,   -81,   -79,   768,
  -462,   641,   640,   639,   645,   642,   582,  -462,   203,  -531,
  -531,  -531,  -531,  -279,  -531,   202,  -531,   479,  -531,   -89,
  -279,  -279,  -279,   -84,   200,  -279,  -279,  -279,   278,  -279,
   -92,   206,   207,   -81,   -79,  -323,   -81,   -79,   229,  -279,
  -279,  -279,  -323,   -81,   -79,   206,   207,   774,   625,  -279,
  -279,  -323,  -279,  -279,  -279,  -279,  -279,   768,  -462,   641,
   640,   639,   645,   642,  -464,   210,   204,   373,   269,  -461,
   226,  -464,   375,   374,   228,   227,  -461,   843,   483,  -279,
  -279,  -279,  -279,  -279,  -279,  -279,  -279,  -279,  -279,  -279,
  -279,  -279,  -279,  -323,   487,  -279,  -279,  -279,   488,   571,
  -279,   774,   625,  -279,   229,    75,  -279,  -279,   494,  -279,
   563,  -279,   495,  -279,    76,  -279,  -279,  -279,  -279,  -279,
  -279,  -279,  -464,  -279,    77,  -279,   724,  -461,   641,   640,
   639,   645,   642,   502,   503,   931,   452,   515,   203,  -279,
  -279,  -279,  -279,  -396,  -279,   414,  -279,   564,  -279,   -91,
  -396,  -396,  -396,   -80,   415,  -396,  -396,  -396,   366,  -396,
   -88,   104,   519,   647,   520,   555,   103,   229,  -396,  -396,
  -396,  -465,   651,   650,   654,   653,  -531,   556,  -465,  -396,
  -396,   559,  -396,  -396,  -396,  -396,  -396,  -465,  -259,   808,
   -82,   641,   640,   639,  -458,   642,   204,   -90,  -459,   226,
  -460,  -458,   210,   228,   227,  -459,   723,  -460,   269,  -396,
  -396,  -396,  -396,  -396,  -396,  -396,  -396,  -396,  -396,  -396,
  -396,  -396,  -396,   573,   229,  -396,  -396,  -396,  -531,  -465,
  -396,   203,   269,  -396,   229,  -531,  -396,  -396,   536,  -396,
  -527,  -396,   701,  -396,  -531,  -396,  -396,  -396,  -396,  -396,
  -396,  -396,  -458,  -396,  -396,  -396,  -459,   203,  -460,   229,
   104,   229,  -531,   210,   478,   103,   210,   210,  -286,  -396,
  -396,   -73,  -396,   476,  -396,  -286,  -286,  -286,  -396,   607,
  -286,  -286,  -286,   210,  -286,   -79,  -531,   492,   618,   204,
  -273,   625,   -87,   210,  -286,  -286,   203,  -273,   106,   107,
   108,   109,   110,   490,  -286,  -286,  -273,  -286,  -286,  -286,
  -286,  -286,   415,   657,   808,   204,   641,   640,   639,   768,
   642,   641,   640,   639,   645,   642,   768,   515,   641,   640,
   639,   645,   642,   664,  -286,  -286,  -286,  -286,  -286,  -286,
  -286,  -286,  -286,  -286,  -286,  -286,  -286,  -286,  -273,   692,
  -286,  -286,  -286,   203,   204,  -286,   764,   278,  -286,   695,
   518,  -286,  -286,   764,  -286,   767,  -286,  -260,  -286,   516,
  -286,  -286,  -286,  -286,  -286,  -286,  -286,   702,  -286,   459,
  -286,   768,   203,   641,   640,   639,   645,   642,   459,   524,
   210,   716,   728,  -274,  -286,  -286,   452,  -286,   526,  -286,
  -274,  -274,  -274,  -286,   450,  -274,  -274,  -274,   210,  -274,
   618,   204,   210,   269,   269,  -280,   618,   229,   764,  -274,
  -274,  -274,  -280,   229,   229,   742,  -259,   767,   746,  -274,
  -274,  -280,  -274,  -274,  -274,  -274,  -274,   723,   754,   756,
   204,   759,   768,   762,   641,   640,   639,   645,   642,   226,
   770,   771,   625,   228,   227,   224,   225,   778,   210,  -274,
  -274,  -274,  -274,  -274,  -274,  -274,  -274,  -274,  -274,  -274,
  -274,  -274,  -274,  -280,   210,  -274,  -274,  -274,   787,   764,
  -274,  -261,   798,  -274,   800,   803,  -274,  -274,   894,  -274,
   804,  -274,   723,  -274,   811,  -274,  -274,  -274,  -274,  -274,
  -274,  -274,   210,  -274,   818,  -274,   819,   618,   723,  -280,
   838,   203,   841,   760,   210,   845,  -280,   847,   878,  -274,
  -274,  -274,  -274,  -232,  -274,  -280,  -274,   876,  -274,   853,
  -232,  -232,  -232,   855,   210,  -232,  -232,  -232,   644,  -232,
   641,   640,   639,   645,   642,  -279,   858,  -262,  -232,  -232,
  -232,   865,  -279,   866,   870,   871,   873,  -528,   450,  -232,
  -232,  -279,  -232,  -232,  -232,  -232,  -232,  -280,   879,   204,
   106,   107,   108,   109,   110,   647,   633,   768,   210,   641,
   640,   639,   645,   642,   651,   650,   654,   653,   883,  -232,
  -232,  -232,  -232,  -232,  -232,  -232,  -232,  -232,  -232,  -232,
  -232,  -232,  -232,  -279,   229,  -232,  -232,  -232,  -279,   886,
  -232,   888,   269,  -232,   764,  -279,  -232,  -232,   890,  -232,
  -528,  -232,   890,  -232,  -279,  -232,  -232,  -232,  -232,  -232,
  -232,  -232,   210,  -232,  -232,  -232,   226,   203,   895,   898,
   228,   227,   224,   225,   915,   899,   904,   906,  -532,  -232,
  -232,   909,  -232,   526,  -232,  -532,  -532,  -532,  -232,   911,
  -532,  -532,  -532,   890,  -532,   229,  -279,   106,   107,   108,
   109,   110,   890,  -532,  -532,  -532,  -532,   916,   494,   924,
   925,   243,   244,   933,  -532,  -532,   450,  -532,  -532,  -532,
  -532,  -532,   210,   946,   890,   204,   890,   226,   890,   232,
   950,   228,   227,   224,   225,   933,   953,   230,   954,   231,
   956,   890,   890,   890,  -532,  -532,  -532,  -532,  -532,  -532,
  -532,  -532,  -532,  -532,  -532,  -532,  -532,  -532,  -528,  -527,
  -532,  -532,  -532,   933,   890,  -532,   933,   890,  -532,   nil,
   nil,  -532,  -532,   nil,  -532,   nil,  -532,   nil,  -532,   nil,
  -532,  -532,  -532,  -532,  -532,  -532,  -532,   nil,  -532,  -532,
  -532,   644,   nil,   641,   640,   639,   645,   642,   nil,   nil,
   nil,   nil,   nil,   nil,  -532,  -532,  -532,  -532,  -533,  -532,
   nil,  -532,   nil,  -532,   nil,  -533,  -533,  -533,   nil,   nil,
  -533,  -533,  -533,   nil,  -533,   229,   687,   688,   647,   682,
   689,    98,    99,  -533,  -533,  -533,  -533,   651,   650,   654,
   653,   243,   244,   nil,  -533,  -533,   nil,  -533,  -533,  -533,
  -533,  -533,   nil,   nil,   nil,   nil,   nil,   226,   nil,   232,
   nil,   228,   227,   224,   225,   nil,   nil,   230,   nil,   231,
   nil,   nil,   nil,   nil,  -533,  -533,  -533,  -533,  -533,  -533,
  -533,  -533,  -533,  -533,  -533,  -533,  -533,  -533,   nil,   nil,
  -533,  -533,  -533,   nil,   nil,  -533,   nil,   nil,  -533,   nil,
   nil,  -533,  -533,   nil,  -533,   nil,  -533,   nil,  -533,   nil,
  -533,  -533,  -533,  -533,  -533,  -533,  -533,   nil,  -533,  -533,
  -533,   644,   nil,   641,   640,   639,   645,   642,   nil,   nil,
   nil,   nil,   nil,   nil,  -533,  -533,  -533,  -533,  -232,  -533,
   nil,  -533,   nil,  -533,   nil,  -232,  -232,  -232,   nil,   nil,
  -232,  -232,  -232,   nil,  -232,   nil,   nil,   768,   647,   641,
   640,   639,   645,   642,  -232,   nil,   nil,   651,   650,   654,
   653,   nil,   nil,   nil,  -232,  -232,   nil,  -232,  -232,  -232,
  -232,  -232,   nil,   768,   nil,   641,   640,   639,   645,   642,
   nil,   nil,   nil,  -232,   764,   nil,   nil,   nil,   nil,   nil,
  -232,  -232,  -232,   nil,   nil,  -232,  -232,  -232,   644,  -232,
   641,   640,   639,   645,   642,  -232,   nil,   nil,   nil,  -232,
   764,   nil,  -232,   nil,   nil,   nil,   nil,   269,  -232,  -232,
  -232,   nil,  -232,  -232,  -232,  -232,  -232,   nil,   644,   nil,
   641,   640,   639,   645,   642,   647,   nil,   nil,   nil,   nil,
  -232,   nil,   nil,   nil,   651,   650,   654,   653,   nil,   nil,
   nil,   nil,   nil,   229,  -232,   nil,   nil,   nil,   nil,  -232,
  -232,   nil,   nil,  -232,   nil,   647,   nil,  -232,   nil,   243,
   244,   nil,   269,  -232,   651,   650,   654,   653,   768,   nil,
   641,   640,   639,   645,   642,   226,   nil,   232,   nil,   228,
   227,   224,   225,   nil,   nil,  -232,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,     5,    68,    69,    65,     7,    51,  -232,
   nil,   nil,    57,    58,  -232,   647,   nil,    61,  -232,    59,
    60,    62,    23,    24,    66,    67,   654,   653,   nil,   nil,
   nil,    22,    28,    27,    92,    91,    93,    94,   nil,   768,
    17,   641,   640,   639,   645,   642,     6,    41,     8,     9,
    96,    95,    97,    86,    50,    88,    87,    89,   nil,    90,
    98,    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   768,   764,   641,   640,   639,
   645,   642,   nil,   nil,   nil,   nil,   nil,    36,   nil,   nil,
   280,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,    32,
   nil,   nil,   nil,    40,   768,   nil,   641,   640,   639,   645,
   642,    18,   647,   nil,   nil,   nil,    83,    75,    78,    79,
   nil,    80,    81,   654,   653,   nil,    76,    82,     5,    68,
    69,    65,     7,    51,    56,   nil,    77,    57,    58,   nil,
   nil,   647,    61,   nil,    59,    60,    62,    23,    24,    66,
    67,   nil,   654,   653,   nil,   nil,    22,    28,    27,    92,
    91,    93,    94,   nil,   nil,    17,   nil,   nil,   nil,   nil,
   nil,     6,    41,     8,     9,    96,    95,    97,    86,    50,
    88,    87,    89,   nil,    90,    98,    99,   nil,    84,    85,
    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   768,   nil,   641,   640,   639,   645,   642,   nil,   nil,   nil,
   nil,   nil,    36,   nil,   nil,    30,   nil,   nil,    52,    53,
   nil,   nil,    54,   nil,    32,   nil,   nil,   nil,    40,   768,
   nil,   641,   640,   639,   645,   642,    18,   647,   nil,   nil,
   nil,    83,    75,    78,    79,   nil,    80,    81,   654,   653,
   nil,    76,    82,     5,    68,    69,    65,     7,    51,    56,
   nil,    77,    57,    58,   nil,   nil,   647,    61,   nil,    59,
    60,    62,    23,    24,    66,    67,   nil,   654,   653,   nil,
   nil,    22,    28,    27,    92,    91,    93,    94,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,     6,    41,     8,     9,
    96,    95,    97,    86,    50,    88,    87,    89,   nil,    90,
    98,    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   768,   nil,   641,   640,   639,
   645,   642,   nil,   nil,   nil,   nil,   nil,    36,   nil,   nil,
    30,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,    32,
   nil,   nil,   nil,    40,   768,   nil,   641,   640,   639,   645,
   642,    18,   647,   nil,   nil,   nil,    83,    75,    78,    79,
   nil,    80,    81,   654,   653,   nil,    76,    82,     5,    68,
    69,    65,     7,    51,    56,   nil,    77,    57,    58,   nil,
   nil,   647,    61,   nil,    59,    60,    62,    23,    24,    66,
    67,   nil,   654,   653,   nil,   nil,    22,    28,    27,    92,
    91,    93,    94,   nil,   nil,    17,   nil,   nil,   nil,   nil,
   nil,     6,    41,     8,     9,    96,    95,    97,    86,    50,
    88,    87,    89,   nil,    90,    98,    99,   nil,    84,    85,
    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   768,   nil,   641,   640,   639,   645,   642,   nil,   nil,   nil,
   nil,   nil,    36,   nil,   nil,    30,   nil,   nil,    52,    53,
   nil,   nil,    54,   nil,    32,   nil,   nil,   nil,    40,   768,
   nil,   641,   640,   639,   645,   642,    18,   647,   nil,   nil,
   nil,    83,    75,    78,    79,   nil,    80,    81,   654,   653,
   nil,    76,    82,     5,    68,    69,    65,     7,    51,    56,
   nil,    77,    57,    58,   nil,   nil,   647,    61,   nil,    59,
    60,    62,    23,    24,    66,    67,   nil,   654,   653,   nil,
   nil,    22,    28,    27,    92,    91,    93,    94,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,     6,    41,     8,     9,
    96,    95,    97,    86,    50,    88,    87,    89,   nil,    90,
    98,    99,   nil,    84,    85,    38,    39,    37,   229,   233,
   238,   239,   240,   235,   237,   245,   246,   241,   242,   nil,
  -552,  -552,   nil,   nil,   243,   244,   nil,    36,   nil,   nil,
    30,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,    32,
   226,   nil,   232,    40,   228,   227,   224,   225,   236,   234,
   230,    18,   231,   nil,   nil,   nil,    83,    75,    78,    79,
   nil,    80,    81,   nil,   nil,   nil,    76,    82,     5,    68,
    69,    65,     7,    51,    56,   nil,    77,    57,    58,   nil,
   nil,   nil,    61,   nil,    59,    60,    62,    23,    24,    66,
    67,   nil,   nil,   nil,   nil,   nil,    22,    28,    27,    92,
    91,    93,    94,   nil,   nil,    17,   nil,   nil,   nil,   nil,
   nil,     6,    41,     8,     9,    96,    95,    97,    86,    50,
    88,    87,    89,   nil,    90,    98,    99,   nil,    84,    85,
    38,    39,    37,   229,   233,   238,   239,   240,   235,   237,
   245,   246,   241,   242,   nil,  -552,  -552,   nil,   nil,   243,
   244,   nil,    36,   nil,   nil,   280,   nil,   nil,    52,    53,
   nil,   nil,    54,   nil,    32,   226,   nil,   232,    40,   228,
   227,   224,   225,   236,   234,   230,    18,   231,   nil,   nil,
   nil,    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,
   nil,    76,    82,     5,    68,    69,    65,     7,    51,    56,
   nil,    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,
    60,    62,    23,    24,    66,    67,   nil,   nil,   nil,   nil,
   nil,    22,    28,    27,    92,    91,    93,    94,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,     6,    41,     8,     9,
    96,    95,    97,    86,    50,    88,    87,    89,   nil,    90,
    98,    99,   nil,    84,    85,    38,    39,    37,   229,  -552,
  -552,  -552,  -552,   235,   237,   nil,   nil,  -552,  -552,   nil,
   nil,   nil,   nil,   nil,   243,   244,   nil,    36,   nil,   nil,
   280,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,    32,
   226,   nil,   232,    40,   228,   227,   224,   225,   236,   234,
   230,    18,   231,   nil,   nil,   nil,    83,    75,    78,    79,
   nil,    80,    81,   nil,   nil,   nil,    76,    82,     5,    68,
    69,    65,     7,    51,    56,   nil,    77,    57,    58,   nil,
   nil,   nil,    61,   nil,    59,    60,    62,    23,    24,    66,
    67,   nil,   nil,   nil,   nil,   nil,    22,    28,    27,    92,
    91,    93,    94,   nil,   nil,    17,   nil,   nil,   nil,   nil,
   nil,     6,    41,     8,     9,    96,    95,    97,    86,    50,
    88,    87,    89,   nil,    90,    98,    99,   nil,    84,    85,
    38,    39,    37,   229,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   243,
   244,   nil,    36,   nil,   nil,    30,   nil,   nil,    52,    53,
   nil,   nil,    54,   nil,    32,   226,   nil,   232,    40,   228,
   227,   224,   225,   nil,   nil,   230,    18,   231,   nil,   nil,
   nil,    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,
   nil,    76,    82,     5,    68,    69,    65,     7,    51,    56,
   nil,    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,
    60,    62,    23,    24,    66,    67,   nil,   nil,   nil,   nil,
   nil,    22,    28,    27,    92,    91,    93,    94,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,     6,    41,     8,     9,
    96,    95,    97,    86,    50,    88,    87,    89,   nil,    90,
    98,    99,   nil,    84,    85,    38,    39,    37,   229,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   243,   244,   nil,    36,   nil,   nil,
    30,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,    32,
   226,   nil,   232,    40,   228,   227,   224,   225,   nil,   nil,
   230,    18,   231,   nil,   nil,   nil,    83,    75,    78,    79,
   nil,    80,    81,   nil,   nil,   nil,    76,    82,     5,    68,
    69,    65,     7,    51,    56,   nil,    77,    57,    58,   nil,
   nil,   nil,    61,   nil,    59,    60,    62,    23,    24,    66,
    67,   nil,   nil,   nil,   nil,   nil,    22,    28,    27,    92,
    91,    93,    94,   nil,   nil,    17,   nil,   nil,   nil,   nil,
   nil,     6,    41,     8,     9,    96,    95,    97,    86,    50,
    88,    87,    89,   nil,    90,    98,    99,   nil,    84,    85,
    38,    39,    37,   229,  -552,  -552,  -552,  -552,   235,   237,
   nil,   nil,  -552,  -552,   nil,   nil,   nil,   nil,   nil,   243,
   244,   nil,    36,   nil,   nil,    30,   nil,   nil,    52,    53,
   nil,   nil,    54,   nil,    32,   226,   nil,   232,    40,   228,
   227,   224,   225,   236,   234,   230,    18,   231,   nil,   nil,
   nil,    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,
   nil,    76,    82,     5,    68,    69,    65,     7,    51,    56,
   nil,    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,
    60,    62,    23,    24,    66,    67,   nil,   nil,   nil,   nil,
   nil,    22,    28,    27,    92,    91,    93,    94,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,     6,    41,     8,     9,
    96,    95,    97,    86,    50,    88,    87,    89,   nil,    90,
    98,    99,   nil,    84,    85,    38,    39,    37,   229,  -552,
  -552,  -552,  -552,   235,   237,   nil,   nil,  -552,  -552,   nil,
   nil,   nil,   nil,   nil,   243,   244,   nil,    36,   nil,   nil,
    30,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,    32,
   226,   nil,   232,    40,   228,   227,   224,   225,   236,   234,
   230,    18,   231,   nil,   nil,   nil,    83,    75,    78,    79,
   nil,    80,    81,   nil,   nil,   nil,    76,    82,     5,    68,
    69,    65,     7,    51,    56,   nil,    77,    57,    58,   nil,
   nil,   nil,    61,   nil,    59,    60,    62,    23,    24,    66,
    67,   nil,   nil,   nil,   nil,   nil,    22,    28,    27,    92,
    91,    93,    94,   nil,   nil,    17,   nil,   nil,   nil,   nil,
   nil,     6,    41,     8,     9,    96,    95,    97,    86,    50,
    88,    87,    89,   nil,    90,    98,    99,   nil,    84,    85,
    38,    39,    37,   229,  -552,  -552,  -552,  -552,   235,   237,
   nil,   nil,  -552,  -552,   nil,   nil,   nil,   nil,   nil,   243,
   244,   nil,    36,   nil,   nil,    30,   nil,   nil,    52,    53,
   nil,   nil,    54,   nil,    32,   226,   nil,   232,    40,   228,
   227,   224,   225,   236,   234,   230,    18,   231,   nil,   nil,
   nil,    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,
   nil,    76,    82,     5,    68,    69,    65,     7,    51,    56,
   nil,    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,
    60,    62,    23,    24,    66,    67,   nil,   nil,   nil,   nil,
   nil,    22,    28,    27,    92,    91,    93,    94,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,     6,    41,     8,     9,
    96,    95,    97,    86,    50,    88,    87,    89,   nil,    90,
    98,    99,   nil,    84,    85,    38,    39,    37,   229,  -552,
  -552,  -552,  -552,   235,   237,   nil,   nil,  -552,  -552,   nil,
   nil,   nil,   nil,   nil,   243,   244,   nil,    36,   nil,   nil,
    30,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,    32,
   226,   nil,   232,    40,   228,   227,   224,   225,   236,   234,
   230,    18,   231,   nil,   nil,   nil,    83,    75,    78,    79,
   nil,    80,    81,   nil,   nil,   nil,    76,    82,     5,    68,
    69,    65,     7,    51,    56,   nil,    77,    57,    58,   nil,
   nil,   nil,    61,   nil,    59,    60,    62,    23,    24,    66,
    67,   nil,   nil,   nil,   nil,   nil,    22,    28,    27,    92,
    91,    93,    94,   nil,   nil,    17,   nil,   nil,   nil,   nil,
   nil,     6,    41,     8,     9,    96,    95,    97,    86,    50,
    88,    87,    89,   nil,    90,    98,    99,   nil,    84,    85,
    38,    39,    37,   229,  -552,  -552,  -552,  -552,   235,   237,
   nil,   nil,  -552,  -552,   nil,   nil,   nil,   nil,   nil,   243,
   244,   nil,    36,   nil,   nil,    30,   nil,   nil,    52,    53,
   nil,   nil,    54,   nil,    32,   226,   nil,   232,    40,   228,
   227,   224,   225,   236,   234,   230,    18,   231,   nil,   nil,
   nil,    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,
   nil,    76,    82,     5,    68,    69,    65,     7,    51,    56,
   nil,    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,
    60,    62,    23,    24,    66,    67,   nil,   nil,   nil,   nil,
   nil,    22,    28,    27,    92,    91,    93,    94,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,     6,    41,     8,     9,
    96,    95,    97,    86,    50,    88,    87,    89,   nil,    90,
    98,    99,   nil,    84,    85,    38,    39,    37,   229,   233,
   238,   239,   240,   235,   237,   nil,   nil,   241,   242,   nil,
   nil,   nil,   nil,   nil,   243,   244,   nil,    36,   nil,   nil,
    30,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,    32,
   226,   nil,   232,    40,   228,   227,   224,   225,   236,   234,
   230,    18,   231,   nil,   nil,   nil,    83,    75,    78,    79,
   nil,    80,    81,   nil,   nil,   nil,    76,    82,     5,    68,
    69,    65,     7,    51,    56,   nil,    77,    57,    58,   nil,
   nil,   nil,    61,   nil,    59,    60,    62,    23,    24,    66,
    67,   nil,   nil,   nil,   nil,   nil,    22,    28,    27,    92,
    91,    93,    94,   nil,   nil,    17,   nil,   nil,   nil,   nil,
   nil,     6,    41,     8,     9,    96,    95,    97,    86,    50,
    88,    87,    89,   nil,    90,    98,    99,   nil,    84,    85,
    38,    39,    37,   229,   233,   238,   239,   240,   235,   237,
   245,   nil,   241,   242,   nil,   nil,   nil,   nil,   nil,   243,
   244,   nil,    36,   nil,   nil,    30,   nil,   nil,    52,    53,
   nil,   nil,    54,   nil,    32,   226,   nil,   232,    40,   228,
   227,   224,   225,   236,   234,   230,    18,   231,   nil,   nil,
   nil,    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,
   nil,    76,    82,     5,    68,    69,    65,     7,    51,    56,
   nil,    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,
    60,    62,    23,    24,    66,    67,   nil,   nil,   nil,   nil,
   nil,    22,    28,    27,    92,    91,    93,    94,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,     6,    41,     8,     9,
    96,    95,    97,    86,    50,    88,    87,    89,   nil,    90,
    98,    99,   nil,    84,    85,    38,    39,    37,   229,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   243,   244,   nil,    36,   nil,   nil,
    30,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,    32,
   226,   nil,   232,    40,   228,   227,   224,   225,   nil,   nil,
   nil,    18,   nil,   nil,   nil,   nil,    83,    75,    78,    79,
   nil,    80,    81,   nil,   nil,   nil,    76,    82,     5,    68,
    69,    65,     7,    51,    56,   nil,    77,    57,    58,   nil,
   nil,   nil,    61,   nil,    59,    60,    62,    23,    24,    66,
    67,   nil,   nil,   nil,   nil,   nil,    22,    28,    27,    92,
    91,    93,    94,   nil,   nil,    17,   nil,   nil,   nil,   nil,
   nil,     6,    41,     8,     9,    96,    95,    97,    86,    50,
    88,    87,    89,   nil,    90,    98,    99,   nil,    84,    85,
    38,    39,    37,   229,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   243,
   244,   nil,    36,   nil,   nil,    30,   nil,   nil,    52,    53,
   nil,   nil,    54,   nil,    32,   226,   nil,   nil,    40,   228,
   227,   224,   225,   nil,   nil,   nil,    18,   nil,   nil,   nil,
   nil,    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,
   nil,    76,    82,     5,    68,    69,    65,     7,    51,    56,
   nil,    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,
    60,    62,    23,    24,    66,    67,   nil,   nil,   nil,   nil,
   nil,    22,    28,    27,    92,    91,    93,    94,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,     6,    41,     8,     9,
    96,    95,    97,    86,    50,    88,    87,    89,   nil,    90,
    98,    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    36,   nil,   nil,
    30,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,    32,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    18,   nil,   nil,   nil,   nil,    83,    75,    78,    79,
   nil,    80,    81,   nil,   nil,   nil,    76,    82,     5,    68,
    69,    65,     7,    51,    56,   nil,    77,    57,    58,   nil,
   nil,   nil,    61,   nil,    59,    60,    62,    23,    24,    66,
    67,   nil,   nil,   nil,   nil,   nil,    22,    28,    27,    92,
    91,    93,    94,   nil,   nil,    17,   nil,   nil,   nil,   nil,
   nil,     6,    41,     8,     9,    96,    95,    97,    86,    50,
    88,    87,    89,   nil,    90,    98,    99,   nil,    84,    85,
    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    36,   nil,   nil,    30,   nil,   nil,    52,    53,
   nil,   nil,    54,   nil,    32,   nil,   nil,   nil,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,   nil,
   nil,    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,
   nil,    76,    82,     5,    68,    69,    65,     7,    51,    56,
   nil,    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,
    60,    62,    23,    24,    66,    67,   nil,   nil,   nil,   nil,
   nil,    22,    28,    27,    92,    91,    93,    94,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,     6,    41,     8,     9,
    96,    95,    97,    86,    50,    88,    87,    89,   nil,    90,
    98,    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    36,   nil,   nil,
    30,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,    32,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    18,   nil,   nil,   nil,   nil,    83,    75,    78,    79,
   nil,    80,    81,   nil,   nil,   nil,    76,    82,     5,    68,
    69,    65,     7,    51,    56,   nil,    77,    57,    58,   nil,
   nil,   nil,    61,   nil,    59,    60,    62,    23,    24,    66,
    67,   nil,   nil,   nil,   nil,   nil,    22,    28,    27,    92,
    91,    93,    94,   nil,   nil,    17,   nil,   nil,   nil,   nil,
   nil,     6,    41,     8,     9,    96,    95,    97,    86,    50,
    88,    87,    89,   nil,    90,    98,    99,   nil,    84,    85,
    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    36,   nil,   nil,    30,   nil,   nil,    52,    53,
   nil,   nil,    54,   nil,    32,   nil,   nil,   nil,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,   nil,
   nil,    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,
   nil,    76,    82,     5,    68,    69,    65,     7,    51,    56,
   nil,    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,
    60,    62,    23,    24,    66,    67,   nil,   nil,   nil,   nil,
   nil,    22,    28,    27,    92,    91,    93,    94,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,     6,    41,     8,     9,
    96,    95,    97,    86,    50,    88,    87,    89,   nil,    90,
    98,    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    36,   nil,   nil,
    30,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,    32,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    18,   nil,   nil,   nil,   nil,    83,    75,    78,    79,
   nil,    80,    81,   nil,   nil,   nil,    76,    82,     5,    68,
    69,    65,     7,    51,    56,   nil,    77,    57,    58,   nil,
   nil,   nil,    61,   nil,    59,    60,    62,    23,    24,    66,
    67,   nil,   nil,   nil,   nil,   nil,    22,    28,    27,    92,
    91,    93,    94,   nil,   nil,    17,   nil,   nil,   nil,   nil,
   nil,     6,    41,     8,     9,    96,    95,    97,    86,    50,
    88,    87,    89,   nil,    90,    98,    99,   nil,    84,    85,
    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    36,   nil,   nil,    30,   nil,   nil,    52,    53,
   nil,   nil,    54,   nil,    32,   nil,   nil,   nil,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,   nil,
   nil,    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,
   nil,    76,    82,     5,    68,    69,    65,     7,    51,    56,
   nil,    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,
    60,    62,    23,    24,    66,    67,   nil,   nil,   nil,   nil,
   nil,    22,    28,    27,    92,    91,    93,    94,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,     6,    41,     8,     9,
    96,    95,    97,    86,    50,    88,    87,    89,   nil,    90,
    98,    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    36,   nil,   nil,
    30,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,    32,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    18,   nil,   nil,   nil,   nil,    83,    75,    78,    79,
   nil,    80,    81,   nil,   nil,   nil,    76,    82,     5,    68,
    69,    65,     7,    51,    56,   nil,    77,    57,    58,   nil,
   nil,   nil,    61,   nil,    59,    60,    62,    23,    24,    66,
    67,   nil,   nil,   nil,   nil,   nil,    22,    28,    27,    92,
    91,    93,    94,   nil,   nil,    17,   nil,   nil,   nil,   nil,
   nil,     6,    41,     8,     9,    96,    95,    97,    86,    50,
    88,    87,    89,   nil,    90,    98,    99,   nil,    84,    85,
    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    36,   nil,   nil,    30,   nil,   nil,    52,    53,
   nil,   nil,    54,   nil,    32,   nil,   nil,   nil,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,   nil,
   nil,    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,
   nil,    76,    82,     5,    68,    69,    65,     7,    51,    56,
   nil,    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,
    60,    62,    23,    24,    66,    67,   nil,   nil,   nil,   nil,
   nil,    22,    28,    27,    92,    91,    93,    94,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,     6,    41,     8,     9,
    96,    95,    97,    86,    50,    88,    87,    89,   nil,    90,
    98,    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    36,   nil,   nil,
    30,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,    32,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    18,   nil,   nil,   nil,   nil,    83,    75,    78,    79,
   nil,    80,    81,   nil,   nil,   nil,    76,    82,     5,    68,
    69,    65,     7,    51,    56,   nil,    77,    57,    58,   nil,
   nil,   nil,    61,   nil,    59,    60,    62,    23,    24,    66,
    67,   nil,   nil,   nil,   nil,   nil,    22,    28,    27,    92,
    91,    93,    94,   nil,   nil,    17,   nil,   nil,   nil,   nil,
   nil,     6,    41,     8,     9,    96,    95,    97,    86,    50,
    88,    87,    89,   nil,    90,    98,    99,   nil,    84,    85,
    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    36,   nil,   nil,    30,   nil,   nil,    52,    53,
   nil,   nil,    54,   nil,    32,   nil,   nil,   nil,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,   nil,
   nil,    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,
   nil,    76,    82,     5,    68,    69,    65,     7,    51,    56,
   nil,    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,
    60,    62,    23,    24,    66,    67,   nil,   nil,   nil,   nil,
   nil,    22,    28,    27,    92,    91,    93,    94,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,     6,    41,     8,     9,
    96,    95,    97,    86,    50,    88,    87,    89,   nil,    90,
    98,    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    36,   nil,   nil,
    30,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,    32,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    18,   nil,   nil,   nil,   nil,    83,    75,    78,    79,
   nil,    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,
    69,    65,     7,    51,    56,   nil,    77,    57,    58,   nil,
   nil,   nil,    61,   nil,    59,    60,    62,    23,    24,    66,
    67,   nil,   nil,   nil,   nil,   nil,    22,    28,    27,    92,
    91,    93,    94,   nil,   nil,    17,   nil,   nil,   nil,   nil,
   nil,     6,    41,     8,     9,    96,    95,    97,    86,    50,
    88,    87,    89,   nil,    90,    98,    99,   nil,    84,    85,
    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    36,   nil,   nil,    30,   nil,   nil,    52,    53,
   nil,   nil,    54,   nil,    32,   nil,   nil,   nil,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,   nil,
   nil,    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,
   nil,    76,    82,   nil,    68,    69,    65,   nil,    51,    56,
   nil,    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,
    60,    62,    23,    24,    66,    67,   nil,   nil,   nil,   nil,
   nil,    22,    28,    27,    92,    91,    93,    94,   nil,   nil,
   221,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
    96,    95,    97,    86,    50,    88,    87,    89,   nil,    90,
    98,    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,
   220,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   219,   nil,   nil,   nil,   nil,    83,    75,    78,    79,
   nil,    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,
    69,    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,
   nil,   nil,    61,   nil,    59,    60,    62,   260,   261,    66,
    67,   nil,   nil,   nil,   nil,   nil,   259,    28,    27,    92,
    91,    93,    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,
   nil,   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,
    88,    87,    89,   263,    90,    98,    99,   nil,    84,    85,
    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,    52,    53,
   nil,   nil,    54,   nil,   258,   nil,   256,   nil,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,
   nil,    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,
   nil,    76,    82,   nil,    68,    69,    65,   nil,    51,    56,
   nil,    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,
    60,    62,   260,   261,    66,    67,   nil,   nil,   nil,   nil,
   nil,   259,    28,    27,    92,    91,    93,    94,   nil,   nil,
   221,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
    96,    95,    97,    86,    50,    88,    87,    89,   263,    90,
    98,    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,
   220,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   258,
   nil,   256,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   219,   nil,   nil,   nil,   nil,    83,    75,    78,    79,
   nil,    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,
    69,    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,
   nil,   nil,    61,   nil,    59,    60,    62,   260,   261,    66,
    67,   nil,   nil,   nil,   nil,   nil,   259,    28,    27,    92,
    91,    93,    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,
   nil,   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,
    88,    87,    89,   263,    90,    98,    99,   nil,    84,    85,
    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,    52,    53,
   nil,   nil,    54,   nil,   258,   nil,   256,   nil,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,
   nil,    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,
   nil,    76,    82,   nil,    68,    69,    65,   nil,    51,    56,
   nil,    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,
    60,    62,   260,   261,    66,    67,   nil,   nil,   nil,   nil,
   nil,   259,   290,   294,    92,    91,    93,    94,   nil,   nil,
   221,   nil,   nil,   nil,   nil,   nil,   nil,   291,   nil,   nil,
    96,    95,    97,    86,    50,    88,    87,    89,   nil,    90,
    98,    99,   nil,    84,    85,   nil,   644,   295,   641,   640,
   639,   645,   642,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   288,   nil,   nil,
   285,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   284,
   nil,   nil,   nil,   647,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   651,   650,   654,   653,    83,    75,    78,    79,
   nil,    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,
    69,    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,
   nil,   nil,    61,   nil,    59,    60,    62,   260,   261,    66,
    67,   nil,   nil,   nil,   nil,   nil,   259,   290,   294,    92,
    91,    93,    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,
   nil,   557,   291,   nil,   nil,    96,    95,    97,    86,    50,
    88,    87,    89,   nil,    90,    98,    99,   nil,    84,    85,
   nil,   nil,   295,   nil,   229,   233,   238,   239,   240,   235,
   237,   245,   246,   241,   242,   nil,   222,   223,   nil,   nil,
   243,   244,   288,   nil,   nil,   220,   nil,   nil,    52,    53,
   nil,   nil,    54,   nil,   nil,   nil,   226,   nil,   232,   nil,
   228,   227,   224,   225,   236,   234,   230,   nil,   231,   nil,
   nil,    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,
   nil,    76,    82,   nil,   nil,   247,   297,  -226,   nil,    56,
   nil,    77,    68,    69,    65,   nil,    51,   nil,   nil,   nil,
    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,
   260,   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,
   290,   294,    92,    91,    93,    94,   nil,   nil,   221,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,
    97,    86,    50,    88,    87,    89,   nil,    90,    98,    99,
   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,
   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,
    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,
   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,
    61,   nil,    59,    60,    62,   260,   261,    66,    67,   nil,
   nil,   nil,   nil,   nil,   259,   290,   294,    92,    91,    93,
    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,
    41,   nil,   nil,    96,    95,    97,    86,    50,    88,    87,
    89,   nil,    90,    98,    99,   nil,    84,    85,    38,    39,
    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,
    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,    83,
    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,
    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,
    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,
   260,   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,
   290,   294,    92,    91,    93,    94,   nil,   nil,   221,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,
    97,    86,    50,    88,    87,    89,   nil,    90,    98,    99,
   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,
   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,
    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,
   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,
    61,   nil,    59,    60,    62,    23,    24,    66,    67,   nil,
   nil,   nil,   nil,   nil,    22,    28,    27,    92,    91,    93,
    94,   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,   nil,
    41,   nil,   nil,    96,    95,    97,    86,    50,    88,    87,
    89,   nil,    90,    98,    99,   nil,    84,    85,    38,    39,
    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,
    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    18,   nil,   nil,   nil,   nil,    83,
    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,
    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,
    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,
   260,   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,
   290,   294,    92,    91,    93,    94,   nil,   nil,   221,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,
    97,    86,    50,    88,    87,    89,   263,    90,    98,    99,
   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   258,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,
   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,
    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,
   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,
    61,   nil,    59,    60,    62,   260,   261,    66,    67,   nil,
   nil,   nil,   nil,   nil,   259,   290,   294,    92,    91,    93,
    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,
    41,   nil,   nil,    96,    95,    97,    86,    50,    88,    87,
    89,   263,    90,    98,    99,   nil,    84,    85,    38,    39,
    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,
    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,    83,
    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,
    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,
    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,
    23,    24,    66,    67,   nil,   nil,   nil,   nil,   nil,    22,
    28,    27,    92,    91,    93,    94,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,
    97,    86,    50,    88,    87,    89,   nil,    90,    98,    99,
   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,
   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,
    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,
   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,
    61,   nil,    59,    60,    62,    23,    24,    66,    67,   nil,
   nil,   nil,   nil,   nil,    22,    28,    27,    92,    91,    93,
    94,   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,   nil,
    41,   nil,   nil,    96,    95,    97,    86,    50,    88,    87,
    89,   nil,    90,    98,    99,   nil,    84,    85,    38,    39,
    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,
    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    18,   nil,   nil,   nil,   nil,    83,
    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,
    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,
    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,
    23,    24,    66,    67,   nil,   nil,   nil,   nil,   nil,    22,
    28,    27,    92,    91,    93,    94,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   560,   nil,    41,   nil,   nil,    96,    95,
    97,    86,    50,    88,    87,    89,   nil,    90,    98,    99,
   nil,    84,    85,    38,    39,    37,   229,   233,   238,   239,
   240,   235,   237,   245,   246,   241,   242,   nil,   222,   223,
   nil,   nil,   243,   244,   nil,   214,   nil,   nil,   220,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   226,   nil,
   232,    40,   228,   227,   224,   225,   236,   234,   230,    18,
   231,   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,
    81,   nil,   nil,   nil,    76,    82,   104,   247,   nil,   nil,
   nil,   103,    56,   nil,    77,    68,    69,    65,   nil,    51,
   nil,   nil,   nil,    57,    58,   nil,   nil,   nil,    61,   nil,
    59,    60,    62,   260,   261,    66,    67,   nil,   nil,   nil,
   nil,   nil,   259,   290,   294,    92,    91,    93,    94,   nil,
   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,   291,   nil,
   nil,    96,    95,    97,    86,    50,    88,    87,    89,   nil,
    90,    98,    99,   nil,    84,    85,   nil,   724,   295,   641,
   640,   639,   645,   642,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   329,   nil,
   nil,    30,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,
    32,   nil,   nil,   nil,   647,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   651,   650,   654,   653,    83,    75,    78,
    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,   nil,
    68,    69,    65,   nil,    51,    56,   nil,    77,    57,    58,
   nil,   nil,   nil,    61,   nil,    59,    60,    62,   260,   261,
    66,    67,   nil,   nil,   nil,   nil,   nil,   259,   290,   294,
    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,   nil,
   nil,   nil,   nil,   291,   nil,   nil,    96,    95,    97,   334,
    50,    88,    87,   335,   nil,    90,    98,    99,   nil,    84,
    85,   nil,   724,   295,   641,   640,   639,   645,   642,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   341,   nil,   nil,   336,   nil,   nil,   220,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,   647,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   651,   650,
   654,   653,    83,    75,    78,    79,   nil,    80,    81,   nil,
   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,    51,
    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,   nil,
    59,    60,    62,   260,   261,    66,    67,   nil,   nil,   nil,
   nil,   nil,   259,   290,   294,    92,    91,    93,    94,   nil,
   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,   291,   nil,
   nil,    96,    95,    97,   334,    50,    88,    87,   335,   nil,
    90,    98,    99,   nil,    84,    85,   nil,   644,   295,   641,
   640,   639,   645,   642,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   336,   nil,
   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,
   nil,   nil,   nil,   nil,   647,   682,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   651,   650,   654,   653,    83,    75,    78,
    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,   nil,
    68,    69,    65,     7,    51,    56,   nil,    77,    57,    58,
   nil,   nil,   nil,    61,   nil,    59,    60,    62,    23,    24,
    66,    67,   nil,   nil,   nil,   nil,   nil,    22,    28,    27,
    92,    91,    93,    94,   nil,   nil,    17,   nil,   nil,   nil,
   nil,   557,     6,    41,     8,     9,    96,    95,    97,    86,
    50,    88,    87,    89,   nil,    90,    98,    99,   nil,    84,
    85,    38,    39,    37,   229,   233,   238,   239,   240,   235,
   237,   245,   246,   241,   242,   nil,   222,   223,   nil,   nil,
   243,   244,   nil,    36,   nil,   nil,    30,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,    32,   226,   nil,   232,    40,
   228,   227,   224,   225,   236,   234,   230,    18,   231,   nil,
   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,   nil,
   nil,   nil,    76,    82,   nil,   247,   nil,   nil,   nil,   366,
    56,   nil,    77,    68,    69,    65,   nil,    51,   nil,   nil,
   nil,    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,
    62,    23,    24,    66,    67,   nil,   nil,   nil,   nil,   nil,
    22,    28,    27,    92,    91,    93,    94,   nil,   nil,    17,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,
    95,    97,    86,    50,    88,    87,    89,   nil,    90,    98,
    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,
   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    18,   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,
    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,
    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,
   nil,    61,   nil,    59,    60,    62,    23,    24,    66,    67,
   nil,   nil,   nil,   nil,   nil,    22,    28,    27,    92,    91,
    93,    94,   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,    88,
    87,    89,   nil,    90,    98,    99,   nil,    84,    85,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,   nil,   nil,
    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,
    76,    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,
    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,
    62,    23,    24,    66,    67,   nil,   nil,   nil,   nil,   nil,
    22,    28,    27,    92,    91,    93,    94,   nil,   nil,    17,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,
    95,    97,    86,    50,    88,    87,    89,   nil,    90,    98,
    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,
   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    18,   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,
    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,
    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,
   nil,    61,   nil,    59,    60,    62,    23,    24,    66,    67,
   nil,   nil,   nil,   nil,   nil,    22,    28,    27,    92,    91,
    93,    94,   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,    88,
    87,    89,   nil,    90,    98,    99,   nil,    84,    85,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,   nil,   nil,
    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,
    76,    82,   nil,    68,    69,    65,     7,    51,    56,   nil,
    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,
    62,    23,    24,    66,    67,   nil,   nil,   nil,   nil,   nil,
    22,    28,    27,    92,    91,    93,    94,   nil,   nil,    17,
   nil,   nil,   nil,   nil,   nil,     6,    41,     8,     9,    96,
    95,    97,    86,    50,    88,    87,    89,   nil,    90,    98,
    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    36,   nil,   nil,    30,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,    32,   nil,
   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    18,   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,
    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,
    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,
   nil,    61,   nil,    59,    60,    62,    23,    24,    66,    67,
   nil,   nil,   nil,   nil,   nil,    22,    28,    27,    92,    91,
    93,    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,    88,
    87,    89,   nil,    90,    98,    99,   nil,    84,    85,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,   383,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,
    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,
    76,    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,
    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,
    62,    23,    24,    66,    67,   nil,   nil,   nil,   nil,   nil,
    22,    28,    27,    92,    91,    93,    94,   nil,   nil,   221,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,
    95,    97,    86,    50,    88,    87,    89,   nil,    90,    98,
    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   383,   nil,
   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   219,   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,
    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,
    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,
   nil,    61,   nil,    59,    60,    62,    23,    24,    66,    67,
   nil,   nil,   nil,   nil,   nil,    22,    28,    27,    92,    91,
    93,    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,    88,
    87,    89,   nil,    90,    98,    99,   nil,    84,    85,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,
    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,
    76,    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,
    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,
    62,   260,   261,    66,    67,   nil,   nil,   nil,   nil,   nil,
   259,    28,    27,    92,    91,    93,    94,   nil,   nil,   221,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,
    95,    97,    86,    50,    88,    87,    89,   263,    90,    98,
    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   258,   nil,
   256,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   219,   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,
    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,
    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,
   nil,    61,   nil,    59,    60,    62,    23,    24,    66,    67,
   nil,   nil,   nil,   nil,   nil,    22,    28,    27,    92,    91,
    93,    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,    88,
    87,    89,   nil,    90,    98,    99,   nil,    84,    85,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,
    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,
    76,    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,
    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,
    62,    23,    24,    66,    67,   nil,   nil,   nil,   nil,   nil,
    22,    28,    27,    92,    91,    93,    94,   nil,   nil,    17,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,
    95,    97,    86,    50,    88,    87,    89,   nil,    90,    98,
    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,
   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    18,   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,
    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,
    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,
   nil,    61,   nil,    59,    60,    62,    23,    24,    66,    67,
   nil,   nil,   nil,   nil,   nil,    22,    28,    27,    92,    91,
    93,    94,   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,    88,
    87,    89,   nil,    90,    98,    99,   nil,    84,    85,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,   nil,   nil,
    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,
    76,    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,
    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,
    62,    23,    24,    66,    67,   nil,   nil,   nil,   nil,   nil,
    22,    28,    27,    92,    91,    93,    94,   nil,   nil,    17,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,
    95,    97,    86,    50,    88,    87,    89,   nil,    90,    98,
    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,
   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    18,   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,
    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,
    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,
   nil,    61,   nil,    59,    60,    62,    23,    24,    66,    67,
   nil,   nil,   nil,   nil,   nil,    22,    28,    27,    92,    91,
    93,    94,   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,    88,
    87,    89,   nil,    90,    98,    99,   nil,    84,    85,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,   nil,   nil,
    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,
    76,    82,   210,    68,    69,    65,   nil,    51,    56,   nil,
    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,
    62,   260,   261,    66,    67,   nil,   nil,   nil,   nil,   nil,
   259,   290,   294,    92,    91,    93,    94,   nil,   nil,   221,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,
    95,    97,    86,    50,    88,    87,    89,   nil,    90,    98,
    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,
   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   219,   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,
    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,
    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,
   nil,    61,   nil,    59,    60,    62,   260,   261,    66,    67,
   nil,   nil,   nil,   nil,   nil,   259,   290,   294,    92,    91,
    93,    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,    88,
    87,    89,   nil,    90,    98,    99,   nil,    84,    85,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,
    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,
    76,    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,
    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,
    62,   260,   261,    66,    67,   nil,   nil,   nil,   nil,   nil,
   259,   290,   294,    92,    91,    93,    94,   nil,   nil,   221,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,
    95,    97,    86,    50,    88,    87,    89,   nil,    90,    98,
    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,
   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   219,   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,
    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,
    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,
   nil,    61,   nil,    59,    60,    62,   260,   261,    66,    67,
   nil,   nil,   nil,   nil,   nil,   259,   290,   294,    92,    91,
    93,    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,    88,
    87,    89,   nil,    90,    98,    99,   nil,    84,    85,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,
    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,
    76,    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,
    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,
    62,   260,   261,    66,    67,   nil,   nil,   nil,   nil,   nil,
   259,   290,   294,    92,    91,    93,    94,   nil,   nil,   221,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,
    95,    97,    86,    50,    88,    87,    89,   nil,    90,    98,
    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,
   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   219,   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,
    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,
    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,
   nil,    61,   nil,    59,    60,    62,   260,   261,    66,    67,
   nil,   nil,   nil,   nil,   nil,   259,   290,   294,    92,    91,
    93,    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,    88,
    87,    89,   nil,    90,    98,    99,   nil,    84,    85,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,
    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,
    76,    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,
    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,
    62,   260,   261,    66,    67,   nil,   nil,   nil,   nil,   nil,
   259,   290,   294,    92,    91,    93,    94,   nil,   nil,   221,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,
    95,    97,    86,    50,    88,    87,    89,   nil,    90,    98,
    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,
   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   219,   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,
    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,
    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,
   nil,    61,   nil,    59,    60,    62,   260,   261,    66,    67,
   nil,   nil,   nil,   nil,   nil,   259,   290,   294,    92,    91,
    93,    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,    88,
    87,    89,   nil,    90,    98,    99,   nil,    84,    85,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,
    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,
    76,    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,
    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,
    62,   260,   261,    66,    67,   nil,   nil,   nil,   nil,   nil,
   259,   290,   294,    92,    91,    93,    94,   nil,   nil,   221,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,
    95,    97,    86,    50,    88,    87,    89,   nil,    90,    98,
    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,
   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   219,   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,
    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,
    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,
   nil,    61,   nil,    59,    60,    62,   260,   261,    66,    67,
   nil,   nil,   nil,   nil,   nil,   259,   290,   294,    92,    91,
    93,    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,    88,
    87,    89,   nil,    90,    98,    99,   nil,    84,    85,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,
    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,
    76,    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,
    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,
    62,   260,   261,    66,    67,   nil,   nil,   nil,   nil,   nil,
   259,   290,   294,    92,    91,    93,    94,   nil,   nil,   221,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,
    95,    97,    86,    50,    88,    87,    89,   nil,    90,    98,
    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,
   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   219,   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,
    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,
    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,
   nil,    61,   nil,    59,    60,    62,   260,   261,    66,    67,
   nil,   nil,   nil,   nil,   nil,   259,   290,   294,    92,    91,
    93,    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,    88,
    87,    89,   nil,    90,    98,    99,   nil,    84,    85,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,
    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,
    76,    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,
    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,
    62,   260,   261,    66,    67,   nil,   nil,   nil,   nil,   nil,
   259,   290,   294,    92,    91,    93,    94,   nil,   nil,   221,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,
    95,    97,    86,    50,    88,    87,    89,   nil,    90,    98,
    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,
   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   219,   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,
    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,
    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,
   nil,    61,   nil,    59,    60,    62,   260,   261,    66,    67,
   nil,   nil,   nil,   nil,   nil,   259,   290,   294,    92,    91,
    93,    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,    88,
    87,    89,   nil,    90,    98,    99,   nil,    84,    85,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,
    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,
    76,    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,
    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,
    62,   260,   261,    66,    67,   nil,   nil,   nil,   nil,   nil,
   259,   290,   294,    92,    91,    93,    94,   nil,   nil,   221,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,
    95,    97,    86,    50,    88,    87,    89,   nil,    90,    98,
    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,
   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   219,   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,
    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,
    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,
   nil,    61,   nil,    59,    60,    62,   260,   261,    66,    67,
   nil,   nil,   nil,   nil,   nil,   259,   290,   294,    92,    91,
    93,    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,    88,
    87,    89,   nil,    90,    98,    99,   nil,    84,    85,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,
    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,
    76,    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,
    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,
    62,   260,   261,    66,    67,   nil,   nil,   nil,   nil,   nil,
   259,   290,   294,    92,    91,    93,    94,   nil,   nil,   221,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,
    95,    97,    86,    50,    88,    87,    89,   nil,    90,    98,
    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,
   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   219,   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,
    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,
    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,
   nil,    61,   nil,    59,    60,    62,   260,   261,    66,    67,
   nil,   nil,   nil,   nil,   nil,   259,   290,   294,    92,    91,
    93,    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,    88,
    87,    89,   nil,    90,    98,    99,   nil,    84,    85,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,
    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,
    76,    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,
    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,
    62,   260,   261,    66,    67,   nil,   nil,   nil,   nil,   nil,
   259,   290,   294,    92,    91,    93,    94,   nil,   nil,   221,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,
    95,    97,    86,    50,    88,    87,    89,   nil,    90,    98,
    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,
   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   219,   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,
    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,
    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,
   nil,    61,   nil,    59,    60,    62,   260,   261,    66,    67,
   nil,   nil,   nil,   nil,   nil,   259,   290,   294,    92,    91,
    93,    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,    88,
    87,    89,   nil,    90,    98,    99,   nil,    84,    85,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,
    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,
    76,    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,
    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,
    62,   260,   261,    66,    67,   nil,   nil,   nil,   nil,   nil,
   259,   290,   294,    92,    91,    93,    94,   nil,   nil,   221,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,
    95,    97,    86,    50,    88,    87,    89,   nil,    90,    98,
    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,
   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   219,   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,
    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,
    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,
   nil,    61,   nil,    59,    60,    62,   260,   261,    66,    67,
   nil,   nil,   nil,   nil,   nil,   259,   290,   294,    92,    91,
    93,    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,    88,
    87,    89,   nil,    90,    98,    99,   nil,    84,    85,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,
    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,
    76,    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,
    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,
    62,   260,   261,    66,    67,   nil,   nil,   nil,   nil,   nil,
   259,   290,   294,    92,    91,    93,    94,   nil,   nil,   221,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,
    95,    97,    86,    50,    88,    87,    89,   nil,    90,    98,
    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,
   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   219,   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,
    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,
    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,
   nil,    61,   nil,    59,    60,    62,   260,   261,    66,    67,
   nil,   nil,   nil,   nil,   nil,   259,   290,   294,    92,    91,
    93,    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,    88,
    87,    89,   nil,    90,    98,    99,   nil,    84,    85,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,
    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,
    76,    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,
    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,
    62,   260,   261,    66,    67,   nil,   nil,   nil,   nil,   nil,
   259,   290,   294,    92,    91,    93,    94,   nil,   nil,   221,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,
    95,    97,    86,    50,    88,    87,    89,   nil,    90,    98,
    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,
   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   219,   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,
    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,
    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,
   nil,    61,   nil,    59,    60,    62,   260,   261,    66,    67,
   nil,   nil,   nil,   nil,   nil,   259,   290,   294,    92,    91,
    93,    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,    88,
    87,    89,   nil,    90,    98,    99,   nil,    84,    85,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,
    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,
    76,    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,
    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,
    62,   260,   261,    66,    67,   nil,   nil,   nil,   nil,   nil,
   259,   290,   294,    92,    91,    93,    94,   nil,   nil,   221,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,
    95,    97,    86,    50,    88,    87,    89,   nil,    90,    98,
    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,
   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   219,   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,
    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,
    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,
   nil,    61,   nil,    59,    60,    62,   260,   261,    66,    67,
   nil,   nil,   nil,   nil,   nil,   259,   290,   294,    92,    91,
    93,    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,    88,
    87,    89,   nil,    90,    98,    99,   nil,    84,    85,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,
    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,
    76,    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,
    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,
    62,   260,   261,    66,    67,   nil,   nil,   nil,   nil,   nil,
   259,   290,   294,    92,    91,    93,    94,   nil,   nil,   221,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,
    95,    97,    86,    50,    88,    87,    89,   nil,    90,    98,
    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,
   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   219,   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,
    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,
    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,
   nil,    61,   nil,    59,    60,    62,   260,   261,    66,    67,
   nil,   nil,   nil,   nil,   nil,   259,   290,   294,    92,    91,
    93,    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,    88,
    87,    89,   nil,    90,    98,    99,   nil,    84,    85,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,
    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,
    76,    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,
    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,
    62,   260,   261,    66,    67,   nil,   nil,   nil,   nil,   nil,
   259,    28,    27,    92,    91,    93,    94,   nil,   nil,   221,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,
    95,    97,    86,    50,    88,    87,    89,   263,    90,    98,
    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   258,   nil,
   256,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   219,   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,
    80,    81,   nil,   nil,   nil,    76,    82,   nil,    68,    69,
    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,
   nil,    61,   nil,    59,    60,    62,   260,   261,    66,    67,
   nil,   nil,   nil,   nil,   nil,   259,    28,    27,    92,    91,
    93,    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    96,    95,    97,    86,    50,    88,
    87,    89,   263,    90,    98,    99,   nil,    84,    85,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   220,   nil,   nil,   464,    53,   nil,
   nil,    54,   nil,   258,   nil,   256,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,
    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,
    76,    82,   nil,    68,    69,    65,   nil,    51,    56,   nil,
    77,    57,    58,   nil,   nil,   nil,    61,   nil,    59,    60,
    62,   260,   261,    66,    67,   nil,   nil,   nil,   nil,   nil,
   259,    28,    27,    92,    91,    93,    94,   nil,   nil,   221,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,
    95,    97,    86,    50,    88,    87,    89,   263,    90,    98,
    99,   nil,    84,    85,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   258,   nil,
   256,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   219,   nil,   nil,   nil,   nil,    83,    75,    78,    79,   nil,
    80,    81,   nil,   nil,   nil,    76,    82,   210,    68,    69,
    65,   nil,    51,    56,   nil,    77,    57,    58,   nil,   nil,
   nil,    61,   nil,    59,    60,    62,   260,   261,    66,    67,
   nil,   nil,   nil,   nil,   nil,   259,   290,   294,    92,    91,
    93,    94,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,
   nil,   291,   nil,   nil,    96,    95,    97,    86,    50,    88,
    87,    89,   nil,    90,    98,    99,   nil,    84,    85,   nil,
   nil,   295,   229,   233,   238,   239,   240,   235,   237,   245,
   246,   241,   242,   nil,   222,   223,   nil,   nil,   243,   244,
   nil,   288,   nil,   nil,   220,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,   nil,   226,   nil,   232,   nil,   228,   227,
   224,   225,   236,   234,   230,   nil,   231,   nil,   nil,   nil,
    83,    75,    78,    79,   nil,    80,    81,   nil,   nil,   nil,
    76,    82,   nil,   247,   nil,   474,   nil,   nil,    56,   nil,
    77,    68,    69,    65,   nil,    51,   nil,   nil,   nil,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,   260,
   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,   290,
   294,    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   nil,   nil,   291,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   nil,    90,    98,    99,   nil,
    84,    85,   nil,   644,   295,   641,   640,   639,   645,   642,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   288,   nil,   nil,   285,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,
   647,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   651,
   650,   654,   653,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,   260,   261,    66,    67,   nil,   nil,
   nil,   nil,   nil,   259,   290,   294,    92,    91,    93,    94,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   nil,    90,    98,    99,   nil,    84,    85,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   220,   492,   nil,    52,    53,   nil,   nil,    54,
   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,    23,
    24,    66,    67,   nil,   nil,   nil,   nil,   nil,    22,    28,
    27,    92,    91,    93,    94,   nil,   nil,    17,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   nil,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,    23,    24,    66,    67,   nil,   nil,
   nil,   nil,   nil,    22,    28,    27,    92,    91,    93,    94,
   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   nil,    90,    98,    99,   nil,    84,    85,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    18,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,    23,
    24,    66,    67,   nil,   nil,   nil,   nil,   nil,    22,    28,
    27,    92,    91,    93,    94,   nil,   nil,    17,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   nil,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,    23,    24,    66,    67,   nil,   nil,
   nil,   nil,   nil,    22,    28,    27,    92,    91,    93,    94,
   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   nil,    90,    98,    99,   nil,    84,    85,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    18,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,   260,
   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,   290,
   294,    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   nil,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,   260,   261,    66,    67,   nil,   nil,
   nil,   nil,   nil,   259,    28,    27,    92,    91,    93,    94,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   263,    90,    98,    99,   nil,    84,    85,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   258,   nil,   256,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,   260,
   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,   290,
   294,    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   nil,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,   260,   261,    66,    67,   nil,   nil,
   nil,   nil,   nil,   259,   290,   294,    92,    91,    93,    94,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   nil,    90,    98,    99,   nil,    84,    85,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,   260,
   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,   290,
   294,    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   nil,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,   260,   261,    66,    67,   nil,   nil,
   nil,   nil,   nil,   259,   290,   294,    92,    91,    93,    94,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   263,    90,    98,    99,   nil,    84,    85,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   588,   nil,   256,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,   260,
   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,   290,
   294,    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   263,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   256,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,   260,   261,    66,    67,   nil,   nil,
   nil,   nil,   nil,   259,   290,   294,    92,    91,    93,    94,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   nil,    90,    98,    99,   nil,    84,    85,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,    23,
    24,    66,    67,   nil,   nil,   nil,   nil,   nil,    22,    28,
    27,    92,    91,    93,    94,   nil,   nil,    17,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   263,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   256,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,   260,   261,    66,    67,   nil,   nil,
   nil,   nil,   nil,   259,   290,   294,    92,    91,    93,    94,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,   291,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   nil,    90,    98,    99,   nil,    84,    85,   nil,   724,   295,
   641,   640,   639,   645,   642,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   288,
   nil,   nil,   285,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   nil,   nil,   nil,   nil,   647,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   651,   650,   654,   653,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,   260,
   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,    28,
    27,    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   263,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   258,   nil,   256,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,   260,   261,    66,    67,   nil,   nil,
   nil,   nil,   nil,   259,   290,   294,    92,    91,    93,    94,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,   291,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   nil,    90,    98,    99,   nil,    84,    85,   nil,   nil,   295,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   288,
   nil,   nil,   285,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,   260,
   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,   290,
   294,    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   nil,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,   260,   261,    66,    67,   nil,   nil,
   nil,   nil,   nil,   259,   290,   294,    92,    91,    93,    94,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   nil,    90,    98,    99,   nil,    84,    85,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,   260,
   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,   290,
   294,    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   nil,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,    23,    24,    66,    67,   nil,   nil,
   nil,   nil,   nil,    22,    28,    27,    92,    91,    93,    94,
   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   nil,    90,    98,    99,   nil,    84,    85,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    18,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,   260,
   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,   290,
   294,    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   nil,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   383,   nil,   nil,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,   260,   261,    66,    67,   nil,   nil,
   nil,   nil,   nil,   259,   290,   294,    92,    91,    93,    94,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   263,    90,    98,    99,   nil,    84,    85,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   588,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,   260,
   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,   290,
   294,    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   263,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,   260,   261,    66,    67,   nil,   nil,
   nil,   nil,   nil,   259,   290,   294,    92,    91,    93,    94,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   nil,    90,    98,    99,   nil,    84,    85,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   258,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,   260,
   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,    28,
    27,    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   263,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   258,   nil,   256,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,   260,   261,    66,    67,   nil,   nil,
   nil,   nil,   nil,   259,    28,    27,    92,    91,    93,    94,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   263,    90,    98,    99,   nil,    84,    85,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   258,   nil,   256,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,    23,
    24,    66,    67,   nil,   nil,   nil,   nil,   nil,    22,    28,
    27,    92,    91,    93,    94,   nil,   nil,    17,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   nil,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,   260,   261,    66,    67,   nil,   nil,
   nil,   nil,   nil,   259,   290,   294,    92,    91,    93,    94,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   nil,    90,    98,    99,   nil,    84,    85,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,   260,
   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,   290,
   294,    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   nil,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   699,   nil,   nil,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,   260,   261,    66,    67,   nil,   nil,
   nil,   nil,   nil,   259,   290,   294,    92,    91,    93,    94,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   nil,    90,    98,    99,   nil,    84,    85,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,    23,
    24,    66,    67,   nil,   nil,   nil,   nil,   nil,    22,    28,
    27,    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   nil,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,    23,    24,    66,    67,   nil,   nil,
   nil,   nil,   nil,    22,    28,    27,    92,    91,    93,    94,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   nil,    90,    98,    99,   nil,    84,    85,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,    23,
    24,    66,    67,   nil,   nil,   nil,   nil,   nil,    22,    28,
    27,    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   nil,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,   260,   261,    66,    67,   nil,   nil,
   nil,   nil,   nil,   259,   290,   294,    92,    91,    93,    94,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   nil,    90,    98,    99,   nil,    84,    85,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,   260,
   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,   290,
   294,    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   nil,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,   260,   261,    66,    67,   nil,   nil,
   nil,   nil,   nil,   259,   290,   294,    92,    91,    93,    94,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,   291,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   nil,    90,    98,    99,   nil,    84,    85,   nil,   nil,   295,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   288,
   nil,   nil,   285,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,   260,
   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,   290,
   294,    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   nil,   nil,   291,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   nil,    90,    98,    99,   nil,
    84,    85,   nil,   nil,   295,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   288,   nil,   nil,   285,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,    23,    24,    66,    67,   nil,   nil,
   nil,   nil,   nil,    22,    28,    27,    92,    91,    93,    94,
   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   nil,    90,    98,    99,   nil,    84,    85,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    18,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,   260,
   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,   290,
   294,    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   nil,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,    23,    24,    66,    67,   nil,   nil,
   nil,   nil,   nil,    22,    28,    27,    92,    91,    93,    94,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   nil,    90,    98,    99,   nil,    84,    85,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,   260,
   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,   290,
   294,    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   nil,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,   260,   261,    66,    67,   nil,   nil,
   nil,   nil,   nil,   259,   290,   294,    92,    91,    93,    94,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   nil,    90,    98,    99,   nil,    84,    85,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,   260,
   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,   290,
   294,    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   nil,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,   260,   261,    66,    67,   nil,   nil,
   nil,   nil,   nil,   259,   290,   294,    92,    91,    93,    94,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   nil,    90,    98,    99,   nil,    84,    85,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,   260,
   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,   290,
   294,    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   263,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   258,   nil,   256,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,   260,   261,    66,    67,   nil,   nil,
   nil,   nil,   nil,   259,   290,   294,    92,    91,    93,    94,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   263,    90,    98,    99,   nil,    84,    85,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   258,   nil,   256,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,   260,
   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,   290,
   294,    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   nil,   nil,   291,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   nil,    90,    98,    99,   nil,
    84,    85,   nil,   nil,   295,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   824,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,   260,   261,    66,    67,   nil,   nil,
   nil,   nil,   nil,   259,   290,   294,    92,    91,    93,    94,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   nil,    90,    98,    99,   nil,    84,    85,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,    23,
    24,    66,    67,   nil,   nil,   nil,   nil,   nil,    22,    28,
    27,    92,    91,    93,    94,   nil,   nil,    17,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   nil,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,   260,   261,    66,    67,   nil,   nil,
   nil,   nil,   nil,   259,   290,   294,    92,    91,    93,    94,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   nil,    90,    98,    99,   nil,    84,    85,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   588,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,   260,
   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,   290,
   294,    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   nil,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,   260,   261,    66,    67,   nil,   nil,
   nil,   nil,   nil,   259,   290,   294,    92,    91,    93,    94,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,   291,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   nil,    90,    98,    99,   nil,    84,    85,   nil,   nil,   295,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   868,
   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,   260,
   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,   290,
   294,    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   nil,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   nil,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   588,   nil,   256,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,    68,    69,    65,   nil,
    51,    56,   nil,    77,    57,    58,   nil,   nil,   nil,    61,
   nil,    59,    60,    62,   260,   261,    66,    67,   nil,   nil,
   nil,   nil,   nil,   259,   290,   294,    92,    91,    93,    94,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    96,    95,    97,    86,    50,    88,    87,    89,
   263,    90,    98,    99,   nil,    84,    85,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   220,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,   588,   nil,   256,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,    83,    75,
    78,    79,   nil,    80,    81,   nil,   nil,   nil,    76,    82,
   nil,    68,    69,    65,   nil,    51,    56,   nil,    77,    57,
    58,   nil,   nil,   nil,    61,   nil,    59,    60,    62,   260,
   261,    66,    67,   nil,   nil,   nil,   nil,   nil,   259,    28,
    27,    92,    91,    93,    94,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,    96,    95,    97,
    86,    50,    88,    87,    89,   263,    90,    98,    99,   nil,
    84,    85,    38,    39,    37,   229,   233,   238,   239,   240,
   235,   237,   245,   246,   241,   242,   nil,   222,   223,   nil,
   nil,   243,   244,   nil,   214,   nil,  -226,   220,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,   258,   226,   256,   232,
    40,   228,   227,   224,   225,   236,   234,   230,   219,   231,
   nil,   nil,   nil,    83,    75,    78,    79,   nil,    80,    81,
   nil,   nil,   nil,    76,    82,   nil,   247,   nil,  -226,   nil,
   nil,    56,   nil,    77,   162,   173,   163,   186,   159,   179,
   169,   168,   189,   190,   184,   167,   166,   161,   187,   191,
   192,   171,   160,   174,   178,   180,   172,   165,   nil,   nil,
   nil,   181,   188,   183,   182,   175,   185,   170,   158,   177,
   176,   nil,   nil,   nil,   nil,   nil,   157,   164,   155,   156,
   152,   153,   154,   114,   116,   113,   nil,   115,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   147,   148,   nil,   144,   126,
   127,   128,   135,   132,   134,   nil,   nil,   129,   130,   nil,
   nil,   nil,   149,   150,   136,   137,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   141,
   140,   nil,   125,   146,   143,   142,   138,   139,   133,   131,
   123,   145,   124,   nil,   nil,   151,    83,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    82,   162,   173,
   163,   186,   159,   179,   169,   168,   189,   190,   184,   167,
   166,   161,   187,   191,   192,   171,   160,   174,   178,   180,
   172,   165,   nil,   nil,   nil,   181,   188,   183,   182,   175,
   185,   170,   158,   177,   176,   nil,   nil,   nil,   nil,   nil,
   157,   164,   155,   156,   152,   153,   154,   114,   116,   nil,
   nil,   115,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   147,
   148,   nil,   144,   126,   127,   128,   135,   132,   134,   nil,
   nil,   129,   130,   nil,   nil,   nil,   149,   150,   136,   137,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   141,   140,   nil,   125,   146,   143,   142,
   138,   139,   133,   131,   123,   145,   124,   nil,   nil,   151,
    83,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    82,   162,   173,   163,   186,   159,   179,   169,   168,
   189,   190,   184,   167,   166,   161,   187,   191,   192,   171,
   160,   174,   178,   180,   172,   165,   nil,   nil,   nil,   181,
   188,   183,   182,   175,   185,   170,   158,   177,   176,   nil,
   nil,   nil,   nil,   nil,   157,   164,   155,   156,   152,   153,
   154,   114,   116,   nil,   nil,   115,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   147,   148,   nil,   144,   126,   127,   128,
   135,   132,   134,   nil,   nil,   129,   130,   nil,   nil,   nil,
   149,   150,   136,   137,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   141,   140,   nil,
   125,   146,   143,   142,   138,   139,   133,   131,   123,   145,
   124,   nil,   nil,   151,    83,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    82,   162,   173,   163,   186,
   159,   179,   169,   168,   189,   190,   184,   167,   166,   161,
   187,   191,   192,   171,   160,   174,   178,   180,   172,   165,
   nil,   nil,   nil,   181,   188,   183,   182,   175,   185,   170,
   158,   177,   176,   nil,   nil,   nil,   nil,   nil,   157,   164,
   155,   156,   152,   153,   154,   114,   116,   nil,   nil,   115,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   147,   148,   nil,
   144,   126,   127,   128,   135,   132,   134,   nil,   nil,   129,
   130,   nil,   nil,   nil,   149,   150,   136,   137,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   141,   140,   nil,   125,   146,   143,   142,   138,   139,
   133,   131,   123,   145,   124,   nil,   nil,   151,    83,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    82,
   162,   173,   163,   186,   159,   179,   169,   168,   189,   190,
   184,   167,   166,   161,   187,   191,   192,   171,   160,   174,
   178,   180,   172,   165,   nil,   nil,   nil,   181,   188,   183,
   351,   350,   352,   349,   158,   177,   176,   nil,   nil,   nil,
   nil,   nil,   157,   164,   155,   156,   346,   347,   348,   344,
   116,    88,    87,   345,   nil,    90,   nil,   nil,   nil,   nil,
   nil,   147,   148,   nil,   144,   126,   127,   128,   135,   132,
   134,   nil,   nil,   129,   130,   nil,   nil,   nil,   149,   150,
   136,   137,   nil,   nil,   nil,   nil,   nil,   356,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   141,   140,   nil,   125,   146,
   143,   142,   138,   139,   133,   131,   123,   145,   124,   nil,
   nil,   151,   162,   173,   163,   186,   159,   179,   169,   168,
   189,   190,   184,   167,   166,   161,   187,   191,   192,   171,
   160,   174,   178,   180,   172,   165,   nil,   nil,   nil,   181,
   188,   183,   182,   175,   185,   170,   158,   177,   176,   nil,
   nil,   nil,   nil,   nil,   157,   164,   155,   156,   152,   153,
   154,   114,   116,   nil,   nil,   115,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   147,   148,   nil,   144,   126,   127,   128,
   135,   132,   134,   nil,   nil,   129,   130,   nil,   nil,   nil,
   149,   150,   136,   137,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   141,   140,   nil,
   125,   146,   143,   142,   138,   139,   133,   131,   123,   145,
   124,   392,   396,   151,   nil,   393,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   147,   148,   nil,   144,   126,   127,   128,
   135,   132,   134,   nil,   nil,   129,   130,   nil,   nil,   nil,
   149,   150,   136,   137,   nil,   nil,   nil,   nil,   nil,   269,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   141,   140,   nil,
   125,   146,   143,   142,   138,   139,   133,   131,   123,   145,
   124,   398,   403,   151,   nil,   400,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   147,   148,   nil,   144,   126,   127,   128,
   135,   132,   134,   nil,   nil,   129,   130,   nil,   nil,   nil,
   149,   150,   136,   137,   nil,   nil,   nil,   nil,   nil,   269,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   141,   140,   nil,
   125,   146,   143,   142,   138,   139,   133,   131,   123,   145,
   124,   456,   396,   151,   nil,   457,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   147,   148,   nil,   144,   126,   127,   128,
   135,   132,   134,   nil,   nil,   129,   130,   nil,   nil,   nil,
   149,   150,   136,   137,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   141,   140,   nil,
   125,   146,   143,   142,   138,   139,   133,   131,   123,   145,
   124,   456,   396,   151,   nil,   457,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   147,   148,   nil,   144,   126,   127,   128,
   135,   132,   134,   nil,   nil,   129,   130,   nil,   nil,   nil,
   149,   150,   136,   137,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   141,   140,   nil,
   125,   146,   143,   142,   138,   139,   133,   131,   123,   145,
   124,   574,   396,   151,   nil,   575,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   147,   148,   nil,   144,   126,   127,   128,
   135,   132,   134,   nil,   nil,   129,   130,   nil,   nil,   nil,
   149,   150,   136,   137,   nil,   nil,   nil,   nil,   nil,   269,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   141,   140,   nil,
   125,   146,   143,   142,   138,   139,   133,   131,   123,   145,
   124,   576,   403,   151,   nil,   577,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   147,   148,   nil,   144,   126,   127,   128,
   135,   132,   134,   nil,   nil,   129,   130,   nil,   nil,   nil,
   149,   150,   136,   137,   nil,   nil,   nil,   nil,   nil,   269,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   141,   140,   nil,
   125,   146,   143,   142,   138,   139,   133,   131,   123,   145,
   124,   611,   396,   151,   nil,   612,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   147,   148,   nil,   144,   126,   127,   128,
   135,   132,   134,   nil,   nil,   129,   130,   nil,   nil,   nil,
   149,   150,   136,   137,   nil,   nil,   nil,   nil,   nil,   269,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   141,   140,   nil,
   125,   146,   143,   142,   138,   139,   133,   131,   123,   145,
   124,   614,   403,   151,   nil,   615,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   147,   148,   nil,   144,   126,   127,   128,
   135,   132,   134,   nil,   nil,   129,   130,   nil,   nil,   nil,
   149,   150,   136,   137,   nil,   nil,   nil,   nil,   nil,   269,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   141,   140,   nil,
   125,   146,   143,   142,   138,   139,   133,   131,   123,   145,
   124,   574,   396,   151,   nil,   575,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   147,   148,   nil,   144,   126,   127,   128,
   135,   132,   134,   nil,   nil,   129,   130,   nil,   nil,   nil,
   149,   150,   136,   137,   nil,   nil,   nil,   nil,   nil,   269,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   141,   140,   nil,
   125,   146,   143,   142,   138,   139,   133,   131,   123,   145,
   124,   576,   403,   151,   nil,   577,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   147,   148,   nil,   144,   126,   127,   128,
   135,   132,   134,   nil,   nil,   129,   130,   nil,   nil,   nil,
   149,   150,   136,   137,   nil,   nil,   nil,   nil,   nil,   269,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   141,   140,   nil,
   125,   146,   143,   142,   138,   139,   133,   131,   123,   145,
   124,   667,   396,   151,   nil,   668,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   147,   148,   nil,   144,   126,   127,   128,
   135,   132,   134,   nil,   nil,   129,   130,   nil,   nil,   nil,
   149,   150,   136,   137,   nil,   nil,   nil,   nil,   nil,   269,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   141,   140,   nil,
   125,   146,   143,   142,   138,   139,   133,   131,   123,   145,
   124,   669,   403,   151,   nil,   670,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   147,   148,   nil,   144,   126,   127,   128,
   135,   132,   134,   nil,   nil,   129,   130,   nil,   nil,   nil,
   149,   150,   136,   137,   nil,   nil,   nil,   nil,   nil,   269,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   141,   140,   nil,
   125,   146,   143,   142,   138,   139,   133,   131,   123,   145,
   124,   672,   403,   151,   nil,   673,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   147,   148,   nil,   144,   126,   127,   128,
   135,   132,   134,   nil,   nil,   129,   130,   nil,   nil,   nil,
   149,   150,   136,   137,   nil,   nil,   nil,   nil,   nil,   269,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   141,   140,   nil,
   125,   146,   143,   142,   138,   139,   133,   131,   123,   145,
   124,   456,   396,   151,   nil,   457,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   147,   148,   nil,   144,   126,   127,   128,
   135,   132,   134,   nil,   nil,   129,   130,   nil,   nil,   nil,
   149,   150,   136,   137,   nil,   nil,   nil,   nil,   nil,   269,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   141,   140,   nil,
   125,   146,   143,   142,   138,   139,   133,   131,   123,   145,
   124,   920,   396,   151,   nil,   921,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   147,   148,   nil,   144,   126,   127,   128,
   135,   132,   134,   nil,   nil,   129,   130,   nil,   nil,   nil,
   149,   150,   136,   137,   nil,   nil,   nil,   nil,   nil,   269,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   141,   140,   nil,
   125,   146,   143,   142,   138,   139,   133,   131,   123,   145,
   124,   922,   403,   151,   nil,   923,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   147,   148,   nil,   144,   126,   127,   128,
   135,   132,   134,   nil,   nil,   129,   130,   nil,   nil,   nil,
   149,   150,   136,   137,   nil,   nil,   nil,   nil,   nil,   269,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   141,   140,   nil,
   125,   146,   143,   142,   138,   139,   133,   131,   123,   145,
   124,   942,   403,   151,   nil,   941,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   147,   148,   nil,   144,   126,   127,   128,
   135,   132,   134,   nil,   nil,   129,   130,   nil,   nil,   nil,
   149,   150,   136,   137,   nil,   nil,   nil,   nil,   nil,   269,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   141,   140,   nil,
   125,   146,   143,   142,   138,   139,   133,   131,   123,   145,
   124,   nil,   nil,   151,   229,   233,   238,   239,   240,   235,
   237,   245,   246,   241,   242,   nil,   222,   223,   nil,   nil,
   243,   244,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   226,   nil,   232,   nil,
   228,   227,   224,   225,   236,   234,   230,   nil,   231,   nil,
   229,   233,   238,   239,   240,   235,   237,   245,   246,   241,
   242,   nil,   222,   223,   nil,   247,   243,   244,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   226,   nil,   232,   nil,   228,   227,   224,   225,
   236,   234,   230,   nil,   231,   nil,   229,   233,   238,   239,
   240,   235,   237,   245,   246,   241,   242,   nil,   222,   223,
   nil,   247,   243,   244,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   226,   nil,
   232,   nil,   228,   227,   224,   225,   236,   234,   230,   nil,
   231,   nil,   229,   233,   238,   239,   240,   235,   237,   245,
   246,   241,   242,   nil,   222,   223,   210,   247,   243,   244,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   226,   nil,   232,   nil,   228,   227,
   224,   225,   236,   234,   230,   nil,   231,   nil,   229,   233,
   238,   239,   240,   235,   237,   245,   246,   241,   242,   nil,
   222,   223,   nil,   247,   243,   244,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   226,   nil,   232,   nil,   228,   227,   224,   225,   236,   234,
   230,   nil,   231,   nil,   229,   233,   238,   239,   240,   235,
   237,   245,   246,   241,   242,   nil,   222,   223,   nil,   247,
   243,   244,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   226,   nil,   232,   nil,
   228,   227,   224,   225,   236,   234,   230,   nil,   231,   nil,
   229,   233,   238,   239,   240,   235,   237,   245,   246,   241,
   242,   nil,   222,   223,   nil,   247,   243,   244,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   226,   nil,   232,   nil,   228,   227,   224,   225,
   236,   234,   230,   nil,   231,   nil,   229,   233,   238,   239,
   240,   235,   237,   245,   246,   241,   242,   nil,   222,   223,
   nil,   247,   243,   244,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   226,   nil,
   232,   nil,   228,   227,   224,   225,   236,   234,   230,   nil,
   231,   nil,   229,   233,   238,   239,   240,   235,   237,   245,
   246,   241,   242,   nil,   222,   223,   nil,   247,   243,   244,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   226,   nil,   232,   nil,   228,   227,
   224,   225,   236,   234,   230,   nil,   231,   nil,   229,   233,
   238,   239,   240,   235,   237,   245,   246,   241,   242,   nil,
   222,   223,   nil,   247,   243,   244,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   226,   nil,   232,   nil,   228,   227,   224,   225,   236,   234,
   230,   nil,   231,   nil,   229,   233,   238,   239,   240,   235,
   237,   245,   246,   241,   242,   nil,   222,   223,   nil,   247,
   243,   244,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   226,   nil,   232,   nil,
   228,   227,   224,   225,   236,   234,   230,   nil,   231,   nil,
   229,   233,   238,   239,   240,   235,   237,   245,   246,   241,
   242,   nil,   222,   223,   nil,   247,   243,   244,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   226,   nil,   232,   nil,   228,   227,   224,   225,
   236,   234,   230,   nil,   231,   nil,   229,   233,   238,   239,
   240,   235,   237,   245,   246,   241,   242,   nil,   222,   223,
   nil,   247,   243,   244,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   226,   nil,
   232,   nil,   228,   227,   224,   225,   236,   234,   230,   nil,
   231,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   247 ]

racc_action_check = [
    86,     0,     0,     0,     0,     0,     0,    86,    86,    86,
     0,     0,    86,    86,    86,     0,    86,     0,     0,     0,
     0,     0,     0,     0,    86,    55,    86,    86,    86,     0,
     0,     0,     0,     0,     0,     0,    86,    86,     0,    86,
    86,    86,    86,    86,     0,     0,     0,     0,     0,     0,
     0,     0,     0,     0,     0,     0,   340,     0,     0,     0,
   320,     0,     0,     0,     0,     0,    86,    86,    86,    86,
    86,    86,    86,    86,    86,    86,    86,    86,    86,    86,
     1,   598,    86,    86,    86,     0,    86,    86,     0,   327,
    86,     0,     0,    86,    86,     0,    86,     0,    86,   362,
    86,     0,    86,    86,    86,    86,    86,    86,    86,     0,
    86,    55,    86,   330,     0,     0,     0,     0,     8,     0,
     0,   548,   519,   598,     0,     0,    86,    86,    86,    86,
    89,    86,     0,    86,     0,    86,    86,    89,    89,    89,
   213,     9,    89,    89,    89,   511,    89,    10,   321,   512,
    17,   410,   410,    25,    89,   665,    89,    89,    89,     3,
    25,   340,   362,   632,     3,   667,    89,    89,   668,    89,
    89,    89,    89,    89,   320,   745,   822,   825,   846,   320,
   781,   340,   548,   548,   879,   920,   340,    17,   336,    11,
   213,   548,   336,   215,   327,   669,    89,    89,    89,    89,
    89,    89,    89,    89,    89,    89,    89,    89,    89,    89,
   324,    25,    89,    89,    89,   324,    89,    89,   330,   921,
    89,   944,   611,    89,    89,    12,    89,   519,    89,   851,
    89,   851,    89,    89,    89,    89,    89,    89,    89,   398,
    89,   410,    89,   215,   344,   511,   398,   398,   398,   512,
   511,   344,   398,   398,   512,   398,    89,    89,    89,    89,
   669,    89,   321,    89,   398,    89,    89,   321,   632,   665,
   667,   670,   665,   668,   665,   398,   398,   782,   398,   398,
   398,   398,   398,   922,   311,   781,   923,   311,   611,   745,
   822,   825,   846,    20,   745,   822,   825,   846,   879,   920,
   669,   612,   344,   879,   920,   398,   398,   398,   398,   398,
   398,   398,   398,   398,   398,   398,   398,   398,   398,   537,
   537,   398,   398,   398,   611,   398,    34,   611,   416,   398,
   644,   358,   398,   921,   611,   944,   670,   398,   921,   398,
   944,   398,   398,   398,   398,   398,   398,   398,   400,   398,
   398,   398,   531,   531,   574,   400,   400,   400,   575,    36,
   922,   400,   400,   923,   400,   398,   398,   612,   398,    41,
   398,   622,   622,    26,   398,   398,   670,   782,   416,   644,
   292,   334,   782,   100,   400,   400,    14,   400,   400,   400,
   400,   400,   358,   358,   358,   312,   315,   922,   312,   315,
   923,   359,   922,   612,   574,   923,   612,   360,   575,   537,
    15,    15,   193,   612,   400,   400,   400,   400,   400,   400,
   400,   400,   400,   400,   400,   400,   400,   400,    35,    14,
   400,   400,   400,   334,   400,   214,    14,    26,   400,   216,
   334,   400,   531,   335,   292,   334,   400,   531,   400,   334,
   400,   400,   400,   400,   400,   400,   400,   361,   400,    26,
   400,   622,   359,   359,   359,   363,   292,   334,   360,   360,
   360,    35,   446,   576,   400,   400,   724,   400,    35,   400,
   576,   576,   576,   400,   400,   576,   576,   576,   715,   576,
   606,   334,    37,    37,   217,   335,   345,   606,   576,   576,
   576,   576,   335,   345,   221,   631,   446,   335,   631,   576,
   576,   335,   576,   576,   576,   576,   576,   253,   361,   361,
   361,   614,   738,   254,   346,   724,   363,   363,   363,   335,
   465,   346,   530,   257,   466,   295,   295,   530,   715,   576,
   576,   576,   576,   576,   576,   576,   576,   576,   576,   576,
   576,   576,   576,   335,   345,   576,   576,   576,   268,   576,
   576,   279,   595,   576,   465,   281,   576,   576,   466,   576,
   299,   576,   282,   576,   283,   576,   576,   576,   576,   576,
   576,   576,   346,   576,   576,   576,   614,   614,   738,   767,
   347,   767,   767,   767,   767,   767,   595,   347,    13,   576,
   576,   576,   576,   577,   576,    13,   576,   288,   576,   576,
   577,   577,   577,   299,    13,   577,   577,   577,   290,   577,
   299,   309,   309,   614,   738,    42,   614,   738,   422,   577,
   577,   577,    42,   614,   738,   319,   319,   656,   656,   577,
   577,    42,   577,   577,   577,   577,   577,   894,   347,   894,
   894,   894,   894,   894,   348,   291,    13,   113,   294,   349,
   422,   348,   113,   113,   422,   422,   349,   767,   296,   577,
   577,   577,   577,   577,   577,   577,   577,   577,   577,   577,
   577,   577,   577,    42,   300,   577,   577,   577,   301,   577,
   577,   934,   934,   577,   304,    73,   577,   577,   307,   577,
   392,   577,   308,   577,    73,   577,   577,   577,   577,   577,
   577,   577,   348,   577,    73,   577,   582,   349,   582,   582,
   582,   582,   582,   313,   314,   894,   316,   325,   212,   577,
   577,   577,   577,    27,   577,   212,   577,   393,   577,   577,
    27,    27,    27,   392,   212,    27,    27,    27,   326,    27,
   392,   677,   329,   582,   331,   377,   677,   423,    27,    27,
    27,   286,   582,   582,   582,   582,   672,   378,   286,    27,
    27,   384,    27,    27,    27,    27,    27,   286,   386,   723,
   393,   723,   723,   723,   350,   723,   212,   393,   351,   423,
   352,   350,   390,   423,   423,   351,   582,   352,   399,    27,
    27,    27,    27,    27,    27,    27,    27,    27,    27,    27,
    27,    27,    27,   412,   424,    27,    27,    27,   672,   286,
    27,   354,    27,    27,   425,   672,    27,    27,   354,    27,
   672,    27,   561,    27,   672,    27,    27,    27,    27,    27,
    27,    27,   350,    27,    27,    27,   351,   287,   352,   426,
   683,   427,   672,   461,   287,   683,   467,   469,    28,    27,
    27,   470,    27,   287,    27,    28,    28,    28,    27,   473,
    28,    28,    28,   475,    28,   561,   672,   480,   484,   354,
   289,   493,   561,   496,    28,    28,   302,   289,     4,     4,
     4,     4,     4,   302,    28,    28,   289,    28,    28,    28,
    28,    28,   302,   508,   866,   287,   866,   866,   866,   647,
   866,   647,   647,   647,   647,   647,   843,   513,   843,   843,
   843,   843,   843,   514,    28,    28,    28,    28,    28,    28,
    28,    28,    28,    28,    28,    28,    28,    28,   289,   545,
    28,    28,    28,   328,   302,    28,   647,    28,    28,   551,
   328,    28,    28,   843,    28,   647,    28,   558,    28,   328,
    28,    28,    28,    28,    28,    28,    28,   562,    28,   565,
    28,   764,   338,   764,   764,   764,   764,   764,   569,   338,
   578,   580,   585,    50,    28,    28,   587,    28,   338,    28,
    50,    50,    50,    28,   599,    50,    50,    50,   601,    50,
   608,   328,   610,   613,   616,   479,   617,   441,   764,    50,
    50,    50,   479,   620,   621,   623,   626,   764,   627,    50,
    50,   479,    50,    50,    50,    50,    50,   634,   635,   636,
   338,   637,   841,   645,   841,   841,   841,   841,   841,   441,
   652,   655,   658,   441,   441,   441,   441,   663,   666,    50,
    50,    50,    50,    50,    50,    50,    50,    50,    50,    50,
    50,    50,    50,   479,   675,    50,    50,    50,   680,   841,
    50,   698,   717,    50,   718,   719,    50,    50,   841,    50,
   721,    50,   722,    50,   726,    50,    50,    50,    50,    50,
    50,    50,   734,    50,   735,    50,   736,   737,   752,   522,
   760,   823,   766,   768,   769,   772,   522,   775,   823,    50,
    50,    50,    50,   394,    50,   522,    50,   823,    50,   784,
   394,   394,   394,   788,   789,   394,   394,   394,   506,   394,
   506,   506,   506,   506,   506,   615,   792,   793,   394,   394,
   394,   805,   615,   806,   812,   813,   814,   615,   816,   394,
   394,   615,   394,   394,   394,   394,   394,   522,   824,   823,
   111,   111,   111,   111,   111,   506,   506,   895,   829,   895,
   895,   895,   895,   895,   506,   506,   506,   506,   830,   394,
   394,   394,   394,   394,   394,   394,   394,   394,   394,   394,
   394,   394,   394,   615,   442,   394,   394,   394,   673,   831,
   394,   834,   394,   394,   895,   673,   394,   394,   835,   394,
   673,   394,   837,   394,   673,   394,   394,   394,   394,   394,
   394,   394,   840,   394,   394,   394,   442,   867,   842,   848,
   442,   442,   442,   442,   867,   849,   854,   859,   403,   394,
   394,   860,   394,   867,   394,   403,   403,   403,   394,   861,
   403,   403,   403,   862,   403,   432,   673,   365,   365,   365,
   365,   365,   864,   403,   403,   403,   403,   868,   875,   880,
   881,   432,   432,   896,   403,   403,   917,   403,   403,   403,
   403,   403,   919,   926,   927,   867,   928,   432,   929,   432,
   930,   432,   432,   432,   432,   932,   935,   432,   936,   432,
   937,   938,   939,   940,   403,   403,   403,   403,   403,   403,
   403,   403,   403,   403,   403,   403,   403,   403,   941,   942,
   403,   403,   403,   951,   960,   403,   961,   962,   403,   nil,
   nil,   403,   403,   nil,   403,   nil,   403,   nil,   403,   nil,
   403,   403,   403,   403,   403,   403,   403,   nil,   403,   403,
   403,   533,   nil,   533,   533,   533,   533,   533,   nil,   nil,
   nil,   nil,   nil,   nil,   403,   403,   403,   403,   404,   403,
   nil,   403,   nil,   403,   nil,   404,   404,   404,   nil,   nil,
   404,   404,   404,   nil,   404,   433,   541,   541,   533,   533,
   541,   541,   541,   404,   404,   404,   404,   533,   533,   533,
   533,   433,   433,   nil,   404,   404,   nil,   404,   404,   404,
   404,   404,   nil,   nil,   nil,   nil,   nil,   433,   nil,   433,
   nil,   433,   433,   433,   433,   nil,   nil,   433,   nil,   433,
   nil,   nil,   nil,   nil,   404,   404,   404,   404,   404,   404,
   404,   404,   404,   404,   404,   404,   404,   404,   nil,   nil,
   404,   404,   404,   nil,   nil,   404,   nil,   nil,   404,   nil,
   nil,   404,   404,   nil,   404,   nil,   404,   nil,   404,   nil,
   404,   404,   404,   404,   404,   404,   404,   nil,   404,   404,
   404,   633,   nil,   633,   633,   633,   633,   633,   nil,   nil,
   nil,   nil,   nil,   nil,   404,   404,   404,   404,   455,   404,
   nil,   404,   nil,   404,   nil,   455,   455,   455,   nil,   nil,
   455,   455,   455,   nil,   455,   nil,   nil,   931,   633,   931,
   931,   931,   931,   931,   455,   nil,   nil,   633,   633,   633,
   633,   nil,   nil,   nil,   455,   455,   nil,   455,   455,   455,
   455,   455,   nil,   933,   nil,   933,   933,   933,   933,   933,
   nil,   nil,   nil,   458,   931,   nil,   nil,   nil,   nil,   nil,
   458,   458,   458,   nil,   nil,   458,   458,   458,   682,   458,
   682,   682,   682,   682,   682,   455,   nil,   nil,   nil,   458,
   933,   nil,   455,   nil,   nil,   nil,   nil,   455,   455,   458,
   458,   nil,   458,   458,   458,   458,   458,   nil,   754,   nil,
   754,   754,   754,   754,   754,   682,   nil,   nil,   nil,   nil,
   455,   nil,   nil,   nil,   682,   682,   682,   682,   nil,   nil,
   nil,   nil,   nil,   428,   455,   nil,   nil,   nil,   nil,   455,
   458,   nil,   nil,   455,   nil,   754,   nil,   458,   nil,   428,
   428,   nil,   458,   458,   754,   754,   754,   754,   759,   nil,
   759,   759,   759,   759,   759,   428,   nil,   428,   nil,   428,
   428,   428,   428,   nil,   nil,   458,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    30,    30,    30,    30,    30,    30,   458,
   nil,   nil,    30,    30,   458,   759,   nil,    30,   458,    30,
    30,    30,    30,    30,    30,    30,   759,   759,   nil,   nil,
   nil,    30,    30,    30,    30,    30,    30,    30,   nil,   950,
    30,   950,   950,   950,   950,   950,    30,    30,    30,    30,
    30,    30,    30,    30,    30,    30,    30,    30,   nil,    30,
    30,    30,   nil,    30,    30,    30,    30,    30,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   803,   950,   803,   803,   803,
   803,   803,   nil,   nil,   nil,   nil,   nil,    30,   nil,   nil,
    30,   nil,   nil,    30,    30,   nil,   nil,    30,   nil,    30,
   nil,   nil,   nil,    30,   886,   nil,   886,   886,   886,   886,
   886,    30,   803,   nil,   nil,   nil,    30,    30,    30,    30,
   nil,    30,    30,   803,   803,   nil,    30,    30,    51,    51,
    51,    51,    51,    51,    30,   nil,    30,    51,    51,   nil,
   nil,   886,    51,   nil,    51,    51,    51,    51,    51,    51,
    51,   nil,   886,   886,   nil,   nil,    51,    51,    51,    51,
    51,    51,    51,   nil,   nil,    51,   nil,   nil,   nil,   nil,
   nil,    51,    51,    51,    51,    51,    51,    51,    51,    51,
    51,    51,    51,   nil,    51,    51,    51,   nil,    51,    51,
    51,    51,    51,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   888,   nil,   888,   888,   888,   888,   888,   nil,   nil,   nil,
   nil,   nil,    51,   nil,   nil,    51,   nil,   nil,    51,    51,
   nil,   nil,    51,   nil,    51,   nil,   nil,   nil,    51,   890,
   nil,   890,   890,   890,   890,   890,    51,   888,   nil,   nil,
   nil,    51,    51,    51,    51,   nil,    51,    51,   888,   888,
   nil,    51,    51,   195,   195,   195,   195,   195,   195,    51,
   nil,    51,   195,   195,   nil,   nil,   890,   195,   nil,   195,
   195,   195,   195,   195,   195,   195,   nil,   890,   890,   nil,
   nil,   195,   195,   195,   195,   195,   195,   195,   nil,   nil,
   195,   nil,   nil,   nil,   nil,   nil,   195,   195,   195,   195,
   195,   195,   195,   195,   195,   195,   195,   195,   nil,   195,
   195,   195,   nil,   195,   195,   195,   195,   195,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   909,   nil,   909,   909,   909,
   909,   909,   nil,   nil,   nil,   nil,   nil,   195,   nil,   nil,
   195,   nil,   nil,   195,   195,   nil,   nil,   195,   nil,   195,
   nil,   nil,   nil,   195,   911,   nil,   911,   911,   911,   911,
   911,   195,   909,   nil,   nil,   nil,   195,   195,   195,   195,
   nil,   195,   195,   909,   909,   nil,   195,   195,   196,   196,
   196,   196,   196,   196,   195,   nil,   195,   196,   196,   nil,
   nil,   911,   196,   nil,   196,   196,   196,   196,   196,   196,
   196,   nil,   911,   911,   nil,   nil,   196,   196,   196,   196,
   196,   196,   196,   nil,   nil,   196,   nil,   nil,   nil,   nil,
   nil,   196,   196,   196,   196,   196,   196,   196,   196,   196,
   196,   196,   196,   nil,   196,   196,   196,   nil,   196,   196,
   196,   196,   196,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   946,   nil,   946,   946,   946,   946,   946,   nil,   nil,   nil,
   nil,   nil,   196,   nil,   nil,   196,   nil,   nil,   196,   196,
   nil,   nil,   196,   nil,   196,   nil,   nil,   nil,   196,   956,
   nil,   956,   956,   956,   956,   956,   196,   946,   nil,   nil,
   nil,   196,   196,   196,   196,   nil,   196,   196,   946,   946,
   nil,   196,   196,   220,   220,   220,   220,   220,   220,   196,
   nil,   196,   220,   220,   nil,   nil,   956,   220,   nil,   220,
   220,   220,   220,   220,   220,   220,   nil,   956,   956,   nil,
   nil,   220,   220,   220,   220,   220,   220,   220,   nil,   nil,
   220,   nil,   nil,   nil,   nil,   nil,   220,   220,   220,   220,
   220,   220,   220,   220,   220,   220,   220,   220,   nil,   220,
   220,   220,   nil,   220,   220,   220,   220,   220,   420,   420,
   420,   420,   420,   420,   420,   420,   420,   420,   420,   nil,
   420,   420,   nil,   nil,   420,   420,   nil,   220,   nil,   nil,
   220,   nil,   nil,   220,   220,   nil,   nil,   220,   nil,   220,
   420,   nil,   420,   220,   420,   420,   420,   420,   420,   420,
   420,   220,   420,   nil,   nil,   nil,   220,   220,   220,   220,
   nil,   220,   220,   nil,   nil,   nil,   220,   220,   280,   280,
   280,   280,   280,   280,   220,   nil,   220,   280,   280,   nil,
   nil,   nil,   280,   nil,   280,   280,   280,   280,   280,   280,
   280,   nil,   nil,   nil,   nil,   nil,   280,   280,   280,   280,
   280,   280,   280,   nil,   nil,   280,   nil,   nil,   nil,   nil,
   nil,   280,   280,   280,   280,   280,   280,   280,   280,   280,
   280,   280,   280,   nil,   280,   280,   280,   nil,   280,   280,
   280,   280,   280,   421,   421,   421,   421,   421,   421,   421,
   421,   421,   421,   421,   nil,   421,   421,   nil,   nil,   421,
   421,   nil,   280,   nil,   nil,   280,   nil,   nil,   280,   280,
   nil,   nil,   280,   nil,   280,   421,   nil,   421,   280,   421,
   421,   421,   421,   421,   421,   421,   280,   421,   nil,   nil,
   nil,   280,   280,   280,   280,   nil,   280,   280,   nil,   nil,
   nil,   280,   280,   285,   285,   285,   285,   285,   285,   280,
   nil,   280,   285,   285,   nil,   nil,   nil,   285,   nil,   285,
   285,   285,   285,   285,   285,   285,   nil,   nil,   nil,   nil,
   nil,   285,   285,   285,   285,   285,   285,   285,   nil,   nil,
   285,   nil,   nil,   nil,   nil,   nil,   285,   285,   285,   285,
   285,   285,   285,   285,   285,   285,   285,   285,   nil,   285,
   285,   285,   nil,   285,   285,   285,   285,   285,   431,   431,
   431,   431,   431,   431,   431,   nil,   nil,   431,   431,   nil,
   nil,   nil,   nil,   nil,   431,   431,   nil,   285,   nil,   nil,
   285,   nil,   nil,   285,   285,   nil,   nil,   285,   nil,   285,
   431,   nil,   431,   285,   431,   431,   431,   431,   431,   431,
   431,   285,   431,   nil,   nil,   nil,   285,   285,   285,   285,
   nil,   285,   285,   nil,   nil,   nil,   285,   285,   507,   507,
   507,   507,   507,   507,   285,   nil,   285,   507,   507,   nil,
   nil,   nil,   507,   nil,   507,   507,   507,   507,   507,   507,
   507,   nil,   nil,   nil,   nil,   nil,   507,   507,   507,   507,
   507,   507,   507,   nil,   nil,   507,   nil,   nil,   nil,   nil,
   nil,   507,   507,   507,   507,   507,   507,   507,   507,   507,
   507,   507,   507,   nil,   507,   507,   507,   nil,   507,   507,
   507,   507,   507,   434,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   434,
   434,   nil,   507,   nil,   nil,   507,   nil,   nil,   507,   507,
   nil,   nil,   507,   nil,   507,   434,   nil,   434,   507,   434,
   434,   434,   434,   nil,   nil,   434,   507,   434,   nil,   nil,
   nil,   507,   507,   507,   507,   nil,   507,   507,   nil,   nil,
   nil,   507,   507,   510,   510,   510,   510,   510,   510,   507,
   nil,   507,   510,   510,   nil,   nil,   nil,   510,   nil,   510,
   510,   510,   510,   510,   510,   510,   nil,   nil,   nil,   nil,
   nil,   510,   510,   510,   510,   510,   510,   510,   nil,   nil,
   510,   nil,   nil,   nil,   nil,   nil,   510,   510,   510,   510,
   510,   510,   510,   510,   510,   510,   510,   510,   nil,   510,
   510,   510,   nil,   510,   510,   510,   510,   510,   435,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   435,   435,   nil,   510,   nil,   nil,
   510,   nil,   nil,   510,   510,   nil,   nil,   510,   nil,   510,
   435,   nil,   435,   510,   435,   435,   435,   435,   nil,   nil,
   435,   510,   435,   nil,   nil,   nil,   510,   510,   510,   510,
   nil,   510,   510,   nil,   nil,   nil,   510,   510,   532,   532,
   532,   532,   532,   532,   510,   nil,   510,   532,   532,   nil,
   nil,   nil,   532,   nil,   532,   532,   532,   532,   532,   532,
   532,   nil,   nil,   nil,   nil,   nil,   532,   532,   532,   532,
   532,   532,   532,   nil,   nil,   532,   nil,   nil,   nil,   nil,
   nil,   532,   532,   532,   532,   532,   532,   532,   532,   532,
   532,   532,   532,   nil,   532,   532,   532,   nil,   532,   532,
   532,   532,   532,   436,   436,   436,   436,   436,   436,   436,
   nil,   nil,   436,   436,   nil,   nil,   nil,   nil,   nil,   436,
   436,   nil,   532,   nil,   nil,   532,   nil,   nil,   532,   532,
   nil,   nil,   532,   nil,   532,   436,   nil,   436,   532,   436,
   436,   436,   436,   436,   436,   436,   532,   436,   nil,   nil,
   nil,   532,   532,   532,   532,   nil,   532,   532,   nil,   nil,
   nil,   532,   532,   584,   584,   584,   584,   584,   584,   532,
   nil,   532,   584,   584,   nil,   nil,   nil,   584,   nil,   584,
   584,   584,   584,   584,   584,   584,   nil,   nil,   nil,   nil,
   nil,   584,   584,   584,   584,   584,   584,   584,   nil,   nil,
   584,   nil,   nil,   nil,   nil,   nil,   584,   584,   584,   584,
   584,   584,   584,   584,   584,   584,   584,   584,   nil,   584,
   584,   584,   nil,   584,   584,   584,   584,   584,   437,   437,
   437,   437,   437,   437,   437,   nil,   nil,   437,   437,   nil,
   nil,   nil,   nil,   nil,   437,   437,   nil,   584,   nil,   nil,
   584,   nil,   nil,   584,   584,   nil,   nil,   584,   nil,   584,
   437,   nil,   437,   584,   437,   437,   437,   437,   437,   437,
   437,   584,   437,   nil,   nil,   nil,   584,   584,   584,   584,
   nil,   584,   584,   nil,   nil,   nil,   584,   584,   603,   603,
   603,   603,   603,   603,   584,   nil,   584,   603,   603,   nil,
   nil,   nil,   603,   nil,   603,   603,   603,   603,   603,   603,
   603,   nil,   nil,   nil,   nil,   nil,   603,   603,   603,   603,
   603,   603,   603,   nil,   nil,   603,   nil,   nil,   nil,   nil,
   nil,   603,   603,   603,   603,   603,   603,   603,   603,   603,
   603,   603,   603,   nil,   603,   603,   603,   nil,   603,   603,
   603,   603,   603,   438,   438,   438,   438,   438,   438,   438,
   nil,   nil,   438,   438,   nil,   nil,   nil,   nil,   nil,   438,
   438,   nil,   603,   nil,   nil,   603,   nil,   nil,   603,   603,
   nil,   nil,   603,   nil,   603,   438,   nil,   438,   603,   438,
   438,   438,   438,   438,   438,   438,   603,   438,   nil,   nil,
   nil,   603,   603,   603,   603,   nil,   603,   603,   nil,   nil,
   nil,   603,   603,   604,   604,   604,   604,   604,   604,   603,
   nil,   603,   604,   604,   nil,   nil,   nil,   604,   nil,   604,
   604,   604,   604,   604,   604,   604,   nil,   nil,   nil,   nil,
   nil,   604,   604,   604,   604,   604,   604,   604,   nil,   nil,
   604,   nil,   nil,   nil,   nil,   nil,   604,   604,   604,   604,
   604,   604,   604,   604,   604,   604,   604,   604,   nil,   604,
   604,   604,   nil,   604,   604,   604,   604,   604,   439,   439,
   439,   439,   439,   439,   439,   nil,   nil,   439,   439,   nil,
   nil,   nil,   nil,   nil,   439,   439,   nil,   604,   nil,   nil,
   604,   nil,   nil,   604,   604,   nil,   nil,   604,   nil,   604,
   439,   nil,   439,   604,   439,   439,   439,   439,   439,   439,
   439,   604,   439,   nil,   nil,   nil,   604,   604,   604,   604,
   nil,   604,   604,   nil,   nil,   nil,   604,   604,   625,   625,
   625,   625,   625,   625,   604,   nil,   604,   625,   625,   nil,
   nil,   nil,   625,   nil,   625,   625,   625,   625,   625,   625,
   625,   nil,   nil,   nil,   nil,   nil,   625,   625,   625,   625,
   625,   625,   625,   nil,   nil,   625,   nil,   nil,   nil,   nil,
   nil,   625,   625,   625,   625,   625,   625,   625,   625,   625,
   625,   625,   625,   nil,   625,   625,   625,   nil,   625,   625,
   625,   625,   625,   440,   440,   440,   440,   440,   440,   440,
   nil,   nil,   440,   440,   nil,   nil,   nil,   nil,   nil,   440,
   440,   nil,   625,   nil,   nil,   625,   nil,   nil,   625,   625,
   nil,   nil,   625,   nil,   625,   440,   nil,   440,   625,   440,
   440,   440,   440,   440,   440,   440,   625,   440,   nil,   nil,
   nil,   625,   625,   625,   625,   nil,   625,   625,   nil,   nil,
   nil,   625,   625,   676,   676,   676,   676,   676,   676,   625,
   nil,   625,   676,   676,   nil,   nil,   nil,   676,   nil,   676,
   676,   676,   676,   676,   676,   676,   nil,   nil,   nil,   nil,
   nil,   676,   676,   676,   676,   676,   676,   676,   nil,   nil,
   676,   nil,   nil,   nil,   nil,   nil,   676,   676,   676,   676,
   676,   676,   676,   676,   676,   676,   676,   676,   nil,   676,
   676,   676,   nil,   676,   676,   676,   676,   676,   443,   443,
   443,   443,   443,   443,   443,   nil,   nil,   443,   443,   nil,
   nil,   nil,   nil,   nil,   443,   443,   nil,   676,   nil,   nil,
   676,   nil,   nil,   676,   676,   nil,   nil,   676,   nil,   676,
   443,   nil,   443,   676,   443,   443,   443,   443,   443,   443,
   443,   676,   443,   nil,   nil,   nil,   676,   676,   676,   676,
   nil,   676,   676,   nil,   nil,   nil,   676,   676,   681,   681,
   681,   681,   681,   681,   676,   nil,   676,   681,   681,   nil,
   nil,   nil,   681,   nil,   681,   681,   681,   681,   681,   681,
   681,   nil,   nil,   nil,   nil,   nil,   681,   681,   681,   681,
   681,   681,   681,   nil,   nil,   681,   nil,   nil,   nil,   nil,
   nil,   681,   681,   681,   681,   681,   681,   681,   681,   681,
   681,   681,   681,   nil,   681,   681,   681,   nil,   681,   681,
   681,   681,   681,   444,   444,   444,   444,   444,   444,   444,
   444,   nil,   444,   444,   nil,   nil,   nil,   nil,   nil,   444,
   444,   nil,   681,   nil,   nil,   681,   nil,   nil,   681,   681,
   nil,   nil,   681,   nil,   681,   444,   nil,   444,   681,   444,
   444,   444,   444,   444,   444,   444,   681,   444,   nil,   nil,
   nil,   681,   681,   681,   681,   nil,   681,   681,   nil,   nil,
   nil,   681,   681,   691,   691,   691,   691,   691,   691,   681,
   nil,   681,   691,   691,   nil,   nil,   nil,   691,   nil,   691,
   691,   691,   691,   691,   691,   691,   nil,   nil,   nil,   nil,
   nil,   691,   691,   691,   691,   691,   691,   691,   nil,   nil,
   691,   nil,   nil,   nil,   nil,   nil,   691,   691,   691,   691,
   691,   691,   691,   691,   691,   691,   691,   691,   nil,   691,
   691,   691,   nil,   691,   691,   691,   691,   691,   429,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   429,   429,   nil,   691,   nil,   nil,
   691,   nil,   nil,   691,   691,   nil,   nil,   691,   nil,   691,
   429,   nil,   429,   691,   429,   429,   429,   429,   nil,   nil,
   nil,   691,   nil,   nil,   nil,   nil,   691,   691,   691,   691,
   nil,   691,   691,   nil,   nil,   nil,   691,   691,   730,   730,
   730,   730,   730,   730,   691,   nil,   691,   730,   730,   nil,
   nil,   nil,   730,   nil,   730,   730,   730,   730,   730,   730,
   730,   nil,   nil,   nil,   nil,   nil,   730,   730,   730,   730,
   730,   730,   730,   nil,   nil,   730,   nil,   nil,   nil,   nil,
   nil,   730,   730,   730,   730,   730,   730,   730,   730,   730,
   730,   730,   730,   nil,   730,   730,   730,   nil,   730,   730,
   730,   730,   730,   430,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   430,
   430,   nil,   730,   nil,   nil,   730,   nil,   nil,   730,   730,
   nil,   nil,   730,   nil,   730,   430,   nil,   nil,   730,   430,
   430,   430,   430,   nil,   nil,   nil,   730,   nil,   nil,   nil,
   nil,   730,   730,   730,   730,   nil,   730,   730,   nil,   nil,
   nil,   730,   730,   742,   742,   742,   742,   742,   742,   730,
   nil,   730,   742,   742,   nil,   nil,   nil,   742,   nil,   742,
   742,   742,   742,   742,   742,   742,   nil,   nil,   nil,   nil,
   nil,   742,   742,   742,   742,   742,   742,   742,   nil,   nil,
   742,   nil,   nil,   nil,   nil,   nil,   742,   742,   742,   742,
   742,   742,   742,   742,   742,   742,   742,   742,   nil,   742,
   742,   742,   nil,   742,   742,   742,   742,   742,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   742,   nil,   nil,
   742,   nil,   nil,   742,   742,   nil,   nil,   742,   nil,   742,
   nil,   nil,   nil,   742,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   742,   nil,   nil,   nil,   nil,   742,   742,   742,   742,
   nil,   742,   742,   nil,   nil,   nil,   742,   742,   776,   776,
   776,   776,   776,   776,   742,   nil,   742,   776,   776,   nil,
   nil,   nil,   776,   nil,   776,   776,   776,   776,   776,   776,
   776,   nil,   nil,   nil,   nil,   nil,   776,   776,   776,   776,
   776,   776,   776,   nil,   nil,   776,   nil,   nil,   nil,   nil,
   nil,   776,   776,   776,   776,   776,   776,   776,   776,   776,
   776,   776,   776,   nil,   776,   776,   776,   nil,   776,   776,
   776,   776,   776,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   776,   nil,   nil,   776,   nil,   nil,   776,   776,
   nil,   nil,   776,   nil,   776,   nil,   nil,   nil,   776,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   776,   nil,   nil,   nil,
   nil,   776,   776,   776,   776,   nil,   776,   776,   nil,   nil,
   nil,   776,   776,   777,   777,   777,   777,   777,   777,   776,
   nil,   776,   777,   777,   nil,   nil,   nil,   777,   nil,   777,
   777,   777,   777,   777,   777,   777,   nil,   nil,   nil,   nil,
   nil,   777,   777,   777,   777,   777,   777,   777,   nil,   nil,
   777,   nil,   nil,   nil,   nil,   nil,   777,   777,   777,   777,
   777,   777,   777,   777,   777,   777,   777,   777,   nil,   777,
   777,   777,   nil,   777,   777,   777,   777,   777,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   777,   nil,   nil,
   777,   nil,   nil,   777,   777,   nil,   nil,   777,   nil,   777,
   nil,   nil,   nil,   777,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   777,   nil,   nil,   nil,   nil,   777,   777,   777,   777,
   nil,   777,   777,   nil,   nil,   nil,   777,   777,   780,   780,
   780,   780,   780,   780,   777,   nil,   777,   780,   780,   nil,
   nil,   nil,   780,   nil,   780,   780,   780,   780,   780,   780,
   780,   nil,   nil,   nil,   nil,   nil,   780,   780,   780,   780,
   780,   780,   780,   nil,   nil,   780,   nil,   nil,   nil,   nil,
   nil,   780,   780,   780,   780,   780,   780,   780,   780,   780,
   780,   780,   780,   nil,   780,   780,   780,   nil,   780,   780,
   780,   780,   780,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   780,   nil,   nil,   780,   nil,   nil,   780,   780,
   nil,   nil,   780,   nil,   780,   nil,   nil,   nil,   780,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   780,   nil,   nil,   nil,
   nil,   780,   780,   780,   780,   nil,   780,   780,   nil,   nil,
   nil,   780,   780,   786,   786,   786,   786,   786,   786,   780,
   nil,   780,   786,   786,   nil,   nil,   nil,   786,   nil,   786,
   786,   786,   786,   786,   786,   786,   nil,   nil,   nil,   nil,
   nil,   786,   786,   786,   786,   786,   786,   786,   nil,   nil,
   786,   nil,   nil,   nil,   nil,   nil,   786,   786,   786,   786,
   786,   786,   786,   786,   786,   786,   786,   786,   nil,   786,
   786,   786,   nil,   786,   786,   786,   786,   786,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   786,   nil,   nil,
   786,   nil,   nil,   786,   786,   nil,   nil,   786,   nil,   786,
   nil,   nil,   nil,   786,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   786,   nil,   nil,   nil,   nil,   786,   786,   786,   786,
   nil,   786,   786,   nil,   nil,   nil,   786,   786,   821,   821,
   821,   821,   821,   821,   786,   nil,   786,   821,   821,   nil,
   nil,   nil,   821,   nil,   821,   821,   821,   821,   821,   821,
   821,   nil,   nil,   nil,   nil,   nil,   821,   821,   821,   821,
   821,   821,   821,   nil,   nil,   821,   nil,   nil,   nil,   nil,
   nil,   821,   821,   821,   821,   821,   821,   821,   821,   821,
   821,   821,   821,   nil,   821,   821,   821,   nil,   821,   821,
   821,   821,   821,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   821,   nil,   nil,   821,   nil,   nil,   821,   821,
   nil,   nil,   821,   nil,   821,   nil,   nil,   nil,   821,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   821,   nil,   nil,   nil,
   nil,   821,   821,   821,   821,   nil,   821,   821,   nil,   nil,
   nil,   821,   821,   827,   827,   827,   827,   827,   827,   821,
   nil,   821,   827,   827,   nil,   nil,   nil,   827,   nil,   827,
   827,   827,   827,   827,   827,   827,   nil,   nil,   nil,   nil,
   nil,   827,   827,   827,   827,   827,   827,   827,   nil,   nil,
   827,   nil,   nil,   nil,   nil,   nil,   827,   827,   827,   827,
   827,   827,   827,   827,   827,   827,   827,   827,   nil,   827,
   827,   827,   nil,   827,   827,   827,   827,   827,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   827,   nil,   nil,
   827,   nil,   nil,   827,   827,   nil,   nil,   827,   nil,   827,
   nil,   nil,   nil,   827,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   827,   nil,   nil,   nil,   nil,   827,   827,   827,   827,
   nil,   827,   827,   nil,   nil,   nil,   827,   827,   828,   828,
   828,   828,   828,   828,   827,   nil,   827,   828,   828,   nil,
   nil,   nil,   828,   nil,   828,   828,   828,   828,   828,   828,
   828,   nil,   nil,   nil,   nil,   nil,   828,   828,   828,   828,
   828,   828,   828,   nil,   nil,   828,   nil,   nil,   nil,   nil,
   nil,   828,   828,   828,   828,   828,   828,   828,   828,   828,
   828,   828,   828,   nil,   828,   828,   828,   nil,   828,   828,
   828,   828,   828,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   828,   nil,   nil,   828,   nil,   nil,   828,   828,
   nil,   nil,   828,   nil,   828,   nil,   nil,   nil,   828,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   828,   nil,   nil,   nil,
   nil,   828,   828,   828,   828,   nil,   828,   828,   nil,   nil,
   nil,   828,   828,   897,   897,   897,   897,   897,   897,   828,
   nil,   828,   897,   897,   nil,   nil,   nil,   897,   nil,   897,
   897,   897,   897,   897,   897,   897,   nil,   nil,   nil,   nil,
   nil,   897,   897,   897,   897,   897,   897,   897,   nil,   nil,
   897,   nil,   nil,   nil,   nil,   nil,   897,   897,   897,   897,
   897,   897,   897,   897,   897,   897,   897,   897,   nil,   897,
   897,   897,   nil,   897,   897,   897,   897,   897,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   897,   nil,   nil,
   897,   nil,   nil,   897,   897,   nil,   nil,   897,   nil,   897,
   nil,   nil,   nil,   897,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   897,   nil,   nil,   nil,   nil,   897,   897,   897,   897,
   nil,   897,   897,   nil,   nil,   nil,   897,   897,   903,   903,
   903,   903,   903,   903,   897,   nil,   897,   903,   903,   nil,
   nil,   nil,   903,   nil,   903,   903,   903,   903,   903,   903,
   903,   nil,   nil,   nil,   nil,   nil,   903,   903,   903,   903,
   903,   903,   903,   nil,   nil,   903,   nil,   nil,   nil,   nil,
   nil,   903,   903,   903,   903,   903,   903,   903,   903,   903,
   903,   903,   903,   nil,   903,   903,   903,   nil,   903,   903,
   903,   903,   903,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   903,   nil,   nil,   903,   nil,   nil,   903,   903,
   nil,   nil,   903,   nil,   903,   nil,   nil,   nil,   903,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   903,   nil,   nil,   nil,
   nil,   903,   903,   903,   903,   nil,   903,   903,   nil,   nil,
   nil,   903,   903,   905,   905,   905,   905,   905,   905,   903,
   nil,   903,   905,   905,   nil,   nil,   nil,   905,   nil,   905,
   905,   905,   905,   905,   905,   905,   nil,   nil,   nil,   nil,
   nil,   905,   905,   905,   905,   905,   905,   905,   nil,   nil,
   905,   nil,   nil,   nil,   nil,   nil,   905,   905,   905,   905,
   905,   905,   905,   905,   905,   905,   905,   905,   nil,   905,
   905,   905,   nil,   905,   905,   905,   905,   905,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   905,   nil,   nil,
   905,   nil,   nil,   905,   905,   nil,   nil,   905,   nil,   905,
   nil,   nil,   nil,   905,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   905,   nil,   nil,   nil,   nil,   905,   905,   905,   905,
   nil,   905,   905,   nil,   nil,   nil,   905,   905,   nil,     5,
     5,     5,     5,     5,   905,   nil,   905,     5,     5,   nil,
   nil,   nil,     5,   nil,     5,     5,     5,     5,     5,     5,
     5,   nil,   nil,   nil,   nil,   nil,     5,     5,     5,     5,
     5,     5,     5,   nil,   nil,     5,   nil,   nil,   nil,   nil,
   nil,     5,     5,     5,     5,     5,     5,     5,     5,     5,
     5,     5,     5,   nil,     5,     5,     5,   nil,     5,     5,
     5,     5,     5,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,     5,   nil,   nil,     5,   nil,   nil,     5,     5,
   nil,   nil,     5,   nil,     5,   nil,   nil,   nil,     5,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,     5,   nil,   nil,   nil,
   nil,     5,     5,     5,     5,   nil,     5,     5,   nil,   nil,
   nil,     5,     5,   nil,    18,    18,    18,   nil,    18,     5,
   nil,     5,    18,    18,   nil,   nil,   nil,    18,   nil,    18,
    18,    18,    18,    18,    18,    18,   nil,   nil,   nil,   nil,
   nil,    18,    18,    18,    18,    18,    18,    18,   nil,   nil,
    18,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,
    18,    18,    18,    18,    18,    18,    18,    18,   nil,    18,
    18,    18,   nil,    18,    18,    18,    18,    18,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,
    18,   nil,   nil,    18,    18,   nil,   nil,    18,   nil,   nil,
   nil,   nil,   nil,    18,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    18,   nil,   nil,   nil,   nil,    18,    18,    18,    18,
   nil,    18,    18,   nil,   nil,   nil,    18,    18,   nil,    22,
    22,    22,   nil,    22,    18,   nil,    18,    22,    22,   nil,
   nil,   nil,    22,   nil,    22,    22,    22,    22,    22,    22,
    22,   nil,   nil,   nil,   nil,   nil,    22,    22,    22,    22,
    22,    22,    22,   nil,   nil,    22,   nil,   nil,   nil,   nil,
   nil,   nil,    22,   nil,   nil,    22,    22,    22,    22,    22,
    22,    22,    22,    22,    22,    22,    22,   nil,    22,    22,
    22,    22,    22,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    22,   nil,   nil,    22,   nil,   nil,    22,    22,
   nil,   nil,    22,   nil,    22,   nil,    22,   nil,    22,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    22,   nil,   nil,   nil,
   nil,    22,    22,    22,    22,   nil,    22,    22,   nil,   nil,
   nil,    22,    22,   nil,    23,    23,    23,   nil,    23,    22,
   nil,    22,    23,    23,   nil,   nil,   nil,    23,   nil,    23,
    23,    23,    23,    23,    23,    23,   nil,   nil,   nil,   nil,
   nil,    23,    23,    23,    23,    23,    23,    23,   nil,   nil,
    23,   nil,   nil,   nil,   nil,   nil,   nil,    23,   nil,   nil,
    23,    23,    23,    23,    23,    23,    23,    23,    23,    23,
    23,    23,   nil,    23,    23,    23,    23,    23,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    23,   nil,   nil,
    23,   nil,   nil,    23,    23,   nil,   nil,    23,   nil,    23,
   nil,    23,   nil,    23,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    23,   nil,   nil,   nil,   nil,    23,    23,    23,    23,
   nil,    23,    23,   nil,   nil,   nil,    23,    23,   nil,    24,
    24,    24,   nil,    24,    23,   nil,    23,    24,    24,   nil,
   nil,   nil,    24,   nil,    24,    24,    24,    24,    24,    24,
    24,   nil,   nil,   nil,   nil,   nil,    24,    24,    24,    24,
    24,    24,    24,   nil,   nil,    24,   nil,   nil,   nil,   nil,
   nil,   nil,    24,   nil,   nil,    24,    24,    24,    24,    24,
    24,    24,    24,    24,    24,    24,    24,   nil,    24,    24,
    24,    24,    24,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    24,   nil,   nil,    24,   nil,   nil,    24,    24,
   nil,   nil,    24,   nil,    24,   nil,    24,   nil,    24,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    24,   nil,   nil,   nil,
   nil,    24,    24,    24,    24,   nil,    24,    24,   nil,   nil,
   nil,    24,    24,   nil,    31,    31,    31,   nil,    31,    24,
   nil,    24,    31,    31,   nil,   nil,   nil,    31,   nil,    31,
    31,    31,    31,    31,    31,    31,   nil,   nil,   nil,   nil,
   nil,    31,    31,    31,    31,    31,    31,    31,   nil,   nil,
    31,   nil,   nil,   nil,   nil,   nil,   nil,    31,   nil,   nil,
    31,    31,    31,    31,    31,    31,    31,    31,   nil,    31,
    31,    31,   nil,    31,    31,   nil,   756,    31,   756,   756,
   756,   756,   756,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    31,   nil,   nil,
    31,   nil,   nil,    31,    31,   nil,   nil,    31,   nil,    31,
   nil,   nil,   nil,   756,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   756,   756,   756,   756,    31,    31,    31,    31,
   nil,    31,    31,   nil,   nil,   nil,    31,    31,   nil,    32,
    32,    32,   nil,    32,    31,   nil,    31,    32,    32,   nil,
   nil,   nil,    32,   nil,    32,    32,    32,    32,    32,    32,
    32,   nil,   nil,   nil,   nil,   nil,    32,    32,    32,    32,
    32,    32,    32,   nil,   nil,    32,   nil,   nil,   nil,   nil,
   nil,   381,    32,   nil,   nil,    32,    32,    32,    32,    32,
    32,    32,    32,   nil,    32,    32,    32,   nil,    32,    32,
   nil,   nil,    32,   nil,   381,   381,   381,   381,   381,   381,
   381,   381,   381,   381,   381,   nil,   381,   381,   nil,   nil,
   381,   381,    32,   nil,   nil,    32,   nil,   nil,    32,    32,
   nil,   nil,    32,   nil,   nil,   nil,   381,   nil,   381,   nil,
   381,   381,   381,   381,   381,   381,   381,   nil,   381,   nil,
   nil,    32,    32,    32,    32,   nil,    32,    32,   nil,   nil,
   nil,    32,    32,   nil,   nil,   381,    32,   381,   nil,    32,
   nil,    32,    38,    38,    38,   nil,    38,   nil,   nil,   nil,
    38,    38,   nil,   nil,   nil,    38,   nil,    38,    38,    38,
    38,    38,    38,    38,   nil,   nil,   nil,   nil,   nil,    38,
    38,    38,    38,    38,    38,    38,   nil,   nil,    38,   nil,
   nil,   nil,   nil,   nil,   nil,    38,   nil,   nil,    38,    38,
    38,    38,    38,    38,    38,    38,   nil,    38,    38,    38,
   nil,    38,    38,    38,    38,    38,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    38,   nil,   nil,    38,   nil,
   nil,    38,    38,   nil,   nil,    38,   nil,   nil,   nil,   nil,
   nil,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    38,
   nil,   nil,   nil,   nil,    38,    38,    38,    38,   nil,    38,
    38,   nil,   nil,   nil,    38,    38,   nil,    39,    39,    39,
   nil,    39,    38,   nil,    38,    39,    39,   nil,   nil,   nil,
    39,   nil,    39,    39,    39,    39,    39,    39,    39,   nil,
   nil,   nil,   nil,   nil,    39,    39,    39,    39,    39,    39,
    39,   nil,   nil,    39,   nil,   nil,   nil,   nil,   nil,   nil,
    39,   nil,   nil,    39,    39,    39,    39,    39,    39,    39,
    39,   nil,    39,    39,    39,   nil,    39,    39,    39,    39,
    39,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    39,   nil,   nil,    39,   nil,   nil,    39,    39,   nil,   nil,
    39,   nil,   nil,   nil,   nil,   nil,    39,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    39,   nil,   nil,   nil,   nil,    39,
    39,    39,    39,   nil,    39,    39,   nil,   nil,   nil,    39,
    39,   nil,    40,    40,    40,   nil,    40,    39,   nil,    39,
    40,    40,   nil,   nil,   nil,    40,   nil,    40,    40,    40,
    40,    40,    40,    40,   nil,   nil,   nil,   nil,   nil,    40,
    40,    40,    40,    40,    40,    40,   nil,   nil,    40,   nil,
   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,    40,    40,
    40,    40,    40,    40,    40,    40,   nil,    40,    40,    40,
   nil,    40,    40,    40,    40,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,    40,   nil,
   nil,    40,    40,   nil,   nil,    40,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,    40,    40,    40,    40,   nil,    40,
    40,   nil,   nil,   nil,    40,    40,   nil,    52,    52,    52,
   nil,    52,    40,   nil,    40,    52,    52,   nil,   nil,   nil,
    52,   nil,    52,    52,    52,    52,    52,    52,    52,   nil,
   nil,   nil,   nil,   nil,    52,    52,    52,    52,    52,    52,
    52,   nil,   nil,    52,   nil,   nil,   nil,   nil,   nil,   nil,
    52,   nil,   nil,    52,    52,    52,    52,    52,    52,    52,
    52,   nil,    52,    52,    52,   nil,    52,    52,    52,    52,
    52,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    52,   nil,   nil,    52,   nil,   nil,    52,    52,   nil,   nil,
    52,   nil,   nil,   nil,   nil,   nil,    52,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    52,   nil,   nil,   nil,   nil,    52,
    52,    52,    52,   nil,    52,    52,   nil,   nil,   nil,    52,
    52,   nil,    53,    53,    53,   nil,    53,    52,   nil,    52,
    53,    53,   nil,   nil,   nil,    53,   nil,    53,    53,    53,
    53,    53,    53,    53,   nil,   nil,   nil,   nil,   nil,    53,
    53,    53,    53,    53,    53,    53,   nil,   nil,    53,   nil,
   nil,   nil,   nil,   nil,   nil,    53,   nil,   nil,    53,    53,
    53,    53,    53,    53,    53,    53,    53,    53,    53,    53,
   nil,    53,    53,    53,    53,    53,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    53,   nil,   nil,    53,   nil,
   nil,    53,    53,   nil,   nil,    53,   nil,    53,   nil,   nil,
   nil,    53,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    53,
   nil,   nil,   nil,   nil,    53,    53,    53,    53,   nil,    53,
    53,   nil,   nil,   nil,    53,    53,   nil,    54,    54,    54,
   nil,    54,    53,   nil,    53,    54,    54,   nil,   nil,   nil,
    54,   nil,    54,    54,    54,    54,    54,    54,    54,   nil,
   nil,   nil,   nil,   nil,    54,    54,    54,    54,    54,    54,
    54,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,   nil,
    54,   nil,   nil,    54,    54,    54,    54,    54,    54,    54,
    54,    54,    54,    54,    54,   nil,    54,    54,    54,    54,
    54,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    54,   nil,   nil,    54,   nil,   nil,    54,    54,   nil,   nil,
    54,   nil,   nil,   nil,   nil,   nil,    54,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    54,   nil,   nil,   nil,   nil,    54,
    54,    54,    54,   nil,    54,    54,   nil,   nil,   nil,    54,
    54,   nil,    57,    57,    57,   nil,    57,    54,   nil,    54,
    57,    57,   nil,   nil,   nil,    57,   nil,    57,    57,    57,
    57,    57,    57,    57,   nil,   nil,   nil,   nil,   nil,    57,
    57,    57,    57,    57,    57,    57,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,   nil,    57,   nil,   nil,    57,    57,
    57,    57,    57,    57,    57,    57,   nil,    57,    57,    57,
   nil,    57,    57,    57,    57,    57,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    57,   nil,   nil,    57,   nil,
   nil,    57,    57,   nil,   nil,    57,   nil,   nil,   nil,   nil,
   nil,    57,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    57,
   nil,   nil,   nil,   nil,    57,    57,    57,    57,   nil,    57,
    57,   nil,   nil,   nil,    57,    57,   nil,    58,    58,    58,
   nil,    58,    57,   nil,    57,    58,    58,   nil,   nil,   nil,
    58,   nil,    58,    58,    58,    58,    58,    58,    58,   nil,
   nil,   nil,   nil,   nil,    58,    58,    58,    58,    58,    58,
    58,   nil,   nil,    58,   nil,   nil,   nil,   nil,   nil,   nil,
    58,   nil,   nil,    58,    58,    58,    58,    58,    58,    58,
    58,   nil,    58,    58,    58,   nil,    58,    58,    58,    58,
    58,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    58,   nil,   nil,    58,   nil,   nil,    58,    58,   nil,   nil,
    58,   nil,   nil,   nil,   nil,   nil,    58,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    58,   nil,   nil,   nil,   nil,    58,
    58,    58,    58,   nil,    58,    58,   nil,   nil,   nil,    58,
    58,   nil,    61,    61,    61,   nil,    61,    58,   nil,    58,
    61,    61,   nil,   nil,   nil,    61,   nil,    61,    61,    61,
    61,    61,    61,    61,   nil,   nil,   nil,   nil,   nil,    61,
    61,    61,    61,    61,    61,    61,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   389,   nil,    61,   nil,   nil,    61,    61,
    61,    61,    61,    61,    61,    61,   nil,    61,    61,    61,
   nil,    61,    61,    61,    61,    61,   389,   389,   389,   389,
   389,   389,   389,   389,   389,   389,   389,   nil,   389,   389,
   nil,   nil,   389,   389,   nil,    61,   nil,   nil,    61,   nil,
   nil,    61,    61,   nil,   nil,    61,   nil,   nil,   389,   nil,
   389,    61,   389,   389,   389,   389,   389,   389,   389,    61,
   389,   nil,   nil,   nil,    61,    61,    61,    61,   nil,    61,
    61,   nil,   nil,   nil,    61,    61,    61,   389,   nil,   nil,
   nil,    61,    61,   nil,    61,    62,    62,    62,   nil,    62,
   nil,   nil,   nil,    62,    62,   nil,   nil,   nil,    62,   nil,
    62,    62,    62,    62,    62,    62,    62,   nil,   nil,   nil,
   nil,   nil,    62,    62,    62,    62,    62,    62,    62,   nil,
   nil,    62,   nil,   nil,   nil,   nil,   nil,   nil,    62,   nil,
   nil,    62,    62,    62,    62,    62,    62,    62,    62,   nil,
    62,    62,    62,   nil,    62,    62,   nil,   798,    62,   798,
   798,   798,   798,   798,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    62,   nil,
   nil,    62,   nil,   nil,    62,    62,   nil,   nil,    62,   nil,
    62,   nil,   nil,   nil,   798,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   798,   798,   798,   798,    62,    62,    62,
    62,   nil,    62,    62,   nil,   nil,   nil,    62,    62,   nil,
    63,    63,    63,   nil,    63,    62,   nil,    62,    63,    63,
   nil,   nil,   nil,    63,   nil,    63,    63,    63,    63,    63,
    63,    63,   nil,   nil,   nil,   nil,   nil,    63,    63,    63,
    63,    63,    63,    63,   nil,   nil,    63,   nil,   nil,   nil,
   nil,   nil,   nil,    63,   nil,   nil,    63,    63,    63,    63,
    63,    63,    63,    63,   nil,    63,    63,    63,   nil,    63,
    63,   nil,   800,    63,   800,   800,   800,   800,   800,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    63,   nil,   nil,    63,   nil,   nil,    63,   nil,   nil,    63,
    63,   nil,   nil,    63,   nil,   nil,   nil,   nil,   nil,   800,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   800,   800,
   800,   800,    63,    63,    63,    63,   nil,    63,    63,   nil,
   nil,   nil,    63,    63,   nil,    64,    64,    64,   nil,    64,
    63,   nil,    63,    64,    64,   nil,   nil,   nil,    64,   nil,
    64,    64,    64,    64,    64,    64,    64,   nil,   nil,   nil,
   nil,   nil,    64,    64,    64,    64,    64,    64,    64,   nil,
   nil,    64,   nil,   nil,   nil,   nil,   nil,   nil,    64,   nil,
   nil,    64,    64,    64,    64,    64,    64,    64,    64,   nil,
    64,    64,    64,   nil,    64,    64,   nil,   857,    64,   857,
   857,   857,   857,   857,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    64,   nil,
   nil,    64,   nil,   nil,    64,    64,   nil,   nil,    64,   nil,
   nil,   nil,   nil,   nil,   857,   857,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   857,   857,   857,   857,    64,    64,    64,
    64,   nil,    64,    64,   nil,   nil,   nil,    64,    64,   nil,
   102,   102,   102,   102,   102,    64,   nil,    64,   102,   102,
   nil,   nil,   nil,   102,   nil,   102,   102,   102,   102,   102,
   102,   102,   nil,   nil,   nil,   nil,   nil,   102,   102,   102,
   102,   102,   102,   102,   nil,   nil,   102,   nil,   nil,   nil,
   nil,   579,   102,   102,   102,   102,   102,   102,   102,   102,
   102,   102,   102,   102,   nil,   102,   102,   102,   nil,   102,
   102,   102,   102,   102,   579,   579,   579,   579,   579,   579,
   579,   579,   579,   579,   579,   nil,   579,   579,   nil,   nil,
   579,   579,   nil,   102,   nil,   nil,   102,   nil,   nil,   102,
   102,   nil,   nil,   102,   nil,   102,   579,   nil,   579,   102,
   579,   579,   579,   579,   579,   579,   579,   102,   579,   nil,
   nil,   nil,   102,   102,   102,   102,   nil,   102,   102,   nil,
   nil,   nil,   102,   102,   nil,   579,   nil,   nil,   nil,   102,
   102,   nil,   102,   106,   106,   106,   nil,   106,   nil,   nil,
   nil,   106,   106,   nil,   nil,   nil,   106,   nil,   106,   106,
   106,   106,   106,   106,   106,   nil,   nil,   nil,   nil,   nil,
   106,   106,   106,   106,   106,   106,   106,   nil,   nil,   106,
   nil,   nil,   nil,   nil,   nil,   nil,   106,   nil,   nil,   106,
   106,   106,   106,   106,   106,   106,   106,   nil,   106,   106,
   106,   nil,   106,   106,   106,   106,   106,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   106,   nil,   nil,   106,
   nil,   nil,   106,   106,   nil,   nil,   106,   nil,   nil,   nil,
   nil,   nil,   106,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   106,   nil,   nil,   nil,   nil,   106,   106,   106,   106,   nil,
   106,   106,   nil,   nil,   nil,   106,   106,   nil,   107,   107,
   107,   nil,   107,   106,   nil,   106,   107,   107,   nil,   nil,
   nil,   107,   nil,   107,   107,   107,   107,   107,   107,   107,
   nil,   nil,   nil,   nil,   nil,   107,   107,   107,   107,   107,
   107,   107,   nil,   nil,   107,   nil,   nil,   nil,   nil,   nil,
   nil,   107,   nil,   nil,   107,   107,   107,   107,   107,   107,
   107,   107,   nil,   107,   107,   107,   nil,   107,   107,   107,
   107,   107,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   107,   nil,   nil,   107,   nil,   nil,   107,   107,   nil,
   nil,   107,   nil,   nil,   nil,   nil,   nil,   107,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   107,   nil,   nil,   nil,   nil,
   107,   107,   107,   107,   nil,   107,   107,   nil,   nil,   nil,
   107,   107,   nil,   108,   108,   108,   nil,   108,   107,   nil,
   107,   108,   108,   nil,   nil,   nil,   108,   nil,   108,   108,
   108,   108,   108,   108,   108,   nil,   nil,   nil,   nil,   nil,
   108,   108,   108,   108,   108,   108,   108,   nil,   nil,   108,
   nil,   nil,   nil,   nil,   nil,   nil,   108,   nil,   nil,   108,
   108,   108,   108,   108,   108,   108,   108,   nil,   108,   108,
   108,   nil,   108,   108,   108,   108,   108,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   108,   nil,   nil,   108,
   nil,   nil,   108,   108,   nil,   nil,   108,   nil,   nil,   nil,
   nil,   nil,   108,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   108,   nil,   nil,   nil,   nil,   108,   108,   108,   108,   nil,
   108,   108,   nil,   nil,   nil,   108,   108,   nil,   109,   109,
   109,   nil,   109,   108,   nil,   108,   109,   109,   nil,   nil,
   nil,   109,   nil,   109,   109,   109,   109,   109,   109,   109,
   nil,   nil,   nil,   nil,   nil,   109,   109,   109,   109,   109,
   109,   109,   nil,   nil,   109,   nil,   nil,   nil,   nil,   nil,
   nil,   109,   nil,   nil,   109,   109,   109,   109,   109,   109,
   109,   109,   nil,   109,   109,   109,   nil,   109,   109,   109,
   109,   109,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   109,   nil,   nil,   109,   nil,   nil,   109,   109,   nil,
   nil,   109,   nil,   nil,   nil,   nil,   nil,   109,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   109,   nil,   nil,   nil,   nil,
   109,   109,   109,   109,   nil,   109,   109,   nil,   nil,   nil,
   109,   109,   nil,   110,   110,   110,   110,   110,   109,   nil,
   109,   110,   110,   nil,   nil,   nil,   110,   nil,   110,   110,
   110,   110,   110,   110,   110,   nil,   nil,   nil,   nil,   nil,
   110,   110,   110,   110,   110,   110,   110,   nil,   nil,   110,
   nil,   nil,   nil,   nil,   nil,   110,   110,   110,   110,   110,
   110,   110,   110,   110,   110,   110,   110,   nil,   110,   110,
   110,   nil,   110,   110,   110,   110,   110,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   110,   nil,   nil,   110,
   nil,   nil,   110,   110,   nil,   nil,   110,   nil,   110,   nil,
   nil,   nil,   110,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   110,   nil,   nil,   nil,   nil,   110,   110,   110,   110,   nil,
   110,   110,   nil,   nil,   nil,   110,   110,   nil,   197,   197,
   197,   nil,   197,   110,   nil,   110,   197,   197,   nil,   nil,
   nil,   197,   nil,   197,   197,   197,   197,   197,   197,   197,
   nil,   nil,   nil,   nil,   nil,   197,   197,   197,   197,   197,
   197,   197,   nil,   nil,   197,   nil,   nil,   nil,   nil,   nil,
   nil,   197,   nil,   nil,   197,   197,   197,   197,   197,   197,
   197,   197,   nil,   197,   197,   197,   nil,   197,   197,   197,
   197,   197,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   197,   nil,   nil,   197,   nil,   nil,   197,   197,   nil,
   nil,   197,   nil,   197,   nil,   nil,   nil,   197,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   197,   nil,   nil,   nil,   nil,
   197,   197,   197,   197,   nil,   197,   197,   nil,   nil,   nil,
   197,   197,   nil,   198,   198,   198,   nil,   198,   197,   nil,
   197,   198,   198,   nil,   nil,   nil,   198,   nil,   198,   198,
   198,   198,   198,   198,   198,   nil,   nil,   nil,   nil,   nil,
   198,   198,   198,   198,   198,   198,   198,   nil,   nil,   198,
   nil,   nil,   nil,   nil,   nil,   nil,   198,   nil,   nil,   198,
   198,   198,   198,   198,   198,   198,   198,   nil,   198,   198,
   198,   nil,   198,   198,   198,   198,   198,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   198,   nil,   nil,   198,
   nil,   nil,   198,   198,   nil,   nil,   198,   nil,   198,   nil,
   nil,   nil,   198,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   198,   nil,   nil,   nil,   nil,   198,   198,   198,   198,   nil,
   198,   198,   nil,   nil,   nil,   198,   198,   nil,   199,   199,
   199,   nil,   199,   198,   nil,   198,   199,   199,   nil,   nil,
   nil,   199,   nil,   199,   199,   199,   199,   199,   199,   199,
   nil,   nil,   nil,   nil,   nil,   199,   199,   199,   199,   199,
   199,   199,   nil,   nil,   199,   nil,   nil,   nil,   nil,   nil,
   nil,   199,   nil,   nil,   199,   199,   199,   199,   199,   199,
   199,   199,   nil,   199,   199,   199,   nil,   199,   199,   199,
   199,   199,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   199,   nil,   nil,   199,   nil,   nil,   199,   199,   nil,
   nil,   199,   nil,   nil,   nil,   nil,   nil,   199,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   199,   nil,   nil,   nil,   nil,
   199,   199,   199,   199,   nil,   199,   199,   nil,   nil,   nil,
   199,   199,   nil,   200,   200,   200,   nil,   200,   199,   nil,
   199,   200,   200,   nil,   nil,   nil,   200,   nil,   200,   200,
   200,   200,   200,   200,   200,   nil,   nil,   nil,   nil,   nil,
   200,   200,   200,   200,   200,   200,   200,   nil,   nil,   200,
   nil,   nil,   nil,   nil,   nil,   nil,   200,   nil,   nil,   200,
   200,   200,   200,   200,   200,   200,   200,   200,   200,   200,
   200,   nil,   200,   200,   200,   200,   200,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   200,   nil,   nil,   200,
   nil,   nil,   200,   200,   nil,   nil,   200,   nil,   200,   nil,
   200,   nil,   200,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   200,   nil,   nil,   nil,   nil,   200,   200,   200,   200,   nil,
   200,   200,   nil,   nil,   nil,   200,   200,   nil,   205,   205,
   205,   nil,   205,   200,   nil,   200,   205,   205,   nil,   nil,
   nil,   205,   nil,   205,   205,   205,   205,   205,   205,   205,
   nil,   nil,   nil,   nil,   nil,   205,   205,   205,   205,   205,
   205,   205,   nil,   nil,   205,   nil,   nil,   nil,   nil,   nil,
   nil,   205,   nil,   nil,   205,   205,   205,   205,   205,   205,
   205,   205,   nil,   205,   205,   205,   nil,   205,   205,   205,
   205,   205,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   205,   nil,   nil,   205,   nil,   nil,   205,   205,   nil,
   nil,   205,   nil,   nil,   nil,   nil,   nil,   205,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   205,   nil,   nil,   nil,   nil,
   205,   205,   205,   205,   nil,   205,   205,   nil,   nil,   nil,
   205,   205,   nil,   206,   206,   206,   nil,   206,   205,   nil,
   205,   206,   206,   nil,   nil,   nil,   206,   nil,   206,   206,
   206,   206,   206,   206,   206,   nil,   nil,   nil,   nil,   nil,
   206,   206,   206,   206,   206,   206,   206,   nil,   nil,   206,
   nil,   nil,   nil,   nil,   nil,   nil,   206,   nil,   nil,   206,
   206,   206,   206,   206,   206,   206,   206,   nil,   206,   206,
   206,   nil,   206,   206,   206,   206,   206,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   206,   nil,   nil,   206,
   nil,   nil,   206,   206,   nil,   nil,   206,   nil,   nil,   nil,
   nil,   nil,   206,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   206,   nil,   nil,   nil,   nil,   206,   206,   206,   206,   nil,
   206,   206,   nil,   nil,   nil,   206,   206,   nil,   207,   207,
   207,   nil,   207,   206,   nil,   206,   207,   207,   nil,   nil,
   nil,   207,   nil,   207,   207,   207,   207,   207,   207,   207,
   nil,   nil,   nil,   nil,   nil,   207,   207,   207,   207,   207,
   207,   207,   nil,   nil,   207,   nil,   nil,   nil,   nil,   nil,
   nil,   207,   nil,   nil,   207,   207,   207,   207,   207,   207,
   207,   207,   nil,   207,   207,   207,   nil,   207,   207,   207,
   207,   207,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   207,   nil,   nil,   207,   nil,   nil,   207,   207,   nil,
   nil,   207,   nil,   nil,   nil,   nil,   nil,   207,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   207,   nil,   nil,   nil,   nil,
   207,   207,   207,   207,   nil,   207,   207,   nil,   nil,   nil,
   207,   207,   nil,   208,   208,   208,   nil,   208,   207,   nil,
   207,   208,   208,   nil,   nil,   nil,   208,   nil,   208,   208,
   208,   208,   208,   208,   208,   nil,   nil,   nil,   nil,   nil,
   208,   208,   208,   208,   208,   208,   208,   nil,   nil,   208,
   nil,   nil,   nil,   nil,   nil,   nil,   208,   nil,   nil,   208,
   208,   208,   208,   208,   208,   208,   208,   nil,   208,   208,
   208,   nil,   208,   208,   208,   208,   208,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   208,   nil,   nil,   208,
   nil,   nil,   208,   208,   nil,   nil,   208,   nil,   nil,   nil,
   nil,   nil,   208,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   208,   nil,   nil,   nil,   nil,   208,   208,   208,   208,   nil,
   208,   208,   nil,   nil,   nil,   208,   208,   nil,   209,   209,
   209,   nil,   209,   208,   nil,   208,   209,   209,   nil,   nil,
   nil,   209,   nil,   209,   209,   209,   209,   209,   209,   209,
   nil,   nil,   nil,   nil,   nil,   209,   209,   209,   209,   209,
   209,   209,   nil,   nil,   209,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   209,   209,   209,   209,   209,   209,
   209,   209,   nil,   209,   209,   209,   nil,   209,   209,   209,
   209,   209,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   209,   nil,   nil,   209,   209,   nil,
   nil,   209,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,   nil,   nil,
   209,   209,   209,   209,   nil,   209,   209,   nil,   nil,   nil,
   209,   209,   209,   219,   219,   219,   nil,   219,   209,   nil,
   209,   219,   219,   nil,   nil,   nil,   219,   nil,   219,   219,
   219,   219,   219,   219,   219,   nil,   nil,   nil,   nil,   nil,
   219,   219,   219,   219,   219,   219,   219,   nil,   nil,   219,
   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   219,
   219,   219,   219,   219,   219,   219,   219,   nil,   219,   219,
   219,   nil,   219,   219,   219,   219,   219,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   219,
   nil,   nil,   219,   219,   nil,   nil,   219,   nil,   nil,   nil,
   nil,   nil,   219,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   219,   nil,   nil,   nil,   nil,   219,   219,   219,   219,   nil,
   219,   219,   nil,   nil,   nil,   219,   219,   nil,   222,   222,
   222,   nil,   222,   219,   nil,   219,   222,   222,   nil,   nil,
   nil,   222,   nil,   222,   222,   222,   222,   222,   222,   222,
   nil,   nil,   nil,   nil,   nil,   222,   222,   222,   222,   222,
   222,   222,   nil,   nil,   222,   nil,   nil,   nil,   nil,   nil,
   nil,   222,   nil,   nil,   222,   222,   222,   222,   222,   222,
   222,   222,   nil,   222,   222,   222,   nil,   222,   222,   222,
   222,   222,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   222,   nil,   nil,   222,   nil,   nil,   222,   222,   nil,
   nil,   222,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   nil,   nil,
   222,   222,   222,   222,   nil,   222,   222,   nil,   nil,   nil,
   222,   222,   nil,   223,   223,   223,   nil,   223,   222,   nil,
   222,   223,   223,   nil,   nil,   nil,   223,   nil,   223,   223,
   223,   223,   223,   223,   223,   nil,   nil,   nil,   nil,   nil,
   223,   223,   223,   223,   223,   223,   223,   nil,   nil,   223,
   nil,   nil,   nil,   nil,   nil,   nil,   223,   nil,   nil,   223,
   223,   223,   223,   223,   223,   223,   223,   nil,   223,   223,
   223,   nil,   223,   223,   223,   223,   223,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   223,   nil,   nil,   223,
   nil,   nil,   223,   223,   nil,   nil,   223,   nil,   nil,   nil,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   223,   nil,   nil,   nil,   nil,   223,   223,   223,   223,   nil,
   223,   223,   nil,   nil,   nil,   223,   223,   nil,   224,   224,
   224,   nil,   224,   223,   nil,   223,   224,   224,   nil,   nil,
   nil,   224,   nil,   224,   224,   224,   224,   224,   224,   224,
   nil,   nil,   nil,   nil,   nil,   224,   224,   224,   224,   224,
   224,   224,   nil,   nil,   224,   nil,   nil,   nil,   nil,   nil,
   nil,   224,   nil,   nil,   224,   224,   224,   224,   224,   224,
   224,   224,   nil,   224,   224,   224,   nil,   224,   224,   224,
   224,   224,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   224,   nil,   nil,   224,   nil,   nil,   224,   224,   nil,
   nil,   224,   nil,   nil,   nil,   nil,   nil,   224,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   224,   nil,   nil,   nil,   nil,
   224,   224,   224,   224,   nil,   224,   224,   nil,   nil,   nil,
   224,   224,   nil,   225,   225,   225,   nil,   225,   224,   nil,
   224,   225,   225,   nil,   nil,   nil,   225,   nil,   225,   225,
   225,   225,   225,   225,   225,   nil,   nil,   nil,   nil,   nil,
   225,   225,   225,   225,   225,   225,   225,   nil,   nil,   225,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   225,
   225,   225,   225,   225,   225,   225,   225,   nil,   225,   225,
   225,   nil,   225,   225,   225,   225,   225,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   225,
   nil,   nil,   225,   225,   nil,   nil,   225,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   225,   nil,   nil,   nil,   nil,   225,   225,   225,   225,   nil,
   225,   225,   nil,   nil,   nil,   225,   225,   nil,   226,   226,
   226,   nil,   226,   225,   nil,   225,   226,   226,   nil,   nil,
   nil,   226,   nil,   226,   226,   226,   226,   226,   226,   226,
   nil,   nil,   nil,   nil,   nil,   226,   226,   226,   226,   226,
   226,   226,   nil,   nil,   226,   nil,   nil,   nil,   nil,   nil,
   nil,   226,   nil,   nil,   226,   226,   226,   226,   226,   226,
   226,   226,   nil,   226,   226,   226,   nil,   226,   226,   226,
   226,   226,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   226,   nil,   nil,   226,   nil,   nil,   226,   226,   nil,
   nil,   226,   nil,   nil,   nil,   nil,   nil,   226,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   226,   nil,   nil,   nil,   nil,
   226,   226,   226,   226,   nil,   226,   226,   nil,   nil,   nil,
   226,   226,   nil,   227,   227,   227,   nil,   227,   226,   nil,
   226,   227,   227,   nil,   nil,   nil,   227,   nil,   227,   227,
   227,   227,   227,   227,   227,   nil,   nil,   nil,   nil,   nil,
   227,   227,   227,   227,   227,   227,   227,   nil,   nil,   227,
   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   227,
   227,   227,   227,   227,   227,   227,   227,   nil,   227,   227,
   227,   nil,   227,   227,   227,   227,   227,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   227,
   nil,   nil,   227,   227,   nil,   nil,   227,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   227,   nil,   nil,   nil,   nil,   227,   227,   227,   227,   nil,
   227,   227,   nil,   nil,   nil,   227,   227,   nil,   228,   228,
   228,   nil,   228,   227,   nil,   227,   228,   228,   nil,   nil,
   nil,   228,   nil,   228,   228,   228,   228,   228,   228,   228,
   nil,   nil,   nil,   nil,   nil,   228,   228,   228,   228,   228,
   228,   228,   nil,   nil,   228,   nil,   nil,   nil,   nil,   nil,
   nil,   228,   nil,   nil,   228,   228,   228,   228,   228,   228,
   228,   228,   nil,   228,   228,   228,   nil,   228,   228,   228,
   228,   228,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   228,   nil,   nil,   228,   nil,   nil,   228,   228,   nil,
   nil,   228,   nil,   nil,   nil,   nil,   nil,   228,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   228,   nil,   nil,   nil,   nil,
   228,   228,   228,   228,   nil,   228,   228,   nil,   nil,   nil,
   228,   228,   nil,   229,   229,   229,   nil,   229,   228,   nil,
   228,   229,   229,   nil,   nil,   nil,   229,   nil,   229,   229,
   229,   229,   229,   229,   229,   nil,   nil,   nil,   nil,   nil,
   229,   229,   229,   229,   229,   229,   229,   nil,   nil,   229,
   nil,   nil,   nil,   nil,   nil,   nil,   229,   nil,   nil,   229,
   229,   229,   229,   229,   229,   229,   229,   nil,   229,   229,
   229,   nil,   229,   229,   229,   229,   229,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   229,   nil,   nil,   229,
   nil,   nil,   229,   229,   nil,   nil,   229,   nil,   nil,   nil,
   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   229,   nil,   nil,   nil,   nil,   229,   229,   229,   229,   nil,
   229,   229,   nil,   nil,   nil,   229,   229,   nil,   230,   230,
   230,   nil,   230,   229,   nil,   229,   230,   230,   nil,   nil,
   nil,   230,   nil,   230,   230,   230,   230,   230,   230,   230,
   nil,   nil,   nil,   nil,   nil,   230,   230,   230,   230,   230,
   230,   230,   nil,   nil,   230,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   230,   230,   230,   230,   230,   230,
   230,   230,   nil,   230,   230,   230,   nil,   230,   230,   230,
   230,   230,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   230,   nil,   nil,   230,   230,   nil,
   nil,   230,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,
   230,   230,   230,   230,   nil,   230,   230,   nil,   nil,   nil,
   230,   230,   nil,   231,   231,   231,   nil,   231,   230,   nil,
   230,   231,   231,   nil,   nil,   nil,   231,   nil,   231,   231,
   231,   231,   231,   231,   231,   nil,   nil,   nil,   nil,   nil,
   231,   231,   231,   231,   231,   231,   231,   nil,   nil,   231,
   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   231,
   231,   231,   231,   231,   231,   231,   231,   nil,   231,   231,
   231,   nil,   231,   231,   231,   231,   231,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   231,
   nil,   nil,   231,   231,   nil,   nil,   231,   nil,   nil,   nil,
   nil,   nil,   231,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   231,   nil,   nil,   nil,   nil,   231,   231,   231,   231,   nil,
   231,   231,   nil,   nil,   nil,   231,   231,   nil,   232,   232,
   232,   nil,   232,   231,   nil,   231,   232,   232,   nil,   nil,
   nil,   232,   nil,   232,   232,   232,   232,   232,   232,   232,
   nil,   nil,   nil,   nil,   nil,   232,   232,   232,   232,   232,
   232,   232,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,
   nil,   232,   nil,   nil,   232,   232,   232,   232,   232,   232,
   232,   232,   nil,   232,   232,   232,   nil,   232,   232,   232,
   232,   232,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   232,   nil,   nil,   232,   nil,   nil,   232,   232,   nil,
   nil,   232,   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,
   232,   232,   232,   232,   nil,   232,   232,   nil,   nil,   nil,
   232,   232,   nil,   233,   233,   233,   nil,   233,   232,   nil,
   232,   233,   233,   nil,   nil,   nil,   233,   nil,   233,   233,
   233,   233,   233,   233,   233,   nil,   nil,   nil,   nil,   nil,
   233,   233,   233,   233,   233,   233,   233,   nil,   nil,   233,
   nil,   nil,   nil,   nil,   nil,   nil,   233,   nil,   nil,   233,
   233,   233,   233,   233,   233,   233,   233,   nil,   233,   233,
   233,   nil,   233,   233,   233,   233,   233,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   233,   nil,   nil,   233,
   nil,   nil,   233,   233,   nil,   nil,   233,   nil,   nil,   nil,
   nil,   nil,   233,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   233,   nil,   nil,   nil,   nil,   233,   233,   233,   233,   nil,
   233,   233,   nil,   nil,   nil,   233,   233,   nil,   234,   234,
   234,   nil,   234,   233,   nil,   233,   234,   234,   nil,   nil,
   nil,   234,   nil,   234,   234,   234,   234,   234,   234,   234,
   nil,   nil,   nil,   nil,   nil,   234,   234,   234,   234,   234,
   234,   234,   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,
   nil,   234,   nil,   nil,   234,   234,   234,   234,   234,   234,
   234,   234,   nil,   234,   234,   234,   nil,   234,   234,   234,
   234,   234,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   234,   nil,   nil,   234,   nil,   nil,   234,   234,   nil,
   nil,   234,   nil,   nil,   nil,   nil,   nil,   234,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   234,   nil,   nil,   nil,   nil,
   234,   234,   234,   234,   nil,   234,   234,   nil,   nil,   nil,
   234,   234,   nil,   235,   235,   235,   nil,   235,   234,   nil,
   234,   235,   235,   nil,   nil,   nil,   235,   nil,   235,   235,
   235,   235,   235,   235,   235,   nil,   nil,   nil,   nil,   nil,
   235,   235,   235,   235,   235,   235,   235,   nil,   nil,   235,
   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   235,
   235,   235,   235,   235,   235,   235,   235,   nil,   235,   235,
   235,   nil,   235,   235,   235,   235,   235,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   235,
   nil,   nil,   235,   235,   nil,   nil,   235,   nil,   nil,   nil,
   nil,   nil,   235,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   235,   nil,   nil,   nil,   nil,   235,   235,   235,   235,   nil,
   235,   235,   nil,   nil,   nil,   235,   235,   nil,   236,   236,
   236,   nil,   236,   235,   nil,   235,   236,   236,   nil,   nil,
   nil,   236,   nil,   236,   236,   236,   236,   236,   236,   236,
   nil,   nil,   nil,   nil,   nil,   236,   236,   236,   236,   236,
   236,   236,   nil,   nil,   236,   nil,   nil,   nil,   nil,   nil,
   nil,   236,   nil,   nil,   236,   236,   236,   236,   236,   236,
   236,   236,   nil,   236,   236,   236,   nil,   236,   236,   236,
   236,   236,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   236,   nil,   nil,   236,   nil,   nil,   236,   236,   nil,
   nil,   236,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,
   236,   236,   236,   236,   nil,   236,   236,   nil,   nil,   nil,
   236,   236,   nil,   237,   237,   237,   nil,   237,   236,   nil,
   236,   237,   237,   nil,   nil,   nil,   237,   nil,   237,   237,
   237,   237,   237,   237,   237,   nil,   nil,   nil,   nil,   nil,
   237,   237,   237,   237,   237,   237,   237,   nil,   nil,   237,
   nil,   nil,   nil,   nil,   nil,   nil,   237,   nil,   nil,   237,
   237,   237,   237,   237,   237,   237,   237,   nil,   237,   237,
   237,   nil,   237,   237,   237,   237,   237,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   237,   nil,   nil,   237,
   nil,   nil,   237,   237,   nil,   nil,   237,   nil,   nil,   nil,
   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   237,   nil,   nil,   nil,   nil,   237,   237,   237,   237,   nil,
   237,   237,   nil,   nil,   nil,   237,   237,   nil,   238,   238,
   238,   nil,   238,   237,   nil,   237,   238,   238,   nil,   nil,
   nil,   238,   nil,   238,   238,   238,   238,   238,   238,   238,
   nil,   nil,   nil,   nil,   nil,   238,   238,   238,   238,   238,
   238,   238,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,
   nil,   238,   nil,   nil,   238,   238,   238,   238,   238,   238,
   238,   238,   nil,   238,   238,   238,   nil,   238,   238,   238,
   238,   238,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   238,   nil,   nil,   238,   nil,   nil,   238,   238,   nil,
   nil,   238,   nil,   nil,   nil,   nil,   nil,   238,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   238,   nil,   nil,   nil,   nil,
   238,   238,   238,   238,   nil,   238,   238,   nil,   nil,   nil,
   238,   238,   nil,   239,   239,   239,   nil,   239,   238,   nil,
   238,   239,   239,   nil,   nil,   nil,   239,   nil,   239,   239,
   239,   239,   239,   239,   239,   nil,   nil,   nil,   nil,   nil,
   239,   239,   239,   239,   239,   239,   239,   nil,   nil,   239,
   nil,   nil,   nil,   nil,   nil,   nil,   239,   nil,   nil,   239,
   239,   239,   239,   239,   239,   239,   239,   nil,   239,   239,
   239,   nil,   239,   239,   239,   239,   239,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   239,   nil,   nil,   239,
   nil,   nil,   239,   239,   nil,   nil,   239,   nil,   nil,   nil,
   nil,   nil,   239,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   239,   nil,   nil,   nil,   nil,   239,   239,   239,   239,   nil,
   239,   239,   nil,   nil,   nil,   239,   239,   nil,   240,   240,
   240,   nil,   240,   239,   nil,   239,   240,   240,   nil,   nil,
   nil,   240,   nil,   240,   240,   240,   240,   240,   240,   240,
   nil,   nil,   nil,   nil,   nil,   240,   240,   240,   240,   240,
   240,   240,   nil,   nil,   240,   nil,   nil,   nil,   nil,   nil,
   nil,   240,   nil,   nil,   240,   240,   240,   240,   240,   240,
   240,   240,   nil,   240,   240,   240,   nil,   240,   240,   240,
   240,   240,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   240,   nil,   nil,   240,   nil,   nil,   240,   240,   nil,
   nil,   240,   nil,   nil,   nil,   nil,   nil,   240,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   240,   nil,   nil,   nil,   nil,
   240,   240,   240,   240,   nil,   240,   240,   nil,   nil,   nil,
   240,   240,   nil,   241,   241,   241,   nil,   241,   240,   nil,
   240,   241,   241,   nil,   nil,   nil,   241,   nil,   241,   241,
   241,   241,   241,   241,   241,   nil,   nil,   nil,   nil,   nil,
   241,   241,   241,   241,   241,   241,   241,   nil,   nil,   241,
   nil,   nil,   nil,   nil,   nil,   nil,   241,   nil,   nil,   241,
   241,   241,   241,   241,   241,   241,   241,   nil,   241,   241,
   241,   nil,   241,   241,   241,   241,   241,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   241,   nil,   nil,   241,
   nil,   nil,   241,   241,   nil,   nil,   241,   nil,   nil,   nil,
   nil,   nil,   241,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   241,   nil,   nil,   nil,   nil,   241,   241,   241,   241,   nil,
   241,   241,   nil,   nil,   nil,   241,   241,   nil,   242,   242,
   242,   nil,   242,   241,   nil,   241,   242,   242,   nil,   nil,
   nil,   242,   nil,   242,   242,   242,   242,   242,   242,   242,
   nil,   nil,   nil,   nil,   nil,   242,   242,   242,   242,   242,
   242,   242,   nil,   nil,   242,   nil,   nil,   nil,   nil,   nil,
   nil,   242,   nil,   nil,   242,   242,   242,   242,   242,   242,
   242,   242,   nil,   242,   242,   242,   nil,   242,   242,   242,
   242,   242,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   242,   nil,   nil,   242,   nil,   nil,   242,   242,   nil,
   nil,   242,   nil,   nil,   nil,   nil,   nil,   242,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   242,   nil,   nil,   nil,   nil,
   242,   242,   242,   242,   nil,   242,   242,   nil,   nil,   nil,
   242,   242,   nil,   243,   243,   243,   nil,   243,   242,   nil,
   242,   243,   243,   nil,   nil,   nil,   243,   nil,   243,   243,
   243,   243,   243,   243,   243,   nil,   nil,   nil,   nil,   nil,
   243,   243,   243,   243,   243,   243,   243,   nil,   nil,   243,
   nil,   nil,   nil,   nil,   nil,   nil,   243,   nil,   nil,   243,
   243,   243,   243,   243,   243,   243,   243,   nil,   243,   243,
   243,   nil,   243,   243,   243,   243,   243,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   243,   nil,   nil,   243,
   nil,   nil,   243,   243,   nil,   nil,   243,   nil,   nil,   nil,
   nil,   nil,   243,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   243,   nil,   nil,   nil,   nil,   243,   243,   243,   243,   nil,
   243,   243,   nil,   nil,   nil,   243,   243,   nil,   244,   244,
   244,   nil,   244,   243,   nil,   243,   244,   244,   nil,   nil,
   nil,   244,   nil,   244,   244,   244,   244,   244,   244,   244,
   nil,   nil,   nil,   nil,   nil,   244,   244,   244,   244,   244,
   244,   244,   nil,   nil,   244,   nil,   nil,   nil,   nil,   nil,
   nil,   244,   nil,   nil,   244,   244,   244,   244,   244,   244,
   244,   244,   nil,   244,   244,   244,   nil,   244,   244,   244,
   244,   244,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   244,   nil,   nil,   244,   nil,   nil,   244,   244,   nil,
   nil,   244,   nil,   nil,   nil,   nil,   nil,   244,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   244,   nil,   nil,   nil,   nil,
   244,   244,   244,   244,   nil,   244,   244,   nil,   nil,   nil,
   244,   244,   nil,   245,   245,   245,   nil,   245,   244,   nil,
   244,   245,   245,   nil,   nil,   nil,   245,   nil,   245,   245,
   245,   245,   245,   245,   245,   nil,   nil,   nil,   nil,   nil,
   245,   245,   245,   245,   245,   245,   245,   nil,   nil,   245,
   nil,   nil,   nil,   nil,   nil,   nil,   245,   nil,   nil,   245,
   245,   245,   245,   245,   245,   245,   245,   nil,   245,   245,
   245,   nil,   245,   245,   245,   245,   245,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   245,   nil,   nil,   245,
   nil,   nil,   245,   245,   nil,   nil,   245,   nil,   nil,   nil,
   nil,   nil,   245,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   245,   nil,   nil,   nil,   nil,   245,   245,   245,   245,   nil,
   245,   245,   nil,   nil,   nil,   245,   245,   nil,   246,   246,
   246,   nil,   246,   245,   nil,   245,   246,   246,   nil,   nil,
   nil,   246,   nil,   246,   246,   246,   246,   246,   246,   246,
   nil,   nil,   nil,   nil,   nil,   246,   246,   246,   246,   246,
   246,   246,   nil,   nil,   246,   nil,   nil,   nil,   nil,   nil,
   nil,   246,   nil,   nil,   246,   246,   246,   246,   246,   246,
   246,   246,   nil,   246,   246,   246,   nil,   246,   246,   246,
   246,   246,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   246,   nil,   nil,   246,   nil,   nil,   246,   246,   nil,
   nil,   246,   nil,   nil,   nil,   nil,   nil,   246,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   246,   nil,   nil,   nil,   nil,
   246,   246,   246,   246,   nil,   246,   246,   nil,   nil,   nil,
   246,   246,   nil,   247,   247,   247,   nil,   247,   246,   nil,
   246,   247,   247,   nil,   nil,   nil,   247,   nil,   247,   247,
   247,   247,   247,   247,   247,   nil,   nil,   nil,   nil,   nil,
   247,   247,   247,   247,   247,   247,   247,   nil,   nil,   247,
   nil,   nil,   nil,   nil,   nil,   nil,   247,   nil,   nil,   247,
   247,   247,   247,   247,   247,   247,   247,   nil,   247,   247,
   247,   nil,   247,   247,   247,   247,   247,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   247,   nil,   nil,   247,
   nil,   nil,   247,   247,   nil,   nil,   247,   nil,   nil,   nil,
   nil,   nil,   247,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   247,   nil,   nil,   nil,   nil,   247,   247,   247,   247,   nil,
   247,   247,   nil,   nil,   nil,   247,   247,   nil,   256,   256,
   256,   nil,   256,   247,   nil,   247,   256,   256,   nil,   nil,
   nil,   256,   nil,   256,   256,   256,   256,   256,   256,   256,
   nil,   nil,   nil,   nil,   nil,   256,   256,   256,   256,   256,
   256,   256,   nil,   nil,   256,   nil,   nil,   nil,   nil,   nil,
   nil,   256,   nil,   nil,   256,   256,   256,   256,   256,   256,
   256,   256,   nil,   256,   256,   256,   nil,   256,   256,   256,
   256,   256,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   256,   nil,   nil,   256,   nil,   nil,   256,   256,   nil,
   nil,   256,   nil,   nil,   nil,   nil,   nil,   256,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   256,   nil,   nil,   nil,   nil,
   256,   256,   256,   256,   nil,   256,   256,   nil,   nil,   nil,
   256,   256,   nil,   258,   258,   258,   nil,   258,   256,   nil,
   256,   258,   258,   nil,   nil,   nil,   258,   nil,   258,   258,
   258,   258,   258,   258,   258,   nil,   nil,   nil,   nil,   nil,
   258,   258,   258,   258,   258,   258,   258,   nil,   nil,   258,
   nil,   nil,   nil,   nil,   nil,   nil,   258,   nil,   nil,   258,
   258,   258,   258,   258,   258,   258,   258,   nil,   258,   258,
   258,   nil,   258,   258,   258,   258,   258,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   258,   nil,   nil,   258,
   nil,   nil,   258,   258,   nil,   nil,   258,   nil,   nil,   nil,
   nil,   nil,   258,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   258,   nil,   nil,   nil,   nil,   258,   258,   258,   258,   nil,
   258,   258,   nil,   nil,   nil,   258,   258,   nil,   263,   263,
   263,   nil,   263,   258,   nil,   258,   263,   263,   nil,   nil,
   nil,   263,   nil,   263,   263,   263,   263,   263,   263,   263,
   nil,   nil,   nil,   nil,   nil,   263,   263,   263,   263,   263,
   263,   263,   nil,   nil,   263,   nil,   nil,   nil,   nil,   nil,
   nil,   263,   nil,   nil,   263,   263,   263,   263,   263,   263,
   263,   263,   nil,   263,   263,   263,   nil,   263,   263,   263,
   263,   263,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   263,   nil,   nil,   263,   nil,   nil,   263,   263,   nil,
   nil,   263,   nil,   nil,   nil,   nil,   nil,   263,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   263,   nil,   nil,   nil,   nil,
   263,   263,   263,   263,   nil,   263,   263,   nil,   nil,   nil,
   263,   263,   nil,   269,   269,   269,   nil,   269,   263,   nil,
   263,   269,   269,   nil,   nil,   nil,   269,   nil,   269,   269,
   269,   269,   269,   269,   269,   nil,   nil,   nil,   nil,   nil,
   269,   269,   269,   269,   269,   269,   269,   nil,   nil,   269,
   nil,   nil,   nil,   nil,   nil,   nil,   269,   nil,   nil,   269,
   269,   269,   269,   269,   269,   269,   269,   269,   269,   269,
   269,   nil,   269,   269,   269,   269,   269,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   269,   nil,   nil,   269,
   nil,   nil,   269,   269,   nil,   nil,   269,   nil,   269,   nil,
   269,   nil,   269,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   269,   nil,   nil,   nil,   nil,   269,   269,   269,   269,   nil,
   269,   269,   nil,   nil,   nil,   269,   269,   nil,   270,   270,
   270,   nil,   270,   269,   nil,   269,   270,   270,   nil,   nil,
   nil,   270,   nil,   270,   270,   270,   270,   270,   270,   270,
   nil,   nil,   nil,   nil,   nil,   270,   270,   270,   270,   270,
   270,   270,   nil,   nil,   270,   nil,   nil,   nil,   nil,   nil,
   nil,   270,   nil,   nil,   270,   270,   270,   270,   270,   270,
   270,   270,   270,   270,   270,   270,   nil,   270,   270,   270,
   270,   270,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   270,   nil,   nil,   270,   nil,   nil,   270,   270,   nil,
   nil,   270,   nil,   270,   nil,   270,   nil,   270,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   270,   nil,   nil,   nil,   nil,
   270,   270,   270,   270,   nil,   270,   270,   nil,   nil,   nil,
   270,   270,   nil,   278,   278,   278,   nil,   278,   270,   nil,
   270,   278,   278,   nil,   nil,   nil,   278,   nil,   278,   278,
   278,   278,   278,   278,   278,   nil,   nil,   nil,   nil,   nil,
   278,   278,   278,   278,   278,   278,   278,   nil,   nil,   278,
   nil,   nil,   nil,   nil,   nil,   nil,   278,   nil,   nil,   278,
   278,   278,   278,   278,   278,   278,   278,   278,   278,   278,
   278,   nil,   278,   278,   278,   278,   278,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   278,   nil,   nil,   278,
   nil,   nil,   278,   278,   nil,   nil,   278,   nil,   278,   nil,
   278,   nil,   278,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   278,   nil,   nil,   nil,   nil,   278,   278,   278,   278,   nil,
   278,   278,   nil,   nil,   nil,   278,   278,   278,   284,   284,
   284,   nil,   284,   278,   nil,   278,   284,   284,   nil,   nil,
   nil,   284,   nil,   284,   284,   284,   284,   284,   284,   284,
   nil,   nil,   nil,   nil,   nil,   284,   284,   284,   284,   284,
   284,   284,   nil,   nil,   284,   nil,   nil,   nil,   nil,   nil,
   nil,   284,   nil,   nil,   284,   284,   284,   284,   284,   284,
   284,   284,   nil,   284,   284,   284,   nil,   284,   284,   nil,
   nil,   284,    19,    19,    19,    19,    19,    19,    19,    19,
    19,    19,    19,   nil,    19,    19,   nil,   nil,    19,    19,
   nil,   284,   nil,   nil,   284,   nil,   nil,   284,   284,   nil,
   nil,   284,   nil,   nil,    19,   nil,    19,   nil,    19,    19,
    19,    19,    19,    19,    19,   nil,    19,   nil,   nil,   nil,
   284,   284,   284,   284,   nil,   284,   284,   nil,   nil,   nil,
   284,   284,   nil,    19,   nil,   284,   nil,   nil,   284,   nil,
   284,   297,   297,   297,   nil,   297,   nil,   nil,   nil,   297,
   297,   nil,   nil,   nil,   297,   nil,   297,   297,   297,   297,
   297,   297,   297,   nil,   nil,   nil,   nil,   nil,   297,   297,
   297,   297,   297,   297,   297,   nil,   nil,   297,   nil,   nil,
   nil,   nil,   nil,   nil,   297,   nil,   nil,   297,   297,   297,
   297,   297,   297,   297,   297,   nil,   297,   297,   297,   nil,
   297,   297,   nil,   883,   297,   883,   883,   883,   883,   883,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   297,   nil,   nil,   297,   nil,   nil,
   297,   297,   nil,   nil,   297,   nil,   nil,   nil,   nil,   nil,
   883,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   883,
   883,   883,   883,   297,   297,   297,   297,   nil,   297,   297,
   nil,   nil,   nil,   297,   297,   nil,   306,   306,   306,   nil,
   306,   297,   nil,   297,   306,   306,   nil,   nil,   nil,   306,
   nil,   306,   306,   306,   306,   306,   306,   306,   nil,   nil,
   nil,   nil,   nil,   306,   306,   306,   306,   306,   306,   306,
   nil,   nil,   306,   nil,   nil,   nil,   nil,   nil,   nil,   306,
   nil,   nil,   306,   306,   306,   306,   306,   306,   306,   306,
   nil,   306,   306,   306,   nil,   306,   306,   306,   306,   306,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   306,
   nil,   nil,   306,   306,   nil,   306,   306,   nil,   nil,   306,
   nil,   nil,   nil,   nil,   nil,   306,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   306,   nil,   nil,   nil,   nil,   306,   306,
   306,   306,   nil,   306,   306,   nil,   nil,   nil,   306,   306,
   nil,   322,   322,   322,   nil,   322,   306,   nil,   306,   322,
   322,   nil,   nil,   nil,   322,   nil,   322,   322,   322,   322,
   322,   322,   322,   nil,   nil,   nil,   nil,   nil,   322,   322,
   322,   322,   322,   322,   322,   nil,   nil,   322,   nil,   nil,
   nil,   nil,   nil,   nil,   322,   nil,   nil,   322,   322,   322,
   322,   322,   322,   322,   322,   nil,   322,   322,   322,   nil,
   322,   322,   322,   322,   322,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   322,   nil,   nil,   322,   nil,   nil,
   322,   322,   nil,   nil,   322,   nil,   nil,   nil,   nil,   nil,
   322,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   322,   nil,
   nil,   nil,   nil,   322,   322,   322,   322,   nil,   322,   322,
   nil,   nil,   nil,   322,   322,   nil,   323,   323,   323,   nil,
   323,   322,   nil,   322,   323,   323,   nil,   nil,   nil,   323,
   nil,   323,   323,   323,   323,   323,   323,   323,   nil,   nil,
   nil,   nil,   nil,   323,   323,   323,   323,   323,   323,   323,
   nil,   nil,   323,   nil,   nil,   nil,   nil,   nil,   nil,   323,
   nil,   nil,   323,   323,   323,   323,   323,   323,   323,   323,
   nil,   323,   323,   323,   nil,   323,   323,   323,   323,   323,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   323,
   nil,   nil,   323,   nil,   nil,   323,   323,   nil,   nil,   323,
   nil,   nil,   nil,   nil,   nil,   323,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   323,   nil,   nil,   nil,   nil,   323,   323,
   323,   323,   nil,   323,   323,   nil,   nil,   nil,   323,   323,
   nil,   341,   341,   341,   nil,   341,   323,   nil,   323,   341,
   341,   nil,   nil,   nil,   341,   nil,   341,   341,   341,   341,
   341,   341,   341,   nil,   nil,   nil,   nil,   nil,   341,   341,
   341,   341,   341,   341,   341,   nil,   nil,   341,   nil,   nil,
   nil,   nil,   nil,   nil,   341,   nil,   nil,   341,   341,   341,
   341,   341,   341,   341,   341,   nil,   341,   341,   341,   nil,
   341,   341,   341,   341,   341,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   341,   nil,   nil,   341,   nil,   nil,
   341,   341,   nil,   nil,   341,   nil,   nil,   nil,   nil,   nil,
   341,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   341,   nil,
   nil,   nil,   nil,   341,   341,   341,   341,   nil,   341,   341,
   nil,   nil,   nil,   341,   341,   nil,   356,   356,   356,   nil,
   356,   341,   nil,   341,   356,   356,   nil,   nil,   nil,   356,
   nil,   356,   356,   356,   356,   356,   356,   356,   nil,   nil,
   nil,   nil,   nil,   356,   356,   356,   356,   356,   356,   356,
   nil,   nil,   356,   nil,   nil,   nil,   nil,   nil,   nil,   356,
   nil,   nil,   356,   356,   356,   356,   356,   356,   356,   356,
   nil,   356,   356,   356,   nil,   356,   356,   356,   356,   356,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   356,
   nil,   nil,   356,   nil,   nil,   356,   356,   nil,   nil,   356,
   nil,   nil,   nil,   nil,   nil,   356,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   356,   nil,   nil,   nil,   nil,   356,   356,
   356,   356,   nil,   356,   356,   nil,   nil,   nil,   356,   356,
   nil,   383,   383,   383,   nil,   383,   356,   nil,   356,   383,
   383,   nil,   nil,   nil,   383,   nil,   383,   383,   383,   383,
   383,   383,   383,   nil,   nil,   nil,   nil,   nil,   383,   383,
   383,   383,   383,   383,   383,   nil,   nil,   383,   nil,   nil,
   nil,   nil,   nil,   nil,   383,   nil,   nil,   383,   383,   383,
   383,   383,   383,   383,   383,   nil,   383,   383,   383,   nil,
   383,   383,   383,   383,   383,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   383,   nil,   nil,   383,   nil,   nil,
   383,   383,   nil,   nil,   383,   nil,   nil,   nil,   nil,   nil,
   383,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   383,   nil,
   nil,   nil,   nil,   383,   383,   383,   383,   nil,   383,   383,
   nil,   nil,   nil,   383,   383,   nil,   415,   415,   415,   nil,
   415,   383,   nil,   383,   415,   415,   nil,   nil,   nil,   415,
   nil,   415,   415,   415,   415,   415,   415,   415,   nil,   nil,
   nil,   nil,   nil,   415,   415,   415,   415,   415,   415,   415,
   nil,   nil,   415,   nil,   nil,   nil,   nil,   nil,   nil,   415,
   nil,   nil,   415,   415,   415,   415,   415,   415,   415,   415,
   415,   415,   415,   415,   nil,   415,   415,   415,   415,   415,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   415,
   nil,   nil,   415,   nil,   nil,   415,   415,   nil,   nil,   415,
   nil,   415,   nil,   415,   nil,   415,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   415,   nil,   nil,   nil,   nil,   415,   415,
   415,   415,   nil,   415,   415,   nil,   nil,   nil,   415,   415,
   nil,   417,   417,   417,   nil,   417,   415,   nil,   415,   417,
   417,   nil,   nil,   nil,   417,   nil,   417,   417,   417,   417,
   417,   417,   417,   nil,   nil,   nil,   nil,   nil,   417,   417,
   417,   417,   417,   417,   417,   nil,   nil,   417,   nil,   nil,
   nil,   nil,   nil,   nil,   417,   nil,   nil,   417,   417,   417,
   417,   417,   417,   417,   417,   nil,   417,   417,   417,   nil,
   417,   417,   417,   417,   417,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   417,   nil,   nil,   417,   nil,   nil,
   417,   417,   nil,   nil,   417,   nil,   nil,   nil,   nil,   nil,
   417,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   417,   nil,
   nil,   nil,   nil,   417,   417,   417,   417,   nil,   417,   417,
   nil,   nil,   nil,   417,   417,   nil,   418,   418,   418,   nil,
   418,   417,   nil,   417,   418,   418,   nil,   nil,   nil,   418,
   nil,   418,   418,   418,   418,   418,   418,   418,   nil,   nil,
   nil,   nil,   nil,   418,   418,   418,   418,   418,   418,   418,
   nil,   nil,   418,   nil,   nil,   nil,   nil,   nil,   nil,   418,
   nil,   nil,   418,   418,   418,   418,   418,   418,   418,   418,
   nil,   418,   418,   418,   nil,   418,   418,   418,   418,   418,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   418,
   nil,   nil,   418,   nil,   nil,   418,   418,   nil,   nil,   418,
   nil,   nil,   nil,   nil,   nil,   418,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   418,   nil,   nil,   nil,   nil,   418,   418,
   418,   418,   nil,   418,   418,   nil,   nil,   nil,   418,   418,
   nil,   419,   419,   419,   nil,   419,   418,   nil,   418,   419,
   419,   nil,   nil,   nil,   419,   nil,   419,   419,   419,   419,
   419,   419,   419,   nil,   nil,   nil,   nil,   nil,   419,   419,
   419,   419,   419,   419,   419,   nil,   nil,   419,   nil,   nil,
   nil,   nil,   nil,   nil,   419,   nil,   nil,   419,   419,   419,
   419,   419,   419,   419,   419,   nil,   419,   419,   419,   nil,
   419,   419,   419,   419,   419,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   419,   nil,   nil,   419,   nil,   nil,
   419,   419,   nil,   nil,   419,   nil,   nil,   nil,   nil,   nil,
   419,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   419,   nil,
   nil,   nil,   nil,   419,   419,   419,   419,   nil,   419,   419,
   nil,   nil,   nil,   419,   419,   nil,   448,   448,   448,   nil,
   448,   419,   nil,   419,   448,   448,   nil,   nil,   nil,   448,
   nil,   448,   448,   448,   448,   448,   448,   448,   nil,   nil,
   nil,   nil,   nil,   448,   448,   448,   448,   448,   448,   448,
   nil,   nil,   448,   nil,   nil,   nil,   nil,   nil,   nil,   448,
   nil,   nil,   448,   448,   448,   448,   448,   448,   448,   448,
   448,   448,   448,   448,   nil,   448,   448,   448,   448,   448,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   448,
   nil,   nil,   448,   nil,   nil,   448,   448,   nil,   nil,   448,
   nil,   448,   nil,   448,   nil,   448,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   448,   nil,   nil,   nil,   nil,   448,   448,
   448,   448,   nil,   448,   448,   nil,   nil,   nil,   448,   448,
   nil,   450,   450,   450,   nil,   450,   448,   nil,   448,   450,
   450,   nil,   nil,   nil,   450,   nil,   450,   450,   450,   450,
   450,   450,   450,   nil,   nil,   nil,   nil,   nil,   450,   450,
   450,   450,   450,   450,   450,   nil,   nil,   450,   nil,   nil,
   nil,   nil,   nil,   nil,   450,   nil,   nil,   450,   450,   450,
   450,   450,   450,   450,   450,   450,   450,   450,   450,   nil,
   450,   450,   450,   450,   450,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   450,   nil,   nil,   450,   nil,   nil,
   450,   450,   nil,   nil,   450,   nil,   nil,   nil,   450,   nil,
   450,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   450,   nil,
   nil,   nil,   nil,   450,   450,   450,   450,   nil,   450,   450,
   nil,   nil,   nil,   450,   450,   nil,   452,   452,   452,   nil,
   452,   450,   nil,   450,   452,   452,   nil,   nil,   nil,   452,
   nil,   452,   452,   452,   452,   452,   452,   452,   nil,   nil,
   nil,   nil,   nil,   452,   452,   452,   452,   452,   452,   452,
   nil,   nil,   452,   nil,   nil,   nil,   nil,   nil,   nil,   452,
   nil,   nil,   452,   452,   452,   452,   452,   452,   452,   452,
   nil,   452,   452,   452,   nil,   452,   452,   452,   452,   452,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   452,
   nil,   nil,   452,   nil,   nil,   452,   452,   nil,   nil,   452,
   nil,   nil,   nil,   nil,   nil,   452,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   452,   nil,   nil,   nil,   nil,   452,   452,
   452,   452,   nil,   452,   452,   nil,   nil,   nil,   452,   452,
   nil,   464,   464,   464,   nil,   464,   452,   nil,   452,   464,
   464,   nil,   nil,   nil,   464,   nil,   464,   464,   464,   464,
   464,   464,   464,   nil,   nil,   nil,   nil,   nil,   464,   464,
   464,   464,   464,   464,   464,   nil,   nil,   464,   nil,   nil,
   nil,   nil,   nil,   nil,   464,   nil,   nil,   464,   464,   464,
   464,   464,   464,   464,   464,   464,   464,   464,   464,   nil,
   464,   464,   464,   464,   464,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   464,   nil,   nil,   464,   nil,   nil,
   464,   464,   nil,   nil,   464,   nil,   nil,   nil,   464,   nil,
   464,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   464,   nil,
   nil,   nil,   nil,   464,   464,   464,   464,   nil,   464,   464,
   nil,   nil,   nil,   464,   464,   nil,   474,   474,   474,   nil,
   474,   464,   nil,   464,   474,   474,   nil,   nil,   nil,   474,
   nil,   474,   474,   474,   474,   474,   474,   474,   nil,   nil,
   nil,   nil,   nil,   474,   474,   474,   474,   474,   474,   474,
   nil,   nil,   474,   nil,   nil,   nil,   nil,   nil,   nil,   474,
   nil,   nil,   474,   474,   474,   474,   474,   474,   474,   474,
   nil,   474,   474,   474,   nil,   474,   474,   nil,   906,   474,
   906,   906,   906,   906,   906,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   474,
   nil,   nil,   474,   nil,   nil,   474,   474,   nil,   nil,   474,
   nil,   nil,   nil,   nil,   nil,   906,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   906,   906,   906,   906,   474,   474,
   474,   474,   nil,   474,   474,   nil,   nil,   nil,   474,   474,
   nil,   476,   476,   476,   nil,   476,   474,   nil,   474,   476,
   476,   nil,   nil,   nil,   476,   nil,   476,   476,   476,   476,
   476,   476,   476,   nil,   nil,   nil,   nil,   nil,   476,   476,
   476,   476,   476,   476,   476,   nil,   nil,   476,   nil,   nil,
   nil,   nil,   nil,   nil,   476,   nil,   nil,   476,   476,   476,
   476,   476,   476,   476,   476,   476,   476,   476,   476,   nil,
   476,   476,   476,   476,   476,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   476,   nil,   nil,   476,   nil,   nil,
   476,   476,   nil,   nil,   476,   nil,   476,   nil,   476,   nil,
   476,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   476,   nil,
   nil,   nil,   nil,   476,   476,   476,   476,   nil,   476,   476,
   nil,   nil,   nil,   476,   476,   nil,   483,   483,   483,   nil,
   483,   476,   nil,   476,   483,   483,   nil,   nil,   nil,   483,
   nil,   483,   483,   483,   483,   483,   483,   483,   nil,   nil,
   nil,   nil,   nil,   483,   483,   483,   483,   483,   483,   483,
   nil,   nil,   483,   nil,   nil,   nil,   nil,   nil,   nil,   483,
   nil,   nil,   483,   483,   483,   483,   483,   483,   483,   483,
   nil,   483,   483,   483,   nil,   483,   483,   nil,   nil,   483,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   483,
   nil,   nil,   483,   nil,   nil,   483,   483,   nil,   nil,   483,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   483,   483,
   483,   483,   nil,   483,   483,   nil,   nil,   nil,   483,   483,
   nil,   486,   486,   486,   nil,   486,   483,   nil,   483,   486,
   486,   nil,   nil,   nil,   486,   nil,   486,   486,   486,   486,
   486,   486,   486,   nil,   nil,   nil,   nil,   nil,   486,   486,
   486,   486,   486,   486,   486,   nil,   nil,   486,   nil,   nil,
   nil,   nil,   nil,   nil,   486,   nil,   nil,   486,   486,   486,
   486,   486,   486,   486,   486,   nil,   486,   486,   486,   nil,
   486,   486,   486,   486,   486,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   486,   nil,   nil,   486,   nil,   nil,
   486,   486,   nil,   nil,   486,   nil,   nil,   nil,   nil,   nil,
   486,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   486,   nil,
   nil,   nil,   nil,   486,   486,   486,   486,   nil,   486,   486,
   nil,   nil,   nil,   486,   486,   nil,   487,   487,   487,   nil,
   487,   486,   nil,   486,   487,   487,   nil,   nil,   nil,   487,
   nil,   487,   487,   487,   487,   487,   487,   487,   nil,   nil,
   nil,   nil,   nil,   487,   487,   487,   487,   487,   487,   487,
   nil,   nil,   487,   nil,   nil,   nil,   nil,   nil,   nil,   487,
   nil,   nil,   487,   487,   487,   487,   487,   487,   487,   487,
   nil,   487,   487,   487,   nil,   487,   487,   487,   487,   487,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   487,
   nil,   nil,   487,   nil,   nil,   487,   487,   nil,   nil,   487,
   nil,   nil,   nil,   nil,   nil,   487,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   487,   nil,   nil,   nil,   nil,   487,   487,
   487,   487,   nil,   487,   487,   nil,   nil,   nil,   487,   487,
   nil,   488,   488,   488,   nil,   488,   487,   nil,   487,   488,
   488,   nil,   nil,   nil,   488,   nil,   488,   488,   488,   488,
   488,   488,   488,   nil,   nil,   nil,   nil,   nil,   488,   488,
   488,   488,   488,   488,   488,   nil,   nil,   488,   nil,   nil,
   nil,   nil,   nil,   nil,   488,   nil,   nil,   488,   488,   488,
   488,   488,   488,   488,   488,   nil,   488,   488,   488,   nil,
   488,   488,   488,   488,   488,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   488,   nil,   nil,   488,   nil,   nil,
   488,   488,   nil,   nil,   488,   nil,   nil,   nil,   nil,   nil,
   488,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   488,   nil,
   nil,   nil,   nil,   488,   488,   488,   488,   nil,   488,   488,
   nil,   nil,   nil,   488,   488,   nil,   492,   492,   492,   nil,
   492,   488,   nil,   488,   492,   492,   nil,   nil,   nil,   492,
   nil,   492,   492,   492,   492,   492,   492,   492,   nil,   nil,
   nil,   nil,   nil,   492,   492,   492,   492,   492,   492,   492,
   nil,   nil,   492,   nil,   nil,   nil,   nil,   nil,   nil,   492,
   nil,   nil,   492,   492,   492,   492,   492,   492,   492,   492,
   nil,   492,   492,   492,   nil,   492,   492,   492,   492,   492,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   492,
   nil,   nil,   492,   nil,   nil,   492,   492,   nil,   nil,   492,
   nil,   nil,   nil,   nil,   nil,   492,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   492,   nil,   nil,   nil,   nil,   492,   492,
   492,   492,   nil,   492,   492,   nil,   nil,   nil,   492,   492,
   nil,   494,   494,   494,   nil,   494,   492,   nil,   492,   494,
   494,   nil,   nil,   nil,   494,   nil,   494,   494,   494,   494,
   494,   494,   494,   nil,   nil,   nil,   nil,   nil,   494,   494,
   494,   494,   494,   494,   494,   nil,   nil,   494,   nil,   nil,
   nil,   nil,   nil,   nil,   494,   nil,   nil,   494,   494,   494,
   494,   494,   494,   494,   494,   nil,   494,   494,   494,   nil,
   494,   494,   494,   494,   494,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   494,   nil,   nil,   494,   nil,   nil,
   494,   494,   nil,   nil,   494,   nil,   494,   nil,   nil,   nil,
   494,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   494,   nil,
   nil,   nil,   nil,   494,   494,   494,   494,   nil,   494,   494,
   nil,   nil,   nil,   494,   494,   nil,   498,   498,   498,   nil,
   498,   494,   nil,   494,   498,   498,   nil,   nil,   nil,   498,
   nil,   498,   498,   498,   498,   498,   498,   498,   nil,   nil,
   nil,   nil,   nil,   498,   498,   498,   498,   498,   498,   498,
   nil,   nil,   498,   nil,   nil,   nil,   nil,   nil,   nil,   498,
   nil,   nil,   498,   498,   498,   498,   498,   498,   498,   498,
   498,   498,   498,   498,   nil,   498,   498,   498,   498,   498,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   498,
   nil,   nil,   498,   nil,   nil,   498,   498,   nil,   nil,   498,
   nil,   498,   nil,   nil,   nil,   498,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   498,   nil,   nil,   nil,   nil,   498,   498,
   498,   498,   nil,   498,   498,   nil,   nil,   nil,   498,   498,
   nil,   501,   501,   501,   nil,   501,   498,   nil,   498,   501,
   501,   nil,   nil,   nil,   501,   nil,   501,   501,   501,   501,
   501,   501,   501,   nil,   nil,   nil,   nil,   nil,   501,   501,
   501,   501,   501,   501,   501,   nil,   nil,   501,   nil,   nil,
   nil,   nil,   nil,   nil,   501,   nil,   nil,   501,   501,   501,
   501,   501,   501,   501,   501,   501,   501,   501,   501,   nil,
   501,   501,   501,   501,   501,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   501,   nil,   nil,   501,   nil,   nil,
   501,   501,   nil,   nil,   501,   nil,   nil,   nil,   nil,   nil,
   501,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   501,   nil,
   nil,   nil,   nil,   501,   501,   501,   501,   nil,   501,   501,
   nil,   nil,   nil,   501,   501,   nil,   515,   515,   515,   nil,
   515,   501,   nil,   501,   515,   515,   nil,   nil,   nil,   515,
   nil,   515,   515,   515,   515,   515,   515,   515,   nil,   nil,
   nil,   nil,   nil,   515,   515,   515,   515,   515,   515,   515,
   nil,   nil,   515,   nil,   nil,   nil,   nil,   nil,   nil,   515,
   nil,   nil,   515,   515,   515,   515,   515,   515,   515,   515,
   nil,   515,   515,   515,   nil,   515,   515,   515,   515,   515,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   515,
   nil,   nil,   515,   nil,   nil,   515,   515,   nil,   nil,   515,
   nil,   515,   nil,   nil,   nil,   515,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   515,   nil,   nil,   nil,   nil,   515,   515,
   515,   515,   nil,   515,   515,   nil,   nil,   nil,   515,   515,
   nil,   516,   516,   516,   nil,   516,   515,   nil,   515,   516,
   516,   nil,   nil,   nil,   516,   nil,   516,   516,   516,   516,
   516,   516,   516,   nil,   nil,   nil,   nil,   nil,   516,   516,
   516,   516,   516,   516,   516,   nil,   nil,   516,   nil,   nil,
   nil,   nil,   nil,   nil,   516,   nil,   nil,   516,   516,   516,
   516,   516,   516,   516,   516,   516,   516,   516,   516,   nil,
   516,   516,   516,   516,   516,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   516,   nil,   nil,   516,   nil,   nil,
   516,   516,   nil,   nil,   516,   nil,   516,   nil,   516,   nil,
   516,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   516,   nil,
   nil,   nil,   nil,   516,   516,   516,   516,   nil,   516,   516,
   nil,   nil,   nil,   516,   516,   nil,   526,   526,   526,   nil,
   526,   516,   nil,   516,   526,   526,   nil,   nil,   nil,   526,
   nil,   526,   526,   526,   526,   526,   526,   526,   nil,   nil,
   nil,   nil,   nil,   526,   526,   526,   526,   526,   526,   526,
   nil,   nil,   526,   nil,   nil,   nil,   nil,   nil,   nil,   526,
   nil,   nil,   526,   526,   526,   526,   526,   526,   526,   526,
   526,   526,   526,   526,   nil,   526,   526,   526,   526,   526,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   526,
   nil,   nil,   526,   nil,   nil,   526,   526,   nil,   nil,   526,
   nil,   526,   nil,   526,   nil,   526,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   526,   nil,   nil,   nil,   nil,   526,   526,
   526,   526,   nil,   526,   526,   nil,   nil,   nil,   526,   526,
   nil,   529,   529,   529,   nil,   529,   526,   nil,   526,   529,
   529,   nil,   nil,   nil,   529,   nil,   529,   529,   529,   529,
   529,   529,   529,   nil,   nil,   nil,   nil,   nil,   529,   529,
   529,   529,   529,   529,   529,   nil,   nil,   529,   nil,   nil,
   nil,   nil,   nil,   nil,   529,   nil,   nil,   529,   529,   529,
   529,   529,   529,   529,   529,   nil,   529,   529,   529,   nil,
   529,   529,   529,   529,   529,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   529,   nil,   nil,   529,   nil,   nil,
   529,   529,   nil,   nil,   529,   nil,   nil,   nil,   nil,   nil,
   529,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   529,   nil,
   nil,   nil,   nil,   529,   529,   529,   529,   nil,   529,   529,
   nil,   nil,   nil,   529,   529,   nil,   557,   557,   557,   nil,
   557,   529,   nil,   529,   557,   557,   nil,   nil,   nil,   557,
   nil,   557,   557,   557,   557,   557,   557,   557,   nil,   nil,
   nil,   nil,   nil,   557,   557,   557,   557,   557,   557,   557,
   nil,   nil,   557,   nil,   nil,   nil,   nil,   nil,   nil,   557,
   nil,   nil,   557,   557,   557,   557,   557,   557,   557,   557,
   nil,   557,   557,   557,   nil,   557,   557,   557,   557,   557,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   557,
   nil,   nil,   557,   nil,   nil,   557,   557,   nil,   nil,   557,
   nil,   nil,   nil,   nil,   nil,   557,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   557,   nil,   nil,   nil,   nil,   557,   557,
   557,   557,   nil,   557,   557,   nil,   nil,   nil,   557,   557,
   nil,   559,   559,   559,   nil,   559,   557,   nil,   557,   559,
   559,   nil,   nil,   nil,   559,   nil,   559,   559,   559,   559,
   559,   559,   559,   nil,   nil,   nil,   nil,   nil,   559,   559,
   559,   559,   559,   559,   559,   nil,   nil,   559,   nil,   nil,
   nil,   nil,   nil,   nil,   559,   nil,   nil,   559,   559,   559,
   559,   559,   559,   559,   559,   nil,   559,   559,   559,   nil,
   559,   559,   559,   559,   559,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   559,   nil,   nil,   559,   nil,   nil,
   559,   559,   nil,   nil,   559,   nil,   559,   nil,   nil,   nil,
   559,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   559,   nil,
   nil,   nil,   nil,   559,   559,   559,   559,   nil,   559,   559,
   nil,   nil,   nil,   559,   559,   nil,   560,   560,   560,   nil,
   560,   559,   nil,   559,   560,   560,   nil,   nil,   nil,   560,
   nil,   560,   560,   560,   560,   560,   560,   560,   nil,   nil,
   nil,   nil,   nil,   560,   560,   560,   560,   560,   560,   560,
   nil,   nil,   560,   nil,   nil,   nil,   nil,   nil,   nil,   560,
   nil,   nil,   560,   560,   560,   560,   560,   560,   560,   560,
   nil,   560,   560,   560,   nil,   560,   560,   560,   560,   560,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   560,
   nil,   nil,   560,   nil,   nil,   560,   560,   nil,   nil,   560,
   nil,   nil,   nil,   nil,   nil,   560,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   560,   nil,   nil,   nil,   nil,   560,   560,
   560,   560,   nil,   560,   560,   nil,   nil,   nil,   560,   560,
   nil,   563,   563,   563,   nil,   563,   560,   nil,   560,   563,
   563,   nil,   nil,   nil,   563,   nil,   563,   563,   563,   563,
   563,   563,   563,   nil,   nil,   nil,   nil,   nil,   563,   563,
   563,   563,   563,   563,   563,   nil,   nil,   563,   nil,   nil,
   nil,   nil,   nil,   nil,   563,   nil,   nil,   563,   563,   563,
   563,   563,   563,   563,   563,   nil,   563,   563,   563,   nil,
   563,   563,   563,   563,   563,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   563,   nil,   nil,   563,   nil,   nil,
   563,   563,   nil,   nil,   563,   nil,   nil,   nil,   nil,   nil,
   563,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   563,   nil,
   nil,   nil,   nil,   563,   563,   563,   563,   nil,   563,   563,
   nil,   nil,   nil,   563,   563,   nil,   564,   564,   564,   nil,
   564,   563,   nil,   563,   564,   564,   nil,   nil,   nil,   564,
   nil,   564,   564,   564,   564,   564,   564,   564,   nil,   nil,
   nil,   nil,   nil,   564,   564,   564,   564,   564,   564,   564,
   nil,   nil,   564,   nil,   nil,   nil,   nil,   nil,   nil,   564,
   nil,   nil,   564,   564,   564,   564,   564,   564,   564,   564,
   nil,   564,   564,   564,   nil,   564,   564,   564,   564,   564,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   564,
   nil,   nil,   564,   nil,   nil,   564,   564,   nil,   nil,   564,
   nil,   nil,   nil,   nil,   nil,   564,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   564,   nil,   nil,   nil,   nil,   564,   564,
   564,   564,   nil,   564,   564,   nil,   nil,   nil,   564,   564,
   nil,   568,   568,   568,   nil,   568,   564,   nil,   564,   568,
   568,   nil,   nil,   nil,   568,   nil,   568,   568,   568,   568,
   568,   568,   568,   nil,   nil,   nil,   nil,   nil,   568,   568,
   568,   568,   568,   568,   568,   nil,   nil,   568,   nil,   nil,
   nil,   nil,   nil,   nil,   568,   nil,   nil,   568,   568,   568,
   568,   568,   568,   568,   568,   nil,   568,   568,   568,   nil,
   568,   568,   568,   568,   568,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   568,   nil,   nil,   568,   nil,   nil,
   568,   568,   nil,   nil,   568,   nil,   nil,   nil,   nil,   nil,
   568,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   568,   nil,
   nil,   nil,   nil,   568,   568,   568,   568,   nil,   568,   568,
   nil,   nil,   nil,   568,   568,   nil,   571,   571,   571,   nil,
   571,   568,   nil,   568,   571,   571,   nil,   nil,   nil,   571,
   nil,   571,   571,   571,   571,   571,   571,   571,   nil,   nil,
   nil,   nil,   nil,   571,   571,   571,   571,   571,   571,   571,
   nil,   nil,   571,   nil,   nil,   nil,   nil,   nil,   nil,   571,
   nil,   nil,   571,   571,   571,   571,   571,   571,   571,   571,
   nil,   571,   571,   571,   nil,   571,   571,   571,   571,   571,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   571,
   nil,   nil,   571,   nil,   nil,   571,   571,   nil,   nil,   571,
   nil,   nil,   nil,   nil,   nil,   571,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   571,   nil,   nil,   nil,   nil,   571,   571,
   571,   571,   nil,   571,   571,   nil,   nil,   nil,   571,   571,
   nil,   588,   588,   588,   nil,   588,   571,   nil,   571,   588,
   588,   nil,   nil,   nil,   588,   nil,   588,   588,   588,   588,
   588,   588,   588,   nil,   nil,   nil,   nil,   nil,   588,   588,
   588,   588,   588,   588,   588,   nil,   nil,   588,   nil,   nil,
   nil,   nil,   nil,   nil,   588,   nil,   nil,   588,   588,   588,
   588,   588,   588,   588,   588,   nil,   588,   588,   588,   nil,
   588,   588,   588,   588,   588,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   588,   nil,   nil,   588,   nil,   nil,
   588,   588,   nil,   nil,   588,   nil,   nil,   nil,   nil,   nil,
   588,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   588,   nil,
   nil,   nil,   nil,   588,   588,   588,   588,   nil,   588,   588,
   nil,   nil,   nil,   588,   588,   nil,   607,   607,   607,   nil,
   607,   588,   nil,   588,   607,   607,   nil,   nil,   nil,   607,
   nil,   607,   607,   607,   607,   607,   607,   607,   nil,   nil,
   nil,   nil,   nil,   607,   607,   607,   607,   607,   607,   607,
   nil,   nil,   607,   nil,   nil,   nil,   nil,   nil,   nil,   607,
   nil,   nil,   607,   607,   607,   607,   607,   607,   607,   607,
   nil,   607,   607,   607,   nil,   607,   607,   nil,   nil,   607,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   607,
   nil,   nil,   607,   nil,   nil,   607,   607,   nil,   nil,   607,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   607,   607,
   607,   607,   nil,   607,   607,   nil,   nil,   nil,   607,   607,
   nil,   618,   618,   618,   nil,   618,   607,   nil,   607,   618,
   618,   nil,   nil,   nil,   618,   nil,   618,   618,   618,   618,
   618,   618,   618,   nil,   nil,   nil,   nil,   nil,   618,   618,
   618,   618,   618,   618,   618,   nil,   nil,   618,   nil,   nil,
   nil,   nil,   nil,   nil,   618,   nil,   nil,   618,   618,   618,
   618,   618,   618,   618,   618,   nil,   618,   618,   618,   nil,
   618,   618,   nil,   nil,   618,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   618,   nil,   nil,   618,   nil,   nil,
   618,   618,   nil,   nil,   618,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   618,   618,   618,   618,   nil,   618,   618,
   nil,   nil,   nil,   618,   618,   nil,   671,   671,   671,   nil,
   671,   618,   nil,   618,   671,   671,   nil,   nil,   nil,   671,
   nil,   671,   671,   671,   671,   671,   671,   671,   nil,   nil,
   nil,   nil,   nil,   671,   671,   671,   671,   671,   671,   671,
   nil,   nil,   671,   nil,   nil,   nil,   nil,   nil,   nil,   671,
   nil,   nil,   671,   671,   671,   671,   671,   671,   671,   671,
   nil,   671,   671,   671,   nil,   671,   671,   671,   671,   671,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   671,
   nil,   nil,   671,   nil,   nil,   671,   671,   nil,   nil,   671,
   nil,   nil,   nil,   nil,   nil,   671,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   671,   nil,   nil,   nil,   nil,   671,   671,
   671,   671,   nil,   671,   671,   nil,   nil,   nil,   671,   671,
   nil,   699,   699,   699,   nil,   699,   671,   nil,   671,   699,
   699,   nil,   nil,   nil,   699,   nil,   699,   699,   699,   699,
   699,   699,   699,   nil,   nil,   nil,   nil,   nil,   699,   699,
   699,   699,   699,   699,   699,   nil,   nil,   699,   nil,   nil,
   nil,   nil,   nil,   nil,   699,   nil,   nil,   699,   699,   699,
   699,   699,   699,   699,   699,   nil,   699,   699,   699,   nil,
   699,   699,   699,   699,   699,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   699,   nil,   nil,   699,   nil,   nil,
   699,   699,   nil,   nil,   699,   nil,   nil,   nil,   nil,   nil,
   699,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   699,   nil,
   nil,   nil,   nil,   699,   699,   699,   699,   nil,   699,   699,
   nil,   nil,   nil,   699,   699,   nil,   701,   701,   701,   nil,
   701,   699,   nil,   699,   701,   701,   nil,   nil,   nil,   701,
   nil,   701,   701,   701,   701,   701,   701,   701,   nil,   nil,
   nil,   nil,   nil,   701,   701,   701,   701,   701,   701,   701,
   nil,   nil,   701,   nil,   nil,   nil,   nil,   nil,   nil,   701,
   nil,   nil,   701,   701,   701,   701,   701,   701,   701,   701,
   nil,   701,   701,   701,   nil,   701,   701,   701,   701,   701,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   701,
   nil,   nil,   701,   nil,   nil,   701,   701,   nil,   nil,   701,
   nil,   nil,   nil,   nil,   nil,   701,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   701,   nil,   nil,   nil,   nil,   701,   701,
   701,   701,   nil,   701,   701,   nil,   nil,   nil,   701,   701,
   nil,   712,   712,   712,   nil,   712,   701,   nil,   701,   712,
   712,   nil,   nil,   nil,   712,   nil,   712,   712,   712,   712,
   712,   712,   712,   nil,   nil,   nil,   nil,   nil,   712,   712,
   712,   712,   712,   712,   712,   nil,   nil,   712,   nil,   nil,
   nil,   nil,   nil,   nil,   712,   nil,   nil,   712,   712,   712,
   712,   712,   712,   712,   712,   nil,   712,   712,   712,   nil,
   712,   712,   712,   712,   712,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   712,   nil,   nil,   712,   nil,   nil,
   712,   712,   nil,   nil,   712,   nil,   nil,   nil,   nil,   nil,
   712,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   712,   nil,
   nil,   nil,   nil,   712,   712,   712,   712,   nil,   712,   712,
   nil,   nil,   nil,   712,   712,   nil,   713,   713,   713,   nil,
   713,   712,   nil,   712,   713,   713,   nil,   nil,   nil,   713,
   nil,   713,   713,   713,   713,   713,   713,   713,   nil,   nil,
   nil,   nil,   nil,   713,   713,   713,   713,   713,   713,   713,
   nil,   nil,   713,   nil,   nil,   nil,   nil,   nil,   nil,   713,
   nil,   nil,   713,   713,   713,   713,   713,   713,   713,   713,
   nil,   713,   713,   713,   nil,   713,   713,   713,   713,   713,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   713,
   nil,   nil,   713,   nil,   nil,   713,   713,   nil,   nil,   713,
   nil,   nil,   nil,   nil,   nil,   713,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   713,   nil,   nil,   nil,   nil,   713,   713,
   713,   713,   nil,   713,   713,   nil,   nil,   nil,   713,   713,
   nil,   714,   714,   714,   nil,   714,   713,   nil,   713,   714,
   714,   nil,   nil,   nil,   714,   nil,   714,   714,   714,   714,
   714,   714,   714,   nil,   nil,   nil,   nil,   nil,   714,   714,
   714,   714,   714,   714,   714,   nil,   nil,   714,   nil,   nil,
   nil,   nil,   nil,   nil,   714,   nil,   nil,   714,   714,   714,
   714,   714,   714,   714,   714,   nil,   714,   714,   714,   nil,
   714,   714,   714,   714,   714,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   714,   nil,   nil,   714,   nil,   nil,
   714,   714,   nil,   nil,   714,   nil,   nil,   nil,   nil,   nil,
   714,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   714,   nil,
   nil,   nil,   nil,   714,   714,   714,   714,   nil,   714,   714,
   nil,   nil,   nil,   714,   714,   nil,   716,   716,   716,   nil,
   716,   714,   nil,   714,   716,   716,   nil,   nil,   nil,   716,
   nil,   716,   716,   716,   716,   716,   716,   716,   nil,   nil,
   nil,   nil,   nil,   716,   716,   716,   716,   716,   716,   716,
   nil,   nil,   716,   nil,   nil,   nil,   nil,   nil,   nil,   716,
   nil,   nil,   716,   716,   716,   716,   716,   716,   716,   716,
   nil,   716,   716,   716,   nil,   716,   716,   716,   716,   716,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   716,
   nil,   nil,   716,   nil,   nil,   716,   716,   nil,   nil,   716,
   nil,   nil,   nil,   nil,   nil,   716,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   716,   nil,   nil,   nil,   nil,   716,   716,
   716,   716,   nil,   716,   716,   nil,   nil,   nil,   716,   716,
   nil,   728,   728,   728,   nil,   728,   716,   nil,   716,   728,
   728,   nil,   nil,   nil,   728,   nil,   728,   728,   728,   728,
   728,   728,   728,   nil,   nil,   nil,   nil,   nil,   728,   728,
   728,   728,   728,   728,   728,   nil,   nil,   728,   nil,   nil,
   nil,   nil,   nil,   nil,   728,   nil,   nil,   728,   728,   728,
   728,   728,   728,   728,   728,   728,   728,   728,   728,   nil,
   728,   728,   728,   728,   728,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   728,   nil,   nil,   728,   nil,   nil,
   728,   728,   nil,   nil,   728,   nil,   728,   nil,   728,   nil,
   728,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   728,   nil,
   nil,   nil,   nil,   728,   728,   728,   728,   nil,   728,   728,
   nil,   nil,   nil,   728,   728,   nil,   731,   731,   731,   nil,
   731,   728,   nil,   728,   731,   731,   nil,   nil,   nil,   731,
   nil,   731,   731,   731,   731,   731,   731,   731,   nil,   nil,
   nil,   nil,   nil,   731,   731,   731,   731,   731,   731,   731,
   nil,   nil,   731,   nil,   nil,   nil,   nil,   nil,   nil,   731,
   nil,   nil,   731,   731,   731,   731,   731,   731,   731,   731,
   731,   731,   731,   731,   nil,   731,   731,   731,   731,   731,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   731,
   nil,   nil,   731,   nil,   nil,   731,   731,   nil,   nil,   731,
   nil,   731,   nil,   731,   nil,   731,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   731,   nil,   nil,   nil,   nil,   731,   731,
   731,   731,   nil,   731,   731,   nil,   nil,   nil,   731,   731,
   nil,   746,   746,   746,   nil,   746,   731,   nil,   731,   746,
   746,   nil,   nil,   nil,   746,   nil,   746,   746,   746,   746,
   746,   746,   746,   nil,   nil,   nil,   nil,   nil,   746,   746,
   746,   746,   746,   746,   746,   nil,   nil,   746,   nil,   nil,
   nil,   nil,   nil,   nil,   746,   nil,   nil,   746,   746,   746,
   746,   746,   746,   746,   746,   nil,   746,   746,   746,   nil,
   746,   746,   nil,   nil,   746,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   746,   nil,   nil,   746,   nil,   nil,
   746,   746,   nil,   nil,   746,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   746,   746,   746,   746,   nil,   746,   746,
   nil,   nil,   nil,   746,   746,   nil,   761,   761,   761,   nil,
   761,   746,   nil,   746,   761,   761,   nil,   nil,   nil,   761,
   nil,   761,   761,   761,   761,   761,   761,   761,   nil,   nil,
   nil,   nil,   nil,   761,   761,   761,   761,   761,   761,   761,
   nil,   nil,   761,   nil,   nil,   nil,   nil,   nil,   nil,   761,
   nil,   nil,   761,   761,   761,   761,   761,   761,   761,   761,
   nil,   761,   761,   761,   nil,   761,   761,   761,   761,   761,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   761,
   nil,   nil,   761,   nil,   nil,   761,   761,   nil,   nil,   761,
   nil,   nil,   nil,   nil,   nil,   761,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   761,   nil,   nil,   nil,   nil,   761,   761,
   761,   761,   nil,   761,   761,   nil,   nil,   nil,   761,   761,
   nil,   774,   774,   774,   nil,   774,   761,   nil,   761,   774,
   774,   nil,   nil,   nil,   774,   nil,   774,   774,   774,   774,
   774,   774,   774,   nil,   nil,   nil,   nil,   nil,   774,   774,
   774,   774,   774,   774,   774,   nil,   nil,   774,   nil,   nil,
   nil,   nil,   nil,   nil,   774,   nil,   nil,   774,   774,   774,
   774,   774,   774,   774,   774,   nil,   774,   774,   774,   nil,
   774,   774,   774,   774,   774,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   774,   nil,   nil,   774,   nil,   nil,
   774,   774,   nil,   nil,   774,   nil,   nil,   nil,   nil,   nil,
   774,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   774,   nil,
   nil,   nil,   nil,   774,   774,   774,   774,   nil,   774,   774,
   nil,   nil,   nil,   774,   774,   nil,   779,   779,   779,   nil,
   779,   774,   nil,   774,   779,   779,   nil,   nil,   nil,   779,
   nil,   779,   779,   779,   779,   779,   779,   779,   nil,   nil,
   nil,   nil,   nil,   779,   779,   779,   779,   779,   779,   779,
   nil,   nil,   779,   nil,   nil,   nil,   nil,   nil,   nil,   779,
   nil,   nil,   779,   779,   779,   779,   779,   779,   779,   779,
   nil,   779,   779,   779,   nil,   779,   779,   779,   779,   779,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   779,
   nil,   nil,   779,   nil,   nil,   779,   779,   nil,   nil,   779,
   nil,   779,   nil,   nil,   nil,   779,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   779,   nil,   nil,   nil,   nil,   779,   779,
   779,   779,   nil,   779,   779,   nil,   nil,   nil,   779,   779,
   nil,   796,   796,   796,   nil,   796,   779,   nil,   779,   796,
   796,   nil,   nil,   nil,   796,   nil,   796,   796,   796,   796,
   796,   796,   796,   nil,   nil,   nil,   nil,   nil,   796,   796,
   796,   796,   796,   796,   796,   nil,   nil,   796,   nil,   nil,
   nil,   nil,   nil,   nil,   796,   nil,   nil,   796,   796,   796,
   796,   796,   796,   796,   796,   nil,   796,   796,   796,   nil,
   796,   796,   796,   796,   796,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   796,   nil,   nil,   796,   nil,   nil,
   796,   796,   nil,   nil,   796,   nil,   nil,   nil,   nil,   nil,
   796,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   796,   nil,
   nil,   nil,   nil,   796,   796,   796,   796,   nil,   796,   796,
   nil,   nil,   nil,   796,   796,   nil,   810,   810,   810,   nil,
   810,   796,   nil,   796,   810,   810,   nil,   nil,   nil,   810,
   nil,   810,   810,   810,   810,   810,   810,   810,   nil,   nil,
   nil,   nil,   nil,   810,   810,   810,   810,   810,   810,   810,
   nil,   nil,   810,   nil,   nil,   nil,   nil,   nil,   nil,   810,
   nil,   nil,   810,   810,   810,   810,   810,   810,   810,   810,
   nil,   810,   810,   810,   nil,   810,   810,   nil,   nil,   810,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   810,
   nil,   nil,   810,   nil,   nil,   810,   810,   nil,   nil,   810,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   810,   810,
   810,   810,   nil,   810,   810,   nil,   nil,   nil,   810,   810,
   nil,   870,   870,   870,   nil,   870,   810,   nil,   810,   870,
   870,   nil,   nil,   nil,   870,   nil,   870,   870,   870,   870,
   870,   870,   870,   nil,   nil,   nil,   nil,   nil,   870,   870,
   870,   870,   870,   870,   870,   nil,   nil,   870,   nil,   nil,
   nil,   nil,   nil,   nil,   870,   nil,   nil,   870,   870,   870,
   870,   870,   870,   870,   870,   nil,   870,   870,   870,   nil,
   870,   870,   870,   870,   870,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   870,   nil,   nil,   870,   nil,   nil,
   870,   870,   nil,   nil,   870,   nil,   870,   nil,   870,   nil,
   870,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   870,   nil,
   nil,   nil,   nil,   870,   870,   870,   870,   nil,   870,   870,
   nil,   nil,   nil,   870,   870,   nil,   873,   873,   873,   nil,
   873,   870,   nil,   870,   873,   873,   nil,   nil,   nil,   873,
   nil,   873,   873,   873,   873,   873,   873,   873,   nil,   nil,
   nil,   nil,   nil,   873,   873,   873,   873,   873,   873,   873,
   nil,   nil,   873,   nil,   nil,   nil,   nil,   nil,   nil,   873,
   nil,   nil,   873,   873,   873,   873,   873,   873,   873,   873,
   873,   873,   873,   873,   nil,   873,   873,   873,   873,   873,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   873,
   nil,   nil,   873,   nil,   nil,   873,   873,   nil,   nil,   873,
   nil,   873,   nil,   873,   nil,   873,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   873,   nil,   nil,   nil,   nil,   873,   873,
   873,   873,   nil,   873,   873,   nil,   nil,   nil,   873,   873,
   nil,   876,   876,   876,   nil,   876,   873,   nil,   873,   876,
   876,   nil,   nil,   nil,   876,   nil,   876,   876,   876,   876,
   876,   876,   876,   nil,   nil,   nil,   nil,   nil,   876,   876,
   876,   876,   876,   876,   876,   nil,   nil,   876,   nil,   nil,
   nil,   nil,   nil,   nil,   876,   nil,   nil,   876,   876,   876,
   876,   876,   876,   876,   876,   876,   876,   876,   876,   nil,
   876,   876,   876,   876,   876,   597,   597,   597,   597,   597,
   597,   597,   597,   597,   597,   597,   nil,   597,   597,   nil,
   nil,   597,   597,   nil,   876,   nil,   597,   876,   nil,   nil,
   876,   876,   nil,   nil,   876,   nil,   876,   597,   876,   597,
   876,   597,   597,   597,   597,   597,   597,   597,   876,   597,
   nil,   nil,   nil,   876,   876,   876,   876,   nil,   876,   876,
   nil,   nil,   nil,   876,   876,   nil,   597,   nil,   597,   nil,
   nil,   876,   nil,   876,     6,     6,     6,     6,     6,     6,
     6,     6,     6,     6,     6,     6,     6,     6,     6,     6,
     6,     6,     6,     6,     6,     6,     6,     6,   nil,   nil,
   nil,     6,     6,     6,     6,     6,     6,     6,     6,     6,
     6,   nil,   nil,   nil,   nil,   nil,     6,     6,     6,     6,
     6,     6,     6,     6,     6,     6,   nil,     6,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,     6,     6,   nil,     6,     6,
     6,     6,     6,     6,     6,   nil,   nil,     6,     6,   nil,
   nil,   nil,     6,     6,     6,     6,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,     6,
     6,   nil,     6,     6,     6,     6,     6,     6,     6,     6,
     6,     6,     6,   nil,   nil,     6,     6,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,     6,     7,     7,
     7,     7,     7,     7,     7,     7,     7,     7,     7,     7,
     7,     7,     7,     7,     7,     7,     7,     7,     7,     7,
     7,     7,   nil,   nil,   nil,     7,     7,     7,     7,     7,
     7,     7,     7,     7,     7,   nil,   nil,   nil,   nil,   nil,
     7,     7,     7,     7,     7,     7,     7,     7,     7,   nil,
   nil,     7,   nil,   nil,   nil,   nil,   nil,   nil,   nil,     7,
     7,   nil,     7,     7,     7,     7,     7,     7,     7,   nil,
   nil,     7,     7,   nil,   nil,   nil,     7,     7,     7,     7,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,     7,     7,   nil,     7,     7,     7,     7,
     7,     7,     7,     7,     7,     7,     7,   nil,   nil,     7,
     7,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,     7,   372,   372,   372,   372,   372,   372,   372,   372,
   372,   372,   372,   372,   372,   372,   372,   372,   372,   372,
   372,   372,   372,   372,   372,   372,   nil,   nil,   nil,   372,
   372,   372,   372,   372,   372,   372,   372,   372,   372,   nil,
   nil,   nil,   nil,   nil,   372,   372,   372,   372,   372,   372,
   372,   372,   372,   nil,   nil,   372,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   372,   372,   nil,   372,   372,   372,   372,
   372,   372,   372,   nil,   nil,   372,   372,   nil,   nil,   nil,
   372,   372,   372,   372,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   372,   372,   nil,
   372,   372,   372,   372,   372,   372,   372,   372,   372,   372,
   372,   nil,   nil,   372,   372,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   372,   554,   554,   554,   554,
   554,   554,   554,   554,   554,   554,   554,   554,   554,   554,
   554,   554,   554,   554,   554,   554,   554,   554,   554,   554,
   nil,   nil,   nil,   554,   554,   554,   554,   554,   554,   554,
   554,   554,   554,   nil,   nil,   nil,   nil,   nil,   554,   554,
   554,   554,   554,   554,   554,   554,   554,   nil,   nil,   554,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   554,   554,   nil,
   554,   554,   554,   554,   554,   554,   554,   nil,   nil,   554,
   554,   nil,   nil,   nil,   554,   554,   554,   554,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   554,   554,   nil,   554,   554,   554,   554,   554,   554,
   554,   554,   554,   554,   554,   nil,   nil,   554,   554,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   554,
    65,    65,    65,    65,    65,    65,    65,    65,    65,    65,
    65,    65,    65,    65,    65,    65,    65,    65,    65,    65,
    65,    65,    65,    65,   nil,   nil,   nil,    65,    65,    65,
    65,    65,    65,    65,    65,    65,    65,   nil,   nil,   nil,
   nil,   nil,    65,    65,    65,    65,    65,    65,    65,    65,
    65,    65,    65,    65,   nil,    65,   nil,   nil,   nil,   nil,
   nil,    65,    65,   nil,    65,    65,    65,    65,    65,    65,
    65,   nil,   nil,    65,    65,   nil,   nil,   nil,    65,    65,
    65,    65,   nil,   nil,   nil,   nil,   nil,    65,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    65,    65,   nil,    65,    65,
    65,    65,    65,    65,    65,    65,    65,    65,    65,   nil,
   nil,    65,   684,   684,   684,   684,   684,   684,   684,   684,
   684,   684,   684,   684,   684,   684,   684,   684,   684,   684,
   684,   684,   684,   684,   684,   684,   nil,   nil,   nil,   684,
   684,   684,   684,   684,   684,   684,   684,   684,   684,   nil,
   nil,   nil,   nil,   nil,   684,   684,   684,   684,   684,   684,
   684,   684,   684,   nil,   nil,   684,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   684,   684,   nil,   684,   684,   684,   684,
   684,   684,   684,   nil,   nil,   684,   684,   nil,   nil,   nil,
   684,   684,   684,   684,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   684,   684,   nil,
   684,   684,   684,   684,   684,   684,   684,   684,   684,   684,
   684,   201,   201,   684,   nil,   201,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   201,   201,   nil,   201,   201,   201,   201,
   201,   201,   201,   nil,   nil,   201,   201,   nil,   nil,   nil,
   201,   201,   201,   201,   nil,   nil,   nil,   nil,   nil,   201,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   201,   201,   nil,
   201,   201,   201,   201,   201,   201,   201,   201,   201,   201,
   201,   202,   202,   201,   nil,   202,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   202,   nil,   202,   202,   202,   202,
   202,   202,   202,   nil,   nil,   202,   202,   nil,   nil,   nil,
   202,   202,   202,   202,   nil,   nil,   nil,   nil,   nil,   202,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   202,   nil,
   202,   202,   202,   202,   202,   202,   202,   202,   202,   202,
   202,   266,   266,   202,   nil,   266,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   266,   266,   nil,   266,   266,   266,   266,
   266,   266,   266,   nil,   nil,   266,   266,   nil,   nil,   nil,
   266,   266,   266,   266,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   266,   266,   nil,
   266,   266,   266,   266,   266,   266,   266,   266,   266,   266,
   266,   267,   267,   266,   nil,   267,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   267,   267,   nil,   267,   267,   267,   267,
   267,   267,   267,   nil,   nil,   267,   267,   nil,   nil,   nil,
   267,   267,   267,   267,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   267,   267,   nil,
   267,   267,   267,   267,   267,   267,   267,   267,   267,   267,
   267,   413,   413,   267,   nil,   413,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   413,   413,   nil,   413,   413,   413,   413,
   413,   413,   413,   nil,   nil,   413,   413,   nil,   nil,   nil,
   413,   413,   413,   413,   nil,   nil,   nil,   nil,   nil,   413,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   413,   413,   nil,
   413,   413,   413,   413,   413,   413,   413,   413,   413,   413,
   413,   414,   414,   413,   nil,   414,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   414,   414,   nil,   414,   414,   414,   414,
   414,   414,   414,   nil,   nil,   414,   414,   nil,   nil,   nil,
   414,   414,   414,   414,   nil,   nil,   nil,   nil,   nil,   414,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   414,   414,   nil,
   414,   414,   414,   414,   414,   414,   414,   414,   414,   414,
   414,   477,   477,   414,   nil,   477,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   477,   477,   nil,   477,   477,   477,   477,
   477,   477,   477,   nil,   nil,   477,   477,   nil,   nil,   nil,
   477,   477,   477,   477,   nil,   nil,   nil,   nil,   nil,   477,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   477,   477,   nil,
   477,   477,   477,   477,   477,   477,   477,   477,   477,   477,
   477,   478,   478,   477,   nil,   478,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   478,   478,   nil,   478,   478,   478,   478,
   478,   478,   478,   nil,   nil,   478,   478,   nil,   nil,   nil,
   478,   478,   478,   478,   nil,   nil,   nil,   nil,   nil,   478,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   478,   478,   nil,
   478,   478,   478,   478,   478,   478,   478,   478,   478,   478,
   478,   489,   489,   478,   nil,   489,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   489,   489,   nil,   489,   489,   489,   489,
   489,   489,   489,   nil,   nil,   489,   489,   nil,   nil,   nil,
   489,   489,   489,   489,   nil,   nil,   nil,   nil,   nil,   489,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   489,   489,   nil,
   489,   489,   489,   489,   489,   489,   489,   489,   489,   489,
   489,   490,   490,   489,   nil,   490,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   490,   490,   nil,   490,   490,   490,   490,
   490,   490,   490,   nil,   nil,   490,   490,   nil,   nil,   nil,
   490,   490,   490,   490,   nil,   nil,   nil,   nil,   nil,   490,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   490,   490,   nil,
   490,   490,   490,   490,   490,   490,   490,   490,   490,   490,
   490,   517,   517,   490,   nil,   517,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   517,   517,   nil,   517,   517,   517,   517,
   517,   517,   517,   nil,   nil,   517,   517,   nil,   nil,   nil,
   517,   517,   517,   517,   nil,   nil,   nil,   nil,   nil,   517,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   517,   517,   nil,
   517,   517,   517,   517,   517,   517,   517,   517,   517,   517,
   517,   518,   518,   517,   nil,   518,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   518,   518,   nil,   518,   518,   518,   518,
   518,   518,   518,   nil,   nil,   518,   518,   nil,   nil,   nil,
   518,   518,   518,   518,   nil,   nil,   nil,   nil,   nil,   518,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   518,   518,   nil,
   518,   518,   518,   518,   518,   518,   518,   518,   518,   518,
   518,   524,   524,   518,   nil,   524,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   524,   524,   nil,   524,   524,   524,   524,
   524,   524,   524,   nil,   nil,   524,   524,   nil,   nil,   nil,
   524,   524,   524,   524,   nil,   nil,   nil,   nil,   nil,   524,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   524,   524,   nil,
   524,   524,   524,   524,   524,   524,   524,   524,   524,   524,
   524,   525,   525,   524,   nil,   525,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   525,   525,   nil,   525,   525,   525,   525,
   525,   525,   525,   nil,   nil,   525,   525,   nil,   nil,   nil,
   525,   525,   525,   525,   nil,   nil,   nil,   nil,   nil,   525,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   525,   525,   nil,
   525,   525,   525,   525,   525,   525,   525,   525,   525,   525,
   525,   877,   877,   525,   nil,   877,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   877,   877,   nil,   877,   877,   877,   877,
   877,   877,   877,   nil,   nil,   877,   877,   nil,   nil,   nil,
   877,   877,   877,   877,   nil,   nil,   nil,   nil,   nil,   877,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   877,   877,   nil,
   877,   877,   877,   877,   877,   877,   877,   877,   877,   877,
   877,   878,   878,   877,   nil,   878,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   878,   878,   nil,   878,   878,   878,   878,
   878,   878,   878,   nil,   nil,   878,   878,   nil,   nil,   nil,
   878,   878,   878,   878,   nil,   nil,   nil,   nil,   nil,   878,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   878,   878,   nil,
   878,   878,   878,   878,   878,   878,   878,   878,   878,   878,
   878,   915,   915,   878,   nil,   915,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   915,   915,   nil,   915,   915,   915,   915,
   915,   915,   915,   nil,   nil,   915,   915,   nil,   nil,   nil,
   915,   915,   915,   915,   nil,   nil,   nil,   nil,   nil,   915,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   915,   915,   nil,
   915,   915,   915,   915,   915,   915,   915,   915,   915,   915,
   915,   nil,   nil,   915,   251,   251,   251,   251,   251,   251,
   251,   251,   251,   251,   251,   nil,   251,   251,   nil,   nil,
   251,   251,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   251,   nil,   251,   nil,
   251,   251,   251,   251,   251,   251,   251,   nil,   251,   nil,
   406,   406,   406,   406,   406,   406,   406,   406,   406,   406,
   406,   nil,   406,   406,   nil,   251,   406,   406,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   406,   nil,   406,   nil,   406,   406,   406,   406,
   406,   406,   406,   nil,   406,   nil,   445,   445,   445,   445,
   445,   445,   445,   445,   445,   445,   445,   nil,   445,   445,
   nil,   406,   445,   445,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   445,   nil,
   445,   nil,   445,   445,   445,   445,   445,   445,   445,   nil,
   445,   nil,   491,   491,   491,   491,   491,   491,   491,   491,
   491,   491,   491,   nil,   491,   491,   445,   445,   491,   491,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   491,   nil,   491,   nil,   491,   491,
   491,   491,   491,   491,   491,   nil,   491,   nil,   619,   619,
   619,   619,   619,   619,   619,   619,   619,   619,   619,   nil,
   619,   619,   nil,   491,   619,   619,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   619,   nil,   619,   nil,   619,   619,   619,   619,   619,   619,
   619,   nil,   619,   nil,   697,   697,   697,   697,   697,   697,
   697,   697,   697,   697,   697,   nil,   697,   697,   nil,   619,
   697,   697,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   697,   nil,   697,   nil,
   697,   697,   697,   697,   697,   697,   697,   nil,   697,   nil,
   700,   700,   700,   700,   700,   700,   700,   700,   700,   700,
   700,   nil,   700,   700,   nil,   697,   700,   700,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   700,   nil,   700,   nil,   700,   700,   700,   700,
   700,   700,   700,   nil,   700,   nil,   704,   704,   704,   704,
   704,   704,   704,   704,   704,   704,   704,   nil,   704,   704,
   nil,   700,   704,   704,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   704,   nil,
   704,   nil,   704,   704,   704,   704,   704,   704,   704,   nil,
   704,   nil,   706,   706,   706,   706,   706,   706,   706,   706,
   706,   706,   706,   nil,   706,   706,   nil,   704,   706,   706,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   706,   nil,   706,   nil,   706,   706,
   706,   706,   706,   706,   706,   nil,   706,   nil,   709,   709,
   709,   709,   709,   709,   709,   709,   709,   709,   709,   nil,
   709,   709,   nil,   706,   709,   709,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   709,   nil,   709,   nil,   709,   709,   709,   709,   709,   709,
   709,   nil,   709,   nil,   711,   711,   711,   711,   711,   711,
   711,   711,   711,   711,   711,   nil,   711,   711,   nil,   709,
   711,   711,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   711,   nil,   711,   nil,
   711,   711,   711,   711,   711,   711,   711,   nil,   711,   nil,
   795,   795,   795,   795,   795,   795,   795,   795,   795,   795,
   795,   nil,   795,   795,   nil,   711,   795,   795,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   795,   nil,   795,   nil,   795,   795,   795,   795,
   795,   795,   795,   nil,   795,   nil,   797,   797,   797,   797,
   797,   797,   797,   797,   797,   797,   797,   nil,   797,   797,
   nil,   795,   797,   797,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   797,   nil,
   797,   nil,   797,   797,   797,   797,   797,   797,   797,   nil,
   797,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   797 ]

racc_action_pointer = [
     0,    80,   nil,    33,   849,  5297, 20962, 21086,     7,    30,
    11,    53,   139,   521,   300,   374,   nil,    61,  5422, 13696,
   266,   nil,  5547,  5672,  5797,    76,   348,   733,   858,   nil,
  1672,  5922,  6047,   nil,   197,   342,   304,   431,  6180,  6305,
  6430,   243,   548,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   983,  1797,  6555,  6680,  6805,     0,   nil,  6930,  7055,   nil,
   nil,  7180,  7313,  7438,  7563, 21458,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   580,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,     0,   nil,   nil,   130,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   383,   nil,  7688,   nil,   nil,   nil,  7821,  7946,  8071,  8196,
  8321,  1121,   nil,   604,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   283,   nil,  1922,  2047,  8446,  8571,  8696,
  8821, 21630, 21690,   nil,   nil,  8946,  9071,  9196,  9321,  9446,
   nil,   nil,   651,    54,   380,   107,   303,   408,   nil,  9571,
  2172,   415,  9696,  9821,  9946, 10071, 10196, 10321, 10446, 10571,
 10696, 10821, 10946, 11071, 11196, 11321, 11446, 11571, 11696, 11821,
 11946, 12071, 12196, 12321, 12446, 12571, 12696, 12821,   nil,   nil,
   nil, 22638,   nil,   388,   394,   nil, 12946,   446, 13071,   nil,
   nil,   nil,   nil, 13196,   nil,   nil, 21750, 21810,   462, 13321,
 13446,   nil,   nil,   nil,   nil,   nil,   nil,   nil, 13571,   425,
  2297,   439,   482,   445, 13696,  2422,   684,   770,   552,   803,
   529,   529,   355,   nil,   569,   474,   539, 13829,   nil,   484,
   618,   622,   809,   nil,   628,   nil, 13954,   691,   693,   585,
   nil,   158,   269,   629,   612,   270,   639,   nil,   nil,   599,
    48,   136, 14079, 14204,    84,   711,   617,    65,   866,   697,
    89,   730,   nil,   nil,   356,   418,   137,   nil,   895,   nil,
    55, 14329,   nil,   nil,   167,   419,   447,   513,   577,   582,
   707,   711,   713,   nil,   744,   nil, 14454,   nil,   271,   341,
   347,   397,    39,   405,   nil,  1218,   nil,   nil,   nil,   nil,
   nil,   nil, 21210,   nil,   nil,   nil,   nil,   643,   655,   nil,
   nil,  6048,   nil, 14579,   642,   nil,   649,   nil,   nil,  7180,
   666,   nil,   614,   651,  1113,   nil,   nil,   nil,   239,   709,
   348,   nil,   nil,  1238,  1368,   nil, 22684,   nil,   nil,   nil,
   115,   nil,   723, 21870, 21930, 14704,   242, 14829, 14954, 15079,
  2172,  2297,   562,   691,   748,   758,   783,   785,  1557,  3672,
  3797,  2422,  1189,  1319,  2547,  2672,  2797,  2922,  3047,  3172,
  3297,   941,  1128,  3422,  3547, 22730,   398,   nil, 15204,   nil,
 15329,   nil, 15454,   nil,   nil,  1498,   nil,   nil,  1553,   nil,
   nil,   727,   nil,   nil, 15579,   456,   460,   730,   nil,   731,
   732,   nil,   nil,   740, 15704,   747, 15829, 21990, 22050,   928,
   788,   nil,   nil, 15954,   749,   nil, 16079, 16204, 16329, 22110,
 22170, 22776, 16454,   867, 16579,   nil,   757,   nil, 16704,   nil,
   nil, 16829,   nil,   nil,   nil,   nil,  1077,  2547,   891,   nil,
  2672,   119,   123,   901,   914, 16954, 17079, 22230, 22290,    98,
   nil,   nil,  1022,   nil, 22350, 22410, 17204,   nil,   nil, 17329,
   406,   316,  2797,  1300,   nil,   nil,   nil,   283,   nil,   nil,
   nil,  1333,   nil,   nil,   nil,   821,   nil,   nil,    61,   nil,
   nil,   819,   nil,   nil, 21334,   nil,   nil, 17454,   828, 17579,
 17704,   746,   873, 17829, 17954,   873,   nil,   nil, 18079,   882,
   nil, 18204,   nil,   nil,   268,   272,   473,   603,   854,  7688,
   853,   nil,   665,   nil,  2922,   853,   nil,   899, 18329,   nil,
   nil,   nil,   nil,   nil,   nil,   488,   nil, 20829,    -6,   865,
   nil,   872,   nil,  3047,  3172,   nil,   361, 18454,   871,   nil,
   876,   198,   277,   914,   497,  1058,   915,   877, 18579, 22822,
   947,   948,   335,  1007,   nil,  3297,   887,   931,   nil,   nil,
   nil,   379,   135,  1430,   896,   899,   900,   902,   nil,   nil,
   nil,   nil,   nil,   nil,   243,   982,   nil,   858,   nil,   nil,
   nil,   nil,   989,   nil,   nil,   990,   624,   nil,  1028,   nil,
   nil,   nil,   nil,  1038,   nil,   143,   922,   141,   144,   171,
   247, 18704,   741,  1121,   nil,   938,  3422,   625,   nil,   nil,
  1059,  3547,  1517,   724, 21570,   nil,   nil,   nil,   nil,   nil,
   nil,  3672,   nil,   nil,   nil,   nil,   nil, 22868,   942, 18829,
 22914, 18954,   nil,   nil, 22960,   nil, 23006,   nil,   nil, 23052,
   nil, 23098, 19079, 19204, 19329,   402, 19454,   943,   945,   946,
   nil,   972,   951,   728,   389,   nil,  1075,   nil, 19579,   nil,
  3797, 19704,   nil,   nil,   966,   982,  1087,   968,   498,   nil,
   nil,   nil,  3922,   nil,   nil,   163, 19829,   nil,   nil,   nil,
   nil,   nil,   967,   nil,  1547,   nil,  5935,   nil,   nil,  1597,
  1049, 19954,   nil,   nil,   920,   nil,   973,   538,  1016,   978,
   nil,   nil,  1096,   nil, 20079,  1098,  4047,  4172,   nil, 20204,
  4297,   156,   251,   nil,  1110,   nil,  4422,   nil,  1114,   998,
   nil,   nil,  1024,  1008,   nil, 23144, 20329, 23190,  7326,   nil,
  7451,   nil,   nil,  1694,   nil,  1033,  1014,   nil,   nil,   nil,
 20454,   nil,  1015,  1033,  1017,   nil,  1019,   nil,   nil,   nil,
   nil,  4547,   164,  1024,  1103,   165,   nil,  4672,  4797,  1042,
  1049,  1070,   nil,   nil,  1072,  1079,   nil,  1083,   nil,   nil,
  1096,   981,  1099,   865,   nil,   nil,   166,   nil,  1220,  1226,
   nil,   215,   nil,   nil,  1227,   nil,   nil,  7576,   nil,  1108,
  1112,  1120,  1124,   nil,  1133,   nil,   853,  1150,  1212,   nil,
 20579,   nil,   nil, 20704,   nil,  1261, 20829, 22470, 22530,   172,
  1157,  1261,   nil, 13842,   nil,   nil,  1723,   nil,  1819,   nil,
  1848,   nil,   nil,   nil,   596,  1116,  1144,  4922,   nil,   nil,
   nil,   nil,   nil,  5047,   nil,  5172, 15717,   nil,   nil,  1944,
   nil,  1973,   nil,   nil,   nil, 22590,   nil,  1147,   nil,  1156,
   173,   207,   271,   274,   nil,   nil,  1154,  1155,  1157,  1159,
  1161,  1466,  1166,  1492,   678,  1287,  1289,  1171,  1172,  1173,
  1174,  1229,  1230,   nil,   209,   nil,  2069,   nil,   nil,   nil,
  1658,  1194,   nil,   nil,   nil,   nil,  2098,   nil,   nil,   nil,
  1195,  1197,  1198,   nil,   nil ]

racc_action_default = [
    -4,  -552,    -1,  -538,    -5,  -552,  -552,  -552,  -552,  -552,
  -552,  -552,  -552,  -552,  -273,   -32,   -33,  -540,  -552,   -38,
   -40,   -41,  -283,  -319,  -320,   -45,  -248,  -248,  -248,   -58,
    -4,   -62,   -70,   -72,  -552,  -465,  -552,  -552,  -552,  -552,
  -552,  -540,  -225,  -266,  -267,  -268,  -269,  -270,  -271,  -272,
  -526,    -4,  -552,  -551,  -518,  -291,  -293,  -552,  -552,  -297,
  -300,  -538,  -552,  -552,  -552,  -552,  -321,  -322,  -324,  -325,
  -414,  -415,  -416,  -417,  -418,  -433,  -421,  -422,  -435,  -437,
  -426,  -431,  -447,  -435,  -449,  -450,  -524,  -454,  -455,  -525,
  -457,  -458,  -459,  -460,  -461,  -462,  -463,  -464,  -467,  -468,
  -552,    -3,  -539,  -547,  -548,  -549,  -552,  -552,  -552,  -552,
  -552,    -7,    -8,  -552,   -99,  -100,  -101,  -102,  -103,  -104,
  -105,  -106,  -107,  -111,  -112,  -113,  -114,  -115,  -116,  -117,
  -118,  -119,  -120,  -121,  -122,  -123,  -124,  -125,  -126,  -127,
  -128,  -129,  -130,  -131,  -132,  -133,  -134,  -135,  -136,  -137,
  -138,  -139,  -140,  -141,  -142,  -143,  -144,  -145,  -146,  -147,
  -148,  -149,  -150,  -151,  -152,  -153,  -154,  -155,  -156,  -157,
  -158,  -159,  -160,  -161,  -162,  -163,  -164,  -165,  -166,  -167,
  -168,  -169,  -170,  -171,  -172,  -173,  -174,  -175,  -176,  -177,
  -178,  -179,  -180,   -13,  -108,    -4,    -4,  -552,  -552,  -552,
  -234,  -552,  -552,  -536,  -537,  -552,  -552,  -552,  -552,  -540,
  -541,   -37,  -552,  -465,  -552,  -273,  -552,  -552,  -217,  -552,
    -4,  -552,  -552,  -552,  -552,  -552,  -552,  -552,  -552,  -552,
  -552,  -552,  -552,  -552,  -552,  -552,  -552,  -552,  -552,  -552,
  -552,  -552,  -552,  -552,  -552,  -552,  -552,  -552,  -384,  -386,
   -42,  -226,  -236,  -258,  -258,  -241,  -552,  -259,  -552,  -283,
  -319,  -320,  -520,  -552,   -43,   -44,  -552,  -552,   -50,  -234,
  -552,  -290,  -389,  -398,  -400,   -56,  -395,   -57,  -540,   -60,
    -4,  -540,  -552,   -63,   -66,    -4,   -78,  -552,  -552,   -85,
  -286,  -540,  -552,  -323,  -396,  -552,   -68,  -552,   -74,  -280,
  -451,  -452,  -552,  -202,  -203,  -218,  -552,  -406,  -552,  -276,
  -227,  -544,  -544,  -552,  -552,  -544,  -552,  -292,  -376,   -39,
  -552,  -552,  -552,  -552,  -538,  -552,  -539,  -465,  -552,  -552,
  -273,  -552,  -335,  -336,   -94,   -95,  -552,   -97,  -552,  -273,
  -552,  -552,  -465,  -312,   -99,  -100,  -140,  -141,  -142,  -158,
  -163,  -170,  -173,  -314,  -552,  -516,  -552,  -419,  -552,  -552,
  -552,  -552,  -552,  -552,   965,    -6,  -550,   -14,   -15,   -16,
   -17,   -18,  -552,   -10,   -11,   -12,  -109,  -552,  -552,   -21,
   -29,  -181,  -259,  -552,  -552,   -22,   -30,   -31,   -23,  -183,
  -540,  -235,  -527,  -528,  -248,  -393,  -529,  -530,  -527,  -248,
  -528,  -392,  -394,  -529,  -530,   -28,  -191,   -34,   -35,   -36,
  -540,  -289,  -552,  -552,  -552,  -234,  -280,  -552,  -552,  -552,
  -192,  -193,  -194,  -195,  -196,  -197,  -198,  -199,  -204,  -205,
  -206,  -207,  -208,  -209,  -210,  -211,  -212,  -213,  -214,  -215,
  -216,  -219,  -220,  -221,  -222,  -540,  -365,  -237,  -257,  -238,
  -257,  -255,  -552,  -260,  -523,  -248,  -527,  -528,  -248,   -48,
   -51,  -540,  -249,  -250,  -251,  -365,  -365,  -540,  -285,  -540,
   -59,  -278,   -75,   -64,  -552,  -540,  -234,  -552,  -552,   -84,
  -552,  -451,  -452,  -552,   -71,   -76,  -552,  -552,  -552,  -552,
  -552,  -223,  -552,  -551,  -551,  -275,  -540,  -228,  -546,  -545,
  -230,  -546,  -281,  -282,  -519,  -294,  -488,    -4,  -326,  -327,
    -4,  -552,  -552,  -552,  -552,  -552,  -234,  -552,  -552,  -280,
  -305,   -94,   -95,   -96,  -552,  -552,  -234,  -308,  -469,  -552,
  -552,  -552,    -4,  -488,  -316,  -534,  -535,  -540,  -420,  -434,
  -439,  -552,  -441,  -423,  -436,  -552,  -438,  -425,  -552,  -428,
  -430,  -552,  -448,    -9,  -552,   -19,   -20,  -552,  -265,  -552,
  -552,  -397,  -552,  -552,  -552,   -52,  -233,  -390,  -552,   -54,
  -391,  -552,  -288,  -542,  -527,  -528,  -527,  -528,  -540,  -181,
  -552,  -366,  -370,  -368,    -4,  -258,  -256,  -261,  -552,  -521,
  -522,   -46,  -387,   -47,  -388,  -365,  -231,   -38,  -552,  -258,
  -247,  -540,  -253,    -4,    -4,  -284,   -61,  -552,   -67,   -73,
  -540,  -527,  -528,  -232,  -531,   -83,  -552,   -69,  -552,  -190,
  -200,  -201,  -540,  -551,  -333,    -4,  -407,  -551,  -408,  -409,
  -277,  -544,  -552,  -488,  -370,  -515,  -515,  -515,  -487,  -489,
  -490,  -491,  -492,  -493,  -494,  -552,  -497,  -552,  -499,  -505,
  -507,  -508,  -510,  -511,  -512,  -552,  -551,  -328,  -551,  -298,
  -329,  -330,  -301,  -552,  -304,  -552,  -540,  -527,  -528,  -531,
  -279,  -552,   -94,   -95,   -98,  -540,    -4,  -552,  -471,  -310,
  -552,    -4,  -488,  -552,  -552,  -517,  -440,  -443,  -444,  -445,
  -446,    -4,  -424,  -427,  -429,  -432,  -110,  -182,  -263,  -552,
  -184,  -552,  -543,   -25,  -186,   -26,  -187,   -53,   -27,  -188,
   -55,  -189,  -552,  -552,  -552,  -397,  -552,  -515,  -515,  -515,
  -364,  -552,  -370,  -552,  -494,  -503,  -552,  -239,  -257,  -262,
    -4,  -552,  -244,  -252,  -540,  -552,  -552,   -65,  -397,   -77,
  -287,    -2,    -4,  -413,  -334,  -552,  -552,  -411,  -229,  -377,
  -380,  -382,  -370,  -379,  -552,  -480,  -552,  -483,  -485,  -552,
  -552,  -552,  -496,  -337,  -552,  -339,  -341,  -348,  -494,  -540,
  -509,  -513,  -552,  -331,  -552,  -552,    -4,    -4,  -303,  -552,
    -4,  -397,  -552,  -397,  -552,  -470,    -4,  -313,  -552,  -540,
  -473,  -317,  -552,  -264,   -24,  -185,  -552,  -224,  -355,  -357,
  -552,  -360,  -362,  -552,  -367,  -552,  -371,  -372,  -374,  -375,
  -552,  -385,  -258,  -552,  -258,  -243,  -258,  -254,  -399,  -401,
  -412,    -4,  -465,  -552,  -552,  -273,  -410,    -4,    -4,  -540,
  -515,  -515,  -500,  -514,  -515,  -515,  -506,  -515,  -495,  -501,
  -540,  -552,  -346,  -552,  -498,  -295,  -552,  -296,  -552,  -552,
  -261,  -551,  -306,  -309,  -552,  -315,  -472,  -488,  -442,  -515,
  -515,  -515,  -515,  -504,  -515,  -369,  -552,  -502,  -552,  -240,
  -257,   -49,  -242,  -257,  -245,  -406,  -234,  -552,  -552,  -280,
  -552,  -552,  -378,  -552,  -476,  -478,  -552,  -481,  -552,  -484,
  -552,  -486,  -338,  -340,  -344,  -552,  -349,    -4,  -299,  -302,
  -402,  -403,  -404,    -4,  -311,    -4,  -552,  -352,  -354,  -552,
  -358,  -552,  -361,  -363,  -373,  -552,  -280,  -258,  -405,  -540,
  -527,  -528,  -531,  -279,  -381,  -383,  -515,  -515,  -515,  -515,
  -342,  -552,  -347,  -552,  -551,  -552,  -552,  -515,  -515,  -515,
  -515,  -279,  -531,  -246,  -397,  -474,  -552,  -477,  -479,  -482,
  -552,  -345,  -332,  -307,  -318,  -350,  -552,  -353,  -356,  -359,
  -515,  -343,  -515,  -475,  -351 ]

racc_goto_table = [
   117,   117,   208,    10,   201,   312,   315,   308,    10,   493,
   310,   390,   717,   293,   293,   353,   266,    12,   283,   507,
   510,   311,    12,   623,   320,   321,   306,   681,   324,   514,
   772,   105,   101,    10,   394,   399,   380,   387,   252,   252,
   252,   718,   589,   296,   293,   293,   293,    12,   112,   194,
   561,   523,   447,   449,    10,   120,   120,   807,   638,   117,
   460,   122,   122,   809,   634,   332,   272,   276,    12,   896,
   268,   275,   277,   367,   368,   369,   370,   659,   662,   586,
   461,   586,   250,   264,   265,   638,   832,   497,   500,   105,
   325,   504,   721,   589,   211,   600,   584,   836,   863,   455,
   458,   769,   359,   893,   111,    10,   592,   363,     1,   594,
   279,   340,   343,    10,   484,   603,   604,   281,   741,    12,
   102,   932,   193,   333,   719,   372,   595,    12,   554,   313,
   832,   602,   462,   539,   720,   546,   549,   601,   734,   355,
   314,   317,   505,   331,   753,   527,   354,   534,   496,   318,
   322,   776,   323,   777,   671,   903,   676,   951,   786,   532,
   533,   684,   857,   722,   806,   384,   384,   632,   749,   506,
   249,   827,   828,   446,   465,   466,   961,   900,   627,   745,
   357,   358,   360,   361,   548,   638,   835,   362,   775,   837,
   686,   752,   691,   830,   763,   893,   404,    13,    10,    10,
   914,   365,    13,   413,   863,   nil,   809,   nil,   nil,   371,
   nil,   nil,    12,    12,   nil,   212,   252,   663,   840,   212,
   212,   212,   832,    10,   836,   nil,   578,    13,   287,   287,
   862,   nil,   805,   864,   638,   nil,   nil,    12,   715,   674,
   789,   395,   nil,   nil,   nil,   730,   394,   399,    13,   212,
   nil,   nil,   nil,   nil,   212,   212,   nil,   859,   212,   328,
   338,   338,   829,   nil,   nil,   nil,   293,   nil,   nil,   nil,
   738,   nil,   nil,   379,   385,   388,   480,   nil,   477,   293,
   nil,   405,   nil,    10,   485,   252,   252,   610,    10,   511,
   512,   608,   nil,   489,   252,   473,   831,    12,   834,    13,
   617,   nil,    12,   212,   212,   212,   212,    13,   952,   nil,
   613,   763,   nil,   927,   842,   nil,   928,   nil,   929,   517,
   589,   nil,   613,   694,   nil,   nil,   781,   666,   nil,   nil,
   463,   nil,   272,   628,   276,   783,   938,   675,   467,   939,
   860,   940,   861,   nil,   nil,   535,   nil,   nil,   852,   nil,
   613,   905,   105,   513,   nil,   nil,   nil,   707,   613,   586,
   279,   710,   815,   nil,   780,   279,   117,   469,   528,   nil,
   nil,   nil,   475,   nil,   nil,   562,   960,   nil,   nil,   nil,
   nil,   901,   nil,   nil,   727,   nil,   962,   nil,   763,   nil,
   763,   nil,    13,    13,   212,   212,   212,   212,   732,   nil,
   585,   nil,   212,   212,   212,   212,   212,   748,   404,   638,
   nil,   nil,   nil,   nil,   553,   nil,   599,    13,   nil,   nil,
   nil,   120,   nil,   nil,   737,   926,   nil,   122,   nil,   nil,
   580,   252,   nil,   nil,   566,   nil,   nil,   nil,   565,   570,
   nil,   930,   763,   569,   821,   nil,   nil,   nil,   937,   nil,
   631,   629,   nil,   395,   nil,   nil,   293,   nil,   nil,   nil,
   nil,   485,   384,   nil,   nil,   293,   212,   212,   nil,   nil,
   485,   nil,   404,   nil,   nil,   212,   nil,    13,   763,   nil,
   763,   287,    13,   665,   404,   nil,   nil,   nil,   680,   nil,
   nil,   nil,   252,   nil,   287,   566,   677,   763,   566,   591,
   nil,   586,   593,   nil,   586,   nil,   nil,   nil,   nil,   nil,
    10,   nil,   404,    10,   nil,   nil,   nil,   395,   404,   212,
   212,   nil,   nil,   nil,    12,   nil,   nil,    12,   nil,   395,
   nil,   nil,   252,   nil,   nil,    10,   nil,   nil,   212,   660,
   660,   nil,   252,   nil,   nil,   897,   nil,   nil,   117,    12,
   nil,   nil,   nil,   212,   nil,   902,   nil,   395,   678,   679,
   nil,   nil,   nil,   562,   nil,   395,   nil,    14,   nil,   nil,
   nil,   nil,    14,   nil,   nil,   nil,   nil,   918,     2,   944,
   743,   nil,   nil,   nil,   747,   nil,   nil,    10,   nil,   293,
   nil,   nil,   nil,   nil,   485,   562,   696,    14,   289,   289,
   293,    12,   nil,   120,   nil,   739,    10,    10,   282,   122,
   nil,   869,   212,   872,   nil,   874,   nil,   nil,    14,   nil,
    12,    12,   nil,   nil,   nil,   nil,   nil,   nil,    10,   330,
   339,   339,   784,   nil,   791,   nil,   nil,   788,   782,   703,
   705,   nil,    12,   nil,   708,   nil,   nil,   nil,   nil,   nil,
   nil,   562,   nil,   566,   nil,   nil,   570,   nil,   nil,   nil,
   562,   212,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    14,
   nil,   287,   nil,   212,   nil,   nil,   nil,    14,   117,    10,
   287,   nil,   nil,   816,    10,   nil,   nil,   919,   nil,   212,
   nil,   nil,   nil,    12,    10,   nil,   812,   nil,    12,   814,
   nil,   nil,   nil,   nil,    13,   785,   nil,    13,    12,   nil,
   613,   790,   nil,   212,   nil,   nil,   943,   nil,   nil,   nil,
   nil,   nil,   nil,   212,   nil,   nil,   212,   nil,   293,    13,
   nil,   nil,   nil,    10,   nil,   nil,   nil,   nil,   nil,   411,
   nil,   846,   854,   nil,   nil,    10,   nil,    12,   nil,   826,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    12,
   212,   212,    14,    14,   nil,   212,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   377,   378,   nil,   nil,   794,   nil,    10,
    10,    13,   nil,    10,   nil,   nil,   nil,    14,   nil,    10,
   nil,   nil,   293,    12,    12,   nil,   nil,    12,   282,   nil,
    13,    13,   nil,    12,   287,   nil,   nil,   nil,   468,   nil,
   660,   470,   nil,   nil,   877,   287,   nil,   nil,   nil,   nil,
   nil,   nil,    13,   nil,    10,   917,   nil,   nil,   nil,   nil,
    10,    10,   nil,   nil,   nil,   nil,   nil,   nil,    12,   nil,
   nil,   nil,   nil,   nil,    12,    12,   nil,    14,   nil,   nil,
   nil,   289,    14,   nil,   nil,   nil,   nil,   nil,   282,   nil,
   nil,   936,   nil,   282,   289,   nil,   nil,   nil,   212,   nil,
   nil,   nil,   404,    13,   nil,   nil,   nil,   nil,    13,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    13,   nil,
   nil,   nil,   252,   nil,   nil,   nil,   nil,   nil,   212,   nil,
    10,   nil,   nil,   nil,   562,   nil,    10,   nil,    10,   404,
   nil,   nil,   nil,   nil,    12,   nil,   nil,   395,   nil,   nil,
    12,   nil,    12,   nil,   nil,   nil,   nil,    13,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,   nil,    13,
   572,   nil,    26,   823,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    26,   nil,   nil,   nil,    26,
    26,    26,   nil,   nil,   nil,   nil,   nil,    26,   nil,   nil,
   nil,   212,   nil,    13,    13,   nil,   nil,    13,   nil,   nil,
   nil,   nil,   nil,    13,   nil,   nil,   nil,   nil,    26,    26,
   nil,   596,   nil,   nil,    26,    26,   nil,   605,    26,   606,
   nil,   nil,   nil,   nil,   nil,   609,   nil,   867,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    13,   nil,
   nil,   nil,   nil,   nil,    13,    13,   630,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,
   nil,   289,   nil,    26,    26,    26,    26,    26,   nil,   nil,
   289,   nil,   nil,   nil,   nil,   nil,   309,   nil,   nil,   nil,
   nil,   319,   319,   nil,   nil,   319,   nil,   685,   nil,   nil,
   nil,   nil,   nil,   212,    14,   nil,   nil,    14,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   656,   nil,   nil,   658,   nil,
   nil,   nil,   nil,   nil,    13,   nil,   nil,   nil,   nil,    14,
    13,   nil,    13,   nil,   nil,   nil,   nil,   nil,   690,   nil,
   319,   319,   319,   319,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   316,   nil,
   nil,   733,    26,    26,    26,    26,    26,    26,   nil,   nil,
   nil,   nil,    26,    26,    26,    26,    26,   nil,   nil,   nil,
   nil,    14,   740,   nil,   nil,   nil,   nil,    26,   nil,   nil,
   nil,   nil,   726,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    14,    14,   nil,   nil,   289,   nil,   nil,   nil,   nil,   nil,
   nil,   735,   736,   nil,   nil,   289,   nil,   nil,   nil,   nil,
   nil,   nil,    14,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   744,   nil,   nil,    26,    26,   nil,   nil,
   407,   408,   409,   410,   nil,    26,   nil,    26,   nil,    35,
   nil,   nil,    26,   nil,    35,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    14,   nil,   nil,   nil,   nil,    14,    35,
   286,   286,   nil,   nil,   nil,   nil,   nil,   nil,    14,    26,
    26,   nil,   nil,   nil,   817,   nil,   nil,   nil,   nil,   792,
    35,   382,   386,   nil,   nil,   nil,   nil,   nil,    26,   nil,
   nil,   327,   342,   342,   342,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    26,   nil,   nil,   nil,    14,   nil,   844,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   813,    14,
   nil,   nil,   nil,   825,   nil,   nil,   nil,   nil,   nil,   856,
   820,    35,   nil,   nil,   nil,   nil,   319,   319,   nil,    35,
   451,   nil,   453,   nil,   nil,   nil,   nil,   454,   nil,   nil,
   nil,   nil,   nil,    14,    14,   531,   nil,    14,   nil,   nil,
   nil,   nil,    26,    14,   848,   849,   nil,   nil,   851,   882,
   537,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   892,   nil,   nil,   nil,   nil,   nil,   nil,   339,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    14,   nil,
   nil,   nil,   nil,   nil,    14,    14,   nil,   nil,   nil,   875,
   nil,    26,   nil,   nil,   nil,   880,   881,   nil,   nil,   nil,
   nil,   nil,   nil,    26,    35,    35,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,
   nil,   nil,   nil,   nil,    26,   nil,   nil,    26,   nil,   nil,
   nil,   nil,   nil,    26,   nil,   nil,   nil,   558,   nil,   nil,
   nil,   nil,   nil,    26,    14,   nil,    26,   nil,   309,    26,
    14,   nil,    14,   nil,   nil,   934,   nil,   nil,   nil,   nil,
   nil,   935,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   622,   nil,   nil,    35,
    26,    26,   nil,   286,    35,    26,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   286,   nil,   nil,   nil,
   nil,    26,   587,   nil,   316,   nil,   590,   nil,   nil,   nil,
   nil,   nil,   nil,   319,   nil,   nil,   nil,   nil,   598,   nil,
    26,    26,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    26,   nil,   nil,   nil,   nil,   nil,   626,   nil,
   nil,   nil,   587,   nil,   nil,   316,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   382,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,   nil,
   nil,   nil,   nil,    26,   nil,   nil,   nil,   nil,    26,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,   nil,
   nil,   nil,   nil,   698,   nil,   nil,   nil,   nil,    26,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   218,   nil,   nil,   nil,   251,   251,   251,   nil,   nil,
   nil,   nil,   729,   nil,   nil,   nil,   nil,    26,   nil,   nil,
   nil,   303,   304,   305,   nil,   319,   nil,   nil,   nil,    26,
   nil,   nil,   nil,   nil,   nil,   nil,   251,   251,   755,   757,
   758,   nil,   nil,   286,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   286,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    26,   nil,    26,    26,   nil,   nil,    26,   nil,   nil,
   nil,   nil,   nil,    26,   nil,   nil,    35,   nil,   nil,    35,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    35,   nil,   nil,   nil,   nil,   nil,   nil,    26,   nil,
   nil,   nil,   nil,   nil,    26,    26,   nil,   nil,   nil,   nil,
   799,   801,   802,   793,   nil,   nil,   nil,   nil,   319,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    35,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    26,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    35,    35,   nil,   nil,   286,   nil,   nil,   nil,
   381,   251,   389,   251,    26,   839,   nil,   286,   406,   nil,
    26,   nil,    26,   nil,    35,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   218,   850,   nil,   420,   421,   422,   423,   424,
   425,   426,   427,   428,   429,   430,   431,   432,   433,   434,
   435,   436,   437,   438,   439,   440,   441,   442,   443,   444,
   445,   nil,   nil,   884,   885,   nil,   nil,   887,   889,   251,
   891,   251,   nil,   nil,   nil,    35,   251,   nil,   nil,   nil,
    35,   nil,   251,   251,   nil,   nil,   nil,   nil,   nil,   nil,
    35,   251,   907,   908,   910,   912,   nil,   913,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   491,
   nil,   nil,   nil,   nil,   850,   nil,   nil,   587,   nil,    35,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    35,   nil,   nil,   nil,   822,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   945,
   947,   948,   949,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   955,   957,   958,   959,   nil,    35,    35,   nil,   nil,    35,
   nil,   nil,   nil,   nil,   nil,    35,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   963,   nil,   964,   251,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   342,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    35,   nil,   nil,   nil,   nil,   nil,    35,    35,   251,   nil,
   406,   579,   389,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   251,   nil,   251,   nil,   251,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   597,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   251,
   nil,   nil,   nil,   nil,   nil,   nil,    35,   nil,   nil,   619,
   620,   621,    35,   nil,    35,   nil,   nil,   251,   nil,   nil,
   nil,   251,   nil,   nil,   251,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   251,   251,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   251,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   697,   nil,   251,   700,   nil,   nil,   704,   706,   nil,   nil,
   nil,   709,   nil,   nil,   711,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   251,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   251,   nil,   795,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   704,   706,   709,   nil,   797,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   251,   nil,   nil,   251,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   251,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   251,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   795,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   251,   nil,   nil,   251,   nil,   nil,   251 ]

racc_goto_check = [
    50,    50,    27,    14,    21,    61,    61,     3,    14,     4,
    58,    19,   113,    56,    56,    49,    21,    17,    43,    82,
    82,    59,    17,     5,    13,    13,    27,    91,    13,    85,
    83,    89,     8,    14,    33,    33,    23,    23,    29,    29,
    29,   114,   155,    44,    56,    56,    56,    17,    11,    11,
    20,    47,    64,    64,    14,    53,    53,   121,   117,    50,
    35,    54,    54,   122,   126,    14,    62,    62,    17,   111,
    34,    34,    34,    13,    13,    13,    13,    84,    84,    65,
    19,    65,    31,    31,    31,   117,   150,    60,    60,    89,
     8,    60,   119,   155,    15,    65,    36,   151,   152,    33,
    33,   110,   141,   108,     9,    14,    63,   141,     1,    63,
    39,    48,    48,    14,    45,    36,    36,    40,     6,    17,
    10,   111,    12,    16,   115,    26,    37,    17,    55,    57,
   150,    66,    67,   145,   117,   145,   145,    69,    70,    77,
    78,    79,    81,    86,   119,    88,    92,    93,    94,    95,
    96,    97,    98,    99,   100,   101,   102,   111,   103,   104,
   105,   106,   107,   112,   120,    59,    59,   123,   124,   125,
   127,   128,   129,   130,   132,   133,   111,   134,   135,   136,
   139,   140,   142,   143,   144,   117,   113,   146,     5,   113,
   147,   126,   148,   149,   109,   108,    50,    18,    14,    14,
   121,     9,    18,    21,   152,   nil,   122,   nil,   nil,     9,
   nil,   nil,    17,    17,   nil,    18,    29,    85,   110,    18,
    18,    18,   150,    14,   151,   nil,    19,    18,    18,    18,
   113,   nil,   119,   113,   117,   nil,   nil,    17,    20,    47,
   126,    62,   nil,   nil,   nil,    36,    33,    33,    18,    18,
   nil,   nil,   nil,   nil,    18,    18,   nil,   114,    18,    18,
    18,    18,   119,   nil,   nil,   nil,    56,   nil,   nil,   nil,
    20,   nil,   nil,    15,    15,    15,    27,   nil,    21,    56,
   nil,    15,   nil,    14,    43,    29,    29,    19,    14,    13,
    13,    45,   nil,    21,    29,    44,   115,    17,   115,    18,
    45,   nil,    17,    18,    18,    18,    18,    18,    83,   nil,
    33,   109,   nil,   113,   109,   nil,   113,   nil,   113,    21,
   155,   nil,    33,   145,   nil,   nil,    20,    19,   nil,   nil,
    31,   nil,    62,    23,    62,    20,   113,    19,    31,   113,
   115,   113,   115,   nil,   nil,    21,   nil,   nil,    84,   nil,
    33,    91,    89,     8,   nil,   nil,   nil,    35,    33,    65,
    39,    35,    65,   nil,    82,    39,    50,    40,    89,   nil,
   nil,   nil,    40,   nil,   nil,    27,   113,   nil,   nil,   nil,
   nil,     5,   nil,   nil,    64,   nil,   113,   nil,   109,   nil,
   109,   nil,    18,    18,    18,    18,    18,    18,    64,   nil,
    61,   nil,    18,    18,    18,    18,    18,    60,    50,   117,
   nil,   nil,   nil,   nil,    11,   nil,    61,    18,   nil,   nil,
   nil,    53,   nil,   nil,    45,   115,   nil,    54,   nil,   nil,
    27,    29,   nil,   nil,    62,   nil,   nil,   nil,    34,    62,
   nil,   109,   109,    34,    82,   nil,   nil,   nil,   115,   nil,
    61,    58,   nil,    62,   nil,   nil,    56,   nil,   nil,   nil,
   nil,    43,    59,   nil,   nil,    56,    18,    18,   nil,   nil,
    43,   nil,    50,   nil,   nil,    18,   nil,    18,   109,   nil,
   109,    18,    18,    59,    50,   nil,   nil,   nil,     3,   nil,
   nil,   nil,    29,   nil,    18,    62,    13,   109,    62,    34,
   nil,    65,    34,   nil,    65,   nil,   nil,   nil,   nil,   nil,
    14,   nil,    50,    14,   nil,   nil,   nil,    62,    50,    18,
    18,   nil,   nil,   nil,    17,   nil,   nil,    17,   nil,    62,
   nil,   nil,    29,   nil,   nil,    14,   nil,   nil,    18,    89,
    89,   nil,    29,   nil,   nil,    82,   nil,   nil,    50,    17,
   nil,   nil,   nil,    18,   nil,    85,   nil,    62,    89,    89,
   nil,   nil,   nil,    27,   nil,    62,   nil,    22,   nil,   nil,
   nil,   nil,    22,   nil,   nil,   nil,   nil,     4,     2,    20,
    58,   nil,   nil,   nil,    58,   nil,   nil,    14,   nil,    56,
   nil,   nil,   nil,   nil,    43,    27,    11,    22,    22,    22,
    56,    17,   nil,    53,   nil,    43,    14,    14,     2,    54,
   nil,    64,    18,    64,   nil,    64,   nil,   nil,    22,   nil,
    17,    17,   nil,   nil,   nil,   nil,   nil,   nil,    14,    22,
    22,    22,     3,   nil,    49,   nil,   nil,     3,    13,    15,
    15,   nil,    17,   nil,    15,   nil,   nil,   nil,   nil,   nil,
   nil,    27,   nil,    62,   nil,   nil,    62,   nil,   nil,   nil,
    27,    18,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    22,
   nil,    18,   nil,    18,   nil,   nil,   nil,    22,    50,    14,
    18,   nil,   nil,    61,    14,   nil,   nil,    19,   nil,    18,
   nil,   nil,   nil,    17,    14,   nil,    59,   nil,    17,    59,
   nil,   nil,   nil,   nil,    18,    89,   nil,    18,    17,   nil,
    33,    89,   nil,    18,   nil,   nil,    64,   nil,   nil,   nil,
   nil,   nil,   nil,    18,   nil,   nil,    18,   nil,    56,    18,
   nil,   nil,   nil,    14,   nil,   nil,   nil,   nil,   nil,    41,
   nil,    13,     3,   nil,   nil,    14,   nil,    17,   nil,    14,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    17,
    18,    18,    22,    22,   nil,    18,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,     2,     2,   nil,   nil,    15,   nil,    14,
    14,    18,   nil,    14,   nil,   nil,   nil,    22,   nil,    14,
   nil,   nil,    56,    17,    17,   nil,   nil,    17,     2,   nil,
    18,    18,   nil,    17,    18,   nil,   nil,   nil,    41,   nil,
    89,    41,   nil,   nil,    21,    18,   nil,   nil,   nil,   nil,
   nil,   nil,    18,   nil,    14,    61,   nil,   nil,   nil,   nil,
    14,    14,   nil,   nil,   nil,   nil,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   nil,    17,    17,   nil,    22,   nil,   nil,
   nil,    22,    22,   nil,   nil,   nil,   nil,   nil,     2,   nil,
   nil,     3,   nil,     2,    22,   nil,   nil,   nil,    18,   nil,
   nil,   nil,    50,    18,   nil,   nil,   nil,   nil,    18,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,
   nil,   nil,    29,   nil,   nil,   nil,   nil,   nil,    18,   nil,
    14,   nil,   nil,   nil,    27,   nil,    14,   nil,    14,    50,
   nil,   nil,   nil,   nil,    17,   nil,   nil,    62,   nil,   nil,
    17,   nil,    17,   nil,   nil,   nil,   nil,    18,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    38,   nil,    18,
    41,   nil,    38,    18,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    38,   nil,   nil,   nil,    38,
    38,    38,   nil,   nil,   nil,   nil,   nil,    38,   nil,   nil,
   nil,    18,   nil,    18,    18,   nil,   nil,    18,   nil,   nil,
   nil,   nil,   nil,    18,   nil,   nil,   nil,   nil,    38,    38,
   nil,    41,   nil,   nil,    38,    38,   nil,    41,    38,    41,
   nil,   nil,   nil,   nil,   nil,    41,   nil,    18,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,
   nil,   nil,   nil,   nil,    18,    18,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    38,
   nil,    22,   nil,    38,    38,    38,    38,    38,   nil,   nil,
    22,   nil,   nil,   nil,   nil,   nil,    25,   nil,   nil,   nil,
   nil,    25,    25,   nil,   nil,    25,   nil,    41,   nil,   nil,
   nil,   nil,   nil,    18,    22,   nil,   nil,    22,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,     2,   nil,   nil,     2,   nil,
   nil,   nil,   nil,   nil,    18,   nil,   nil,   nil,   nil,    22,
    18,   nil,    18,   nil,   nil,   nil,   nil,   nil,    22,   nil,
    25,    25,    25,    25,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    24,   nil,
   nil,    41,    38,    38,    38,    38,    38,    38,   nil,   nil,
   nil,   nil,    38,    38,    38,    38,    38,   nil,   nil,   nil,
   nil,    22,    41,   nil,   nil,   nil,   nil,    38,   nil,   nil,
   nil,   nil,     2,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    22,    22,   nil,   nil,    22,   nil,   nil,   nil,   nil,   nil,
   nil,     2,     2,   nil,   nil,    22,   nil,   nil,   nil,   nil,
   nil,   nil,    22,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,     2,   nil,   nil,    38,    38,   nil,   nil,
    25,    25,    25,    25,   nil,    38,   nil,    38,   nil,    46,
   nil,   nil,    38,   nil,    46,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    22,   nil,   nil,   nil,   nil,    22,    46,
    46,    46,   nil,   nil,   nil,   nil,   nil,   nil,    22,    38,
    38,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,     2,
    46,    24,    24,   nil,   nil,   nil,   nil,   nil,    38,   nil,
   nil,    46,    46,    46,    46,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    38,   nil,   nil,   nil,    22,   nil,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,     2,    22,
   nil,   nil,   nil,    22,   nil,   nil,   nil,   nil,   nil,    41,
     2,    46,   nil,   nil,   nil,   nil,    25,    25,   nil,    46,
    24,   nil,    24,   nil,   nil,   nil,   nil,    24,   nil,   nil,
   nil,   nil,   nil,    22,    22,    25,   nil,    22,   nil,   nil,
   nil,   nil,    38,    22,     2,     2,   nil,   nil,     2,    41,
    25,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,    22,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    22,   nil,
   nil,   nil,   nil,   nil,    22,    22,   nil,   nil,   nil,     2,
   nil,    38,   nil,   nil,   nil,     2,     2,   nil,   nil,   nil,
   nil,   nil,   nil,    38,    46,    46,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    46,
   nil,   nil,   nil,   nil,    38,   nil,   nil,    38,   nil,   nil,
   nil,   nil,   nil,    38,   nil,   nil,   nil,    24,   nil,   nil,
   nil,   nil,   nil,    38,    22,   nil,    38,   nil,    25,    38,
    22,   nil,    22,   nil,   nil,     2,   nil,   nil,   nil,   nil,
   nil,     2,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    25,   nil,   nil,    46,
    38,    38,   nil,    46,    46,    38,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   nil,
   nil,    38,    24,   nil,    24,   nil,    24,   nil,   nil,   nil,
   nil,   nil,   nil,    25,   nil,   nil,   nil,   nil,    24,   nil,
    38,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    38,   nil,   nil,   nil,   nil,   nil,    24,   nil,
   nil,   nil,    24,   nil,   nil,    24,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    24,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    38,   nil,
   nil,   nil,   nil,    38,   nil,   nil,   nil,   nil,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    38,   nil,
   nil,   nil,   nil,    24,   nil,   nil,   nil,   nil,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    28,   nil,   nil,   nil,    28,    28,    28,   nil,   nil,
   nil,   nil,    24,   nil,   nil,   nil,   nil,    38,   nil,   nil,
   nil,    28,    28,    28,   nil,    25,   nil,   nil,   nil,    38,
   nil,   nil,   nil,   nil,   nil,   nil,    28,    28,   116,   116,
   116,   nil,   nil,    46,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    46,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    38,   nil,    38,    38,   nil,   nil,    38,   nil,   nil,
   nil,   nil,   nil,    38,   nil,   nil,    46,   nil,   nil,    46,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    46,   nil,   nil,   nil,   nil,   nil,   nil,    38,   nil,
   nil,   nil,   nil,   nil,    38,    38,   nil,   nil,   nil,   nil,
   116,   116,   116,    24,   nil,   nil,   nil,   nil,    25,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    46,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    46,    46,   nil,   nil,    46,   nil,   nil,   nil,
    28,    28,    28,    28,    38,    24,   nil,    46,    28,   nil,
    38,   nil,    38,   nil,    46,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    28,    24,   nil,    28,    28,    28,    28,    28,
    28,    28,    28,    28,    28,    28,    28,    28,    28,    28,
    28,    28,    28,    28,    28,    28,    28,    28,    28,    28,
    28,   nil,   nil,   116,   116,   nil,   nil,   116,   116,    28,
   116,    28,   nil,   nil,   nil,    46,    28,   nil,   nil,   nil,
    46,   nil,    28,    28,   nil,   nil,   nil,   nil,   nil,   nil,
    46,    28,   116,   116,   116,   116,   nil,   116,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    28,
   nil,   nil,   nil,   nil,    24,   nil,   nil,    24,   nil,    46,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    46,   nil,   nil,   nil,    46,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   116,
   116,   116,   116,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   116,   116,   116,   116,   nil,    46,    46,   nil,   nil,    46,
   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   116,   nil,   116,    28,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    46,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   nil,   nil,   nil,    46,    46,    28,   nil,
    28,    28,    28,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    28,   nil,    28,   nil,    28,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    28,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    28,
   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,    28,
    28,    28,    46,   nil,    46,   nil,   nil,    28,   nil,   nil,
   nil,    28,   nil,   nil,    28,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    28,    28,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    28,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    28,   nil,    28,    28,   nil,   nil,    28,    28,   nil,   nil,
   nil,    28,   nil,   nil,    28,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    28,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    28,   nil,    28,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    28,    28,    28,   nil,    28,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    28,   nil,   nil,    28,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    28,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    28,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    28,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    28,   nil,   nil,    28,   nil,   nil,    28 ]

racc_goto_pointer = [
   nil,   108,   578,   -44,  -298,  -470,  -505,   nil,    29,    99,
   117,    42,   115,   -33,     3,    76,    61,    17,   197,  -189,
  -340,    -9,   567,  -161,  1074,  1004,    13,   -15,  1633,    16,
   nil,    60,   nil,  -167,    44,  -208,  -350,  -333,   937,    80,
    87,   530,   nil,   -13,    11,  -183,  1219,  -285,    48,   -50,
    -6,   nil,   nil,    49,    55,  -248,   -18,    76,   -43,   -32,
  -224,   -48,    40,  -349,  -201,  -369,  -333,  -138,   nil,  -327,
  -464,   nil,   nil,   nil,   nil,   nil,   nil,    74,    86,    86,
   nil,  -176,  -301,  -626,  -434,  -296,    81,   nil,  -195,    28,
   nil,  -506,    81,  -207,  -161,    93,    91,  -508,    92,  -509,
  -366,  -697,  -371,  -521,  -184,  -193,  -373,  -629,  -738,  -453,
  -546,  -774,  -419,  -570,  -541,  -458,  1053,  -448,   nil,  -490,
  -559,  -666,  -660,  -339,  -464,  -149,  -442,   150,  -579,  -579,
   -75,   nil,   -99,   -99,  -674,  -316,  -448,   nil,   nil,   107,
   106,    24,   103,   103,  -177,  -225,   106,  -351,  -350,  -561,
  -668,  -659,  -702,   nil,   nil,  -408 ]

racc_goto_default = [
   nil,   nil,   307,   nil,   nil,   773,   nil,     3,   nil,     4,
   326,   nil,   nil,   nil,   216,    16,    11,   217,   302,   nil,
   nil,   525,   215,   nil,   257,    15,   nil,   412,    19,    20,
    21,   391,    25,   616,   nil,   nil,   nil,   nil,   292,    29,
   nil,   nil,    31,    34,    33,   nil,   213,   337,   nil,   119,
   397,   118,   121,    71,    72,   nil,    42,   nil,   624,   253,
   nil,   254,   402,   567,   nil,   255,   nil,   nil,   270,   nil,
   nil,    43,    44,    45,    46,    47,    48,    49,   nil,   271,
    55,   nil,   nil,   nil,   nil,   nil,   nil,    63,   nil,   508,
    64,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   765,   646,
   nil,   766,   nil,   635,   nil,   637,   nil,   833,   581,   nil,
   nil,   nil,   643,   nil,   nil,   nil,   683,   nil,   nil,   nil,
   nil,   401,   nil,   nil,   nil,   nil,   nil,    70,    73,    74,
   nil,   nil,   nil,   nil,   nil,   544,   nil,   nil,   nil,   636,
   648,   649,   725,   652,   655,   262 ]

racc_reduce_table = [
  0, 0, :racc_error,
  1, 139, :_reduce_none,
  4, 141, :_reduce_2,
  2, 140, :_reduce_3,
  0, 145, :_reduce_4,
  1, 145, :_reduce_5,
  3, 145, :_reduce_6,
  2, 145, :_reduce_7,
  0, 164, :_reduce_8,
  4, 147, :_reduce_9,
  3, 147, :_reduce_10,
  3, 147, :_reduce_11,
  3, 147, :_reduce_12,
  2, 147, :_reduce_13,
  3, 147, :_reduce_14,
  3, 147, :_reduce_15,
  3, 147, :_reduce_16,
  3, 147, :_reduce_17,
  3, 147, :_reduce_18,
  4, 147, :_reduce_19,
  4, 147, :_reduce_20,
  3, 147, :_reduce_21,
  3, 147, :_reduce_22,
  3, 147, :_reduce_23,
  6, 147, :_reduce_24,
  5, 147, :_reduce_25,
  5, 147, :_reduce_26,
  5, 147, :_reduce_27,
  3, 147, :_reduce_28,
  3, 147, :_reduce_29,
  3, 147, :_reduce_30,
  3, 147, :_reduce_31,
  1, 147, :_reduce_none,
  1, 163, :_reduce_none,
  3, 163, :_reduce_34,
  3, 163, :_reduce_35,
  3, 163, :_reduce_36,
  2, 163, :_reduce_37,
  1, 163, :_reduce_none,
  1, 151, :_reduce_none,
  1, 153, :_reduce_none,
  1, 153, :_reduce_none,
  2, 153, :_reduce_42,
  2, 153, :_reduce_43,
  2, 153, :_reduce_44,
  1, 168, :_reduce_none,
  4, 168, :_reduce_46,
  4, 168, :_reduce_47,
  0, 175, :_reduce_48,
  5, 173, :_reduce_49,
  2, 167, :_reduce_50,
  3, 167, :_reduce_51,
  4, 167, :_reduce_52,
  5, 167, :_reduce_53,
  4, 167, :_reduce_54,
  5, 167, :_reduce_55,
  2, 167, :_reduce_56,
  2, 167, :_reduce_57,
  1, 154, :_reduce_58,
  3, 154, :_reduce_59,
  1, 178, :_reduce_60,
  3, 178, :_reduce_61,
  1, 177, :_reduce_none,
  2, 177, :_reduce_63,
  3, 177, :_reduce_64,
  5, 177, :_reduce_65,
  2, 177, :_reduce_66,
  4, 177, :_reduce_67,
  2, 177, :_reduce_68,
  4, 177, :_reduce_69,
  1, 177, :_reduce_70,
  3, 177, :_reduce_71,
  1, 181, :_reduce_none,
  3, 181, :_reduce_73,
  2, 180, :_reduce_74,
  3, 180, :_reduce_75,
  1, 183, :_reduce_76,
  3, 183, :_reduce_77,
  1, 182, :_reduce_78,
  4, 182, :_reduce_79,
  3, 182, :_reduce_80,
  3, 182, :_reduce_81,
  3, 182, :_reduce_82,
  3, 182, :_reduce_83,
  2, 182, :_reduce_84,
  1, 182, :_reduce_85,
  1, 152, :_reduce_86,
  4, 152, :_reduce_87,
  3, 152, :_reduce_88,
  3, 152, :_reduce_89,
  3, 152, :_reduce_90,
  3, 152, :_reduce_91,
  2, 152, :_reduce_92,
  1, 152, :_reduce_93,
  1, 185, :_reduce_94,
  1, 185, :_reduce_none,
  2, 186, :_reduce_96,
  1, 186, :_reduce_97,
  3, 186, :_reduce_98,
  1, 187, :_reduce_none,
  1, 187, :_reduce_none,
  1, 187, :_reduce_none,
  1, 187, :_reduce_none,
  1, 187, :_reduce_none,
  1, 190, :_reduce_104,
  1, 190, :_reduce_none,
  1, 149, :_reduce_none,
  1, 149, :_reduce_none,
  1, 150, :_reduce_108,
  0, 193, :_reduce_109,
  4, 150, :_reduce_110,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  3, 166, :_reduce_181,
  5, 166, :_reduce_182,
  3, 166, :_reduce_183,
  5, 166, :_reduce_184,
  6, 166, :_reduce_185,
  5, 166, :_reduce_186,
  5, 166, :_reduce_187,
  5, 166, :_reduce_188,
  5, 166, :_reduce_189,
  4, 166, :_reduce_190,
  3, 166, :_reduce_191,
  3, 166, :_reduce_192,
  3, 166, :_reduce_193,
  3, 166, :_reduce_194,
  3, 166, :_reduce_195,
  3, 166, :_reduce_196,
  3, 166, :_reduce_197,
  3, 166, :_reduce_198,
  3, 166, :_reduce_199,
  4, 166, :_reduce_200,
  4, 166, :_reduce_201,
  2, 166, :_reduce_202,
  2, 166, :_reduce_203,
  3, 166, :_reduce_204,
  3, 166, :_reduce_205,
  3, 166, :_reduce_206,
  3, 166, :_reduce_207,
  3, 166, :_reduce_208,
  3, 166, :_reduce_209,
  3, 166, :_reduce_210,
  3, 166, :_reduce_211,
  3, 166, :_reduce_212,
  3, 166, :_reduce_213,
  3, 166, :_reduce_214,
  3, 166, :_reduce_215,
  3, 166, :_reduce_216,
  2, 166, :_reduce_217,
  2, 166, :_reduce_218,
  3, 166, :_reduce_219,
  3, 166, :_reduce_220,
  3, 166, :_reduce_221,
  3, 166, :_reduce_222,
  3, 166, :_reduce_223,
  6, 166, :_reduce_224,
  1, 166, :_reduce_none,
  1, 162, :_reduce_none,
  1, 195, :_reduce_none,
  2, 195, :_reduce_none,
  4, 195, :_reduce_229,
  2, 195, :_reduce_230,
  3, 200, :_reduce_231,
  0, 201, :_reduce_232,
  1, 201, :_reduce_none,
  0, 157, :_reduce_234,
  1, 157, :_reduce_none,
  1, 169, :_reduce_236,
  2, 169, :_reduce_237,
  2, 169, :_reduce_238,
  4, 169, :_reduce_239,
  6, 169, :_reduce_240,
  1, 169, :_reduce_241,
  4, 204, :_reduce_242,
  3, 204, :_reduce_243,
  2, 204, :_reduce_244,
  4, 204, :_reduce_245,
  6, 204, :_reduce_246,
  1, 204, :_reduce_247,
  0, 206, :_reduce_248,
  2, 172, :_reduce_249,
  1, 205, :_reduce_250,
  0, 207, :_reduce_251,
  3, 205, :_reduce_252,
  0, 208, :_reduce_253,
  4, 205, :_reduce_254,
  2, 203, :_reduce_255,
  2, 202, :_reduce_256,
  1, 202, :_reduce_257,
  0, 202, :_reduce_258,
  1, 197, :_reduce_259,
  2, 197, :_reduce_260,
  3, 197, :_reduce_261,
  4, 197, :_reduce_262,
  3, 161, :_reduce_263,
  4, 161, :_reduce_264,
  2, 161, :_reduce_265,
  1, 194, :_reduce_none,
  1, 194, :_reduce_none,
  1, 194, :_reduce_none,
  1, 194, :_reduce_none,
  1, 194, :_reduce_none,
  1, 194, :_reduce_none,
  1, 194, :_reduce_none,
  1, 194, :_reduce_none,
  1, 194, :_reduce_274,
  3, 194, :_reduce_275,
  0, 232, :_reduce_276,
  4, 194, :_reduce_277,
  3, 194, :_reduce_278,
  3, 194, :_reduce_279,
  2, 194, :_reduce_280,
  3, 194, :_reduce_281,
  3, 194, :_reduce_282,
  1, 194, :_reduce_283,
  4, 194, :_reduce_284,
  3, 194, :_reduce_285,
  1, 194, :_reduce_286,
  5, 194, :_reduce_287,
  4, 194, :_reduce_288,
  3, 194, :_reduce_289,
  2, 194, :_reduce_290,
  1, 194, :_reduce_none,
  2, 194, :_reduce_292,
  0, 233, :_reduce_293,
  3, 194, :_reduce_294,
  6, 194, :_reduce_295,
  6, 194, :_reduce_296,
  0, 234, :_reduce_297,
  0, 235, :_reduce_298,
  7, 194, :_reduce_299,
  0, 236, :_reduce_300,
  0, 237, :_reduce_301,
  7, 194, :_reduce_302,
  5, 194, :_reduce_303,
  4, 194, :_reduce_304,
  0, 238, :_reduce_305,
  0, 239, :_reduce_306,
  9, 194, :_reduce_307,
  0, 240, :_reduce_308,
  6, 194, :_reduce_309,
  0, 241, :_reduce_310,
  7, 194, :_reduce_311,
  0, 242, :_reduce_312,
  5, 194, :_reduce_313,
  0, 243, :_reduce_314,
  6, 194, :_reduce_315,
  0, 244, :_reduce_316,
  0, 245, :_reduce_317,
  9, 194, :_reduce_318,
  1, 194, :_reduce_319,
  1, 194, :_reduce_320,
  1, 194, :_reduce_321,
  1, 194, :_reduce_322,
  1, 156, :_reduce_none,
  1, 225, :_reduce_324,
  1, 228, :_reduce_325,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  2, 220, :_reduce_328,
  1, 222, :_reduce_none,
  1, 222, :_reduce_none,
  1, 221, :_reduce_none,
  5, 221, :_reduce_332,
  1, 143, :_reduce_none,
  2, 143, :_reduce_334,
  1, 224, :_reduce_none,
  1, 224, :_reduce_none,
  1, 246, :_reduce_none,
  3, 246, :_reduce_338,
  1, 249, :_reduce_339,
  3, 249, :_reduce_340,
  1, 248, :_reduce_none,
  4, 248, :_reduce_342,
  6, 248, :_reduce_343,
  3, 248, :_reduce_344,
  5, 248, :_reduce_345,
  2, 248, :_reduce_346,
  4, 248, :_reduce_347,
  1, 248, :_reduce_348,
  3, 248, :_reduce_349,
  6, 250, :_reduce_350,
  8, 250, :_reduce_351,
  4, 250, :_reduce_352,
  6, 250, :_reduce_353,
  4, 250, :_reduce_354,
  2, 250, :_reduce_none,
  6, 250, :_reduce_356,
  2, 250, :_reduce_357,
  4, 250, :_reduce_358,
  6, 250, :_reduce_359,
  2, 250, :_reduce_360,
  4, 250, :_reduce_361,
  2, 250, :_reduce_362,
  4, 250, :_reduce_363,
  1, 250, :_reduce_364,
  0, 174, :_reduce_365,
  1, 174, :_reduce_366,
  3, 256, :_reduce_367,
  1, 256, :_reduce_368,
  4, 256, :_reduce_369,
  0, 257, :_reduce_370,
  2, 257, :_reduce_371,
  1, 258, :_reduce_372,
  3, 258, :_reduce_373,
  1, 259, :_reduce_374,
  1, 259, :_reduce_none,
  0, 263, :_reduce_376,
  3, 219, :_reduce_377,
  4, 261, :_reduce_378,
  2, 261, :_reduce_379,
  0, 266, :_reduce_380,
  4, 262, :_reduce_381,
  0, 267, :_reduce_382,
  4, 262, :_reduce_383,
  0, 268, :_reduce_384,
  5, 265, :_reduce_385,
  2, 170, :_reduce_386,
  4, 170, :_reduce_387,
  4, 170, :_reduce_388,
  2, 218, :_reduce_389,
  4, 218, :_reduce_390,
  4, 218, :_reduce_391,
  3, 218, :_reduce_392,
  3, 218, :_reduce_393,
  3, 218, :_reduce_394,
  2, 218, :_reduce_395,
  1, 218, :_reduce_396,
  4, 218, :_reduce_397,
  0, 270, :_reduce_398,
  5, 217, :_reduce_399,
  0, 271, :_reduce_400,
  5, 217, :_reduce_401,
  5, 223, :_reduce_402,
  1, 272, :_reduce_403,
  1, 272, :_reduce_none,
  6, 142, :_reduce_405,
  0, 142, :_reduce_406,
  1, 273, :_reduce_407,
  1, 273, :_reduce_none,
  1, 273, :_reduce_none,
  2, 274, :_reduce_410,
  1, 274, :_reduce_none,
  2, 144, :_reduce_412,
  1, 144, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 210, :_reduce_417,
  1, 276, :_reduce_418,
  2, 276, :_reduce_419,
  3, 277, :_reduce_420,
  1, 277, :_reduce_421,
  1, 277, :_reduce_422,
  3, 211, :_reduce_423,
  4, 212, :_reduce_424,
  3, 213, :_reduce_425,
  0, 281, :_reduce_426,
  3, 281, :_reduce_427,
  1, 282, :_reduce_428,
  2, 282, :_reduce_429,
  3, 214, :_reduce_430,
  0, 284, :_reduce_431,
  3, 284, :_reduce_432,
  0, 278, :_reduce_433,
  2, 278, :_reduce_434,
  0, 279, :_reduce_435,
  2, 279, :_reduce_436,
  0, 280, :_reduce_437,
  2, 280, :_reduce_438,
  1, 283, :_reduce_439,
  2, 283, :_reduce_440,
  0, 286, :_reduce_441,
  4, 283, :_reduce_442,
  1, 285, :_reduce_443,
  1, 285, :_reduce_444,
  1, 285, :_reduce_445,
  1, 285, :_reduce_none,
  1, 191, :_reduce_447,
  3, 192, :_reduce_448,
  1, 275, :_reduce_449,
  1, 275, :_reduce_450,
  2, 275, :_reduce_451,
  2, 275, :_reduce_452,
  1, 184, :_reduce_453,
  1, 184, :_reduce_454,
  1, 184, :_reduce_455,
  1, 184, :_reduce_456,
  1, 184, :_reduce_457,
  1, 184, :_reduce_458,
  1, 184, :_reduce_459,
  1, 184, :_reduce_460,
  1, 184, :_reduce_461,
  1, 184, :_reduce_462,
  1, 184, :_reduce_463,
  1, 184, :_reduce_464,
  1, 215, :_reduce_465,
  1, 155, :_reduce_466,
  1, 160, :_reduce_467,
  1, 160, :_reduce_468,
  1, 226, :_reduce_469,
  3, 226, :_reduce_470,
  2, 226, :_reduce_471,
  3, 229, :_reduce_472,
  2, 229, :_reduce_473,
  6, 264, :_reduce_474,
  8, 264, :_reduce_475,
  4, 264, :_reduce_476,
  6, 264, :_reduce_477,
  4, 264, :_reduce_478,
  6, 264, :_reduce_479,
  2, 264, :_reduce_480,
  4, 264, :_reduce_481,
  6, 264, :_reduce_482,
  2, 264, :_reduce_483,
  4, 264, :_reduce_484,
  2, 264, :_reduce_485,
  4, 264, :_reduce_486,
  1, 264, :_reduce_487,
  0, 264, :_reduce_488,
  1, 260, :_reduce_489,
  1, 260, :_reduce_490,
  1, 260, :_reduce_491,
  1, 260, :_reduce_492,
  1, 247, :_reduce_none,
  1, 247, :_reduce_494,
  3, 247, :_reduce_495,
  2, 247, :_reduce_496,
  1, 288, :_reduce_none,
  3, 288, :_reduce_498,
  1, 251, :_reduce_499,
  3, 251, :_reduce_500,
  3, 289, :_reduce_501,
  3, 290, :_reduce_502,
  1, 252, :_reduce_503,
  3, 252, :_reduce_504,
  1, 287, :_reduce_505,
  3, 287, :_reduce_506,
  1, 291, :_reduce_none,
  1, 291, :_reduce_none,
  2, 253, :_reduce_509,
  1, 253, :_reduce_510,
  1, 292, :_reduce_none,
  1, 292, :_reduce_none,
  2, 255, :_reduce_513,
  2, 254, :_reduce_514,
  0, 254, :_reduce_515,
  1, 230, :_reduce_none,
  3, 230, :_reduce_517,
  0, 216, :_reduce_518,
  2, 216, :_reduce_none,
  1, 199, :_reduce_520,
  3, 199, :_reduce_521,
  3, 293, :_reduce_522,
  2, 293, :_reduce_523,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 171, :_reduce_none,
  1, 171, :_reduce_none,
  1, 171, :_reduce_none,
  1, 171, :_reduce_none,
  1, 269, :_reduce_none,
  1, 269, :_reduce_none,
  1, 269, :_reduce_none,
  1, 231, :_reduce_none,
  1, 231, :_reduce_none,
  1, 159, :_reduce_536,
  1, 159, :_reduce_537,
  0, 146, :_reduce_none,
  1, 146, :_reduce_none,
  0, 165, :_reduce_none,
  1, 165, :_reduce_none,
  2, 179, :_reduce_542,
  2, 158, :_reduce_543,
  0, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 227, :_reduce_547,
  1, 227, :_reduce_none,
  1, 148, :_reduce_none,
  2, 148, :_reduce_none,
  0, 196, :_reduce_551 ]

racc_reduce_n = 552

racc_shift_n = 965

racc_token_table = {
  false => 0,
  :error => 1,
  :kCLASS => 2,
  :kMODULE => 3,
  :kDEF => 4,
  :kUNDEF => 5,
  :kBEGIN => 6,
  :kRESCUE => 7,
  :kENSURE => 8,
  :kEND => 9,
  :kIF => 10,
  :kUNLESS => 11,
  :kTHEN => 12,
  :kELSIF => 13,
  :kELSE => 14,
  :kCASE => 15,
  :kWHEN => 16,
  :kWHILE => 17,
  :kUNTIL => 18,
  :kFOR => 19,
  :kBREAK => 20,
  :kNEXT => 21,
  :kREDO => 22,
  :kRETRY => 23,
  :kIN => 24,
  :kDO => 25,
  :kDO_COND => 26,
  :kDO_BLOCK => 27,
  :kDO_LAMBDA => 28,
  :kRETURN => 29,
  :kYIELD => 30,
  :kSUPER => 31,
  :kSELF => 32,
  :kNIL => 33,
  :kTRUE => 34,
  :kFALSE => 35,
  :kAND => 36,
  :kOR => 37,
  :kNOT => 38,
  :kIF_MOD => 39,
  :kUNLESS_MOD => 40,
  :kWHILE_MOD => 41,
  :kUNTIL_MOD => 42,
  :kRESCUE_MOD => 43,
  :kALIAS => 44,
  :kDEFINED => 45,
  :klBEGIN => 46,
  :klEND => 47,
  :k__LINE__ => 48,
  :k__FILE__ => 49,
  :k__ENCODING__ => 50,
  :tIDENTIFIER => 51,
  :tFID => 52,
  :tGVAR => 53,
  :tIVAR => 54,
  :tCONSTANT => 55,
  :tLABEL => 56,
  :tCVAR => 57,
  :tNTH_REF => 58,
  :tBACK_REF => 59,
  :tSTRING_CONTENT => 60,
  :tINTEGER => 61,
  :tFLOAT => 62,
  :tUPLUS => 63,
  :tUMINUS => 64,
  :tUNARY_NUM => 65,
  :tPOW => 66,
  :tCMP => 67,
  :tEQ => 68,
  :tEQQ => 69,
  :tNEQ => 70,
  :tGEQ => 71,
  :tLEQ => 72,
  :tANDOP => 73,
  :tOROP => 74,
  :tMATCH => 75,
  :tNMATCH => 76,
  :tDOT => 77,
  :tDOT2 => 78,
  :tDOT3 => 79,
  :tAREF => 80,
  :tASET => 81,
  :tLSHFT => 82,
  :tRSHFT => 83,
  :tCOLON2 => 84,
  :tCOLON3 => 85,
  :tOP_ASGN => 86,
  :tASSOC => 87,
  :tLPAREN => 88,
  :tLPAREN2 => 89,
  :tRPAREN => 90,
  :tLPAREN_ARG => 91,
  :tLBRACK => 92,
  :tLBRACK2 => 93,
  :tRBRACK => 94,
  :tLBRACE => 95,
  :tLBRACE_ARG => 96,
  :tSTAR => 97,
  :tSTAR2 => 98,
  :tAMPER => 99,
  :tAMPER2 => 100,
  :tTILDE => 101,
  :tPERCENT => 102,
  :tDIVIDE => 103,
  :tPLUS => 104,
  :tMINUS => 105,
  :tLT => 106,
  :tGT => 107,
  :tPIPE => 108,
  :tBANG => 109,
  :tCARET => 110,
  :tLCURLY => 111,
  :tRCURLY => 112,
  :tBACK_REF2 => 113,
  :tSYMBEG => 114,
  :tSTRING_BEG => 115,
  :tXSTRING_BEG => 116,
  :tREGEXP_BEG => 117,
  :tREGEXP_OPT => 118,
  :tWORDS_BEG => 119,
  :tQWORDS_BEG => 120,
  :tSTRING_DBEG => 121,
  :tSTRING_DVAR => 122,
  :tSTRING_END => 123,
  :tSTRING => 124,
  :tSYMBOL => 125,
  :tNL => 126,
  :tEH => 127,
  :tCOLON => 128,
  :tCOMMA => 129,
  :tSPACE => 130,
  :tSEMI => 131,
  :tLAMBDA => 132,
  :tLAMBEG => 133,
  :tCHARACTER => 134,
  :tANDDOT => 135,
  :tEQL => 136,
  :tLOWEST => 137 }

racc_nt_base = 138

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]
Ractor.make_shareable(Racc_arg) if defined?(Ractor)

Racc_token_to_s_table = [
  "$end",
  "error",
  "kCLASS",
  "kMODULE",
  "kDEF",
  "kUNDEF",
  "kBEGIN",
  "kRESCUE",
  "kENSURE",
  "kEND",
  "kIF",
  "kUNLESS",
  "kTHEN",
  "kELSIF",
  "kELSE",
  "kCASE",
  "kWHEN",
  "kWHILE",
  "kUNTIL",
  "kFOR",
  "kBREAK",
  "kNEXT",
  "kREDO",
  "kRETRY",
  "kIN",
  "kDO",
  "kDO_COND",
  "kDO_BLOCK",
  "kDO_LAMBDA",
  "kRETURN",
  "kYIELD",
  "kSUPER",
  "kSELF",
  "kNIL",
  "kTRUE",
  "kFALSE",
  "kAND",
  "kOR",
  "kNOT",
  "kIF_MOD",
  "kUNLESS_MOD",
  "kWHILE_MOD",
  "kUNTIL_MOD",
  "kRESCUE_MOD",
  "kALIAS",
  "kDEFINED",
  "klBEGIN",
  "klEND",
  "k__LINE__",
  "k__FILE__",
  "k__ENCODING__",
  "tIDENTIFIER",
  "tFID",
  "tGVAR",
  "tIVAR",
  "tCONSTANT",
  "tLABEL",
  "tCVAR",
  "tNTH_REF",
  "tBACK_REF",
  "tSTRING_CONTENT",
  "tINTEGER",
  "tFLOAT",
  "tUPLUS",
  "tUMINUS",
  "tUNARY_NUM",
  "tPOW",
  "tCMP",
  "tEQ",
  "tEQQ",
  "tNEQ",
  "tGEQ",
  "tLEQ",
  "tANDOP",
  "tOROP",
  "tMATCH",
  "tNMATCH",
  "tDOT",
  "tDOT2",
  "tDOT3",
  "tAREF",
  "tASET",
  "tLSHFT",
  "tRSHFT",
  "tCOLON2",
  "tCOLON3",
  "tOP_ASGN",
  "tASSOC",
  "tLPAREN",
  "tLPAREN2",
  "tRPAREN",
  "tLPAREN_ARG",
  "tLBRACK",
  "tLBRACK2",
  "tRBRACK",
  "tLBRACE",
  "tLBRACE_ARG",
  "tSTAR",
  "tSTAR2",
  "tAMPER",
  "tAMPER2",
  "tTILDE",
  "tPERCENT",
  "tDIVIDE",
  "tPLUS",
  "tMINUS",
  "tLT",
  "tGT",
  "tPIPE",
  "tBANG",
  "tCARET",
  "tLCURLY",
  "tRCURLY",
  "tBACK_REF2",
  "tSYMBEG",
  "tSTRING_BEG",
  "tXSTRING_BEG",
  "tREGEXP_BEG",
  "tREGEXP_OPT",
  "tWORDS_BEG",
  "tQWORDS_BEG",
  "tSTRING_DBEG",
  "tSTRING_DVAR",
  "tSTRING_END",
  "tSTRING",
  "tSYMBOL",
  "tNL",
  "tEH",
  "tCOLON",
  "tCOMMA",
  "tSPACE",
  "tSEMI",
  "tLAMBDA",
  "tLAMBEG",
  "tCHARACTER",
  "tANDDOT",
  "tEQL",
  "tLOWEST",
  "$start",
  "program",
  "compstmt",
  "bodystmt",
  "opt_rescue",
  "opt_else",
  "opt_ensure",
  "stmts",
  "opt_terms",
  "stmt",
  "terms",
  "fitem",
  "undef_list",
  "expr_value",
  "lhs",
  "command_call",
  "mlhs",
  "var_lhs",
  "primary_value",
  "opt_call_args",
  "rbracket",
  "call_op",
  "backref",
  "mrhs",
  "arg_value",
  "expr",
  "@1",
  "opt_nl",
  "arg",
  "command",
  "block_command",
  "call_args",
  "block_call",
  "operation2",
  "command_args",
  "cmd_brace_block",
  "opt_block_param",
  "@2",
  "operation",
  "mlhs_basic",
  "mlhs_inner",
  "rparen",
  "mlhs_head",
  "mlhs_item",
  "mlhs_node",
  "mlhs_post",
  "variable",
  "cname",
  "cpath",
  "fname",
  "op",
  "reswords",
  "fsym",
  "symbol",
  "dsym",
  "@3",
  "primary",
  "aref_args",
  "none",
  "args",
  "trailer",
  "assocs",
  "paren_args",
  "opt_paren_args",
  "opt_block_arg",
  "block_arg",
  "call_args2",
  "open_args",
  "@4",
  "@5",
  "@6",
  "literal",
  "strings",
  "xstring",
  "regexp",
  "words",
  "qwords",
  "var_ref",
  "assoc_list",
  "brace_block",
  "method_call",
  "lambda",
  "then",
  "if_tail",
  "do",
  "case_body",
  "for_var",
  "k_class",
  "superclass",
  "term",
  "k_module",
  "f_arglist",
  "singleton",
  "dot_or_colon",
  "@7",
  "@8",
  "@9",
  "@10",
  "@11",
  "@12",
  "@13",
  "@14",
  "@15",
  "@16",
  "@17",
  "@18",
  "@19",
  "@20",
  "f_marg",
  "f_norm_arg",
  "f_margs",
  "f_marg_list",
  "block_param",
  "f_arg",
  "f_block_optarg",
  "f_rest_arg",
  "opt_f_block_arg",
  "f_block_arg",
  "block_param_def",
  "opt_bv_decl",
  "bv_decls",
  "bvar",
  "f_bad_arg",
  "f_larglist",
  "lambda_body",
  "@21",
  "f_args",
  "do_block",
  "@22",
  "@23",
  "@24",
  "operation3",
  "@25",
  "@26",
  "cases",
  "exc_list",
  "exc_var",
  "numeric",
  "string",
  "string1",
  "string_contents",
  "xstring_contents",
  "regexp_contents",
  "word_list",
  "word",
  "string_content",
  "qword_list",
  "string_dvar",
  "@27",
  "f_optarg",
  "f_arg_item",
  "f_opt",
  "f_block_opt",
  "restarg_mark",
  "blkarg_mark",
  "assoc" ]
Ractor.make_shareable(Racc_token_to_s_table) if defined?(Ractor)

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

# reduce 1 omitted

def _reduce_2(val, _values, result)
                      rescue_bodies     = val[1]
                      else_t,   else_   = val[2]
                      ensure_t, ensure_ = val[3]

                      if rescue_bodies.empty? && !else_t.nil?
                        diagnostic :warning, :useless_else, nil, else_t
                      end

                      result = @builder.begin_body(val[0],
                                  rescue_bodies,
                                  else_t,   else_,
                                  ensure_t, ensure_)

    result
end

def _reduce_3(val, _values, result)
                      result = @builder.compstmt(val[0])

    result
end

def _reduce_4(val, _values, result)
                      result = []

    result
end

def _reduce_5(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_6(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_7(val, _values, result)
                      result = [ val[1] ]

    result
end

def _reduce_8(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_9(val, _values, result)
                      result = @builder.alias(val[0], val[1], val[3])

    result
end

def _reduce_10(val, _values, result)
                      result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.gvar(val[2]))

    result
end

def _reduce_11(val, _values, result)
                      result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.back_ref(val[2]))

    result
end

def _reduce_12(val, _values, result)
                      diagnostic :error, :nth_ref_alias, nil, val[2]

    result
end

def _reduce_13(val, _values, result)
                      result = @builder.undef_method(val[0], val[1])

    result
end

def _reduce_14(val, _values, result)
                      result = @builder.condition_mod(val[0], nil,
                                                      val[1], val[2])

    result
end

def _reduce_15(val, _values, result)
                      result = @builder.condition_mod(nil, val[0],
                                                      val[1], val[2])

    result
end

def _reduce_16(val, _values, result)
                      result = @builder.loop_mod(:while, val[0], val[1], val[2])

    result
end

def _reduce_17(val, _values, result)
                      result = @builder.loop_mod(:until, val[0], val[1], val[2])

    result
end

def _reduce_18(val, _values, result)
                      rescue_body = @builder.rescue_body(val[1],
                                        nil, nil, nil,
                                        nil, val[2])

                      result = @builder.begin_body(val[0], [ rescue_body ])

    result
end

def _reduce_19(val, _values, result)
                      if @context.in_def
                        diagnostic :error, :begin_in_method, nil, val[0]
                      end

                      result = @builder.preexe(val[0], val[1], val[2], val[3])

    result
end

def _reduce_20(val, _values, result)
                      result = @builder.postexe(val[0], val[1], val[2], val[3])

    result
end

def _reduce_21(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

def _reduce_22(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1], val[2])

    result
end

def _reduce_23(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_24(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])

    result
end

def _reduce_25(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_26(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_27(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_28(val, _values, result)
                      @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_29(val, _values, result)
                      result = @builder.assign(val[0], val[1],
                                  @builder.array(nil, val[2], nil))

    result
end

def _reduce_30(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1], val[2])

    result
end

def _reduce_31(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1],
                                  @builder.array(nil, val[2], nil))

    result
end

# reduce 32 omitted

# reduce 33 omitted

def _reduce_34(val, _values, result)
                      result = @builder.logical_op(:and, val[0], val[1], val[2])

    result
end

def _reduce_35(val, _values, result)
                      result = @builder.logical_op(:or, val[0], val[1], val[2])

    result
end

def _reduce_36(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[2], nil)

    result
end

def _reduce_37(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[1], nil)

    result
end

# reduce 38 omitted

# reduce 39 omitted

# reduce 40 omitted

# reduce 41 omitted

def _reduce_42(val, _values, result)
                      result = @builder.keyword_cmd(:return, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_43(val, _values, result)
                      result = @builder.keyword_cmd(:break, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_44(val, _values, result)
                      result = @builder.keyword_cmd(:next, val[0],
                                  nil, val[1], nil)

    result
end

# reduce 45 omitted

def _reduce_46(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  *val[3])

    result
end

def _reduce_47(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  *val[3])

    result
end

def _reduce_48(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_49(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_50(val, _values, result)
                      result = @builder.call_method(nil, nil, val[0],
                                  *val[1])

    result
end

def _reduce_51(val, _values, result)
                      method_call = @builder.call_method(nil, nil, val[0],
                                        *val[1])

                      begin_t, args, body, end_t = val[2]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_52(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  *val[3])

    result
end

def _reduce_53(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                        *val[3])

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_54(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  *val[3])

    result
end

def _reduce_55(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                        *val[3])

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_56(val, _values, result)
                      result = @builder.keyword_cmd(:super, val[0],
                                  *val[1])

    result
end

def _reduce_57(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0],
                                  *val[1])

    result
end

def _reduce_58(val, _values, result)
                      result = @builder.multi_lhs(nil, val[0], nil)

    result
end

def _reduce_59(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_60(val, _values, result)
                      result = @builder.multi_lhs(nil, val[0], nil)

    result
end

def _reduce_61(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

# reduce 62 omitted

def _reduce_63(val, _values, result)
                      result = val[0].
                                  push(val[1])

    result
end

def _reduce_64(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1], val[2]))

    result
end

def _reduce_65(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1], val[2])).
                                  concat(val[4])

    result
end

def _reduce_66(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1]))

    result
end

def _reduce_67(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1])).
                                  concat(val[3])

    result
end

def _reduce_68(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

def _reduce_69(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]),
                                 *val[3] ]

    result
end

def _reduce_70(val, _values, result)
                      result = [ @builder.splat(val[0]) ]

    result
end

def _reduce_71(val, _values, result)
                      result = [ @builder.splat(val[0]),
                                 *val[2] ]

    result
end

# reduce 72 omitted

def _reduce_73(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_74(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_75(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_76(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_77(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_78(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_79(val, _values, result)
                      result = @builder.index_asgn(val[0], val[1], val[2], val[3])

    result
end

def _reduce_80(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_81(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_82(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_83(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))

    result
end

def _reduce_84(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))

    result
end

def _reduce_85(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_86(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_87(val, _values, result)
                      result = @builder.index_asgn(val[0], val[1], val[2], val[3])

    result
end

def _reduce_88(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_89(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_90(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_91(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))

    result
end

def _reduce_92(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))

    result
end

def _reduce_93(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_94(val, _values, result)
                      diagnostic :error, :module_name_const, nil, val[0]

    result
end

# reduce 95 omitted

def _reduce_96(val, _values, result)
                      result = @builder.const_global(val[0], val[1])

    result
end

def _reduce_97(val, _values, result)
                      result = @builder.const(val[0])

    result
end

def _reduce_98(val, _values, result)
                      result = @builder.const_fetch(val[0], val[1], val[2])

    result
end

# reduce 99 omitted

# reduce 100 omitted

# reduce 101 omitted

# reduce 102 omitted

# reduce 103 omitted

def _reduce_104(val, _values, result)
                      result = @builder.symbol_internal(val[0])

    result
end

# reduce 105 omitted

# reduce 106 omitted

# reduce 107 omitted

def _reduce_108(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_109(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_110(val, _values, result)
                      result = val[0] << val[3]

    result
end

# reduce 111 omitted

# reduce 112 omitted

# reduce 113 omitted

# reduce 114 omitted

# reduce 115 omitted

# reduce 116 omitted

# reduce 117 omitted

# reduce 118 omitted

# reduce 119 omitted

# reduce 120 omitted

# reduce 121 omitted

# reduce 122 omitted

# reduce 123 omitted

# reduce 124 omitted

# reduce 125 omitted

# reduce 126 omitted

# reduce 127 omitted

# reduce 128 omitted

# reduce 129 omitted

# reduce 130 omitted

# reduce 131 omitted

# reduce 132 omitted

# reduce 133 omitted

# reduce 134 omitted

# reduce 135 omitted

# reduce 136 omitted

# reduce 137 omitted

# reduce 138 omitted

# reduce 139 omitted

# reduce 140 omitted

# reduce 141 omitted

# reduce 142 omitted

# reduce 143 omitted

# reduce 144 omitted

# reduce 145 omitted

# reduce 146 omitted

# reduce 147 omitted

# reduce 148 omitted

# reduce 149 omitted

# reduce 150 omitted

# reduce 151 omitted

# reduce 152 omitted

# reduce 153 omitted

# reduce 154 omitted

# reduce 155 omitted

# reduce 156 omitted

# reduce 157 omitted

# reduce 158 omitted

# reduce 159 omitted

# reduce 160 omitted

# reduce 161 omitted

# reduce 162 omitted

# reduce 163 omitted

# reduce 164 omitted

# reduce 165 omitted

# reduce 166 omitted

# reduce 167 omitted

# reduce 168 omitted

# reduce 169 omitted

# reduce 170 omitted

# reduce 171 omitted

# reduce 172 omitted

# reduce 173 omitted

# reduce 174 omitted

# reduce 175 omitted

# reduce 176 omitted

# reduce 177 omitted

# reduce 178 omitted

# reduce 179 omitted

# reduce 180 omitted

def _reduce_181(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

def _reduce_182(val, _values, result)
                      rescue_body = @builder.rescue_body(val[3],
                                        nil, nil, nil,
                                        nil, val[4])

                      rescue_ = @builder.begin_body(val[2], [ rescue_body ])

                      result  = @builder.assign(val[0], val[1], rescue_)

    result
end

def _reduce_183(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_184(val, _values, result)
                      rescue_body = @builder.rescue_body(val[3],
                                        nil, nil, nil,
                                        nil, val[4])

                      rescue_ = @builder.begin_body(val[2], [ rescue_body ])

                      result = @builder.op_assign(val[0], val[1], rescue_)

    result
end

def _reduce_185(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])

    result
end

def _reduce_186(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_187(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_188(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_189(val, _values, result)
                      diagnostic :error, :dynamic_const, nil, val[2], [ val[3] ]

    result
end

def _reduce_190(val, _values, result)
                      diagnostic :error, :dynamic_const, nil, val[1], [ val[2] ]

    result
end

def _reduce_191(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_192(val, _values, result)
                      result = @builder.range_inclusive(val[0], val[1], val[2])

    result
end

def _reduce_193(val, _values, result)
                      result = @builder.range_exclusive(val[0], val[1], val[2])

    result
end

def _reduce_194(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_195(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_196(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_197(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_198(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_199(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_200(val, _values, result)
                      result = @builder.unary_op(val[0],
                                  @builder.binary_op(
                                    @builder.integer(val[1]),
                                      val[2], val[3]))

    result
end

def _reduce_201(val, _values, result)
                      result = @builder.unary_op(val[0],
                                  @builder.binary_op(
                                    @builder.float(val[1]),
                                      val[2], val[3]))

    result
end

def _reduce_202(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_203(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_204(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_205(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_206(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_207(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_208(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_209(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_210(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_211(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_212(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_213(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_214(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_215(val, _values, result)
                      result = @builder.match_op(val[0], val[1], val[2])

    result
end

def _reduce_216(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_217(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[1], nil)

    result
end

def _reduce_218(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_219(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_220(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_221(val, _values, result)
                      result = @builder.logical_op(:and, val[0], val[1], val[2])

    result
end

def _reduce_222(val, _values, result)
                      result = @builder.logical_op(:or, val[0], val[1], val[2])

    result
end

def _reduce_223(val, _values, result)
                      result = @builder.keyword_cmd(:defined?, val[0], nil, [ val[2] ], nil)

    result
end

def _reduce_224(val, _values, result)
                      result = @builder.ternary(val[0], val[1],
                                                val[2], val[4], val[5])

    result
end

# reduce 225 omitted

# reduce 226 omitted

# reduce 227 omitted

# reduce 228 omitted

def _reduce_229(val, _values, result)
                      result = val[0] << @builder.associate(nil, val[2], nil)

    result
end

def _reduce_230(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]

    result
end

def _reduce_231(val, _values, result)
                      result = val

    result
end

def _reduce_232(val, _values, result)
                      result = [ nil, [], nil ]

    result
end

# reduce 233 omitted

def _reduce_234(val, _values, result)
                      result = []

    result
end

# reduce 235 omitted

def _reduce_236(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_237(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_238(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]
                      result.concat(val[1])

    result
end

def _reduce_239(val, _values, result)
                      assocs = @builder.associate(nil, val[2], nil)
                      result = val[0] << assocs
                      result.concat(val[3])

    result
end

def _reduce_240(val, _values, result)
                      val[2][-1] = @builder.objc_varargs(val[2][-1], val[4])
                      assocs = @builder.associate(nil, val[2], nil)
                      result = val[0] << assocs
                      result.concat(val[5])

    result
end

def _reduce_241(val, _values, result)
                      result =  [ val[0] ]

    result
end

def _reduce_242(val, _values, result)
                      result = [ val[0], *val[2].concat(val[3]) ]

    result
end

def _reduce_243(val, _values, result)
                      result = [ val[0], val[2] ]

    result
end

def _reduce_244(val, _values, result)
                      result =  [ @builder.associate(nil, val[0], nil),
                                  *val[1] ]

    result
end

def _reduce_245(val, _values, result)
                      result =  [ val[0],
                                  @builder.associate(nil, val[2], nil),
                                  *val[3] ]

    result
end

def _reduce_246(val, _values, result)
                      result =  [ val[0],
                                  *val[2].
                                    push(@builder.associate(nil, val[4], nil)).
                                    concat(val[5]) ]

    result
end

def _reduce_247(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_248(val, _values, result)
                      result = @lexer.cmdarg.dup
                      @lexer.cmdarg.push(true)

    result
end

def _reduce_249(val, _values, result)
                      @lexer.cmdarg = val[0]

                      result = val[1]

    result
end

def _reduce_250(val, _values, result)
                      result = [ nil, val[0], nil ]

    result
end

def _reduce_251(val, _values, result)
                      @lexer.state = :expr_endarg

    result
end

def _reduce_252(val, _values, result)
                      result = [ val[0], [], val[2] ]

    result
end

def _reduce_253(val, _values, result)
                      @lexer.state = :expr_endarg

    result
end

def _reduce_254(val, _values, result)
                      result = [ val[0], val[1], val[3] ]

    result
end

def _reduce_255(val, _values, result)
                      result = @builder.block_pass(val[0], val[1])

    result
end

def _reduce_256(val, _values, result)
                      result = [ val[1] ]

    result
end

def _reduce_257(val, _values, result)
                      result = []

    result
end

def _reduce_258(val, _values, result)
                      result = []

    result
end

def _reduce_259(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_260(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

def _reduce_261(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_262(val, _values, result)
                      result = val[0] << @builder.splat(val[2], val[3])

    result
end

def _reduce_263(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_264(val, _values, result)
                      result = val[0] << @builder.splat(val[2], val[3])

    result
end

def _reduce_265(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

# reduce 266 omitted

# reduce 267 omitted

# reduce 268 omitted

# reduce 269 omitted

# reduce 270 omitted

# reduce 271 omitted

# reduce 272 omitted

# reduce 273 omitted

def _reduce_274(val, _values, result)
                      result = @builder.call_method(nil, nil, val[0])

    result
end

def _reduce_275(val, _values, result)
                      result = @builder.begin_keyword(val[0], val[1], val[2])

    result
end

def _reduce_276(val, _values, result)
                      @lexer.state = :expr_endarg

    result
end

def _reduce_277(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[3])

    result
end

def _reduce_278(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_279(val, _values, result)
                      result = @builder.const_fetch(val[0], val[1], val[2])

    result
end

def _reduce_280(val, _values, result)
                      result = @builder.const_global(val[0], val[1])

    result
end

def _reduce_281(val, _values, result)
                      result = @builder.array(val[0], val[1], val[2])

    result
end

def _reduce_282(val, _values, result)
                      result = @builder.associate(val[0], val[1], val[2])

    result
end

def _reduce_283(val, _values, result)
                      result = @builder.keyword_cmd(:return, val[0])

    result
end

def _reduce_284(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0], val[1], val[2], val[3])

    result
end

def _reduce_285(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0], val[1], [], val[2])

    result
end

def _reduce_286(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0])

    result
end

def _reduce_287(val, _values, result)
                      result = @builder.keyword_cmd(:defined?, val[0],
                                                    val[2], [ val[3] ], val[4])

    result
end

def _reduce_288(val, _values, result)
                      result = @builder.not_op(val[0], val[1], val[2], val[3])

    result
end

def _reduce_289(val, _values, result)
                      result = @builder.not_op(val[0], val[1], nil, val[2])

    result
end

def _reduce_290(val, _values, result)
                      method_call = @builder.call_method(nil, nil, val[0])

                      begin_t, args, body, end_t = val[1]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

# reduce 291 omitted

def _reduce_292(val, _values, result)
                      begin_t, args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, args, body, end_t)

    result
end

def _reduce_293(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_294(val, _values, result)
                      lambda_call = @builder.call_lambda(val[0])

                      args, (begin_t, body, end_t) = val[2]
                      result      = @builder.block(lambda_call,
                                      begin_t, args, body, end_t)

                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_295(val, _values, result)
                      else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  val[3], else_t,
                                                  else_,  val[5])

    result
end

def _reduce_296(val, _values, result)
                      else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  else_,  else_t,
                                                  val[3], val[5])

    result
end

def _reduce_297(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_298(val, _values, result)
                      @lexer.cond.pop

    result
end

def _reduce_299(val, _values, result)
                      result = @builder.loop(:while, val[0], val[2], val[3],
                                             val[5], val[6])

    result
end

def _reduce_300(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_301(val, _values, result)
                      @lexer.cond.pop

    result
end

def _reduce_302(val, _values, result)
                      result = @builder.loop(:until, val[0], val[2], val[3],
                                             val[5], val[6])

    result
end

def _reduce_303(val, _values, result)
                      *when_bodies, (else_t, else_body) = *val[3]

                      result = @builder.case(val[0], val[1],
                                             when_bodies, else_t, else_body,
                                             val[4])

    result
end

def _reduce_304(val, _values, result)
                      *when_bodies, (else_t, else_body) = *val[2]

                      result = @builder.case(val[0], nil,
                                             when_bodies, else_t, else_body,
                                             val[3])

    result
end

def _reduce_305(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_306(val, _values, result)
                      @lexer.cond.pop

    result
end

def _reduce_307(val, _values, result)
                      result = @builder.for(val[0], val[1],
                                            val[2], val[4],
                                            val[5], val[7], val[8])

    result
end

def _reduce_308(val, _values, result)
                      local_push
                      @context.in_class = true

    result
end

def _reduce_309(val, _values, result)
                      k_class, ctx = val[0]
                      if @context.in_def
                        diagnostic :error, :class_in_def, nil, k_class
                      end

                      lt_t, superclass = val[2]
                      result = @builder.def_class(k_class, val[1],
                                                  lt_t, superclass,
                                                  val[4], val[5])

                      local_pop
                      @context.in_class = ctx.in_class

    result
end

def _reduce_310(val, _values, result)
                      @context.in_def = false
                      @context.in_class = false
                      local_push

    result
end

def _reduce_311(val, _values, result)
                      k_class, ctx = val[0]
                      result = @builder.def_sclass(k_class, val[1], val[2],
                                                   val[5], val[6])

                      local_pop
                      @context.in_def = ctx.in_def
                      @context.in_class = ctx.in_class

    result
end

def _reduce_312(val, _values, result)
                      @context.in_class = true
                      local_push

    result
end

def _reduce_313(val, _values, result)
                      k_mod, ctx = val[0]
                      if @context.in_def
                        diagnostic :error, :module_in_def, nil, k_mod
                      end

                      result = @builder.def_module(k_mod, val[1],
                                                   val[3], val[4])

                      local_pop
                      @context.in_class = ctx.in_class

    result
end

def _reduce_314(val, _values, result)
                      local_push
                      result = context.dup
                      @context.in_def = true

    result
end

def _reduce_315(val, _values, result)
                      result = @builder.def_method(val[0], val[1],
                                  val[3], val[4], val[5])

                      local_pop
                      @context.in_def = val[2].in_def

    result
end

def _reduce_316(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_317(val, _values, result)
                      local_push
                      result = context.dup
                      @context.in_def = true

    result
end

def _reduce_318(val, _values, result)
                      result = @builder.def_singleton(val[0], val[1], val[2],
                                  val[4], val[6], val[7], val[8])

                      local_pop
                      @context.in_def = val[5].in_def

    result
end

def _reduce_319(val, _values, result)
                      result = @builder.keyword_cmd(:break, val[0])

    result
end

def _reduce_320(val, _values, result)
                      result = @builder.keyword_cmd(:next, val[0])

    result
end

def _reduce_321(val, _values, result)
                      result = @builder.keyword_cmd(:redo, val[0])

    result
end

def _reduce_322(val, _values, result)
                      result = @builder.keyword_cmd(:retry, val[0])

    result
end

# reduce 323 omitted

def _reduce_324(val, _values, result)
                      result = [ val[0], @context.dup ]

    result
end

def _reduce_325(val, _values, result)
                      result = [ val[0], @context.dup ]

    result
end

# reduce 326 omitted

# reduce 327 omitted

def _reduce_328(val, _values, result)
                      result = val[1]

    result
end

# reduce 329 omitted

# reduce 330 omitted

# reduce 331 omitted

def _reduce_332(val, _values, result)
                      else_t, else_ = val[4]
                      result = [ val[0],
                                 @builder.condition(val[0], val[1], val[2],
                                                    val[3], else_t,
                                                    else_,  nil),
                               ]

    result
end

# reduce 333 omitted

def _reduce_334(val, _values, result)
                      result = val

    result
end

# reduce 335 omitted

# reduce 336 omitted

# reduce 337 omitted

def _reduce_338(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

def _reduce_339(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_340(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 341 omitted

def _reduce_342(val, _values, result)
                      result = val[0].
                                  push(@builder.objc_restarg(val[2], val[3]))

    result
end

def _reduce_343(val, _values, result)
                      result = val[0].
                                  push(@builder.objc_restarg(val[2], val[3])).
                                  concat(val[5])

    result
end

def _reduce_344(val, _values, result)
                      result = val[0].
                                  push(@builder.objc_restarg(val[2]))

    result
end

def _reduce_345(val, _values, result)
                      result = val[0].
                                  push(@builder.objc_restarg(val[2])).
                                  concat(val[4])

    result
end

def _reduce_346(val, _values, result)
                      result = [ @builder.objc_restarg(val[0], val[1]) ]

    result
end

def _reduce_347(val, _values, result)
                      result = [ @builder.objc_restarg(val[0], val[1]),
                                 *val[3] ]

    result
end

def _reduce_348(val, _values, result)
                      result = [ @builder.objc_restarg(val[0]) ]

    result
end

def _reduce_349(val, _values, result)
                      result = [ @builder.objc_restarg(val[0]),
                                 *val[2] ]

    result
end

def _reduce_350(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_351(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[6]).
                                  concat(val[7])

    result
end

def _reduce_352(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_353(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_354(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

# reduce 355 omitted

def _reduce_356(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_357(val, _values, result)
                      if val[1].empty? && val[0].size == 1
                        result = [@builder.procarg0(val[0][0])]
                      else
                        result = val[0].concat(val[1])
                      end

    result
end

def _reduce_358(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_359(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_360(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_361(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_362(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_363(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_364(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_365(val, _values, result)
                      result = @builder.args(nil, [], nil)

    result
end

def _reduce_366(val, _values, result)
                      @lexer.state = :expr_value

    result
end

def _reduce_367(val, _values, result)
                      result = @builder.args(val[0], val[1], val[2])

    result
end

def _reduce_368(val, _values, result)
                      result = @builder.args(val[0], [], val[0])

    result
end

def _reduce_369(val, _values, result)
                      result = @builder.args(val[0], val[1].concat(val[2]), val[3])

    result
end

def _reduce_370(val, _values, result)
                      result = []

    result
end

def _reduce_371(val, _values, result)
                      result = val[1]

    result
end

def _reduce_372(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_373(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_374(val, _values, result)
                      @static_env.declare val[0][0]
                      result = @builder.shadowarg(val[0])

    result
end

# reduce 375 omitted

def _reduce_376(val, _values, result)
                      @static_env.extend_dynamic

    result
end

def _reduce_377(val, _values, result)
                      result = [ val[1], val[2] ]

                      @static_env.unextend

    result
end

def _reduce_378(val, _values, result)
                      result = @builder.args(val[0], val[1].concat(val[2]), val[3])

    result
end

def _reduce_379(val, _values, result)
                      result = @builder.args(nil, val[0].concat(val[1]), nil)

    result
end

def _reduce_380(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_381(val, _values, result)
                      result = [ val[0], val[2], val[3] ]
                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_382(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_383(val, _values, result)
                      result = [ val[0], val[2], val[3] ]
                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_384(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_385(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_386(val, _values, result)
                      begin_t, block_args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, block_args, body, end_t)

    result
end

def _reduce_387(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_388(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_389(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.call_method(nil, nil, val[0],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_390(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_391(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_392(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2])

    result
end

def _reduce_393(val, _values, result)
                      lparen_t, args, rparen_t = val[2]
                      result = @builder.call_method(val[0], val[1], nil,
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_394(val, _values, result)
                      lparen_t, args, rparen_t = val[2]
                      result = @builder.call_method(val[0], val[1], nil,
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_395(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.keyword_cmd(:super, val[0],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_396(val, _values, result)
                      result = @builder.keyword_cmd(:zsuper, val[0])

    result
end

def _reduce_397(val, _values, result)
                      result = @builder.index(val[0], val[1], val[2], val[3])

    result
end

def _reduce_398(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_399(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_400(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_401(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_402(val, _values, result)
                      result = [ @builder.when(val[0], val[1], val[2], val[3]),
                                 *val[4] ]

    result
end

def _reduce_403(val, _values, result)
                      result = [ val[0] ]

    result
end

# reduce 404 omitted

def _reduce_405(val, _values, result)
                      assoc_t, exc_var = val[2]

                      if val[1]
                        exc_list = @builder.array(nil, val[1], nil)
                      end

                      result = [ @builder.rescue_body(val[0],
                                      exc_list, assoc_t, exc_var,
                                      val[3], val[4]),
                                 *val[5] ]

    result
end

def _reduce_406(val, _values, result)
                      result = []

    result
end

def _reduce_407(val, _values, result)
                      result = [ val[0] ]

    result
end

# reduce 408 omitted

# reduce 409 omitted

def _reduce_410(val, _values, result)
                      result = [ val[0], val[1] ]

    result
end

# reduce 411 omitted

def _reduce_412(val, _values, result)
                      result = [ val[0], val[1] ]

    result
end

# reduce 413 omitted

# reduce 414 omitted

# reduce 415 omitted

# reduce 416 omitted

def _reduce_417(val, _values, result)
                      result = @builder.string_compose(nil, val[0], nil)

    result
end

def _reduce_418(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_419(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_420(val, _values, result)
                      result = @builder.string_compose(val[0], val[1], val[2])

    result
end

def _reduce_421(val, _values, result)
                      result = @builder.string(val[0])

    result
end

def _reduce_422(val, _values, result)
                      result = @builder.character(val[0])

    result
end

def _reduce_423(val, _values, result)
                      result = @builder.xstring_compose(val[0], val[1], val[2])

    result
end

def _reduce_424(val, _values, result)
                      opts   = @builder.regexp_options(val[3])
                      result = @builder.regexp_compose(val[0], val[1], val[2], opts)

    result
end

def _reduce_425(val, _values, result)
                      result = @builder.words_compose(val[0], val[1], val[2])

    result
end

def _reduce_426(val, _values, result)
                      result = []

    result
end

def _reduce_427(val, _values, result)
                      result = val[0] << @builder.word(val[1])

    result
end

def _reduce_428(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_429(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_430(val, _values, result)
                      result = @builder.words_compose(val[0], val[1], val[2])

    result
end

def _reduce_431(val, _values, result)
                      result = []

    result
end

def _reduce_432(val, _values, result)
                      result = val[0] << @builder.string_internal(val[1])

    result
end

def _reduce_433(val, _values, result)
                      result = []

    result
end

def _reduce_434(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_435(val, _values, result)
                      result = []

    result
end

def _reduce_436(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_437(val, _values, result)
                      result = []

    result
end

def _reduce_438(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_439(val, _values, result)
                      result = @builder.string_internal(val[0])

    result
end

def _reduce_440(val, _values, result)
                      result = val[1]

    result
end

def _reduce_441(val, _values, result)
                      @lexer.cond.push(false)
                      @lexer.cmdarg.push(false)

    result
end

def _reduce_442(val, _values, result)
                      @lexer.cond.lexpop
                      @lexer.cmdarg.lexpop

                      result = @builder.begin(val[0], val[2], val[3])

    result
end

def _reduce_443(val, _values, result)
                      result = @builder.gvar(val[0])

    result
end

def _reduce_444(val, _values, result)
                      result = @builder.ivar(val[0])

    result
end

def _reduce_445(val, _values, result)
                      result = @builder.cvar(val[0])

    result
end

# reduce 446 omitted

def _reduce_447(val, _values, result)
                      result = @builder.symbol(val[0])

    result
end

def _reduce_448(val, _values, result)
                      result = @builder.symbol_compose(val[0], val[1], val[2])

    result
end

def _reduce_449(val, _values, result)
                      result = @builder.integer(val[0])

    result
end

def _reduce_450(val, _values, result)
                      result = @builder.float(val[0])

    result
end

def _reduce_451(val, _values, result)
                      num = @builder.integer(val[1])
                      if @builder.respond_to? :negate
                        # AST builder interface compatibility
                        result = @builder.negate(val[0], num)
                      else
                        result = @builder.unary_num(val[0], num)
                      end

    result
end

def _reduce_452(val, _values, result)
                      num = @builder.float(val[1])
                      if @builder.respond_to? :negate
                        # AST builder interface compatibility
                        result = @builder.negate(val[0], num)
                      else
                        result = @builder.unary_num(val[0], num)
                      end

    result
end

def _reduce_453(val, _values, result)
                      result = @builder.ident(val[0])

    result
end

def _reduce_454(val, _values, result)
                      result = @builder.ivar(val[0])

    result
end

def _reduce_455(val, _values, result)
                      result = @builder.gvar(val[0])

    result
end

def _reduce_456(val, _values, result)
                      result = @builder.const(val[0])

    result
end

def _reduce_457(val, _values, result)
                      result = @builder.cvar(val[0])

    result
end

def _reduce_458(val, _values, result)
                      result = @builder.nil(val[0])

    result
end

def _reduce_459(val, _values, result)
                      result = @builder.self(val[0])

    result
end

def _reduce_460(val, _values, result)
                      result = @builder.true(val[0])

    result
end

def _reduce_461(val, _values, result)
                      result = @builder.false(val[0])

    result
end

def _reduce_462(val, _values, result)
                      result = @builder.__FILE__(val[0])

    result
end

def _reduce_463(val, _values, result)
                      result = @builder.__LINE__(val[0])

    result
end

def _reduce_464(val, _values, result)
                      result = @builder.__ENCODING__(val[0])

    result
end

def _reduce_465(val, _values, result)
                      result = @builder.accessible(val[0])

    result
end

def _reduce_466(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_467(val, _values, result)
                      result = @builder.nth_ref(val[0])

    result
end

def _reduce_468(val, _values, result)
                      result = @builder.back_ref(val[0])

    result
end

def _reduce_469(val, _values, result)
                      result = nil

    result
end

def _reduce_470(val, _values, result)
                      result = [ val[0], val[1] ]

    result
end

def _reduce_471(val, _values, result)
                      yyerrok
                      result = nil

    result
end

def _reduce_472(val, _values, result)
                      result = @builder.args(val[0], val[1], val[2])

                      @lexer.state = :expr_value

    result
end

def _reduce_473(val, _values, result)
                      result = @builder.args(nil, val[0], nil)

    result
end

def _reduce_474(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_475(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[6]).
                                  concat(val[7])

    result
end

def _reduce_476(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_477(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_478(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_479(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_480(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_481(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_482(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_483(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_484(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_485(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_486(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_487(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_488(val, _values, result)
                      result = []

    result
end

def _reduce_489(val, _values, result)
                      diagnostic :error, :argument_const, nil, val[0]

    result
end

def _reduce_490(val, _values, result)
                      diagnostic :error, :argument_ivar, nil, val[0]

    result
end

def _reduce_491(val, _values, result)
                      diagnostic :error, :argument_gvar, nil, val[0]

    result
end

def _reduce_492(val, _values, result)
                      diagnostic :error, :argument_cvar, nil, val[0]

    result
end

# reduce 493 omitted

def _reduce_494(val, _values, result)
                      @static_env.declare val[0][0]

                      result = @builder.arg(val[0])

    result
end

def _reduce_495(val, _values, result)
                      @static_env.declare val[2][0]

                      result = @builder.objc_kwarg(val[0], val[1], val[2])

    result
end

def _reduce_496(val, _values, result)
                      @static_env.declare val[1][0]

                      result = @builder.objc_kwarg(val[0], nil, val[1])

    result
end

# reduce 497 omitted

def _reduce_498(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

def _reduce_499(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_500(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_501(val, _values, result)
                      @static_env.declare val[0][0]

                      result = @builder.optarg(val[0], val[1], val[2])

    result
end

def _reduce_502(val, _values, result)
                      @static_env.declare val[0][0]

                      result = @builder.optarg(val[0], val[1], val[2])

    result
end

def _reduce_503(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_504(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_505(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_506(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 507 omitted

# reduce 508 omitted

def _reduce_509(val, _values, result)
                      @static_env.declare val[1][0]

                      result = [ @builder.restarg(val[0], val[1]) ]

    result
end

def _reduce_510(val, _values, result)
                      result = [ @builder.restarg(val[0]) ]

    result
end

# reduce 511 omitted

# reduce 512 omitted

def _reduce_513(val, _values, result)
                      @static_env.declare val[1][0]

                      result = @builder.blockarg(val[0], val[1])

    result
end

def _reduce_514(val, _values, result)
                      result = [ val[1] ]

    result
end

def _reduce_515(val, _values, result)
                      result = []

    result
end

# reduce 516 omitted

def _reduce_517(val, _values, result)
                      result = val[1]

    result
end

def _reduce_518(val, _values, result)
                      result = []

    result
end

# reduce 519 omitted

def _reduce_520(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_521(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_522(val, _values, result)
                      result = @builder.pair(val[0], val[1], val[2])

    result
end

def _reduce_523(val, _values, result)
                      result = @builder.pair_keyword(val[0], val[1])

    result
end

# reduce 524 omitted

# reduce 525 omitted

# reduce 526 omitted

# reduce 527 omitted

# reduce 528 omitted

# reduce 529 omitted

# reduce 530 omitted

# reduce 531 omitted

# reduce 532 omitted

# reduce 533 omitted

# reduce 534 omitted

# reduce 535 omitted

def _reduce_536(val, _values, result)
                      result = [:dot, val[0][1]]

    result
end

def _reduce_537(val, _values, result)
                      result = [:anddot, val[0][1]]

    result
end

# reduce 538 omitted

# reduce 539 omitted

# reduce 540 omitted

# reduce 541 omitted

def _reduce_542(val, _values, result)
                      result = val[1]

    result
end

def _reduce_543(val, _values, result)
                      result = val[1]

    result
end

# reduce 544 omitted

# reduce 545 omitted

# reduce 546 omitted

def _reduce_547(val, _values, result)
                    yyerrok

    result
end

# reduce 548 omitted

# reduce 549 omitted

# reduce 550 omitted

def _reduce_551(val, _values, result)
                    result = nil

    result
end

def _reduce_none(val, _values, result)
  val[0]
end

  end   # class RubyMotion
end   # module Parser
