# -*- encoding:utf-8; warn-indent:false; frozen_string_literal: true  -*-
#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.7.3
# from Racc grammar file "ruby18.y".
#

require 'racc/parser.rb'


require_relative '../parser'

module Parser
  class Ruby18 < Parser::Base


  def version
    18
  end

  def default_encoding
    Encoding::BINARY
  end

  def local_push
    @static_env.extend_static
    @lexer.cmdarg.push(false)
    @lexer.cond.push(false)
  end

  def local_pop
    @static_env.unextend
    @lexer.cmdarg.pop
    @lexer.cond.pop
  end
##### State transition tables begin ###

racc_action_table = [
  -482,   197,   198,   197,   198,   491,   816,  -482,  -482,  -482,
   513,   580,   580,  -482,  -482,   -80,  -482,   433,   581,   581,
   491,   560,   533,   -87,   560,  -482,   504,   491,   -86,    97,
   505,  -431,   397,   197,   198,  -482,  -482,   -82,  -482,  -482,
  -482,  -482,  -482,   491,   491,   497,   465,   498,   -84,   -83,
   -81,   661,   660,   664,   663,   559,   188,   -61,   559,   715,
   293,   293,   189,  -482,  -482,  -482,  -482,  -482,  -482,  -482,
  -482,  -482,  -482,  -482,  -482,  -482,  -482,   -80,   -87,  -482,
  -482,  -482,   261,   548,   532,   724,   -74,  -482,   190,   101,
  -482,   293,   -86,   623,   100,  -482,   -69,  -482,   191,  -482,
  -482,  -482,  -482,  -482,  -482,  -482,  -277,  -482,  -482,  -482,
   192,  -478,   512,  -277,  -277,  -277,   101,   -72,   490,  -277,
  -277,   100,  -277,  -482,  -482,   -79,   -75,   236,  -482,   -83,
   -78,   101,   101,   490,   623,   -76,   100,   100,   101,   -74,
   490,  -277,  -277,   100,  -277,  -277,  -277,  -277,  -277,   -74,
   -76,   -75,   -73,   692,   101,   101,   490,   490,   623,   100,
   100,   -85,   608,   560,   498,   260,   261,   -74,   693,  -277,
  -277,  -277,  -277,  -277,  -277,  -277,  -277,  -277,  -277,  -277,
  -277,  -277,  -277,   261,   560,  -277,  -277,  -277,   -74,   551,
   101,   -74,   622,  -277,    74,   100,  -277,   559,   -76,   -82,
   196,  -277,    75,  -277,   523,  -277,  -277,  -277,  -277,  -277,
  -277,  -277,  -272,  -277,   -84,  -277,   -76,   284,   559,  -272,
  -272,  -272,   816,  -479,  -272,  -272,  -272,   256,  -272,  -277,
  -277,   101,   -77,   622,  -277,   -85,   100,   -76,  -272,  -272,
   -76,   197,   198,   -79,   256,  -475,   -87,  -272,  -272,   260,
  -272,  -272,  -272,  -272,  -272,   101,   101,   622,   286,   287,
   100,   100,   523,   -77,   525,   524,   260,   661,   660,   664,
   663,   463,   464,   285,   675,  -272,  -272,  -272,  -272,  -272,
  -272,  -272,  -272,  -272,  -272,  -272,  -272,  -272,  -272,   293,
  -476,  -272,  -272,  -272,  -419,   101,  -272,   770,   351,  -272,
   100,  -419,  -272,  -272,  -431,  -272,  -475,  -272,   363,  -272,
  -419,  -272,  -272,  -272,  -272,  -272,  -272,  -272,  -226,  -272,
   465,  -272,   525,   524,   521,  -226,  -226,  -226,  -475,   543,
  -226,  -226,  -226,  -482,  -226,  -272,  -272,  -272,  -272,  -423,
  -272,   523,   253,   -81,  -226,   194,  -423,   -72,   396,   254,
   -80,  -476,   195,  -226,  -226,  -423,  -226,  -226,  -226,  -226,
  -226,   193,  -419,   -78,   479,  -482,   -86,   478,  -478,  -419,
   398,  -226,   -74,  -476,   523,   -82,   544,   682,  -226,  -226,
  -226,   399,  -482,  -226,  -226,  -226,  -423,  -226,  -479,  -482,
   428,   360,  -226,  -423,  -478,   362,   361,  -226,  -482,  -226,
   523,   525,   524,   526,   256,  -226,  -226,  -226,   523,  -226,
  -226,  -226,  -226,  -226,  -482,   430,  -482,  -478,  -482,   -76,
   -73,  -482,   -84,   -81,  -478,  -429,  -478,  -226,   433,  -478,
  -482,  -319,  -429,  -478,   525,   524,   528,  -479,  -319,   479,
   217,  -226,   481,  -226,  -479,  -226,  -226,  -319,  -482,  -479,
   101,  -478,  -226,  -479,   438,   100,  -428,   256,  -226,   393,
   525,   524,   529,  -428,   197,   198,   394,  -482,   525,   524,
   534,  -479,   214,   101,  -482,   395,   216,   215,   100,  -478,
  -226,  -427,   657,  -482,   655,   654,   653,   656,  -427,  -478,
  -478,  -478,   -60,  -478,  -226,   101,  -226,  -478,  -478,  -226,
   100,  -482,  -478,   453,  -478,  -478,  -478,  -478,  -478,  -478,
  -478,   479,   197,   198,   484,  -478,  -478,  -478,  -478,  -478,
  -478,  -478,   217,   736,   608,  -424,  -425,   661,   660,   664,
   663,  -478,  -424,  -425,  -478,  -478,  -478,  -478,  -478,  -478,
  -478,  -478,  -478,  -478,  -426,  -478,  -478,  -478,  -478,  -478,
  -430,  -426,   664,   663,   214,   459,   518,  -430,   216,   215,
   212,   213,   460,   519,   479,   454,  -430,   481,   455,  -478,
   694,   458,  -478,  -478,   461,  -478,  -478,   736,   608,  -478,
  -271,  -478,   265,  -478,   293,  -478,   657,  -271,   655,   654,
   653,   656,   256,  -478,   466,   467,  -271,   217,  -478,  -478,
  -478,  -478,  -478,  -478,   473,   474,   293,  -478,  -478,  -479,
  -479,  -479,   217,  -479,   483,   486,   -83,  -479,  -479,   353,
   502,   503,  -479,   647,  -479,  -479,  -479,  -479,  -479,  -479,
  -479,   661,   660,   664,   663,  -479,  -479,  -479,  -479,  -479,
  -479,  -479,   217,   537,   214,   538,   540,  -259,   216,   215,
   542,  -479,   256,   217,  -479,  -479,  -479,  -479,  -479,  -479,
  -479,  -479,  -479,  -479,   217,  -479,  -479,  -479,  -479,  -479,
   468,   217,   217,   570,   214,   500,   577,   469,   216,   215,
   212,   213,   501,   293,   582,   236,   395,   592,   593,  -479,
   551,   499,  -479,  -479,   -69,  -479,  -479,   594,   471,  -479,
   509,  -479,   608,  -479,   293,  -479,   657,   507,   655,   654,
   653,   656,   618,  -479,   498,   626,   508,   674,  -479,  -479,
  -479,  -479,  -479,  -479,   677,  -278,   438,  -479,  -479,    67,
    68,    64,  -278,    51,   438,   695,   -85,    56,    57,   706,
   433,  -278,    60,   433,    58,    59,    61,    23,    24,    65,
    66,   661,   660,   664,   663,    22,    28,    27,    90,    89,
    91,    92,   669,   670,    17,   671,    95,    96,   245,   539,
   709,    41,   710,   717,    94,    93,    84,    50,    86,    85,
    88,    87,    95,    96,   719,    82,    83,    38,    39,    37,
   217,   221,   226,   227,   228,   223,   225,   233,   234,   229,
   230,  -278,   210,   211,  -279,   723,   231,   232,  -278,   202,
   256,  -279,   206,   256,   217,    52,    53,  -278,   217,    54,
  -279,   726,   214,  -259,   220,    40,   216,   215,   212,   213,
   224,   222,   218,    18,   219,   730,   732,   608,    81,    74,
    76,    77,    78,    79,   740,   741,   742,    75,    80,   745,
   101,   235,   747,  -215,  -277,   100,    67,    68,    64,     7,
    51,  -277,   751,   755,    56,    57,  -479,   757,   760,    60,
  -277,    58,    59,    61,    23,    24,    65,    66,   761,   762,
   763,   765,    22,    28,    27,    90,    89,    91,    92,  -260,
   771,    17,   103,   104,   105,   106,   107,     6,    41,     8,
     9,    94,    93,    84,    50,    86,    85,    88,    87,    95,
    96,  -277,    82,    83,    38,    39,    37,  -430,  -277,   825,
   779,  -271,  -278,  -479,  -430,  -279,   826,  -277,  -271,  -278,
   780,   570,  -279,  -430,   570,   824,    36,  -271,  -278,    30,
   256,  -279,    52,    53,   256,   236,    54,  -277,    32,   570,
   792,   793,    40,   657,  -277,   655,   654,   653,   656,  -479,
    18,   794,   799,  -277,   801,    81,    74,    76,    77,    78,
    79,   807,   809,   293,    75,    80,    67,    68,    64,   820,
    51,   827,   353,   828,    56,    57,   829,   831,   832,    60,
   647,    58,    59,    61,   248,   249,    65,    66,   661,   660,
   664,   663,   247,   277,   281,    90,    89,    91,    92,   103,
   104,   105,   106,   107,   834,   837,   841,   842,   278,   848,
   849,    94,    93,    84,    50,    86,    85,    88,    87,    95,
    96,   850,    82,    83,   760,   760,   282,   217,   221,   226,
   227,   228,   223,   225,   233,   234,   229,   230,   761,  -499,
  -499,   863,   570,   231,   232,   570,   774,   473,   876,   206,
   877,   878,    52,    53,   882,   885,    54,   760,   887,   214,
   888,   220,   570,   216,   215,   212,   213,   224,   222,   218,
   570,   219,   570,   nil,   nil,    81,    74,    76,    77,    78,
    79,   nil,   nil,   nil,    75,    80,   nil,    67,    68,    64,
   777,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   103,   104,   105,   106,   107,   nil,   nil,   539,   nil,   278,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,   nil,   nil,   282,   217,   221,
   226,   227,   228,   223,   225,   233,   234,   229,   230,   nil,
   210,   211,   nil,   nil,   231,   232,   nil,   774,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   214,   nil,   220,   nil,   216,   215,   212,   213,   224,   222,
   218,   nil,   219,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,   nil,   nil,   235,
   nil,   857,     5,    67,    68,    64,     7,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,
    28,    27,    90,    89,    91,    92,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   nil,     6,    41,     8,     9,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   217,   221,   226,   227,   228,   223,
   225,   233,   234,   229,   230,   nil,  -499,  -499,   nil,   nil,
   231,   232,   nil,    36,   nil,   nil,    30,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,    32,   214,   nil,   220,    40,
   216,   215,   212,   213,   224,   222,   218,    18,   219,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,     5,    67,    68,    64,     7,    51,   nil,
   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,
    59,    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,
    22,    28,    27,    90,    89,    91,    92,   nil,   nil,    17,
   nil,   nil,   nil,   nil,   nil,     6,    41,     8,     9,    94,
    93,    84,    50,    86,    85,    88,    87,    95,    96,   nil,
    82,    83,    38,    39,    37,   217,  -499,  -499,  -499,  -499,
   223,   225,   nil,   nil,  -499,  -499,   nil,   nil,   nil,   nil,
   nil,   231,   232,   nil,    36,   nil,   nil,   267,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,    32,   214,   nil,   220,
    40,   216,   215,   212,   213,   224,   222,   218,    18,   219,
   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,
   nil,   nil,    75,    80,     5,    67,    68,    64,     7,    51,
   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,    23,    24,    65,    66,   nil,   nil,   nil,
   nil,    22,    28,    27,    90,    89,    91,    92,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,     6,    41,     8,     9,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   217,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   231,   232,   nil,    36,   nil,   nil,    30,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,    32,   214,   nil,
   220,    40,   216,   215,   212,   213,   nil,   nil,   218,    18,
   219,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,     5,    67,    68,    64,     7,
    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,
   nil,    58,    59,    61,    23,    24,    65,    66,   nil,   nil,
   nil,   nil,    22,    28,    27,    90,    89,    91,    92,   nil,
   nil,    17,   nil,   nil,   nil,   nil,   nil,     6,    41,     8,
     9,    94,    93,    84,    50,    86,    85,    88,    87,    95,
    96,   nil,    82,    83,    38,    39,    37,   217,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   231,   232,   nil,    36,   nil,   nil,    30,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,    32,   214,
   nil,   220,    40,   216,   215,   212,   213,   nil,   nil,   218,
    18,   219,   nil,   nil,   nil,    81,    74,    76,    77,    78,
    79,   nil,   nil,   nil,    75,    80,     5,    67,    68,    64,
     7,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,    23,    24,    65,    66,   nil,
   nil,   nil,   nil,    22,    28,    27,    90,    89,    91,    92,
   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,     6,    41,
     8,     9,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   217,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   231,   232,   nil,    36,   nil,   nil,
    30,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,    32,
   214,   nil,   220,    40,   216,   215,   212,   213,   nil,   nil,
   218,    18,   219,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,     5,    67,    68,
    64,     7,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,
   nil,    60,   nil,    58,    59,    61,    23,    24,    65,    66,
   nil,   nil,   nil,   nil,    22,    28,    27,    90,    89,    91,
    92,   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,     6,
    41,     8,     9,    94,    93,    84,    50,    86,    85,    88,
    87,    95,    96,   nil,    82,    83,    38,    39,    37,   217,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   231,   232,   nil,    36,   nil,
   nil,    30,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,
    32,   214,   nil,   220,    40,   216,   215,   212,   213,   nil,
   nil,   218,    18,   219,   nil,   nil,   nil,    81,    74,    76,
    77,    78,    79,   nil,   nil,   nil,    75,    80,     5,    67,
    68,    64,     7,    51,   nil,   nil,   nil,    56,    57,   nil,
   nil,   nil,    60,   nil,    58,    59,    61,    23,    24,    65,
    66,   nil,   nil,   nil,   nil,    22,    28,    27,    90,    89,
    91,    92,   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,
     6,    41,     8,     9,    94,    93,    84,    50,    86,    85,
    88,    87,    95,    96,   nil,    82,    83,    38,    39,    37,
   217,  -499,  -499,  -499,  -499,   223,   225,   nil,   nil,  -499,
  -499,   nil,   nil,   nil,   nil,   nil,   231,   232,   nil,    36,
   nil,   nil,   267,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,    32,   214,   nil,   220,    40,   216,   215,   212,   213,
   224,   222,   218,    18,   219,   nil,   nil,   nil,    81,    74,
    76,    77,    78,    79,   nil,   nil,   nil,    75,    80,     5,
    67,    68,    64,     7,    51,   nil,   nil,   nil,    56,    57,
   nil,   nil,   nil,    60,   nil,    58,    59,    61,    23,    24,
    65,    66,   nil,   nil,   nil,   nil,    22,    28,    27,    90,
    89,    91,    92,   nil,   nil,    17,   nil,   nil,   nil,   nil,
   nil,     6,    41,     8,     9,    94,    93,    84,    50,    86,
    85,    88,    87,    95,    96,   nil,    82,    83,    38,    39,
    37,   217,  -499,  -499,  -499,  -499,   223,   225,   nil,   nil,
  -499,  -499,   nil,   nil,   nil,   nil,   nil,   231,   232,   nil,
    36,   nil,   nil,   267,   nil,   nil,    52,    53,   nil,   nil,
    54,   nil,    32,   214,   nil,   220,    40,   216,   215,   212,
   213,   224,   222,   218,    18,   219,   nil,   nil,   nil,    81,
    74,    76,    77,    78,    79,   nil,   nil,   nil,    75,    80,
     5,    67,    68,    64,     7,    51,   nil,   nil,   nil,    56,
    57,   nil,   nil,   nil,    60,   nil,    58,    59,    61,    23,
    24,    65,    66,   nil,   nil,   nil,   nil,    22,    28,    27,
    90,    89,    91,    92,   nil,   nil,    17,   nil,   nil,   nil,
   nil,   nil,     6,    41,     8,     9,    94,    93,    84,    50,
    86,    85,    88,    87,    95,    96,   nil,    82,    83,    38,
    39,    37,   217,  -499,  -499,  -499,  -499,   223,   225,   nil,
   nil,  -499,  -499,   nil,   nil,   nil,   nil,   nil,   231,   232,
   nil,    36,   nil,   nil,    30,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,    32,   214,   nil,   220,    40,   216,   215,
   212,   213,   224,   222,   218,    18,   219,   nil,   nil,   nil,
    81,    74,    76,    77,    78,    79,   nil,   nil,   nil,    75,
    80,     5,    67,    68,    64,     7,    51,   nil,   nil,   nil,
    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,    61,
    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,    28,
    27,    90,    89,    91,    92,   nil,   nil,    17,   nil,   nil,
   nil,   nil,   nil,     6,    41,     8,     9,    94,    93,    84,
    50,    86,    85,    88,    87,    95,    96,   nil,    82,    83,
    38,    39,    37,   217,  -499,  -499,  -499,  -499,   223,   225,
   nil,   nil,  -499,  -499,   nil,   nil,   nil,   nil,   nil,   231,
   232,   nil,    36,   nil,   nil,    30,   nil,   nil,    52,    53,
   nil,   nil,    54,   nil,    32,   214,   nil,   220,    40,   216,
   215,   212,   213,   224,   222,   218,    18,   219,   nil,   nil,
   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,   nil,
    75,    80,     5,    67,    68,    64,     7,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,
    28,    27,    90,    89,    91,    92,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   nil,     6,    41,     8,     9,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   217,  -499,  -499,  -499,  -499,   223,
   225,   nil,   nil,  -499,  -499,   nil,   nil,   nil,   nil,   nil,
   231,   232,   nil,    36,   nil,   nil,    30,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,    32,   214,   nil,   220,    40,
   216,   215,   212,   213,   224,   222,   218,    18,   219,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,     5,    67,    68,    64,     7,    51,   nil,
   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,
    59,    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,
    22,    28,    27,    90,    89,    91,    92,   nil,   nil,    17,
   nil,   nil,   nil,   nil,   nil,     6,    41,     8,     9,    94,
    93,    84,    50,    86,    85,    88,    87,    95,    96,   nil,
    82,    83,    38,    39,    37,   217,   221,   226,   227,   228,
   223,   225,   nil,   nil,   229,   230,   nil,   nil,   nil,   nil,
   nil,   231,   232,   nil,    36,   nil,   nil,    30,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,    32,   214,   nil,   220,
    40,   216,   215,   212,   213,   224,   222,   218,    18,   219,
   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,
   nil,   nil,    75,    80,     5,    67,    68,    64,     7,    51,
   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,    23,    24,    65,    66,   nil,   nil,   nil,
   nil,    22,    28,    27,    90,    89,    91,    92,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,     6,    41,     8,     9,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   217,   221,   226,   227,
   228,   223,   225,   233,   nil,   229,   230,   nil,   nil,   nil,
   nil,   nil,   231,   232,   nil,    36,   nil,   nil,    30,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,    32,   214,   nil,
   220,    40,   216,   215,   212,   213,   224,   222,   218,    18,
   219,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,     5,    67,    68,    64,     7,
    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,
   nil,    58,    59,    61,    23,    24,    65,    66,   nil,   nil,
   nil,   nil,    22,    28,    27,    90,    89,    91,    92,   nil,
   nil,    17,   nil,   nil,   nil,   nil,   nil,     6,    41,     8,
     9,    94,    93,    84,    50,    86,    85,    88,    87,    95,
    96,   nil,    82,    83,    38,    39,    37,   217,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   231,   232,   nil,    36,   nil,   nil,    30,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,    32,   214,
   nil,   220,    40,   216,   215,   212,   213,   nil,   nil,   nil,
    18,   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,
    79,   nil,   nil,   nil,    75,    80,     5,    67,    68,    64,
     7,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,    23,    24,    65,    66,   nil,
   nil,   nil,   nil,    22,    28,    27,    90,    89,    91,    92,
   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,     6,    41,
     8,     9,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   217,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   231,   232,   nil,    36,   nil,   nil,
    30,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,    32,
   214,   nil,   220,    40,   216,   215,   212,   213,   nil,   nil,
   nil,    18,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,     5,    67,    68,
    64,     7,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,
   nil,    60,   nil,    58,    59,    61,    23,    24,    65,    66,
   nil,   nil,   nil,   nil,    22,    28,    27,    90,    89,    91,
    92,   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,     6,
    41,     8,     9,    94,    93,    84,    50,    86,    85,    88,
    87,    95,    96,   nil,    82,    83,    38,    39,    37,   217,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   231,   232,   nil,    36,   nil,
   nil,    30,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,
    32,   214,   nil,   nil,    40,   216,   215,   212,   213,   nil,
   nil,   nil,    18,   nil,   nil,   nil,   nil,    81,    74,    76,
    77,    78,    79,   nil,   nil,   nil,    75,    80,     5,    67,
    68,    64,     7,    51,   nil,   nil,   nil,    56,    57,   nil,
   nil,   nil,    60,   nil,    58,    59,    61,    23,    24,    65,
    66,   nil,   nil,   nil,   nil,    22,    28,    27,    90,    89,
    91,    92,   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,
     6,    41,     8,     9,    94,    93,    84,    50,    86,    85,
    88,    87,    95,    96,   nil,    82,    83,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    36,
   nil,   nil,    30,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,    32,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    18,   nil,   nil,   nil,   nil,    81,    74,
    76,    77,    78,    79,   nil,   nil,   nil,    75,    80,     5,
    67,    68,    64,     7,    51,   nil,   nil,   nil,    56,    57,
   nil,   nil,   nil,    60,   nil,    58,    59,    61,    23,    24,
    65,    66,   nil,   nil,   nil,   nil,    22,    28,    27,    90,
    89,    91,    92,   nil,   nil,    17,   nil,   nil,   nil,   nil,
   nil,     6,    41,     8,     9,    94,    93,    84,    50,    86,
    85,    88,    87,    95,    96,   nil,    82,    83,    38,    39,
    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    36,   nil,   nil,    30,   nil,   nil,    52,    53,   nil,   nil,
    54,   nil,    32,   nil,   nil,   nil,    40,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    18,   nil,   nil,   nil,   nil,    81,
    74,    76,    77,    78,    79,   nil,   nil,   nil,    75,    80,
     5,    67,    68,    64,     7,    51,   nil,   nil,   nil,    56,
    57,   nil,   nil,   nil,    60,   nil,    58,    59,    61,    23,
    24,    65,    66,   nil,   nil,   nil,   nil,    22,    28,    27,
    90,    89,    91,    92,   nil,   nil,    17,   nil,   nil,   nil,
   nil,   nil,     6,    41,     8,     9,    94,    93,    84,    50,
    86,    85,    88,    87,    95,    96,   nil,    82,    83,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    36,   nil,   nil,    30,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,    32,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,   nil,   nil,
    81,    74,    76,    77,    78,    79,   nil,   nil,   nil,    75,
    80,     5,    67,    68,    64,     7,    51,   nil,   nil,   nil,
    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,    61,
    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,    28,
    27,    90,    89,    91,    92,   nil,   nil,    17,   nil,   nil,
   nil,   nil,   nil,     6,    41,     8,     9,    94,    93,    84,
    50,    86,    85,    88,    87,    95,    96,   nil,    82,    83,
    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    36,   nil,   nil,    30,   nil,   nil,    52,    53,
   nil,   nil,    54,   nil,    32,   nil,   nil,   nil,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,   nil,
   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,   nil,
    75,    80,     5,    67,    68,    64,     7,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,
    28,    27,    90,    89,    91,    92,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   nil,     6,    41,     8,     9,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    36,   nil,   nil,    30,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,    32,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,     5,    67,    68,    64,     7,    51,   nil,
   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,
    59,    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,
    22,    28,    27,    90,    89,    91,    92,   nil,   nil,    17,
   nil,   nil,   nil,   nil,   nil,     6,    41,     8,     9,    94,
    93,    84,    50,    86,    85,    88,    87,    95,    96,   nil,
    82,    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    36,   nil,   nil,    30,   nil,   nil,
    52,    53,   nil,   nil,    54,   nil,    32,   nil,   nil,   nil,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,
   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,
   nil,   nil,    75,    80,     5,    67,    68,    64,     7,    51,
   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,    23,    24,    65,    66,   nil,   nil,   nil,
   nil,    22,    28,    27,    90,    89,    91,    92,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,     6,    41,     8,     9,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    36,   nil,   nil,    30,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,    32,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,
   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,     5,    67,    68,    64,     7,
    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,
   nil,    58,    59,    61,    23,    24,    65,    66,   nil,   nil,
   nil,   nil,    22,    28,    27,    90,    89,    91,    92,   nil,
   nil,    17,   nil,   nil,   nil,   nil,   nil,     6,    41,     8,
     9,    94,    93,    84,    50,    86,    85,    88,    87,    95,
    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    36,   nil,   nil,    30,
   nil,   nil,    52,    53,   nil,   nil,    54,   nil,    32,   nil,
   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    18,   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,
    79,   nil,   nil,   nil,    75,    80,     5,    67,    68,    64,
     7,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,    23,    24,    65,    66,   nil,
   nil,   nil,   nil,    22,    28,    27,    90,    89,    91,    92,
   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,     6,    41,
     8,     9,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    36,   nil,   nil,
    30,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,    32,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    18,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,     5,    67,    68,
    64,     7,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,
   nil,    60,   nil,    58,    59,    61,    23,    24,    65,    66,
   nil,   nil,   nil,   nil,    22,    28,    27,    90,    89,    91,
    92,   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,     6,
    41,     8,     9,    94,    93,    84,    50,    86,    85,    88,
    87,    95,    96,   nil,    82,    83,    38,    39,    37,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    36,   nil,
   nil,    30,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,
    32,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    18,   nil,   nil,   nil,   nil,    81,    74,    76,
    77,    78,    79,   nil,   nil,   nil,    75,    80,     5,    67,
    68,    64,     7,    51,   nil,   nil,   nil,    56,    57,   nil,
   nil,   nil,    60,   nil,    58,    59,    61,    23,    24,    65,
    66,   nil,   nil,   nil,   nil,    22,    28,    27,    90,    89,
    91,    92,   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,
     6,    41,     8,     9,    94,    93,    84,    50,    86,    85,
    88,    87,    95,    96,   nil,    82,    83,    38,    39,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    36,
   nil,   nil,    30,   nil,   nil,    52,    53,   nil,   nil,    54,
   nil,    32,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    18,   nil,   nil,   nil,   nil,    81,    74,
    76,    77,    78,    79,   nil,   nil,   nil,    75,    80,     5,
    67,    68,    64,     7,    51,   nil,   nil,   nil,    56,    57,
   nil,   nil,   nil,    60,   nil,    58,    59,    61,    23,    24,
    65,    66,   nil,   nil,   nil,   nil,    22,    28,    27,    90,
    89,    91,    92,   nil,   nil,    17,   nil,   nil,   nil,   nil,
   nil,     6,    41,     8,     9,    94,    93,    84,    50,    86,
    85,    88,    87,    95,    96,   nil,    82,    83,    38,    39,
    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    36,   nil,   nil,    30,   nil,   nil,    52,    53,   nil,   nil,
    54,   nil,    32,   nil,   nil,   nil,    40,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    18,   nil,   nil,   nil,   nil,    81,
    74,    76,    77,    78,    79,   nil,   nil,   nil,    75,    80,
     5,    67,    68,    64,     7,    51,   nil,   nil,   nil,    56,
    57,   nil,   nil,   nil,    60,   nil,    58,    59,    61,    23,
    24,    65,    66,   nil,   nil,   nil,   nil,    22,    28,    27,
    90,    89,    91,    92,   nil,   nil,    17,   nil,   nil,   nil,
   nil,   nil,     6,    41,     8,     9,    94,    93,    84,    50,
    86,    85,    88,    87,    95,    96,   nil,    82,    83,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    36,   nil,   nil,    30,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,    32,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,   nil,   nil,
    81,    74,    76,    77,    78,    79,   nil,   nil,   nil,    75,
    80,    67,    68,    64,     7,    51,   nil,   nil,   nil,    56,
    57,   nil,   nil,   nil,    60,   nil,    58,    59,    61,    23,
    24,    65,    66,   nil,   nil,   nil,   nil,    22,    28,    27,
    90,    89,    91,    92,   nil,   nil,    17,   nil,   nil,   nil,
   nil,   nil,     6,    41,     8,     9,    94,    93,    84,    50,
    86,    85,    88,    87,    95,    96,   nil,    82,    83,    38,
    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    36,   nil,   nil,    30,   nil,   nil,    52,    53,   nil,
   nil,    54,   nil,    32,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,   nil,   nil,
    81,    74,    76,    77,    78,    79,   nil,   nil,   nil,    75,
    80,   155,   166,   156,   179,   152,   172,   162,   161,   182,
   183,   177,   160,   159,   154,   180,   184,   185,   164,   153,
   167,   171,   173,   165,   158,   nil,   nil,   174,   181,   176,
   175,   168,   178,   163,   151,   170,   169,   nil,   nil,   nil,
   nil,   nil,   150,   157,   148,   149,   146,   147,   111,   113,
   110,   nil,   112,   nil,   nil,   nil,   nil,   nil,   nil,   141,
   142,   nil,   139,   123,   124,   125,   nil,   128,   130,   nil,
   nil,   126,   nil,   nil,   nil,   nil,   143,   144,   131,   132,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   136,   135,   nil,   122,   140,   138,   137,
   133,   134,   129,   127,   120,   nil,   121,   nil,   nil,   145,
    81,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    80,   155,   166,   156,   179,   152,   172,   162,   161,   182,
   183,   177,   160,   159,   154,   180,   184,   185,   164,   153,
   167,   171,   173,   165,   158,   nil,   nil,   174,   181,   176,
   175,   168,   178,   163,   151,   170,   169,   nil,   nil,   nil,
   nil,   nil,   150,   157,   148,   149,   146,   147,   111,   113,
   nil,   nil,   112,   nil,   nil,   nil,   nil,   nil,   nil,   141,
   142,   nil,   139,   123,   124,   125,   nil,   128,   130,   nil,
   nil,   126,   nil,   nil,   nil,   nil,   143,   144,   131,   132,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   136,   135,   nil,   122,   140,   138,   137,
   133,   134,   129,   127,   120,   nil,   121,   nil,   nil,   145,
    81,   nil,   nil,    67,    68,    64,   nil,    51,   nil,   nil,
    80,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,
    28,    27,    90,    89,    91,    92,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,
    28,    27,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
    28,    27,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   243,   nil,   245,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
    28,    27,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   243,   nil,   245,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
    28,    27,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   243,   nil,   245,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,  -249,  -249,  -249,   nil,  -249,   nil,   nil,
   nil,  -249,  -249,   nil,   nil,   nil,  -249,   nil,  -249,  -249,
  -249,  -249,  -249,  -249,  -249,   nil,   nil,   nil,   nil,  -249,
  -249,  -249,  -249,  -249,  -249,  -249,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,  -249,   nil,   nil,  -249,  -249,
  -249,  -249,  -249,  -249,  -249,  -249,  -249,  -249,   nil,  -249,
  -249,  -249,  -249,  -249,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,  -249,   nil,   nil,  -249,   256,   nil,  -249,
  -249,   nil,   nil,  -249,   nil,  -249,   nil,  -249,   nil,  -249,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -249,   nil,   nil,
   nil,   nil,  -249,  -249,  -249,  -249,  -249,  -249,   nil,   nil,
   nil,  -249,  -249,  -249,  -249,  -249,   nil,  -249,   nil,   nil,
   nil,  -249,  -249,   nil,   nil,   nil,  -249,   nil,  -249,  -249,
  -249,  -249,  -249,  -249,  -249,   nil,   nil,   nil,   nil,  -249,
  -249,  -249,  -249,  -249,  -249,  -249,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,  -249,   nil,   nil,  -249,  -249,
  -249,  -249,  -249,  -249,  -249,  -249,  -249,  -249,   nil,  -249,
  -249,  -249,  -249,  -249,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,  -249,   nil,   nil,  -249,   265,   nil,  -249,
  -249,   nil,   nil,  -249,   nil,  -249,   nil,  -249,   nil,  -249,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -249,   nil,   nil,
   nil,   nil,  -249,  -249,  -249,  -249,  -249,  -249,   nil,   nil,
   nil,  -249,  -249,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   278,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,   nil,   nil,   282,   nil,   217,   221,   226,   227,   228,
   223,   225,   233,   234,   229,   230,   nil,   210,   211,   nil,
   nil,   231,   232,   275,   nil,   nil,   272,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   271,   nil,   214,   nil,   220,
   nil,   216,   215,   212,   213,   224,   222,   218,   nil,   219,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   235,    51,   570,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   278,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,   nil,   nil,   282,   nil,   217,   221,   226,   227,   228,
   223,   225,   233,   234,   229,   230,   nil,   210,   211,   nil,
   nil,   231,   232,   275,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   214,   nil,   220,
   nil,   216,   215,   212,   213,   224,   222,   218,   nil,   219,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   235,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,
    28,    27,    90,    89,    91,    92,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
    28,    27,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   301,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,
    28,    27,    90,    89,    91,    92,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,
    28,    27,    90,    89,    91,    92,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   278,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,   nil,   nil,   282,   nil,   217,   221,   226,   227,   228,
   223,   225,   233,   234,   229,   230,   nil,   210,   211,   nil,
   nil,   231,   232,   317,   nil,   nil,    30,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,    32,   nil,   214,   nil,   220,
   nil,   216,   215,   212,   213,   224,   222,   218,   nil,   219,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   235,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   278,   nil,   nil,    94,    93,
   322,    50,    86,    85,   323,    87,    95,    96,   nil,    82,
    83,   nil,   nil,   282,   nil,   217,   221,   226,   227,   228,
   223,   225,   233,   234,   229,   230,   nil,   210,   211,   nil,
   329,   231,   232,   324,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   214,   nil,   220,
   nil,   216,   215,   212,   213,   224,   222,   218,   nil,   219,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   235,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   278,   nil,   nil,    94,    93,
   322,    50,    86,    85,   323,    87,    95,    96,   nil,    82,
    83,   nil,   nil,   282,   nil,   217,   221,   226,   227,   228,
   223,   225,   233,   234,   229,   230,   nil,   210,   211,   nil,
   nil,   231,   232,   324,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   214,   nil,   220,
   nil,   216,   215,   212,   213,   224,   222,   218,   nil,   219,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,  -475,  -475,  -475,   235,  -475,   nil,   nil,
   nil,  -475,  -475,   nil,   nil,   nil,  -475,   nil,  -475,  -475,
  -475,  -475,  -475,  -475,  -475,   nil,  -475,   nil,   nil,  -475,
  -475,  -475,  -475,  -475,  -475,  -475,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,  -475,   nil,   nil,  -475,  -475,
  -475,  -475,  -475,  -475,  -475,  -475,  -475,  -475,   nil,  -475,
  -475,  -475,  -475,  -475,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,  -475,   nil,   nil,  -475,  -475,   nil,  -475,
  -475,   nil,   nil,  -475,   nil,  -475,   nil,  -475,   nil,  -475,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -475,   nil,  -475,
   nil,   nil,  -475,  -475,  -475,  -475,  -475,  -475,   nil,   nil,
   nil,  -475,  -475,  -476,  -476,  -476,   nil,  -476,   nil,   nil,
   nil,  -476,  -476,   nil,   nil,   nil,  -476,   nil,  -476,  -476,
  -476,  -476,  -476,  -476,  -476,   nil,  -476,   nil,   nil,  -476,
  -476,  -476,  -476,  -476,  -476,  -476,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,  -476,   nil,   nil,  -476,  -476,
  -476,  -476,  -476,  -476,  -476,  -476,  -476,  -476,   nil,  -476,
  -476,  -476,  -476,  -476,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,  -476,   nil,   nil,  -476,  -476,   nil,  -476,
  -476,   nil,   nil,  -476,   nil,  -476,   nil,  -476,   nil,  -476,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -476,   nil,  -476,
   nil,   nil,  -476,  -476,  -476,  -476,  -476,  -476,   nil,   nil,
   nil,  -476,  -476,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,
    28,    27,    90,    89,    91,    92,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,
    28,    27,    90,    89,    91,    92,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,
    28,    27,    90,    89,    91,    92,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,
    28,    27,    90,    89,    91,    92,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,     7,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,
    28,    27,    90,    89,    91,    92,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   nil,     6,    41,     8,     9,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    36,   nil,   nil,    30,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,    32,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,
    28,    27,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   371,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,
    28,    27,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   371,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,
    28,    27,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
    28,    27,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   301,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,
    28,    27,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,
    28,    27,    90,    89,    91,    92,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,
    28,    27,    90,    89,    91,    92,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
    28,    27,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   243,   nil,   245,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
    28,    27,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,   447,
    53,   nil,   nil,    54,   nil,   243,   nil,   245,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
    28,    27,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   451,    52,
    53,   nil,   nil,    54,   nil,   243,   nil,   245,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   278,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,   nil,   nil,   282,   nil,   217,   221,   226,   227,   228,
   223,   225,   233,   234,   229,   230,   nil,   210,   211,   nil,
   nil,   231,   232,   275,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   214,   nil,   220,
   nil,   216,   215,   212,   213,   224,   222,   218,   nil,   219,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   235,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   471,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,   248,   249,    65,    66,   nil,   nil,   nil,   nil,   247,
   277,   281,    90,    89,    91,    92,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,
    28,    27,    90,    89,    91,    92,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,
    28,    27,    90,    89,    91,    92,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,
    28,    27,    90,    89,    91,    92,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,    67,    68,    64,   nil,    51,   nil,   nil,
   nil,    56,    57,   nil,   nil,   nil,    60,   nil,    58,    59,
    61,    23,    24,    65,    66,   nil,   nil,   nil,   nil,    22,
    28,    27,    90,    89,    91,    92,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,    94,    93,
    84,    50,    86,    85,    88,    87,    95,    96,   nil,    82,
    83,    38,    39,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,   nil,    52,
    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,
   nil,   nil,    81,    74,    76,    77,    78,    79,   nil,   nil,
   nil,    75,    80,   155,   166,   156,   179,   152,   172,   162,
   161,   182,   183,   177,   160,   159,   154,   180,   184,   185,
   164,   153,   167,   171,   173,   165,   158,   nil,   nil,   174,
   181,   176,   175,   168,   178,   163,   151,   170,   169,   nil,
   nil,   nil,   nil,   nil,   150,   157,   148,   149,   146,   147,
   111,   113,   nil,   nil,   112,   nil,   nil,   nil,   nil,   nil,
   nil,   141,   142,   nil,   139,   123,   124,   125,   nil,   128,
   130,   nil,   nil,   126,   nil,   nil,   nil,   nil,   143,   144,
   131,   132,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   136,   135,   nil,   122,   140,
   138,   137,   133,   134,   129,   127,   120,   nil,   121,   nil,
   nil,   145,    81,   nil,   nil,    67,    68,    64,   nil,    51,
   nil,   nil,    80,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,   248,   249,    65,    66,   nil,   nil,   nil,
   nil,   247,   277,   281,    90,    89,    91,    92,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,
   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,  -249,  -249,  -249,   nil,  -249,
   nil,   nil,   nil,  -249,  -249,   nil,   nil,   nil,  -249,   nil,
  -249,  -249,  -249,  -249,  -249,  -249,  -249,   nil,   nil,   nil,
   nil,  -249,  -249,  -249,  -249,  -249,  -249,  -249,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -249,   nil,   nil,
  -249,  -249,  -249,  -249,  -249,  -249,  -249,  -249,  -249,  -249,
   nil,  -249,  -249,  -249,  -249,  -249,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,  -249,   nil,   nil,  -249,   256,
   nil,  -249,  -249,   nil,   nil,  -249,   nil,  -249,   nil,  -249,
   nil,  -249,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -249,
   nil,   nil,   nil,   nil,  -249,  -249,  -249,  -249,  -249,  -249,
   nil,   nil,   nil,  -249,  -249,  -480,  -480,  -480,   nil,  -480,
   nil,   nil,   nil,  -480,  -480,   nil,   nil,   nil,  -480,   nil,
  -480,  -480,  -480,  -480,  -480,  -480,  -480,   nil,   nil,   nil,
   nil,  -480,  -480,  -480,  -480,  -480,  -480,  -480,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -480,   nil,   nil,
  -480,  -480,  -480,  -480,  -480,  -480,  -480,  -480,  -480,  -480,
   nil,  -480,  -480,  -480,  -480,  -480,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,  -480,   nil,   nil,  -480,  -480,
   nil,  -480,  -480,   nil,   nil,  -480,   nil,  -480,   nil,  -480,
   nil,  -480,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -480,
   nil,   nil,   nil,   nil,  -480,  -480,  -480,  -480,  -480,  -480,
   nil,   nil,   nil,  -480,  -480,  -481,  -481,  -481,   nil,  -481,
   nil,   nil,   nil,  -481,  -481,   nil,   nil,   nil,  -481,   nil,
  -481,  -481,  -481,  -481,  -481,  -481,  -481,   nil,   nil,   nil,
   nil,  -481,  -481,  -481,  -481,  -481,  -481,  -481,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -481,   nil,   nil,
  -481,  -481,  -481,  -481,  -481,  -481,  -481,  -481,  -481,  -481,
   nil,  -481,  -481,  -481,  -481,  -481,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,  -481,   nil,   nil,  -481,  -481,
   nil,  -481,  -481,   nil,   nil,  -481,   nil,  -481,   nil,  -481,
   nil,  -481,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -481,
   nil,   nil,   nil,   nil,  -481,  -481,  -481,  -481,  -481,  -481,
   nil,   nil,   nil,  -481,  -481,    67,    68,    64,   nil,    51,
   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,   248,   249,    65,    66,   nil,   nil,   nil,
   nil,   247,    28,    27,    90,    89,    91,    92,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   301,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,
   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,    67,    68,    64,   nil,    51,
   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,   248,   249,    65,    66,   nil,   nil,   nil,
   nil,   247,   277,   281,    90,    89,    91,    92,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,
   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,    67,    68,    64,   nil,    51,
   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,   248,   249,    65,    66,   nil,   nil,   nil,
   nil,   247,   277,   281,    90,    89,    91,    92,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,
   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,    67,    68,    64,   nil,    51,
   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,   248,   249,    65,    66,   nil,   nil,   nil,
   nil,   247,   277,   281,    90,    89,    91,    92,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,
   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,    67,    68,    64,   nil,    51,
   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,   248,   249,    65,    66,   nil,   nil,   nil,
   nil,   247,   277,   281,    90,    89,    91,    92,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   562,   nil,   245,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,
   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,    67,    68,    64,   nil,    51,
   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,   248,   249,    65,    66,   nil,   nil,   nil,
   nil,   247,   277,   281,    90,    89,    91,    92,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   566,   nil,   245,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,
   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,    67,    68,    64,   nil,    51,
   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,   248,   249,    65,    66,   nil,   nil,   nil,
   nil,   247,   277,   281,    90,    89,    91,    92,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,
   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,    67,    68,    64,   nil,    51,
   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,    23,    24,    65,    66,   nil,   nil,   nil,
   nil,    22,    28,    27,    90,    89,    91,    92,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   586,   nil,   245,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,
   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,    67,    68,    64,   nil,    51,
   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,   248,   249,    65,    66,   nil,   nil,   nil,
   nil,   247,    28,    27,    90,    89,    91,    92,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   301,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,
   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,    67,    68,    64,   nil,    51,
   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,   248,   249,    65,    66,   nil,   nil,   nil,
   nil,   247,   277,   281,    90,    89,    91,    92,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,
   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,    67,    68,    64,   nil,    51,
   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,   248,   249,    65,    66,   nil,   nil,   nil,
   nil,   247,   277,   281,    90,    89,    91,    92,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,
   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,    67,    68,    64,   nil,    51,
   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,   248,   249,    65,    66,   nil,   nil,   nil,
   nil,   247,   277,   281,    90,    89,    91,    92,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,
   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,    67,    68,    64,   nil,    51,
   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,    23,    24,    65,    66,   nil,   nil,   nil,
   nil,    22,    28,    27,    90,    89,    91,    92,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,
   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,    67,    68,    64,   nil,    51,
   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,   248,   249,    65,    66,   nil,   nil,   nil,
   nil,   247,   277,   281,    90,    89,    91,    92,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   371,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,
   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,    67,    68,    64,   nil,    51,
   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,   248,   249,    65,    66,   nil,   nil,   nil,
   nil,   247,   277,   281,    90,    89,    91,    92,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   614,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,
   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,    67,    68,    64,   nil,    51,
   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,   248,   249,    65,    66,   nil,   nil,   nil,
   nil,   247,   277,   281,    90,    89,    91,    92,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,
   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,    67,    68,    64,   nil,    51,
   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,   248,   249,    65,    66,   nil,   nil,   nil,
   nil,   247,   277,   281,    90,    89,    91,    92,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,
   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,    67,    68,    64,   nil,    51,
   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,   248,   249,    65,    66,   nil,   nil,   nil,
   nil,   247,   277,   281,    90,    89,    91,    92,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   630,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,
   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,    67,    68,    64,   nil,    51,
   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,   248,   249,    65,    66,   nil,   nil,   nil,
   nil,   247,    28,    27,    90,    89,    91,    92,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   301,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,
   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,    67,    68,    64,   nil,    51,
   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,   248,   249,    65,    66,   nil,   nil,   nil,
   nil,   247,    28,    27,    90,    89,    91,    92,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   301,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,
   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,    67,    68,    64,   nil,    51,
   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,    60,   nil,
    58,    59,    61,    23,    24,    65,    66,   nil,   nil,   nil,
   nil,    22,    28,    27,    90,    89,    91,    92,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
    94,    93,    84,    50,    86,    85,    88,    87,    95,    96,
   nil,    82,    83,    38,    39,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,   206,   nil,
   nil,    52,    53,   nil,   nil,    54,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,
   nil,   nil,   nil,   nil,    81,    74,    76,    77,    78,    79,
   nil,   nil,   nil,    75,    80,   155,   166,   156,   179,   152,
   172,   162,   161,   182,   183,   177,   160,   159,   154,   180,
   184,   185,   164,   153,   167,   171,   173,   165,   158,   nil,
   nil,   174,   181,   176,   175,   168,   178,   163,   151,   170,
   169,   nil,   nil,   nil,   nil,   nil,   150,   157,   148,   149,
   146,   147,   111,   113,   nil,   nil,   112,   nil,   nil,   nil,
   nil,   nil,   nil,   141,   142,   nil,   139,   123,   124,   125,
   nil,   128,   130,   nil,   nil,   126,   nil,   nil,   nil,   nil,
   143,   144,   131,   132,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   136,   135,   nil,
   122,   140,   138,   137,   133,   134,   129,   127,   120,   nil,
   121,   nil,   nil,   145,    81,   nil,   nil,    67,    68,    64,
   nil,    51,   nil,   nil,    80,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   681,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,    23,    24,    65,    66,   nil,
   nil,   nil,   nil,    22,    28,    27,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,    23,    24,    65,    66,   nil,
   nil,   nil,   nil,    22,    28,    27,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,    23,    24,    65,    66,   nil,
   nil,   nil,   nil,    22,    28,    27,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   278,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,   nil,   nil,   282,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   275,   nil,   nil,
   272,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   699,
   nil,   700,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   701,   nil,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,    28,    27,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   562,
   nil,   245,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,    23,    24,    65,    66,   nil,
   nil,   nil,   nil,    22,    28,    27,    90,    89,    91,    92,
   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    18,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,    23,    24,    65,    66,   nil,
   nil,   nil,   nil,    22,    28,    27,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   278,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,   nil,   nil,   282,   nil,   217,
   221,   226,   227,   228,   223,   225,   233,   234,   229,   230,
   nil,   210,   211,   nil,   nil,   231,   232,   774,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   214,   nil,   220,   nil,   216,   215,   212,   213,   224,
   222,   218,   nil,   219,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   235,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   782,
   nil,   245,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   788,
   nil,   245,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   790,
   nil,   245,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   278,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,   nil,   nil,   282,   nil,   217,
   221,   226,   227,   228,   223,   225,   233,   234,   229,   230,
   nil,   210,   211,   nil,   nil,   231,   232,   774,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   214,   nil,   220,   nil,   216,   215,   212,   213,   224,
   222,   218,   nil,   219,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   235,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,    23,    24,    65,    66,   nil,
   nil,   nil,   nil,    22,    28,    27,    90,    89,    91,    92,
   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    18,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   804,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   278,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,   nil,   nil,   282,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   275,   nil,   nil,
   272,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   823,
   nil,   822,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   278,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,   nil,   nil,   282,   nil,   217,
   221,   226,   227,   228,   223,   225,   233,   234,   229,   230,
   nil,   210,   211,   nil,   nil,   231,   232,   774,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   214,   nil,   220,   nil,   216,   215,   212,   213,   224,
   222,   218,   nil,   219,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   235,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,    28,    27,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   301,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   278,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,   nil,   nil,   282,   nil,   217,
   221,   226,   227,   228,   223,   225,   233,   234,   229,   230,
   nil,   210,   211,   nil,   nil,   231,   232,   774,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   214,   nil,   220,   nil,   216,   215,   212,   213,   224,
   222,   218,   nil,   219,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   235,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   866,
   nil,   245,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   869,
   nil,   245,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   278,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,   nil,   nil,   282,   nil,   217,
   221,   226,   227,   228,   223,   225,   233,   234,   229,   230,
   nil,   210,   211,   nil,   nil,   231,   232,   774,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   214,   nil,   220,   nil,   216,   215,   212,   213,   224,
   222,   218,   nil,   219,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   235,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   278,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,   nil,   nil,   282,   nil,   217,
   221,   226,   227,   228,   223,   225,   233,   234,   229,   230,
   nil,   210,   211,   nil,   nil,   231,   232,   774,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   214,   nil,   220,   nil,   216,   215,   212,   213,   224,
   222,   218,   nil,   219,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   235,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   891,
   nil,   245,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   nil,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   278,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,   nil,   nil,   282,   nil,   217,
   221,   226,   227,   228,   223,   225,   233,   234,   229,   230,
   nil,   210,   211,   nil,   nil,   231,   232,   774,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   214,   nil,   220,   nil,   216,   215,   212,   213,   224,
   222,   218,   nil,   219,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,    67,    68,    64,
   235,    51,   nil,   nil,   nil,    56,    57,   nil,   nil,   nil,
    60,   nil,    58,    59,    61,   248,   249,    65,    66,   nil,
   nil,   nil,   nil,   247,   277,   281,    90,    89,    91,    92,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,   nil,    94,    93,    84,    50,    86,    85,    88,    87,
    95,    96,   nil,    82,    83,    38,    39,    37,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   206,   nil,   nil,    52,    53,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,    81,    74,    76,    77,
    78,    79,   nil,   nil,   nil,    75,    80,   155,   166,   156,
   179,   152,   172,   162,   161,   182,   183,   177,   160,   159,
   154,   180,   184,   185,   164,   153,   167,   171,   173,   165,
   158,   nil,   nil,   174,   181,   176,   338,   337,   339,   336,
   151,   170,   169,   nil,   nil,   nil,   nil,   nil,   150,   157,
   148,   149,   334,   335,   332,   113,    86,    85,   333,    87,
   nil,   nil,   nil,   nil,   nil,   141,   142,   nil,   139,   123,
   124,   125,   nil,   128,   130,   nil,   nil,   126,   nil,   nil,
   nil,   nil,   143,   144,   131,   132,   nil,   nil,   nil,   nil,
   nil,   343,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   136,
   135,   nil,   122,   140,   138,   137,   133,   134,   129,   127,
   120,   nil,   121,   nil,   nil,   145,   155,   166,   156,   179,
   152,   172,   162,   161,   182,   183,   177,   160,   159,   154,
   180,   184,   185,   164,   153,   167,   171,   173,   165,   158,
   nil,   nil,   174,   181,   176,   175,   168,   178,   163,   151,
   170,   169,   nil,   nil,   nil,   nil,   nil,   150,   157,   148,
   149,   146,   147,   111,   113,   nil,   nil,   112,   nil,   nil,
   nil,   nil,   nil,   nil,   141,   142,   nil,   139,   123,   124,
   125,   nil,   128,   130,   nil,   nil,   126,   nil,   nil,   nil,
   nil,   143,   144,   131,   132,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   136,   135,
   nil,   122,   140,   138,   137,   133,   134,   129,   127,   120,
   nil,   121,   nil,   nil,   145,   217,   221,   226,   227,   228,
   223,   225,   233,   234,   229,   230,   nil,   210,   211,   nil,
   nil,   231,   232,   nil,   nil,   nil,  -215,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   220,
   nil,   216,   215,   212,   213,   224,   222,   218,   nil,   219,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,  -215,   217,
   221,   226,   227,   228,   223,   225,   233,   234,   229,   230,
   nil,   210,   211,   nil,   nil,   231,   232,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   220,   nil,   216,   215,   212,   213,   224,
   222,   218,   nil,   219,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   378,   381,   nil,   nil,   379,   nil,   nil,   nil,   nil,
   235,   558,   141,   142,   nil,   139,   123,   124,   125,   nil,
   128,   130,   nil,   nil,   126,   nil,   nil,   nil,   nil,   143,
   144,   131,   132,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   136,   135,   nil,   122,
   140,   138,   137,   133,   134,   129,   127,   120,   nil,   121,
   383,   387,   145,   nil,   385,   nil,   nil,   nil,   nil,   nil,
   nil,   141,   142,   nil,   139,   123,   124,   125,   nil,   128,
   130,   nil,   nil,   126,   nil,   nil,   nil,   nil,   143,   144,
   131,   132,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   136,   135,   nil,   122,   140,
   138,   137,   133,   134,   129,   127,   120,   nil,   121,   435,
   381,   145,   nil,   436,   nil,   nil,   nil,   nil,   nil,   nil,
   141,   142,   nil,   139,   123,   124,   125,   nil,   128,   130,
   nil,   nil,   126,   nil,   nil,   nil,   nil,   143,   144,   131,
   132,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   136,   135,   nil,   122,   140,   138,
   137,   133,   134,   129,   127,   120,   nil,   121,   435,   381,
   145,   nil,   436,   nil,   nil,   nil,   nil,   nil,   nil,   141,
   142,   nil,   139,   123,   124,   125,   nil,   128,   130,   nil,
   nil,   126,   nil,   nil,   nil,   nil,   143,   144,   131,   132,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   136,   135,   nil,   122,   140,   138,   137,
   133,   134,   129,   127,   120,   nil,   121,   552,   381,   145,
   nil,   553,   nil,   nil,   nil,   nil,   nil,   nil,   141,   142,
   nil,   139,   123,   124,   125,   nil,   128,   130,   nil,   nil,
   126,   nil,   nil,   nil,   nil,   143,   144,   131,   132,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   136,   135,   nil,   122,   140,   138,   137,   133,
   134,   129,   127,   120,   nil,   121,   554,   387,   145,   nil,
   555,   nil,   nil,   nil,   nil,   nil,   nil,   141,   142,   nil,
   139,   123,   124,   125,   nil,   128,   130,   nil,   nil,   126,
   nil,   nil,   nil,   nil,   143,   144,   131,   132,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   136,   135,   nil,   122,   140,   138,   137,   133,   134,
   129,   127,   120,   nil,   121,   596,   381,   145,   nil,   597,
   nil,   nil,   nil,   nil,   nil,   nil,   141,   142,   nil,   139,
   123,   124,   125,   nil,   128,   130,   nil,   nil,   126,   nil,
   nil,   nil,   nil,   143,   144,   131,   132,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   136,   135,   nil,   122,   140,   138,   137,   133,   134,   129,
   127,   120,   nil,   121,   599,   387,   145,   nil,   600,   nil,
   nil,   nil,   nil,   nil,   nil,   141,   142,   nil,   139,   123,
   124,   125,   nil,   128,   130,   nil,   nil,   126,   nil,   nil,
   nil,   nil,   143,   144,   131,   132,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   136,
   135,   nil,   122,   140,   138,   137,   133,   134,   129,   127,
   120,   nil,   121,   552,   381,   145,   nil,   553,   nil,   nil,
   nil,   nil,   nil,   nil,   141,   142,   nil,   139,   123,   124,
   125,   nil,   128,   130,   nil,   nil,   126,   nil,   nil,   nil,
   nil,   143,   144,   131,   132,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   136,   135,
   nil,   122,   140,   138,   137,   133,   134,   129,   127,   120,
   nil,   121,   554,   387,   145,   nil,   555,   nil,   nil,   nil,
   nil,   nil,   nil,   141,   142,   nil,   139,   123,   124,   125,
   nil,   128,   130,   nil,   nil,   126,   nil,   nil,   nil,   nil,
   143,   144,   131,   132,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   136,   135,   nil,
   122,   140,   138,   137,   133,   134,   129,   127,   120,   nil,
   121,   nil,   nil,   145,   217,   221,   226,   227,   228,   223,
   225,   233,   234,   229,   230,   nil,   210,   211,   nil,   nil,
   231,   232,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   220,   nil,
   216,   215,   212,   213,   224,   222,   218,   nil,   219,   nil,
   nil,   nil,   nil,   nil,   nil,   632,   381,   nil,   nil,   633,
   nil,   nil,   nil,   nil,   293,   235,   141,   142,   nil,   139,
   123,   124,   125,   nil,   128,   130,   nil,   nil,   126,   nil,
   nil,   nil,   nil,   143,   144,   131,   132,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   136,   135,   nil,   122,   140,   138,   137,   133,   134,   129,
   127,   120,   nil,   121,   634,   387,   145,   nil,   635,   nil,
   nil,   nil,   nil,   nil,   nil,   141,   142,   nil,   139,   123,
   124,   125,   nil,   128,   130,   nil,   nil,   126,   nil,   nil,
   nil,   nil,   143,   144,   131,   132,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   136,
   135,   nil,   122,   140,   138,   137,   133,   134,   129,   127,
   120,   nil,   121,   637,   387,   145,   nil,   638,   nil,   nil,
   nil,   nil,   nil,   nil,   141,   142,   nil,   139,   123,   124,
   125,   nil,   128,   130,   nil,   nil,   126,   nil,   nil,   nil,
   nil,   143,   144,   131,   132,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   136,   135,
   nil,   122,   140,   138,   137,   133,   134,   129,   127,   120,
   nil,   121,   435,   381,   145,   nil,   436,   nil,   nil,   nil,
   nil,   nil,   nil,   141,   142,   nil,   139,   123,   124,   125,
   nil,   128,   130,   nil,   nil,   126,   nil,   nil,   nil,   nil,
   143,   144,   131,   132,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   136,   135,   nil,
   122,   140,   138,   137,   133,   134,   129,   127,   120,   nil,
   121,   435,   381,   145,   nil,   436,   nil,   nil,   nil,   nil,
   nil,   nil,   141,   142,   nil,   139,   123,   124,   125,   nil,
   128,   130,   nil,   nil,   126,   nil,   nil,   nil,   nil,   143,
   144,   131,   132,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   136,   135,   nil,   122,
   140,   138,   137,   133,   134,   129,   127,   120,   nil,   121,
   435,   381,   145,   nil,   436,   nil,   nil,   nil,   nil,   nil,
   nil,   141,   142,   nil,   139,   123,   124,   125,   nil,   128,
   130,   nil,   nil,   126,   nil,   nil,   nil,   nil,   143,   144,
   131,   132,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   136,   135,   nil,   122,   140,
   138,   137,   133,   134,   129,   127,   120,   nil,   121,   nil,
   nil,   145,   217,   221,   226,   227,   228,   223,   225,   233,
   234,   229,   230,   nil,   210,   211,   nil,   nil,   231,   232,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   214,   nil,   220,   nil,   216,   215,
   212,   213,   224,   222,   218,   nil,   219,   nil,   217,   221,
   226,   227,   228,   223,   225,   233,   234,   229,   230,   nil,
   210,   211,   293,   235,   231,   232,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   214,   nil,   220,   nil,   216,   215,   212,   213,   224,   222,
   218,   nil,   219,   nil,   nil,   nil,   859,   381,   nil,   nil,
   860,   nil,   nil,   nil,   nil,   nil,   nil,   141,   142,   235,
   139,   123,   124,   125,   nil,   128,   130,   nil,   nil,   126,
   nil,   nil,   nil,   nil,   143,   144,   131,   132,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   136,   135,   nil,   122,   140,   138,   137,   133,   134,
   129,   127,   120,   nil,   121,   861,   387,   145,   nil,   862,
   nil,   nil,   nil,   nil,   nil,   nil,   141,   142,   nil,   139,
   123,   124,   125,   nil,   128,   130,   nil,   nil,   126,   nil,
   nil,   nil,   nil,   143,   144,   131,   132,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   136,   135,   nil,   122,   140,   138,   137,   133,   134,   129,
   127,   120,   nil,   121,   nil,   nil,   145 ]

racc_action_check = [
   383,   514,   514,   520,   520,   308,   757,   383,   383,   383,
   328,   442,   713,   383,   383,   315,   383,   584,   442,   713,
   309,   426,   349,   318,   448,   383,   324,   629,   502,     1,
   324,   201,   203,   605,   605,   383,   383,   632,   383,   383,
   383,   383,   383,   729,   800,   313,   396,   313,   633,   634,
   745,   757,   757,   757,   757,   426,     8,   593,   448,   584,
   442,   713,     9,   383,   383,   383,   383,   383,   383,   383,
   383,   383,   383,   383,   383,   383,   383,   201,   203,   383,
   383,   383,    55,   383,   349,   605,   596,   383,    10,   514,
   383,   520,   396,   493,   514,   383,   593,   383,    11,   383,
   383,   383,   383,   383,   383,   383,   385,   383,   383,   383,
    12,   634,   328,   385,   385,   385,   308,   315,   308,   385,
   385,   308,   385,   383,   383,   318,   383,    20,   383,   383,
   502,   309,   328,   309,   494,   597,   309,   328,   629,   632,
   629,   385,   385,   629,   385,   385,   385,   385,   385,   596,
   633,   634,   745,   552,   729,   800,   729,   800,   746,   729,
   800,   635,   805,   449,   805,    55,    26,   596,   553,   385,
   385,   385,   385,   385,   385,   385,   385,   385,   385,   385,
   385,   385,   385,   279,   576,   385,   385,   385,   596,   385,
   493,   596,   493,   385,    72,   493,   385,   449,   597,   552,
    14,   385,    72,   385,   530,   385,   385,   385,   385,   385,
   385,   385,    50,   385,   553,   385,   597,    34,   576,    50,
    50,    50,   850,   635,    50,    50,    50,    26,    50,   385,
   385,   494,   385,   494,   385,   385,   494,   597,    50,    50,
   597,    15,    15,    14,   279,   322,    14,    50,    50,    26,
    50,    50,    50,    50,    50,   746,     3,   746,    37,    37,
   746,     3,   345,   635,   530,   530,   279,   850,   850,   850,
   850,   282,   282,    36,   530,    50,    50,    50,    50,    50,
    50,    50,    50,    50,    50,    50,    50,    50,    50,    41,
   323,    50,    50,    50,   322,   312,    50,   695,    97,    50,
   312,   322,    50,    50,    35,    50,   322,    50,   186,    50,
   322,    50,    50,    50,    50,    50,    50,    50,   434,    50,
   285,    50,   345,   345,   345,   434,   434,   434,   322,   378,
   434,   434,   434,   599,   434,    50,    50,    50,    50,   323,
    50,   346,    25,   695,   434,    13,   323,    35,   202,    25,
    35,   323,    13,   434,   434,   323,   434,   434,   434,   434,
   434,    13,   332,   285,   299,   637,   285,   299,   859,   332,
   204,   437,   378,   323,   347,   378,   379,   542,   437,   437,
   437,   205,   599,   437,   437,   437,   333,   437,   860,   599,
   241,   110,   434,   333,   599,   110,   110,   437,   599,   434,
   348,   346,   346,   346,   434,   434,   437,   437,   350,   437,
   437,   437,   437,   437,   637,   242,   599,   859,   861,   379,
   542,   637,   379,   542,   859,   334,   637,   434,   246,   859,
   637,    42,   334,   859,   347,   347,   347,   860,    42,   300,
   402,   434,   300,   434,   860,   437,   434,    42,   637,   860,
   513,   859,   437,   860,   255,   513,   335,   437,   437,   200,
   348,   348,   348,   335,   296,   296,   200,   861,   350,   350,
   350,   860,   402,   642,   861,   200,   402,   402,   642,   861,
   437,   336,   647,   861,   647,   647,   647,   647,   336,   554,
   554,   554,   266,   554,   437,   648,   437,   554,   554,   437,
   648,   861,   554,   268,   554,   554,   554,   554,   554,   554,
   554,   303,   307,   307,   303,   554,   554,   554,   554,   554,
   554,   554,   421,   617,   617,   337,   338,   647,   647,   647,
   647,   554,   337,   338,   554,   554,   554,   554,   554,   554,
   554,   554,   554,   554,   339,   554,   554,   554,   554,   554,
   273,   339,   760,   760,   421,   274,   341,   273,   421,   421,
   421,   421,   274,   341,   305,   269,   273,   305,   270,   554,
   554,   274,   554,   554,   275,   554,   554,   872,   872,   554,
   276,   554,   277,   554,   278,   554,   516,   276,   516,   516,
   516,   516,   281,   554,   286,   287,   276,   290,   554,   554,
   554,   554,   554,   554,   294,   295,   298,   554,   554,   555,
   555,   555,   403,   555,   302,   304,   554,   555,   555,   314,
   317,   319,   555,   516,   555,   555,   555,   555,   555,   555,
   555,   516,   516,   516,   516,   555,   555,   555,   555,   555,
   555,   555,   422,   364,   403,   365,   370,   373,   403,   403,
   377,   555,   384,   404,   555,   555,   555,   555,   555,   555,
   555,   555,   555,   555,   405,   555,   555,   555,   555,   555,
   288,   406,   407,   431,   422,   316,   440,   288,   422,   422,
   422,   422,   316,   441,   443,   444,   288,   450,   452,   555,
   555,   316,   555,   555,   453,   555,   555,   457,   462,   555,
   326,   555,   472,   555,   475,   555,   755,   326,   755,   755,
   755,   755,   489,   555,   495,   496,   326,   528,   555,   555,
   555,   555,   555,   555,   533,   461,   545,   555,   555,    60,
    60,    60,   461,    60,   549,   556,   555,    60,    60,   563,
   565,   461,    60,   568,    60,    60,    60,    60,    60,    60,
    60,   755,   755,   755,   755,    60,    60,    60,    60,    60,
    60,    60,   524,   524,    60,   524,   524,   524,   570,   368,
   578,    60,   579,   585,    60,    60,    60,    60,    60,    60,
    60,    60,    60,    60,   588,    60,    60,    60,    60,    60,
   368,   368,   368,   368,   368,   368,   368,   368,   368,   368,
   368,   505,   368,   368,   723,   595,   368,   368,   505,    60,
   598,   723,    60,   601,   603,    60,    60,   505,   604,    60,
   723,   606,   368,   609,   368,    60,   368,   368,   368,   368,
   368,   368,   368,    60,   368,   610,   613,   619,    60,    60,
    60,    60,    60,    60,   625,   627,   628,    60,    60,   631,
    60,   368,   640,   368,   600,    60,    99,    99,    99,    99,
    99,   600,   645,   649,    99,    99,   600,   650,   651,    99,
   600,    99,    99,    99,    99,    99,    99,    99,   657,   662,
   665,   667,    99,    99,    99,    99,    99,    99,    99,   680,
   698,    99,     4,     4,     4,     4,     4,    99,    99,    99,
    99,    99,    99,    99,    99,    99,    99,    99,    99,    99,
    99,   638,    99,    99,    99,    99,    99,   772,   638,   773,
   702,   775,   827,   638,   772,   878,   773,   638,   775,   827,
   703,   704,   878,   772,   707,   773,    99,   775,   827,    99,
   711,   878,    99,    99,   712,   714,    99,   862,    99,   718,
   720,   721,    99,   819,   862,   819,   819,   819,   819,   862,
    99,   722,   734,   862,   737,    99,    99,    99,    99,    99,
    99,   748,   752,   753,    99,    99,   699,   699,   699,   766,
   699,   774,    99,   776,   699,   699,   777,   784,   785,   699,
   819,   699,   699,   699,   699,   699,   699,   699,   819,   819,
   819,   819,   699,   699,   699,   699,   699,   699,   699,   108,
   108,   108,   108,   108,   786,   789,   802,   803,   699,   808,
   810,   699,   699,   699,   699,   699,   699,   699,   699,   699,
   699,   811,   699,   699,   812,   815,   699,   400,   400,   400,
   400,   400,   400,   400,   400,   400,   400,   400,   816,   400,
   400,   828,   835,   400,   400,   838,   699,   839,   856,   699,
   857,   858,   699,   699,   867,   873,   699,   874,   875,   400,
   876,   400,   880,   400,   400,   400,   400,   400,   400,   400,
   883,   400,   894,   nil,   nil,   699,   699,   699,   699,   699,
   699,   nil,   nil,   nil,   699,   699,   nil,   823,   823,   823,
   699,   823,   nil,   nil,   nil,   823,   823,   nil,   nil,   nil,
   823,   nil,   823,   823,   823,   823,   823,   823,   823,   nil,
   nil,   nil,   nil,   823,   823,   823,   823,   823,   823,   823,
   352,   352,   352,   352,   352,   nil,   nil,   557,   nil,   823,
   nil,   nil,   823,   823,   823,   823,   823,   823,   823,   823,
   823,   823,   nil,   823,   823,   nil,   nil,   823,   557,   557,
   557,   557,   557,   557,   557,   557,   557,   557,   557,   nil,
   557,   557,   nil,   nil,   557,   557,   nil,   823,   nil,   nil,
   823,   nil,   nil,   823,   823,   nil,   nil,   823,   nil,   nil,
   557,   nil,   557,   nil,   557,   557,   557,   557,   557,   557,
   557,   nil,   557,   nil,   nil,   nil,   823,   823,   823,   823,
   823,   823,   nil,   nil,   nil,   823,   823,   nil,   nil,   557,
   nil,   823,     0,     0,     0,     0,     0,     0,   nil,   nil,
   nil,     0,     0,   nil,   nil,   nil,     0,   nil,     0,     0,
     0,     0,     0,     0,     0,   nil,   nil,   nil,   nil,     0,
     0,     0,     0,     0,     0,     0,   nil,   nil,     0,   nil,
   nil,   nil,   nil,   nil,     0,     0,     0,     0,     0,     0,
     0,     0,     0,     0,     0,     0,     0,     0,   nil,     0,
     0,     0,     0,     0,   401,   401,   401,   401,   401,   401,
   401,   401,   401,   401,   401,   nil,   401,   401,   nil,   nil,
   401,   401,   nil,     0,   nil,   nil,     0,   nil,   nil,     0,
     0,   nil,   nil,     0,   nil,     0,   401,   nil,   401,     0,
   401,   401,   401,   401,   401,   401,   401,     0,   401,   nil,
   nil,   nil,     0,     0,     0,     0,     0,     0,   nil,   nil,
   nil,     0,     0,    30,    30,    30,    30,    30,    30,   nil,
   nil,   nil,    30,    30,   nil,   nil,   nil,    30,   nil,    30,
    30,    30,    30,    30,    30,    30,   nil,   nil,   nil,   nil,
    30,    30,    30,    30,    30,    30,    30,   nil,   nil,    30,
   nil,   nil,   nil,   nil,   nil,    30,    30,    30,    30,    30,
    30,    30,    30,    30,    30,    30,    30,    30,    30,   nil,
    30,    30,    30,    30,    30,   411,   411,   411,   411,   411,
   411,   411,   nil,   nil,   411,   411,   nil,   nil,   nil,   nil,
   nil,   411,   411,   nil,    30,   nil,   nil,    30,   nil,   nil,
    30,    30,   nil,   nil,    30,   nil,    30,   411,   nil,   411,
    30,   411,   411,   411,   411,   411,   411,   411,    30,   411,
   nil,   nil,   nil,    30,    30,    30,    30,    30,    30,   nil,
   nil,   nil,    30,    30,    51,    51,    51,    51,    51,    51,
   nil,   nil,   nil,    51,    51,   nil,   nil,   nil,    51,   nil,
    51,    51,    51,    51,    51,    51,    51,   nil,   nil,   nil,
   nil,    51,    51,    51,    51,    51,    51,    51,   nil,   nil,
    51,   nil,   nil,   nil,   nil,   nil,    51,    51,    51,    51,
    51,    51,    51,    51,    51,    51,    51,    51,    51,    51,
   nil,    51,    51,    51,    51,    51,   412,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   412,   412,   nil,    51,   nil,   nil,    51,   nil,
   nil,    51,    51,   nil,   nil,    51,   nil,    51,   412,   nil,
   412,    51,   412,   412,   412,   412,   nil,   nil,   412,    51,
   412,   nil,   nil,   nil,    51,    51,    51,    51,    51,    51,
   nil,   nil,   nil,    51,    51,   188,   188,   188,   188,   188,
   188,   nil,   nil,   nil,   188,   188,   nil,   nil,   nil,   188,
   nil,   188,   188,   188,   188,   188,   188,   188,   nil,   nil,
   nil,   nil,   188,   188,   188,   188,   188,   188,   188,   nil,
   nil,   188,   nil,   nil,   nil,   nil,   nil,   188,   188,   188,
   188,   188,   188,   188,   188,   188,   188,   188,   188,   188,
   188,   nil,   188,   188,   188,   188,   188,   413,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   413,   413,   nil,   188,   nil,   nil,   188,
   nil,   nil,   188,   188,   nil,   nil,   188,   nil,   188,   413,
   nil,   413,   188,   413,   413,   413,   413,   nil,   nil,   413,
   188,   413,   nil,   nil,   nil,   188,   188,   188,   188,   188,
   188,   nil,   nil,   nil,   188,   188,   189,   189,   189,   189,
   189,   189,   nil,   nil,   nil,   189,   189,   nil,   nil,   nil,
   189,   nil,   189,   189,   189,   189,   189,   189,   189,   nil,
   nil,   nil,   nil,   189,   189,   189,   189,   189,   189,   189,
   nil,   nil,   189,   nil,   nil,   nil,   nil,   nil,   189,   189,
   189,   189,   189,   189,   189,   189,   189,   189,   189,   189,
   189,   189,   nil,   189,   189,   189,   189,   189,   414,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   414,   414,   nil,   189,   nil,   nil,
   189,   nil,   nil,   189,   189,   nil,   nil,   189,   nil,   189,
   414,   nil,   414,   189,   414,   414,   414,   414,   nil,   nil,
   414,   189,   414,   nil,   nil,   nil,   189,   189,   189,   189,
   189,   189,   nil,   nil,   nil,   189,   189,   206,   206,   206,
   206,   206,   206,   nil,   nil,   nil,   206,   206,   nil,   nil,
   nil,   206,   nil,   206,   206,   206,   206,   206,   206,   206,
   nil,   nil,   nil,   nil,   206,   206,   206,   206,   206,   206,
   206,   nil,   nil,   206,   nil,   nil,   nil,   nil,   nil,   206,
   206,   206,   206,   206,   206,   206,   206,   206,   206,   206,
   206,   206,   206,   nil,   206,   206,   206,   206,   206,   415,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   415,   415,   nil,   206,   nil,
   nil,   206,   nil,   nil,   206,   206,   nil,   nil,   206,   nil,
   206,   415,   nil,   415,   206,   415,   415,   415,   415,   nil,
   nil,   415,   206,   415,   nil,   nil,   nil,   206,   206,   206,
   206,   206,   206,   nil,   nil,   nil,   206,   206,   267,   267,
   267,   267,   267,   267,   nil,   nil,   nil,   267,   267,   nil,
   nil,   nil,   267,   nil,   267,   267,   267,   267,   267,   267,
   267,   nil,   nil,   nil,   nil,   267,   267,   267,   267,   267,
   267,   267,   nil,   nil,   267,   nil,   nil,   nil,   nil,   nil,
   267,   267,   267,   267,   267,   267,   267,   267,   267,   267,
   267,   267,   267,   267,   nil,   267,   267,   267,   267,   267,
   416,   416,   416,   416,   416,   416,   416,   nil,   nil,   416,
   416,   nil,   nil,   nil,   nil,   nil,   416,   416,   nil,   267,
   nil,   nil,   267,   nil,   nil,   267,   267,   nil,   nil,   267,
   nil,   267,   416,   nil,   416,   267,   416,   416,   416,   416,
   416,   416,   416,   267,   416,   nil,   nil,   nil,   267,   267,
   267,   267,   267,   267,   nil,   nil,   nil,   267,   267,   272,
   272,   272,   272,   272,   272,   nil,   nil,   nil,   272,   272,
   nil,   nil,   nil,   272,   nil,   272,   272,   272,   272,   272,
   272,   272,   nil,   nil,   nil,   nil,   272,   272,   272,   272,
   272,   272,   272,   nil,   nil,   272,   nil,   nil,   nil,   nil,
   nil,   272,   272,   272,   272,   272,   272,   272,   272,   272,
   272,   272,   272,   272,   272,   nil,   272,   272,   272,   272,
   272,   417,   417,   417,   417,   417,   417,   417,   nil,   nil,
   417,   417,   nil,   nil,   nil,   nil,   nil,   417,   417,   nil,
   272,   nil,   nil,   272,   nil,   nil,   272,   272,   nil,   nil,
   272,   nil,   272,   417,   nil,   417,   272,   417,   417,   417,
   417,   417,   417,   417,   272,   417,   nil,   nil,   nil,   272,
   272,   272,   272,   272,   272,   nil,   nil,   nil,   272,   272,
   488,   488,   488,   488,   488,   488,   nil,   nil,   nil,   488,
   488,   nil,   nil,   nil,   488,   nil,   488,   488,   488,   488,
   488,   488,   488,   nil,   nil,   nil,   nil,   488,   488,   488,
   488,   488,   488,   488,   nil,   nil,   488,   nil,   nil,   nil,
   nil,   nil,   488,   488,   488,   488,   488,   488,   488,   488,
   488,   488,   488,   488,   488,   488,   nil,   488,   488,   488,
   488,   488,   418,   418,   418,   418,   418,   418,   418,   nil,
   nil,   418,   418,   nil,   nil,   nil,   nil,   nil,   418,   418,
   nil,   488,   nil,   nil,   488,   nil,   nil,   488,   488,   nil,
   nil,   488,   nil,   488,   418,   nil,   418,   488,   418,   418,
   418,   418,   418,   418,   418,   488,   418,   nil,   nil,   nil,
   488,   488,   488,   488,   488,   488,   nil,   nil,   nil,   488,
   488,   492,   492,   492,   492,   492,   492,   nil,   nil,   nil,
   492,   492,   nil,   nil,   nil,   492,   nil,   492,   492,   492,
   492,   492,   492,   492,   nil,   nil,   nil,   nil,   492,   492,
   492,   492,   492,   492,   492,   nil,   nil,   492,   nil,   nil,
   nil,   nil,   nil,   492,   492,   492,   492,   492,   492,   492,
   492,   492,   492,   492,   492,   492,   492,   nil,   492,   492,
   492,   492,   492,   419,   419,   419,   419,   419,   419,   419,
   nil,   nil,   419,   419,   nil,   nil,   nil,   nil,   nil,   419,
   419,   nil,   492,   nil,   nil,   492,   nil,   nil,   492,   492,
   nil,   nil,   492,   nil,   492,   419,   nil,   419,   492,   419,
   419,   419,   419,   419,   419,   419,   492,   419,   nil,   nil,
   nil,   492,   492,   492,   492,   492,   492,   nil,   nil,   nil,
   492,   492,   497,   497,   497,   497,   497,   497,   nil,   nil,
   nil,   497,   497,   nil,   nil,   nil,   497,   nil,   497,   497,
   497,   497,   497,   497,   497,   nil,   nil,   nil,   nil,   497,
   497,   497,   497,   497,   497,   497,   nil,   nil,   497,   nil,
   nil,   nil,   nil,   nil,   497,   497,   497,   497,   497,   497,
   497,   497,   497,   497,   497,   497,   497,   497,   nil,   497,
   497,   497,   497,   497,   420,   420,   420,   420,   420,   420,
   420,   nil,   nil,   420,   420,   nil,   nil,   nil,   nil,   nil,
   420,   420,   nil,   497,   nil,   nil,   497,   nil,   nil,   497,
   497,   nil,   nil,   497,   nil,   497,   420,   nil,   420,   497,
   420,   420,   420,   420,   420,   420,   420,   497,   420,   nil,
   nil,   nil,   497,   497,   497,   497,   497,   497,   nil,   nil,
   nil,   497,   497,   515,   515,   515,   515,   515,   515,   nil,
   nil,   nil,   515,   515,   nil,   nil,   nil,   515,   nil,   515,
   515,   515,   515,   515,   515,   515,   nil,   nil,   nil,   nil,
   515,   515,   515,   515,   515,   515,   515,   nil,   nil,   515,
   nil,   nil,   nil,   nil,   nil,   515,   515,   515,   515,   515,
   515,   515,   515,   515,   515,   515,   515,   515,   515,   nil,
   515,   515,   515,   515,   515,   423,   423,   423,   423,   423,
   423,   423,   nil,   nil,   423,   423,   nil,   nil,   nil,   nil,
   nil,   423,   423,   nil,   515,   nil,   nil,   515,   nil,   nil,
   515,   515,   nil,   nil,   515,   nil,   515,   423,   nil,   423,
   515,   423,   423,   423,   423,   423,   423,   423,   515,   423,
   nil,   nil,   nil,   515,   515,   515,   515,   515,   515,   nil,
   nil,   nil,   515,   515,   561,   561,   561,   561,   561,   561,
   nil,   nil,   nil,   561,   561,   nil,   nil,   nil,   561,   nil,
   561,   561,   561,   561,   561,   561,   561,   nil,   nil,   nil,
   nil,   561,   561,   561,   561,   561,   561,   561,   nil,   nil,
   561,   nil,   nil,   nil,   nil,   nil,   561,   561,   561,   561,
   561,   561,   561,   561,   561,   561,   561,   561,   561,   561,
   nil,   561,   561,   561,   561,   561,   424,   424,   424,   424,
   424,   424,   424,   424,   nil,   424,   424,   nil,   nil,   nil,
   nil,   nil,   424,   424,   nil,   561,   nil,   nil,   561,   nil,
   nil,   561,   561,   nil,   nil,   561,   nil,   561,   424,   nil,
   424,   561,   424,   424,   424,   424,   424,   424,   424,   561,
   424,   nil,   nil,   nil,   561,   561,   561,   561,   561,   561,
   nil,   nil,   nil,   561,   561,   590,   590,   590,   590,   590,
   590,   nil,   nil,   nil,   590,   590,   nil,   nil,   nil,   590,
   nil,   590,   590,   590,   590,   590,   590,   590,   nil,   nil,
   nil,   nil,   590,   590,   590,   590,   590,   590,   590,   nil,
   nil,   590,   nil,   nil,   nil,   nil,   nil,   590,   590,   590,
   590,   590,   590,   590,   590,   590,   590,   590,   590,   590,
   590,   nil,   590,   590,   590,   590,   590,   408,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   408,   408,   nil,   590,   nil,   nil,   590,
   nil,   nil,   590,   590,   nil,   nil,   590,   nil,   590,   408,
   nil,   408,   590,   408,   408,   408,   408,   nil,   nil,   nil,
   590,   nil,   nil,   nil,   nil,   590,   590,   590,   590,   590,
   590,   nil,   nil,   nil,   590,   590,   591,   591,   591,   591,
   591,   591,   nil,   nil,   nil,   591,   591,   nil,   nil,   nil,
   591,   nil,   591,   591,   591,   591,   591,   591,   591,   nil,
   nil,   nil,   nil,   591,   591,   591,   591,   591,   591,   591,
   nil,   nil,   591,   nil,   nil,   nil,   nil,   nil,   591,   591,
   591,   591,   591,   591,   591,   591,   591,   591,   591,   591,
   591,   591,   nil,   591,   591,   591,   591,   591,   409,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   409,   409,   nil,   591,   nil,   nil,
   591,   nil,   nil,   591,   591,   nil,   nil,   591,   nil,   591,
   409,   nil,   409,   591,   409,   409,   409,   409,   nil,   nil,
   nil,   591,   nil,   nil,   nil,   nil,   591,   591,   591,   591,
   591,   591,   nil,   nil,   nil,   591,   591,   608,   608,   608,
   608,   608,   608,   nil,   nil,   nil,   608,   608,   nil,   nil,
   nil,   608,   nil,   608,   608,   608,   608,   608,   608,   608,
   nil,   nil,   nil,   nil,   608,   608,   608,   608,   608,   608,
   608,   nil,   nil,   608,   nil,   nil,   nil,   nil,   nil,   608,
   608,   608,   608,   608,   608,   608,   608,   608,   608,   608,
   608,   608,   608,   nil,   608,   608,   608,   608,   608,   410,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   410,   410,   nil,   608,   nil,
   nil,   608,   nil,   nil,   608,   608,   nil,   nil,   608,   nil,
   608,   410,   nil,   nil,   608,   410,   410,   410,   410,   nil,
   nil,   nil,   608,   nil,   nil,   nil,   nil,   608,   608,   608,
   608,   608,   608,   nil,   nil,   nil,   608,   608,   641,   641,
   641,   641,   641,   641,   nil,   nil,   nil,   641,   641,   nil,
   nil,   nil,   641,   nil,   641,   641,   641,   641,   641,   641,
   641,   nil,   nil,   nil,   nil,   641,   641,   641,   641,   641,
   641,   641,   nil,   nil,   641,   nil,   nil,   nil,   nil,   nil,
   641,   641,   641,   641,   641,   641,   641,   641,   641,   641,
   641,   641,   641,   641,   nil,   641,   641,   641,   641,   641,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   641,
   nil,   nil,   641,   nil,   nil,   641,   641,   nil,   nil,   641,
   nil,   641,   nil,   nil,   nil,   641,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   641,   nil,   nil,   nil,   nil,   641,   641,
   641,   641,   641,   641,   nil,   nil,   nil,   641,   641,   646,
   646,   646,   646,   646,   646,   nil,   nil,   nil,   646,   646,
   nil,   nil,   nil,   646,   nil,   646,   646,   646,   646,   646,
   646,   646,   nil,   nil,   nil,   nil,   646,   646,   646,   646,
   646,   646,   646,   nil,   nil,   646,   nil,   nil,   nil,   nil,
   nil,   646,   646,   646,   646,   646,   646,   646,   646,   646,
   646,   646,   646,   646,   646,   nil,   646,   646,   646,   646,
   646,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   646,   nil,   nil,   646,   nil,   nil,   646,   646,   nil,   nil,
   646,   nil,   646,   nil,   nil,   nil,   646,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   646,   nil,   nil,   nil,   nil,   646,
   646,   646,   646,   646,   646,   nil,   nil,   nil,   646,   646,
   673,   673,   673,   673,   673,   673,   nil,   nil,   nil,   673,
   673,   nil,   nil,   nil,   673,   nil,   673,   673,   673,   673,
   673,   673,   673,   nil,   nil,   nil,   nil,   673,   673,   673,
   673,   673,   673,   673,   nil,   nil,   673,   nil,   nil,   nil,
   nil,   nil,   673,   673,   673,   673,   673,   673,   673,   673,
   673,   673,   673,   673,   673,   673,   nil,   673,   673,   673,
   673,   673,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   673,   nil,   nil,   673,   nil,   nil,   673,   673,   nil,
   nil,   673,   nil,   673,   nil,   nil,   nil,   673,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   673,   nil,   nil,   nil,   nil,
   673,   673,   673,   673,   673,   673,   nil,   nil,   nil,   673,
   673,   708,   708,   708,   708,   708,   708,   nil,   nil,   nil,
   708,   708,   nil,   nil,   nil,   708,   nil,   708,   708,   708,
   708,   708,   708,   708,   nil,   nil,   nil,   nil,   708,   708,
   708,   708,   708,   708,   708,   nil,   nil,   708,   nil,   nil,
   nil,   nil,   nil,   708,   708,   708,   708,   708,   708,   708,
   708,   708,   708,   708,   708,   708,   708,   nil,   708,   708,
   708,   708,   708,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   708,   nil,   nil,   708,   nil,   nil,   708,   708,
   nil,   nil,   708,   nil,   708,   nil,   nil,   nil,   708,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   708,   nil,   nil,   nil,
   nil,   708,   708,   708,   708,   708,   708,   nil,   nil,   nil,
   708,   708,   726,   726,   726,   726,   726,   726,   nil,   nil,
   nil,   726,   726,   nil,   nil,   nil,   726,   nil,   726,   726,
   726,   726,   726,   726,   726,   nil,   nil,   nil,   nil,   726,
   726,   726,   726,   726,   726,   726,   nil,   nil,   726,   nil,
   nil,   nil,   nil,   nil,   726,   726,   726,   726,   726,   726,
   726,   726,   726,   726,   726,   726,   726,   726,   nil,   726,
   726,   726,   726,   726,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   726,   nil,   nil,   726,   nil,   nil,   726,
   726,   nil,   nil,   726,   nil,   726,   nil,   nil,   nil,   726,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   726,   nil,   nil,
   nil,   nil,   726,   726,   726,   726,   726,   726,   nil,   nil,
   nil,   726,   726,   738,   738,   738,   738,   738,   738,   nil,
   nil,   nil,   738,   738,   nil,   nil,   nil,   738,   nil,   738,
   738,   738,   738,   738,   738,   738,   nil,   nil,   nil,   nil,
   738,   738,   738,   738,   738,   738,   738,   nil,   nil,   738,
   nil,   nil,   nil,   nil,   nil,   738,   738,   738,   738,   738,
   738,   738,   738,   738,   738,   738,   738,   738,   738,   nil,
   738,   738,   738,   738,   738,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   738,   nil,   nil,   738,   nil,   nil,
   738,   738,   nil,   nil,   738,   nil,   738,   nil,   nil,   nil,
   738,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   738,   nil,
   nil,   nil,   nil,   738,   738,   738,   738,   738,   738,   nil,
   nil,   nil,   738,   738,   739,   739,   739,   739,   739,   739,
   nil,   nil,   nil,   739,   739,   nil,   nil,   nil,   739,   nil,
   739,   739,   739,   739,   739,   739,   739,   nil,   nil,   nil,
   nil,   739,   739,   739,   739,   739,   739,   739,   nil,   nil,
   739,   nil,   nil,   nil,   nil,   nil,   739,   739,   739,   739,
   739,   739,   739,   739,   739,   739,   739,   739,   739,   739,
   nil,   739,   739,   739,   739,   739,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   739,   nil,   nil,   739,   nil,
   nil,   739,   739,   nil,   nil,   739,   nil,   739,   nil,   nil,
   nil,   739,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   739,
   nil,   nil,   nil,   nil,   739,   739,   739,   739,   739,   739,
   nil,   nil,   nil,   739,   739,   743,   743,   743,   743,   743,
   743,   nil,   nil,   nil,   743,   743,   nil,   nil,   nil,   743,
   nil,   743,   743,   743,   743,   743,   743,   743,   nil,   nil,
   nil,   nil,   743,   743,   743,   743,   743,   743,   743,   nil,
   nil,   743,   nil,   nil,   nil,   nil,   nil,   743,   743,   743,
   743,   743,   743,   743,   743,   743,   743,   743,   743,   743,
   743,   nil,   743,   743,   743,   743,   743,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   743,   nil,   nil,   743,
   nil,   nil,   743,   743,   nil,   nil,   743,   nil,   743,   nil,
   nil,   nil,   743,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   743,   nil,   nil,   nil,   nil,   743,   743,   743,   743,   743,
   743,   nil,   nil,   nil,   743,   743,   750,   750,   750,   750,
   750,   750,   nil,   nil,   nil,   750,   750,   nil,   nil,   nil,
   750,   nil,   750,   750,   750,   750,   750,   750,   750,   nil,
   nil,   nil,   nil,   750,   750,   750,   750,   750,   750,   750,
   nil,   nil,   750,   nil,   nil,   nil,   nil,   nil,   750,   750,
   750,   750,   750,   750,   750,   750,   750,   750,   750,   750,
   750,   750,   nil,   750,   750,   750,   750,   750,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   750,   nil,   nil,
   750,   nil,   nil,   750,   750,   nil,   nil,   750,   nil,   750,
   nil,   nil,   nil,   750,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   750,   nil,   nil,   nil,   nil,   750,   750,   750,   750,
   750,   750,   nil,   nil,   nil,   750,   750,   796,   796,   796,
   796,   796,   796,   nil,   nil,   nil,   796,   796,   nil,   nil,
   nil,   796,   nil,   796,   796,   796,   796,   796,   796,   796,
   nil,   nil,   nil,   nil,   796,   796,   796,   796,   796,   796,
   796,   nil,   nil,   796,   nil,   nil,   nil,   nil,   nil,   796,
   796,   796,   796,   796,   796,   796,   796,   796,   796,   796,
   796,   796,   796,   nil,   796,   796,   796,   796,   796,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   796,   nil,
   nil,   796,   nil,   nil,   796,   796,   nil,   nil,   796,   nil,
   796,   nil,   nil,   nil,   796,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   796,   nil,   nil,   nil,   nil,   796,   796,   796,
   796,   796,   796,   nil,   nil,   nil,   796,   796,   840,   840,
   840,   840,   840,   840,   nil,   nil,   nil,   840,   840,   nil,
   nil,   nil,   840,   nil,   840,   840,   840,   840,   840,   840,
   840,   nil,   nil,   nil,   nil,   840,   840,   840,   840,   840,
   840,   840,   nil,   nil,   840,   nil,   nil,   nil,   nil,   nil,
   840,   840,   840,   840,   840,   840,   840,   840,   840,   840,
   840,   840,   840,   840,   nil,   840,   840,   840,   840,   840,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   840,
   nil,   nil,   840,   nil,   nil,   840,   840,   nil,   nil,   840,
   nil,   840,   nil,   nil,   nil,   840,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   840,   nil,   nil,   nil,   nil,   840,   840,
   840,   840,   840,   840,   nil,   nil,   nil,   840,   840,   847,
   847,   847,   847,   847,   847,   nil,   nil,   nil,   847,   847,
   nil,   nil,   nil,   847,   nil,   847,   847,   847,   847,   847,
   847,   847,   nil,   nil,   nil,   nil,   847,   847,   847,   847,
   847,   847,   847,   nil,   nil,   847,   nil,   nil,   nil,   nil,
   nil,   847,   847,   847,   847,   847,   847,   847,   847,   847,
   847,   847,   847,   847,   847,   nil,   847,   847,   847,   847,
   847,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   847,   nil,   nil,   847,   nil,   nil,   847,   847,   nil,   nil,
   847,   nil,   847,   nil,   nil,   nil,   847,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   847,   nil,   nil,   nil,   nil,   847,
   847,   847,   847,   847,   847,   nil,   nil,   nil,   847,   847,
   854,   854,   854,   854,   854,   854,   nil,   nil,   nil,   854,
   854,   nil,   nil,   nil,   854,   nil,   854,   854,   854,   854,
   854,   854,   854,   nil,   nil,   nil,   nil,   854,   854,   854,
   854,   854,   854,   854,   nil,   nil,   854,   nil,   nil,   nil,
   nil,   nil,   854,   854,   854,   854,   854,   854,   854,   854,
   854,   854,   854,   854,   854,   854,   nil,   854,   854,   854,
   854,   854,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   854,   nil,   nil,   854,   nil,   nil,   854,   854,   nil,
   nil,   854,   nil,   854,   nil,   nil,   nil,   854,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   854,   nil,   nil,   nil,   nil,
   854,   854,   854,   854,   854,   854,   nil,   nil,   nil,   854,
   854,     5,     5,     5,     5,     5,   nil,   nil,   nil,     5,
     5,   nil,   nil,   nil,     5,   nil,     5,     5,     5,     5,
     5,     5,     5,   nil,   nil,   nil,   nil,     5,     5,     5,
     5,     5,     5,     5,   nil,   nil,     5,   nil,   nil,   nil,
   nil,   nil,     5,     5,     5,     5,     5,     5,     5,     5,
     5,     5,     5,     5,     5,     5,   nil,     5,     5,     5,
     5,     5,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,     5,   nil,   nil,     5,   nil,   nil,     5,     5,   nil,
   nil,     5,   nil,     5,   nil,   nil,   nil,     5,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,     5,   nil,   nil,   nil,   nil,
     5,     5,     5,     5,     5,     5,   nil,   nil,   nil,     5,
     5,     6,     6,     6,     6,     6,     6,     6,     6,     6,
     6,     6,     6,     6,     6,     6,     6,     6,     6,     6,
     6,     6,     6,     6,     6,   nil,   nil,     6,     6,     6,
     6,     6,     6,     6,     6,     6,     6,   nil,   nil,   nil,
   nil,   nil,     6,     6,     6,     6,     6,     6,     6,     6,
     6,   nil,     6,   nil,   nil,   nil,   nil,   nil,   nil,     6,
     6,   nil,     6,     6,     6,     6,   nil,     6,     6,   nil,
   nil,     6,   nil,   nil,   nil,   nil,     6,     6,     6,     6,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,     6,     6,   nil,     6,     6,     6,     6,
     6,     6,     6,     6,     6,   nil,     6,   nil,   nil,     6,
     6,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
     6,     7,     7,     7,     7,     7,     7,     7,     7,     7,
     7,     7,     7,     7,     7,     7,     7,     7,     7,     7,
     7,     7,     7,     7,     7,   nil,   nil,     7,     7,     7,
     7,     7,     7,     7,     7,     7,     7,   nil,   nil,   nil,
   nil,   nil,     7,     7,     7,     7,     7,     7,     7,     7,
   nil,   nil,     7,   nil,   nil,   nil,   nil,   nil,   nil,     7,
     7,   nil,     7,     7,     7,     7,   nil,     7,     7,   nil,
   nil,     7,   nil,   nil,   nil,   nil,     7,     7,     7,     7,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,     7,     7,   nil,     7,     7,     7,     7,
     7,     7,     7,     7,     7,   nil,     7,   nil,   nil,     7,
     7,   nil,   nil,    17,    17,    17,   nil,    17,   nil,   nil,
     7,    17,    17,   nil,   nil,   nil,    17,   nil,    17,    17,
    17,    17,    17,    17,    17,   nil,   nil,   nil,   nil,    17,
    17,    17,    17,    17,    17,    17,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   nil,   nil,    17,   nil,   nil,    17,    17,
    17,    17,    17,    17,    17,    17,    17,    17,   nil,    17,
    17,    17,    17,    17,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    17,   nil,   nil,    17,   nil,   nil,    17,
    17,   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,    17,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    17,   nil,   nil,
   nil,   nil,    17,    17,    17,    17,    17,    17,   nil,   nil,
   nil,    17,    17,    18,    18,    18,   nil,    18,   nil,   nil,
   nil,    18,    18,   nil,   nil,   nil,    18,   nil,    18,    18,
    18,    18,    18,    18,    18,   nil,   nil,   nil,   nil,    18,
    18,    18,    18,    18,    18,    18,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,    18,    18,
    18,    18,    18,    18,    18,    18,    18,    18,   nil,    18,
    18,    18,    18,    18,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    18,   nil,   nil,    18,   nil,   nil,    18,
    18,   nil,   nil,    18,   nil,   nil,   nil,   nil,   nil,    18,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,
   nil,   nil,    18,    18,    18,    18,    18,    18,   nil,   nil,
   nil,    18,    18,    22,    22,    22,   nil,    22,   nil,   nil,
   nil,    22,    22,   nil,   nil,   nil,    22,   nil,    22,    22,
    22,    22,    22,    22,    22,   nil,   nil,   nil,   nil,    22,
    22,    22,    22,    22,    22,    22,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    22,   nil,   nil,    22,    22,
    22,    22,    22,    22,    22,    22,    22,    22,   nil,    22,
    22,    22,    22,    22,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    22,   nil,   nil,    22,   nil,   nil,    22,
    22,   nil,   nil,    22,   nil,    22,   nil,    22,   nil,    22,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    22,   nil,   nil,
   nil,   nil,    22,    22,    22,    22,    22,    22,   nil,   nil,
   nil,    22,    22,    23,    23,    23,   nil,    23,   nil,   nil,
   nil,    23,    23,   nil,   nil,   nil,    23,   nil,    23,    23,
    23,    23,    23,    23,    23,   nil,   nil,   nil,   nil,    23,
    23,    23,    23,    23,    23,    23,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    23,   nil,   nil,    23,    23,
    23,    23,    23,    23,    23,    23,    23,    23,   nil,    23,
    23,    23,    23,    23,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    23,   nil,   nil,    23,   nil,   nil,    23,
    23,   nil,   nil,    23,   nil,    23,   nil,    23,   nil,    23,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    23,   nil,   nil,
   nil,   nil,    23,    23,    23,    23,    23,    23,   nil,   nil,
   nil,    23,    23,    24,    24,    24,   nil,    24,   nil,   nil,
   nil,    24,    24,   nil,   nil,   nil,    24,   nil,    24,    24,
    24,    24,    24,    24,    24,   nil,   nil,   nil,   nil,    24,
    24,    24,    24,    24,    24,    24,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    24,   nil,   nil,    24,    24,
    24,    24,    24,    24,    24,    24,    24,    24,   nil,    24,
    24,    24,    24,    24,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    24,   nil,   nil,    24,   nil,   nil,    24,
    24,   nil,   nil,    24,   nil,    24,   nil,    24,   nil,    24,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    24,   nil,   nil,
   nil,   nil,    24,    24,    24,    24,    24,    24,   nil,   nil,
   nil,    24,    24,    27,    27,    27,   nil,    27,   nil,   nil,
   nil,    27,    27,   nil,   nil,   nil,    27,   nil,    27,    27,
    27,    27,    27,    27,    27,   nil,   nil,   nil,   nil,    27,
    27,    27,    27,    27,    27,    27,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    27,   nil,   nil,    27,    27,
    27,    27,    27,    27,    27,    27,    27,    27,   nil,    27,
    27,    27,    27,    27,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    27,   nil,   nil,    27,    27,   nil,    27,
    27,   nil,   nil,    27,   nil,    27,   nil,    27,   nil,    27,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    27,   nil,   nil,
   nil,   nil,    27,    27,    27,    27,    27,    27,   nil,   nil,
   nil,    27,    27,    28,    28,    28,   nil,    28,   nil,   nil,
   nil,    28,    28,   nil,   nil,   nil,    28,   nil,    28,    28,
    28,    28,    28,    28,    28,   nil,   nil,   nil,   nil,    28,
    28,    28,    28,    28,    28,    28,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    28,   nil,   nil,    28,    28,
    28,    28,    28,    28,    28,    28,    28,    28,   nil,    28,
    28,    28,    28,    28,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    28,   nil,   nil,    28,    28,   nil,    28,
    28,   nil,   nil,    28,   nil,    28,   nil,    28,   nil,    28,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    28,   nil,   nil,
   nil,   nil,    28,    28,    28,    28,    28,    28,   nil,   nil,
   nil,    28,    28,    31,    31,    31,   nil,    31,   nil,   nil,
   nil,    31,    31,   nil,   nil,   nil,    31,   nil,    31,    31,
    31,    31,    31,    31,    31,   nil,   nil,   nil,   nil,    31,
    31,    31,    31,    31,    31,    31,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    31,   nil,   nil,    31,    31,
    31,    31,    31,    31,    31,    31,    31,    31,   nil,    31,
    31,   nil,   nil,    31,   nil,   830,   830,   830,   830,   830,
   830,   830,   830,   830,   830,   830,   nil,   830,   830,   nil,
   nil,   830,   830,    31,   nil,   nil,    31,   nil,   nil,    31,
    31,   nil,   nil,    31,   nil,    31,   nil,   830,   nil,   830,
   nil,   830,   830,   830,   830,   830,   830,   830,   nil,   830,
   nil,   nil,    31,    31,    31,    31,    31,    31,   nil,   nil,
   nil,    31,    31,    32,    32,    32,   830,    32,   830,   nil,
   nil,    32,    32,   nil,   nil,   nil,    32,   nil,    32,    32,
    32,    32,    32,    32,    32,   nil,   nil,   nil,   nil,    32,
    32,    32,    32,    32,    32,    32,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    32,   nil,   nil,    32,    32,
    32,    32,    32,    32,    32,    32,    32,    32,   nil,    32,
    32,   nil,   nil,    32,   nil,    19,    19,    19,    19,    19,
    19,    19,    19,    19,    19,    19,   nil,    19,    19,   nil,
   nil,    19,    19,    32,   nil,   nil,    32,   nil,   nil,    32,
    32,   nil,   nil,    32,   nil,   nil,   nil,    19,   nil,    19,
   nil,    19,    19,    19,    19,    19,    19,    19,   nil,    19,
   nil,   nil,    32,    32,    32,    32,    32,    32,   nil,   nil,
   nil,    32,    32,    38,    38,    38,    19,    38,   nil,   nil,
   nil,    38,    38,   nil,   nil,   nil,    38,   nil,    38,    38,
    38,    38,    38,    38,    38,   nil,   nil,   nil,   nil,    38,
    38,    38,    38,    38,    38,    38,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    38,   nil,   nil,    38,    38,
    38,    38,    38,    38,    38,    38,    38,    38,   nil,    38,
    38,    38,    38,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    38,   nil,   nil,    38,   nil,   nil,    38,
    38,   nil,   nil,    38,   nil,   nil,   nil,   nil,   nil,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    38,   nil,   nil,
   nil,   nil,    38,    38,    38,    38,    38,    38,   nil,   nil,
   nil,    38,    38,    39,    39,    39,   nil,    39,   nil,   nil,
   nil,    39,    39,   nil,   nil,   nil,    39,   nil,    39,    39,
    39,    39,    39,    39,    39,   nil,   nil,   nil,   nil,    39,
    39,    39,    39,    39,    39,    39,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    39,   nil,   nil,    39,    39,
    39,    39,    39,    39,    39,    39,    39,    39,   nil,    39,
    39,    39,    39,    39,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    39,   nil,   nil,    39,   nil,   nil,    39,
    39,   nil,   nil,    39,   nil,   nil,   nil,   nil,   nil,    39,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    39,   nil,   nil,
   nil,   nil,    39,    39,    39,    39,    39,    39,   nil,   nil,
   nil,    39,    39,    40,    40,    40,   nil,    40,   nil,   nil,
   nil,    40,    40,   nil,   nil,   nil,    40,   nil,    40,    40,
    40,    40,    40,    40,    40,   nil,   nil,   nil,   nil,    40,
    40,    40,    40,    40,    40,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,    40,    40,
    40,    40,    40,    40,    40,    40,    40,    40,   nil,    40,
    40,    40,    40,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,    40,   nil,   nil,    40,
    40,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
   nil,   nil,    40,    40,    40,    40,    40,    40,   nil,   nil,
   nil,    40,    40,    52,    52,    52,   nil,    52,   nil,   nil,
   nil,    52,    52,   nil,   nil,   nil,    52,   nil,    52,    52,
    52,    52,    52,    52,    52,   nil,   nil,   nil,   nil,    52,
    52,    52,    52,    52,    52,    52,   nil,   nil,    52,   nil,
   nil,   nil,   nil,   nil,   nil,    52,   nil,   nil,    52,    52,
    52,    52,    52,    52,    52,    52,    52,    52,   nil,    52,
    52,    52,    52,    52,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    52,   nil,   nil,    52,   nil,   nil,    52,
    52,   nil,   nil,    52,   nil,   nil,   nil,   nil,   nil,    52,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    52,   nil,   nil,
   nil,   nil,    52,    52,    52,    52,    52,    52,   nil,   nil,
   nil,    52,    52,    53,    53,    53,   nil,    53,   nil,   nil,
   nil,    53,    53,   nil,   nil,   nil,    53,   nil,    53,    53,
    53,    53,    53,    53,    53,   nil,   nil,   nil,   nil,    53,
    53,    53,    53,    53,    53,    53,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    53,   nil,   nil,    53,    53,
    53,    53,    53,    53,    53,    53,    53,    53,   nil,    53,
    53,    53,    53,    53,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    53,   nil,   nil,    53,   nil,   nil,    53,
    53,   nil,   nil,    53,   nil,    53,   nil,   nil,   nil,    53,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    53,   nil,   nil,
   nil,   nil,    53,    53,    53,    53,    53,    53,   nil,   nil,
   nil,    53,    53,    54,    54,    54,   nil,    54,   nil,   nil,
   nil,    54,    54,   nil,   nil,   nil,    54,   nil,    54,    54,
    54,    54,    54,    54,    54,   nil,   nil,   nil,   nil,    54,
    54,    54,    54,    54,    54,    54,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    54,   nil,   nil,    54,    54,
    54,    54,    54,    54,    54,    54,    54,    54,   nil,    54,
    54,    54,    54,    54,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    54,   nil,   nil,    54,   nil,   nil,    54,
    54,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    54,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    54,   nil,   nil,
   nil,   nil,    54,    54,    54,    54,    54,    54,   nil,   nil,
   nil,    54,    54,    56,    56,    56,   nil,    56,   nil,   nil,
   nil,    56,    56,   nil,   nil,   nil,    56,   nil,    56,    56,
    56,    56,    56,    56,    56,   nil,   nil,   nil,   nil,    56,
    56,    56,    56,    56,    56,    56,   nil,   nil,    56,   nil,
   nil,   nil,   nil,   nil,   nil,    56,   nil,   nil,    56,    56,
    56,    56,    56,    56,    56,    56,    56,    56,   nil,    56,
    56,    56,    56,    56,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    56,   nil,   nil,    56,   nil,   nil,    56,
    56,   nil,   nil,    56,   nil,   nil,   nil,   nil,   nil,    56,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    56,   nil,   nil,
   nil,   nil,    56,    56,    56,    56,    56,    56,   nil,   nil,
   nil,    56,    56,    57,    57,    57,   nil,    57,   nil,   nil,
   nil,    57,    57,   nil,   nil,   nil,    57,   nil,    57,    57,
    57,    57,    57,    57,    57,   nil,   nil,   nil,   nil,    57,
    57,    57,    57,    57,    57,    57,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,   nil,    57,   nil,   nil,    57,    57,
    57,    57,    57,    57,    57,    57,    57,    57,   nil,    57,
    57,    57,    57,    57,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    57,   nil,   nil,    57,   nil,   nil,    57,
    57,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,    57,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    57,   nil,   nil,
   nil,   nil,    57,    57,    57,    57,    57,    57,   nil,   nil,
   nil,    57,    57,    61,    61,    61,   nil,    61,   nil,   nil,
   nil,    61,    61,   nil,   nil,   nil,    61,   nil,    61,    61,
    61,    61,    61,    61,    61,   nil,   nil,   nil,   nil,    61,
    61,    61,    61,    61,    61,    61,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    61,   nil,   nil,    61,    61,
    61,    61,    61,    61,    61,    61,    61,    61,   nil,    61,
    61,   nil,   nil,    61,   nil,   239,   239,   239,   239,   239,
   239,   239,   239,   239,   239,   239,   nil,   239,   239,   nil,
   nil,   239,   239,    61,   nil,   nil,    61,   nil,   nil,    61,
    61,   nil,   nil,    61,   nil,    61,   nil,   239,   nil,   239,
   nil,   239,   239,   239,   239,   239,   239,   239,   nil,   239,
   nil,   nil,    61,    61,    61,    61,    61,    61,   nil,   nil,
   nil,    61,    61,    62,    62,    62,   239,    62,   nil,   nil,
   nil,    62,    62,   nil,   nil,   nil,    62,   nil,    62,    62,
    62,    62,    62,    62,    62,   nil,   nil,   nil,   nil,    62,
    62,    62,    62,    62,    62,    62,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    62,   nil,   nil,    62,    62,
    62,    62,    62,    62,    62,    62,    62,    62,   nil,    62,
    62,   nil,   nil,    62,   nil,   376,   376,   376,   376,   376,
   376,   376,   376,   376,   376,   376,   nil,   376,   376,   nil,
    62,   376,   376,    62,   nil,   nil,    62,   nil,   nil,    62,
    62,   nil,   nil,    62,   nil,   nil,   nil,   376,   nil,   376,
   nil,   376,   376,   376,   376,   376,   376,   376,   nil,   376,
   nil,   nil,    62,    62,    62,    62,    62,    62,   nil,   nil,
   nil,    62,    62,    63,    63,    63,   376,    63,   nil,   nil,
   nil,    63,    63,   nil,   nil,   nil,    63,   nil,    63,    63,
    63,    63,    63,    63,    63,   nil,   nil,   nil,   nil,    63,
    63,    63,    63,    63,    63,    63,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    63,   nil,   nil,    63,    63,
    63,    63,    63,    63,    63,    63,    63,    63,   nil,    63,
    63,   nil,   nil,    63,   nil,   390,   390,   390,   390,   390,
   390,   390,   390,   390,   390,   390,   nil,   390,   390,   nil,
   nil,   390,   390,    63,   nil,   nil,    63,   nil,   nil,    63,
    63,   nil,   nil,    63,   nil,   nil,   nil,   390,   nil,   390,
   nil,   390,   390,   390,   390,   390,   390,   390,   nil,   390,
   nil,   nil,    63,    63,    63,    63,    63,    63,   nil,   nil,
   nil,    63,    63,    84,    84,    84,   390,    84,   nil,   nil,
   nil,    84,    84,   nil,   nil,   nil,    84,   nil,    84,    84,
    84,    84,    84,    84,    84,   nil,    84,   nil,   nil,    84,
    84,    84,    84,    84,    84,    84,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    84,   nil,   nil,    84,    84,
    84,    84,    84,    84,    84,    84,    84,    84,   nil,    84,
    84,    84,    84,    84,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    84,   nil,   nil,    84,    84,   nil,    84,
    84,   nil,   nil,    84,   nil,    84,   nil,    84,   nil,    84,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    84,   nil,    84,
   nil,   nil,    84,    84,    84,    84,    84,    84,   nil,   nil,
   nil,    84,    84,    88,    88,    88,   nil,    88,   nil,   nil,
   nil,    88,    88,   nil,   nil,   nil,    88,   nil,    88,    88,
    88,    88,    88,    88,    88,   nil,    88,   nil,   nil,    88,
    88,    88,    88,    88,    88,    88,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    88,   nil,   nil,    88,    88,
    88,    88,    88,    88,    88,    88,    88,    88,   nil,    88,
    88,    88,    88,    88,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    88,   nil,   nil,    88,    88,   nil,    88,
    88,   nil,   nil,    88,   nil,    88,   nil,    88,   nil,    88,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    88,   nil,    88,
   nil,   nil,    88,    88,    88,    88,    88,    88,   nil,   nil,
   nil,    88,    88,   103,   103,   103,   nil,   103,   nil,   nil,
   nil,   103,   103,   nil,   nil,   nil,   103,   nil,   103,   103,
   103,   103,   103,   103,   103,   nil,   nil,   nil,   nil,   103,
   103,   103,   103,   103,   103,   103,   nil,   nil,   103,   nil,
   nil,   nil,   nil,   nil,   nil,   103,   nil,   nil,   103,   103,
   103,   103,   103,   103,   103,   103,   103,   103,   nil,   103,
   103,   103,   103,   103,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   103,   nil,   nil,   103,   nil,   nil,   103,
   103,   nil,   nil,   103,   nil,   nil,   nil,   nil,   nil,   103,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   103,   nil,   nil,
   nil,   nil,   103,   103,   103,   103,   103,   103,   nil,   nil,
   nil,   103,   103,   104,   104,   104,   nil,   104,   nil,   nil,
   nil,   104,   104,   nil,   nil,   nil,   104,   nil,   104,   104,
   104,   104,   104,   104,   104,   nil,   nil,   nil,   nil,   104,
   104,   104,   104,   104,   104,   104,   nil,   nil,   104,   nil,
   nil,   nil,   nil,   nil,   nil,   104,   nil,   nil,   104,   104,
   104,   104,   104,   104,   104,   104,   104,   104,   nil,   104,
   104,   104,   104,   104,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   104,   nil,   nil,   104,   nil,   nil,   104,
   104,   nil,   nil,   104,   nil,   nil,   nil,   nil,   nil,   104,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   104,   nil,   nil,
   nil,   nil,   104,   104,   104,   104,   104,   104,   nil,   nil,
   nil,   104,   104,   105,   105,   105,   nil,   105,   nil,   nil,
   nil,   105,   105,   nil,   nil,   nil,   105,   nil,   105,   105,
   105,   105,   105,   105,   105,   nil,   nil,   nil,   nil,   105,
   105,   105,   105,   105,   105,   105,   nil,   nil,   105,   nil,
   nil,   nil,   nil,   nil,   nil,   105,   nil,   nil,   105,   105,
   105,   105,   105,   105,   105,   105,   105,   105,   nil,   105,
   105,   105,   105,   105,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   105,   nil,   nil,   105,   nil,   nil,   105,
   105,   nil,   nil,   105,   nil,   nil,   nil,   nil,   nil,   105,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   105,   nil,   nil,
   nil,   nil,   105,   105,   105,   105,   105,   105,   nil,   nil,
   nil,   105,   105,   106,   106,   106,   nil,   106,   nil,   nil,
   nil,   106,   106,   nil,   nil,   nil,   106,   nil,   106,   106,
   106,   106,   106,   106,   106,   nil,   nil,   nil,   nil,   106,
   106,   106,   106,   106,   106,   106,   nil,   nil,   106,   nil,
   nil,   nil,   nil,   nil,   nil,   106,   nil,   nil,   106,   106,
   106,   106,   106,   106,   106,   106,   106,   106,   nil,   106,
   106,   106,   106,   106,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   106,   nil,   nil,   106,   nil,   nil,   106,
   106,   nil,   nil,   106,   nil,   nil,   nil,   nil,   nil,   106,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   106,   nil,   nil,
   nil,   nil,   106,   106,   106,   106,   106,   106,   nil,   nil,
   nil,   106,   106,   107,   107,   107,   107,   107,   nil,   nil,
   nil,   107,   107,   nil,   nil,   nil,   107,   nil,   107,   107,
   107,   107,   107,   107,   107,   nil,   nil,   nil,   nil,   107,
   107,   107,   107,   107,   107,   107,   nil,   nil,   107,   nil,
   nil,   nil,   nil,   nil,   107,   107,   107,   107,   107,   107,
   107,   107,   107,   107,   107,   107,   107,   107,   nil,   107,
   107,   107,   107,   107,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   107,   nil,   nil,   107,   nil,   nil,   107,
   107,   nil,   nil,   107,   nil,   107,   nil,   nil,   nil,   107,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   107,   nil,   nil,
   nil,   nil,   107,   107,   107,   107,   107,   107,   nil,   nil,
   nil,   107,   107,   190,   190,   190,   nil,   190,   nil,   nil,
   nil,   190,   190,   nil,   nil,   nil,   190,   nil,   190,   190,
   190,   190,   190,   190,   190,   nil,   nil,   nil,   nil,   190,
   190,   190,   190,   190,   190,   190,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   190,   nil,   nil,   190,   190,
   190,   190,   190,   190,   190,   190,   190,   190,   nil,   190,
   190,   190,   190,   190,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   190,   nil,   nil,   190,   nil,   nil,   190,
   190,   nil,   nil,   190,   nil,   190,   nil,   nil,   nil,   190,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   190,   nil,   nil,
   nil,   nil,   190,   190,   190,   190,   190,   190,   nil,   nil,
   nil,   190,   190,   191,   191,   191,   nil,   191,   nil,   nil,
   nil,   191,   191,   nil,   nil,   nil,   191,   nil,   191,   191,
   191,   191,   191,   191,   191,   nil,   nil,   nil,   nil,   191,
   191,   191,   191,   191,   191,   191,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   191,   nil,   nil,   191,   191,
   191,   191,   191,   191,   191,   191,   191,   191,   nil,   191,
   191,   191,   191,   191,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   191,   nil,   nil,   191,   nil,   nil,   191,
   191,   nil,   nil,   191,   nil,   191,   nil,   nil,   nil,   191,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   191,   nil,   nil,
   nil,   nil,   191,   191,   191,   191,   191,   191,   nil,   nil,
   nil,   191,   191,   192,   192,   192,   nil,   192,   nil,   nil,
   nil,   192,   192,   nil,   nil,   nil,   192,   nil,   192,   192,
   192,   192,   192,   192,   192,   nil,   nil,   nil,   nil,   192,
   192,   192,   192,   192,   192,   192,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   192,   nil,   nil,   192,   192,
   192,   192,   192,   192,   192,   192,   192,   192,   nil,   192,
   192,   192,   192,   192,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   192,   nil,   nil,   192,   nil,   nil,   192,
   192,   nil,   nil,   192,   nil,   nil,   nil,   nil,   nil,   192,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   192,   nil,   nil,
   nil,   nil,   192,   192,   192,   192,   192,   192,   nil,   nil,
   nil,   192,   192,   193,   193,   193,   nil,   193,   nil,   nil,
   nil,   193,   193,   nil,   nil,   nil,   193,   nil,   193,   193,
   193,   193,   193,   193,   193,   nil,   nil,   nil,   nil,   193,
   193,   193,   193,   193,   193,   193,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   193,   nil,   nil,   193,   193,
   193,   193,   193,   193,   193,   193,   193,   193,   nil,   193,
   193,   193,   193,   193,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   193,   nil,   nil,   193,   nil,   nil,   193,
   193,   nil,   nil,   193,   nil,   193,   nil,   nil,   nil,   193,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   193,   nil,   nil,
   nil,   nil,   193,   193,   193,   193,   193,   193,   nil,   nil,
   nil,   193,   193,   196,   196,   196,   nil,   196,   nil,   nil,
   nil,   196,   196,   nil,   nil,   nil,   196,   nil,   196,   196,
   196,   196,   196,   196,   196,   nil,   nil,   nil,   nil,   196,
   196,   196,   196,   196,   196,   196,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   196,   nil,   nil,   196,   196,
   196,   196,   196,   196,   196,   196,   196,   196,   nil,   196,
   196,   196,   196,   196,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   196,   nil,   nil,   196,   nil,   nil,   196,
   196,   nil,   nil,   196,   nil,   nil,   nil,   nil,   nil,   196,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   196,   nil,   nil,
   nil,   nil,   196,   196,   196,   196,   196,   196,   nil,   nil,
   nil,   196,   196,   197,   197,   197,   nil,   197,   nil,   nil,
   nil,   197,   197,   nil,   nil,   nil,   197,   nil,   197,   197,
   197,   197,   197,   197,   197,   nil,   nil,   nil,   nil,   197,
   197,   197,   197,   197,   197,   197,   nil,   nil,   197,   nil,
   nil,   nil,   nil,   nil,   nil,   197,   nil,   nil,   197,   197,
   197,   197,   197,   197,   197,   197,   197,   197,   nil,   197,
   197,   197,   197,   197,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   197,   nil,   nil,   197,   nil,   nil,   197,
   197,   nil,   nil,   197,   nil,   nil,   nil,   nil,   nil,   197,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   197,   nil,   nil,
   nil,   nil,   197,   197,   197,   197,   197,   197,   nil,   nil,
   nil,   197,   197,   198,   198,   198,   nil,   198,   nil,   nil,
   nil,   198,   198,   nil,   nil,   nil,   198,   nil,   198,   198,
   198,   198,   198,   198,   198,   nil,   nil,   nil,   nil,   198,
   198,   198,   198,   198,   198,   198,   nil,   nil,   198,   nil,
   nil,   nil,   nil,   nil,   nil,   198,   nil,   nil,   198,   198,
   198,   198,   198,   198,   198,   198,   198,   198,   nil,   198,
   198,   198,   198,   198,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   198,   nil,   nil,   198,   nil,   nil,   198,
   198,   nil,   nil,   198,   nil,   nil,   nil,   nil,   nil,   198,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   198,   nil,   nil,
   nil,   nil,   198,   198,   198,   198,   198,   198,   nil,   nil,
   nil,   198,   198,   209,   209,   209,   nil,   209,   nil,   nil,
   nil,   209,   209,   nil,   nil,   nil,   209,   nil,   209,   209,
   209,   209,   209,   209,   209,   nil,   nil,   nil,   nil,   209,
   209,   209,   209,   209,   209,   209,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,   209,   209,
   209,   209,   209,   209,   209,   209,   209,   209,   nil,   209,
   209,   209,   209,   209,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   209,   nil,   nil,   209,   nil,   nil,   209,
   209,   nil,   nil,   209,   nil,   nil,   nil,   nil,   nil,   209,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,   209,   209,   209,   209,   209,   209,   nil,   nil,
   nil,   209,   209,   210,   210,   210,   nil,   210,   nil,   nil,
   nil,   210,   210,   nil,   nil,   nil,   210,   nil,   210,   210,
   210,   210,   210,   210,   210,   nil,   nil,   nil,   nil,   210,
   210,   210,   210,   210,   210,   210,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   210,   nil,   nil,   210,   210,
   210,   210,   210,   210,   210,   210,   210,   210,   nil,   210,
   210,   210,   210,   210,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   210,   nil,   nil,   210,   nil,   nil,   210,
   210,   nil,   nil,   210,   nil,   nil,   nil,   nil,   nil,   210,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   210,   nil,   nil,
   nil,   nil,   210,   210,   210,   210,   210,   210,   nil,   nil,
   nil,   210,   210,   211,   211,   211,   nil,   211,   nil,   nil,
   nil,   211,   211,   nil,   nil,   nil,   211,   nil,   211,   211,
   211,   211,   211,   211,   211,   nil,   nil,   nil,   nil,   211,
   211,   211,   211,   211,   211,   211,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   211,   nil,   nil,   211,   211,
   211,   211,   211,   211,   211,   211,   211,   211,   nil,   211,
   211,   211,   211,   211,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   211,   nil,   nil,   211,   nil,   nil,   211,
   211,   nil,   nil,   211,   nil,   nil,   nil,   nil,   nil,   211,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   211,   nil,   nil,
   nil,   nil,   211,   211,   211,   211,   211,   211,   nil,   nil,
   nil,   211,   211,   212,   212,   212,   nil,   212,   nil,   nil,
   nil,   212,   212,   nil,   nil,   nil,   212,   nil,   212,   212,
   212,   212,   212,   212,   212,   nil,   nil,   nil,   nil,   212,
   212,   212,   212,   212,   212,   212,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   212,   nil,   nil,   212,   212,
   212,   212,   212,   212,   212,   212,   212,   212,   nil,   212,
   212,   212,   212,   212,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   212,   nil,   nil,   212,   nil,   nil,   212,
   212,   nil,   nil,   212,   nil,   nil,   nil,   nil,   nil,   212,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   212,   nil,   nil,
   nil,   nil,   212,   212,   212,   212,   212,   212,   nil,   nil,
   nil,   212,   212,   213,   213,   213,   nil,   213,   nil,   nil,
   nil,   213,   213,   nil,   nil,   nil,   213,   nil,   213,   213,
   213,   213,   213,   213,   213,   nil,   nil,   nil,   nil,   213,
   213,   213,   213,   213,   213,   213,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   213,   nil,   nil,   213,   213,
   213,   213,   213,   213,   213,   213,   213,   213,   nil,   213,
   213,   213,   213,   213,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   213,   nil,   nil,   213,   nil,   nil,   213,
   213,   nil,   nil,   213,   nil,   nil,   nil,   nil,   nil,   213,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,   nil,
   nil,   nil,   213,   213,   213,   213,   213,   213,   nil,   nil,
   nil,   213,   213,   214,   214,   214,   nil,   214,   nil,   nil,
   nil,   214,   214,   nil,   nil,   nil,   214,   nil,   214,   214,
   214,   214,   214,   214,   214,   nil,   nil,   nil,   nil,   214,
   214,   214,   214,   214,   214,   214,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   214,   214,
   214,   214,   214,   214,   214,   214,   214,   214,   nil,   214,
   214,   214,   214,   214,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   214,   nil,   nil,   214,   nil,   nil,   214,
   214,   nil,   nil,   214,   nil,   nil,   nil,   nil,   nil,   214,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,
   nil,   nil,   214,   214,   214,   214,   214,   214,   nil,   nil,
   nil,   214,   214,   215,   215,   215,   nil,   215,   nil,   nil,
   nil,   215,   215,   nil,   nil,   nil,   215,   nil,   215,   215,
   215,   215,   215,   215,   215,   nil,   nil,   nil,   nil,   215,
   215,   215,   215,   215,   215,   215,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   215,   nil,   nil,   215,   215,
   215,   215,   215,   215,   215,   215,   215,   215,   nil,   215,
   215,   215,   215,   215,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   215,   nil,   nil,   215,   nil,   nil,   215,
   215,   nil,   nil,   215,   nil,   nil,   nil,   nil,   nil,   215,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   215,   nil,   nil,
   nil,   nil,   215,   215,   215,   215,   215,   215,   nil,   nil,
   nil,   215,   215,   216,   216,   216,   nil,   216,   nil,   nil,
   nil,   216,   216,   nil,   nil,   nil,   216,   nil,   216,   216,
   216,   216,   216,   216,   216,   nil,   nil,   nil,   nil,   216,
   216,   216,   216,   216,   216,   216,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   216,   nil,   nil,   216,   216,
   216,   216,   216,   216,   216,   216,   216,   216,   nil,   216,
   216,   216,   216,   216,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   216,   nil,   nil,   216,
   216,   nil,   nil,   216,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,   nil,
   nil,   nil,   216,   216,   216,   216,   216,   216,   nil,   nil,
   nil,   216,   216,   217,   217,   217,   nil,   217,   nil,   nil,
   nil,   217,   217,   nil,   nil,   nil,   217,   nil,   217,   217,
   217,   217,   217,   217,   217,   nil,   nil,   nil,   nil,   217,
   217,   217,   217,   217,   217,   217,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   217,   nil,   nil,   217,   217,
   217,   217,   217,   217,   217,   217,   217,   217,   nil,   217,
   217,   217,   217,   217,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   217,   nil,   nil,   217,   nil,   nil,   217,
   217,   nil,   nil,   217,   nil,   nil,   nil,   nil,   nil,   217,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   217,   nil,   nil,
   nil,   nil,   217,   217,   217,   217,   217,   217,   nil,   nil,
   nil,   217,   217,   218,   218,   218,   nil,   218,   nil,   nil,
   nil,   218,   218,   nil,   nil,   nil,   218,   nil,   218,   218,
   218,   218,   218,   218,   218,   nil,   nil,   nil,   nil,   218,
   218,   218,   218,   218,   218,   218,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,   218,   218,
   218,   218,   218,   218,   218,   218,   218,   218,   nil,   218,
   218,   218,   218,   218,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   218,   nil,   nil,   218,   nil,   nil,   218,
   218,   nil,   nil,   218,   nil,   nil,   nil,   nil,   nil,   218,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,
   nil,   nil,   218,   218,   218,   218,   218,   218,   nil,   nil,
   nil,   218,   218,   219,   219,   219,   nil,   219,   nil,   nil,
   nil,   219,   219,   nil,   nil,   nil,   219,   nil,   219,   219,
   219,   219,   219,   219,   219,   nil,   nil,   nil,   nil,   219,
   219,   219,   219,   219,   219,   219,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   219,   219,
   219,   219,   219,   219,   219,   219,   219,   219,   nil,   219,
   219,   219,   219,   219,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   219,   nil,   nil,   219,   nil,   nil,   219,
   219,   nil,   nil,   219,   nil,   nil,   nil,   nil,   nil,   219,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,
   nil,   nil,   219,   219,   219,   219,   219,   219,   nil,   nil,
   nil,   219,   219,   220,   220,   220,   nil,   220,   nil,   nil,
   nil,   220,   220,   nil,   nil,   nil,   220,   nil,   220,   220,
   220,   220,   220,   220,   220,   nil,   nil,   nil,   nil,   220,
   220,   220,   220,   220,   220,   220,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   220,   nil,   nil,   220,   220,
   220,   220,   220,   220,   220,   220,   220,   220,   nil,   220,
   220,   220,   220,   220,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   220,   nil,   nil,   220,   nil,   nil,   220,
   220,   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   220,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   220,   nil,   nil,
   nil,   nil,   220,   220,   220,   220,   220,   220,   nil,   nil,
   nil,   220,   220,   221,   221,   221,   nil,   221,   nil,   nil,
   nil,   221,   221,   nil,   nil,   nil,   221,   nil,   221,   221,
   221,   221,   221,   221,   221,   nil,   nil,   nil,   nil,   221,
   221,   221,   221,   221,   221,   221,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,   221,   221,
   221,   221,   221,   221,   221,   221,   221,   221,   nil,   221,
   221,   221,   221,   221,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   221,   nil,   nil,   221,   nil,   nil,   221,
   221,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   221,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,
   nil,   nil,   221,   221,   221,   221,   221,   221,   nil,   nil,
   nil,   221,   221,   222,   222,   222,   nil,   222,   nil,   nil,
   nil,   222,   222,   nil,   nil,   nil,   222,   nil,   222,   222,
   222,   222,   222,   222,   222,   nil,   nil,   nil,   nil,   222,
   222,   222,   222,   222,   222,   222,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   222,   222,
   222,   222,   222,   222,   222,   222,   222,   222,   nil,   222,
   222,   222,   222,   222,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   222,   nil,   nil,   222,   nil,   nil,   222,
   222,   nil,   nil,   222,   nil,   nil,   nil,   nil,   nil,   222,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,
   nil,   nil,   222,   222,   222,   222,   222,   222,   nil,   nil,
   nil,   222,   222,   223,   223,   223,   nil,   223,   nil,   nil,
   nil,   223,   223,   nil,   nil,   nil,   223,   nil,   223,   223,
   223,   223,   223,   223,   223,   nil,   nil,   nil,   nil,   223,
   223,   223,   223,   223,   223,   223,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   223,   nil,   nil,   223,   223,
   223,   223,   223,   223,   223,   223,   223,   223,   nil,   223,
   223,   223,   223,   223,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   223,   nil,   nil,   223,   nil,   nil,   223,
   223,   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   223,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   223,   nil,   nil,
   nil,   nil,   223,   223,   223,   223,   223,   223,   nil,   nil,
   nil,   223,   223,   224,   224,   224,   nil,   224,   nil,   nil,
   nil,   224,   224,   nil,   nil,   nil,   224,   nil,   224,   224,
   224,   224,   224,   224,   224,   nil,   nil,   nil,   nil,   224,
   224,   224,   224,   224,   224,   224,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   224,   nil,   nil,   224,   224,
   224,   224,   224,   224,   224,   224,   224,   224,   nil,   224,
   224,   224,   224,   224,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   224,   nil,   nil,   224,   nil,   nil,   224,
   224,   nil,   nil,   224,   nil,   nil,   nil,   nil,   nil,   224,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   224,   nil,   nil,
   nil,   nil,   224,   224,   224,   224,   224,   224,   nil,   nil,
   nil,   224,   224,   225,   225,   225,   nil,   225,   nil,   nil,
   nil,   225,   225,   nil,   nil,   nil,   225,   nil,   225,   225,
   225,   225,   225,   225,   225,   nil,   nil,   nil,   nil,   225,
   225,   225,   225,   225,   225,   225,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   225,   225,
   225,   225,   225,   225,   225,   225,   225,   225,   nil,   225,
   225,   225,   225,   225,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   nil,   225,   nil,   nil,   225,
   225,   nil,   nil,   225,   nil,   nil,   nil,   nil,   nil,   225,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,
   nil,   nil,   225,   225,   225,   225,   225,   225,   nil,   nil,
   nil,   225,   225,   226,   226,   226,   nil,   226,   nil,   nil,
   nil,   226,   226,   nil,   nil,   nil,   226,   nil,   226,   226,
   226,   226,   226,   226,   226,   nil,   nil,   nil,   nil,   226,
   226,   226,   226,   226,   226,   226,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   226,   nil,   nil,   226,   226,
   226,   226,   226,   226,   226,   226,   226,   226,   nil,   226,
   226,   226,   226,   226,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   226,   nil,   nil,   226,   nil,   nil,   226,
   226,   nil,   nil,   226,   nil,   nil,   nil,   nil,   nil,   226,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   226,   nil,   nil,
   nil,   nil,   226,   226,   226,   226,   226,   226,   nil,   nil,
   nil,   226,   226,   227,   227,   227,   nil,   227,   nil,   nil,
   nil,   227,   227,   nil,   nil,   nil,   227,   nil,   227,   227,
   227,   227,   227,   227,   227,   nil,   nil,   nil,   nil,   227,
   227,   227,   227,   227,   227,   227,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   227,   227,
   227,   227,   227,   227,   227,   227,   227,   227,   nil,   227,
   227,   227,   227,   227,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   227,   nil,   nil,   227,   nil,   nil,   227,
   227,   nil,   nil,   227,   nil,   nil,   nil,   nil,   nil,   227,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,
   nil,   nil,   227,   227,   227,   227,   227,   227,   nil,   nil,
   nil,   227,   227,   228,   228,   228,   nil,   228,   nil,   nil,
   nil,   228,   228,   nil,   nil,   nil,   228,   nil,   228,   228,
   228,   228,   228,   228,   228,   nil,   nil,   nil,   nil,   228,
   228,   228,   228,   228,   228,   228,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   228,   nil,   nil,   228,   228,
   228,   228,   228,   228,   228,   228,   228,   228,   nil,   228,
   228,   228,   228,   228,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   228,   nil,   nil,   228,   nil,   nil,   228,
   228,   nil,   nil,   228,   nil,   nil,   nil,   nil,   nil,   228,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   228,   nil,   nil,
   nil,   nil,   228,   228,   228,   228,   228,   228,   nil,   nil,
   nil,   228,   228,   229,   229,   229,   nil,   229,   nil,   nil,
   nil,   229,   229,   nil,   nil,   nil,   229,   nil,   229,   229,
   229,   229,   229,   229,   229,   nil,   nil,   nil,   nil,   229,
   229,   229,   229,   229,   229,   229,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   229,   nil,   nil,   229,   229,
   229,   229,   229,   229,   229,   229,   229,   229,   nil,   229,
   229,   229,   229,   229,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   229,   nil,   nil,   229,   nil,   nil,   229,
   229,   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,   229,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   229,   nil,   nil,
   nil,   nil,   229,   229,   229,   229,   229,   229,   nil,   nil,
   nil,   229,   229,   230,   230,   230,   nil,   230,   nil,   nil,
   nil,   230,   230,   nil,   nil,   nil,   230,   nil,   230,   230,
   230,   230,   230,   230,   230,   nil,   nil,   nil,   nil,   230,
   230,   230,   230,   230,   230,   230,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   230,   230,
   230,   230,   230,   230,   230,   230,   230,   230,   nil,   230,
   230,   230,   230,   230,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   230,   nil,   nil,   230,
   230,   nil,   nil,   230,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   nil,   nil,   230,   230,   230,   230,   230,   230,   nil,   nil,
   nil,   230,   230,   231,   231,   231,   nil,   231,   nil,   nil,
   nil,   231,   231,   nil,   nil,   nil,   231,   nil,   231,   231,
   231,   231,   231,   231,   231,   nil,   nil,   nil,   nil,   231,
   231,   231,   231,   231,   231,   231,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   231,   231,
   231,   231,   231,   231,   231,   231,   231,   231,   nil,   231,
   231,   231,   231,   231,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   231,   nil,   nil,   231,   nil,   nil,   231,
   231,   nil,   nil,   231,   nil,   nil,   nil,   nil,   nil,   231,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,
   nil,   nil,   231,   231,   231,   231,   231,   231,   nil,   nil,
   nil,   231,   231,   232,   232,   232,   nil,   232,   nil,   nil,
   nil,   232,   232,   nil,   nil,   nil,   232,   nil,   232,   232,
   232,   232,   232,   232,   232,   nil,   nil,   nil,   nil,   232,
   232,   232,   232,   232,   232,   232,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,   232,   232,
   232,   232,   232,   232,   232,   232,   232,   232,   nil,   232,
   232,   232,   232,   232,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   232,   nil,   nil,   232,   nil,   nil,   232,
   232,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   232,   232,   232,   232,   232,   232,   nil,   nil,
   nil,   232,   232,   233,   233,   233,   nil,   233,   nil,   nil,
   nil,   233,   233,   nil,   nil,   nil,   233,   nil,   233,   233,
   233,   233,   233,   233,   233,   nil,   nil,   nil,   nil,   233,
   233,   233,   233,   233,   233,   233,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   233,   nil,   nil,   233,   233,
   233,   233,   233,   233,   233,   233,   233,   233,   nil,   233,
   233,   233,   233,   233,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   233,   nil,   nil,   233,   nil,   nil,   233,
   233,   nil,   nil,   233,   nil,   nil,   nil,   nil,   nil,   233,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   233,   nil,   nil,
   nil,   nil,   233,   233,   233,   233,   233,   233,   nil,   nil,
   nil,   233,   233,   234,   234,   234,   nil,   234,   nil,   nil,
   nil,   234,   234,   nil,   nil,   nil,   234,   nil,   234,   234,
   234,   234,   234,   234,   234,   nil,   nil,   nil,   nil,   234,
   234,   234,   234,   234,   234,   234,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   234,   nil,   nil,   234,   234,
   234,   234,   234,   234,   234,   234,   234,   234,   nil,   234,
   234,   234,   234,   234,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   234,   nil,   nil,   234,   nil,   nil,   234,
   234,   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   234,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   234,   nil,   nil,
   nil,   nil,   234,   234,   234,   234,   234,   234,   nil,   nil,
   nil,   234,   234,   235,   235,   235,   nil,   235,   nil,   nil,
   nil,   235,   235,   nil,   nil,   nil,   235,   nil,   235,   235,
   235,   235,   235,   235,   235,   nil,   nil,   nil,   nil,   235,
   235,   235,   235,   235,   235,   235,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   235,   235,
   235,   235,   235,   235,   235,   235,   235,   235,   nil,   235,
   235,   235,   235,   235,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   235,   nil,   nil,   235,   nil,   nil,   235,
   235,   nil,   nil,   235,   nil,   nil,   nil,   nil,   nil,   235,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,
   nil,   nil,   235,   235,   235,   235,   235,   235,   nil,   nil,
   nil,   235,   235,   243,   243,   243,   nil,   243,   nil,   nil,
   nil,   243,   243,   nil,   nil,   nil,   243,   nil,   243,   243,
   243,   243,   243,   243,   243,   nil,   nil,   nil,   nil,   243,
   243,   243,   243,   243,   243,   243,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   243,   nil,   nil,   243,   243,
   243,   243,   243,   243,   243,   243,   243,   243,   nil,   243,
   243,   243,   243,   243,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   243,   nil,   nil,   243,   nil,   nil,   243,
   243,   nil,   nil,   243,   nil,   nil,   nil,   nil,   nil,   243,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   243,   nil,   nil,
   nil,   nil,   243,   243,   243,   243,   243,   243,   nil,   nil,
   nil,   243,   243,   245,   245,   245,   nil,   245,   nil,   nil,
   nil,   245,   245,   nil,   nil,   nil,   245,   nil,   245,   245,
   245,   245,   245,   245,   245,   nil,   nil,   nil,   nil,   245,
   245,   245,   245,   245,   245,   245,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   245,   nil,   nil,   245,   245,
   245,   245,   245,   245,   245,   245,   245,   245,   nil,   245,
   245,   245,   245,   245,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   245,   nil,   nil,   245,   nil,   nil,   245,
   245,   nil,   nil,   245,   nil,   nil,   nil,   nil,   nil,   245,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   245,   nil,   nil,
   nil,   nil,   245,   245,   245,   245,   245,   245,   nil,   nil,
   nil,   245,   245,   256,   256,   256,   nil,   256,   nil,   nil,
   nil,   256,   256,   nil,   nil,   nil,   256,   nil,   256,   256,
   256,   256,   256,   256,   256,   nil,   nil,   nil,   nil,   256,
   256,   256,   256,   256,   256,   256,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   256,   nil,   nil,   256,   256,
   256,   256,   256,   256,   256,   256,   256,   256,   nil,   256,
   256,   256,   256,   256,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   256,   nil,   nil,   256,   nil,   nil,   256,
   256,   nil,   nil,   256,   nil,   256,   nil,   256,   nil,   256,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   256,   nil,   nil,
   nil,   nil,   256,   256,   256,   256,   256,   256,   nil,   nil,
   nil,   256,   256,   257,   257,   257,   nil,   257,   nil,   nil,
   nil,   257,   257,   nil,   nil,   nil,   257,   nil,   257,   257,
   257,   257,   257,   257,   257,   nil,   nil,   nil,   nil,   257,
   257,   257,   257,   257,   257,   257,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   257,   nil,   nil,   257,   257,
   257,   257,   257,   257,   257,   257,   257,   257,   nil,   257,
   257,   257,   257,   257,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   257,   nil,   nil,   257,   nil,   nil,   257,
   257,   nil,   nil,   257,   nil,   257,   nil,   257,   nil,   257,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   257,   nil,   nil,
   nil,   nil,   257,   257,   257,   257,   257,   257,   nil,   nil,
   nil,   257,   257,   265,   265,   265,   nil,   265,   nil,   nil,
   nil,   265,   265,   nil,   nil,   nil,   265,   nil,   265,   265,
   265,   265,   265,   265,   265,   nil,   nil,   nil,   nil,   265,
   265,   265,   265,   265,   265,   265,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   265,   nil,   nil,   265,   265,
   265,   265,   265,   265,   265,   265,   265,   265,   nil,   265,
   265,   265,   265,   265,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   265,   nil,   nil,   265,   nil,   265,   265,
   265,   nil,   nil,   265,   nil,   265,   nil,   265,   nil,   265,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   265,   nil,   nil,
   nil,   nil,   265,   265,   265,   265,   265,   265,   nil,   nil,
   nil,   265,   265,   271,   271,   271,   nil,   271,   nil,   nil,
   nil,   271,   271,   nil,   nil,   nil,   271,   nil,   271,   271,
   271,   271,   271,   271,   271,   nil,   nil,   nil,   nil,   271,
   271,   271,   271,   271,   271,   271,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   271,   nil,   nil,   271,   271,
   271,   271,   271,   271,   271,   271,   271,   271,   nil,   271,
   271,   nil,   nil,   271,   nil,   470,   470,   470,   470,   470,
   470,   470,   470,   470,   470,   470,   nil,   470,   470,   nil,
   nil,   470,   470,   271,   nil,   nil,   271,   nil,   nil,   271,
   271,   nil,   nil,   271,   nil,   nil,   nil,   470,   nil,   470,
   nil,   470,   470,   470,   470,   470,   470,   470,   nil,   470,
   nil,   nil,   271,   271,   271,   271,   271,   271,   nil,   nil,
   nil,   271,   271,   292,   292,   292,   470,   292,   nil,   nil,
   nil,   292,   292,   nil,   nil,   nil,   292,   nil,   292,   292,
   292,   292,   292,   292,   292,   nil,   nil,   nil,   nil,   292,
   292,   292,   292,   292,   292,   292,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   292,   nil,   nil,   292,   292,
   292,   292,   292,   292,   292,   292,   292,   292,   nil,   292,
   292,   292,   292,   292,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   292,   nil,   nil,   292,   292,   nil,   292,
   292,   nil,   nil,   292,   nil,   nil,   nil,   nil,   nil,   292,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   292,   nil,   nil,
   nil,   nil,   292,   292,   292,   292,   292,   292,   nil,   nil,
   nil,   292,   292,   301,   301,   301,   nil,   301,   nil,   nil,
   nil,   301,   301,   nil,   nil,   nil,   301,   nil,   301,   301,
   301,   301,   301,   301,   301,   nil,   nil,   nil,   nil,   301,
   301,   301,   301,   301,   301,   301,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   301,   nil,   nil,   301,   301,
   301,   301,   301,   301,   301,   301,   301,   301,   nil,   301,
   301,   301,   301,   301,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   301,   nil,   nil,   301,   nil,   nil,   301,
   301,   nil,   nil,   301,   nil,   nil,   nil,   nil,   nil,   301,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   301,   nil,   nil,
   nil,   nil,   301,   301,   301,   301,   301,   301,   nil,   nil,
   nil,   301,   301,   310,   310,   310,   nil,   310,   nil,   nil,
   nil,   310,   310,   nil,   nil,   nil,   310,   nil,   310,   310,
   310,   310,   310,   310,   310,   nil,   nil,   nil,   nil,   310,
   310,   310,   310,   310,   310,   310,   nil,   nil,   310,   nil,
   nil,   nil,   nil,   nil,   nil,   310,   nil,   nil,   310,   310,
   310,   310,   310,   310,   310,   310,   310,   310,   nil,   310,
   310,   310,   310,   310,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   310,   nil,   nil,   310,   nil,   nil,   310,
   310,   nil,   nil,   310,   nil,   nil,   nil,   nil,   nil,   310,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   310,   nil,   nil,
   nil,   nil,   310,   310,   310,   310,   310,   310,   nil,   nil,
   nil,   310,   310,   311,   311,   311,   nil,   311,   nil,   nil,
   nil,   311,   311,   nil,   nil,   nil,   311,   nil,   311,   311,
   311,   311,   311,   311,   311,   nil,   nil,   nil,   nil,   311,
   311,   311,   311,   311,   311,   311,   nil,   nil,   311,   nil,
   nil,   nil,   nil,   nil,   nil,   311,   nil,   nil,   311,   311,
   311,   311,   311,   311,   311,   311,   311,   311,   nil,   311,
   311,   311,   311,   311,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   311,   nil,   nil,   311,   nil,   nil,   311,
   311,   nil,   nil,   311,   nil,   nil,   nil,   nil,   nil,   311,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   311,   nil,   nil,
   nil,   nil,   311,   311,   311,   311,   311,   311,   nil,   nil,
   nil,   311,   311,   329,   329,   329,   nil,   329,   nil,   nil,
   nil,   329,   329,   nil,   nil,   nil,   329,   nil,   329,   329,
   329,   329,   329,   329,   329,   nil,   nil,   nil,   nil,   329,
   329,   329,   329,   329,   329,   329,   nil,   nil,   329,   nil,
   nil,   nil,   nil,   nil,   nil,   329,   nil,   nil,   329,   329,
   329,   329,   329,   329,   329,   329,   329,   329,   nil,   329,
   329,   329,   329,   329,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   329,   nil,   nil,   329,   nil,   nil,   329,
   329,   nil,   nil,   329,   nil,   nil,   nil,   nil,   nil,   329,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   329,   nil,   nil,
   nil,   nil,   329,   329,   329,   329,   329,   329,   nil,   nil,
   nil,   329,   329,   343,   343,   343,   nil,   343,   nil,   nil,
   nil,   343,   343,   nil,   nil,   nil,   343,   nil,   343,   343,
   343,   343,   343,   343,   343,   nil,   nil,   nil,   nil,   343,
   343,   343,   343,   343,   343,   343,   nil,   nil,   343,   nil,
   nil,   nil,   nil,   nil,   nil,   343,   nil,   nil,   343,   343,
   343,   343,   343,   343,   343,   343,   343,   343,   nil,   343,
   343,   343,   343,   343,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   343,   nil,   nil,   343,   nil,   nil,   343,
   343,   nil,   nil,   343,   nil,   nil,   nil,   nil,   nil,   343,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   343,   nil,   nil,
   nil,   nil,   343,   343,   343,   343,   343,   343,   nil,   nil,
   nil,   343,   343,   359,   359,   359,   359,   359,   359,   359,
   359,   359,   359,   359,   359,   359,   359,   359,   359,   359,
   359,   359,   359,   359,   359,   359,   359,   nil,   nil,   359,
   359,   359,   359,   359,   359,   359,   359,   359,   359,   nil,
   nil,   nil,   nil,   nil,   359,   359,   359,   359,   359,   359,
   359,   359,   nil,   nil,   359,   nil,   nil,   nil,   nil,   nil,
   nil,   359,   359,   nil,   359,   359,   359,   359,   nil,   359,
   359,   nil,   nil,   359,   nil,   nil,   nil,   nil,   359,   359,
   359,   359,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   359,   359,   nil,   359,   359,
   359,   359,   359,   359,   359,   359,   359,   nil,   359,   nil,
   nil,   359,   359,   nil,   nil,   371,   371,   371,   nil,   371,
   nil,   nil,   359,   371,   371,   nil,   nil,   nil,   371,   nil,
   371,   371,   371,   371,   371,   371,   371,   nil,   nil,   nil,
   nil,   371,   371,   371,   371,   371,   371,   371,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   371,   nil,   nil,
   371,   371,   371,   371,   371,   371,   371,   371,   371,   371,
   nil,   371,   371,   371,   371,   371,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   371,   nil,   nil,   371,   nil,
   nil,   371,   371,   nil,   nil,   371,   nil,   nil,   nil,   nil,
   nil,   371,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   371,
   nil,   nil,   nil,   nil,   371,   371,   371,   371,   371,   371,
   nil,   nil,   nil,   371,   371,   380,   380,   380,   nil,   380,
   nil,   nil,   nil,   380,   380,   nil,   nil,   nil,   380,   nil,
   380,   380,   380,   380,   380,   380,   380,   nil,   nil,   nil,
   nil,   380,   380,   380,   380,   380,   380,   380,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   380,   nil,   nil,
   380,   380,   380,   380,   380,   380,   380,   380,   380,   380,
   nil,   380,   380,   380,   380,   380,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   380,   nil,   nil,   380,   380,
   nil,   380,   380,   nil,   nil,   380,   nil,   380,   nil,   380,
   nil,   380,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   380,
   nil,   nil,   nil,   nil,   380,   380,   380,   380,   380,   380,
   nil,   nil,   nil,   380,   380,   387,   387,   387,   nil,   387,
   nil,   nil,   nil,   387,   387,   nil,   nil,   nil,   387,   nil,
   387,   387,   387,   387,   387,   387,   387,   nil,   nil,   nil,
   nil,   387,   387,   387,   387,   387,   387,   387,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   387,   nil,   nil,
   387,   387,   387,   387,   387,   387,   387,   387,   387,   387,
   nil,   387,   387,   387,   387,   387,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   387,   nil,   nil,   387,   387,
   nil,   387,   387,   nil,   nil,   387,   nil,   387,   nil,   387,
   nil,   387,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   387,
   nil,   nil,   nil,   nil,   387,   387,   387,   387,   387,   387,
   nil,   nil,   nil,   387,   387,   388,   388,   388,   nil,   388,
   nil,   nil,   nil,   388,   388,   nil,   nil,   nil,   388,   nil,
   388,   388,   388,   388,   388,   388,   388,   nil,   nil,   nil,
   nil,   388,   388,   388,   388,   388,   388,   388,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   388,   nil,   nil,
   388,   388,   388,   388,   388,   388,   388,   388,   388,   388,
   nil,   388,   388,   388,   388,   388,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   388,   nil,   nil,   388,   388,
   nil,   388,   388,   nil,   nil,   388,   nil,   388,   nil,   388,
   nil,   388,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   388,
   nil,   nil,   nil,   nil,   388,   388,   388,   388,   388,   388,
   nil,   nil,   nil,   388,   388,   395,   395,   395,   nil,   395,
   nil,   nil,   nil,   395,   395,   nil,   nil,   nil,   395,   nil,
   395,   395,   395,   395,   395,   395,   395,   nil,   nil,   nil,
   nil,   395,   395,   395,   395,   395,   395,   395,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   395,   nil,   nil,
   395,   395,   395,   395,   395,   395,   395,   395,   395,   395,
   nil,   395,   395,   395,   395,   395,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   395,   nil,   nil,   395,   nil,
   nil,   395,   395,   nil,   nil,   395,   nil,   395,   nil,   nil,
   nil,   395,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   395,
   nil,   nil,   nil,   nil,   395,   395,   395,   395,   395,   395,
   nil,   nil,   nil,   395,   395,   397,   397,   397,   nil,   397,
   nil,   nil,   nil,   397,   397,   nil,   nil,   nil,   397,   nil,
   397,   397,   397,   397,   397,   397,   397,   nil,   nil,   nil,
   nil,   397,   397,   397,   397,   397,   397,   397,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   397,   nil,   nil,
   397,   397,   397,   397,   397,   397,   397,   397,   397,   397,
   nil,   397,   397,   397,   397,   397,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   397,   nil,   nil,   397,   nil,
   nil,   397,   397,   nil,   nil,   397,   nil,   nil,   nil,   nil,
   nil,   397,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   397,
   nil,   nil,   nil,   nil,   397,   397,   397,   397,   397,   397,
   nil,   nil,   nil,   397,   397,   398,   398,   398,   nil,   398,
   nil,   nil,   nil,   398,   398,   nil,   nil,   nil,   398,   nil,
   398,   398,   398,   398,   398,   398,   398,   nil,   nil,   nil,
   nil,   398,   398,   398,   398,   398,   398,   398,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   398,   nil,   nil,
   398,   398,   398,   398,   398,   398,   398,   398,   398,   398,
   nil,   398,   398,   398,   398,   398,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   398,   nil,   nil,   398,   nil,
   nil,   398,   398,   nil,   nil,   398,   nil,   nil,   nil,   nil,
   nil,   398,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   398,
   nil,   nil,   nil,   nil,   398,   398,   398,   398,   398,   398,
   nil,   nil,   nil,   398,   398,   399,   399,   399,   nil,   399,
   nil,   nil,   nil,   399,   399,   nil,   nil,   nil,   399,   nil,
   399,   399,   399,   399,   399,   399,   399,   nil,   nil,   nil,
   nil,   399,   399,   399,   399,   399,   399,   399,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   399,   nil,   nil,
   399,   399,   399,   399,   399,   399,   399,   399,   399,   399,
   nil,   399,   399,   399,   399,   399,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   399,   nil,   nil,   399,   nil,
   nil,   399,   399,   nil,   nil,   399,   nil,   nil,   nil,   nil,
   nil,   399,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   399,
   nil,   nil,   nil,   nil,   399,   399,   399,   399,   399,   399,
   nil,   nil,   nil,   399,   399,   428,   428,   428,   nil,   428,
   nil,   nil,   nil,   428,   428,   nil,   nil,   nil,   428,   nil,
   428,   428,   428,   428,   428,   428,   428,   nil,   nil,   nil,
   nil,   428,   428,   428,   428,   428,   428,   428,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   428,   nil,   nil,
   428,   428,   428,   428,   428,   428,   428,   428,   428,   428,
   nil,   428,   428,   428,   428,   428,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   428,   nil,   nil,   428,   nil,
   nil,   428,   428,   nil,   nil,   428,   nil,   428,   nil,   428,
   nil,   428,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   428,
   nil,   nil,   nil,   nil,   428,   428,   428,   428,   428,   428,
   nil,   nil,   nil,   428,   428,   430,   430,   430,   nil,   430,
   nil,   nil,   nil,   430,   430,   nil,   nil,   nil,   430,   nil,
   430,   430,   430,   430,   430,   430,   430,   nil,   nil,   nil,
   nil,   430,   430,   430,   430,   430,   430,   430,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   430,   nil,   nil,
   430,   430,   430,   430,   430,   430,   430,   430,   430,   430,
   nil,   430,   430,   430,   430,   430,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   430,   nil,   nil,   430,   nil,
   nil,   430,   430,   nil,   nil,   430,   nil,   430,   nil,   430,
   nil,   430,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   430,
   nil,   nil,   nil,   nil,   430,   430,   430,   430,   430,   430,
   nil,   nil,   nil,   430,   430,   433,   433,   433,   nil,   433,
   nil,   nil,   nil,   433,   433,   nil,   nil,   nil,   433,   nil,
   433,   433,   433,   433,   433,   433,   433,   nil,   nil,   nil,
   nil,   433,   433,   433,   433,   433,   433,   433,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   433,   nil,   nil,
   433,   433,   433,   433,   433,   433,   433,   433,   433,   433,
   nil,   433,   433,   433,   433,   433,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   433,   nil,   nil,   433,   nil,
   nil,   433,   433,   nil,   nil,   433,   nil,   nil,   nil,   nil,
   nil,   433,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   433,
   nil,   nil,   nil,   nil,   433,   433,   433,   433,   433,   433,
   nil,   nil,   nil,   433,   433,   447,   447,   447,   nil,   447,
   nil,   nil,   nil,   447,   447,   nil,   nil,   nil,   447,   nil,
   447,   447,   447,   447,   447,   447,   447,   nil,   nil,   nil,
   nil,   447,   447,   447,   447,   447,   447,   447,   nil,   nil,
   447,   nil,   nil,   nil,   nil,   nil,   nil,   447,   nil,   nil,
   447,   447,   447,   447,   447,   447,   447,   447,   447,   447,
   nil,   447,   447,   447,   447,   447,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   447,   nil,   nil,   447,   nil,
   nil,   447,   447,   nil,   nil,   447,   nil,   447,   nil,   447,
   nil,   447,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   447,
   nil,   nil,   nil,   nil,   447,   447,   447,   447,   447,   447,
   nil,   nil,   nil,   447,   447,   458,   458,   458,   nil,   458,
   nil,   nil,   nil,   458,   458,   nil,   nil,   nil,   458,   nil,
   458,   458,   458,   458,   458,   458,   458,   nil,   nil,   nil,
   nil,   458,   458,   458,   458,   458,   458,   458,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   458,   nil,   nil,
   458,   458,   458,   458,   458,   458,   458,   458,   458,   458,
   nil,   458,   458,   458,   458,   458,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   458,   nil,   nil,   458,   nil,
   nil,   458,   458,   nil,   nil,   458,   nil,   458,   nil,   nil,
   nil,   458,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   458,
   nil,   nil,   nil,   nil,   458,   458,   458,   458,   458,   458,
   nil,   nil,   nil,   458,   458,   465,   465,   465,   nil,   465,
   nil,   nil,   nil,   465,   465,   nil,   nil,   nil,   465,   nil,
   465,   465,   465,   465,   465,   465,   465,   nil,   nil,   nil,
   nil,   465,   465,   465,   465,   465,   465,   465,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   465,   nil,   nil,
   465,   465,   465,   465,   465,   465,   465,   465,   465,   465,
   nil,   465,   465,   465,   465,   465,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   465,   nil,   nil,   465,   nil,
   nil,   465,   465,   nil,   nil,   465,   nil,   nil,   nil,   nil,
   nil,   465,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   465,
   nil,   nil,   nil,   nil,   465,   465,   465,   465,   465,   465,
   nil,   nil,   nil,   465,   465,   466,   466,   466,   nil,   466,
   nil,   nil,   nil,   466,   466,   nil,   nil,   nil,   466,   nil,
   466,   466,   466,   466,   466,   466,   466,   nil,   nil,   nil,
   nil,   466,   466,   466,   466,   466,   466,   466,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   466,   nil,   nil,
   466,   466,   466,   466,   466,   466,   466,   466,   466,   466,
   nil,   466,   466,   466,   466,   466,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   466,   nil,   nil,   466,   nil,
   nil,   466,   466,   nil,   nil,   466,   nil,   nil,   nil,   nil,
   nil,   466,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   466,
   nil,   nil,   nil,   nil,   466,   466,   466,   466,   466,   466,
   nil,   nil,   nil,   466,   466,   467,   467,   467,   nil,   467,
   nil,   nil,   nil,   467,   467,   nil,   nil,   nil,   467,   nil,
   467,   467,   467,   467,   467,   467,   467,   nil,   nil,   nil,
   nil,   467,   467,   467,   467,   467,   467,   467,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   467,   nil,   nil,
   467,   467,   467,   467,   467,   467,   467,   467,   467,   467,
   nil,   467,   467,   467,   467,   467,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   467,   nil,   nil,   467,   nil,
   nil,   467,   467,   nil,   nil,   467,   nil,   nil,   nil,   nil,
   nil,   467,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   467,
   nil,   nil,   nil,   nil,   467,   467,   467,   467,   467,   467,
   nil,   nil,   nil,   467,   467,   471,   471,   471,   nil,   471,
   nil,   nil,   nil,   471,   471,   nil,   nil,   nil,   471,   nil,
   471,   471,   471,   471,   471,   471,   471,   nil,   nil,   nil,
   nil,   471,   471,   471,   471,   471,   471,   471,   nil,   nil,
   471,   nil,   nil,   nil,   nil,   nil,   nil,   471,   nil,   nil,
   471,   471,   471,   471,   471,   471,   471,   471,   471,   471,
   nil,   471,   471,   471,   471,   471,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   471,   nil,   nil,   471,   nil,
   nil,   471,   471,   nil,   nil,   471,   nil,   nil,   nil,   nil,
   nil,   471,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   471,
   nil,   nil,   nil,   nil,   471,   471,   471,   471,   471,   471,
   nil,   nil,   nil,   471,   471,   473,   473,   473,   nil,   473,
   nil,   nil,   nil,   473,   473,   nil,   nil,   nil,   473,   nil,
   473,   473,   473,   473,   473,   473,   473,   nil,   nil,   nil,
   nil,   473,   473,   473,   473,   473,   473,   473,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   473,   nil,   nil,
   473,   473,   473,   473,   473,   473,   473,   473,   473,   473,
   nil,   473,   473,   473,   473,   473,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   473,   nil,   nil,   473,   nil,
   nil,   473,   473,   nil,   nil,   473,   nil,   473,   nil,   nil,
   nil,   473,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   473,
   nil,   nil,   nil,   nil,   473,   473,   473,   473,   473,   473,
   nil,   nil,   nil,   473,   473,   478,   478,   478,   nil,   478,
   nil,   nil,   nil,   478,   478,   nil,   nil,   nil,   478,   nil,
   478,   478,   478,   478,   478,   478,   478,   nil,   nil,   nil,
   nil,   478,   478,   478,   478,   478,   478,   478,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   478,   nil,   nil,
   478,   478,   478,   478,   478,   478,   478,   478,   478,   478,
   nil,   478,   478,   478,   478,   478,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   478,   nil,   nil,   478,   nil,
   nil,   478,   478,   nil,   nil,   478,   nil,   478,   nil,   nil,
   nil,   478,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   478,
   nil,   nil,   nil,   nil,   478,   478,   478,   478,   478,   478,
   nil,   nil,   nil,   478,   478,   481,   481,   481,   nil,   481,
   nil,   nil,   nil,   481,   481,   nil,   nil,   nil,   481,   nil,
   481,   481,   481,   481,   481,   481,   481,   nil,   nil,   nil,
   nil,   481,   481,   481,   481,   481,   481,   481,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   481,   nil,   nil,
   481,   481,   481,   481,   481,   481,   481,   481,   481,   481,
   nil,   481,   481,   481,   481,   481,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   481,   nil,   nil,   481,   nil,
   nil,   481,   481,   nil,   nil,   481,   nil,   nil,   nil,   nil,
   nil,   481,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   481,
   nil,   nil,   nil,   nil,   481,   481,   481,   481,   481,   481,
   nil,   nil,   nil,   481,   481,   484,   484,   484,   nil,   484,
   nil,   nil,   nil,   484,   484,   nil,   nil,   nil,   484,   nil,
   484,   484,   484,   484,   484,   484,   484,   nil,   nil,   nil,
   nil,   484,   484,   484,   484,   484,   484,   484,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   484,   nil,   nil,
   484,   484,   484,   484,   484,   484,   484,   484,   484,   484,
   nil,   484,   484,   484,   484,   484,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   484,   nil,   nil,   484,   nil,
   nil,   484,   484,   nil,   nil,   484,   nil,   nil,   nil,   nil,
   nil,   484,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   484,
   nil,   nil,   nil,   nil,   484,   484,   484,   484,   484,   484,
   nil,   nil,   nil,   484,   484,   498,   498,   498,   nil,   498,
   nil,   nil,   nil,   498,   498,   nil,   nil,   nil,   498,   nil,
   498,   498,   498,   498,   498,   498,   498,   nil,   nil,   nil,
   nil,   498,   498,   498,   498,   498,   498,   498,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   498,   nil,   nil,
   498,   498,   498,   498,   498,   498,   498,   498,   498,   498,
   nil,   498,   498,   498,   498,   498,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   498,   nil,   nil,   498,   nil,
   nil,   498,   498,   nil,   nil,   498,   nil,   498,   nil,   nil,
   nil,   498,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   498,
   nil,   nil,   nil,   nil,   498,   498,   498,   498,   498,   498,
   nil,   nil,   nil,   498,   498,   499,   499,   499,   nil,   499,
   nil,   nil,   nil,   499,   499,   nil,   nil,   nil,   499,   nil,
   499,   499,   499,   499,   499,   499,   499,   nil,   nil,   nil,
   nil,   499,   499,   499,   499,   499,   499,   499,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   499,   nil,   nil,
   499,   499,   499,   499,   499,   499,   499,   499,   499,   499,
   nil,   499,   499,   499,   499,   499,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   499,   nil,   nil,   499,   nil,
   nil,   499,   499,   nil,   nil,   499,   nil,   499,   nil,   nil,
   nil,   499,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   499,
   nil,   nil,   nil,   nil,   499,   499,   499,   499,   499,   499,
   nil,   nil,   nil,   499,   499,   508,   508,   508,   nil,   508,
   nil,   nil,   nil,   508,   508,   nil,   nil,   nil,   508,   nil,
   508,   508,   508,   508,   508,   508,   508,   nil,   nil,   nil,
   nil,   508,   508,   508,   508,   508,   508,   508,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   508,   nil,   nil,
   508,   508,   508,   508,   508,   508,   508,   508,   508,   508,
   nil,   508,   508,   508,   508,   508,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   508,   nil,   nil,   508,   nil,
   nil,   508,   508,   nil,   nil,   508,   nil,   508,   nil,   nil,
   nil,   508,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   508,
   nil,   nil,   nil,   nil,   508,   508,   508,   508,   508,   508,
   nil,   nil,   nil,   508,   508,   512,   512,   512,   nil,   512,
   nil,   nil,   nil,   512,   512,   nil,   nil,   nil,   512,   nil,
   512,   512,   512,   512,   512,   512,   512,   nil,   nil,   nil,
   nil,   512,   512,   512,   512,   512,   512,   512,   nil,   nil,
   512,   nil,   nil,   nil,   nil,   nil,   nil,   512,   nil,   nil,
   512,   512,   512,   512,   512,   512,   512,   512,   512,   512,
   nil,   512,   512,   512,   512,   512,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   512,   nil,   nil,   512,   nil,
   nil,   512,   512,   nil,   nil,   512,   nil,   nil,   nil,   nil,
   nil,   512,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   512,
   nil,   nil,   nil,   nil,   512,   512,   512,   512,   512,   512,
   nil,   nil,   nil,   512,   512,   536,   536,   536,   536,   536,
   536,   536,   536,   536,   536,   536,   536,   536,   536,   536,
   536,   536,   536,   536,   536,   536,   536,   536,   536,   nil,
   nil,   536,   536,   536,   536,   536,   536,   536,   536,   536,
   536,   nil,   nil,   nil,   nil,   nil,   536,   536,   536,   536,
   536,   536,   536,   536,   nil,   nil,   536,   nil,   nil,   nil,
   nil,   nil,   nil,   536,   536,   nil,   536,   536,   536,   536,
   nil,   536,   536,   nil,   nil,   536,   nil,   nil,   nil,   nil,
   536,   536,   536,   536,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   536,   536,   nil,
   536,   536,   536,   536,   536,   536,   536,   536,   536,   nil,
   536,   nil,   nil,   536,   536,   nil,   nil,   539,   539,   539,
   nil,   539,   nil,   nil,   536,   539,   539,   nil,   nil,   nil,
   539,   nil,   539,   539,   539,   539,   539,   539,   539,   nil,
   nil,   nil,   nil,   539,   539,   539,   539,   539,   539,   539,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   539,
   nil,   nil,   539,   539,   539,   539,   539,   539,   539,   539,
   539,   539,   nil,   539,   539,   539,   539,   539,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   539,   nil,   nil,
   539,   nil,   nil,   539,   539,   nil,   nil,   539,   nil,   nil,
   nil,   nil,   nil,   539,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   539,   nil,   nil,   nil,   nil,   539,   539,   539,   539,
   539,   539,   nil,   nil,   nil,   539,   539,   540,   540,   540,
   nil,   540,   nil,   nil,   nil,   540,   540,   nil,   nil,   nil,
   540,   nil,   540,   540,   540,   540,   540,   540,   540,   nil,
   nil,   nil,   nil,   540,   540,   540,   540,   540,   540,   540,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   540,
   nil,   nil,   540,   540,   540,   540,   540,   540,   540,   540,
   540,   540,   nil,   540,   540,   540,   540,   540,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   540,   nil,   nil,
   540,   nil,   nil,   540,   540,   nil,   nil,   540,   nil,   540,
   nil,   nil,   nil,   540,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   540,   nil,   nil,   nil,   nil,   540,   540,   540,   540,
   540,   540,   nil,   nil,   nil,   540,   540,   543,   543,   543,
   nil,   543,   nil,   nil,   nil,   543,   543,   nil,   nil,   nil,
   543,   nil,   543,   543,   543,   543,   543,   543,   543,   nil,
   nil,   nil,   nil,   543,   543,   543,   543,   543,   543,   543,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   543,
   nil,   nil,   543,   543,   543,   543,   543,   543,   543,   543,
   543,   543,   nil,   543,   543,   543,   543,   543,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   543,   nil,   nil,
   543,   nil,   nil,   543,   543,   nil,   nil,   543,   nil,   nil,
   nil,   nil,   nil,   543,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   543,   nil,   nil,   nil,   nil,   543,   543,   543,   543,
   543,   543,   nil,   nil,   nil,   543,   543,   544,   544,   544,
   nil,   544,   nil,   nil,   nil,   544,   544,   nil,   nil,   nil,
   544,   nil,   544,   544,   544,   544,   544,   544,   544,   nil,
   nil,   nil,   nil,   544,   544,   544,   544,   544,   544,   544,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   544,
   nil,   nil,   544,   544,   544,   544,   544,   544,   544,   544,
   544,   544,   nil,   544,   544,   544,   544,   544,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   544,   nil,   nil,
   544,   nil,   nil,   544,   544,   nil,   nil,   544,   nil,   nil,
   nil,   nil,   nil,   544,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   544,   nil,   nil,   nil,   nil,   544,   544,   544,   544,
   544,   544,   nil,   nil,   nil,   544,   544,   548,   548,   548,
   nil,   548,   nil,   nil,   nil,   548,   548,   nil,   nil,   nil,
   548,   nil,   548,   548,   548,   548,   548,   548,   548,   nil,
   nil,   nil,   nil,   548,   548,   548,   548,   548,   548,   548,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   548,
   nil,   nil,   548,   548,   548,   548,   548,   548,   548,   548,
   548,   548,   nil,   548,   548,   548,   548,   548,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   548,   nil,   nil,
   548,   nil,   nil,   548,   548,   nil,   nil,   548,   nil,   nil,
   nil,   nil,   nil,   548,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   548,   nil,   nil,   nil,   nil,   548,   548,   548,   548,
   548,   548,   nil,   nil,   nil,   548,   548,   551,   551,   551,
   nil,   551,   nil,   nil,   nil,   551,   551,   nil,   nil,   nil,
   551,   nil,   551,   551,   551,   551,   551,   551,   551,   nil,
   nil,   nil,   nil,   551,   551,   551,   551,   551,   551,   551,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   551,
   nil,   nil,   551,   551,   551,   551,   551,   551,   551,   551,
   551,   551,   nil,   551,   551,   551,   551,   551,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   551,   nil,   nil,
   551,   nil,   nil,   551,   551,   nil,   nil,   551,   nil,   nil,
   nil,   nil,   nil,   551,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   551,   nil,   nil,   nil,   nil,   551,   551,   551,   551,
   551,   551,   nil,   nil,   nil,   551,   551,   558,   558,   558,
   nil,   558,   nil,   nil,   nil,   558,   558,   nil,   nil,   nil,
   558,   nil,   558,   558,   558,   558,   558,   558,   558,   nil,
   nil,   nil,   nil,   558,   558,   558,   558,   558,   558,   558,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   558,
   nil,   nil,   558,   558,   558,   558,   558,   558,   558,   558,
   558,   558,   nil,   558,   558,   558,   558,   558,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   558,   nil,   nil,
   558,   nil,   nil,   558,   558,   nil,   nil,   558,   nil,   nil,
   nil,   nil,   nil,   558,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   558,   nil,   nil,   nil,   nil,   558,   558,   558,   558,
   558,   558,   nil,   nil,   nil,   558,   558,   559,   559,   559,
   nil,   559,   nil,   nil,   nil,   559,   559,   nil,   nil,   nil,
   559,   nil,   559,   559,   559,   559,   559,   559,   559,   nil,
   nil,   nil,   nil,   559,   559,   559,   559,   559,   559,   559,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   559,
   nil,   nil,   559,   559,   559,   559,   559,   559,   559,   559,
   559,   559,   nil,   559,   559,   nil,   nil,   559,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   559,   nil,   nil,
   559,   nil,   nil,   559,   559,   nil,   nil,   559,   nil,   559,
   nil,   559,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   559,   nil,   nil,   nil,   nil,   nil,   559,   559,   559,   559,
   559,   559,   nil,   nil,   nil,   559,   559,   562,   562,   562,
   nil,   562,   nil,   nil,   nil,   562,   562,   nil,   nil,   nil,
   562,   nil,   562,   562,   562,   562,   562,   562,   562,   nil,
   nil,   nil,   nil,   562,   562,   562,   562,   562,   562,   562,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   562,
   nil,   nil,   562,   562,   562,   562,   562,   562,   562,   562,
   562,   562,   nil,   562,   562,   562,   562,   562,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   562,   nil,   nil,
   562,   nil,   nil,   562,   562,   nil,   nil,   562,   nil,   nil,
   nil,   nil,   nil,   562,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   562,   nil,   nil,   nil,   nil,   562,   562,   562,   562,
   562,   562,   nil,   nil,   nil,   562,   562,   566,   566,   566,
   nil,   566,   nil,   nil,   nil,   566,   566,   nil,   nil,   nil,
   566,   nil,   566,   566,   566,   566,   566,   566,   566,   nil,
   nil,   nil,   nil,   566,   566,   566,   566,   566,   566,   566,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   566,
   nil,   nil,   566,   566,   566,   566,   566,   566,   566,   566,
   566,   566,   nil,   566,   566,   566,   566,   566,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   566,   nil,   nil,
   566,   nil,   nil,   566,   566,   nil,   nil,   566,   nil,   nil,
   nil,   nil,   nil,   566,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   566,   nil,   nil,   nil,   nil,   566,   566,   566,   566,
   566,   566,   nil,   nil,   nil,   566,   566,   582,   582,   582,
   nil,   582,   nil,   nil,   nil,   582,   582,   nil,   nil,   nil,
   582,   nil,   582,   582,   582,   582,   582,   582,   582,   nil,
   nil,   nil,   nil,   582,   582,   582,   582,   582,   582,   582,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   582,
   nil,   nil,   582,   582,   582,   582,   582,   582,   582,   582,
   582,   582,   nil,   582,   582,   582,   582,   582,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   582,   nil,   nil,
   582,   nil,   nil,   582,   582,   nil,   nil,   582,   nil,   582,
   nil,   582,   nil,   582,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   582,   nil,   nil,   nil,   nil,   582,   582,   582,   582,
   582,   582,   nil,   nil,   nil,   582,   582,   586,   586,   586,
   nil,   586,   nil,   nil,   nil,   586,   586,   nil,   nil,   nil,
   586,   nil,   586,   586,   586,   586,   586,   586,   586,   nil,
   nil,   nil,   nil,   586,   586,   586,   586,   586,   586,   586,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   586,
   nil,   nil,   586,   586,   586,   586,   586,   586,   586,   586,
   586,   586,   nil,   586,   586,   586,   586,   586,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   586,   nil,   nil,
   586,   nil,   nil,   586,   586,   nil,   nil,   586,   nil,   nil,
   nil,   nil,   nil,   586,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   586,   nil,   nil,   nil,   nil,   586,   586,   586,   586,
   586,   586,   nil,   nil,   nil,   586,   586,   614,   614,   614,
   nil,   614,   nil,   nil,   nil,   614,   614,   nil,   nil,   nil,
   614,   nil,   614,   614,   614,   614,   614,   614,   614,   nil,
   nil,   nil,   nil,   614,   614,   614,   614,   614,   614,   614,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   614,
   nil,   nil,   614,   614,   614,   614,   614,   614,   614,   614,
   614,   614,   nil,   614,   614,   614,   614,   614,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   614,   nil,   nil,
   614,   nil,   nil,   614,   614,   nil,   nil,   614,   nil,   nil,
   nil,   nil,   nil,   614,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   614,   nil,   nil,   nil,   nil,   614,   614,   614,   614,
   614,   614,   nil,   nil,   nil,   614,   614,   630,   630,   630,
   nil,   630,   nil,   nil,   nil,   630,   630,   nil,   nil,   nil,
   630,   nil,   630,   630,   630,   630,   630,   630,   630,   nil,
   nil,   nil,   nil,   630,   630,   630,   630,   630,   630,   630,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   630,
   nil,   nil,   630,   630,   630,   630,   630,   630,   630,   630,
   630,   630,   nil,   630,   630,   630,   630,   630,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   630,   nil,   nil,
   630,   nil,   nil,   630,   630,   nil,   nil,   630,   nil,   nil,
   nil,   nil,   nil,   630,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   630,   nil,   nil,   nil,   nil,   630,   630,   630,   630,
   630,   630,   nil,   nil,   nil,   630,   630,   636,   636,   636,
   nil,   636,   nil,   nil,   nil,   636,   636,   nil,   nil,   nil,
   636,   nil,   636,   636,   636,   636,   636,   636,   636,   nil,
   nil,   nil,   nil,   636,   636,   636,   636,   636,   636,   636,
   nil,   nil,   636,   nil,   nil,   nil,   nil,   nil,   nil,   636,
   nil,   nil,   636,   636,   636,   636,   636,   636,   636,   636,
   636,   636,   nil,   636,   636,   636,   636,   636,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   636,   nil,   nil,
   636,   nil,   nil,   636,   636,   nil,   nil,   636,   nil,   nil,
   nil,   nil,   nil,   636,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   636,   nil,   nil,   nil,   nil,   636,   636,   636,   636,
   636,   636,   nil,   nil,   nil,   636,   636,   681,   681,   681,
   nil,   681,   nil,   nil,   nil,   681,   681,   nil,   nil,   nil,
   681,   nil,   681,   681,   681,   681,   681,   681,   681,   nil,
   nil,   nil,   nil,   681,   681,   681,   681,   681,   681,   681,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   681,
   nil,   nil,   681,   681,   681,   681,   681,   681,   681,   681,
   681,   681,   nil,   681,   681,   681,   681,   681,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   681,   nil,   nil,
   681,   nil,   nil,   681,   681,   nil,   nil,   681,   nil,   nil,
   nil,   nil,   nil,   681,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   681,   nil,   nil,   nil,   nil,   681,   681,   681,   681,
   681,   681,   nil,   nil,   nil,   681,   681,   682,   682,   682,
   nil,   682,   nil,   nil,   nil,   682,   682,   nil,   nil,   nil,
   682,   nil,   682,   682,   682,   682,   682,   682,   682,   nil,
   nil,   nil,   nil,   682,   682,   682,   682,   682,   682,   682,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   682,
   nil,   nil,   682,   682,   682,   682,   682,   682,   682,   682,
   682,   682,   nil,   682,   682,   682,   682,   682,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   682,   nil,   nil,
   682,   nil,   nil,   682,   682,   nil,   nil,   682,   nil,   nil,
   nil,   nil,   nil,   682,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   682,   nil,   nil,   nil,   nil,   682,   682,   682,   682,
   682,   682,   nil,   nil,   nil,   682,   682,   692,   692,   692,
   nil,   692,   nil,   nil,   nil,   692,   692,   nil,   nil,   nil,
   692,   nil,   692,   692,   692,   692,   692,   692,   692,   nil,
   nil,   nil,   nil,   692,   692,   692,   692,   692,   692,   692,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   692,
   nil,   nil,   692,   692,   692,   692,   692,   692,   692,   692,
   692,   692,   nil,   692,   692,   692,   692,   692,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   692,   nil,   nil,
   692,   nil,   nil,   692,   692,   nil,   nil,   692,   nil,   nil,
   nil,   nil,   nil,   692,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   692,   nil,   nil,   nil,   nil,   692,   692,   692,   692,
   692,   692,   nil,   nil,   nil,   692,   692,   693,   693,   693,
   nil,   693,   nil,   nil,   nil,   693,   693,   nil,   nil,   nil,
   693,   nil,   693,   693,   693,   693,   693,   693,   693,   nil,
   nil,   nil,   nil,   693,   693,   693,   693,   693,   693,   693,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   693,
   nil,   nil,   693,   693,   693,   693,   693,   693,   693,   693,
   693,   693,   nil,   693,   693,   693,   693,   693,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   693,   nil,   nil,
   693,   nil,   nil,   693,   693,   nil,   nil,   693,   nil,   nil,
   nil,   nil,   nil,   693,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   693,   nil,   nil,   nil,   nil,   693,   693,   693,   693,
   693,   693,   nil,   nil,   nil,   693,   693,   694,   694,   694,
   nil,   694,   nil,   nil,   nil,   694,   694,   nil,   nil,   nil,
   694,   nil,   694,   694,   694,   694,   694,   694,   694,   nil,
   nil,   nil,   nil,   694,   694,   694,   694,   694,   694,   694,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   694,
   nil,   nil,   694,   694,   694,   694,   694,   694,   694,   694,
   694,   694,   nil,   694,   694,   694,   694,   694,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   694,   nil,   nil,
   694,   nil,   nil,   694,   694,   nil,   nil,   694,   nil,   nil,
   nil,   nil,   nil,   694,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   694,   nil,   nil,   nil,   nil,   694,   694,   694,   694,
   694,   694,   nil,   nil,   nil,   694,   694,   700,   700,   700,
   nil,   700,   nil,   nil,   nil,   700,   700,   nil,   nil,   nil,
   700,   nil,   700,   700,   700,   700,   700,   700,   700,   nil,
   nil,   nil,   nil,   700,   700,   700,   700,   700,   700,   700,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   700,
   nil,   nil,   700,   700,   700,   700,   700,   700,   700,   700,
   700,   700,   nil,   700,   700,   nil,   nil,   700,   nil,   602,
   602,   602,   602,   602,   602,   602,   602,   602,   602,   602,
   nil,   602,   602,   nil,   nil,   602,   602,   700,   nil,   nil,
   700,   nil,   nil,   700,   700,   nil,   nil,   700,   nil,   nil,
   nil,   602,   nil,   602,   nil,   602,   602,   602,   602,   602,
   602,   602,   nil,   602,   nil,   nil,   700,   700,   700,   700,
   700,   700,   nil,   nil,   nil,   700,   700,   706,   706,   706,
   602,   706,   nil,   nil,   nil,   706,   706,   nil,   nil,   nil,
   706,   nil,   706,   706,   706,   706,   706,   706,   706,   nil,
   nil,   nil,   nil,   706,   706,   706,   706,   706,   706,   706,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   706,
   nil,   nil,   706,   706,   706,   706,   706,   706,   706,   706,
   706,   706,   nil,   706,   706,   706,   706,   706,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   706,   nil,   nil,
   706,   nil,   nil,   706,   706,   nil,   nil,   706,   nil,   706,
   nil,   706,   nil,   706,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   706,   nil,   nil,   nil,   nil,   706,   706,   706,   706,
   706,   706,   nil,   nil,   nil,   706,   706,   715,   715,   715,
   nil,   715,   nil,   nil,   nil,   715,   715,   nil,   nil,   nil,
   715,   nil,   715,   715,   715,   715,   715,   715,   715,   nil,
   nil,   nil,   nil,   715,   715,   715,   715,   715,   715,   715,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   715,
   nil,   nil,   715,   715,   715,   715,   715,   715,   715,   715,
   715,   715,   nil,   715,   715,   715,   715,   715,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   715,   nil,   nil,
   715,   nil,   nil,   715,   715,   nil,   nil,   715,   nil,   715,
   nil,   715,   nil,   715,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   715,   nil,   nil,   nil,   nil,   715,   715,   715,   715,
   715,   715,   nil,   nil,   nil,   715,   715,   717,   717,   717,
   nil,   717,   nil,   nil,   nil,   717,   717,   nil,   nil,   nil,
   717,   nil,   717,   717,   717,   717,   717,   717,   717,   nil,
   nil,   nil,   nil,   717,   717,   717,   717,   717,   717,   717,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   717,
   nil,   nil,   717,   717,   717,   717,   717,   717,   717,   717,
   717,   717,   nil,   717,   717,   717,   717,   717,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   717,   nil,   nil,
   717,   nil,   nil,   717,   717,   nil,   nil,   717,   nil,   717,
   nil,   717,   nil,   717,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   717,   nil,   nil,   nil,   nil,   717,   717,   717,   717,
   717,   717,   nil,   nil,   nil,   717,   717,   730,   730,   730,
   nil,   730,   nil,   nil,   nil,   730,   730,   nil,   nil,   nil,
   730,   nil,   730,   730,   730,   730,   730,   730,   730,   nil,
   nil,   nil,   nil,   730,   730,   730,   730,   730,   730,   730,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   730,
   nil,   nil,   730,   730,   730,   730,   730,   730,   730,   730,
   730,   730,   nil,   730,   730,   nil,   nil,   730,   nil,   679,
   679,   679,   679,   679,   679,   679,   679,   679,   679,   679,
   nil,   679,   679,   nil,   nil,   679,   679,   730,   nil,   nil,
   730,   nil,   nil,   730,   730,   nil,   nil,   730,   nil,   nil,
   nil,   679,   nil,   679,   nil,   679,   679,   679,   679,   679,
   679,   679,   nil,   679,   nil,   nil,   730,   730,   730,   730,
   730,   730,   nil,   nil,   nil,   730,   730,   736,   736,   736,
   679,   736,   nil,   nil,   nil,   736,   736,   nil,   nil,   nil,
   736,   nil,   736,   736,   736,   736,   736,   736,   736,   nil,
   nil,   nil,   nil,   736,   736,   736,   736,   736,   736,   736,
   nil,   nil,   736,   nil,   nil,   nil,   nil,   nil,   nil,   736,
   nil,   nil,   736,   736,   736,   736,   736,   736,   736,   736,
   736,   736,   nil,   736,   736,   736,   736,   736,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   736,   nil,   nil,
   736,   nil,   nil,   736,   736,   nil,   nil,   736,   nil,   nil,
   nil,   nil,   nil,   736,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   736,   nil,   nil,   nil,   nil,   736,   736,   736,   736,
   736,   736,   nil,   nil,   nil,   736,   736,   742,   742,   742,
   nil,   742,   nil,   nil,   nil,   742,   742,   nil,   nil,   nil,
   742,   nil,   742,   742,   742,   742,   742,   742,   742,   nil,
   nil,   nil,   nil,   742,   742,   742,   742,   742,   742,   742,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   742,
   nil,   nil,   742,   742,   742,   742,   742,   742,   742,   742,
   742,   742,   nil,   742,   742,   742,   742,   742,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   742,   nil,   nil,
   742,   nil,   nil,   742,   742,   nil,   nil,   742,   nil,   742,
   nil,   nil,   nil,   742,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   742,   nil,   nil,   nil,   nil,   742,   742,   742,   742,
   742,   742,   nil,   nil,   nil,   742,   742,   761,   761,   761,
   nil,   761,   nil,   nil,   nil,   761,   761,   nil,   nil,   nil,
   761,   nil,   761,   761,   761,   761,   761,   761,   761,   nil,
   nil,   nil,   nil,   761,   761,   761,   761,   761,   761,   761,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   761,
   nil,   nil,   761,   761,   761,   761,   761,   761,   761,   761,
   761,   761,   nil,   761,   761,   761,   761,   761,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   761,   nil,   nil,
   761,   nil,   nil,   761,   761,   nil,   nil,   761,   nil,   nil,
   nil,   nil,   nil,   761,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   761,   nil,   nil,   nil,   nil,   761,   761,   761,   761,
   761,   761,   nil,   nil,   nil,   761,   761,   770,   770,   770,
   nil,   770,   nil,   nil,   nil,   770,   770,   nil,   nil,   nil,
   770,   nil,   770,   770,   770,   770,   770,   770,   770,   nil,
   nil,   nil,   nil,   770,   770,   770,   770,   770,   770,   770,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   770,
   nil,   nil,   770,   770,   770,   770,   770,   770,   770,   770,
   770,   770,   nil,   770,   770,   770,   770,   770,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   770,   nil,   nil,
   770,   nil,   nil,   770,   770,   nil,   nil,   770,   nil,   nil,
   nil,   nil,   nil,   770,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   770,   nil,   nil,   nil,   nil,   770,   770,   770,   770,
   770,   770,   nil,   nil,   nil,   770,   770,   771,   771,   771,
   nil,   771,   nil,   nil,   nil,   771,   771,   nil,   nil,   nil,
   771,   nil,   771,   771,   771,   771,   771,   771,   771,   nil,
   nil,   nil,   nil,   771,   771,   771,   771,   771,   771,   771,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   771,
   nil,   nil,   771,   771,   771,   771,   771,   771,   771,   771,
   771,   771,   nil,   771,   771,   nil,   nil,   771,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   771,   nil,   nil,
   771,   nil,   nil,   771,   771,   nil,   nil,   771,   nil,   771,
   nil,   771,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   771,   771,   771,   771,
   771,   771,   nil,   nil,   nil,   771,   771,   782,   782,   782,
   nil,   782,   nil,   nil,   nil,   782,   782,   nil,   nil,   nil,
   782,   nil,   782,   782,   782,   782,   782,   782,   782,   nil,
   nil,   nil,   nil,   782,   782,   782,   782,   782,   782,   782,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   782,
   nil,   nil,   782,   782,   782,   782,   782,   782,   782,   782,
   782,   782,   nil,   782,   782,   782,   782,   782,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   782,   nil,   nil,
   782,   nil,   nil,   782,   782,   nil,   nil,   782,   nil,   nil,
   nil,   nil,   nil,   782,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   782,   nil,   nil,   nil,   nil,   782,   782,   782,   782,
   782,   782,   nil,   nil,   nil,   782,   782,   788,   788,   788,
   nil,   788,   nil,   nil,   nil,   788,   788,   nil,   nil,   nil,
   788,   nil,   788,   788,   788,   788,   788,   788,   788,   nil,
   nil,   nil,   nil,   788,   788,   788,   788,   788,   788,   788,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   788,
   nil,   nil,   788,   788,   788,   788,   788,   788,   788,   788,
   788,   788,   nil,   788,   788,   788,   788,   788,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   788,   nil,   nil,
   788,   nil,   nil,   788,   788,   nil,   nil,   788,   nil,   nil,
   nil,   nil,   nil,   788,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   788,   nil,   nil,   nil,   nil,   788,   788,   788,   788,
   788,   788,   nil,   nil,   nil,   788,   788,   790,   790,   790,
   nil,   790,   nil,   nil,   nil,   790,   790,   nil,   nil,   nil,
   790,   nil,   790,   790,   790,   790,   790,   790,   790,   nil,
   nil,   nil,   nil,   790,   790,   790,   790,   790,   790,   790,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   790,
   nil,   nil,   790,   790,   790,   790,   790,   790,   790,   790,
   790,   790,   nil,   790,   790,   790,   790,   790,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   790,   nil,   nil,
   790,   nil,   nil,   790,   790,   nil,   nil,   790,   nil,   nil,
   nil,   nil,   nil,   790,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   790,   nil,   nil,   nil,   nil,   790,   790,   790,   790,
   790,   790,   nil,   nil,   nil,   790,   790,   804,   804,   804,
   nil,   804,   nil,   nil,   nil,   804,   804,   nil,   nil,   nil,
   804,   nil,   804,   804,   804,   804,   804,   804,   804,   nil,
   nil,   nil,   nil,   804,   804,   804,   804,   804,   804,   804,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   804,
   nil,   nil,   804,   804,   804,   804,   804,   804,   804,   804,
   804,   804,   nil,   804,   804,   804,   804,   804,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   804,   nil,   nil,
   804,   nil,   nil,   804,   804,   nil,   nil,   804,   nil,   nil,
   nil,   nil,   nil,   804,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   804,   nil,   nil,   nil,   nil,   804,   804,   804,   804,
   804,   804,   nil,   nil,   nil,   804,   804,   822,   822,   822,
   nil,   822,   nil,   nil,   nil,   822,   822,   nil,   nil,   nil,
   822,   nil,   822,   822,   822,   822,   822,   822,   822,   nil,
   nil,   nil,   nil,   822,   822,   822,   822,   822,   822,   822,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   822,
   nil,   nil,   822,   822,   822,   822,   822,   822,   822,   822,
   822,   822,   nil,   822,   822,   nil,   nil,   822,   nil,   684,
   684,   684,   684,   684,   684,   684,   684,   684,   684,   684,
   nil,   684,   684,   nil,   nil,   684,   684,   822,   nil,   nil,
   822,   nil,   nil,   822,   822,   nil,   nil,   822,   nil,   nil,
   nil,   684,   nil,   684,   nil,   684,   684,   684,   684,   684,
   684,   684,   nil,   684,   nil,   nil,   822,   822,   822,   822,
   822,   822,   nil,   nil,   nil,   822,   822,   824,   824,   824,
   684,   824,   nil,   nil,   nil,   824,   824,   nil,   nil,   nil,
   824,   nil,   824,   824,   824,   824,   824,   824,   824,   nil,
   nil,   nil,   nil,   824,   824,   824,   824,   824,   824,   824,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   824,
   nil,   nil,   824,   824,   824,   824,   824,   824,   824,   824,
   824,   824,   nil,   824,   824,   824,   824,   824,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   824,   nil,   nil,
   824,   nil,   nil,   824,   824,   nil,   nil,   824,   nil,   824,
   nil,   nil,   nil,   824,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   824,   nil,   nil,   nil,   nil,   824,   824,   824,   824,
   824,   824,   nil,   nil,   nil,   824,   824,   829,   829,   829,
   nil,   829,   nil,   nil,   nil,   829,   829,   nil,   nil,   nil,
   829,   nil,   829,   829,   829,   829,   829,   829,   829,   nil,
   nil,   nil,   nil,   829,   829,   829,   829,   829,   829,   829,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   829,
   nil,   nil,   829,   829,   829,   829,   829,   829,   829,   829,
   829,   829,   nil,   829,   829,   nil,   nil,   829,   nil,   686,
   686,   686,   686,   686,   686,   686,   686,   686,   686,   686,
   nil,   686,   686,   nil,   nil,   686,   686,   829,   nil,   nil,
   829,   nil,   nil,   829,   829,   nil,   nil,   829,   nil,   nil,
   nil,   686,   nil,   686,   nil,   686,   686,   686,   686,   686,
   686,   686,   nil,   686,   nil,   nil,   829,   829,   829,   829,
   829,   829,   nil,   nil,   nil,   829,   829,   834,   834,   834,
   686,   834,   nil,   nil,   nil,   834,   834,   nil,   nil,   nil,
   834,   nil,   834,   834,   834,   834,   834,   834,   834,   nil,
   nil,   nil,   nil,   834,   834,   834,   834,   834,   834,   834,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   834,
   nil,   nil,   834,   834,   834,   834,   834,   834,   834,   834,
   834,   834,   nil,   834,   834,   834,   834,   834,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   834,   nil,   nil,
   834,   nil,   nil,   834,   834,   nil,   nil,   834,   nil,   834,
   nil,   834,   nil,   834,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   834,   nil,   nil,   nil,   nil,   834,   834,   834,   834,
   834,   834,   nil,   nil,   nil,   834,   834,   837,   837,   837,
   nil,   837,   nil,   nil,   nil,   837,   837,   nil,   nil,   nil,
   837,   nil,   837,   837,   837,   837,   837,   837,   837,   nil,
   nil,   nil,   nil,   837,   837,   837,   837,   837,   837,   837,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   837,
   nil,   nil,   837,   837,   837,   837,   837,   837,   837,   837,
   837,   837,   nil,   837,   837,   837,   837,   837,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   837,   nil,   nil,
   837,   nil,   nil,   837,   837,   nil,   nil,   837,   nil,   837,
   nil,   837,   nil,   837,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   837,   nil,   nil,   nil,   nil,   837,   837,   837,   837,
   837,   837,   nil,   nil,   nil,   837,   837,   863,   863,   863,
   nil,   863,   nil,   nil,   nil,   863,   863,   nil,   nil,   nil,
   863,   nil,   863,   863,   863,   863,   863,   863,   863,   nil,
   nil,   nil,   nil,   863,   863,   863,   863,   863,   863,   863,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   863,
   nil,   nil,   863,   863,   863,   863,   863,   863,   863,   863,
   863,   863,   nil,   863,   863,   nil,   nil,   863,   nil,   689,
   689,   689,   689,   689,   689,   689,   689,   689,   689,   689,
   nil,   689,   689,   nil,   nil,   689,   689,   863,   nil,   nil,
   863,   nil,   nil,   863,   863,   nil,   nil,   863,   nil,   nil,
   nil,   689,   nil,   689,   nil,   689,   689,   689,   689,   689,
   689,   689,   nil,   689,   nil,   nil,   863,   863,   863,   863,
   863,   863,   nil,   nil,   nil,   863,   863,   866,   866,   866,
   689,   866,   nil,   nil,   nil,   866,   866,   nil,   nil,   nil,
   866,   nil,   866,   866,   866,   866,   866,   866,   866,   nil,
   nil,   nil,   nil,   866,   866,   866,   866,   866,   866,   866,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   866,
   nil,   nil,   866,   866,   866,   866,   866,   866,   866,   866,
   866,   866,   nil,   866,   866,   866,   866,   866,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   866,   nil,   nil,
   866,   nil,   nil,   866,   866,   nil,   nil,   866,   nil,   nil,
   nil,   nil,   nil,   866,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   866,   nil,   nil,   nil,   nil,   866,   866,   866,   866,
   866,   866,   nil,   nil,   nil,   866,   866,   869,   869,   869,
   nil,   869,   nil,   nil,   nil,   869,   869,   nil,   nil,   nil,
   869,   nil,   869,   869,   869,   869,   869,   869,   869,   nil,
   nil,   nil,   nil,   869,   869,   869,   869,   869,   869,   869,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   869,
   nil,   nil,   869,   869,   869,   869,   869,   869,   869,   869,
   869,   869,   nil,   869,   869,   869,   869,   869,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   869,   nil,   nil,
   869,   nil,   nil,   869,   869,   nil,   nil,   869,   nil,   nil,
   nil,   nil,   nil,   869,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   869,   nil,   nil,   nil,   nil,   869,   869,   869,   869,
   869,   869,   nil,   nil,   nil,   869,   869,   877,   877,   877,
   nil,   877,   nil,   nil,   nil,   877,   877,   nil,   nil,   nil,
   877,   nil,   877,   877,   877,   877,   877,   877,   877,   nil,
   nil,   nil,   nil,   877,   877,   877,   877,   877,   877,   877,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   877,
   nil,   nil,   877,   877,   877,   877,   877,   877,   877,   877,
   877,   877,   nil,   877,   877,   nil,   nil,   877,   nil,   691,
   691,   691,   691,   691,   691,   691,   691,   691,   691,   691,
   nil,   691,   691,   nil,   nil,   691,   691,   877,   nil,   nil,
   877,   nil,   nil,   877,   877,   nil,   nil,   877,   nil,   nil,
   nil,   691,   nil,   691,   nil,   691,   691,   691,   691,   691,
   691,   691,   nil,   691,   nil,   nil,   877,   877,   877,   877,
   877,   877,   nil,   nil,   nil,   877,   877,   882,   882,   882,
   691,   882,   nil,   nil,   nil,   882,   882,   nil,   nil,   nil,
   882,   nil,   882,   882,   882,   882,   882,   882,   882,   nil,
   nil,   nil,   nil,   882,   882,   882,   882,   882,   882,   882,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   882,
   nil,   nil,   882,   882,   882,   882,   882,   882,   882,   882,
   882,   882,   nil,   882,   882,   882,   882,   882,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   882,   nil,   nil,
   882,   nil,   nil,   882,   882,   nil,   nil,   882,   nil,   882,
   nil,   882,   nil,   882,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   882,   nil,   nil,   nil,   nil,   882,   882,   882,   882,
   882,   882,   nil,   nil,   nil,   882,   882,   888,   888,   888,
   nil,   888,   nil,   nil,   nil,   888,   888,   nil,   nil,   nil,
   888,   nil,   888,   888,   888,   888,   888,   888,   888,   nil,
   nil,   nil,   nil,   888,   888,   888,   888,   888,   888,   888,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   888,
   nil,   nil,   888,   888,   888,   888,   888,   888,   888,   888,
   888,   888,   nil,   888,   888,   nil,   nil,   888,   nil,   696,
   696,   696,   696,   696,   696,   696,   696,   696,   696,   696,
   nil,   696,   696,   nil,   nil,   696,   696,   888,   nil,   nil,
   888,   nil,   nil,   888,   888,   nil,   nil,   888,   nil,   nil,
   nil,   696,   nil,   696,   nil,   696,   696,   696,   696,   696,
   696,   696,   nil,   696,   nil,   nil,   888,   888,   888,   888,
   888,   888,   nil,   nil,   nil,   888,   888,   891,   891,   891,
   696,   891,   nil,   nil,   nil,   891,   891,   nil,   nil,   nil,
   891,   nil,   891,   891,   891,   891,   891,   891,   891,   nil,
   nil,   nil,   nil,   891,   891,   891,   891,   891,   891,   891,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   891,
   nil,   nil,   891,   891,   891,   891,   891,   891,   891,   891,
   891,   891,   nil,   891,   891,   891,   891,   891,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   891,   nil,   nil,
   891,   nil,   nil,   891,   891,   nil,   nil,   891,   nil,   nil,
   nil,   nil,   nil,   891,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   891,   nil,   nil,   nil,   nil,   891,   891,   891,   891,
   891,   891,   nil,   nil,   nil,   891,   891,    64,    64,    64,
    64,    64,    64,    64,    64,    64,    64,    64,    64,    64,
    64,    64,    64,    64,    64,    64,    64,    64,    64,    64,
    64,   nil,   nil,    64,    64,    64,    64,    64,    64,    64,
    64,    64,    64,   nil,   nil,   nil,   nil,   nil,    64,    64,
    64,    64,    64,    64,    64,    64,    64,    64,    64,    64,
   nil,   nil,   nil,   nil,   nil,    64,    64,   nil,    64,    64,
    64,    64,   nil,    64,    64,   nil,   nil,    64,   nil,   nil,
   nil,   nil,    64,    64,    64,    64,   nil,   nil,   nil,   nil,
   nil,    64,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    64,
    64,   nil,    64,    64,    64,    64,    64,    64,    64,    64,
    64,   nil,    64,   nil,   nil,    64,   666,   666,   666,   666,
   666,   666,   666,   666,   666,   666,   666,   666,   666,   666,
   666,   666,   666,   666,   666,   666,   666,   666,   666,   666,
   nil,   nil,   666,   666,   666,   666,   666,   666,   666,   666,
   666,   666,   nil,   nil,   nil,   nil,   nil,   666,   666,   666,
   666,   666,   666,   666,   666,   nil,   nil,   666,   nil,   nil,
   nil,   nil,   nil,   nil,   666,   666,   nil,   666,   666,   666,
   666,   nil,   666,   666,   nil,   nil,   666,   nil,   nil,   nil,
   nil,   666,   666,   666,   666,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   666,   666,
   nil,   666,   666,   666,   666,   666,   666,   666,   666,   666,
   nil,   666,   nil,   nil,   666,   583,   583,   583,   583,   583,
   583,   583,   583,   583,   583,   583,   nil,   583,   583,   nil,
   nil,   583,   583,   nil,   nil,   nil,   583,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   583,   nil,   583,
   nil,   583,   583,   583,   583,   583,   583,   583,   nil,   583,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   583,   nil,   583,   425,
   425,   425,   425,   425,   425,   425,   425,   425,   425,   425,
   nil,   425,   425,   nil,   nil,   425,   425,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   425,   nil,   425,   nil,   425,   425,   425,   425,   425,
   425,   425,   nil,   425,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   194,   194,   nil,   nil,   194,   nil,   nil,   nil,   nil,
   425,   425,   194,   194,   nil,   194,   194,   194,   194,   nil,
   194,   194,   nil,   nil,   194,   nil,   nil,   nil,   nil,   194,
   194,   194,   194,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   194,   194,   nil,   194,
   194,   194,   194,   194,   194,   194,   194,   194,   nil,   194,
   195,   195,   194,   nil,   195,   nil,   nil,   nil,   nil,   nil,
   nil,   195,   195,   nil,   195,   195,   195,   195,   nil,   195,
   195,   nil,   nil,   195,   nil,   nil,   nil,   nil,   195,   195,
   195,   195,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   195,   195,   nil,   195,   195,
   195,   195,   195,   195,   195,   195,   195,   nil,   195,   253,
   253,   195,   nil,   253,   nil,   nil,   nil,   nil,   nil,   nil,
   253,   253,   nil,   253,   253,   253,   253,   nil,   253,   253,
   nil,   nil,   253,   nil,   nil,   nil,   nil,   253,   253,   253,
   253,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   253,   253,   nil,   253,   253,   253,
   253,   253,   253,   253,   253,   253,   nil,   253,   254,   254,
   253,   nil,   254,   nil,   nil,   nil,   nil,   nil,   nil,   254,
   254,   nil,   254,   254,   254,   254,   nil,   254,   254,   nil,
   nil,   254,   nil,   nil,   nil,   nil,   254,   254,   254,   254,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   254,   254,   nil,   254,   254,   254,   254,
   254,   254,   254,   254,   254,   nil,   254,   393,   393,   254,
   nil,   393,   nil,   nil,   nil,   nil,   nil,   nil,   393,   393,
   nil,   393,   393,   393,   393,   nil,   393,   393,   nil,   nil,
   393,   nil,   nil,   nil,   nil,   393,   393,   393,   393,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   393,   393,   nil,   393,   393,   393,   393,   393,
   393,   393,   393,   393,   nil,   393,   394,   394,   393,   nil,
   394,   nil,   nil,   nil,   nil,   nil,   nil,   394,   394,   nil,
   394,   394,   394,   394,   nil,   394,   394,   nil,   nil,   394,
   nil,   nil,   nil,   nil,   394,   394,   394,   394,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   394,   394,   nil,   394,   394,   394,   394,   394,   394,
   394,   394,   394,   nil,   394,   459,   459,   394,   nil,   459,
   nil,   nil,   nil,   nil,   nil,   nil,   459,   459,   nil,   459,
   459,   459,   459,   nil,   459,   459,   nil,   nil,   459,   nil,
   nil,   nil,   nil,   459,   459,   459,   459,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   459,   459,   nil,   459,   459,   459,   459,   459,   459,   459,
   459,   459,   nil,   459,   460,   460,   459,   nil,   460,   nil,
   nil,   nil,   nil,   nil,   nil,   460,   460,   nil,   460,   460,
   460,   460,   nil,   460,   460,   nil,   nil,   460,   nil,   nil,
   nil,   nil,   460,   460,   460,   460,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   460,
   460,   nil,   460,   460,   460,   460,   460,   460,   460,   460,
   460,   nil,   460,   468,   468,   460,   nil,   468,   nil,   nil,
   nil,   nil,   nil,   nil,   468,   468,   nil,   468,   468,   468,
   468,   nil,   468,   468,   nil,   nil,   468,   nil,   nil,   nil,
   nil,   468,   468,   468,   468,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   468,   468,
   nil,   468,   468,   468,   468,   468,   468,   468,   468,   468,
   nil,   468,   469,   469,   468,   nil,   469,   nil,   nil,   nil,
   nil,   nil,   nil,   469,   469,   nil,   469,   469,   469,   469,
   nil,   469,   469,   nil,   nil,   469,   nil,   nil,   nil,   nil,
   469,   469,   469,   469,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   469,   469,   nil,
   469,   469,   469,   469,   469,   469,   469,   469,   469,   nil,
   469,   nil,   nil,   469,   482,   482,   482,   482,   482,   482,
   482,   482,   482,   482,   482,   nil,   482,   482,   nil,   nil,
   482,   482,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   482,   nil,   482,   nil,
   482,   482,   482,   482,   482,   482,   482,   nil,   482,   nil,
   nil,   nil,   nil,   nil,   nil,   500,   500,   nil,   nil,   500,
   nil,   nil,   nil,   nil,   482,   482,   500,   500,   nil,   500,
   500,   500,   500,   nil,   500,   500,   nil,   nil,   500,   nil,
   nil,   nil,   nil,   500,   500,   500,   500,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   500,   500,   nil,   500,   500,   500,   500,   500,   500,   500,
   500,   500,   nil,   500,   501,   501,   500,   nil,   501,   nil,
   nil,   nil,   nil,   nil,   nil,   501,   501,   nil,   501,   501,
   501,   501,   nil,   501,   501,   nil,   nil,   501,   nil,   nil,
   nil,   nil,   501,   501,   501,   501,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   501,
   501,   nil,   501,   501,   501,   501,   501,   501,   501,   501,
   501,   nil,   501,   507,   507,   501,   nil,   507,   nil,   nil,
   nil,   nil,   nil,   nil,   507,   507,   nil,   507,   507,   507,
   507,   nil,   507,   507,   nil,   nil,   507,   nil,   nil,   nil,
   nil,   507,   507,   507,   507,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   507,   507,
   nil,   507,   507,   507,   507,   507,   507,   507,   507,   507,
   nil,   507,   509,   509,   507,   nil,   509,   nil,   nil,   nil,
   nil,   nil,   nil,   509,   509,   nil,   509,   509,   509,   509,
   nil,   509,   509,   nil,   nil,   509,   nil,   nil,   nil,   nil,
   509,   509,   509,   509,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   509,   509,   nil,
   509,   509,   509,   509,   509,   509,   509,   509,   509,   nil,
   509,   580,   580,   509,   nil,   580,   nil,   nil,   nil,   nil,
   nil,   nil,   580,   580,   nil,   580,   580,   580,   580,   nil,
   580,   580,   nil,   nil,   580,   nil,   nil,   nil,   nil,   580,
   580,   580,   580,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   580,   580,   nil,   580,
   580,   580,   580,   580,   580,   580,   580,   580,   nil,   580,
   581,   581,   580,   nil,   581,   nil,   nil,   nil,   nil,   nil,
   nil,   581,   581,   nil,   581,   581,   581,   581,   nil,   581,
   581,   nil,   nil,   581,   nil,   nil,   nil,   nil,   581,   581,
   581,   581,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   581,   581,   nil,   581,   581,
   581,   581,   581,   581,   581,   581,   581,   nil,   581,   nil,
   nil,   581,   733,   733,   733,   733,   733,   733,   733,   733,
   733,   733,   733,   nil,   733,   733,   nil,   nil,   733,   733,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   733,   nil,   733,   nil,   733,   733,
   733,   733,   733,   733,   733,   nil,   733,   nil,   769,   769,
   769,   769,   769,   769,   769,   769,   769,   769,   769,   nil,
   769,   769,   733,   733,   769,   769,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   769,   nil,   769,   nil,   769,   769,   769,   769,   769,   769,
   769,   nil,   769,   nil,   nil,   nil,   825,   825,   nil,   nil,
   825,   nil,   nil,   nil,   nil,   nil,   nil,   825,   825,   769,
   825,   825,   825,   825,   nil,   825,   825,   nil,   nil,   825,
   nil,   nil,   nil,   nil,   825,   825,   825,   825,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   825,   825,   nil,   825,   825,   825,   825,   825,   825,
   825,   825,   825,   nil,   825,   826,   826,   825,   nil,   826,
   nil,   nil,   nil,   nil,   nil,   nil,   826,   826,   nil,   826,
   826,   826,   826,   nil,   826,   826,   nil,   nil,   826,   nil,
   nil,   nil,   nil,   826,   826,   826,   826,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   826,   826,   nil,   826,   826,   826,   826,   826,   826,   826,
   826,   826,   nil,   826,   nil,   nil,   826 ]

racc_action_pointer = [
  1221,    29,   nil,   133,   854,  4729,  4849,  4969,   -52,   -46,
   -41,   -31,    27,   271,   117,   206,   nil,  5081,  5201,  6042,
   100,   nil,  5321,  5441,  5561,   268,   141,  5681,  5801,   nil,
  1342,  5921,  6041,   nil,    91,   221,   220,   200,  6161,  6281,
  6401,   166,   357,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   212,  1463,  6521,  6641,  6761,    57,  6881,  7001,   nil,   nil,
   727,  7121,  7241,  7361, 22585,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    82,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,  7481,   nil,   nil,   nil,  7601,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   298,   nil,   854,
   nil,   nil,   nil,  7721,  7841,  7961,  8081,  8201,   971,   nil,
   340,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   182,   nil,  1584,  1705,
  8321,  8441,  8561,  8681, 22872, 22931,  8801,  8921,  9041,   nil,
   385,   -52,   295,   -51,   241,   298,  1826,   nil,   nil,  9161,
  9281,  9401,  9521,  9641,  9761,  9881, 10001, 10121, 10241, 10361,
 10481, 10601, 10721, 10841, 10961, 11081, 11201, 11321, 11441, 11561,
 11681, 11801, 11921, 12041, 12161, 12281,   nil,   nil,   nil,  7122,
   nil,   264,   289, 12401,   nil, 12521,   344,   nil,   nil,   nil,
   nil,   nil,   nil, 22990, 23049,   361, 12641, 12761,   nil,   nil,
   nil,   nil,   nil,   nil,   nil, 12881,   405,  1947,   416,   478,
   442, 13001,  2068,   476,   481,   521,   506,   496,   461,   158,
   nil,   506,   213,   nil,   nil,   237,   531,   532,   596,   nil,
   534,   nil, 13121,   nil,   597,   596,   429,   nil,   483,   241,
   316, 13241,   523,   388,   506,   441,   nil,   477,    -7,     8,
 13361, 13481,   172,    31,   491,    -9,   601,   567,    -1,   597,
   nil,   nil,   220,   265,   -23,   nil,   626,   nil,     9, 13601,
   nil,   nil,   288,   312,   351,   382,   407,   451,   452,   470,
   nil,   482,   nil, 13721,   nil,   205,   284,   317,   343,   -35,
   351,   nil,  1092,   nil,   nil,   nil,   nil,   nil,   nil, 13841,
   nil,   nil,   nil,   nil,   534,   536,   nil,   nil,   727,   nil,
   520, 13953,   nil,   521,   nil,   nil,  7242,   559,   246,   293,
 14073,   nil,   nil,     0,   566,   106,   nil, 14193, 14313,   nil,
  7362,   nil,   nil, 23108, 23167, 14433,   -37, 14553, 14673, 14793,
   974,  1221,   377,   549,   590,   601,   608,   609,  2794,  2915,
  3036,  1342,  1463,  1584,  1705,  1826,  1947,  2068,  2189,  2310,
  2431,   459,   579,  2552,  2673, 22806,   -50,   nil, 14913,   nil,
 15033,   547,   nil, 15153,   318,   nil,   nil,   371,   nil,   nil,
   589,   560,   -63,   558,   658,   nil,   nil, 15273,   -47,    92,
   600,   nil,   601,   568,   nil,   nil,   nil,   610, 15393, 23226,
 23285,   651,   612,   nil,   nil, 15513, 15633, 15753, 23344, 23403,
 13002, 15873,   688, 15993,   nil,   581,   nil,   nil, 16113,   nil,
   nil, 16233, 23451,   nil, 16353,   nil,   nil,   nil,  2189,   700,
   nil,   nil,  2310,    67,   108,   698,   706,  2431, 16473, 16593,
 23516, 23575,     4,   nil,   nil,   727,   nil, 23634, 16713, 23693,
   nil,   nil, 16833,   327,   -34,  2552,   537,   nil,   nil,   nil,
   -32,   nil,   nil,   nil,   711,   nil,   nil,   nil,   595,   nil,
   147,   nil,   nil,   597,   nil,   nil, 16953,   nil,   nil, 17065,
 17185,   nil,   294, 17305, 17425,   633,   nil,   nil, 17545,   641,
   nil, 17665,    70,    85,   487,   607,   644,  1095, 17785, 17905,
   nil,  2673, 18025,   613,   nil,   656, 18145,   nil,   659,   nil,
   672,   nil,   nil,   nil,   nil,   nil,   113,   nil,   683,   685,
 23752, 23811, 18265, 22742,   -67,   647, 18385,   nil,   697,   nil,
  2794,  2915,   nil,   -30,   nil,   714,    62,   111,   724,   308,
   780,   727, 19466,   751,   755,    -2,   813,   nil,  3036,   697,
   751,   nil,   nil,   749, 18505,   nil,   nil,   510,   nil,   823,
   nil,   nil,   nil,   nil,   nil,   835,   nil,   836,   720,    15,
 18625,   758,    13,    24,    25,   137, 18745,   340,   837,   nil,
   761,  3157,   350,   nil,   nil,   853,  3278,   433,   372,   737,
   741,   742,   nil,   nil,   nil,   nil,   nil,   749,   nil,   nil,
   nil,   nil,   830,   nil,   nil,   831, 22694,   794,   nil,   nil,
   nil,   nil,   nil,  3399,   nil,   nil,   nil,   nil,   nil, 19946,
   763, 18865, 18985,   nil, 21146,   nil, 21386,   nil,   nil, 21746,
   nil, 22106, 19105, 19225, 19345,   214, 22346,   nil,   764,   974,
 19465,   nil,   815,   921,   805,   nil, 19585,   808,  3520,   nil,
   nil,   854,   858,   -62,   918, 19705,   nil, 19825,   823,   nil,
   863,   842,   952,   730,   nil,   nil,  3641,   nil,   nil,    31,
 19945,   nil,   nil, 23859,   953,   nil, 20065,   955,  3762,  3883,
   nil,   nil, 20185,  4004,   nil,    26,   132,   nil,   962,   nil,
  4125,   nil,   963,   850,   nil,   657,   nil,   -43,   nil,   nil,
   456, 20305,   nil,   nil,   nil,   nil,   870,   nil,   nil, 23905,
 20425, 20545,   843,   845,   928,   847,   857,   890,   nil,   nil,
   nil,   nil, 20665,   nil,   878,   901,   888,   nil, 20785,   889,
 20905,   nil,   nil,   nil,   nil,   nil,  4246,   nil,   nil,   nil,
    32,   nil,  1007,  1008, 21025,   148,   nil,   nil,  1010,   nil,
   933,   905,   908,   nil,   nil,   909,   919,   nil,   nil,   904,
   nil,   nil, 21145,  1095, 21265, 23967, 24026,   848,   955, 21385,
  5922,   nil,   nil,   nil, 21505,   926,   nil, 21625,   929,  1050,
  4367,   nil,   nil,   nil,   nil,   nil,   nil,  4488,   nil,   nil,
   173,   nil,   nil,   nil,  4609,   nil,   932,   964,   970,   343,
   363,   393,   873, 21745,   nil,   nil, 21865,   938,   nil, 21985,
   nil,   nil,   564,  1056,   941,  1059,   974, 22105,   851,   nil,
   946,   nil, 22225,   954,   nil,   nil,   nil,   nil, 22345,   nil,
   nil, 22465,   nil,   nil,   956,   nil ]

racc_action_default = [
    -4,  -499,    -1,  -487,    -5,  -499,  -499,  -499,  -499,  -499,
  -499,  -499,  -499,  -499,  -271,   -32,   -33,  -499,  -499,   -38,
   -40,   -41,  -282,  -315,  -316,   -45,  -249,  -363,  -285,   -58,
    -4,   -62,   -67,   -68,  -499,  -430,  -499,  -499,  -499,  -499,
  -499,  -489,  -214,  -264,  -265,  -266,  -267,  -268,  -269,  -270,
  -477,    -4,  -499,  -498,  -469,  -288,  -499,  -499,  -292,  -295,
  -487,  -499,  -499,  -499,  -499,  -317,  -318,  -320,  -321,  -383,
  -384,  -385,  -386,  -387,  -401,  -390,  -403,  -403,  -394,  -399,
  -413,  -403,  -415,  -416,  -419,  -420,  -421,  -422,  -423,  -424,
  -425,  -426,  -427,  -428,  -429,  -432,  -433,  -499,    -3,  -488,
  -494,  -495,  -496,  -499,  -499,  -499,  -499,  -499,    -6,    -8,
  -499,   -93,   -94,   -95,   -96,   -97,   -98,   -99,  -100,  -101,
  -105,  -106,  -107,  -108,  -109,  -110,  -111,  -112,  -113,  -114,
  -115,  -116,  -117,  -118,  -119,  -120,  -121,  -122,  -123,  -124,
  -125,  -126,  -127,  -128,  -129,  -130,  -131,  -132,  -133,  -134,
  -135,  -136,  -137,  -138,  -139,  -140,  -141,  -142,  -143,  -144,
  -145,  -146,  -147,  -148,  -149,  -150,  -151,  -152,  -153,  -154,
  -155,  -156,  -157,  -158,  -159,  -160,  -161,  -162,  -163,  -164,
  -165,  -166,  -167,  -168,  -169,  -170,   -13,  -102,    -4,    -4,
  -499,  -499,  -499,  -498,  -499,  -499,  -499,  -499,  -499,   -36,
  -499,  -430,  -499,  -271,  -499,  -499,    -4,   -37,  -206,  -499,
  -499,  -499,  -499,  -499,  -499,  -499,  -499,  -499,  -499,  -499,
  -499,  -499,  -499,  -499,  -499,  -499,  -499,  -499,  -499,  -499,
  -499,  -499,  -499,  -499,  -499,  -499,  -353,  -355,   -42,  -215,
  -228,  -258,  -258,  -499,  -236,  -499,  -259,  -282,  -315,  -316,
  -472,   -43,   -44,  -499,  -499,   -50,  -498,  -499,  -287,  -358,
  -364,  -366,   -56,  -362,   -57,  -499,   -58,    -4,  -499,  -499,
   -63,   -65,    -4,   -72,  -499,  -499,   -79,  -285,  -489,  -499,
  -319,  -363,  -499,   -66,   -70,  -278,  -417,  -418,  -499,  -191,
  -192,  -207,  -499,  -490,  -375,  -499,  -274,  -216,  -489,  -491,
  -491,  -499,  -499,  -491,  -499,  -491,  -289,   -39,  -499,  -499,
  -499,  -499,  -487,  -499,  -488,  -430,  -499,  -499,  -271,  -499,
  -333,  -334,   -88,   -89,  -499,   -91,  -499,  -271,  -499,  -499,
  -430,  -308,   -93,   -94,  -131,  -132,  -148,  -153,  -160,  -163,
  -310,  -499,  -467,  -499,  -388,  -499,  -499,  -499,  -499,  -499,
  -499,   896,    -7,  -497,   -14,   -15,   -16,   -17,   -18,  -499,
   -10,   -11,   -12,  -103,  -499,  -499,   -21,   -29,  -171,  -259,
  -499,  -499,   -22,   -30,   -31,   -23,  -173,  -499,  -478,  -479,
  -226,  -480,  -481,  -478,  -249,  -479,  -361,  -483,  -484,   -28,
  -180,   -34,   -35,  -499,  -499,  -498,  -278,  -499,  -499,  -499,
  -181,  -182,  -183,  -184,  -185,  -186,  -187,  -188,  -193,  -194,
  -195,  -196,  -197,  -198,  -199,  -200,  -201,  -202,  -203,  -204,
  -205,  -208,  -209,  -210,  -211,  -499,  -349,  -229,  -499,  -231,
  -499,  -258,  -256,  -499,  -249,  -478,  -479,  -249,   -48,   -51,
  -499,  -489,  -489,  -258,  -228,  -250,  -251,  -252,  -349,  -349,
  -499,  -284,  -499,   -59,  -276,   -71,   -64,  -499,  -498,  -499,
  -499,   -78,  -499,  -417,  -418,  -499,  -499,  -499,  -499,  -499,
  -212,  -499,  -498,  -498,  -273,  -489,  -217,  -218,  -493,  -492,
  -220,  -493,  -489,  -280,  -493,  -471,  -281,  -470,    -4,  -322,
  -323,  -324,    -4,  -499,  -499,  -499,  -499,    -4,  -499,  -498,
  -499,  -499,  -278,  -301,   -88,   -89,   -90,  -499,  -498,  -499,
  -304,  -434,  -499,  -499,  -499,    -4,  -447,  -312,  -485,  -486,
  -489,  -389,  -402,  -405,  -499,  -407,  -391,  -404,  -499,  -393,
  -499,  -396,  -398,  -499,  -414,    -9,  -499,   -19,   -20,  -499,
  -499,  -263,  -279,  -499,  -499,   -52,  -227,  -359,  -499,   -54,
  -360,  -499,  -478,  -479,  -482,  -277,  -499,  -171,  -499,  -499,
  -351,    -4,  -499,  -258,  -257,  -260,  -499,  -473,  -499,  -235,
  -499,  -474,   -46,  -356,   -47,  -357,  -349,  -222,  -499,  -499,
  -499,  -499,  -499,   -38,  -499,  -258,  -499,  -248,  -499,  -254,
    -4,    -4,  -283,   -59,   -69,  -499,  -478,  -479,  -226,   -75,
   -77,  -499,  -179,  -189,  -190,  -499,  -498,  -331,    -4,  -376,
  -498,  -377,  -378,  -499,  -499,  -260,  -221,  -498,  -325,  -498,
  -293,  -326,  -327,  -328,  -296,  -499,  -299,  -499,  -369,  -499,
  -499,  -499,  -478,  -479,  -482,  -277,  -499,   -88,   -89,   -92,
  -499,    -4,  -499,  -436,  -306,  -499,    -4,  -447,  -499,  -466,
  -466,  -466,  -446,  -448,  -449,  -450,  -451,  -452,  -453,  -456,
  -458,  -459,  -461,  -462,  -463,  -499,  -499,  -499,  -406,  -409,
  -410,  -411,  -412,    -4,  -392,  -395,  -397,  -400,  -104,  -172,
  -261,  -499,  -499,   -25,  -175,   -26,  -176,   -53,   -27,  -177,
   -55,  -178,  -499,  -499,  -499,  -279,  -213,  -335,  -337,  -347,
  -499,  -350,  -499,  -499,  -258,  -233,  -499,  -258,    -4,  -223,
  -224,  -226,  -226,  -489,  -499,  -499,  -241,  -499,  -258,  -253,
  -499,  -499,  -499,   -73,  -286,    -2,    -4,  -382,  -332,  -499,
  -499,  -380,  -275,  -489,  -499,  -329,  -499,  -499,    -4,    -4,
  -298,  -300,  -499,    -4,  -371,  -279,  -499,  -279,  -499,  -435,
    -4,  -309,  -499,  -489,  -438,  -499,  -442,  -499,  -444,  -445,
  -499,  -499,  -460,  -464,  -313,  -468,  -499,  -262,   -24,  -174,
  -499,  -338,   -80,  -499,  -499,   -87,  -346,  -499,  -348,  -352,
  -354,  -230,  -499,  -232,  -499,  -499,  -258,  -238,  -499,  -258,
  -499,  -247,  -255,  -365,  -367,  -381,    -4,  -379,  -219,  -290,
  -499,  -291,  -499,  -499,  -499,  -498,  -302,  -305,  -499,  -311,
  -499,  -466,  -466,  -454,  -465,  -466,  -499,  -457,  -455,  -447,
  -408,  -336,  -499,  -343,  -498,  -499,  -499,   -86,  -499,  -499,
  -258,   -49,  -225,  -237,  -499,  -258,  -243,  -499,  -258,  -375,
    -4,  -294,  -297,  -370,  -368,  -372,  -373,    -4,  -307,  -437,
  -499,  -440,  -441,  -443,    -4,  -339,  -342,  -499,  -499,   -82,
   -84,   -83,   -85,  -499,  -345,  -234,  -499,  -258,  -239,  -499,
  -242,  -374,  -498,  -499,  -466,  -499,  -499,  -499,   -81,  -344,
  -258,  -244,  -499,  -258,  -330,  -303,  -439,  -314,  -499,  -341,
  -240,  -499,  -245,  -340,  -258,  -246 ]

racc_goto_table = [
    10,   472,   242,   242,   242,    10,   270,   280,   280,   340,
   295,   259,   263,   302,   240,   240,   240,   244,   244,   244,
   102,   496,   114,   114,   109,   187,   606,   117,   117,    98,
    10,   442,     2,   646,   305,   119,   119,   280,   280,   280,
   255,   262,   264,   292,   573,   298,   652,   575,   734,   439,
   207,    10,   266,   199,   567,     1,   506,   367,   374,   620,
   624,   320,   269,   328,   331,   268,   283,   817,   522,   380,
   384,   531,   488,   492,   241,   241,   241,   102,   725,   561,
   114,   108,    99,   812,   186,   815,   313,   321,   296,   238,
   251,   252,   307,   307,   359,    13,   307,   576,   536,    10,
    13,   590,   591,   346,   347,   567,   303,    10,   350,   589,
   477,   480,   200,   200,   485,   445,   487,   200,   200,   200,
   588,   720,   342,   304,   306,    13,   274,   274,   434,   437,
   319,   510,   341,   517,   475,   310,   738,   311,   739,   307,
   307,   307,   307,   636,   847,   641,    13,   200,   200,   750,
   515,   200,   200,   377,   516,   200,   316,   326,   326,   666,
   817,   819,   698,   702,   426,   448,   449,   629,   308,   309,
   844,   610,   312,   737,   729,   352,   344,   652,   874,   345,
   348,   530,   349,   358,   668,   298,   673,   753,    10,    10,
   811,   813,   nil,   nil,    13,   440,   nil,   nil,   200,   200,
   200,   200,    13,   625,   nil,   nil,    10,   nil,   756,   758,
   759,   388,   nil,   nil,   nil,   354,   355,   356,   357,   nil,
   364,   365,   366,   372,   375,   nil,   nil,   nil,   389,   708,
   nil,   nil,   nil,   391,   392,   nil,   242,   242,   269,   639,
   nil,   nil,   370,   370,   nil,   242,   nil,   280,   444,   240,
   nil,   244,   244,   676,   nil,   nil,   nil,   240,   nil,   nil,
   244,   nil,   nil,   nil,   259,    14,   263,    10,   380,   384,
    14,   nil,    10,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   462,   nil,   nil,    13,    13,   200,   200,   200,   200,   266,
   nil,   200,   200,   200,   266,    14,   276,   276,   nil,   269,
   476,    13,   452,   884,   269,   456,   nil,   457,   443,   241,
   nil,   nil,   806,   nil,   nil,   nil,    14,   241,   nil,   nil,
   nil,   573,   575,   441,   446,   nil,   318,   327,   327,   102,
   567,   nil,   450,   nil,   nil,   601,   854,   nil,   495,   687,
   611,   567,   nil,   690,   601,   511,   307,   307,   nil,   652,
   nil,   200,   200,   nil,   nil,   556,   nil,   713,   nil,   845,
   200,   nil,    13,   nil,    14,   514,   274,    13,   nil,   550,
   851,   852,    14,   nil,   853,   114,   601,   535,   nil,   520,
   117,   429,   601,   nil,   nil,   nil,   nil,   298,   119,   nil,
   nil,   nil,   nil,   743,   545,   nil,   nil,   nil,   549,   nil,
   nil,   nil,   nil,   nil,   nil,   200,   200,   nil,   563,   nil,
   388,   607,   612,   nil,   nil,   nil,   nil,   nil,   595,   nil,
   nil,   nil,   493,   494,   200,   nil,   nil,   585,   nil,   nil,
   nil,   nil,   nil,   886,   nil,   nil,   nil,   nil,   200,   nil,
   nil,   nil,   587,   578,   579,   nil,   nil,   nil,   572,   nil,
   298,   574,   nil,    14,    14,   711,   712,   nil,   nil,   631,
   nil,   567,   nil,   nil,   nil,   nil,   nil,   nil,   640,   nil,
   nil,    14,   nil,   nil,   645,   nil,   388,   613,   nil,   nil,
   nil,   nil,   nil,   296,   616,   388,   nil,   nil,    10,   nil,
   200,   298,    10,   796,   nil,   nil,   nil,    10,   nil,   nil,
   298,   nil,   nil,   nil,   nil,   nil,   567,   605,   nil,   nil,
   621,   621,   nil,   846,   nil,    10,   nil,   388,   nil,   nil,
   617,   nil,   667,   388,   619,   370,   nil,   nil,   nil,   627,
   643,   644,    14,   nil,   697,   280,   276,    14,   nil,   nil,
   nil,   nil,   200,   nil,   nil,   727,   871,   nil,   307,   731,
   628,   nil,   114,   200,   678,   nil,   607,   117,   607,   nil,
   nil,    10,   563,   nil,   840,   119,   200,   nil,   nil,   nil,
   569,   nil,   nil,   nil,   714,   683,   685,   nil,   nil,   nil,
   688,   nil,   nil,    13,   nil,   nil,   550,    13,   nil,   nil,
    10,    10,    13,   703,   200,   nil,   nil,   nil,   nil,   nil,
   748,   nil,   nil,   200,   nil,   752,   nil,   200,    10,   nil,
    13,   764,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   721,   722,   642,   nil,   nil,   nil,   nil,   nil,
    35,   nil,   nil,   nil,   nil,    35,   nil,   nil,   200,   200,
   728,    10,   nil,   200,   nil,   nil,    10,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   274,   nil,    13,   nil,   nil,   749,
    35,   273,   273,   nil,   nil,   754,   nil,   nil,   nil,   nil,
   nil,   nil,   307,    10,   nil,   280,   280,   200,   nil,   nil,
   nil,    35,   114,   nil,   nil,    13,    13,   nil,   nil,   nil,
   nil,   315,   330,   330,   330,   789,   nil,   nil,   nil,   776,
   778,   601,   705,    13,   nil,   766,   280,   nil,    10,   808,
   787,   nil,   nil,   nil,   768,   785,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   716,   nil,    10,   nil,   nil,    35,
   797,   200,   nil,   nil,   nil,   798,    13,    35,    10,    10,
   784,    13,   nil,    10,   607,   nil,   821,   280,   746,   nil,
    10,   nil,   nil,    14,   nil,   810,   nil,    14,   795,   nil,
   nil,   nil,    14,   621,   nil,   nil,   nil,   786,    13,   nil,
   802,   803,   307,   nil,   nil,   805,   nil,   200,   nil,   nil,
    14,   nil,   nil,   nil,   858,   nil,   nil,   nil,   nil,   672,
   nil,   nil,   nil,   nil,   773,   773,    10,   nil,   280,   280,
   nil,   nil,   nil,    13,   nil,   280,   nil,   nil,   nil,   nil,
   nil,   607,   nil,   875,   867,   nil,   298,   nil,    35,    35,
   nil,    13,   855,   856,   276,   773,    14,   nil,   839,   864,
   nil,   200,   nil,    13,    13,   nil,    35,   nil,    13,   280,
    10,   nil,   388,   781,   nil,    13,   783,    10,   800,   nil,
   nil,   nil,   nil,   280,    10,    14,    14,   791,   nil,   nil,
   nil,   nil,   nil,   879,   280,   nil,   274,   nil,   nil,   nil,
   nil,   nil,   872,    14,   nil,   nil,   nil,   889,   nil,   873,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   893,   nil,
   nil,    13,   nil,   nil,   nil,   nil,   nil,    35,   nil,   nil,
   nil,   273,    35,   nil,   nil,   nil,    14,   nil,   nil,   nil,
   nil,    14,   nil,   nil,   nil,   nil,   nil,   773,   773,   200,
   nil,   nil,   nil,   nil,   773,   833,   nil,   nil,   836,   nil,
   nil,   nil,   nil,   nil,   nil,    13,   nil,   nil,    14,   nil,
   nil,   nil,    13,   nil,   nil,   nil,   nil,   nil,   nil,    13,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   773,   nil,
   nil,   nil,   nil,   nil,   775,   775,   nil,   nil,   nil,   865,
   nil,   nil,   773,    14,   868,   nil,   nil,   870,   nil,   nil,
   nil,   nil,   nil,   773,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    14,   nil,   nil,   nil,   775,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    14,    14,   nil,   881,   nil,    14,   nil,
   nil,   nil,   nil,   nil,   nil,    14,   nil,   nil,   nil,   890,
   nil,   nil,   892,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   895,   nil,   nil,   276,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   208,   nil,   nil,   nil,   239,   239,   239,   nil,   nil,   nil,
   nil,    14,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   289,   290,   291,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   239,   239,   775,   775,   nil,
   nil,   nil,   nil,   nil,   775,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    12,    14,   nil,   nil,   nil,    12,
   nil,   nil,    14,   nil,   nil,   nil,   nil,   nil,    35,    14,
   nil,   nil,    35,   nil,   nil,   nil,   nil,    35,   775,   nil,
   nil,   nil,   nil,   nil,    12,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   775,   nil,   nil,    35,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   775,   nil,    12,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   273,
   nil,    35,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    12,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    12,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    35,    35,   368,   239,   376,   239,   nil,   nil,   390,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,   nil,
   nil,   208,   400,   401,   402,   403,   404,   405,   406,   407,
   408,   409,   410,   411,   412,   413,   414,   415,   416,   417,
   418,   419,   420,   421,   422,   423,   424,   425,   nil,   nil,
   nil,    35,   nil,   nil,   nil,   239,    35,   239,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   239,   239,
   nil,   nil,    12,    12,   nil,   nil,   nil,   239,   nil,   nil,
   nil,   nil,   nil,    35,   nil,   nil,   nil,   nil,   nil,   nil,
    12,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   470,   nil,   nil,   nil,   nil,   772,
   772,   nil,   nil,   482,   nil,   nil,   nil,   nil,    35,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    35,   nil,   nil,   nil,
   772,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,    35,
   nil,    12,   nil,    35,   nil,   nil,    12,   nil,   nil,   nil,
    35,   nil,   nil,   369,   373,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   273,   nil,   239,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    35,   239,   nil,   390,
   557,   376,   nil,   nil,   nil,   nil,   431,   nil,   432,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   772,   772,   nil,   nil,   nil,   nil,   nil,   772,
   239,   nil,   239,   nil,   nil,   239,   nil,   nil,   nil,   nil,
    35,   nil,   nil,   nil,   nil,   nil,   nil,    35,   nil,   583,
   nil,   nil,   nil,   nil,    35,   nil,   nil,   nil,   nil,   nil,
   239,   nil,   nil,   772,   nil,   nil,   nil,   602,   603,   604,
   nil,   nil,   nil,   nil,   nil,   239,   nil,   772,   nil,   nil,
   239,   nil,   nil,   239,   nil,   nil,   239,   nil,   772,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   239,   239,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   239,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   541,   nil,   nil,   nil,   nil,   nil,
   nil,   679,   239,   nil,   nil,   684,   686,   nil,   nil,   nil,
   689,   nil,   nil,   691,   nil,   nil,   nil,   nil,   nil,   nil,
   696,   nil,    12,   nil,   239,   nil,    12,   nil,   239,   nil,
   nil,    12,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   239,   nil,   nil,   nil,   239,    12,
   nil,   565,   nil,   568,   nil,   nil,   571,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   584,   nil,   nil,   nil,   nil,   nil,   733,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   239,   nil,   nil,    12,   609,   nil,   nil,   nil,
   nil,   615,   nil,   nil,   568,   nil,   nil,   615,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   369,   nil,   nil,    12,    12,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    12,   239,   769,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   684,   686,   689,   nil,   nil,   nil,
   nil,   nil,   nil,   680,   nil,   nil,   nil,   nil,   239,   nil,
   nil,   nil,   nil,   nil,   nil,    12,   nil,   239,   nil,   239,
    12,   nil,   nil,   nil,   nil,   704,   nil,   nil,   nil,   707,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   239,   565,   nil,    12,   nil,   718,
   nil,    26,   nil,   nil,   nil,   nil,    26,   nil,   nil,   nil,
   nil,   nil,   nil,   239,   nil,   nil,   nil,   nil,    26,    26,
   nil,   nil,   769,    26,    26,    26,   nil,   nil,   nil,   nil,
   nil,    26,    12,   nil,   830,   nil,   nil,   nil,   nil,   nil,
   239,   nil,   239,   744,   nil,   nil,   nil,   nil,   nil,   nil,
    12,   nil,    26,    26,    26,   nil,   239,    26,    26,   nil,
   nil,    26,    12,    12,   nil,   nil,   nil,    12,   nil,   nil,
   nil,   nil,   nil,   nil,    12,   nil,   239,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   239,   nil,   nil,   239,
   nil,   nil,   nil,   nil,   767,   nil,   nil,   nil,   nil,   nil,
    26,   nil,   nil,   nil,    26,    26,    26,    26,    26,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   239,   568,
    12,   239,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   568,   nil,   nil,   nil,   239,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   239,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   615,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    12,   nil,   nil,   nil,   nil,   nil,
   nil,    12,   nil,   nil,   818,   nil,   nil,   nil,    12,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,
    26,    26,    26,    26,    26,   nil,   nil,    26,    26,    26,
   nil,   835,   nil,   838,   nil,   nil,   nil,    26,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   843,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   565,   nil,   nil,
   568,   nil,   nil,   nil,   nil,   nil,   nil,    26,    26,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    26,   nil,    26,   nil,
   nil,   nil,   nil,    26,   nil,   nil,   nil,   nil,   nil,   880,
   nil,   nil,   883,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   568,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   894,   nil,   nil,   nil,   nil,   nil,
   nil,    26,    26,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    26,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    26,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    26,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    26,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,
   nil,   nil,   nil,    26,   nil,   nil,   nil,   nil,    26,   nil,
    26,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,
   nil,   nil,   nil,    26,   nil,   nil,    26,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    26,    26,   nil,   nil,   nil,    26,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    26,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    26,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    26,    26,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,   nil,   nil,
   nil,   nil,    26,   nil,   nil,   nil,   nil,    26,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    26,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    26,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,   nil,    26,
    26,   nil,   nil,   nil,    26,   nil,   nil,   nil,   nil,   nil,
   nil,    26,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    26,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    26,   nil,   nil,   nil,   nil,   nil,   nil,    26,   nil,
   nil,   nil,   nil,   nil,   nil,    26 ]

racc_goto_check = [
    14,     4,    56,    56,    56,    14,    39,    52,    52,    44,
     3,    57,    57,    19,    26,    26,    26,    60,    60,    60,
    83,    79,    45,    45,    11,    11,     5,    48,    48,     8,
    14,    29,     2,    85,    56,    49,    49,    52,    52,    52,
    31,    31,    31,    51,    58,    26,   128,    58,    77,    32,
    15,    14,    36,    23,   133,     1,    42,    21,    21,    78,
    78,    14,     2,    43,    43,    37,    40,   130,   119,    30,
    30,   119,    76,    76,    54,    54,    54,    83,     6,    33,
    45,     9,    10,   126,    12,   126,     8,    16,    23,    28,
    28,    28,    23,    23,    24,    18,    23,    34,    50,    14,
    18,    33,    33,   116,   116,   133,    54,    14,   116,    61,
    55,    55,    18,    18,    55,    62,    55,    18,    18,    18,
    64,    65,    72,    73,    74,    18,    18,    18,    30,    30,
    80,    82,    86,    87,    88,    89,    90,    91,    92,    23,
    23,    23,    23,    93,    94,    95,    18,    18,    18,    96,
    97,    18,    18,    19,    98,    18,    18,    18,    18,    99,
   130,   100,   101,   102,   104,   106,   107,   108,    13,    13,
   109,   110,    13,     5,   111,     9,   114,   128,   126,   115,
   117,   118,   120,     9,   121,    26,   122,   123,    14,    14,
   125,   129,   nil,   nil,    18,    53,   nil,   nil,    18,    18,
    18,    18,    18,    79,   nil,   nil,    14,   nil,   127,   127,
   127,    45,   nil,   nil,   nil,    13,    13,    13,    13,   nil,
     2,     2,    15,    15,    15,   nil,   nil,   nil,    15,    33,
   nil,   nil,   nil,    23,    23,   nil,    56,    56,     2,    42,
   nil,   nil,    54,    54,   nil,    56,   nil,    52,    26,    26,
   nil,    60,    60,   119,   nil,   nil,   nil,    26,   nil,   nil,
    60,   nil,   nil,   nil,    57,    20,    57,    14,    30,    30,
    20,   nil,    14,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    51,   nil,   nil,    18,    18,    18,    18,    18,    18,    36,
   nil,    18,    18,    18,    36,    20,    20,    20,   nil,     2,
    51,    18,    37,    77,     2,    40,   nil,    37,    54,    54,
   nil,   nil,    78,   nil,   nil,   nil,    20,    54,   nil,   nil,
   nil,    58,    58,    28,    28,   nil,    20,    20,    20,    83,
   133,   nil,    28,   nil,   nil,    30,    85,   nil,     8,    32,
    21,   133,   nil,    32,    30,    83,    23,    23,   nil,   128,
   nil,    18,    18,   nil,   nil,    19,   nil,    29,   nil,     5,
    18,   nil,    18,   nil,    20,    23,    18,    18,   nil,    57,
   127,   127,    20,   nil,   127,    45,    30,    11,   nil,    23,
    48,    59,    30,   nil,   nil,   nil,   nil,    26,    49,   nil,
   nil,   nil,   nil,    76,    31,   nil,   nil,   nil,    31,   nil,
   nil,   nil,   nil,   nil,   nil,    18,    18,   nil,    56,   nil,
    45,    53,    53,   nil,   nil,   nil,   nil,   nil,    19,   nil,
   nil,   nil,    13,    13,    18,   nil,   nil,    56,   nil,   nil,
   nil,   nil,   nil,   127,   nil,   nil,   nil,   nil,    18,   nil,
   nil,   nil,    60,    51,    51,   nil,   nil,   nil,    31,   nil,
    26,    31,   nil,    20,    20,    30,    30,   nil,   nil,    19,
   nil,   133,   nil,   nil,   nil,   nil,   nil,   nil,    19,   nil,
   nil,    20,   nil,   nil,     3,   nil,    45,    51,   nil,   nil,
   nil,   nil,   nil,    23,    51,    45,   nil,   nil,    14,   nil,
    18,    26,    14,    76,   nil,   nil,   nil,    14,   nil,   nil,
    26,   nil,   nil,   nil,   nil,   nil,   133,    23,   nil,   nil,
    83,    83,   nil,    79,   nil,    14,   nil,    45,   nil,   nil,
     2,   nil,    51,    45,     2,    54,   nil,   nil,   nil,     2,
    83,    83,    20,   nil,    39,    52,    20,    20,   nil,   nil,
   nil,   nil,    18,   nil,   nil,    53,     4,   nil,    23,    53,
    54,   nil,    45,    18,    11,   nil,    53,    48,    53,   nil,
   nil,    14,    56,   nil,    76,    49,    18,   nil,   nil,   nil,
    59,   nil,   nil,   nil,    26,    15,    15,   nil,   nil,   nil,
    15,   nil,   nil,    18,   nil,   nil,    57,    18,   nil,   nil,
    14,    14,    18,     2,    18,   nil,   nil,   nil,   nil,   nil,
     3,   nil,   nil,    18,   nil,     3,   nil,    18,    14,   nil,
    18,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,     2,     2,    13,   nil,   nil,   nil,   nil,   nil,
    41,   nil,   nil,   nil,   nil,    41,   nil,   nil,    18,    18,
     2,    14,   nil,    18,   nil,   nil,    14,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    18,   nil,    18,   nil,   nil,    83,
    41,    41,    41,   nil,   nil,    83,   nil,   nil,   nil,   nil,
   nil,   nil,    23,    14,   nil,    52,    52,    18,   nil,   nil,
   nil,    41,    45,   nil,   nil,    18,    18,   nil,   nil,   nil,
   nil,    41,    41,    41,    41,    56,   nil,   nil,   nil,    14,
    14,    30,    59,    18,   nil,     2,    52,   nil,    14,     3,
    60,   nil,   nil,   nil,    15,    51,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    59,   nil,    14,   nil,   nil,    41,
    14,    18,   nil,   nil,   nil,    51,    18,    41,    14,    14,
     2,    18,   nil,    14,    53,   nil,    39,    52,    13,   nil,
    14,   nil,   nil,    20,   nil,    51,   nil,    20,     2,   nil,
   nil,   nil,    20,    83,   nil,   nil,   nil,    54,    18,   nil,
     2,     2,    23,   nil,   nil,     2,   nil,    18,   nil,   nil,
    20,   nil,   nil,   nil,    19,   nil,   nil,   nil,   nil,    20,
   nil,   nil,   nil,   nil,    18,    18,    14,   nil,    52,    52,
   nil,   nil,   nil,    18,   nil,    52,   nil,   nil,   nil,   nil,
   nil,    53,   nil,     3,    56,   nil,    26,   nil,    41,    41,
   nil,    18,    14,    14,    20,    18,    20,   nil,     2,    14,
   nil,    18,   nil,    18,    18,   nil,    41,   nil,    18,    52,
    14,   nil,    45,    59,   nil,    18,    59,    14,    13,   nil,
   nil,   nil,   nil,    52,    14,    20,    20,    59,   nil,   nil,
   nil,   nil,   nil,    14,    52,   nil,    18,   nil,   nil,   nil,
   nil,   nil,     2,    20,   nil,   nil,   nil,    14,   nil,     2,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    14,   nil,
   nil,    18,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
   nil,    41,    41,   nil,   nil,   nil,    20,   nil,   nil,   nil,
   nil,    20,   nil,   nil,   nil,   nil,   nil,    18,    18,    18,
   nil,   nil,   nil,   nil,    18,    59,   nil,   nil,    59,   nil,
   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,    20,   nil,
   nil,   nil,    18,   nil,   nil,   nil,   nil,   nil,   nil,    18,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,
   nil,   nil,   nil,   nil,    20,    20,   nil,   nil,   nil,    59,
   nil,   nil,    18,    20,    59,   nil,   nil,    59,   nil,   nil,
   nil,   nil,   nil,    18,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    20,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,    20,   nil,    59,   nil,    20,   nil,
   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,    59,
   nil,   nil,    59,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    59,   nil,   nil,    20,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    25,   nil,   nil,   nil,    25,    25,    25,   nil,   nil,   nil,
   nil,    20,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    25,    25,    25,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    25,    25,    20,    20,   nil,
   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    17,    20,   nil,   nil,   nil,    17,
   nil,   nil,    20,   nil,   nil,   nil,   nil,   nil,    41,    20,
   nil,   nil,    41,   nil,   nil,   nil,   nil,    41,    20,   nil,
   nil,   nil,   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    20,   nil,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,    17,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,
   nil,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    17,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    41,    41,    25,    25,    25,    25,   nil,   nil,    25,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,
   nil,    25,    25,    25,    25,    25,    25,    25,    25,    25,
    25,    25,    25,    25,    25,    25,    25,    25,    25,    25,
    25,    25,    25,    25,    25,    25,    25,    25,   nil,   nil,
   nil,    41,   nil,   nil,   nil,    25,    41,    25,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    25,    25,
   nil,   nil,    17,    17,   nil,   nil,   nil,    25,   nil,   nil,
   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    25,   nil,   nil,   nil,   nil,    41,
    41,   nil,   nil,    25,   nil,   nil,   nil,   nil,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,    41,
   nil,    17,   nil,    41,   nil,   nil,    17,   nil,   nil,   nil,
    41,   nil,   nil,    22,    22,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    41,   nil,    25,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    41,    25,   nil,    25,
    25,    25,   nil,   nil,   nil,   nil,    22,   nil,    22,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    41,    41,   nil,   nil,   nil,   nil,   nil,    41,
    25,   nil,    25,   nil,   nil,    25,   nil,   nil,   nil,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,    25,
   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,
    25,   nil,   nil,    41,   nil,   nil,   nil,    25,    25,    25,
   nil,   nil,   nil,   nil,   nil,    25,   nil,    41,   nil,   nil,
    25,   nil,   nil,    25,   nil,   nil,    25,   nil,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    25,    25,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    25,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    22,   nil,   nil,   nil,   nil,   nil,
   nil,    25,    25,   nil,   nil,    25,    25,   nil,   nil,   nil,
    25,   nil,   nil,    25,   nil,   nil,   nil,   nil,   nil,   nil,
    25,   nil,    17,   nil,    25,   nil,    17,   nil,    25,   nil,
   nil,    17,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    25,   nil,   nil,   nil,    25,    17,
   nil,    22,   nil,    22,   nil,   nil,    22,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    22,   nil,   nil,   nil,   nil,   nil,    25,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    25,   nil,   nil,    17,    22,   nil,   nil,   nil,
   nil,    22,   nil,   nil,    22,   nil,   nil,    22,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    22,   nil,   nil,    17,    17,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    17,    25,    25,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    25,    25,    25,   nil,   nil,   nil,
   nil,   nil,   nil,    22,   nil,   nil,   nil,   nil,    25,   nil,
   nil,   nil,   nil,   nil,   nil,    17,   nil,    25,   nil,    25,
    17,   nil,   nil,   nil,   nil,    22,   nil,   nil,   nil,    22,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    25,    22,   nil,    17,   nil,    22,
   nil,    35,   nil,   nil,   nil,   nil,    35,   nil,   nil,   nil,
   nil,   nil,   nil,    25,   nil,   nil,   nil,   nil,    35,    35,
   nil,   nil,    25,    35,    35,    35,   nil,   nil,   nil,   nil,
   nil,    35,    17,   nil,    25,   nil,   nil,   nil,   nil,   nil,
    25,   nil,    25,    22,   nil,   nil,   nil,   nil,   nil,   nil,
    17,   nil,    35,    35,    35,   nil,    25,    35,    35,   nil,
   nil,    35,    17,    17,   nil,   nil,   nil,    17,   nil,   nil,
   nil,   nil,   nil,   nil,    17,   nil,    25,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    25,   nil,   nil,    25,
   nil,   nil,   nil,   nil,    22,   nil,   nil,   nil,   nil,   nil,
    35,   nil,   nil,   nil,    35,    35,    35,    35,    35,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    25,    22,
    17,    25,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    22,   nil,   nil,   nil,    25,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    25,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    22,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,
   nil,    17,   nil,   nil,    22,   nil,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,
    35,    35,    35,    35,    35,   nil,   nil,    35,    35,    35,
   nil,    22,   nil,    22,   nil,   nil,   nil,    35,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    22,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    22,   nil,   nil,
    22,   nil,   nil,   nil,   nil,   nil,   nil,    35,    35,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    35,   nil,    35,   nil,
   nil,   nil,   nil,    35,   nil,   nil,   nil,   nil,   nil,    22,
   nil,   nil,    22,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    22,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    22,   nil,   nil,   nil,   nil,   nil,
   nil,    35,    35,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    35,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    35,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    35,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    35,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,
   nil,   nil,   nil,    35,   nil,   nil,   nil,   nil,    35,   nil,
    35,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,
   nil,   nil,   nil,    35,   nil,   nil,    35,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    35,    35,   nil,   nil,   nil,    35,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    35,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    35,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    35,    35,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,   nil,   nil,
   nil,   nil,    35,   nil,   nil,   nil,   nil,    35,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    35,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    35,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,   nil,    35,
    35,   nil,   nil,   nil,    35,   nil,   nil,   nil,   nil,   nil,
   nil,    35,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    35,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    35,   nil,   nil,   nil,   nil,   nil,   nil,    35,   nil,
   nil,   nil,   nil,   nil,   nil,    35 ]

racc_goto_pointer = [
   nil,    55,    32,   -41,  -293,  -446,  -528,   nil,    26,    76,
    79,    18,    77,   112,     0,    32,    26,  1104,    95,   -40,
   265,  -133,  1193,    36,   -15,  1032,    -8,   nil,    67,  -225,
  -125,    14,  -206,  -347,  -341,  1781,    22,    35,   nil,   -25,
    34,   630,  -268,     1,   -55,    16,   nil,   nil,    21,    29,
  -265,     2,   -24,   -61,    52,  -189,   -20,   -15,  -390,   139,
    -5,  -338,  -142,   nil,  -327,  -468,   nil,   nil,   nil,   nil,
   nil,   nil,    58,    69,    69,   nil,  -236,  -569,  -434,  -292,
    69,   nil,  -197,    17,   nil,  -483,    68,  -208,  -162,    77,
  -484,    78,  -486,  -360,  -662,  -365,  -495,  -181,  -186,  -358,
  -603,  -397,  -396,   nil,   -72,   nil,   -95,   -95,  -331,  -635,
  -302,  -436,   nil,   nil,   104,   105,    27,   102,  -167,  -277,
   103,  -340,  -339,  -460,   nil,  -565,  -672,  -441,  -470,  -564,
  -690,   nil,   nil,  -376 ]

racc_goto_default = [
   nil,   nil,   294,   nil,   nil,   735,   nil,     3,   nil,     4,
   314,   nil,   nil,   nil,   204,    16,    11,   205,   288,   nil,
   203,   nil,   246,    15,   nil,    19,    20,    21,   nil,    25,
   598,   nil,   nil,   nil,   nil,   279,    29,   nil,    31,    34,
    33,   201,   325,   nil,   116,   382,   115,   118,    70,    71,
   nil,   nil,    42,   297,   299,   nil,   300,   546,   547,   427,
   564,   nil,   nil,   257,   nil,   nil,    43,    44,    45,    46,
    47,    48,    49,   nil,   258,    55,   nil,   nil,   nil,   nil,
   nil,    62,   nil,   489,    63,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   237,   nil,   386,   nil,   nil,   nil,   nil,
   nil,   nil,    69,    72,    73,   nil,   nil,   nil,   nil,   527,
   nil,   nil,   nil,   648,   649,   650,   651,   nil,   814,   658,
   659,   662,   665,   250 ]

racc_reduce_table = [
  0, 0, :racc_error,
  1, 132, :_reduce_1,
  4, 134, :_reduce_2,
  2, 133, :_reduce_3,
  0, 138, :_reduce_4,
  1, 138, :_reduce_5,
  2, 138, :_reduce_6,
  3, 138, :_reduce_7,
  0, 155, :_reduce_8,
  4, 140, :_reduce_9,
  3, 140, :_reduce_10,
  3, 140, :_reduce_11,
  3, 140, :_reduce_12,
  2, 140, :_reduce_13,
  3, 140, :_reduce_14,
  3, 140, :_reduce_15,
  3, 140, :_reduce_16,
  3, 140, :_reduce_17,
  3, 140, :_reduce_18,
  4, 140, :_reduce_19,
  4, 140, :_reduce_20,
  3, 140, :_reduce_21,
  3, 140, :_reduce_22,
  3, 140, :_reduce_23,
  6, 140, :_reduce_24,
  5, 140, :_reduce_25,
  5, 140, :_reduce_26,
  5, 140, :_reduce_27,
  3, 140, :_reduce_28,
  3, 140, :_reduce_29,
  3, 140, :_reduce_30,
  3, 140, :_reduce_31,
  1, 140, :_reduce_none,
  1, 154, :_reduce_none,
  3, 154, :_reduce_34,
  3, 154, :_reduce_35,
  2, 154, :_reduce_36,
  2, 154, :_reduce_37,
  1, 154, :_reduce_none,
  1, 144, :_reduce_none,
  1, 146, :_reduce_none,
  1, 146, :_reduce_none,
  2, 146, :_reduce_42,
  2, 146, :_reduce_43,
  2, 146, :_reduce_44,
  1, 158, :_reduce_none,
  4, 158, :_reduce_46,
  4, 158, :_reduce_47,
  0, 165, :_reduce_48,
  5, 163, :_reduce_49,
  2, 157, :_reduce_50,
  3, 157, :_reduce_51,
  4, 157, :_reduce_52,
  5, 157, :_reduce_53,
  4, 157, :_reduce_54,
  5, 157, :_reduce_55,
  2, 157, :_reduce_56,
  2, 157, :_reduce_57,
  1, 147, :_reduce_58,
  3, 147, :_reduce_59,
  1, 168, :_reduce_60,
  3, 168, :_reduce_61,
  1, 167, :_reduce_62,
  2, 167, :_reduce_63,
  3, 167, :_reduce_64,
  2, 167, :_reduce_65,
  2, 167, :_reduce_66,
  1, 167, :_reduce_67,
  1, 170, :_reduce_none,
  3, 170, :_reduce_69,
  2, 169, :_reduce_70,
  3, 169, :_reduce_71,
  1, 171, :_reduce_72,
  4, 171, :_reduce_73,
  3, 171, :_reduce_74,
  3, 171, :_reduce_75,
  3, 171, :_reduce_76,
  3, 171, :_reduce_77,
  2, 171, :_reduce_78,
  1, 171, :_reduce_79,
  1, 145, :_reduce_80,
  4, 145, :_reduce_81,
  3, 145, :_reduce_82,
  3, 145, :_reduce_83,
  3, 145, :_reduce_84,
  3, 145, :_reduce_85,
  2, 145, :_reduce_86,
  1, 145, :_reduce_87,
  1, 173, :_reduce_88,
  1, 173, :_reduce_none,
  2, 174, :_reduce_90,
  1, 174, :_reduce_91,
  3, 174, :_reduce_92,
  1, 175, :_reduce_none,
  1, 175, :_reduce_none,
  1, 175, :_reduce_none,
  1, 175, :_reduce_none,
  1, 175, :_reduce_none,
  1, 178, :_reduce_98,
  1, 178, :_reduce_none,
  1, 142, :_reduce_none,
  1, 142, :_reduce_none,
  1, 143, :_reduce_102,
  0, 181, :_reduce_103,
  4, 143, :_reduce_104,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  3, 156, :_reduce_171,
  5, 156, :_reduce_172,
  3, 156, :_reduce_173,
  6, 156, :_reduce_174,
  5, 156, :_reduce_175,
  5, 156, :_reduce_176,
  5, 156, :_reduce_177,
  5, 156, :_reduce_178,
  4, 156, :_reduce_179,
  3, 156, :_reduce_180,
  3, 156, :_reduce_181,
  3, 156, :_reduce_182,
  3, 156, :_reduce_183,
  3, 156, :_reduce_184,
  3, 156, :_reduce_185,
  3, 156, :_reduce_186,
  3, 156, :_reduce_187,
  3, 156, :_reduce_188,
  4, 156, :_reduce_189,
  4, 156, :_reduce_190,
  2, 156, :_reduce_191,
  2, 156, :_reduce_192,
  3, 156, :_reduce_193,
  3, 156, :_reduce_194,
  3, 156, :_reduce_195,
  3, 156, :_reduce_196,
  3, 156, :_reduce_197,
  3, 156, :_reduce_198,
  3, 156, :_reduce_199,
  3, 156, :_reduce_200,
  3, 156, :_reduce_201,
  3, 156, :_reduce_202,
  3, 156, :_reduce_203,
  3, 156, :_reduce_204,
  3, 156, :_reduce_205,
  2, 156, :_reduce_206,
  2, 156, :_reduce_207,
  3, 156, :_reduce_208,
  3, 156, :_reduce_209,
  3, 156, :_reduce_210,
  3, 156, :_reduce_211,
  3, 156, :_reduce_212,
  5, 156, :_reduce_213,
  1, 156, :_reduce_none,
  1, 153, :_reduce_none,
  1, 150, :_reduce_216,
  2, 150, :_reduce_217,
  2, 150, :_reduce_218,
  5, 150, :_reduce_219,
  2, 150, :_reduce_220,
  3, 150, :_reduce_221,
  3, 188, :_reduce_222,
  4, 188, :_reduce_223,
  4, 188, :_reduce_224,
  6, 188, :_reduce_225,
  0, 189, :_reduce_226,
  1, 189, :_reduce_none,
  1, 159, :_reduce_228,
  2, 159, :_reduce_229,
  5, 159, :_reduce_230,
  2, 159, :_reduce_231,
  5, 159, :_reduce_232,
  4, 159, :_reduce_233,
  7, 159, :_reduce_234,
  3, 159, :_reduce_235,
  1, 159, :_reduce_236,
  4, 192, :_reduce_237,
  3, 192, :_reduce_238,
  5, 192, :_reduce_239,
  7, 192, :_reduce_240,
  2, 192, :_reduce_241,
  5, 192, :_reduce_242,
  4, 192, :_reduce_243,
  6, 192, :_reduce_244,
  7, 192, :_reduce_245,
  9, 192, :_reduce_246,
  3, 192, :_reduce_247,
  1, 192, :_reduce_248,
  0, 194, :_reduce_249,
  2, 162, :_reduce_250,
  1, 193, :_reduce_251,
  0, 195, :_reduce_252,
  3, 193, :_reduce_253,
  0, 196, :_reduce_254,
  4, 193, :_reduce_255,
  2, 191, :_reduce_256,
  2, 190, :_reduce_257,
  0, 190, :_reduce_258,
  1, 185, :_reduce_259,
  3, 185, :_reduce_260,
  3, 152, :_reduce_261,
  4, 152, :_reduce_262,
  2, 152, :_reduce_263,
  1, 183, :_reduce_none,
  1, 183, :_reduce_none,
  1, 183, :_reduce_none,
  1, 183, :_reduce_none,
  1, 183, :_reduce_none,
  1, 183, :_reduce_none,
  1, 183, :_reduce_none,
  1, 183, :_reduce_none,
  1, 183, :_reduce_272,
  3, 183, :_reduce_273,
  0, 219, :_reduce_274,
  5, 183, :_reduce_275,
  3, 183, :_reduce_276,
  3, 183, :_reduce_277,
  2, 183, :_reduce_278,
  4, 183, :_reduce_279,
  3, 183, :_reduce_280,
  3, 183, :_reduce_281,
  1, 183, :_reduce_282,
  4, 183, :_reduce_283,
  3, 183, :_reduce_284,
  1, 183, :_reduce_285,
  5, 183, :_reduce_286,
  2, 183, :_reduce_287,
  1, 183, :_reduce_none,
  2, 183, :_reduce_289,
  6, 183, :_reduce_290,
  6, 183, :_reduce_291,
  0, 220, :_reduce_292,
  0, 221, :_reduce_293,
  7, 183, :_reduce_294,
  0, 222, :_reduce_295,
  0, 223, :_reduce_296,
  7, 183, :_reduce_297,
  5, 183, :_reduce_298,
  4, 183, :_reduce_299,
  5, 183, :_reduce_300,
  0, 224, :_reduce_301,
  0, 225, :_reduce_302,
  9, 183, :_reduce_303,
  0, 226, :_reduce_304,
  6, 183, :_reduce_305,
  0, 227, :_reduce_306,
  7, 183, :_reduce_307,
  0, 228, :_reduce_308,
  5, 183, :_reduce_309,
  0, 229, :_reduce_310,
  6, 183, :_reduce_311,
  0, 230, :_reduce_312,
  0, 231, :_reduce_313,
  9, 183, :_reduce_314,
  1, 183, :_reduce_315,
  1, 183, :_reduce_316,
  1, 183, :_reduce_317,
  1, 183, :_reduce_318,
  1, 149, :_reduce_none,
  1, 212, :_reduce_320,
  1, 215, :_reduce_321,
  1, 207, :_reduce_none,
  1, 207, :_reduce_none,
  1, 207, :_reduce_none,
  2, 207, :_reduce_325,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 208, :_reduce_none,
  5, 208, :_reduce_330,
  1, 136, :_reduce_none,
  2, 136, :_reduce_332,
  1, 211, :_reduce_none,
  1, 211, :_reduce_none,
  1, 232, :_reduce_335,
  3, 232, :_reduce_336,
  1, 233, :_reduce_none,
  2, 233, :_reduce_none,
  4, 233, :_reduce_339,
  7, 233, :_reduce_340,
  6, 233, :_reduce_341,
  4, 233, :_reduce_342,
  3, 233, :_reduce_343,
  5, 233, :_reduce_344,
  4, 233, :_reduce_345,
  2, 233, :_reduce_346,
  1, 233, :_reduce_347,
  2, 233, :_reduce_348,
  0, 164, :_reduce_349,
  2, 164, :_reduce_350,
  1, 164, :_reduce_351,
  3, 164, :_reduce_352,
  0, 235, :_reduce_353,
  5, 234, :_reduce_354,
  2, 160, :_reduce_355,
  4, 160, :_reduce_356,
  4, 160, :_reduce_357,
  2, 206, :_reduce_358,
  4, 206, :_reduce_359,
  4, 206, :_reduce_360,
  3, 206, :_reduce_361,
  2, 206, :_reduce_362,
  1, 206, :_reduce_363,
  0, 237, :_reduce_364,
  5, 205, :_reduce_365,
  0, 238, :_reduce_366,
  5, 205, :_reduce_367,
  5, 210, :_reduce_368,
  1, 239, :_reduce_none,
  4, 239, :_reduce_370,
  2, 239, :_reduce_371,
  1, 240, :_reduce_372,
  1, 240, :_reduce_none,
  6, 135, :_reduce_374,
  0, 135, :_reduce_375,
  1, 241, :_reduce_376,
  1, 241, :_reduce_none,
  1, 241, :_reduce_none,
  2, 242, :_reduce_379,
  1, 242, :_reduce_none,
  2, 137, :_reduce_381,
  1, 137, :_reduce_none,
  1, 197, :_reduce_none,
  1, 197, :_reduce_none,
  1, 197, :_reduce_none,
  1, 198, :_reduce_386,
  1, 244, :_reduce_387,
  2, 244, :_reduce_388,
  3, 245, :_reduce_389,
  1, 245, :_reduce_390,
  3, 199, :_reduce_391,
  4, 200, :_reduce_392,
  3, 201, :_reduce_393,
  0, 248, :_reduce_394,
  3, 248, :_reduce_395,
  1, 249, :_reduce_396,
  2, 249, :_reduce_397,
  3, 202, :_reduce_398,
  0, 251, :_reduce_399,
  3, 251, :_reduce_400,
  0, 246, :_reduce_401,
  2, 246, :_reduce_402,
  0, 247, :_reduce_403,
  2, 247, :_reduce_404,
  1, 250, :_reduce_405,
  2, 250, :_reduce_406,
  0, 253, :_reduce_407,
  4, 250, :_reduce_408,
  1, 252, :_reduce_409,
  1, 252, :_reduce_410,
  1, 252, :_reduce_411,
  1, 252, :_reduce_none,
  1, 179, :_reduce_413,
  3, 180, :_reduce_414,
  1, 243, :_reduce_415,
  1, 243, :_reduce_416,
  2, 243, :_reduce_417,
  2, 243, :_reduce_418,
  1, 172, :_reduce_419,
  1, 172, :_reduce_420,
  1, 172, :_reduce_421,
  1, 172, :_reduce_422,
  1, 172, :_reduce_423,
  1, 172, :_reduce_424,
  1, 172, :_reduce_425,
  1, 172, :_reduce_426,
  1, 172, :_reduce_427,
  1, 172, :_reduce_428,
  1, 172, :_reduce_429,
  1, 203, :_reduce_430,
  1, 148, :_reduce_431,
  1, 151, :_reduce_432,
  1, 151, :_reduce_433,
  1, 213, :_reduce_434,
  3, 213, :_reduce_435,
  2, 213, :_reduce_436,
  4, 216, :_reduce_437,
  2, 216, :_reduce_438,
  6, 254, :_reduce_439,
  4, 254, :_reduce_440,
  4, 254, :_reduce_441,
  2, 254, :_reduce_442,
  4, 254, :_reduce_443,
  2, 254, :_reduce_444,
  2, 254, :_reduce_445,
  1, 254, :_reduce_446,
  0, 254, :_reduce_447,
  1, 260, :_reduce_448,
  1, 260, :_reduce_449,
  1, 260, :_reduce_450,
  1, 260, :_reduce_451,
  1, 260, :_reduce_452,
  1, 255, :_reduce_453,
  3, 255, :_reduce_454,
  3, 261, :_reduce_455,
  1, 256, :_reduce_456,
  3, 256, :_reduce_457,
  1, 262, :_reduce_none,
  1, 262, :_reduce_none,
  2, 257, :_reduce_460,
  1, 257, :_reduce_461,
  1, 263, :_reduce_none,
  1, 263, :_reduce_none,
  2, 259, :_reduce_464,
  2, 258, :_reduce_465,
  0, 258, :_reduce_466,
  1, 217, :_reduce_none,
  4, 217, :_reduce_468,
  0, 204, :_reduce_469,
  2, 204, :_reduce_470,
  2, 204, :_reduce_471,
  1, 187, :_reduce_472,
  3, 187, :_reduce_473,
  3, 264, :_reduce_474,
  1, 166, :_reduce_none,
  1, 166, :_reduce_none,
  1, 166, :_reduce_none,
  1, 161, :_reduce_none,
  1, 161, :_reduce_none,
  1, 161, :_reduce_none,
  1, 161, :_reduce_none,
  1, 236, :_reduce_none,
  1, 236, :_reduce_none,
  1, 236, :_reduce_none,
  1, 218, :_reduce_none,
  1, 218, :_reduce_none,
  0, 139, :_reduce_none,
  1, 139, :_reduce_none,
  0, 182, :_reduce_none,
  1, 182, :_reduce_none,
  0, 186, :_reduce_none,
  1, 186, :_reduce_none,
  1, 186, :_reduce_none,
  1, 214, :_reduce_494,
  1, 214, :_reduce_none,
  1, 141, :_reduce_none,
  2, 141, :_reduce_none,
  0, 184, :_reduce_498 ]

racc_reduce_n = 499

racc_shift_n = 896

racc_token_table = {
  false => 0,
  :error => 1,
  :kCLASS => 2,
  :kMODULE => 3,
  :kDEF => 4,
  :kUNDEF => 5,
  :kBEGIN => 6,
  :kRESCUE => 7,
  :kENSURE => 8,
  :kEND => 9,
  :kIF => 10,
  :kUNLESS => 11,
  :kTHEN => 12,
  :kELSIF => 13,
  :kELSE => 14,
  :kCASE => 15,
  :kWHEN => 16,
  :kWHILE => 17,
  :kUNTIL => 18,
  :kFOR => 19,
  :kBREAK => 20,
  :kNEXT => 21,
  :kREDO => 22,
  :kRETRY => 23,
  :kIN => 24,
  :kDO => 25,
  :kDO_COND => 26,
  :kDO_BLOCK => 27,
  :kRETURN => 28,
  :kYIELD => 29,
  :kSUPER => 30,
  :kSELF => 31,
  :kNIL => 32,
  :kTRUE => 33,
  :kFALSE => 34,
  :kAND => 35,
  :kOR => 36,
  :kNOT => 37,
  :kIF_MOD => 38,
  :kUNLESS_MOD => 39,
  :kWHILE_MOD => 40,
  :kUNTIL_MOD => 41,
  :kRESCUE_MOD => 42,
  :kALIAS => 43,
  :kDEFINED => 44,
  :klBEGIN => 45,
  :klEND => 46,
  :k__LINE__ => 47,
  :k__FILE__ => 48,
  :tIDENTIFIER => 49,
  :tFID => 50,
  :tGVAR => 51,
  :tIVAR => 52,
  :tCONSTANT => 53,
  :tCVAR => 54,
  :tNTH_REF => 55,
  :tBACK_REF => 56,
  :tSTRING_CONTENT => 57,
  :tINTEGER => 58,
  :tFLOAT => 59,
  :tUPLUS => 60,
  :tUMINUS => 61,
  :tUNARY_NUM => 62,
  :tPOW => 63,
  :tCMP => 64,
  :tEQ => 65,
  :tEQQ => 66,
  :tNEQ => 67,
  :tGEQ => 68,
  :tLEQ => 69,
  :tANDOP => 70,
  :tOROP => 71,
  :tMATCH => 72,
  :tNMATCH => 73,
  :tDOT => 74,
  :tDOT2 => 75,
  :tDOT3 => 76,
  :tAREF => 77,
  :tASET => 78,
  :tLSHFT => 79,
  :tRSHFT => 80,
  :tCOLON2 => 81,
  :tCOLON3 => 82,
  :tOP_ASGN => 83,
  :tASSOC => 84,
  :tLPAREN => 85,
  :tLPAREN2 => 86,
  :tRPAREN => 87,
  :tLPAREN_ARG => 88,
  :tLBRACK => 89,
  :tLBRACK2 => 90,
  :tRBRACK => 91,
  :tLBRACE => 92,
  :tLBRACE_ARG => 93,
  :tSTAR => 94,
  :tSTAR2 => 95,
  :tAMPER => 96,
  :tAMPER2 => 97,
  :tTILDE => 98,
  :tPERCENT => 99,
  :tDIVIDE => 100,
  :tPLUS => 101,
  :tMINUS => 102,
  :tLT => 103,
  :tGT => 104,
  :tPIPE => 105,
  :tBANG => 106,
  :tCARET => 107,
  :tLCURLY => 108,
  :tRCURLY => 109,
  :tBACK_REF2 => 110,
  :tSYMBEG => 111,
  :tSTRING_BEG => 112,
  :tXSTRING_BEG => 113,
  :tREGEXP_BEG => 114,
  :tWORDS_BEG => 115,
  :tQWORDS_BEG => 116,
  :tSTRING_DBEG => 117,
  :tSTRING_DVAR => 118,
  :tSTRING_END => 119,
  :tSTRING => 120,
  :tSYMBOL => 121,
  :tREGEXP_OPT => 122,
  :tNL => 123,
  :tEH => 124,
  :tCOLON => 125,
  :tCOMMA => 126,
  :tSPACE => 127,
  :tSEMI => 128,
  :tEQL => 129,
  :tLOWEST => 130 }

racc_nt_base = 131

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]
Ractor.make_shareable(Racc_arg) if defined?(Ractor)

Racc_token_to_s_table = [
  "$end",
  "error",
  "kCLASS",
  "kMODULE",
  "kDEF",
  "kUNDEF",
  "kBEGIN",
  "kRESCUE",
  "kENSURE",
  "kEND",
  "kIF",
  "kUNLESS",
  "kTHEN",
  "kELSIF",
  "kELSE",
  "kCASE",
  "kWHEN",
  "kWHILE",
  "kUNTIL",
  "kFOR",
  "kBREAK",
  "kNEXT",
  "kREDO",
  "kRETRY",
  "kIN",
  "kDO",
  "kDO_COND",
  "kDO_BLOCK",
  "kRETURN",
  "kYIELD",
  "kSUPER",
  "kSELF",
  "kNIL",
  "kTRUE",
  "kFALSE",
  "kAND",
  "kOR",
  "kNOT",
  "kIF_MOD",
  "kUNLESS_MOD",
  "kWHILE_MOD",
  "kUNTIL_MOD",
  "kRESCUE_MOD",
  "kALIAS",
  "kDEFINED",
  "klBEGIN",
  "klEND",
  "k__LINE__",
  "k__FILE__",
  "tIDENTIFIER",
  "tFID",
  "tGVAR",
  "tIVAR",
  "tCONSTANT",
  "tCVAR",
  "tNTH_REF",
  "tBACK_REF",
  "tSTRING_CONTENT",
  "tINTEGER",
  "tFLOAT",
  "tUPLUS",
  "tUMINUS",
  "tUNARY_NUM",
  "tPOW",
  "tCMP",
  "tEQ",
  "tEQQ",
  "tNEQ",
  "tGEQ",
  "tLEQ",
  "tANDOP",
  "tOROP",
  "tMATCH",
  "tNMATCH",
  "tDOT",
  "tDOT2",
  "tDOT3",
  "tAREF",
  "tASET",
  "tLSHFT",
  "tRSHFT",
  "tCOLON2",
  "tCOLON3",
  "tOP_ASGN",
  "tASSOC",
  "tLPAREN",
  "tLPAREN2",
  "tRPAREN",
  "tLPAREN_ARG",
  "tLBRACK",
  "tLBRACK2",
  "tRBRACK",
  "tLBRACE",
  "tLBRACE_ARG",
  "tSTAR",
  "tSTAR2",
  "tAMPER",
  "tAMPER2",
  "tTILDE",
  "tPERCENT",
  "tDIVIDE",
  "tPLUS",
  "tMINUS",
  "tLT",
  "tGT",
  "tPIPE",
  "tBANG",
  "tCARET",
  "tLCURLY",
  "tRCURLY",
  "tBACK_REF2",
  "tSYMBEG",
  "tSTRING_BEG",
  "tXSTRING_BEG",
  "tREGEXP_BEG",
  "tWORDS_BEG",
  "tQWORDS_BEG",
  "tSTRING_DBEG",
  "tSTRING_DVAR",
  "tSTRING_END",
  "tSTRING",
  "tSYMBOL",
  "tREGEXP_OPT",
  "tNL",
  "tEH",
  "tCOLON",
  "tCOMMA",
  "tSPACE",
  "tSEMI",
  "tEQL",
  "tLOWEST",
  "$start",
  "program",
  "compstmt",
  "bodystmt",
  "opt_rescue",
  "opt_else",
  "opt_ensure",
  "stmts",
  "opt_terms",
  "stmt",
  "terms",
  "fitem",
  "undef_list",
  "expr_value",
  "lhs",
  "command_call",
  "mlhs",
  "var_lhs",
  "primary_value",
  "aref_args",
  "backref",
  "mrhs",
  "arg_value",
  "expr",
  "@1",
  "arg",
  "command",
  "block_command",
  "call_args",
  "block_call",
  "operation2",
  "command_args",
  "cmd_brace_block",
  "opt_block_var",
  "@2",
  "operation",
  "mlhs_basic",
  "mlhs_entry",
  "mlhs_head",
  "mlhs_item",
  "mlhs_node",
  "variable",
  "cname",
  "cpath",
  "fname",
  "op",
  "reswords",
  "fsym",
  "symbol",
  "dsym",
  "@3",
  "opt_nl",
  "primary",
  "none",
  "args",
  "trailer",
  "assocs",
  "paren_args",
  "opt_paren_args",
  "opt_block_arg",
  "block_arg",
  "call_args2",
  "open_args",
  "@4",
  "@5",
  "@6",
  "literal",
  "strings",
  "xstring",
  "regexp",
  "words",
  "qwords",
  "var_ref",
  "assoc_list",
  "brace_block",
  "method_call",
  "then",
  "if_tail",
  "do",
  "case_body",
  "for_var",
  "k_class",
  "superclass",
  "term",
  "k_module",
  "f_arglist",
  "singleton",
  "dot_or_colon",
  "@7",
  "@8",
  "@9",
  "@10",
  "@11",
  "@12",
  "@13",
  "@14",
  "@15",
  "@16",
  "@17",
  "@18",
  "@19",
  "block_par",
  "block_var",
  "do_block",
  "@20",
  "operation3",
  "@21",
  "@22",
  "when_args",
  "cases",
  "exc_list",
  "exc_var",
  "numeric",
  "string",
  "string1",
  "string_contents",
  "xstring_contents",
  "word_list",
  "word",
  "string_content",
  "qword_list",
  "string_dvar",
  "@23",
  "f_args",
  "f_arg",
  "f_optarg",
  "f_rest_arg",
  "opt_f_block_arg",
  "f_block_arg",
  "f_norm_arg",
  "f_opt",
  "restarg_mark",
  "blkarg_mark",
  "assoc" ]
Ractor.make_shareable(Racc_token_to_s_table) if defined?(Ractor)

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

def _reduce_1(val, _values, result)
                      result = val[0]

    result
end

def _reduce_2(val, _values, result)
                      rescue_bodies     = val[1]
                      else_t,   else_   = val[2]
                      ensure_t, ensure_ = val[3]

                      if rescue_bodies.empty? && !else_t.nil?
                        diagnostic :warning, :useless_else, nil, else_t
                      end

                      result = @builder.begin_body(val[0],
                                  rescue_bodies,
                                  else_t,   else_,
                                  ensure_t, ensure_)

    result
end

def _reduce_3(val, _values, result)
                      result = @builder.compstmt(val[0])

    result
end

def _reduce_4(val, _values, result)
                      result = []

    result
end

def _reduce_5(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_6(val, _values, result)
                      result = [ val[1] ]

    result
end

def _reduce_7(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_8(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_9(val, _values, result)
                      result = @builder.alias(val[0], val[1], val[3])

    result
end

def _reduce_10(val, _values, result)
                      result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.gvar(val[2]))

    result
end

def _reduce_11(val, _values, result)
                      result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.back_ref(val[2]))

    result
end

def _reduce_12(val, _values, result)
                      diagnostic :error, :nth_ref_alias, nil, val[2]

    result
end

def _reduce_13(val, _values, result)
                      result = @builder.undef_method(val[0], val[1])

    result
end

def _reduce_14(val, _values, result)
                      result = @builder.condition_mod(val[0], nil,
                                                      val[1], val[2])

    result
end

def _reduce_15(val, _values, result)
                      result = @builder.condition_mod(nil, val[0],
                                                      val[1], val[2])

    result
end

def _reduce_16(val, _values, result)
                      result = @builder.loop_mod(:while, val[0], val[1], val[2])

    result
end

def _reduce_17(val, _values, result)
                      result = @builder.loop_mod(:until, val[0], val[1], val[2])

    result
end

def _reduce_18(val, _values, result)
                      rescue_body = @builder.rescue_body(val[1],
                                        nil, nil, nil,
                                        nil, val[2])

                      result = @builder.begin_body(val[0], [ rescue_body ])

    result
end

def _reduce_19(val, _values, result)
                      if @context.in_def
                        diagnostic :error, :begin_in_method, nil, val[0]
                      end

                      result = @builder.preexe(val[0], val[1], val[2], val[3])

    result
end

def _reduce_20(val, _values, result)
                      result = @builder.postexe(val[0], val[1], val[2], val[3])

    result
end

def _reduce_21(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

def _reduce_22(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1], val[2])

    result
end

def _reduce_23(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_24(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])

    result
end

def _reduce_25(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_26(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_27(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_28(val, _values, result)
                      @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_29(val, _values, result)
                      result = @builder.assign(val[0], val[1],
                                  @builder.array(nil, val[2], nil))

    result
end

def _reduce_30(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1], val[2])

    result
end

def _reduce_31(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1],
                                  @builder.array(nil, val[2], nil))

    result
end

# reduce 32 omitted

# reduce 33 omitted

def _reduce_34(val, _values, result)
                      result = @builder.logical_op(:and, val[0], val[1], val[2])

    result
end

def _reduce_35(val, _values, result)
                      result = @builder.logical_op(:or, val[0], val[1], val[2])

    result
end

def _reduce_36(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[1], nil)

    result
end

def _reduce_37(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[1], nil)

    result
end

# reduce 38 omitted

# reduce 39 omitted

# reduce 40 omitted

# reduce 41 omitted

def _reduce_42(val, _values, result)
                      result = @builder.keyword_cmd(:return, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_43(val, _values, result)
                      result = @builder.keyword_cmd(:break, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_44(val, _values, result)
                      result = @builder.keyword_cmd(:next, val[0],
                                  nil, val[1], nil)

    result
end

# reduce 45 omitted

def _reduce_46(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_47(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_48(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_49(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_50(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.call_method(nil, nil, val[0],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_51(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      method_call = @builder.call_method(nil, nil, val[0],
                                      lparen_t, args, rparen_t)

                      begin_t, block_args, body, end_t = val[2]
                      result      = @builder.block(method_call,
                                      begin_t, block_args, body, end_t)

    result
end

def _reduce_52(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)


    result
end

def _reduce_53(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                      lparen_t, args, rparen_t)

                      begin_t, block_args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, block_args, body, end_t)

    result
end

def _reduce_54(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_55(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                      lparen_t, args, rparen_t)

                      begin_t, block_args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, block_args, body, end_t)

    result
end

def _reduce_56(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.keyword_cmd(:super, val[0],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_57(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.keyword_cmd(:yield, val[0],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_58(val, _values, result)
                      result = @builder.multi_lhs(nil, val[0], nil)

    result
end

def _reduce_59(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_60(val, _values, result)
                      result = @builder.multi_lhs(nil, val[0], nil)

    result
end

def _reduce_61(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

def _reduce_62(val, _values, result)
                      result = val[0]

    result
end

def _reduce_63(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_64(val, _values, result)
                      result = val[0] << @builder.splat(val[1], val[2])

    result
end

def _reduce_65(val, _values, result)
                      result = val[0] << @builder.splat(val[1])

    result
end

def _reduce_66(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

def _reduce_67(val, _values, result)
                      result = [ @builder.splat(val[0]) ]

    result
end

# reduce 68 omitted

def _reduce_69(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_70(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_71(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_72(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_73(val, _values, result)
                      result = @builder.index_asgn(val[0], val[1], val[2], val[3])

    result
end

def _reduce_74(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_75(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_76(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_77(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))

    result
end

def _reduce_78(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))

    result
end

def _reduce_79(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_80(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_81(val, _values, result)
                      result = @builder.index_asgn(val[0], val[1], val[2], val[3])

    result
end

def _reduce_82(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_83(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_84(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_85(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))

    result
end

def _reduce_86(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))

    result
end

def _reduce_87(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_88(val, _values, result)
                      diagnostic :error, :module_name_const, nil, val[0]

    result
end

# reduce 89 omitted

def _reduce_90(val, _values, result)
                      result = @builder.const_global(val[0], val[1])

    result
end

def _reduce_91(val, _values, result)
                      result = @builder.const(val[0])

    result
end

def _reduce_92(val, _values, result)
                      result = @builder.const_fetch(val[0], val[1], val[2])

    result
end

# reduce 93 omitted

# reduce 94 omitted

# reduce 95 omitted

# reduce 96 omitted

# reduce 97 omitted

def _reduce_98(val, _values, result)
                      result = @builder.symbol_internal(val[0])

    result
end

# reduce 99 omitted

# reduce 100 omitted

# reduce 101 omitted

def _reduce_102(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_103(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_104(val, _values, result)
                      result = val[0] << val[3]

    result
end

# reduce 105 omitted

# reduce 106 omitted

# reduce 107 omitted

# reduce 108 omitted

# reduce 109 omitted

# reduce 110 omitted

# reduce 111 omitted

# reduce 112 omitted

# reduce 113 omitted

# reduce 114 omitted

# reduce 115 omitted

# reduce 116 omitted

# reduce 117 omitted

# reduce 118 omitted

# reduce 119 omitted

# reduce 120 omitted

# reduce 121 omitted

# reduce 122 omitted

# reduce 123 omitted

# reduce 124 omitted

# reduce 125 omitted

# reduce 126 omitted

# reduce 127 omitted

# reduce 128 omitted

# reduce 129 omitted

# reduce 130 omitted

# reduce 131 omitted

# reduce 132 omitted

# reduce 133 omitted

# reduce 134 omitted

# reduce 135 omitted

# reduce 136 omitted

# reduce 137 omitted

# reduce 138 omitted

# reduce 139 omitted

# reduce 140 omitted

# reduce 141 omitted

# reduce 142 omitted

# reduce 143 omitted

# reduce 144 omitted

# reduce 145 omitted

# reduce 146 omitted

# reduce 147 omitted

# reduce 148 omitted

# reduce 149 omitted

# reduce 150 omitted

# reduce 151 omitted

# reduce 152 omitted

# reduce 153 omitted

# reduce 154 omitted

# reduce 155 omitted

# reduce 156 omitted

# reduce 157 omitted

# reduce 158 omitted

# reduce 159 omitted

# reduce 160 omitted

# reduce 161 omitted

# reduce 162 omitted

# reduce 163 omitted

# reduce 164 omitted

# reduce 165 omitted

# reduce 166 omitted

# reduce 167 omitted

# reduce 168 omitted

# reduce 169 omitted

# reduce 170 omitted

def _reduce_171(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

def _reduce_172(val, _values, result)
                      rescue_body = @builder.rescue_body(val[3],
                                        nil, nil, nil,
                                        nil, val[4])

                      rescue_ = @builder.begin_body(val[2], [ rescue_body ])

                      result  = @builder.assign(val[0], val[1], rescue_)

    result
end

def _reduce_173(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_174(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])

    result
end

def _reduce_175(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_176(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_177(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_178(val, _values, result)
                      diagnostic :error, :dynamic_const, nil, val[2], [ val[3] ]

    result
end

def _reduce_179(val, _values, result)
                      diagnostic :error, :dynamic_const, nil, val[1], [ val[2] ]

    result
end

def _reduce_180(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_181(val, _values, result)
                      result = @builder.range_inclusive(val[0], val[1], val[2])

    result
end

def _reduce_182(val, _values, result)
                      result = @builder.range_exclusive(val[0], val[1], val[2])

    result
end

def _reduce_183(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_184(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_185(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_186(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_187(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_188(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_189(val, _values, result)
                      result = @builder.unary_op(val[0],
                                  @builder.binary_op(
                                    @builder.integer(val[1]),
                                      val[2], val[3]))

    result
end

def _reduce_190(val, _values, result)
                      result = @builder.unary_op(val[0],
                                  @builder.binary_op(
                                    @builder.float(val[1]),
                                      val[2], val[3]))

    result
end

def _reduce_191(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_192(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_193(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_194(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_195(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_196(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_197(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_198(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_199(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_200(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_201(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_202(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_203(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_204(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_205(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_206(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[1], nil)

    result
end

def _reduce_207(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_208(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_209(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_210(val, _values, result)
                      result = @builder.logical_op(:and, val[0], val[1], val[2])

    result
end

def _reduce_211(val, _values, result)
                      result = @builder.logical_op(:or, val[0], val[1], val[2])

    result
end

def _reduce_212(val, _values, result)
                      result = @builder.keyword_cmd(:defined?, val[0], nil, [ val[2] ], nil)

    result
end

def _reduce_213(val, _values, result)
                      result = @builder.ternary(val[0], val[1],
                                                val[2], val[3], val[4])

    result
end

# reduce 214 omitted

# reduce 215 omitted

def _reduce_216(val, _values, result)
                      result = []

    result
end

def _reduce_217(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_218(val, _values, result)
                      result = val[0]

    result
end

def _reduce_219(val, _values, result)
                      result = val[0] << @builder.splat(val[2], val[3])

    result
end

def _reduce_220(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]

    result
end

def _reduce_221(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

def _reduce_222(val, _values, result)
                      result = [ val[0], [], val[2] ]

    result
end

def _reduce_223(val, _values, result)
                      result = [ val[0], val[1], val[3] ]

    result
end

def _reduce_224(val, _values, result)
                      result = [ val[0], [ val[1] ], val[3] ]

    result
end

def _reduce_225(val, _values, result)
                      result = [ val[0], val[1] << val[3], val[5] ]

    result
end

def _reduce_226(val, _values, result)
                      result = [ nil, [], nil ]

    result
end

# reduce 227 omitted

def _reduce_228(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_229(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_230(val, _values, result)
                      result = val[0].concat(
                                [ @builder.splat(val[2], val[3]),
                                   *val[4] ])

    result
end

def _reduce_231(val, _values, result)
                      result =  [ @builder.associate(nil, val[0], nil),
                                  *val[1] ]

    result
end

def _reduce_232(val, _values, result)
                      result =  [ @builder.associate(nil, val[0], nil),
                                  @builder.splat(val[2], val[3]),
                                  *val[4] ]

    result
end

def _reduce_233(val, _values, result)
                      result = val[0].concat(
                                [ @builder.associate(nil, val[2], nil),
                                  *val[3] ])

    result
end

def _reduce_234(val, _values, result)
                      result = val[0].concat(
                                [ @builder.associate(nil, val[2], nil),
                                  @builder.splat(val[4], val[5]),
                                  *val[6] ])

    result
end

def _reduce_235(val, _values, result)
                      result =  [ @builder.splat(val[0], val[1]),
                                  *val[2] ]

    result
end

def _reduce_236(val, _values, result)
                      result =  [ val[0] ]

    result
end

def _reduce_237(val, _values, result)
                      result = [ val[0], *val[2].concat(val[3]) ]

    result
end

def _reduce_238(val, _values, result)
                      result = [ val[0], val[2] ]

    result
end

def _reduce_239(val, _values, result)
                      result =  [ val[0],
                                  @builder.splat(val[2], val[3]),
                                  *val[4] ]

    result
end

def _reduce_240(val, _values, result)
                      result =  [ val[0],
                                  *val[2].
                                    push(@builder.splat(val[4], val[5])).
                                    concat(val[6]) ]

    result
end

def _reduce_241(val, _values, result)
                      result =  [ @builder.associate(nil, val[0], nil),
                                  *val[1] ]

    result
end

def _reduce_242(val, _values, result)
                      result =  [ @builder.associate(nil, val[0], nil),
                                  @builder.splat(val[2], val[3]),
                                  *val[4] ]

    result
end

def _reduce_243(val, _values, result)
                      result =  [ val[0],
                                  @builder.associate(nil, val[2], nil),
                                  *val[3] ]

    result
end

def _reduce_244(val, _values, result)
                      result =  [ val[0],
                                  *val[2].
                                    push(@builder.associate(nil, val[4], nil)).
                                    concat(val[5]) ]

    result
end

def _reduce_245(val, _values, result)
                      result =  [ val[0],
                                  @builder.associate(nil, val[2], nil),
                                  @builder.splat(val[4], val[5]),
                                  *val[6] ]

    result
end

def _reduce_246(val, _values, result)
                      result =  [ val[0],
                                  *val[2].
                                    push(@builder.associate(nil, val[4], nil)).
                                    push(@builder.splat(val[6], val[7])).
                                    concat(val[8]) ]

    result
end

def _reduce_247(val, _values, result)
                      result =  [ @builder.splat(val[0], val[1]),
                                  *val[2] ]

    result
end

def _reduce_248(val, _values, result)
                      result =  [ val[0] ]

    result
end

def _reduce_249(val, _values, result)
                      result = @lexer.cmdarg.dup
                      @lexer.cmdarg.push(true)

    result
end

def _reduce_250(val, _values, result)
                      @lexer.cmdarg = val[0]

                      result = val[1]

    result
end

def _reduce_251(val, _values, result)
                      result = [ nil, val[0], nil ]

    result
end

def _reduce_252(val, _values, result)
                      @lexer.state = :expr_endarg

    result
end

def _reduce_253(val, _values, result)
                      result = [ val[0], [], val[2] ]

    result
end

def _reduce_254(val, _values, result)
                      @lexer.state = :expr_endarg

    result
end

def _reduce_255(val, _values, result)
                      result = [ val[0], val[1], val[3] ]

    result
end

def _reduce_256(val, _values, result)
                      result = @builder.block_pass(val[0], val[1])

    result
end

def _reduce_257(val, _values, result)
                      result = [ val[1] ]

    result
end

def _reduce_258(val, _values, result)
                      result = []

    result
end

def _reduce_259(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_260(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_261(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_262(val, _values, result)
                      result = val[0] << @builder.splat(val[2], val[3])

    result
end

def _reduce_263(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

# reduce 264 omitted

# reduce 265 omitted

# reduce 266 omitted

# reduce 267 omitted

# reduce 268 omitted

# reduce 269 omitted

# reduce 270 omitted

# reduce 271 omitted

def _reduce_272(val, _values, result)
                      result = @builder.call_method(nil, nil, val[0])

    result
end

def _reduce_273(val, _values, result)
                      result = @builder.begin_keyword(val[0], val[1], val[2])

    result
end

def _reduce_274(val, _values, result)
                      @lexer.state = :expr_endarg

    result
end

def _reduce_275(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[4])

    result
end

def _reduce_276(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_277(val, _values, result)
                      result = @builder.const_fetch(val[0], val[1], val[2])

    result
end

def _reduce_278(val, _values, result)
                      result = @builder.const_global(val[0], val[1])

    result
end

def _reduce_279(val, _values, result)
                      result = @builder.index(val[0], val[1], val[2], val[3])

    result
end

def _reduce_280(val, _values, result)
                      result = @builder.array(val[0], val[1], val[2])

    result
end

def _reduce_281(val, _values, result)
                      result = @builder.associate(val[0], val[1], val[2])

    result
end

def _reduce_282(val, _values, result)
                      result = @builder.keyword_cmd(:return, val[0])

    result
end

def _reduce_283(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0], val[1], val[2], val[3])

    result
end

def _reduce_284(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0], val[1], [], val[2])

    result
end

def _reduce_285(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0])

    result
end

def _reduce_286(val, _values, result)
                      result = @builder.keyword_cmd(:defined?, val[0],
                                                    val[2], [ val[3] ], val[4])

    result
end

def _reduce_287(val, _values, result)
                      method_call = @builder.call_method(nil, nil, val[0])

                      begin_t, args, body, end_t = val[1]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

# reduce 288 omitted

def _reduce_289(val, _values, result)
                      begin_t, args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, args, body, end_t)

    result
end

def _reduce_290(val, _values, result)
                      else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  val[3], else_t,
                                                  else_,  val[5])

    result
end

def _reduce_291(val, _values, result)
                      else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  else_,  else_t,
                                                  val[3], val[5])

    result
end

def _reduce_292(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_293(val, _values, result)
                      @lexer.cond.pop

    result
end

def _reduce_294(val, _values, result)
                      result = @builder.loop(:while, val[0], val[2], val[3],
                                             val[5], val[6])

    result
end

def _reduce_295(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_296(val, _values, result)
                      @lexer.cond.pop

    result
end

def _reduce_297(val, _values, result)
                      result = @builder.loop(:until, val[0], val[2], val[3],
                                             val[5], val[6])

    result
end

def _reduce_298(val, _values, result)
                      when_bodies       = val[3][0..-2]
                      else_t, else_body = val[3][-1]

                      result = @builder.case(val[0], val[1],
                                             when_bodies, else_t, else_body,
                                             val[4])

    result
end

def _reduce_299(val, _values, result)
                      when_bodies       = val[2][0..-2]
                      else_t, else_body = val[2][-1]

                      result = @builder.case(val[0], nil,
                                             when_bodies, else_t, else_body,
                                             val[3])

    result
end

def _reduce_300(val, _values, result)
                      result = @builder.case(val[0], nil,
                                             [], val[2], val[3],
                                             val[4])

    result
end

def _reduce_301(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_302(val, _values, result)
                      @lexer.cond.pop

    result
end

def _reduce_303(val, _values, result)
                      result = @builder.for(val[0], val[1],
                                            val[2], val[4],
                                            val[5], val[7], val[8])

    result
end

def _reduce_304(val, _values, result)
                      local_push
                      @context.in_class = true

    result
end

def _reduce_305(val, _values, result)
                      k_class, ctx = val[0]
                      if @context.in_def
                        diagnostic :error, :class_in_def, nil, k_class
                      end

                      lt_t, superclass = val[2]
                      result = @builder.def_class(k_class, val[1],
                                                  lt_t, superclass,
                                                  val[4], val[5])

                      local_pop
                      @context.in_class = ctx.in_class

    result
end

def _reduce_306(val, _values, result)
                      @context.in_def = false
                      @context.in_class = false
                      local_push

    result
end

def _reduce_307(val, _values, result)
                      k_class, ctx = val[0]
                      result = @builder.def_sclass(k_class, val[1], val[2],
                                                   val[5], val[6])

                      local_pop
                      @context.in_def = ctx.in_def
                      @context.in_class = ctx.in_class

    result
end

def _reduce_308(val, _values, result)
                      @context.in_class = true
                      local_push

    result
end

def _reduce_309(val, _values, result)
                      k_mod, ctx = val[0]
                      if @context.in_def
                        diagnostic :error, :module_in_def, nil, k_mod
                      end

                      result = @builder.def_module(k_mod, val[1],
                                                   val[3], val[4])

                      local_pop
                      @context.in_class = ctx.in_class

    result
end

def _reduce_310(val, _values, result)
                      local_push
                      result = context.dup
                      @context.in_def = true

    result
end

def _reduce_311(val, _values, result)
                      result = @builder.def_method(val[0], val[1],
                                  val[3], val[4], val[5])

                      local_pop
                      @context.in_def = val[2].in_def

    result
end

def _reduce_312(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_313(val, _values, result)
                      local_push
                      result = context.dup
                      @context.in_def = true

    result
end

def _reduce_314(val, _values, result)
                      result = @builder.def_singleton(val[0], val[1], val[2],
                                  val[4], val[6], val[7], val[8])

                      local_pop
                      @context.in_def = val[5].in_def

    result
end

def _reduce_315(val, _values, result)
                      result = @builder.keyword_cmd(:break, val[0])

    result
end

def _reduce_316(val, _values, result)
                      result = @builder.keyword_cmd(:next, val[0])

    result
end

def _reduce_317(val, _values, result)
                      result = @builder.keyword_cmd(:redo, val[0])

    result
end

def _reduce_318(val, _values, result)
                      result = @builder.keyword_cmd(:retry, val[0])

    result
end

# reduce 319 omitted

def _reduce_320(val, _values, result)
                      result = [ val[0], @context.dup ]

    result
end

def _reduce_321(val, _values, result)
                      result = [ val[0], @context.dup ]

    result
end

# reduce 322 omitted

# reduce 323 omitted

# reduce 324 omitted

def _reduce_325(val, _values, result)
                      result = val[1]

    result
end

# reduce 326 omitted

# reduce 327 omitted

# reduce 328 omitted

# reduce 329 omitted

def _reduce_330(val, _values, result)
                      else_t, else_ = val[4]
                      result = [ val[0],
                                 @builder.condition(val[0], val[1], val[2],
                                                    val[3], else_t,
                                                    else_,  nil),
                               ]

    result
end

# reduce 331 omitted

def _reduce_332(val, _values, result)
                      result = val

    result
end

# reduce 333 omitted

# reduce 334 omitted

def _reduce_335(val, _values, result)
                      result = [ @builder.arg_expr(val[0]) ]

    result
end

def _reduce_336(val, _values, result)
                      result = val[0] << @builder.arg_expr(val[2])

    result
end

# reduce 337 omitted

# reduce 338 omitted

def _reduce_339(val, _values, result)
                      result =  val[0].
                                  push(@builder.blockarg_expr(val[2], val[3]))

    result
end

def _reduce_340(val, _values, result)
                      result =  val[0].
                                  push(@builder.restarg_expr(val[2], val[3])).
                                  push(@builder.blockarg_expr(val[5], val[6]))

    result
end

def _reduce_341(val, _values, result)
                      result =  val[0].
                                  push(@builder.restarg_expr(val[2])).
                                  push(@builder.blockarg_expr(val[4], val[5]))

    result
end

def _reduce_342(val, _values, result)
                      result =  val[0].
                                  push(@builder.restarg_expr(val[2], val[3]))

    result
end

def _reduce_343(val, _values, result)
                      result =  val[0].
                                  push(@builder.restarg_expr(val[2]))

    result
end

def _reduce_344(val, _values, result)
                      result =  [ @builder.restarg_expr(val[0], val[1]),
                                  @builder.blockarg_expr(val[3], val[4]) ]

    result
end

def _reduce_345(val, _values, result)
                      result =  [ @builder.restarg_expr(val[0]),
                                  @builder.blockarg_expr(val[2], val[3]) ]

    result
end

def _reduce_346(val, _values, result)
                      result =  [ @builder.restarg_expr(val[0], val[1]) ]

    result
end

def _reduce_347(val, _values, result)
                      result =  [ @builder.restarg_expr(val[0]) ]

    result
end

def _reduce_348(val, _values, result)
                      result =  [ @builder.blockarg_expr(val[0], val[1]) ]

    result
end

def _reduce_349(val, _values, result)
                      result = @builder.args(nil, [], nil)

    result
end

def _reduce_350(val, _values, result)
                      result = @builder.args(val[0], [], val[1])

    result
end

def _reduce_351(val, _values, result)
                      result = @builder.args(val[0], [], val[0])

    result
end

def _reduce_352(val, _values, result)
                      result = @builder.args(val[0], val[1], val[2], false)

    result
end

def _reduce_353(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_354(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_355(val, _values, result)
                      begin_t, block_args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, block_args, body, end_t)

    result
end

def _reduce_356(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_357(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_358(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.call_method(nil, nil, val[0],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_359(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_360(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_361(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2])

    result
end

def _reduce_362(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.keyword_cmd(:super, val[0],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_363(val, _values, result)
                      result = @builder.keyword_cmd(:zsuper, val[0])

    result
end

def _reduce_364(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_365(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_366(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_367(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_368(val, _values, result)
                      result = [ @builder.when(val[0], val[1], val[2], val[3]),
                                 *val[4] ]

    result
end

# reduce 369 omitted

def _reduce_370(val, _values, result)
                      result = val[0] << @builder.splat(val[2], val[3])

    result
end

def _reduce_371(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

def _reduce_372(val, _values, result)
                      result = [ val[0] ]

    result
end

# reduce 373 omitted

def _reduce_374(val, _values, result)
                      assoc_t, exc_var = val[2]

                      if val[1]
                        exc_list = @builder.array(nil, val[1], nil)
                      end

                      result = [ @builder.rescue_body(val[0],
                                      exc_list, assoc_t, exc_var,
                                      val[3], val[4]),
                                 *val[5] ]

    result
end

def _reduce_375(val, _values, result)
                      result = []

    result
end

def _reduce_376(val, _values, result)
                      result = [ val[0] ]

    result
end

# reduce 377 omitted

# reduce 378 omitted

def _reduce_379(val, _values, result)
                      result = [ val[0], val[1] ]

    result
end

# reduce 380 omitted

def _reduce_381(val, _values, result)
                      result = [ val[0], val[1] ]

    result
end

# reduce 382 omitted

# reduce 383 omitted

# reduce 384 omitted

# reduce 385 omitted

def _reduce_386(val, _values, result)
                      result = @builder.string_compose(nil, val[0], nil)

    result
end

def _reduce_387(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_388(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_389(val, _values, result)
                      result = @builder.string_compose(val[0], val[1], val[2])

    result
end

def _reduce_390(val, _values, result)
                      result = @builder.string(val[0])

    result
end

def _reduce_391(val, _values, result)
                      result = @builder.xstring_compose(val[0], val[1], val[2])

    result
end

def _reduce_392(val, _values, result)
                      opts   = @builder.regexp_options(val[3])
                      result = @builder.regexp_compose(val[0], val[1], val[2], opts)

    result
end

def _reduce_393(val, _values, result)
                      result = @builder.words_compose(val[0], val[1], val[2])

    result
end

def _reduce_394(val, _values, result)
                      result = []

    result
end

def _reduce_395(val, _values, result)
                      result = val[0] << @builder.word(val[1])

    result
end

def _reduce_396(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_397(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_398(val, _values, result)
                      result = @builder.words_compose(val[0], val[1], val[2])

    result
end

def _reduce_399(val, _values, result)
                      result = []

    result
end

def _reduce_400(val, _values, result)
                      result = val[0] << @builder.string_internal(val[1])

    result
end

def _reduce_401(val, _values, result)
                      result = []

    result
end

def _reduce_402(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_403(val, _values, result)
                      result = []

    result
end

def _reduce_404(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_405(val, _values, result)
                      result = @builder.string_internal(val[0])

    result
end

def _reduce_406(val, _values, result)
                      result = val[1]

    result
end

def _reduce_407(val, _values, result)
                      @lexer.cond.push(false)
                      @lexer.cmdarg.push(false)

    result
end

def _reduce_408(val, _values, result)
                      @lexer.cond.lexpop
                      @lexer.cmdarg.lexpop

                      result = @builder.begin(val[0], val[2], val[3])

    result
end

def _reduce_409(val, _values, result)
                      result = @builder.gvar(val[0])

    result
end

def _reduce_410(val, _values, result)
                      result = @builder.ivar(val[0])

    result
end

def _reduce_411(val, _values, result)
                      result = @builder.cvar(val[0])

    result
end

# reduce 412 omitted

def _reduce_413(val, _values, result)
                      result = @builder.symbol(val[0])

    result
end

def _reduce_414(val, _values, result)
                      result = @builder.symbol_compose(val[0], val[1], val[2])

    result
end

def _reduce_415(val, _values, result)
                      result = @builder.integer(val[0])

    result
end

def _reduce_416(val, _values, result)
                      result = @builder.float(val[0])

    result
end

def _reduce_417(val, _values, result)
                      num = @builder.integer(val[1])
                      if @builder.respond_to? :negate
                        # AST builder interface compatibility
                        result = @builder.negate(val[0], num)
                      else
                        result = @builder.unary_num(val[0], num)
                      end

    result
end

def _reduce_418(val, _values, result)
                      num = @builder.float(val[1])
                      if @builder.respond_to? :negate
                        # AST builder interface compatibility
                        result = @builder.negate(val[0], num)
                      else
                        result = @builder.unary_num(val[0], num)
                      end

    result
end

def _reduce_419(val, _values, result)
                      result = @builder.ident(val[0])

    result
end

def _reduce_420(val, _values, result)
                      result = @builder.ivar(val[0])

    result
end

def _reduce_421(val, _values, result)
                      result = @builder.gvar(val[0])

    result
end

def _reduce_422(val, _values, result)
                      result = @builder.cvar(val[0])

    result
end

def _reduce_423(val, _values, result)
                      result = @builder.const(val[0])

    result
end

def _reduce_424(val, _values, result)
                      result = @builder.nil(val[0])

    result
end

def _reduce_425(val, _values, result)
                      result = @builder.self(val[0])

    result
end

def _reduce_426(val, _values, result)
                      result = @builder.true(val[0])

    result
end

def _reduce_427(val, _values, result)
                      result = @builder.false(val[0])

    result
end

def _reduce_428(val, _values, result)
                      result = @builder.__FILE__(val[0])

    result
end

def _reduce_429(val, _values, result)
                      result = @builder.__LINE__(val[0])

    result
end

def _reduce_430(val, _values, result)
                      result = @builder.accessible(val[0])

    result
end

def _reduce_431(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_432(val, _values, result)
                      result = @builder.nth_ref(val[0])

    result
end

def _reduce_433(val, _values, result)
                      result = @builder.back_ref(val[0])

    result
end

def _reduce_434(val, _values, result)
                      result = nil

    result
end

def _reduce_435(val, _values, result)
                      result = [ val[0], val[1] ]

    result
end

def _reduce_436(val, _values, result)
                      yyerrok
                      result = nil

    result
end

def _reduce_437(val, _values, result)
                      result = @builder.args(val[0], val[1], val[3])

                      @lexer.state = :expr_beg

    result
end

def _reduce_438(val, _values, result)
                      result = @builder.args(nil, val[0], nil)

    result
end

def _reduce_439(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_440(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_441(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_442(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_443(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_444(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_445(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_446(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_447(val, _values, result)
                      result = []

    result
end

def _reduce_448(val, _values, result)
                      diagnostic :error, :argument_const, nil, val[0]

    result
end

def _reduce_449(val, _values, result)
                      diagnostic :error, :argument_ivar, nil, val[0]

    result
end

def _reduce_450(val, _values, result)
                      diagnostic :error, :argument_gvar, nil, val[0]

    result
end

def _reduce_451(val, _values, result)
                      diagnostic :error, :argument_cvar, nil, val[0]

    result
end

def _reduce_452(val, _values, result)
                      @static_env.declare val[0][0]

                      result = @builder.arg(val[0])

    result
end

def _reduce_453(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_454(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_455(val, _values, result)
                      @static_env.declare val[0][0]

                      result = @builder.optarg(val[0], val[1], val[2])

    result
end

def _reduce_456(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_457(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 458 omitted

# reduce 459 omitted

def _reduce_460(val, _values, result)
                      @static_env.declare val[1][0]

                      result = [ @builder.restarg(val[0], val[1]) ]

    result
end

def _reduce_461(val, _values, result)
                      result = [ @builder.restarg(val[0]) ]

    result
end

# reduce 462 omitted

# reduce 463 omitted

def _reduce_464(val, _values, result)
                      @static_env.declare val[1][0]

                      result = @builder.blockarg(val[0], val[1])

    result
end

def _reduce_465(val, _values, result)
                      result = [ val[1] ]

    result
end

def _reduce_466(val, _values, result)
                      result = []

    result
end

# reduce 467 omitted

def _reduce_468(val, _values, result)
                      result = val[1]

    result
end

def _reduce_469(val, _values, result)
                      result = []

    result
end

def _reduce_470(val, _values, result)
                      result = val[0]

    result
end

def _reduce_471(val, _values, result)
                      result = @builder.pair_list_18(val[0])

    result
end

def _reduce_472(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_473(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_474(val, _values, result)
                      result = @builder.pair(val[0], val[1], val[2])

    result
end

# reduce 475 omitted

# reduce 476 omitted

# reduce 477 omitted

# reduce 478 omitted

# reduce 479 omitted

# reduce 480 omitted

# reduce 481 omitted

# reduce 482 omitted

# reduce 483 omitted

# reduce 484 omitted

# reduce 485 omitted

# reduce 486 omitted

# reduce 487 omitted

# reduce 488 omitted

# reduce 489 omitted

# reduce 490 omitted

# reduce 491 omitted

# reduce 492 omitted

# reduce 493 omitted

def _reduce_494(val, _values, result)
                      yyerrok

    result
end

# reduce 495 omitted

# reduce 496 omitted

# reduce 497 omitted

def _reduce_498(val, _values, result)
                      result = nil

    result
end

def _reduce_none(val, _values, result)
  val[0]
end

  end   # class Ruby18
end   # module Parser
