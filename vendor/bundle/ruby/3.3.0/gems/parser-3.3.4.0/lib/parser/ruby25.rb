# -*- encoding:utf-8; warn-indent:false; frozen_string_literal: true  -*-
#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.7.3
# from Racc grammar file "ruby25.y".
#

require 'racc/parser.rb'


require_relative '../parser'

module Parser
  class Ruby25 < Parser::Base


  def version
    25
  end

  def default_encoding
    Encoding::UTF_8
  end

  def local_push
    @static_env.extend_static
    @lexer.cmdarg.push(false)
    @lexer.cond.push(false)
  end

  def local_pop
    @static_env.unextend
    @lexer.cmdarg.pop
    @lexer.cond.pop
  end
##### State transition tables begin ###

racc_action_table = [
  -486,   214,   215,   214,   215,   222,  -102,  -486,  -486,  -486,
   113,   549,  -486,  -486,  -486,   220,  -486,   276,   224,   590,
   262,   263,   269,   549,  -486,   592,  -486,  -486,  -486,   276,
   562,   276,  -500,   126,   563,  -103,  -486,  -486,   549,  -486,
  -486,  -486,  -486,  -486,   549,   549,  -102,  -103,  -110,  -110,
   -88,  -109,  -101,   791,  -109,   556,   261,   260,   276,   225,
   -74,   211,  -110,   212,  -105,  -107,  -486,  -486,  -486,  -486,
  -486,  -486,  -486,  -486,  -486,  -486,  -486,  -486,  -486,  -486,
   221,   271,  -486,  -486,  -486,   589,  -486,  -486,   705,  -102,
  -486,   591,   872,  -486,  -486,   225,  -486,   225,  -486,   213,
  -486,   216,  -486,  -486,   275,  -486,  -486,  -486,  -486,  -486,
  -105,  -486,  -489,  -486,  -107,   -93,   275,  -104,   275,  -489,
  -489,  -489,   271,  -106,  -489,  -489,  -489,  -486,  -489,   117,
  -486,  -486,  -486,  -486,   116,  -486,  -489,  -486,  -489,  -489,
  -489,   117,  -486,  -486,   -94,   275,   116,   -96,  -489,  -489,
  -108,  -489,  -489,  -489,  -489,  -489,   117,  -104,  -101,   825,
  -100,   116,   117,   117,  -102,  -103,  -110,   116,   116,  -102,
  -103,  -110,  -109,   -96,   -98,  -106,   779,  -109,  -489,  -489,
  -489,  -489,  -489,  -489,  -489,  -489,  -489,  -489,  -489,  -489,
  -489,  -489,   117,   265,  -489,  -489,  -489,   116,  -489,  -489,
  -583,   871,  -489,   324,   -98,  -489,  -489,   325,  -489,  -108,
  -489,   778,  -489,   -96,  -489,  -489,   225,  -489,  -489,  -489,
  -489,  -489,  -296,  -489,   395,  -489,   -95,  -584,  -105,  -296,
  -296,  -296,  -107,  -105,   779,  -296,  -296,  -107,  -296,  -489,
  -583,  -106,  -489,  -489,  -489,  -489,  -106,  -489,   408,  -489,
   214,   215,  -501,   -96,  -489,  -489,   -96,   457,  -296,  -296,
   -95,  -296,  -296,  -296,  -296,  -296,   -96,   459,  -108,   778,
   -98,  -486,   -97,  -108,  -584,  -104,   214,   215,  -486,   682,
  -104,   679,   678,   677,   -97,   680,   214,   215,  -296,  -296,
  -296,  -296,  -296,  -296,  -296,  -296,  -296,  -296,  -296,  -296,
  -296,  -296,   460,   458,  -296,  -296,  -296,  -489,   615,  -103,
   -98,  -500,  -296,   -98,  -489,  -296,    95,    96,   -99,   577,
  -296,   224,  -296,   -98,  -296,  -296,   -95,  -296,  -296,  -296,
  -296,  -296,  -496,  -296,  -587,  -296,  -486,  -583,   -97,  -496,
   405,  -587,  -587,  -587,   225,   407,   406,  -587,  -587,  -296,
  -587,   117,  -296,  -296,   488,   -99,   116,  -296,   -93,  -587,
  -110,   890,   214,   215,  -296,  -108,   -95,  -580,  -102,   -95,
  -587,  -587,  -489,  -587,  -587,  -587,  -587,  -587,   -97,   -95,
   117,   -97,   497,   579,   578,   116,   682,   577,   679,   678,
   677,   -97,   680,   737,    97,    98,   499,  -496,   577,   222,
  -587,  -587,  -587,  -587,  -587,  -587,  -587,  -587,  -587,  -587,
  -587,  -587,  -587,  -587,   526,   501,  -587,  -587,  -587,  -486,
   616,    95,    96,   577,  -587,   117,  -486,  -587,   117,   765,
   116,  -580,  -587,   116,  -587,  -486,  -587,  -587,  -501,  -587,
  -587,  -587,  -587,  -587,   766,  -587,  -587,  -587,   577,   126,
  -495,   579,   578,   575,  -580,   577,  -110,  -495,   -73,  -581,
   577,  -587,   579,   578,  -587,  -587,  -587,   -97,   983,  -587,
   225,  -109,   739,  -587,  -587,  -587,  -587,  -106,  -587,  -587,
  -587,   512,  -587,   513,  -486,   -94,  -105,   579,   578,   575,
  -497,  -587,  -587,  -587,  -587,  -103,   520,  -497,   280,    97,
    98,  -107,  -587,  -587,   643,  -587,  -587,  -587,  -587,  -587,
   526,  -489,   579,   578,   580,  -495,   577,   225,  -489,   579,
   578,   582,   577,  -581,   579,   578,   584,  -489,   539,   822,
   791,   538,  -587,  -587,  -587,  -587,  -587,  -587,  -587,  -587,
  -587,  -587,  -587,  -587,  -587,  -587,  -581,   271,  -587,  -587,
  -587,   523,   767,  -587,   220,  -497,  -587,  -100,  -494,  -587,
  -587,   219,  -587,   527,  -587,  -494,  -587,  -109,  -587,  -587,
   217,  -587,  -587,  -587,  -587,  -587,  -489,  -587,  -587,  -587,
   579,   578,   588,  -491,  -334,  -492,   579,   578,   593,   245,
  -491,  -334,  -492,  -587,   245,   610,  -587,  -587,  -587,  -587,
  -334,  -587,   610,  -587,  -296,   611,   844,   611,  -587,  -106,
   117,  -296,  -296,  -296,   225,   116,  -296,  -296,  -296,   221,
  -296,   242,   542,  -494,   543,   244,   243,  -493,   220,   245,
  -296,  -296,  -296,   539,  -493,   269,   541,   501,   752,   752,
  -296,  -296,   -96,  -296,  -296,  -296,  -296,  -296,  -491,  -334,
  -492,   220,  -105,  -498,   -98,   698,   697,   556,   455,  -105,
  -498,   242,  -107,  -104,  -107,   244,   243,   456,   397,  -498,
  -296,  -296,  -296,  -296,  -296,  -296,  -296,  -296,  -296,  -296,
  -296,  -296,  -296,  -296,   117,   -95,  -296,  -296,  -296,   116,
   768,  -296,  -493,   221,  -296,  -104,  -104,  -296,  -296,   560,
  -296,   561,  -296,   569,  -296,   539,  -296,  -296,   541,  -296,
  -296,  -296,  -296,  -296,  -296,  -296,   221,  -296,  -498,   822,
   791,  -296,  -296,  -296,   594,   597,   539,  -296,  -296,   541,
  -296,  -296,   245,  -269,  -296,  -296,  -296,  -296,   599,  -296,
    83,  -296,  -587,   600,   604,   225,  -296,  -108,   255,   256,
  -296,  -296,    84,  -296,  -296,  -296,  -296,  -296,   225,  -499,
   608,   999,    85,   220,   242,   609,  -499,   271,   244,   243,
   519,   240,   241,   620,   245,  -499,   245,   245,   245,   517,
  -296,  -296,  -296,  -296,  -296,  -296,  -296,  -296,  -296,  -296,
  -296,  -296,  -296,  -296,  -587,  -287,  -296,  -296,  -296,   225,
   615,  -587,  -287,   225,  -296,   225,  -583,  -296,   -88,   646,
  -587,  -287,  -296,   225,  -296,   532,  -296,  -296,   657,  -296,
  -296,  -296,  -296,  -296,  -499,  -296,  -587,  -296,   221,  -587,
   662,   663,   225,  -587,  -587,  -587,   687,   665,   701,  -587,
  -587,  -296,  -587,   706,  -296,  -296,   690,  -296,   220,  -296,
   707,  -587,   556,   709,   726,   529,  -296,  -108,   736,  -587,
  -287,   740,  -587,  -587,   456,  -587,  -587,  -587,  -587,  -587,
   682,   220,   679,   678,   677,   741,   680,  -270,   559,   698,
   697,   753,   488,   488,   691,   731,   732,   557,   225,   733,
   111,   112,  -587,  -587,  -587,  -587,  -587,  -587,  -587,  -587,
  -587,  -587,  -587,  -587,  -587,  -587,   770,   811,  -587,  -587,
  -587,   220,   616,   221,   220,   771,  -587,   265,   565,  -587,
   776,   603,   781,   499,  -587,   501,  -587,   567,  -587,  -587,
   601,  -587,  -587,  -587,  -587,  -587,   221,  -587,  -587,  -587,
   682,   657,   679,   678,   677,   687,   680,  -297,   120,   121,
   122,   123,   124,  -587,  -297,   690,  -587,  -587,  -416,  -587,
   225,  -587,   271,  -297,   271,  -416,  -416,  -416,  -587,  -106,
  -416,  -416,  -416,   657,  -416,   245,   221,   685,   791,   221,
   799,   245,   802,  -416,  -416,  -416,   695,   694,   698,   697,
   803,   805,   807,   691,  -416,  -416,   809,  -416,  -416,  -416,
  -416,  -416,   682,   817,   679,   678,   677,  -297,   680,   818,
   819,   791,  -297,   242,  -297,   824,   225,   244,   243,   225,
   240,   241,   225,  -297,  -416,  -416,  -416,  -416,  -416,  -416,
  -416,  -416,  -416,  -416,  -416,  -416,  -416,  -416,   833,   811,
  -416,  -416,  -416,  -271,   225,  -416,   843,   271,  -416,   847,
   657,  -416,  -416,   864,  -416,  -269,  -416,   868,  -416,   225,
  -416,  -416,   888,  -416,  -416,  -416,  -416,  -416,  -303,  -416,
  -416,  -416,  -297,   225,   892,  -303,  -303,  -303,   894,   897,
  -303,  -303,  -303,   220,  -303,  -416,   898,   901,  -416,  -416,
   969,  -416,   225,  -416,  -303,  -303,   905,  -272,   907,   567,
  -416,   802,   910,   912,  -303,  -303,   914,  -303,  -303,  -303,
  -303,  -303,   916,   225,   918,  -296,   919,  -296,   932,   802,
   934,   936,  -296,   938,  -296,   940,   940,  -584,   225,  -584,
   946,  -296,   952,  -296,  -303,  -303,  -303,  -303,  -303,  -303,
  -303,  -303,  -303,  -303,  -303,  -303,  -303,  -303,   221,   726,
  -303,  -303,  -303,   963,   970,  -303,   975,   280,  -303,   985,
   802,  -303,  -303,   989,  -303,   991,  -303,   993,  -303,   995,
  -303,  -303,   995,  -303,  -303,  -303,  -303,  -303,  -288,  -303,
  -296,  -303,  -296,   662,  1008,  -288,  -288,  -288,  1009,  1010,
  -288,  -288,  -288,   220,  -288,  -303,   940,   940,  -303,  -303,
   974,  -303,   940,  -303,  -288,  -288,  -288,  1015,   985,   972,
  -303,  1018,  -584,  -583,  -288,  -288,   225,  -288,  -288,  -288,
  -288,  -288,   966,   220,   679,   678,   677,   985,   680,   682,
   969,   679,   678,   677,   966,   680,   679,   678,   677,   567,
   680,  1027,   995,   995,  -288,  -288,  -288,  -288,  -288,  -288,
  -288,  -288,  -288,  -288,  -288,  -288,  -288,  -288,   221,   687,
  -288,  -288,  -288,   995,   940,  -288,   811,   985,  -288,   690,
   995,  -288,  -288,   nil,  -288,   814,  -288,   nil,  -288,   nil,
  -288,  -288,   nil,  -288,  -288,  -288,  -288,  -288,   221,  -288,
   nil,  -288,   682,   nil,   679,   678,   677,   nil,   680,   nil,
   nil,   nil,   698,   697,   nil,  -288,   nil,   691,  -288,  -288,
  -288,  -288,   nil,  -288,  -252,  -288,   nil,   nil,   nil,   nil,
  -288,  -252,  -252,  -252,   nil,   nil,  -252,  -252,  -252,   811,
  -252,   682,   nil,   679,   678,   677,   nil,   680,   814,  -252,
  -252,  -252,   682,   nil,   679,   678,   677,   nil,   680,   nil,
  -252,  -252,   nil,  -252,  -252,  -252,  -252,  -252,   682,   nil,
   679,   678,   677,   682,   680,   679,   678,   677,   811,   680,
   120,   121,   122,   123,   124,   nil,   nil,   945,   nil,   811,
  -252,  -252,  -252,  -252,  -252,  -252,  -252,  -252,  -252,  -252,
  -252,  -252,  -252,  -252,   nil,   811,  -252,  -252,  -252,   nil,
   811,  -252,   nil,   271,  -252,   nil,   nil,  -252,  -252,   nil,
  -252,   nil,  -252,   nil,  -252,   nil,  -252,  -252,   nil,  -252,
  -252,  -252,  -252,  -252,   nil,  -252,  -252,  -252,   682,   nil,
   679,   678,   677,   687,   680,   120,   121,   122,   123,   124,
   nil,  -252,   nil,   690,  -252,  -252,  -588,  -252,   nil,  -252,
   nil,   nil,   nil,  -588,  -588,  -588,  -252,   nil,  -588,  -588,
  -588,   nil,  -588,   245,   nil,   685,   120,   121,   122,   123,
   124,  -588,  -588,  -588,  -588,   nil,   698,   697,   nil,   255,
   256,   691,  -588,  -588,   nil,  -588,  -588,  -588,  -588,  -588,
   nil,   nil,   nil,   nil,   nil,   242,   nil,   248,   nil,   244,
   243,   nil,   240,   241,   nil,   nil,   246,   nil,   247,   nil,
   nil,   nil,  -588,  -588,  -588,  -588,  -588,  -588,  -588,  -588,
  -588,  -588,  -588,  -588,  -588,  -588,   nil,   nil,  -588,  -588,
  -588,   245,   nil,  -588,   nil,   nil,  -588,   nil,   nil,  -588,
  -588,   nil,  -588,   nil,  -588,   nil,  -588,   nil,  -588,  -588,
   nil,  -588,  -588,  -588,  -588,  -588,   nil,  -588,  -588,  -588,
   nil,   nil,   nil,   242,   nil,   nil,   nil,   244,   243,   nil,
   240,   241,   nil,  -588,   nil,   nil,  -588,  -588,  -588,  -588,
   nil,  -588,  -589,  -588,   nil,   nil,   nil,   nil,  -588,  -589,
  -589,  -589,   nil,   nil,  -589,  -589,  -589,   245,  -589,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -589,  -589,  -589,
  -589,   nil,   nil,   255,   256,   nil,   nil,   nil,  -589,  -589,
   nil,  -589,  -589,  -589,  -589,  -589,   nil,   nil,   nil,   242,
   nil,   248,   nil,   244,   243,   nil,   240,   241,   nil,   nil,
   246,   nil,   247,   nil,   nil,   nil,   nil,   nil,  -589,  -589,
  -589,  -589,  -589,  -589,  -589,  -589,  -589,  -589,  -589,  -589,
  -589,  -589,   nil,   nil,  -589,  -589,  -589,   nil,   nil,  -589,
   nil,   nil,  -589,   nil,   nil,  -589,  -589,   nil,  -589,   nil,
  -589,   nil,  -589,   nil,  -589,  -589,   nil,  -589,  -589,  -589,
  -589,  -589,   nil,  -589,  -589,  -589,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -589,
   nil,   nil,  -589,  -589,  -589,  -589,   nil,  -589,  -252,  -589,
   nil,   nil,   nil,   nil,  -589,  -252,  -252,  -252,   nil,   nil,
  -252,  -252,  -252,   245,  -252,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,  -252,  -252,   nil,   nil,   nil,   nil,   255,
   256,   nil,   nil,   nil,  -252,  -252,   nil,  -252,  -252,  -252,
  -252,  -252,   nil,   nil,   nil,   242,   nil,   248,   nil,   244,
   243,   nil,   240,   241,   nil,   nil,   nil,   245,   249,   250,
   251,   252,   262,   263,   257,   258,   253,   254,   nil,   238,
   239,   nil,   nil,   255,   256,  -252,   nil,   nil,   nil,   nil,
   nil,   nil,  -252,   nil,   nil,   nil,   nil,   271,  -252,   242,
   nil,   248,   nil,   244,   243,   nil,   240,   241,   261,   260,
   246,   nil,   247,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
  -252,  -252,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   259,   nil,   nil,  -252,   nil,   nil,  -252,   nil,
   nil,   nil,   nil,  -252,     5,    75,    76,    72,     9,    58,
  -252,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   605,     8,    46,     7,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   245,
   249,   250,   251,   252,   262,   263,   257,   258,   253,   254,
   nil,   238,   239,   nil,   nil,   255,   256,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   242,   nil,   248,    45,   244,   243,   nil,   240,   241,
   261,   260,   246,    20,   247,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   259,   nil,  -244,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   605,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   245,
   249,   250,   251,   252,   262,   263,   257,   258,   253,   254,
   nil,   238,   239,   nil,   nil,   255,   256,   nil,    40,   nil,
   nil,   302,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   242,   nil,   248,    45,   244,   243,   nil,   240,   241,
   261,   260,   246,    20,   247,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   259,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,     5,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,     7,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   245,
   249,   250,   251,   252,   262,   263,   257,   258,   253,   254,
   nil,   238,   239,   nil,   nil,   255,   256,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   242,   nil,   248,    45,   244,   243,   nil,   240,   241,
   261,   260,   246,    20,   247,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   259,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   245,
   249,   250,   251,   252,   262,   263,   257,   258,   253,   254,
   nil,   238,   239,   nil,   nil,   255,   256,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   242,   nil,   248,    45,   244,   243,   nil,   240,   241,
   261,   260,   246,    20,   247,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   225,   259,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   245,
   249,   250,   251,   252,   262,   263,   257,   258,   253,   254,
   nil,   238,   239,   nil,   nil,   255,   256,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   242,   nil,   248,    45,   244,   243,   nil,   240,   241,
   261,   260,   246,    20,   247,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   259,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   245,
   249,   250,   251,   252,   262,   263,   257,   258,   253,   254,
   nil,   238,   239,   nil,   nil,   255,   256,   nil,    40,   nil,
   nil,   302,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   242,   nil,   248,    45,   244,   243,   nil,   240,   241,
   261,   260,   246,    20,   247,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   259,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   245,
   249,   250,   251,   252,   262,   263,   257,   258,   253,   254,
   nil,   238,   239,   nil,   nil,   255,   256,   nil,    40,   nil,
   nil,   302,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   242,   nil,   248,    45,   244,   243,   nil,   240,   241,
   261,   260,   246,    20,   247,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   259,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   245,
   249,   250,   251,   252,   262,   263,   257,   258,   253,   254,
   nil,  -608,  -608,   nil,   nil,   255,   256,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   242,   nil,   248,    45,   244,   243,   nil,   240,   241,
   261,   260,   246,    20,   247,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   245,
   249,   250,   251,   252,   262,   263,   257,   258,   253,   254,
   nil,  -608,  -608,   nil,   nil,   255,   256,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   242,   nil,   248,    45,   244,   243,   nil,   240,   241,
   261,   260,   246,    20,   247,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   245,
  -608,  -608,  -608,  -608,   262,   263,   nil,   nil,  -608,  -608,
   nil,   nil,   nil,   nil,   nil,   255,   256,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   242,   nil,   248,    45,   244,   243,   nil,   240,   241,
   261,   260,   246,    20,   247,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   245,
  -608,  -608,  -608,  -608,   262,   263,   nil,   nil,  -608,  -608,
   nil,   nil,   nil,   nil,   nil,   255,   256,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   242,   nil,   248,    45,   244,   243,   nil,   240,   241,
   261,   260,   246,    20,   247,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   245,
  -608,  -608,  -608,  -608,   262,   263,   nil,   nil,  -608,  -608,
   nil,   nil,   nil,   nil,   nil,   255,   256,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   242,   nil,   248,    45,   244,   243,   nil,   240,   241,
   261,   260,   246,    20,   247,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   245,
  -608,  -608,  -608,  -608,   262,   263,   nil,   nil,  -608,  -608,
   nil,   nil,   nil,   nil,   nil,   255,   256,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   242,   nil,   248,    45,   244,   243,   nil,   240,   241,
   261,   260,   246,    20,   247,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   245,
  -608,  -608,  -608,  -608,   262,   263,   nil,   nil,  -608,  -608,
   nil,   nil,   nil,   nil,   nil,   255,   256,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   242,   nil,   248,    45,   244,   243,   nil,   240,   241,
   261,   260,   246,    20,   247,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   245,
  -608,  -608,  -608,  -608,   262,   263,   nil,   nil,  -608,  -608,
   nil,   nil,   nil,   nil,   nil,   255,   256,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   242,   nil,   248,    45,   244,   243,   nil,   240,   241,
   261,   260,   246,    20,   247,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   245,
   249,   250,   251,   252,   262,   263,   nil,   nil,   253,   254,
   nil,   nil,   nil,   nil,   nil,   255,   256,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   242,   nil,   248,    45,   244,   243,   nil,   240,   241,
   261,   260,   246,    20,   247,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   245,
   249,   250,   251,   252,   262,   263,   257,   nil,   253,   254,
   nil,   nil,   nil,   nil,   nil,   255,   256,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   242,   nil,   248,    45,   244,   243,   nil,   240,   241,
   261,   260,   246,    20,   247,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   245,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   255,   256,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   242,   nil,   248,    45,   244,   243,   nil,   240,   241,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,   298,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   300,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    85,    97,    98,    75,    76,    72,     9,    58,   nil,
   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,   nil,
   nil,    77,    28,    27,   105,   104,   106,   107,   nil,   nil,
    19,   nil,   nil,   nil,   nil,   nil,     8,    46,     7,    10,
   109,   108,   110,    99,    57,   101,   100,   102,   nil,   103,
   111,   112,   nil,    95,    96,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,    35,
   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    20,   nil,   nil,   nil,   nil,    93,    83,    86,
    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,   nil,
    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,    58,
    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,
   nil,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,
    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,    30,    31,    73,    74,   nil,   nil,
   nil,   nil,   nil,    77,    28,    27,   105,   104,   106,   107,
   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,
   nil,   nil,   109,   108,   110,    99,    57,   101,   100,   102,
   292,   103,   111,   112,   nil,    95,    96,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   289,   nil,   287,   nil,    45,   nil,   nil,   293,   nil,
   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,
   290,    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,
   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,
    63,    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,    30,    31,    73,    74,   nil,
   nil,   nil,   nil,   nil,    77,    28,    27,   105,   104,   106,
   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   109,   108,   110,    99,    57,   101,   100,
   102,   292,   103,   111,   112,   nil,    95,    96,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,   289,   nil,   287,   nil,    45,   nil,   nil,   293,
   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,
    93,   290,    86,    87,   nil,    88,    90,    89,    91,   nil,
   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,
    72,    63,    58,    85,    97,    98,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,    30,    31,    73,    74,
   nil,   nil,   nil,   nil,   nil,    77,    28,    27,   105,   104,
   106,   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,
   nil,    46,   nil,   nil,   109,   108,   110,    99,    57,   101,
   100,   102,   292,   103,   111,   112,   nil,    95,    96,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   236,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,   289,   nil,   287,   nil,    45,   nil,   nil,
   293,   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,
   nil,    93,   290,    86,    87,   nil,    88,    90,    89,    91,
   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,    75,
    76,    72,    63,    58,    85,    97,    98,    64,    65,   nil,
   nil,   nil,    68,   nil,    66,    67,    69,   317,   318,    73,
    74,   nil,   nil,   nil,   nil,   nil,    77,   314,   320,   105,
   104,   106,   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,
   nil,   nil,   315,   nil,   nil,   109,   108,   110,    99,    57,
   101,   100,   102,   nil,   103,   111,   112,   nil,    95,    96,
   nil,   nil,   321,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   311,   nil,   nil,   307,   nil,   nil,    59,    60,
   nil,   nil,    61,   nil,   306,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    93,    83,    86,    87,   nil,    88,    90,    89,
    91,   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,
    75,    76,    72,    63,    58,    85,    97,    98,    64,    65,
   nil,   nil,   nil,    68,   nil,    66,    67,    69,   317,   318,
    73,    74,   nil,   nil,   nil,   nil,   nil,    77,   314,   320,
   105,   104,   106,   107,   nil,   nil,   237,   nil,   nil,   nil,
   nil,   nil,   nil,   315,   nil,   nil,   109,   108,   110,    99,
    57,   101,   100,   102,   nil,   103,   111,   112,   nil,    95,
    96,   nil,   nil,   321,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   311,   nil,   nil,   236,   nil,   nil,    59,
    60,   nil,   nil,    61,   nil,   nil,   682,   nil,   679,   678,
   677,   687,   680,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   690,   nil,    93,    83,    86,    87,   nil,    88,    90,
    89,    91,   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,
   nil,   323,   nil,   685,    63,   nil,    85,    97,    98,    75,
    76,    72,   nil,    58,   698,   697,   nil,    64,    65,   691,
   nil,   nil,    68,   nil,    66,    67,    69,   317,   318,    73,
    74,   nil,   nil,   nil,   nil,   nil,    77,   314,   320,   105,
   104,   106,   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,
   nil,   nil,    46,   nil,   nil,   109,   108,   110,    99,    57,
   101,   100,   102,   nil,   103,   111,   112,   nil,    95,    96,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,    59,    60,
   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,
   nil,   nil,    93,    83,    86,    87,   nil,    88,    90,    89,
    91,   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,
    75,    76,    72,    63,    58,    85,    97,    98,    64,    65,
   nil,   nil,   nil,    68,   nil,    66,    67,    69,   317,   318,
    73,    74,   nil,   nil,   nil,   nil,   nil,    77,   314,   320,
   105,   104,   106,   107,   nil,   nil,   237,   nil,   nil,   nil,
   nil,   nil,   nil,    46,   nil,   nil,   109,   108,   110,    99,
    57,   101,   100,   102,   nil,   103,   111,   112,   nil,    95,
    96,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,    59,
    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,
   nil,   nil,   nil,    93,    83,    86,    87,   nil,    88,    90,
    89,    91,   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,
   nil,    75,    76,    72,    63,    58,    85,    97,    98,    64,
    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,   317,
   318,    73,    74,   nil,   nil,   nil,   nil,   nil,    77,   314,
   320,   105,   104,   106,   107,   nil,   nil,   237,   nil,   nil,
   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,   108,   110,
    99,    57,   101,   100,   102,   nil,   103,   111,   112,   nil,
    95,    96,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,
    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,
   nil,   nil,   nil,   nil,    93,    83,    86,    87,   nil,    88,
    90,    89,    91,   nil,   nil,   nil,   nil,    84,    92,   nil,
   nil,   nil,   nil,   nil,   nil,    63,   nil,    85,    97,    98,
    75,    76,    72,     9,    58,   nil,   nil,   nil,    64,    65,
   nil,   nil,   nil,    68,   nil,    66,    67,    69,    30,    31,
    73,    74,   nil,   nil,   nil,   nil,   nil,    77,    28,    27,
   105,   104,   106,   107,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,     8,    46,   nil,    10,   109,   108,   110,    99,
    57,   101,   100,   102,   nil,   103,   111,   112,   nil,    95,
    96,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,    33,   nil,   nil,    59,
    60,   nil,   nil,    61,   nil,    35,   nil,   nil,   nil,    45,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    93,    83,    86,    87,   nil,    88,    90,
    89,    91,   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,
   nil,    75,    76,    72,    63,    58,    85,    97,    98,    64,
    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,   317,
   318,    73,    74,   nil,   nil,   nil,   nil,   nil,    77,   314,
   320,   105,   104,   106,   107,   nil,   nil,   237,   nil,   nil,
   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,   108,   110,
    99,    57,   101,   100,   102,   292,   103,   111,   112,   nil,
    95,    96,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,
    59,    60,   nil,   nil,    61,   nil,   289,   nil,   nil,   nil,
    45,   nil,   nil,   293,   nil,   nil,   nil,   nil,   nil,   235,
   nil,   nil,   nil,   nil,    93,   290,    86,    87,   nil,    88,
    90,    89,    91,   nil,   nil,   nil,   nil,    84,    92,   nil,
   nil,   nil,    75,    76,    72,    63,    58,    85,    97,    98,
    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,
   317,   318,    73,    74,   nil,   nil,   nil,   nil,   nil,    77,
   314,   320,   105,   104,   106,   107,   nil,   nil,   237,   nil,
   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,   108,
   110,    99,    57,   101,   100,   102,   292,   103,   111,   112,
   nil,    95,    96,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,
   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   293,   nil,   nil,   nil,   nil,   nil,
   235,   nil,   nil,   nil,   nil,    93,   290,    86,    87,   nil,
    88,    90,    89,    91,   nil,   nil,   nil,   nil,    84,    92,
   nil,   nil,   nil,    75,    76,    72,    63,    58,    85,    97,
    98,    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,
    69,    30,    31,    73,    74,   nil,   nil,   nil,   nil,   nil,
    77,    28,    27,   105,   104,   106,   107,   nil,   nil,    19,
   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,
   108,   110,    99,    57,   101,   100,   102,   nil,   103,   111,
   112,   nil,    95,    96,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,
   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    20,   nil,   nil,   nil,   nil,    93,    83,    86,    87,
   nil,    88,    90,    89,    91,   nil,   nil,   nil,   nil,    84,
    92,   nil,   nil,   nil,    75,    76,    72,    63,    58,    85,
    97,    98,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,   nil,
   nil,    77,    28,    27,   105,   104,   106,   107,   nil,   nil,
    19,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,
   109,   108,   110,    99,    57,   101,   100,   102,   nil,   103,
   111,   112,   nil,    95,    96,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   236,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    20,   nil,   nil,   nil,   nil,    93,    83,    86,
    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,   nil,
    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,    58,
    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,
   nil,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   117,   nil,   nil,   nil,   nil,   116,    63,
   nil,    85,    97,    98,    75,    76,    72,   nil,    58,   nil,
   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,   317,   318,    73,    74,   nil,   nil,   nil,   nil,
   nil,    77,   314,   320,   105,   104,   106,   107,   nil,   nil,
   237,   nil,   nil,   nil,   nil,   nil,   nil,   315,   nil,   nil,
   109,   108,   110,    99,    57,   101,   100,   102,   nil,   103,
   111,   112,   nil,    95,    96,   nil,   nil,   321,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   357,   nil,   nil,
    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,    35,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    93,    83,    86,
    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,   nil,
    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,    58,
    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,   317,   318,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,   314,   320,   105,   104,   106,   107,   nil,
   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,   315,   nil,
   nil,   109,   108,   110,   362,    57,   101,   100,   363,   nil,
   103,   111,   112,   nil,    95,    96,   nil,   nil,   321,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   369,   nil,   nil,   364,   nil,
   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,
    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,   317,   318,    73,    74,   nil,   nil,
   nil,   nil,   nil,    77,   314,   320,   105,   104,   106,   107,
   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,   315,
   nil,   nil,   109,   108,   110,   362,    57,   101,   100,   363,
   nil,   103,   111,   112,   nil,    95,    96,   nil,   nil,   321,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   364,
   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   nil,   682,   nil,   679,   678,   677,   687,   680,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   690,   nil,    93,
    83,    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,
   nil,   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,   685,
    63,   nil,    85,    97,    98,    75,    76,    72,     9,    58,
   698,   697,   nil,    64,    65,   691,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,     7,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,   397,    63,
   nil,    85,    97,    98,    75,    76,    72,   nil,    58,   nil,
   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,   nil,
   nil,    77,    28,    27,   105,   104,   106,   107,   nil,   nil,
    19,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,
   109,   108,   110,    99,    57,   101,   100,   102,   nil,   103,
   111,   112,   nil,    95,    96,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   236,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    20,   nil,   nil,   nil,   nil,    93,    83,    86,
    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,   nil,
    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,    58,
    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,
   nil,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,
    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,    30,    31,    73,    74,   nil,   nil,
   nil,   nil,   nil,    77,    28,    27,   105,   104,   106,   107,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    46,
   nil,   nil,   109,   108,   110,    99,    57,   101,   100,   102,
   nil,   103,   111,   112,   nil,    95,    96,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    93,
    83,    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,
   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,
    63,    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,    30,    31,    73,    74,   nil,
   nil,   nil,   nil,   nil,    77,    28,    27,   105,   104,   106,
   107,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   109,   108,   110,    99,    57,   101,   100,
   102,   nil,   103,   111,   112,   nil,    95,    96,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,
    93,    83,    86,    87,   nil,    88,    90,    89,    91,   nil,
   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,
   nil,    63,   nil,    85,    97,    98,    75,    76,    72,     9,
    58,   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,    30,    31,    73,    74,   nil,   nil,
   nil,   nil,   nil,    77,    28,    27,   105,   104,   106,   107,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,
   nil,    10,   109,   108,   110,    99,    57,   101,   100,   102,
   nil,   103,   111,   112,   nil,    95,    96,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    93,
    83,    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,
   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,
    63,    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,    30,    31,    73,    74,   nil,
   nil,   nil,   nil,   nil,    77,    28,    27,   105,   104,   106,
   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   109,   108,   110,    99,    57,   101,   100,
   102,   nil,   103,   111,   112,   nil,    95,    96,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,   413,   nil,   nil,   nil,    45,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,
    93,    83,    86,    87,   nil,    88,    90,    89,    91,   nil,
   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,
    72,    63,    58,    85,    97,    98,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,    30,    31,    73,    74,
   nil,   nil,   nil,   nil,   nil,    77,    28,    27,   105,   104,
   106,   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,
   nil,    46,   nil,   nil,   109,   108,   110,    99,    57,   101,
   100,   102,   nil,   103,   111,   112,   nil,    95,    96,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   236,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,   413,   nil,   nil,   nil,    45,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,
   nil,    93,    83,    86,    87,   nil,    88,    90,    89,    91,
   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,    75,
    76,    72,    63,    58,    85,    97,    98,    64,    65,   nil,
   nil,   nil,    68,   nil,    66,    67,    69,    30,    31,    73,
    74,   nil,   nil,   nil,   nil,   nil,    77,    28,    27,   105,
   104,   106,   107,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,   nil,    46,   nil,   nil,   109,   108,   110,    99,    57,
   101,   100,   102,   nil,   103,   111,   112,   nil,    95,    96,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,    59,    60,
   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    93,    83,    86,    87,   nil,    88,    90,    89,
    91,   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,
    75,    76,    72,    63,    58,    85,    97,    98,    64,    65,
   nil,   nil,   nil,    68,   nil,    66,    67,    69,    30,    31,
    73,    74,   nil,   nil,   nil,   nil,   nil,    77,    28,    27,
   105,   104,   106,   107,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,   nil,    46,   nil,   nil,   109,   108,   110,    99,
    57,   101,   100,   102,   nil,   103,   111,   112,   nil,    95,
    96,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,    59,
    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    93,    83,    86,    87,   nil,    88,    90,
    89,    91,   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,
   nil,    75,    76,    72,    63,    58,    85,    97,    98,    64,
    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,    30,
    31,    73,    74,   nil,   nil,   nil,   nil,   nil,    77,    28,
    27,   105,   104,   106,   107,   nil,   nil,   237,   nil,   nil,
   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,   108,   110,
    99,    57,   101,   100,   102,   nil,   103,   111,   112,   nil,
    95,    96,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,
    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,
   nil,   nil,   nil,   nil,    93,    83,    86,    87,   nil,    88,
    90,    89,    91,   nil,   nil,   nil,   nil,    84,    92,   nil,
   nil,   nil,    75,    76,    72,    63,    58,    85,    97,    98,
    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,
    30,    31,    73,    74,   nil,   nil,   nil,   nil,   nil,    77,
    28,    27,   105,   104,   106,   107,   nil,   nil,   237,   nil,
   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,   108,
   110,    99,    57,   101,   100,   102,   292,   103,   111,   112,
   nil,    95,    96,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,
   nil,    59,    60,   nil,   nil,    61,   nil,   289,   nil,   287,
   nil,    45,   nil,   nil,   293,   nil,   nil,   nil,   nil,   nil,
   235,   nil,   nil,   nil,   nil,    93,   290,    86,    87,   nil,
    88,    90,    89,    91,   nil,   nil,   nil,   nil,    84,    92,
   nil,   nil,   nil,    75,    76,    72,    63,    58,    85,    97,
    98,    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,
    69,    30,    31,    73,    74,   nil,   nil,   nil,   nil,   nil,
    77,    28,    27,   105,   104,   106,   107,   nil,   nil,   237,
   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,
   108,   110,    99,    57,   101,   100,   102,   nil,   103,   111,
   112,   nil,    95,    96,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,
   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   235,   nil,   nil,   nil,   nil,    93,    83,    86,    87,
   nil,    88,    90,    89,    91,   nil,   nil,   nil,   nil,    84,
    92,   nil,   nil,   nil,    75,    76,    72,    63,    58,    85,
    97,    98,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,   nil,
   nil,    77,    28,    27,   105,   104,   106,   107,   nil,   nil,
    19,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,
   109,   108,   110,    99,    57,   101,   100,   102,   nil,   103,
   111,   112,   nil,    95,    96,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   236,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    20,   nil,   nil,   nil,   nil,    93,    83,    86,
    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,   nil,
    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,    58,
    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,
   nil,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   225,   nil,   nil,    75,    76,    72,    63,
    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,   317,   318,    73,    74,   nil,   nil,
   nil,   nil,   nil,    77,   314,   320,   105,   104,   106,   107,
   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,
   nil,   nil,   109,   108,   110,    99,    57,   101,   100,   102,
   nil,   103,   111,   112,   nil,    95,    96,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,
    83,    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,
   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,
    63,    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,   317,   318,    73,    74,   nil,
   nil,   nil,   nil,   nil,    77,   314,   320,   105,   104,   106,
   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   109,   108,   110,    99,    57,   101,   100,
   102,   nil,   103,   111,   112,   nil,    95,    96,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,
    93,    83,    86,    87,   nil,    88,    90,    89,    91,   nil,
   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,
    72,    63,    58,    85,    97,    98,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,   317,   318,    73,    74,
   nil,   nil,   nil,   nil,   nil,    77,   314,   320,   105,   104,
   106,   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,
   nil,    46,   nil,   nil,   109,   108,   110,    99,    57,   101,
   100,   102,   nil,   103,   111,   112,   nil,    95,    96,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   236,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,
   nil,    93,    83,    86,    87,   nil,    88,    90,    89,    91,
   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,    75,
    76,    72,    63,    58,    85,    97,    98,    64,    65,   nil,
   nil,   nil,    68,   nil,    66,    67,    69,   317,   318,    73,
    74,   nil,   nil,   nil,   nil,   nil,    77,   314,   320,   105,
   104,   106,   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,
   nil,   nil,    46,   nil,   nil,   109,   108,   110,    99,    57,
   101,   100,   102,   nil,   103,   111,   112,   nil,    95,    96,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,    59,    60,
   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,
   nil,   nil,    93,    83,    86,    87,   nil,    88,    90,    89,
    91,   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,
    75,    76,    72,    63,    58,    85,    97,    98,    64,    65,
   nil,   nil,   nil,    68,   nil,    66,    67,    69,   317,   318,
    73,    74,   nil,   nil,   nil,   nil,   nil,    77,   314,   320,
   105,   104,   106,   107,   nil,   nil,   237,   nil,   nil,   nil,
   nil,   nil,   nil,    46,   nil,   nil,   109,   108,   110,    99,
    57,   101,   100,   102,   nil,   103,   111,   112,   nil,    95,
    96,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,    59,
    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,
   nil,   nil,   nil,    93,    83,    86,    87,   nil,    88,    90,
    89,    91,   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,
   nil,    75,    76,    72,    63,    58,    85,    97,    98,    64,
    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,   317,
   318,    73,    74,   nil,   nil,   nil,   nil,   nil,    77,   314,
   320,   105,   104,   106,   107,   nil,   nil,   237,   nil,   nil,
   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,   108,   110,
    99,    57,   101,   100,   102,   nil,   103,   111,   112,   nil,
    95,    96,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,
    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,
   nil,   nil,   nil,   nil,    93,    83,    86,    87,   nil,    88,
    90,    89,    91,   nil,   nil,   nil,   nil,    84,    92,   nil,
   nil,   nil,    75,    76,    72,    63,    58,    85,    97,    98,
    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,
   317,   318,    73,    74,   nil,   nil,   nil,   nil,   nil,    77,
   314,   320,   105,   104,   106,   107,   nil,   nil,   237,   nil,
   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,   108,
   110,    99,    57,   101,   100,   102,   nil,   103,   111,   112,
   nil,    95,    96,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,
   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   235,   nil,   nil,   nil,   nil,    93,    83,    86,    87,   nil,
    88,    90,    89,    91,   nil,   nil,   nil,   nil,    84,    92,
   nil,   nil,   nil,    75,    76,    72,    63,    58,    85,    97,
    98,    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,
    69,   317,   318,    73,    74,   nil,   nil,   nil,   nil,   nil,
    77,   314,   320,   105,   104,   106,   107,   nil,   nil,   237,
   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,
   108,   110,    99,    57,   101,   100,   102,   nil,   103,   111,
   112,   nil,    95,    96,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,
   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   235,   nil,   nil,   nil,   nil,    93,    83,    86,    87,
   nil,    88,    90,    89,    91,   nil,   nil,   nil,   nil,    84,
    92,   nil,   nil,   nil,    75,    76,    72,    63,    58,    85,
    97,    98,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,   317,   318,    73,    74,   nil,   nil,   nil,   nil,
   nil,    77,   314,   320,   105,   104,   106,   107,   nil,   nil,
   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,
   109,   108,   110,    99,    57,   101,   100,   102,   nil,   103,
   111,   112,   nil,    95,    96,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   236,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,    83,    86,
    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,   nil,
    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,    58,
    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,   317,   318,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,   314,   320,   105,   104,   106,   107,   nil,
   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,
   nil,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,
    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,   317,   318,    73,    74,   nil,   nil,
   nil,   nil,   nil,    77,   314,   320,   105,   104,   106,   107,
   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,
   nil,   nil,   109,   108,   110,    99,    57,   101,   100,   102,
   nil,   103,   111,   112,   nil,    95,    96,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,
    83,    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,
   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,
    63,    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,   317,   318,    73,    74,   nil,
   nil,   nil,   nil,   nil,    77,   314,   320,   105,   104,   106,
   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   109,   108,   110,    99,    57,   101,   100,
   102,   nil,   103,   111,   112,   nil,    95,    96,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,
    93,    83,    86,    87,   nil,    88,    90,    89,    91,   nil,
   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,
    72,    63,    58,    85,    97,    98,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,   317,   318,    73,    74,
   nil,   nil,   nil,   nil,   nil,    77,   314,   320,   105,   104,
   106,   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,
   nil,    46,   nil,   nil,   109,   108,   110,    99,    57,   101,
   100,   102,   nil,   103,   111,   112,   nil,    95,    96,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   236,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,
   nil,    93,    83,    86,    87,   nil,    88,    90,    89,    91,
   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,    75,
    76,    72,    63,    58,    85,    97,    98,    64,    65,   nil,
   nil,   nil,    68,   nil,    66,    67,    69,   317,   318,    73,
    74,   nil,   nil,   nil,   nil,   nil,    77,   314,   320,   105,
   104,   106,   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,
   nil,   nil,    46,   nil,   nil,   109,   108,   110,    99,    57,
   101,   100,   102,   nil,   103,   111,   112,   nil,    95,    96,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,    59,    60,
   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,
   nil,   nil,    93,    83,    86,    87,   nil,    88,    90,    89,
    91,   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,
    75,    76,    72,    63,    58,    85,    97,    98,    64,    65,
   nil,   nil,   nil,    68,   nil,    66,    67,    69,   317,   318,
    73,    74,   nil,   nil,   nil,   nil,   nil,    77,   314,   320,
   105,   104,   106,   107,   nil,   nil,   237,   nil,   nil,   nil,
   nil,   nil,   nil,    46,   nil,   nil,   109,   108,   110,    99,
    57,   101,   100,   102,   nil,   103,   111,   112,   nil,    95,
    96,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,    59,
    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,
   nil,   nil,   nil,    93,    83,    86,    87,   nil,    88,    90,
    89,    91,   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,
   nil,    75,    76,    72,    63,    58,    85,    97,    98,    64,
    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,   317,
   318,    73,    74,   nil,   nil,   nil,   nil,   nil,    77,   314,
   320,   105,   104,   106,   107,   nil,   nil,   237,   nil,   nil,
   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,   108,   110,
    99,    57,   101,   100,   102,   nil,   103,   111,   112,   nil,
    95,    96,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,
    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,
   nil,   nil,   nil,   nil,    93,    83,    86,    87,   nil,    88,
    90,    89,    91,   nil,   nil,   nil,   nil,    84,    92,   nil,
   nil,   nil,    75,    76,    72,    63,    58,    85,    97,    98,
    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,
   317,   318,    73,    74,   nil,   nil,   nil,   nil,   nil,    77,
   314,   320,   105,   104,   106,   107,   nil,   nil,   237,   nil,
   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,   108,
   110,    99,    57,   101,   100,   102,   nil,   103,   111,   112,
   nil,    95,    96,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,
   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   235,   nil,   nil,   nil,   nil,    93,    83,    86,    87,   nil,
    88,    90,    89,    91,   nil,   nil,   nil,   nil,    84,    92,
   nil,   nil,   nil,    75,    76,    72,    63,    58,    85,    97,
    98,    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,
    69,   317,   318,    73,    74,   nil,   nil,   nil,   nil,   nil,
    77,   314,   320,   105,   104,   106,   107,   nil,   nil,   237,
   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,
   108,   110,    99,    57,   101,   100,   102,   nil,   103,   111,
   112,   nil,    95,    96,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,
   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   235,   nil,   nil,   nil,   nil,    93,    83,    86,    87,
   nil,    88,    90,    89,    91,   nil,   nil,   nil,   nil,    84,
    92,   nil,   nil,   nil,    75,    76,    72,    63,    58,    85,
    97,    98,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,   317,   318,    73,    74,   nil,   nil,   nil,   nil,
   nil,    77,   314,   320,   105,   104,   106,   107,   nil,   nil,
   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,
   109,   108,   110,    99,    57,   101,   100,   102,   nil,   103,
   111,   112,   nil,    95,    96,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   236,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,    83,    86,
    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,   nil,
    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,    58,
    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,   317,   318,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,   314,   320,   105,   104,   106,   107,   nil,
   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,
   nil,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,
    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,   317,   318,    73,    74,   nil,   nil,
   nil,   nil,   nil,    77,   314,   320,   105,   104,   106,   107,
   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,
   nil,   nil,   109,   108,   110,    99,    57,   101,   100,   102,
   nil,   103,   111,   112,   nil,    95,    96,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,
    83,    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,
   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,
    63,    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,   317,   318,    73,    74,   nil,
   nil,   nil,   nil,   nil,    77,   314,   320,   105,   104,   106,
   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   109,   108,   110,    99,    57,   101,   100,
   102,   nil,   103,   111,   112,   nil,    95,    96,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,
    93,    83,    86,    87,   nil,    88,    90,    89,    91,   nil,
   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,
    72,    63,    58,    85,    97,    98,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,   317,   318,    73,    74,
   nil,   nil,   nil,   nil,   nil,    77,   314,   320,   105,   104,
   106,   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,
   nil,    46,   nil,   nil,   109,   108,   110,    99,    57,   101,
   100,   102,   nil,   103,   111,   112,   nil,    95,    96,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   236,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,
   nil,    93,    83,    86,    87,   nil,    88,    90,    89,    91,
   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,    75,
    76,    72,    63,    58,    85,    97,    98,    64,    65,   nil,
   nil,   nil,    68,   nil,    66,    67,    69,   317,   318,    73,
    74,   nil,   nil,   nil,   nil,   nil,    77,   314,   320,   105,
   104,   106,   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,
   nil,   nil,    46,   nil,   nil,   109,   108,   110,    99,    57,
   101,   100,   102,   nil,   103,   111,   112,   nil,    95,    96,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,    59,    60,
   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,
   nil,   nil,    93,    83,    86,    87,   nil,    88,    90,    89,
    91,   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,
    75,    76,    72,    63,    58,    85,    97,    98,    64,    65,
   nil,   nil,   nil,    68,   nil,    66,    67,    69,    30,    31,
    73,    74,   nil,   nil,   nil,   nil,   nil,    77,    28,    27,
   105,   104,   106,   107,   nil,   nil,   237,   nil,   nil,   nil,
   nil,   nil,   nil,    46,   nil,   nil,   109,   108,   110,    99,
    57,   101,   100,   102,   292,   103,   111,   112,   nil,    95,
    96,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,    59,
    60,   nil,   nil,    61,   nil,   289,   nil,   287,   nil,    45,
   nil,   nil,   293,   nil,   nil,   nil,   nil,   nil,   235,   nil,
   nil,   nil,   nil,    93,   290,    86,    87,   nil,    88,    90,
    89,    91,   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,
   nil,    75,    76,    72,    63,    58,    85,    97,    98,    64,
    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,    30,
    31,    73,    74,   nil,   nil,   nil,   nil,   nil,    77,    28,
    27,   105,   104,   106,   107,   nil,   nil,   237,   nil,   nil,
   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,   108,   110,
    99,    57,   101,   100,   102,   292,   103,   111,   112,   nil,
    95,    96,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,
    59,    60,   nil,   nil,    61,   nil,   289,   nil,   287,   nil,
    45,   nil,   nil,   293,   nil,   nil,   nil,   nil,   nil,   235,
   nil,   nil,   nil,   nil,    93,   290,    86,    87,   nil,    88,
    90,    89,    91,   nil,   nil,   nil,   nil,    84,    92,   nil,
   nil,   nil,    75,    76,    72,    63,    58,    85,    97,    98,
    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,
    30,    31,    73,    74,   nil,   nil,   nil,   nil,   nil,    77,
    28,    27,   105,   104,   106,   107,   nil,   nil,   237,   nil,
   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,   108,
   110,    99,    57,   101,   100,   102,   292,   103,   111,   112,
   nil,    95,    96,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,
   nil,    59,    60,   nil,   nil,    61,   nil,   289,   nil,   287,
   nil,    45,   nil,   nil,   293,   nil,   nil,   nil,   nil,   nil,
   235,   nil,   nil,   nil,   nil,    93,   290,    86,    87,   nil,
    88,    90,    89,    91,   nil,   nil,   nil,   nil,    84,    92,
   225,   nil,   nil,    75,    76,    72,    63,    58,    85,    97,
    98,    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,
    69,   317,   318,    73,    74,   nil,   nil,   nil,   nil,   nil,
    77,   314,   320,   105,   104,   106,   107,   nil,   nil,   237,
   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,
   108,   110,    99,    57,   101,   100,   102,   nil,   103,   111,
   112,   nil,    95,    96,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,
   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   235,   nil,   nil,   nil,   nil,    93,    83,    86,    87,
   nil,    88,    90,    89,    91,   nil,   nil,   nil,   nil,    84,
    92,   nil,   nil,   nil,    75,    76,    72,    63,    58,    85,
    97,    98,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,   317,   318,    73,    74,   nil,   nil,   nil,   nil,
   nil,    77,   314,   320,   105,   104,   106,   107,   nil,   nil,
   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,
   109,   108,   110,    99,    57,   101,   100,   102,   nil,   103,
   111,   112,   nil,    95,    96,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   236,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,    83,    86,
    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,   nil,
    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,    58,
    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,   317,   318,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,   314,   320,   105,   104,   106,   107,   nil,
   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,
   nil,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,
    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,   317,   318,    73,    74,   nil,   nil,
   nil,   nil,   nil,    77,   314,   320,   105,   104,   106,   107,
   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,
   nil,   nil,   109,   108,   110,    99,    57,   101,   100,   102,
   nil,   103,   111,   112,   nil,    95,    96,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,
    83,    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,
   nil,   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,   nil,
    63,   nil,    85,    97,    98,    75,    76,    72,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   nil,
    10,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,
    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,   317,   318,    73,    74,   nil,   nil,
   nil,   nil,   nil,    77,   314,   320,   105,   104,   106,   107,
   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,   315,
   nil,   nil,   109,   108,   110,    99,    57,   101,   100,   102,
   nil,   103,   111,   112,   nil,    95,    96,   nil,   nil,   321,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   311,
   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   nil,   682,   nil,   679,   678,   677,   687,   680,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   690,   nil,    93,
    83,    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,
   nil,   nil,    84,    92,   nil,   nil,   nil,   515,   nil,   685,
    63,   nil,    85,    97,    98,    75,    76,    72,   nil,    58,
   698,   697,   nil,    64,    65,   691,   nil,   nil,    68,   nil,
    66,    67,    69,   317,   318,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,   314,   320,   105,   104,   106,   107,   nil,
   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,   315,   nil,
   nil,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,   nil,   nil,   321,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   311,   nil,
   nil,   307,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,
    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,   317,   318,    73,    74,   nil,   nil,
   nil,   nil,   nil,    77,   314,   320,   105,   104,   106,   107,
   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,
   nil,   nil,   109,   108,   110,    99,    57,   101,   100,   102,
   nil,   103,   111,   112,   nil,    95,    96,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,
    83,    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,
   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,
    63,    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,   317,   318,    73,    74,   nil,
   nil,   nil,   nil,   nil,    77,   314,   320,   105,   104,   106,
   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   109,   108,   110,    99,    57,   101,   100,
   102,   nil,   103,   111,   112,   nil,    95,    96,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   236,   532,   nil,    59,    60,   nil,   nil,
    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,
    93,    83,    86,    87,   nil,    88,    90,    89,    91,   nil,
   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,
    72,    63,    58,    85,    97,    98,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,    30,    31,    73,    74,
   nil,   nil,   nil,   nil,   nil,    77,    28,    27,   105,   104,
   106,   107,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,
   nil,    46,   nil,   nil,   109,   108,   110,    99,    57,   101,
   100,   102,   nil,   103,   111,   112,   nil,    95,    96,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   236,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,
   nil,    93,    83,    86,    87,   nil,    88,    90,    89,    91,
   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,    75,
    76,    72,    63,    58,    85,    97,    98,    64,    65,   nil,
   nil,   nil,    68,   nil,    66,    67,    69,    30,    31,    73,
    74,   nil,   nil,   nil,   nil,   nil,    77,    28,    27,   105,
   104,   106,   107,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,   nil,    46,   nil,   nil,   109,   108,   110,    99,    57,
   101,   100,   102,   nil,   103,   111,   112,   nil,    95,    96,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,    59,    60,
   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    93,    83,    86,    87,   nil,    88,    90,    89,
    91,   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,
    75,    76,    72,    63,    58,    85,    97,    98,    64,    65,
   nil,   nil,   nil,    68,   nil,    66,    67,    69,    30,    31,
    73,    74,   nil,   nil,   nil,   nil,   nil,    77,    28,    27,
   105,   104,   106,   107,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,   nil,    46,   nil,   nil,   109,   108,   110,    99,
    57,   101,   100,   102,   nil,   103,   111,   112,   nil,    95,
    96,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,    59,
    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    93,    83,    86,    87,   nil,    88,    90,
    89,    91,   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,
   nil,    75,    76,    72,    63,    58,    85,    97,    98,    64,
    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,   317,
   318,    73,    74,   nil,   nil,   nil,   nil,   nil,    77,   314,
   320,   105,   104,   106,   107,   nil,   nil,   237,   nil,   nil,
   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,   108,   110,
    99,    57,   101,   100,   102,   nil,   103,   111,   112,   nil,
    95,    96,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,
    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,
   nil,   nil,   nil,   nil,    93,    83,    86,    87,   nil,    88,
    90,    89,    91,   nil,   nil,   nil,   nil,    84,    92,   nil,
   nil,   nil,    75,    76,    72,    63,    58,    85,    97,    98,
    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,
    30,    31,    73,    74,   nil,   nil,   nil,   nil,   nil,    77,
    28,    27,   105,   104,   106,   107,   nil,   nil,   237,   nil,
   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,   108,
   110,    99,    57,   101,   100,   102,   292,   103,   111,   112,
   nil,    95,    96,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,
   nil,    59,    60,   nil,   nil,    61,   nil,   289,   nil,   287,
   nil,    45,   nil,   nil,   293,   nil,   nil,   nil,   nil,   nil,
   235,   nil,   nil,   nil,   nil,    93,   290,    86,    87,   nil,
    88,    90,    89,    91,   nil,   nil,   nil,   nil,    84,    92,
   nil,   nil,   nil,    75,    76,    72,    63,    58,    85,    97,
    98,    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,
    69,   317,   318,    73,    74,   nil,   nil,   nil,   nil,   nil,
    77,   314,   320,   105,   104,   106,   107,   nil,   nil,   237,
   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,
   108,   110,    99,    57,   101,   100,   102,   nil,   103,   111,
   112,   nil,    95,    96,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,
   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   235,   nil,   nil,   nil,   nil,    93,    83,    86,    87,
   nil,    88,    90,    89,    91,   nil,   nil,   nil,   nil,    84,
    92,   nil,   nil,   nil,    75,    76,    72,    63,    58,    85,
    97,    98,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,   317,   318,    73,    74,   nil,   nil,   nil,   nil,
   nil,    77,   314,   320,   105,   104,   106,   107,   nil,   nil,
   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,
   109,   108,   110,    99,    57,   101,   100,   102,   nil,   103,
   111,   112,   nil,    95,    96,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   236,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,    83,    86,
    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,   nil,
    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,    58,
    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,   317,   318,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,   314,   320,   105,   104,   106,   107,   nil,
   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,
   nil,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,
    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,   317,   318,    73,    74,   nil,   nil,
   nil,   nil,   nil,    77,   314,   320,   105,   104,   106,   107,
   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,
   nil,   nil,   109,   108,   110,    99,    57,   101,   100,   102,
   292,   103,   111,   112,   nil,    95,    96,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   640,   nil,   287,   nil,    45,   nil,   nil,   293,   nil,
   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,
   290,    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,
   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,
    63,    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,   317,   318,    73,    74,   nil,
   nil,   nil,   nil,   nil,    77,   314,   320,   105,   104,   106,
   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   109,   108,   110,    99,    57,   101,   100,
   102,   292,   103,   111,   112,   nil,    95,    96,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,   nil,   nil,   287,   nil,    45,   nil,   nil,   293,
   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,
    93,   290,    86,    87,   nil,    88,    90,    89,    91,   nil,
   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,
    72,    63,    58,    85,    97,    98,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,   317,   318,    73,    74,
   nil,   nil,   nil,   nil,   nil,    77,   314,   320,   105,   104,
   106,   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,
   nil,    46,   nil,   nil,   109,   108,   110,    99,    57,   101,
   100,   102,   nil,   103,   111,   112,   nil,    95,    96,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   236,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,
   nil,    93,    83,    86,    87,   nil,    88,    90,    89,    91,
   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,   nil,
   nil,   nil,    63,   nil,    85,    97,    98,    75,    76,    72,
     9,    58,   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,    30,    31,    73,    74,   nil,
   nil,   nil,   nil,   nil,    77,    28,    27,   105,   104,   106,
   107,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,
    46,   300,    10,   109,   108,   110,    99,    57,   101,   100,
   102,   nil,   103,   111,   112,   nil,    95,    96,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    40,   nil,   nil,    33,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,
    93,    83,    86,    87,   nil,    88,    90,    89,    91,   nil,
   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,   nil,   nil,
   397,    63,   nil,    85,    97,    98,    75,    76,    72,   nil,
    58,   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,   317,   318,    73,    74,   nil,   nil,
   nil,   nil,   nil,    77,   314,   320,   105,   104,   106,   107,
   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,   315,
   nil,   nil,   109,   108,   110,    99,    57,   101,   100,   102,
   nil,   103,   111,   112,   nil,    95,    96,   nil,   nil,   321,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   311,
   nil,   nil,   307,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    93,
    83,    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,
   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,
    63,    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,    30,    31,    73,    74,   nil,
   nil,   nil,   nil,   nil,    77,    28,    27,   105,   104,   106,
   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   109,   108,   110,    99,    57,   101,   100,
   102,   292,   103,   111,   112,   nil,    95,    96,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,   289,   nil,   287,   nil,    45,   nil,   nil,   293,
   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,
    93,   290,    86,    87,   nil,    88,    90,    89,    91,   nil,
   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,
    72,    63,    58,    85,    97,    98,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,   317,   318,    73,    74,
   nil,   nil,   nil,   nil,   nil,    77,   314,   320,   105,   104,
   106,   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,
   nil,   315,   nil,   nil,   109,   108,   110,    99,    57,   101,
   100,   102,   nil,   103,   111,   112,   nil,    95,    96,   nil,
   nil,   321,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   311,   nil,   nil,   307,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    93,    83,    86,    87,   nil,    88,    90,    89,    91,
   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,    75,
    76,    72,    63,    58,    85,    97,    98,    64,    65,   nil,
   nil,   nil,    68,   nil,    66,    67,    69,   317,   318,    73,
    74,   nil,   nil,   nil,   nil,   nil,    77,   314,   320,   105,
   104,   106,   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,
   nil,   nil,    46,   nil,   nil,   109,   108,   110,    99,    57,
   101,   100,   102,   nil,   103,   111,   112,   nil,    95,    96,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,    59,    60,
   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,
   nil,   nil,    93,    83,    86,    87,   nil,    88,    90,    89,
    91,   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,
    75,    76,    72,    63,    58,    85,    97,    98,    64,    65,
   nil,   nil,   nil,    68,   nil,    66,    67,    69,   317,   318,
    73,    74,   nil,   nil,   nil,   nil,   nil,    77,   314,   320,
   105,   104,   106,   107,   nil,   nil,   237,   nil,   nil,   nil,
   nil,   nil,   nil,    46,   nil,   nil,   109,   108,   110,    99,
    57,   101,   100,   102,   nil,   103,   111,   112,   nil,    95,
    96,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,    59,
    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,
   nil,   nil,   nil,    93,    83,    86,    87,   nil,    88,    90,
    89,    91,   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,
   nil,    75,    76,    72,    63,    58,    85,    97,    98,    64,
    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,    30,
    31,    73,    74,   nil,   nil,   nil,   nil,   nil,    77,    28,
    27,   105,   104,   106,   107,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,   108,   110,
    99,    57,   101,   100,   102,   nil,   103,   111,   112,   nil,
    95,    96,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,
    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,
   nil,   nil,   nil,   nil,    93,    83,    86,    87,   nil,    88,
    90,    89,    91,   nil,   nil,   nil,   nil,    84,    92,   nil,
   nil,   nil,    75,    76,    72,    63,    58,    85,    97,    98,
    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,
   317,   318,    73,    74,   nil,   nil,   nil,   nil,   nil,    77,
   314,   320,   105,   104,   106,   107,   nil,   nil,   237,   nil,
   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,   108,
   110,    99,    57,   101,   100,   102,   292,   103,   111,   112,
   nil,    95,    96,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,
   nil,    59,    60,   nil,   nil,    61,   nil,   640,   nil,   nil,
   nil,    45,   nil,   nil,   293,   nil,   nil,   nil,   nil,   nil,
   235,   nil,   nil,   nil,   nil,    93,   290,    86,    87,   nil,
    88,    90,    89,    91,   nil,   nil,   nil,   nil,    84,    92,
   nil,   nil,   nil,    75,    76,    72,    63,    58,    85,    97,
    98,    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,
    69,   317,   318,    73,    74,   nil,   nil,   nil,   nil,   nil,
    77,   314,   320,   105,   104,   106,   107,   nil,   nil,   237,
   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,
   108,   110,    99,    57,   101,   100,   102,   292,   103,   111,
   112,   nil,    95,    96,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,
   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   293,   nil,   nil,   nil,   nil,
   nil,   235,   nil,   nil,   nil,   nil,    93,   290,    86,    87,
   nil,    88,    90,    89,    91,   nil,   nil,   nil,   nil,    84,
    92,   nil,   nil,   nil,    75,    76,    72,    63,    58,    85,
    97,    98,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,   317,   318,    73,    74,   nil,   nil,   nil,   nil,
   nil,    77,   314,   320,   105,   104,   106,   107,   nil,   nil,
   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,
   109,   108,   110,    99,    57,   101,   100,   102,   nil,   103,
   111,   112,   nil,    95,    96,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   236,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   289,
   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,    83,    86,
    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,   nil,
    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,    58,
    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,
   nil,   109,   108,   110,    99,    57,   101,   100,   102,   292,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   289,   nil,   287,   nil,    45,   nil,   nil,   293,   nil,   nil,
   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,   290,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,
    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,    30,    31,    73,    74,   nil,   nil,
   nil,   nil,   nil,    77,    28,    27,   105,   104,   106,   107,
   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,
   nil,   nil,   109,   108,   110,    99,    57,   101,   100,   102,
   292,   103,   111,   112,   nil,    95,    96,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   289,   nil,   287,   nil,    45,   nil,   nil,   293,   nil,
   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,
   290,    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,
   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,
    63,    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,   317,   318,    73,    74,   nil,
   nil,   nil,   nil,   nil,    77,   314,   320,   105,   104,   106,
   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   109,   108,   110,    99,    57,   101,   100,
   102,   nil,   103,   111,   112,   nil,    95,    96,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,   744,   nil,   nil,   nil,    45,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,
    93,    83,    86,    87,   nil,    88,    90,    89,    91,   nil,
   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,
    72,    63,    58,    85,    97,    98,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,    30,    31,    73,    74,
   nil,   nil,   nil,   nil,   nil,    77,    28,    27,   105,   104,
   106,   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,
   nil,    46,   nil,   nil,   109,   108,   110,    99,    57,   101,
   100,   102,   nil,   103,   111,   112,   nil,    95,    96,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   236,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,
   nil,    93,    83,    86,    87,   nil,    88,    90,    89,    91,
   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,    75,
    76,    72,    63,    58,    85,    97,    98,    64,    65,   nil,
   nil,   nil,    68,   nil,    66,    67,    69,    30,    31,    73,
    74,   nil,   nil,   nil,   nil,   nil,    77,    28,    27,   105,
   104,   106,   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,
   nil,   nil,    46,   nil,   nil,   109,   108,   110,    99,    57,
   101,   100,   102,   292,   103,   111,   112,   nil,    95,    96,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,    59,    60,
   nil,   nil,    61,   nil,   289,   nil,   287,   nil,    45,   nil,
   nil,   293,   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,
   nil,   nil,    93,   290,    86,    87,   nil,    88,    90,    89,
    91,   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,
   nil,   nil,   nil,    63,   nil,    85,    97,    98,    75,    76,
    72,     9,    58,   nil,   nil,   nil,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,    30,    31,    73,    74,
   nil,   nil,   nil,   nil,   nil,    77,    28,    27,   105,   104,
   106,   107,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,
     8,    46,   nil,    10,   109,   108,   110,    99,    57,   101,
   100,   102,   nil,   103,   111,   112,   nil,    95,    96,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,    33,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,    35,   nil,   nil,   nil,    45,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,
   nil,    93,    83,    86,    87,   nil,    88,    90,    89,    91,
   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,    75,
    76,    72,    63,    58,    85,    97,    98,    64,    65,   nil,
   nil,   nil,    68,   nil,    66,    67,    69,   317,   318,    73,
    74,   nil,   nil,   nil,   nil,   nil,    77,   314,   320,   105,
   104,   106,   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,
   nil,   nil,    46,   nil,   nil,   109,   108,   110,    99,    57,
   101,   100,   102,   nil,   103,   111,   112,   nil,    95,    96,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,    59,    60,
   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,
   nil,   nil,    93,    83,    86,    87,   nil,    88,    90,    89,
    91,   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,
    75,    76,    72,    63,    58,    85,    97,    98,    64,    65,
   nil,   nil,   nil,    68,   nil,    66,    67,    69,   317,   318,
    73,    74,   nil,   nil,   nil,   nil,   nil,    77,   314,   320,
   105,   104,   106,   107,   nil,   nil,   237,   nil,   nil,   nil,
   nil,   nil,   nil,    46,   nil,   nil,   109,   108,   110,    99,
    57,   101,   100,   102,   292,   103,   111,   112,   nil,    95,
    96,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,    59,
    60,   nil,   nil,    61,   nil,   640,   nil,   287,   nil,    45,
   nil,   nil,   293,   nil,   nil,   nil,   nil,   nil,   235,   nil,
   nil,   nil,   nil,    93,   290,    86,    87,   nil,    88,    90,
    89,    91,   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,
   nil,    75,    76,    72,    63,    58,    85,    97,    98,    64,
    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,   317,
   318,    73,    74,   nil,   nil,   nil,   nil,   nil,    77,   314,
   320,   105,   104,   106,   107,   nil,   nil,   237,   nil,   nil,
   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,   108,   110,
    99,    57,   101,   100,   102,   292,   103,   111,   112,   nil,
    95,    96,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,
    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   287,   nil,
    45,   nil,   nil,   293,   nil,   nil,   nil,   nil,   nil,   235,
   nil,   nil,   nil,   nil,    93,   290,    86,    87,   nil,    88,
    90,    89,    91,   nil,   nil,   nil,   nil,    84,    92,   nil,
   nil,   nil,    75,    76,    72,    63,    58,    85,    97,    98,
    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,
    30,    31,    73,    74,   nil,   nil,   nil,   nil,   nil,    77,
    28,    27,   105,   104,   106,   107,   nil,   nil,   237,   nil,
   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,   108,
   110,    99,    57,   101,   100,   102,   nil,   103,   111,   112,
   nil,    95,    96,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,
   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   235,   nil,   nil,   nil,   nil,    93,    83,    86,    87,   nil,
    88,    90,    89,    91,   nil,   nil,   nil,   nil,    84,    92,
   nil,   nil,   nil,    75,    76,    72,    63,    58,    85,    97,
    98,    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,
    69,    30,    31,    73,    74,   nil,   nil,   nil,   nil,   nil,
    77,    28,    27,   105,   104,   106,   107,   nil,   nil,   237,
   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,
   108,   110,    99,    57,   101,   100,   102,   nil,   103,   111,
   112,   nil,    95,    96,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,
   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   235,   nil,   nil,   nil,   nil,    93,    83,    86,    87,
   nil,    88,    90,    89,    91,   nil,   nil,   nil,   nil,    84,
    92,   nil,   nil,   nil,    75,    76,    72,    63,    58,    85,
    97,    98,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,   nil,
   nil,    77,    28,    27,   105,   104,   106,   107,   nil,   nil,
   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,
   109,   108,   110,    99,    57,   101,   100,   102,   nil,   103,
   111,   112,   nil,    95,    96,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   236,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,    83,    86,
    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,   nil,
    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,    58,
    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,    28,    27,   105,   104,   106,   107,   nil,
   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,
   nil,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,
    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,   317,   318,    73,    74,   nil,   nil,
   nil,   nil,   nil,    77,   314,   320,   105,   104,   106,   107,
   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,
   nil,   nil,   109,   108,   110,    99,    57,   101,   100,   102,
   nil,   103,   111,   112,   nil,    95,    96,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,
    83,    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,
   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,
    63,    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,   317,   318,    73,    74,   nil,
   nil,   nil,   nil,   nil,    77,   314,   320,   105,   104,   106,
   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   109,   108,   110,    99,    57,   101,   100,
   102,   nil,   103,   111,   112,   nil,    95,    96,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,
    93,    83,    86,    87,   nil,    88,    90,    89,    91,   nil,
   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,
    72,    63,    58,    85,    97,    98,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,   317,   318,    73,    74,
   nil,   nil,   nil,   nil,   nil,    77,   314,   320,   105,   104,
   106,   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,
   nil,   315,   nil,   nil,   109,   108,   110,    99,    57,   101,
   100,   102,   nil,   103,   111,   112,   nil,    95,    96,   nil,
   nil,   321,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   311,   nil,   nil,   307,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    93,    83,    86,    87,   nil,    88,    90,    89,    91,
   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,    75,
    76,    72,    63,    58,    85,    97,    98,    64,    65,   nil,
   nil,   nil,    68,   nil,    66,    67,    69,   317,   318,    73,
    74,   nil,   nil,   nil,   nil,   nil,    77,   314,   320,   105,
   104,   106,   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,
   nil,   nil,   315,   nil,   nil,   109,   108,   110,    99,    57,
   101,   100,   102,   nil,   103,   111,   112,   nil,    95,    96,
   nil,   nil,   321,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   311,   nil,   nil,   307,   nil,   nil,    59,    60,
   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    93,    83,    86,    87,   nil,    88,    90,    89,
    91,   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,
    75,    76,    72,    63,    58,    85,    97,    98,    64,    65,
   nil,   nil,   nil,    68,   nil,    66,    67,    69,   317,   318,
    73,    74,   nil,   nil,   nil,   nil,   nil,    77,   314,   320,
   105,   104,   106,   107,   nil,   nil,   237,   nil,   nil,   nil,
   nil,   nil,   nil,    46,   nil,   nil,   109,   108,   110,    99,
    57,   101,   100,   102,   nil,   103,   111,   112,   nil,    95,
    96,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,    59,
    60,   nil,   nil,    61,   nil,   413,   nil,   nil,   nil,    45,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,
   nil,   nil,   nil,    93,    83,    86,    87,   nil,    88,    90,
    89,    91,   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,
   nil,    75,    76,    72,    63,    58,    85,    97,    98,    64,
    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,   317,
   318,    73,    74,   nil,   nil,   nil,   nil,   nil,    77,   314,
   320,   105,   104,   106,   107,   nil,   nil,   237,   nil,   nil,
   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,   108,   110,
    99,    57,   101,   100,   102,   nil,   103,   111,   112,   nil,
    95,    96,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,
    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,
   nil,   nil,   nil,   nil,    93,    83,    86,    87,   nil,    88,
    90,    89,    91,   nil,   nil,   nil,   nil,    84,    92,   nil,
   nil,   nil,    75,    76,    72,    63,    58,    85,    97,    98,
    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,
    30,    31,    73,    74,   nil,   nil,   nil,   nil,   nil,    77,
    28,    27,   105,   104,   106,   107,   nil,   nil,    19,   nil,
   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,   108,
   110,    99,    57,   101,   100,   102,   nil,   103,   111,   112,
   nil,    95,    96,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,
   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    20,   nil,   nil,   nil,   nil,    93,    83,    86,    87,   nil,
    88,    90,    89,    91,   nil,   nil,   nil,   nil,    84,    92,
   nil,   nil,   nil,    75,    76,    72,    63,    58,    85,    97,
    98,    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,
    69,   317,   318,    73,    74,   nil,   nil,   nil,   nil,   nil,
    77,   314,   320,   105,   104,   106,   107,   nil,   nil,   237,
   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,
   108,   110,    99,    57,   101,   100,   102,   nil,   103,   111,
   112,   nil,    95,    96,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,
   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   235,   nil,   nil,   nil,   nil,    93,    83,    86,    87,
   nil,    88,    90,    89,    91,   nil,   nil,   nil,   nil,    84,
    92,   nil,   nil,   nil,    75,    76,    72,    63,    58,    85,
    97,    98,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,   nil,
   nil,    77,    28,    27,   105,   104,   106,   107,   nil,   nil,
   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,
   109,   108,   110,    99,    57,   101,   100,   102,   nil,   103,
   111,   112,   nil,    95,    96,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   236,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,    83,    86,
    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,   nil,
    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,    58,
    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,   317,   318,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,   314,   320,   105,   104,   106,   107,   nil,
   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,
   nil,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,
    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,   317,   318,    73,    74,   nil,   nil,
   nil,   nil,   nil,    77,   314,   320,   105,   104,   106,   107,
   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,
   nil,   nil,   109,   108,   110,    99,    57,   101,   100,   102,
   nil,   103,   111,   112,   nil,    95,    96,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,
    83,    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,
   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,
    63,    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,   317,   318,    73,    74,   nil,
   nil,   nil,   nil,   nil,    77,   314,   320,   105,   104,   106,
   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   109,   108,   110,    99,    57,   101,   100,
   102,   nil,   103,   111,   112,   nil,    95,    96,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,
    93,    83,    86,    87,   nil,    88,    90,    89,    91,   nil,
   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,
    72,    63,    58,    85,    97,    98,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,   317,   318,    73,    74,
   nil,   nil,   nil,   nil,   nil,    77,   314,   320,   105,   104,
   106,   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,
   nil,    46,   nil,   nil,   109,   108,   110,    99,    57,   101,
   100,   102,   nil,   103,   111,   112,   nil,    95,    96,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   236,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,
   nil,    93,    83,    86,    87,   nil,    88,    90,    89,    91,
   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,    75,
    76,    72,    63,    58,    85,    97,    98,    64,    65,   nil,
   nil,   nil,    68,   nil,    66,    67,    69,   317,   318,    73,
    74,   nil,   nil,   nil,   nil,   nil,    77,   314,   320,   105,
   104,   106,   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,
   nil,   nil,    46,   nil,   nil,   109,   108,   110,    99,    57,
   101,   100,   102,   nil,   103,   111,   112,   nil,    95,    96,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,    59,    60,
   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,
   nil,   nil,    93,    83,    86,    87,   nil,    88,    90,    89,
    91,   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,
    75,    76,    72,    63,    58,    85,    97,    98,    64,    65,
   nil,   nil,   nil,    68,   nil,    66,    67,    69,   317,   318,
    73,    74,   nil,   nil,   nil,   nil,   nil,    77,   314,   320,
   105,   104,   106,   107,   nil,   nil,   237,   nil,   nil,   nil,
   nil,   nil,   nil,    46,   nil,   nil,   109,   108,   110,    99,
    57,   101,   100,   102,   nil,   103,   111,   112,   nil,    95,
    96,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,    59,
    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,
   nil,   nil,   nil,    93,    83,    86,    87,   nil,    88,    90,
    89,    91,   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,
   nil,    75,    76,    72,    63,    58,    85,    97,    98,    64,
    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,    30,
    31,    73,    74,   nil,   nil,   nil,   nil,   nil,    77,    28,
    27,   105,   104,   106,   107,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,   108,   110,
    99,    57,   101,   100,   102,   nil,   103,   111,   112,   nil,
    95,    96,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,   nil,
    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,
   nil,   nil,   nil,   nil,    93,    83,    86,    87,   nil,    88,
    90,    89,    91,   nil,   nil,   nil,   nil,    84,    92,   nil,
   nil,   nil,    75,    76,    72,    63,    58,    85,    97,    98,
    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,
   317,   318,    73,    74,   nil,   nil,   nil,   nil,   nil,    77,
   314,   320,   105,   104,   106,   107,   nil,   nil,   237,   nil,
   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,   108,
   110,    99,    57,   101,   100,   102,   nil,   103,   111,   112,
   nil,    95,    96,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,   nil,
   nil,    59,    60,   nil,   nil,    61,   nil,   640,   nil,   nil,
   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   235,   nil,   nil,   nil,   nil,    93,    83,    86,    87,   nil,
    88,    90,    89,    91,   nil,   nil,   nil,   nil,    84,    92,
   nil,   nil,   nil,    75,    76,    72,    63,    58,    85,    97,
    98,    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,
    69,   317,   318,    73,    74,   nil,   nil,   nil,   nil,   nil,
    77,   314,   320,   105,   104,   106,   107,   nil,   nil,   237,
   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   109,
   108,   110,    99,    57,   101,   100,   102,   292,   103,   111,
   112,   nil,    95,    96,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   236,
   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,
   287,   nil,    45,   nil,   nil,   293,   nil,   nil,   nil,   nil,
   nil,   235,   nil,   nil,   nil,   nil,    93,   290,    86,    87,
   nil,    88,    90,    89,    91,   nil,   nil,   nil,   nil,    84,
    92,   nil,   nil,   nil,    75,    76,    72,    63,    58,    85,
    97,    98,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,   317,   318,    73,    74,   nil,   nil,   nil,   nil,
   nil,    77,   314,   320,   105,   104,   106,   107,   nil,   nil,
   237,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,
   109,   108,   110,    99,    57,   101,   100,   102,   nil,   103,
   111,   112,   nil,    95,    96,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   236,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   235,   nil,   nil,   nil,   nil,    93,    83,    86,
    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,   nil,
    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,    58,
    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,   317,   318,    73,    74,   nil,   nil,   nil,
   nil,   nil,    77,   314,   320,   105,   104,   106,   107,   nil,
   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,   315,   nil,
   nil,   109,   108,   110,    99,    57,   101,   100,   102,   nil,
   103,   111,   112,   nil,    95,    96,   nil,   nil,   321,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   921,   nil,
   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    93,    83,
    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,   nil,
   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,    63,
    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,   317,   318,    73,    74,   nil,   nil,
   nil,   nil,   nil,    77,   314,   320,   105,   104,   106,   107,
   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,   315,
   nil,   nil,   109,   108,   110,    99,    57,   101,   100,   102,
   nil,   103,   111,   112,   nil,    95,    96,   nil,   nil,   321,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   927,
   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    93,
    83,    86,    87,   nil,    88,    90,    89,    91,   nil,   nil,
   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,    72,
    63,    58,    85,    97,    98,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,   317,   318,    73,    74,   nil,
   nil,   nil,   nil,   nil,    77,   314,   320,   105,   104,   106,
   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,
   315,   nil,   nil,   109,   108,   110,    99,    57,   101,   100,
   102,   nil,   103,   111,   112,   nil,    95,    96,   nil,   nil,
   321,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   921,   nil,   nil,   236,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    93,    83,    86,    87,   nil,    88,    90,    89,    91,   nil,
   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,    75,    76,
    72,    63,    58,    85,    97,    98,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,    30,    31,    73,    74,
   nil,   nil,   nil,   nil,   nil,    77,    28,    27,   105,   104,
   106,   107,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,
   nil,    46,   nil,   nil,   109,   108,   110,    99,    57,   101,
   100,   102,   292,   103,   111,   112,   nil,    95,    96,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   236,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,   289,   nil,   287,   nil,    45,   nil,   nil,
   293,   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,
   nil,    93,   290,    86,    87,   nil,    88,    90,    89,    91,
   nil,   nil,   nil,   nil,    84,    92,   nil,   nil,   nil,   nil,
   nil,   nil,    63,   nil,    85,    97,    98,   178,   189,   179,
   202,   175,   195,   185,   184,   205,   206,   200,   183,   182,
   177,   203,   207,   208,   187,   176,   190,   194,   196,   188,
   181,   nil,   nil,   nil,   197,   204,   199,   198,   191,   201,
   186,   174,   193,   192,   nil,   nil,   nil,   nil,   nil,   173,
   180,   171,   172,   168,   169,   170,   129,   131,   128,   nil,
   130,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   162,   163,
   nil,   159,   141,   142,   143,   150,   147,   149,   nil,   nil,
   144,   145,   nil,   nil,   nil,   164,   165,   151,   152,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   156,   155,   nil,   140,   161,   158,   157,   166,
   153,   154,   148,   146,   138,   160,   139,   nil,   nil,   167,
    93,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    92,   178,   189,   179,   202,   175,
   195,   185,   184,   205,   206,   200,   183,   182,   177,   203,
   207,   208,   187,   176,   190,   194,   196,   188,   181,   nil,
   nil,   nil,   197,   204,   199,   198,   191,   201,   186,   174,
   193,   192,   nil,   nil,   nil,   nil,   nil,   173,   180,   171,
   172,   168,   169,   170,   129,   131,   nil,   nil,   130,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   162,   163,   nil,   159,
   141,   142,   143,   150,   147,   149,   nil,   nil,   144,   145,
   nil,   nil,   nil,   164,   165,   151,   152,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   156,   155,   nil,   140,   161,   158,   157,   166,   153,   154,
   148,   146,   138,   160,   139,   nil,   nil,   167,    93,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    92,   178,   189,   179,   202,   175,   195,   185,
   184,   205,   206,   200,   183,   182,   177,   203,   207,   208,
   187,   176,   190,   194,   196,   188,   181,   nil,   nil,   nil,
   197,   204,   199,   198,   191,   201,   186,   174,   193,   192,
   nil,   nil,   nil,   nil,   nil,   173,   180,   171,   172,   168,
   169,   170,   129,   131,   nil,   nil,   130,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   162,   163,   nil,   159,   141,   142,
   143,   150,   147,   149,   nil,   nil,   144,   145,   nil,   nil,
   nil,   164,   165,   151,   152,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   156,   155,
   nil,   140,   161,   158,   157,   166,   153,   154,   148,   146,
   138,   160,   139,   nil,   nil,   167,    93,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    92,   178,   189,   179,   202,   175,   195,   185,   184,   205,
   206,   200,   183,   182,   177,   203,   207,   208,   187,   176,
   190,   194,   196,   188,   181,   nil,   nil,   nil,   197,   204,
   199,   198,   191,   201,   186,   174,   193,   192,   nil,   nil,
   nil,   nil,   nil,   173,   180,   171,   172,   168,   169,   170,
   129,   131,   nil,   nil,   130,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   162,   163,   nil,   159,   141,   142,   143,   150,
   147,   149,   nil,   nil,   144,   145,   nil,   nil,   nil,   164,
   165,   151,   152,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   156,   155,   nil,   140,
   161,   158,   157,   166,   153,   154,   148,   146,   138,   160,
   139,   nil,   nil,   167,    93,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    92,   178,
   189,   179,   202,   175,   195,   185,   184,   205,   206,   200,
   183,   182,   177,   203,   207,   208,   187,   176,   190,   194,
   196,   188,   181,   nil,   nil,   nil,   197,   204,   199,   380,
   379,   381,   378,   174,   193,   192,   nil,   nil,   nil,   nil,
   nil,   173,   180,   171,   172,   375,   376,   377,   373,   131,
   101,   100,   374,   nil,   103,   nil,   nil,   nil,   nil,   nil,
   162,   163,   nil,   159,   141,   142,   143,   150,   147,   149,
   nil,   nil,   144,   145,   nil,   nil,   nil,   164,   165,   151,
   152,   nil,   nil,   nil,   nil,   nil,   385,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   156,   155,   nil,   140,   161,   158,
   157,   166,   153,   154,   148,   146,   138,   160,   139,   nil,
   nil,   167,   178,   189,   179,   202,   175,   195,   185,   184,
   205,   206,   200,   183,   182,   177,   203,   207,   208,   187,
   176,   190,   194,   196,   188,   181,   nil,   nil,   nil,   197,
   204,   199,   198,   191,   201,   186,   174,   193,   192,   nil,
   nil,   nil,   nil,   nil,   173,   180,   171,   172,   168,   169,
   170,   129,   131,   nil,   nil,   130,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   162,   163,   nil,   159,   141,   142,   143,
   150,   147,   149,   nil,   nil,   144,   145,   nil,   nil,   nil,
   164,   165,   151,   152,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   156,   155,   nil,
   140,   161,   158,   157,   166,   153,   154,   148,   146,   138,
   160,   139,   435,   439,   167,   nil,   436,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   162,   163,   nil,   159,   141,   142,
   143,   150,   147,   149,   nil,   nil,   144,   145,   nil,   nil,
   nil,   164,   165,   151,   152,   nil,   nil,   nil,   nil,   nil,
   271,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   156,   155,
   nil,   140,   161,   158,   157,   166,   153,   154,   148,   146,
   138,   160,   139,   442,   446,   167,   nil,   441,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   162,   163,   nil,   159,   141,
   142,   143,   150,   147,   149,   nil,   nil,   144,   145,   nil,
   nil,   nil,   164,   165,   151,   152,   nil,   nil,   nil,   nil,
   nil,   271,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   156,
   155,   nil,   140,   161,   158,   157,   166,   153,   154,   148,
   146,   138,   160,   139,   486,   439,   167,   nil,   487,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   162,   163,   nil,   159,
   141,   142,   143,   150,   147,   149,   nil,   nil,   144,   145,
   nil,   nil,   nil,   164,   165,   151,   152,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   156,   155,   nil,   140,   161,   158,   157,   166,   153,   154,
   148,   146,   138,   160,   139,   621,   439,   167,   nil,   622,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   162,   163,   nil,
   159,   141,   142,   143,   150,   147,   149,   nil,   nil,   144,
   145,   nil,   nil,   nil,   164,   165,   151,   152,   nil,   nil,
   nil,   nil,   nil,   271,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   156,   155,   nil,   140,   161,   158,   157,   166,   153,
   154,   148,   146,   138,   160,   139,   623,   446,   167,   nil,
   624,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   162,   163,
   nil,   159,   141,   142,   143,   150,   147,   149,   nil,   nil,
   144,   145,   nil,   nil,   nil,   164,   165,   151,   152,   nil,
   nil,   nil,   nil,   nil,   271,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   156,   155,   nil,   140,   161,   158,   157,   166,
   153,   154,   148,   146,   138,   160,   139,   650,   439,   167,
   nil,   651,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   162,
   163,   nil,   159,   141,   142,   143,   150,   147,   149,   nil,
   nil,   144,   145,   nil,   nil,   nil,   164,   165,   151,   152,
   nil,   nil,   nil,   nil,   nil,   271,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   156,   155,   nil,   140,   161,   158,   157,
   166,   153,   154,   148,   146,   138,   160,   139,   653,   446,
   167,   nil,   654,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   162,   163,   nil,   159,   141,   142,   143,   150,   147,   149,
   nil,   nil,   144,   145,   nil,   nil,   nil,   164,   165,   151,
   152,   nil,   nil,   nil,   nil,   nil,   271,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   156,   155,   nil,   140,   161,   158,
   157,   166,   153,   154,   148,   146,   138,   160,   139,   621,
   439,   167,   nil,   622,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   162,   163,   nil,   159,   141,   142,   143,   150,   147,
   149,   nil,   nil,   144,   145,   nil,   nil,   nil,   164,   165,
   151,   152,   nil,   nil,   nil,   nil,   nil,   271,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   156,   155,   nil,   140,   161,
   158,   157,   166,   153,   154,   148,   146,   138,   160,   139,
   623,   446,   167,   nil,   624,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   162,   163,   nil,   159,   141,   142,   143,   150,
   147,   149,   nil,   nil,   144,   145,   nil,   nil,   nil,   164,
   165,   151,   152,   nil,   nil,   nil,   nil,   nil,   271,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   156,   155,   nil,   140,
   161,   158,   157,   166,   153,   154,   148,   146,   138,   160,
   139,   712,   439,   167,   nil,   713,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   162,   163,   nil,   159,   141,   142,   143,
   150,   147,   149,   nil,   nil,   144,   145,   nil,   nil,   nil,
   164,   165,   151,   152,   nil,   nil,   nil,   nil,   nil,   271,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   156,   155,   nil,
   140,   161,   158,   157,   166,   153,   154,   148,   146,   138,
   160,   139,   714,   446,   167,   nil,   715,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   162,   163,   nil,   159,   141,   142,
   143,   150,   147,   149,   nil,   nil,   144,   145,   nil,   nil,
   nil,   164,   165,   151,   152,   nil,   nil,   nil,   nil,   nil,
   271,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   156,   155,
   nil,   140,   161,   158,   157,   166,   153,   154,   148,   146,
   138,   160,   139,   717,   446,   167,   nil,   718,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   162,   163,   nil,   159,   141,
   142,   143,   150,   147,   149,   nil,   nil,   144,   145,   nil,
   nil,   nil,   164,   165,   151,   152,   nil,   nil,   nil,   nil,
   nil,   271,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   156,
   155,   nil,   140,   161,   158,   157,   166,   153,   154,   148,
   146,   138,   160,   139,   486,   439,   167,   nil,   487,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   162,   163,   nil,   159,
   141,   142,   143,   150,   147,   149,   nil,   nil,   144,   145,
   nil,   nil,   nil,   164,   165,   151,   152,   nil,   nil,   nil,
   nil,   nil,   271,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   156,   155,   nil,   140,   161,   158,   157,   166,   153,   154,
   148,   146,   138,   160,   139,   746,   439,   167,   nil,   747,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   162,   163,   nil,
   159,   141,   142,   143,   150,   147,   149,   nil,   nil,   144,
   145,   nil,   nil,   nil,   164,   165,   151,   152,   nil,   nil,
   nil,   nil,   nil,   271,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   156,   155,   nil,   140,   161,   158,   157,   166,   153,
   154,   148,   146,   138,   160,   139,   749,   446,   167,   nil,
   748,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   162,   163,
   nil,   159,   141,   142,   143,   150,   147,   149,   nil,   nil,
   144,   145,   nil,   nil,   nil,   164,   165,   151,   152,   nil,
   nil,   nil,   nil,   nil,   271,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   156,   155,   nil,   140,   161,   158,   157,   166,
   153,   154,   148,   146,   138,   160,   139,  1001,   446,   167,
   nil,  1000,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   162,
   163,   nil,   159,   141,   142,   143,   150,   147,   149,   nil,
   nil,   144,   145,   nil,   nil,   nil,   164,   165,   151,   152,
   nil,   nil,   nil,   nil,   nil,   271,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   156,   155,   nil,   140,   161,   158,   157,
   166,   153,   154,   148,   146,   138,   160,   139,  1004,   439,
   167,   nil,  1005,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   162,   163,   nil,   159,   141,   142,   143,   150,   147,   149,
   nil,   nil,   144,   145,   nil,   nil,   nil,   164,   165,   151,
   152,   nil,   nil,   nil,   nil,   nil,   271,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   156,   155,   nil,   140,   161,   158,
   157,   166,   153,   154,   148,   146,   138,   160,   139,  1006,
   446,   167,   nil,  1007,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   162,   163,   nil,   159,   141,   142,   143,   150,   147,
   149,   nil,   nil,   144,   145,   nil,   nil,   nil,   164,   165,
   151,   152,   nil,   nil,   nil,   nil,   nil,   271,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   156,   155,   nil,   140,   161,
   158,   157,   166,   153,   154,   148,   146,   138,   160,   139,
   nil,   682,   167,   679,   678,   677,   687,   680,   nil,   682,
   nil,   679,   678,   677,   687,   680,   690,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   690,   nil,   682,   nil,   679,   678,
   677,   687,   680,   nil,   nil,   nil,   nil,   nil,   685,   668,
   nil,   690,   nil,   nil,   nil,   nil,   685,   695,   694,   698,
   697,   nil,   nil,   nil,   691,   695,   694,   698,   697,   nil,
   nil,   nil,   691,   685,   nil,   682,   nil,   679,   678,   677,
   687,   680,   695,   694,   698,   697,   nil,   nil,   nil,   691,
   690,   nil,   682,   nil,   679,   678,   677,   687,   680,   nil,
   682,   nil,   679,   678,   677,   687,   680,   690,   nil,   nil,
   nil,   nil,   685,   nil,   nil,   690,   nil,   nil,   nil,   nil,
   nil,   695,   694,   698,   697,   nil,   nil,   nil,   691,   685,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   685,   695,   694,
   698,   697,   nil,   nil,   nil,   691,   695,   694,   698,   697,
   nil,   nil,   682,   691,   679,   678,   677,   687,   680,   nil,
   682,   nil,   679,   678,   677,   687,   680,   690,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   690,   nil,   682,   nil,   679,
   678,   677,   687,   680,   nil,   nil,   nil,   nil,   nil,   685,
   nil,   nil,   690,   nil,   nil,   nil,   nil,   685,   695,   694,
   698,   697,   nil,   nil,   nil,   691,   695,   694,   698,   697,
   nil,   nil,   nil,   691,   685,   nil,   682,   nil,   679,   678,
   677,   687,   680,   695,   694,   698,   697,   nil,   nil,   nil,
   691,   690,   nil,   682,   nil,   679,   678,   677,   687,   680,
   682,   nil,   679,   678,   677,   687,   680,   nil,   690,   nil,
   nil,   nil,   nil,   685,   nil,   690,   nil,   682,   nil,   679,
   678,   677,   687,   680,   698,   697,   nil,   nil,   nil,   691,
   685,   nil,   690,   nil,   nil,   nil,   nil,   685,   nil,   695,
   694,   698,   697,   nil,   nil,   nil,   691,   nil,   698,   697,
   nil,   nil,   nil,   691,   685,   nil,   682,   nil,   679,   678,
   677,   687,   680,   nil,   nil,   698,   697,   nil,   nil,   nil,
   691,   690,   nil,   682,   nil,   679,   678,   677,   687,   680,
   682,   nil,   679,   678,   677,   687,   680,   nil,   690,   nil,
   nil,   nil,   nil,   685,   nil,   690,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   698,   697,   nil,   nil,   nil,   691,
   685,   nil,   nil,   nil,   nil,   nil,   nil,   685,   nil,   nil,
   nil,   698,   697,   nil,   nil,   nil,   691,   nil,   698,   697,
   nil,   nil,   nil,   691 ]

racc_action_check = [
    99,   451,   451,   574,   574,    17,   354,    99,    99,    99,
     1,   346,    99,    99,    99,    24,    99,    26,    19,   392,
    44,    44,    24,   347,    99,   393,    99,    99,    99,    62,
   364,   630,   228,     7,   364,   355,    99,    99,   710,    99,
    99,    99,    99,    99,   867,   893,   924,   925,   928,   358,
   645,   560,    17,   896,   975,   896,    44,    44,   316,    19,
   645,    10,    17,    12,   712,   713,    99,    99,    99,    99,
    99,    99,    99,    99,    99,    99,    99,    99,    99,    99,
    24,    26,    99,    99,    99,   392,    99,    99,   551,   228,
    99,   393,   797,    99,    99,   451,    99,   574,    99,    13,
    99,    15,    99,    99,    26,    99,    99,    99,    99,    99,
  1004,    99,   102,    99,  1005,   354,    62,   827,   630,   102,
   102,   102,   316,  1006,   102,   102,   102,    99,   102,   346,
    99,    99,    99,    99,   346,    99,   102,    99,   102,   102,
   102,   347,    99,    99,   355,   316,   347,   650,   102,   102,
  1007,   102,   102,   102,   102,   102,   710,  1024,   358,   710,
   560,   710,   867,   893,   924,   925,   928,   867,   893,   924,
   925,   928,   975,   712,   713,   714,   634,   975,   102,   102,
   102,   102,   102,   102,   102,   102,   102,   102,   102,   102,
   102,   102,   551,    22,   102,   102,   102,   551,   102,   102,
  1006,   797,   102,    37,   651,   102,   102,    40,   102,   715,
   102,   634,   102,   650,   102,   102,    46,   102,   102,   102,
   102,   102,   441,   102,   113,   102,   827,  1007,  1004,   441,
   441,   441,  1005,  1004,   772,   441,   441,  1005,   441,   102,
   714,  1006,   102,   102,   102,   102,  1006,   102,   209,   102,
   660,   660,   229,   650,   102,   102,   650,   230,   441,   441,
   786,   441,   441,   441,   441,   441,   650,   232,  1007,   772,
   651,   373,   653,  1007,   715,  1024,    14,    14,   373,   814,
  1024,   814,   814,   814,   714,   814,   570,   570,   441,   441,
   441,   441,   441,   441,   441,   441,   441,   441,   441,   441,
   441,   441,   233,   231,   441,   441,   441,   374,   441,   229,
   651,    38,   441,   651,   374,   441,    41,    41,   715,   585,
   441,   237,   441,   651,   441,   441,   786,   441,   441,   441,
   441,   441,   375,   441,   442,   441,   373,   653,   653,   375,
   128,   442,   442,   442,   660,   128,   128,   442,   442,   441,
   442,     3,   441,   441,   270,   441,     3,   441,    38,   442,
   231,   814,   345,   345,   441,   441,   786,   362,    38,   786,
   442,   442,   374,   442,   442,   442,   442,   442,   653,   786,
   570,   653,   284,   585,   585,   570,   945,   387,   945,   945,
   945,   653,   945,   585,    41,    41,   285,   375,   587,   420,
   442,   442,   442,   442,   442,   442,   442,   442,   442,   442,
   442,   442,   442,   442,   457,   288,   442,   442,   442,   362,
   442,   321,   321,   503,   442,   296,   362,   442,   351,   621,
   296,   362,   442,   351,   442,   362,   442,   442,    39,   442,
   442,   442,   442,   442,   622,   442,   442,   442,   388,   300,
   376,   387,   387,   387,   362,   389,   420,   376,   301,   363,
   390,   442,   587,   587,   442,   442,   623,   442,   945,   442,
   303,   457,   587,   623,   623,   623,   442,   442,   623,   623,
   623,   304,   623,   305,   362,    39,   621,   503,   503,   503,
   377,   623,   623,   623,   623,    39,   311,   377,   314,   321,
   321,   622,   623,   623,   503,   623,   623,   623,   623,   623,
   325,   363,   388,   388,   388,   376,   391,   315,   363,   389,
   389,   389,   394,   363,   390,   390,   390,   363,   337,   700,
   700,   337,   623,   623,   623,   623,   623,   623,   623,   623,
   623,   623,   623,   623,   623,   623,   363,   320,   623,   623,
   623,   322,   623,   623,    16,   377,   623,   325,   378,   623,
   623,    16,   623,   326,   623,   378,   623,   325,   623,   623,
    16,   623,   623,   623,   623,   623,   363,   623,   623,   623,
   391,   391,   391,   379,    47,   380,   394,   394,   394,   463,
   379,    47,   380,   623,   329,   435,   623,   623,   623,   623,
    47,   623,   746,   623,   624,   747,   769,   436,   623,   623,
   831,   624,   624,   624,   335,   831,   624,   624,   624,    16,
   624,   463,   339,   378,   340,   463,   463,   381,   383,   464,
   624,   624,   624,   338,   381,   383,   338,   342,   606,   840,
   624,   624,   435,   624,   624,   624,   624,   624,   379,    47,
   380,   227,   435,   308,   436,   802,   802,   352,   227,   746,
   308,   464,   747,   769,   436,   464,   464,   227,   353,   308,
   624,   624,   624,   624,   624,   624,   624,   624,   624,   624,
   624,   624,   624,   624,   836,   606,   624,   624,   624,   836,
   624,   624,   381,   383,   624,   606,   840,   624,   624,   357,
   624,   359,   624,   368,   624,   341,   624,   624,   341,   624,
   624,   624,   624,   624,   748,   624,   227,   624,   308,   986,
   986,   748,   748,   748,   403,   409,   666,   748,   748,   666,
   748,   624,   471,   412,   624,   624,   624,   624,   414,   624,
    81,   624,   717,   417,   421,   431,   624,   624,   471,   471,
   748,   748,    81,   748,   748,   748,   748,   748,   964,   309,
   433,   964,    81,   310,   471,   434,   309,   443,   471,   471,
   310,   471,   471,   453,   465,   309,   466,   467,   468,   310,
   748,   748,   748,   748,   748,   748,   748,   748,   748,   748,
   748,   748,   748,   748,   717,   312,   748,   748,   748,   490,
   748,   717,   312,   494,   748,   510,   717,   748,   511,   514,
   717,   312,   748,   516,   748,   521,   748,   748,   524,   748,
   748,   748,   748,   748,   309,   748,   749,   748,   310,   717,
   533,   534,   535,   749,   749,   749,   799,   536,   548,   749,
   749,   748,   749,   552,   748,   748,   799,   748,   327,   748,
   553,   749,   554,   555,   572,   327,   748,   748,   582,   717,
   312,   590,   749,   749,   327,   749,   749,   749,   749,   749,
   890,   356,   890,   890,   890,   592,   890,   598,   356,   799,
   799,   607,   612,   617,   799,   578,   578,   356,   625,   578,
   578,   578,   749,   749,   749,   749,   749,   749,   749,   749,
   749,   749,   749,   749,   749,   749,   626,   890,   749,   749,
   749,   366,   749,   327,   419,   627,   749,   629,   366,   749,
   633,   419,   635,   637,   749,   639,   749,   366,   749,   749,
   419,   749,   749,   749,   749,   749,   356,   749,   749,   749,
   778,   647,   778,   778,   778,   778,   778,   520,     6,     6,
     6,     6,     6,   749,   520,   778,   749,   749,    27,   749,
   649,   749,   652,   520,   655,    27,    27,    27,   749,   749,
    27,    27,    27,   656,    27,   659,   366,   778,   661,   419,
   670,   478,   671,    27,    27,    27,   778,   778,   778,   778,
   673,   674,   675,   778,    27,    27,   684,    27,    27,    27,
    27,    27,   946,   692,   946,   946,   946,   563,   946,   696,
   699,   702,   520,   478,   563,   708,   711,   478,   478,   778,
   478,   478,   720,   563,    27,    27,    27,    27,    27,    27,
    27,    27,    27,    27,    27,    27,    27,    27,   724,   946,
    27,    27,    27,   743,   745,    27,   754,    27,    27,   775,
   785,    27,    27,   789,    27,   792,    27,   793,    27,   798,
    27,    27,   813,    27,    27,    27,    27,    27,    28,    27,
    27,    27,   563,   815,   820,    28,    28,    28,   823,   828,
    28,    28,    28,   920,    28,    27,   830,   834,    27,    27,
   920,    27,   835,    27,    28,    28,   838,   839,   848,   920,
    27,   849,   851,   852,    28,    28,   853,    28,    28,    28,
    28,    28,   855,   856,   857,   654,   858,   718,   873,   874,
   878,   879,   654,   881,   718,   882,   884,   654,   887,   718,
   889,   654,   900,   718,    28,    28,    28,    28,    28,    28,
    28,    28,    28,    28,    28,    28,    28,    28,   920,   904,
    28,    28,    28,   917,   921,    28,   927,    28,    28,   947,
   954,    28,    28,   957,    28,   958,    28,   959,    28,   960,
    28,    28,   962,    28,    28,    28,    28,    28,    57,    28,
   654,    28,   718,   971,   976,    57,    57,    57,   977,   978,
    57,    57,    57,   926,    57,    28,   979,   980,    28,    28,
   926,    28,   981,    28,    57,    57,    57,   982,   984,   926,
    28,   987,  1000,  1001,    57,    57,  1003,    57,    57,    57,
    57,    57,   918,   968,   918,   918,   918,  1016,   918,   685,
   968,   685,   685,   685,   999,   685,   999,   999,   999,   968,
   999,  1019,  1020,  1021,    57,    57,    57,    57,    57,    57,
    57,    57,    57,    57,    57,    57,    57,    57,   926,   907,
    57,    57,    57,  1022,  1025,    57,   685,  1026,    57,   907,
  1033,    57,    57,   nil,    57,   685,    57,   nil,    57,   nil,
    57,    57,   nil,    57,    57,    57,    57,    57,   968,    57,
   nil,    57,   811,   nil,   811,   811,   811,   nil,   811,   nil,
   nil,   nil,   907,   907,   nil,    57,   nil,   907,    57,    57,
    57,    57,   nil,    57,   437,    57,   nil,   nil,   nil,   nil,
    57,   437,   437,   437,   nil,   nil,   437,   437,   437,   811,
   437,   888,   nil,   888,   888,   888,   nil,   888,   811,   437,
   437,   437,   983,   nil,   983,   983,   983,   nil,   983,   nil,
   437,   437,   nil,   437,   437,   437,   437,   437,   985,   nil,
   985,   985,   985,  1015,   985,  1015,  1015,  1015,   888,  1015,
   299,   299,   299,   299,   299,   nil,   nil,   888,   nil,   983,
   437,   437,   437,   437,   437,   437,   437,   437,   437,   437,
   437,   437,   437,   437,   nil,   985,   437,   437,   437,   nil,
  1015,   437,   nil,   437,   437,   nil,   nil,   437,   437,   nil,
   437,   nil,   437,   nil,   437,   nil,   437,   437,   nil,   437,
   437,   437,   437,   437,   nil,   437,   437,   437,   807,   nil,
   807,   807,   807,   807,   807,   334,   334,   334,   334,   334,
   nil,   437,   nil,   807,   437,   437,   446,   437,   nil,   437,
   nil,   nil,   nil,   446,   446,   446,   437,   nil,   446,   446,
   446,   nil,   446,   483,   nil,   807,   508,   508,   508,   508,
   508,   446,   446,   446,   446,   nil,   807,   807,   nil,   483,
   483,   807,   446,   446,   nil,   446,   446,   446,   446,   446,
   nil,   nil,   nil,   nil,   nil,   483,   nil,   483,   nil,   483,
   483,   nil,   483,   483,   nil,   nil,   483,   nil,   483,   nil,
   nil,   nil,   446,   446,   446,   446,   446,   446,   446,   446,
   446,   446,   446,   446,   446,   446,   nil,   nil,   446,   446,
   446,   479,   nil,   446,   nil,   nil,   446,   nil,   nil,   446,
   446,   nil,   446,   nil,   446,   nil,   446,   nil,   446,   446,
   nil,   446,   446,   446,   446,   446,   nil,   446,   446,   446,
   nil,   nil,   nil,   479,   nil,   nil,   nil,   479,   479,   nil,
   479,   479,   nil,   446,   nil,   nil,   446,   446,   446,   446,
   nil,   446,   447,   446,   nil,   nil,   nil,   nil,   446,   447,
   447,   447,   nil,   nil,   447,   447,   447,   530,   447,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   447,   447,   447,
   447,   nil,   nil,   530,   530,   nil,   nil,   nil,   447,   447,
   nil,   447,   447,   447,   447,   447,   nil,   nil,   nil,   530,
   nil,   530,   nil,   530,   530,   nil,   530,   530,   nil,   nil,
   530,   nil,   530,   nil,   nil,   nil,   nil,   nil,   447,   447,
   447,   447,   447,   447,   447,   447,   447,   447,   447,   447,
   447,   447,   nil,   nil,   447,   447,   447,   nil,   nil,   447,
   nil,   nil,   447,   nil,   nil,   447,   447,   nil,   447,   nil,
   447,   nil,   447,   nil,   447,   447,   nil,   447,   447,   447,
   447,   447,   nil,   447,   447,   447,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   447,
   nil,   nil,   447,   447,   447,   447,   nil,   447,   485,   447,
   nil,   nil,   nil,   nil,   447,   485,   485,   485,   nil,   nil,
   485,   485,   485,   469,   485,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   485,   485,   nil,   nil,   nil,   nil,   469,
   469,   nil,   nil,   nil,   485,   485,   nil,   485,   485,   485,
   485,   485,   nil,   nil,   nil,   469,   nil,   469,   nil,   469,
   469,   nil,   469,   469,   nil,   nil,   nil,    21,    21,    21,
    21,    21,    21,    21,    21,    21,    21,    21,   nil,    21,
    21,   nil,   nil,    21,    21,   485,   nil,   nil,   nil,   nil,
   nil,   nil,   485,   nil,   nil,   nil,   nil,   485,   485,    21,
   nil,    21,   nil,    21,    21,   nil,    21,    21,    21,    21,
    21,   nil,    21,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   485,   485,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    21,   nil,   nil,   485,   nil,   nil,   485,   nil,
   nil,   nil,   nil,   485,     0,     0,     0,     0,     0,     0,
   485,   nil,   nil,     0,     0,   nil,   nil,   nil,     0,   nil,
     0,     0,     0,     0,     0,     0,     0,   nil,   nil,   nil,
   nil,   nil,     0,     0,     0,     0,     0,     0,     0,   nil,
   nil,     0,   nil,   nil,   nil,   nil,   424,     0,     0,     0,
     0,     0,     0,     0,     0,     0,     0,     0,     0,   nil,
     0,     0,     0,   nil,     0,     0,     0,     0,     0,   424,
   424,   424,   424,   424,   424,   424,   424,   424,   424,   424,
   nil,   424,   424,   nil,   nil,   424,   424,   nil,     0,   nil,
   nil,     0,   nil,   nil,     0,     0,   nil,   nil,     0,   nil,
     0,   424,   nil,   424,     0,   424,   424,   nil,   424,   424,
   424,   424,   424,     0,   424,   nil,   nil,   nil,     0,     0,
     0,     0,   nil,     0,     0,     0,     0,   nil,   nil,   nil,
   nil,     0,     0,   nil,   424,   nil,   424,   nil,   nil,     0,
   nil,     0,     0,     0,    33,    33,    33,    33,    33,    33,
   nil,   nil,   nil,    33,    33,   nil,   nil,   nil,    33,   nil,
    33,    33,    33,    33,    33,    33,    33,   nil,   nil,   nil,
   nil,   nil,    33,    33,    33,    33,    33,    33,    33,   nil,
   nil,    33,   nil,   nil,   nil,   nil,   430,    33,    33,    33,
    33,    33,    33,    33,    33,    33,    33,    33,    33,   nil,
    33,    33,    33,   nil,    33,    33,    33,    33,    33,   430,
   430,   430,   430,   430,   430,   430,   430,   430,   430,   430,
   nil,   430,   430,   nil,   nil,   430,   430,   nil,    33,   nil,
   nil,    33,   nil,   nil,    33,    33,   nil,   nil,    33,   nil,
    33,   430,   nil,   430,    33,   430,   430,   nil,   430,   430,
   430,   430,   430,    33,   430,   nil,   nil,   nil,    33,    33,
    33,    33,   nil,    33,    33,    33,    33,   nil,   nil,   nil,
   nil,    33,    33,   nil,   430,   nil,   nil,   nil,   nil,    33,
   nil,    33,    33,    33,   126,   126,   126,   126,   126,   126,
   nil,   nil,   nil,   126,   126,   nil,   nil,   nil,   126,   nil,
   126,   126,   126,   126,   126,   126,   126,   nil,   nil,   nil,
   nil,   nil,   126,   126,   126,   126,   126,   126,   126,   nil,
   nil,   126,   nil,   nil,   nil,   nil,   nil,   126,   126,   126,
   126,   126,   126,   126,   126,   126,   126,   126,   126,   nil,
   126,   126,   126,   nil,   126,   126,   126,   126,   126,   282,
   282,   282,   282,   282,   282,   282,   282,   282,   282,   282,
   nil,   282,   282,   nil,   nil,   282,   282,   nil,   126,   nil,
   nil,   126,   nil,   nil,   126,   126,   nil,   nil,   126,   nil,
   126,   282,   nil,   282,   126,   282,   282,   nil,   282,   282,
   282,   282,   282,   126,   282,   nil,   nil,   nil,   126,   126,
   126,   126,   nil,   126,   126,   126,   126,   nil,   nil,   nil,
   nil,   126,   126,   nil,   282,   nil,   nil,   nil,   nil,   126,
   nil,   126,   126,   126,   211,   211,   211,   211,   211,   211,
   nil,   nil,   nil,   211,   211,   nil,   nil,   nil,   211,   nil,
   211,   211,   211,   211,   211,   211,   211,   nil,   nil,   nil,
   nil,   nil,   211,   211,   211,   211,   211,   211,   211,   nil,
   nil,   211,   nil,   nil,   nil,   nil,   nil,   211,   211,   211,
   211,   211,   211,   211,   211,   211,   211,   211,   211,   nil,
   211,   211,   211,   nil,   211,   211,   211,   211,   211,   482,
   482,   482,   482,   482,   482,   482,   482,   482,   482,   482,
   nil,   482,   482,   nil,   nil,   482,   482,   nil,   211,   nil,
   nil,   211,   nil,   nil,   211,   211,   nil,   nil,   211,   nil,
   211,   482,   nil,   482,   211,   482,   482,   nil,   482,   482,
   482,   482,   482,   211,   482,   nil,   nil,   nil,   211,   211,
   211,   211,   nil,   211,   211,   211,   211,   nil,   nil,   nil,
   nil,   211,   211,   482,   482,   nil,   nil,   nil,   nil,   211,
   nil,   211,   211,   211,   236,   236,   236,   236,   236,   236,
   nil,   nil,   nil,   236,   236,   nil,   nil,   nil,   236,   nil,
   236,   236,   236,   236,   236,   236,   236,   nil,   nil,   nil,
   nil,   nil,   236,   236,   236,   236,   236,   236,   236,   nil,
   nil,   236,   nil,   nil,   nil,   nil,   nil,   236,   236,   236,
   236,   236,   236,   236,   236,   236,   236,   236,   236,   nil,
   236,   236,   236,   nil,   236,   236,   236,   236,   236,   531,
   531,   531,   531,   531,   531,   531,   531,   531,   531,   531,
   nil,   531,   531,   nil,   nil,   531,   531,   nil,   236,   nil,
   nil,   236,   nil,   nil,   236,   236,   nil,   nil,   236,   nil,
   236,   531,   nil,   531,   236,   531,   531,   nil,   531,   531,
   531,   531,   531,   236,   531,   nil,   nil,   nil,   236,   236,
   236,   236,   nil,   236,   236,   236,   236,   nil,   nil,   nil,
   nil,   236,   236,   nil,   531,   nil,   nil,   nil,   nil,   236,
   nil,   236,   236,   236,   302,   302,   302,   302,   302,   302,
   nil,   nil,   nil,   302,   302,   nil,   nil,   nil,   302,   nil,
   302,   302,   302,   302,   302,   302,   302,   nil,   nil,   nil,
   nil,   nil,   302,   302,   302,   302,   302,   302,   302,   nil,
   nil,   302,   nil,   nil,   nil,   nil,   nil,   302,   302,   302,
   302,   302,   302,   302,   302,   302,   302,   302,   302,   nil,
   302,   302,   302,   nil,   302,   302,   302,   302,   302,   751,
   751,   751,   751,   751,   751,   751,   751,   751,   751,   751,
   nil,   751,   751,   nil,   nil,   751,   751,   nil,   302,   nil,
   nil,   302,   nil,   nil,   302,   302,   nil,   nil,   302,   nil,
   302,   751,   nil,   751,   302,   751,   751,   nil,   751,   751,
   751,   751,   751,   302,   751,   nil,   nil,   nil,   302,   302,
   302,   302,   nil,   302,   302,   302,   302,   nil,   nil,   nil,
   nil,   302,   302,   nil,   751,   nil,   nil,   nil,   nil,   302,
   nil,   302,   302,   302,   307,   307,   307,   307,   307,   307,
   nil,   nil,   nil,   307,   307,   nil,   nil,   nil,   307,   nil,
   307,   307,   307,   307,   307,   307,   307,   nil,   nil,   nil,
   nil,   nil,   307,   307,   307,   307,   307,   307,   307,   nil,
   nil,   307,   nil,   nil,   nil,   nil,   nil,   307,   307,   307,
   307,   307,   307,   307,   307,   307,   307,   307,   307,   nil,
   307,   307,   307,   nil,   307,   307,   307,   307,   307,   845,
   845,   845,   845,   845,   845,   845,   845,   845,   845,   845,
   nil,   845,   845,   nil,   nil,   845,   845,   nil,   307,   nil,
   nil,   307,   nil,   nil,   307,   307,   nil,   nil,   307,   nil,
   307,   845,   nil,   845,   307,   845,   845,   nil,   845,   845,
   845,   845,   845,   307,   845,   nil,   nil,   nil,   307,   307,
   307,   307,   nil,   307,   307,   307,   307,   nil,   nil,   nil,
   nil,   307,   307,   nil,   845,   nil,   nil,   nil,   nil,   307,
   nil,   307,   307,   307,   333,   333,   333,   333,   333,   333,
   nil,   nil,   nil,   333,   333,   nil,   nil,   nil,   333,   nil,
   333,   333,   333,   333,   333,   333,   333,   nil,   nil,   nil,
   nil,   nil,   333,   333,   333,   333,   333,   333,   333,   nil,
   nil,   333,   nil,   nil,   nil,   nil,   nil,   333,   333,   333,
   333,   333,   333,   333,   333,   333,   333,   333,   333,   nil,
   333,   333,   333,   nil,   333,   333,   333,   333,   333,   461,
   461,   461,   461,   461,   461,   461,   461,   461,   461,   461,
   nil,   461,   461,   nil,   nil,   461,   461,   nil,   333,   nil,
   nil,   333,   nil,   nil,   333,   333,   nil,   nil,   333,   nil,
   333,   461,   nil,   461,   333,   461,   461,   nil,   461,   461,
   461,   461,   461,   333,   461,   nil,   nil,   nil,   333,   333,
   333,   333,   nil,   333,   333,   333,   333,   nil,   nil,   nil,
   nil,   333,   333,   nil,   nil,   nil,   nil,   nil,   nil,   333,
   nil,   333,   333,   333,   349,   349,   349,   349,   349,   349,
   nil,   nil,   nil,   349,   349,   nil,   nil,   nil,   349,   nil,
   349,   349,   349,   349,   349,   349,   349,   nil,   nil,   nil,
   nil,   nil,   349,   349,   349,   349,   349,   349,   349,   nil,
   nil,   349,   nil,   nil,   nil,   nil,   nil,   349,   349,   349,
   349,   349,   349,   349,   349,   349,   349,   349,   349,   nil,
   349,   349,   349,   nil,   349,   349,   349,   349,   349,   462,
   462,   462,   462,   462,   462,   462,   462,   462,   462,   462,
   nil,   462,   462,   nil,   nil,   462,   462,   nil,   349,   nil,
   nil,   349,   nil,   nil,   349,   349,   nil,   nil,   349,   nil,
   349,   462,   nil,   462,   349,   462,   462,   nil,   462,   462,
   462,   462,   462,   349,   462,   nil,   nil,   nil,   349,   349,
   349,   349,   nil,   349,   349,   349,   349,   nil,   nil,   nil,
   nil,   349,   349,   nil,   nil,   nil,   nil,   nil,   nil,   349,
   nil,   349,   349,   349,   350,   350,   350,   350,   350,   350,
   nil,   nil,   nil,   350,   350,   nil,   nil,   nil,   350,   nil,
   350,   350,   350,   350,   350,   350,   350,   nil,   nil,   nil,
   nil,   nil,   350,   350,   350,   350,   350,   350,   350,   nil,
   nil,   350,   nil,   nil,   nil,   nil,   nil,   350,   350,   350,
   350,   350,   350,   350,   350,   350,   350,   350,   350,   nil,
   350,   350,   350,   nil,   350,   350,   350,   350,   350,   472,
   472,   472,   472,   472,   472,   472,   nil,   nil,   472,   472,
   nil,   nil,   nil,   nil,   nil,   472,   472,   nil,   350,   nil,
   nil,   350,   nil,   nil,   350,   350,   nil,   nil,   350,   nil,
   350,   472,   nil,   472,   350,   472,   472,   nil,   472,   472,
   472,   472,   472,   350,   472,   nil,   nil,   nil,   350,   350,
   350,   350,   nil,   350,   350,   350,   350,   nil,   nil,   nil,
   nil,   350,   350,   nil,   nil,   nil,   nil,   nil,   nil,   350,
   nil,   350,   350,   350,   547,   547,   547,   547,   547,   547,
   nil,   nil,   nil,   547,   547,   nil,   nil,   nil,   547,   nil,
   547,   547,   547,   547,   547,   547,   547,   nil,   nil,   nil,
   nil,   nil,   547,   547,   547,   547,   547,   547,   547,   nil,
   nil,   547,   nil,   nil,   nil,   nil,   nil,   547,   547,   547,
   547,   547,   547,   547,   547,   547,   547,   547,   547,   nil,
   547,   547,   547,   nil,   547,   547,   547,   547,   547,   473,
   473,   473,   473,   473,   473,   473,   nil,   nil,   473,   473,
   nil,   nil,   nil,   nil,   nil,   473,   473,   nil,   547,   nil,
   nil,   547,   nil,   nil,   547,   547,   nil,   nil,   547,   nil,
   547,   473,   nil,   473,   547,   473,   473,   nil,   473,   473,
   473,   473,   473,   547,   473,   nil,   nil,   nil,   547,   547,
   547,   547,   nil,   547,   547,   547,   547,   nil,   nil,   nil,
   nil,   547,   547,   nil,   nil,   nil,   nil,   nil,   nil,   547,
   nil,   547,   547,   547,   550,   550,   550,   550,   550,   550,
   nil,   nil,   nil,   550,   550,   nil,   nil,   nil,   550,   nil,
   550,   550,   550,   550,   550,   550,   550,   nil,   nil,   nil,
   nil,   nil,   550,   550,   550,   550,   550,   550,   550,   nil,
   nil,   550,   nil,   nil,   nil,   nil,   nil,   550,   550,   550,
   550,   550,   550,   550,   550,   550,   550,   550,   550,   nil,
   550,   550,   550,   nil,   550,   550,   550,   550,   550,   474,
   474,   474,   474,   474,   474,   474,   nil,   nil,   474,   474,
   nil,   nil,   nil,   nil,   nil,   474,   474,   nil,   550,   nil,
   nil,   550,   nil,   nil,   550,   550,   nil,   nil,   550,   nil,
   550,   474,   nil,   474,   550,   474,   474,   nil,   474,   474,
   474,   474,   474,   550,   474,   nil,   nil,   nil,   550,   550,
   550,   550,   nil,   550,   550,   550,   550,   nil,   nil,   nil,
   nil,   550,   550,   nil,   nil,   nil,   nil,   nil,   nil,   550,
   nil,   550,   550,   550,   571,   571,   571,   571,   571,   571,
   nil,   nil,   nil,   571,   571,   nil,   nil,   nil,   571,   nil,
   571,   571,   571,   571,   571,   571,   571,   nil,   nil,   nil,
   nil,   nil,   571,   571,   571,   571,   571,   571,   571,   nil,
   nil,   571,   nil,   nil,   nil,   nil,   nil,   571,   571,   571,
   571,   571,   571,   571,   571,   571,   571,   571,   571,   nil,
   571,   571,   571,   nil,   571,   571,   571,   571,   571,   475,
   475,   475,   475,   475,   475,   475,   nil,   nil,   475,   475,
   nil,   nil,   nil,   nil,   nil,   475,   475,   nil,   571,   nil,
   nil,   571,   nil,   nil,   571,   571,   nil,   nil,   571,   nil,
   571,   475,   nil,   475,   571,   475,   475,   nil,   475,   475,
   475,   475,   475,   571,   475,   nil,   nil,   nil,   571,   571,
   571,   571,   nil,   571,   571,   571,   571,   nil,   nil,   nil,
   nil,   571,   571,   nil,   nil,   nil,   nil,   nil,   nil,   571,
   nil,   571,   571,   571,   716,   716,   716,   716,   716,   716,
   nil,   nil,   nil,   716,   716,   nil,   nil,   nil,   716,   nil,
   716,   716,   716,   716,   716,   716,   716,   nil,   nil,   nil,
   nil,   nil,   716,   716,   716,   716,   716,   716,   716,   nil,
   nil,   716,   nil,   nil,   nil,   nil,   nil,   716,   716,   716,
   716,   716,   716,   716,   716,   716,   716,   716,   716,   nil,
   716,   716,   716,   nil,   716,   716,   716,   716,   716,   476,
   476,   476,   476,   476,   476,   476,   nil,   nil,   476,   476,
   nil,   nil,   nil,   nil,   nil,   476,   476,   nil,   716,   nil,
   nil,   716,   nil,   nil,   716,   716,   nil,   nil,   716,   nil,
   716,   476,   nil,   476,   716,   476,   476,   nil,   476,   476,
   476,   476,   476,   716,   476,   nil,   nil,   nil,   716,   716,
   716,   716,   nil,   716,   716,   716,   716,   nil,   nil,   nil,
   nil,   716,   716,   nil,   nil,   nil,   nil,   nil,   nil,   716,
   nil,   716,   716,   716,   721,   721,   721,   721,   721,   721,
   nil,   nil,   nil,   721,   721,   nil,   nil,   nil,   721,   nil,
   721,   721,   721,   721,   721,   721,   721,   nil,   nil,   nil,
   nil,   nil,   721,   721,   721,   721,   721,   721,   721,   nil,
   nil,   721,   nil,   nil,   nil,   nil,   nil,   721,   721,   721,
   721,   721,   721,   721,   721,   721,   721,   721,   721,   nil,
   721,   721,   721,   nil,   721,   721,   721,   721,   721,   477,
   477,   477,   477,   477,   477,   477,   nil,   nil,   477,   477,
   nil,   nil,   nil,   nil,   nil,   477,   477,   nil,   721,   nil,
   nil,   721,   nil,   nil,   721,   721,   nil,   nil,   721,   nil,
   721,   477,   nil,   477,   721,   477,   477,   nil,   477,   477,
   477,   477,   477,   721,   477,   nil,   nil,   nil,   721,   721,
   721,   721,   nil,   721,   721,   721,   721,   nil,   nil,   nil,
   nil,   721,   721,   nil,   nil,   nil,   nil,   nil,   nil,   721,
   nil,   721,   721,   721,   725,   725,   725,   725,   725,   725,
   nil,   nil,   nil,   725,   725,   nil,   nil,   nil,   725,   nil,
   725,   725,   725,   725,   725,   725,   725,   nil,   nil,   nil,
   nil,   nil,   725,   725,   725,   725,   725,   725,   725,   nil,
   nil,   725,   nil,   nil,   nil,   nil,   nil,   725,   725,   725,
   725,   725,   725,   725,   725,   725,   725,   725,   725,   nil,
   725,   725,   725,   nil,   725,   725,   725,   725,   725,   480,
   480,   480,   480,   480,   480,   480,   nil,   nil,   480,   480,
   nil,   nil,   nil,   nil,   nil,   480,   480,   nil,   725,   nil,
   nil,   725,   nil,   nil,   725,   725,   nil,   nil,   725,   nil,
   725,   480,   nil,   480,   725,   480,   480,   nil,   480,   480,
   480,   480,   480,   725,   480,   nil,   nil,   nil,   725,   725,
   725,   725,   nil,   725,   725,   725,   725,   nil,   nil,   nil,
   nil,   725,   725,   nil,   nil,   nil,   nil,   nil,   nil,   725,
   nil,   725,   725,   725,   735,   735,   735,   735,   735,   735,
   nil,   nil,   nil,   735,   735,   nil,   nil,   nil,   735,   nil,
   735,   735,   735,   735,   735,   735,   735,   nil,   nil,   nil,
   nil,   nil,   735,   735,   735,   735,   735,   735,   735,   nil,
   nil,   735,   nil,   nil,   nil,   nil,   nil,   735,   735,   735,
   735,   735,   735,   735,   735,   735,   735,   735,   735,   nil,
   735,   735,   735,   nil,   735,   735,   735,   735,   735,   481,
   481,   481,   481,   481,   481,   481,   481,   nil,   481,   481,
   nil,   nil,   nil,   nil,   nil,   481,   481,   nil,   735,   nil,
   nil,   735,   nil,   nil,   735,   735,   nil,   nil,   735,   nil,
   735,   481,   nil,   481,   735,   481,   481,   nil,   481,   481,
   481,   481,   481,   735,   481,   nil,   nil,   nil,   735,   735,
   735,   735,   nil,   735,   735,   735,   735,   nil,   nil,   nil,
   nil,   735,   735,   nil,   nil,   nil,   nil,   nil,   nil,   735,
   nil,   735,   735,   735,   780,   780,   780,   780,   780,   780,
   nil,   nil,   nil,   780,   780,   nil,   nil,   nil,   780,   nil,
   780,   780,   780,   780,   780,   780,   780,   nil,   nil,   nil,
   nil,   nil,   780,   780,   780,   780,   780,   780,   780,   nil,
   nil,   780,   nil,   nil,   nil,   nil,   nil,   780,   780,   780,
   780,   780,   780,   780,   780,   780,   780,   780,   780,   nil,
   780,   780,   780,   nil,   780,   780,   780,   780,   780,   470,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   470,   470,   nil,   780,   nil,
   nil,   780,   nil,   nil,   780,   780,   nil,   nil,   780,   nil,
   780,   470,   nil,   470,   780,   470,   470,   nil,   470,   470,
   nil,   nil,   nil,   780,   nil,   nil,   nil,   nil,   780,   780,
   780,   780,   nil,   780,   780,   780,   780,   nil,   nil,   nil,
   nil,   780,   780,   nil,   nil,   nil,   nil,   nil,   nil,   780,
   nil,   780,   780,   780,   791,   791,   791,   791,   791,   791,
   nil,   nil,   nil,   791,   791,   nil,   nil,   nil,   791,   nil,
   791,   791,   791,   791,   791,   791,   791,   nil,   nil,   nil,
   nil,   nil,   791,   791,   791,   791,   791,   791,   791,   nil,
   nil,   791,   nil,   nil,   nil,   nil,   nil,   791,   791,   791,
   791,   791,   791,   791,   791,   791,   791,   791,   791,   nil,
   791,   791,   791,   nil,   791,   791,   791,   791,   791,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   791,   nil,
   nil,   791,   nil,   nil,   791,   791,   nil,   nil,   791,   nil,
   791,   nil,   nil,   nil,   791,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   791,   nil,   nil,   nil,   nil,   791,   791,
   791,   791,   nil,   791,   791,   791,   791,   nil,   nil,   nil,
   nil,   791,   791,   nil,   nil,   nil,   nil,   nil,   nil,   791,
   nil,   791,   791,   791,   826,   826,   826,   826,   826,   826,
   nil,   nil,   nil,   826,   826,   nil,   nil,   nil,   826,   nil,
   826,   826,   826,   826,   826,   826,   826,   nil,   nil,   nil,
   nil,   nil,   826,   826,   826,   826,   826,   826,   826,   nil,
   nil,   826,   nil,   nil,   nil,   nil,   nil,   826,   826,   826,
   826,   826,   826,   826,   826,   826,   826,   826,   826,   nil,
   826,   826,   826,   nil,   826,   826,   826,   826,   826,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   826,   nil,
   nil,   826,   nil,   nil,   826,   826,   nil,   nil,   826,   nil,
   826,   nil,   nil,   nil,   826,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   826,   nil,   nil,   nil,   nil,   826,   826,
   826,   826,   nil,   826,   826,   826,   826,   nil,   nil,   nil,
   nil,   826,   826,   nil,   nil,   nil,   nil,   nil,   nil,   826,
   nil,   826,   826,   826,   832,   832,   832,   832,   832,   832,
   nil,   nil,   nil,   832,   832,   nil,   nil,   nil,   832,   nil,
   832,   832,   832,   832,   832,   832,   832,   nil,   nil,   nil,
   nil,   nil,   832,   832,   832,   832,   832,   832,   832,   nil,
   nil,   832,   nil,   nil,   nil,   nil,   nil,   832,   832,   832,
   832,   832,   832,   832,   832,   832,   832,   832,   832,   nil,
   832,   832,   832,   nil,   832,   832,   832,   832,   832,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   832,   nil,
   nil,   832,   nil,   nil,   832,   832,   nil,   nil,   832,   nil,
   832,   nil,   nil,   nil,   832,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   832,   nil,   nil,   nil,   nil,   832,   832,
   832,   832,   nil,   832,   832,   832,   832,   nil,   nil,   nil,
   nil,   832,   832,   nil,   nil,   nil,   nil,   nil,   nil,   832,
   nil,   832,   832,   832,   846,   846,   846,   846,   846,   846,
   nil,   nil,   nil,   846,   846,   nil,   nil,   nil,   846,   nil,
   846,   846,   846,   846,   846,   846,   846,   nil,   nil,   nil,
   nil,   nil,   846,   846,   846,   846,   846,   846,   846,   nil,
   nil,   846,   nil,   nil,   nil,   nil,   nil,   846,   846,   846,
   846,   846,   846,   846,   846,   846,   846,   846,   846,   nil,
   846,   846,   846,   nil,   846,   846,   846,   846,   846,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   846,   nil,
   nil,   846,   nil,   nil,   846,   846,   nil,   nil,   846,   nil,
   846,   nil,   nil,   nil,   846,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   846,   nil,   nil,   nil,   nil,   846,   846,
   846,   846,   nil,   846,   846,   846,   846,   nil,   nil,   nil,
   nil,   846,   846,   nil,   nil,   nil,   nil,   nil,   nil,   846,
   nil,   846,   846,   846,   864,   864,   864,   864,   864,   864,
   nil,   nil,   nil,   864,   864,   nil,   nil,   nil,   864,   nil,
   864,   864,   864,   864,   864,   864,   864,   nil,   nil,   nil,
   nil,   nil,   864,   864,   864,   864,   864,   864,   864,   nil,
   nil,   864,   nil,   nil,   nil,   nil,   nil,   864,   864,   864,
   864,   864,   864,   864,   864,   864,   864,   864,   864,   nil,
   864,   864,   864,   nil,   864,   864,   864,   864,   864,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   864,   nil,
   nil,   864,   nil,   nil,   864,   864,   nil,   nil,   864,   nil,
   864,   nil,   nil,   nil,   864,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   864,   nil,   nil,   nil,   nil,   864,   864,
   864,   864,   nil,   864,   864,   864,   864,   nil,   nil,   nil,
   nil,   864,   864,   nil,   nil,   nil,   nil,   nil,   nil,   864,
   nil,   864,   864,   864,   923,   923,   923,   923,   923,   923,
   nil,   nil,   nil,   923,   923,   nil,   nil,   nil,   923,   nil,
   923,   923,   923,   923,   923,   923,   923,   nil,   nil,   nil,
   nil,   nil,   923,   923,   923,   923,   923,   923,   923,   nil,
   nil,   923,   nil,   nil,   nil,   nil,   nil,   923,   923,   923,
   923,   923,   923,   923,   923,   923,   923,   923,   923,   nil,
   923,   923,   923,   nil,   923,   923,   923,   923,   923,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   923,   nil,
   nil,   923,   nil,   nil,   923,   923,   nil,   nil,   923,   nil,
   923,   nil,   nil,   nil,   923,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   923,   nil,   nil,   nil,   nil,   923,   923,
   923,   923,   nil,   923,   923,   923,   923,   nil,   nil,   nil,
   nil,   923,   923,   nil,   nil,   nil,   nil,   nil,   nil,   923,
   nil,   923,   923,   923,   930,   930,   930,   930,   930,   930,
   nil,   nil,   nil,   930,   930,   nil,   nil,   nil,   930,   nil,
   930,   930,   930,   930,   930,   930,   930,   nil,   nil,   nil,
   nil,   nil,   930,   930,   930,   930,   930,   930,   930,   nil,
   nil,   930,   nil,   nil,   nil,   nil,   nil,   930,   930,   930,
   930,   930,   930,   930,   930,   930,   930,   930,   930,   nil,
   930,   930,   930,   nil,   930,   930,   930,   930,   930,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   930,   nil,
   nil,   930,   nil,   nil,   930,   930,   nil,   nil,   930,   nil,
   930,   nil,   nil,   nil,   930,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   930,   nil,   nil,   nil,   nil,   930,   930,
   930,   930,   nil,   930,   930,   930,   930,   nil,   nil,   nil,
   nil,   930,   930,   nil,   nil,   nil,   nil,   nil,   nil,   930,
   nil,   930,   930,   930,   931,   931,   931,   931,   931,   931,
   nil,   nil,   nil,   931,   931,   nil,   nil,   nil,   931,   nil,
   931,   931,   931,   931,   931,   931,   931,   nil,   nil,   nil,
   nil,   nil,   931,   931,   931,   931,   931,   931,   931,   nil,
   nil,   931,   nil,   nil,   nil,   nil,   nil,   931,   931,   931,
   931,   931,   931,   931,   931,   931,   931,   931,   931,   nil,
   931,   931,   931,   nil,   931,   931,   931,   931,   931,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   931,   nil,
   nil,   931,   nil,   nil,   931,   931,   nil,   nil,   931,   nil,
   931,   nil,   nil,   nil,   931,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   931,   nil,   nil,   nil,   nil,   931,   931,
   931,   931,   nil,   931,   931,   931,   931,   nil,   nil,   nil,
   nil,   931,   931,   nil,   nil,   nil,   nil,   nil,   nil,   931,
   nil,   931,   931,   931,   948,   948,   948,   948,   948,   948,
   nil,   nil,   nil,   948,   948,   nil,   nil,   nil,   948,   nil,
   948,   948,   948,   948,   948,   948,   948,   nil,   nil,   nil,
   nil,   nil,   948,   948,   948,   948,   948,   948,   948,   nil,
   nil,   948,   nil,   nil,   nil,   nil,   nil,   948,   948,   948,
   948,   948,   948,   948,   948,   948,   948,   948,   948,   nil,
   948,   948,   948,   nil,   948,   948,   948,   948,   948,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   948,   nil,
   nil,   948,   nil,   nil,   948,   948,   nil,   nil,   948,   nil,
   948,   nil,   nil,   nil,   948,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   948,   nil,   nil,   nil,   nil,   948,   948,
   948,   948,   nil,   948,   948,   948,   948,   nil,   nil,   nil,
   nil,   948,   948,   nil,   nil,   nil,   nil,   nil,   nil,   948,
   nil,   948,   948,   948,   953,   953,   953,   953,   953,   953,
   nil,   nil,   nil,   953,   953,   nil,   nil,   nil,   953,   nil,
   953,   953,   953,   953,   953,   953,   953,   nil,   nil,   nil,
   nil,   nil,   953,   953,   953,   953,   953,   953,   953,   nil,
   nil,   953,   nil,   nil,   nil,   nil,   nil,   953,   953,   953,
   953,   953,   953,   953,   953,   953,   953,   953,   953,   nil,
   953,   953,   953,   nil,   953,   953,   953,   953,   953,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   953,   nil,
   nil,   953,   nil,   nil,   953,   953,   nil,   nil,   953,   nil,
   953,   nil,   nil,   nil,   953,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   953,   nil,   nil,   nil,   nil,   953,   953,
   953,   953,   nil,   953,   953,   953,   953,   nil,   nil,   nil,
   nil,   953,   953,   nil,   nil,   nil,   nil,   nil,   nil,   953,
   nil,   953,   953,   953,     5,     5,     5,     5,     5,   nil,
   nil,   nil,     5,     5,   nil,   nil,   nil,     5,   nil,     5,
     5,     5,     5,     5,     5,     5,   nil,   nil,   nil,   nil,
   nil,     5,     5,     5,     5,     5,     5,     5,   nil,   nil,
     5,   nil,   nil,   nil,   nil,   nil,     5,     5,     5,     5,
     5,     5,     5,     5,     5,     5,     5,     5,   nil,     5,
     5,     5,   nil,     5,     5,     5,     5,     5,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,     5,   nil,   nil,
     5,   nil,   nil,     5,     5,   nil,   nil,     5,   nil,     5,
   nil,   nil,   nil,     5,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,     5,   nil,   nil,   nil,   nil,     5,     5,     5,
     5,   nil,     5,     5,     5,     5,   nil,   nil,   nil,   nil,
     5,     5,   nil,   nil,   nil,    20,    20,    20,     5,    20,
     5,     5,     5,    20,    20,   nil,   nil,   nil,    20,   nil,
    20,    20,    20,    20,    20,    20,    20,   nil,   nil,   nil,
   nil,   nil,    20,    20,    20,    20,    20,    20,    20,   nil,
   nil,    20,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,    20,    20,    20,    20,    20,    20,    20,    20,   nil,
    20,    20,    20,   nil,    20,    20,    20,    20,    20,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,    20,   nil,   nil,    20,    20,   nil,   nil,    20,   nil,
   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    20,    20,
    20,    20,   nil,    20,    20,    20,    20,   nil,   nil,   nil,
   nil,    20,    20,   nil,   nil,   nil,    29,    29,    29,    20,
    29,    20,    20,    20,    29,    29,   nil,   nil,   nil,    29,
   nil,    29,    29,    29,    29,    29,    29,    29,   nil,   nil,
   nil,   nil,   nil,    29,    29,    29,    29,    29,    29,    29,
   nil,   nil,    29,   nil,   nil,   nil,   nil,   nil,   nil,    29,
   nil,   nil,    29,    29,    29,    29,    29,    29,    29,    29,
    29,    29,    29,    29,   nil,    29,    29,    29,    29,    29,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    29,
   nil,   nil,    29,   nil,   nil,    29,    29,   nil,   nil,    29,
   nil,    29,   nil,    29,   nil,    29,   nil,   nil,    29,   nil,
   nil,   nil,   nil,   nil,    29,   nil,   nil,   nil,   nil,    29,
    29,    29,    29,   nil,    29,    29,    29,    29,   nil,   nil,
   nil,   nil,    29,    29,   nil,   nil,   nil,    30,    30,    30,
    29,    30,    29,    29,    29,    30,    30,   nil,   nil,   nil,
    30,   nil,    30,    30,    30,    30,    30,    30,    30,   nil,
   nil,   nil,   nil,   nil,    30,    30,    30,    30,    30,    30,
    30,   nil,   nil,    30,   nil,   nil,   nil,   nil,   nil,   nil,
    30,   nil,   nil,    30,    30,    30,    30,    30,    30,    30,
    30,    30,    30,    30,    30,   nil,    30,    30,    30,    30,
    30,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    30,   nil,   nil,    30,   nil,   nil,    30,    30,   nil,   nil,
    30,   nil,    30,   nil,    30,   nil,    30,   nil,   nil,    30,
   nil,   nil,   nil,   nil,   nil,    30,   nil,   nil,   nil,   nil,
    30,    30,    30,    30,   nil,    30,    30,    30,    30,   nil,
   nil,   nil,   nil,    30,    30,   nil,   nil,   nil,    31,    31,
    31,    30,    31,    30,    30,    30,    31,    31,   nil,   nil,
   nil,    31,   nil,    31,    31,    31,    31,    31,    31,    31,
   nil,   nil,   nil,   nil,   nil,    31,    31,    31,    31,    31,
    31,    31,   nil,   nil,    31,   nil,   nil,   nil,   nil,   nil,
   nil,    31,   nil,   nil,    31,    31,    31,    31,    31,    31,
    31,    31,    31,    31,    31,    31,   nil,    31,    31,    31,
    31,    31,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    31,   nil,   nil,    31,   nil,   nil,    31,    31,   nil,
   nil,    31,   nil,    31,   nil,    31,   nil,    31,   nil,   nil,
    31,   nil,   nil,   nil,   nil,   nil,    31,   nil,   nil,   nil,
   nil,    31,    31,    31,    31,   nil,    31,    31,    31,    31,
   nil,   nil,   nil,   nil,    31,    31,   nil,   nil,   nil,    34,
    34,    34,    31,    34,    31,    31,    31,    34,    34,   nil,
   nil,   nil,    34,   nil,    34,    34,    34,    34,    34,    34,
    34,   nil,   nil,   nil,   nil,   nil,    34,    34,    34,    34,
    34,    34,    34,   nil,   nil,    34,   nil,   nil,   nil,   nil,
   nil,   nil,    34,   nil,   nil,    34,    34,    34,    34,    34,
    34,    34,    34,   nil,    34,    34,    34,   nil,    34,    34,
   nil,   nil,    34,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    34,   nil,   nil,    34,   nil,   nil,    34,    34,
   nil,   nil,    34,   nil,    34,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    34,    34,    34,    34,   nil,    34,    34,    34,
    34,   nil,   nil,   nil,   nil,    34,    34,   nil,   nil,   nil,
    35,    35,    35,    34,    35,    34,    34,    34,    35,    35,
   nil,   nil,   nil,    35,   nil,    35,    35,    35,    35,    35,
    35,    35,   nil,   nil,   nil,   nil,   nil,    35,    35,    35,
    35,    35,    35,    35,   nil,   nil,    35,   nil,   nil,   nil,
   nil,   nil,   nil,    35,   nil,   nil,    35,    35,    35,    35,
    35,    35,    35,    35,   nil,    35,    35,    35,   nil,    35,
    35,   nil,   nil,    35,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    35,   nil,   nil,    35,   nil,   nil,    35,
    35,   nil,   nil,    35,   nil,   nil,   914,   nil,   914,   914,
   914,   914,   914,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   914,   nil,    35,    35,    35,    35,   nil,    35,    35,
    35,    35,   nil,   nil,   nil,   nil,    35,    35,   nil,   nil,
   nil,    35,   nil,   914,    35,   nil,    35,    35,    35,    42,
    42,    42,   nil,    42,   914,   914,   nil,    42,    42,   914,
   nil,   nil,    42,   nil,    42,    42,    42,    42,    42,    42,
    42,   nil,   nil,   nil,   nil,   nil,    42,    42,    42,    42,
    42,    42,    42,   nil,   nil,    42,   nil,   nil,   nil,   nil,
   nil,   nil,    42,   nil,   nil,    42,    42,    42,    42,    42,
    42,    42,    42,   nil,    42,    42,    42,   nil,    42,    42,
    42,    42,    42,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    42,   nil,   nil,    42,   nil,   nil,    42,    42,
   nil,   nil,    42,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,   nil,
   nil,   nil,    42,    42,    42,    42,   nil,    42,    42,    42,
    42,   nil,   nil,   nil,   nil,    42,    42,   nil,   nil,   nil,
    43,    43,    43,    42,    43,    42,    42,    42,    43,    43,
   nil,   nil,   nil,    43,   nil,    43,    43,    43,    43,    43,
    43,    43,   nil,   nil,   nil,   nil,   nil,    43,    43,    43,
    43,    43,    43,    43,   nil,   nil,    43,   nil,   nil,   nil,
   nil,   nil,   nil,    43,   nil,   nil,    43,    43,    43,    43,
    43,    43,    43,    43,   nil,    43,    43,    43,   nil,    43,
    43,    43,    43,    43,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    43,   nil,   nil,    43,   nil,   nil,    43,
    43,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    43,   nil,
   nil,   nil,   nil,    43,    43,    43,    43,   nil,    43,    43,
    43,    43,   nil,   nil,   nil,   nil,    43,    43,   nil,   nil,
   nil,    45,    45,    45,    43,    45,    43,    43,    43,    45,
    45,   nil,   nil,   nil,    45,   nil,    45,    45,    45,    45,
    45,    45,    45,   nil,   nil,   nil,   nil,   nil,    45,    45,
    45,    45,    45,    45,    45,   nil,   nil,    45,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,    45,    45,    45,
    45,    45,    45,    45,    45,   nil,    45,    45,    45,   nil,
    45,    45,    45,    45,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,    45,   nil,   nil,
    45,    45,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   nil,   nil,    45,    45,    45,    45,   nil,    45,
    45,    45,    45,   nil,   nil,   nil,   nil,    45,    45,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,    45,    45,    45,
    59,    59,    59,    59,    59,   nil,   nil,   nil,    59,    59,
   nil,   nil,   nil,    59,   nil,    59,    59,    59,    59,    59,
    59,    59,   nil,   nil,   nil,   nil,   nil,    59,    59,    59,
    59,    59,    59,    59,   nil,   nil,    59,   nil,   nil,   nil,
   nil,   nil,    59,    59,   nil,    59,    59,    59,    59,    59,
    59,    59,    59,    59,   nil,    59,    59,    59,   nil,    59,
    59,    59,    59,    59,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    59,   nil,   nil,    59,   nil,   nil,    59,
    59,   nil,   nil,    59,   nil,    59,   nil,   nil,   nil,    59,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    59,   nil,
   nil,   nil,   nil,    59,    59,    59,    59,   nil,    59,    59,
    59,    59,   nil,   nil,   nil,   nil,    59,    59,   nil,   nil,
   nil,    60,    60,    60,    59,    60,    59,    59,    59,    60,
    60,   nil,   nil,   nil,    60,   nil,    60,    60,    60,    60,
    60,    60,    60,   nil,   nil,   nil,   nil,   nil,    60,    60,
    60,    60,    60,    60,    60,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,   nil,    60,   nil,   nil,    60,    60,    60,
    60,    60,    60,    60,    60,    60,    60,    60,    60,   nil,
    60,    60,    60,    60,    60,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    60,   nil,   nil,    60,   nil,   nil,
    60,    60,   nil,   nil,    60,   nil,    60,   nil,   nil,   nil,
    60,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    60,
   nil,   nil,   nil,   nil,    60,    60,    60,    60,   nil,    60,
    60,    60,    60,   nil,   nil,   nil,   nil,    60,    60,   nil,
   nil,   nil,    61,    61,    61,    60,    61,    60,    60,    60,
    61,    61,   nil,   nil,   nil,    61,   nil,    61,    61,    61,
    61,    61,    61,    61,   nil,   nil,   nil,   nil,   nil,    61,
    61,    61,    61,    61,    61,    61,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,   nil,    61,   nil,   nil,    61,    61,
    61,    61,    61,    61,    61,    61,    61,    61,    61,    61,
   nil,    61,    61,    61,    61,    61,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    61,   nil,   nil,    61,   nil,
   nil,    61,    61,   nil,   nil,    61,   nil,   nil,   nil,   nil,
   nil,    61,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,
    61,   nil,   nil,   nil,   nil,    61,    61,    61,    61,   nil,
    61,    61,    61,    61,   nil,   nil,   nil,   nil,    61,    61,
   nil,   nil,   nil,    64,    64,    64,    61,    64,    61,    61,
    61,    64,    64,   nil,   nil,   nil,    64,   nil,    64,    64,
    64,    64,    64,    64,    64,   nil,   nil,   nil,   nil,   nil,
    64,    64,    64,    64,    64,    64,    64,   nil,   nil,    64,
   nil,   nil,   nil,   nil,   nil,   nil,    64,   nil,   nil,    64,
    64,    64,    64,    64,    64,    64,    64,   nil,    64,    64,
    64,   nil,    64,    64,    64,    64,    64,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    64,   nil,   nil,    64,
   nil,   nil,    64,    64,   nil,   nil,    64,   nil,   nil,   nil,
   nil,   nil,    64,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    64,   nil,   nil,   nil,   nil,    64,    64,    64,    64,
   nil,    64,    64,    64,    64,   nil,   nil,   nil,   nil,    64,
    64,   nil,   nil,   nil,    65,    65,    65,    64,    65,    64,
    64,    64,    65,    65,   nil,   nil,   nil,    65,   nil,    65,
    65,    65,    65,    65,    65,    65,   nil,   nil,   nil,   nil,
   nil,    65,    65,    65,    65,    65,    65,    65,   nil,   nil,
    65,   nil,   nil,   nil,   nil,   nil,   nil,    65,   nil,   nil,
    65,    65,    65,    65,    65,    65,    65,    65,   nil,    65,
    65,    65,   nil,    65,    65,    65,    65,    65,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    65,   nil,   nil,
    65,   nil,   nil,    65,    65,   nil,   nil,    65,   nil,   nil,
   nil,   nil,   nil,    65,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    65,   nil,   nil,   nil,   nil,    65,    65,    65,
    65,   nil,    65,    65,    65,    65,   nil,   nil,   nil,   nil,
    65,    65,   nil,   nil,   nil,    68,    68,    68,    65,    68,
    65,    65,    65,    68,    68,   nil,   nil,   nil,    68,   nil,
    68,    68,    68,    68,    68,    68,    68,   nil,   nil,   nil,
   nil,   nil,    68,    68,    68,    68,    68,    68,    68,   nil,
   nil,    68,   nil,   nil,   nil,   nil,   nil,   nil,    68,   nil,
   nil,    68,    68,    68,    68,    68,    68,    68,    68,   nil,
    68,    68,    68,   nil,    68,    68,    68,    68,    68,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    68,   nil,
   nil,    68,   nil,   nil,    68,    68,   nil,   nil,    68,   nil,
   nil,   nil,   nil,   nil,    68,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    68,   nil,   nil,   nil,   nil,    68,    68,
    68,    68,   nil,    68,    68,    68,    68,   nil,   nil,   nil,
   nil,    68,    68,    68,   nil,   nil,   nil,   nil,    68,    68,
   nil,    68,    68,    68,    69,    69,    69,   nil,    69,   nil,
   nil,   nil,    69,    69,   nil,   nil,   nil,    69,   nil,    69,
    69,    69,    69,    69,    69,    69,   nil,   nil,   nil,   nil,
   nil,    69,    69,    69,    69,    69,    69,    69,   nil,   nil,
    69,   nil,   nil,   nil,   nil,   nil,   nil,    69,   nil,   nil,
    69,    69,    69,    69,    69,    69,    69,    69,   nil,    69,
    69,    69,   nil,    69,    69,   nil,   nil,    69,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    69,   nil,   nil,
    69,   nil,   nil,    69,    69,   nil,   nil,    69,   nil,    69,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    69,    69,    69,
    69,   nil,    69,    69,    69,    69,   nil,   nil,   nil,   nil,
    69,    69,   nil,   nil,   nil,    70,    70,    70,    69,    70,
    69,    69,    69,    70,    70,   nil,   nil,   nil,    70,   nil,
    70,    70,    70,    70,    70,    70,    70,   nil,   nil,   nil,
   nil,   nil,    70,    70,    70,    70,    70,    70,    70,   nil,
   nil,    70,   nil,   nil,   nil,   nil,   nil,   nil,    70,   nil,
   nil,    70,    70,    70,    70,    70,    70,    70,    70,   nil,
    70,    70,    70,   nil,    70,    70,   nil,   nil,    70,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    70,   nil,   nil,    70,   nil,
   nil,    70,   nil,   nil,    70,    70,   nil,   nil,    70,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    70,    70,
    70,    70,   nil,    70,    70,    70,    70,   nil,   nil,   nil,
   nil,    70,    70,   nil,   nil,   nil,    71,    71,    71,    70,
    71,    70,    70,    70,    71,    71,   nil,   nil,   nil,    71,
   nil,    71,    71,    71,    71,    71,    71,    71,   nil,   nil,
   nil,   nil,   nil,    71,    71,    71,    71,    71,    71,    71,
   nil,   nil,    71,   nil,   nil,   nil,   nil,   nil,   nil,    71,
   nil,   nil,    71,    71,    71,    71,    71,    71,    71,    71,
   nil,    71,    71,    71,   nil,    71,    71,   nil,   nil,    71,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    71,
   nil,   nil,    71,   nil,   nil,    71,    71,   nil,   nil,    71,
   nil,   nil,   936,   nil,   936,   936,   936,   936,   936,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   936,   nil,    71,
    71,    71,    71,   nil,    71,    71,    71,    71,   nil,   nil,
   nil,   nil,    71,    71,   nil,   nil,   nil,   nil,   nil,   936,
    71,   nil,    71,    71,    71,   115,   115,   115,   115,   115,
   936,   936,   nil,   115,   115,   936,   nil,   nil,   115,   nil,
   115,   115,   115,   115,   115,   115,   115,   nil,   nil,   nil,
   nil,   nil,   115,   115,   115,   115,   115,   115,   115,   nil,
   nil,   115,   nil,   nil,   nil,   nil,   nil,   115,   115,   115,
   115,   115,   115,   115,   115,   115,   115,   115,   115,   nil,
   115,   115,   115,   nil,   115,   115,   115,   115,   115,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   115,   nil,
   nil,   115,   nil,   nil,   115,   115,   nil,   nil,   115,   nil,
   115,   nil,   nil,   nil,   115,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   115,   nil,   nil,   nil,   nil,   115,   115,
   115,   115,   nil,   115,   115,   115,   115,   nil,   nil,   nil,
   nil,   115,   115,   nil,   nil,   nil,   nil,   nil,   115,   115,
   nil,   115,   115,   115,   120,   120,   120,   nil,   120,   nil,
   nil,   nil,   120,   120,   nil,   nil,   nil,   120,   nil,   120,
   120,   120,   120,   120,   120,   120,   nil,   nil,   nil,   nil,
   nil,   120,   120,   120,   120,   120,   120,   120,   nil,   nil,
   120,   nil,   nil,   nil,   nil,   nil,   nil,   120,   nil,   nil,
   120,   120,   120,   120,   120,   120,   120,   120,   nil,   120,
   120,   120,   nil,   120,   120,   120,   120,   120,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   120,   nil,   nil,
   120,   nil,   nil,   120,   120,   nil,   nil,   120,   nil,   nil,
   nil,   nil,   nil,   120,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   120,   nil,   nil,   nil,   nil,   120,   120,   120,
   120,   nil,   120,   120,   120,   120,   nil,   nil,   nil,   nil,
   120,   120,   nil,   nil,   nil,   121,   121,   121,   120,   121,
   120,   120,   120,   121,   121,   nil,   nil,   nil,   121,   nil,
   121,   121,   121,   121,   121,   121,   121,   nil,   nil,   nil,
   nil,   nil,   121,   121,   121,   121,   121,   121,   121,   nil,
   nil,   121,   nil,   nil,   nil,   nil,   nil,   nil,   121,   nil,
   nil,   121,   121,   121,   121,   121,   121,   121,   121,   nil,
   121,   121,   121,   nil,   121,   121,   121,   121,   121,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   121,   nil,
   nil,   121,   nil,   nil,   121,   121,   nil,   nil,   121,   nil,
   nil,   nil,   nil,   nil,   121,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   121,   nil,   nil,   nil,   nil,   121,   121,
   121,   121,   nil,   121,   121,   121,   121,   nil,   nil,   nil,
   nil,   121,   121,   nil,   nil,   nil,   122,   122,   122,   121,
   122,   121,   121,   121,   122,   122,   nil,   nil,   nil,   122,
   nil,   122,   122,   122,   122,   122,   122,   122,   nil,   nil,
   nil,   nil,   nil,   122,   122,   122,   122,   122,   122,   122,
   nil,   nil,   122,   nil,   nil,   nil,   nil,   nil,   nil,   122,
   nil,   nil,   122,   122,   122,   122,   122,   122,   122,   122,
   nil,   122,   122,   122,   nil,   122,   122,   122,   122,   122,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   122,
   nil,   nil,   122,   nil,   nil,   122,   122,   nil,   nil,   122,
   nil,   nil,   nil,   nil,   nil,   122,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   122,   nil,   nil,   nil,   nil,   122,
   122,   122,   122,   nil,   122,   122,   122,   122,   nil,   nil,
   nil,   nil,   122,   122,   nil,   nil,   nil,   123,   123,   123,
   122,   123,   122,   122,   122,   123,   123,   nil,   nil,   nil,
   123,   nil,   123,   123,   123,   123,   123,   123,   123,   nil,
   nil,   nil,   nil,   nil,   123,   123,   123,   123,   123,   123,
   123,   nil,   nil,   123,   nil,   nil,   nil,   nil,   nil,   nil,
   123,   nil,   nil,   123,   123,   123,   123,   123,   123,   123,
   123,   nil,   123,   123,   123,   nil,   123,   123,   123,   123,
   123,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   123,   nil,   nil,   123,   nil,   nil,   123,   123,   nil,   nil,
   123,   nil,   nil,   nil,   nil,   nil,   123,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   123,   nil,   nil,   nil,   nil,
   123,   123,   123,   123,   nil,   123,   123,   123,   123,   nil,
   nil,   nil,   nil,   123,   123,   nil,   nil,   nil,   nil,   nil,
   nil,   123,   nil,   123,   123,   123,   124,   124,   124,   124,
   124,   nil,   nil,   nil,   124,   124,   nil,   nil,   nil,   124,
   nil,   124,   124,   124,   124,   124,   124,   124,   nil,   nil,
   nil,   nil,   nil,   124,   124,   124,   124,   124,   124,   124,
   nil,   nil,   124,   nil,   nil,   nil,   nil,   nil,   124,   124,
   nil,   124,   124,   124,   124,   124,   124,   124,   124,   124,
   nil,   124,   124,   124,   nil,   124,   124,   124,   124,   124,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   124,
   nil,   nil,   124,   nil,   nil,   124,   124,   nil,   nil,   124,
   nil,   124,   nil,   nil,   nil,   124,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   124,   nil,   nil,   nil,   nil,   124,
   124,   124,   124,   nil,   124,   124,   124,   124,   nil,   nil,
   nil,   nil,   124,   124,   nil,   nil,   nil,   212,   212,   212,
   124,   212,   124,   124,   124,   212,   212,   nil,   nil,   nil,
   212,   nil,   212,   212,   212,   212,   212,   212,   212,   nil,
   nil,   nil,   nil,   nil,   212,   212,   212,   212,   212,   212,
   212,   nil,   nil,   212,   nil,   nil,   nil,   nil,   nil,   nil,
   212,   nil,   nil,   212,   212,   212,   212,   212,   212,   212,
   212,   nil,   212,   212,   212,   nil,   212,   212,   212,   212,
   212,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   212,   nil,   nil,   212,   nil,   nil,   212,   212,   nil,   nil,
   212,   nil,   212,   nil,   nil,   nil,   212,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   212,   nil,   nil,   nil,   nil,
   212,   212,   212,   212,   nil,   212,   212,   212,   212,   nil,
   nil,   nil,   nil,   212,   212,   nil,   nil,   nil,   213,   213,
   213,   212,   213,   212,   212,   212,   213,   213,   nil,   nil,
   nil,   213,   nil,   213,   213,   213,   213,   213,   213,   213,
   nil,   nil,   nil,   nil,   nil,   213,   213,   213,   213,   213,
   213,   213,   nil,   nil,   213,   nil,   nil,   nil,   nil,   nil,
   nil,   213,   nil,   nil,   213,   213,   213,   213,   213,   213,
   213,   213,   nil,   213,   213,   213,   nil,   213,   213,   213,
   213,   213,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   213,   nil,   nil,   213,   nil,   nil,   213,   213,   nil,
   nil,   213,   nil,   213,   nil,   nil,   nil,   213,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   213,   nil,   nil,   nil,
   nil,   213,   213,   213,   213,   nil,   213,   213,   213,   213,
   nil,   nil,   nil,   nil,   213,   213,   nil,   nil,   nil,   214,
   214,   214,   213,   214,   213,   213,   213,   214,   214,   nil,
   nil,   nil,   214,   nil,   214,   214,   214,   214,   214,   214,
   214,   nil,   nil,   nil,   nil,   nil,   214,   214,   214,   214,
   214,   214,   214,   nil,   nil,   214,   nil,   nil,   nil,   nil,
   nil,   nil,   214,   nil,   nil,   214,   214,   214,   214,   214,
   214,   214,   214,   nil,   214,   214,   214,   nil,   214,   214,
   214,   214,   214,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   214,   nil,   nil,   214,   nil,   nil,   214,   214,
   nil,   nil,   214,   nil,   nil,   nil,   nil,   nil,   214,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,
   nil,   nil,   214,   214,   214,   214,   nil,   214,   214,   214,
   214,   nil,   nil,   nil,   nil,   214,   214,   nil,   nil,   nil,
   215,   215,   215,   214,   215,   214,   214,   214,   215,   215,
   nil,   nil,   nil,   215,   nil,   215,   215,   215,   215,   215,
   215,   215,   nil,   nil,   nil,   nil,   nil,   215,   215,   215,
   215,   215,   215,   215,   nil,   nil,   215,   nil,   nil,   nil,
   nil,   nil,   nil,   215,   nil,   nil,   215,   215,   215,   215,
   215,   215,   215,   215,   nil,   215,   215,   215,   nil,   215,
   215,   215,   215,   215,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   215,   nil,   nil,   215,   nil,   nil,   215,
   215,   nil,   nil,   215,   nil,   nil,   nil,   nil,   nil,   215,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   215,   nil,
   nil,   nil,   nil,   215,   215,   215,   215,   nil,   215,   215,
   215,   215,   nil,   nil,   nil,   nil,   215,   215,   nil,   nil,
   nil,   216,   216,   216,   215,   216,   215,   215,   215,   216,
   216,   nil,   nil,   nil,   216,   nil,   216,   216,   216,   216,
   216,   216,   216,   nil,   nil,   nil,   nil,   nil,   216,   216,
   216,   216,   216,   216,   216,   nil,   nil,   216,   nil,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   216,   216,   216,
   216,   216,   216,   216,   216,   nil,   216,   216,   216,   nil,
   216,   216,   216,   216,   216,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   216,   nil,   nil,
   216,   216,   nil,   nil,   216,   nil,   nil,   nil,   nil,   nil,
   216,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   nil,   nil,   216,   216,   216,   216,   nil,   216,
   216,   216,   216,   nil,   nil,   nil,   nil,   216,   216,   nil,
   nil,   nil,   217,   217,   217,   216,   217,   216,   216,   216,
   217,   217,   nil,   nil,   nil,   217,   nil,   217,   217,   217,
   217,   217,   217,   217,   nil,   nil,   nil,   nil,   nil,   217,
   217,   217,   217,   217,   217,   217,   nil,   nil,   217,   nil,
   nil,   nil,   nil,   nil,   nil,   217,   nil,   nil,   217,   217,
   217,   217,   217,   217,   217,   217,   217,   217,   217,   217,
   nil,   217,   217,   217,   217,   217,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   217,   nil,   nil,   217,   nil,
   nil,   217,   217,   nil,   nil,   217,   nil,   217,   nil,   217,
   nil,   217,   nil,   nil,   217,   nil,   nil,   nil,   nil,   nil,
   217,   nil,   nil,   nil,   nil,   217,   217,   217,   217,   nil,
   217,   217,   217,   217,   nil,   nil,   nil,   nil,   217,   217,
   nil,   nil,   nil,   222,   222,   222,   217,   222,   217,   217,
   217,   222,   222,   nil,   nil,   nil,   222,   nil,   222,   222,
   222,   222,   222,   222,   222,   nil,   nil,   nil,   nil,   nil,
   222,   222,   222,   222,   222,   222,   222,   nil,   nil,   222,
   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   222,
   222,   222,   222,   222,   222,   222,   222,   nil,   222,   222,
   222,   nil,   222,   222,   222,   222,   222,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,   nil,   222,
   nil,   nil,   222,   222,   nil,   nil,   222,   nil,   nil,   nil,
   nil,   nil,   222,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   222,   nil,   nil,   nil,   nil,   222,   222,   222,   222,
   nil,   222,   222,   222,   222,   nil,   nil,   nil,   nil,   222,
   222,   nil,   nil,   nil,   223,   223,   223,   222,   223,   222,
   222,   222,   223,   223,   nil,   nil,   nil,   223,   nil,   223,
   223,   223,   223,   223,   223,   223,   nil,   nil,   nil,   nil,
   nil,   223,   223,   223,   223,   223,   223,   223,   nil,   nil,
   223,   nil,   nil,   nil,   nil,   nil,   nil,   223,   nil,   nil,
   223,   223,   223,   223,   223,   223,   223,   223,   nil,   223,
   223,   223,   nil,   223,   223,   223,   223,   223,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   223,   nil,   nil,
   223,   nil,   nil,   223,   223,   nil,   nil,   223,   nil,   nil,
   nil,   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   223,   223,   223,
   223,   nil,   223,   223,   223,   223,   nil,   nil,   nil,   nil,
   223,   223,   nil,   nil,   nil,   224,   224,   224,   223,   224,
   223,   223,   223,   224,   224,   nil,   nil,   nil,   224,   nil,
   224,   224,   224,   224,   224,   224,   224,   nil,   nil,   nil,
   nil,   nil,   224,   224,   224,   224,   224,   224,   224,   nil,
   nil,   224,   nil,   nil,   nil,   nil,   nil,   nil,   224,   nil,
   nil,   224,   224,   224,   224,   224,   224,   224,   224,   nil,
   224,   224,   224,   nil,   224,   224,   224,   224,   224,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   224,   nil,
   nil,   224,   nil,   nil,   224,   224,   nil,   nil,   224,   nil,
   nil,   nil,   nil,   nil,   224,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   224,   nil,   nil,   nil,   nil,   224,   224,
   224,   224,   nil,   224,   224,   224,   224,   nil,   nil,   nil,
   nil,   224,   224,   224,   nil,   nil,   235,   235,   235,   224,
   235,   224,   224,   224,   235,   235,   nil,   nil,   nil,   235,
   nil,   235,   235,   235,   235,   235,   235,   235,   nil,   nil,
   nil,   nil,   nil,   235,   235,   235,   235,   235,   235,   235,
   nil,   nil,   235,   nil,   nil,   nil,   nil,   nil,   nil,   235,
   nil,   nil,   235,   235,   235,   235,   235,   235,   235,   235,
   nil,   235,   235,   235,   nil,   235,   235,   235,   235,   235,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,
   nil,   nil,   235,   nil,   nil,   235,   235,   nil,   nil,   235,
   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,   235,
   235,   235,   235,   nil,   235,   235,   235,   235,   nil,   nil,
   nil,   nil,   235,   235,   nil,   nil,   nil,   238,   238,   238,
   235,   238,   235,   235,   235,   238,   238,   nil,   nil,   nil,
   238,   nil,   238,   238,   238,   238,   238,   238,   238,   nil,
   nil,   nil,   nil,   nil,   238,   238,   238,   238,   238,   238,
   238,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,
   238,   nil,   nil,   238,   238,   238,   238,   238,   238,   238,
   238,   nil,   238,   238,   238,   nil,   238,   238,   238,   238,
   238,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   238,   nil,   nil,   238,   nil,   nil,   238,   238,   nil,   nil,
   238,   nil,   nil,   nil,   nil,   nil,   238,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   238,   nil,   nil,   nil,   nil,
   238,   238,   238,   238,   nil,   238,   238,   238,   238,   nil,
   nil,   nil,   nil,   238,   238,   nil,   nil,   nil,   239,   239,
   239,   238,   239,   238,   238,   238,   239,   239,   nil,   nil,
   nil,   239,   nil,   239,   239,   239,   239,   239,   239,   239,
   nil,   nil,   nil,   nil,   nil,   239,   239,   239,   239,   239,
   239,   239,   nil,   nil,   239,   nil,   nil,   nil,   nil,   nil,
   nil,   239,   nil,   nil,   239,   239,   239,   239,   239,   239,
   239,   239,   nil,   239,   239,   239,   nil,   239,   239,   239,
   239,   239,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   239,   nil,   nil,   239,   nil,   nil,   239,   239,   nil,
   nil,   239,   nil,   nil,   nil,   nil,   nil,   239,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   239,   nil,   nil,   nil,
   nil,   239,   239,   239,   239,   nil,   239,   239,   239,   239,
   nil,   nil,   nil,   nil,   239,   239,   nil,   nil,   nil,   240,
   240,   240,   239,   240,   239,   239,   239,   240,   240,   nil,
   nil,   nil,   240,   nil,   240,   240,   240,   240,   240,   240,
   240,   nil,   nil,   nil,   nil,   nil,   240,   240,   240,   240,
   240,   240,   240,   nil,   nil,   240,   nil,   nil,   nil,   nil,
   nil,   nil,   240,   nil,   nil,   240,   240,   240,   240,   240,
   240,   240,   240,   nil,   240,   240,   240,   nil,   240,   240,
   240,   240,   240,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   240,   nil,   nil,   240,   nil,   nil,   240,   240,
   nil,   nil,   240,   nil,   nil,   nil,   nil,   nil,   240,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   240,   nil,   nil,
   nil,   nil,   240,   240,   240,   240,   nil,   240,   240,   240,
   240,   nil,   nil,   nil,   nil,   240,   240,   nil,   nil,   nil,
   241,   241,   241,   240,   241,   240,   240,   240,   241,   241,
   nil,   nil,   nil,   241,   nil,   241,   241,   241,   241,   241,
   241,   241,   nil,   nil,   nil,   nil,   nil,   241,   241,   241,
   241,   241,   241,   241,   nil,   nil,   241,   nil,   nil,   nil,
   nil,   nil,   nil,   241,   nil,   nil,   241,   241,   241,   241,
   241,   241,   241,   241,   nil,   241,   241,   241,   nil,   241,
   241,   241,   241,   241,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   241,   nil,   nil,   241,   nil,   nil,   241,
   241,   nil,   nil,   241,   nil,   nil,   nil,   nil,   nil,   241,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   241,   nil,
   nil,   nil,   nil,   241,   241,   241,   241,   nil,   241,   241,
   241,   241,   nil,   nil,   nil,   nil,   241,   241,   nil,   nil,
   nil,   242,   242,   242,   241,   242,   241,   241,   241,   242,
   242,   nil,   nil,   nil,   242,   nil,   242,   242,   242,   242,
   242,   242,   242,   nil,   nil,   nil,   nil,   nil,   242,   242,
   242,   242,   242,   242,   242,   nil,   nil,   242,   nil,   nil,
   nil,   nil,   nil,   nil,   242,   nil,   nil,   242,   242,   242,
   242,   242,   242,   242,   242,   nil,   242,   242,   242,   nil,
   242,   242,   242,   242,   242,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   242,   nil,   nil,   242,   nil,   nil,
   242,   242,   nil,   nil,   242,   nil,   nil,   nil,   nil,   nil,
   242,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   242,
   nil,   nil,   nil,   nil,   242,   242,   242,   242,   nil,   242,
   242,   242,   242,   nil,   nil,   nil,   nil,   242,   242,   nil,
   nil,   nil,   243,   243,   243,   242,   243,   242,   242,   242,
   243,   243,   nil,   nil,   nil,   243,   nil,   243,   243,   243,
   243,   243,   243,   243,   nil,   nil,   nil,   nil,   nil,   243,
   243,   243,   243,   243,   243,   243,   nil,   nil,   243,   nil,
   nil,   nil,   nil,   nil,   nil,   243,   nil,   nil,   243,   243,
   243,   243,   243,   243,   243,   243,   nil,   243,   243,   243,
   nil,   243,   243,   243,   243,   243,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   243,   nil,   nil,   243,   nil,
   nil,   243,   243,   nil,   nil,   243,   nil,   nil,   nil,   nil,
   nil,   243,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   243,   nil,   nil,   nil,   nil,   243,   243,   243,   243,   nil,
   243,   243,   243,   243,   nil,   nil,   nil,   nil,   243,   243,
   nil,   nil,   nil,   244,   244,   244,   243,   244,   243,   243,
   243,   244,   244,   nil,   nil,   nil,   244,   nil,   244,   244,
   244,   244,   244,   244,   244,   nil,   nil,   nil,   nil,   nil,
   244,   244,   244,   244,   244,   244,   244,   nil,   nil,   244,
   nil,   nil,   nil,   nil,   nil,   nil,   244,   nil,   nil,   244,
   244,   244,   244,   244,   244,   244,   244,   nil,   244,   244,
   244,   nil,   244,   244,   244,   244,   244,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   244,   nil,   nil,   244,
   nil,   nil,   244,   244,   nil,   nil,   244,   nil,   nil,   nil,
   nil,   nil,   244,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   244,   nil,   nil,   nil,   nil,   244,   244,   244,   244,
   nil,   244,   244,   244,   244,   nil,   nil,   nil,   nil,   244,
   244,   nil,   nil,   nil,   245,   245,   245,   244,   245,   244,
   244,   244,   245,   245,   nil,   nil,   nil,   245,   nil,   245,
   245,   245,   245,   245,   245,   245,   nil,   nil,   nil,   nil,
   nil,   245,   245,   245,   245,   245,   245,   245,   nil,   nil,
   245,   nil,   nil,   nil,   nil,   nil,   nil,   245,   nil,   nil,
   245,   245,   245,   245,   245,   245,   245,   245,   nil,   245,
   245,   245,   nil,   245,   245,   245,   245,   245,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   245,   nil,   nil,
   245,   nil,   nil,   245,   245,   nil,   nil,   245,   nil,   nil,
   nil,   nil,   nil,   245,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   245,   nil,   nil,   nil,   nil,   245,   245,   245,
   245,   nil,   245,   245,   245,   245,   nil,   nil,   nil,   nil,
   245,   245,   nil,   nil,   nil,   246,   246,   246,   245,   246,
   245,   245,   245,   246,   246,   nil,   nil,   nil,   246,   nil,
   246,   246,   246,   246,   246,   246,   246,   nil,   nil,   nil,
   nil,   nil,   246,   246,   246,   246,   246,   246,   246,   nil,
   nil,   246,   nil,   nil,   nil,   nil,   nil,   nil,   246,   nil,
   nil,   246,   246,   246,   246,   246,   246,   246,   246,   nil,
   246,   246,   246,   nil,   246,   246,   246,   246,   246,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   246,   nil,
   nil,   246,   nil,   nil,   246,   246,   nil,   nil,   246,   nil,
   nil,   nil,   nil,   nil,   246,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   246,   nil,   nil,   nil,   nil,   246,   246,
   246,   246,   nil,   246,   246,   246,   246,   nil,   nil,   nil,
   nil,   246,   246,   nil,   nil,   nil,   247,   247,   247,   246,
   247,   246,   246,   246,   247,   247,   nil,   nil,   nil,   247,
   nil,   247,   247,   247,   247,   247,   247,   247,   nil,   nil,
   nil,   nil,   nil,   247,   247,   247,   247,   247,   247,   247,
   nil,   nil,   247,   nil,   nil,   nil,   nil,   nil,   nil,   247,
   nil,   nil,   247,   247,   247,   247,   247,   247,   247,   247,
   nil,   247,   247,   247,   nil,   247,   247,   247,   247,   247,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   247,
   nil,   nil,   247,   nil,   nil,   247,   247,   nil,   nil,   247,
   nil,   nil,   nil,   nil,   nil,   247,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   247,   nil,   nil,   nil,   nil,   247,
   247,   247,   247,   nil,   247,   247,   247,   247,   nil,   nil,
   nil,   nil,   247,   247,   nil,   nil,   nil,   248,   248,   248,
   247,   248,   247,   247,   247,   248,   248,   nil,   nil,   nil,
   248,   nil,   248,   248,   248,   248,   248,   248,   248,   nil,
   nil,   nil,   nil,   nil,   248,   248,   248,   248,   248,   248,
   248,   nil,   nil,   248,   nil,   nil,   nil,   nil,   nil,   nil,
   248,   nil,   nil,   248,   248,   248,   248,   248,   248,   248,
   248,   nil,   248,   248,   248,   nil,   248,   248,   248,   248,
   248,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   248,   nil,   nil,   248,   nil,   nil,   248,   248,   nil,   nil,
   248,   nil,   nil,   nil,   nil,   nil,   248,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   248,   nil,   nil,   nil,   nil,
   248,   248,   248,   248,   nil,   248,   248,   248,   248,   nil,
   nil,   nil,   nil,   248,   248,   nil,   nil,   nil,   249,   249,
   249,   248,   249,   248,   248,   248,   249,   249,   nil,   nil,
   nil,   249,   nil,   249,   249,   249,   249,   249,   249,   249,
   nil,   nil,   nil,   nil,   nil,   249,   249,   249,   249,   249,
   249,   249,   nil,   nil,   249,   nil,   nil,   nil,   nil,   nil,
   nil,   249,   nil,   nil,   249,   249,   249,   249,   249,   249,
   249,   249,   nil,   249,   249,   249,   nil,   249,   249,   249,
   249,   249,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   249,   nil,   nil,   249,   nil,   nil,   249,   249,   nil,
   nil,   249,   nil,   nil,   nil,   nil,   nil,   249,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   249,   nil,   nil,   nil,
   nil,   249,   249,   249,   249,   nil,   249,   249,   249,   249,
   nil,   nil,   nil,   nil,   249,   249,   nil,   nil,   nil,   250,
   250,   250,   249,   250,   249,   249,   249,   250,   250,   nil,
   nil,   nil,   250,   nil,   250,   250,   250,   250,   250,   250,
   250,   nil,   nil,   nil,   nil,   nil,   250,   250,   250,   250,
   250,   250,   250,   nil,   nil,   250,   nil,   nil,   nil,   nil,
   nil,   nil,   250,   nil,   nil,   250,   250,   250,   250,   250,
   250,   250,   250,   nil,   250,   250,   250,   nil,   250,   250,
   250,   250,   250,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   250,   nil,   nil,   250,   nil,   nil,   250,   250,
   nil,   nil,   250,   nil,   nil,   nil,   nil,   nil,   250,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   250,   nil,   nil,
   nil,   nil,   250,   250,   250,   250,   nil,   250,   250,   250,
   250,   nil,   nil,   nil,   nil,   250,   250,   nil,   nil,   nil,
   251,   251,   251,   250,   251,   250,   250,   250,   251,   251,
   nil,   nil,   nil,   251,   nil,   251,   251,   251,   251,   251,
   251,   251,   nil,   nil,   nil,   nil,   nil,   251,   251,   251,
   251,   251,   251,   251,   nil,   nil,   251,   nil,   nil,   nil,
   nil,   nil,   nil,   251,   nil,   nil,   251,   251,   251,   251,
   251,   251,   251,   251,   nil,   251,   251,   251,   nil,   251,
   251,   251,   251,   251,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   251,   nil,   nil,   251,   nil,   nil,   251,
   251,   nil,   nil,   251,   nil,   nil,   nil,   nil,   nil,   251,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   251,   nil,
   nil,   nil,   nil,   251,   251,   251,   251,   nil,   251,   251,
   251,   251,   nil,   nil,   nil,   nil,   251,   251,   nil,   nil,
   nil,   252,   252,   252,   251,   252,   251,   251,   251,   252,
   252,   nil,   nil,   nil,   252,   nil,   252,   252,   252,   252,
   252,   252,   252,   nil,   nil,   nil,   nil,   nil,   252,   252,
   252,   252,   252,   252,   252,   nil,   nil,   252,   nil,   nil,
   nil,   nil,   nil,   nil,   252,   nil,   nil,   252,   252,   252,
   252,   252,   252,   252,   252,   nil,   252,   252,   252,   nil,
   252,   252,   252,   252,   252,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   252,   nil,   nil,   252,   nil,   nil,
   252,   252,   nil,   nil,   252,   nil,   nil,   nil,   nil,   nil,
   252,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   252,
   nil,   nil,   nil,   nil,   252,   252,   252,   252,   nil,   252,
   252,   252,   252,   nil,   nil,   nil,   nil,   252,   252,   nil,
   nil,   nil,   253,   253,   253,   252,   253,   252,   252,   252,
   253,   253,   nil,   nil,   nil,   253,   nil,   253,   253,   253,
   253,   253,   253,   253,   nil,   nil,   nil,   nil,   nil,   253,
   253,   253,   253,   253,   253,   253,   nil,   nil,   253,   nil,
   nil,   nil,   nil,   nil,   nil,   253,   nil,   nil,   253,   253,
   253,   253,   253,   253,   253,   253,   nil,   253,   253,   253,
   nil,   253,   253,   253,   253,   253,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   253,   nil,   nil,   253,   nil,
   nil,   253,   253,   nil,   nil,   253,   nil,   nil,   nil,   nil,
   nil,   253,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   253,   nil,   nil,   nil,   nil,   253,   253,   253,   253,   nil,
   253,   253,   253,   253,   nil,   nil,   nil,   nil,   253,   253,
   nil,   nil,   nil,   254,   254,   254,   253,   254,   253,   253,
   253,   254,   254,   nil,   nil,   nil,   254,   nil,   254,   254,
   254,   254,   254,   254,   254,   nil,   nil,   nil,   nil,   nil,
   254,   254,   254,   254,   254,   254,   254,   nil,   nil,   254,
   nil,   nil,   nil,   nil,   nil,   nil,   254,   nil,   nil,   254,
   254,   254,   254,   254,   254,   254,   254,   nil,   254,   254,
   254,   nil,   254,   254,   254,   254,   254,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   254,   nil,   nil,   254,
   nil,   nil,   254,   254,   nil,   nil,   254,   nil,   nil,   nil,
   nil,   nil,   254,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   254,   nil,   nil,   nil,   nil,   254,   254,   254,   254,
   nil,   254,   254,   254,   254,   nil,   nil,   nil,   nil,   254,
   254,   nil,   nil,   nil,   255,   255,   255,   254,   255,   254,
   254,   254,   255,   255,   nil,   nil,   nil,   255,   nil,   255,
   255,   255,   255,   255,   255,   255,   nil,   nil,   nil,   nil,
   nil,   255,   255,   255,   255,   255,   255,   255,   nil,   nil,
   255,   nil,   nil,   nil,   nil,   nil,   nil,   255,   nil,   nil,
   255,   255,   255,   255,   255,   255,   255,   255,   nil,   255,
   255,   255,   nil,   255,   255,   255,   255,   255,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   255,   nil,   nil,
   255,   nil,   nil,   255,   255,   nil,   nil,   255,   nil,   nil,
   nil,   nil,   nil,   255,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   255,   nil,   nil,   nil,   nil,   255,   255,   255,
   255,   nil,   255,   255,   255,   255,   nil,   nil,   nil,   nil,
   255,   255,   nil,   nil,   nil,   256,   256,   256,   255,   256,
   255,   255,   255,   256,   256,   nil,   nil,   nil,   256,   nil,
   256,   256,   256,   256,   256,   256,   256,   nil,   nil,   nil,
   nil,   nil,   256,   256,   256,   256,   256,   256,   256,   nil,
   nil,   256,   nil,   nil,   nil,   nil,   nil,   nil,   256,   nil,
   nil,   256,   256,   256,   256,   256,   256,   256,   256,   nil,
   256,   256,   256,   nil,   256,   256,   256,   256,   256,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   256,   nil,
   nil,   256,   nil,   nil,   256,   256,   nil,   nil,   256,   nil,
   nil,   nil,   nil,   nil,   256,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   256,   nil,   nil,   nil,   nil,   256,   256,
   256,   256,   nil,   256,   256,   256,   256,   nil,   nil,   nil,
   nil,   256,   256,   nil,   nil,   nil,   257,   257,   257,   256,
   257,   256,   256,   256,   257,   257,   nil,   nil,   nil,   257,
   nil,   257,   257,   257,   257,   257,   257,   257,   nil,   nil,
   nil,   nil,   nil,   257,   257,   257,   257,   257,   257,   257,
   nil,   nil,   257,   nil,   nil,   nil,   nil,   nil,   nil,   257,
   nil,   nil,   257,   257,   257,   257,   257,   257,   257,   257,
   nil,   257,   257,   257,   nil,   257,   257,   257,   257,   257,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   257,
   nil,   nil,   257,   nil,   nil,   257,   257,   nil,   nil,   257,
   nil,   nil,   nil,   nil,   nil,   257,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   257,   nil,   nil,   nil,   nil,   257,
   257,   257,   257,   nil,   257,   257,   257,   257,   nil,   nil,
   nil,   nil,   257,   257,   nil,   nil,   nil,   258,   258,   258,
   257,   258,   257,   257,   257,   258,   258,   nil,   nil,   nil,
   258,   nil,   258,   258,   258,   258,   258,   258,   258,   nil,
   nil,   nil,   nil,   nil,   258,   258,   258,   258,   258,   258,
   258,   nil,   nil,   258,   nil,   nil,   nil,   nil,   nil,   nil,
   258,   nil,   nil,   258,   258,   258,   258,   258,   258,   258,
   258,   nil,   258,   258,   258,   nil,   258,   258,   258,   258,
   258,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   258,   nil,   nil,   258,   nil,   nil,   258,   258,   nil,   nil,
   258,   nil,   nil,   nil,   nil,   nil,   258,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   258,   nil,   nil,   nil,   nil,
   258,   258,   258,   258,   nil,   258,   258,   258,   258,   nil,
   nil,   nil,   nil,   258,   258,   nil,   nil,   nil,   259,   259,
   259,   258,   259,   258,   258,   258,   259,   259,   nil,   nil,
   nil,   259,   nil,   259,   259,   259,   259,   259,   259,   259,
   nil,   nil,   nil,   nil,   nil,   259,   259,   259,   259,   259,
   259,   259,   nil,   nil,   259,   nil,   nil,   nil,   nil,   nil,
   nil,   259,   nil,   nil,   259,   259,   259,   259,   259,   259,
   259,   259,   nil,   259,   259,   259,   nil,   259,   259,   259,
   259,   259,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   259,   nil,   nil,   259,   nil,   nil,   259,   259,   nil,
   nil,   259,   nil,   nil,   nil,   nil,   nil,   259,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   259,   nil,   nil,   nil,
   nil,   259,   259,   259,   259,   nil,   259,   259,   259,   259,
   nil,   nil,   nil,   nil,   259,   259,   nil,   nil,   nil,   264,
   264,   264,   259,   264,   259,   259,   259,   264,   264,   nil,
   nil,   nil,   264,   nil,   264,   264,   264,   264,   264,   264,
   264,   nil,   nil,   nil,   nil,   nil,   264,   264,   264,   264,
   264,   264,   264,   nil,   nil,   264,   nil,   nil,   nil,   nil,
   nil,   nil,   264,   nil,   nil,   264,   264,   264,   264,   264,
   264,   264,   264,   nil,   264,   264,   264,   nil,   264,   264,
   264,   264,   264,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   264,   nil,   nil,   264,   nil,   nil,   264,   264,
   nil,   nil,   264,   nil,   nil,   nil,   nil,   nil,   264,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   264,   nil,   nil,
   nil,   nil,   264,   264,   264,   264,   nil,   264,   264,   264,
   264,   nil,   nil,   nil,   nil,   264,   264,   nil,   nil,   nil,
   271,   271,   271,   264,   271,   264,   264,   264,   271,   271,
   nil,   nil,   nil,   271,   nil,   271,   271,   271,   271,   271,
   271,   271,   nil,   nil,   nil,   nil,   nil,   271,   271,   271,
   271,   271,   271,   271,   nil,   nil,   271,   nil,   nil,   nil,
   nil,   nil,   nil,   271,   nil,   nil,   271,   271,   271,   271,
   271,   271,   271,   271,   271,   271,   271,   271,   nil,   271,
   271,   271,   271,   271,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   271,   nil,   nil,   271,   nil,   nil,   271,
   271,   nil,   nil,   271,   nil,   271,   nil,   271,   nil,   271,
   nil,   nil,   271,   nil,   nil,   nil,   nil,   nil,   271,   nil,
   nil,   nil,   nil,   271,   271,   271,   271,   nil,   271,   271,
   271,   271,   nil,   nil,   nil,   nil,   271,   271,   nil,   nil,
   nil,   272,   272,   272,   271,   272,   271,   271,   271,   272,
   272,   nil,   nil,   nil,   272,   nil,   272,   272,   272,   272,
   272,   272,   272,   nil,   nil,   nil,   nil,   nil,   272,   272,
   272,   272,   272,   272,   272,   nil,   nil,   272,   nil,   nil,
   nil,   nil,   nil,   nil,   272,   nil,   nil,   272,   272,   272,
   272,   272,   272,   272,   272,   272,   272,   272,   272,   nil,
   272,   272,   272,   272,   272,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   272,   nil,   nil,   272,   nil,   nil,
   272,   272,   nil,   nil,   272,   nil,   272,   nil,   272,   nil,
   272,   nil,   nil,   272,   nil,   nil,   nil,   nil,   nil,   272,
   nil,   nil,   nil,   nil,   272,   272,   272,   272,   nil,   272,
   272,   272,   272,   nil,   nil,   nil,   nil,   272,   272,   nil,
   nil,   nil,   280,   280,   280,   272,   280,   272,   272,   272,
   280,   280,   nil,   nil,   nil,   280,   nil,   280,   280,   280,
   280,   280,   280,   280,   nil,   nil,   nil,   nil,   nil,   280,
   280,   280,   280,   280,   280,   280,   nil,   nil,   280,   nil,
   nil,   nil,   nil,   nil,   nil,   280,   nil,   nil,   280,   280,
   280,   280,   280,   280,   280,   280,   280,   280,   280,   280,
   nil,   280,   280,   280,   280,   280,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   280,   nil,   nil,   280,   nil,
   nil,   280,   280,   nil,   nil,   280,   nil,   280,   nil,   280,
   nil,   280,   nil,   nil,   280,   nil,   nil,   nil,   nil,   nil,
   280,   nil,   nil,   nil,   nil,   280,   280,   280,   280,   nil,
   280,   280,   280,   280,   nil,   nil,   nil,   nil,   280,   280,
   280,   nil,   nil,   287,   287,   287,   280,   287,   280,   280,
   280,   287,   287,   nil,   nil,   nil,   287,   nil,   287,   287,
   287,   287,   287,   287,   287,   nil,   nil,   nil,   nil,   nil,
   287,   287,   287,   287,   287,   287,   287,   nil,   nil,   287,
   nil,   nil,   nil,   nil,   nil,   nil,   287,   nil,   nil,   287,
   287,   287,   287,   287,   287,   287,   287,   nil,   287,   287,
   287,   nil,   287,   287,   287,   287,   287,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   287,   nil,   nil,   287,
   nil,   nil,   287,   287,   nil,   nil,   287,   nil,   nil,   nil,
   nil,   nil,   287,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   287,   nil,   nil,   nil,   nil,   287,   287,   287,   287,
   nil,   287,   287,   287,   287,   nil,   nil,   nil,   nil,   287,
   287,   nil,   nil,   nil,   289,   289,   289,   287,   289,   287,
   287,   287,   289,   289,   nil,   nil,   nil,   289,   nil,   289,
   289,   289,   289,   289,   289,   289,   nil,   nil,   nil,   nil,
   nil,   289,   289,   289,   289,   289,   289,   289,   nil,   nil,
   289,   nil,   nil,   nil,   nil,   nil,   nil,   289,   nil,   nil,
   289,   289,   289,   289,   289,   289,   289,   289,   nil,   289,
   289,   289,   nil,   289,   289,   289,   289,   289,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   289,   nil,   nil,
   289,   nil,   nil,   289,   289,   nil,   nil,   289,   nil,   nil,
   nil,   nil,   nil,   289,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   289,   nil,   nil,   nil,   nil,   289,   289,   289,
   289,   nil,   289,   289,   289,   289,   nil,   nil,   nil,   nil,
   289,   289,   nil,   nil,   nil,   292,   292,   292,   289,   292,
   289,   289,   289,   292,   292,   nil,   nil,   nil,   292,   nil,
   292,   292,   292,   292,   292,   292,   292,   nil,   nil,   nil,
   nil,   nil,   292,   292,   292,   292,   292,   292,   292,   nil,
   nil,   292,   nil,   nil,   nil,   nil,   nil,   nil,   292,   nil,
   nil,   292,   292,   292,   292,   292,   292,   292,   292,   nil,
   292,   292,   292,   nil,   292,   292,   292,   292,   292,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   292,   nil,
   nil,   292,   nil,   nil,   292,   292,   nil,   nil,   292,   nil,
   nil,   nil,   nil,   nil,   292,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   292,   nil,   nil,   nil,   nil,   292,   292,
   292,   292,   nil,   292,   292,   292,   292,   nil,   nil,   nil,
   nil,   292,   292,   nil,   nil,   nil,   293,   293,   293,   292,
   293,   292,   292,   292,   293,   293,   nil,   nil,   nil,   293,
   nil,   293,   293,   293,   293,   293,   293,   293,   nil,   nil,
   nil,   nil,   nil,   293,   293,   293,   293,   293,   293,   293,
   nil,   nil,   293,   nil,   nil,   nil,   nil,   nil,   nil,   293,
   nil,   nil,   293,   293,   293,   293,   293,   293,   293,   293,
   nil,   293,   293,   293,   nil,   293,   293,   293,   293,   293,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   293,
   nil,   nil,   293,   nil,   nil,   293,   293,   nil,   nil,   293,
   nil,   nil,   nil,   nil,   nil,   293,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   293,   nil,   nil,   nil,   nil,   293,
   293,   293,   293,   nil,   293,   293,   293,   293,   nil,   nil,
   nil,   nil,   293,   293,   nil,   nil,   nil,   nil,   nil,   nil,
   293,   nil,   293,   293,   293,   298,   298,   298,   298,   298,
   nil,   nil,   nil,   298,   298,   nil,   nil,   nil,   298,   nil,
   298,   298,   298,   298,   298,   298,   298,   nil,   nil,   nil,
   nil,   nil,   298,   298,   298,   298,   298,   298,   298,   nil,
   nil,   298,   nil,   nil,   nil,   nil,   nil,   298,   298,   nil,
   298,   298,   298,   298,   298,   298,   298,   298,   298,   nil,
   298,   298,   298,   nil,   298,   298,   298,   298,   298,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   298,   nil,
   nil,   298,   nil,   nil,   298,   298,   nil,   nil,   298,   nil,
   298,   nil,   nil,   nil,   298,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   298,   nil,   nil,   nil,   nil,   298,   298,
   298,   298,   nil,   298,   298,   298,   298,   nil,   nil,   nil,
   nil,   298,   298,   nil,   nil,   nil,   306,   306,   306,   298,
   306,   298,   298,   298,   306,   306,   nil,   nil,   nil,   306,
   nil,   306,   306,   306,   306,   306,   306,   306,   nil,   nil,
   nil,   nil,   nil,   306,   306,   306,   306,   306,   306,   306,
   nil,   nil,   306,   nil,   nil,   nil,   nil,   nil,   nil,   306,
   nil,   nil,   306,   306,   306,   306,   306,   306,   306,   306,
   nil,   306,   306,   306,   nil,   306,   306,   nil,   nil,   306,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   306,
   nil,   nil,   306,   nil,   nil,   306,   306,   nil,   nil,   306,
   nil,   nil,   938,   nil,   938,   938,   938,   938,   938,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   938,   nil,   306,
   306,   306,   306,   nil,   306,   306,   306,   306,   nil,   nil,
   nil,   nil,   306,   306,   nil,   nil,   nil,   306,   nil,   938,
   306,   nil,   306,   306,   306,   323,   323,   323,   nil,   323,
   938,   938,   nil,   323,   323,   938,   nil,   nil,   323,   nil,
   323,   323,   323,   323,   323,   323,   323,   nil,   nil,   nil,
   nil,   nil,   323,   323,   323,   323,   323,   323,   323,   nil,
   nil,   323,   nil,   nil,   nil,   nil,   nil,   nil,   323,   nil,
   nil,   323,   323,   323,   323,   323,   323,   323,   323,   nil,
   323,   323,   323,   nil,   323,   323,   nil,   nil,   323,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   323,   nil,
   nil,   323,   nil,   nil,   323,   323,   nil,   nil,   323,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   323,   323,
   323,   323,   nil,   323,   323,   323,   323,   nil,   nil,   nil,
   nil,   323,   323,   nil,   nil,   nil,   330,   330,   330,   323,
   330,   323,   323,   323,   330,   330,   nil,   nil,   nil,   330,
   nil,   330,   330,   330,   330,   330,   330,   330,   nil,   nil,
   nil,   nil,   nil,   330,   330,   330,   330,   330,   330,   330,
   nil,   nil,   330,   nil,   nil,   nil,   nil,   nil,   nil,   330,
   nil,   nil,   330,   330,   330,   330,   330,   330,   330,   330,
   nil,   330,   330,   330,   nil,   330,   330,   330,   330,   330,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   330,
   nil,   nil,   330,   nil,   nil,   330,   330,   nil,   nil,   330,
   nil,   nil,   nil,   nil,   nil,   330,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   330,   nil,   nil,   nil,   nil,   330,
   330,   330,   330,   nil,   330,   330,   330,   330,   nil,   nil,
   nil,   nil,   330,   330,   nil,   nil,   nil,   332,   332,   332,
   330,   332,   330,   330,   330,   332,   332,   nil,   nil,   nil,
   332,   nil,   332,   332,   332,   332,   332,   332,   332,   nil,
   nil,   nil,   nil,   nil,   332,   332,   332,   332,   332,   332,
   332,   nil,   nil,   332,   nil,   nil,   nil,   nil,   nil,   nil,
   332,   nil,   nil,   332,   332,   332,   332,   332,   332,   332,
   332,   nil,   332,   332,   332,   nil,   332,   332,   332,   332,
   332,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   332,   nil,   nil,   332,   332,   nil,   332,   332,   nil,   nil,
   332,   nil,   nil,   nil,   nil,   nil,   332,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   332,   nil,   nil,   nil,   nil,
   332,   332,   332,   332,   nil,   332,   332,   332,   332,   nil,
   nil,   nil,   nil,   332,   332,   nil,   nil,   nil,   348,   348,
   348,   332,   348,   332,   332,   332,   348,   348,   nil,   nil,
   nil,   348,   nil,   348,   348,   348,   348,   348,   348,   348,
   nil,   nil,   nil,   nil,   nil,   348,   348,   348,   348,   348,
   348,   348,   nil,   nil,   348,   nil,   nil,   nil,   nil,   nil,
   nil,   348,   nil,   nil,   348,   348,   348,   348,   348,   348,
   348,   348,   nil,   348,   348,   348,   nil,   348,   348,   348,
   348,   348,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   348,   nil,   nil,   348,   nil,   nil,   348,   348,   nil,
   nil,   348,   nil,   nil,   nil,   nil,   nil,   348,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   348,   nil,   nil,   nil,
   nil,   348,   348,   348,   348,   nil,   348,   348,   348,   348,
   nil,   nil,   nil,   nil,   348,   348,   nil,   nil,   nil,   369,
   369,   369,   348,   369,   348,   348,   348,   369,   369,   nil,
   nil,   nil,   369,   nil,   369,   369,   369,   369,   369,   369,
   369,   nil,   nil,   nil,   nil,   nil,   369,   369,   369,   369,
   369,   369,   369,   nil,   nil,   369,   nil,   nil,   nil,   nil,
   nil,   nil,   369,   nil,   nil,   369,   369,   369,   369,   369,
   369,   369,   369,   nil,   369,   369,   369,   nil,   369,   369,
   369,   369,   369,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   369,   nil,   nil,   369,   nil,   nil,   369,   369,
   nil,   nil,   369,   nil,   nil,   nil,   nil,   nil,   369,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   369,   nil,   nil,
   nil,   nil,   369,   369,   369,   369,   nil,   369,   369,   369,
   369,   nil,   nil,   nil,   nil,   369,   369,   nil,   nil,   nil,
   385,   385,   385,   369,   385,   369,   369,   369,   385,   385,
   nil,   nil,   nil,   385,   nil,   385,   385,   385,   385,   385,
   385,   385,   nil,   nil,   nil,   nil,   nil,   385,   385,   385,
   385,   385,   385,   385,   nil,   nil,   385,   nil,   nil,   nil,
   nil,   nil,   nil,   385,   nil,   nil,   385,   385,   385,   385,
   385,   385,   385,   385,   nil,   385,   385,   385,   nil,   385,
   385,   385,   385,   385,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   385,   nil,   nil,   385,   nil,   nil,   385,
   385,   nil,   nil,   385,   nil,   nil,   nil,   nil,   nil,   385,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   385,   nil,
   nil,   nil,   nil,   385,   385,   385,   385,   nil,   385,   385,
   385,   385,   nil,   nil,   nil,   nil,   385,   385,   nil,   nil,
   nil,   413,   413,   413,   385,   413,   385,   385,   385,   413,
   413,   nil,   nil,   nil,   413,   nil,   413,   413,   413,   413,
   413,   413,   413,   nil,   nil,   nil,   nil,   nil,   413,   413,
   413,   413,   413,   413,   413,   nil,   nil,   413,   nil,   nil,
   nil,   nil,   nil,   nil,   413,   nil,   nil,   413,   413,   413,
   413,   413,   413,   413,   413,   nil,   413,   413,   413,   nil,
   413,   413,   413,   413,   413,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   413,   nil,   nil,   413,   nil,   nil,
   413,   413,   nil,   nil,   413,   nil,   nil,   nil,   nil,   nil,
   413,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   413,
   nil,   nil,   nil,   nil,   413,   413,   413,   413,   nil,   413,
   413,   413,   413,   nil,   nil,   nil,   nil,   413,   413,   nil,
   nil,   nil,   456,   456,   456,   413,   456,   413,   413,   413,
   456,   456,   nil,   nil,   nil,   456,   nil,   456,   456,   456,
   456,   456,   456,   456,   nil,   nil,   nil,   nil,   nil,   456,
   456,   456,   456,   456,   456,   456,   nil,   nil,   456,   nil,
   nil,   nil,   nil,   nil,   nil,   456,   nil,   nil,   456,   456,
   456,   456,   456,   456,   456,   456,   456,   456,   456,   456,
   nil,   456,   456,   456,   456,   456,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   456,   nil,   nil,   456,   nil,
   nil,   456,   456,   nil,   nil,   456,   nil,   456,   nil,   456,
   nil,   456,   nil,   nil,   456,   nil,   nil,   nil,   nil,   nil,
   456,   nil,   nil,   nil,   nil,   456,   456,   456,   456,   nil,
   456,   456,   456,   456,   nil,   nil,   nil,   nil,   456,   456,
   nil,   nil,   nil,   458,   458,   458,   456,   458,   456,   456,
   456,   458,   458,   nil,   nil,   nil,   458,   nil,   458,   458,
   458,   458,   458,   458,   458,   nil,   nil,   nil,   nil,   nil,
   458,   458,   458,   458,   458,   458,   458,   nil,   nil,   458,
   nil,   nil,   nil,   nil,   nil,   nil,   458,   nil,   nil,   458,
   458,   458,   458,   458,   458,   458,   458,   nil,   458,   458,
   458,   nil,   458,   458,   458,   458,   458,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   458,   nil,   nil,   458,
   nil,   nil,   458,   458,   nil,   nil,   458,   nil,   nil,   nil,
   nil,   nil,   458,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   458,   nil,   nil,   nil,   nil,   458,   458,   458,   458,
   nil,   458,   458,   458,   458,   nil,   nil,   nil,   nil,   458,
   458,   nil,   nil,   nil,   459,   459,   459,   458,   459,   458,
   458,   458,   459,   459,   nil,   nil,   nil,   459,   nil,   459,
   459,   459,   459,   459,   459,   459,   nil,   nil,   nil,   nil,
   nil,   459,   459,   459,   459,   459,   459,   459,   nil,   nil,
   459,   nil,   nil,   nil,   nil,   nil,   nil,   459,   nil,   nil,
   459,   459,   459,   459,   459,   459,   459,   459,   nil,   459,
   459,   459,   nil,   459,   459,   459,   459,   459,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   459,   nil,   nil,
   459,   nil,   nil,   459,   459,   nil,   nil,   459,   nil,   nil,
   nil,   nil,   nil,   459,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   459,   nil,   nil,   nil,   nil,   459,   459,   459,
   459,   nil,   459,   459,   459,   459,   nil,   nil,   nil,   nil,
   459,   459,   nil,   nil,   nil,   460,   460,   460,   459,   460,
   459,   459,   459,   460,   460,   nil,   nil,   nil,   460,   nil,
   460,   460,   460,   460,   460,   460,   460,   nil,   nil,   nil,
   nil,   nil,   460,   460,   460,   460,   460,   460,   460,   nil,
   nil,   460,   nil,   nil,   nil,   nil,   nil,   nil,   460,   nil,
   nil,   460,   460,   460,   460,   460,   460,   460,   460,   nil,
   460,   460,   460,   nil,   460,   460,   460,   460,   460,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   460,   nil,
   nil,   460,   nil,   nil,   460,   460,   nil,   nil,   460,   nil,
   nil,   nil,   nil,   nil,   460,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   460,   nil,   nil,   nil,   nil,   460,   460,
   460,   460,   nil,   460,   460,   460,   460,   nil,   nil,   nil,
   nil,   460,   460,   nil,   nil,   nil,   497,   497,   497,   460,
   497,   460,   460,   460,   497,   497,   nil,   nil,   nil,   497,
   nil,   497,   497,   497,   497,   497,   497,   497,   nil,   nil,
   nil,   nil,   nil,   497,   497,   497,   497,   497,   497,   497,
   nil,   nil,   497,   nil,   nil,   nil,   nil,   nil,   nil,   497,
   nil,   nil,   497,   497,   497,   497,   497,   497,   497,   497,
   497,   497,   497,   497,   nil,   497,   497,   497,   497,   497,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   497,
   nil,   nil,   497,   nil,   nil,   497,   497,   nil,   nil,   497,
   nil,   497,   nil,   497,   nil,   497,   nil,   nil,   497,   nil,
   nil,   nil,   nil,   nil,   497,   nil,   nil,   nil,   nil,   497,
   497,   497,   497,   nil,   497,   497,   497,   497,   nil,   nil,
   nil,   nil,   497,   497,   nil,   nil,   nil,   499,   499,   499,
   497,   499,   497,   497,   497,   499,   499,   nil,   nil,   nil,
   499,   nil,   499,   499,   499,   499,   499,   499,   499,   nil,
   nil,   nil,   nil,   nil,   499,   499,   499,   499,   499,   499,
   499,   nil,   nil,   499,   nil,   nil,   nil,   nil,   nil,   nil,
   499,   nil,   nil,   499,   499,   499,   499,   499,   499,   499,
   499,   499,   499,   499,   499,   nil,   499,   499,   499,   499,
   499,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   499,   nil,   nil,   499,   nil,   nil,   499,   499,   nil,   nil,
   499,   nil,   nil,   nil,   499,   nil,   499,   nil,   nil,   499,
   nil,   nil,   nil,   nil,   nil,   499,   nil,   nil,   nil,   nil,
   499,   499,   499,   499,   nil,   499,   499,   499,   499,   nil,
   nil,   nil,   nil,   499,   499,   nil,   nil,   nil,   501,   501,
   501,   499,   501,   499,   499,   499,   501,   501,   nil,   nil,
   nil,   501,   nil,   501,   501,   501,   501,   501,   501,   501,
   nil,   nil,   nil,   nil,   nil,   501,   501,   501,   501,   501,
   501,   501,   nil,   nil,   501,   nil,   nil,   nil,   nil,   nil,
   nil,   501,   nil,   nil,   501,   501,   501,   501,   501,   501,
   501,   501,   nil,   501,   501,   501,   nil,   501,   501,   501,
   501,   501,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   501,   nil,   nil,   501,   nil,   nil,   501,   501,   nil,
   nil,   501,   nil,   nil,   nil,   nil,   nil,   501,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   501,   nil,   nil,   nil,
   nil,   501,   501,   501,   501,   nil,   501,   501,   501,   501,
   nil,   nil,   nil,   nil,   501,   501,   nil,   nil,   nil,   nil,
   nil,   nil,   501,   nil,   501,   501,   501,   507,   507,   507,
   507,   507,   nil,   nil,   nil,   507,   507,   nil,   nil,   nil,
   507,   nil,   507,   507,   507,   507,   507,   507,   507,   nil,
   nil,   nil,   nil,   nil,   507,   507,   507,   507,   507,   507,
   507,   nil,   nil,   507,   nil,   nil,   nil,   nil,   nil,   507,
   507,   507,   507,   507,   507,   507,   507,   507,   507,   507,
   507,   nil,   507,   507,   507,   nil,   507,   507,   507,   507,
   507,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   507,   nil,   nil,   507,   nil,   nil,   507,   507,   nil,   nil,
   507,   nil,   507,   nil,   nil,   nil,   507,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   507,   nil,   nil,   nil,   nil,
   507,   507,   507,   507,   nil,   507,   507,   507,   507,   nil,
   nil,   nil,   nil,   507,   507,   nil,   nil,   nil,   nil,   nil,
   507,   507,   nil,   507,   507,   507,   515,   515,   515,   nil,
   515,   nil,   nil,   nil,   515,   515,   nil,   nil,   nil,   515,
   nil,   515,   515,   515,   515,   515,   515,   515,   nil,   nil,
   nil,   nil,   nil,   515,   515,   515,   515,   515,   515,   515,
   nil,   nil,   515,   nil,   nil,   nil,   nil,   nil,   nil,   515,
   nil,   nil,   515,   515,   515,   515,   515,   515,   515,   515,
   nil,   515,   515,   515,   nil,   515,   515,   nil,   nil,   515,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   515,
   nil,   nil,   515,   nil,   nil,   515,   515,   nil,   nil,   515,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   515,
   515,   515,   515,   nil,   515,   515,   515,   515,   nil,   nil,
   nil,   nil,   515,   515,   nil,   nil,   nil,   517,   517,   517,
   515,   517,   515,   515,   515,   517,   517,   nil,   nil,   nil,
   517,   nil,   517,   517,   517,   517,   517,   517,   517,   nil,
   nil,   nil,   nil,   nil,   517,   517,   517,   517,   517,   517,
   517,   nil,   nil,   517,   nil,   nil,   nil,   nil,   nil,   nil,
   517,   nil,   nil,   517,   517,   517,   517,   517,   517,   517,
   517,   517,   517,   517,   517,   nil,   517,   517,   517,   517,
   517,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   517,   nil,   nil,   517,   nil,   nil,   517,   517,   nil,   nil,
   517,   nil,   517,   nil,   517,   nil,   517,   nil,   nil,   517,
   nil,   nil,   nil,   nil,   nil,   517,   nil,   nil,   nil,   nil,
   517,   517,   517,   517,   nil,   517,   517,   517,   517,   nil,
   nil,   nil,   nil,   517,   517,   nil,   nil,   nil,   523,   523,
   523,   517,   523,   517,   517,   517,   523,   523,   nil,   nil,
   nil,   523,   nil,   523,   523,   523,   523,   523,   523,   523,
   nil,   nil,   nil,   nil,   nil,   523,   523,   523,   523,   523,
   523,   523,   nil,   nil,   523,   nil,   nil,   nil,   nil,   nil,
   nil,   523,   nil,   nil,   523,   523,   523,   523,   523,   523,
   523,   523,   nil,   523,   523,   523,   nil,   523,   523,   nil,
   nil,   523,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   523,   nil,   nil,   523,   nil,   nil,   523,   523,   nil,
   nil,   523,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   523,   523,   523,   523,   nil,   523,   523,   523,   523,
   nil,   nil,   nil,   nil,   523,   523,   nil,   nil,   nil,   526,
   526,   526,   523,   526,   523,   523,   523,   526,   526,   nil,
   nil,   nil,   526,   nil,   526,   526,   526,   526,   526,   526,
   526,   nil,   nil,   nil,   nil,   nil,   526,   526,   526,   526,
   526,   526,   526,   nil,   nil,   526,   nil,   nil,   nil,   nil,
   nil,   nil,   526,   nil,   nil,   526,   526,   526,   526,   526,
   526,   526,   526,   nil,   526,   526,   526,   nil,   526,   526,
   526,   526,   526,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   526,   nil,   nil,   526,   nil,   nil,   526,   526,
   nil,   nil,   526,   nil,   nil,   nil,   nil,   nil,   526,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   526,   nil,   nil,
   nil,   nil,   526,   526,   526,   526,   nil,   526,   526,   526,
   526,   nil,   nil,   nil,   nil,   526,   526,   nil,   nil,   nil,
   527,   527,   527,   526,   527,   526,   526,   526,   527,   527,
   nil,   nil,   nil,   527,   nil,   527,   527,   527,   527,   527,
   527,   527,   nil,   nil,   nil,   nil,   nil,   527,   527,   527,
   527,   527,   527,   527,   nil,   nil,   527,   nil,   nil,   nil,
   nil,   nil,   nil,   527,   nil,   nil,   527,   527,   527,   527,
   527,   527,   527,   527,   nil,   527,   527,   527,   nil,   527,
   527,   527,   527,   527,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   527,   nil,   nil,   527,   nil,   nil,   527,
   527,   nil,   nil,   527,   nil,   nil,   nil,   nil,   nil,   527,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   527,   nil,
   nil,   nil,   nil,   527,   527,   527,   527,   nil,   527,   527,
   527,   527,   nil,   nil,   nil,   nil,   527,   527,   nil,   nil,
   nil,   532,   532,   532,   527,   532,   527,   527,   527,   532,
   532,   nil,   nil,   nil,   532,   nil,   532,   532,   532,   532,
   532,   532,   532,   nil,   nil,   nil,   nil,   nil,   532,   532,
   532,   532,   532,   532,   532,   nil,   nil,   532,   nil,   nil,
   nil,   nil,   nil,   nil,   532,   nil,   nil,   532,   532,   532,
   532,   532,   532,   532,   532,   nil,   532,   532,   532,   nil,
   532,   532,   532,   532,   532,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   532,   nil,   nil,   532,   nil,   nil,
   532,   532,   nil,   nil,   532,   nil,   nil,   nil,   nil,   nil,
   532,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   532,
   nil,   nil,   nil,   nil,   532,   532,   532,   532,   nil,   532,
   532,   532,   532,   nil,   nil,   nil,   nil,   532,   532,   nil,
   nil,   nil,   538,   538,   538,   532,   538,   532,   532,   532,
   538,   538,   nil,   nil,   nil,   538,   nil,   538,   538,   538,
   538,   538,   538,   538,   nil,   nil,   nil,   nil,   nil,   538,
   538,   538,   538,   538,   538,   538,   nil,   nil,   538,   nil,
   nil,   nil,   nil,   nil,   nil,   538,   nil,   nil,   538,   538,
   538,   538,   538,   538,   538,   538,   538,   538,   538,   538,
   nil,   538,   538,   538,   538,   538,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   538,   nil,   nil,   538,   nil,
   nil,   538,   538,   nil,   nil,   538,   nil,   538,   nil,   nil,
   nil,   538,   nil,   nil,   538,   nil,   nil,   nil,   nil,   nil,
   538,   nil,   nil,   nil,   nil,   538,   538,   538,   538,   nil,
   538,   538,   538,   538,   nil,   nil,   nil,   nil,   538,   538,
   nil,   nil,   nil,   541,   541,   541,   538,   541,   538,   538,
   538,   541,   541,   nil,   nil,   nil,   541,   nil,   541,   541,
   541,   541,   541,   541,   541,   nil,   nil,   nil,   nil,   nil,
   541,   541,   541,   541,   541,   541,   541,   nil,   nil,   541,
   nil,   nil,   nil,   nil,   nil,   nil,   541,   nil,   nil,   541,
   541,   541,   541,   541,   541,   541,   541,   541,   541,   541,
   541,   nil,   541,   541,   541,   541,   541,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   541,   nil,   nil,   541,
   nil,   nil,   541,   541,   nil,   nil,   541,   nil,   nil,   nil,
   nil,   nil,   541,   nil,   nil,   541,   nil,   nil,   nil,   nil,
   nil,   541,   nil,   nil,   nil,   nil,   541,   541,   541,   541,
   nil,   541,   541,   541,   541,   nil,   nil,   nil,   nil,   541,
   541,   nil,   nil,   nil,   556,   556,   556,   541,   556,   541,
   541,   541,   556,   556,   nil,   nil,   nil,   556,   nil,   556,
   556,   556,   556,   556,   556,   556,   nil,   nil,   nil,   nil,
   nil,   556,   556,   556,   556,   556,   556,   556,   nil,   nil,
   556,   nil,   nil,   nil,   nil,   nil,   nil,   556,   nil,   nil,
   556,   556,   556,   556,   556,   556,   556,   556,   nil,   556,
   556,   556,   nil,   556,   556,   556,   556,   556,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   556,   nil,   nil,
   556,   nil,   nil,   556,   556,   nil,   nil,   556,   nil,   556,
   nil,   nil,   nil,   556,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   556,   nil,   nil,   nil,   nil,   556,   556,   556,
   556,   nil,   556,   556,   556,   556,   nil,   nil,   nil,   nil,
   556,   556,   nil,   nil,   nil,   557,   557,   557,   556,   557,
   556,   556,   556,   557,   557,   nil,   nil,   nil,   557,   nil,
   557,   557,   557,   557,   557,   557,   557,   nil,   nil,   nil,
   nil,   nil,   557,   557,   557,   557,   557,   557,   557,   nil,
   nil,   557,   nil,   nil,   nil,   nil,   nil,   nil,   557,   nil,
   nil,   557,   557,   557,   557,   557,   557,   557,   557,   557,
   557,   557,   557,   nil,   557,   557,   557,   557,   557,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   557,   nil,
   nil,   557,   nil,   nil,   557,   557,   nil,   nil,   557,   nil,
   557,   nil,   557,   nil,   557,   nil,   nil,   557,   nil,   nil,
   nil,   nil,   nil,   557,   nil,   nil,   nil,   nil,   557,   557,
   557,   557,   nil,   557,   557,   557,   557,   nil,   nil,   nil,
   nil,   557,   557,   nil,   nil,   nil,   567,   567,   567,   557,
   567,   557,   557,   557,   567,   567,   nil,   nil,   nil,   567,
   nil,   567,   567,   567,   567,   567,   567,   567,   nil,   nil,
   nil,   nil,   nil,   567,   567,   567,   567,   567,   567,   567,
   nil,   nil,   567,   nil,   nil,   nil,   nil,   nil,   nil,   567,
   nil,   nil,   567,   567,   567,   567,   567,   567,   567,   567,
   567,   567,   567,   567,   nil,   567,   567,   567,   567,   567,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   567,
   nil,   nil,   567,   nil,   nil,   567,   567,   nil,   nil,   567,
   nil,   567,   nil,   567,   nil,   567,   nil,   nil,   567,   nil,
   nil,   nil,   nil,   nil,   567,   nil,   nil,   nil,   nil,   567,
   567,   567,   567,   nil,   567,   567,   567,   567,   nil,   nil,
   nil,   nil,   567,   567,   nil,   nil,   nil,   599,   599,   599,
   567,   599,   567,   567,   567,   599,   599,   nil,   nil,   nil,
   599,   nil,   599,   599,   599,   599,   599,   599,   599,   nil,
   nil,   nil,   nil,   nil,   599,   599,   599,   599,   599,   599,
   599,   nil,   nil,   599,   nil,   nil,   nil,   nil,   nil,   nil,
   599,   nil,   nil,   599,   599,   599,   599,   599,   599,   599,
   599,   nil,   599,   599,   599,   nil,   599,   599,   599,   599,
   599,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   599,   nil,   nil,   599,   nil,   nil,   599,   599,   nil,   nil,
   599,   nil,   599,   nil,   nil,   nil,   599,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   599,   nil,   nil,   nil,   nil,
   599,   599,   599,   599,   nil,   599,   599,   599,   599,   nil,
   nil,   nil,   nil,   599,   599,   nil,   nil,   nil,   600,   600,
   600,   599,   600,   599,   599,   599,   600,   600,   nil,   nil,
   nil,   600,   nil,   600,   600,   600,   600,   600,   600,   600,
   nil,   nil,   nil,   nil,   nil,   600,   600,   600,   600,   600,
   600,   600,   nil,   nil,   600,   nil,   nil,   nil,   nil,   nil,
   nil,   600,   nil,   nil,   600,   600,   600,   600,   600,   600,
   600,   600,   nil,   600,   600,   600,   nil,   600,   600,   600,
   600,   600,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   600,   nil,   nil,   600,   nil,   nil,   600,   600,   nil,
   nil,   600,   nil,   nil,   nil,   nil,   nil,   600,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   600,   nil,   nil,   nil,
   nil,   600,   600,   600,   600,   nil,   600,   600,   600,   600,
   nil,   nil,   nil,   nil,   600,   600,   nil,   nil,   nil,   601,
   601,   601,   600,   601,   600,   600,   600,   601,   601,   nil,
   nil,   nil,   601,   nil,   601,   601,   601,   601,   601,   601,
   601,   nil,   nil,   nil,   nil,   nil,   601,   601,   601,   601,
   601,   601,   601,   nil,   nil,   601,   nil,   nil,   nil,   nil,
   nil,   nil,   601,   nil,   nil,   601,   601,   601,   601,   601,
   601,   601,   601,   601,   601,   601,   601,   nil,   601,   601,
   601,   601,   601,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   601,   nil,   nil,   601,   nil,   nil,   601,   601,
   nil,   nil,   601,   nil,   601,   nil,   601,   nil,   601,   nil,
   nil,   601,   nil,   nil,   nil,   nil,   nil,   601,   nil,   nil,
   nil,   nil,   601,   601,   601,   601,   nil,   601,   601,   601,
   601,   nil,   nil,   nil,   nil,   601,   601,   nil,   nil,   nil,
   nil,   nil,   nil,   601,   nil,   601,   601,   601,   604,   604,
   604,   604,   604,   nil,   nil,   nil,   604,   604,   nil,   nil,
   nil,   604,   nil,   604,   604,   604,   604,   604,   604,   604,
   nil,   nil,   nil,   nil,   nil,   604,   604,   604,   604,   604,
   604,   604,   nil,   nil,   604,   nil,   nil,   nil,   nil,   nil,
   604,   604,   nil,   604,   604,   604,   604,   604,   604,   604,
   604,   604,   nil,   604,   604,   604,   nil,   604,   604,   604,
   604,   604,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   604,   nil,   nil,   604,   nil,   nil,   604,   604,   nil,
   nil,   604,   nil,   604,   nil,   nil,   nil,   604,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   604,   nil,   nil,   nil,
   nil,   604,   604,   604,   604,   nil,   604,   604,   604,   604,
   nil,   nil,   nil,   nil,   604,   604,   nil,   nil,   nil,   605,
   605,   605,   604,   605,   604,   604,   604,   605,   605,   nil,
   nil,   nil,   605,   nil,   605,   605,   605,   605,   605,   605,
   605,   nil,   nil,   nil,   nil,   nil,   605,   605,   605,   605,
   605,   605,   605,   nil,   nil,   605,   nil,   nil,   nil,   nil,
   nil,   nil,   605,   nil,   nil,   605,   605,   605,   605,   605,
   605,   605,   605,   nil,   605,   605,   605,   nil,   605,   605,
   605,   605,   605,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   605,   nil,   nil,   605,   nil,   nil,   605,   605,
   nil,   nil,   605,   nil,   nil,   nil,   nil,   nil,   605,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   605,   nil,   nil,
   nil,   nil,   605,   605,   605,   605,   nil,   605,   605,   605,
   605,   nil,   nil,   nil,   nil,   605,   605,   nil,   nil,   nil,
   608,   608,   608,   605,   608,   605,   605,   605,   608,   608,
   nil,   nil,   nil,   608,   nil,   608,   608,   608,   608,   608,
   608,   608,   nil,   nil,   nil,   nil,   nil,   608,   608,   608,
   608,   608,   608,   608,   nil,   nil,   608,   nil,   nil,   nil,
   nil,   nil,   nil,   608,   nil,   nil,   608,   608,   608,   608,
   608,   608,   608,   608,   608,   608,   608,   608,   nil,   608,
   608,   608,   608,   608,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   608,   nil,   nil,   608,   nil,   nil,   608,
   608,   nil,   nil,   608,   nil,   608,   nil,   608,   nil,   608,
   nil,   nil,   608,   nil,   nil,   nil,   nil,   nil,   608,   nil,
   nil,   nil,   nil,   608,   608,   608,   608,   nil,   608,   608,
   608,   608,   nil,   nil,   nil,   nil,   608,   608,   nil,   nil,
   nil,   609,   609,   609,   608,   609,   608,   608,   608,   609,
   609,   nil,   nil,   nil,   609,   nil,   609,   609,   609,   609,
   609,   609,   609,   nil,   nil,   nil,   nil,   nil,   609,   609,
   609,   609,   609,   609,   609,   nil,   nil,   609,   nil,   nil,
   nil,   nil,   nil,   nil,   609,   nil,   nil,   609,   609,   609,
   609,   609,   609,   609,   609,   609,   609,   609,   609,   nil,
   609,   609,   609,   609,   609,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   609,   nil,   nil,   609,   nil,   nil,
   609,   609,   nil,   nil,   609,   nil,   nil,   nil,   609,   nil,
   609,   nil,   nil,   609,   nil,   nil,   nil,   nil,   nil,   609,
   nil,   nil,   nil,   nil,   609,   609,   609,   609,   nil,   609,
   609,   609,   609,   nil,   nil,   nil,   nil,   609,   609,   nil,
   nil,   nil,   610,   610,   610,   609,   610,   609,   609,   609,
   610,   610,   nil,   nil,   nil,   610,   nil,   610,   610,   610,
   610,   610,   610,   610,   nil,   nil,   nil,   nil,   nil,   610,
   610,   610,   610,   610,   610,   610,   nil,   nil,   610,   nil,
   nil,   nil,   nil,   nil,   nil,   610,   nil,   nil,   610,   610,
   610,   610,   610,   610,   610,   610,   nil,   610,   610,   610,
   nil,   610,   610,   610,   610,   610,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   610,   nil,   nil,   610,   nil,
   nil,   610,   610,   nil,   nil,   610,   nil,   nil,   nil,   nil,
   nil,   610,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   610,   nil,   nil,   nil,   nil,   610,   610,   610,   610,   nil,
   610,   610,   610,   610,   nil,   nil,   nil,   nil,   610,   610,
   nil,   nil,   nil,   611,   611,   611,   610,   611,   610,   610,
   610,   611,   611,   nil,   nil,   nil,   611,   nil,   611,   611,
   611,   611,   611,   611,   611,   nil,   nil,   nil,   nil,   nil,
   611,   611,   611,   611,   611,   611,   611,   nil,   nil,   611,
   nil,   nil,   nil,   nil,   nil,   nil,   611,   nil,   nil,   611,
   611,   611,   611,   611,   611,   611,   611,   nil,   611,   611,
   611,   nil,   611,   611,   611,   611,   611,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   611,   nil,   nil,   611,
   nil,   nil,   611,   611,   nil,   nil,   611,   nil,   nil,   nil,
   nil,   nil,   611,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   611,   nil,   nil,   nil,   nil,   611,   611,   611,   611,
   nil,   611,   611,   611,   611,   nil,   nil,   nil,   nil,   611,
   611,   nil,   nil,   nil,   615,   615,   615,   611,   615,   611,
   611,   611,   615,   615,   nil,   nil,   nil,   615,   nil,   615,
   615,   615,   615,   615,   615,   615,   nil,   nil,   nil,   nil,
   nil,   615,   615,   615,   615,   615,   615,   615,   nil,   nil,
   615,   nil,   nil,   nil,   nil,   nil,   nil,   615,   nil,   nil,
   615,   615,   615,   615,   615,   615,   615,   615,   nil,   615,
   615,   615,   nil,   615,   615,   615,   615,   615,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   615,   nil,   nil,
   615,   nil,   nil,   615,   615,   nil,   nil,   615,   nil,   nil,
   nil,   nil,   nil,   615,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   615,   nil,   nil,   nil,   nil,   615,   615,   615,
   615,   nil,   615,   615,   615,   615,   nil,   nil,   nil,   nil,
   615,   615,   nil,   nil,   nil,   616,   616,   616,   615,   616,
   615,   615,   615,   616,   616,   nil,   nil,   nil,   616,   nil,
   616,   616,   616,   616,   616,   616,   616,   nil,   nil,   nil,
   nil,   nil,   616,   616,   616,   616,   616,   616,   616,   nil,
   nil,   616,   nil,   nil,   nil,   nil,   nil,   nil,   616,   nil,
   nil,   616,   616,   616,   616,   616,   616,   616,   616,   nil,
   616,   616,   616,   nil,   616,   616,   616,   616,   616,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   616,   nil,
   nil,   616,   nil,   nil,   616,   616,   nil,   nil,   616,   nil,
   nil,   nil,   nil,   nil,   616,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   616,   nil,   nil,   nil,   nil,   616,   616,
   616,   616,   nil,   616,   616,   616,   616,   nil,   nil,   nil,
   nil,   616,   616,   nil,   nil,   nil,   640,   640,   640,   616,
   640,   616,   616,   616,   640,   640,   nil,   nil,   nil,   640,
   nil,   640,   640,   640,   640,   640,   640,   640,   nil,   nil,
   nil,   nil,   nil,   640,   640,   640,   640,   640,   640,   640,
   nil,   nil,   640,   nil,   nil,   nil,   nil,   nil,   nil,   640,
   nil,   nil,   640,   640,   640,   640,   640,   640,   640,   640,
   nil,   640,   640,   640,   nil,   640,   640,   640,   640,   640,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   640,
   nil,   nil,   640,   nil,   nil,   640,   640,   nil,   nil,   640,
   nil,   nil,   nil,   nil,   nil,   640,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   640,   nil,   nil,   nil,   nil,   640,
   640,   640,   640,   nil,   640,   640,   640,   640,   nil,   nil,
   nil,   nil,   640,   640,   nil,   nil,   nil,   643,   643,   643,
   640,   643,   640,   640,   640,   643,   643,   nil,   nil,   nil,
   643,   nil,   643,   643,   643,   643,   643,   643,   643,   nil,
   nil,   nil,   nil,   nil,   643,   643,   643,   643,   643,   643,
   643,   nil,   nil,   643,   nil,   nil,   nil,   nil,   nil,   nil,
   643,   nil,   nil,   643,   643,   643,   643,   643,   643,   643,
   643,   nil,   643,   643,   643,   nil,   643,   643,   643,   643,
   643,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   643,   nil,   nil,   643,   nil,   nil,   643,   643,   nil,   nil,
   643,   nil,   nil,   nil,   nil,   nil,   643,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   643,   nil,   nil,   nil,   nil,
   643,   643,   643,   643,   nil,   643,   643,   643,   643,   nil,
   nil,   nil,   nil,   643,   643,   nil,   nil,   nil,   646,   646,
   646,   643,   646,   643,   643,   643,   646,   646,   nil,   nil,
   nil,   646,   nil,   646,   646,   646,   646,   646,   646,   646,
   nil,   nil,   nil,   nil,   nil,   646,   646,   646,   646,   646,
   646,   646,   nil,   nil,   646,   nil,   nil,   nil,   nil,   nil,
   nil,   646,   nil,   nil,   646,   646,   646,   646,   646,   646,
   646,   646,   nil,   646,   646,   646,   nil,   646,   646,   nil,
   nil,   646,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   646,   nil,   nil,   646,   nil,   nil,   646,   646,   nil,
   nil,   646,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   646,   646,   646,   646,   nil,   646,   646,   646,   646,
   nil,   nil,   nil,   nil,   646,   646,   nil,   nil,   nil,   657,
   657,   657,   646,   657,   646,   646,   646,   657,   657,   nil,
   nil,   nil,   657,   nil,   657,   657,   657,   657,   657,   657,
   657,   nil,   nil,   nil,   nil,   nil,   657,   657,   657,   657,
   657,   657,   657,   nil,   nil,   657,   nil,   nil,   nil,   nil,
   nil,   nil,   657,   nil,   nil,   657,   657,   657,   657,   657,
   657,   657,   657,   nil,   657,   657,   657,   nil,   657,   657,
   nil,   nil,   657,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   657,   nil,   nil,   657,   nil,   nil,   657,   657,
   nil,   nil,   657,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   657,   657,   657,   657,   nil,   657,   657,   657,
   657,   nil,   nil,   nil,   nil,   657,   657,   nil,   nil,   nil,
   662,   662,   662,   657,   662,   657,   657,   657,   662,   662,
   nil,   nil,   nil,   662,   nil,   662,   662,   662,   662,   662,
   662,   662,   nil,   nil,   nil,   nil,   nil,   662,   662,   662,
   662,   662,   662,   662,   nil,   nil,   662,   nil,   nil,   nil,
   nil,   nil,   nil,   662,   nil,   nil,   662,   662,   662,   662,
   662,   662,   662,   662,   nil,   662,   662,   662,   nil,   662,
   662,   662,   662,   662,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   662,   nil,   nil,   662,   nil,   nil,   662,
   662,   nil,   nil,   662,   nil,   662,   nil,   nil,   nil,   662,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   662,   nil,
   nil,   nil,   nil,   662,   662,   662,   662,   nil,   662,   662,
   662,   662,   nil,   nil,   nil,   nil,   662,   662,   nil,   nil,
   nil,   688,   688,   688,   662,   688,   662,   662,   662,   688,
   688,   nil,   nil,   nil,   688,   nil,   688,   688,   688,   688,
   688,   688,   688,   nil,   nil,   nil,   nil,   nil,   688,   688,
   688,   688,   688,   688,   688,   nil,   nil,   688,   nil,   nil,
   nil,   nil,   nil,   nil,   688,   nil,   nil,   688,   688,   688,
   688,   688,   688,   688,   688,   nil,   688,   688,   688,   nil,
   688,   688,   688,   688,   688,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   688,   nil,   nil,   688,   nil,   nil,
   688,   688,   nil,   nil,   688,   nil,   nil,   nil,   nil,   nil,
   688,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   688,
   nil,   nil,   nil,   nil,   688,   688,   688,   688,   nil,   688,
   688,   688,   688,   nil,   nil,   nil,   nil,   688,   688,   nil,
   nil,   nil,   722,   722,   722,   688,   722,   688,   688,   688,
   722,   722,   nil,   nil,   nil,   722,   nil,   722,   722,   722,
   722,   722,   722,   722,   nil,   nil,   nil,   nil,   nil,   722,
   722,   722,   722,   722,   722,   722,   nil,   nil,   722,   nil,
   nil,   nil,   nil,   nil,   nil,   722,   nil,   nil,   722,   722,
   722,   722,   722,   722,   722,   722,   nil,   722,   722,   722,
   nil,   722,   722,   722,   722,   722,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   722,   nil,   nil,   722,   nil,
   nil,   722,   722,   nil,   nil,   722,   nil,   nil,   nil,   nil,
   nil,   722,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   722,   nil,   nil,   nil,   nil,   722,   722,   722,   722,   nil,
   722,   722,   722,   722,   nil,   nil,   nil,   nil,   722,   722,
   nil,   nil,   nil,   744,   744,   744,   722,   744,   722,   722,
   722,   744,   744,   nil,   nil,   nil,   744,   nil,   744,   744,
   744,   744,   744,   744,   744,   nil,   nil,   nil,   nil,   nil,
   744,   744,   744,   744,   744,   744,   744,   nil,   nil,   744,
   nil,   nil,   nil,   nil,   nil,   nil,   744,   nil,   nil,   744,
   744,   744,   744,   744,   744,   744,   744,   nil,   744,   744,
   744,   nil,   744,   744,   744,   744,   744,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   744,   nil,   nil,   744,
   nil,   nil,   744,   744,   nil,   nil,   744,   nil,   nil,   nil,
   nil,   nil,   744,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   744,   nil,   nil,   nil,   nil,   744,   744,   744,   744,
   nil,   744,   744,   744,   744,   nil,   nil,   nil,   nil,   744,
   744,   nil,   nil,   nil,   752,   752,   752,   744,   752,   744,
   744,   744,   752,   752,   nil,   nil,   nil,   752,   nil,   752,
   752,   752,   752,   752,   752,   752,   nil,   nil,   nil,   nil,
   nil,   752,   752,   752,   752,   752,   752,   752,   nil,   nil,
   752,   nil,   nil,   nil,   nil,   nil,   nil,   752,   nil,   nil,
   752,   752,   752,   752,   752,   752,   752,   752,   nil,   752,
   752,   752,   nil,   752,   752,   752,   752,   752,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   752,   nil,   nil,
   752,   nil,   nil,   752,   752,   nil,   nil,   752,   nil,   nil,
   nil,   nil,   nil,   752,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   752,   nil,   nil,   nil,   nil,   752,   752,   752,
   752,   nil,   752,   752,   752,   752,   nil,   nil,   nil,   nil,
   752,   752,   nil,   nil,   nil,   765,   765,   765,   752,   765,
   752,   752,   752,   765,   765,   nil,   nil,   nil,   765,   nil,
   765,   765,   765,   765,   765,   765,   765,   nil,   nil,   nil,
   nil,   nil,   765,   765,   765,   765,   765,   765,   765,   nil,
   nil,   765,   nil,   nil,   nil,   nil,   nil,   nil,   765,   nil,
   nil,   765,   765,   765,   765,   765,   765,   765,   765,   nil,
   765,   765,   765,   nil,   765,   765,   765,   765,   765,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   765,   nil,
   nil,   765,   nil,   nil,   765,   765,   nil,   nil,   765,   nil,
   nil,   nil,   nil,   nil,   765,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   765,   nil,   nil,   nil,   nil,   765,   765,
   765,   765,   nil,   765,   765,   765,   765,   nil,   nil,   nil,
   nil,   765,   765,   nil,   nil,   nil,   766,   766,   766,   765,
   766,   765,   765,   765,   766,   766,   nil,   nil,   nil,   766,
   nil,   766,   766,   766,   766,   766,   766,   766,   nil,   nil,
   nil,   nil,   nil,   766,   766,   766,   766,   766,   766,   766,
   nil,   nil,   766,   nil,   nil,   nil,   nil,   nil,   nil,   766,
   nil,   nil,   766,   766,   766,   766,   766,   766,   766,   766,
   nil,   766,   766,   766,   nil,   766,   766,   766,   766,   766,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   766,
   nil,   nil,   766,   nil,   nil,   766,   766,   nil,   nil,   766,
   nil,   nil,   nil,   nil,   nil,   766,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   766,   nil,   nil,   nil,   nil,   766,
   766,   766,   766,   nil,   766,   766,   766,   766,   nil,   nil,
   nil,   nil,   766,   766,   nil,   nil,   nil,   767,   767,   767,
   766,   767,   766,   766,   766,   767,   767,   nil,   nil,   nil,
   767,   nil,   767,   767,   767,   767,   767,   767,   767,   nil,
   nil,   nil,   nil,   nil,   767,   767,   767,   767,   767,   767,
   767,   nil,   nil,   767,   nil,   nil,   nil,   nil,   nil,   nil,
   767,   nil,   nil,   767,   767,   767,   767,   767,   767,   767,
   767,   nil,   767,   767,   767,   nil,   767,   767,   767,   767,
   767,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   767,   nil,   nil,   767,   nil,   nil,   767,   767,   nil,   nil,
   767,   nil,   nil,   nil,   nil,   nil,   767,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   767,   nil,   nil,   nil,   nil,
   767,   767,   767,   767,   nil,   767,   767,   767,   767,   nil,
   nil,   nil,   nil,   767,   767,   nil,   nil,   nil,   768,   768,
   768,   767,   768,   767,   767,   767,   768,   768,   nil,   nil,
   nil,   768,   nil,   768,   768,   768,   768,   768,   768,   768,
   nil,   nil,   nil,   nil,   nil,   768,   768,   768,   768,   768,
   768,   768,   nil,   nil,   768,   nil,   nil,   nil,   nil,   nil,
   nil,   768,   nil,   nil,   768,   768,   768,   768,   768,   768,
   768,   768,   nil,   768,   768,   768,   nil,   768,   768,   768,
   768,   768,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   768,   nil,   nil,   768,   nil,   nil,   768,   768,   nil,
   nil,   768,   nil,   nil,   nil,   nil,   nil,   768,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   768,   nil,   nil,   nil,
   nil,   768,   768,   768,   768,   nil,   768,   768,   768,   768,
   nil,   nil,   nil,   nil,   768,   768,   nil,   nil,   nil,   770,
   770,   770,   768,   770,   768,   768,   768,   770,   770,   nil,
   nil,   nil,   770,   nil,   770,   770,   770,   770,   770,   770,
   770,   nil,   nil,   nil,   nil,   nil,   770,   770,   770,   770,
   770,   770,   770,   nil,   nil,   770,   nil,   nil,   nil,   nil,
   nil,   nil,   770,   nil,   nil,   770,   770,   770,   770,   770,
   770,   770,   770,   nil,   770,   770,   770,   nil,   770,   770,
   770,   770,   770,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   770,   nil,   nil,   770,   nil,   nil,   770,   770,
   nil,   nil,   770,   nil,   nil,   nil,   nil,   nil,   770,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   770,   nil,   nil,
   nil,   nil,   770,   770,   770,   770,   nil,   770,   770,   770,
   770,   nil,   nil,   nil,   nil,   770,   770,   nil,   nil,   nil,
   809,   809,   809,   770,   809,   770,   770,   770,   809,   809,
   nil,   nil,   nil,   809,   nil,   809,   809,   809,   809,   809,
   809,   809,   nil,   nil,   nil,   nil,   nil,   809,   809,   809,
   809,   809,   809,   809,   nil,   nil,   809,   nil,   nil,   nil,
   nil,   nil,   nil,   809,   nil,   nil,   809,   809,   809,   809,
   809,   809,   809,   809,   nil,   809,   809,   809,   nil,   809,
   809,   809,   809,   809,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   809,   nil,   nil,   809,   nil,   nil,   809,
   809,   nil,   nil,   809,   nil,   nil,   nil,   nil,   nil,   809,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   809,   nil,
   nil,   nil,   nil,   809,   809,   809,   809,   nil,   809,   809,
   809,   809,   nil,   nil,   nil,   nil,   809,   809,   nil,   nil,
   nil,   822,   822,   822,   809,   822,   809,   809,   809,   822,
   822,   nil,   nil,   nil,   822,   nil,   822,   822,   822,   822,
   822,   822,   822,   nil,   nil,   nil,   nil,   nil,   822,   822,
   822,   822,   822,   822,   822,   nil,   nil,   822,   nil,   nil,
   nil,   nil,   nil,   nil,   822,   nil,   nil,   822,   822,   822,
   822,   822,   822,   822,   822,   nil,   822,   822,   822,   nil,
   822,   822,   822,   822,   822,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   822,   nil,   nil,   822,   nil,   nil,
   822,   822,   nil,   nil,   822,   nil,   nil,   nil,   nil,   nil,
   822,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   822,
   nil,   nil,   nil,   nil,   822,   822,   822,   822,   nil,   822,
   822,   822,   822,   nil,   nil,   nil,   nil,   822,   822,   nil,
   nil,   nil,   825,   825,   825,   822,   825,   822,   822,   822,
   825,   825,   nil,   nil,   nil,   825,   nil,   825,   825,   825,
   825,   825,   825,   825,   nil,   nil,   nil,   nil,   nil,   825,
   825,   825,   825,   825,   825,   825,   nil,   nil,   825,   nil,
   nil,   nil,   nil,   nil,   nil,   825,   nil,   nil,   825,   825,
   825,   825,   825,   825,   825,   825,   nil,   825,   825,   825,
   nil,   825,   825,   825,   825,   825,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   825,   nil,   nil,   825,   nil,
   nil,   825,   825,   nil,   nil,   825,   nil,   825,   nil,   nil,
   nil,   825,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   825,   nil,   nil,   nil,   nil,   825,   825,   825,   825,   nil,
   825,   825,   825,   825,   nil,   nil,   nil,   nil,   825,   825,
   nil,   nil,   nil,   843,   843,   843,   825,   843,   825,   825,
   825,   843,   843,   nil,   nil,   nil,   843,   nil,   843,   843,
   843,   843,   843,   843,   843,   nil,   nil,   nil,   nil,   nil,
   843,   843,   843,   843,   843,   843,   843,   nil,   nil,   843,
   nil,   nil,   nil,   nil,   nil,   nil,   843,   nil,   nil,   843,
   843,   843,   843,   843,   843,   843,   843,   843,   843,   843,
   843,   nil,   843,   843,   843,   843,   843,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   843,   nil,   nil,   843,
   nil,   nil,   843,   843,   nil,   nil,   843,   nil,   nil,   nil,
   843,   nil,   843,   nil,   nil,   843,   nil,   nil,   nil,   nil,
   nil,   843,   nil,   nil,   nil,   nil,   843,   843,   843,   843,
   nil,   843,   843,   843,   843,   nil,   nil,   nil,   nil,   843,
   843,   nil,   nil,   nil,   844,   844,   844,   843,   844,   843,
   843,   843,   844,   844,   nil,   nil,   nil,   844,   nil,   844,
   844,   844,   844,   844,   844,   844,   nil,   nil,   nil,   nil,
   nil,   844,   844,   844,   844,   844,   844,   844,   nil,   nil,
   844,   nil,   nil,   nil,   nil,   nil,   nil,   844,   nil,   nil,
   844,   844,   844,   844,   844,   844,   844,   844,   nil,   844,
   844,   844,   nil,   844,   844,   844,   844,   844,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   844,   nil,   nil,
   844,   nil,   nil,   844,   844,   nil,   nil,   844,   nil,   nil,
   nil,   nil,   nil,   844,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   844,   nil,   nil,   nil,   nil,   844,   844,   844,
   844,   nil,   844,   844,   844,   844,   nil,   nil,   nil,   nil,
   844,   844,   nil,   nil,   nil,   859,   859,   859,   844,   859,
   844,   844,   844,   859,   859,   nil,   nil,   nil,   859,   nil,
   859,   859,   859,   859,   859,   859,   859,   nil,   nil,   nil,
   nil,   nil,   859,   859,   859,   859,   859,   859,   859,   nil,
   nil,   859,   nil,   nil,   nil,   nil,   nil,   nil,   859,   nil,
   nil,   859,   859,   859,   859,   859,   859,   859,   859,   nil,
   859,   859,   859,   nil,   859,   859,   nil,   nil,   859,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   859,   nil,
   nil,   859,   nil,   nil,   859,   859,   nil,   nil,   859,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   859,   859,
   859,   859,   nil,   859,   859,   859,   859,   nil,   nil,   nil,
   nil,   859,   859,   nil,   nil,   nil,   868,   868,   868,   859,
   868,   859,   859,   859,   868,   868,   nil,   nil,   nil,   868,
   nil,   868,   868,   868,   868,   868,   868,   868,   nil,   nil,
   nil,   nil,   nil,   868,   868,   868,   868,   868,   868,   868,
   nil,   nil,   868,   nil,   nil,   nil,   nil,   nil,   nil,   868,
   nil,   nil,   868,   868,   868,   868,   868,   868,   868,   868,
   nil,   868,   868,   868,   nil,   868,   868,   nil,   nil,   868,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   868,
   nil,   nil,   868,   nil,   nil,   868,   868,   nil,   nil,   868,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   868,
   868,   868,   868,   nil,   868,   868,   868,   868,   nil,   nil,
   nil,   nil,   868,   868,   nil,   nil,   nil,   919,   919,   919,
   868,   919,   868,   868,   868,   919,   919,   nil,   nil,   nil,
   919,   nil,   919,   919,   919,   919,   919,   919,   919,   nil,
   nil,   nil,   nil,   nil,   919,   919,   919,   919,   919,   919,
   919,   nil,   nil,   919,   nil,   nil,   nil,   nil,   nil,   nil,
   919,   nil,   nil,   919,   919,   919,   919,   919,   919,   919,
   919,   nil,   919,   919,   919,   nil,   919,   919,   nil,   nil,
   919,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   919,   nil,   nil,   919,   nil,   nil,   919,   919,   nil,   nil,
   919,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   919,   919,   919,   919,   nil,   919,   919,   919,   919,   nil,
   nil,   nil,   nil,   919,   919,   nil,   nil,   nil,   972,   972,
   972,   919,   972,   919,   919,   919,   972,   972,   nil,   nil,
   nil,   972,   nil,   972,   972,   972,   972,   972,   972,   972,
   nil,   nil,   nil,   nil,   nil,   972,   972,   972,   972,   972,
   972,   972,   nil,   nil,   972,   nil,   nil,   nil,   nil,   nil,
   nil,   972,   nil,   nil,   972,   972,   972,   972,   972,   972,
   972,   972,   972,   972,   972,   972,   nil,   972,   972,   972,
   972,   972,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   972,   nil,   nil,   972,   nil,   nil,   972,   972,   nil,
   nil,   972,   nil,   972,   nil,   972,   nil,   972,   nil,   nil,
   972,   nil,   nil,   nil,   nil,   nil,   972,   nil,   nil,   nil,
   nil,   972,   972,   972,   972,   nil,   972,   972,   972,   972,
   nil,   nil,   nil,   nil,   972,   972,   nil,   nil,   nil,   nil,
   nil,   nil,   972,   nil,   972,   972,   972,     8,     8,     8,
     8,     8,     8,     8,     8,     8,     8,     8,     8,     8,
     8,     8,     8,     8,     8,     8,     8,     8,     8,     8,
     8,   nil,   nil,   nil,     8,     8,     8,     8,     8,     8,
     8,     8,     8,     8,   nil,   nil,   nil,   nil,   nil,     8,
     8,     8,     8,     8,     8,     8,     8,     8,     8,   nil,
     8,   nil,   nil,   nil,   nil,   nil,   nil,   nil,     8,     8,
   nil,     8,     8,     8,     8,     8,     8,     8,   nil,   nil,
     8,     8,   nil,   nil,   nil,     8,     8,     8,     8,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,     8,     8,   nil,     8,     8,     8,     8,     8,
     8,     8,     8,     8,     8,     8,     8,   nil,   nil,     8,
     8,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,     8,     9,     9,     9,     9,     9,
     9,     9,     9,     9,     9,     9,     9,     9,     9,     9,
     9,     9,     9,     9,     9,     9,     9,     9,     9,   nil,
   nil,   nil,     9,     9,     9,     9,     9,     9,     9,     9,
     9,     9,   nil,   nil,   nil,   nil,   nil,     9,     9,     9,
     9,     9,     9,     9,     9,     9,   nil,   nil,     9,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,     9,     9,   nil,     9,
     9,     9,     9,     9,     9,     9,   nil,   nil,     9,     9,
   nil,   nil,   nil,     9,     9,     9,     9,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
     9,     9,   nil,     9,     9,     9,     9,     9,     9,     9,
     9,     9,     9,     9,     9,   nil,   nil,     9,     9,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,     9,   404,   404,   404,   404,   404,   404,   404,
   404,   404,   404,   404,   404,   404,   404,   404,   404,   404,
   404,   404,   404,   404,   404,   404,   404,   nil,   nil,   nil,
   404,   404,   404,   404,   404,   404,   404,   404,   404,   404,
   nil,   nil,   nil,   nil,   nil,   404,   404,   404,   404,   404,
   404,   404,   404,   404,   nil,   nil,   404,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   404,   404,   nil,   404,   404,   404,
   404,   404,   404,   404,   nil,   nil,   404,   404,   nil,   nil,
   nil,   404,   404,   404,   404,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   404,   404,
   nil,   404,   404,   404,   404,   404,   404,   404,   404,   404,
   404,   404,   404,   nil,   nil,   404,   404,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   404,   596,   596,   596,   596,   596,   596,   596,   596,   596,
   596,   596,   596,   596,   596,   596,   596,   596,   596,   596,
   596,   596,   596,   596,   596,   nil,   nil,   nil,   596,   596,
   596,   596,   596,   596,   596,   596,   596,   596,   nil,   nil,
   nil,   nil,   nil,   596,   596,   596,   596,   596,   596,   596,
   596,   596,   nil,   nil,   596,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   596,   596,   nil,   596,   596,   596,   596,   596,
   596,   596,   nil,   nil,   596,   596,   nil,   nil,   nil,   596,
   596,   596,   596,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   596,   596,   nil,   596,
   596,   596,   596,   596,   596,   596,   596,   596,   596,   596,
   596,   nil,   nil,   596,   596,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   596,    72,
    72,    72,    72,    72,    72,    72,    72,    72,    72,    72,
    72,    72,    72,    72,    72,    72,    72,    72,    72,    72,
    72,    72,    72,   nil,   nil,   nil,    72,    72,    72,    72,
    72,    72,    72,    72,    72,    72,   nil,   nil,   nil,   nil,
   nil,    72,    72,    72,    72,    72,    72,    72,    72,    72,
    72,    72,    72,   nil,    72,   nil,   nil,   nil,   nil,   nil,
    72,    72,   nil,    72,    72,    72,    72,    72,    72,    72,
   nil,   nil,    72,    72,   nil,   nil,   nil,    72,    72,    72,
    72,   nil,   nil,   nil,   nil,   nil,    72,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    72,    72,   nil,    72,    72,    72,
    72,    72,    72,    72,    72,    72,    72,    72,    72,   nil,
   nil,    72,   728,   728,   728,   728,   728,   728,   728,   728,
   728,   728,   728,   728,   728,   728,   728,   728,   728,   728,
   728,   728,   728,   728,   728,   728,   nil,   nil,   nil,   728,
   728,   728,   728,   728,   728,   728,   728,   728,   728,   nil,
   nil,   nil,   nil,   nil,   728,   728,   728,   728,   728,   728,
   728,   728,   728,   nil,   nil,   728,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   728,   728,   nil,   728,   728,   728,   728,
   728,   728,   728,   nil,   nil,   728,   728,   nil,   nil,   nil,
   728,   728,   728,   728,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   728,   728,   nil,
   728,   728,   728,   728,   728,   728,   728,   728,   728,   728,
   728,   728,   218,   218,   728,   nil,   218,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   218,   218,   nil,   218,   218,   218,
   218,   218,   218,   218,   nil,   nil,   218,   218,   nil,   nil,
   nil,   218,   218,   218,   218,   nil,   nil,   nil,   nil,   nil,
   218,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,   218,
   nil,   218,   218,   218,   218,   218,   218,   218,   218,   218,
   218,   218,   218,   219,   219,   218,   nil,   219,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   219,   219,   nil,   219,   219,
   219,   219,   219,   219,   219,   nil,   nil,   219,   219,   nil,
   nil,   nil,   219,   219,   219,   219,   nil,   nil,   nil,   nil,
   nil,   219,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,
   219,   nil,   219,   219,   219,   219,   219,   219,   219,   219,
   219,   219,   219,   219,   267,   267,   219,   nil,   267,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   267,   267,   nil,   267,
   267,   267,   267,   267,   267,   267,   nil,   nil,   267,   267,
   nil,   nil,   nil,   267,   267,   267,   267,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   267,   267,   nil,   267,   267,   267,   267,   267,   267,   267,
   267,   267,   267,   267,   267,   454,   454,   267,   nil,   454,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   454,   454,   nil,
   454,   454,   454,   454,   454,   454,   454,   nil,   nil,   454,
   454,   nil,   nil,   nil,   454,   454,   454,   454,   nil,   nil,
   nil,   nil,   nil,   454,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   454,   454,   nil,   454,   454,   454,   454,   454,   454,
   454,   454,   454,   454,   454,   454,   455,   455,   454,   nil,
   455,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   455,   455,
   nil,   455,   455,   455,   455,   455,   455,   455,   nil,   nil,
   455,   455,   nil,   nil,   nil,   455,   455,   455,   455,   nil,
   nil,   nil,   nil,   nil,   455,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   455,   455,   nil,   455,   455,   455,   455,   455,
   455,   455,   455,   455,   455,   455,   455,   518,   518,   455,
   nil,   518,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   518,
   518,   nil,   518,   518,   518,   518,   518,   518,   518,   nil,
   nil,   518,   518,   nil,   nil,   nil,   518,   518,   518,   518,
   nil,   nil,   nil,   nil,   nil,   518,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   518,   518,   nil,   518,   518,   518,   518,
   518,   518,   518,   518,   518,   518,   518,   518,   519,   519,
   518,   nil,   519,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   519,   519,   nil,   519,   519,   519,   519,   519,   519,   519,
   nil,   nil,   519,   519,   nil,   nil,   nil,   519,   519,   519,
   519,   nil,   nil,   nil,   nil,   nil,   519,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   519,   519,   nil,   519,   519,   519,
   519,   519,   519,   519,   519,   519,   519,   519,   519,   528,
   528,   519,   nil,   528,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   528,   528,   nil,   528,   528,   528,   528,   528,   528,
   528,   nil,   nil,   528,   528,   nil,   nil,   nil,   528,   528,
   528,   528,   nil,   nil,   nil,   nil,   nil,   528,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   528,   528,   nil,   528,   528,
   528,   528,   528,   528,   528,   528,   528,   528,   528,   528,
   529,   529,   528,   nil,   529,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   529,   529,   nil,   529,   529,   529,   529,   529,
   529,   529,   nil,   nil,   529,   529,   nil,   nil,   nil,   529,
   529,   529,   529,   nil,   nil,   nil,   nil,   nil,   529,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   529,   529,   nil,   529,
   529,   529,   529,   529,   529,   529,   529,   529,   529,   529,
   529,   558,   558,   529,   nil,   558,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   558,   558,   nil,   558,   558,   558,   558,
   558,   558,   558,   nil,   nil,   558,   558,   nil,   nil,   nil,
   558,   558,   558,   558,   nil,   nil,   nil,   nil,   nil,   558,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   558,   558,   nil,
   558,   558,   558,   558,   558,   558,   558,   558,   558,   558,
   558,   558,   559,   559,   558,   nil,   559,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   559,   559,   nil,   559,   559,   559,
   559,   559,   559,   559,   nil,   nil,   559,   559,   nil,   nil,
   nil,   559,   559,   559,   559,   nil,   nil,   nil,   nil,   nil,
   559,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   559,   559,
   nil,   559,   559,   559,   559,   559,   559,   559,   559,   559,
   559,   559,   559,   565,   565,   559,   nil,   565,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   565,   565,   nil,   565,   565,
   565,   565,   565,   565,   565,   nil,   nil,   565,   565,   nil,
   nil,   nil,   565,   565,   565,   565,   nil,   nil,   nil,   nil,
   nil,   565,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   565,
   565,   nil,   565,   565,   565,   565,   565,   565,   565,   565,
   565,   565,   565,   565,   566,   566,   565,   nil,   566,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   566,   566,   nil,   566,
   566,   566,   566,   566,   566,   566,   nil,   nil,   566,   566,
   nil,   nil,   nil,   566,   566,   566,   566,   nil,   nil,   nil,
   nil,   nil,   566,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   566,   566,   nil,   566,   566,   566,   566,   566,   566,   566,
   566,   566,   566,   566,   566,   602,   602,   566,   nil,   602,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   602,   602,   nil,
   602,   602,   602,   602,   602,   602,   602,   nil,   nil,   602,
   602,   nil,   nil,   nil,   602,   602,   602,   602,   nil,   nil,
   nil,   nil,   nil,   602,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   602,   602,   nil,   602,   602,   602,   602,   602,   602,
   602,   602,   602,   602,   602,   602,   603,   603,   602,   nil,
   603,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   603,   603,
   nil,   603,   603,   603,   603,   603,   603,   603,   nil,   nil,
   603,   603,   nil,   nil,   nil,   603,   603,   603,   603,   nil,
   nil,   nil,   nil,   nil,   603,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   603,   603,   nil,   603,   603,   603,   603,   603,
   603,   603,   603,   603,   603,   603,   603,   969,   969,   603,
   nil,   969,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   969,
   969,   nil,   969,   969,   969,   969,   969,   969,   969,   nil,
   nil,   969,   969,   nil,   nil,   nil,   969,   969,   969,   969,
   nil,   nil,   nil,   nil,   nil,   969,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   969,   969,   nil,   969,   969,   969,   969,
   969,   969,   969,   969,   969,   969,   969,   969,   973,   973,
   969,   nil,   973,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   973,   973,   nil,   973,   973,   973,   973,   973,   973,   973,
   nil,   nil,   973,   973,   nil,   nil,   nil,   973,   973,   973,
   973,   nil,   nil,   nil,   nil,   nil,   973,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   973,   973,   nil,   973,   973,   973,
   973,   973,   973,   973,   973,   973,   973,   973,   973,   974,
   974,   973,   nil,   974,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   974,   974,   nil,   974,   974,   974,   974,   974,   974,
   974,   nil,   nil,   974,   974,   nil,   nil,   nil,   974,   974,
   974,   974,   nil,   nil,   nil,   nil,   nil,   974,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   974,   974,   nil,   974,   974,
   974,   974,   974,   974,   974,   974,   974,   974,   974,   974,
   nil,   546,   974,   546,   546,   546,   546,   546,   nil,   668,
   nil,   668,   668,   668,   668,   668,   546,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   668,   nil,   726,   nil,   726,   726,
   726,   726,   726,   nil,   nil,   nil,   nil,   nil,   546,   546,
   nil,   726,   nil,   nil,   nil,   nil,   668,   546,   546,   546,
   546,   nil,   nil,   nil,   546,   668,   668,   668,   668,   nil,
   nil,   nil,   668,   726,   nil,   727,   nil,   727,   727,   727,
   727,   727,   726,   726,   726,   726,   nil,   nil,   nil,   726,
   727,   nil,   803,   nil,   803,   803,   803,   803,   803,   nil,
   805,   nil,   805,   805,   805,   805,   805,   803,   nil,   nil,
   nil,   nil,   727,   nil,   nil,   805,   nil,   nil,   nil,   nil,
   nil,   727,   727,   727,   727,   nil,   nil,   nil,   727,   803,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   805,   803,   803,
   803,   803,   nil,   nil,   nil,   803,   805,   805,   805,   805,
   nil,   nil,   910,   805,   910,   910,   910,   910,   910,   nil,
   912,   nil,   912,   912,   912,   912,   912,   910,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   912,   nil,   934,   nil,   934,
   934,   934,   934,   934,   nil,   nil,   nil,   nil,   nil,   910,
   nil,   nil,   934,   nil,   nil,   nil,   nil,   912,   910,   910,
   910,   910,   nil,   nil,   nil,   910,   912,   912,   912,   912,
   nil,   nil,   nil,   912,   934,   nil,   940,   nil,   940,   940,
   940,   940,   940,   934,   934,   934,   934,   nil,   nil,   nil,
   934,   940,   nil,   989,   nil,   989,   989,   989,   989,   989,
   991,   nil,   991,   991,   991,   991,   991,   nil,   989,   nil,
   nil,   nil,   nil,   940,   nil,   991,   nil,   993,   nil,   993,
   993,   993,   993,   993,   940,   940,   nil,   nil,   nil,   940,
   989,   nil,   993,   nil,   nil,   nil,   nil,   991,   nil,   989,
   989,   989,   989,   nil,   nil,   nil,   989,   nil,   991,   991,
   nil,   nil,   nil,   991,   993,   nil,   995,   nil,   995,   995,
   995,   995,   995,   nil,   nil,   993,   993,   nil,   nil,   nil,
   993,   995,   nil,  1010,   nil,  1010,  1010,  1010,  1010,  1010,
  1027,   nil,  1027,  1027,  1027,  1027,  1027,   nil,  1010,   nil,
   nil,   nil,   nil,   995,   nil,  1027,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   995,   995,   nil,   nil,   nil,   995,
  1010,   nil,   nil,   nil,   nil,   nil,   nil,  1027,   nil,   nil,
   nil,  1010,  1010,   nil,   nil,   nil,  1010,   nil,  1027,  1027,
   nil,   nil,   nil,  1027 ]

racc_action_pointer = [
  1853,    10,   nil,   221,   nil,  5772,   909,   -79, 22505, 22633,
   -51,   nil,   -80,   -44,   240,    15,   477,   -81,   nil,   -71,
  5903,  1711,   166,   nil,   -62,   nil,    -8,   958,  1068,  6034,
  6165,  6296,   nil,  1993,  6427,  6558,   nil,    70,   225,   352,
   152,   255,  6697,  6828,   -51,  6959,    86,   507,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,  1178,   nil,  7098,
  7229,  7360,     4,   nil,  7491,  7622,   nil,   nil,  7753,  7892,
  8023,  8154, 23017,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   624,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,     0,
   nil,   nil,   112,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   224,   nil,  8293,   nil,   nil,   nil,   nil,
  8432,  8563,  8694,  8825,  8964,   nil,  2133,   nil,   287,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   115,
   nil,  2273,  9095,  9226,  9357,  9488,  9619,  9750, 23191, 23252,
   nil,   nil,  9881, 10012, 10143,   nil,   nil,   574,   -54,   166,
   202,   217,   124,   216,   nil, 10274,  2413,   232, 10405, 10536,
 10667, 10798, 10929, 11060, 11191, 11322, 11453, 11584, 11715, 11846,
 11977, 12108, 12239, 12370, 12501, 12632, 12763, 12894, 13025, 13156,
   nil,   nil,   nil,   nil, 13287,   nil,   nil, 23313,   nil,   nil,
   258, 13418, 13549,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
 13680,   nil,  2133,   nil,   249,   263,   nil, 13811,   328, 13942,
   nil,   nil, 14073, 14204,   nil,   nil,   295,   nil, 14343,  1331,
   337,   315,  2553,   340,   391,   350, 14474,  2693,   576,   682,
   686,   441,   718,   nil,   409,   387,    33,   nil,   nil,   nil,
   458,   360,   418, 14613,   nil,   424,   497,   771,   nil,   528,
 14744,   nil, 14875,  2833,  1396,   484,   nil,   398,   503,   528,
   511,   575,   550,   nil,   nil,   326,    -1,    11, 15006,  2973,
  3113,   298,   641,   533,   -18,    11,   794,   644,    25,   677,
   nil,   nil,   342,   434,   -21,   nil,   834,   nil,   596, 15137,
   nil,   nil,   nil,   194,   230,   255,   373,   413,   481,   506,
   508,   550,   nil,   551,   nil, 15268,   nil,   327,   388,   395,
   400,   456,   -41,   -35,   462,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   611, 22761,   nil,   nil,   nil,   nil,   612,
   nil,   nil,   600, 15399,   605,   nil,   nil,   600,   nil,   837,
   313,   701,   nil,   nil,  1853,   nil,   nil,   nil,   nil,   nil,
  1993,   615,   nil,   627,   632,   509,   521,  1314,   nil,   nil,
   nil,   222,   334,   678,   nil,   nil,  1446,  1582,   nil,   nil,
   nil,   -35,   nil,   683, 23374, 23435, 15530,   328, 15661, 15792,
 15923,  2833,  2973,   523,   563,   708,   710,   711,   712,  1667,
  4233,   666,  3113,  3253,  3393,  3533,  3673,  3813,   915,  1465,
  3953,  4093,  2273,  1397,   nil,  1718,   nil,   nil,   nil,   nil,
   669,   nil,   nil,   nil,   673,   nil,   nil, 16054,   nil, 16185,
   nil, 16316,   nil,   363,   nil,   nil,   nil, 16455,  1427,   nil,
   675,   675,   nil,   nil,   676, 16594,   683, 16725, 23496, 23557,
   870,   726,   nil, 16856,   685,   nil, 16987, 17118, 23618, 23679,
  1531,  2413, 17249,   823,   822,   702,   747,   nil, 17380,   nil,
   nil, 17511,   nil,   nil,   nil,   nil, 24290,  3253,   826,   nil,
  3393,    62,   834,   841,   836,   844, 17642, 17773, 23740, 23801,
    27,   nil,   nil,   930,   nil, 23862, 23923, 17904,   nil,   nil,
   250,  3533,   765,   nil,   -33,   nil,   nil,   nil,   832,   nil,
   nil,   nil,   739,   nil,   nil,   259,   nil,   338,   nil,   nil,
   727,   nil,   741,   nil,   nil,   nil, 22889,   nil,   744, 18035,
 18166, 18297, 23984, 24045, 18436, 18567,   552,   787, 18698, 18829,
 18960, 19091,   786,   nil,   nil, 19222, 19353,   787,   nil,   nil,
   nil,   343,   358,   466,   604,   758,   774,   906,   nil,   890,
     6,   nil,   nil,   807,   102,   913,   nil,   790,   nil,   838,
 19484,   nil,   nil, 19615,   nil,   -83, 19746,   808,   nil,   830,
   123,   180,   873,   248,  1038,   875,   840, 19877,   nil,   909,
   214,   964, 20008,   nil,   nil,   nil,   596,   nil, 24298,   nil,
   847,   849,   nil,   857,   858,   859,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   853,  1178,   nil,   nil, 20139,   nil,
   nil,   nil,   952,   nil,   nil,   nil,   958,   nil,   nil,   959,
   516,   nil,   997,   nil,   nil,   nil,   nil,   nil,  1006,   nil,
    26,   886,    40,    41,   151,   185,  3673,   717,  1040,   nil,
   892,  3813, 20270,   nil,  1029,  3953, 24315, 24354, 23130,   nil,
   nil,   nil,   nil,   nil,   nil,  4093,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   910, 20401,   914,   516,   519,   714,   826,
   nil,  2553, 20532,   nil,   913,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil, 20663, 20794, 20925, 21056,   520,
 21187,   nil,   160,   nil,   nil,   936,   nil,   nil,   889,   nil,
  4233,   nil,   nil,   nil,   nil,   917,   236,   nil,   nil,  1045,
   nil,  4373,   922,   970,   nil,   nil,   nil,    64,   929,   780,
   nil,   nil,   556, 24371,   nil, 24379,   nil,  1377,   nil, 21318,
   nil,  1241,   nil,   929,   228,   943,   nil,   nil,   nil,   nil,
  1065,   nil, 21449,  1069,   nil, 21580,  4513,    93,  1070,   nil,
  1077,   480,  4653,   nil,  1078,   962,   554,   nil,   969,   964,
   553,   nil,   nil, 21711, 21842,  2693,  4793,   nil,   965,   968,
   nil,   969,   970,   973,   nil,  1003,   983,   979,   973, 21973,
   nil,   nil,   nil,   nil,  4933,   nil,   nil,    32, 22104,   nil,
   nil,   nil,   nil,  1028,   986,   nil,   nil,   nil,   987,   988,
   nil,   990,   992,   nil,   993,   nil,   nil,   998,  1280,   997,
   819,   nil,   nil,    33,   nil,   nil,    39,   nil,   nil,   nil,
  1123,   nil,   nil,   nil,  1060,   nil,   nil,  1203,   nil,   nil,
 24431,   nil, 24439,   nil,  6605,   nil,   nil,  1044,  1171, 22235,
  1006,  1099,   nil,  5073,    34,    35,  1116,  1101,    36,   nil,
  5213,  5353,   nil,   nil, 24456,   nil,  8201,   nil, 14521,   nil,
 24495,   nil,   nil,   nil,   nil,   335,   951,  1026,  5493,   nil,
   nil,   nil,   nil,  5633,  1027,   nil,   nil,  1030,  1032,  1034,
  1036,   nil,  1039,   nil,   628,   nil,   nil,   nil,  1146, 24106,
   nil,  1176, 22366, 24167, 24228,    42,  1071,  1179,  1056,  1063,
  1064,  1069,  1074,  1291,  1075,  1307,   706,  1202,   nil, 24512,
   nil, 24519,   nil, 24536,   nil, 24575,   nil,   nil,   nil,  1183,
  1123,  1124,   nil,  1086,    98,   102,   111,   138,   nil,   nil,
 24592,   nil,   nil,   nil,   nil,  1312,  1094,   nil,   nil,  1108,
  1109,  1110,  1130,   nil,   145,  1131,  1134, 24599,   nil,   nil,
   nil,   nil,   nil,  1137,   nil ]

racc_action_default = [
    -3,  -608,    -1,  -594,    -4,  -608,    -7,  -608,  -608,  -608,
  -608,   -30,  -608,  -608,   -34,  -608,  -608,  -287,   -46,  -596,
  -608,   -51,   -55,   -56,   -57,   -61,  -264,  -264,  -264,  -300,
  -330,  -331,   -73,   -12,   -77,   -85,   -87,  -608,  -498,  -499,
  -608,  -608,  -608,  -608,  -223,  -608,  -596,  -237,  -278,  -279,
  -280,  -281,  -282,  -283,  -284,  -285,  -286,  -582,  -289,  -293,
  -607,  -572,  -308,  -310,  -608,  -608,   -53,   -53,  -594,  -608,
  -608,  -608,  -608,  -332,  -333,  -335,  -336,  -337,  -439,  -440,
  -441,  -442,  -443,  -464,  -446,  -447,  -466,  -468,  -451,  -456,
  -460,  -462,  -478,  -464,  -480,  -482,  -483,  -484,  -485,  -580,
  -487,  -488,  -581,  -490,  -491,  -492,  -493,  -494,  -495,  -496,
  -497,  -502,  -503,  -608,    -2,  -595,  -603,  -604,  -605,    -6,
  -608,  -608,  -608,  -608,  -608,    -8,    -3,   -18,  -608,  -116,
  -117,  -118,  -119,  -120,  -121,  -122,  -123,  -124,  -128,  -129,
  -130,  -131,  -132,  -133,  -134,  -135,  -136,  -137,  -138,  -139,
  -140,  -141,  -142,  -143,  -144,  -145,  -146,  -147,  -148,  -149,
  -150,  -151,  -152,  -153,  -154,  -155,  -156,  -157,  -158,  -159,
  -160,  -161,  -162,  -163,  -164,  -165,  -166,  -167,  -168,  -169,
  -170,  -171,  -172,  -173,  -174,  -175,  -176,  -177,  -178,  -179,
  -180,  -181,  -182,  -183,  -184,  -185,  -186,  -187,  -188,  -189,
  -190,  -191,  -192,  -193,  -194,  -195,  -196,  -197,  -198,   -23,
  -125,   -12,  -608,  -608,  -608,  -608,  -608,  -254,  -608,  -608,
  -592,  -593,  -608,  -608,  -596,  -597,   -50,  -608,  -498,  -499,
  -608,  -287,  -608,  -608,  -229,  -608,   -12,  -608,  -608,  -608,
  -608,  -608,  -608,  -608,  -608,  -608,  -608,  -608,  -608,  -608,
  -608,  -608,  -608,  -608,  -608,  -608,  -608,  -608,  -608,  -608,
  -238,  -239,  -240,  -241,  -608,  -403,  -405,  -608,  -590,  -591,
   -62,  -254,  -608,  -307,  -409,  -418,  -420,   -68,  -415,   -69,
  -596,   -70,  -244,  -259,  -268,  -268,  -263,  -608,  -269,  -608,
  -464,  -574,  -608,  -608,   -71,   -72,  -594,   -13,  -608,   -16,
  -608,   -75,   -12,  -596,  -608,   -78,   -81,   -12,   -93,   -94,
  -608,  -608,  -101,  -300,  -303,  -596,  -608,  -330,  -331,  -334,
  -416,  -608,   -83,  -608,   -89,  -297,  -481,  -608,  -217,  -218,
  -608,  -230,  -608,   -12,  -291,  -596,  -245,  -600,  -600,  -608,
  -608,  -600,  -608,  -309,  -394,   -52,  -608,  -608,  -608,   -12,
   -12,  -594,  -608,  -595,  -498,  -499,  -608,  -608,  -287,  -608,
  -347,  -348,  -111,  -112,  -608,  -114,  -608,  -287,  -506,  -608,
  -498,  -499,  -323,  -116,  -117,  -158,  -159,  -160,  -176,  -181,
  -188,  -191,  -325,  -608,  -570,  -608,  -444,  -608,  -608,  -608,
  -608,  -608,  -608,  -608,  -608,  1035,    -5,  -606,   -24,   -25,
   -26,   -27,   -28,  -608,  -608,   -20,   -21,   -22,  -126,  -608,
   -31,   -33,  -274,  -608,  -608,  -273,   -32,  -608,   -35,  -608,
  -287,   -43,   -45,  -199,  -249,  -269,   -47,   -48,   -36,  -200,
  -249,  -596,  -255,  -268,  -268,  -583,  -584,  -264,  -413,  -585,
  -586,  -584,  -583,  -264,  -412,  -414,  -585,  -586,   -42,  -207,
   -49,  -596,  -306,  -608,  -608,  -608,  -254,  -297,  -608,  -608,
  -608,  -208,  -209,  -210,  -211,  -212,  -213,  -214,  -215,  -219,
  -220,  -221,  -222,  -224,  -225,  -226,  -227,  -228,  -231,  -232,
  -233,  -234,  -596,  -242,  -424,  -264,  -583,  -584,   -59,   -63,
  -596,  -265,  -422,  -424,  -596,  -302,  -260,  -608,  -261,  -608,
  -266,  -608,  -270,  -608,  -577,  -579,   -11,  -595,   -15,   -17,
  -596,   -74,  -295,   -90,   -79,  -608,  -596,  -254,  -608,  -608,
  -100,  -608,  -481,  -608,   -86,   -91,  -608,  -608,  -608,  -608,
  -243,  -235,  -608,  -431,  -608,  -596,  -608,  -246,  -602,  -601,
  -248,  -602,  -298,  -299,  -573,  -311,  -530,   -12,  -338,  -339,
   -12,  -608,  -608,  -608,  -608,  -608,  -608,  -254,  -608,  -608,
  -297,   -53,  -111,  -112,  -113,  -608,  -608,  -254,  -319,  -504,
  -608,   -12,  -508,  -327,  -596,  -445,  -465,  -470,  -608,  -472,
  -448,  -467,  -608,  -469,  -450,  -608,  -453,  -608,  -455,  -458,
  -608,  -459,  -608,  -479,    -9,   -19,  -608,   -29,  -277,  -608,
  -608,  -254,  -608,  -608,  -608,  -608,  -417,  -608,  -256,  -258,
  -608,  -608,   -64,  -253,  -410,  -608,  -608,   -66,  -411,  -305,
  -598,  -583,  -584,  -583,  -584,  -596,  -608,  -608,  -425,   -58,
  -406,  -422,  -251,  -608,  -383,  -608,  -301,  -268,  -267,  -271,
  -608,  -575,  -576,  -608,   -14,   -76,  -608,   -82,   -88,  -596,
  -583,  -584,  -252,  -587,   -99,  -608,   -84,  -608,  -206,  -216,
  -596,  -607,  -607,  -290,  -292,  -294,  -600,  -395,  -530,  -398,
  -569,  -569,  -513,  -515,  -515,  -515,  -529,  -531,  -532,  -533,
  -534,  -535,  -536,  -537,  -538,  -608,  -540,  -542,  -544,  -549,
  -551,  -552,  -554,  -559,  -561,  -562,  -564,  -565,  -566,  -608,
  -607,  -340,  -607,   -54,  -341,  -342,  -314,  -315,  -608,  -317,
  -608,  -596,  -583,  -584,  -587,  -296,   -12,  -111,  -112,  -115,
  -596,   -12,  -608,  -321,  -608,   -12,  -530,  -530,  -608,  -571,
  -471,  -474,  -475,  -476,  -477,   -12,  -449,  -452,  -454,  -457,
  -461,  -463,  -127,  -275,  -608,  -596,  -583,  -584,  -584,  -583,
   -44,  -250,  -608,  -599,  -268,   -38,  -202,   -39,  -203,   -65,
   -40,  -205,   -41,  -204,   -67,  -608,  -608,  -608,  -608,  -417,
  -608,  -404,  -383,  -408,  -407,  -608,  -419,  -384,  -596,  -386,
   -12,  -421,  -262,  -272,  -578,   -80,  -417,   -92,  -304,  -607,
  -345,   -12,  -432,  -607,  -433,  -434,  -247,  -608,  -596,  -608,
  -511,  -512,  -608,  -608,  -522,  -608,  -525,  -608,  -527,  -608,
  -349,  -608,  -351,  -353,  -360,  -596,  -543,  -553,  -563,  -567,
  -608,  -343,  -608,  -608,  -316,  -608,   -12,  -417,  -608,  -417,
  -608,  -608,   -12,  -324,  -608,  -596,  -608,  -328,  -608,  -276,
  -417,   -37,  -201,  -257,  -608,  -236,   -12,   -60,  -569,  -569,
  -365,  -367,  -367,  -367,  -382,  -608,  -596,  -388,  -538,  -546,
  -547,  -557,  -423,   -10,   -12,  -438,  -346,  -608,  -608,  -436,
  -396,  -399,  -401,  -608,  -569,  -550,  -568,  -514,  -515,  -515,
  -541,  -515,  -515,  -560,  -515,  -538,  -555,  -596,  -608,  -358,
  -608,  -539,  -312,  -608,  -313,  -271,  -607,  -318,  -320,  -505,
  -608,  -326,  -507,  -509,  -508,  -473,  -426,  -608,  -363,  -364,
  -373,  -375,  -608,  -378,  -608,  -380,  -385,  -608,  -608,  -608,
  -545,  -608,  -437,   -12,  -498,  -499,  -608,  -608,  -287,  -435,
   -12,   -12,  -397,  -510,  -608,  -518,  -608,  -520,  -608,  -523,
  -608,  -526,  -528,  -350,  -352,  -356,  -608,  -361,   -12,  -427,
  -428,  -429,  -322,   -12,  -569,  -548,  -366,  -367,  -367,  -367,
  -367,  -558,  -367,  -387,  -596,  -390,  -392,  -393,  -556,  -608,
  -297,  -431,  -254,  -608,  -608,  -297,  -608,  -608,  -515,  -515,
  -515,  -515,  -354,  -608,  -359,  -608,  -607,  -608,  -362,  -608,
  -370,  -608,  -372,  -608,  -376,  -608,  -379,  -381,  -389,  -608,
  -296,  -587,  -430,  -596,  -583,  -584,  -587,  -296,  -400,  -402,
  -608,  -516,  -519,  -521,  -524,  -608,  -357,  -344,  -329,  -367,
  -367,  -367,  -367,  -391,  -417,  -515,  -355,  -608,  -368,  -371,
  -374,  -377,  -517,  -367,  -369 ]

racc_goto_table = [
   223,   343,   304,    16,   534,   382,   127,   210,    16,   283,
   283,   283,   498,   319,   319,   431,   266,   218,   336,   267,
   132,   132,   555,   227,   661,   268,   489,   332,   118,   437,
   443,   114,   227,   227,   227,   725,    16,   310,   310,     6,
   274,   278,   326,   524,     6,   115,   135,   135,   319,   319,
   319,   346,   347,   137,   137,   351,   349,   350,   638,   305,
   638,   606,    16,   547,   550,   415,   416,   227,   227,   490,
   820,   227,   356,   366,   366,   270,   277,   279,   485,   537,
   540,   125,   633,   544,   132,   428,   345,   345,   226,   641,
   345,   448,   789,   118,   564,   780,   352,   880,   334,   119,
   581,   583,   586,   586,     2,   944,   387,   398,   399,   400,
   401,   322,   301,   800,   801,   852,   394,   855,    16,   452,
   284,   284,   284,   227,   227,   227,   227,    16,   883,    16,
   815,   641,   965,   823,   961,     1,   947,   873,   368,   372,
   863,   303,   345,   345,   345,   345,   627,   281,   294,   295,
   644,   337,   585,   587,     6,   635,   209,   967,   285,   285,
   285,   498,   361,   402,   669,     6,   411,   404,   703,   638,
   638,   631,   596,   330,   339,   495,   630,   384,   676,   340,
   409,   545,   359,   568,   383,   333,   535,   335,   344,   338,
   341,   721,   984,   832,   571,   917,   572,   283,   511,   641,
   728,   904,   944,   854,   880,   304,   856,   964,   667,   396,
   870,   961,   546,  1023,    16,   227,   419,   227,   227,   419,
   227,   775,   797,   930,   708,   419,   227,   227,   454,  1016,
   403,   447,   438,   846,   880,   647,   426,   427,   967,    16,
   931,   484,   724,   656,   492,   450,   451,   957,   493,   772,
   949,   283,   283,   793,   625,   769,   887,   883,   867,   386,
   283,  1026,   388,   389,   390,   437,   443,   391,   392,   393,
   730,   304,   735,   722,   227,   227,   304,   878,   875,   786,
   410,   421,   955,   227,   421,   319,   798,   853,   nil,   880,
   421,   908,   909,   nil,   nil,   719,   521,   738,   nil,   738,
   676,    16,   319,   414,   414,    16,   nil,   nil,   nil,   310,
    16,   518,   879,   503,   881,   649,   536,   933,   552,   553,
   nil,   118,   522,   nil,   506,   nil,   310,   950,   528,   652,
   274,   nil,   nil,   nil,   278,   551,    16,   508,   507,   652,
   nil,   827,   nil,   nil,   835,   836,   619,   nil,   525,   nil,
   829,   227,    16,    16,   nil,   711,  1017,   558,   676,   676,
   nil,   nil,   nil,   284,   782,   720,   785,   953,   759,   652,
   345,   284,   227,   764,   509,   840,   118,   652,   573,   554,
   nil,   301,   514,   nil,   268,   632,   301,   nil,   227,   636,
   491,   570,   830,   nil,   nil,   nil,   834,   988,   494,   745,
   nil,   285,   595,   nil,   638,   645,   nil,   574,   796,   285,
   510,   648,   607,   437,   443,   516,   132,   nil,   nil,   958,
   602,   959,   nil,   nil,   nil,   422,   nil,   826,   422,   nil,
   664,   nil,   nil,   641,   422,   nil,   283,   nil,   nil,   nil,
   nil,    26,   135,   978,   nil,   nil,    26,   nil,   nil,   137,
   nil,   613,   nil,   nil,   nil,   nil,   nil,   618,   nil,   227,
   nil,    26,  1002,   626,   nil,   nil,   nil,   447,   438,   729,
    26,    26,    26,   nil,    26,   nil,   nil,   nil,   nil,   755,
   757,   782,   nil,   nil,   760,   762,   612,   nil,   nil,   nil,
   nil,   nil,   617,   nil,   319,   nil,   nil,   283,  1019,   613,
    26,   nil,   319,   900,   nil,    26,    26,   nil,   nil,    26,
    16,   nil,   nil,   nil,   nil,   794,   700,   906,   310,   702,
   227,   nil,   nil,   nil,   nil,   nil,   310,   nil,   nil,   nil,
   nil,   447,   438,   nil,   629,   227,   nil,   283,   nil,   nil,
   525,   447,   438,   nil,   nil,   nil,   nil,   283,   525,   nil,
    16,   716,   nil,    16,   660,   788,    26,   nil,   nil,   nil,
   227,    26,    26,    26,    26,    26,   951,    26,   nil,   774,
   227,   447,   438,   nil,    16,   nil,   704,   447,   nil,   nil,
   438,   283,   nil,   nil,   923,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   742,   723,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   419,   227,   nil,   607,    16,   132,   nil,
   948,   nil,   nil,   419,   419,   447,   438,   nil,   419,   419,
   795,   841,   nil,   773,   987,   319,   637,   nil,   nil,   nil,
   607,   nil,   nil,  1024,   135,   nil,   319,   nil,   nil,   nil,
   nil,   137,   nil,   750,   nil,   nil,   nil,   710,   nil,   310,
   nil,   nil,    26,    26,    26,    26,    26,    26,    26,   nil,
   310,   837,   nil,    26,    26,    26,   613,   666,   421,   618,
   nil,   525,   nil,   nil,   nil,   nil,   nil,    26,   421,   421,
   nil,   nil,   787,   421,   421,   828,   nil,   429,   nil,   nil,
   nil,   nil,   607,   449,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   607,   nil,   nil,   838,   810,   nil,   nil,   nil,   831,
   891,   nil,    26,    26,   nil,   nil,   nil,   nil,   nil,    16,
   nil,    26,   nil,   nil,    16,   227,   607,   nil,    16,   nil,
   902,   nil,   nil,   nil,   nil,   nil,   nil,   754,    16,    26,
   132,   nil,   nil,    26,   345,   nil,   nil,   865,    26,   862,
   nil,   869,   nil,   414,   nil,   419,   nil,   nil,   nil,   857,
   866,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
  1003,   804,   806,   808,    26,   nil,   nil,   nil,   nil,   857,
   nil,   684,   943,    16,   652,   nil,   nil,   nil,   nil,    26,
    26,    26,   nil,   nil,    16,   896,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   893,
    26,   nil,   422,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   421,   nil,   422,   422,   nil,   227,    26,   422,   422,    16,
   nil,   810,   nil,   922,   889,    16,   nil,   857,   319,   nil,
   nil,   nil,   nil,   nil,   345,   nil,   nil,   319,    29,    16,
   nil,   nil,   nil,    29,   nil,   nil,   899,   nil,   nil,   nil,
   nil,   903,   920,   nil,   nil,   nil,   nil,    16,    29,   nil,
   nil,   926,   nil,   nil,   nil,   nil,   nil,    29,    29,    29,
   nil,    29,   nil,   nil,   nil,    13,   nil,   nil,   nil,   nil,
    13,   nil,   971,   849,   nil,   nil,   nil,    26,   319,   976,
   977,   nil,   nil,   684,   nil,   nil,   nil,    29,   810,   nil,
   810,   nil,    29,    29,   874,   nil,    29,   986,    13,   nil,
   nil,   nil,   968,   nil,   nil,   nil,    16,   973,   nil,   449,
   nil,   429,   nil,    16,    16,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    13,   998,   nil,   nil,    26,   nil,
   nil,    16,   283,   nil,   360,   nil,    16,   nil,    26,   nil,
   nil,   684,   684,    29,   422,   982,   810,   nil,    29,    29,
    29,    29,    29,    26,    29,   227,   935,   937,   nil,   939,
   941,   447,   942,   nil,   607,   nil,   447,   438,    26,   nil,
   nil,    26,   nil,   850,   nil,   nil,   nil,   658,    26,   nil,
    13,   nil,   nil,   810,   nil,   810,   nil,   nil,    26,    13,
   nil,    13,    26,   858,   876,   nil,   nil,   876,   nil,   nil,
   nil,   nil,   954,   nil,   nil,   849,   nil,   849,   nil,   849,
   nil,   nil,   nil,   nil,    15,   810,   nil,   nil,   684,    15,
   684,    26,    26,   nil,   nil,    26,   nil,   nil,   nil,   nil,
   nil,    26,    26,   nil,   nil,   nil,    26,    26,   nil,    29,
    29,    29,    29,    29,    29,    29,   nil,    15,   nil,   nil,
    29,    29,    29,   nil,   nil,   nil,  1011,  1012,  1013,  1014,
   859,   756,   758,   nil,    29,   nil,   761,   763,   nil,   nil,
   nil,   nil,   nil,    15,   nil,   nil,    13,   nil,   417,   nil,
   nil,   417,   nil,   nil,   849,   nil,   849,   417,   849,   nil,
   849,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    29,
    29,    13,   876,  1032,   851,   850,   nil,   850,    29,   850,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   849,   nil,   nil,   858,    29,   858,   nil,    15,
    29,   882,   nil,   884,   nil,    29,   nil,    26,    15,   nil,
    15,   nil,    26,    26,   nil,   nil,    26,   nil,   nil,   684,
   nil,   nil,   nil,   nil,   nil,   nil,    26,   nil,   nil,   nil,
   nil,    29,   nil,    13,   nil,   nil,   nil,    13,   nil,   nil,
   nil,   nil,    13,    26,   nil,   nil,    29,    29,    29,   nil,
   nil,   nil,   nil,   nil,   850,   nil,   850,   nil,   850,   859,
   850,   nil,   859,   nil,   859,   nil,   859,    29,    13,   nil,
   nil,    26,   nil,   842,   858,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    26,    29,    13,    13,   756,   758,   763,   761,
   nil,   nil,   850,   nil,   nil,    15,   nil,    15,   nil,   nil,
    15,   nil,   911,   913,   915,   nil,    15,   nil,   960,   nil,
   962,   nil,   nil,    26,   nil,   nil,   nil,    26,   nil,   nil,
    15,   nil,   nil,    26,   nil,   nil,   nil,   nil,   nil,   nil,
   979,   nil,   980,   nil,   981,   nil,   nil,    26,   nil,   nil,
   nil,   859,   nil,   859,   nil,   859,   nil,   859,   nil,   nil,
   nil,   nil,   nil,   nil,    29,    26,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   842,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   859,
   nil,   nil,    15,   nil,   nil,  1020,    15,  1021,   nil,  1022,
   nil,    15,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    29,  1025,   nil,   990,   992,
   994,   996,   nil,   997,    26,    29,   nil,    15,   nil,   nil,
   nil,    26,    26,  1033,   nil,   nil,   nil,   nil,   nil,   nil,
    29,   nil,   nil,    15,    15,   nil,   nil,   nil,   nil,    26,
   nil,   nil,    13,   nil,    26,    29,   nil,   nil,    29,   nil,
   nil,   nil,   nil,   nil,   nil,    29,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    26,   nil,    29,   nil,   nil,   nil,    29,
  1028,  1029,  1030,  1031,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    13,   nil,  1034,    13,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    29,    29,
   nil,   nil,    29,   nil,   nil,   nil,    13,   nil,    29,    29,
   nil,   nil,   nil,    29,    29,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   417,   nil,   nil,   nil,    13,
   nil,   nil,   nil,   nil,   nil,   417,   417,   nil,   nil,   nil,
   417,   417,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    15,   nil,   nil,   nil,    17,   nil,   nil,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    29,   nil,   nil,   nil,   nil,    29,
    29,   nil,   nil,    29,   nil,   nil,   nil,   nil,    17,   312,
   312,    15,   nil,    29,    15,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    29,    13,   nil,   nil,    17,    15,    13,   nil,   nil,   nil,
    13,   nil,   nil,   nil,   358,   367,   367,   nil,   nil,    38,
    13,   nil,   nil,   nil,    38,   nil,   nil,   nil,    29,   nil,
   nil,   nil,   nil,   nil,    15,   nil,   nil,   417,    15,    29,
   nil,   nil,   nil,   nil,    15,    15,   nil,   nil,   nil,    15,
    15,   nil,    38,   308,   308,   nil,   nil,   nil,   nil,   nil,
    17,   nil,   nil,   nil,   nil,    13,   nil,   nil,   nil,    17,
    29,    17,   nil,   nil,    29,   nil,    13,   nil,    38,   nil,
    29,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   354,   370,
   370,   370,   nil,   nil,    29,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    13,    29,   nil,   nil,   nil,   nil,    13,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    13,   nil,   nil,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    38,   nil,    38,   nil,   nil,   nil,    13,
    15,   nil,   nil,   929,   nil,    15,    17,   nil,   420,    15,
   nil,   420,   nil,   342,   nil,   nil,   nil,   420,   nil,    15,
   nil,    29,   nil,   nil,   nil,   nil,   nil,   nil,    29,    29,
   nil,    17,   nil,   nil,   nil,   nil,    15,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    29,   nil,   nil,   nil,
   nil,    29,   nil,   nil,   nil,   nil,   nil,   nil,    13,   nil,
   nil,   nil,   nil,   nil,    15,    13,    13,   nil,   nil,   nil,
    29,   nil,   nil,   nil,   nil,    15,   nil,   nil,   nil,   nil,
    38,   nil,   nil,    13,   nil,   nil,   nil,   nil,    13,   nil,
   nil,   nil,   nil,    17,   nil,   nil,   nil,    17,   nil,   nil,
   nil,   312,    17,   nil,   nil,    38,   nil,   nil,   nil,   nil,
    15,   nil,   nil,   nil,   nil,   nil,    15,   nil,   312,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    39,    17,   nil,
    15,   nil,    39,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    17,    17,   nil,   nil,    15,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    39,   309,   309,   nil,   412,   425,   nil,    38,   nil,   nil,
   nil,    38,   nil,   nil,   nil,   308,    38,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    39,   nil,   nil,   nil,
   nil,   nil,   308,   nil,   nil,   nil,   355,   371,   371,   371,
   nil,   nil,    38,   nil,   nil,   nil,   nil,    15,   nil,   nil,
   nil,   nil,   nil,   nil,    15,    15,   nil,   nil,    38,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    15,   nil,   nil,   nil,   nil,    15,   nil,   500,
   nil,   502,    39,   nil,   504,   505,   nil,   nil,   nil,   nil,
   nil,    39,   nil,    39,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   312,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   312,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    39,   nil,
   nil,   nil,    17,   nil,   nil,    17,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    39,   nil,   598,    17,   nil,   nil,   nil,
   nil,   nil,   nil,   734,   nil,   nil,    38,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   308,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   308,   nil,   nil,   420,   nil,   nil,   nil,    17,
   nil,   nil,   nil,   nil,   nil,   420,   420,   nil,   nil,   nil,
   420,   420,   nil,   nil,   nil,   nil,    38,   nil,   nil,    38,
   nil,   nil,   nil,   nil,   nil,    39,   nil,   nil,   nil,    39,
   nil,   nil,   nil,   309,    39,   nil,   nil,   nil,   nil,   nil,
    38,   312,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   639,
   309,   342,   312,   642,   nil,   nil,   nil,   nil,   nil,   nil,
    39,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    38,   nil,   nil,    39,    39,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   639,   nil,   nil,   342,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   425,   nil,
   nil,    17,   nil,   nil,   nil,   308,    17,   nil,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,   308,   nil,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   420,   nil,   nil,
   nil,   743,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   639,   342,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    17,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    38,    17,   nil,   nil,   nil,
    38,   nil,   783,   nil,    38,   784,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   792,   nil,   nil,   nil,   nil,   nil,
   nil,    17,   nil,   nil,   nil,   nil,   nil,    17,   nil,   nil,
   nil,   nil,   nil,   nil,    39,   nil,   nil,   nil,   nil,   nil,
   816,    17,   309,   nil,   nil,   nil,   nil,   nil,   nil,    38,
   309,   nil,   nil,   nil,   367,   nil,   nil,   nil,   nil,    17,
    38,   nil,   234,   928,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   282,   282,   282,    39,   nil,   nil,    39,   nil,   nil,
   nil,   nil,   nil,   nil,   328,   329,   nil,   331,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    38,   839,   nil,    39,   nil,
   nil,    38,   282,   282,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   367,    38,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   nil,   nil,    17,    17,   nil,   370,   nil,
   nil,    39,   nil,    38,   nil,   nil,   nil,   924,   nil,   nil,
   nil,   nil,   nil,    17,   nil,   nil,   nil,   nil,    17,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   886,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   309,   nil,   nil,   nil,   895,   nil,   nil,
   nil,   nil,   nil,   nil,   309,   nil,   nil,   nil,   370,   nil,
   nil,   nil,    38,   nil,   nil,   342,   nil,   nil,   nil,    38,
    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    38,   nil,   nil,
   nil,   nil,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    39,   nil,   nil,   nil,   nil,    39,   nil,
   nil,   nil,    39,   nil,   282,   424,   nil,   nil,   430,   282,
   nil,   nil,    39,   nil,   430,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   234,   nil,   nil,
   461,   462,   463,   464,   465,   466,   467,   468,   469,   470,
   471,   472,   473,   474,   475,   476,   477,   478,   479,   480,
   481,   482,   nil,   nil,   nil,   nil,   483,    39,   nil,   nil,
   nil,   nil,   nil,   282,   282,   nil,   nil,   nil,    39,   nil,
   nil,   nil,   282,   nil,   nil,   nil,   nil,   nil,   nil,   282,
   nil,   282,   nil,   nil,   282,   282,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    39,   nil,   nil,   nil,   nil,   nil,    39,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   530,    39,   531,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   371,   nil,   nil,   nil,
   nil,    39,   nil,   nil,   nil,   925,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   371,   nil,   nil,   nil,
    39,   nil,   nil,   nil,   nil,   282,   nil,    39,    39,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    39,   nil,   nil,   nil,   nil,
    39,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   282,   nil,
   430,   430,   430,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   282,
   nil,   282,   nil,   282,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   282,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   430,   659,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   282,   nil,   nil,   282,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   282,   282,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   282,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   282,   430,   282,   nil,   nil,   nil,   751,   nil,   nil,
   282,   282,   430,   430,   nil,   nil,   nil,   430,   430,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   282,   nil,   nil,   282,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   282,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   282,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   282,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   430,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   430,   430,   430,
   430,   nil,   845,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   282,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   282,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   282,   430,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   282 ]

racc_goto_check = [
    34,    96,    10,    29,     9,    63,    16,    16,    29,    39,
    39,    39,    83,    73,    73,    30,   144,    32,    77,    42,
    64,    64,   101,    29,    11,    32,    45,    34,   105,    43,
    43,     4,    29,    29,    29,   107,    29,    29,    29,     7,
    81,    81,    71,    58,     7,     6,    67,    67,    73,    73,
    73,    18,    18,    68,    68,    18,    36,    36,    84,    56,
    84,    31,    29,    99,    99,    23,    23,    29,    29,    30,
   100,    29,    29,    29,    29,    44,    44,    44,    43,    79,
    79,     8,    46,    79,    64,    27,    25,    25,    21,   188,
    25,    27,    12,   105,    61,   133,     4,   179,     7,     5,
   166,   166,   166,   166,     2,   119,   161,    18,    18,    18,
    18,    57,    52,   126,   126,   131,   161,   135,    29,    54,
    78,    78,    78,    29,    29,    29,    29,    29,   184,    29,
   121,   188,   137,    12,   185,     1,   122,   135,    62,    62,
    13,    53,    25,    25,    25,    25,   147,    51,    51,    51,
    15,    78,   165,   165,     7,   147,    17,   138,    80,    80,
    80,    83,    20,     7,   143,     7,    24,    26,    37,    84,
    84,    48,    69,    74,    76,    54,    82,    94,   173,    95,
    10,    98,   102,   104,   108,   109,   110,   111,   112,    80,
    80,   113,   122,   114,   115,   135,   116,    39,    54,   188,
   117,   118,   119,   123,   179,    10,   129,   136,   139,     5,
   140,   185,   141,   137,    29,    29,    29,    29,    29,    29,
    29,    46,   142,   145,   101,    29,    29,    29,    32,   122,
     2,    64,    81,   133,   179,    58,    25,    25,   138,    29,
   146,   148,     9,    58,   150,    25,    25,   131,   151,   154,
   155,    39,    39,   156,    30,    31,   121,   184,   157,   160,
    39,   122,   162,   163,   164,    43,    43,   167,   168,   169,
   170,    10,   171,   172,    29,    29,    10,   177,   181,    31,
    21,    21,   182,    29,    21,    73,   143,   132,   nil,   179,
    21,   126,   126,   nil,   nil,    61,    34,   166,   nil,   166,
   173,    29,    73,    78,    78,    29,   nil,   nil,   nil,    29,
    29,    32,   132,   161,   132,    30,    34,   126,    10,    10,
   nil,   105,    71,   nil,     4,   nil,    29,    12,    32,    43,
    81,   nil,   nil,   nil,    81,    18,    29,     7,     6,    43,
   nil,    31,   nil,   nil,   143,   143,    54,   nil,    56,   nil,
    31,    29,    29,    29,   nil,    30,   100,    32,   173,   173,
   nil,   nil,   nil,    78,    83,    30,    58,   107,    45,    43,
    25,    78,    29,    45,     8,    31,   105,    43,    42,     4,
   nil,    52,    57,   nil,    32,    54,    52,   nil,    29,    54,
    51,    25,     9,   nil,   nil,   nil,     9,   126,    51,    30,
   nil,    80,    16,   nil,    84,    54,   nil,    25,    79,    80,
    53,    54,    34,    43,    43,    53,    64,   nil,   nil,   132,
    32,   132,   nil,   nil,   nil,    19,   nil,    99,    19,   nil,
    54,   nil,   nil,   188,    19,   nil,    39,   nil,   nil,   nil,
   nil,    47,    67,   132,   nil,   nil,    47,   nil,   nil,    68,
   nil,    81,   nil,   nil,   nil,   nil,   nil,    81,   nil,    29,
   nil,    47,    11,    34,   nil,   nil,   nil,    64,    81,    54,
    47,    47,    47,   nil,    47,   nil,   nil,   nil,   nil,    27,
    27,    83,   nil,   nil,    27,    27,    44,   nil,   nil,   nil,
   nil,   nil,    44,   nil,    73,   nil,   nil,    39,   132,    81,
    47,   nil,    73,     9,   nil,    47,    47,   nil,   nil,    47,
    29,   nil,   nil,   nil,   nil,    23,    10,     9,    29,    10,
    29,   nil,   nil,   nil,   nil,   nil,    29,   nil,   nil,   nil,
   nil,    64,    81,   nil,    44,    29,   nil,    39,   nil,   nil,
    56,    64,    81,   nil,   nil,   nil,   nil,    39,    56,   nil,
    29,    36,   nil,    29,    25,    54,    47,   nil,   nil,   nil,
    29,    47,    47,    47,    47,    47,   101,    47,   nil,    96,
    29,    64,    81,   nil,    29,   nil,   105,    64,   nil,   nil,
    81,    39,   nil,   nil,    99,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    16,   105,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    29,    29,   nil,    34,    29,    64,   nil,
    99,   nil,   nil,    29,    29,    64,    81,   nil,    29,    29,
    77,    27,   nil,   144,     9,    73,    80,   nil,   nil,   nil,
    34,   nil,   nil,    31,    67,   nil,    73,   nil,   nil,   nil,
   nil,    68,   nil,     7,   nil,   nil,   nil,    78,   nil,    29,
   nil,   nil,    47,    47,    47,    47,    47,    47,    47,   nil,
    29,    63,   nil,    47,    47,    47,    81,    80,    21,    81,
   nil,    56,   nil,   nil,   nil,   nil,   nil,    47,    21,    21,
   nil,   nil,    56,    21,    21,    10,   nil,    70,   nil,   nil,
   nil,   nil,    34,    70,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    34,   nil,   nil,    10,   120,   nil,   nil,   nil,    18,
    54,   nil,    47,    47,   nil,   nil,   nil,   nil,   nil,    29,
   nil,    47,   nil,   nil,    29,    29,    34,   nil,    29,   nil,
    54,   nil,   nil,   nil,   nil,   nil,   nil,    80,    29,    47,
    64,   nil,   nil,    47,    25,   nil,   nil,    77,    47,    10,
   nil,    77,   nil,    78,   nil,    29,   nil,   nil,   nil,    34,
    10,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    30,   176,   176,   176,    47,   nil,   nil,   nil,   nil,    34,
   nil,   178,    54,    29,    43,   nil,   nil,   nil,   nil,    47,
    47,    47,   nil,   nil,    29,    10,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,
    47,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    21,   nil,    19,    19,   nil,    29,    47,    19,    19,    29,
   nil,   120,   nil,    10,   120,    29,   nil,    34,    73,   nil,
   nil,   nil,   nil,   nil,    25,   nil,   nil,    73,    50,    29,
   nil,   nil,   nil,    50,   nil,   nil,   105,   nil,   nil,   nil,
   nil,   105,    29,   nil,   nil,   nil,   nil,    29,    50,   nil,
   nil,    29,   nil,   nil,   nil,   nil,   nil,    50,    50,    50,
   nil,    50,   nil,   nil,   nil,    22,   nil,   nil,   nil,   nil,
    22,   nil,    10,   125,   nil,   nil,   nil,    47,    73,    10,
    10,   nil,   nil,   178,   nil,   nil,   nil,    50,   120,   nil,
   120,   nil,    50,    50,   125,   nil,    50,    10,    22,   nil,
   nil,   nil,    29,   nil,   nil,   nil,    29,    32,   nil,    70,
   nil,    70,   nil,    29,    29,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    22,    34,   nil,   nil,    47,   nil,
   nil,    29,    39,   nil,    22,   nil,    29,   nil,    47,   nil,
   nil,   178,   178,    50,    19,   120,   120,   nil,    50,    50,
    50,    50,    50,    47,    50,    29,   176,   176,   nil,   176,
   176,    64,   176,   nil,    34,   nil,    64,    81,    47,   nil,
   nil,    47,   nil,   127,   nil,   nil,   nil,    70,    47,   nil,
    22,   nil,   nil,   120,   nil,   120,   nil,   nil,    47,    22,
   nil,    22,    47,   178,   127,   nil,   nil,   127,   nil,   nil,
   nil,   nil,   125,   nil,   nil,   125,   nil,   125,   nil,   125,
   nil,   nil,   nil,   nil,    28,   120,   nil,   nil,   178,    28,
   178,    47,    47,   nil,   nil,    47,   nil,   nil,   nil,   nil,
   nil,    47,    47,   nil,   nil,   nil,    47,    47,   nil,    50,
    50,    50,    50,    50,    50,    50,   nil,    28,   nil,   nil,
    50,    50,    50,   nil,   nil,   nil,   176,   176,   176,   176,
   180,    70,    70,   nil,    50,   nil,    70,    70,   nil,   nil,
   nil,   nil,   nil,    28,   nil,   nil,    22,   nil,    22,   nil,
   nil,    22,   nil,   nil,   125,   nil,   125,    22,   125,   nil,
   125,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    50,
    50,    22,   127,   176,   130,   127,   nil,   127,    50,   127,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   125,   nil,   nil,   178,    50,   178,   nil,    28,
    50,   130,   nil,   130,   nil,    50,   nil,    47,    28,   nil,
    28,   nil,    47,    47,   nil,   nil,    47,   nil,   nil,   178,
   nil,   nil,   nil,   nil,   nil,   nil,    47,   nil,   nil,   nil,
   nil,    50,   nil,    22,   nil,   nil,   nil,    22,   nil,   nil,
   nil,   nil,    22,    47,   nil,   nil,    50,    50,    50,   nil,
   nil,   nil,   nil,   nil,   127,   nil,   127,   nil,   127,   180,
   127,   nil,   180,   nil,   180,   nil,   180,    50,    22,   nil,
   nil,    47,   nil,    70,   178,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    47,    50,    22,    22,    70,    70,    70,    70,
   nil,   nil,   127,   nil,   nil,    28,   nil,    28,   nil,   nil,
    28,   nil,   128,   128,   128,   nil,    28,   nil,   130,   nil,
   130,   nil,   nil,    47,   nil,   nil,   nil,    47,   nil,   nil,
    28,   nil,   nil,    47,   nil,   nil,   nil,   nil,   nil,   nil,
   130,   nil,   130,   nil,   130,   nil,   nil,    47,   nil,   nil,
   nil,   180,   nil,   180,   nil,   180,   nil,   180,   nil,   nil,
   nil,   nil,   nil,   nil,    50,    47,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    70,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   180,
   nil,   nil,    28,   nil,   nil,   130,    28,   130,   nil,   130,
   nil,    28,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    50,   130,   nil,   128,   128,
   128,   128,   nil,   128,    47,    50,   nil,    28,   nil,   nil,
   nil,    47,    47,   130,   nil,   nil,   nil,   nil,   nil,   nil,
    50,   nil,   nil,    28,    28,   nil,   nil,   nil,   nil,    47,
   nil,   nil,    22,   nil,    47,    50,   nil,   nil,    50,   nil,
   nil,   nil,   nil,   nil,   nil,    50,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    47,   nil,    50,   nil,   nil,   nil,    50,
   128,   128,   128,   128,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    22,   nil,   128,    22,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    50,    50,
   nil,   nil,    50,   nil,   nil,   nil,    22,   nil,    50,    50,
   nil,   nil,   nil,    50,    50,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    22,   nil,   nil,   nil,    22,
   nil,   nil,   nil,   nil,   nil,    22,    22,   nil,   nil,   nil,
    22,    22,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    28,   nil,   nil,   nil,    33,   nil,   nil,   nil,   nil,
    33,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    50,   nil,   nil,   nil,   nil,    50,
    50,   nil,   nil,    50,   nil,   nil,   nil,   nil,    33,    33,
    33,    28,   nil,    50,    28,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    50,    22,   nil,   nil,    33,    28,    22,   nil,   nil,   nil,
    22,   nil,   nil,   nil,    33,    33,    33,   nil,   nil,    59,
    22,   nil,   nil,   nil,    59,   nil,   nil,   nil,    50,   nil,
   nil,   nil,   nil,   nil,    28,   nil,   nil,    22,    28,    50,
   nil,   nil,   nil,   nil,    28,    28,   nil,   nil,   nil,    28,
    28,   nil,    59,    59,    59,   nil,   nil,   nil,   nil,   nil,
    33,   nil,   nil,   nil,   nil,    22,   nil,   nil,   nil,    33,
    50,    33,   nil,   nil,    50,   nil,    22,   nil,    59,   nil,
    50,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    59,    59,
    59,    59,   nil,   nil,    50,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    22,    50,   nil,   nil,   nil,   nil,    22,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    22,   nil,   nil,    59,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    59,   nil,    59,   nil,   nil,   nil,    22,
    28,   nil,   nil,    22,   nil,    28,    33,   nil,    33,    28,
   nil,    33,   nil,    75,   nil,   nil,   nil,    33,   nil,    28,
   nil,    50,   nil,   nil,   nil,   nil,   nil,   nil,    50,    50,
   nil,    33,   nil,   nil,   nil,   nil,    28,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    50,   nil,   nil,   nil,
   nil,    50,   nil,   nil,   nil,   nil,   nil,   nil,    22,   nil,
   nil,   nil,   nil,   nil,    28,    22,    22,   nil,   nil,   nil,
    50,   nil,   nil,   nil,   nil,    28,   nil,   nil,   nil,   nil,
    59,   nil,   nil,    22,   nil,   nil,   nil,   nil,    22,   nil,
   nil,   nil,   nil,    33,   nil,   nil,   nil,    33,   nil,   nil,
   nil,    33,    33,   nil,   nil,    59,   nil,   nil,   nil,   nil,
    28,   nil,   nil,   nil,   nil,   nil,    28,   nil,    33,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    60,    33,   nil,
    28,   nil,    60,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    33,    33,   nil,   nil,    28,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    60,    60,    60,   nil,    75,    75,   nil,    59,   nil,   nil,
   nil,    59,   nil,   nil,   nil,    59,    59,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    59,   nil,   nil,   nil,    60,    60,    60,    60,
   nil,   nil,    59,   nil,   nil,   nil,   nil,    28,   nil,   nil,
   nil,   nil,   nil,   nil,    28,    28,   nil,   nil,    59,    59,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    28,   nil,   nil,   nil,   nil,    28,   nil,    75,
   nil,    75,    60,   nil,    75,    75,   nil,   nil,   nil,   nil,
   nil,    60,   nil,    60,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    33,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    33,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    33,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    60,   nil,
   nil,   nil,    33,   nil,   nil,    33,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    60,   nil,    75,    33,   nil,   nil,   nil,
   nil,   nil,   nil,    33,   nil,   nil,    59,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    59,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    59,   nil,   nil,    33,   nil,   nil,   nil,    33,
   nil,   nil,   nil,   nil,   nil,    33,    33,   nil,   nil,   nil,
    33,    33,   nil,   nil,   nil,   nil,    59,   nil,   nil,    59,
   nil,   nil,   nil,   nil,   nil,    60,   nil,   nil,   nil,    60,
   nil,   nil,   nil,    60,    60,   nil,   nil,   nil,   nil,   nil,
    59,    33,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    75,
    60,    75,    33,    75,   nil,   nil,   nil,   nil,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    59,   nil,   nil,    60,    60,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    75,   nil,   nil,    75,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    75,   nil,
   nil,    33,   nil,   nil,   nil,    59,    33,   nil,   nil,   nil,
    33,   nil,   nil,   nil,   nil,   nil,    59,   nil,   nil,   nil,
    33,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    33,   nil,   nil,
   nil,    75,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    75,    75,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    33,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    59,    33,   nil,   nil,   nil,
    59,   nil,    75,   nil,    59,    75,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    59,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    75,   nil,   nil,   nil,   nil,   nil,
   nil,    33,   nil,   nil,   nil,   nil,   nil,    33,   nil,   nil,
   nil,   nil,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    75,    33,    60,   nil,   nil,   nil,   nil,   nil,   nil,    59,
    60,   nil,   nil,   nil,    33,   nil,   nil,   nil,   nil,    33,
    59,   nil,    35,    33,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    35,    35,    35,    60,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,   nil,    35,    35,   nil,    35,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    59,    75,   nil,    60,   nil,
   nil,    59,    35,    35,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    33,    59,   nil,   nil,    33,   nil,
   nil,   nil,   nil,   nil,   nil,    33,    33,   nil,    59,   nil,
   nil,    60,   nil,    59,   nil,   nil,   nil,    59,   nil,   nil,
   nil,   nil,   nil,    33,   nil,   nil,   nil,   nil,    33,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    75,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    60,   nil,   nil,   nil,    75,   nil,   nil,
   nil,   nil,   nil,   nil,    60,   nil,   nil,   nil,    59,   nil,
   nil,   nil,    59,   nil,   nil,    75,   nil,   nil,   nil,    59,
    59,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    59,   nil,   nil,
   nil,   nil,    59,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    60,   nil,   nil,   nil,   nil,    60,   nil,
   nil,   nil,    60,   nil,    35,    35,   nil,   nil,    35,    35,
   nil,   nil,    60,   nil,    35,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,   nil,   nil,
    35,    35,    35,    35,    35,    35,    35,    35,    35,    35,
    35,    35,    35,    35,    35,    35,    35,    35,    35,    35,
    35,    35,   nil,   nil,   nil,   nil,    35,    60,   nil,   nil,
   nil,   nil,   nil,    35,    35,   nil,   nil,   nil,    60,   nil,
   nil,   nil,    35,   nil,   nil,   nil,   nil,   nil,   nil,    35,
   nil,    35,   nil,   nil,    35,    35,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    35,    60,    35,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    60,   nil,   nil,   nil,
   nil,    60,   nil,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    60,   nil,   nil,   nil,
    60,   nil,   nil,   nil,   nil,    35,   nil,    60,    60,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    60,   nil,   nil,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,   nil,
    35,    35,    35,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,
   nil,    35,   nil,    35,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,    35,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    35,   nil,   nil,    35,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,    35,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    35,    35,    35,   nil,   nil,   nil,    35,   nil,   nil,
    35,    35,    35,    35,   nil,   nil,   nil,    35,    35,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    35,   nil,   nil,    35,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    35,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    35,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    35,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    35,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,    35,    35,
    35,   nil,    35,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    35,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    35,    35,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    35 ]

racc_goto_pointer = [
   nil,   135,   104,   nil,    28,    94,    42,    39,    74,  -329,
   -31,  -509,  -569,  -649,   nil,  -357,    -2,   147,   -13,   212,
    93,    68,   885,  -147,   -46,    22,    40,  -131,  1034,     3,
  -202,  -370,     1,  1545,   -19,  2392,   -10,  -383,   nil,   -20,
   nil,   nil,    -5,  -189,    49,  -244,  -410,   441,  -317,   nil,
   848,   118,    79,   108,  -105,   nil,    25,    76,  -280,  1619,
  1877,  -270,    68,   -67,    12,   nil,   nil,    38,    45,  -236,
   471,     1,   nil,   -21,   129,  1702,   114,   -42,    91,  -258,
   129,    14,  -309,  -273,  -439,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   105,   118,   -61,   nil,  -163,  -283,
  -630,  -330,   113,   nil,  -185,    25,   nil,  -537,   112,   127,
  -148,   128,   125,  -377,  -530,  -178,  -186,  -373,  -636,  -783,
    20,  -555,  -754,  -575,   nil,   115,  -557,   215,   401,  -572,
   346,  -663,  -491,  -539,   nil,  -661,  -711,  -786,  -761,  -338,
  -587,  -132,  -445,  -382,    -6,  -648,  -632,  -338,   -24,   nil,
   -31,   -28,   nil,   nil,  -379,  -646,  -409,  -535,   nil,   nil,
   178,    23,   176,   176,   176,  -238,  -288,   178,   178,   178,
  -308,  -307,  -296,  -368,   nil,   nil,    98,  -526,   235,  -706,
   302,  -521,  -625,   nil,  -677,  -778,   nil,   nil,  -410 ]

racc_goto_default = [
   nil,   nil,   nil,     3,   nil,     4,   353,   299,   nil,   nil,
   533,   nil,   821,   nil,   296,   297,   nil,   nil,   nil,    11,
    12,    18,   232,   nil,   nil,    14,   nil,   418,   233,   327,
   nil,   nil,   566,   231,   453,    21,   nil,   nil,   348,    22,
    23,    24,   nil,   655,   nil,   nil,   nil,   316,   nil,    25,
   313,   432,    32,   nil,   nil,    34,    37,    36,   nil,   228,
   229,   365,   nil,   134,   440,   133,   136,    79,    80,   nil,
   423,    94,    44,    47,   264,   288,   nil,   790,   433,   nil,
   434,   445,   614,   496,   286,   272,    48,    49,    50,    51,
    52,    53,    54,    55,    56,   nil,   273,    62,   nil,   nil,
   nil,   nil,   nil,    70,   nil,   548,    71,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   812,
   683,   nil,   813,   956,   848,   671,   nil,   672,   nil,   nil,
   673,   nil,   675,   nil,   777,   nil,   nil,   nil,   681,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   444,
   nil,   nil,   634,   628,   nil,   nil,   nil,   nil,    78,    81,
    82,   nil,   nil,   nil,   nil,   nil,   576,   nil,   nil,   nil,
   nil,   nil,   nil,   877,   727,   670,   nil,   674,   885,   686,
   688,   689,   860,   692,   693,   861,   696,   699,   291 ]

racc_reduce_table = [
  0, 0, :racc_error,
  1, 146, :_reduce_none,
  2, 147, :_reduce_2,
  0, 148, :_reduce_3,
  1, 148, :_reduce_4,
  3, 148, :_reduce_5,
  2, 148, :_reduce_6,
  1, 150, :_reduce_none,
  2, 150, :_reduce_8,
  3, 153, :_reduce_9,
  4, 154, :_reduce_10,
  2, 155, :_reduce_11,
  0, 159, :_reduce_12,
  1, 159, :_reduce_13,
  3, 159, :_reduce_14,
  2, 159, :_reduce_15,
  1, 160, :_reduce_none,
  2, 160, :_reduce_17,
  0, 171, :_reduce_18,
  4, 152, :_reduce_19,
  3, 152, :_reduce_20,
  3, 152, :_reduce_21,
  3, 152, :_reduce_22,
  2, 152, :_reduce_23,
  3, 152, :_reduce_24,
  3, 152, :_reduce_25,
  3, 152, :_reduce_26,
  3, 152, :_reduce_27,
  3, 152, :_reduce_28,
  4, 152, :_reduce_29,
  1, 152, :_reduce_none,
  3, 152, :_reduce_31,
  3, 152, :_reduce_32,
  3, 152, :_reduce_33,
  1, 152, :_reduce_none,
  3, 164, :_reduce_35,
  3, 164, :_reduce_36,
  6, 164, :_reduce_37,
  5, 164, :_reduce_38,
  5, 164, :_reduce_39,
  5, 164, :_reduce_40,
  5, 164, :_reduce_41,
  3, 164, :_reduce_42,
  1, 172, :_reduce_none,
  3, 172, :_reduce_44,
  1, 172, :_reduce_none,
  1, 170, :_reduce_none,
  3, 170, :_reduce_47,
  3, 170, :_reduce_48,
  3, 170, :_reduce_49,
  2, 170, :_reduce_50,
  1, 170, :_reduce_none,
  1, 163, :_reduce_none,
  0, 183, :_reduce_53,
  3, 181, :_reduce_54,
  1, 166, :_reduce_none,
  1, 166, :_reduce_none,
  1, 185, :_reduce_none,
  4, 185, :_reduce_58,
  0, 193, :_reduce_59,
  4, 190, :_reduce_60,
  1, 192, :_reduce_none,
  2, 184, :_reduce_62,
  3, 184, :_reduce_63,
  4, 184, :_reduce_64,
  5, 184, :_reduce_65,
  4, 184, :_reduce_66,
  5, 184, :_reduce_67,
  2, 184, :_reduce_68,
  2, 184, :_reduce_69,
  2, 184, :_reduce_70,
  2, 184, :_reduce_71,
  2, 184, :_reduce_72,
  1, 165, :_reduce_73,
  3, 165, :_reduce_74,
  1, 198, :_reduce_75,
  3, 198, :_reduce_76,
  1, 197, :_reduce_none,
  2, 197, :_reduce_78,
  3, 197, :_reduce_79,
  5, 197, :_reduce_80,
  2, 197, :_reduce_81,
  4, 197, :_reduce_82,
  2, 197, :_reduce_83,
  4, 197, :_reduce_84,
  1, 197, :_reduce_85,
  3, 197, :_reduce_86,
  1, 201, :_reduce_none,
  3, 201, :_reduce_88,
  2, 200, :_reduce_89,
  3, 200, :_reduce_90,
  1, 203, :_reduce_91,
  3, 203, :_reduce_92,
  1, 202, :_reduce_93,
  1, 202, :_reduce_94,
  4, 202, :_reduce_95,
  3, 202, :_reduce_96,
  3, 202, :_reduce_97,
  3, 202, :_reduce_98,
  3, 202, :_reduce_99,
  2, 202, :_reduce_100,
  1, 202, :_reduce_101,
  1, 167, :_reduce_102,
  1, 167, :_reduce_103,
  4, 167, :_reduce_104,
  3, 167, :_reduce_105,
  3, 167, :_reduce_106,
  3, 167, :_reduce_107,
  3, 167, :_reduce_108,
  2, 167, :_reduce_109,
  1, 167, :_reduce_110,
  1, 206, :_reduce_111,
  1, 206, :_reduce_none,
  2, 207, :_reduce_113,
  1, 207, :_reduce_114,
  3, 207, :_reduce_115,
  1, 208, :_reduce_none,
  1, 208, :_reduce_none,
  1, 208, :_reduce_none,
  1, 208, :_reduce_none,
  1, 208, :_reduce_none,
  1, 211, :_reduce_121,
  1, 211, :_reduce_none,
  1, 161, :_reduce_none,
  1, 161, :_reduce_none,
  1, 162, :_reduce_125,
  0, 214, :_reduce_126,
  4, 162, :_reduce_127,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  3, 180, :_reduce_199,
  3, 180, :_reduce_200,
  6, 180, :_reduce_201,
  5, 180, :_reduce_202,
  5, 180, :_reduce_203,
  5, 180, :_reduce_204,
  5, 180, :_reduce_205,
  4, 180, :_reduce_206,
  3, 180, :_reduce_207,
  3, 180, :_reduce_208,
  3, 180, :_reduce_209,
  3, 180, :_reduce_210,
  3, 180, :_reduce_211,
  3, 180, :_reduce_212,
  3, 180, :_reduce_213,
  3, 180, :_reduce_214,
  3, 180, :_reduce_215,
  4, 180, :_reduce_216,
  2, 180, :_reduce_217,
  2, 180, :_reduce_218,
  3, 180, :_reduce_219,
  3, 180, :_reduce_220,
  3, 180, :_reduce_221,
  3, 180, :_reduce_222,
  1, 180, :_reduce_none,
  3, 180, :_reduce_224,
  3, 180, :_reduce_225,
  3, 180, :_reduce_226,
  3, 180, :_reduce_227,
  3, 180, :_reduce_228,
  2, 180, :_reduce_229,
  2, 180, :_reduce_230,
  3, 180, :_reduce_231,
  3, 180, :_reduce_232,
  3, 180, :_reduce_233,
  3, 180, :_reduce_234,
  3, 180, :_reduce_235,
  6, 180, :_reduce_236,
  1, 180, :_reduce_none,
  1, 219, :_reduce_none,
  1, 219, :_reduce_none,
  1, 219, :_reduce_none,
  1, 219, :_reduce_none,
  3, 217, :_reduce_242,
  3, 217, :_reduce_243,
  1, 220, :_reduce_none,
  1, 221, :_reduce_none,
  2, 221, :_reduce_none,
  4, 221, :_reduce_247,
  2, 221, :_reduce_248,
  1, 215, :_reduce_none,
  3, 215, :_reduce_250,
  3, 226, :_reduce_251,
  0, 227, :_reduce_252,
  1, 227, :_reduce_none,
  0, 175, :_reduce_254,
  1, 175, :_reduce_none,
  2, 175, :_reduce_none,
  4, 175, :_reduce_257,
  2, 175, :_reduce_258,
  1, 196, :_reduce_259,
  2, 196, :_reduce_260,
  2, 196, :_reduce_261,
  4, 196, :_reduce_262,
  1, 196, :_reduce_263,
  0, 230, :_reduce_264,
  2, 189, :_reduce_265,
  2, 229, :_reduce_266,
  2, 228, :_reduce_267,
  0, 228, :_reduce_268,
  1, 223, :_reduce_269,
  2, 223, :_reduce_270,
  3, 223, :_reduce_271,
  4, 223, :_reduce_272,
  1, 169, :_reduce_273,
  1, 169, :_reduce_none,
  3, 168, :_reduce_275,
  4, 168, :_reduce_276,
  2, 168, :_reduce_277,
  1, 218, :_reduce_none,
  1, 218, :_reduce_none,
  1, 218, :_reduce_none,
  1, 218, :_reduce_none,
  1, 218, :_reduce_none,
  1, 218, :_reduce_none,
  1, 218, :_reduce_none,
  1, 218, :_reduce_none,
  1, 218, :_reduce_none,
  1, 218, :_reduce_none,
  1, 218, :_reduce_288,
  0, 254, :_reduce_289,
  4, 218, :_reduce_290,
  0, 255, :_reduce_291,
  4, 218, :_reduce_292,
  0, 256, :_reduce_293,
  4, 218, :_reduce_294,
  3, 218, :_reduce_295,
  3, 218, :_reduce_296,
  2, 218, :_reduce_297,
  3, 218, :_reduce_298,
  3, 218, :_reduce_299,
  1, 218, :_reduce_300,
  4, 218, :_reduce_301,
  3, 218, :_reduce_302,
  1, 218, :_reduce_303,
  5, 218, :_reduce_304,
  4, 218, :_reduce_305,
  3, 218, :_reduce_306,
  2, 218, :_reduce_307,
  1, 218, :_reduce_none,
  2, 218, :_reduce_309,
  0, 257, :_reduce_310,
  3, 218, :_reduce_311,
  6, 218, :_reduce_312,
  6, 218, :_reduce_313,
  4, 218, :_reduce_314,
  4, 218, :_reduce_315,
  5, 218, :_reduce_316,
  4, 218, :_reduce_317,
  6, 218, :_reduce_318,
  0, 258, :_reduce_319,
  6, 218, :_reduce_320,
  0, 259, :_reduce_321,
  7, 218, :_reduce_322,
  0, 260, :_reduce_323,
  5, 218, :_reduce_324,
  0, 261, :_reduce_325,
  6, 218, :_reduce_326,
  0, 262, :_reduce_327,
  0, 263, :_reduce_328,
  9, 218, :_reduce_329,
  1, 218, :_reduce_330,
  1, 218, :_reduce_331,
  1, 218, :_reduce_332,
  1, 218, :_reduce_333,
  1, 174, :_reduce_none,
  1, 248, :_reduce_335,
  1, 251, :_reduce_336,
  1, 195, :_reduce_337,
  1, 244, :_reduce_none,
  1, 244, :_reduce_none,
  2, 244, :_reduce_340,
  1, 182, :_reduce_none,
  1, 182, :_reduce_none,
  1, 245, :_reduce_none,
  5, 245, :_reduce_344,
  1, 157, :_reduce_none,
  2, 157, :_reduce_346,
  1, 247, :_reduce_none,
  1, 247, :_reduce_none,
  1, 264, :_reduce_349,
  3, 264, :_reduce_350,
  1, 267, :_reduce_351,
  3, 267, :_reduce_352,
  1, 266, :_reduce_none,
  4, 266, :_reduce_354,
  6, 266, :_reduce_355,
  3, 266, :_reduce_356,
  5, 266, :_reduce_357,
  2, 266, :_reduce_358,
  4, 266, :_reduce_359,
  1, 266, :_reduce_360,
  3, 266, :_reduce_361,
  4, 268, :_reduce_362,
  2, 268, :_reduce_363,
  2, 268, :_reduce_364,
  1, 268, :_reduce_365,
  2, 273, :_reduce_366,
  0, 273, :_reduce_367,
  6, 274, :_reduce_368,
  8, 274, :_reduce_369,
  4, 274, :_reduce_370,
  6, 274, :_reduce_371,
  4, 274, :_reduce_372,
  2, 274, :_reduce_none,
  6, 274, :_reduce_374,
  2, 274, :_reduce_375,
  4, 274, :_reduce_376,
  6, 274, :_reduce_377,
  2, 274, :_reduce_378,
  4, 274, :_reduce_379,
  2, 274, :_reduce_380,
  4, 274, :_reduce_381,
  1, 274, :_reduce_none,
  0, 278, :_reduce_383,
  1, 278, :_reduce_384,
  3, 279, :_reduce_385,
  1, 279, :_reduce_386,
  4, 279, :_reduce_387,
  1, 280, :_reduce_388,
  4, 280, :_reduce_389,
  1, 281, :_reduce_390,
  3, 281, :_reduce_391,
  1, 282, :_reduce_392,
  1, 282, :_reduce_none,
  0, 286, :_reduce_394,
  0, 287, :_reduce_395,
  4, 243, :_reduce_396,
  4, 284, :_reduce_397,
  1, 284, :_reduce_398,
  0, 290, :_reduce_399,
  4, 285, :_reduce_400,
  0, 291, :_reduce_401,
  4, 285, :_reduce_402,
  0, 293, :_reduce_403,
  4, 289, :_reduce_404,
  2, 186, :_reduce_405,
  4, 186, :_reduce_406,
  5, 186, :_reduce_407,
  5, 186, :_reduce_408,
  2, 242, :_reduce_409,
  4, 242, :_reduce_410,
  4, 242, :_reduce_411,
  3, 242, :_reduce_412,
  3, 242, :_reduce_413,
  3, 242, :_reduce_414,
  2, 242, :_reduce_415,
  1, 242, :_reduce_416,
  4, 242, :_reduce_417,
  0, 295, :_reduce_418,
  4, 241, :_reduce_419,
  0, 296, :_reduce_420,
  4, 241, :_reduce_421,
  0, 297, :_reduce_422,
  3, 191, :_reduce_423,
  0, 298, :_reduce_424,
  0, 299, :_reduce_425,
  4, 292, :_reduce_426,
  5, 246, :_reduce_427,
  1, 300, :_reduce_428,
  1, 300, :_reduce_none,
  6, 156, :_reduce_430,
  0, 156, :_reduce_431,
  1, 301, :_reduce_432,
  1, 301, :_reduce_none,
  1, 301, :_reduce_none,
  2, 302, :_reduce_435,
  1, 302, :_reduce_none,
  2, 158, :_reduce_437,
  1, 158, :_reduce_none,
  1, 231, :_reduce_none,
  1, 231, :_reduce_none,
  1, 231, :_reduce_none,
  1, 232, :_reduce_442,
  1, 304, :_reduce_443,
  2, 304, :_reduce_444,
  3, 305, :_reduce_445,
  1, 305, :_reduce_446,
  1, 305, :_reduce_447,
  3, 233, :_reduce_448,
  4, 234, :_reduce_449,
  3, 235, :_reduce_450,
  0, 309, :_reduce_451,
  3, 309, :_reduce_452,
  1, 310, :_reduce_453,
  2, 310, :_reduce_454,
  3, 237, :_reduce_455,
  0, 312, :_reduce_456,
  3, 312, :_reduce_457,
  3, 236, :_reduce_458,
  3, 238, :_reduce_459,
  0, 313, :_reduce_460,
  3, 313, :_reduce_461,
  0, 314, :_reduce_462,
  3, 314, :_reduce_463,
  0, 306, :_reduce_464,
  2, 306, :_reduce_465,
  0, 307, :_reduce_466,
  2, 307, :_reduce_467,
  0, 308, :_reduce_468,
  2, 308, :_reduce_469,
  1, 311, :_reduce_470,
  2, 311, :_reduce_471,
  0, 316, :_reduce_472,
  4, 311, :_reduce_473,
  1, 315, :_reduce_474,
  1, 315, :_reduce_475,
  1, 315, :_reduce_476,
  1, 315, :_reduce_none,
  1, 212, :_reduce_478,
  3, 213, :_reduce_479,
  1, 303, :_reduce_480,
  2, 303, :_reduce_481,
  1, 216, :_reduce_482,
  1, 216, :_reduce_483,
  1, 216, :_reduce_484,
  1, 216, :_reduce_485,
  1, 204, :_reduce_486,
  1, 204, :_reduce_487,
  1, 204, :_reduce_488,
  1, 204, :_reduce_489,
  1, 204, :_reduce_490,
  1, 205, :_reduce_491,
  1, 205, :_reduce_492,
  1, 205, :_reduce_493,
  1, 205, :_reduce_494,
  1, 205, :_reduce_495,
  1, 205, :_reduce_496,
  1, 205, :_reduce_497,
  1, 239, :_reduce_498,
  1, 239, :_reduce_499,
  1, 173, :_reduce_500,
  1, 173, :_reduce_501,
  1, 178, :_reduce_502,
  1, 178, :_reduce_503,
  0, 317, :_reduce_504,
  4, 249, :_reduce_505,
  0, 249, :_reduce_506,
  3, 252, :_reduce_507,
  0, 319, :_reduce_508,
  3, 252, :_reduce_509,
  4, 318, :_reduce_510,
  2, 318, :_reduce_511,
  2, 318, :_reduce_512,
  1, 318, :_reduce_513,
  2, 321, :_reduce_514,
  0, 321, :_reduce_515,
  6, 288, :_reduce_516,
  8, 288, :_reduce_517,
  4, 288, :_reduce_518,
  6, 288, :_reduce_519,
  4, 288, :_reduce_520,
  6, 288, :_reduce_521,
  2, 288, :_reduce_522,
  4, 288, :_reduce_523,
  6, 288, :_reduce_524,
  2, 288, :_reduce_525,
  4, 288, :_reduce_526,
  2, 288, :_reduce_527,
  4, 288, :_reduce_528,
  1, 288, :_reduce_529,
  0, 288, :_reduce_530,
  1, 283, :_reduce_531,
  1, 283, :_reduce_532,
  1, 283, :_reduce_533,
  1, 283, :_reduce_534,
  1, 265, :_reduce_none,
  1, 265, :_reduce_536,
  1, 323, :_reduce_537,
  1, 324, :_reduce_538,
  3, 324, :_reduce_539,
  1, 275, :_reduce_540,
  3, 275, :_reduce_541,
  1, 325, :_reduce_542,
  2, 326, :_reduce_543,
  1, 326, :_reduce_544,
  2, 327, :_reduce_545,
  1, 327, :_reduce_546,
  1, 269, :_reduce_547,
  3, 269, :_reduce_548,
  1, 320, :_reduce_549,
  3, 320, :_reduce_550,
  1, 328, :_reduce_none,
  1, 328, :_reduce_none,
  2, 270, :_reduce_553,
  1, 270, :_reduce_554,
  3, 329, :_reduce_555,
  3, 330, :_reduce_556,
  1, 276, :_reduce_557,
  3, 276, :_reduce_558,
  1, 322, :_reduce_559,
  3, 322, :_reduce_560,
  1, 331, :_reduce_none,
  1, 331, :_reduce_none,
  2, 277, :_reduce_563,
  1, 277, :_reduce_564,
  1, 332, :_reduce_none,
  1, 332, :_reduce_none,
  2, 272, :_reduce_567,
  2, 271, :_reduce_568,
  0, 271, :_reduce_569,
  1, 253, :_reduce_none,
  3, 253, :_reduce_571,
  0, 240, :_reduce_572,
  2, 240, :_reduce_none,
  1, 225, :_reduce_574,
  3, 225, :_reduce_575,
  3, 333, :_reduce_576,
  2, 333, :_reduce_577,
  4, 333, :_reduce_578,
  2, 333, :_reduce_579,
  1, 194, :_reduce_none,
  1, 194, :_reduce_none,
  1, 194, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 294, :_reduce_none,
  1, 294, :_reduce_none,
  1, 294, :_reduce_none,
  1, 187, :_reduce_none,
  1, 187, :_reduce_none,
  1, 177, :_reduce_592,
  1, 177, :_reduce_593,
  0, 149, :_reduce_none,
  1, 149, :_reduce_none,
  0, 179, :_reduce_none,
  1, 179, :_reduce_none,
  2, 199, :_reduce_598,
  2, 176, :_reduce_599,
  0, 224, :_reduce_none,
  1, 224, :_reduce_none,
  1, 224, :_reduce_none,
  1, 250, :_reduce_603,
  1, 250, :_reduce_none,
  1, 151, :_reduce_none,
  2, 151, :_reduce_none,
  0, 222, :_reduce_607 ]

racc_reduce_n = 608

racc_shift_n = 1035

racc_token_table = {
  false => 0,
  :error => 1,
  :kCLASS => 2,
  :kMODULE => 3,
  :kDEF => 4,
  :kUNDEF => 5,
  :kBEGIN => 6,
  :kRESCUE => 7,
  :kENSURE => 8,
  :kEND => 9,
  :kIF => 10,
  :kUNLESS => 11,
  :kTHEN => 12,
  :kELSIF => 13,
  :kELSE => 14,
  :kCASE => 15,
  :kWHEN => 16,
  :kWHILE => 17,
  :kUNTIL => 18,
  :kFOR => 19,
  :kBREAK => 20,
  :kNEXT => 21,
  :kREDO => 22,
  :kRETRY => 23,
  :kIN => 24,
  :kDO => 25,
  :kDO_COND => 26,
  :kDO_BLOCK => 27,
  :kDO_LAMBDA => 28,
  :kRETURN => 29,
  :kYIELD => 30,
  :kSUPER => 31,
  :kSELF => 32,
  :kNIL => 33,
  :kTRUE => 34,
  :kFALSE => 35,
  :kAND => 36,
  :kOR => 37,
  :kNOT => 38,
  :kIF_MOD => 39,
  :kUNLESS_MOD => 40,
  :kWHILE_MOD => 41,
  :kUNTIL_MOD => 42,
  :kRESCUE_MOD => 43,
  :kALIAS => 44,
  :kDEFINED => 45,
  :klBEGIN => 46,
  :klEND => 47,
  :k__LINE__ => 48,
  :k__FILE__ => 49,
  :k__ENCODING__ => 50,
  :tIDENTIFIER => 51,
  :tFID => 52,
  :tGVAR => 53,
  :tIVAR => 54,
  :tCONSTANT => 55,
  :tLABEL => 56,
  :tCVAR => 57,
  :tNTH_REF => 58,
  :tBACK_REF => 59,
  :tSTRING_CONTENT => 60,
  :tINTEGER => 61,
  :tFLOAT => 62,
  :tUPLUS => 63,
  :tUMINUS => 64,
  :tUNARY_NUM => 65,
  :tPOW => 66,
  :tCMP => 67,
  :tEQ => 68,
  :tEQQ => 69,
  :tNEQ => 70,
  :tGEQ => 71,
  :tLEQ => 72,
  :tANDOP => 73,
  :tOROP => 74,
  :tMATCH => 75,
  :tNMATCH => 76,
  :tDOT => 77,
  :tDOT2 => 78,
  :tDOT3 => 79,
  :tAREF => 80,
  :tASET => 81,
  :tLSHFT => 82,
  :tRSHFT => 83,
  :tCOLON2 => 84,
  :tCOLON3 => 85,
  :tOP_ASGN => 86,
  :tASSOC => 87,
  :tLPAREN => 88,
  :tLPAREN2 => 89,
  :tRPAREN => 90,
  :tLPAREN_ARG => 91,
  :tLBRACK => 92,
  :tLBRACK2 => 93,
  :tRBRACK => 94,
  :tLBRACE => 95,
  :tLBRACE_ARG => 96,
  :tSTAR => 97,
  :tSTAR2 => 98,
  :tAMPER => 99,
  :tAMPER2 => 100,
  :tTILDE => 101,
  :tPERCENT => 102,
  :tDIVIDE => 103,
  :tDSTAR => 104,
  :tPLUS => 105,
  :tMINUS => 106,
  :tLT => 107,
  :tGT => 108,
  :tPIPE => 109,
  :tBANG => 110,
  :tCARET => 111,
  :tLCURLY => 112,
  :tRCURLY => 113,
  :tBACK_REF2 => 114,
  :tSYMBEG => 115,
  :tSTRING_BEG => 116,
  :tXSTRING_BEG => 117,
  :tREGEXP_BEG => 118,
  :tREGEXP_OPT => 119,
  :tWORDS_BEG => 120,
  :tQWORDS_BEG => 121,
  :tSYMBOLS_BEG => 122,
  :tQSYMBOLS_BEG => 123,
  :tSTRING_DBEG => 124,
  :tSTRING_DVAR => 125,
  :tSTRING_END => 126,
  :tSTRING_DEND => 127,
  :tSTRING => 128,
  :tSYMBOL => 129,
  :tNL => 130,
  :tEH => 131,
  :tCOLON => 132,
  :tCOMMA => 133,
  :tSPACE => 134,
  :tSEMI => 135,
  :tLAMBDA => 136,
  :tLAMBEG => 137,
  :tCHARACTER => 138,
  :tRATIONAL => 139,
  :tIMAGINARY => 140,
  :tLABEL_END => 141,
  :tANDDOT => 142,
  :tEQL => 143,
  :tLOWEST => 144 }

racc_nt_base = 145

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]
Ractor.make_shareable(Racc_arg) if defined?(Ractor)

Racc_token_to_s_table = [
  "$end",
  "error",
  "kCLASS",
  "kMODULE",
  "kDEF",
  "kUNDEF",
  "kBEGIN",
  "kRESCUE",
  "kENSURE",
  "kEND",
  "kIF",
  "kUNLESS",
  "kTHEN",
  "kELSIF",
  "kELSE",
  "kCASE",
  "kWHEN",
  "kWHILE",
  "kUNTIL",
  "kFOR",
  "kBREAK",
  "kNEXT",
  "kREDO",
  "kRETRY",
  "kIN",
  "kDO",
  "kDO_COND",
  "kDO_BLOCK",
  "kDO_LAMBDA",
  "kRETURN",
  "kYIELD",
  "kSUPER",
  "kSELF",
  "kNIL",
  "kTRUE",
  "kFALSE",
  "kAND",
  "kOR",
  "kNOT",
  "kIF_MOD",
  "kUNLESS_MOD",
  "kWHILE_MOD",
  "kUNTIL_MOD",
  "kRESCUE_MOD",
  "kALIAS",
  "kDEFINED",
  "klBEGIN",
  "klEND",
  "k__LINE__",
  "k__FILE__",
  "k__ENCODING__",
  "tIDENTIFIER",
  "tFID",
  "tGVAR",
  "tIVAR",
  "tCONSTANT",
  "tLABEL",
  "tCVAR",
  "tNTH_REF",
  "tBACK_REF",
  "tSTRING_CONTENT",
  "tINTEGER",
  "tFLOAT",
  "tUPLUS",
  "tUMINUS",
  "tUNARY_NUM",
  "tPOW",
  "tCMP",
  "tEQ",
  "tEQQ",
  "tNEQ",
  "tGEQ",
  "tLEQ",
  "tANDOP",
  "tOROP",
  "tMATCH",
  "tNMATCH",
  "tDOT",
  "tDOT2",
  "tDOT3",
  "tAREF",
  "tASET",
  "tLSHFT",
  "tRSHFT",
  "tCOLON2",
  "tCOLON3",
  "tOP_ASGN",
  "tASSOC",
  "tLPAREN",
  "tLPAREN2",
  "tRPAREN",
  "tLPAREN_ARG",
  "tLBRACK",
  "tLBRACK2",
  "tRBRACK",
  "tLBRACE",
  "tLBRACE_ARG",
  "tSTAR",
  "tSTAR2",
  "tAMPER",
  "tAMPER2",
  "tTILDE",
  "tPERCENT",
  "tDIVIDE",
  "tDSTAR",
  "tPLUS",
  "tMINUS",
  "tLT",
  "tGT",
  "tPIPE",
  "tBANG",
  "tCARET",
  "tLCURLY",
  "tRCURLY",
  "tBACK_REF2",
  "tSYMBEG",
  "tSTRING_BEG",
  "tXSTRING_BEG",
  "tREGEXP_BEG",
  "tREGEXP_OPT",
  "tWORDS_BEG",
  "tQWORDS_BEG",
  "tSYMBOLS_BEG",
  "tQSYMBOLS_BEG",
  "tSTRING_DBEG",
  "tSTRING_DVAR",
  "tSTRING_END",
  "tSTRING_DEND",
  "tSTRING",
  "tSYMBOL",
  "tNL",
  "tEH",
  "tCOLON",
  "tCOMMA",
  "tSPACE",
  "tSEMI",
  "tLAMBDA",
  "tLAMBEG",
  "tCHARACTER",
  "tRATIONAL",
  "tIMAGINARY",
  "tLABEL_END",
  "tANDDOT",
  "tEQL",
  "tLOWEST",
  "$start",
  "program",
  "top_compstmt",
  "top_stmts",
  "opt_terms",
  "top_stmt",
  "terms",
  "stmt",
  "begin_block",
  "bodystmt",
  "compstmt",
  "opt_rescue",
  "opt_else",
  "opt_ensure",
  "stmts",
  "stmt_or_begin",
  "fitem",
  "undef_list",
  "expr_value",
  "command_asgn",
  "mlhs",
  "command_call",
  "lhs",
  "mrhs",
  "mrhs_arg",
  "expr",
  "@1",
  "command_rhs",
  "var_lhs",
  "primary_value",
  "opt_call_args",
  "rbracket",
  "call_op",
  "backref",
  "opt_nl",
  "arg",
  "expr_value_do",
  "do",
  "@2",
  "command",
  "block_command",
  "block_call",
  "dot_or_colon",
  "operation2",
  "command_args",
  "cmd_brace_block",
  "brace_body",
  "fcall",
  "@3",
  "operation",
  "k_return",
  "call_args",
  "mlhs_basic",
  "mlhs_inner",
  "rparen",
  "mlhs_head",
  "mlhs_item",
  "mlhs_node",
  "mlhs_post",
  "user_variable",
  "keyword_variable",
  "cname",
  "cpath",
  "fname",
  "op",
  "reswords",
  "fsym",
  "symbol",
  "dsym",
  "@4",
  "arg_rhs",
  "simple_numeric",
  "rel_expr",
  "primary",
  "relop",
  "arg_value",
  "aref_args",
  "none",
  "args",
  "trailer",
  "assocs",
  "paren_args",
  "opt_paren_args",
  "opt_block_arg",
  "block_arg",
  "@5",
  "literal",
  "strings",
  "xstring",
  "regexp",
  "words",
  "qwords",
  "symbols",
  "qsymbols",
  "var_ref",
  "assoc_list",
  "brace_block",
  "method_call",
  "lambda",
  "then",
  "if_tail",
  "case_body",
  "for_var",
  "k_class",
  "superclass",
  "term",
  "k_module",
  "f_arglist",
  "singleton",
  "@6",
  "@7",
  "@8",
  "@9",
  "@10",
  "@11",
  "@12",
  "@13",
  "@14",
  "@15",
  "f_marg",
  "f_norm_arg",
  "f_margs",
  "f_marg_list",
  "block_args_tail",
  "f_block_kwarg",
  "f_kwrest",
  "opt_f_block_arg",
  "f_block_arg",
  "opt_block_args_tail",
  "block_param",
  "f_arg",
  "f_block_optarg",
  "f_rest_arg",
  "opt_block_param",
  "block_param_def",
  "opt_bv_decl",
  "bv_decls",
  "bvar",
  "f_bad_arg",
  "f_larglist",
  "lambda_body",
  "@16",
  "@17",
  "f_args",
  "do_block",
  "@18",
  "@19",
  "do_body",
  "@20",
  "operation3",
  "@21",
  "@22",
  "@23",
  "@24",
  "@25",
  "cases",
  "exc_list",
  "exc_var",
  "numeric",
  "string",
  "string1",
  "string_contents",
  "xstring_contents",
  "regexp_contents",
  "word_list",
  "word",
  "string_content",
  "symbol_list",
  "qword_list",
  "qsym_list",
  "string_dvar",
  "@26",
  "@27",
  "args_tail",
  "@28",
  "f_kwarg",
  "opt_args_tail",
  "f_optarg",
  "f_arg_asgn",
  "f_arg_item",
  "f_label",
  "f_kw",
  "f_block_kw",
  "kwrest_mark",
  "f_opt",
  "f_block_opt",
  "restarg_mark",
  "blkarg_mark",
  "assoc" ]
Ractor.make_shareable(Racc_token_to_s_table) if defined?(Ractor)

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

# reduce 1 omitted

def _reduce_2(val, _values, result)
                      result = @builder.compstmt(val[0])

    result
end

def _reduce_3(val, _values, result)
                      result = []

    result
end

def _reduce_4(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_5(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_6(val, _values, result)
                      result = [ val[1] ]

    result
end

# reduce 7 omitted

def _reduce_8(val, _values, result)
                      result = @builder.preexe(val[0], *val[1])

    result
end

def _reduce_9(val, _values, result)
                      result = val

    result
end

def _reduce_10(val, _values, result)
                      rescue_bodies     = val[1]
                      else_t,   else_   = val[2]
                      ensure_t, ensure_ = val[3]

                      if rescue_bodies.empty? && !else_t.nil?
                        diagnostic :warning, :useless_else, nil, else_t
                      end

                      result = @builder.begin_body(val[0],
                                  rescue_bodies,
                                  else_t,   else_,
                                  ensure_t, ensure_)

    result
end

def _reduce_11(val, _values, result)
                      result = @builder.compstmt(val[0])

    result
end

def _reduce_12(val, _values, result)
                      result = []

    result
end

def _reduce_13(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_14(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_15(val, _values, result)
                      result = [ val[1] ]

    result
end

# reduce 16 omitted

def _reduce_17(val, _values, result)
                      diagnostic :error, :begin_in_method, nil, val[0]

    result
end

def _reduce_18(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_19(val, _values, result)
                      result = @builder.alias(val[0], val[1], val[3])

    result
end

def _reduce_20(val, _values, result)
                      result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.gvar(val[2]))

    result
end

def _reduce_21(val, _values, result)
                      result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.back_ref(val[2]))

    result
end

def _reduce_22(val, _values, result)
                      diagnostic :error, :nth_ref_alias, nil, val[2]

    result
end

def _reduce_23(val, _values, result)
                      result = @builder.undef_method(val[0], val[1])

    result
end

def _reduce_24(val, _values, result)
                      result = @builder.condition_mod(val[0], nil,
                                                      val[1], val[2])

    result
end

def _reduce_25(val, _values, result)
                      result = @builder.condition_mod(nil, val[0],
                                                      val[1], val[2])

    result
end

def _reduce_26(val, _values, result)
                      result = @builder.loop_mod(:while, val[0], val[1], val[2])

    result
end

def _reduce_27(val, _values, result)
                      result = @builder.loop_mod(:until, val[0], val[1], val[2])

    result
end

def _reduce_28(val, _values, result)
                      rescue_body = @builder.rescue_body(val[1],
                                        nil, nil, nil,
                                        nil, val[2])

                      result = @builder.begin_body(val[0], [ rescue_body ])

    result
end

def _reduce_29(val, _values, result)
                      result = @builder.postexe(val[0], val[1], val[2], val[3])

    result
end

# reduce 30 omitted

def _reduce_31(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1], val[2])

    result
end

def _reduce_32(val, _values, result)
                      result = @builder.assign(val[0], val[1],
                                  @builder.array(nil, val[2], nil))

    result
end

def _reduce_33(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1], val[2])

    result
end

# reduce 34 omitted

def _reduce_35(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

def _reduce_36(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_37(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])

    result
end

def _reduce_38(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_39(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_40(val, _values, result)
                      const  = @builder.const_op_assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))
                      result = @builder.op_assign(const, val[3], val[4])

    result
end

def _reduce_41(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_42(val, _values, result)
                      @builder.op_assign(val[0], val[1], val[2])

    result
end

# reduce 43 omitted

def _reduce_44(val, _values, result)
                      rescue_body = @builder.rescue_body(val[1],
                                        nil, nil, nil,
                                        nil, val[2])

                      result = @builder.begin_body(val[0], [ rescue_body ])

    result
end

# reduce 45 omitted

# reduce 46 omitted

def _reduce_47(val, _values, result)
                      result = @builder.logical_op(:and, val[0], val[1], val[2])

    result
end

def _reduce_48(val, _values, result)
                      result = @builder.logical_op(:or, val[0], val[1], val[2])

    result
end

def _reduce_49(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[2], nil)

    result
end

def _reduce_50(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[1], nil)

    result
end

# reduce 51 omitted

# reduce 52 omitted

def _reduce_53(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_54(val, _values, result)
                      @lexer.cond.pop
                      result = [ val[1], val[2] ]

    result
end

# reduce 55 omitted

# reduce 56 omitted

# reduce 57 omitted

def _reduce_58(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
end

def _reduce_59(val, _values, result)
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_60(val, _values, result)
                      result = [ val[0], *val[2], val[3] ]
                      @context.in_block = val[1].in_block

    result
end

# reduce 61 omitted

def _reduce_62(val, _values, result)
                      result = @builder.call_method(nil, nil, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_63(val, _values, result)
                      method_call = @builder.call_method(nil, nil, val[0],
                                        nil, val[1], nil)

                      begin_t, args, body, end_t = val[2]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_64(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
end

def _reduce_65(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                        nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_66(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
end

def _reduce_67(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                        nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_68(val, _values, result)
                      result = @builder.keyword_cmd(:super, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_69(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_70(val, _values, result)
                      result = @builder.keyword_cmd(:return, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_71(val, _values, result)
                      result = @builder.keyword_cmd(:break, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_72(val, _values, result)
                      result = @builder.keyword_cmd(:next, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_73(val, _values, result)
                      result = @builder.multi_lhs(nil, val[0], nil)

    result
end

def _reduce_74(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_75(val, _values, result)
                      result = @builder.multi_lhs(nil, val[0], nil)

    result
end

def _reduce_76(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

# reduce 77 omitted

def _reduce_78(val, _values, result)
                      result = val[0].
                                  push(val[1])

    result
end

def _reduce_79(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1], val[2]))

    result
end

def _reduce_80(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1], val[2])).
                                  concat(val[4])

    result
end

def _reduce_81(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1]))

    result
end

def _reduce_82(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1])).
                                  concat(val[3])

    result
end

def _reduce_83(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

def _reduce_84(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]),
                                 *val[3] ]

    result
end

def _reduce_85(val, _values, result)
                      result = [ @builder.splat(val[0]) ]

    result
end

def _reduce_86(val, _values, result)
                      result = [ @builder.splat(val[0]),
                                 *val[2] ]

    result
end

# reduce 87 omitted

def _reduce_88(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_89(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_90(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_91(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_92(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_93(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_94(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_95(val, _values, result)
                      result = @builder.index_asgn(val[0], val[1], val[2], val[3])

    result
end

def _reduce_96(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_97(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_98(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_99(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))

    result
end

def _reduce_100(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))

    result
end

def _reduce_101(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_102(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_103(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_104(val, _values, result)
                      result = @builder.index_asgn(val[0], val[1], val[2], val[3])

    result
end

def _reduce_105(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_106(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_107(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_108(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))

    result
end

def _reduce_109(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))

    result
end

def _reduce_110(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_111(val, _values, result)
                      diagnostic :error, :module_name_const, nil, val[0]

    result
end

# reduce 112 omitted

def _reduce_113(val, _values, result)
                      result = @builder.const_global(val[0], val[1])

    result
end

def _reduce_114(val, _values, result)
                      result = @builder.const(val[0])

    result
end

def _reduce_115(val, _values, result)
                      result = @builder.const_fetch(val[0], val[1], val[2])

    result
end

# reduce 116 omitted

# reduce 117 omitted

# reduce 118 omitted

# reduce 119 omitted

# reduce 120 omitted

def _reduce_121(val, _values, result)
                      result = @builder.symbol_internal(val[0])

    result
end

# reduce 122 omitted

# reduce 123 omitted

# reduce 124 omitted

def _reduce_125(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_126(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_127(val, _values, result)
                      result = val[0] << val[3]

    result
end

# reduce 128 omitted

# reduce 129 omitted

# reduce 130 omitted

# reduce 131 omitted

# reduce 132 omitted

# reduce 133 omitted

# reduce 134 omitted

# reduce 135 omitted

# reduce 136 omitted

# reduce 137 omitted

# reduce 138 omitted

# reduce 139 omitted

# reduce 140 omitted

# reduce 141 omitted

# reduce 142 omitted

# reduce 143 omitted

# reduce 144 omitted

# reduce 145 omitted

# reduce 146 omitted

# reduce 147 omitted

# reduce 148 omitted

# reduce 149 omitted

# reduce 150 omitted

# reduce 151 omitted

# reduce 152 omitted

# reduce 153 omitted

# reduce 154 omitted

# reduce 155 omitted

# reduce 156 omitted

# reduce 157 omitted

# reduce 158 omitted

# reduce 159 omitted

# reduce 160 omitted

# reduce 161 omitted

# reduce 162 omitted

# reduce 163 omitted

# reduce 164 omitted

# reduce 165 omitted

# reduce 166 omitted

# reduce 167 omitted

# reduce 168 omitted

# reduce 169 omitted

# reduce 170 omitted

# reduce 171 omitted

# reduce 172 omitted

# reduce 173 omitted

# reduce 174 omitted

# reduce 175 omitted

# reduce 176 omitted

# reduce 177 omitted

# reduce 178 omitted

# reduce 179 omitted

# reduce 180 omitted

# reduce 181 omitted

# reduce 182 omitted

# reduce 183 omitted

# reduce 184 omitted

# reduce 185 omitted

# reduce 186 omitted

# reduce 187 omitted

# reduce 188 omitted

# reduce 189 omitted

# reduce 190 omitted

# reduce 191 omitted

# reduce 192 omitted

# reduce 193 omitted

# reduce 194 omitted

# reduce 195 omitted

# reduce 196 omitted

# reduce 197 omitted

# reduce 198 omitted

def _reduce_199(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

def _reduce_200(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_201(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])

    result
end

def _reduce_202(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_203(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_204(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_205(val, _values, result)
                      const  = @builder.const_op_assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))
                      result = @builder.op_assign(const, val[3], val[4])

    result
end

def _reduce_206(val, _values, result)
                      const  = @builder.const_op_assignable(
                                  @builder.const_global(val[0], val[1]))
                      result = @builder.op_assign(const, val[2], val[3])

    result
end

def _reduce_207(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_208(val, _values, result)
                      result = @builder.range_inclusive(val[0], val[1], val[2])

    result
end

def _reduce_209(val, _values, result)
                      result = @builder.range_exclusive(val[0], val[1], val[2])

    result
end

def _reduce_210(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_211(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_212(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_213(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_214(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_215(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_216(val, _values, result)
                      result = @builder.unary_op(val[0],
                                  @builder.binary_op(
                                    val[1], val[2], val[3]))

    result
end

def _reduce_217(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_218(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_219(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_220(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_221(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_222(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

# reduce 223 omitted

def _reduce_224(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_225(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_226(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_227(val, _values, result)
                      result = @builder.match_op(val[0], val[1], val[2])

    result
end

def _reduce_228(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_229(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[1], nil)

    result
end

def _reduce_230(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_231(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_232(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_233(val, _values, result)
                      result = @builder.logical_op(:and, val[0], val[1], val[2])

    result
end

def _reduce_234(val, _values, result)
                      result = @builder.logical_op(:or, val[0], val[1], val[2])

    result
end

def _reduce_235(val, _values, result)
                      result = @builder.keyword_cmd(:defined?, val[0], nil, [ val[2] ], nil)

    result
end

def _reduce_236(val, _values, result)
                      result = @builder.ternary(val[0], val[1],
                                                val[2], val[4], val[5])

    result
end

# reduce 237 omitted

# reduce 238 omitted

# reduce 239 omitted

# reduce 240 omitted

# reduce 241 omitted

def _reduce_242(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_243(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

# reduce 244 omitted

# reduce 245 omitted

# reduce 246 omitted

def _reduce_247(val, _values, result)
                      result = val[0] << @builder.associate(nil, val[2], nil)

    result
end

def _reduce_248(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]

    result
end

# reduce 249 omitted

def _reduce_250(val, _values, result)
                      rescue_body = @builder.rescue_body(val[1],
                                        nil, nil, nil,
                                        nil, val[2])

                      result = @builder.begin_body(val[0], [ rescue_body ])

    result
end

def _reduce_251(val, _values, result)
                      result = val

    result
end

def _reduce_252(val, _values, result)
                      result = [ nil, [], nil ]

    result
end

# reduce 253 omitted

def _reduce_254(val, _values, result)
                      result = []

    result
end

# reduce 255 omitted

# reduce 256 omitted

def _reduce_257(val, _values, result)
                      result = val[0] << @builder.associate(nil, val[2], nil)

    result
end

def _reduce_258(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]

    result
end

def _reduce_259(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_260(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_261(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]
                      result.concat(val[1])

    result
end

def _reduce_262(val, _values, result)
                      assocs = @builder.associate(nil, val[2], nil)
                      result = val[0] << assocs
                      result.concat(val[3])

    result
end

def _reduce_263(val, _values, result)
                      result =  [ val[0] ]

    result
end

def _reduce_264(val, _values, result)
                      # When branch gets invoked by RACC's lookahead
                      # and command args start with '[' or '('
                      # we need to put `true` to the cmdarg stack
                      # **before** `false` pushed by lexer
                      #   m [], n
                      #     ^
                      # Right here we have cmdarg [...0] because
                      # lexer pushed it on '['
                      # We need to modify cmdarg stack to [...10]
                      #
                      # For all other cases (like `m n` or `m n, []`) we simply put 1 to the stack
                      # and later lexer pushes corresponding bits on top of it.
                      last_token = @last_token[0]
                      lookahead = last_token == :tLBRACK || last_token == :tLPAREN_ARG

                      if lookahead
                        top = @lexer.cmdarg.pop
                        @lexer.cmdarg.push(true)
                        @lexer.cmdarg.push(top)
                      else
                        @lexer.cmdarg.push(true)
                      end

    result
end

def _reduce_265(val, _values, result)
                      # call_args can be followed by tLBRACE_ARG (that does cmdarg.push(0) in the lexer)
                      # but the push must be done after cmdarg.pop() in the parser.
                      # So this code does cmdarg.pop() to pop 0 pushed by tLBRACE_ARG,
                      # cmdarg.pop() to pop 1 pushed by command_args,
                      # and cmdarg.push(0) to restore back the flag set by tLBRACE_ARG.
                      last_token = @last_token[0]
                      lookahead = last_token == :tLBRACE_ARG
                      if lookahead
                        top = @lexer.cmdarg.pop
                        @lexer.cmdarg.pop
                        @lexer.cmdarg.push(top)
                      else
                        @lexer.cmdarg.pop
                      end

                      result = val[1]

    result
end

def _reduce_266(val, _values, result)
                      result = @builder.block_pass(val[0], val[1])

    result
end

def _reduce_267(val, _values, result)
                      result = [ val[1] ]

    result
end

def _reduce_268(val, _values, result)
                      result = []

    result
end

def _reduce_269(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_270(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

def _reduce_271(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_272(val, _values, result)
                      result = val[0] << @builder.splat(val[2], val[3])

    result
end

def _reduce_273(val, _values, result)
                      result = @builder.array(nil, val[0], nil)

    result
end

# reduce 274 omitted

def _reduce_275(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_276(val, _values, result)
                      result = val[0] << @builder.splat(val[2], val[3])

    result
end

def _reduce_277(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

# reduce 278 omitted

# reduce 279 omitted

# reduce 280 omitted

# reduce 281 omitted

# reduce 282 omitted

# reduce 283 omitted

# reduce 284 omitted

# reduce 285 omitted

# reduce 286 omitted

# reduce 287 omitted

def _reduce_288(val, _values, result)
                      result = @builder.call_method(nil, nil, val[0])

    result
end

def _reduce_289(val, _values, result)
                      @lexer.cmdarg.push(false)

    result
end

def _reduce_290(val, _values, result)
                      @lexer.cmdarg.pop

                      result = @builder.begin_keyword(val[0], val[2], val[3])

    result
end

def _reduce_291(val, _values, result)
                      @lexer.state = :expr_endarg

    result
end

def _reduce_292(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[3])

    result
end

def _reduce_293(val, _values, result)
                      @lexer.state = :expr_endarg

    result
end

def _reduce_294(val, _values, result)
                      result = @builder.begin(val[0], nil, val[3])

    result
end

def _reduce_295(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_296(val, _values, result)
                      result = @builder.const_fetch(val[0], val[1], val[2])

    result
end

def _reduce_297(val, _values, result)
                      result = @builder.const_global(val[0], val[1])

    result
end

def _reduce_298(val, _values, result)
                      result = @builder.array(val[0], val[1], val[2])

    result
end

def _reduce_299(val, _values, result)
                      result = @builder.associate(val[0], val[1], val[2])

    result
end

def _reduce_300(val, _values, result)
                      result = @builder.keyword_cmd(:return, val[0])

    result
end

def _reduce_301(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0], val[1], val[2], val[3])

    result
end

def _reduce_302(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0], val[1], [], val[2])

    result
end

def _reduce_303(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0])

    result
end

def _reduce_304(val, _values, result)
                      result = @builder.keyword_cmd(:defined?, val[0],
                                                    val[2], [ val[3] ], val[4])

    result
end

def _reduce_305(val, _values, result)
                      result = @builder.not_op(val[0], val[1], val[2], val[3])

    result
end

def _reduce_306(val, _values, result)
                      result = @builder.not_op(val[0], val[1], nil, val[2])

    result
end

def _reduce_307(val, _values, result)
                      method_call = @builder.call_method(nil, nil, val[0])

                      begin_t, args, body, end_t = val[1]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

# reduce 308 omitted

def _reduce_309(val, _values, result)
                      begin_t, args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, args, body, end_t)

    result
end

def _reduce_310(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_311(val, _values, result)
                      lambda_call = @builder.call_lambda(val[0])

                      args, (begin_t, body, end_t) = val[2]
                      result      = @builder.block(lambda_call,
                                      begin_t, args, body, end_t)

                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_312(val, _values, result)
                      else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  val[3], else_t,
                                                  else_,  val[5])

    result
end

def _reduce_313(val, _values, result)
                      else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  else_,  else_t,
                                                  val[3], val[5])

    result
end

def _reduce_314(val, _values, result)
                      result = @builder.loop(:while, val[0], *val[1], val[2], val[3])

    result
end

def _reduce_315(val, _values, result)
                      result = @builder.loop(:until, val[0], *val[1], val[2], val[3])

    result
end

def _reduce_316(val, _values, result)
                      *when_bodies, (else_t, else_body) = *val[3]

                      result = @builder.case(val[0], val[1],
                                             when_bodies, else_t, else_body,
                                             val[4])

    result
end

def _reduce_317(val, _values, result)
                      *when_bodies, (else_t, else_body) = *val[2]

                      result = @builder.case(val[0], nil,
                                             when_bodies, else_t, else_body,
                                             val[3])

    result
end

def _reduce_318(val, _values, result)
                      result = @builder.for(val[0], val[1], val[2], *val[3], val[4], val[5])

    result
end

def _reduce_319(val, _values, result)
                      local_push
                      @context.in_class = true

    result
end

def _reduce_320(val, _values, result)
                      k_class, ctx = val[0]
                      if @context.in_def
                        diagnostic :error, :class_in_def, nil, k_class
                      end

                      lt_t, superclass = val[2]
                      result = @builder.def_class(k_class, val[1],
                                                  lt_t, superclass,
                                                  val[4], val[5])

                      local_pop
                      @context.in_class = ctx.in_class

    result
end

def _reduce_321(val, _values, result)
                      @context.in_def = false
                      @context.in_class = false
                      local_push

    result
end

def _reduce_322(val, _values, result)
                      k_class, ctx = val[0]
                      result = @builder.def_sclass(k_class, val[1], val[2],
                                                   val[5], val[6])

                      local_pop
                      @context.in_def = ctx.in_def
                      @context.in_class = ctx.in_class

    result
end

def _reduce_323(val, _values, result)
                      @context.in_class = true
                      local_push

    result
end

def _reduce_324(val, _values, result)
                      k_mod, ctx = val[0]
                      if @context.in_def
                        diagnostic :error, :module_in_def, nil, k_mod
                      end

                      result = @builder.def_module(k_mod, val[1],
                                                   val[3], val[4])

                      local_pop
                      @context.in_class = ctx.in_class

    result
end

def _reduce_325(val, _values, result)
                      local_push
                      result = context.dup
                      @context.in_def = true

    result
end

def _reduce_326(val, _values, result)
                      result = @builder.def_method(val[0], val[1],
                                  val[3], val[4], val[5])

                      local_pop
                      @context.in_def = val[2].in_def

    result
end

def _reduce_327(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_328(val, _values, result)
                      local_push
                      result = context.dup
                      @context.in_def = true

    result
end

def _reduce_329(val, _values, result)
                      result = @builder.def_singleton(val[0], val[1], val[2],
                                  val[4], val[6], val[7], val[8])

                      local_pop
                      @context.in_def = val[5].in_def

    result
end

def _reduce_330(val, _values, result)
                      result = @builder.keyword_cmd(:break, val[0])

    result
end

def _reduce_331(val, _values, result)
                      result = @builder.keyword_cmd(:next, val[0])

    result
end

def _reduce_332(val, _values, result)
                      result = @builder.keyword_cmd(:redo, val[0])

    result
end

def _reduce_333(val, _values, result)
                      result = @builder.keyword_cmd(:retry, val[0])

    result
end

# reduce 334 omitted

def _reduce_335(val, _values, result)
                      result = [ val[0], @context.dup ]

    result
end

def _reduce_336(val, _values, result)
                      result = [ val[0], @context.dup ]

    result
end

def _reduce_337(val, _values, result)
                      if @context.in_class && !@context.in_def && !(context.in_block || context.in_lambda)
                        diagnostic :error, :invalid_return, nil, val[0]
                      end

    result
end

# reduce 338 omitted

# reduce 339 omitted

def _reduce_340(val, _values, result)
                      result = val[1]

    result
end

# reduce 341 omitted

# reduce 342 omitted

# reduce 343 omitted

def _reduce_344(val, _values, result)
                      else_t, else_ = val[4]
                      result = [ val[0],
                                 @builder.condition(val[0], val[1], val[2],
                                                    val[3], else_t,
                                                    else_,  nil),
                               ]

    result
end

# reduce 345 omitted

def _reduce_346(val, _values, result)
                      result = val

    result
end

# reduce 347 omitted

# reduce 348 omitted

def _reduce_349(val, _values, result)
                      result = @builder.arg(val[0])

    result
end

def _reduce_350(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

def _reduce_351(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_352(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 353 omitted

def _reduce_354(val, _values, result)
                      result = val[0].
                                  push(@builder.restarg(val[2], val[3]))

    result
end

def _reduce_355(val, _values, result)
                      result = val[0].
                                  push(@builder.restarg(val[2], val[3])).
                                  concat(val[5])

    result
end

def _reduce_356(val, _values, result)
                      result = val[0].
                                  push(@builder.restarg(val[2]))

    result
end

def _reduce_357(val, _values, result)
                      result = val[0].
                                  push(@builder.restarg(val[2])).
                                  concat(val[4])

    result
end

def _reduce_358(val, _values, result)
                      result = [ @builder.restarg(val[0], val[1]) ]

    result
end

def _reduce_359(val, _values, result)
                      result = [ @builder.restarg(val[0], val[1]),
                                 *val[3] ]

    result
end

def _reduce_360(val, _values, result)
                      result = [ @builder.restarg(val[0]) ]

    result
end

def _reduce_361(val, _values, result)
                      result = [ @builder.restarg(val[0]),
                                 *val[2] ]

    result
end

def _reduce_362(val, _values, result)
                      result = val[0].concat(val[2]).concat(val[3])

    result
end

def _reduce_363(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_364(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_365(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_366(val, _values, result)
                      result = val[1]

    result
end

def _reduce_367(val, _values, result)
                      result = []

    result
end

def _reduce_368(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_369(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[6]).
                                  concat(val[7])

    result
end

def _reduce_370(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_371(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_372(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

# reduce 373 omitted

def _reduce_374(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_375(val, _values, result)
                      if val[1].empty? && val[0].size == 1
                        result = [@builder.procarg0(val[0][0])]
                      else
                        result = val[0].concat(val[1])
                      end

    result
end

def _reduce_376(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_377(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_378(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_379(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_380(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_381(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

# reduce 382 omitted

def _reduce_383(val, _values, result)
                      result = @builder.args(nil, [], nil)

    result
end

def _reduce_384(val, _values, result)
                      @lexer.state = :expr_value

    result
end

def _reduce_385(val, _values, result)
                      result = @builder.args(val[0], val[1], val[2])

    result
end

def _reduce_386(val, _values, result)
                      result = @builder.args(val[0], [], val[0])

    result
end

def _reduce_387(val, _values, result)
                      result = @builder.args(val[0], val[1].concat(val[2]), val[3])

    result
end

def _reduce_388(val, _values, result)
                      result = []

    result
end

def _reduce_389(val, _values, result)
                      result = val[2]

    result
end

def _reduce_390(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_391(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_392(val, _values, result)
                      @static_env.declare val[0][0]
                      result = @builder.shadowarg(val[0])

    result
end

# reduce 393 omitted

def _reduce_394(val, _values, result)
                      @static_env.extend_dynamic

    result
end

def _reduce_395(val, _values, result)
                      @lexer.cmdarg.push(false)

    result
end

def _reduce_396(val, _values, result)
                      @lexer.cmdarg.pop

                      result = [ val[1], val[3] ]

                      @static_env.unextend

    result
end

def _reduce_397(val, _values, result)
                      result = @builder.args(val[0], val[1].concat(val[2]), val[3])

    result
end

def _reduce_398(val, _values, result)
                      result = @builder.args(nil, val[0], nil)

    result
end

def _reduce_399(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_400(val, _values, result)
                      result = [ val[0], val[2], val[3] ]
                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_401(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_402(val, _values, result)
                      result = [ val[0], val[2], val[3] ]
                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_403(val, _values, result)
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_404(val, _values, result)
                      result = [ val[0], *val[2], val[3] ]
                      @context.in_block = val[1].in_block

    result
end

def _reduce_405(val, _values, result)
                      begin_t, block_args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, block_args, body, end_t)

    result
end

def _reduce_406(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_407(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                      lparen_t, args, rparen_t)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_408(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                      nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_409(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.call_method(nil, nil, val[0],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_410(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_411(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_412(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2])

    result
end

def _reduce_413(val, _values, result)
                      lparen_t, args, rparen_t = val[2]
                      result = @builder.call_method(val[0], val[1], nil,
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_414(val, _values, result)
                      lparen_t, args, rparen_t = val[2]
                      result = @builder.call_method(val[0], val[1], nil,
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_415(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.keyword_cmd(:super, val[0],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_416(val, _values, result)
                      result = @builder.keyword_cmd(:zsuper, val[0])

    result
end

def _reduce_417(val, _values, result)
                      result = @builder.index(val[0], val[1], val[2], val[3])

    result
end

def _reduce_418(val, _values, result)
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_419(val, _values, result)
                      result = [ val[0], *val[2], val[3] ]
                      @context.in_block = val[1].in_block

    result
end

def _reduce_420(val, _values, result)
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_421(val, _values, result)
                      result = [ val[0], *val[2], val[3] ]
                      @context.in_block = val[1].in_block

    result
end

def _reduce_422(val, _values, result)
                      @static_env.extend_dynamic

    result
end

def _reduce_423(val, _values, result)
                      result = [ val[1], val[2] ]

                      @static_env.unextend

    result
end

def _reduce_424(val, _values, result)
                      @static_env.extend_dynamic

    result
end

def _reduce_425(val, _values, result)
                      @lexer.cmdarg.push(false)

    result
end

def _reduce_426(val, _values, result)
                      result = [ val[2], val[3] ]

                      @static_env.unextend
                      @lexer.cmdarg.pop

    result
end

def _reduce_427(val, _values, result)
                      result = [ @builder.when(val[0], val[1], val[2], val[3]),
                                 *val[4] ]

    result
end

def _reduce_428(val, _values, result)
                      result = [ val[0] ]

    result
end

# reduce 429 omitted

def _reduce_430(val, _values, result)
                      assoc_t, exc_var = val[2]

                      if val[1]
                        exc_list = @builder.array(nil, val[1], nil)
                      end

                      result = [ @builder.rescue_body(val[0],
                                      exc_list, assoc_t, exc_var,
                                      val[3], val[4]),
                                 *val[5] ]

    result
end

def _reduce_431(val, _values, result)
                      result = []

    result
end

def _reduce_432(val, _values, result)
                      result = [ val[0] ]

    result
end

# reduce 433 omitted

# reduce 434 omitted

def _reduce_435(val, _values, result)
                      result = [ val[0], val[1] ]

    result
end

# reduce 436 omitted

def _reduce_437(val, _values, result)
                      result = [ val[0], val[1] ]

    result
end

# reduce 438 omitted

# reduce 439 omitted

# reduce 440 omitted

# reduce 441 omitted

def _reduce_442(val, _values, result)
                      result = @builder.string_compose(nil, val[0], nil)

    result
end

def _reduce_443(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_444(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_445(val, _values, result)
                      string = @builder.string_compose(val[0], val[1], val[2])
                      result = @builder.dedent_string(string, @lexer.dedent_level)

    result
end

def _reduce_446(val, _values, result)
                      string = @builder.string(val[0])
                      result = @builder.dedent_string(string, @lexer.dedent_level)

    result
end

def _reduce_447(val, _values, result)
                      result = @builder.character(val[0])

    result
end

def _reduce_448(val, _values, result)
                      string = @builder.xstring_compose(val[0], val[1], val[2])
                      result = @builder.dedent_string(string, @lexer.dedent_level)

    result
end

def _reduce_449(val, _values, result)
                      opts   = @builder.regexp_options(val[3])
                      result = @builder.regexp_compose(val[0], val[1], val[2], opts)

    result
end

def _reduce_450(val, _values, result)
                      result = @builder.words_compose(val[0], val[1], val[2])

    result
end

def _reduce_451(val, _values, result)
                      result = []

    result
end

def _reduce_452(val, _values, result)
                      result = val[0] << @builder.word(val[1])

    result
end

def _reduce_453(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_454(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_455(val, _values, result)
                      result = @builder.symbols_compose(val[0], val[1], val[2])

    result
end

def _reduce_456(val, _values, result)
                      result = []

    result
end

def _reduce_457(val, _values, result)
                      result = val[0] << @builder.word(val[1])

    result
end

def _reduce_458(val, _values, result)
                      result = @builder.words_compose(val[0], val[1], val[2])

    result
end

def _reduce_459(val, _values, result)
                      result = @builder.symbols_compose(val[0], val[1], val[2])

    result
end

def _reduce_460(val, _values, result)
                      result = []

    result
end

def _reduce_461(val, _values, result)
                      result = val[0] << @builder.string_internal(val[1])

    result
end

def _reduce_462(val, _values, result)
                      result = []

    result
end

def _reduce_463(val, _values, result)
                      result = val[0] << @builder.symbol_internal(val[1])

    result
end

def _reduce_464(val, _values, result)
                      result = []

    result
end

def _reduce_465(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_466(val, _values, result)
                      result = []

    result
end

def _reduce_467(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_468(val, _values, result)
                      result = []

    result
end

def _reduce_469(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_470(val, _values, result)
                      result = @builder.string_internal(val[0])

    result
end

def _reduce_471(val, _values, result)
                      result = val[1]

    result
end

def _reduce_472(val, _values, result)
                      @lexer.cmdarg.push(false)
                      @lexer.cond.push(false)

    result
end

def _reduce_473(val, _values, result)
                      @lexer.cmdarg.pop
                      @lexer.cond.pop

                      result = @builder.begin(val[0], val[2], val[3])

    result
end

def _reduce_474(val, _values, result)
                      result = @builder.gvar(val[0])

    result
end

def _reduce_475(val, _values, result)
                      result = @builder.ivar(val[0])

    result
end

def _reduce_476(val, _values, result)
                      result = @builder.cvar(val[0])

    result
end

# reduce 477 omitted

def _reduce_478(val, _values, result)
                      @lexer.state = :expr_end
                      result = @builder.symbol(val[0])

    result
end

def _reduce_479(val, _values, result)
                      @lexer.state = :expr_end
                      result = @builder.symbol_compose(val[0], val[1], val[2])

    result
end

def _reduce_480(val, _values, result)
                      result = val[0]

    result
end

def _reduce_481(val, _values, result)
                      if @builder.respond_to? :negate
                        # AST builder interface compatibility
                        result = @builder.negate(val[0], val[1])
                      else
                        result = @builder.unary_num(val[0], val[1])
                      end

    result
end

def _reduce_482(val, _values, result)
                      @lexer.state = :expr_end
                      result = @builder.integer(val[0])

    result
end

def _reduce_483(val, _values, result)
                      @lexer.state = :expr_end
                      result = @builder.float(val[0])

    result
end

def _reduce_484(val, _values, result)
                      @lexer.state = :expr_end
                      result = @builder.rational(val[0])

    result
end

def _reduce_485(val, _values, result)
                      @lexer.state = :expr_end
                      result = @builder.complex(val[0])

    result
end

def _reduce_486(val, _values, result)
                      result = @builder.ident(val[0])

    result
end

def _reduce_487(val, _values, result)
                      result = @builder.ivar(val[0])

    result
end

def _reduce_488(val, _values, result)
                      result = @builder.gvar(val[0])

    result
end

def _reduce_489(val, _values, result)
                      result = @builder.const(val[0])

    result
end

def _reduce_490(val, _values, result)
                      result = @builder.cvar(val[0])

    result
end

def _reduce_491(val, _values, result)
                      result = @builder.nil(val[0])

    result
end

def _reduce_492(val, _values, result)
                      result = @builder.self(val[0])

    result
end

def _reduce_493(val, _values, result)
                      result = @builder.true(val[0])

    result
end

def _reduce_494(val, _values, result)
                      result = @builder.false(val[0])

    result
end

def _reduce_495(val, _values, result)
                      result = @builder.__FILE__(val[0])

    result
end

def _reduce_496(val, _values, result)
                      result = @builder.__LINE__(val[0])

    result
end

def _reduce_497(val, _values, result)
                      result = @builder.__ENCODING__(val[0])

    result
end

def _reduce_498(val, _values, result)
                      result = @builder.accessible(val[0])

    result
end

def _reduce_499(val, _values, result)
                      result = @builder.accessible(val[0])

    result
end

def _reduce_500(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_501(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_502(val, _values, result)
                      result = @builder.nth_ref(val[0])

    result
end

def _reduce_503(val, _values, result)
                      result = @builder.back_ref(val[0])

    result
end

def _reduce_504(val, _values, result)
                      @lexer.state = :expr_value

    result
end

def _reduce_505(val, _values, result)
                      result = [ val[0], val[2] ]

    result
end

def _reduce_506(val, _values, result)
                      result = nil

    result
end

def _reduce_507(val, _values, result)
                      result = @builder.args(val[0], val[1], val[2])

                      @lexer.state = :expr_value

    result
end

def _reduce_508(val, _values, result)
                      result = @context.in_kwarg
                      @context.in_kwarg = true

    result
end

def _reduce_509(val, _values, result)
                      @context.in_kwarg = val[0]
                      result = @builder.args(nil, val[1], nil)

    result
end

def _reduce_510(val, _values, result)
                      result = val[0].concat(val[2]).concat(val[3])

    result
end

def _reduce_511(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_512(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_513(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_514(val, _values, result)
                      result = val[1]

    result
end

def _reduce_515(val, _values, result)
                      result = []

    result
end

def _reduce_516(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_517(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[6]).
                                  concat(val[7])

    result
end

def _reduce_518(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_519(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_520(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_521(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_522(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_523(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_524(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_525(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_526(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_527(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_528(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_529(val, _values, result)
                      result = val[0]

    result
end

def _reduce_530(val, _values, result)
                      result = []

    result
end

def _reduce_531(val, _values, result)
                      diagnostic :error, :argument_const, nil, val[0]

    result
end

def _reduce_532(val, _values, result)
                      diagnostic :error, :argument_ivar, nil, val[0]

    result
end

def _reduce_533(val, _values, result)
                      diagnostic :error, :argument_gvar, nil, val[0]

    result
end

def _reduce_534(val, _values, result)
                      diagnostic :error, :argument_cvar, nil, val[0]

    result
end

# reduce 535 omitted

def _reduce_536(val, _values, result)
                      @static_env.declare val[0][0]

                      result = val[0]

    result
end

def _reduce_537(val, _values, result)
                      result = val[0]

    result
end

def _reduce_538(val, _values, result)
                      result = @builder.arg(val[0])

    result
end

def _reduce_539(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

def _reduce_540(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_541(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_542(val, _values, result)
                      check_kwarg_name(val[0])

                      @static_env.declare val[0][0]

                      result = val[0]

    result
end

def _reduce_543(val, _values, result)
                      result = @builder.kwoptarg(val[0], val[1])

    result
end

def _reduce_544(val, _values, result)
                      result = @builder.kwarg(val[0])

    result
end

def _reduce_545(val, _values, result)
                      result = @builder.kwoptarg(val[0], val[1])

    result
end

def _reduce_546(val, _values, result)
                      result = @builder.kwarg(val[0])

    result
end

def _reduce_547(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_548(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_549(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_550(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 551 omitted

# reduce 552 omitted

def _reduce_553(val, _values, result)
                      @static_env.declare val[1][0]

                      result = [ @builder.kwrestarg(val[0], val[1]) ]

    result
end

def _reduce_554(val, _values, result)
                      result = [ @builder.kwrestarg(val[0]) ]

    result
end

def _reduce_555(val, _values, result)
                      result = @builder.optarg(val[0], val[1], val[2])

    result
end

def _reduce_556(val, _values, result)
                      result = @builder.optarg(val[0], val[1], val[2])

    result
end

def _reduce_557(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_558(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_559(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_560(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 561 omitted

# reduce 562 omitted

def _reduce_563(val, _values, result)
                      @static_env.declare val[1][0]

                      result = [ @builder.restarg(val[0], val[1]) ]

    result
end

def _reduce_564(val, _values, result)
                      result = [ @builder.restarg(val[0]) ]

    result
end

# reduce 565 omitted

# reduce 566 omitted

def _reduce_567(val, _values, result)
                      @static_env.declare val[1][0]

                      result = @builder.blockarg(val[0], val[1])

    result
end

def _reduce_568(val, _values, result)
                      result = [ val[1] ]

    result
end

def _reduce_569(val, _values, result)
                      result = []

    result
end

# reduce 570 omitted

def _reduce_571(val, _values, result)
                      result = val[1]

    result
end

def _reduce_572(val, _values, result)
                      result = []

    result
end

# reduce 573 omitted

def _reduce_574(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_575(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_576(val, _values, result)
                      result = @builder.pair(val[0], val[1], val[2])

    result
end

def _reduce_577(val, _values, result)
                      result = @builder.pair_keyword(val[0], val[1])

    result
end

def _reduce_578(val, _values, result)
                      result = @builder.pair_quoted(val[0], val[1], val[2], val[3])

    result
end

def _reduce_579(val, _values, result)
                      result = @builder.kwsplat(val[0], val[1])

    result
end

# reduce 580 omitted

# reduce 581 omitted

# reduce 582 omitted

# reduce 583 omitted

# reduce 584 omitted

# reduce 585 omitted

# reduce 586 omitted

# reduce 587 omitted

# reduce 588 omitted

# reduce 589 omitted

# reduce 590 omitted

# reduce 591 omitted

def _reduce_592(val, _values, result)
                      result = [:dot, val[0][1]]

    result
end

def _reduce_593(val, _values, result)
                      result = [:anddot, val[0][1]]

    result
end

# reduce 594 omitted

# reduce 595 omitted

# reduce 596 omitted

# reduce 597 omitted

def _reduce_598(val, _values, result)
                      result = val[1]

    result
end

def _reduce_599(val, _values, result)
                      result = val[1]

    result
end

# reduce 600 omitted

# reduce 601 omitted

# reduce 602 omitted

def _reduce_603(val, _values, result)
                    yyerrok

    result
end

# reduce 604 omitted

# reduce 605 omitted

# reduce 606 omitted

def _reduce_607(val, _values, result)
                    result = nil

    result
end

def _reduce_none(val, _values, result)
  val[0]
end

  end   # class Ruby25
end   # module Parser
