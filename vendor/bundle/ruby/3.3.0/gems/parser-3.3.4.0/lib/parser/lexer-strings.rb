# -*- encoding:utf-8; warn-indent:false; frozen_string_literal: true  -*-

# line 1 "lib/parser/lexer-strings.rl"

# line 3 "lib/parser/lexer-strings.rl"
class Parser::LexerStrings

  
# line 9 "lib/parser/lexer-strings.rb"
class << self
	attr_accessor :_lex_actions
	private :_lex_actions, :_lex_actions=
end
self._lex_actions = [
	0, 1, 0, 1, 18, 1, 22, 1, 
	23, 1, 24, 1, 25, 1, 26, 1, 
	27, 1, 28, 1, 32, 1, 33, 1, 
	34, 1, 35, 1, 36, 1, 37, 1, 
	38, 1, 42, 1, 43, 1, 44, 1, 
	46, 1, 47, 1, 48, 1, 49, 1, 
	52, 1, 53, 1, 54, 1, 55, 1, 
	56, 1, 57, 1, 60, 1, 61, 1, 
	62, 1, 63, 1, 64, 1, 65, 1, 
	66, 1, 69, 1, 70, 1, 71, 1, 
	72, 1, 73, 1, 74, 1, 75, 1, 
	76, 1, 77, 1, 79, 1, 80, 1, 
	81, 2, 0, 27, 2, 0, 37, 2, 
	0, 46, 2, 0, 52, 2, 0, 56, 
	2, 0, 62, 2, 0, 65, 2, 0, 
	72, 2, 0, 77, 2, 1, 31, 2, 
	1, 41, 2, 1, 78, 2, 2, 31, 
	2, 2, 41, 2, 2, 78, 2, 3, 
	31, 2, 3, 41, 2, 3, 78, 2, 
	8, 31, 2, 8, 41, 2, 8, 78, 
	2, 10, 31, 2, 10, 41, 2, 10, 
	78, 2, 11, 31, 2, 11, 41, 2, 
	11, 78, 2, 12, 31, 2, 12, 41, 
	2, 12, 78, 2, 13, 31, 2, 13, 
	41, 2, 13, 78, 2, 14, 31, 2, 
	14, 41, 2, 14, 78, 2, 15, 31, 
	2, 15, 41, 2, 15, 78, 2, 16, 
	31, 2, 16, 41, 2, 16, 78, 2, 
	17, 31, 2, 17, 41, 2, 17, 78, 
	2, 18, 45, 2, 18, 51, 2, 19, 
	30, 2, 19, 40, 2, 19, 59, 2, 
	19, 68, 2, 20, 29, 2, 20, 30, 
	2, 20, 39, 2, 20, 40, 2, 20, 
	58, 2, 20, 59, 2, 20, 67, 2, 
	20, 68, 2, 21, 29, 2, 21, 30, 
	2, 21, 39, 2, 21, 40, 2, 21, 
	58, 2, 21, 59, 2, 21, 67, 2, 
	21, 68, 2, 22, 78, 2, 25, 0, 
	3, 0, 50, 18, 3, 2, 5, 31, 
	3, 2, 5, 41, 3, 2, 5, 78, 
	3, 2, 6, 31, 3, 2, 6, 41, 
	3, 2, 6, 78, 3, 4, 5, 31, 
	3, 4, 5, 41, 3, 4, 5, 78, 
	3, 4, 6, 31, 3, 4, 6, 41, 
	3, 4, 6, 78, 3, 8, 6, 31, 
	3, 8, 6, 41, 3, 8, 6, 78, 
	3, 9, 5, 31, 3, 9, 5, 41, 
	3, 9, 5, 78, 3, 15, 16, 31, 
	3, 15, 16, 41, 3, 15, 16, 78, 
	3, 18, 17, 31, 3, 18, 17, 41, 
	3, 18, 17, 78, 4, 2, 5, 6, 
	31, 4, 2, 5, 6, 41, 4, 2, 
	5, 6, 78, 4, 4, 5, 6, 31, 
	4, 4, 5, 6, 41, 4, 4, 5, 
	6, 78, 4, 7, 5, 6, 31, 4, 
	7, 5, 6, 41, 4, 7, 5, 6, 
	78, 4, 9, 5, 6, 31, 4, 9, 
	5, 6, 41, 4, 9, 5, 6, 78
]

class << self
	attr_accessor :_lex_trans_keys
	private :_lex_trans_keys, :_lex_trans_keys=
end
self._lex_trans_keys = [
	0, 0, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	45, 0, 120, 0, 120, 
	0, 92, 0, 120, 0, 120, 
	0, 45, 0, 120, 0, 
	120, 67, 99, 45, 45, 
	0, 92, 0, 120, 0, 102, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 45, 
	0, 120, 0, 120, 0, 92, 
	0, 120, 0, 120, 0, 
	45, 0, 120, 0, 120, 
	67, 99, 45, 45, 0, 92, 
	0, 120, 0, 102, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	122, 0, 45, 0, 120, 
	0, 120, 0, 92, 0, 120, 
	0, 120, 0, 45, 0, 
	120, 0, 120, 67, 99, 
	45, 45, 0, 92, 0, 120, 
	0, 102, 0, 26, 0, 
	92, 9, 32, 36, 123, 
	0, 127, 0, 0, 48, 57, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 120, 
	0, 0, 0, 0, 48, 55, 
	48, 55, 0, 0, 0, 
	0, 0, 92, 0, 0, 
	0, 0, 0, 0, 0, 92, 
	45, 45, 0, 0, 0, 
	0, 0, 0, 0, 92, 
	48, 102, 48, 102, 0, 0, 
	48, 102, 48, 102, 0, 
	0, 0, 45, 0, 92, 
	0, 92, 0, 0, 0, 0, 
	0, 92, 48, 102, 48, 
	102, 0, 0, 0, 45, 
	10, 10, 0, 92, 48, 123, 
	48, 102, 48, 102, 48, 
	102, 0, 0, 0, 125, 
	0, 125, 0, 0, 0, 125, 
	0, 0, 0, 125, 0, 
	125, 0, 125, 0, 125, 
	0, 0, 0, 125, 0, 125, 
	0, 125, 0, 125, 0, 
	125, 0, 125, 0, 0, 
	0, 0, 48, 102, 0, 0, 
	0, 92, 36, 123, 0, 
	127, 0, 0, 48, 57, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 120, 0, 
	0, 0, 0, 48, 55, 
	48, 55, 0, 0, 0, 0, 
	0, 92, 0, 0, 0, 
	0, 0, 0, 0, 92, 
	45, 45, 0, 0, 0, 0, 
	0, 0, 0, 92, 48, 
	102, 48, 102, 0, 0, 
	48, 102, 48, 102, 0, 0, 
	0, 45, 0, 92, 0, 
	92, 0, 0, 0, 0, 
	0, 92, 48, 102, 48, 102, 
	0, 0, 0, 45, 10, 
	10, 0, 92, 48, 123, 
	48, 102, 48, 102, 48, 102, 
	0, 0, 0, 125, 0, 
	125, 0, 0, 0, 125, 
	0, 0, 0, 125, 0, 125, 
	0, 125, 0, 125, 0, 
	0, 0, 125, 0, 125, 
	0, 125, 0, 125, 0, 125, 
	0, 125, 0, 0, 0, 
	0, 48, 102, 0, 0, 
	0, 92, 9, 32, 0, 26, 
	0, 92, 0, 26, 0, 
	35, 36, 123, 0, 127, 
	0, 0, 48, 57, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 26, 0, 35, 
	9, 32, 36, 123, 0, 127, 
	0, 0, 48, 57, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 32, 9, 32, 
	65, 122, 65, 122, 63, 
	63, 0, 0, 0, 127, 
	0, 127, 0, 120, 0, 0, 
	0, 0, 48, 55, 48, 
	55, 0, 0, 0, 0, 
	0, 92, 0, 0, 0, 0, 
	0, 0, 0, 92, 45, 
	45, 0, 0, 0, 0, 
	0, 0, 0, 92, 48, 102, 
	48, 102, 0, 0, 48, 
	102, 48, 102, 0, 0, 
	0, 45, 0, 92, 0, 92, 
	0, 0, 0, 0, 0, 
	92, 48, 102, 48, 102, 
	0, 0, 0, 45, 10, 10, 
	0, 92, 48, 123, 48, 
	102, 48, 102, 48, 102, 
	0, 0, 0, 125, 0, 125, 
	0, 0, 0, 125, 0, 
	0, 0, 125, 0, 125, 
	0, 125, 0, 125, 0, 0, 
	0, 125, 0, 125, 0, 
	125, 0, 125, 0, 125, 
	0, 125, 0, 125, 0, 125, 
	0, 125, 0, 125, 0, 
	125, 0, 125, 0, 125, 
	0, 125, 0, 125, 0, 125, 
	0, 125, 0, 125, 0, 
	125, 0, 0, 0, 0, 
	48, 102, 0, 0, 0
]

class << self
	attr_accessor :_lex_key_spans
	private :_lex_key_spans, :_lex_key_spans=
end
self._lex_key_spans = [
	0, 128, 128, 128, 128, 46, 121, 121, 
	93, 121, 121, 46, 121, 121, 33, 1, 
	93, 121, 103, 128, 128, 128, 128, 46, 
	121, 121, 93, 121, 121, 46, 121, 121, 
	33, 1, 93, 121, 103, 128, 128, 128, 
	128, 128, 128, 128, 128, 123, 46, 121, 
	121, 93, 121, 121, 46, 121, 121, 33, 
	1, 93, 121, 103, 27, 93, 24, 88, 
	128, 0, 10, 128, 128, 128, 128, 121, 
	0, 0, 8, 8, 0, 0, 93, 0, 
	0, 0, 93, 1, 0, 0, 0, 93, 
	55, 55, 0, 55, 55, 0, 46, 93, 
	93, 0, 0, 93, 55, 55, 0, 46, 
	1, 93, 76, 55, 55, 55, 0, 126, 
	126, 0, 126, 0, 126, 126, 126, 126, 
	0, 126, 126, 126, 126, 126, 126, 0, 
	0, 55, 0, 93, 88, 128, 0, 10, 
	128, 128, 128, 128, 121, 0, 0, 8, 
	8, 0, 0, 93, 0, 0, 0, 93, 
	1, 0, 0, 0, 93, 55, 55, 0, 
	55, 55, 0, 46, 93, 93, 0, 0, 
	93, 55, 55, 0, 46, 1, 93, 76, 
	55, 55, 55, 0, 126, 126, 0, 126, 
	0, 126, 126, 126, 126, 0, 126, 126, 
	126, 126, 126, 126, 0, 0, 55, 0, 
	93, 24, 27, 93, 27, 36, 88, 128, 
	0, 10, 128, 128, 128, 128, 27, 36, 
	24, 88, 128, 0, 10, 128, 128, 128, 
	128, 33, 24, 58, 58, 1, 0, 128, 
	128, 121, 0, 0, 8, 8, 0, 0, 
	93, 0, 0, 0, 93, 1, 0, 0, 
	0, 93, 55, 55, 0, 55, 55, 0, 
	46, 93, 93, 0, 0, 93, 55, 55, 
	0, 46, 1, 93, 76, 55, 55, 55, 
	0, 126, 126, 0, 126, 0, 126, 126, 
	126, 126, 0, 126, 126, 126, 126, 126, 
	126, 126, 126, 126, 126, 126, 126, 126, 
	126, 126, 126, 126, 126, 126, 0, 0, 
	55, 0
]

class << self
	attr_accessor :_lex_index_offsets
	private :_lex_index_offsets, :_lex_index_offsets=
end
self._lex_index_offsets = [
	0, 0, 129, 258, 387, 516, 563, 685, 
	807, 901, 1023, 1145, 1192, 1314, 1436, 1470, 
	1472, 1566, 1688, 1792, 1921, 2050, 2179, 2308, 
	2355, 2477, 2599, 2693, 2815, 2937, 2984, 3106, 
	3228, 3262, 3264, 3358, 3480, 3584, 3713, 3842, 
	3971, 4100, 4229, 4358, 4487, 4616, 4740, 4787, 
	4909, 5031, 5125, 5247, 5369, 5416, 5538, 5660, 
	5694, 5696, 5790, 5912, 6016, 6044, 6138, 6163, 
	6252, 6381, 6382, 6393, 6522, 6651, 6780, 6909, 
	7031, 7032, 7033, 7042, 7051, 7052, 7053, 7147, 
	7148, 7149, 7150, 7244, 7246, 7247, 7248, 7249, 
	7343, 7399, 7455, 7456, 7512, 7568, 7569, 7616, 
	7710, 7804, 7805, 7806, 7900, 7956, 8012, 8013, 
	8060, 8062, 8156, 8233, 8289, 8345, 8401, 8402, 
	8529, 8656, 8657, 8784, 8785, 8912, 9039, 9166, 
	9293, 9294, 9421, 9548, 9675, 9802, 9929, 10056, 
	10057, 10058, 10114, 10115, 10209, 10298, 10427, 10428, 
	10439, 10568, 10697, 10826, 10955, 11077, 11078, 11079, 
	11088, 11097, 11098, 11099, 11193, 11194, 11195, 11196, 
	11290, 11292, 11293, 11294, 11295, 11389, 11445, 11501, 
	11502, 11558, 11614, 11615, 11662, 11756, 11850, 11851, 
	11852, 11946, 12002, 12058, 12059, 12106, 12108, 12202, 
	12279, 12335, 12391, 12447, 12448, 12575, 12702, 12703, 
	12830, 12831, 12958, 13085, 13212, 13339, 13340, 13467, 
	13594, 13721, 13848, 13975, 14102, 14103, 14104, 14160, 
	14161, 14255, 14280, 14308, 14402, 14430, 14467, 14556, 
	14685, 14686, 14697, 14826, 14955, 15084, 15213, 15241, 
	15278, 15303, 15392, 15521, 15522, 15533, 15662, 15791, 
	15920, 16049, 16083, 16108, 16167, 16226, 16228, 16229, 
	16358, 16487, 16609, 16610, 16611, 16620, 16629, 16630, 
	16631, 16725, 16726, 16727, 16728, 16822, 16824, 16825, 
	16826, 16827, 16921, 16977, 17033, 17034, 17090, 17146, 
	17147, 17194, 17288, 17382, 17383, 17384, 17478, 17534, 
	17590, 17591, 17638, 17640, 17734, 17811, 17867, 17923, 
	17979, 17980, 18107, 18234, 18235, 18362, 18363, 18490, 
	18617, 18744, 18871, 18872, 18999, 19126, 19253, 19380, 
	19507, 19634, 19761, 19888, 20015, 20142, 20269, 20396, 
	20523, 20650, 20777, 20904, 21031, 21158, 21285, 21286, 
	21287, 21343
]

class << self
	attr_accessor :_lex_indicies
	private :_lex_indicies, :_lex_indicies=
end
self._lex_indicies = [
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 2, 2, 0, 2, 0, 2, 2, 
	0, 0, 2, 2, 2, 3, 2, 2, 
	4, 4, 4, 4, 4, 4, 4, 4, 
	4, 4, 2, 2, 2, 2, 2, 2, 
	2, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 0, 2, 0, 0, 1, 
	2, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 0, 0, 0, 2, 0, 
	1, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 0, 0, 0, 0, 0, 
	0, 0, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 0, 0, 0, 0, 
	2, 0, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 0, 0, 0, 0, 
	0, 2, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 6, 6, 6, 6, 6, 6, 
	6, 6, 6, 6, 0, 0, 0, 0, 
	0, 0, 7, 5, 5, 5, 5, 5, 
	5, 5, 5, 5, 5, 5, 5, 5, 
	5, 5, 5, 5, 5, 5, 5, 5, 
	5, 5, 5, 5, 5, 0, 0, 0, 
	0, 5, 0, 5, 5, 5, 5, 5, 
	5, 5, 5, 5, 5, 5, 5, 5, 
	5, 5, 5, 5, 5, 5, 5, 5, 
	5, 5, 5, 5, 5, 0, 0, 0, 
	0, 0, 5, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 9, 9, 9, 9, 9, 
	9, 9, 9, 9, 9, 0, 0, 0, 
	0, 0, 0, 0, 8, 8, 8, 8, 
	8, 8, 8, 8, 8, 8, 8, 8, 
	8, 8, 8, 8, 8, 8, 8, 8, 
	8, 8, 8, 8, 8, 8, 0, 0, 
	0, 0, 8, 0, 8, 8, 8, 8, 
	8, 8, 8, 8, 8, 8, 8, 8, 
	8, 8, 8, 8, 8, 8, 8, 8, 
	8, 8, 8, 8, 8, 8, 0, 0, 
	0, 0, 0, 8, 10, 11, 11, 11, 
	10, 11, 11, 11, 11, 11, 11, 11, 
	11, 11, 11, 11, 11, 11, 11, 11, 
	11, 11, 11, 11, 11, 11, 10, 11, 
	11, 11, 11, 11, 11, 11, 11, 11, 
	11, 11, 11, 11, 11, 11, 11, 11, 
	11, 12, 11, 10, 13, 13, 13, 10, 
	13, 13, 13, 13, 13, 14, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 10, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	15, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 16, 13, 10, 13, 13, 
	13, 10, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 10, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 17, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 16, 13, 10, 
	18, 18, 18, 10, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 10, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 19, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 20, 18, 10, 21, 21, 
	21, 10, 21, 21, 21, 21, 21, 22, 
	21, 21, 21, 21, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 21, 21, 10, 
	21, 21, 21, 21, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 23, 21, 10, 
	21, 21, 21, 10, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 21, 21, 21, 
	21, 10, 21, 21, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 21, 21, 21, 
	21, 21, 21, 21, 21, 21, 21, 23, 
	21, 10, 11, 11, 11, 10, 11, 11, 
	11, 11, 11, 11, 11, 11, 11, 11, 
	11, 11, 11, 11, 11, 11, 11, 11, 
	11, 11, 11, 10, 11, 11, 11, 11, 
	11, 11, 11, 11, 11, 11, 11, 11, 
	11, 11, 11, 11, 11, 11, 24, 11, 
	10, 25, 25, 25, 10, 25, 25, 25, 
	25, 25, 26, 25, 25, 25, 25, 25, 
	25, 25, 25, 25, 25, 25, 25, 25, 
	25, 25, 10, 25, 25, 25, 25, 25, 
	25, 25, 25, 25, 25, 25, 25, 25, 
	25, 25, 25, 25, 25, 25, 25, 25, 
	25, 25, 25, 25, 25, 25, 25, 25, 
	25, 25, 25, 25, 25, 25, 25, 25, 
	25, 25, 25, 27, 25, 25, 25, 25, 
	25, 25, 25, 25, 25, 25, 25, 25, 
	25, 25, 25, 25, 25, 25, 25, 25, 
	25, 25, 25, 25, 28, 25, 25, 25, 
	25, 25, 25, 29, 25, 25, 25, 25, 
	25, 25, 25, 25, 25, 25, 25, 25, 
	25, 25, 25, 25, 25, 25, 25, 25, 
	30, 25, 10, 25, 25, 25, 10, 25, 
	25, 25, 25, 25, 25, 25, 25, 25, 
	25, 25, 25, 25, 25, 25, 25, 25, 
	25, 25, 25, 25, 10, 25, 25, 25, 
	25, 25, 25, 25, 25, 25, 25, 25, 
	25, 25, 25, 25, 25, 25, 25, 25, 
	25, 25, 25, 25, 25, 25, 25, 25, 
	25, 25, 25, 25, 25, 25, 25, 25, 
	25, 25, 25, 25, 25, 25, 25, 25, 
	25, 25, 25, 25, 25, 25, 25, 25, 
	25, 25, 25, 25, 25, 25, 25, 25, 
	25, 25, 25, 25, 25, 25, 25, 25, 
	25, 25, 25, 25, 25, 25, 25, 25, 
	25, 25, 25, 25, 25, 25, 25, 25, 
	25, 25, 25, 25, 25, 25, 25, 25, 
	25, 25, 30, 25, 31, 10, 10, 10, 
	10, 10, 10, 10, 10, 10, 10, 10, 
	10, 10, 10, 10, 10, 10, 10, 10, 
	10, 10, 10, 10, 10, 10, 10, 10, 
	10, 10, 10, 10, 32, 10, 32, 10, 
	10, 33, 33, 33, 10, 33, 33, 33, 
	33, 33, 33, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 33, 33, 33, 33, 
	33, 33, 10, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 33, 33, 33, 34, 
	33, 33, 33, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 35, 33, 10, 13, 
	13, 13, 10, 13, 13, 13, 13, 13, 
	14, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	10, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 17, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 16, 13, 
	10, 36, 36, 36, 10, 36, 36, 36, 
	36, 36, 36, 36, 36, 36, 36, 36, 
	36, 36, 36, 36, 36, 36, 36, 36, 
	36, 36, 10, 36, 36, 36, 36, 36, 
	36, 36, 36, 36, 36, 36, 36, 36, 
	36, 36, 36, 36, 36, 36, 36, 36, 
	37, 37, 37, 37, 37, 37, 37, 37, 
	37, 37, 36, 36, 36, 36, 36, 36, 
	36, 37, 37, 37, 37, 37, 37, 36, 
	36, 36, 36, 36, 36, 36, 36, 36, 
	36, 36, 36, 36, 36, 36, 36, 36, 
	36, 36, 36, 36, 36, 36, 36, 36, 
	36, 37, 37, 37, 37, 37, 37, 36, 
	38, 38, 38, 38, 38, 38, 38, 38, 
	38, 38, 38, 38, 38, 38, 38, 38, 
	38, 38, 38, 38, 38, 38, 38, 38, 
	38, 38, 38, 38, 38, 38, 38, 38, 
	38, 40, 40, 38, 40, 38, 40, 40, 
	38, 38, 40, 40, 40, 41, 40, 40, 
	42, 42, 42, 42, 42, 42, 42, 42, 
	42, 42, 40, 40, 40, 40, 40, 40, 
	40, 39, 39, 39, 39, 39, 39, 39, 
	39, 39, 39, 39, 39, 39, 39, 39, 
	39, 39, 39, 39, 39, 39, 39, 39, 
	39, 39, 39, 38, 40, 38, 38, 39, 
	40, 39, 39, 39, 39, 39, 39, 39, 
	39, 39, 39, 39, 39, 39, 39, 39, 
	39, 39, 39, 39, 39, 39, 39, 39, 
	39, 39, 39, 38, 38, 38, 40, 38, 
	39, 38, 38, 38, 38, 38, 38, 38, 
	38, 38, 38, 38, 38, 38, 38, 38, 
	38, 38, 38, 38, 38, 38, 38, 38, 
	38, 38, 38, 38, 38, 38, 38, 38, 
	38, 38, 38, 38, 38, 38, 38, 38, 
	38, 38, 38, 38, 38, 38, 38, 38, 
	38, 40, 40, 40, 40, 40, 40, 40, 
	40, 40, 40, 38, 38, 38, 38, 38, 
	38, 38, 40, 40, 40, 40, 40, 40, 
	40, 40, 40, 40, 40, 40, 40, 40, 
	40, 40, 40, 40, 40, 40, 40, 40, 
	40, 40, 40, 40, 38, 38, 38, 38, 
	40, 38, 40, 40, 40, 40, 40, 40, 
	40, 40, 40, 40, 40, 40, 40, 40, 
	40, 40, 40, 40, 40, 40, 40, 40, 
	40, 40, 40, 40, 38, 38, 38, 38, 
	38, 40, 38, 38, 38, 38, 38, 38, 
	38, 38, 38, 38, 38, 38, 38, 38, 
	38, 38, 38, 38, 38, 38, 38, 38, 
	38, 38, 38, 38, 38, 38, 38, 38, 
	38, 38, 38, 38, 38, 38, 38, 38, 
	38, 38, 38, 38, 38, 38, 38, 38, 
	38, 38, 44, 44, 44, 44, 44, 44, 
	44, 44, 44, 44, 38, 38, 38, 38, 
	38, 38, 45, 43, 43, 43, 43, 43, 
	43, 43, 43, 43, 43, 43, 43, 43, 
	43, 43, 43, 43, 43, 43, 43, 43, 
	43, 43, 43, 43, 43, 38, 38, 38, 
	38, 43, 38, 43, 43, 43, 43, 43, 
	43, 43, 43, 43, 43, 43, 43, 43, 
	43, 43, 43, 43, 43, 43, 43, 43, 
	43, 43, 43, 43, 43, 38, 38, 38, 
	38, 38, 43, 38, 38, 38, 38, 38, 
	38, 38, 38, 38, 38, 38, 38, 38, 
	38, 38, 38, 38, 38, 38, 38, 38, 
	38, 38, 38, 38, 38, 38, 38, 38, 
	38, 38, 38, 38, 38, 38, 38, 38, 
	38, 38, 38, 38, 38, 38, 38, 38, 
	38, 38, 38, 47, 47, 47, 47, 47, 
	47, 47, 47, 47, 47, 38, 38, 38, 
	38, 38, 38, 38, 46, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 38, 38, 
	38, 38, 46, 38, 46, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 38, 38, 
	38, 38, 38, 46, 48, 49, 49, 49, 
	48, 49, 49, 49, 49, 49, 49, 49, 
	49, 49, 49, 49, 49, 49, 49, 49, 
	49, 49, 49, 49, 49, 49, 48, 49, 
	49, 49, 49, 49, 49, 49, 49, 49, 
	49, 49, 49, 49, 49, 49, 49, 49, 
	49, 50, 49, 48, 51, 51, 51, 48, 
	51, 51, 51, 51, 51, 52, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 48, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	53, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 54, 51, 48, 51, 51, 
	51, 48, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 48, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 55, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 54, 51, 48, 
	56, 56, 56, 48, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 48, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 57, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 58, 56, 48, 59, 59, 
	59, 48, 59, 59, 59, 59, 59, 60, 
	59, 59, 59, 59, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 59, 59, 48, 
	59, 59, 59, 59, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 61, 59, 48, 
	59, 59, 59, 48, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 59, 59, 59, 
	59, 48, 59, 59, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 59, 59, 59, 
	59, 59, 59, 59, 59, 59, 59, 61, 
	59, 48, 49, 49, 49, 48, 49, 49, 
	49, 49, 49, 49, 49, 49, 49, 49, 
	49, 49, 49, 49, 49, 49, 49, 49, 
	49, 49, 49, 48, 49, 49, 49, 49, 
	49, 49, 49, 49, 49, 49, 49, 49, 
	49, 49, 49, 49, 49, 49, 62, 49, 
	48, 63, 63, 63, 48, 63, 63, 63, 
	63, 63, 64, 63, 63, 63, 63, 63, 
	63, 63, 63, 63, 63, 63, 63, 63, 
	63, 63, 48, 63, 63, 63, 63, 63, 
	63, 63, 63, 63, 63, 63, 63, 63, 
	63, 63, 63, 63, 63, 63, 63, 63, 
	63, 63, 63, 63, 63, 63, 63, 63, 
	63, 63, 63, 63, 63, 63, 63, 63, 
	63, 63, 63, 65, 63, 63, 63, 63, 
	63, 63, 63, 63, 63, 63, 63, 63, 
	63, 63, 63, 63, 63, 63, 63, 63, 
	63, 63, 63, 63, 66, 63, 63, 63, 
	63, 63, 63, 67, 63, 63, 63, 63, 
	63, 63, 63, 63, 63, 63, 63, 63, 
	63, 63, 63, 63, 63, 63, 63, 63, 
	68, 63, 48, 63, 63, 63, 48, 63, 
	63, 63, 63, 63, 63, 63, 63, 63, 
	63, 63, 63, 63, 63, 63, 63, 63, 
	63, 63, 63, 63, 48, 63, 63, 63, 
	63, 63, 63, 63, 63, 63, 63, 63, 
	63, 63, 63, 63, 63, 63, 63, 63, 
	63, 63, 63, 63, 63, 63, 63, 63, 
	63, 63, 63, 63, 63, 63, 63, 63, 
	63, 63, 63, 63, 63, 63, 63, 63, 
	63, 63, 63, 63, 63, 63, 63, 63, 
	63, 63, 63, 63, 63, 63, 63, 63, 
	63, 63, 63, 63, 63, 63, 63, 63, 
	63, 63, 63, 63, 63, 63, 63, 63, 
	63, 63, 63, 63, 63, 63, 63, 63, 
	63, 63, 63, 63, 63, 63, 63, 63, 
	63, 63, 68, 63, 69, 48, 48, 48, 
	48, 48, 48, 48, 48, 48, 48, 48, 
	48, 48, 48, 48, 48, 48, 48, 48, 
	48, 48, 48, 48, 48, 48, 48, 48, 
	48, 48, 48, 48, 70, 48, 70, 48, 
	48, 71, 71, 71, 48, 71, 71, 71, 
	71, 71, 71, 71, 71, 71, 71, 71, 
	71, 71, 71, 71, 71, 71, 71, 71, 
	71, 71, 48, 71, 71, 71, 71, 71, 
	71, 71, 71, 71, 71, 71, 71, 71, 
	71, 71, 71, 71, 71, 71, 71, 71, 
	71, 71, 71, 71, 71, 71, 71, 71, 
	71, 71, 71, 71, 71, 71, 71, 72, 
	71, 71, 71, 71, 71, 71, 71, 71, 
	71, 71, 71, 71, 71, 71, 71, 71, 
	71, 71, 71, 71, 71, 71, 71, 71, 
	71, 71, 71, 71, 73, 71, 48, 51, 
	51, 51, 48, 51, 51, 51, 51, 51, 
	52, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	48, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 55, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 54, 51, 
	48, 74, 74, 74, 48, 74, 74, 74, 
	74, 74, 74, 74, 74, 74, 74, 74, 
	74, 74, 74, 74, 74, 74, 74, 74, 
	74, 74, 48, 74, 74, 74, 74, 74, 
	74, 74, 74, 74, 74, 74, 74, 74, 
	74, 74, 74, 74, 74, 74, 74, 74, 
	75, 75, 75, 75, 75, 75, 75, 75, 
	75, 75, 74, 74, 74, 74, 74, 74, 
	74, 75, 75, 75, 75, 75, 75, 74, 
	74, 74, 74, 74, 74, 74, 74, 74, 
	74, 74, 74, 74, 74, 74, 74, 74, 
	74, 74, 74, 74, 74, 74, 74, 74, 
	74, 75, 75, 75, 75, 75, 75, 74, 
	76, 76, 76, 76, 76, 76, 76, 76, 
	76, 76, 76, 76, 76, 76, 76, 76, 
	76, 76, 76, 76, 76, 76, 76, 76, 
	76, 76, 76, 76, 76, 76, 76, 76, 
	76, 78, 78, 76, 78, 76, 78, 78, 
	76, 76, 78, 78, 78, 79, 78, 78, 
	80, 80, 80, 80, 80, 80, 80, 80, 
	80, 80, 78, 78, 78, 78, 78, 78, 
	78, 77, 77, 77, 77, 77, 77, 77, 
	77, 77, 77, 77, 77, 77, 77, 77, 
	77, 77, 77, 77, 77, 77, 77, 77, 
	77, 77, 77, 76, 78, 76, 76, 77, 
	78, 77, 77, 77, 77, 77, 77, 77, 
	77, 77, 77, 77, 77, 77, 77, 77, 
	77, 77, 77, 77, 77, 77, 77, 77, 
	77, 77, 77, 76, 76, 76, 78, 76, 
	77, 76, 76, 76, 76, 76, 76, 76, 
	76, 76, 76, 76, 76, 76, 76, 76, 
	76, 76, 76, 76, 76, 76, 76, 76, 
	76, 76, 76, 76, 76, 76, 76, 76, 
	76, 76, 76, 76, 76, 76, 76, 76, 
	76, 76, 76, 76, 76, 76, 76, 76, 
	76, 78, 78, 78, 78, 78, 78, 78, 
	78, 78, 78, 76, 76, 76, 76, 76, 
	76, 76, 78, 78, 78, 78, 78, 78, 
	78, 78, 78, 78, 78, 78, 78, 78, 
	78, 78, 78, 78, 78, 78, 78, 78, 
	78, 78, 78, 78, 76, 76, 76, 76, 
	78, 76, 78, 78, 78, 78, 78, 78, 
	78, 78, 78, 78, 78, 78, 78, 78, 
	78, 78, 78, 78, 78, 78, 78, 78, 
	78, 78, 78, 78, 76, 76, 76, 76, 
	76, 78, 76, 76, 76, 76, 76, 76, 
	76, 76, 76, 76, 76, 76, 76, 76, 
	76, 76, 76, 76, 76, 76, 76, 76, 
	76, 76, 76, 76, 76, 76, 76, 76, 
	76, 76, 76, 76, 76, 76, 76, 76, 
	76, 76, 76, 76, 76, 76, 76, 76, 
	76, 76, 82, 82, 82, 82, 82, 82, 
	82, 82, 82, 82, 76, 76, 76, 76, 
	76, 76, 83, 81, 81, 81, 81, 81, 
	81, 81, 81, 81, 81, 81, 81, 81, 
	81, 81, 81, 81, 81, 81, 81, 81, 
	81, 81, 81, 81, 81, 76, 76, 76, 
	76, 81, 76, 81, 81, 81, 81, 81, 
	81, 81, 81, 81, 81, 81, 81, 81, 
	81, 81, 81, 81, 81, 81, 81, 81, 
	81, 81, 81, 81, 81, 76, 76, 76, 
	76, 76, 81, 76, 76, 76, 76, 76, 
	76, 76, 76, 76, 76, 76, 76, 76, 
	76, 76, 76, 76, 76, 76, 76, 76, 
	76, 76, 76, 76, 76, 76, 76, 76, 
	76, 76, 76, 76, 76, 76, 76, 76, 
	76, 76, 76, 76, 76, 76, 76, 76, 
	76, 76, 76, 85, 85, 85, 85, 85, 
	85, 85, 85, 85, 85, 76, 76, 76, 
	76, 76, 76, 76, 84, 84, 84, 84, 
	84, 84, 84, 84, 84, 84, 84, 84, 
	84, 84, 84, 84, 84, 84, 84, 84, 
	84, 84, 84, 84, 84, 84, 76, 76, 
	76, 76, 84, 76, 84, 84, 84, 84, 
	84, 84, 84, 84, 84, 84, 84, 84, 
	84, 84, 84, 84, 84, 84, 84, 84, 
	84, 84, 84, 84, 84, 84, 76, 76, 
	76, 76, 76, 84, 86, 86, 86, 86, 
	86, 86, 86, 86, 86, 86, 86, 86, 
	86, 86, 86, 86, 86, 86, 86, 86, 
	86, 86, 86, 86, 86, 86, 86, 86, 
	86, 86, 86, 86, 86, 88, 88, 86, 
	88, 86, 88, 88, 86, 86, 88, 88, 
	88, 89, 88, 88, 90, 90, 90, 90, 
	90, 90, 90, 90, 90, 90, 88, 88, 
	88, 88, 88, 88, 88, 87, 87, 87, 
	87, 87, 87, 87, 87, 87, 87, 87, 
	87, 87, 87, 87, 87, 87, 87, 87, 
	87, 87, 87, 87, 87, 87, 87, 86, 
	88, 86, 86, 87, 88, 87, 87, 87, 
	87, 87, 87, 87, 87, 87, 87, 87, 
	87, 87, 87, 87, 87, 87, 87, 87, 
	87, 87, 87, 87, 87, 87, 87, 86, 
	86, 86, 88, 86, 87, 86, 86, 86, 
	86, 86, 86, 86, 86, 86, 86, 86, 
	86, 86, 86, 86, 86, 86, 86, 86, 
	86, 86, 86, 86, 86, 86, 86, 86, 
	86, 86, 86, 86, 86, 86, 86, 86, 
	86, 86, 86, 86, 86, 86, 86, 86, 
	86, 86, 86, 86, 86, 88, 88, 88, 
	88, 88, 88, 88, 88, 88, 88, 86, 
	86, 86, 86, 86, 86, 86, 88, 88, 
	88, 88, 88, 88, 88, 88, 88, 88, 
	88, 88, 88, 88, 88, 88, 88, 88, 
	88, 88, 88, 88, 88, 88, 88, 88, 
	86, 86, 86, 86, 88, 86, 88, 88, 
	88, 88, 88, 88, 88, 88, 88, 88, 
	88, 88, 88, 88, 88, 88, 88, 88, 
	88, 88, 88, 88, 88, 88, 88, 88, 
	86, 86, 86, 86, 86, 88, 86, 86, 
	86, 86, 86, 86, 86, 86, 86, 86, 
	86, 86, 86, 86, 86, 86, 86, 86, 
	86, 86, 86, 86, 86, 86, 86, 86, 
	86, 86, 86, 86, 86, 86, 86, 86, 
	86, 86, 86, 86, 86, 86, 86, 86, 
	86, 86, 86, 86, 86, 86, 92, 92, 
	92, 92, 92, 92, 92, 92, 92, 92, 
	86, 86, 86, 86, 86, 86, 93, 91, 
	91, 91, 91, 91, 91, 91, 91, 91, 
	91, 91, 91, 91, 91, 91, 91, 91, 
	91, 91, 91, 91, 91, 91, 91, 91, 
	91, 86, 86, 86, 86, 91, 86, 91, 
	91, 91, 91, 91, 91, 91, 91, 91, 
	91, 91, 91, 91, 91, 91, 91, 91, 
	91, 91, 91, 91, 91, 91, 91, 91, 
	91, 86, 86, 86, 86, 86, 91, 86, 
	86, 86, 86, 86, 86, 86, 86, 86, 
	86, 86, 86, 86, 86, 86, 86, 86, 
	86, 86, 86, 86, 86, 86, 86, 86, 
	86, 86, 86, 86, 86, 86, 86, 86, 
	86, 86, 86, 86, 86, 86, 86, 86, 
	86, 86, 86, 86, 86, 86, 86, 95, 
	95, 95, 95, 95, 95, 95, 95, 95, 
	95, 86, 86, 86, 86, 86, 86, 86, 
	94, 94, 94, 94, 94, 94, 94, 94, 
	94, 94, 94, 94, 94, 94, 94, 94, 
	94, 94, 94, 94, 94, 94, 94, 94, 
	94, 94, 86, 86, 86, 86, 94, 86, 
	94, 94, 94, 94, 94, 94, 94, 94, 
	94, 94, 94, 94, 94, 94, 94, 94, 
	94, 94, 94, 94, 94, 94, 94, 94, 
	94, 94, 86, 86, 86, 86, 86, 94, 
	97, 96, 96, 96, 97, 96, 96, 96, 
	96, 98, 99, 98, 98, 98, 96, 96, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 96, 97, 96, 96, 96, 96, 96, 
	98, 96, 96, 96, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 100, 100, 100, 100, 100, 100, 100, 
	100, 100, 100, 100, 100, 100, 100, 100, 
	100, 100, 100, 100, 100, 100, 100, 100, 
	100, 100, 100, 96, 101, 96, 96, 100, 
	96, 100, 100, 100, 100, 100, 100, 100, 
	100, 100, 100, 100, 100, 100, 100, 100, 
	100, 100, 100, 100, 100, 100, 100, 100, 
	100, 100, 100, 96, 102, 103, 103, 103, 
	102, 103, 103, 103, 103, 103, 103, 103, 
	103, 103, 103, 103, 103, 103, 103, 103, 
	103, 103, 103, 103, 103, 103, 102, 103, 
	103, 103, 103, 103, 103, 103, 103, 103, 
	103, 103, 103, 103, 103, 103, 103, 103, 
	103, 104, 103, 102, 105, 105, 105, 102, 
	105, 105, 105, 105, 105, 106, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 102, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	107, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 108, 105, 102, 105, 105, 
	105, 102, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 102, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 109, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 108, 105, 102, 
	110, 110, 110, 102, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 102, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 111, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 112, 110, 102, 113, 113, 
	113, 102, 113, 113, 113, 113, 113, 114, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 102, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 115, 113, 102, 
	113, 113, 113, 102, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 102, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 115, 
	113, 102, 103, 103, 103, 102, 103, 103, 
	103, 103, 103, 103, 103, 103, 103, 103, 
	103, 103, 103, 103, 103, 103, 103, 103, 
	103, 103, 103, 102, 103, 103, 103, 103, 
	103, 103, 103, 103, 103, 103, 103, 103, 
	103, 103, 103, 103, 103, 103, 116, 103, 
	102, 117, 117, 117, 102, 117, 117, 117, 
	117, 117, 118, 117, 117, 117, 117, 117, 
	117, 117, 117, 117, 117, 117, 117, 117, 
	117, 117, 102, 117, 117, 117, 117, 117, 
	117, 117, 117, 117, 117, 117, 117, 117, 
	117, 117, 117, 117, 117, 117, 117, 117, 
	117, 117, 117, 117, 117, 117, 117, 117, 
	117, 117, 117, 117, 117, 117, 117, 117, 
	117, 117, 117, 119, 117, 117, 117, 117, 
	117, 117, 117, 117, 117, 117, 117, 117, 
	117, 117, 117, 117, 117, 117, 117, 117, 
	117, 117, 117, 117, 120, 117, 117, 117, 
	117, 117, 117, 121, 117, 117, 117, 117, 
	117, 117, 117, 117, 117, 117, 117, 117, 
	117, 117, 117, 117, 117, 117, 117, 117, 
	122, 117, 102, 117, 117, 117, 102, 117, 
	117, 117, 117, 117, 117, 117, 117, 117, 
	117, 117, 117, 117, 117, 117, 117, 117, 
	117, 117, 117, 117, 102, 117, 117, 117, 
	117, 117, 117, 117, 117, 117, 117, 117, 
	117, 117, 117, 117, 117, 117, 117, 117, 
	117, 117, 117, 117, 117, 117, 117, 117, 
	117, 117, 117, 117, 117, 117, 117, 117, 
	117, 117, 117, 117, 117, 117, 117, 117, 
	117, 117, 117, 117, 117, 117, 117, 117, 
	117, 117, 117, 117, 117, 117, 117, 117, 
	117, 117, 117, 117, 117, 117, 117, 117, 
	117, 117, 117, 117, 117, 117, 117, 117, 
	117, 117, 117, 117, 117, 117, 117, 117, 
	117, 117, 117, 117, 117, 117, 117, 117, 
	117, 117, 122, 117, 123, 102, 102, 102, 
	102, 102, 102, 102, 102, 102, 102, 102, 
	102, 102, 102, 102, 102, 102, 102, 102, 
	102, 102, 102, 102, 102, 102, 102, 102, 
	102, 102, 102, 102, 124, 102, 124, 102, 
	102, 125, 125, 125, 102, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 125, 102, 125, 125, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 126, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 125, 125, 125, 127, 125, 102, 105, 
	105, 105, 102, 105, 105, 105, 105, 105, 
	106, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	102, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 109, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 108, 105, 
	102, 128, 128, 128, 102, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 102, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	129, 129, 129, 129, 129, 129, 129, 129, 
	129, 129, 128, 128, 128, 128, 128, 128, 
	128, 129, 129, 129, 129, 129, 129, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 129, 129, 129, 129, 129, 129, 128, 
	97, 130, 130, 130, 97, 130, 130, 130, 
	130, 130, 130, 130, 130, 130, 130, 130, 
	130, 130, 130, 130, 130, 130, 130, 130, 
	130, 130, 97, 130, 132, 131, 131, 131, 
	132, 131, 131, 131, 131, 133, 134, 133, 
	133, 133, 131, 131, 131, 131, 131, 131, 
	131, 131, 131, 131, 131, 131, 132, 131, 
	131, 131, 131, 131, 133, 131, 131, 135, 
	131, 131, 131, 131, 131, 131, 131, 131, 
	131, 131, 131, 131, 131, 131, 131, 131, 
	131, 131, 131, 131, 131, 131, 131, 131, 
	131, 131, 131, 131, 131, 131, 131, 131, 
	131, 131, 131, 131, 131, 131, 131, 131, 
	131, 131, 131, 131, 131, 131, 131, 131, 
	131, 131, 131, 131, 131, 131, 131, 131, 
	136, 131, 133, 137, 133, 133, 133, 137, 
	137, 137, 137, 137, 137, 137, 137, 137, 
	137, 137, 137, 137, 137, 137, 137, 137, 
	137, 133, 137, 139, 138, 138, 138, 138, 
	138, 138, 138, 138, 138, 138, 138, 138, 
	138, 138, 138, 138, 138, 138, 138, 138, 
	138, 138, 138, 138, 138, 138, 138, 140, 
	138, 138, 138, 138, 138, 138, 138, 138, 
	138, 138, 138, 138, 138, 138, 138, 138, 
	138, 138, 138, 138, 138, 138, 138, 138, 
	138, 138, 138, 138, 138, 138, 138, 138, 
	138, 138, 138, 138, 138, 138, 138, 138, 
	138, 138, 138, 138, 138, 138, 138, 138, 
	138, 138, 138, 138, 138, 138, 138, 138, 
	138, 138, 141, 138, 142, 142, 142, 142, 
	142, 142, 142, 142, 142, 142, 142, 142, 
	142, 142, 142, 142, 142, 142, 142, 142, 
	142, 142, 142, 142, 142, 142, 142, 142, 
	142, 142, 142, 142, 142, 142, 142, 142, 
	142, 142, 142, 142, 142, 142, 142, 142, 
	142, 142, 142, 142, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 142, 142, 
	142, 142, 142, 142, 142, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 142, 
	142, 142, 142, 1, 142, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 142, 
	142, 142, 142, 142, 1, 142, 4, 4, 
	4, 4, 4, 4, 4, 4, 4, 4, 
	142, 143, 143, 143, 143, 143, 143, 143, 
	143, 143, 143, 143, 143, 143, 143, 143, 
	143, 143, 143, 143, 143, 143, 143, 143, 
	143, 143, 143, 143, 143, 143, 143, 143, 
	143, 143, 143, 143, 143, 143, 143, 143, 
	143, 143, 143, 143, 143, 143, 143, 143, 
	143, 5, 5, 5, 5, 5, 5, 5, 
	5, 5, 5, 143, 143, 143, 143, 143, 
	143, 143, 5, 5, 5, 5, 5, 5, 
	5, 5, 5, 5, 5, 5, 5, 5, 
	5, 5, 5, 5, 5, 5, 5, 5, 
	5, 5, 5, 5, 143, 143, 143, 143, 
	5, 143, 5, 5, 5, 5, 5, 5, 
	5, 5, 5, 5, 5, 5, 5, 5, 
	5, 5, 5, 5, 5, 5, 5, 5, 
	5, 5, 5, 5, 143, 143, 143, 143, 
	143, 5, 144, 144, 144, 144, 144, 144, 
	144, 144, 144, 144, 144, 144, 144, 144, 
	144, 144, 144, 144, 144, 144, 144, 144, 
	144, 144, 144, 144, 144, 144, 144, 144, 
	144, 144, 144, 144, 144, 144, 144, 144, 
	144, 144, 144, 144, 144, 144, 144, 144, 
	144, 144, 5, 5, 5, 5, 5, 5, 
	5, 5, 5, 5, 144, 144, 144, 144, 
	144, 144, 144, 6, 6, 6, 6, 6, 
	6, 6, 6, 6, 6, 6, 6, 6, 
	6, 6, 6, 6, 6, 6, 6, 6, 
	6, 6, 6, 6, 6, 144, 144, 144, 
	144, 6, 144, 6, 6, 6, 6, 6, 
	6, 6, 6, 6, 6, 6, 6, 6, 
	6, 6, 6, 6, 6, 6, 6, 6, 
	6, 6, 6, 6, 6, 144, 144, 144, 
	144, 144, 6, 145, 145, 145, 145, 145, 
	145, 145, 145, 145, 145, 145, 145, 145, 
	145, 145, 145, 145, 145, 145, 145, 145, 
	145, 145, 145, 145, 145, 145, 145, 145, 
	145, 145, 145, 145, 145, 145, 145, 145, 
	145, 145, 145, 145, 145, 145, 145, 145, 
	145, 145, 145, 8, 8, 8, 8, 8, 
	8, 8, 8, 8, 8, 145, 145, 145, 
	145, 145, 145, 145, 8, 8, 8, 8, 
	8, 8, 8, 8, 8, 8, 8, 8, 
	8, 8, 8, 8, 8, 8, 8, 8, 
	8, 8, 8, 8, 8, 8, 145, 145, 
	145, 145, 8, 145, 8, 8, 8, 8, 
	8, 8, 8, 8, 8, 8, 8, 8, 
	8, 8, 8, 8, 8, 8, 8, 8, 
	8, 8, 8, 8, 8, 8, 145, 145, 
	145, 145, 145, 8, 146, 146, 146, 146, 
	146, 146, 146, 146, 146, 146, 146, 146, 
	146, 146, 146, 146, 146, 146, 146, 146, 
	146, 146, 146, 146, 146, 146, 146, 146, 
	146, 146, 146, 146, 146, 146, 146, 146, 
	146, 146, 146, 146, 146, 146, 146, 146, 
	146, 146, 146, 146, 8, 8, 8, 8, 
	8, 8, 8, 8, 8, 8, 146, 146, 
	146, 146, 146, 146, 146, 9, 9, 9, 
	9, 9, 9, 9, 9, 9, 9, 9, 
	9, 9, 9, 9, 9, 9, 9, 9, 
	9, 9, 9, 9, 9, 9, 9, 146, 
	146, 146, 146, 9, 146, 9, 9, 9, 
	9, 9, 9, 9, 9, 9, 9, 9, 
	9, 9, 9, 9, 9, 9, 9, 9, 
	9, 9, 9, 9, 9, 9, 9, 146, 
	146, 146, 146, 146, 9, 149, 148, 148, 
	148, 149, 148, 148, 148, 148, 148, 148, 
	148, 148, 148, 148, 148, 148, 148, 148, 
	148, 148, 148, 148, 148, 148, 148, 149, 
	148, 148, 148, 148, 148, 148, 148, 148, 
	148, 148, 148, 148, 148, 148, 148, 148, 
	148, 148, 148, 148, 148, 150, 150, 150, 
	150, 150, 150, 150, 150, 148, 148, 148, 
	148, 148, 148, 148, 148, 148, 148, 148, 
	151, 148, 148, 148, 148, 148, 148, 148, 
	148, 148, 152, 148, 148, 148, 148, 148, 
	148, 148, 148, 148, 148, 148, 148, 148, 
	148, 148, 148, 148, 148, 148, 148, 148, 
	153, 148, 148, 148, 148, 148, 148, 148, 
	148, 148, 148, 148, 148, 148, 148, 148, 
	148, 148, 154, 148, 148, 155, 148, 156, 
	157, 159, 159, 159, 159, 159, 159, 159, 
	159, 158, 160, 160, 160, 160, 160, 160, 
	160, 160, 158, 158, 161, 161, 33, 33, 
	33, 161, 33, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 33, 33, 33, 161, 
	33, 33, 33, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 34, 33, 33, 33, 
	33, 33, 33, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 33, 33, 33, 33, 
	33, 162, 33, 163, 164, 165, 165, 33, 
	33, 33, 165, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 33, 33, 33, 33, 
	165, 33, 33, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 33, 34, 33, 33, 
	33, 33, 33, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 33, 33, 33, 33, 
	33, 33, 166, 33, 32, 165, 167, 168, 
	169, 169, 18, 18, 18, 169, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 169, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	19, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 170, 18, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 169, 169, 169, 169, 169, 169, 169, 
	171, 171, 171, 171, 171, 171, 169, 169, 
	169, 169, 169, 169, 169, 169, 169, 169, 
	169, 169, 169, 169, 169, 169, 169, 169, 
	169, 169, 169, 169, 169, 169, 169, 169, 
	171, 171, 171, 171, 171, 171, 169, 173, 
	173, 173, 173, 173, 173, 173, 173, 173, 
	173, 172, 172, 172, 172, 172, 172, 172, 
	173, 173, 173, 173, 173, 173, 172, 172, 
	172, 172, 172, 172, 172, 172, 172, 172, 
	172, 172, 172, 172, 172, 172, 172, 172, 
	172, 172, 172, 172, 172, 172, 172, 172, 
	173, 173, 173, 173, 173, 173, 172, 172, 
	174, 174, 174, 174, 174, 174, 174, 174, 
	174, 174, 165, 165, 165, 165, 165, 165, 
	165, 174, 174, 174, 174, 174, 174, 165, 
	165, 165, 165, 165, 165, 165, 165, 165, 
	165, 165, 165, 165, 165, 165, 165, 165, 
	165, 165, 165, 165, 165, 165, 165, 165, 
	165, 174, 174, 174, 174, 174, 174, 165, 
	176, 176, 176, 176, 176, 176, 176, 176, 
	176, 176, 175, 175, 175, 175, 175, 175, 
	175, 176, 176, 176, 176, 176, 176, 175, 
	175, 175, 175, 175, 175, 175, 175, 175, 
	175, 175, 175, 175, 175, 175, 175, 175, 
	175, 175, 175, 175, 175, 175, 175, 175, 
	175, 176, 176, 176, 176, 176, 176, 175, 
	175, 165, 11, 11, 11, 165, 11, 11, 
	11, 11, 11, 11, 11, 11, 11, 11, 
	11, 11, 11, 11, 11, 11, 11, 11, 
	11, 11, 11, 165, 11, 11, 11, 11, 
	11, 11, 11, 11, 11, 11, 11, 11, 
	11, 11, 11, 11, 11, 11, 177, 11, 
	161, 18, 18, 18, 161, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 161, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 19, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 20, 18, 161, 178, 
	178, 178, 161, 178, 178, 178, 178, 178, 
	178, 178, 178, 178, 178, 178, 178, 178, 
	178, 178, 178, 178, 178, 178, 178, 178, 
	161, 178, 178, 178, 178, 178, 178, 178, 
	178, 178, 178, 178, 178, 178, 178, 178, 
	178, 178, 178, 178, 178, 178, 178, 178, 
	178, 178, 178, 178, 178, 178, 178, 178, 
	178, 178, 178, 178, 178, 178, 178, 178, 
	178, 178, 178, 178, 178, 178, 178, 178, 
	178, 178, 178, 178, 178, 178, 178, 178, 
	178, 178, 178, 178, 178, 178, 178, 178, 
	178, 178, 179, 178, 180, 181, 181, 178, 
	178, 178, 181, 178, 178, 178, 178, 178, 
	178, 178, 178, 178, 178, 178, 178, 178, 
	178, 178, 178, 178, 178, 178, 178, 178, 
	181, 178, 178, 178, 178, 178, 178, 178, 
	178, 178, 178, 178, 178, 178, 178, 178, 
	178, 178, 178, 178, 178, 178, 178, 178, 
	178, 178, 178, 178, 178, 178, 178, 178, 
	178, 178, 178, 178, 178, 178, 178, 178, 
	178, 178, 178, 178, 178, 178, 178, 178, 
	178, 178, 178, 178, 178, 178, 178, 178, 
	178, 178, 178, 178, 178, 178, 178, 178, 
	178, 178, 182, 178, 183, 183, 183, 183, 
	183, 183, 183, 183, 183, 183, 181, 181, 
	181, 181, 181, 181, 181, 183, 183, 183, 
	183, 183, 183, 181, 181, 181, 181, 181, 
	181, 181, 181, 181, 181, 181, 181, 181, 
	181, 181, 181, 181, 181, 181, 181, 181, 
	181, 181, 181, 181, 181, 183, 183, 183, 
	183, 183, 183, 181, 185, 185, 185, 185, 
	185, 185, 185, 185, 185, 185, 184, 184, 
	184, 184, 184, 184, 184, 185, 185, 185, 
	185, 185, 185, 184, 184, 184, 184, 184, 
	184, 184, 184, 184, 184, 184, 184, 184, 
	184, 184, 184, 184, 184, 184, 184, 184, 
	184, 184, 184, 184, 184, 185, 185, 185, 
	185, 185, 185, 184, 184, 181, 11, 11, 
	11, 181, 11, 11, 11, 11, 11, 11, 
	11, 11, 11, 11, 11, 11, 11, 11, 
	11, 11, 11, 11, 11, 11, 11, 181, 
	11, 11, 11, 11, 11, 11, 11, 11, 
	11, 11, 11, 11, 11, 11, 11, 11, 
	11, 11, 177, 11, 186, 181, 181, 18, 
	18, 18, 181, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	181, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 19, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 20, 18, 188, 188, 188, 188, 
	188, 188, 188, 188, 188, 188, 187, 187, 
	187, 187, 187, 187, 187, 188, 188, 188, 
	188, 188, 188, 187, 187, 187, 187, 187, 
	187, 187, 187, 187, 187, 187, 187, 187, 
	187, 187, 187, 187, 187, 187, 187, 187, 
	187, 187, 187, 187, 187, 188, 188, 188, 
	188, 188, 188, 187, 187, 187, 187, 187, 
	187, 187, 187, 187, 187, 187, 187, 187, 
	187, 187, 187, 187, 187, 187, 187, 189, 
	187, 190, 190, 190, 190, 190, 190, 190, 
	190, 190, 190, 187, 187, 187, 187, 187, 
	187, 187, 190, 190, 190, 190, 190, 190, 
	187, 187, 187, 187, 187, 187, 187, 187, 
	187, 187, 187, 187, 187, 187, 187, 187, 
	187, 187, 187, 187, 187, 187, 187, 187, 
	187, 187, 190, 190, 190, 190, 190, 190, 
	187, 191, 191, 191, 191, 191, 191, 191, 
	191, 191, 191, 187, 187, 187, 187, 187, 
	187, 187, 191, 191, 191, 191, 191, 191, 
	187, 187, 187, 187, 187, 187, 187, 187, 
	187, 187, 187, 187, 187, 187, 187, 187, 
	187, 187, 187, 187, 187, 187, 187, 187, 
	187, 187, 191, 191, 191, 191, 191, 191, 
	187, 192, 192, 192, 192, 192, 192, 192, 
	192, 192, 192, 187, 187, 187, 187, 187, 
	187, 187, 192, 192, 192, 192, 192, 192, 
	187, 187, 187, 187, 187, 187, 187, 187, 
	187, 187, 187, 187, 187, 187, 187, 187, 
	187, 187, 187, 187, 187, 187, 187, 187, 
	187, 187, 192, 192, 192, 192, 192, 192, 
	187, 193, 196, 195, 195, 195, 196, 195, 
	195, 195, 195, 197, 195, 195, 195, 195, 
	195, 195, 195, 195, 195, 195, 195, 195, 
	195, 195, 195, 195, 196, 195, 195, 195, 
	195, 195, 197, 195, 195, 195, 195, 195, 
	195, 195, 195, 195, 195, 195, 195, 195, 
	195, 195, 198, 198, 198, 198, 198, 198, 
	198, 198, 198, 198, 195, 195, 195, 195, 
	195, 195, 195, 198, 198, 198, 198, 198, 
	198, 195, 195, 195, 195, 195, 195, 195, 
	195, 195, 195, 195, 195, 195, 195, 195, 
	195, 195, 195, 195, 195, 195, 195, 195, 
	195, 195, 195, 198, 198, 198, 198, 198, 
	198, 195, 195, 195, 195, 195, 195, 195, 
	195, 195, 195, 195, 195, 195, 195, 195, 
	195, 195, 195, 195, 195, 195, 195, 199, 
	195, 196, 195, 195, 195, 196, 195, 195, 
	195, 195, 194, 195, 195, 195, 195, 195, 
	195, 195, 195, 195, 195, 195, 195, 195, 
	195, 195, 195, 196, 195, 195, 195, 195, 
	195, 194, 195, 195, 195, 195, 195, 195, 
	195, 195, 195, 195, 195, 195, 195, 195, 
	195, 200, 200, 200, 200, 200, 200, 200, 
	200, 200, 200, 195, 195, 195, 195, 195, 
	195, 195, 200, 200, 200, 200, 200, 200, 
	195, 195, 195, 195, 195, 195, 195, 195, 
	195, 195, 195, 195, 195, 195, 195, 195, 
	195, 195, 195, 195, 195, 195, 195, 195, 
	195, 195, 200, 200, 200, 200, 200, 200, 
	195, 195, 195, 195, 195, 195, 195, 195, 
	195, 195, 195, 195, 195, 195, 195, 195, 
	195, 195, 195, 195, 195, 195, 201, 195, 
	194, 196, 200, 200, 200, 196, 200, 200, 
	200, 200, 194, 200, 200, 200, 200, 200, 
	200, 200, 200, 200, 200, 200, 200, 200, 
	200, 200, 200, 196, 200, 200, 200, 200, 
	200, 194, 200, 200, 200, 200, 200, 200, 
	200, 200, 200, 200, 200, 200, 200, 200, 
	200, 200, 200, 200, 200, 200, 200, 200, 
	200, 200, 200, 200, 200, 200, 200, 200, 
	200, 200, 200, 200, 200, 200, 200, 200, 
	200, 200, 200, 200, 200, 200, 200, 200, 
	200, 200, 200, 200, 200, 200, 200, 200, 
	200, 200, 200, 200, 200, 200, 200, 200, 
	200, 200, 200, 200, 200, 200, 200, 200, 
	200, 200, 200, 200, 200, 200, 200, 200, 
	200, 200, 200, 200, 200, 200, 200, 200, 
	200, 200, 200, 200, 200, 200, 194, 200, 
	202, 196, 203, 203, 203, 196, 203, 203, 
	203, 203, 197, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 196, 203, 203, 203, 203, 
	203, 197, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 198, 198, 198, 198, 198, 198, 198, 
	198, 198, 198, 203, 203, 203, 203, 203, 
	203, 203, 198, 198, 198, 198, 198, 198, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 198, 198, 198, 198, 198, 198, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 194, 203, 
	196, 203, 203, 203, 196, 203, 203, 203, 
	203, 194, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 196, 203, 203, 203, 203, 203, 
	194, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	200, 200, 200, 200, 200, 200, 200, 200, 
	200, 200, 203, 203, 203, 203, 203, 203, 
	203, 200, 200, 200, 200, 200, 200, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 200, 200, 200, 200, 200, 200, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 196, 203, 196, 
	203, 203, 203, 196, 203, 203, 203, 203, 
	204, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 196, 203, 203, 203, 203, 203, 204, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 205, 
	205, 205, 205, 205, 205, 205, 205, 205, 
	205, 203, 203, 203, 203, 203, 203, 203, 
	205, 205, 205, 205, 205, 205, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	205, 205, 205, 205, 205, 205, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 206, 203, 196, 203, 
	203, 203, 196, 203, 203, 203, 203, 204, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	196, 203, 203, 203, 203, 203, 204, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 198, 198, 
	198, 198, 198, 198, 198, 198, 198, 198, 
	203, 203, 203, 203, 203, 203, 203, 198, 
	198, 198, 198, 198, 198, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 198, 
	198, 198, 198, 198, 198, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 206, 203, 207, 196, 203, 
	203, 203, 196, 203, 203, 203, 203, 204, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	196, 203, 203, 203, 203, 203, 204, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 208, 208, 
	208, 208, 208, 208, 208, 208, 208, 208, 
	203, 203, 203, 203, 203, 203, 203, 208, 
	208, 208, 208, 208, 208, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 208, 
	208, 208, 208, 208, 208, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 206, 203, 196, 203, 203, 
	203, 196, 203, 203, 203, 203, 204, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 196, 
	203, 203, 203, 203, 203, 204, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 209, 209, 209, 
	209, 209, 209, 209, 209, 209, 209, 203, 
	203, 203, 203, 203, 203, 203, 209, 209, 
	209, 209, 209, 209, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 209, 209, 
	209, 209, 209, 209, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 206, 203, 196, 203, 203, 203, 
	196, 203, 203, 203, 203, 204, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 196, 203, 
	203, 203, 203, 203, 204, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 210, 210, 210, 210, 
	210, 210, 210, 210, 210, 210, 203, 203, 
	203, 203, 203, 203, 203, 210, 210, 210, 
	210, 210, 210, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 210, 210, 210, 
	210, 210, 210, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 206, 203, 196, 203, 203, 203, 196, 
	203, 203, 203, 203, 204, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 196, 203, 203, 
	203, 203, 203, 204, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 211, 211, 211, 211, 211, 
	211, 211, 211, 211, 211, 203, 203, 203, 
	203, 203, 203, 203, 211, 211, 211, 211, 
	211, 211, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 211, 211, 211, 211, 
	211, 211, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	206, 203, 196, 203, 203, 203, 196, 203, 
	203, 203, 203, 204, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 196, 203, 203, 203, 
	203, 203, 204, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 212, 212, 212, 212, 212, 212, 
	212, 212, 212, 212, 203, 203, 203, 203, 
	203, 203, 203, 212, 212, 212, 212, 212, 
	212, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 212, 212, 212, 212, 212, 
	212, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 206, 
	203, 196, 203, 203, 203, 196, 203, 203, 
	203, 203, 194, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 196, 203, 203, 203, 203, 
	203, 194, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 212, 212, 212, 212, 212, 212, 212, 
	212, 212, 212, 203, 203, 203, 203, 203, 
	203, 203, 212, 212, 212, 212, 212, 212, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 212, 212, 212, 212, 212, 212, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 203, 203, 
	203, 203, 203, 203, 203, 203, 194, 203, 
	213, 214, 216, 216, 216, 216, 216, 216, 
	216, 216, 216, 216, 215, 215, 215, 215, 
	215, 215, 215, 216, 216, 216, 216, 216, 
	216, 215, 215, 215, 215, 215, 215, 215, 
	215, 215, 215, 215, 215, 215, 215, 215, 
	215, 215, 215, 215, 215, 215, 215, 215, 
	215, 215, 215, 216, 216, 216, 216, 216, 
	216, 215, 215, 218, 217, 217, 217, 218, 
	217, 217, 217, 217, 217, 219, 217, 217, 
	217, 217, 217, 217, 217, 217, 217, 217, 
	217, 217, 217, 217, 217, 218, 217, 217, 
	217, 217, 217, 217, 217, 217, 220, 217, 
	217, 217, 217, 217, 217, 217, 217, 217, 
	217, 217, 217, 217, 217, 217, 217, 217, 
	217, 217, 217, 217, 217, 217, 217, 217, 
	217, 217, 217, 217, 217, 217, 217, 217, 
	217, 217, 217, 217, 217, 217, 217, 217, 
	217, 217, 217, 217, 217, 217, 217, 217, 
	217, 217, 217, 217, 217, 217, 217, 221, 
	217, 223, 222, 222, 222, 222, 222, 222, 
	222, 222, 222, 222, 222, 222, 222, 222, 
	222, 222, 222, 222, 222, 222, 222, 222, 
	222, 222, 222, 222, 222, 224, 222, 222, 
	222, 222, 222, 222, 222, 222, 222, 222, 
	222, 222, 222, 222, 222, 222, 222, 222, 
	222, 222, 222, 222, 222, 222, 222, 222, 
	222, 222, 222, 222, 222, 222, 222, 222, 
	222, 222, 222, 222, 222, 222, 222, 222, 
	222, 222, 222, 222, 222, 222, 222, 222, 
	222, 222, 222, 222, 222, 222, 222, 222, 
	225, 222, 226, 226, 226, 226, 226, 226, 
	226, 226, 226, 226, 226, 226, 226, 226, 
	226, 226, 226, 226, 226, 226, 226, 226, 
	226, 226, 226, 226, 226, 226, 226, 226, 
	226, 226, 226, 226, 226, 226, 226, 226, 
	226, 226, 226, 226, 226, 226, 226, 226, 
	226, 226, 39, 39, 39, 39, 39, 39, 
	39, 39, 39, 39, 226, 226, 226, 226, 
	226, 226, 226, 39, 39, 39, 39, 39, 
	39, 39, 39, 39, 39, 39, 39, 39, 
	39, 39, 39, 39, 39, 39, 39, 39, 
	39, 39, 39, 39, 39, 226, 226, 226, 
	226, 39, 226, 39, 39, 39, 39, 39, 
	39, 39, 39, 39, 39, 39, 39, 39, 
	39, 39, 39, 39, 39, 39, 39, 39, 
	39, 39, 39, 39, 39, 226, 226, 226, 
	226, 226, 39, 226, 42, 42, 42, 42, 
	42, 42, 42, 42, 42, 42, 226, 227, 
	227, 227, 227, 227, 227, 227, 227, 227, 
	227, 227, 227, 227, 227, 227, 227, 227, 
	227, 227, 227, 227, 227, 227, 227, 227, 
	227, 227, 227, 227, 227, 227, 227, 227, 
	227, 227, 227, 227, 227, 227, 227, 227, 
	227, 227, 227, 227, 227, 227, 227, 43, 
	43, 43, 43, 43, 43, 43, 43, 43, 
	43, 227, 227, 227, 227, 227, 227, 227, 
	43, 43, 43, 43, 43, 43, 43, 43, 
	43, 43, 43, 43, 43, 43, 43, 43, 
	43, 43, 43, 43, 43, 43, 43, 43, 
	43, 43, 227, 227, 227, 227, 43, 227, 
	43, 43, 43, 43, 43, 43, 43, 43, 
	43, 43, 43, 43, 43, 43, 43, 43, 
	43, 43, 43, 43, 43, 43, 43, 43, 
	43, 43, 227, 227, 227, 227, 227, 43, 
	228, 228, 228, 228, 228, 228, 228, 228, 
	228, 228, 228, 228, 228, 228, 228, 228, 
	228, 228, 228, 228, 228, 228, 228, 228, 
	228, 228, 228, 228, 228, 228, 228, 228, 
	228, 228, 228, 228, 228, 228, 228, 228, 
	228, 228, 228, 228, 228, 228, 228, 228, 
	43, 43, 43, 43, 43, 43, 43, 43, 
	43, 43, 228, 228, 228, 228, 228, 228, 
	228, 44, 44, 44, 44, 44, 44, 44, 
	44, 44, 44, 44, 44, 44, 44, 44, 
	44, 44, 44, 44, 44, 44, 44, 44, 
	44, 44, 44, 228, 228, 228, 228, 44, 
	228, 44, 44, 44, 44, 44, 44, 44, 
	44, 44, 44, 44, 44, 44, 44, 44, 
	44, 44, 44, 44, 44, 44, 44, 44, 
	44, 44, 44, 228, 228, 228, 228, 228, 
	44, 229, 229, 229, 229, 229, 229, 229, 
	229, 229, 229, 229, 229, 229, 229, 229, 
	229, 229, 229, 229, 229, 229, 229, 229, 
	229, 229, 229, 229, 229, 229, 229, 229, 
	229, 229, 229, 229, 229, 229, 229, 229, 
	229, 229, 229, 229, 229, 229, 229, 229, 
	229, 46, 46, 46, 46, 46, 46, 46, 
	46, 46, 46, 229, 229, 229, 229, 229, 
	229, 229, 46, 46, 46, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	46, 46, 46, 46, 229, 229, 229, 229, 
	46, 229, 46, 46, 46, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	46, 46, 46, 46, 229, 229, 229, 229, 
	229, 46, 230, 230, 230, 230, 230, 230, 
	230, 230, 230, 230, 230, 230, 230, 230, 
	230, 230, 230, 230, 230, 230, 230, 230, 
	230, 230, 230, 230, 230, 230, 230, 230, 
	230, 230, 230, 230, 230, 230, 230, 230, 
	230, 230, 230, 230, 230, 230, 230, 230, 
	230, 230, 46, 46, 46, 46, 46, 46, 
	46, 46, 46, 46, 230, 230, 230, 230, 
	230, 230, 230, 47, 47, 47, 47, 47, 
	47, 47, 47, 47, 47, 47, 47, 47, 
	47, 47, 47, 47, 47, 47, 47, 47, 
	47, 47, 47, 47, 47, 230, 230, 230, 
	230, 47, 230, 47, 47, 47, 47, 47, 
	47, 47, 47, 47, 47, 47, 47, 47, 
	47, 47, 47, 47, 47, 47, 47, 47, 
	47, 47, 47, 47, 47, 230, 230, 230, 
	230, 230, 47, 233, 232, 232, 232, 233, 
	232, 232, 232, 232, 232, 232, 232, 232, 
	232, 232, 232, 232, 232, 232, 232, 232, 
	232, 232, 232, 232, 232, 233, 232, 232, 
	232, 232, 232, 232, 232, 232, 232, 232, 
	232, 232, 232, 232, 232, 232, 232, 232, 
	232, 232, 232, 234, 234, 234, 234, 234, 
	234, 234, 234, 232, 232, 232, 232, 232, 
	232, 232, 232, 232, 232, 232, 235, 232, 
	232, 232, 232, 232, 232, 232, 232, 232, 
	236, 232, 232, 232, 232, 232, 232, 232, 
	232, 232, 232, 232, 232, 232, 232, 232, 
	232, 232, 232, 232, 232, 232, 237, 232, 
	232, 232, 232, 232, 232, 232, 232, 232, 
	232, 232, 232, 232, 232, 232, 232, 232, 
	238, 232, 232, 239, 232, 240, 241, 243, 
	243, 243, 243, 243, 243, 243, 243, 242, 
	244, 244, 244, 244, 244, 244, 244, 244, 
	242, 242, 245, 245, 71, 71, 71, 245, 
	71, 71, 71, 71, 71, 71, 71, 71, 
	71, 71, 71, 71, 71, 71, 71, 71, 
	71, 71, 71, 71, 71, 245, 71, 71, 
	71, 71, 71, 71, 71, 71, 71, 71, 
	71, 71, 71, 71, 71, 71, 71, 71, 
	71, 71, 71, 71, 71, 71, 71, 71, 
	71, 71, 71, 71, 71, 71, 71, 71, 
	71, 71, 72, 71, 71, 71, 71, 71, 
	71, 71, 71, 71, 71, 71, 71, 71, 
	71, 71, 71, 71, 71, 71, 71, 71, 
	71, 71, 71, 71, 71, 71, 71, 246, 
	71, 247, 248, 249, 249, 71, 71, 71, 
	249, 71, 71, 71, 71, 71, 71, 71, 
	71, 71, 71, 71, 71, 71, 71, 71, 
	71, 71, 71, 71, 71, 71, 249, 71, 
	71, 71, 71, 71, 71, 71, 71, 71, 
	71, 71, 71, 71, 71, 71, 71, 71, 
	71, 71, 71, 71, 71, 71, 71, 71, 
	71, 71, 71, 71, 71, 71, 71, 71, 
	71, 71, 71, 72, 71, 71, 71, 71, 
	71, 71, 71, 71, 71, 71, 71, 71, 
	71, 71, 71, 71, 71, 71, 71, 71, 
	71, 71, 71, 71, 71, 71, 71, 71, 
	250, 71, 70, 249, 251, 252, 253, 253, 
	56, 56, 56, 253, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 253, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 57, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 254, 56, 255, 255, 255, 
	255, 255, 255, 255, 255, 255, 255, 253, 
	253, 253, 253, 253, 253, 253, 255, 255, 
	255, 255, 255, 255, 253, 253, 253, 253, 
	253, 253, 253, 253, 253, 253, 253, 253, 
	253, 253, 253, 253, 253, 253, 253, 253, 
	253, 253, 253, 253, 253, 253, 255, 255, 
	255, 255, 255, 255, 253, 257, 257, 257, 
	257, 257, 257, 257, 257, 257, 257, 256, 
	256, 256, 256, 256, 256, 256, 257, 257, 
	257, 257, 257, 257, 256, 256, 256, 256, 
	256, 256, 256, 256, 256, 256, 256, 256, 
	256, 256, 256, 256, 256, 256, 256, 256, 
	256, 256, 256, 256, 256, 256, 257, 257, 
	257, 257, 257, 257, 256, 256, 258, 258, 
	258, 258, 258, 258, 258, 258, 258, 258, 
	249, 249, 249, 249, 249, 249, 249, 258, 
	258, 258, 258, 258, 258, 249, 249, 249, 
	249, 249, 249, 249, 249, 249, 249, 249, 
	249, 249, 249, 249, 249, 249, 249, 249, 
	249, 249, 249, 249, 249, 249, 249, 258, 
	258, 258, 258, 258, 258, 249, 260, 260, 
	260, 260, 260, 260, 260, 260, 260, 260, 
	259, 259, 259, 259, 259, 259, 259, 260, 
	260, 260, 260, 260, 260, 259, 259, 259, 
	259, 259, 259, 259, 259, 259, 259, 259, 
	259, 259, 259, 259, 259, 259, 259, 259, 
	259, 259, 259, 259, 259, 259, 259, 260, 
	260, 260, 260, 260, 260, 259, 259, 249, 
	49, 49, 49, 249, 49, 49, 49, 49, 
	49, 49, 49, 49, 49, 49, 49, 49, 
	49, 49, 49, 49, 49, 49, 49, 49, 
	49, 249, 49, 49, 49, 49, 49, 49, 
	49, 49, 49, 49, 49, 49, 49, 49, 
	49, 49, 49, 49, 261, 49, 245, 56, 
	56, 56, 245, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	245, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 57, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 58, 56, 245, 262, 262, 262, 
	245, 262, 262, 262, 262, 262, 262, 262, 
	262, 262, 262, 262, 262, 262, 262, 262, 
	262, 262, 262, 262, 262, 262, 245, 262, 
	262, 262, 262, 262, 262, 262, 262, 262, 
	262, 262, 262, 262, 262, 262, 262, 262, 
	262, 262, 262, 262, 262, 262, 262, 262, 
	262, 262, 262, 262, 262, 262, 262, 262, 
	262, 262, 262, 262, 262, 262, 262, 262, 
	262, 262, 262, 262, 262, 262, 262, 262, 
	262, 262, 262, 262, 262, 262, 262, 262, 
	262, 262, 262, 262, 262, 262, 262, 262, 
	263, 262, 264, 265, 265, 262, 262, 262, 
	265, 262, 262, 262, 262, 262, 262, 262, 
	262, 262, 262, 262, 262, 262, 262, 262, 
	262, 262, 262, 262, 262, 262, 265, 262, 
	262, 262, 262, 262, 262, 262, 262, 262, 
	262, 262, 262, 262, 262, 262, 262, 262, 
	262, 262, 262, 262, 262, 262, 262, 262, 
	262, 262, 262, 262, 262, 262, 262, 262, 
	262, 262, 262, 262, 262, 262, 262, 262, 
	262, 262, 262, 262, 262, 262, 262, 262, 
	262, 262, 262, 262, 262, 262, 262, 262, 
	262, 262, 262, 262, 262, 262, 262, 262, 
	266, 262, 267, 267, 267, 267, 267, 267, 
	267, 267, 267, 267, 265, 265, 265, 265, 
	265, 265, 265, 267, 267, 267, 267, 267, 
	267, 265, 265, 265, 265, 265, 265, 265, 
	265, 265, 265, 265, 265, 265, 265, 265, 
	265, 265, 265, 265, 265, 265, 265, 265, 
	265, 265, 265, 267, 267, 267, 267, 267, 
	267, 265, 269, 269, 269, 269, 269, 269, 
	269, 269, 269, 269, 268, 268, 268, 268, 
	268, 268, 268, 269, 269, 269, 269, 269, 
	269, 268, 268, 268, 268, 268, 268, 268, 
	268, 268, 268, 268, 268, 268, 268, 268, 
	268, 268, 268, 268, 268, 268, 268, 268, 
	268, 268, 268, 269, 269, 269, 269, 269, 
	269, 268, 268, 265, 49, 49, 49, 265, 
	49, 49, 49, 49, 49, 49, 49, 49, 
	49, 49, 49, 49, 49, 49, 49, 49, 
	49, 49, 49, 49, 49, 265, 49, 49, 
	49, 49, 49, 49, 49, 49, 49, 49, 
	49, 49, 49, 49, 49, 49, 49, 49, 
	261, 49, 270, 265, 265, 56, 56, 56, 
	265, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 265, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 57, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	56, 56, 56, 56, 56, 56, 56, 56, 
	58, 56, 272, 272, 272, 272, 272, 272, 
	272, 272, 272, 272, 271, 271, 271, 271, 
	271, 271, 271, 272, 272, 272, 272, 272, 
	272, 271, 271, 271, 271, 271, 271, 271, 
	271, 271, 271, 271, 271, 271, 271, 271, 
	271, 271, 271, 271, 271, 271, 271, 271, 
	271, 271, 271, 272, 272, 272, 272, 272, 
	272, 271, 271, 271, 271, 271, 271, 271, 
	271, 271, 271, 271, 271, 271, 271, 271, 
	271, 271, 271, 271, 271, 273, 271, 274, 
	274, 274, 274, 274, 274, 274, 274, 274, 
	274, 271, 271, 271, 271, 271, 271, 271, 
	274, 274, 274, 274, 274, 274, 271, 271, 
	271, 271, 271, 271, 271, 271, 271, 271, 
	271, 271, 271, 271, 271, 271, 271, 271, 
	271, 271, 271, 271, 271, 271, 271, 271, 
	274, 274, 274, 274, 274, 274, 271, 275, 
	275, 275, 275, 275, 275, 275, 275, 275, 
	275, 271, 271, 271, 271, 271, 271, 271, 
	275, 275, 275, 275, 275, 275, 271, 271, 
	271, 271, 271, 271, 271, 271, 271, 271, 
	271, 271, 271, 271, 271, 271, 271, 271, 
	271, 271, 271, 271, 271, 271, 271, 271, 
	275, 275, 275, 275, 275, 275, 271, 276, 
	276, 276, 276, 276, 276, 276, 276, 276, 
	276, 271, 271, 271, 271, 271, 271, 271, 
	276, 276, 276, 276, 276, 276, 271, 271, 
	271, 271, 271, 271, 271, 271, 271, 271, 
	271, 271, 271, 271, 271, 271, 271, 271, 
	271, 271, 271, 271, 271, 271, 271, 271, 
	276, 276, 276, 276, 276, 276, 271, 277, 
	280, 279, 279, 279, 280, 279, 279, 279, 
	279, 281, 279, 279, 279, 279, 279, 279, 
	279, 279, 279, 279, 279, 279, 279, 279, 
	279, 279, 280, 279, 279, 279, 279, 279, 
	281, 279, 279, 279, 279, 279, 279, 279, 
	279, 279, 279, 279, 279, 279, 279, 279, 
	282, 282, 282, 282, 282, 282, 282, 282, 
	282, 282, 279, 279, 279, 279, 279, 279, 
	279, 282, 282, 282, 282, 282, 282, 279, 
	279, 279, 279, 279, 279, 279, 279, 279, 
	279, 279, 279, 279, 279, 279, 279, 279, 
	279, 279, 279, 279, 279, 279, 279, 279, 
	279, 282, 282, 282, 282, 282, 282, 279, 
	279, 279, 279, 279, 279, 279, 279, 279, 
	279, 279, 279, 279, 279, 279, 279, 279, 
	279, 279, 279, 279, 279, 283, 279, 280, 
	279, 279, 279, 280, 279, 279, 279, 279, 
	278, 279, 279, 279, 279, 279, 279, 279, 
	279, 279, 279, 279, 279, 279, 279, 279, 
	279, 280, 279, 279, 279, 279, 279, 278, 
	279, 279, 279, 279, 279, 279, 279, 279, 
	279, 279, 279, 279, 279, 279, 279, 284, 
	284, 284, 284, 284, 284, 284, 284, 284, 
	284, 279, 279, 279, 279, 279, 279, 279, 
	284, 284, 284, 284, 284, 284, 279, 279, 
	279, 279, 279, 279, 279, 279, 279, 279, 
	279, 279, 279, 279, 279, 279, 279, 279, 
	279, 279, 279, 279, 279, 279, 279, 279, 
	284, 284, 284, 284, 284, 284, 279, 279, 
	279, 279, 279, 279, 279, 279, 279, 279, 
	279, 279, 279, 279, 279, 279, 279, 279, 
	279, 279, 279, 279, 285, 279, 278, 280, 
	284, 284, 284, 280, 284, 284, 284, 284, 
	278, 284, 284, 284, 284, 284, 284, 284, 
	284, 284, 284, 284, 284, 284, 284, 284, 
	284, 280, 284, 284, 284, 284, 284, 278, 
	284, 284, 284, 284, 284, 284, 284, 284, 
	284, 284, 284, 284, 284, 284, 284, 284, 
	284, 284, 284, 284, 284, 284, 284, 284, 
	284, 284, 284, 284, 284, 284, 284, 284, 
	284, 284, 284, 284, 284, 284, 284, 284, 
	284, 284, 284, 284, 284, 284, 284, 284, 
	284, 284, 284, 284, 284, 284, 284, 284, 
	284, 284, 284, 284, 284, 284, 284, 284, 
	284, 284, 284, 284, 284, 284, 284, 284, 
	284, 284, 284, 284, 284, 284, 284, 284, 
	284, 284, 284, 284, 284, 284, 284, 284, 
	284, 284, 284, 284, 278, 284, 286, 280, 
	287, 287, 287, 280, 287, 287, 287, 287, 
	281, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 280, 287, 287, 287, 287, 287, 281, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 282, 
	282, 282, 282, 282, 282, 282, 282, 282, 
	282, 287, 287, 287, 287, 287, 287, 287, 
	282, 282, 282, 282, 282, 282, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	282, 282, 282, 282, 282, 282, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 278, 287, 280, 287, 
	287, 287, 280, 287, 287, 287, 287, 278, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	280, 287, 287, 287, 287, 287, 278, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 284, 284, 
	284, 284, 284, 284, 284, 284, 284, 284, 
	287, 287, 287, 287, 287, 287, 287, 284, 
	284, 284, 284, 284, 284, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 284, 
	284, 284, 284, 284, 284, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 280, 287, 280, 287, 287, 
	287, 280, 287, 287, 287, 287, 288, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 280, 
	287, 287, 287, 287, 287, 288, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 289, 289, 289, 
	289, 289, 289, 289, 289, 289, 289, 287, 
	287, 287, 287, 287, 287, 287, 289, 289, 
	289, 289, 289, 289, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 289, 289, 
	289, 289, 289, 289, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 290, 287, 280, 287, 287, 287, 
	280, 287, 287, 287, 287, 288, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 280, 287, 
	287, 287, 287, 287, 288, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 282, 282, 282, 282, 
	282, 282, 282, 282, 282, 282, 287, 287, 
	287, 287, 287, 287, 287, 282, 282, 282, 
	282, 282, 282, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 282, 282, 282, 
	282, 282, 282, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 290, 287, 291, 280, 287, 287, 287, 
	280, 287, 287, 287, 287, 288, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 280, 287, 
	287, 287, 287, 287, 288, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 292, 292, 292, 292, 
	292, 292, 292, 292, 292, 292, 287, 287, 
	287, 287, 287, 287, 287, 292, 292, 292, 
	292, 292, 292, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 292, 292, 292, 
	292, 292, 292, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 290, 287, 280, 287, 287, 287, 280, 
	287, 287, 287, 287, 288, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 280, 287, 287, 
	287, 287, 287, 288, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 293, 293, 293, 293, 293, 
	293, 293, 293, 293, 293, 287, 287, 287, 
	287, 287, 287, 287, 293, 293, 293, 293, 
	293, 293, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 293, 293, 293, 293, 
	293, 293, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	290, 287, 280, 287, 287, 287, 280, 287, 
	287, 287, 287, 288, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 280, 287, 287, 287, 
	287, 287, 288, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 294, 294, 294, 294, 294, 294, 
	294, 294, 294, 294, 287, 287, 287, 287, 
	287, 287, 287, 294, 294, 294, 294, 294, 
	294, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 294, 294, 294, 294, 294, 
	294, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 290, 
	287, 280, 287, 287, 287, 280, 287, 287, 
	287, 287, 288, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 280, 287, 287, 287, 287, 
	287, 288, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 295, 295, 295, 295, 295, 295, 295, 
	295, 295, 295, 287, 287, 287, 287, 287, 
	287, 287, 295, 295, 295, 295, 295, 295, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 295, 295, 295, 295, 295, 295, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 290, 287, 
	280, 287, 287, 287, 280, 287, 287, 287, 
	287, 288, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 280, 287, 287, 287, 287, 287, 
	288, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	296, 296, 296, 296, 296, 296, 296, 296, 
	296, 296, 287, 287, 287, 287, 287, 287, 
	287, 296, 296, 296, 296, 296, 296, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 296, 296, 296, 296, 296, 296, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 290, 287, 280, 
	287, 287, 287, 280, 287, 287, 287, 287, 
	278, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 280, 287, 287, 287, 287, 287, 278, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 296, 
	296, 296, 296, 296, 296, 296, 296, 296, 
	296, 287, 287, 287, 287, 287, 287, 287, 
	296, 296, 296, 296, 296, 296, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	296, 296, 296, 296, 296, 296, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 287, 287, 287, 287, 
	287, 287, 287, 287, 278, 287, 297, 298, 
	300, 300, 300, 300, 300, 300, 300, 300, 
	300, 300, 299, 299, 299, 299, 299, 299, 
	299, 300, 300, 300, 300, 300, 300, 299, 
	299, 299, 299, 299, 299, 299, 299, 299, 
	299, 299, 299, 299, 299, 299, 299, 299, 
	299, 299, 299, 299, 299, 299, 299, 299, 
	299, 300, 300, 300, 300, 300, 300, 299, 
	299, 302, 301, 301, 301, 302, 301, 301, 
	301, 301, 303, 304, 303, 303, 303, 301, 
	301, 301, 301, 301, 301, 301, 301, 301, 
	301, 301, 301, 302, 301, 301, 301, 301, 
	301, 303, 301, 301, 301, 301, 301, 301, 
	301, 301, 301, 301, 301, 301, 301, 301, 
	301, 301, 301, 301, 301, 301, 301, 301, 
	301, 301, 301, 301, 301, 301, 301, 301, 
	301, 301, 301, 301, 301, 301, 301, 301, 
	301, 301, 301, 301, 301, 301, 301, 301, 
	301, 301, 301, 301, 301, 301, 301, 301, 
	301, 301, 301, 301, 301, 305, 301, 303, 
	306, 303, 303, 303, 306, 306, 306, 306, 
	306, 306, 306, 306, 306, 306, 306, 306, 
	306, 306, 306, 306, 306, 306, 303, 306, 
	307, 308, 308, 308, 307, 308, 308, 308, 
	308, 308, 308, 308, 308, 308, 308, 308, 
	308, 308, 308, 308, 308, 308, 308, 308, 
	308, 308, 307, 308, 310, 309, 309, 309, 
	310, 309, 309, 309, 309, 309, 311, 309, 
	309, 309, 309, 309, 309, 309, 309, 309, 
	309, 309, 309, 309, 309, 309, 310, 309, 
	309, 309, 309, 309, 309, 309, 309, 309, 
	309, 309, 309, 309, 309, 309, 309, 309, 
	309, 309, 309, 309, 309, 309, 309, 309, 
	309, 309, 309, 309, 309, 309, 309, 309, 
	309, 309, 309, 309, 309, 309, 309, 309, 
	309, 309, 309, 309, 309, 309, 309, 309, 
	309, 309, 309, 309, 309, 309, 309, 309, 
	309, 309, 309, 309, 309, 309, 309, 309, 
	312, 309, 313, 314, 314, 314, 313, 314, 
	314, 314, 314, 314, 315, 314, 314, 314, 
	314, 314, 314, 314, 314, 314, 314, 314, 
	314, 314, 314, 314, 313, 314, 317, 316, 
	316, 316, 317, 316, 316, 316, 316, 316, 
	318, 316, 316, 316, 316, 316, 316, 316, 
	316, 316, 316, 316, 316, 316, 316, 316, 
	317, 316, 316, 316, 316, 316, 316, 316, 
	316, 319, 316, 321, 320, 320, 320, 320, 
	320, 320, 320, 320, 320, 320, 320, 320, 
	320, 320, 320, 320, 320, 320, 320, 320, 
	320, 320, 320, 320, 320, 320, 320, 322, 
	320, 320, 320, 320, 320, 320, 320, 320, 
	320, 320, 320, 320, 320, 320, 320, 320, 
	320, 320, 320, 320, 320, 320, 320, 320, 
	320, 320, 320, 320, 320, 320, 320, 320, 
	320, 320, 320, 320, 320, 320, 320, 320, 
	320, 320, 320, 320, 320, 320, 320, 320, 
	320, 320, 320, 320, 320, 320, 320, 320, 
	320, 320, 323, 320, 324, 324, 324, 324, 
	324, 324, 324, 324, 324, 324, 324, 324, 
	324, 324, 324, 324, 324, 324, 324, 324, 
	324, 324, 324, 324, 324, 324, 324, 324, 
	324, 324, 324, 324, 324, 324, 324, 324, 
	324, 324, 324, 324, 324, 324, 324, 324, 
	324, 324, 324, 324, 77, 77, 77, 77, 
	77, 77, 77, 77, 77, 77, 324, 324, 
	324, 324, 324, 324, 324, 77, 77, 77, 
	77, 77, 77, 77, 77, 77, 77, 77, 
	77, 77, 77, 77, 77, 77, 77, 77, 
	77, 77, 77, 77, 77, 77, 77, 324, 
	324, 324, 324, 77, 324, 77, 77, 77, 
	77, 77, 77, 77, 77, 77, 77, 77, 
	77, 77, 77, 77, 77, 77, 77, 77, 
	77, 77, 77, 77, 77, 77, 77, 324, 
	324, 324, 324, 324, 77, 324, 80, 80, 
	80, 80, 80, 80, 80, 80, 80, 80, 
	324, 325, 325, 325, 325, 325, 325, 325, 
	325, 325, 325, 325, 325, 325, 325, 325, 
	325, 325, 325, 325, 325, 325, 325, 325, 
	325, 325, 325, 325, 325, 325, 325, 325, 
	325, 325, 325, 325, 325, 325, 325, 325, 
	325, 325, 325, 325, 325, 325, 325, 325, 
	325, 81, 81, 81, 81, 81, 81, 81, 
	81, 81, 81, 325, 325, 325, 325, 325, 
	325, 325, 81, 81, 81, 81, 81, 81, 
	81, 81, 81, 81, 81, 81, 81, 81, 
	81, 81, 81, 81, 81, 81, 81, 81, 
	81, 81, 81, 81, 325, 325, 325, 325, 
	81, 325, 81, 81, 81, 81, 81, 81, 
	81, 81, 81, 81, 81, 81, 81, 81, 
	81, 81, 81, 81, 81, 81, 81, 81, 
	81, 81, 81, 81, 325, 325, 325, 325, 
	325, 81, 326, 326, 326, 326, 326, 326, 
	326, 326, 326, 326, 326, 326, 326, 326, 
	326, 326, 326, 326, 326, 326, 326, 326, 
	326, 326, 326, 326, 326, 326, 326, 326, 
	326, 326, 326, 326, 326, 326, 326, 326, 
	326, 326, 326, 326, 326, 326, 326, 326, 
	326, 326, 81, 81, 81, 81, 81, 81, 
	81, 81, 81, 81, 326, 326, 326, 326, 
	326, 326, 326, 82, 82, 82, 82, 82, 
	82, 82, 82, 82, 82, 82, 82, 82, 
	82, 82, 82, 82, 82, 82, 82, 82, 
	82, 82, 82, 82, 82, 326, 326, 326, 
	326, 82, 326, 82, 82, 82, 82, 82, 
	82, 82, 82, 82, 82, 82, 82, 82, 
	82, 82, 82, 82, 82, 82, 82, 82, 
	82, 82, 82, 82, 82, 326, 326, 326, 
	326, 326, 82, 327, 327, 327, 327, 327, 
	327, 327, 327, 327, 327, 327, 327, 327, 
	327, 327, 327, 327, 327, 327, 327, 327, 
	327, 327, 327, 327, 327, 327, 327, 327, 
	327, 327, 327, 327, 327, 327, 327, 327, 
	327, 327, 327, 327, 327, 327, 327, 327, 
	327, 327, 327, 84, 84, 84, 84, 84, 
	84, 84, 84, 84, 84, 327, 327, 327, 
	327, 327, 327, 327, 84, 84, 84, 84, 
	84, 84, 84, 84, 84, 84, 84, 84, 
	84, 84, 84, 84, 84, 84, 84, 84, 
	84, 84, 84, 84, 84, 84, 327, 327, 
	327, 327, 84, 327, 84, 84, 84, 84, 
	84, 84, 84, 84, 84, 84, 84, 84, 
	84, 84, 84, 84, 84, 84, 84, 84, 
	84, 84, 84, 84, 84, 84, 327, 327, 
	327, 327, 327, 84, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 84, 84, 84, 84, 
	84, 84, 84, 84, 84, 84, 328, 328, 
	328, 328, 328, 328, 328, 85, 85, 85, 
	85, 85, 85, 85, 85, 85, 85, 85, 
	85, 85, 85, 85, 85, 85, 85, 85, 
	85, 85, 85, 85, 85, 85, 85, 328, 
	328, 328, 328, 85, 328, 85, 85, 85, 
	85, 85, 85, 85, 85, 85, 85, 85, 
	85, 85, 85, 85, 85, 85, 85, 85, 
	85, 85, 85, 85, 85, 85, 85, 328, 
	328, 328, 328, 328, 85, 330, 329, 329, 
	329, 330, 329, 329, 329, 329, 329, 331, 
	329, 329, 329, 329, 329, 329, 329, 329, 
	329, 329, 329, 329, 329, 329, 329, 330, 
	329, 333, 332, 332, 332, 333, 332, 332, 
	332, 332, 334, 335, 334, 334, 334, 332, 
	332, 332, 332, 332, 332, 332, 332, 332, 
	332, 332, 332, 333, 332, 332, 332, 332, 
	332, 334, 332, 332, 336, 332, 334, 337, 
	334, 334, 334, 337, 337, 337, 337, 337, 
	337, 337, 337, 337, 337, 337, 337, 337, 
	337, 337, 337, 337, 337, 334, 337, 339, 
	338, 338, 338, 338, 338, 338, 338, 338, 
	338, 338, 338, 338, 338, 338, 338, 338, 
	338, 338, 338, 338, 338, 338, 338, 338, 
	338, 338, 338, 340, 338, 338, 338, 338, 
	338, 338, 338, 338, 338, 338, 338, 338, 
	338, 338, 338, 338, 338, 338, 338, 338, 
	338, 338, 338, 338, 338, 338, 338, 338, 
	338, 338, 338, 338, 338, 338, 338, 338, 
	338, 338, 338, 338, 338, 338, 338, 338, 
	338, 338, 338, 338, 338, 338, 338, 338, 
	338, 338, 338, 338, 338, 338, 341, 338, 
	342, 342, 342, 342, 342, 342, 342, 342, 
	342, 342, 342, 342, 342, 342, 342, 342, 
	342, 342, 342, 342, 342, 342, 342, 342, 
	342, 342, 342, 342, 342, 342, 342, 342, 
	342, 342, 342, 342, 342, 342, 342, 342, 
	342, 342, 342, 342, 342, 342, 342, 342, 
	87, 87, 87, 87, 87, 87, 87, 87, 
	87, 87, 342, 342, 342, 342, 342, 342, 
	342, 87, 87, 87, 87, 87, 87, 87, 
	87, 87, 87, 87, 87, 87, 87, 87, 
	87, 87, 87, 87, 87, 87, 87, 87, 
	87, 87, 87, 342, 342, 342, 342, 87, 
	342, 87, 87, 87, 87, 87, 87, 87, 
	87, 87, 87, 87, 87, 87, 87, 87, 
	87, 87, 87, 87, 87, 87, 87, 87, 
	87, 87, 87, 342, 342, 342, 342, 342, 
	87, 342, 90, 90, 90, 90, 90, 90, 
	90, 90, 90, 90, 342, 343, 343, 343, 
	343, 343, 343, 343, 343, 343, 343, 343, 
	343, 343, 343, 343, 343, 343, 343, 343, 
	343, 343, 343, 343, 343, 343, 343, 343, 
	343, 343, 343, 343, 343, 343, 343, 343, 
	343, 343, 343, 343, 343, 343, 343, 343, 
	343, 343, 343, 343, 343, 91, 91, 91, 
	91, 91, 91, 91, 91, 91, 91, 343, 
	343, 343, 343, 343, 343, 343, 91, 91, 
	91, 91, 91, 91, 91, 91, 91, 91, 
	91, 91, 91, 91, 91, 91, 91, 91, 
	91, 91, 91, 91, 91, 91, 91, 91, 
	343, 343, 343, 343, 91, 343, 91, 91, 
	91, 91, 91, 91, 91, 91, 91, 91, 
	91, 91, 91, 91, 91, 91, 91, 91, 
	91, 91, 91, 91, 91, 91, 91, 91, 
	343, 343, 343, 343, 343, 91, 344, 344, 
	344, 344, 344, 344, 344, 344, 344, 344, 
	344, 344, 344, 344, 344, 344, 344, 344, 
	344, 344, 344, 344, 344, 344, 344, 344, 
	344, 344, 344, 344, 344, 344, 344, 344, 
	344, 344, 344, 344, 344, 344, 344, 344, 
	344, 344, 344, 344, 344, 344, 91, 91, 
	91, 91, 91, 91, 91, 91, 91, 91, 
	344, 344, 344, 344, 344, 344, 344, 92, 
	92, 92, 92, 92, 92, 92, 92, 92, 
	92, 92, 92, 92, 92, 92, 92, 92, 
	92, 92, 92, 92, 92, 92, 92, 92, 
	92, 344, 344, 344, 344, 92, 344, 92, 
	92, 92, 92, 92, 92, 92, 92, 92, 
	92, 92, 92, 92, 92, 92, 92, 92, 
	92, 92, 92, 92, 92, 92, 92, 92, 
	92, 344, 344, 344, 344, 344, 92, 345, 
	345, 345, 345, 345, 345, 345, 345, 345, 
	345, 345, 345, 345, 345, 345, 345, 345, 
	345, 345, 345, 345, 345, 345, 345, 345, 
	345, 345, 345, 345, 345, 345, 345, 345, 
	345, 345, 345, 345, 345, 345, 345, 345, 
	345, 345, 345, 345, 345, 345, 345, 94, 
	94, 94, 94, 94, 94, 94, 94, 94, 
	94, 345, 345, 345, 345, 345, 345, 345, 
	94, 94, 94, 94, 94, 94, 94, 94, 
	94, 94, 94, 94, 94, 94, 94, 94, 
	94, 94, 94, 94, 94, 94, 94, 94, 
	94, 94, 345, 345, 345, 345, 94, 345, 
	94, 94, 94, 94, 94, 94, 94, 94, 
	94, 94, 94, 94, 94, 94, 94, 94, 
	94, 94, 94, 94, 94, 94, 94, 94, 
	94, 94, 345, 345, 345, 345, 345, 94, 
	346, 346, 346, 346, 346, 346, 346, 346, 
	346, 346, 346, 346, 346, 346, 346, 346, 
	346, 346, 346, 346, 346, 346, 346, 346, 
	346, 346, 346, 346, 346, 346, 346, 346, 
	346, 346, 346, 346, 346, 346, 346, 346, 
	346, 346, 346, 346, 346, 346, 346, 346, 
	94, 94, 94, 94, 94, 94, 94, 94, 
	94, 94, 346, 346, 346, 346, 346, 346, 
	346, 95, 95, 95, 95, 95, 95, 95, 
	95, 95, 95, 95, 95, 95, 95, 95, 
	95, 95, 95, 95, 95, 95, 95, 95, 
	95, 95, 95, 346, 346, 346, 346, 95, 
	346, 95, 95, 95, 95, 95, 95, 95, 
	95, 95, 95, 95, 95, 95, 95, 95, 
	95, 95, 95, 95, 95, 95, 95, 95, 
	95, 95, 95, 346, 346, 346, 346, 346, 
	95, 348, 347, 347, 347, 348, 347, 347, 
	347, 347, 349, 350, 349, 349, 349, 347, 
	347, 347, 347, 347, 347, 347, 347, 347, 
	347, 347, 347, 348, 347, 347, 347, 347, 
	347, 349, 347, 349, 351, 349, 349, 349, 
	351, 351, 351, 351, 351, 351, 351, 351, 
	351, 351, 351, 351, 351, 351, 351, 351, 
	351, 351, 349, 351, 353, 353, 353, 353, 
	353, 353, 353, 353, 353, 353, 353, 353, 
	353, 353, 353, 353, 353, 353, 353, 353, 
	353, 353, 353, 353, 353, 353, 352, 352, 
	352, 352, 352, 352, 353, 353, 353, 353, 
	353, 353, 353, 353, 353, 353, 353, 353, 
	353, 353, 353, 353, 353, 353, 353, 353, 
	353, 353, 353, 353, 353, 353, 352, 353, 
	353, 353, 353, 353, 353, 353, 353, 353, 
	353, 353, 353, 353, 353, 353, 353, 353, 
	353, 353, 353, 353, 353, 353, 353, 353, 
	353, 354, 354, 354, 354, 354, 354, 353, 
	353, 353, 353, 353, 353, 353, 353, 353, 
	353, 353, 353, 353, 353, 353, 353, 353, 
	353, 353, 353, 353, 353, 353, 353, 353, 
	353, 354, 355, 97, 356, 356, 356, 356, 
	356, 356, 356, 356, 356, 356, 356, 356, 
	356, 356, 356, 356, 356, 356, 356, 356, 
	356, 356, 356, 356, 356, 356, 356, 356, 
	356, 356, 356, 356, 356, 356, 356, 356, 
	356, 356, 356, 356, 356, 356, 356, 356, 
	356, 356, 356, 356, 356, 356, 356, 356, 
	356, 356, 356, 356, 356, 356, 356, 356, 
	356, 356, 356, 356, 356, 356, 357, 357, 
	357, 357, 357, 357, 357, 357, 357, 357, 
	357, 357, 357, 357, 357, 357, 357, 357, 
	357, 357, 357, 357, 357, 357, 357, 357, 
	356, 356, 356, 356, 357, 356, 357, 357, 
	357, 357, 357, 357, 357, 357, 357, 357, 
	357, 357, 357, 357, 357, 357, 357, 357, 
	357, 357, 357, 357, 357, 357, 357, 357, 
	356, 356, 356, 356, 356, 357, 358, 358, 
	358, 358, 358, 358, 358, 358, 358, 358, 
	358, 358, 358, 358, 358, 358, 358, 358, 
	358, 358, 358, 358, 358, 358, 358, 358, 
	358, 358, 358, 358, 358, 358, 358, 358, 
	358, 358, 358, 358, 358, 358, 358, 358, 
	358, 358, 358, 358, 358, 358, 359, 359, 
	359, 359, 359, 359, 359, 359, 359, 359, 
	358, 358, 358, 358, 358, 358, 358, 359, 
	359, 359, 359, 359, 359, 359, 359, 359, 
	359, 359, 359, 359, 359, 359, 359, 359, 
	359, 359, 359, 359, 359, 359, 359, 359, 
	359, 358, 358, 358, 358, 359, 358, 359, 
	359, 359, 359, 359, 359, 359, 359, 359, 
	359, 359, 359, 359, 359, 359, 359, 359, 
	359, 359, 359, 359, 359, 359, 359, 359, 
	359, 358, 358, 358, 358, 358, 359, 362, 
	361, 361, 361, 362, 361, 361, 361, 361, 
	361, 361, 361, 361, 361, 361, 361, 361, 
	361, 361, 361, 361, 361, 361, 361, 361, 
	361, 362, 361, 361, 361, 361, 361, 361, 
	361, 361, 361, 361, 361, 361, 361, 361, 
	361, 361, 361, 361, 361, 361, 361, 363, 
	363, 363, 363, 363, 363, 363, 363, 361, 
	361, 361, 361, 361, 361, 361, 361, 361, 
	361, 361, 364, 361, 361, 361, 361, 361, 
	361, 361, 361, 361, 365, 361, 361, 361, 
	361, 361, 361, 361, 361, 361, 361, 361, 
	361, 361, 361, 361, 361, 361, 361, 361, 
	361, 361, 366, 361, 361, 361, 361, 361, 
	361, 361, 361, 361, 361, 361, 361, 361, 
	361, 361, 361, 361, 367, 361, 361, 368, 
	361, 369, 370, 372, 372, 372, 372, 372, 
	372, 372, 372, 371, 373, 373, 373, 373, 
	373, 373, 373, 373, 371, 371, 374, 374, 
	125, 125, 125, 374, 125, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 374, 125, 125, 125, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 126, 125, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 125, 125, 375, 125, 376, 377, 378, 
	378, 125, 125, 125, 378, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 125, 378, 125, 125, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 126, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 125, 125, 125, 379, 125, 124, 378, 
	380, 381, 382, 382, 110, 110, 110, 382, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 382, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 111, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 383, 
	110, 384, 384, 384, 384, 384, 384, 384, 
	384, 384, 384, 382, 382, 382, 382, 382, 
	382, 382, 384, 384, 384, 384, 384, 384, 
	382, 382, 382, 382, 382, 382, 382, 382, 
	382, 382, 382, 382, 382, 382, 382, 382, 
	382, 382, 382, 382, 382, 382, 382, 382, 
	382, 382, 384, 384, 384, 384, 384, 384, 
	382, 386, 386, 386, 386, 386, 386, 386, 
	386, 386, 386, 385, 385, 385, 385, 385, 
	385, 385, 386, 386, 386, 386, 386, 386, 
	385, 385, 385, 385, 385, 385, 385, 385, 
	385, 385, 385, 385, 385, 385, 385, 385, 
	385, 385, 385, 385, 385, 385, 385, 385, 
	385, 385, 386, 386, 386, 386, 386, 386, 
	385, 385, 387, 387, 387, 387, 387, 387, 
	387, 387, 387, 387, 378, 378, 378, 378, 
	378, 378, 378, 387, 387, 387, 387, 387, 
	387, 378, 378, 378, 378, 378, 378, 378, 
	378, 378, 378, 378, 378, 378, 378, 378, 
	378, 378, 378, 378, 378, 378, 378, 378, 
	378, 378, 378, 387, 387, 387, 387, 387, 
	387, 378, 389, 389, 389, 389, 389, 389, 
	389, 389, 389, 389, 388, 388, 388, 388, 
	388, 388, 388, 389, 389, 389, 389, 389, 
	389, 388, 388, 388, 388, 388, 388, 388, 
	388, 388, 388, 388, 388, 388, 388, 388, 
	388, 388, 388, 388, 388, 388, 388, 388, 
	388, 388, 388, 389, 389, 389, 389, 389, 
	389, 388, 388, 378, 103, 103, 103, 378, 
	103, 103, 103, 103, 103, 103, 103, 103, 
	103, 103, 103, 103, 103, 103, 103, 103, 
	103, 103, 103, 103, 103, 378, 103, 103, 
	103, 103, 103, 103, 103, 103, 103, 103, 
	103, 103, 103, 103, 103, 103, 103, 103, 
	390, 103, 374, 110, 110, 110, 374, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 374, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 111, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 112, 110, 
	374, 391, 391, 391, 374, 391, 391, 391, 
	391, 391, 391, 391, 391, 391, 391, 391, 
	391, 391, 391, 391, 391, 391, 391, 391, 
	391, 391, 374, 391, 391, 391, 391, 391, 
	391, 391, 391, 391, 391, 391, 391, 391, 
	391, 391, 391, 391, 391, 391, 391, 391, 
	391, 391, 391, 391, 391, 391, 391, 391, 
	391, 391, 391, 391, 391, 391, 391, 391, 
	391, 391, 391, 391, 391, 391, 391, 391, 
	391, 391, 391, 391, 391, 391, 391, 391, 
	391, 391, 391, 391, 391, 391, 391, 391, 
	391, 391, 391, 391, 392, 391, 393, 394, 
	394, 391, 391, 391, 394, 391, 391, 391, 
	391, 391, 391, 391, 391, 391, 391, 391, 
	391, 391, 391, 391, 391, 391, 391, 391, 
	391, 391, 394, 391, 391, 391, 391, 391, 
	391, 391, 391, 391, 391, 391, 391, 391, 
	391, 391, 391, 391, 391, 391, 391, 391, 
	391, 391, 391, 391, 391, 391, 391, 391, 
	391, 391, 391, 391, 391, 391, 391, 391, 
	391, 391, 391, 391, 391, 391, 391, 391, 
	391, 391, 391, 391, 391, 391, 391, 391, 
	391, 391, 391, 391, 391, 391, 391, 391, 
	391, 391, 391, 391, 395, 391, 396, 396, 
	396, 396, 396, 396, 396, 396, 396, 396, 
	394, 394, 394, 394, 394, 394, 394, 396, 
	396, 396, 396, 396, 396, 394, 394, 394, 
	394, 394, 394, 394, 394, 394, 394, 394, 
	394, 394, 394, 394, 394, 394, 394, 394, 
	394, 394, 394, 394, 394, 394, 394, 396, 
	396, 396, 396, 396, 396, 394, 398, 398, 
	398, 398, 398, 398, 398, 398, 398, 398, 
	397, 397, 397, 397, 397, 397, 397, 398, 
	398, 398, 398, 398, 398, 397, 397, 397, 
	397, 397, 397, 397, 397, 397, 397, 397, 
	397, 397, 397, 397, 397, 397, 397, 397, 
	397, 397, 397, 397, 397, 397, 397, 398, 
	398, 398, 398, 398, 398, 397, 397, 394, 
	103, 103, 103, 394, 103, 103, 103, 103, 
	103, 103, 103, 103, 103, 103, 103, 103, 
	103, 103, 103, 103, 103, 103, 103, 103, 
	103, 394, 103, 103, 103, 103, 103, 103, 
	103, 103, 103, 103, 103, 103, 103, 103, 
	103, 103, 103, 103, 390, 103, 399, 394, 
	394, 110, 110, 110, 394, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 394, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 111, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 110, 110, 110, 110, 
	110, 110, 110, 110, 112, 110, 401, 401, 
	401, 401, 401, 401, 401, 401, 401, 401, 
	400, 400, 400, 400, 400, 400, 400, 401, 
	401, 401, 401, 401, 401, 400, 400, 400, 
	400, 400, 400, 400, 400, 400, 400, 400, 
	400, 400, 400, 400, 400, 400, 400, 400, 
	400, 400, 400, 400, 400, 400, 400, 401, 
	401, 401, 401, 401, 401, 400, 400, 400, 
	400, 400, 400, 400, 400, 400, 400, 400, 
	400, 400, 400, 400, 400, 400, 400, 400, 
	400, 402, 400, 403, 403, 403, 403, 403, 
	403, 403, 403, 403, 403, 400, 400, 400, 
	400, 400, 400, 400, 403, 403, 403, 403, 
	403, 403, 400, 400, 400, 400, 400, 400, 
	400, 400, 400, 400, 400, 400, 400, 400, 
	400, 400, 400, 400, 400, 400, 400, 400, 
	400, 400, 400, 400, 403, 403, 403, 403, 
	403, 403, 400, 404, 404, 404, 404, 404, 
	404, 404, 404, 404, 404, 400, 400, 400, 
	400, 400, 400, 400, 404, 404, 404, 404, 
	404, 404, 400, 400, 400, 400, 400, 400, 
	400, 400, 400, 400, 400, 400, 400, 400, 
	400, 400, 400, 400, 400, 400, 400, 400, 
	400, 400, 400, 400, 404, 404, 404, 404, 
	404, 404, 400, 405, 405, 405, 405, 405, 
	405, 405, 405, 405, 405, 400, 400, 400, 
	400, 400, 400, 400, 405, 405, 405, 405, 
	405, 405, 400, 400, 400, 400, 400, 400, 
	400, 400, 400, 400, 400, 400, 400, 400, 
	400, 400, 400, 400, 400, 400, 400, 400, 
	400, 400, 400, 400, 405, 405, 405, 405, 
	405, 405, 400, 406, 409, 408, 408, 408, 
	409, 408, 408, 408, 408, 410, 408, 408, 
	408, 408, 408, 408, 408, 408, 408, 408, 
	408, 408, 408, 408, 408, 408, 409, 408, 
	408, 408, 408, 408, 410, 408, 408, 408, 
	408, 408, 408, 408, 408, 408, 408, 408, 
	408, 408, 408, 408, 411, 411, 411, 411, 
	411, 411, 411, 411, 411, 411, 408, 408, 
	408, 408, 408, 408, 408, 411, 411, 411, 
	411, 411, 411, 408, 408, 408, 408, 408, 
	408, 408, 408, 408, 408, 408, 408, 408, 
	408, 408, 408, 408, 408, 408, 408, 408, 
	408, 408, 408, 408, 408, 411, 411, 411, 
	411, 411, 411, 408, 408, 408, 408, 408, 
	408, 408, 408, 408, 408, 408, 408, 408, 
	408, 408, 408, 408, 408, 408, 408, 408, 
	408, 412, 408, 409, 408, 408, 408, 409, 
	408, 408, 408, 408, 407, 408, 408, 408, 
	408, 408, 408, 408, 408, 408, 408, 408, 
	408, 408, 408, 408, 408, 409, 408, 408, 
	408, 408, 408, 407, 408, 408, 408, 408, 
	408, 408, 408, 408, 408, 408, 408, 408, 
	408, 408, 408, 413, 413, 413, 413, 413, 
	413, 413, 413, 413, 413, 408, 408, 408, 
	408, 408, 408, 408, 413, 413, 413, 413, 
	413, 413, 408, 408, 408, 408, 408, 408, 
	408, 408, 408, 408, 408, 408, 408, 408, 
	408, 408, 408, 408, 408, 408, 408, 408, 
	408, 408, 408, 408, 413, 413, 413, 413, 
	413, 413, 408, 408, 408, 408, 408, 408, 
	408, 408, 408, 408, 408, 408, 408, 408, 
	408, 408, 408, 408, 408, 408, 408, 408, 
	414, 408, 407, 409, 413, 413, 413, 409, 
	413, 413, 413, 413, 407, 413, 413, 413, 
	413, 413, 413, 413, 413, 413, 413, 413, 
	413, 413, 413, 413, 413, 409, 413, 413, 
	413, 413, 413, 407, 413, 413, 413, 413, 
	413, 413, 413, 413, 413, 413, 413, 413, 
	413, 413, 413, 413, 413, 413, 413, 413, 
	413, 413, 413, 413, 413, 413, 413, 413, 
	413, 413, 413, 413, 413, 413, 413, 413, 
	413, 413, 413, 413, 413, 413, 413, 413, 
	413, 413, 413, 413, 413, 413, 413, 413, 
	413, 413, 413, 413, 413, 413, 413, 413, 
	413, 413, 413, 413, 413, 413, 413, 413, 
	413, 413, 413, 413, 413, 413, 413, 413, 
	413, 413, 413, 413, 413, 413, 413, 413, 
	413, 413, 413, 413, 413, 413, 413, 413, 
	407, 413, 415, 409, 416, 416, 416, 409, 
	416, 416, 416, 416, 410, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 409, 416, 416, 
	416, 416, 416, 410, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 417, 417, 417, 417, 417, 
	417, 417, 417, 417, 417, 416, 416, 416, 
	416, 416, 416, 416, 417, 417, 417, 417, 
	417, 417, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 417, 417, 417, 417, 
	417, 417, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	407, 416, 409, 416, 416, 416, 409, 416, 
	416, 416, 416, 407, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 409, 416, 416, 416, 
	416, 416, 407, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 413, 413, 413, 413, 413, 413, 
	413, 413, 413, 413, 416, 416, 416, 416, 
	416, 416, 416, 413, 413, 413, 413, 413, 
	413, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 413, 413, 413, 413, 413, 
	413, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 409, 
	416, 409, 416, 416, 416, 409, 416, 416, 
	416, 416, 418, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 409, 416, 416, 416, 416, 
	416, 418, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 419, 419, 419, 419, 419, 419, 419, 
	419, 419, 419, 416, 416, 416, 416, 416, 
	416, 416, 419, 419, 419, 419, 419, 419, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 419, 419, 419, 419, 419, 419, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 420, 416, 
	409, 416, 416, 416, 409, 416, 416, 416, 
	416, 418, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 409, 416, 416, 416, 416, 416, 
	418, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	417, 417, 417, 417, 417, 417, 417, 417, 
	417, 417, 416, 416, 416, 416, 416, 416, 
	416, 417, 417, 417, 417, 417, 417, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 417, 417, 417, 417, 417, 417, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 420, 416, 421, 
	409, 416, 416, 416, 409, 416, 416, 416, 
	416, 418, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 409, 416, 416, 416, 416, 416, 
	418, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	422, 422, 422, 422, 422, 422, 422, 422, 
	422, 422, 416, 416, 416, 416, 416, 416, 
	416, 422, 422, 422, 422, 422, 422, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 422, 422, 422, 422, 422, 422, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 420, 416, 409, 
	416, 416, 416, 409, 416, 416, 416, 416, 
	418, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 409, 416, 416, 416, 416, 416, 418, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 423, 
	423, 423, 423, 423, 423, 423, 423, 423, 
	423, 416, 416, 416, 416, 416, 416, 416, 
	423, 423, 423, 423, 423, 423, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	423, 423, 423, 423, 423, 423, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 420, 416, 409, 416, 
	416, 416, 409, 416, 416, 416, 416, 418, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	409, 416, 416, 416, 416, 416, 418, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 424, 424, 
	424, 424, 424, 424, 424, 424, 424, 424, 
	416, 416, 416, 416, 416, 416, 416, 424, 
	424, 424, 424, 424, 424, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 424, 
	424, 424, 424, 424, 424, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 420, 416, 409, 416, 416, 
	416, 409, 416, 416, 416, 416, 418, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 409, 
	416, 416, 416, 416, 416, 418, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 425, 425, 425, 
	425, 425, 425, 425, 425, 425, 425, 416, 
	416, 416, 416, 416, 416, 416, 425, 425, 
	425, 425, 425, 425, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 425, 425, 
	425, 425, 425, 425, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 420, 416, 409, 416, 416, 416, 
	409, 416, 416, 416, 416, 418, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 409, 416, 
	416, 416, 416, 416, 418, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 426, 426, 426, 426, 
	426, 426, 426, 426, 426, 426, 416, 416, 
	416, 416, 416, 416, 416, 426, 426, 426, 
	426, 426, 426, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 426, 426, 426, 
	426, 426, 426, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 420, 416, 409, 416, 416, 416, 409, 
	416, 416, 416, 416, 407, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 409, 416, 416, 
	416, 416, 416, 407, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 426, 426, 426, 426, 426, 
	426, 426, 426, 426, 426, 416, 416, 416, 
	416, 416, 416, 416, 426, 426, 426, 426, 
	426, 426, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 426, 426, 426, 426, 
	426, 426, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	407, 416, 409, 416, 416, 416, 409, 416, 
	416, 416, 416, 427, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 409, 416, 416, 416, 
	416, 416, 427, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 428, 428, 428, 428, 428, 428, 
	428, 428, 428, 428, 416, 416, 416, 416, 
	416, 416, 416, 428, 428, 428, 428, 428, 
	428, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 428, 428, 428, 428, 428, 
	428, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 420, 
	416, 409, 416, 416, 416, 409, 416, 416, 
	416, 416, 427, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 409, 416, 416, 416, 416, 
	416, 427, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 429, 429, 429, 429, 429, 429, 429, 
	429, 429, 429, 416, 416, 416, 416, 416, 
	416, 416, 429, 429, 429, 429, 429, 429, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 429, 429, 429, 429, 429, 429, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 420, 416, 
	409, 416, 416, 416, 409, 416, 416, 416, 
	416, 427, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 409, 416, 416, 416, 416, 416, 
	427, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	430, 430, 430, 430, 430, 430, 430, 430, 
	430, 430, 416, 416, 416, 416, 416, 416, 
	416, 430, 430, 430, 430, 430, 430, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 430, 430, 430, 430, 430, 430, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 407, 416, 409, 
	416, 416, 416, 409, 416, 416, 416, 416, 
	427, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 409, 416, 416, 416, 416, 416, 427, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 431, 
	431, 431, 431, 431, 431, 431, 431, 431, 
	431, 416, 416, 416, 416, 416, 416, 416, 
	431, 431, 431, 431, 431, 431, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	431, 431, 431, 431, 431, 431, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 407, 416, 409, 416, 
	416, 416, 409, 416, 416, 416, 416, 427, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	409, 416, 416, 416, 416, 416, 427, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 432, 432, 
	432, 432, 432, 432, 432, 432, 432, 432, 
	416, 416, 416, 416, 416, 416, 416, 432, 
	432, 432, 432, 432, 432, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 432, 
	432, 432, 432, 432, 432, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 407, 416, 409, 416, 416, 
	416, 409, 416, 416, 416, 416, 427, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 409, 
	416, 416, 416, 416, 416, 427, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 433, 433, 433, 
	433, 433, 433, 433, 433, 433, 433, 416, 
	416, 416, 416, 416, 416, 416, 433, 433, 
	433, 433, 433, 433, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 433, 433, 
	433, 433, 433, 433, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 407, 416, 409, 416, 416, 416, 
	409, 416, 416, 416, 416, 427, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 409, 416, 
	416, 416, 416, 416, 427, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 434, 434, 434, 434, 
	434, 434, 434, 434, 434, 434, 416, 416, 
	416, 416, 416, 416, 416, 434, 434, 434, 
	434, 434, 434, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 434, 434, 434, 
	434, 434, 434, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 407, 416, 409, 416, 416, 416, 409, 
	416, 416, 416, 416, 427, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 409, 416, 416, 
	416, 416, 416, 427, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 426, 426, 426, 426, 426, 
	426, 426, 426, 426, 426, 416, 416, 416, 
	416, 416, 416, 416, 426, 426, 426, 426, 
	426, 426, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 426, 426, 426, 426, 
	426, 426, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	407, 416, 409, 416, 416, 416, 409, 416, 
	416, 416, 416, 427, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 409, 416, 416, 416, 
	416, 416, 427, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 435, 435, 435, 435, 435, 435, 
	435, 435, 435, 435, 416, 416, 416, 416, 
	416, 416, 416, 435, 435, 435, 435, 435, 
	435, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 435, 435, 435, 435, 435, 
	435, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 420, 
	416, 409, 416, 416, 416, 409, 416, 416, 
	416, 416, 427, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 409, 416, 416, 416, 416, 
	416, 427, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 436, 436, 436, 436, 436, 436, 436, 
	436, 436, 436, 416, 416, 416, 416, 416, 
	416, 416, 436, 436, 436, 436, 436, 436, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 436, 436, 436, 436, 436, 436, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 420, 416, 
	409, 416, 416, 416, 409, 416, 416, 416, 
	416, 427, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 409, 416, 416, 416, 416, 416, 
	427, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	437, 437, 437, 437, 437, 437, 437, 437, 
	437, 437, 416, 416, 416, 416, 416, 416, 
	416, 437, 437, 437, 437, 437, 437, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 437, 437, 437, 437, 437, 437, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 420, 416, 409, 
	416, 416, 416, 409, 416, 416, 416, 416, 
	427, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 409, 416, 416, 416, 416, 416, 427, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 438, 
	438, 438, 438, 438, 438, 438, 438, 438, 
	438, 416, 416, 416, 416, 416, 416, 416, 
	438, 438, 438, 438, 438, 438, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	438, 438, 438, 438, 438, 438, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 420, 416, 409, 416, 
	416, 416, 409, 416, 416, 416, 416, 427, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	409, 416, 416, 416, 416, 416, 427, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 426, 426, 
	426, 426, 426, 426, 426, 426, 426, 426, 
	416, 416, 416, 416, 416, 416, 416, 426, 
	426, 426, 426, 426, 426, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 426, 
	426, 426, 426, 426, 426, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 420, 416, 439, 440, 442, 
	442, 442, 442, 442, 442, 442, 442, 442, 
	442, 441, 441, 441, 441, 441, 441, 441, 
	442, 442, 442, 442, 442, 442, 441, 441, 
	441, 441, 441, 441, 441, 441, 441, 441, 
	441, 441, 441, 441, 441, 441, 441, 441, 
	441, 441, 441, 441, 441, 441, 441, 441, 
	442, 442, 442, 442, 442, 442, 441, 441, 
	0
]

class << self
	attr_accessor :_lex_trans_targs
	private :_lex_trans_targs, :_lex_trans_targs=
end
self._lex_trans_targs = [
	61, 64, 65, 2, 66, 67, 68, 4, 
	69, 70, 61, 77, 78, 81, 82, 94, 
	91, 83, 84, 85, 9, 86, 87, 88, 
	96, 98, 99, 103, 104, 105, 100, 15, 
	8, 79, 80, 17, 128, 129, 131, 133, 
	134, 20, 135, 136, 137, 22, 138, 139, 
	131, 146, 147, 150, 151, 163, 160, 152, 
	153, 154, 27, 155, 156, 157, 165, 167, 
	168, 172, 173, 174, 169, 33, 26, 148, 
	149, 35, 197, 198, 205, 207, 208, 38, 
	209, 210, 211, 40, 212, 213, 215, 218, 
	219, 42, 220, 221, 222, 44, 223, 224, 
	230, 0, 229, 229, 231, 233, 229, 239, 
	240, 243, 244, 256, 253, 245, 246, 247, 
	50, 248, 249, 250, 258, 260, 261, 265, 
	266, 267, 262, 56, 49, 241, 242, 58, 
	303, 304, 60, 61, 61, 62, 61, 63, 
	71, 61, 61, 1, 3, 61, 61, 61, 
	61, 61, 61, 61, 72, 73, 74, 5, 
	11, 16, 106, 18, 61, 61, 61, 75, 
	76, 61, 6, 61, 61, 61, 7, 61, 
	61, 61, 10, 89, 61, 90, 92, 61, 
	93, 95, 97, 12, 61, 61, 13, 101, 
	61, 102, 14, 61, 107, 111, 108, 109, 
	110, 61, 61, 112, 113, 116, 118, 127, 
	114, 115, 61, 117, 119, 121, 120, 61, 
	122, 123, 124, 125, 126, 61, 61, 61, 
	130, 131, 131, 131, 132, 140, 131, 19, 
	21, 131, 131, 131, 131, 131, 131, 131, 
	141, 142, 143, 23, 29, 34, 175, 36, 
	131, 131, 131, 144, 145, 131, 24, 131, 
	131, 131, 25, 131, 131, 131, 28, 158, 
	131, 159, 161, 131, 162, 164, 166, 30, 
	131, 131, 31, 170, 131, 171, 32, 131, 
	176, 180, 177, 178, 179, 131, 131, 181, 
	182, 185, 187, 196, 183, 184, 131, 186, 
	188, 190, 189, 131, 191, 192, 193, 194, 
	195, 131, 131, 131, 199, 200, 200, 201, 
	200, 202, 200, 200, 200, 203, 203, 203, 
	204, 203, 203, 203, 205, 205, 205, 206, 
	205, 37, 39, 205, 205, 205, 205, 205, 
	205, 214, 214, 214, 215, 215, 216, 215, 
	217, 215, 215, 41, 43, 215, 215, 215, 
	215, 215, 215, 225, 225, 226, 225, 225, 
	227, 228, 227, 45, 229, 232, 229, 232, 
	229, 234, 235, 236, 46, 52, 57, 268, 
	59, 229, 229, 229, 237, 238, 229, 47, 
	229, 229, 229, 48, 229, 229, 229, 51, 
	251, 229, 252, 254, 229, 255, 257, 259, 
	53, 229, 229, 54, 263, 229, 264, 55, 
	229, 269, 273, 270, 271, 272, 229, 229, 
	274, 275, 278, 289, 302, 276, 277, 229, 
	279, 280, 281, 283, 282, 229, 284, 285, 
	286, 287, 288, 290, 297, 291, 292, 293, 
	294, 295, 296, 298, 299, 300, 301, 229, 
	229, 229, 305
]

class << self
	attr_accessor :_lex_trans_actions
	private :_lex_trans_actions, :_lex_trans_actions=
end
self._lex_trans_actions = [
	25, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 23, 0, 11, 0, 301, 0, 
	0, 11, 0, 0, 0, 0, 301, 0, 
	11, 0, 301, 0, 11, 11, 0, 0, 
	0, 0, 0, 0, 0, 0, 37, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	35, 0, 11, 0, 301, 0, 0, 11, 
	0, 0, 0, 0, 301, 0, 11, 0, 
	301, 0, 11, 11, 0, 0, 0, 0, 
	0, 0, 0, 0, 61, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 77, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 89, 121, 0, 11, 93, 0, 
	11, 0, 301, 0, 0, 11, 0, 0, 
	0, 0, 301, 0, 11, 0, 301, 0, 
	11, 11, 0, 0, 0, 0, 0, 0, 
	0, 0, 95, 17, 15, 0, 97, 11, 
	11, 19, 21, 0, 0, 13, 238, 277, 
	274, 253, 250, 392, 3, 3, 3, 3, 
	3, 3, 3, 3, 133, 223, 160, 0, 
	0, 142, 0, 332, 151, 308, 0, 419, 
	356, 404, 0, 0, 449, 0, 0, 368, 
	0, 11, 0, 0, 344, 320, 0, 0, 
	434, 0, 1, 196, 0, 0, 0, 0, 
	0, 187, 214, 0, 0, 0, 0, 0, 
	0, 0, 380, 0, 0, 0, 0, 124, 
	0, 0, 0, 0, 0, 205, 178, 169, 
	0, 31, 29, 100, 11, 11, 33, 0, 
	0, 27, 241, 283, 280, 259, 256, 396, 
	3, 3, 3, 3, 3, 3, 3, 3, 
	136, 226, 163, 0, 0, 145, 0, 336, 
	154, 312, 0, 424, 360, 409, 0, 0, 
	454, 0, 0, 372, 0, 11, 0, 0, 
	348, 324, 0, 0, 439, 0, 1, 199, 
	0, 0, 0, 0, 0, 190, 217, 0, 
	0, 0, 0, 0, 0, 0, 384, 0, 
	0, 0, 0, 127, 0, 0, 0, 0, 
	0, 208, 181, 172, 0, 41, 39, 0, 
	103, 0, 43, 45, 232, 49, 47, 106, 
	0, 51, 235, 304, 57, 55, 109, 11, 
	59, 0, 0, 53, 244, 289, 286, 265, 
	262, 65, 63, 112, 71, 69, 0, 115, 
	11, 73, 75, 0, 0, 67, 247, 295, 
	292, 271, 268, 81, 79, 0, 118, 83, 
	85, 0, 87, 0, 298, 5, 91, 0, 
	400, 3, 3, 3, 3, 3, 3, 3, 
	3, 139, 229, 166, 0, 0, 148, 0, 
	340, 157, 316, 0, 429, 364, 414, 0, 
	0, 459, 0, 0, 376, 0, 11, 0, 
	0, 352, 328, 0, 0, 444, 0, 1, 
	202, 0, 0, 0, 0, 0, 193, 220, 
	0, 0, 0, 0, 0, 0, 0, 388, 
	0, 0, 0, 0, 0, 130, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 211, 
	184, 175, 0
]

class << self
	attr_accessor :_lex_to_state_actions
	private :_lex_to_state_actions, :_lex_to_state_actions=
end
self._lex_to_state_actions = [
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 7, 7, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 7, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	7, 0, 0, 7, 0, 7, 0, 0, 
	0, 0, 0, 0, 0, 0, 7, 7, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 7, 0, 7, 0, 7, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0
]

class << self
	attr_accessor :_lex_from_state_actions
	private :_lex_from_state_actions, :_lex_from_state_actions=
end
self._lex_from_state_actions = [
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 9, 9, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 9, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	9, 0, 0, 9, 0, 9, 0, 0, 
	0, 0, 0, 0, 0, 0, 9, 9, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 9, 0, 9, 0, 9, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0
]

class << self
	attr_accessor :_lex_eof_trans
	private :_lex_eof_trans, :_lex_eof_trans=
end
self._lex_eof_trans = [
	0, 1, 1, 1, 1, 11, 11, 11, 
	11, 11, 11, 11, 11, 11, 11, 11, 
	11, 11, 11, 39, 39, 39, 39, 49, 
	49, 49, 49, 49, 49, 49, 49, 49, 
	49, 49, 49, 49, 49, 77, 77, 77, 
	77, 87, 87, 87, 87, 0, 103, 103, 
	103, 103, 103, 103, 103, 103, 103, 103, 
	103, 103, 103, 103, 0, 0, 138, 139, 
	143, 143, 143, 144, 145, 146, 147, 148, 
	157, 158, 159, 159, 159, 162, 162, 164, 
	165, 166, 166, 166, 168, 169, 170, 170, 
	170, 173, 173, 166, 176, 176, 166, 162, 
	162, 181, 182, 182, 182, 185, 185, 182, 
	182, 182, 188, 188, 188, 188, 194, 195, 
	195, 195, 195, 203, 195, 195, 195, 195, 
	208, 195, 195, 195, 195, 195, 195, 214, 
	215, 216, 216, 0, 223, 227, 227, 227, 
	228, 229, 230, 231, 232, 241, 242, 243, 
	243, 243, 246, 246, 248, 249, 250, 250, 
	250, 252, 253, 254, 254, 254, 257, 257, 
	250, 260, 260, 250, 246, 246, 265, 266, 
	266, 266, 269, 269, 266, 266, 266, 272, 
	272, 272, 272, 278, 279, 279, 279, 279, 
	287, 279, 279, 279, 279, 292, 279, 279, 
	279, 279, 279, 279, 298, 299, 300, 300, 
	0, 307, 308, 0, 314, 0, 321, 325, 
	325, 325, 326, 327, 328, 329, 0, 0, 
	338, 339, 343, 343, 343, 344, 345, 346, 
	347, 0, 352, 0, 355, 0, 357, 357, 
	359, 361, 370, 371, 372, 372, 372, 375, 
	375, 377, 378, 379, 379, 379, 381, 382, 
	383, 383, 383, 386, 386, 379, 389, 389, 
	379, 375, 375, 394, 395, 395, 395, 398, 
	398, 395, 395, 395, 401, 401, 401, 401, 
	407, 408, 408, 408, 408, 416, 408, 408, 
	408, 408, 422, 408, 408, 408, 408, 408, 
	408, 408, 408, 408, 408, 408, 408, 408, 
	408, 408, 408, 408, 408, 408, 440, 441, 
	442, 442
]

class << self
	attr_accessor :lex_start
end
self.lex_start = 60;
class << self
	attr_accessor :lex_error
end
self.lex_error = 0;

class << self
	attr_accessor :lex_en_interp_words
end
self.lex_en_interp_words = 61;
class << self
	attr_accessor :lex_en_interp_string
end
self.lex_en_interp_string = 131;
class << self
	attr_accessor :lex_en_plain_words
end
self.lex_en_plain_words = 200;
class << self
	attr_accessor :lex_en_plain_string
end
self.lex_en_plain_string = 203;
class << self
	attr_accessor :lex_en_interp_backslash_delimited
end
self.lex_en_interp_backslash_delimited = 205;
class << self
	attr_accessor :lex_en_plain_backslash_delimited
end
self.lex_en_plain_backslash_delimited = 214;
class << self
	attr_accessor :lex_en_interp_backslash_delimited_words
end
self.lex_en_interp_backslash_delimited_words = 215;
class << self
	attr_accessor :lex_en_plain_backslash_delimited_words
end
self.lex_en_plain_backslash_delimited_words = 225;
class << self
	attr_accessor :lex_en_regexp_modifiers
end
self.lex_en_regexp_modifiers = 227;
class << self
	attr_accessor :lex_en_character
end
self.lex_en_character = 229;
class << self
	attr_accessor :lex_en_unknown
end
self.lex_en_unknown = 60;


# line 6 "lib/parser/lexer-strings.rl"
  # %

  ESCAPES = {
    ?a.ord => "\a", ?b.ord  => "\b", ?e.ord => "\e", ?f.ord => "\f",
    ?n.ord => "\n", ?r.ord  => "\r", ?s.ord => "\s", ?t.ord => "\t",
    ?v.ord => "\v", ?\\.ord => "\\"
  }.freeze

  REGEXP_META_CHARACTERS = Regexp.union(*"\\$()*+.<>?[]^{|}".chars).freeze

  attr_accessor :herebody_s

  # Set by "main" lexer
  attr_accessor :source_buffer, :source_pts

  def initialize(lexer, version)
    @lexer = lexer
    @version = version

    @_lex_actions =
      if self.class.respond_to?(:_lex_actions, true)
        self.class.send :_lex_actions
      else
        []
      end

    reset
  end

  def reset
    @cs            = self.class.lex_en_unknown
    @literal_stack = []

    @escape_s      = nil # starting position of current sequence
    @escape        = nil # last escaped sequence, as string

    @herebody_s    = nil # starting position of current heredoc line

    # After encountering the closing line of <<~SQUIGGLY_HEREDOC,
    # we store the indentation level and give it out to the parser
    # on request. It is not possible to infer indentation level just
    # from the AST because escape sequences such as `\ ` or `\t` are
    # expanded inside the lexer, but count as non-whitespace for
    # indentation purposes.
    @dedent_level  = nil
  end

  LEX_STATES = {
    :interp_string => lex_en_interp_string,
    :interp_words  => lex_en_interp_words,
    :plain_string  => lex_en_plain_string,
    :plain_words   => lex_en_plain_string,
  }

  def advance(p)
    # Ugly, but dependent on Ragel output. Consider refactoring it somehow.
    klass = self.class
    _lex_trans_keys         = klass.send :_lex_trans_keys
    _lex_key_spans          = klass.send :_lex_key_spans
    _lex_index_offsets      = klass.send :_lex_index_offsets
    _lex_indicies           = klass.send :_lex_indicies
    _lex_trans_targs        = klass.send :_lex_trans_targs
    _lex_trans_actions      = klass.send :_lex_trans_actions
    _lex_to_state_actions   = klass.send :_lex_to_state_actions
    _lex_from_state_actions = klass.send :_lex_from_state_actions
    _lex_eof_trans          = klass.send :_lex_eof_trans
    _lex_actions            = @_lex_actions

    pe = source_pts.size + 2
    eof = pe

    
# line 3357 "lib/parser/lexer-strings.rb"
begin # ragel flat
	testEof = false
	_slen, _trans, _keys, _inds, _acts, _nacts = nil
	_goto_level = 0
	_resume = 10
	_eof_trans = 15
	_again = 20
	_test_eof = 30
	_out = 40
	while true
	_trigger_goto = false
	if _goto_level <= 0
	if p == pe
		_goto_level = _test_eof
		next
	end
	if  @cs == 0
		_goto_level = _out
		next
	end
	end
	if _goto_level <= _resume
	_acts = _lex_from_state_actions[ @cs]
	_nacts = _lex_actions[_acts]
	_acts += 1
	while _nacts > 0
		_nacts -= 1
		_acts += 1
		case _lex_actions[_acts - 1]
	when 24 then
# line 1 "NONE"
		begin
 @ts = p
		end
# line 3392 "lib/parser/lexer-strings.rb"
		end # from state action switch
	end
	if _trigger_goto
		next
	end
	_keys =  @cs << 1
	_inds = _lex_index_offsets[ @cs]
	_slen = _lex_key_spans[ @cs]
	_wide = ( (source_pts[p] || 0))
	_trans = if (   _slen > 0 && 
			_lex_trans_keys[_keys] <= _wide && 
			_wide <= _lex_trans_keys[_keys + 1] 
		    ) then
			_lex_indicies[ _inds + _wide - _lex_trans_keys[_keys] ] 
		 else 
			_lex_indicies[ _inds + _slen ]
		 end
	end
	if _goto_level <= _eof_trans
	 @cs = _lex_trans_targs[_trans]
	if _lex_trans_actions[_trans] != 0
		_acts = _lex_trans_actions[_trans]
		_nacts = _lex_actions[_acts]
		_acts += 1
		while _nacts > 0
			_nacts -= 1
			_acts += 1
			case _lex_actions[_acts - 1]
	when 0 then
# line 525 "lib/parser/lexer-strings.rl"
		begin

    # Record position of a newline for precise location reporting on tNL
    # tokens.
    #
    # This action is embedded directly into c_nl, as it is idempotent and
    # there are no cases when we need to skip it.
    @newline_s = p
  		end
	when 1 then
# line 581 "lib/parser/lexer-strings.rl"
		begin

    unicode_points(p)
  		end
	when 2 then
# line 585 "lib/parser/lexer-strings.rl"
		begin

    unescape_char(p)
  		end
	when 3 then
# line 589 "lib/parser/lexer-strings.rl"
		begin

    diagnostic :fatal, :invalid_escape
  		end
	when 4 then
# line 593 "lib/parser/lexer-strings.rl"
		begin

    read_post_meta_or_ctrl_char(p)
  		end
	when 5 then
# line 597 "lib/parser/lexer-strings.rl"
		begin

    slash_c_char
  		end
	when 6 then
# line 601 "lib/parser/lexer-strings.rl"
		begin

    slash_m_char
  		end
	when 7 then
# line 607 "lib/parser/lexer-strings.rl"
		begin
 encode_escaped_char(p) 		end
	when 8 then
# line 613 "lib/parser/lexer-strings.rl"
		begin
 @escape = "\x7f" 		end
	when 9 then
# line 614 "lib/parser/lexer-strings.rl"
		begin
 encode_escaped_char(p) 		end
	when 10 then
# line 621 "lib/parser/lexer-strings.rl"
		begin
 @escape = encode_escape(tok(@escape_s, p).to_i(8) % 0x100) 		end
	when 11 then
# line 625 "lib/parser/lexer-strings.rl"
		begin
 @escape = encode_escape(tok(@escape_s + 1, p).to_i(16)) 		end
	when 12 then
# line 629 "lib/parser/lexer-strings.rl"
		begin

        diagnostic :fatal, :invalid_hex_escape, nil, range(@escape_s - 1, p + 2)
      		end
	when 13 then
# line 635 "lib/parser/lexer-strings.rl"
		begin
 @escape = tok(@escape_s + 1, p).to_i(16).chr(Encoding::UTF_8) 		end
	when 14 then
# line 639 "lib/parser/lexer-strings.rl"
		begin

        check_invalid_escapes(p)
      		end
	when 15 then
# line 645 "lib/parser/lexer-strings.rl"
		begin

        check_invalid_escapes(p)
      		end
	when 16 then
# line 659 "lib/parser/lexer-strings.rl"
		begin

          diagnostic :fatal, :unterminated_unicode, nil, range(p - 1, p)
        		end
	when 17 then
# line 685 "lib/parser/lexer-strings.rl"
		begin

      diagnostic :fatal, :escape_eof, nil, range(p - 1, p)
    		end
	when 18 then
# line 691 "lib/parser/lexer-strings.rl"
		begin

    @escape_s = p
    @escape   = nil
  		end
	when 19 then
# line 808 "lib/parser/lexer-strings.rl"
		begin
 interp_var_kind = :gvar 		end
	when 20 then
# line 809 "lib/parser/lexer-strings.rl"
		begin
 interp_var_kind = :cvar 		end
	when 21 then
# line 810 "lib/parser/lexer-strings.rl"
		begin
 interp_var_kind = :ivar 		end
	when 22 then
# line 944 "lib/parser/lexer-strings.rl"
		begin
 @escape = nil 		end
	when 25 then
# line 1 "NONE"
		begin
 @te = p+1
		end
	when 26 then
# line 842 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
    current_literal = literal
    extend_interp_code(current_literal)
    @root_lexer_state = @lexer.class.lex_en_expr_value;
    	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

   end
		end
	when 27 then
# line 761 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
    current_literal = literal
    extend_string_eol_check_eof(current_literal, pe)

    if current_literal.heredoc?
      line = extend_string_eol_heredoc_line

      # Try ending the heredoc with the complete most recently
      # scanned line. @herebody_s always refers to the start of such line.
      if current_literal.nest_and_try_closing(line, @herebody_s, @ts)
        # Adjust @herebody_s to point to the next line.
        @herebody_s = @te

        # Continue regular lexing after the heredoc reference (<<END).
        p = current_literal.heredoc_e - 1
         @cs = (pop_literal); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      else
        # Calculate indentation level for <<~HEREDOCs.
        current_literal.infer_indent_level(line)

        # Ditto.
        @herebody_s = @te
      end
    else
      # Try ending the literal with a newline.
      if current_literal.nest_and_try_closing(tok, @ts, @te)
         @cs = (pop_literal); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      end

      p = extend_string_eol_heredoc_intertwined(p)
    end

    extend_string_eol_words(current_literal, p)
   end
		end
	when 28 then
# line 731 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
    string = tok

    lookahead = extend_string_slice_end(lookahead)

    current_literal = literal
    if !current_literal.heredoc? &&
          (token = current_literal.nest_and_try_closing(string, @ts, @te, lookahead))
      if token[0] == :tLABEL_END
        p += 1
        pop_literal
        @root_lexer_state = @lexer.class.lex_en_expr_labelarg
      else
        if state = pop_literal
           @cs = (state);
        end
      end
      	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    else
      extend_string_for_token_range(current_literal, string)
    end
   end
		end
	when 29 then
# line 824 "lib/parser/lexer-strings.rl"
		begin
 @te = p
p = p - 1; begin 
    extend_interp_digit_var
   end
		end
	when 30 then
# line 813 "lib/parser/lexer-strings.rl"
		begin
 @te = p
p = p - 1; begin 
    current_literal = literal
    extend_interp_var(current_literal)
    emit_interp_var(interp_var_kind)
   end
		end
	when 31 then
# line 754 "lib/parser/lexer-strings.rl"
		begin
 @te = p
p = p - 1; begin 
    extend_string_escaped
   end
		end
	when 32 then
# line 796 "lib/parser/lexer-strings.rl"
		begin
 @te = p
p = p - 1; begin 
    literal.extend_space @ts, @te
   end
		end
	when 33 then
# line 731 "lib/parser/lexer-strings.rl"
		begin
 @te = p
p = p - 1; begin 
    string = tok

    lookahead = extend_string_slice_end(lookahead)

    current_literal = literal
    if !current_literal.heredoc? &&
          (token = current_literal.nest_and_try_closing(string, @ts, @te, lookahead))
      if token[0] == :tLABEL_END
        p += 1
        pop_literal
        @root_lexer_state = @lexer.class.lex_en_expr_labelarg
      else
        if state = pop_literal
           @cs = (state);
        end
      end
      	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    else
      extend_string_for_token_range(current_literal, string)
    end
   end
		end
	when 34 then
# line 754 "lib/parser/lexer-strings.rl"
		begin
 begin p = (( @te))-1; end
 begin 
    extend_string_escaped
   end
		end
	when 35 then
# line 731 "lib/parser/lexer-strings.rl"
		begin
 begin p = (( @te))-1; end
 begin 
    string = tok

    lookahead = extend_string_slice_end(lookahead)

    current_literal = literal
    if !current_literal.heredoc? &&
          (token = current_literal.nest_and_try_closing(string, @ts, @te, lookahead))
      if token[0] == :tLABEL_END
        p += 1
        pop_literal
        @root_lexer_state = @lexer.class.lex_en_expr_labelarg
      else
        if state = pop_literal
           @cs = (state);
        end
      end
      	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    else
      extend_string_for_token_range(current_literal, string)
    end
   end
		end
	when 36 then
# line 842 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
    current_literal = literal
    extend_interp_code(current_literal)
    @root_lexer_state = @lexer.class.lex_en_expr_value;
    	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

   end
		end
	when 37 then
# line 761 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
    current_literal = literal
    extend_string_eol_check_eof(current_literal, pe)

    if current_literal.heredoc?
      line = extend_string_eol_heredoc_line

      # Try ending the heredoc with the complete most recently
      # scanned line. @herebody_s always refers to the start of such line.
      if current_literal.nest_and_try_closing(line, @herebody_s, @ts)
        # Adjust @herebody_s to point to the next line.
        @herebody_s = @te

        # Continue regular lexing after the heredoc reference (<<END).
        p = current_literal.heredoc_e - 1
         @cs = (pop_literal); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      else
        # Calculate indentation level for <<~HEREDOCs.
        current_literal.infer_indent_level(line)

        # Ditto.
        @herebody_s = @te
      end
    else
      # Try ending the literal with a newline.
      if current_literal.nest_and_try_closing(tok, @ts, @te)
         @cs = (pop_literal); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      end

      p = extend_string_eol_heredoc_intertwined(p)
    end

    extend_string_eol_words(current_literal, p)
   end
		end
	when 38 then
# line 731 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
    string = tok

    lookahead = extend_string_slice_end(lookahead)

    current_literal = literal
    if !current_literal.heredoc? &&
          (token = current_literal.nest_and_try_closing(string, @ts, @te, lookahead))
      if token[0] == :tLABEL_END
        p += 1
        pop_literal
        @root_lexer_state = @lexer.class.lex_en_expr_labelarg
      else
        if state = pop_literal
           @cs = (state);
        end
      end
      	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    else
      extend_string_for_token_range(current_literal, string)
    end
   end
		end
	when 39 then
# line 824 "lib/parser/lexer-strings.rl"
		begin
 @te = p
p = p - 1; begin 
    extend_interp_digit_var
   end
		end
	when 40 then
# line 813 "lib/parser/lexer-strings.rl"
		begin
 @te = p
p = p - 1; begin 
    current_literal = literal
    extend_interp_var(current_literal)
    emit_interp_var(interp_var_kind)
   end
		end
	when 41 then
# line 754 "lib/parser/lexer-strings.rl"
		begin
 @te = p
p = p - 1; begin 
    extend_string_escaped
   end
		end
	when 42 then
# line 731 "lib/parser/lexer-strings.rl"
		begin
 @te = p
p = p - 1; begin 
    string = tok

    lookahead = extend_string_slice_end(lookahead)

    current_literal = literal
    if !current_literal.heredoc? &&
          (token = current_literal.nest_and_try_closing(string, @ts, @te, lookahead))
      if token[0] == :tLABEL_END
        p += 1
        pop_literal
        @root_lexer_state = @lexer.class.lex_en_expr_labelarg
      else
        if state = pop_literal
           @cs = (state);
        end
      end
      	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    else
      extend_string_for_token_range(current_literal, string)
    end
   end
		end
	when 43 then
# line 754 "lib/parser/lexer-strings.rl"
		begin
 begin p = (( @te))-1; end
 begin 
    extend_string_escaped
   end
		end
	when 44 then
# line 731 "lib/parser/lexer-strings.rl"
		begin
 begin p = (( @te))-1; end
 begin 
    string = tok

    lookahead = extend_string_slice_end(lookahead)

    current_literal = literal
    if !current_literal.heredoc? &&
          (token = current_literal.nest_and_try_closing(string, @ts, @te, lookahead))
      if token[0] == :tLABEL_END
        p += 1
        pop_literal
        @root_lexer_state = @lexer.class.lex_en_expr_labelarg
      else
        if state = pop_literal
           @cs = (state);
        end
      end
      	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    else
      extend_string_for_token_range(current_literal, string)
    end
   end
		end
	when 45 then
# line 754 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
    extend_string_escaped
   end
		end
	when 46 then
# line 761 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
    current_literal = literal
    extend_string_eol_check_eof(current_literal, pe)

    if current_literal.heredoc?
      line = extend_string_eol_heredoc_line

      # Try ending the heredoc with the complete most recently
      # scanned line. @herebody_s always refers to the start of such line.
      if current_literal.nest_and_try_closing(line, @herebody_s, @ts)
        # Adjust @herebody_s to point to the next line.
        @herebody_s = @te

        # Continue regular lexing after the heredoc reference (<<END).
        p = current_literal.heredoc_e - 1
         @cs = (pop_literal); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      else
        # Calculate indentation level for <<~HEREDOCs.
        current_literal.infer_indent_level(line)

        # Ditto.
        @herebody_s = @te
      end
    else
      # Try ending the literal with a newline.
      if current_literal.nest_and_try_closing(tok, @ts, @te)
         @cs = (pop_literal); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      end

      p = extend_string_eol_heredoc_intertwined(p)
    end

    extend_string_eol_words(current_literal, p)
   end
		end
	when 47 then
# line 731 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
    string = tok

    lookahead = extend_string_slice_end(lookahead)

    current_literal = literal
    if !current_literal.heredoc? &&
          (token = current_literal.nest_and_try_closing(string, @ts, @te, lookahead))
      if token[0] == :tLABEL_END
        p += 1
        pop_literal
        @root_lexer_state = @lexer.class.lex_en_expr_labelarg
      else
        if state = pop_literal
           @cs = (state);
        end
      end
      	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    else
      extend_string_for_token_range(current_literal, string)
    end
   end
		end
	when 48 then
# line 796 "lib/parser/lexer-strings.rl"
		begin
 @te = p
p = p - 1; begin 
    literal.extend_space @ts, @te
   end
		end
	when 49 then
# line 731 "lib/parser/lexer-strings.rl"
		begin
 @te = p
p = p - 1; begin 
    string = tok

    lookahead = extend_string_slice_end(lookahead)

    current_literal = literal
    if !current_literal.heredoc? &&
          (token = current_literal.nest_and_try_closing(string, @ts, @te, lookahead))
      if token[0] == :tLABEL_END
        p += 1
        pop_literal
        @root_lexer_state = @lexer.class.lex_en_expr_labelarg
      else
        if state = pop_literal
           @cs = (state);
        end
      end
      	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    else
      extend_string_for_token_range(current_literal, string)
    end
   end
		end
	when 50 then
# line 761 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
    current_literal = literal
    extend_string_eol_check_eof(current_literal, pe)

    if current_literal.heredoc?
      line = extend_string_eol_heredoc_line

      # Try ending the heredoc with the complete most recently
      # scanned line. @herebody_s always refers to the start of such line.
      if current_literal.nest_and_try_closing(line, @herebody_s, @ts)
        # Adjust @herebody_s to point to the next line.
        @herebody_s = @te

        # Continue regular lexing after the heredoc reference (<<END).
        p = current_literal.heredoc_e - 1
         @cs = (pop_literal); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      else
        # Calculate indentation level for <<~HEREDOCs.
        current_literal.infer_indent_level(line)

        # Ditto.
        @herebody_s = @te
      end
    else
      # Try ending the literal with a newline.
      if current_literal.nest_and_try_closing(tok, @ts, @te)
         @cs = (pop_literal); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      end

      p = extend_string_eol_heredoc_intertwined(p)
    end

    extend_string_eol_words(current_literal, p)
   end
		end
	when 51 then
# line 754 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
    extend_string_escaped
   end
		end
	when 52 then
# line 761 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
    current_literal = literal
    extend_string_eol_check_eof(current_literal, pe)

    if current_literal.heredoc?
      line = extend_string_eol_heredoc_line

      # Try ending the heredoc with the complete most recently
      # scanned line. @herebody_s always refers to the start of such line.
      if current_literal.nest_and_try_closing(line, @herebody_s, @ts)
        # Adjust @herebody_s to point to the next line.
        @herebody_s = @te

        # Continue regular lexing after the heredoc reference (<<END).
        p = current_literal.heredoc_e - 1
         @cs = (pop_literal); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      else
        # Calculate indentation level for <<~HEREDOCs.
        current_literal.infer_indent_level(line)

        # Ditto.
        @herebody_s = @te
      end
    else
      # Try ending the literal with a newline.
      if current_literal.nest_and_try_closing(tok, @ts, @te)
         @cs = (pop_literal); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      end

      p = extend_string_eol_heredoc_intertwined(p)
    end

    extend_string_eol_words(current_literal, p)
   end
		end
	when 53 then
# line 731 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
    string = tok

    lookahead = extend_string_slice_end(lookahead)

    current_literal = literal
    if !current_literal.heredoc? &&
          (token = current_literal.nest_and_try_closing(string, @ts, @te, lookahead))
      if token[0] == :tLABEL_END
        p += 1
        pop_literal
        @root_lexer_state = @lexer.class.lex_en_expr_labelarg
      else
        if state = pop_literal
           @cs = (state);
        end
      end
      	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    else
      extend_string_for_token_range(current_literal, string)
    end
   end
		end
	when 54 then
# line 731 "lib/parser/lexer-strings.rl"
		begin
 @te = p
p = p - 1; begin 
    string = tok

    lookahead = extend_string_slice_end(lookahead)

    current_literal = literal
    if !current_literal.heredoc? &&
          (token = current_literal.nest_and_try_closing(string, @ts, @te, lookahead))
      if token[0] == :tLABEL_END
        p += 1
        pop_literal
        @root_lexer_state = @lexer.class.lex_en_expr_labelarg
      else
        if state = pop_literal
           @cs = (state);
        end
      end
      	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    else
      extend_string_for_token_range(current_literal, string)
    end
   end
		end
	when 55 then
# line 842 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
    current_literal = literal
    extend_interp_code(current_literal)
    @root_lexer_state = @lexer.class.lex_en_expr_value;
    	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

   end
		end
	when 56 then
# line 761 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
    current_literal = literal
    extend_string_eol_check_eof(current_literal, pe)

    if current_literal.heredoc?
      line = extend_string_eol_heredoc_line

      # Try ending the heredoc with the complete most recently
      # scanned line. @herebody_s always refers to the start of such line.
      if current_literal.nest_and_try_closing(line, @herebody_s, @ts)
        # Adjust @herebody_s to point to the next line.
        @herebody_s = @te

        # Continue regular lexing after the heredoc reference (<<END).
        p = current_literal.heredoc_e - 1
         @cs = (pop_literal); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      else
        # Calculate indentation level for <<~HEREDOCs.
        current_literal.infer_indent_level(line)

        # Ditto.
        @herebody_s = @te
      end
    else
      # Try ending the literal with a newline.
      if current_literal.nest_and_try_closing(tok, @ts, @te)
         @cs = (pop_literal); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      end

      p = extend_string_eol_heredoc_intertwined(p)
    end

    extend_string_eol_words(current_literal, p)
   end
		end
	when 57 then
# line 731 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
    string = tok

    lookahead = extend_string_slice_end(lookahead)

    current_literal = literal
    if !current_literal.heredoc? &&
          (token = current_literal.nest_and_try_closing(string, @ts, @te, lookahead))
      if token[0] == :tLABEL_END
        p += 1
        pop_literal
        @root_lexer_state = @lexer.class.lex_en_expr_labelarg
      else
        if state = pop_literal
           @cs = (state);
        end
      end
      	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    else
      extend_string_for_token_range(current_literal, string)
    end
   end
		end
	when 58 then
# line 824 "lib/parser/lexer-strings.rl"
		begin
 @te = p
p = p - 1; begin 
    extend_interp_digit_var
   end
		end
	when 59 then
# line 813 "lib/parser/lexer-strings.rl"
		begin
 @te = p
p = p - 1; begin 
    current_literal = literal
    extend_interp_var(current_literal)
    emit_interp_var(interp_var_kind)
   end
		end
	when 60 then
# line 731 "lib/parser/lexer-strings.rl"
		begin
 @te = p
p = p - 1; begin 
    string = tok

    lookahead = extend_string_slice_end(lookahead)

    current_literal = literal
    if !current_literal.heredoc? &&
          (token = current_literal.nest_and_try_closing(string, @ts, @te, lookahead))
      if token[0] == :tLABEL_END
        p += 1
        pop_literal
        @root_lexer_state = @lexer.class.lex_en_expr_labelarg
      else
        if state = pop_literal
           @cs = (state);
        end
      end
      	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    else
      extend_string_for_token_range(current_literal, string)
    end
   end
		end
	when 61 then
# line 731 "lib/parser/lexer-strings.rl"
		begin
 begin p = (( @te))-1; end
 begin 
    string = tok

    lookahead = extend_string_slice_end(lookahead)

    current_literal = literal
    if !current_literal.heredoc? &&
          (token = current_literal.nest_and_try_closing(string, @ts, @te, lookahead))
      if token[0] == :tLABEL_END
        p += 1
        pop_literal
        @root_lexer_state = @lexer.class.lex_en_expr_labelarg
      else
        if state = pop_literal
           @cs = (state);
        end
      end
      	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    else
      extend_string_for_token_range(current_literal, string)
    end
   end
		end
	when 62 then
# line 761 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
    current_literal = literal
    extend_string_eol_check_eof(current_literal, pe)

    if current_literal.heredoc?
      line = extend_string_eol_heredoc_line

      # Try ending the heredoc with the complete most recently
      # scanned line. @herebody_s always refers to the start of such line.
      if current_literal.nest_and_try_closing(line, @herebody_s, @ts)
        # Adjust @herebody_s to point to the next line.
        @herebody_s = @te

        # Continue regular lexing after the heredoc reference (<<END).
        p = current_literal.heredoc_e - 1
         @cs = (pop_literal); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      else
        # Calculate indentation level for <<~HEREDOCs.
        current_literal.infer_indent_level(line)

        # Ditto.
        @herebody_s = @te
      end
    else
      # Try ending the literal with a newline.
      if current_literal.nest_and_try_closing(tok, @ts, @te)
         @cs = (pop_literal); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      end

      p = extend_string_eol_heredoc_intertwined(p)
    end

    extend_string_eol_words(current_literal, p)
   end
		end
	when 63 then
# line 731 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
    string = tok

    lookahead = extend_string_slice_end(lookahead)

    current_literal = literal
    if !current_literal.heredoc? &&
          (token = current_literal.nest_and_try_closing(string, @ts, @te, lookahead))
      if token[0] == :tLABEL_END
        p += 1
        pop_literal
        @root_lexer_state = @lexer.class.lex_en_expr_labelarg
      else
        if state = pop_literal
           @cs = (state);
        end
      end
      	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    else
      extend_string_for_token_range(current_literal, string)
    end
   end
		end
	when 64 then
# line 842 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
    current_literal = literal
    extend_interp_code(current_literal)
    @root_lexer_state = @lexer.class.lex_en_expr_value;
    	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

   end
		end
	when 65 then
# line 761 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
    current_literal = literal
    extend_string_eol_check_eof(current_literal, pe)

    if current_literal.heredoc?
      line = extend_string_eol_heredoc_line

      # Try ending the heredoc with the complete most recently
      # scanned line. @herebody_s always refers to the start of such line.
      if current_literal.nest_and_try_closing(line, @herebody_s, @ts)
        # Adjust @herebody_s to point to the next line.
        @herebody_s = @te

        # Continue regular lexing after the heredoc reference (<<END).
        p = current_literal.heredoc_e - 1
         @cs = (pop_literal); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      else
        # Calculate indentation level for <<~HEREDOCs.
        current_literal.infer_indent_level(line)

        # Ditto.
        @herebody_s = @te
      end
    else
      # Try ending the literal with a newline.
      if current_literal.nest_and_try_closing(tok, @ts, @te)
         @cs = (pop_literal); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      end

      p = extend_string_eol_heredoc_intertwined(p)
    end

    extend_string_eol_words(current_literal, p)
   end
		end
	when 66 then
# line 731 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
    string = tok

    lookahead = extend_string_slice_end(lookahead)

    current_literal = literal
    if !current_literal.heredoc? &&
          (token = current_literal.nest_and_try_closing(string, @ts, @te, lookahead))
      if token[0] == :tLABEL_END
        p += 1
        pop_literal
        @root_lexer_state = @lexer.class.lex_en_expr_labelarg
      else
        if state = pop_literal
           @cs = (state);
        end
      end
      	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    else
      extend_string_for_token_range(current_literal, string)
    end
   end
		end
	when 67 then
# line 824 "lib/parser/lexer-strings.rl"
		begin
 @te = p
p = p - 1; begin 
    extend_interp_digit_var
   end
		end
	when 68 then
# line 813 "lib/parser/lexer-strings.rl"
		begin
 @te = p
p = p - 1; begin 
    current_literal = literal
    extend_interp_var(current_literal)
    emit_interp_var(interp_var_kind)
   end
		end
	when 69 then
# line 796 "lib/parser/lexer-strings.rl"
		begin
 @te = p
p = p - 1; begin 
    literal.extend_space @ts, @te
   end
		end
	when 70 then
# line 731 "lib/parser/lexer-strings.rl"
		begin
 @te = p
p = p - 1; begin 
    string = tok

    lookahead = extend_string_slice_end(lookahead)

    current_literal = literal
    if !current_literal.heredoc? &&
          (token = current_literal.nest_and_try_closing(string, @ts, @te, lookahead))
      if token[0] == :tLABEL_END
        p += 1
        pop_literal
        @root_lexer_state = @lexer.class.lex_en_expr_labelarg
      else
        if state = pop_literal
           @cs = (state);
        end
      end
      	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    else
      extend_string_for_token_range(current_literal, string)
    end
   end
		end
	when 71 then
# line 731 "lib/parser/lexer-strings.rl"
		begin
 begin p = (( @te))-1; end
 begin 
    string = tok

    lookahead = extend_string_slice_end(lookahead)

    current_literal = literal
    if !current_literal.heredoc? &&
          (token = current_literal.nest_and_try_closing(string, @ts, @te, lookahead))
      if token[0] == :tLABEL_END
        p += 1
        pop_literal
        @root_lexer_state = @lexer.class.lex_en_expr_labelarg
      else
        if state = pop_literal
           @cs = (state);
        end
      end
      	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    else
      extend_string_for_token_range(current_literal, string)
    end
   end
		end
	when 72 then
# line 761 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
    current_literal = literal
    extend_string_eol_check_eof(current_literal, pe)

    if current_literal.heredoc?
      line = extend_string_eol_heredoc_line

      # Try ending the heredoc with the complete most recently
      # scanned line. @herebody_s always refers to the start of such line.
      if current_literal.nest_and_try_closing(line, @herebody_s, @ts)
        # Adjust @herebody_s to point to the next line.
        @herebody_s = @te

        # Continue regular lexing after the heredoc reference (<<END).
        p = current_literal.heredoc_e - 1
         @cs = (pop_literal); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      else
        # Calculate indentation level for <<~HEREDOCs.
        current_literal.infer_indent_level(line)

        # Ditto.
        @herebody_s = @te
      end
    else
      # Try ending the literal with a newline.
      if current_literal.nest_and_try_closing(tok, @ts, @te)
         @cs = (pop_literal); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      end

      p = extend_string_eol_heredoc_intertwined(p)
    end

    extend_string_eol_words(current_literal, p)
   end
		end
	when 73 then
# line 731 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
    string = tok

    lookahead = extend_string_slice_end(lookahead)

    current_literal = literal
    if !current_literal.heredoc? &&
          (token = current_literal.nest_and_try_closing(string, @ts, @te, lookahead))
      if token[0] == :tLABEL_END
        p += 1
        pop_literal
        @root_lexer_state = @lexer.class.lex_en_expr_labelarg
      else
        if state = pop_literal
           @cs = (state);
        end
      end
      	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    else
      extend_string_for_token_range(current_literal, string)
    end
   end
		end
	when 74 then
# line 796 "lib/parser/lexer-strings.rl"
		begin
 @te = p
p = p - 1; begin 
    literal.extend_space @ts, @te
   end
		end
	when 75 then
# line 928 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
        emit(:tREGEXP_OPT, tok(@ts, @te - 1), @ts, @te - 1)
        p = p - 1;
        @root_lexer_state = @lexer.class.lex_en_expr_end;
        	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 76 then
# line 915 "lib/parser/lexer-strings.rl"
		begin
 @te = p
p = p - 1; begin 
        unknown_options = tok.scan(/[^imxouesn]/)
        if unknown_options.any?
          diagnostic :error, :regexp_options,
                     { :options => unknown_options.join }
        end

        emit(:tREGEXP_OPT)
        @root_lexer_state = @lexer.class.lex_en_expr_end;
        	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 77 then
# line 953 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin 
        escape = ESCAPE_WHITESPACE[source_buffer.slice(@ts + 1, 1)]
        diagnostic :warning, :invalid_escape_use, { :escape => escape }, range

        p = @ts - 1
        @root_lexer_state = @lexer.class.lex_en_expr_end;
        	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 78 then
# line 946 "lib/parser/lexer-strings.rl"
		begin
 @te = p
p = p - 1; begin 
        emit_character_constant

        @root_lexer_state = @lexer.class.lex_en_expr_end; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 79 then
# line 964 "lib/parser/lexer-strings.rl"
		begin
 @te = p
p = p - 1; begin 
        p = @ts - 1
        @root_lexer_state = @lexer.class.lex_en_expr_end;
        	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 80 then
# line 946 "lib/parser/lexer-strings.rl"
		begin
 begin p = (( @te))-1; end
 begin 
        emit_character_constant

        @root_lexer_state = @lexer.class.lex_en_expr_end; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 81 then
# line 972 "lib/parser/lexer-strings.rl"
		begin
 @te = p+1
 begin  raise 'bug'  end
		end
# line 4935 "lib/parser/lexer-strings.rb"
			end # action switch
		end
	end
	if _trigger_goto
		next
	end
	end
	if _goto_level <= _again
	_acts = _lex_to_state_actions[ @cs]
	_nacts = _lex_actions[_acts]
	_acts += 1
	while _nacts > 0
		_nacts -= 1
		_acts += 1
		case _lex_actions[_acts - 1]
	when 23 then
# line 1 "NONE"
		begin
 @ts = nil;		end
# line 4955 "lib/parser/lexer-strings.rb"
		end # to state action switch
	end
	if _trigger_goto
		next
	end
	if  @cs == 0
		_goto_level = _out
		next
	end
	p += 1
	if p != pe
		_goto_level = _resume
		next
	end
	end
	if _goto_level <= _test_eof
	if p == eof
	if _lex_eof_trans[ @cs] > 0
		_trans = _lex_eof_trans[ @cs] - 1;
		_goto_level = _eof_trans
		next;
	end
	end
	end
	if _goto_level <= _out
		break
	end
	end
	end

# line 78 "lib/parser/lexer-strings.rl"
    # %

    # Ragel creates a local variable called `testEof` but it doesn't use
    # it in any assignment. This dead code is here to swallow the warning.
    # It has no runtime cost because Ruby doesn't produce any instructions from it.
    if false
      testEof
    end

    [p, @root_lexer_state]
  end

  def read_character_constant(p)
    @cs = self.class.lex_en_character

    advance(p)
  end

  #
  # === LITERAL STACK ===
  #

  def push_literal(*args)
    new_literal = Parser::Lexer::Literal.new(self, *args)
    @literal_stack.push(new_literal)
    @cs = next_state_for_literal(new_literal)
  end

  def next_state_for_literal(literal)
    if literal.words? && literal.backslash_delimited?
      if literal.interpolate?
        self.class.lex_en_interp_backslash_delimited_words
      else
        self.class.lex_en_plain_backslash_delimited_words
      end
    elsif literal.words? && !literal.backslash_delimited?
      if literal.interpolate?
        self.class.lex_en_interp_words
      else
        self.class.lex_en_plain_words
      end
    elsif !literal.words? && literal.backslash_delimited?
      if literal.interpolate?
        self.class.lex_en_interp_backslash_delimited
      else
        self.class.lex_en_plain_backslash_delimited
      end
    else
      if literal.interpolate?
        self.class.lex_en_interp_string
      else
        self.class.lex_en_plain_string
      end
    end
  end

  def continue_lexing(current_literal)
    @cs = next_state_for_literal(current_literal)
  end

  def literal
    @literal_stack.last
  end

  def pop_literal
    old_literal = @literal_stack.pop

    @dedent_level = old_literal.dedent_level

    if old_literal.type == :tREGEXP_BEG
      @root_lexer_state = @lexer.class.lex_en_inside_string

      # Fetch modifiers.
      self.class.lex_en_regexp_modifiers
    else
      @root_lexer_state = @lexer.class.lex_en_expr_end

      # Do nothing, yield to main lexer
      nil
    end
  end

  def close_interp_on_current_literal(p)
    current_literal = literal
    if current_literal
      if current_literal.end_interp_brace_and_try_closing
        if version?(18, 19)
          emit(:tRCURLY, '}'.freeze, p - 1, p)
          @lexer.cond.lexpop
          @lexer.cmdarg.lexpop
        else
          emit(:tSTRING_DEND, '}'.freeze, p - 1, p)
        end

        if current_literal.saved_herebody_s
          @herebody_s = current_literal.saved_herebody_s
        end

        continue_lexing(current_literal)

        return true
      end
    end
  end

  def dedent_level
    # We erase @dedent_level as a precaution to avoid accidentally
    # using a stale value.
    dedent_level, @dedent_level = @dedent_level, nil
    dedent_level
  end

  # This hook is triggered by "main" lexer on every newline character
  def on_newline(p)
    # After every heredoc was parsed, @herebody_s contains the
    # position of next token after all heredocs.
    if @herebody_s
      p = @herebody_s
      @herebody_s = nil
    end
    p
  end

  protected

  def eof_codepoint?(point)
    [0x04, 0x1a, 0x00].include? point
  end

  def version?(*versions)
    versions.include?(@version)
  end

  def tok(s = @ts, e = @te)
    @source_buffer.slice(s, e - s)
  end

  def range(s = @ts, e = @te)
    Parser::Source::Range.new(@source_buffer, s, e)
  end

  def emit(type, value = tok, s = @ts, e = @te)
    @lexer.send(:emit, type, value, s, e)
  end

  def diagnostic(type, reason, arguments=nil, location=range, highlights=[])
    @lexer.send(:diagnostic, type, reason, arguments, location, highlights)
  end

  def cond
    @lexer.cond
  end

  def emit_invalid_escapes?
    # always true for old Rubies
    return true if @version < 32

    # in "?\u123" case we don't push any literals
    # but we always emit invalid escapes
    return true if literal.nil?

    # Ruby >= 32, regexp, exceptional case
    !literal.regexp?
  end

  # String escaping

  def extend_string_escaped
    current_literal = literal
    # Get the first character after the backslash.
    escaped_char = source_buffer.slice(@escape_s, 1).chr

    if current_literal.munge_escape? escaped_char
      # If this particular literal uses this character as an opening
      # or closing delimiter, it is an escape sequence for that
      # particular character. Write it without the backslash.

      if current_literal.regexp? && REGEXP_META_CHARACTERS.match(escaped_char)
        # Regular expressions should include escaped delimiters in their
        # escaped form, except when the escaped character is
        # a closing delimiter but not a regexp metacharacter.
        #
        # The backslash itself cannot be used as a closing delimiter
        # at the same time as an escape symbol, but it is always munged,
        # so this branch also executes for the non-closing-delimiter case
        # for the backslash.
        current_literal.extend_string(tok, @ts, @te)
      else
        current_literal.extend_string(escaped_char, @ts, @te)
      end
    else
      # It does not. So this is an actual escape sequence, yay!
      if current_literal.squiggly_heredoc? && escaped_char == "\n".freeze
        # Squiggly heredocs like
        #   <<~-HERE
        #     1\
        #     2
        #   HERE
        # treat '\' as a line continuation, but still dedent the body, so the heredoc above becomes "12\n".
        # This information is emitted as is, without escaping,
        # later this escape sequence (\\\n) gets handled manually in the Lexer::Dedenter
        current_literal.extend_string(tok, @ts, @te)
      elsif current_literal.supports_line_continuation_via_slash? && escaped_char == "\n".freeze
        # Heredocs, regexp and a few other types of literals support line
        # continuation via \\\n sequence. The code like
        #   "a\
        #   b"
        # must be parsed as "ab"
        current_literal.extend_string(tok.gsub("\\\n".freeze, ''.freeze), @ts, @te)
      elsif current_literal.regexp? && @version >= 31 && %w[c C m M].include?(escaped_char)
        # Ruby >= 3.1 escapes \c- and \m chars, that's the only escape sequence
        # supported by regexes so far, so it needs a separate branch.
        current_literal.extend_string(@escape, @ts, @te)
      elsif current_literal.regexp?
        # Regular expressions should include escape sequences in their
        # escaped form. On the other hand, escaped newlines are removed (in cases like "\\C-\\\n\\M-x")
        current_literal.extend_string(tok.gsub("\\\n".freeze, ''.freeze), @ts, @te)
      else
        current_literal.extend_string(@escape || tok, @ts, @te)
      end
    end
  end

  def extend_interp_code(current_literal)
    current_literal.flush_string
    current_literal.extend_content

    emit(:tSTRING_DBEG, '#{'.freeze)

    if current_literal.heredoc?
      current_literal.saved_herebody_s = @herebody_s
      @herebody_s = nil
    end

    current_literal.start_interp_brace
    @lexer.command_start = true
  end

  def extend_interp_digit_var
    if @version >= 27
      literal.extend_string(tok, @ts, @te)
    else
      message = tok.start_with?('#@@') ? :cvar_name : :ivar_name
      diagnostic :error, message, { :name => tok(@ts + 1, @te) }, range(@ts + 1, @te)
    end
  end

  def extend_string_eol_check_eof(current_literal, pe)
    if @te == pe
      diagnostic :fatal, :string_eof, nil,
                 range(current_literal.str_s, current_literal.str_s + 1)
    end
  end

  def extend_string_eol_heredoc_line
    line = tok(@herebody_s, @ts).gsub(/\r+$/, ''.freeze)

    if version?(18, 19, 20)
      # See ruby:c48b4209c
      line = line.gsub(/\r.*$/, ''.freeze)
    end
    line
  end

  def extend_string_eol_heredoc_intertwined(p)
    if @herebody_s
      # This is a regular literal intertwined with a heredoc. Like:
      #
      #     p <<-foo+"1
      #     bar
      #     foo
      #     2"
      #
      # which, incidentally, evaluates to "bar\n1\n2".
      p = @herebody_s - 1
      @herebody_s = nil
    end
    p
  end

  def extend_string_eol_words(current_literal, p)
    if current_literal.words? && !eof_codepoint?(source_pts[p])
      current_literal.extend_space @ts, @te
    else
      # A literal newline is appended if the heredoc was _not_ closed
      # this time (see fbreak above). See also Literal#nest_and_try_closing
      # for rationale of calling #flush_string here.
      current_literal.extend_string tok, @ts, @te
      current_literal.flush_string
    end
  end

  def extend_string_slice_end(lookahead)
    # tLABEL_END is only possible in non-cond context on >= 2.2
    if @version >= 22 && !cond.active?
      lookahead = source_buffer.slice(@te, 2)
    end
    lookahead
  end

  def extend_string_for_token_range(current_literal, string)
    current_literal.extend_string(string, @ts, @te)
  end

  def encode_escape(ord)
    ord.chr.force_encoding(source_buffer.source.encoding)
  end

  def unescape_char(p)
    codepoint = source_pts[p - 1]

    if @version >= 30 && (codepoint == 117 || codepoint == 85) # 'u' or 'U'
      diagnostic :fatal, :invalid_escape
    end

    if (@escape = ESCAPES[codepoint]).nil?
      @escape = encode_escape(source_buffer.slice(p - 1, 1))
    end
  end

  def unicode_points(p)
    @escape = ""

    codepoints = tok(@escape_s + 2, p - 1)
    codepoint_s = @escape_s + 2

    if @version < 24
      if codepoints.start_with?(" ") || codepoints.start_with?("\t")
        diagnostic :fatal, :invalid_unicode_escape, nil,
                   range(@escape_s + 2, @escape_s + 3)
      end

      if spaces_p = codepoints.index(/[ \t]{2}/)
        diagnostic :fatal, :invalid_unicode_escape, nil,
                   range(codepoint_s + spaces_p + 1, codepoint_s + spaces_p + 2)
      end

      if codepoints.end_with?(" ") || codepoints.end_with?("\t")
        diagnostic :fatal, :invalid_unicode_escape, nil, range(p - 1, p)
      end
    end

    codepoints.scan(/([0-9a-fA-F]+)|([ \t]+)/).each do |(codepoint_str, spaces)|
      if spaces
        codepoint_s += spaces.length
      else
        codepoint = codepoint_str.to_i(16)

        if codepoint >= 0x110000
          diagnostic :error, :unicode_point_too_large, nil,
                     range(codepoint_s, codepoint_s + codepoint_str.length)
          break
        end

        @escape += codepoint.chr(Encoding::UTF_8)
        codepoint_s += codepoint_str.length
      end
    end
  end

  def read_post_meta_or_ctrl_char(p)
    @escape = source_buffer.slice(p - 1, 1).chr

    if @version >= 27 && ((0..8).include?(@escape.ord) || (14..31).include?(@escape.ord))
      diagnostic :fatal, :invalid_escape
    end
  end

  def extend_interp_var(current_literal)
    current_literal.flush_string
    current_literal.extend_content

    emit(:tSTRING_DVAR, nil, @ts, @ts + 1)

    @ts
  end

  def emit_interp_var(interp_var_kind)
    case interp_var_kind
    when :cvar
      @lexer.send(:emit_class_var, @ts + 1, @te)
    when :ivar
      @lexer.send(:emit_instance_var, @ts + 1, @te)
    when :gvar
      @lexer.send(:emit_global_var, @ts + 1, @te)
    end
  end

  def encode_escaped_char(p)
    @escape = encode_escape(tok(p - 2, p).to_i(16))
  end

  def slash_c_char
    @escape = encode_escape(@escape[0].ord & 0x9f)
  end

  def slash_m_char
    @escape = encode_escape(@escape[0].ord | 0x80)
  end

  def emit_character_constant
    value = @escape || tok(@ts + 1)

    if version?(18)
      emit(:tINTEGER, value.getbyte(0))
    else
      emit(:tCHARACTER, value)
    end
  end

  def check_ambiguous_slash(tm)
    if tok(tm, tm + 1) == '/'.freeze
      # Ambiguous regexp literal.
      if @version < 30
        diagnostic :warning, :ambiguous_literal, nil, range(tm, tm + 1)
      else
        diagnostic :warning, :ambiguous_regexp, nil, range(tm, tm + 1)
      end
    end
  end

  def check_invalid_escapes(p)
    if emit_invalid_escapes?
      diagnostic :fatal, :invalid_unicode_escape, nil, range(@escape_s - 1, p)
    end
  end

  ESCAPE_WHITESPACE = {
    " "  => '\s', "\r" => '\r', "\n" => '\n', "\t" => '\t',
    "\v" => '\v', "\f" => '\f'
  }

  
# line 975 "lib/parser/lexer-strings.rl"

  # %

end
