# -*- encoding:utf-8; warn-indent:false; frozen_string_literal: true  -*-
#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.7.3
# from Racc grammar file "ruby21.y".
#

require 'racc/parser.rb'


require_relative '../parser'

module Parser
  class Ruby21 < Parser::Base


  def version
    21
  end

  def default_encoding
    Encoding::UTF_8
  end

  def local_push
    @static_env.extend_static
    @lexer.cmdarg.push(false)
    @lexer.cond.push(false)
  end

  def local_pop
    @static_env.unextend
    @lexer.cmdarg.pop
    @lexer.cond.pop
  end
##### State transition tables begin ###

racc_action_table = [
  -479,   216,   217,   216,   217,   270,   -97,  -479,  -479,  -479,
   219,   536,  -479,  -479,  -479,   615,  -479,   270,  -286,   578,
   111,   615,   216,   217,  -479,   580,  -479,  -479,  -479,   526,
   270,   123,   525,   -98,  -105,   270,  -479,  -479,   557,  -479,
  -479,  -479,  -479,  -479,   208,   536,  -493,   536,   536,  -104,
   614,   220,  -100,   691,   615,   691,   614,   115,  -286,   781,
   209,   542,   114,  -494,  -102,   867,  -479,  -479,  -479,  -479,
  -479,  -479,  -479,  -479,  -479,  -479,  -479,  -479,  -479,  -479,
   210,   265,  -479,  -479,  -479,   577,  -479,  -479,   -99,   614,
  -479,   579,   269,  -479,  -479,   220,  -479,   220,  -479,   265,
  -479,   -97,  -479,  -479,   269,  -479,  -479,  -479,  -479,  -479,
   536,  -479,  -482,  -479,  -100,   -88,   220,   269,   -98,  -482,
  -482,  -482,   269,  -102,  -482,  -482,  -482,  -479,  -482,   115,
  -479,  -479,  -479,  -479,   114,  -479,  -482,  -479,  -482,  -482,
  -482,  -479,   -89,   -96,   556,   214,  -101,   691,  -482,  -482,
  -101,  -482,  -482,  -482,  -482,  -482,   -99,   115,   -95,   115,
   -91,   -91,   114,   115,   114,   115,   115,   115,   114,   818,
   114,   114,   114,   -93,   866,  -103,   216,   217,  -482,  -482,
  -482,  -482,  -482,  -482,  -482,  -482,  -482,  -482,  -482,  -482,
  -482,  -482,   -96,   444,  -482,  -482,  -482,   -90,  -482,  -482,
  -105,   -93,  -482,    93,    94,  -482,  -482,   215,  -482,   548,
  -482,  -575,  -482,   549,  -482,  -482,   514,  -482,  -482,  -482,
  -482,  -482,  -289,  -482,   259,  -482,   -91,  -575,   115,  -289,
  -289,  -289,  -100,   114,   317,  -289,  -289,  -100,  -289,  -482,
  -576,  -102,  -482,  -482,  -482,  -482,  -102,  -482,  -105,  -482,
   262,   115,   565,  -482,   746,   -92,   114,   263,  -289,  -289,
   318,  -289,  -289,  -289,  -289,  -289,   -91,   -93,  -101,   -91,
   115,  -104,   565,  -101,   -99,   114,   615,   -91,   115,   -99,
   220,    95,    96,   114,   -94,  -493,    93,    94,  -289,  -289,
  -289,  -289,  -289,  -289,  -289,  -289,  -289,  -289,  -289,  -289,
  -289,  -289,   565,   747,  -289,  -289,  -289,   -93,   598,  -100,
   -93,   614,  -289,   -83,  -494,  -289,   567,   566,   -93,   386,
  -289,   -69,  -289,   399,  -289,  -289,   723,  -289,  -289,  -289,
  -289,  -289,   -88,  -289,  -579,  -289,   567,   566,   514,   836,
   -97,  -579,  -579,  -579,   593,   443,   725,  -579,  -579,  -289,
  -579,   115,  -289,  -289,   445,   -94,   114,  -289,  -102,  -579,
   446,   -89,  -572,  -103,    95,    96,   567,   566,   563,   -98,
  -579,  -579,  -479,  -579,  -579,  -579,  -579,  -579,   669,  -479,
   666,   665,   664,   219,   667,   -95,   669,   565,   666,   665,
   664,   -91,   667,  -104,   -99,   216,   217,   477,  -573,  -100,
  -579,  -579,  -579,  -579,  -579,  -579,  -579,  -579,  -579,  -579,
  -579,  -579,  -579,  -579,  -479,   212,  -579,  -579,  -579,   486,
   599,  -479,   213,   488,  -579,   565,  -572,  -579,   673,   490,
  -479,   211,  -579,   594,  -579,  -579,  -579,  -579,   676,  -579,
  -579,  -579,  -579,  -579,   497,  -579,  -579,  -579,  -482,  -572,
  -482,   567,   566,   568,   565,  -482,   -68,  -482,   220,   565,
   885,  -579,  -573,   565,  -579,  -579,  -482,   -92,   982,  -579,
  -579,   684,   683,   500,  -489,  -101,   677,  -579,  -579,  -579,
   -93,  -489,  -579,  -579,  -579,  -573,  -579,  -579,  -102,   567,
   566,   570,   501,    81,  -579,  -579,  -579,  -579,  -579,  -575,
   508,  -333,   274,  -579,   732,    82,  -579,  -579,  -333,  -579,
  -579,  -579,  -579,  -579,   440,    83,   220,  -333,   567,   566,
   572,   441,  -579,   567,   566,   576,  -488,   567,   566,   581,
   442,   526,   526,  -488,   528,   528,  -579,  -579,  -579,  -579,
  -579,  -579,  -579,  -579,  -579,  -579,  -579,  -579,  -579,  -579,
   265,   -90,  -579,  -579,  -579,   240,   748,  -579,  -491,   -99,
  -579,   511,  -490,  -579,  -579,  -491,  -579,   515,  -579,  -490,
  -579,   240,  -579,  -579,  -491,  -579,  -579,  -579,  -579,  -579,
   115,  -579,  -579,  -579,   220,   114,  -487,   237,   529,  -484,
   530,   239,   238,  -487,   235,   236,  -484,  -579,   490,   115,
  -579,  -579,  -579,  -579,   114,  -579,  -289,  -579,    74,    75,
    71,  -101,    57,  -289,  -289,  -289,    63,    64,  -289,  -289,
  -289,    67,  -289,    65,    66,    68,    30,    31,    72,    73,
   216,   217,  -289,  -289,  -289,    29,    28,    27,   103,   102,
   104,   105,  -289,  -289,   232,  -289,  -289,  -289,  -289,  -289,
   526,    45,   542,   528,   107,   106,   108,    97,    56,    99,
    98,   100,   388,   101,   109,   110,   546,    93,    94,    42,
    43,    41,  -289,  -289,  -289,  -289,  -289,  -289,  -289,  -289,
  -289,  -289,  -289,  -289,  -289,  -289,   216,   217,  -289,  -289,
  -289,   225,   749,  -289,   231,   547,  -289,    58,    59,  -289,
  -289,    60,  -289,  -485,  -289,   582,  -289,    44,  -289,  -289,
  -485,  -289,  -289,  -289,  -289,  -289,   230,  -289,  -486,  -289,
   585,    91,    81,    84,    85,  -486,    86,    88,    87,    89,
   813,   781,  -261,  -289,    82,    90,  -289,  -289,  -289,  -289,
   587,  -289,    62,  -289,    83,    95,    96,  -103,     5,    74,
    75,    71,     9,    57,   262,   115,   220,    63,    64,   967,
   114,   263,    67,   220,    65,    66,    68,    30,    31,    72,
    73,   591,  -579,   684,   683,   592,    29,    28,    27,   103,
   102,   104,   105,   396,   265,    19,   813,   781,   398,   397,
   603,     8,    45,     7,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   602,   101,   109,   110,   605,    93,    94,
    42,    43,    41,   240,   244,   249,   250,   251,   246,   248,
   256,   257,   252,   253,  -579,   233,   234,  -492,   240,   254,
   255,  -579,    40,   240,  -492,    33,  -575,   240,    58,    59,
  -579,   240,    60,  -492,    35,   237,   220,   243,    44,   239,
   238,   673,   235,   236,   247,   245,   241,    20,   242,  -579,
   220,   676,    91,    81,    84,    85,   506,    86,    88,    87,
    89,   220,   -83,   507,   633,    82,    90,   220,   258,   519,
  -238,   644,   505,    62,   649,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   684,   683,   650,    63,    64,   677,
   652,   687,    67,   542,    65,    66,    68,    30,    31,    72,
    73,   694,  -415,   712,   722,   726,    29,    28,    27,   103,
   102,   104,   105,   717,   718,    19,   727,   719,   109,   110,
   588,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,  -262,   101,   109,   110,   733,    93,    94,
    42,    43,    41,   240,   244,   249,   250,   251,   246,   248,
   256,   257,   252,   253,  -415,   233,   234,  -279,   477,   254,
   255,  -415,    40,   477,  -279,   295,   220,   751,    58,    59,
  -415,   259,    60,  -279,    35,   237,   488,   243,    44,   239,
   238,   490,   235,   236,   247,   245,   241,    20,   242,  -415,
   774,   644,    91,    81,    84,    85,   516,    86,    88,    87,
    89,   220,   265,   517,   265,    82,    90,   644,   258,   240,
   781,   220,   442,    62,   790,    83,    95,    96,     5,    74,
    75,    71,     9,    57,   793,   794,   544,    63,    64,   796,
   798,   800,    67,   545,    65,    66,    68,    30,    31,    72,
    73,   808,   543,   809,   810,   781,    29,    28,    27,   103,
   102,   104,   105,   817,   220,    19,   220,   826,  -263,   835,
   603,     8,    45,     7,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   838,   101,   109,   110,   793,    93,    94,
    42,    43,    41,   240,   244,   249,   250,   251,   246,   248,
   256,   257,   252,   253,   552,   233,   234,  -290,   841,   254,
   255,   551,    40,   843,  -290,    33,   845,   847,    58,    59,
   553,   220,    60,  -290,    35,   237,   849,   243,    44,   239,
   238,   850,   235,   236,   247,   245,   241,    20,   242,   853,
   855,   856,    91,    81,    84,    85,  -290,    86,    88,    87,
    89,   644,   858,  -290,  -261,    82,    90,   862,   258,   864,
   220,   883,  -290,    62,   220,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   887,   889,   552,    63,    64,   895,
   898,   220,    67,   918,    65,    66,    68,    30,    31,    72,
    73,   902,   553,  -264,   912,   919,    29,    28,    27,   103,
   102,   104,   105,   920,   915,    19,   666,   665,   664,   931,
   667,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   793,   101,   109,   110,   933,    93,    94,
    42,    43,    41,   240,   244,   249,   250,   251,   246,   248,
   256,   257,   252,   253,  -289,   233,   234,   552,   935,   254,
   255,  -289,    40,   937,   918,    33,  -576,   939,    58,    59,
  -289,   939,    60,   553,    35,   237,   220,   243,    44,   239,
   238,   945,   235,   236,   247,   245,   241,    20,   242,   948,
   949,   954,    91,    81,    84,    85,  -491,    86,    88,    87,
    89,   712,   793,  -491,   957,    82,    90,   959,   258,   961,
   963,   963,  -491,    62,   974,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   984,  -576,  -492,    63,    64,  -575,
   649,   999,    67,  -492,    65,    66,    68,    30,    31,    72,
    73,  1000,  -492,  1001,   939,   939,    29,    28,    27,   103,
   102,   104,   105,   939,   915,    19,   666,   665,   664,  1006,
   667,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   984,   101,   109,   110,  1009,    93,    94,
    42,    43,    41,   240,   244,   249,   250,   251,   246,   248,
   256,   257,   252,   253,  -289,   233,   234,   972,  1010,   254,
   255,  -289,    40,  1011,   973,    33,  -576,   963,    58,    59,
  -289,   963,    60,   971,    35,   237,   963,   243,    44,   239,
   238,   220,   235,   236,   247,   245,   241,    20,   242,   984,
   939,   984,    91,    81,    84,    85,  -279,    86,    88,    87,
    89,   963,   nil,  -279,   nil,    82,    90,   nil,   258,   nil,
   nil,   nil,  -279,    62,   nil,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,  -290,    63,    64,   nil,
   nil,   nil,    67,  -290,    65,    66,    68,    30,    31,    72,
    73,   nil,  -290,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   118,   119,   120,   121,
   122,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,   244,   249,   250,   251,   246,   248,
   256,   257,   252,   253,  -289,   233,   234,   nil,   nil,   254,
   255,  -289,    40,   nil,   nil,   295,  -576,   nil,    58,    59,
  -289,   nil,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   nil,   235,   236,   247,   245,   241,    20,   242,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   258,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   118,   119,   120,   121,   122,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   118,   119,   120,   121,
   122,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,   244,   249,   250,   251,   246,   248,
   256,   257,   252,   253,   nil,   233,   234,   nil,   nil,   254,
   255,   nil,    40,   nil,   nil,   295,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   nil,   235,   236,   247,   245,   241,    20,   242,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   220,   258,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,   244,   249,   250,   251,   246,   248,
   256,   257,   252,   253,   nil,   233,   234,   nil,   nil,   254,
   255,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   nil,   235,   236,   247,   245,   241,    20,   242,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   258,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,     5,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,     7,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,   244,   249,   250,   251,   246,   248,
   256,   257,   252,   253,   nil,   233,   234,   nil,   nil,   254,
   255,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   nil,   235,   236,   247,   245,   241,    20,   242,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   258,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,   244,   249,   250,   251,   246,   248,
   256,   257,   252,   253,   nil,   233,   234,   nil,   nil,   254,
   255,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   nil,   235,   236,   247,   245,   241,    20,   242,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   258,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,   244,   249,   250,   251,   246,   248,
   256,   257,   252,   253,   nil,   233,   234,   nil,   nil,   254,
   255,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   nil,   235,   236,   247,   245,   241,    20,   242,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   258,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,   244,   249,   250,   251,   246,   248,
   256,   257,   252,   253,   nil,   233,   234,   nil,   nil,   254,
   255,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   nil,   235,   236,   247,   245,   241,    20,   242,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   258,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,   244,   249,   250,   251,   246,   248,
   256,   257,   252,   253,   nil,   233,   234,   nil,   nil,   254,
   255,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   nil,   235,   236,   247,   245,   241,    20,   242,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   258,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,   244,   249,   250,   251,   246,   248,
   256,   257,   252,   253,   nil,   233,   234,   nil,   nil,   254,
   255,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   nil,   235,   236,   247,   245,   241,    20,   242,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   258,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,   244,   249,   250,   251,   246,   248,
   256,   257,   252,   253,   nil,   233,   234,   nil,   nil,   254,
   255,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   nil,   235,   236,   247,   245,   241,    20,   242,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   258,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,   244,   249,   250,   251,   246,   248,
   256,   257,   252,   253,   nil,   233,   234,   nil,   nil,   254,
   255,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   nil,   235,   236,   247,   245,   241,    20,   242,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   258,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,   244,   249,   250,   251,   246,   248,
   256,   257,   252,   253,   nil,   233,   234,   nil,   nil,   254,
   255,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   nil,   235,   236,   247,   245,   241,    20,   242,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   258,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   669,    19,   666,   665,   664,   nil,
   667,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   669,   nil,   666,   665,   664,   nil,   667,
   nil,   802,   nil,   nil,   240,   nil,   nil,   nil,   nil,   nil,
   805,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   nil,   nil,   nil,    44,   nil,
   802,   nil,   nil,   nil,   nil,   nil,   237,    20,   nil,   805,
   239,   238,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   669,   nil,
   666,   665,   664,    62,   667,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   802,    29,    28,    27,   103,
   102,   104,   105,   nil,   944,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,   244,   249,   250,   251,   246,   248,
   256,   257,   252,   253,   nil,  -598,  -598,   nil,   nil,   254,
   255,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   240,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   nil,   235,   236,   247,   245,   241,    20,   242,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   237,   nil,    82,    90,   239,   238,   nil,
   235,   236,   nil,    62,   nil,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,   244,   249,   250,   251,   246,   248,
   256,   257,   252,   253,   nil,  -598,  -598,   nil,   nil,   254,
   255,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   240,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   nil,   235,   236,   247,   245,   241,    20,   242,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   237,   nil,    82,    90,   239,   238,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   669,    19,   666,   665,   664,   nil,
   667,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,  -598,  -598,  -598,  -598,   246,   248,
   nil,   802,  -598,  -598,   nil,   nil,   nil,   nil,   nil,   254,
   255,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   nil,   235,   236,   247,   245,   241,    20,   242,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   669,   nil,
   666,   665,   664,    62,   667,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   802,    29,    28,    27,   103,
   102,   104,   105,   nil,   669,    19,   666,   665,   664,   nil,
   667,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,   669,   nil,   666,   665,   664,   nil,
   667,   802,   669,   nil,   666,   665,   664,   nil,   667,   254,
   255,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   802,   235,   236,   nil,   nil,   241,    20,   242,   802,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   254,
   255,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   nil,   235,   236,   nil,   nil,   241,    20,   242,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   254,
   255,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   nil,   235,   236,   nil,   nil,   241,    20,   242,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   254,
   255,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   nil,   235,   236,   nil,   nil,   241,    20,   242,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,  -598,  -598,  -598,  -598,   246,   248,
   nil,   nil,  -598,  -598,   nil,   nil,   nil,   nil,   nil,   254,
   255,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   nil,   235,   236,   247,   245,   241,    20,   242,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,  -598,  -598,  -598,  -598,   246,   248,
   nil,   nil,  -598,  -598,   nil,   nil,   nil,   nil,   nil,   254,
   255,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   nil,   235,   236,   247,   245,   241,    20,   242,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,  -598,  -598,  -598,  -598,   246,   248,
   nil,   nil,  -598,  -598,   nil,   nil,   nil,   nil,   nil,   254,
   255,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   nil,   235,   236,   247,   245,   241,    20,   242,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,  -598,  -598,  -598,  -598,   246,   248,
   nil,   nil,  -598,  -598,   nil,   nil,   nil,   nil,   nil,   254,
   255,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   nil,   235,   236,   247,   245,   241,    20,   242,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,  -598,  -598,  -598,  -598,   246,   248,
   nil,   nil,  -598,  -598,   nil,   nil,   nil,   nil,   nil,   254,
   255,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   nil,   235,   236,   247,   245,   241,    20,   242,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   291,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   293,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,   244,   249,   250,   251,   246,   248,
   nil,   nil,   252,   253,   nil,   nil,   nil,   nil,   nil,   254,
   255,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   237,   nil,   243,    44,   239,
   238,   nil,   235,   236,   247,   245,   241,    20,   242,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,    74,    75,
    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,    30,    31,    72,    73,
   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,
   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,
     8,    45,     7,    10,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   240,   244,   249,   250,   251,   246,   248,   256,
   nil,   252,   253,   nil,   nil,   nil,   nil,   nil,   254,   255,
   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,    35,   237,   nil,   243,    44,   239,   238,
   nil,   235,   236,   247,   245,   241,    20,   242,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   285,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   240,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   254,
   255,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   283,   237,   281,   243,    44,   239,
   238,   286,   235,   236,   nil,   nil,   nil,   230,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   285,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   240,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   254,   255,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   283,   237,   281,   243,    44,
   239,   238,   286,   235,   236,   nil,   nil,   nil,   230,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,
    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,
    27,   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   285,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   240,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   254,   255,   nil,   225,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   283,   237,   281,   nil,
    44,   239,   238,   286,   235,   236,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   310,   311,    72,    73,   nil,   nil,   nil,   nil,   nil,   306,
   307,   313,   103,   102,   104,   105,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,   308,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,   nil,   nil,   314,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   304,   nil,   nil,   300,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   299,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   310,   311,    72,    73,   nil,   nil,   nil,   nil,   nil,
   306,   307,   313,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,   308,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,   nil,   nil,   314,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   304,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   669,
   nil,   666,   665,   664,   673,   667,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   676,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,   316,   nil,   671,    62,   nil,    83,
    95,    96,    74,    75,    71,   nil,    57,   684,   683,   nil,
    63,    64,   677,   nil,   nil,    67,   nil,    65,    66,    68,
   310,   311,    72,    73,   nil,   nil,   nil,   nil,   nil,   306,
   307,   313,   103,   102,   104,   105,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   310,   311,    72,    73,   nil,   nil,   nil,   nil,   nil,
   306,   307,   313,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   310,   311,    72,    73,   nil,   nil,   nil,   nil,
   nil,   306,   307,   313,   103,   102,   104,   105,   nil,   nil,
   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,
   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   310,   311,    72,    73,   nil,   nil,   nil,
   nil,   nil,   306,   307,   313,   103,   102,   104,   105,   nil,
   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   285,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   283,   nil,   nil,   nil,    44,   nil,   nil,   286,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   310,   311,    72,    73,   nil,   nil,
   nil,   nil,   nil,   306,   307,   313,   103,   102,   104,   105,
   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   285,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,
   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   286,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,    30,    31,    72,    73,   nil,
   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,
   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,    30,    31,    72,    73,
   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,
   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   115,   nil,   nil,
   nil,   nil,   114,    62,   nil,    83,    95,    96,    74,    75,
    71,   nil,    57,   nil,   nil,   nil,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   310,   311,    72,    73,
   nil,   nil,   nil,   nil,   nil,   306,   307,   313,   103,   102,
   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,
   nil,   308,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,   nil,
   nil,   314,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   348,   nil,   nil,    33,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,    35,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   310,   311,    72,
    73,   nil,   nil,   nil,   nil,   nil,   306,   307,   313,   103,
   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,
   nil,   nil,   308,   nil,   nil,   107,   106,   108,   353,    56,
    99,    98,   354,   nil,   101,   109,   110,   nil,    93,    94,
   nil,   nil,   314,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   360,
   nil,   nil,   355,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   310,   311,
    72,    73,   nil,   nil,   nil,   nil,   nil,   306,   307,   313,
   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,
   nil,   nil,   nil,   308,   nil,   nil,   107,   106,   108,   353,
    56,    99,    98,   354,   nil,   101,   109,   110,   nil,    93,
    94,   nil,   nil,   314,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   355,   nil,   nil,   231,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   669,   nil,   666,   665,
   664,   673,   667,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   676,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,   nil,   nil,   671,    62,   nil,    83,    95,    96,    74,
    75,    71,     9,    57,   684,   683,   nil,    63,    64,   677,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,     7,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   388,    62,   nil,    83,    95,    96,    74,    75,
    71,   nil,    57,   nil,   nil,   nil,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,    30,    31,    72,    73,
   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,
   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   103,   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,
    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,
    27,   103,   102,   104,   105,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,   nil,   nil,   nil,    62,   nil,    83,    95,    96,
    74,    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   103,   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,     8,    45,   nil,    10,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,    35,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,
    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,
    27,   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   404,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,
    28,    27,   103,   102,   104,   105,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,
    29,    28,    27,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   285,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   283,   nil,
   281,   nil,    44,   nil,   nil,   286,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,
   nil,    29,    28,    27,   103,   102,   104,   105,   nil,   nil,
   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,
   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,
   nil,   nil,    29,    28,    27,   103,   102,   104,   105,   nil,
   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   404,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,
   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,    30,    31,    72,    73,   nil,
   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,
   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,    30,    31,    72,    73,
   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,
   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   220,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   310,   311,
    72,    73,   nil,   nil,   nil,   nil,   nil,   306,   307,   313,
   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   310,
   311,    72,    73,   nil,   nil,   nil,   nil,   nil,   306,   307,
   313,   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   310,   311,    72,    73,   nil,   nil,   nil,   nil,   nil,   306,
   307,   313,   103,   102,   104,   105,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   310,   311,    72,    73,   nil,   nil,   nil,   nil,   nil,
   306,   307,   313,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   310,   311,    72,    73,   nil,   nil,   nil,   nil,
   nil,   306,   307,   313,   103,   102,   104,   105,   nil,   nil,
   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,
   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   310,   311,    72,    73,   nil,   nil,   nil,
   nil,   nil,   306,   307,   313,   103,   102,   104,   105,   nil,
   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   310,   311,    72,    73,   nil,   nil,
   nil,   nil,   nil,   306,   307,   313,   103,   102,   104,   105,
   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,
   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   310,   311,    72,    73,   nil,
   nil,   nil,   nil,   nil,   306,   307,   313,   103,   102,   104,
   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   310,   311,    72,    73,
   nil,   nil,   nil,   nil,   nil,   306,   307,   313,   103,   102,
   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   310,   311,    72,
    73,   nil,   nil,   nil,   nil,   nil,   306,   307,   313,   103,
   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   310,   311,
    72,    73,   nil,   nil,   nil,   nil,   nil,   306,   307,   313,
   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   310,
   311,    72,    73,   nil,   nil,   nil,   nil,   nil,   306,   307,
   313,   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   310,   311,    72,    73,   nil,   nil,   nil,   nil,   nil,   306,
   307,   313,   103,   102,   104,   105,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   310,   311,    72,    73,   nil,   nil,   nil,   nil,   nil,
   306,   307,   313,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   310,   311,    72,    73,   nil,   nil,   nil,   nil,
   nil,   306,   307,   313,   103,   102,   104,   105,   nil,   nil,
   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,
   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   310,   311,    72,    73,   nil,   nil,   nil,
   nil,   nil,   306,   307,   313,   103,   102,   104,   105,   nil,
   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   310,   311,    72,    73,   nil,   nil,
   nil,   nil,   nil,   306,   307,   313,   103,   102,   104,   105,
   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,
   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   310,   311,    72,    73,   nil,
   nil,   nil,   nil,   nil,   306,   307,   313,   103,   102,   104,
   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   310,   311,    72,    73,
   nil,   nil,   nil,   nil,   nil,   306,   307,   313,   103,   102,
   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   310,   311,    72,
    73,   nil,   nil,   nil,   nil,   nil,   306,   307,   313,   103,
   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   310,   311,
    72,    73,   nil,   nil,   nil,   nil,   nil,   306,   307,   313,
   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   310,
   311,    72,    73,   nil,   nil,   nil,   nil,   nil,   306,   307,
   313,   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   310,   311,    72,    73,   nil,   nil,   nil,   nil,   nil,   306,
   307,   313,   103,   102,   104,   105,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   310,   311,    72,    73,   nil,   nil,   nil,   nil,   nil,
   306,   307,   313,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   310,   311,    72,    73,   nil,   nil,   nil,   nil,
   nil,   306,   307,   313,   103,   102,   104,   105,   nil,   nil,
   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,
   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   310,   311,    72,    73,   nil,   nil,   nil,
   nil,   nil,   306,   307,   313,   103,   102,   104,   105,   nil,
   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   310,   311,    72,    73,   nil,   nil,
   nil,   nil,   nil,   306,   307,   313,   103,   102,   104,   105,
   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,
   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,    30,    31,    72,    73,   nil,
   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,
   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   285,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   283,   nil,   281,   nil,    44,   nil,   nil,   286,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,    30,    31,    72,    73,
   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,
   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   285,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   283,   nil,   281,   nil,    44,   nil,   nil,
   286,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   285,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   283,   nil,   281,   nil,    44,   nil,
   nil,   286,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   220,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   310,   311,
    72,    73,   nil,   nil,   nil,   nil,   nil,   306,   307,   313,
   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   310,
   311,    72,    73,   nil,   nil,   nil,   nil,   nil,   306,   307,
   313,   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   310,   311,    72,    73,   nil,   nil,   nil,   nil,   nil,   306,
   307,   313,   103,   102,   104,   105,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   310,   311,    72,    73,   nil,   nil,   nil,   nil,   nil,
   306,   307,   313,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,   nil,   nil,   nil,    62,   nil,    83,
    95,    96,    74,    75,    71,     9,    57,   nil,   nil,   nil,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,
    28,    27,   103,   102,   104,   105,   nil,   nil,    19,   nil,
   nil,   nil,   nil,   nil,     8,    45,   nil,    10,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,    33,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,    35,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    20,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   310,   311,    72,    73,   nil,   nil,   nil,   nil,   nil,
   306,   307,   313,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,   308,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,   nil,   nil,   314,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   304,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   669,
   nil,   666,   665,   664,   673,   667,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   676,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,   503,   nil,   671,    62,   nil,    83,
    95,    96,    74,    75,    71,   nil,    57,   684,   683,   nil,
    63,    64,   677,   nil,   nil,    67,   nil,    65,    66,    68,
   310,   311,    72,    73,   nil,   nil,   nil,   nil,   nil,   306,
   307,   313,   103,   102,   104,   105,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,   308,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,   nil,   nil,   314,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   304,   nil,   nil,   300,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   310,   311,    72,    73,   nil,   nil,   nil,   nil,   nil,
   306,   307,   313,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,
   519,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,
   nil,    29,    28,    27,   103,   102,   104,   105,   nil,   nil,
    19,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,
   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,
   nil,   nil,    29,    28,    27,   103,   102,   104,   105,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,
   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,    30,    31,    72,    73,   nil,
   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,
   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,    30,    31,    72,    73,
   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,
   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   310,   311,    72,
    73,   nil,   nil,   nil,   nil,   nil,   306,   307,   313,   103,
   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   285,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   283,   nil,   281,   nil,    44,
   nil,   nil,   286,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   310,
   311,    72,    73,   nil,   nil,   nil,   nil,   nil,   306,   307,
   313,   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   310,   311,    72,    73,   nil,   nil,   nil,   nil,   nil,   306,
   307,   313,   103,   102,   104,   105,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   310,   311,    72,    73,   nil,   nil,   nil,   nil,   nil,
   306,   307,   313,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   310,   311,    72,    73,   nil,   nil,   nil,   nil,
   nil,   306,   307,   313,   103,   102,   104,   105,   nil,   nil,
   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   285,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,
   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   627,
   nil,   281,   nil,    44,   nil,   nil,   286,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   310,   311,    72,    73,   nil,   nil,   nil,
   nil,   nil,   306,   307,   313,   103,   102,   104,   105,   nil,
   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   285,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   281,   nil,    44,   nil,   nil,   286,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   310,   311,    72,    73,   nil,   nil,
   nil,   nil,   nil,   306,   307,   313,   103,   102,   104,   105,
   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,
   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,    74,    75,    71,     9,    57,
   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,
   nil,   nil,    29,    28,    27,   103,   102,   104,   105,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,   293,
    10,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
    35,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,   nil,   nil,   388,    62,
   nil,    83,    95,    96,    74,    75,    71,   nil,    57,   nil,
   nil,   nil,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   310,   311,    72,    73,   nil,   nil,   nil,   nil,
   nil,   306,   307,   313,   103,   102,   104,   105,   nil,   nil,
   232,   nil,   nil,   nil,   nil,   nil,   nil,   308,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,   nil,   nil,   314,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   304,   nil,   nil,
   300,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,
   nil,   nil,    29,    28,    27,   103,   102,   104,   105,   nil,
   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   285,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   283,   nil,   281,   nil,    44,   nil,   nil,   286,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   310,   311,    72,    73,   nil,   nil,
   nil,   nil,   nil,   306,   307,   313,   103,   102,   104,   105,
   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,   308,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,   nil,   nil,   314,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   304,
   nil,   nil,   300,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   310,   311,    72,    73,   nil,
   nil,   nil,   nil,   nil,   306,   307,   313,   103,   102,   104,
   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   310,   311,    72,    73,
   nil,   nil,   nil,   nil,   nil,   306,   307,   313,   103,   102,
   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   310,   311,
    72,    73,   nil,   nil,   nil,   nil,   nil,   306,   307,   313,
   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   285,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   627,   nil,   nil,   nil,    44,
   nil,   nil,   286,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   310,
   311,    72,    73,   nil,   nil,   nil,   nil,   nil,   306,   307,
   313,   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   285,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   286,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   310,   311,    72,    73,   nil,   nil,   nil,   nil,   nil,   306,
   307,   313,   103,   102,   104,   105,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   283,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,
    29,    28,    27,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   285,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   283,   nil,
   281,   nil,    44,   nil,   nil,   286,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,
   nil,    29,    28,    27,   103,   102,   104,   105,   nil,   nil,
   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   285,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,
   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   283,
   nil,   281,   nil,    44,   nil,   nil,   286,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   310,   311,    72,    73,   nil,   nil,   nil,
   nil,   nil,   306,   307,   313,   103,   102,   104,   105,   nil,
   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   730,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   310,   311,    72,    73,   nil,   nil,
   nil,   nil,   nil,   306,   307,   313,   103,   102,   104,   105,
   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,
   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   310,   311,    72,    73,   nil,
   nil,   nil,   nil,   nil,   306,   307,   313,   103,   102,   104,
   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   285,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   627,   nil,   281,   nil,    44,   nil,   nil,   286,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   310,   311,    72,    73,
   nil,   nil,   nil,   nil,   nil,   306,   307,   313,   103,   102,
   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   285,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   281,   nil,    44,   nil,   nil,
   286,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,
    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,
    27,   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,
    28,    27,   103,   102,   104,   105,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,
    29,    28,    27,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   310,   311,    72,    73,   nil,   nil,   nil,   nil,
   nil,   306,   307,   313,   103,   102,   104,   105,   nil,   nil,
   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,
   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   310,   311,    72,    73,   nil,   nil,   nil,
   nil,   nil,   306,   307,   313,   103,   102,   104,   105,   nil,
   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   310,   311,    72,    73,   nil,   nil,
   nil,   nil,   nil,   306,   307,   313,   103,   102,   104,   105,
   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,   308,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,   nil,   nil,   314,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   304,
   nil,   nil,   300,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   310,   311,    72,    73,   nil,
   nil,   nil,   nil,   nil,   306,   307,   313,   103,   102,   104,
   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,
   308,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,   nil,   nil,
   314,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   304,   nil,   nil,   300,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   310,   311,    72,    73,
   nil,   nil,   nil,   nil,   nil,   306,   307,   313,   103,   102,
   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   404,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   310,   311,    72,
    73,   nil,   nil,   nil,   nil,   nil,   306,   307,   313,   103,
   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   103,   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,
    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,
    27,   103,   102,   104,   105,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   310,   311,    72,    73,   nil,   nil,   nil,   nil,   nil,   306,
   307,   313,   103,   102,   104,   105,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,
    29,    28,    27,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   310,   311,    72,    73,   nil,   nil,   nil,   nil,
   nil,   306,   307,   313,   103,   102,   104,   105,   nil,   nil,
   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,
   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   310,   311,    72,    73,   nil,   nil,   nil,
   nil,   nil,   306,   307,   313,   103,   102,   104,   105,   nil,
   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   310,   311,    72,    73,   nil,   nil,
   nil,   nil,   nil,   306,   307,   313,   103,   102,   104,   105,
   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,
   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   310,   311,    72,    73,   nil,
   nil,   nil,   nil,   nil,   306,   307,   313,   103,   102,   104,
   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   310,   311,    72,    73,
   nil,   nil,   nil,   nil,   nil,   306,   307,   313,   103,   102,
   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   310,   311,    72,
    73,   nil,   nil,   nil,   nil,   nil,   306,   307,   313,   103,
   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,
   nil,   nil,   308,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
   nil,   nil,   314,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   852,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   310,   311,
    72,    73,   nil,   nil,   nil,   nil,   nil,   306,   307,   313,
   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,
    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,
    27,   103,   102,   104,   105,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   310,   311,    72,    73,   nil,   nil,   nil,   nil,   nil,   306,
   307,   313,   103,   102,   104,   105,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   627,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   310,   311,    72,    73,   nil,   nil,   nil,   nil,   nil,
   306,   307,   313,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   285,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   281,   nil,    44,   nil,   nil,   286,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   310,   311,    72,    73,   nil,   nil,   nil,   nil,
   nil,   306,   307,   313,   103,   102,   104,   105,   nil,   nil,
   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,
   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   310,   311,    72,    73,   nil,   nil,   nil,
   nil,   nil,   306,   307,   313,   103,   102,   104,   105,   nil,
   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,   308,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,   nil,   nil,   314,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   852,   nil,
   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   310,   311,    72,    73,   nil,   nil,
   nil,   nil,   nil,   306,   307,   313,   103,   102,   104,   105,
   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,   308,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,   nil,   nil,   314,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   926,
   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,    30,    31,    72,    73,   nil,
   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,
   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   285,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   283,   nil,   281,   nil,    44,   nil,   nil,   286,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,   nil,  -280,
   nil,    62,   nil,    83,    95,    96,  -280,  -280,  -280,   nil,
   nil,  -280,  -280,  -280,   nil,  -280,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,  -280,  -280,  -280,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,  -280,  -280,   nil,  -280,  -280,
  -280,  -280,  -280,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,  -280,  -280,  -280,  -280,  -280,
  -280,  -280,  -280,  -280,  -280,  -280,  -280,  -280,  -280,   nil,
   nil,  -280,  -280,  -280,   nil,   nil,  -280,   nil,   nil,  -280,
   nil,   nil,  -280,  -280,   nil,  -280,   nil,  -280,   nil,  -280,
   nil,  -280,  -280,   nil,  -280,  -280,  -280,  -280,  -280,   nil,
  -280,   nil,  -280,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,  -280,   nil,   nil,  -280,
  -280,  -280,  -280,  -580,  -280,   nil,  -280,   nil,   nil,   nil,
  -580,  -580,  -580,   nil,   nil,  -580,  -580,  -580,   nil,  -580,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -580,  -580,
  -580,  -580,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -580,
  -580,   nil,  -580,  -580,  -580,  -580,  -580,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -580,
  -580,  -580,  -580,  -580,  -580,  -580,  -580,  -580,  -580,  -580,
  -580,  -580,  -580,   nil,   nil,  -580,  -580,  -580,   nil,   nil,
  -580,   nil,   nil,  -580,   nil,   nil,  -580,  -580,   nil,  -580,
   nil,  -580,   nil,  -580,   nil,  -580,  -580,   nil,  -580,  -580,
  -580,  -580,  -580,   nil,  -580,  -580,  -580,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
  -580,   nil,   nil,  -580,  -580,  -580,  -580,  -581,  -580,   nil,
  -580,   nil,   nil,   nil,  -581,  -581,  -581,   nil,   nil,  -581,
  -581,  -581,   nil,  -581,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,  -581,  -581,  -581,  -581,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,  -581,  -581,   nil,  -581,  -581,  -581,  -581,
  -581,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,  -581,  -581,  -581,  -581,  -581,  -581,  -581,
  -581,  -581,  -581,  -581,  -581,  -581,  -581,   nil,   nil,  -581,
  -581,  -581,   nil,   nil,  -581,   nil,   nil,  -581,   nil,   nil,
  -581,  -581,   nil,  -581,   nil,  -581,   nil,  -581,   nil,  -581,
  -581,   nil,  -581,  -581,  -581,  -581,  -581,   nil,  -581,  -581,
  -581,   669,   nil,   666,   665,   664,   673,   667,   nil,   nil,
   nil,   nil,   nil,   nil,  -581,   nil,   676,  -581,  -581,  -581,
  -581,  -414,  -581,   nil,  -581,   nil,   nil,   nil,  -414,  -414,
  -414,   nil,   nil,  -414,  -414,  -414,   nil,  -414,   671,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,  -414,  -414,  -414,   684,
   683,   nil,   nil,   nil,   677,   nil,   nil,  -414,  -414,   nil,
  -414,  -414,  -414,  -414,  -414,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -414,  -414,  -414,
  -414,  -414,  -414,  -414,  -414,  -414,  -414,  -414,  -414,  -414,
  -414,   nil,   nil,  -414,  -414,  -414,   nil,   nil,  -414,   nil,
   265,  -414,   nil,   nil,  -414,  -414,   nil,  -414,   nil,  -414,
   nil,  -414,   nil,  -414,  -414,   nil,  -414,  -414,  -414,  -414,
  -414,  -296,  -414,  -414,  -414,   nil,   nil,   nil,  -296,  -296,
  -296,   nil,   nil,  -296,  -296,  -296,   nil,  -296,  -414,   nil,
   nil,  -414,  -414,   nil,  -414,   nil,  -414,  -296,  -296,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -296,  -296,   nil,
  -296,  -296,  -296,  -296,  -296,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -296,  -296,  -296,
  -296,  -296,  -296,  -296,  -296,  -296,  -296,  -296,  -296,  -296,
  -296,   nil,   nil,  -296,  -296,  -296,   nil,   nil,  -296,   nil,
   274,  -296,   nil,   nil,  -296,  -296,   nil,  -296,   nil,  -296,
   nil,  -296,   nil,  -296,  -296,   nil,  -296,  -296,  -296,  -296,
  -296,   nil,  -296,  -244,  -296,   nil,   nil,   nil,   nil,   nil,
  -244,  -244,  -244,   nil,   nil,  -244,  -244,  -244,  -296,  -244,
   nil,  -296,  -296,   nil,  -296,   nil,  -296,   nil,  -244,  -244,
  -244,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -244,
  -244,   nil,  -244,  -244,  -244,  -244,  -244,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -244,
  -244,  -244,  -244,  -244,  -244,  -244,  -244,  -244,  -244,  -244,
  -244,  -244,  -244,   nil,   nil,  -244,  -244,  -244,   nil,   nil,
  -244,   nil,   265,  -244,   nil,   nil,  -244,  -244,   nil,  -244,
   nil,  -244,   nil,  -244,   nil,  -244,  -244,   nil,  -244,  -244,
  -244,  -244,  -244,   nil,  -244,  -244,  -244,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
  -244,   nil,  -244,  -244,  -244,   nil,  -244,   nil,  -244,  -244,
  -244,  -244,   nil,   nil,  -244,  -244,  -244,   669,  -244,   666,
   665,   664,   673,   667,   nil,   nil,   nil,  -244,  -244,   nil,
   nil,   nil,   676,   nil,   nil,   nil,   nil,   nil,  -244,  -244,
   nil,  -244,  -244,  -244,  -244,  -244,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   671,   nil,   669,   nil,   666,   665,
   664,   673,   667,   681,   680,   684,   683,   nil,   nil,   nil,
   677,   676,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -244,
   nil,   nil,   nil,   nil,   nil,   nil,  -244,   nil,   nil,   nil,
   nil,   265,  -244,   671,   655,   nil,   220,   nil,   nil,   nil,
   nil,   nil,   681,   680,   684,   683,   nil,   nil,   nil,   677,
   nil,   nil,   nil,   nil,  -244,  -244,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -244,
   nil,   nil,  -244,   nil,   nil,   nil,   nil,  -244,   175,   186,
   176,   199,   172,   192,   182,   181,   202,   203,   197,   180,
   179,   174,   200,   204,   205,   184,   173,   187,   191,   193,
   185,   178,   nil,   nil,   nil,   194,   201,   196,   195,   188,
   198,   183,   171,   190,   189,   nil,   nil,   nil,   nil,   nil,
   170,   177,   168,   169,   165,   166,   167,   126,   128,   125,
   nil,   127,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   159,
   160,   nil,   156,   138,   139,   140,   147,   144,   146,   nil,
   nil,   141,   142,   nil,   nil,   nil,   161,   162,   148,   149,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   153,   152,   nil,   137,   158,   155,   154,
   163,   150,   151,   145,   143,   135,   157,   136,   nil,   nil,
   164,    91,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    90,   175,   186,   176,   199,
   172,   192,   182,   181,   202,   203,   197,   180,   179,   174,
   200,   204,   205,   184,   173,   187,   191,   193,   185,   178,
   nil,   nil,   nil,   194,   201,   196,   195,   188,   198,   183,
   171,   190,   189,   nil,   nil,   nil,   nil,   nil,   170,   177,
   168,   169,   165,   166,   167,   126,   128,   nil,   nil,   127,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   159,   160,   nil,
   156,   138,   139,   140,   147,   144,   146,   nil,   nil,   141,
   142,   nil,   nil,   nil,   161,   162,   148,   149,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   153,   152,   nil,   137,   158,   155,   154,   163,   150,
   151,   145,   143,   135,   157,   136,   nil,   nil,   164,    91,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    90,   175,   186,   176,   199,   172,   192,
   182,   181,   202,   203,   197,   180,   179,   174,   200,   204,
   205,   184,   173,   187,   191,   193,   185,   178,   nil,   nil,
   nil,   194,   201,   196,   195,   188,   198,   183,   171,   190,
   189,   nil,   nil,   nil,   nil,   nil,   170,   177,   168,   169,
   165,   166,   167,   126,   128,   nil,   nil,   127,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   159,   160,   nil,   156,   138,
   139,   140,   147,   144,   146,   nil,   nil,   141,   142,   nil,
   nil,   nil,   161,   162,   148,   149,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   153,
   152,   nil,   137,   158,   155,   154,   163,   150,   151,   145,
   143,   135,   157,   136,   nil,   nil,   164,    91,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    90,   175,   186,   176,   199,   172,   192,   182,   181,
   202,   203,   197,   180,   179,   174,   200,   204,   205,   184,
   173,   187,   191,   193,   185,   178,   nil,   nil,   nil,   194,
   201,   196,   195,   188,   198,   183,   171,   190,   189,   nil,
   nil,   nil,   nil,   nil,   170,   177,   168,   169,   165,   166,
   167,   126,   128,   nil,   nil,   127,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   159,   160,   nil,   156,   138,   139,   140,
   147,   144,   146,   nil,   nil,   141,   142,   nil,   nil,   nil,
   161,   162,   148,   149,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   153,   152,   nil,
   137,   158,   155,   154,   163,   150,   151,   145,   143,   135,
   157,   136,   nil,   nil,   164,    91,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    90,
   175,   186,   176,   199,   172,   192,   182,   181,   202,   203,
   197,   180,   179,   174,   200,   204,   205,   184,   173,   187,
   191,   193,   185,   178,   nil,   nil,   nil,   194,   201,   196,
   371,   370,   372,   369,   171,   190,   189,   nil,   nil,   nil,
   nil,   nil,   170,   177,   168,   169,   366,   367,   368,   364,
   128,    99,    98,   365,   nil,   101,   nil,   nil,   nil,   nil,
   nil,   159,   160,   nil,   156,   138,   139,   140,   147,   144,
   146,   nil,   nil,   141,   142,   nil,   nil,   nil,   161,   162,
   148,   149,   nil,   nil,   nil,   nil,   nil,   376,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   153,   152,   nil,   137,   158,
   155,   154,   163,   150,   151,   145,   143,   135,   157,   136,
   nil,   nil,   164,   175,   186,   176,   199,   172,   192,   182,
   181,   202,   203,   197,   180,   179,   174,   200,   204,   205,
   184,   173,   187,   191,   193,   185,   178,   nil,   nil,   nil,
   194,   201,   196,   195,   188,   198,   183,   171,   190,   189,
   nil,   nil,   nil,   nil,   nil,   170,   177,   168,   169,   165,
   166,   167,   126,   128,   nil,   nil,   127,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   159,   160,   nil,   156,   138,   139,
   140,   147,   144,   146,   nil,   nil,   141,   142,   nil,   nil,
   nil,   161,   162,   148,   149,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   153,   152,
   nil,   137,   158,   155,   154,   163,   150,   151,   145,   143,
   135,   157,   136,   413,   417,   164,   nil,   414,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   159,   160,   nil,   156,   138,
   139,   140,   147,   144,   146,   nil,   nil,   141,   142,   nil,
   nil,   nil,   161,   162,   148,   149,   nil,   nil,   nil,   nil,
   nil,   265,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   153,
   152,   nil,   137,   158,   155,   154,   163,   150,   151,   145,
   143,   135,   157,   136,   420,   424,   164,   nil,   419,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   159,   160,   nil,   156,
   138,   139,   140,   147,   144,   146,   nil,   nil,   141,   142,
   nil,   nil,   nil,   161,   162,   148,   149,   nil,   nil,   nil,
   nil,   nil,   265,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   153,   152,   nil,   137,   158,   155,   154,   163,   150,   151,
   145,   143,   135,   157,   136,   475,   417,   164,   nil,   476,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   159,   160,   nil,
   156,   138,   139,   140,   147,   144,   146,   nil,   nil,   141,
   142,   nil,   nil,   nil,   161,   162,   148,   149,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   153,   152,   nil,   137,   158,   155,   154,   163,   150,
   151,   145,   143,   135,   157,   136,   606,   417,   164,   nil,
   607,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   159,   160,
   nil,   156,   138,   139,   140,   147,   144,   146,   nil,   nil,
   141,   142,   nil,   nil,   nil,   161,   162,   148,   149,   nil,
   nil,   nil,   nil,   nil,   265,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   153,   152,   nil,   137,   158,   155,   154,   163,
   150,   151,   145,   143,   135,   157,   136,   608,   424,   164,
   nil,   609,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   159,
   160,   nil,   156,   138,   139,   140,   147,   144,   146,   nil,
   nil,   141,   142,   nil,   nil,   nil,   161,   162,   148,   149,
   nil,   nil,   nil,   nil,   nil,   265,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   153,   152,   nil,   137,   158,   155,   154,
   163,   150,   151,   145,   143,   135,   157,   136,   637,   417,
   164,   nil,   638,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   159,   160,   nil,   156,   138,   139,   140,   147,   144,   146,
   nil,   nil,   141,   142,   nil,   nil,   nil,   161,   162,   148,
   149,   nil,   nil,   nil,   nil,   nil,   265,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   153,   152,   nil,   137,   158,   155,
   154,   163,   150,   151,   145,   143,   135,   157,   136,   640,
   424,   164,   nil,   641,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   159,   160,   nil,   156,   138,   139,   140,   147,   144,
   146,   nil,   nil,   141,   142,   nil,   nil,   nil,   161,   162,
   148,   149,   nil,   nil,   nil,   nil,   nil,   265,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   153,   152,   nil,   137,   158,
   155,   154,   163,   150,   151,   145,   143,   135,   157,   136,
   606,   417,   164,   nil,   607,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   159,   160,   nil,   156,   138,   139,   140,   147,
   144,   146,   nil,   nil,   141,   142,   nil,   nil,   nil,   161,
   162,   148,   149,   nil,   nil,   nil,   nil,   nil,   265,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   153,   152,   nil,   137,
   158,   155,   154,   163,   150,   151,   145,   143,   135,   157,
   136,   608,   424,   164,   nil,   609,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   159,   160,   nil,   156,   138,   139,   140,
   147,   144,   146,   nil,   nil,   141,   142,   nil,   nil,   nil,
   161,   162,   148,   149,   nil,   nil,   nil,   nil,   nil,   265,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   153,   152,   nil,
   137,   158,   155,   154,   163,   150,   151,   145,   143,   135,
   157,   136,   697,   417,   164,   nil,   698,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   159,   160,   nil,   156,   138,   139,
   140,   147,   144,   146,   nil,   nil,   141,   142,   nil,   nil,
   nil,   161,   162,   148,   149,   nil,   nil,   nil,   nil,   nil,
   265,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   153,   152,
   nil,   137,   158,   155,   154,   163,   150,   151,   145,   143,
   135,   157,   136,   699,   424,   164,   nil,   700,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   159,   160,   nil,   156,   138,
   139,   140,   147,   144,   146,   nil,   nil,   141,   142,   nil,
   nil,   nil,   161,   162,   148,   149,   nil,   nil,   nil,   nil,
   nil,   265,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   153,
   152,   nil,   137,   158,   155,   154,   163,   150,   151,   145,
   143,   135,   157,   136,   702,   424,   164,   nil,   703,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   159,   160,   nil,   156,
   138,   139,   140,   147,   144,   146,   nil,   nil,   141,   142,
   nil,   nil,   nil,   161,   162,   148,   149,   nil,   nil,   nil,
   nil,   nil,   265,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   153,   152,   nil,   137,   158,   155,   154,   163,   150,   151,
   145,   143,   135,   157,   136,   475,   417,   164,   nil,   476,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   159,   160,   nil,
   156,   138,   139,   140,   147,   144,   146,   nil,   nil,   141,
   142,   nil,   nil,   nil,   161,   162,   148,   149,   nil,   nil,
   nil,   nil,   nil,   265,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   153,   152,   nil,   137,   158,   155,   154,   163,   150,
   151,   145,   143,   135,   157,   136,   969,   424,   164,   nil,
   968,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   159,   160,
   nil,   156,   138,   139,   140,   147,   144,   146,   nil,   nil,
   141,   142,   nil,   nil,   nil,   161,   162,   148,   149,   nil,
   nil,   nil,   nil,   nil,   265,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   153,   152,   nil,   137,   158,   155,   154,   163,
   150,   151,   145,   143,   135,   157,   136,   995,   417,   164,
   nil,   996,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   159,
   160,   nil,   156,   138,   139,   140,   147,   144,   146,   nil,
   nil,   141,   142,   nil,   nil,   nil,   161,   162,   148,   149,
   nil,   nil,   nil,   nil,   nil,   265,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   153,   152,   nil,   137,   158,   155,   154,
   163,   150,   151,   145,   143,   135,   157,   136,   997,   424,
   164,   nil,   998,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   159,   160,   nil,   156,   138,   139,   140,   147,   144,   146,
   nil,   nil,   141,   142,   nil,   nil,   nil,   161,   162,   148,
   149,   nil,   nil,   nil,   nil,   nil,   265,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   153,   152,   nil,   137,   158,   155,
   154,   163,   150,   151,   145,   143,   135,   157,   136,   nil,
   669,   164,   666,   665,   664,   673,   667,   nil,   669,   nil,
   666,   665,   664,   673,   667,   676,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   676,   nil,   669,   nil,   666,   665,   664,
   673,   667,   nil,   nil,   nil,   nil,   nil,   671,   nil,   nil,
   676,   nil,   nil,   nil,   nil,   671,   681,   680,   684,   683,
   nil,   nil,   nil,   677,   681,   680,   684,   683,   nil,   nil,
   nil,   677,   671,   nil,   669,   nil,   666,   665,   664,   673,
   667,   681,   680,   684,   683,   nil,   nil,   nil,   677,   676,
   nil,   669,   nil,   666,   665,   664,   673,   667,   nil,   669,
   nil,   666,   665,   664,   673,   667,   676,   nil,   nil,   nil,
   nil,   671,   nil,   nil,   676,   nil,   nil,   nil,   nil,   nil,
   681,   680,   684,   683,   nil,   nil,   nil,   677,   671,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   671,   681,   680,   684,
   683,   nil,   nil,   nil,   677,   681,   680,   684,   683,   nil,
   nil,   669,   677,   666,   665,   664,   673,   667,   nil,   669,
   nil,   666,   665,   664,   673,   667,   676,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   676,   nil,   669,   nil,   666,   665,
   664,   673,   667,   nil,   nil,   nil,   nil,   nil,   671,   nil,
   nil,   676,   nil,   nil,   nil,   nil,   671,   681,   680,   684,
   683,   nil,   nil,   nil,   677,   681,   680,   684,   683,   nil,
   nil,   nil,   677,   671,   nil,   669,   nil,   666,   665,   664,
   673,   667,   nil,   nil,   684,   683,   nil,   nil,   nil,   677,
   676,   nil,   669,   nil,   666,   665,   664,   673,   667,   669,
   nil,   666,   665,   664,   673,   667,   nil,   676,   nil,   nil,
   nil,   nil,   671,   nil,   676,   nil,   nil,   nil,   nil,   nil,
   nil,   681,   680,   684,   683,   nil,   nil,   nil,   677,   671,
   nil,   nil,   nil,   nil,   nil,   nil,   671,   nil,   nil,   nil,
   684,   683,   nil,   nil,   nil,   677,   nil,   684,   683,   nil,
   nil,   669,   677,   666,   665,   664,   673,   667,   669,   nil,
   666,   665,   664,   673,   667,   669,   676,   666,   665,   664,
   673,   667,   nil,   676,   nil,   nil,   nil,   nil,   nil,   nil,
   676,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   671,   nil,
   nil,   nil,   nil,   nil,   nil,   671,   nil,   nil,   nil,   684,
   683,   nil,   671,   nil,   677,   nil,   684,   683,   nil,   nil,
   nil,   677,   nil,   684,   683,   nil,   nil,   nil,   677 ]

racc_action_check = [
    97,   437,   437,   562,   562,    61,   345,    97,    97,    97,
    19,   338,    97,    97,    97,   473,    97,    26,    58,   383,
     1,   481,   647,   647,    97,   384,    97,    97,    97,   329,
   618,     7,   329,   346,   349,   309,    97,    97,   359,    97,
    97,    97,    97,    97,    10,   339,   223,   861,   695,   546,
   473,    19,   697,   538,   482,   539,   481,     3,    58,   893,
    12,   893,     3,   224,   698,   788,    97,    97,    97,    97,
    97,    97,    97,    97,    97,    97,    97,    97,    97,    97,
    13,    26,    97,    97,    97,   383,    97,    97,   820,   482,
    97,   384,    61,    97,    97,   437,    97,   562,    97,   309,
    97,   223,    97,    97,    26,    97,    97,    97,    97,    97,
   888,    97,   100,    97,   995,   345,   647,   618,   224,   100,
   100,   100,   309,   996,   100,   100,   100,    97,   100,   338,
    97,    97,    97,    97,   338,    97,   100,    97,   100,   100,
   100,    97,   346,   349,   359,    15,   699,   821,   100,   100,
   997,   100,   100,   100,   100,   100,  1016,   538,   546,   539,
   637,   697,   538,   339,   539,   861,   695,   359,   339,   695,
   861,   695,   359,   698,   788,   700,   558,   558,   100,   100,
   100,   100,   100,   100,   100,   100,   100,   100,   100,   100,
   100,   100,    15,   226,   100,   100,   100,   820,   100,   100,
    15,   638,   100,    41,    41,   100,   100,    16,   100,   355,
   100,   699,   100,   355,   100,   100,   443,   100,   100,   100,
   100,   100,   419,   100,    22,   100,   637,   997,   888,   419,
   419,   419,   995,   888,    37,   419,   419,   995,   419,   100,
   700,   996,   100,   100,   100,   100,   996,   100,   226,   100,
    24,   821,   573,   100,   606,   699,   821,    24,   419,   419,
    40,   419,   419,   419,   419,   419,   637,   638,   997,   637,
   558,   443,   575,   997,  1016,   558,   619,   637,   289,  1016,
    45,    41,    41,   289,   700,    38,   314,   314,   419,   419,
   419,   419,   419,   419,   419,   419,   419,   419,   419,   419,
   419,   419,   378,   607,   419,   419,   419,   638,   419,   606,
   638,   619,   419,   632,    39,   419,   573,   573,   638,   111,
   419,   632,   419,   206,   419,   419,   573,   419,   419,   419,
   419,   419,    38,   419,   420,   419,   575,   575,   318,   750,
    38,   420,   420,   420,   413,   225,   575,   420,   420,   419,
   420,   342,   419,   419,   227,   419,   342,   419,   607,   420,
   228,    39,   353,   419,   314,   314,   378,   378,   378,    39,
   420,   420,   364,   420,   420,   420,   420,   420,   805,   364,
   805,   805,   805,   232,   805,   318,   944,   379,   944,   944,
   944,   413,   944,   318,   750,    17,    17,   264,   354,   413,
   420,   420,   420,   420,   420,   420,   420,   420,   420,   420,
   420,   420,   420,   420,   353,    14,   420,   420,   420,   278,
   420,   353,    14,   279,   420,   380,   353,   420,   790,   282,
   353,    14,   420,   414,   420,   640,   420,   420,   790,   420,
   420,   420,   420,   420,   293,   420,   420,   420,   365,   353,
   354,   379,   379,   379,   381,   365,   294,   354,   296,   382,
   805,   420,   354,   385,   420,   420,   354,   420,   944,   420,
   608,   790,   790,   297,   366,   420,   790,   608,   608,   608,
   414,   366,   608,   608,   608,   354,   608,   640,   414,   380,
   380,   380,   298,    79,   640,   608,   608,   608,   608,   640,
   304,    46,   307,   640,   589,    79,   608,   608,    46,   608,
   608,   608,   608,   608,   222,    79,   308,    46,   381,   381,
   381,   222,   640,   382,   382,   382,   367,   385,   385,   385,
   222,   330,   333,   367,   330,   333,   608,   608,   608,   608,
   608,   608,   608,   608,   608,   608,   608,   608,   608,   608,
   313,   589,   608,   608,   608,   468,   608,   608,   301,   589,
   608,   315,   368,   608,   608,   301,   608,   319,   608,   368,
   608,   322,   608,   608,   301,   608,   608,   608,   608,   608,
   557,   608,   608,   608,   327,   557,   369,   468,   331,   370,
   332,   468,   468,   369,   468,   468,   370,   608,   334,   824,
   608,   608,   608,   608,   824,   608,   609,   608,    20,    20,
    20,   608,    20,   609,   609,   609,    20,    20,   609,   609,
   609,    20,   609,    20,    20,    20,    20,    20,    20,    20,
   337,   337,   609,   609,   609,    20,    20,    20,    20,    20,
    20,    20,   609,   609,    20,   609,   609,   609,   609,   609,
   653,    20,   343,   653,    20,    20,    20,    20,    20,    20,
    20,    20,   344,    20,    20,    20,   348,    20,    20,    20,
    20,    20,   609,   609,   609,   609,   609,   609,   609,   609,
   609,   609,   609,   609,   609,   609,   522,   522,   609,   609,
   609,    20,   609,   609,    20,   350,   609,    20,    20,   609,
   609,    20,   609,   371,   609,   394,   609,    20,   609,   609,
   371,   609,   609,   609,   609,   609,    20,   609,   372,   609,
   400,    20,    20,    20,    20,   372,    20,    20,    20,    20,
   686,   686,   403,   609,    20,    20,   609,   609,   609,   609,
   405,   609,    20,   609,    20,    20,    20,   609,     0,     0,
     0,     0,     0,     0,   374,   829,   913,     0,     0,   913,
   829,   374,     0,   409,     0,     0,     0,     0,     0,     0,
     0,   411,   702,   793,   793,   412,     0,     0,     0,     0,
     0,     0,     0,   125,   421,     0,   985,   985,   125,   125,
   432,     0,     0,     0,     0,     0,     0,     0,     0,     0,
     0,     0,     0,   429,     0,     0,     0,   439,     0,     0,
     0,     0,     0,   432,   432,   432,   432,   432,   432,   432,
   432,   432,   432,   432,   702,   432,   432,   302,   451,   432,
   432,   702,     0,   452,   302,     0,   702,   453,     0,     0,
   702,   454,     0,   302,     0,   432,   479,   432,     0,   432,
   432,   838,   432,   432,   432,   432,   432,     0,   432,   702,
   483,   838,     0,     0,     0,     0,   303,     0,     0,     0,
     0,   498,   499,   303,   502,     0,     0,   504,   432,   509,
   432,   512,   303,     0,   520,     0,     0,     0,    33,    33,
    33,    33,    33,    33,   838,   838,   521,    33,    33,   838,
   523,   535,    33,   540,    33,    33,    33,    33,    33,    33,
    33,   541,   776,   560,   570,   578,    33,    33,    33,    33,
    33,    33,    33,   566,   566,    33,   580,   566,   566,   566,
   408,    33,    33,    33,    33,    33,    33,    33,    33,    33,
    33,    33,    33,   586,    33,    33,    33,   590,    33,    33,
    33,    33,    33,   408,   408,   408,   408,   408,   408,   408,
   408,   408,   408,   408,   776,   408,   408,   305,   595,   408,
   408,   776,    33,   600,   305,    33,   610,   612,    33,    33,
   776,   617,    33,   305,    33,   408,   624,   408,    33,   408,
   408,   626,   408,   408,   408,   408,   408,    33,   408,   776,
   631,   634,    33,    33,    33,    33,   320,    33,    33,    33,
    33,   636,   639,   320,   642,    33,    33,   643,   408,   646,
   648,   651,   320,    33,   657,    33,    33,    33,   123,   123,
   123,   123,   123,   123,   658,   660,   347,   123,   123,   661,
   662,   670,   123,   347,   123,   123,   123,   123,   123,   123,
   123,   678,   347,   682,   685,   688,   123,   123,   123,   123,
   123,   123,   123,   693,   696,   123,   705,   710,   729,   734,
   611,   123,   123,   123,   123,   123,   123,   123,   123,   123,
   123,   123,   123,   752,   123,   123,   123,   753,   123,   123,
   123,   123,   123,   611,   611,   611,   611,   611,   611,   611,
   611,   611,   611,   611,   357,   611,   611,   508,   755,   611,
   611,   357,   123,   756,   508,   123,   757,   759,   123,   123,
   357,   760,   123,   508,   123,   611,   761,   611,   123,   611,
   611,   762,   611,   611,   611,   611,   611,   123,   611,   766,
   770,   771,   123,   123,   123,   123,   549,   123,   123,   123,
   123,   775,   779,   549,   782,   123,   123,   783,   611,   786,
   789,   804,   549,   123,   806,   123,   123,   123,   208,   208,
   208,   208,   208,   208,   811,   814,   851,   208,   208,   823,
   827,   828,   208,   851,   208,   208,   208,   208,   208,   208,
   208,   831,   851,   832,   848,   852,   208,   208,   208,   208,
   208,   208,   208,   854,   849,   208,   849,   849,   849,   868,
   849,   208,   208,   208,   208,   208,   208,   208,   208,   208,
   208,   208,   208,   869,   208,   208,   208,   873,   208,   208,
   208,   208,   208,    21,    21,    21,    21,    21,    21,    21,
    21,    21,    21,    21,   641,    21,    21,   917,   874,    21,
    21,   641,   208,   876,   917,   208,   641,   877,   208,   208,
   641,   879,   208,   917,   208,    21,   882,    21,   208,    21,
    21,   884,    21,    21,    21,    21,    21,   208,    21,   890,
   891,   897,   208,   208,   208,   208,   923,   208,   208,   208,
   208,   901,   903,   923,   906,   208,   208,   907,    21,   908,
   909,   911,   923,   208,   926,   208,   208,   208,   231,   231,
   231,   231,   231,   231,   946,   968,   924,   231,   231,   969,
   970,   975,   231,   924,   231,   231,   231,   231,   231,   231,
   231,   976,   924,   977,   978,   979,   231,   231,   231,   231,
   231,   231,   231,   980,   967,   231,   967,   967,   967,   981,
   967,   231,   231,   231,   231,   231,   231,   231,   231,   231,
   231,   231,   231,   983,   231,   231,   231,   986,   231,   231,
   231,   231,   231,   276,   276,   276,   276,   276,   276,   276,
   276,   276,   276,   276,   703,   276,   276,   925,   987,   276,
   276,   703,   231,   988,   925,   231,   703,   989,   231,   231,
   703,   990,   231,   925,   231,   276,   991,   276,   231,   276,
   276,   994,   276,   276,   276,   276,   276,   231,   276,  1007,
  1017,  1018,   231,   231,   231,   231,   927,   231,   231,   231,
   231,  1019,   nil,   927,   nil,   231,   231,   nil,   276,   nil,
   nil,   nil,   927,   231,   nil,   231,   231,   231,   295,   295,
   295,   295,   295,   295,   nil,   nil,   974,   295,   295,   nil,
   nil,   nil,   295,   974,   295,   295,   295,   295,   295,   295,
   295,   nil,   974,   nil,   nil,   nil,   295,   295,   295,   295,
   295,   295,   295,   nil,   nil,   295,     6,     6,     6,     6,
     6,   295,   295,   295,   295,   295,   295,   295,   295,   295,
   295,   295,   295,   nil,   295,   295,   295,   nil,   295,   295,
   295,   295,   295,   427,   427,   427,   427,   427,   427,   427,
   427,   427,   427,   427,   998,   427,   427,   nil,   nil,   427,
   427,   998,   295,   nil,   nil,   295,   998,   nil,   295,   295,
   998,   nil,   295,   nil,   295,   427,   nil,   427,   295,   427,
   427,   nil,   427,   427,   427,   427,   427,   295,   427,   nil,
   nil,   nil,   295,   295,   295,   295,   nil,   295,   295,   295,
   295,   nil,   nil,   nil,   nil,   295,   295,   nil,   427,   nil,
   nil,   nil,   nil,   295,   nil,   295,   295,   295,   300,   300,
   300,   300,   300,   300,   nil,   nil,   nil,   300,   300,   nil,
   nil,   nil,   300,   nil,   300,   300,   300,   300,   300,   300,
   300,   292,   292,   292,   292,   292,   300,   300,   300,   300,
   300,   300,   300,   nil,   nil,   300,   496,   496,   496,   496,
   496,   300,   300,   300,   300,   300,   300,   300,   300,   300,
   300,   300,   300,   nil,   300,   300,   300,   nil,   300,   300,
   300,   300,   300,   472,   472,   472,   472,   472,   472,   472,
   472,   472,   472,   472,   nil,   472,   472,   nil,   nil,   472,
   472,   nil,   300,   nil,   nil,   300,   nil,   nil,   300,   300,
   nil,   nil,   300,   nil,   300,   472,   nil,   472,   300,   472,
   472,   nil,   472,   472,   472,   472,   472,   300,   472,   nil,
   nil,   nil,   300,   300,   300,   300,   nil,   300,   300,   300,
   300,   nil,   nil,   nil,   nil,   300,   300,   472,   472,   nil,
   nil,   nil,   nil,   300,   nil,   300,   300,   300,   325,   325,
   325,   325,   325,   325,   nil,   nil,   nil,   325,   325,   nil,
   nil,   nil,   325,   nil,   325,   325,   325,   325,   325,   325,
   325,   nil,   nil,   nil,   nil,   nil,   325,   325,   325,   325,
   325,   325,   325,   nil,   nil,   325,   nil,   nil,   nil,   nil,
   nil,   325,   325,   325,   325,   325,   325,   325,   325,   325,
   325,   325,   325,   nil,   325,   325,   325,   nil,   325,   325,
   325,   325,   325,   518,   518,   518,   518,   518,   518,   518,
   518,   518,   518,   518,   nil,   518,   518,   nil,   nil,   518,
   518,   nil,   325,   nil,   nil,   325,   nil,   nil,   325,   325,
   nil,   nil,   325,   nil,   325,   518,   nil,   518,   325,   518,
   518,   nil,   518,   518,   518,   518,   518,   325,   518,   nil,
   nil,   nil,   325,   325,   325,   325,   nil,   325,   325,   325,
   325,   nil,   nil,   nil,   nil,   325,   325,   nil,   518,   nil,
   nil,   nil,   nil,   325,   nil,   325,   325,   325,   497,   497,
   497,   497,   497,   497,   nil,   nil,   nil,   497,   497,   nil,
   nil,   nil,   497,   nil,   497,   497,   497,   497,   497,   497,
   497,   nil,   nil,   nil,   nil,   nil,   497,   497,   497,   497,
   497,   497,   497,   nil,   nil,   497,   nil,   nil,   nil,   nil,
   nil,   497,   497,   497,   497,   497,   497,   497,   497,   497,
   497,   497,   497,   nil,   497,   497,   497,   nil,   497,   497,
   497,   497,   497,   645,   645,   645,   645,   645,   645,   645,
   645,   645,   645,   645,   nil,   645,   645,   nil,   nil,   645,
   645,   nil,   497,   nil,   nil,   497,   nil,   nil,   497,   497,
   nil,   nil,   497,   nil,   497,   645,   nil,   645,   497,   645,
   645,   nil,   645,   645,   645,   645,   645,   497,   645,   nil,
   nil,   nil,   497,   497,   497,   497,   nil,   497,   497,   497,
   497,   nil,   nil,   nil,   nil,   497,   497,   nil,   645,   nil,
   nil,   nil,   nil,   497,   nil,   497,   497,   497,   534,   534,
   534,   534,   534,   534,   nil,   nil,   nil,   534,   534,   nil,
   nil,   nil,   534,   nil,   534,   534,   534,   534,   534,   534,
   534,   nil,   nil,   nil,   nil,   nil,   534,   534,   534,   534,
   534,   534,   534,   nil,   nil,   534,   nil,   nil,   nil,   nil,
   nil,   534,   534,   534,   534,   534,   534,   534,   534,   534,
   534,   534,   534,   nil,   534,   534,   534,   nil,   534,   534,
   534,   534,   534,   731,   731,   731,   731,   731,   731,   731,
   731,   731,   731,   731,   nil,   731,   731,   nil,   nil,   731,
   731,   nil,   534,   nil,   nil,   534,   nil,   nil,   534,   534,
   nil,   nil,   534,   nil,   534,   731,   nil,   731,   534,   731,
   731,   nil,   731,   731,   731,   731,   731,   534,   731,   nil,
   nil,   nil,   534,   534,   534,   534,   nil,   534,   534,   534,
   534,   nil,   nil,   nil,   nil,   534,   534,   nil,   731,   nil,
   nil,   nil,   nil,   534,   nil,   534,   534,   534,   537,   537,
   537,   537,   537,   537,   nil,   nil,   nil,   537,   537,   nil,
   nil,   nil,   537,   nil,   537,   537,   537,   537,   537,   537,
   537,   nil,   nil,   nil,   nil,   nil,   537,   537,   537,   537,
   537,   537,   537,   nil,   nil,   537,   nil,   nil,   nil,   nil,
   nil,   537,   537,   537,   537,   537,   537,   537,   537,   537,
   537,   537,   537,   nil,   537,   537,   537,   nil,   537,   537,
   537,   537,   537,   736,   736,   736,   736,   736,   736,   736,
   736,   736,   736,   736,   nil,   736,   736,   nil,   nil,   736,
   736,   nil,   537,   nil,   nil,   537,   nil,   nil,   537,   537,
   nil,   nil,   537,   nil,   537,   736,   nil,   736,   537,   736,
   736,   nil,   736,   736,   736,   736,   736,   537,   736,   nil,
   nil,   nil,   537,   537,   537,   537,   nil,   537,   537,   537,
   537,   nil,   nil,   nil,   nil,   537,   537,   nil,   736,   nil,
   nil,   nil,   nil,   537,   nil,   537,   537,   537,   559,   559,
   559,   559,   559,   559,   nil,   nil,   nil,   559,   559,   nil,
   nil,   nil,   559,   nil,   559,   559,   559,   559,   559,   559,
   559,   nil,   nil,   nil,   nil,   nil,   559,   559,   559,   559,
   559,   559,   559,   nil,   nil,   559,   nil,   nil,   nil,   nil,
   nil,   559,   559,   559,   559,   559,   559,   559,   559,   559,
   559,   559,   559,   nil,   559,   559,   559,   nil,   559,   559,
   559,   559,   559,   738,   738,   738,   738,   738,   738,   738,
   738,   738,   738,   738,   nil,   738,   738,   nil,   nil,   738,
   738,   nil,   559,   nil,   nil,   559,   nil,   nil,   559,   559,
   nil,   nil,   559,   nil,   559,   738,   nil,   738,   559,   738,
   738,   nil,   738,   738,   738,   738,   738,   559,   738,   nil,
   nil,   nil,   559,   559,   559,   559,   nil,   559,   559,   559,
   559,   nil,   nil,   nil,   nil,   559,   559,   nil,   738,   nil,
   nil,   nil,   nil,   559,   nil,   559,   559,   559,   616,   616,
   616,   616,   616,   616,   nil,   nil,   nil,   616,   616,   nil,
   nil,   nil,   616,   nil,   616,   616,   616,   616,   616,   616,
   616,   nil,   nil,   nil,   nil,   nil,   616,   616,   616,   616,
   616,   616,   616,   nil,   nil,   616,   nil,   nil,   nil,   nil,
   nil,   616,   616,   616,   616,   616,   616,   616,   616,   616,
   616,   616,   616,   nil,   616,   616,   616,   nil,   616,   616,
   616,   616,   616,   741,   741,   741,   741,   741,   741,   741,
   741,   741,   741,   741,   nil,   741,   741,   nil,   nil,   741,
   741,   nil,   616,   nil,   nil,   616,   nil,   nil,   616,   616,
   nil,   nil,   616,   nil,   616,   741,   nil,   741,   616,   741,
   741,   nil,   741,   741,   741,   741,   741,   616,   741,   nil,
   nil,   nil,   616,   616,   616,   616,   nil,   616,   616,   616,
   616,   nil,   nil,   nil,   nil,   616,   616,   nil,   741,   nil,
   nil,   nil,   nil,   616,   nil,   616,   616,   616,   621,   621,
   621,   621,   621,   621,   nil,   nil,   nil,   621,   621,   nil,
   nil,   nil,   621,   nil,   621,   621,   621,   621,   621,   621,
   621,   nil,   nil,   nil,   nil,   nil,   621,   621,   621,   621,
   621,   621,   621,   nil,   nil,   621,   nil,   nil,   nil,   nil,
   nil,   621,   621,   621,   621,   621,   621,   621,   621,   621,
   621,   621,   621,   nil,   621,   621,   621,   nil,   621,   621,
   621,   621,   621,   743,   743,   743,   743,   743,   743,   743,
   743,   743,   743,   743,   nil,   743,   743,   nil,   nil,   743,
   743,   nil,   621,   nil,   nil,   621,   nil,   nil,   621,   621,
   nil,   nil,   621,   nil,   621,   743,   nil,   743,   621,   743,
   743,   nil,   743,   743,   743,   743,   743,   621,   743,   nil,
   nil,   nil,   621,   621,   621,   621,   nil,   621,   621,   621,
   621,   nil,   nil,   nil,   nil,   621,   621,   nil,   743,   nil,
   nil,   nil,   nil,   621,   nil,   621,   621,   621,   622,   622,
   622,   622,   622,   622,   nil,   nil,   nil,   622,   622,   nil,
   nil,   nil,   622,   nil,   622,   622,   622,   622,   622,   622,
   622,   nil,   nil,   nil,   nil,   nil,   622,   622,   622,   622,
   622,   622,   622,   nil,   nil,   622,   nil,   nil,   nil,   nil,
   nil,   622,   622,   622,   622,   622,   622,   622,   622,   622,
   622,   622,   622,   nil,   622,   622,   622,   nil,   622,   622,
   622,   622,   622,   745,   745,   745,   745,   745,   745,   745,
   745,   745,   745,   745,   nil,   745,   745,   nil,   nil,   745,
   745,   nil,   622,   nil,   nil,   622,   nil,   nil,   622,   622,
   nil,   nil,   622,   nil,   622,   745,   nil,   745,   622,   745,
   745,   nil,   745,   745,   745,   745,   745,   622,   745,   nil,
   nil,   nil,   622,   622,   622,   622,   nil,   622,   622,   622,
   622,   nil,   nil,   nil,   nil,   622,   622,   nil,   745,   nil,
   nil,   nil,   nil,   622,   nil,   622,   622,   622,   706,   706,
   706,   706,   706,   706,   nil,   nil,   nil,   706,   706,   nil,
   nil,   nil,   706,   nil,   706,   706,   706,   706,   706,   706,
   706,   nil,   nil,   nil,   nil,   nil,   706,   706,   706,   706,
   706,   706,   706,   nil,   nil,   706,   nil,   nil,   nil,   nil,
   nil,   706,   706,   706,   706,   706,   706,   706,   706,   706,
   706,   706,   706,   nil,   706,   706,   706,   nil,   706,   706,
   706,   706,   706,   834,   834,   834,   834,   834,   834,   834,
   834,   834,   834,   834,   nil,   834,   834,   nil,   nil,   834,
   834,   nil,   706,   nil,   nil,   706,   nil,   nil,   706,   706,
   nil,   nil,   706,   nil,   706,   834,   nil,   834,   706,   834,
   834,   nil,   834,   834,   834,   834,   834,   706,   834,   nil,
   nil,   nil,   706,   706,   706,   706,   nil,   706,   706,   706,
   706,   nil,   nil,   nil,   nil,   706,   706,   nil,   834,   nil,
   nil,   nil,   nil,   706,   nil,   706,   706,   706,   711,   711,
   711,   711,   711,   711,   nil,   nil,   nil,   711,   711,   nil,
   nil,   nil,   711,   nil,   711,   711,   711,   711,   711,   711,
   711,   nil,   nil,   nil,   nil,   nil,   711,   711,   711,   711,
   711,   711,   711,   nil,   nil,   711,   nil,   nil,   nil,   nil,
   nil,   711,   711,   711,   711,   711,   711,   711,   711,   711,
   711,   711,   711,   nil,   711,   711,   711,   nil,   711,   711,
   711,   711,   711,   837,   837,   837,   837,   837,   837,   837,
   837,   837,   837,   837,   nil,   837,   837,   nil,   nil,   837,
   837,   nil,   711,   nil,   nil,   711,   nil,   nil,   711,   711,
   nil,   nil,   711,   nil,   711,   837,   nil,   837,   711,   837,
   837,   nil,   837,   837,   837,   837,   837,   711,   837,   nil,
   nil,   nil,   711,   711,   711,   711,   nil,   711,   711,   711,
   711,   nil,   nil,   nil,   nil,   711,   711,   nil,   837,   nil,
   nil,   nil,   nil,   711,   nil,   711,   711,   711,   721,   721,
   721,   721,   721,   721,   nil,   nil,   nil,   721,   721,   nil,
   nil,   nil,   721,   nil,   721,   721,   721,   721,   721,   721,
   721,   nil,   nil,   nil,   nil,   nil,   721,   721,   721,   721,
   721,   721,   721,   nil,   671,   721,   671,   671,   671,   nil,
   671,   721,   721,   721,   721,   721,   721,   721,   721,   721,
   721,   721,   721,   nil,   721,   721,   721,   nil,   721,   721,
   721,   721,   721,   802,   nil,   802,   802,   802,   nil,   802,
   nil,   671,   nil,   nil,   449,   nil,   nil,   nil,   nil,   nil,
   671,   nil,   721,   nil,   nil,   721,   nil,   nil,   721,   721,
   nil,   nil,   721,   nil,   721,   nil,   nil,   nil,   721,   nil,
   802,   nil,   nil,   nil,   nil,   nil,   449,   721,   nil,   802,
   449,   449,   721,   721,   721,   721,   nil,   721,   721,   721,
   721,   nil,   nil,   nil,   nil,   721,   721,   nil,   883,   nil,
   883,   883,   883,   721,   883,   721,   721,   721,   769,   769,
   769,   769,   769,   769,   nil,   nil,   nil,   769,   769,   nil,
   nil,   nil,   769,   nil,   769,   769,   769,   769,   769,   769,
   769,   nil,   nil,   nil,   nil,   883,   769,   769,   769,   769,
   769,   769,   769,   nil,   883,   769,   nil,   nil,   nil,   nil,
   nil,   769,   769,   769,   769,   769,   769,   769,   769,   769,
   769,   769,   769,   nil,   769,   769,   769,   nil,   769,   769,
   769,   769,   769,   447,   447,   447,   447,   447,   447,   447,
   447,   447,   447,   447,   nil,   447,   447,   nil,   nil,   447,
   447,   nil,   769,   nil,   nil,   769,   nil,   nil,   769,   769,
   nil,   469,   769,   nil,   769,   447,   nil,   447,   769,   447,
   447,   nil,   447,   447,   447,   447,   447,   769,   447,   nil,
   nil,   nil,   769,   769,   769,   769,   nil,   769,   769,   769,
   769,   nil,   nil,   469,   nil,   769,   769,   469,   469,   nil,
   469,   469,   nil,   769,   nil,   769,   769,   769,   781,   781,
   781,   781,   781,   781,   nil,   nil,   nil,   781,   781,   nil,
   nil,   nil,   781,   nil,   781,   781,   781,   781,   781,   781,
   781,   nil,   nil,   nil,   nil,   nil,   781,   781,   781,   781,
   781,   781,   781,   nil,   nil,   781,   nil,   nil,   nil,   nil,
   nil,   781,   781,   781,   781,   781,   781,   781,   781,   781,
   781,   781,   781,   nil,   781,   781,   781,   nil,   781,   781,
   781,   781,   781,   448,   448,   448,   448,   448,   448,   448,
   448,   448,   448,   448,   nil,   448,   448,   nil,   nil,   448,
   448,   nil,   781,   nil,   nil,   781,   nil,   nil,   781,   781,
   nil,   450,   781,   nil,   781,   448,   nil,   448,   781,   448,
   448,   nil,   448,   448,   448,   448,   448,   781,   448,   nil,
   nil,   nil,   781,   781,   781,   781,   nil,   781,   781,   781,
   781,   nil,   nil,   450,   nil,   781,   781,   450,   450,   nil,
   nil,   nil,   nil,   781,   nil,   781,   781,   781,   815,   815,
   815,   815,   815,   815,   nil,   nil,   nil,   815,   815,   nil,
   nil,   nil,   815,   nil,   815,   815,   815,   815,   815,   815,
   815,   nil,   nil,   nil,   nil,   nil,   815,   815,   815,   815,
   815,   815,   815,   nil,   885,   815,   885,   885,   885,   nil,
   885,   815,   815,   815,   815,   815,   815,   815,   815,   815,
   815,   815,   815,   nil,   815,   815,   815,   nil,   815,   815,
   815,   815,   815,   458,   458,   458,   458,   458,   458,   458,
   nil,   885,   458,   458,   nil,   nil,   nil,   nil,   nil,   458,
   458,   nil,   815,   nil,   nil,   815,   nil,   nil,   815,   815,
   nil,   nil,   815,   nil,   815,   458,   nil,   458,   815,   458,
   458,   nil,   458,   458,   458,   458,   458,   815,   458,   nil,
   nil,   nil,   815,   815,   815,   815,   nil,   815,   815,   815,
   815,   nil,   nil,   nil,   nil,   815,   815,   nil,   945,   nil,
   945,   945,   945,   815,   945,   815,   815,   815,   816,   816,
   816,   816,   816,   816,   nil,   nil,   nil,   816,   816,   nil,
   nil,   nil,   816,   nil,   816,   816,   816,   816,   816,   816,
   816,   nil,   nil,   nil,   nil,   945,   816,   816,   816,   816,
   816,   816,   816,   nil,   982,   816,   982,   982,   982,   nil,
   982,   816,   816,   816,   816,   816,   816,   816,   816,   816,
   816,   816,   816,   nil,   816,   816,   816,   nil,   816,   816,
   816,   816,   816,   459,   984,   nil,   984,   984,   984,   nil,
   984,   982,  1006,   nil,  1006,  1006,  1006,   nil,  1006,   459,
   459,   nil,   816,   nil,   nil,   816,   nil,   nil,   816,   816,
   nil,   nil,   816,   nil,   816,   459,   nil,   459,   816,   459,
   459,   984,   459,   459,   nil,   nil,   459,   816,   459,  1006,
   nil,   nil,   816,   816,   816,   816,   nil,   816,   816,   816,
   816,   nil,   nil,   nil,   nil,   816,   816,   nil,   nil,   nil,
   nil,   nil,   nil,   816,   nil,   816,   816,   816,   819,   819,
   819,   819,   819,   819,   nil,   nil,   nil,   819,   819,   nil,
   nil,   nil,   819,   nil,   819,   819,   819,   819,   819,   819,
   819,   nil,   nil,   nil,   nil,   nil,   819,   819,   819,   819,
   819,   819,   819,   nil,   nil,   819,   nil,   nil,   nil,   nil,
   nil,   819,   819,   819,   819,   819,   819,   819,   819,   819,
   819,   819,   819,   nil,   819,   819,   819,   nil,   819,   819,
   819,   819,   819,   460,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   460,
   460,   nil,   819,   nil,   nil,   819,   nil,   nil,   819,   819,
   nil,   nil,   819,   nil,   819,   460,   nil,   460,   819,   460,
   460,   nil,   460,   460,   nil,   nil,   460,   819,   460,   nil,
   nil,   nil,   819,   819,   819,   819,   nil,   819,   819,   819,
   819,   nil,   nil,   nil,   nil,   819,   819,   nil,   nil,   nil,
   nil,   nil,   nil,   819,   nil,   819,   819,   819,   825,   825,
   825,   825,   825,   825,   nil,   nil,   nil,   825,   825,   nil,
   nil,   nil,   825,   nil,   825,   825,   825,   825,   825,   825,
   825,   nil,   nil,   nil,   nil,   nil,   825,   825,   825,   825,
   825,   825,   825,   nil,   nil,   825,   nil,   nil,   nil,   nil,
   nil,   825,   825,   825,   825,   825,   825,   825,   825,   825,
   825,   825,   825,   nil,   825,   825,   825,   nil,   825,   825,
   825,   825,   825,   461,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   461,
   461,   nil,   825,   nil,   nil,   825,   nil,   nil,   825,   825,
   nil,   nil,   825,   nil,   825,   461,   nil,   461,   825,   461,
   461,   nil,   461,   461,   nil,   nil,   461,   825,   461,   nil,
   nil,   nil,   825,   825,   825,   825,   nil,   825,   825,   825,
   825,   nil,   nil,   nil,   nil,   825,   825,   nil,   nil,   nil,
   nil,   nil,   nil,   825,   nil,   825,   825,   825,   858,   858,
   858,   858,   858,   858,   nil,   nil,   nil,   858,   858,   nil,
   nil,   nil,   858,   nil,   858,   858,   858,   858,   858,   858,
   858,   nil,   nil,   nil,   nil,   nil,   858,   858,   858,   858,
   858,   858,   858,   nil,   nil,   858,   nil,   nil,   nil,   nil,
   nil,   858,   858,   858,   858,   858,   858,   858,   858,   858,
   858,   858,   858,   nil,   858,   858,   858,   nil,   858,   858,
   858,   858,   858,   462,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   462,
   462,   nil,   858,   nil,   nil,   858,   nil,   nil,   858,   858,
   nil,   nil,   858,   nil,   858,   462,   nil,   462,   858,   462,
   462,   nil,   462,   462,   nil,   nil,   462,   858,   462,   nil,
   nil,   nil,   858,   858,   858,   858,   nil,   858,   858,   858,
   858,   nil,   nil,   nil,   nil,   858,   858,   nil,   nil,   nil,
   nil,   nil,   nil,   858,   nil,   858,   858,   858,   922,   922,
   922,   922,   922,   922,   nil,   nil,   nil,   922,   922,   nil,
   nil,   nil,   922,   nil,   922,   922,   922,   922,   922,   922,
   922,   nil,   nil,   nil,   nil,   nil,   922,   922,   922,   922,
   922,   922,   922,   nil,   nil,   922,   nil,   nil,   nil,   nil,
   nil,   922,   922,   922,   922,   922,   922,   922,   922,   922,
   922,   922,   922,   nil,   922,   922,   922,   nil,   922,   922,
   922,   922,   922,   463,   463,   463,   463,   463,   463,   463,
   nil,   nil,   463,   463,   nil,   nil,   nil,   nil,   nil,   463,
   463,   nil,   922,   nil,   nil,   922,   nil,   nil,   922,   922,
   nil,   nil,   922,   nil,   922,   463,   nil,   463,   922,   463,
   463,   nil,   463,   463,   463,   463,   463,   922,   463,   nil,
   nil,   nil,   922,   922,   922,   922,   nil,   922,   922,   922,
   922,   nil,   nil,   nil,   nil,   922,   922,   nil,   nil,   nil,
   nil,   nil,   nil,   922,   nil,   922,   922,   922,   929,   929,
   929,   929,   929,   929,   nil,   nil,   nil,   929,   929,   nil,
   nil,   nil,   929,   nil,   929,   929,   929,   929,   929,   929,
   929,   nil,   nil,   nil,   nil,   nil,   929,   929,   929,   929,
   929,   929,   929,   nil,   nil,   929,   nil,   nil,   nil,   nil,
   nil,   929,   929,   929,   929,   929,   929,   929,   929,   929,
   929,   929,   929,   nil,   929,   929,   929,   nil,   929,   929,
   929,   929,   929,   464,   464,   464,   464,   464,   464,   464,
   nil,   nil,   464,   464,   nil,   nil,   nil,   nil,   nil,   464,
   464,   nil,   929,   nil,   nil,   929,   nil,   nil,   929,   929,
   nil,   nil,   929,   nil,   929,   464,   nil,   464,   929,   464,
   464,   nil,   464,   464,   464,   464,   464,   929,   464,   nil,
   nil,   nil,   929,   929,   929,   929,   nil,   929,   929,   929,
   929,   nil,   nil,   nil,   nil,   929,   929,   nil,   nil,   nil,
   nil,   nil,   nil,   929,   nil,   929,   929,   929,   930,   930,
   930,   930,   930,   930,   nil,   nil,   nil,   930,   930,   nil,
   nil,   nil,   930,   nil,   930,   930,   930,   930,   930,   930,
   930,   nil,   nil,   nil,   nil,   nil,   930,   930,   930,   930,
   930,   930,   930,   nil,   nil,   930,   nil,   nil,   nil,   nil,
   nil,   930,   930,   930,   930,   930,   930,   930,   930,   930,
   930,   930,   930,   nil,   930,   930,   930,   nil,   930,   930,
   930,   930,   930,   465,   465,   465,   465,   465,   465,   465,
   nil,   nil,   465,   465,   nil,   nil,   nil,   nil,   nil,   465,
   465,   nil,   930,   nil,   nil,   930,   nil,   nil,   930,   930,
   nil,   nil,   930,   nil,   930,   465,   nil,   465,   930,   465,
   465,   nil,   465,   465,   465,   465,   465,   930,   465,   nil,
   nil,   nil,   930,   930,   930,   930,   nil,   930,   930,   930,
   930,   nil,   nil,   nil,   nil,   930,   930,   nil,   nil,   nil,
   nil,   nil,   nil,   930,   nil,   930,   930,   930,   947,   947,
   947,   947,   947,   947,   nil,   nil,   nil,   947,   947,   nil,
   nil,   nil,   947,   nil,   947,   947,   947,   947,   947,   947,
   947,   nil,   nil,   nil,   nil,   nil,   947,   947,   947,   947,
   947,   947,   947,   nil,   nil,   947,   nil,   nil,   nil,   nil,
   nil,   947,   947,   947,   947,   947,   947,   947,   947,   947,
   947,   947,   947,   nil,   947,   947,   947,   nil,   947,   947,
   947,   947,   947,   466,   466,   466,   466,   466,   466,   466,
   nil,   nil,   466,   466,   nil,   nil,   nil,   nil,   nil,   466,
   466,   nil,   947,   nil,   nil,   947,   nil,   nil,   947,   947,
   nil,   nil,   947,   nil,   947,   466,   nil,   466,   947,   466,
   466,   nil,   466,   466,   466,   466,   466,   947,   466,   nil,
   nil,   nil,   947,   947,   947,   947,   nil,   947,   947,   947,
   947,   nil,   nil,   nil,   nil,   947,   947,   nil,   nil,   nil,
   nil,   nil,   nil,   947,   nil,   947,   947,   947,   953,   953,
   953,   953,   953,   953,   nil,   nil,   nil,   953,   953,   nil,
   nil,   nil,   953,   nil,   953,   953,   953,   953,   953,   953,
   953,   nil,   nil,   nil,   nil,   nil,   953,   953,   953,   953,
   953,   953,   953,   nil,   nil,   953,   nil,   nil,   nil,   nil,
   nil,   953,   953,   953,   953,   953,   953,   953,   953,   953,
   953,   953,   953,   nil,   953,   953,   953,   nil,   953,   953,
   953,   953,   953,   467,   467,   467,   467,   467,   467,   467,
   nil,   nil,   467,   467,   nil,   nil,   nil,   nil,   nil,   467,
   467,   nil,   953,   nil,   nil,   953,   nil,   nil,   953,   953,
   nil,   nil,   953,   nil,   953,   467,   nil,   467,   953,   467,
   467,   nil,   467,   467,   467,   467,   467,   953,   467,   nil,
   nil,   nil,   953,   953,   953,   953,   nil,   953,   953,   953,
   953,   nil,   nil,   nil,   nil,   953,   953,   nil,   nil,   nil,
   nil,   nil,   nil,   953,   nil,   953,   953,   953,   955,   955,
   955,   955,   955,   955,   nil,   nil,   nil,   955,   955,   nil,
   nil,   nil,   955,   nil,   955,   955,   955,   955,   955,   955,
   955,   nil,   nil,   nil,   nil,   nil,   955,   955,   955,   955,
   955,   955,   955,   nil,   nil,   955,   nil,   nil,   nil,   nil,
   nil,   955,   955,   955,   955,   955,   955,   955,   955,   955,
   955,   955,   955,   nil,   955,   955,   955,   nil,   955,   955,
   955,   955,   955,   470,   470,   470,   470,   470,   470,   470,
   nil,   nil,   470,   470,   nil,   nil,   nil,   nil,   nil,   470,
   470,   nil,   955,   nil,   nil,   955,   nil,   nil,   955,   955,
   nil,   nil,   955,   nil,   955,   470,   nil,   470,   955,   470,
   470,   nil,   470,   470,   470,   470,   470,   955,   470,   nil,
   nil,   nil,   955,   955,   955,   955,   nil,   955,   955,   955,
   955,   nil,   nil,   nil,   nil,   955,   955,   nil,   nil,   nil,
   nil,   nil,   nil,   955,   nil,   955,   955,   955,     5,     5,
     5,     5,     5,   nil,   nil,   nil,     5,     5,   nil,   nil,
   nil,     5,   nil,     5,     5,     5,     5,     5,     5,     5,
   nil,   nil,   nil,   nil,   nil,     5,     5,     5,     5,     5,
     5,     5,   nil,   nil,     5,   nil,   nil,   nil,   nil,   nil,
     5,     5,     5,     5,     5,     5,     5,     5,     5,     5,
     5,     5,   nil,     5,     5,     5,   nil,     5,     5,     5,
     5,     5,   471,   471,   471,   471,   471,   471,   471,   471,
   nil,   471,   471,   nil,   nil,   nil,   nil,   nil,   471,   471,
   nil,     5,   nil,   nil,     5,   nil,   nil,     5,     5,   nil,
   nil,     5,   nil,     5,   471,   nil,   471,     5,   471,   471,
   nil,   471,   471,   471,   471,   471,     5,   471,   nil,   nil,
   nil,     5,     5,     5,     5,   nil,     5,     5,     5,     5,
   nil,   nil,   nil,   nil,     5,     5,   nil,   nil,   nil,    29,
    29,    29,     5,    29,     5,     5,     5,    29,    29,   nil,
   nil,   nil,    29,   nil,    29,    29,    29,    29,    29,    29,
    29,   nil,   nil,   nil,   nil,   nil,    29,    29,    29,    29,
    29,    29,    29,   nil,   nil,    29,   nil,   nil,   nil,   nil,
   nil,   nil,    29,   nil,   nil,    29,    29,    29,    29,    29,
    29,    29,    29,    29,    29,    29,    29,   nil,    29,    29,
    29,    29,    29,   455,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   455,
   455,   nil,    29,   nil,   nil,    29,   nil,   nil,    29,    29,
   nil,   nil,    29,   nil,    29,   455,    29,   455,    29,   455,
   455,    29,   455,   455,   nil,   nil,   nil,    29,   nil,   nil,
   nil,   nil,    29,    29,    29,    29,   nil,    29,    29,    29,
    29,   nil,   nil,   nil,   nil,    29,    29,   nil,   nil,   nil,
    30,    30,    30,    29,    30,    29,    29,    29,    30,    30,
   nil,   nil,   nil,    30,   nil,    30,    30,    30,    30,    30,
    30,    30,   nil,   nil,   nil,   nil,   nil,    30,    30,    30,
    30,    30,    30,    30,   nil,   nil,    30,   nil,   nil,   nil,
   nil,   nil,   nil,    30,   nil,   nil,    30,    30,    30,    30,
    30,    30,    30,    30,    30,    30,    30,    30,   nil,    30,
    30,    30,    30,    30,   456,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   456,   456,   nil,    30,   nil,   nil,    30,   nil,   nil,    30,
    30,   nil,   nil,    30,   nil,    30,   456,    30,   456,    30,
   456,   456,    30,   456,   456,   nil,   nil,   nil,    30,   nil,
   nil,   nil,   nil,    30,    30,    30,    30,   nil,    30,    30,
    30,    30,   nil,   nil,   nil,   nil,    30,    30,   nil,   nil,
   nil,    31,    31,    31,    30,    31,    30,    30,    30,    31,
    31,   nil,   nil,   nil,    31,   nil,    31,    31,    31,    31,
    31,    31,    31,   nil,   nil,   nil,   nil,   nil,    31,    31,
    31,    31,    31,    31,    31,   nil,   nil,    31,   nil,   nil,
   nil,   nil,   nil,   nil,    31,   nil,   nil,    31,    31,    31,
    31,    31,    31,    31,    31,    31,    31,    31,    31,   nil,
    31,    31,    31,    31,    31,   457,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   457,   457,   nil,    31,   nil,   nil,    31,   nil,   nil,
    31,    31,   nil,   nil,    31,   nil,    31,   457,    31,   nil,
    31,   457,   457,    31,   457,   457,   nil,   nil,   nil,    31,
   nil,   nil,   nil,   nil,    31,    31,    31,    31,   nil,    31,
    31,    31,    31,   nil,   nil,   nil,   nil,    31,    31,   nil,
   nil,   nil,    34,    34,    34,    31,    34,    31,    31,    31,
    34,    34,   nil,   nil,   nil,    34,   nil,    34,    34,    34,
    34,    34,    34,    34,   nil,   nil,   nil,   nil,   nil,    34,
    34,    34,    34,    34,    34,    34,   nil,   nil,    34,   nil,
   nil,   nil,   nil,   nil,   nil,    34,   nil,   nil,    34,    34,
    34,    34,    34,    34,    34,    34,   nil,    34,    34,    34,
   nil,    34,    34,   nil,   nil,    34,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    34,   nil,   nil,    34,   nil,
   nil,    34,    34,   nil,   nil,    34,   nil,    34,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    34,    34,    34,    34,   nil,
    34,    34,    34,    34,   nil,   nil,   nil,   nil,    34,    34,
   nil,   nil,   nil,    35,    35,    35,    34,    35,    34,    34,
    34,    35,    35,   nil,   nil,   nil,    35,   nil,    35,    35,
    35,    35,    35,    35,    35,   nil,   nil,   nil,   nil,   nil,
    35,    35,    35,    35,    35,    35,    35,   nil,   nil,    35,
   nil,   nil,   nil,   nil,   nil,   nil,    35,   nil,   nil,    35,
    35,    35,    35,    35,    35,    35,    35,   nil,    35,    35,
    35,   nil,    35,    35,   nil,   nil,    35,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    35,   nil,   nil,    35,
   nil,   nil,    35,    35,   nil,   nil,    35,   nil,   nil,   798,
   nil,   798,   798,   798,   798,   798,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   798,   nil,    35,    35,    35,    35,
   nil,    35,    35,    35,    35,   nil,   nil,   nil,   nil,    35,
    35,   nil,   nil,   nil,    35,   nil,   798,    35,   nil,    35,
    35,    35,    42,    42,    42,   nil,    42,   798,   798,   nil,
    42,    42,   798,   nil,   nil,    42,   nil,    42,    42,    42,
    42,    42,    42,    42,   nil,   nil,   nil,   nil,   nil,    42,
    42,    42,    42,    42,    42,    42,   nil,   nil,    42,   nil,
   nil,   nil,   nil,   nil,   nil,    42,   nil,   nil,    42,    42,
    42,    42,    42,    42,    42,    42,   nil,    42,    42,    42,
   nil,    42,    42,    42,    42,    42,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    42,   nil,   nil,    42,   nil,
   nil,    42,    42,   nil,   nil,    42,   nil,   nil,   nil,   nil,
   nil,    42,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    42,   nil,   nil,   nil,   nil,    42,    42,    42,    42,   nil,
    42,    42,    42,    42,   nil,   nil,   nil,   nil,    42,    42,
   nil,   nil,   nil,    43,    43,    43,    42,    43,    42,    42,
    42,    43,    43,   nil,   nil,   nil,    43,   nil,    43,    43,
    43,    43,    43,    43,    43,   nil,   nil,   nil,   nil,   nil,
    43,    43,    43,    43,    43,    43,    43,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,    43,
    43,    43,    43,    43,    43,    43,    43,   nil,    43,    43,
    43,   nil,    43,    43,    43,    43,    43,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,    43,
   nil,   nil,    43,    43,   nil,   nil,    43,   nil,   nil,   nil,
   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    43,   nil,   nil,   nil,   nil,    43,    43,    43,    43,
   nil,    43,    43,    43,    43,   nil,   nil,   nil,   nil,    43,
    43,   nil,   nil,   nil,    44,    44,    44,    43,    44,    43,
    43,    43,    44,    44,   nil,   nil,   nil,    44,   nil,    44,
    44,    44,    44,    44,    44,    44,   nil,   nil,   nil,   nil,
   nil,    44,    44,    44,    44,    44,    44,    44,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
    44,    44,    44,    44,    44,    44,    44,    44,   nil,    44,
    44,    44,   nil,    44,    44,    44,    44,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
    44,   nil,   nil,    44,    44,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,    44,    44,    44,
    44,   nil,    44,    44,    44,    44,   nil,   nil,   nil,   nil,
    44,    44,   nil,   nil,   nil,    59,    59,    59,    44,    59,
    44,    44,    44,    59,    59,   nil,   nil,   nil,    59,   nil,
    59,    59,    59,    59,    59,    59,    59,   nil,   nil,   nil,
   nil,   nil,    59,    59,    59,    59,    59,    59,    59,   nil,
   nil,    59,   nil,   nil,   nil,   nil,   nil,   nil,    59,   nil,
   nil,    59,    59,    59,    59,    59,    59,    59,    59,    59,
    59,    59,    59,   nil,    59,    59,    59,    59,    59,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    59,   nil,
   nil,    59,   nil,   nil,    59,    59,   nil,   nil,    59,   nil,
    59,   nil,   nil,   nil,    59,   nil,   nil,    59,   nil,   nil,
   nil,   nil,   nil,    59,   nil,   nil,   nil,   nil,    59,    59,
    59,    59,   nil,    59,    59,    59,    59,   nil,   nil,   nil,
   nil,    59,    59,   nil,   nil,   nil,    60,    60,    60,    59,
    60,    59,    59,    59,    60,    60,   nil,   nil,   nil,    60,
   nil,    60,    60,    60,    60,    60,    60,    60,   nil,   nil,
   nil,   nil,   nil,    60,    60,    60,    60,    60,    60,    60,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,   nil,    60,
   nil,   nil,    60,    60,    60,    60,    60,    60,    60,    60,
    60,    60,    60,    60,   nil,    60,    60,    60,    60,    60,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    60,
   nil,   nil,    60,   nil,   nil,    60,    60,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    60,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    60,   nil,   nil,   nil,   nil,    60,
    60,    60,    60,   nil,    60,    60,    60,    60,   nil,   nil,
   nil,   nil,    60,    60,   nil,   nil,   nil,    63,    63,    63,
    60,    63,    60,    60,    60,    63,    63,   nil,   nil,   nil,
    63,   nil,    63,    63,    63,    63,    63,    63,    63,   nil,
   nil,   nil,   nil,   nil,    63,    63,    63,    63,    63,    63,
    63,   nil,   nil,    63,   nil,   nil,   nil,   nil,   nil,   nil,
    63,   nil,   nil,    63,    63,    63,    63,    63,    63,    63,
    63,   nil,    63,    63,    63,   nil,    63,    63,    63,    63,
    63,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    63,   nil,   nil,    63,   nil,   nil,    63,    63,   nil,   nil,
    63,   nil,   nil,   nil,   nil,   nil,    63,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    63,   nil,   nil,   nil,   nil,
    63,    63,    63,    63,   nil,    63,    63,    63,    63,   nil,
   nil,   nil,   nil,    63,    63,   nil,   nil,   nil,    64,    64,
    64,    63,    64,    63,    63,    63,    64,    64,   nil,   nil,
   nil,    64,   nil,    64,    64,    64,    64,    64,    64,    64,
   nil,   nil,   nil,   nil,   nil,    64,    64,    64,    64,    64,
    64,    64,   nil,   nil,    64,   nil,   nil,   nil,   nil,   nil,
   nil,    64,   nil,   nil,    64,    64,    64,    64,    64,    64,
    64,    64,   nil,    64,    64,    64,   nil,    64,    64,    64,
    64,    64,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    64,   nil,   nil,    64,   nil,   nil,    64,    64,   nil,
   nil,    64,   nil,   nil,   nil,   nil,   nil,    64,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    64,   nil,   nil,   nil,
   nil,    64,    64,    64,    64,   nil,    64,    64,    64,    64,
   nil,   nil,   nil,   nil,    64,    64,   nil,   nil,   nil,    67,
    67,    67,    64,    67,    64,    64,    64,    67,    67,   nil,
   nil,   nil,    67,   nil,    67,    67,    67,    67,    67,    67,
    67,   nil,   nil,   nil,   nil,   nil,    67,    67,    67,    67,
    67,    67,    67,   nil,   nil,    67,   nil,   nil,   nil,   nil,
   nil,   nil,    67,   nil,   nil,    67,    67,    67,    67,    67,
    67,    67,    67,   nil,    67,    67,    67,   nil,    67,    67,
    67,    67,    67,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    67,   nil,   nil,    67,   nil,   nil,    67,    67,
   nil,   nil,    67,   nil,   nil,   nil,   nil,   nil,    67,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    67,   nil,   nil,
   nil,   nil,    67,    67,    67,    67,   nil,    67,    67,    67,
    67,   nil,   nil,   nil,   nil,    67,    67,    67,   nil,   nil,
   nil,   nil,    67,    67,   nil,    67,    67,    67,    68,    68,
    68,   nil,    68,   nil,   nil,   nil,    68,    68,   nil,   nil,
   nil,    68,   nil,    68,    68,    68,    68,    68,    68,    68,
   nil,   nil,   nil,   nil,   nil,    68,    68,    68,    68,    68,
    68,    68,   nil,   nil,    68,   nil,   nil,   nil,   nil,   nil,
   nil,    68,   nil,   nil,    68,    68,    68,    68,    68,    68,
    68,    68,   nil,    68,    68,    68,   nil,    68,    68,   nil,
   nil,    68,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    68,   nil,   nil,    68,   nil,   nil,    68,    68,   nil,
   nil,    68,   nil,    68,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    68,    68,    68,    68,   nil,    68,    68,    68,    68,
   nil,   nil,   nil,   nil,    68,    68,   nil,   nil,   nil,    69,
    69,    69,    68,    69,    68,    68,    68,    69,    69,   nil,
   nil,   nil,    69,   nil,    69,    69,    69,    69,    69,    69,
    69,   nil,   nil,   nil,   nil,   nil,    69,    69,    69,    69,
    69,    69,    69,   nil,   nil,    69,   nil,   nil,   nil,   nil,
   nil,   nil,    69,   nil,   nil,    69,    69,    69,    69,    69,
    69,    69,    69,   nil,    69,    69,    69,   nil,    69,    69,
   nil,   nil,    69,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    69,
   nil,   nil,    69,   nil,   nil,    69,   nil,   nil,    69,    69,
   nil,   nil,    69,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    69,    69,    69,    69,   nil,    69,    69,    69,
    69,   nil,   nil,   nil,   nil,    69,    69,   nil,   nil,   nil,
    70,    70,    70,    69,    70,    69,    69,    69,    70,    70,
   nil,   nil,   nil,    70,   nil,    70,    70,    70,    70,    70,
    70,    70,   nil,   nil,   nil,   nil,   nil,    70,    70,    70,
    70,    70,    70,    70,   nil,   nil,    70,   nil,   nil,   nil,
   nil,   nil,   nil,    70,   nil,   nil,    70,    70,    70,    70,
    70,    70,    70,    70,   nil,    70,    70,    70,   nil,    70,
    70,   nil,   nil,    70,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    70,   nil,   nil,    70,   nil,   nil,    70,
    70,   nil,   nil,    70,   nil,   nil,   845,   nil,   845,   845,
   845,   845,   845,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   845,   nil,    70,    70,    70,    70,   nil,    70,    70,
    70,    70,   nil,   nil,   nil,   nil,    70,    70,   nil,   nil,
   nil,   nil,   nil,   845,    70,   nil,    70,    70,    70,   113,
   113,   113,   113,   113,   845,   845,   nil,   113,   113,   845,
   nil,   nil,   113,   nil,   113,   113,   113,   113,   113,   113,
   113,   nil,   nil,   nil,   nil,   nil,   113,   113,   113,   113,
   113,   113,   113,   nil,   nil,   113,   nil,   nil,   nil,   nil,
   nil,   113,   113,   113,   113,   113,   113,   113,   113,   113,
   113,   113,   113,   nil,   113,   113,   113,   nil,   113,   113,
   113,   113,   113,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   113,   nil,   nil,   113,   nil,   nil,   113,   113,
   nil,   nil,   113,   nil,   113,   nil,   nil,   nil,   113,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   113,   nil,   nil,
   nil,   nil,   113,   113,   113,   113,   nil,   113,   113,   113,
   113,   nil,   nil,   nil,   nil,   113,   113,   nil,   nil,   nil,
   nil,   nil,   113,   113,   nil,   113,   113,   113,   118,   118,
   118,   nil,   118,   nil,   nil,   nil,   118,   118,   nil,   nil,
   nil,   118,   nil,   118,   118,   118,   118,   118,   118,   118,
   nil,   nil,   nil,   nil,   nil,   118,   118,   118,   118,   118,
   118,   118,   nil,   nil,   118,   nil,   nil,   nil,   nil,   nil,
   nil,   118,   nil,   nil,   118,   118,   118,   118,   118,   118,
   118,   118,   nil,   118,   118,   118,   nil,   118,   118,   118,
   118,   118,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   118,   nil,   nil,   118,   nil,   nil,   118,   118,   nil,
   nil,   118,   nil,   nil,   nil,   nil,   nil,   118,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   118,   nil,   nil,   nil,
   nil,   118,   118,   118,   118,   nil,   118,   118,   118,   118,
   nil,   nil,   nil,   nil,   118,   118,   nil,   nil,   nil,   119,
   119,   119,   118,   119,   118,   118,   118,   119,   119,   nil,
   nil,   nil,   119,   nil,   119,   119,   119,   119,   119,   119,
   119,   nil,   nil,   nil,   nil,   nil,   119,   119,   119,   119,
   119,   119,   119,   nil,   nil,   119,   nil,   nil,   nil,   nil,
   nil,   nil,   119,   nil,   nil,   119,   119,   119,   119,   119,
   119,   119,   119,   nil,   119,   119,   119,   nil,   119,   119,
   119,   119,   119,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   119,   nil,   nil,   119,   nil,   nil,   119,   119,
   nil,   nil,   119,   nil,   nil,   nil,   nil,   nil,   119,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   119,   nil,   nil,
   nil,   nil,   119,   119,   119,   119,   nil,   119,   119,   119,
   119,   nil,   nil,   nil,   nil,   119,   119,   nil,   nil,   nil,
   120,   120,   120,   119,   120,   119,   119,   119,   120,   120,
   nil,   nil,   nil,   120,   nil,   120,   120,   120,   120,   120,
   120,   120,   nil,   nil,   nil,   nil,   nil,   120,   120,   120,
   120,   120,   120,   120,   nil,   nil,   120,   nil,   nil,   nil,
   nil,   nil,   nil,   120,   nil,   nil,   120,   120,   120,   120,
   120,   120,   120,   120,   nil,   120,   120,   120,   nil,   120,
   120,   120,   120,   120,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   120,   nil,   nil,   120,   nil,   nil,   120,
   120,   nil,   nil,   120,   nil,   nil,   nil,   nil,   nil,   120,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   120,   nil,
   nil,   nil,   nil,   120,   120,   120,   120,   nil,   120,   120,
   120,   120,   nil,   nil,   nil,   nil,   120,   120,   nil,   nil,
   nil,   121,   121,   121,   120,   121,   120,   120,   120,   121,
   121,   nil,   nil,   nil,   121,   nil,   121,   121,   121,   121,
   121,   121,   121,   nil,   nil,   nil,   nil,   nil,   121,   121,
   121,   121,   121,   121,   121,   nil,   nil,   121,   nil,   nil,
   nil,   nil,   nil,   nil,   121,   nil,   nil,   121,   121,   121,
   121,   121,   121,   121,   121,   nil,   121,   121,   121,   nil,
   121,   121,   121,   121,   121,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   121,   nil,   nil,   121,   nil,   nil,
   121,   121,   nil,   nil,   121,   nil,   nil,   nil,   nil,   nil,
   121,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   121,
   nil,   nil,   nil,   nil,   121,   121,   121,   121,   nil,   121,
   121,   121,   121,   nil,   nil,   nil,   nil,   121,   121,   nil,
   nil,   nil,   nil,   nil,   nil,   121,   nil,   121,   121,   121,
   122,   122,   122,   122,   122,   nil,   nil,   nil,   122,   122,
   nil,   nil,   nil,   122,   nil,   122,   122,   122,   122,   122,
   122,   122,   nil,   nil,   nil,   nil,   nil,   122,   122,   122,
   122,   122,   122,   122,   nil,   nil,   122,   nil,   nil,   nil,
   nil,   nil,   122,   122,   nil,   122,   122,   122,   122,   122,
   122,   122,   122,   122,   nil,   122,   122,   122,   nil,   122,
   122,   122,   122,   122,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   122,   nil,   nil,   122,   nil,   nil,   122,
   122,   nil,   nil,   122,   nil,   122,   nil,   nil,   nil,   122,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   122,   nil,
   nil,   nil,   nil,   122,   122,   122,   122,   nil,   122,   122,
   122,   122,   nil,   nil,   nil,   nil,   122,   122,   nil,   nil,
   nil,   209,   209,   209,   122,   209,   122,   122,   122,   209,
   209,   nil,   nil,   nil,   209,   nil,   209,   209,   209,   209,
   209,   209,   209,   nil,   nil,   nil,   nil,   nil,   209,   209,
   209,   209,   209,   209,   209,   nil,   nil,   209,   nil,   nil,
   nil,   nil,   nil,   nil,   209,   nil,   nil,   209,   209,   209,
   209,   209,   209,   209,   209,   nil,   209,   209,   209,   nil,
   209,   209,   209,   209,   209,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   209,   nil,   nil,   209,   nil,   nil,
   209,   209,   nil,   nil,   209,   nil,   209,   nil,   nil,   nil,
   209,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,
   nil,   nil,   nil,   nil,   209,   209,   209,   209,   nil,   209,
   209,   209,   209,   nil,   nil,   nil,   nil,   209,   209,   nil,
   nil,   nil,   210,   210,   210,   209,   210,   209,   209,   209,
   210,   210,   nil,   nil,   nil,   210,   nil,   210,   210,   210,
   210,   210,   210,   210,   nil,   nil,   nil,   nil,   nil,   210,
   210,   210,   210,   210,   210,   210,   nil,   nil,   210,   nil,
   nil,   nil,   nil,   nil,   nil,   210,   nil,   nil,   210,   210,
   210,   210,   210,   210,   210,   210,   nil,   210,   210,   210,
   nil,   210,   210,   210,   210,   210,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   210,   nil,   nil,   210,   nil,
   nil,   210,   210,   nil,   nil,   210,   nil,   nil,   nil,   nil,
   nil,   210,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   210,   nil,   nil,   nil,   nil,   210,   210,   210,   210,   nil,
   210,   210,   210,   210,   nil,   nil,   nil,   nil,   210,   210,
   nil,   nil,   nil,   211,   211,   211,   210,   211,   210,   210,
   210,   211,   211,   nil,   nil,   nil,   211,   nil,   211,   211,
   211,   211,   211,   211,   211,   nil,   nil,   nil,   nil,   nil,
   211,   211,   211,   211,   211,   211,   211,   nil,   nil,   211,
   nil,   nil,   nil,   nil,   nil,   nil,   211,   nil,   nil,   211,
   211,   211,   211,   211,   211,   211,   211,   211,   211,   211,
   211,   nil,   211,   211,   211,   211,   211,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   211,   nil,   nil,   211,
   nil,   nil,   211,   211,   nil,   nil,   211,   nil,   211,   nil,
   211,   nil,   211,   nil,   nil,   211,   nil,   nil,   nil,   nil,
   nil,   211,   nil,   nil,   nil,   nil,   211,   211,   211,   211,
   nil,   211,   211,   211,   211,   nil,   nil,   nil,   nil,   211,
   211,   nil,   nil,   nil,   214,   214,   214,   211,   214,   211,
   211,   211,   214,   214,   nil,   nil,   nil,   214,   nil,   214,
   214,   214,   214,   214,   214,   214,   nil,   nil,   nil,   nil,
   nil,   214,   214,   214,   214,   214,   214,   214,   nil,   nil,
   214,   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,
   214,   214,   214,   214,   214,   214,   214,   214,   nil,   214,
   214,   214,   nil,   214,   214,   214,   214,   214,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,
   214,   nil,   nil,   214,   214,   nil,   nil,   214,   nil,   nil,
   nil,   nil,   nil,   214,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   214,   nil,   nil,   nil,   nil,   214,   214,   214,
   214,   nil,   214,   214,   214,   214,   nil,   nil,   nil,   nil,
   214,   214,   nil,   nil,   nil,   215,   215,   215,   214,   215,
   214,   214,   214,   215,   215,   nil,   nil,   nil,   215,   nil,
   215,   215,   215,   215,   215,   215,   215,   nil,   nil,   nil,
   nil,   nil,   215,   215,   215,   215,   215,   215,   215,   nil,
   nil,   215,   nil,   nil,   nil,   nil,   nil,   nil,   215,   nil,
   nil,   215,   215,   215,   215,   215,   215,   215,   215,   nil,
   215,   215,   215,   nil,   215,   215,   215,   215,   215,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   215,   nil,
   nil,   215,   nil,   nil,   215,   215,   nil,   nil,   215,   nil,
   215,   nil,   nil,   nil,   215,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   215,   nil,   nil,   nil,   nil,   215,   215,
   215,   215,   nil,   215,   215,   215,   215,   nil,   nil,   nil,
   nil,   215,   215,   nil,   nil,   nil,   216,   216,   216,   215,
   216,   215,   215,   215,   216,   216,   nil,   nil,   nil,   216,
   nil,   216,   216,   216,   216,   216,   216,   216,   nil,   nil,
   nil,   nil,   nil,   216,   216,   216,   216,   216,   216,   216,
   nil,   nil,   216,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   216,   216,   216,   216,   216,   216,   216,   216,
   nil,   216,   216,   216,   nil,   216,   216,   216,   216,   216,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   216,   nil,   nil,   216,   216,   nil,   nil,   216,
   nil,   nil,   nil,   nil,   nil,   216,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   nil,   nil,   216,
   216,   216,   216,   nil,   216,   216,   216,   216,   nil,   nil,
   nil,   nil,   216,   216,   nil,   nil,   nil,   217,   217,   217,
   216,   217,   216,   216,   216,   217,   217,   nil,   nil,   nil,
   217,   nil,   217,   217,   217,   217,   217,   217,   217,   nil,
   nil,   nil,   nil,   nil,   217,   217,   217,   217,   217,   217,
   217,   nil,   nil,   217,   nil,   nil,   nil,   nil,   nil,   nil,
   217,   nil,   nil,   217,   217,   217,   217,   217,   217,   217,
   217,   nil,   217,   217,   217,   nil,   217,   217,   217,   217,
   217,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   217,   nil,   nil,   217,   nil,   nil,   217,   217,   nil,   nil,
   217,   nil,   nil,   nil,   nil,   nil,   217,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   217,   nil,   nil,   nil,   nil,
   217,   217,   217,   217,   nil,   217,   217,   217,   217,   nil,
   nil,   nil,   nil,   217,   217,   nil,   nil,   nil,   218,   218,
   218,   217,   218,   217,   217,   217,   218,   218,   nil,   nil,
   nil,   218,   nil,   218,   218,   218,   218,   218,   218,   218,
   nil,   nil,   nil,   nil,   nil,   218,   218,   218,   218,   218,
   218,   218,   nil,   nil,   218,   nil,   nil,   nil,   nil,   nil,
   nil,   218,   nil,   nil,   218,   218,   218,   218,   218,   218,
   218,   218,   nil,   218,   218,   218,   nil,   218,   218,   218,
   218,   218,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   218,   nil,   nil,   218,   nil,   nil,   218,   218,   nil,
   nil,   218,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,   nil,
   nil,   218,   218,   218,   218,   nil,   218,   218,   218,   218,
   nil,   nil,   nil,   nil,   218,   218,   nil,   nil,   nil,   219,
   219,   219,   218,   219,   218,   218,   218,   219,   219,   nil,
   nil,   nil,   219,   nil,   219,   219,   219,   219,   219,   219,
   219,   nil,   nil,   nil,   nil,   nil,   219,   219,   219,   219,
   219,   219,   219,   nil,   nil,   219,   nil,   nil,   nil,   nil,
   nil,   nil,   219,   nil,   nil,   219,   219,   219,   219,   219,
   219,   219,   219,   nil,   219,   219,   219,   nil,   219,   219,
   219,   219,   219,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   219,   nil,   nil,   219,   nil,   nil,   219,   219,
   nil,   nil,   219,   nil,   nil,   nil,   nil,   nil,   219,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,
   nil,   nil,   219,   219,   219,   219,   nil,   219,   219,   219,
   219,   nil,   nil,   nil,   nil,   219,   219,   219,   nil,   nil,
   230,   230,   230,   219,   230,   219,   219,   219,   230,   230,
   nil,   nil,   nil,   230,   nil,   230,   230,   230,   230,   230,
   230,   230,   nil,   nil,   nil,   nil,   nil,   230,   230,   230,
   230,   230,   230,   230,   nil,   nil,   230,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   230,   230,   230,   230,
   230,   230,   230,   230,   nil,   230,   230,   230,   nil,   230,
   230,   230,   230,   230,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   230,   nil,   nil,   230,
   230,   nil,   nil,   230,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   nil,   nil,   230,   230,   230,   230,   nil,   230,   230,
   230,   230,   nil,   nil,   nil,   nil,   230,   230,   nil,   nil,
   nil,   233,   233,   233,   230,   233,   230,   230,   230,   233,
   233,   nil,   nil,   nil,   233,   nil,   233,   233,   233,   233,
   233,   233,   233,   nil,   nil,   nil,   nil,   nil,   233,   233,
   233,   233,   233,   233,   233,   nil,   nil,   233,   nil,   nil,
   nil,   nil,   nil,   nil,   233,   nil,   nil,   233,   233,   233,
   233,   233,   233,   233,   233,   nil,   233,   233,   233,   nil,
   233,   233,   233,   233,   233,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   233,   nil,   nil,   233,   nil,   nil,
   233,   233,   nil,   nil,   233,   nil,   nil,   nil,   nil,   nil,
   233,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   233,
   nil,   nil,   nil,   nil,   233,   233,   233,   233,   nil,   233,
   233,   233,   233,   nil,   nil,   nil,   nil,   233,   233,   nil,
   nil,   nil,   234,   234,   234,   233,   234,   233,   233,   233,
   234,   234,   nil,   nil,   nil,   234,   nil,   234,   234,   234,
   234,   234,   234,   234,   nil,   nil,   nil,   nil,   nil,   234,
   234,   234,   234,   234,   234,   234,   nil,   nil,   234,   nil,
   nil,   nil,   nil,   nil,   nil,   234,   nil,   nil,   234,   234,
   234,   234,   234,   234,   234,   234,   nil,   234,   234,   234,
   nil,   234,   234,   234,   234,   234,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   234,   nil,   nil,   234,   nil,
   nil,   234,   234,   nil,   nil,   234,   nil,   nil,   nil,   nil,
   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   234,   nil,   nil,   nil,   nil,   234,   234,   234,   234,   nil,
   234,   234,   234,   234,   nil,   nil,   nil,   nil,   234,   234,
   nil,   nil,   nil,   235,   235,   235,   234,   235,   234,   234,
   234,   235,   235,   nil,   nil,   nil,   235,   nil,   235,   235,
   235,   235,   235,   235,   235,   nil,   nil,   nil,   nil,   nil,
   235,   235,   235,   235,   235,   235,   235,   nil,   nil,   235,
   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   235,
   235,   235,   235,   235,   235,   235,   235,   nil,   235,   235,
   235,   nil,   235,   235,   235,   235,   235,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   235,
   nil,   nil,   235,   235,   nil,   nil,   235,   nil,   nil,   nil,
   nil,   nil,   235,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   235,   nil,   nil,   nil,   nil,   235,   235,   235,   235,
   nil,   235,   235,   235,   235,   nil,   nil,   nil,   nil,   235,
   235,   nil,   nil,   nil,   236,   236,   236,   235,   236,   235,
   235,   235,   236,   236,   nil,   nil,   nil,   236,   nil,   236,
   236,   236,   236,   236,   236,   236,   nil,   nil,   nil,   nil,
   nil,   236,   236,   236,   236,   236,   236,   236,   nil,   nil,
   236,   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,
   236,   236,   236,   236,   236,   236,   236,   236,   nil,   236,
   236,   236,   nil,   236,   236,   236,   236,   236,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,
   236,   nil,   nil,   236,   236,   nil,   nil,   236,   nil,   nil,
   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   236,   nil,   nil,   nil,   nil,   236,   236,   236,
   236,   nil,   236,   236,   236,   236,   nil,   nil,   nil,   nil,
   236,   236,   nil,   nil,   nil,   237,   237,   237,   236,   237,
   236,   236,   236,   237,   237,   nil,   nil,   nil,   237,   nil,
   237,   237,   237,   237,   237,   237,   237,   nil,   nil,   nil,
   nil,   nil,   237,   237,   237,   237,   237,   237,   237,   nil,
   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,   237,   nil,
   nil,   237,   237,   237,   237,   237,   237,   237,   237,   nil,
   237,   237,   237,   nil,   237,   237,   237,   237,   237,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   237,   nil,
   nil,   237,   nil,   nil,   237,   237,   nil,   nil,   237,   nil,
   nil,   nil,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   237,   nil,   nil,   nil,   nil,   237,   237,
   237,   237,   nil,   237,   237,   237,   237,   nil,   nil,   nil,
   nil,   237,   237,   nil,   nil,   nil,   238,   238,   238,   237,
   238,   237,   237,   237,   238,   238,   nil,   nil,   nil,   238,
   nil,   238,   238,   238,   238,   238,   238,   238,   nil,   nil,
   nil,   nil,   nil,   238,   238,   238,   238,   238,   238,   238,
   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,   238,
   nil,   nil,   238,   238,   238,   238,   238,   238,   238,   238,
   nil,   238,   238,   238,   nil,   238,   238,   238,   238,   238,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   238,
   nil,   nil,   238,   nil,   nil,   238,   238,   nil,   nil,   238,
   nil,   nil,   nil,   nil,   nil,   238,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   238,   nil,   nil,   nil,   nil,   238,
   238,   238,   238,   nil,   238,   238,   238,   238,   nil,   nil,
   nil,   nil,   238,   238,   nil,   nil,   nil,   239,   239,   239,
   238,   239,   238,   238,   238,   239,   239,   nil,   nil,   nil,
   239,   nil,   239,   239,   239,   239,   239,   239,   239,   nil,
   nil,   nil,   nil,   nil,   239,   239,   239,   239,   239,   239,
   239,   nil,   nil,   239,   nil,   nil,   nil,   nil,   nil,   nil,
   239,   nil,   nil,   239,   239,   239,   239,   239,   239,   239,
   239,   nil,   239,   239,   239,   nil,   239,   239,   239,   239,
   239,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   239,   nil,   nil,   239,   nil,   nil,   239,   239,   nil,   nil,
   239,   nil,   nil,   nil,   nil,   nil,   239,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   239,   nil,   nil,   nil,   nil,
   239,   239,   239,   239,   nil,   239,   239,   239,   239,   nil,
   nil,   nil,   nil,   239,   239,   nil,   nil,   nil,   240,   240,
   240,   239,   240,   239,   239,   239,   240,   240,   nil,   nil,
   nil,   240,   nil,   240,   240,   240,   240,   240,   240,   240,
   nil,   nil,   nil,   nil,   nil,   240,   240,   240,   240,   240,
   240,   240,   nil,   nil,   240,   nil,   nil,   nil,   nil,   nil,
   nil,   240,   nil,   nil,   240,   240,   240,   240,   240,   240,
   240,   240,   nil,   240,   240,   240,   nil,   240,   240,   240,
   240,   240,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   240,   nil,   nil,   240,   nil,   nil,   240,   240,   nil,
   nil,   240,   nil,   nil,   nil,   nil,   nil,   240,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   240,   nil,   nil,   nil,
   nil,   240,   240,   240,   240,   nil,   240,   240,   240,   240,
   nil,   nil,   nil,   nil,   240,   240,   nil,   nil,   nil,   241,
   241,   241,   240,   241,   240,   240,   240,   241,   241,   nil,
   nil,   nil,   241,   nil,   241,   241,   241,   241,   241,   241,
   241,   nil,   nil,   nil,   nil,   nil,   241,   241,   241,   241,
   241,   241,   241,   nil,   nil,   241,   nil,   nil,   nil,   nil,
   nil,   nil,   241,   nil,   nil,   241,   241,   241,   241,   241,
   241,   241,   241,   nil,   241,   241,   241,   nil,   241,   241,
   241,   241,   241,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   241,   nil,   nil,   241,   nil,   nil,   241,   241,
   nil,   nil,   241,   nil,   nil,   nil,   nil,   nil,   241,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   241,   nil,   nil,
   nil,   nil,   241,   241,   241,   241,   nil,   241,   241,   241,
   241,   nil,   nil,   nil,   nil,   241,   241,   nil,   nil,   nil,
   242,   242,   242,   241,   242,   241,   241,   241,   242,   242,
   nil,   nil,   nil,   242,   nil,   242,   242,   242,   242,   242,
   242,   242,   nil,   nil,   nil,   nil,   nil,   242,   242,   242,
   242,   242,   242,   242,   nil,   nil,   242,   nil,   nil,   nil,
   nil,   nil,   nil,   242,   nil,   nil,   242,   242,   242,   242,
   242,   242,   242,   242,   nil,   242,   242,   242,   nil,   242,
   242,   242,   242,   242,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   242,   nil,   nil,   242,   nil,   nil,   242,
   242,   nil,   nil,   242,   nil,   nil,   nil,   nil,   nil,   242,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   242,   nil,
   nil,   nil,   nil,   242,   242,   242,   242,   nil,   242,   242,
   242,   242,   nil,   nil,   nil,   nil,   242,   242,   nil,   nil,
   nil,   243,   243,   243,   242,   243,   242,   242,   242,   243,
   243,   nil,   nil,   nil,   243,   nil,   243,   243,   243,   243,
   243,   243,   243,   nil,   nil,   nil,   nil,   nil,   243,   243,
   243,   243,   243,   243,   243,   nil,   nil,   243,   nil,   nil,
   nil,   nil,   nil,   nil,   243,   nil,   nil,   243,   243,   243,
   243,   243,   243,   243,   243,   nil,   243,   243,   243,   nil,
   243,   243,   243,   243,   243,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   243,   nil,   nil,   243,   nil,   nil,
   243,   243,   nil,   nil,   243,   nil,   nil,   nil,   nil,   nil,
   243,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   243,
   nil,   nil,   nil,   nil,   243,   243,   243,   243,   nil,   243,
   243,   243,   243,   nil,   nil,   nil,   nil,   243,   243,   nil,
   nil,   nil,   244,   244,   244,   243,   244,   243,   243,   243,
   244,   244,   nil,   nil,   nil,   244,   nil,   244,   244,   244,
   244,   244,   244,   244,   nil,   nil,   nil,   nil,   nil,   244,
   244,   244,   244,   244,   244,   244,   nil,   nil,   244,   nil,
   nil,   nil,   nil,   nil,   nil,   244,   nil,   nil,   244,   244,
   244,   244,   244,   244,   244,   244,   nil,   244,   244,   244,
   nil,   244,   244,   244,   244,   244,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   244,   nil,   nil,   244,   nil,
   nil,   244,   244,   nil,   nil,   244,   nil,   nil,   nil,   nil,
   nil,   244,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   244,   nil,   nil,   nil,   nil,   244,   244,   244,   244,   nil,
   244,   244,   244,   244,   nil,   nil,   nil,   nil,   244,   244,
   nil,   nil,   nil,   245,   245,   245,   244,   245,   244,   244,
   244,   245,   245,   nil,   nil,   nil,   245,   nil,   245,   245,
   245,   245,   245,   245,   245,   nil,   nil,   nil,   nil,   nil,
   245,   245,   245,   245,   245,   245,   245,   nil,   nil,   245,
   nil,   nil,   nil,   nil,   nil,   nil,   245,   nil,   nil,   245,
   245,   245,   245,   245,   245,   245,   245,   nil,   245,   245,
   245,   nil,   245,   245,   245,   245,   245,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   245,   nil,   nil,   245,
   nil,   nil,   245,   245,   nil,   nil,   245,   nil,   nil,   nil,
   nil,   nil,   245,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   245,   nil,   nil,   nil,   nil,   245,   245,   245,   245,
   nil,   245,   245,   245,   245,   nil,   nil,   nil,   nil,   245,
   245,   nil,   nil,   nil,   246,   246,   246,   245,   246,   245,
   245,   245,   246,   246,   nil,   nil,   nil,   246,   nil,   246,
   246,   246,   246,   246,   246,   246,   nil,   nil,   nil,   nil,
   nil,   246,   246,   246,   246,   246,   246,   246,   nil,   nil,
   246,   nil,   nil,   nil,   nil,   nil,   nil,   246,   nil,   nil,
   246,   246,   246,   246,   246,   246,   246,   246,   nil,   246,
   246,   246,   nil,   246,   246,   246,   246,   246,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   246,   nil,   nil,
   246,   nil,   nil,   246,   246,   nil,   nil,   246,   nil,   nil,
   nil,   nil,   nil,   246,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   246,   nil,   nil,   nil,   nil,   246,   246,   246,
   246,   nil,   246,   246,   246,   246,   nil,   nil,   nil,   nil,
   246,   246,   nil,   nil,   nil,   247,   247,   247,   246,   247,
   246,   246,   246,   247,   247,   nil,   nil,   nil,   247,   nil,
   247,   247,   247,   247,   247,   247,   247,   nil,   nil,   nil,
   nil,   nil,   247,   247,   247,   247,   247,   247,   247,   nil,
   nil,   247,   nil,   nil,   nil,   nil,   nil,   nil,   247,   nil,
   nil,   247,   247,   247,   247,   247,   247,   247,   247,   nil,
   247,   247,   247,   nil,   247,   247,   247,   247,   247,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   247,   nil,
   nil,   247,   nil,   nil,   247,   247,   nil,   nil,   247,   nil,
   nil,   nil,   nil,   nil,   247,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   247,   nil,   nil,   nil,   nil,   247,   247,
   247,   247,   nil,   247,   247,   247,   247,   nil,   nil,   nil,
   nil,   247,   247,   nil,   nil,   nil,   248,   248,   248,   247,
   248,   247,   247,   247,   248,   248,   nil,   nil,   nil,   248,
   nil,   248,   248,   248,   248,   248,   248,   248,   nil,   nil,
   nil,   nil,   nil,   248,   248,   248,   248,   248,   248,   248,
   nil,   nil,   248,   nil,   nil,   nil,   nil,   nil,   nil,   248,
   nil,   nil,   248,   248,   248,   248,   248,   248,   248,   248,
   nil,   248,   248,   248,   nil,   248,   248,   248,   248,   248,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   248,
   nil,   nil,   248,   nil,   nil,   248,   248,   nil,   nil,   248,
   nil,   nil,   nil,   nil,   nil,   248,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   248,   nil,   nil,   nil,   nil,   248,
   248,   248,   248,   nil,   248,   248,   248,   248,   nil,   nil,
   nil,   nil,   248,   248,   nil,   nil,   nil,   249,   249,   249,
   248,   249,   248,   248,   248,   249,   249,   nil,   nil,   nil,
   249,   nil,   249,   249,   249,   249,   249,   249,   249,   nil,
   nil,   nil,   nil,   nil,   249,   249,   249,   249,   249,   249,
   249,   nil,   nil,   249,   nil,   nil,   nil,   nil,   nil,   nil,
   249,   nil,   nil,   249,   249,   249,   249,   249,   249,   249,
   249,   nil,   249,   249,   249,   nil,   249,   249,   249,   249,
   249,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   249,   nil,   nil,   249,   nil,   nil,   249,   249,   nil,   nil,
   249,   nil,   nil,   nil,   nil,   nil,   249,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   249,   nil,   nil,   nil,   nil,
   249,   249,   249,   249,   nil,   249,   249,   249,   249,   nil,
   nil,   nil,   nil,   249,   249,   nil,   nil,   nil,   250,   250,
   250,   249,   250,   249,   249,   249,   250,   250,   nil,   nil,
   nil,   250,   nil,   250,   250,   250,   250,   250,   250,   250,
   nil,   nil,   nil,   nil,   nil,   250,   250,   250,   250,   250,
   250,   250,   nil,   nil,   250,   nil,   nil,   nil,   nil,   nil,
   nil,   250,   nil,   nil,   250,   250,   250,   250,   250,   250,
   250,   250,   nil,   250,   250,   250,   nil,   250,   250,   250,
   250,   250,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   250,   nil,   nil,   250,   nil,   nil,   250,   250,   nil,
   nil,   250,   nil,   nil,   nil,   nil,   nil,   250,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   250,   nil,   nil,   nil,
   nil,   250,   250,   250,   250,   nil,   250,   250,   250,   250,
   nil,   nil,   nil,   nil,   250,   250,   nil,   nil,   nil,   251,
   251,   251,   250,   251,   250,   250,   250,   251,   251,   nil,
   nil,   nil,   251,   nil,   251,   251,   251,   251,   251,   251,
   251,   nil,   nil,   nil,   nil,   nil,   251,   251,   251,   251,
   251,   251,   251,   nil,   nil,   251,   nil,   nil,   nil,   nil,
   nil,   nil,   251,   nil,   nil,   251,   251,   251,   251,   251,
   251,   251,   251,   nil,   251,   251,   251,   nil,   251,   251,
   251,   251,   251,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   251,   nil,   nil,   251,   nil,   nil,   251,   251,
   nil,   nil,   251,   nil,   nil,   nil,   nil,   nil,   251,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   251,   nil,   nil,
   nil,   nil,   251,   251,   251,   251,   nil,   251,   251,   251,
   251,   nil,   nil,   nil,   nil,   251,   251,   nil,   nil,   nil,
   252,   252,   252,   251,   252,   251,   251,   251,   252,   252,
   nil,   nil,   nil,   252,   nil,   252,   252,   252,   252,   252,
   252,   252,   nil,   nil,   nil,   nil,   nil,   252,   252,   252,
   252,   252,   252,   252,   nil,   nil,   252,   nil,   nil,   nil,
   nil,   nil,   nil,   252,   nil,   nil,   252,   252,   252,   252,
   252,   252,   252,   252,   nil,   252,   252,   252,   nil,   252,
   252,   252,   252,   252,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   252,   nil,   nil,   252,   nil,   nil,   252,
   252,   nil,   nil,   252,   nil,   nil,   nil,   nil,   nil,   252,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   252,   nil,
   nil,   nil,   nil,   252,   252,   252,   252,   nil,   252,   252,
   252,   252,   nil,   nil,   nil,   nil,   252,   252,   nil,   nil,
   nil,   253,   253,   253,   252,   253,   252,   252,   252,   253,
   253,   nil,   nil,   nil,   253,   nil,   253,   253,   253,   253,
   253,   253,   253,   nil,   nil,   nil,   nil,   nil,   253,   253,
   253,   253,   253,   253,   253,   nil,   nil,   253,   nil,   nil,
   nil,   nil,   nil,   nil,   253,   nil,   nil,   253,   253,   253,
   253,   253,   253,   253,   253,   nil,   253,   253,   253,   nil,
   253,   253,   253,   253,   253,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   253,   nil,   nil,   253,   nil,   nil,
   253,   253,   nil,   nil,   253,   nil,   nil,   nil,   nil,   nil,
   253,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   253,
   nil,   nil,   nil,   nil,   253,   253,   253,   253,   nil,   253,
   253,   253,   253,   nil,   nil,   nil,   nil,   253,   253,   nil,
   nil,   nil,   254,   254,   254,   253,   254,   253,   253,   253,
   254,   254,   nil,   nil,   nil,   254,   nil,   254,   254,   254,
   254,   254,   254,   254,   nil,   nil,   nil,   nil,   nil,   254,
   254,   254,   254,   254,   254,   254,   nil,   nil,   254,   nil,
   nil,   nil,   nil,   nil,   nil,   254,   nil,   nil,   254,   254,
   254,   254,   254,   254,   254,   254,   nil,   254,   254,   254,
   nil,   254,   254,   254,   254,   254,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   254,   nil,   nil,   254,   nil,
   nil,   254,   254,   nil,   nil,   254,   nil,   nil,   nil,   nil,
   nil,   254,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   254,   nil,   nil,   nil,   nil,   254,   254,   254,   254,   nil,
   254,   254,   254,   254,   nil,   nil,   nil,   nil,   254,   254,
   nil,   nil,   nil,   255,   255,   255,   254,   255,   254,   254,
   254,   255,   255,   nil,   nil,   nil,   255,   nil,   255,   255,
   255,   255,   255,   255,   255,   nil,   nil,   nil,   nil,   nil,
   255,   255,   255,   255,   255,   255,   255,   nil,   nil,   255,
   nil,   nil,   nil,   nil,   nil,   nil,   255,   nil,   nil,   255,
   255,   255,   255,   255,   255,   255,   255,   nil,   255,   255,
   255,   nil,   255,   255,   255,   255,   255,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   255,   nil,   nil,   255,
   nil,   nil,   255,   255,   nil,   nil,   255,   nil,   nil,   nil,
   nil,   nil,   255,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   255,   nil,   nil,   nil,   nil,   255,   255,   255,   255,
   nil,   255,   255,   255,   255,   nil,   nil,   nil,   nil,   255,
   255,   nil,   nil,   nil,   256,   256,   256,   255,   256,   255,
   255,   255,   256,   256,   nil,   nil,   nil,   256,   nil,   256,
   256,   256,   256,   256,   256,   256,   nil,   nil,   nil,   nil,
   nil,   256,   256,   256,   256,   256,   256,   256,   nil,   nil,
   256,   nil,   nil,   nil,   nil,   nil,   nil,   256,   nil,   nil,
   256,   256,   256,   256,   256,   256,   256,   256,   nil,   256,
   256,   256,   nil,   256,   256,   256,   256,   256,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   256,   nil,   nil,
   256,   nil,   nil,   256,   256,   nil,   nil,   256,   nil,   nil,
   nil,   nil,   nil,   256,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   256,   nil,   nil,   nil,   nil,   256,   256,   256,
   256,   nil,   256,   256,   256,   256,   nil,   nil,   nil,   nil,
   256,   256,   nil,   nil,   nil,   257,   257,   257,   256,   257,
   256,   256,   256,   257,   257,   nil,   nil,   nil,   257,   nil,
   257,   257,   257,   257,   257,   257,   257,   nil,   nil,   nil,
   nil,   nil,   257,   257,   257,   257,   257,   257,   257,   nil,
   nil,   257,   nil,   nil,   nil,   nil,   nil,   nil,   257,   nil,
   nil,   257,   257,   257,   257,   257,   257,   257,   257,   nil,
   257,   257,   257,   nil,   257,   257,   257,   257,   257,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   257,   nil,
   nil,   257,   nil,   nil,   257,   257,   nil,   nil,   257,   nil,
   nil,   nil,   nil,   nil,   257,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   257,   nil,   nil,   nil,   nil,   257,   257,
   257,   257,   nil,   257,   257,   257,   257,   nil,   nil,   nil,
   nil,   257,   257,   nil,   nil,   nil,   258,   258,   258,   257,
   258,   257,   257,   257,   258,   258,   nil,   nil,   nil,   258,
   nil,   258,   258,   258,   258,   258,   258,   258,   nil,   nil,
   nil,   nil,   nil,   258,   258,   258,   258,   258,   258,   258,
   nil,   nil,   258,   nil,   nil,   nil,   nil,   nil,   nil,   258,
   nil,   nil,   258,   258,   258,   258,   258,   258,   258,   258,
   nil,   258,   258,   258,   nil,   258,   258,   258,   258,   258,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   258,
   nil,   nil,   258,   nil,   nil,   258,   258,   nil,   nil,   258,
   nil,   nil,   nil,   nil,   nil,   258,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   258,   nil,   nil,   nil,   nil,   258,
   258,   258,   258,   nil,   258,   258,   258,   258,   nil,   nil,
   nil,   nil,   258,   258,   nil,   nil,   nil,   265,   265,   265,
   258,   265,   258,   258,   258,   265,   265,   nil,   nil,   nil,
   265,   nil,   265,   265,   265,   265,   265,   265,   265,   nil,
   nil,   nil,   nil,   nil,   265,   265,   265,   265,   265,   265,
   265,   nil,   nil,   265,   nil,   nil,   nil,   nil,   nil,   nil,
   265,   nil,   nil,   265,   265,   265,   265,   265,   265,   265,
   265,   265,   265,   265,   265,   nil,   265,   265,   265,   265,
   265,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   265,   nil,   nil,   265,   nil,   nil,   265,   265,   nil,   nil,
   265,   nil,   265,   nil,   265,   nil,   265,   nil,   nil,   265,
   nil,   nil,   nil,   nil,   nil,   265,   nil,   nil,   nil,   nil,
   265,   265,   265,   265,   nil,   265,   265,   265,   265,   nil,
   nil,   nil,   nil,   265,   265,   nil,   nil,   nil,   266,   266,
   266,   265,   266,   265,   265,   265,   266,   266,   nil,   nil,
   nil,   266,   nil,   266,   266,   266,   266,   266,   266,   266,
   nil,   nil,   nil,   nil,   nil,   266,   266,   266,   266,   266,
   266,   266,   nil,   nil,   266,   nil,   nil,   nil,   nil,   nil,
   nil,   266,   nil,   nil,   266,   266,   266,   266,   266,   266,
   266,   266,   266,   266,   266,   266,   nil,   266,   266,   266,
   266,   266,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   266,   nil,   nil,   266,   nil,   nil,   266,   266,   nil,
   nil,   266,   nil,   266,   nil,   266,   nil,   266,   nil,   nil,
   266,   nil,   nil,   nil,   nil,   nil,   266,   nil,   nil,   nil,
   nil,   266,   266,   266,   266,   nil,   266,   266,   266,   266,
   nil,   nil,   nil,   nil,   266,   266,   nil,   nil,   nil,   274,
   274,   274,   266,   274,   266,   266,   266,   274,   274,   nil,
   nil,   nil,   274,   nil,   274,   274,   274,   274,   274,   274,
   274,   nil,   nil,   nil,   nil,   nil,   274,   274,   274,   274,
   274,   274,   274,   nil,   nil,   274,   nil,   nil,   nil,   nil,
   nil,   nil,   274,   nil,   nil,   274,   274,   274,   274,   274,
   274,   274,   274,   274,   274,   274,   274,   nil,   274,   274,
   274,   274,   274,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   274,   nil,   nil,   274,   nil,   nil,   274,   274,
   nil,   nil,   274,   nil,   274,   nil,   274,   nil,   274,   nil,
   nil,   274,   nil,   nil,   nil,   nil,   nil,   274,   nil,   nil,
   nil,   nil,   274,   274,   274,   274,   nil,   274,   274,   274,
   274,   nil,   nil,   nil,   nil,   274,   274,   274,   nil,   nil,
   281,   281,   281,   274,   281,   274,   274,   274,   281,   281,
   nil,   nil,   nil,   281,   nil,   281,   281,   281,   281,   281,
   281,   281,   nil,   nil,   nil,   nil,   nil,   281,   281,   281,
   281,   281,   281,   281,   nil,   nil,   281,   nil,   nil,   nil,
   nil,   nil,   nil,   281,   nil,   nil,   281,   281,   281,   281,
   281,   281,   281,   281,   nil,   281,   281,   281,   nil,   281,
   281,   281,   281,   281,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   281,   nil,   nil,   281,   nil,   nil,   281,
   281,   nil,   nil,   281,   nil,   nil,   nil,   nil,   nil,   281,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   281,   nil,
   nil,   nil,   nil,   281,   281,   281,   281,   nil,   281,   281,
   281,   281,   nil,   nil,   nil,   nil,   281,   281,   nil,   nil,
   nil,   283,   283,   283,   281,   283,   281,   281,   281,   283,
   283,   nil,   nil,   nil,   283,   nil,   283,   283,   283,   283,
   283,   283,   283,   nil,   nil,   nil,   nil,   nil,   283,   283,
   283,   283,   283,   283,   283,   nil,   nil,   283,   nil,   nil,
   nil,   nil,   nil,   nil,   283,   nil,   nil,   283,   283,   283,
   283,   283,   283,   283,   283,   nil,   283,   283,   283,   nil,
   283,   283,   283,   283,   283,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   283,   nil,   nil,   283,   nil,   nil,
   283,   283,   nil,   nil,   283,   nil,   nil,   nil,   nil,   nil,
   283,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   283,
   nil,   nil,   nil,   nil,   283,   283,   283,   283,   nil,   283,
   283,   283,   283,   nil,   nil,   nil,   nil,   283,   283,   nil,
   nil,   nil,   285,   285,   285,   283,   285,   283,   283,   283,
   285,   285,   nil,   nil,   nil,   285,   nil,   285,   285,   285,
   285,   285,   285,   285,   nil,   nil,   nil,   nil,   nil,   285,
   285,   285,   285,   285,   285,   285,   nil,   nil,   285,   nil,
   nil,   nil,   nil,   nil,   nil,   285,   nil,   nil,   285,   285,
   285,   285,   285,   285,   285,   285,   nil,   285,   285,   285,
   nil,   285,   285,   285,   285,   285,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   285,   nil,   nil,   285,   nil,
   nil,   285,   285,   nil,   nil,   285,   nil,   nil,   nil,   nil,
   nil,   285,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   285,   nil,   nil,   nil,   nil,   285,   285,   285,   285,   nil,
   285,   285,   285,   285,   nil,   nil,   nil,   nil,   285,   285,
   nil,   nil,   nil,   286,   286,   286,   285,   286,   285,   285,
   285,   286,   286,   nil,   nil,   nil,   286,   nil,   286,   286,
   286,   286,   286,   286,   286,   nil,   nil,   nil,   nil,   nil,
   286,   286,   286,   286,   286,   286,   286,   nil,   nil,   286,
   nil,   nil,   nil,   nil,   nil,   nil,   286,   nil,   nil,   286,
   286,   286,   286,   286,   286,   286,   286,   nil,   286,   286,
   286,   nil,   286,   286,   286,   286,   286,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   286,   nil,   nil,   286,
   nil,   nil,   286,   286,   nil,   nil,   286,   nil,   nil,   nil,
   nil,   nil,   286,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   286,   nil,   nil,   nil,   nil,   286,   286,   286,   286,
   nil,   286,   286,   286,   286,   nil,   nil,   nil,   nil,   286,
   286,   nil,   nil,   nil,   nil,   nil,   nil,   286,   nil,   286,
   286,   286,   291,   291,   291,   291,   291,   nil,   nil,   nil,
   291,   291,   nil,   nil,   nil,   291,   nil,   291,   291,   291,
   291,   291,   291,   291,   nil,   nil,   nil,   nil,   nil,   291,
   291,   291,   291,   291,   291,   291,   nil,   nil,   291,   nil,
   nil,   nil,   nil,   nil,   291,   291,   nil,   291,   291,   291,
   291,   291,   291,   291,   291,   291,   nil,   291,   291,   291,
   nil,   291,   291,   291,   291,   291,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   291,   nil,   nil,   291,   nil,
   nil,   291,   291,   nil,   nil,   291,   nil,   291,   nil,   nil,
   nil,   291,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   291,   nil,   nil,   nil,   nil,   291,   291,   291,   291,   nil,
   291,   291,   291,   291,   nil,   nil,   nil,   nil,   291,   291,
   nil,   nil,   nil,   299,   299,   299,   291,   299,   291,   291,
   291,   299,   299,   nil,   nil,   nil,   299,   nil,   299,   299,
   299,   299,   299,   299,   299,   nil,   nil,   nil,   nil,   nil,
   299,   299,   299,   299,   299,   299,   299,   nil,   nil,   299,
   nil,   nil,   nil,   nil,   nil,   nil,   299,   nil,   nil,   299,
   299,   299,   299,   299,   299,   299,   299,   nil,   299,   299,
   299,   nil,   299,   299,   nil,   nil,   299,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   299,   nil,   nil,   299,
   nil,   nil,   299,   299,   nil,   nil,   299,   nil,   nil,   935,
   nil,   935,   935,   935,   935,   935,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   935,   nil,   299,   299,   299,   299,
   nil,   299,   299,   299,   299,   nil,   nil,   nil,   nil,   299,
   299,   nil,   nil,   nil,   299,   nil,   935,   299,   nil,   299,
   299,   299,   316,   316,   316,   nil,   316,   935,   935,   nil,
   316,   316,   935,   nil,   nil,   316,   nil,   316,   316,   316,
   316,   316,   316,   316,   nil,   nil,   nil,   nil,   nil,   316,
   316,   316,   316,   316,   316,   316,   nil,   nil,   316,   nil,
   nil,   nil,   nil,   nil,   nil,   316,   nil,   nil,   316,   316,
   316,   316,   316,   316,   316,   316,   nil,   316,   316,   316,
   nil,   316,   316,   nil,   nil,   316,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   316,   nil,   nil,   316,   nil,
   nil,   316,   316,   nil,   nil,   316,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   316,   316,   316,   316,   nil,
   316,   316,   316,   316,   nil,   nil,   nil,   nil,   316,   316,
   nil,   nil,   nil,   324,   324,   324,   316,   324,   316,   316,
   316,   324,   324,   nil,   nil,   nil,   324,   nil,   324,   324,
   324,   324,   324,   324,   324,   nil,   nil,   nil,   nil,   nil,
   324,   324,   324,   324,   324,   324,   324,   nil,   nil,   324,
   nil,   nil,   nil,   nil,   nil,   nil,   324,   nil,   nil,   324,
   324,   324,   324,   324,   324,   324,   324,   nil,   324,   324,
   324,   nil,   324,   324,   324,   324,   324,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   324,   nil,   nil,   324,
   324,   nil,   324,   324,   nil,   nil,   324,   nil,   nil,   nil,
   nil,   nil,   324,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   324,   nil,   nil,   nil,   nil,   324,   324,   324,   324,
   nil,   324,   324,   324,   324,   nil,   nil,   nil,   nil,   324,
   324,   nil,   nil,   nil,   326,   326,   326,   324,   326,   324,
   324,   324,   326,   326,   nil,   nil,   nil,   326,   nil,   326,
   326,   326,   326,   326,   326,   326,   nil,   nil,   nil,   nil,
   nil,   326,   326,   326,   326,   326,   326,   326,   nil,   nil,
   326,   nil,   nil,   nil,   nil,   nil,   nil,   326,   nil,   nil,
   326,   326,   326,   326,   326,   326,   326,   326,   nil,   326,
   326,   326,   nil,   326,   326,   326,   326,   326,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   326,   nil,   nil,
   326,   nil,   nil,   326,   326,   nil,   nil,   326,   nil,   nil,
   nil,   nil,   nil,   326,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   326,   nil,   nil,   nil,   nil,   326,   326,   326,
   326,   nil,   326,   326,   326,   326,   nil,   nil,   nil,   nil,
   326,   326,   nil,   nil,   nil,   340,   340,   340,   326,   340,
   326,   326,   326,   340,   340,   nil,   nil,   nil,   340,   nil,
   340,   340,   340,   340,   340,   340,   340,   nil,   nil,   nil,
   nil,   nil,   340,   340,   340,   340,   340,   340,   340,   nil,
   nil,   340,   nil,   nil,   nil,   nil,   nil,   nil,   340,   nil,
   nil,   340,   340,   340,   340,   340,   340,   340,   340,   nil,
   340,   340,   340,   nil,   340,   340,   340,   340,   340,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   340,   nil,
   nil,   340,   nil,   nil,   340,   340,   nil,   nil,   340,   nil,
   nil,   nil,   nil,   nil,   340,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   340,   nil,   nil,   nil,   nil,   340,   340,
   340,   340,   nil,   340,   340,   340,   340,   nil,   nil,   nil,
   nil,   340,   340,   nil,   nil,   nil,   341,   341,   341,   340,
   341,   340,   340,   340,   341,   341,   nil,   nil,   nil,   341,
   nil,   341,   341,   341,   341,   341,   341,   341,   nil,   nil,
   nil,   nil,   nil,   341,   341,   341,   341,   341,   341,   341,
   nil,   nil,   341,   nil,   nil,   nil,   nil,   nil,   nil,   341,
   nil,   nil,   341,   341,   341,   341,   341,   341,   341,   341,
   nil,   341,   341,   341,   nil,   341,   341,   341,   341,   341,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   341,
   nil,   nil,   341,   nil,   nil,   341,   341,   nil,   nil,   341,
   nil,   nil,   nil,   nil,   nil,   341,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   341,   nil,   nil,   nil,   nil,   341,
   341,   341,   341,   nil,   341,   341,   341,   341,   nil,   nil,
   nil,   nil,   341,   341,   nil,   nil,   nil,   360,   360,   360,
   341,   360,   341,   341,   341,   360,   360,   nil,   nil,   nil,
   360,   nil,   360,   360,   360,   360,   360,   360,   360,   nil,
   nil,   nil,   nil,   nil,   360,   360,   360,   360,   360,   360,
   360,   nil,   nil,   360,   nil,   nil,   nil,   nil,   nil,   nil,
   360,   nil,   nil,   360,   360,   360,   360,   360,   360,   360,
   360,   nil,   360,   360,   360,   nil,   360,   360,   360,   360,
   360,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   360,   nil,   nil,   360,   nil,   nil,   360,   360,   nil,   nil,
   360,   nil,   nil,   nil,   nil,   nil,   360,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   360,   nil,   nil,   nil,   nil,
   360,   360,   360,   360,   nil,   360,   360,   360,   360,   nil,
   nil,   nil,   nil,   360,   360,   nil,   nil,   nil,   376,   376,
   376,   360,   376,   360,   360,   360,   376,   376,   nil,   nil,
   nil,   376,   nil,   376,   376,   376,   376,   376,   376,   376,
   nil,   nil,   nil,   nil,   nil,   376,   376,   376,   376,   376,
   376,   376,   nil,   nil,   376,   nil,   nil,   nil,   nil,   nil,
   nil,   376,   nil,   nil,   376,   376,   376,   376,   376,   376,
   376,   376,   nil,   376,   376,   376,   nil,   376,   376,   376,
   376,   376,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   376,   nil,   nil,   376,   nil,   nil,   376,   376,   nil,
   nil,   376,   nil,   nil,   nil,   nil,   nil,   376,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   376,   nil,   nil,   nil,
   nil,   376,   376,   376,   376,   nil,   376,   376,   376,   376,
   nil,   nil,   nil,   nil,   376,   376,   nil,   nil,   nil,   404,
   404,   404,   376,   404,   376,   376,   376,   404,   404,   nil,
   nil,   nil,   404,   nil,   404,   404,   404,   404,   404,   404,
   404,   nil,   nil,   nil,   nil,   nil,   404,   404,   404,   404,
   404,   404,   404,   nil,   nil,   404,   nil,   nil,   nil,   nil,
   nil,   nil,   404,   nil,   nil,   404,   404,   404,   404,   404,
   404,   404,   404,   nil,   404,   404,   404,   nil,   404,   404,
   404,   404,   404,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   404,   nil,   nil,   404,   nil,   nil,   404,   404,
   nil,   nil,   404,   nil,   nil,   nil,   nil,   nil,   404,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   404,   nil,   nil,
   nil,   nil,   404,   404,   404,   404,   nil,   404,   404,   404,
   404,   nil,   nil,   nil,   nil,   404,   404,   nil,   nil,   nil,
   442,   442,   442,   404,   442,   404,   404,   404,   442,   442,
   nil,   nil,   nil,   442,   nil,   442,   442,   442,   442,   442,
   442,   442,   nil,   nil,   nil,   nil,   nil,   442,   442,   442,
   442,   442,   442,   442,   nil,   nil,   442,   nil,   nil,   nil,
   nil,   nil,   nil,   442,   nil,   nil,   442,   442,   442,   442,
   442,   442,   442,   442,   442,   442,   442,   442,   nil,   442,
   442,   442,   442,   442,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   442,   nil,   nil,   442,   nil,   nil,   442,
   442,   nil,   nil,   442,   nil,   442,   nil,   442,   nil,   442,
   nil,   nil,   442,   nil,   nil,   nil,   nil,   nil,   442,   nil,
   nil,   nil,   nil,   442,   442,   442,   442,   nil,   442,   442,
   442,   442,   nil,   nil,   nil,   nil,   442,   442,   nil,   nil,
   nil,   444,   444,   444,   442,   444,   442,   442,   442,   444,
   444,   nil,   nil,   nil,   444,   nil,   444,   444,   444,   444,
   444,   444,   444,   nil,   nil,   nil,   nil,   nil,   444,   444,
   444,   444,   444,   444,   444,   nil,   nil,   444,   nil,   nil,
   nil,   nil,   nil,   nil,   444,   nil,   nil,   444,   444,   444,
   444,   444,   444,   444,   444,   nil,   444,   444,   444,   nil,
   444,   444,   444,   444,   444,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   444,   nil,   nil,   444,   nil,   nil,
   444,   444,   nil,   nil,   444,   nil,   nil,   nil,   nil,   nil,
   444,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   444,
   nil,   nil,   nil,   nil,   444,   444,   444,   444,   nil,   444,
   444,   444,   444,   nil,   nil,   nil,   nil,   444,   444,   nil,
   nil,   nil,   445,   445,   445,   444,   445,   444,   444,   444,
   445,   445,   nil,   nil,   nil,   445,   nil,   445,   445,   445,
   445,   445,   445,   445,   nil,   nil,   nil,   nil,   nil,   445,
   445,   445,   445,   445,   445,   445,   nil,   nil,   445,   nil,
   nil,   nil,   nil,   nil,   nil,   445,   nil,   nil,   445,   445,
   445,   445,   445,   445,   445,   445,   nil,   445,   445,   445,
   nil,   445,   445,   445,   445,   445,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   445,   nil,   nil,   445,   nil,
   nil,   445,   445,   nil,   nil,   445,   nil,   nil,   nil,   nil,
   nil,   445,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   445,   nil,   nil,   nil,   nil,   445,   445,   445,   445,   nil,
   445,   445,   445,   445,   nil,   nil,   nil,   nil,   445,   445,
   nil,   nil,   nil,   446,   446,   446,   445,   446,   445,   445,
   445,   446,   446,   nil,   nil,   nil,   446,   nil,   446,   446,
   446,   446,   446,   446,   446,   nil,   nil,   nil,   nil,   nil,
   446,   446,   446,   446,   446,   446,   446,   nil,   nil,   446,
   nil,   nil,   nil,   nil,   nil,   nil,   446,   nil,   nil,   446,
   446,   446,   446,   446,   446,   446,   446,   nil,   446,   446,
   446,   nil,   446,   446,   446,   446,   446,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   446,   nil,   nil,   446,
   nil,   nil,   446,   446,   nil,   nil,   446,   nil,   nil,   nil,
   nil,   nil,   446,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   446,   nil,   nil,   nil,   nil,   446,   446,   446,   446,
   nil,   446,   446,   446,   446,   nil,   nil,   nil,   nil,   446,
   446,   nil,   nil,   nil,   486,   486,   486,   446,   486,   446,
   446,   446,   486,   486,   nil,   nil,   nil,   486,   nil,   486,
   486,   486,   486,   486,   486,   486,   nil,   nil,   nil,   nil,
   nil,   486,   486,   486,   486,   486,   486,   486,   nil,   nil,
   486,   nil,   nil,   nil,   nil,   nil,   nil,   486,   nil,   nil,
   486,   486,   486,   486,   486,   486,   486,   486,   486,   486,
   486,   486,   nil,   486,   486,   486,   486,   486,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   486,   nil,   nil,
   486,   nil,   nil,   486,   486,   nil,   nil,   486,   nil,   486,
   nil,   486,   nil,   486,   nil,   nil,   486,   nil,   nil,   nil,
   nil,   nil,   486,   nil,   nil,   nil,   nil,   486,   486,   486,
   486,   nil,   486,   486,   486,   486,   nil,   nil,   nil,   nil,
   486,   486,   nil,   nil,   nil,   488,   488,   488,   486,   488,
   486,   486,   486,   488,   488,   nil,   nil,   nil,   488,   nil,
   488,   488,   488,   488,   488,   488,   488,   nil,   nil,   nil,
   nil,   nil,   488,   488,   488,   488,   488,   488,   488,   nil,
   nil,   488,   nil,   nil,   nil,   nil,   nil,   nil,   488,   nil,
   nil,   488,   488,   488,   488,   488,   488,   488,   488,   488,
   488,   488,   488,   nil,   488,   488,   488,   488,   488,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   488,   nil,
   nil,   488,   nil,   nil,   488,   488,   nil,   nil,   488,   nil,
   nil,   nil,   488,   nil,   488,   nil,   nil,   488,   nil,   nil,
   nil,   nil,   nil,   488,   nil,   nil,   nil,   nil,   488,   488,
   488,   488,   nil,   488,   488,   488,   488,   nil,   nil,   nil,
   nil,   488,   488,   nil,   nil,   nil,   490,   490,   490,   488,
   490,   488,   488,   488,   490,   490,   nil,   nil,   nil,   490,
   nil,   490,   490,   490,   490,   490,   490,   490,   nil,   nil,
   nil,   nil,   nil,   490,   490,   490,   490,   490,   490,   490,
   nil,   nil,   490,   nil,   nil,   nil,   nil,   nil,   nil,   490,
   nil,   nil,   490,   490,   490,   490,   490,   490,   490,   490,
   nil,   490,   490,   490,   nil,   490,   490,   490,   490,   490,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   490,
   nil,   nil,   490,   nil,   nil,   490,   490,   nil,   nil,   490,
   nil,   nil,   nil,   nil,   nil,   490,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   490,   nil,   nil,   nil,   nil,   490,
   490,   490,   490,   nil,   490,   490,   490,   490,   nil,   nil,
   nil,   nil,   490,   490,   nil,   nil,   nil,   nil,   nil,   nil,
   490,   nil,   490,   490,   490,   495,   495,   495,   495,   495,
   nil,   nil,   nil,   495,   495,   nil,   nil,   nil,   495,   nil,
   495,   495,   495,   495,   495,   495,   495,   nil,   nil,   nil,
   nil,   nil,   495,   495,   495,   495,   495,   495,   495,   nil,
   nil,   495,   nil,   nil,   nil,   nil,   nil,   495,   495,   495,
   495,   495,   495,   495,   495,   495,   495,   495,   495,   nil,
   495,   495,   495,   nil,   495,   495,   495,   495,   495,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   495,   nil,
   nil,   495,   nil,   nil,   495,   495,   nil,   nil,   495,   nil,
   495,   nil,   nil,   nil,   495,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   495,   nil,   nil,   nil,   nil,   495,   495,
   495,   495,   nil,   495,   495,   495,   495,   nil,   nil,   nil,
   nil,   495,   495,   nil,   nil,   nil,   nil,   nil,   495,   495,
   nil,   495,   495,   495,   503,   503,   503,   nil,   503,   nil,
   nil,   nil,   503,   503,   nil,   nil,   nil,   503,   nil,   503,
   503,   503,   503,   503,   503,   503,   nil,   nil,   nil,   nil,
   nil,   503,   503,   503,   503,   503,   503,   503,   nil,   nil,
   503,   nil,   nil,   nil,   nil,   nil,   nil,   503,   nil,   nil,
   503,   503,   503,   503,   503,   503,   503,   503,   nil,   503,
   503,   503,   nil,   503,   503,   nil,   nil,   503,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   503,   nil,   nil,
   503,   nil,   nil,   503,   503,   nil,   nil,   503,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   503,   503,   503,
   503,   nil,   503,   503,   503,   503,   nil,   nil,   nil,   nil,
   503,   503,   nil,   nil,   nil,   505,   505,   505,   503,   505,
   503,   503,   503,   505,   505,   nil,   nil,   nil,   505,   nil,
   505,   505,   505,   505,   505,   505,   505,   nil,   nil,   nil,
   nil,   nil,   505,   505,   505,   505,   505,   505,   505,   nil,
   nil,   505,   nil,   nil,   nil,   nil,   nil,   nil,   505,   nil,
   nil,   505,   505,   505,   505,   505,   505,   505,   505,   505,
   505,   505,   505,   nil,   505,   505,   505,   505,   505,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   505,   nil,
   nil,   505,   nil,   nil,   505,   505,   nil,   nil,   505,   nil,
   505,   nil,   505,   nil,   505,   nil,   nil,   505,   nil,   nil,
   nil,   nil,   nil,   505,   nil,   nil,   nil,   nil,   505,   505,
   505,   505,   nil,   505,   505,   505,   505,   nil,   nil,   nil,
   nil,   505,   505,   nil,   nil,   nil,   511,   511,   511,   505,
   511,   505,   505,   505,   511,   511,   nil,   nil,   nil,   511,
   nil,   511,   511,   511,   511,   511,   511,   511,   nil,   nil,
   nil,   nil,   nil,   511,   511,   511,   511,   511,   511,   511,
   nil,   nil,   511,   nil,   nil,   nil,   nil,   nil,   nil,   511,
   nil,   nil,   511,   511,   511,   511,   511,   511,   511,   511,
   nil,   511,   511,   511,   nil,   511,   511,   nil,   nil,   511,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   511,
   nil,   nil,   511,   nil,   nil,   511,   511,   nil,   nil,   511,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   511,
   511,   511,   511,   nil,   511,   511,   511,   511,   nil,   nil,
   nil,   nil,   511,   511,   nil,   nil,   nil,   514,   514,   514,
   511,   514,   511,   511,   511,   514,   514,   nil,   nil,   nil,
   514,   nil,   514,   514,   514,   514,   514,   514,   514,   nil,
   nil,   nil,   nil,   nil,   514,   514,   514,   514,   514,   514,
   514,   nil,   nil,   514,   nil,   nil,   nil,   nil,   nil,   nil,
   514,   nil,   nil,   514,   514,   514,   514,   514,   514,   514,
   514,   nil,   514,   514,   514,   nil,   514,   514,   514,   514,
   514,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   514,   nil,   nil,   514,   nil,   nil,   514,   514,   nil,   nil,
   514,   nil,   nil,   nil,   nil,   nil,   514,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   514,   nil,   nil,   nil,   nil,
   514,   514,   514,   514,   nil,   514,   514,   514,   514,   nil,
   nil,   nil,   nil,   514,   514,   nil,   nil,   nil,   515,   515,
   515,   514,   515,   514,   514,   514,   515,   515,   nil,   nil,
   nil,   515,   nil,   515,   515,   515,   515,   515,   515,   515,
   nil,   nil,   nil,   nil,   nil,   515,   515,   515,   515,   515,
   515,   515,   nil,   nil,   515,   nil,   nil,   nil,   nil,   nil,
   nil,   515,   nil,   nil,   515,   515,   515,   515,   515,   515,
   515,   515,   nil,   515,   515,   515,   nil,   515,   515,   515,
   515,   515,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   515,   nil,   nil,   515,   nil,   nil,   515,   515,   nil,
   nil,   515,   nil,   nil,   nil,   nil,   nil,   515,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   515,   nil,   nil,   nil,
   nil,   515,   515,   515,   515,   nil,   515,   515,   515,   515,
   nil,   nil,   nil,   nil,   515,   515,   nil,   nil,   nil,   519,
   519,   519,   515,   519,   515,   515,   515,   519,   519,   nil,
   nil,   nil,   519,   nil,   519,   519,   519,   519,   519,   519,
   519,   nil,   nil,   nil,   nil,   nil,   519,   519,   519,   519,
   519,   519,   519,   nil,   nil,   519,   nil,   nil,   nil,   nil,
   nil,   nil,   519,   nil,   nil,   519,   519,   519,   519,   519,
   519,   519,   519,   nil,   519,   519,   519,   nil,   519,   519,
   519,   519,   519,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   519,   nil,   nil,   519,   nil,   nil,   519,   519,
   nil,   nil,   519,   nil,   nil,   nil,   nil,   nil,   519,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   519,   nil,   nil,
   nil,   nil,   519,   519,   519,   519,   nil,   519,   519,   519,
   519,   nil,   nil,   nil,   nil,   519,   519,   nil,   nil,   nil,
   525,   525,   525,   519,   525,   519,   519,   519,   525,   525,
   nil,   nil,   nil,   525,   nil,   525,   525,   525,   525,   525,
   525,   525,   nil,   nil,   nil,   nil,   nil,   525,   525,   525,
   525,   525,   525,   525,   nil,   nil,   525,   nil,   nil,   nil,
   nil,   nil,   nil,   525,   nil,   nil,   525,   525,   525,   525,
   525,   525,   525,   525,   525,   525,   525,   525,   nil,   525,
   525,   525,   525,   525,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   525,   nil,   nil,   525,   nil,   nil,   525,
   525,   nil,   nil,   525,   nil,   525,   nil,   nil,   nil,   525,
   nil,   nil,   525,   nil,   nil,   nil,   nil,   nil,   525,   nil,
   nil,   nil,   nil,   525,   525,   525,   525,   nil,   525,   525,
   525,   525,   nil,   nil,   nil,   nil,   525,   525,   nil,   nil,
   nil,   528,   528,   528,   525,   528,   525,   525,   525,   528,
   528,   nil,   nil,   nil,   528,   nil,   528,   528,   528,   528,
   528,   528,   528,   nil,   nil,   nil,   nil,   nil,   528,   528,
   528,   528,   528,   528,   528,   nil,   nil,   528,   nil,   nil,
   nil,   nil,   nil,   nil,   528,   nil,   nil,   528,   528,   528,
   528,   528,   528,   528,   528,   528,   528,   528,   528,   nil,
   528,   528,   528,   528,   528,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   528,   nil,   nil,   528,   nil,   nil,
   528,   528,   nil,   nil,   528,   nil,   nil,   nil,   nil,   nil,
   528,   nil,   nil,   528,   nil,   nil,   nil,   nil,   nil,   528,
   nil,   nil,   nil,   nil,   528,   528,   528,   528,   nil,   528,
   528,   528,   528,   nil,   nil,   nil,   nil,   528,   528,   nil,
   nil,   nil,   542,   542,   542,   528,   542,   528,   528,   528,
   542,   542,   nil,   nil,   nil,   542,   nil,   542,   542,   542,
   542,   542,   542,   542,   nil,   nil,   nil,   nil,   nil,   542,
   542,   542,   542,   542,   542,   542,   nil,   nil,   542,   nil,
   nil,   nil,   nil,   nil,   nil,   542,   nil,   nil,   542,   542,
   542,   542,   542,   542,   542,   542,   nil,   542,   542,   542,
   nil,   542,   542,   542,   542,   542,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   542,   nil,   nil,   542,   nil,
   nil,   542,   542,   nil,   nil,   542,   nil,   542,   nil,   nil,
   nil,   542,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   542,   nil,   nil,   nil,   nil,   542,   542,   542,   542,   nil,
   542,   542,   542,   542,   nil,   nil,   nil,   nil,   542,   542,
   nil,   nil,   nil,   543,   543,   543,   542,   543,   542,   542,
   542,   543,   543,   nil,   nil,   nil,   543,   nil,   543,   543,
   543,   543,   543,   543,   543,   nil,   nil,   nil,   nil,   nil,
   543,   543,   543,   543,   543,   543,   543,   nil,   nil,   543,
   nil,   nil,   nil,   nil,   nil,   nil,   543,   nil,   nil,   543,
   543,   543,   543,   543,   543,   543,   543,   543,   543,   543,
   543,   nil,   543,   543,   543,   543,   543,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   543,   nil,   nil,   543,
   nil,   nil,   543,   543,   nil,   nil,   543,   nil,   543,   nil,
   543,   nil,   543,   nil,   nil,   543,   nil,   nil,   nil,   nil,
   nil,   543,   nil,   nil,   nil,   nil,   543,   543,   543,   543,
   nil,   543,   543,   543,   543,   nil,   nil,   nil,   nil,   543,
   543,   nil,   nil,   nil,   553,   553,   553,   543,   553,   543,
   543,   543,   553,   553,   nil,   nil,   nil,   553,   nil,   553,
   553,   553,   553,   553,   553,   553,   nil,   nil,   nil,   nil,
   nil,   553,   553,   553,   553,   553,   553,   553,   nil,   nil,
   553,   nil,   nil,   nil,   nil,   nil,   nil,   553,   nil,   nil,
   553,   553,   553,   553,   553,   553,   553,   553,   553,   553,
   553,   553,   nil,   553,   553,   553,   553,   553,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   553,   nil,   nil,
   553,   nil,   nil,   553,   553,   nil,   nil,   553,   nil,   553,
   nil,   553,   nil,   553,   nil,   nil,   553,   nil,   nil,   nil,
   nil,   nil,   553,   nil,   nil,   nil,   nil,   553,   553,   553,
   553,   nil,   553,   553,   553,   553,   nil,   nil,   nil,   nil,
   553,   553,   nil,   nil,   nil,   587,   587,   587,   553,   587,
   553,   553,   553,   587,   587,   nil,   nil,   nil,   587,   nil,
   587,   587,   587,   587,   587,   587,   587,   nil,   nil,   nil,
   nil,   nil,   587,   587,   587,   587,   587,   587,   587,   nil,
   nil,   587,   nil,   nil,   nil,   nil,   nil,   nil,   587,   nil,
   nil,   587,   587,   587,   587,   587,   587,   587,   587,   nil,
   587,   587,   587,   nil,   587,   587,   587,   587,   587,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   587,   nil,
   nil,   587,   nil,   nil,   587,   587,   nil,   nil,   587,   nil,
   587,   nil,   nil,   nil,   587,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   587,   nil,   nil,   nil,   nil,   587,   587,
   587,   587,   nil,   587,   587,   587,   587,   nil,   nil,   nil,
   nil,   587,   587,   nil,   nil,   nil,   588,   588,   588,   587,
   588,   587,   587,   587,   588,   588,   nil,   nil,   nil,   588,
   nil,   588,   588,   588,   588,   588,   588,   588,   nil,   nil,
   nil,   nil,   nil,   588,   588,   588,   588,   588,   588,   588,
   nil,   nil,   588,   nil,   nil,   nil,   nil,   nil,   nil,   588,
   nil,   nil,   588,   588,   588,   588,   588,   588,   588,   588,
   nil,   588,   588,   588,   nil,   588,   588,   588,   588,   588,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   588,
   nil,   nil,   588,   nil,   nil,   588,   588,   nil,   nil,   588,
   nil,   nil,   nil,   nil,   nil,   588,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   588,   nil,   nil,   nil,   nil,   588,
   588,   588,   588,   nil,   588,   588,   588,   588,   nil,   nil,
   nil,   nil,   588,   588,   nil,   nil,   nil,   591,   591,   591,
   588,   591,   588,   588,   588,   591,   591,   nil,   nil,   nil,
   591,   nil,   591,   591,   591,   591,   591,   591,   591,   nil,
   nil,   nil,   nil,   nil,   591,   591,   591,   591,   591,   591,
   591,   nil,   nil,   591,   nil,   nil,   nil,   nil,   nil,   nil,
   591,   nil,   nil,   591,   591,   591,   591,   591,   591,   591,
   591,   591,   591,   591,   591,   nil,   591,   591,   591,   591,
   591,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   591,   nil,   nil,   591,   nil,   nil,   591,   591,   nil,   nil,
   591,   nil,   591,   nil,   591,   nil,   591,   nil,   nil,   591,
   nil,   nil,   nil,   nil,   nil,   591,   nil,   nil,   nil,   nil,
   591,   591,   591,   591,   nil,   591,   591,   591,   591,   nil,
   nil,   nil,   nil,   591,   591,   nil,   nil,   nil,   592,   592,
   592,   591,   592,   591,   591,   591,   592,   592,   nil,   nil,
   nil,   592,   nil,   592,   592,   592,   592,   592,   592,   592,
   nil,   nil,   nil,   nil,   nil,   592,   592,   592,   592,   592,
   592,   592,   nil,   nil,   592,   nil,   nil,   nil,   nil,   nil,
   nil,   592,   nil,   nil,   592,   592,   592,   592,   592,   592,
   592,   592,   592,   592,   592,   592,   nil,   592,   592,   592,
   592,   592,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   592,   nil,   nil,   592,   nil,   nil,   592,   592,   nil,
   nil,   592,   nil,   nil,   nil,   592,   nil,   592,   nil,   nil,
   592,   nil,   nil,   nil,   nil,   nil,   592,   nil,   nil,   nil,
   nil,   592,   592,   592,   592,   nil,   592,   592,   592,   592,
   nil,   nil,   nil,   nil,   592,   592,   nil,   nil,   nil,   593,
   593,   593,   592,   593,   592,   592,   592,   593,   593,   nil,
   nil,   nil,   593,   nil,   593,   593,   593,   593,   593,   593,
   593,   nil,   nil,   nil,   nil,   nil,   593,   593,   593,   593,
   593,   593,   593,   nil,   nil,   593,   nil,   nil,   nil,   nil,
   nil,   nil,   593,   nil,   nil,   593,   593,   593,   593,   593,
   593,   593,   593,   nil,   593,   593,   593,   nil,   593,   593,
   593,   593,   593,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   593,   nil,   nil,   593,   nil,   nil,   593,   593,
   nil,   nil,   593,   nil,   nil,   nil,   nil,   nil,   593,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   593,   nil,   nil,
   nil,   nil,   593,   593,   593,   593,   nil,   593,   593,   593,
   593,   nil,   nil,   nil,   nil,   593,   593,   nil,   nil,   nil,
   594,   594,   594,   593,   594,   593,   593,   593,   594,   594,
   nil,   nil,   nil,   594,   nil,   594,   594,   594,   594,   594,
   594,   594,   nil,   nil,   nil,   nil,   nil,   594,   594,   594,
   594,   594,   594,   594,   nil,   nil,   594,   nil,   nil,   nil,
   nil,   nil,   nil,   594,   nil,   nil,   594,   594,   594,   594,
   594,   594,   594,   594,   nil,   594,   594,   594,   nil,   594,
   594,   594,   594,   594,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   594,   nil,   nil,   594,   nil,   nil,   594,
   594,   nil,   nil,   594,   nil,   nil,   nil,   nil,   nil,   594,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   594,   nil,
   nil,   nil,   nil,   594,   594,   594,   594,   nil,   594,   594,
   594,   594,   nil,   nil,   nil,   nil,   594,   594,   nil,   nil,
   nil,   598,   598,   598,   594,   598,   594,   594,   594,   598,
   598,   nil,   nil,   nil,   598,   nil,   598,   598,   598,   598,
   598,   598,   598,   nil,   nil,   nil,   nil,   nil,   598,   598,
   598,   598,   598,   598,   598,   nil,   nil,   598,   nil,   nil,
   nil,   nil,   nil,   nil,   598,   nil,   nil,   598,   598,   598,
   598,   598,   598,   598,   598,   nil,   598,   598,   598,   nil,
   598,   598,   598,   598,   598,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   598,   nil,   nil,   598,   nil,   nil,
   598,   598,   nil,   nil,   598,   nil,   nil,   nil,   nil,   nil,
   598,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   598,
   nil,   nil,   nil,   nil,   598,   598,   598,   598,   nil,   598,
   598,   598,   598,   nil,   nil,   nil,   nil,   598,   598,   nil,
   nil,   nil,   599,   599,   599,   598,   599,   598,   598,   598,
   599,   599,   nil,   nil,   nil,   599,   nil,   599,   599,   599,
   599,   599,   599,   599,   nil,   nil,   nil,   nil,   nil,   599,
   599,   599,   599,   599,   599,   599,   nil,   nil,   599,   nil,
   nil,   nil,   nil,   nil,   nil,   599,   nil,   nil,   599,   599,
   599,   599,   599,   599,   599,   599,   nil,   599,   599,   599,
   nil,   599,   599,   599,   599,   599,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   599,   nil,   nil,   599,   nil,
   nil,   599,   599,   nil,   nil,   599,   nil,   nil,   nil,   nil,
   nil,   599,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   599,   nil,   nil,   nil,   nil,   599,   599,   599,   599,   nil,
   599,   599,   599,   599,   nil,   nil,   nil,   nil,   599,   599,
   nil,   nil,   nil,   602,   602,   602,   599,   602,   599,   599,
   599,   602,   602,   nil,   nil,   nil,   602,   nil,   602,   602,
   602,   602,   602,   602,   602,   nil,   nil,   nil,   nil,   nil,
   602,   602,   602,   602,   602,   602,   602,   nil,   nil,   602,
   nil,   nil,   nil,   nil,   nil,   nil,   602,   nil,   nil,   602,
   602,   602,   602,   602,   602,   602,   602,   nil,   602,   602,
   602,   nil,   602,   602,   602,   602,   602,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   602,   nil,   nil,   602,
   nil,   nil,   602,   602,   nil,   nil,   602,   nil,   nil,   nil,
   nil,   nil,   602,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   602,   nil,   nil,   nil,   nil,   602,   602,   602,   602,
   nil,   602,   602,   602,   602,   nil,   nil,   nil,   nil,   602,
   602,   nil,   nil,   nil,   603,   603,   603,   602,   603,   602,
   602,   602,   603,   603,   nil,   nil,   nil,   603,   nil,   603,
   603,   603,   603,   603,   603,   603,   nil,   nil,   nil,   nil,
   nil,   603,   603,   603,   603,   603,   603,   603,   nil,   nil,
   603,   nil,   nil,   nil,   nil,   nil,   nil,   603,   nil,   nil,
   603,   603,   603,   603,   603,   603,   603,   603,   nil,   603,
   603,   603,   nil,   603,   603,   603,   603,   603,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   603,   nil,   nil,
   603,   nil,   nil,   603,   603,   nil,   nil,   603,   nil,   nil,
   nil,   nil,   nil,   603,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   603,   nil,   nil,   nil,   nil,   603,   603,   603,
   603,   nil,   603,   603,   603,   603,   nil,   nil,   nil,   nil,
   603,   603,   nil,   nil,   nil,   627,   627,   627,   603,   627,
   603,   603,   603,   627,   627,   nil,   nil,   nil,   627,   nil,
   627,   627,   627,   627,   627,   627,   627,   nil,   nil,   nil,
   nil,   nil,   627,   627,   627,   627,   627,   627,   627,   nil,
   nil,   627,   nil,   nil,   nil,   nil,   nil,   nil,   627,   nil,
   nil,   627,   627,   627,   627,   627,   627,   627,   627,   nil,
   627,   627,   627,   nil,   627,   627,   627,   627,   627,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   627,   nil,
   nil,   627,   nil,   nil,   627,   627,   nil,   nil,   627,   nil,
   nil,   nil,   nil,   nil,   627,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   627,   nil,   nil,   nil,   nil,   627,   627,
   627,   627,   nil,   627,   627,   627,   627,   nil,   nil,   nil,
   nil,   627,   627,   nil,   nil,   nil,   633,   633,   633,   627,
   633,   627,   627,   627,   633,   633,   nil,   nil,   nil,   633,
   nil,   633,   633,   633,   633,   633,   633,   633,   nil,   nil,
   nil,   nil,   nil,   633,   633,   633,   633,   633,   633,   633,
   nil,   nil,   633,   nil,   nil,   nil,   nil,   nil,   nil,   633,
   nil,   nil,   633,   633,   633,   633,   633,   633,   633,   633,
   nil,   633,   633,   633,   nil,   633,   633,   nil,   nil,   633,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   633,
   nil,   nil,   633,   nil,   nil,   633,   633,   nil,   nil,   633,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   633,
   633,   633,   633,   nil,   633,   633,   633,   633,   nil,   nil,
   nil,   nil,   633,   633,   nil,   nil,   nil,   644,   644,   644,
   633,   644,   633,   633,   633,   644,   644,   nil,   nil,   nil,
   644,   nil,   644,   644,   644,   644,   644,   644,   644,   nil,
   nil,   nil,   nil,   nil,   644,   644,   644,   644,   644,   644,
   644,   nil,   nil,   644,   nil,   nil,   nil,   nil,   nil,   nil,
   644,   nil,   nil,   644,   644,   644,   644,   644,   644,   644,
   644,   nil,   644,   644,   644,   nil,   644,   644,   nil,   nil,
   644,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   644,   nil,   nil,   644,   nil,   nil,   644,   644,   nil,   nil,
   644,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   644,   644,   644,   644,   nil,   644,   644,   644,   644,   nil,
   nil,   nil,   nil,   644,   644,   nil,   nil,   nil,   649,   649,
   649,   644,   649,   644,   644,   644,   649,   649,   nil,   nil,
   nil,   649,   nil,   649,   649,   649,   649,   649,   649,   649,
   nil,   nil,   nil,   nil,   nil,   649,   649,   649,   649,   649,
   649,   649,   nil,   nil,   649,   nil,   nil,   nil,   nil,   nil,
   nil,   649,   nil,   nil,   649,   649,   649,   649,   649,   649,
   649,   649,   nil,   649,   649,   649,   nil,   649,   649,   649,
   649,   649,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   649,   nil,   nil,   649,   nil,   nil,   649,   649,   nil,
   nil,   649,   nil,   649,   nil,   nil,   nil,   649,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   649,   nil,   nil,   nil,
   nil,   649,   649,   649,   649,   nil,   649,   649,   649,   649,
   nil,   nil,   nil,   nil,   649,   649,   nil,   nil,   nil,   674,
   674,   674,   649,   674,   649,   649,   649,   674,   674,   nil,
   nil,   nil,   674,   nil,   674,   674,   674,   674,   674,   674,
   674,   nil,   nil,   nil,   nil,   nil,   674,   674,   674,   674,
   674,   674,   674,   nil,   nil,   674,   nil,   nil,   nil,   nil,
   nil,   nil,   674,   nil,   nil,   674,   674,   674,   674,   674,
   674,   674,   674,   nil,   674,   674,   674,   nil,   674,   674,
   674,   674,   674,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   674,   nil,   nil,   674,   nil,   nil,   674,   674,
   nil,   nil,   674,   nil,   nil,   nil,   nil,   nil,   674,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   674,   nil,   nil,
   nil,   nil,   674,   674,   674,   674,   nil,   674,   674,   674,
   674,   nil,   nil,   nil,   nil,   674,   674,   nil,   nil,   nil,
   701,   701,   701,   674,   701,   674,   674,   674,   701,   701,
   nil,   nil,   nil,   701,   nil,   701,   701,   701,   701,   701,
   701,   701,   nil,   nil,   nil,   nil,   nil,   701,   701,   701,
   701,   701,   701,   701,   nil,   nil,   701,   nil,   nil,   nil,
   nil,   nil,   nil,   701,   nil,   nil,   701,   701,   701,   701,
   701,   701,   701,   701,   nil,   701,   701,   701,   nil,   701,
   701,   701,   701,   701,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   701,   nil,   nil,   701,   nil,   nil,   701,
   701,   nil,   nil,   701,   nil,   nil,   nil,   nil,   nil,   701,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   701,   nil,
   nil,   nil,   nil,   701,   701,   701,   701,   nil,   701,   701,
   701,   701,   nil,   nil,   nil,   nil,   701,   701,   nil,   nil,
   nil,   707,   707,   707,   701,   707,   701,   701,   701,   707,
   707,   nil,   nil,   nil,   707,   nil,   707,   707,   707,   707,
   707,   707,   707,   nil,   nil,   nil,   nil,   nil,   707,   707,
   707,   707,   707,   707,   707,   nil,   nil,   707,   nil,   nil,
   nil,   nil,   nil,   nil,   707,   nil,   nil,   707,   707,   707,
   707,   707,   707,   707,   707,   nil,   707,   707,   707,   nil,
   707,   707,   707,   707,   707,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   707,   nil,   nil,   707,   nil,   nil,
   707,   707,   nil,   nil,   707,   nil,   nil,   nil,   nil,   nil,
   707,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   707,
   nil,   nil,   nil,   nil,   707,   707,   707,   707,   nil,   707,
   707,   707,   707,   nil,   nil,   nil,   nil,   707,   707,   nil,
   nil,   nil,   730,   730,   730,   707,   730,   707,   707,   707,
   730,   730,   nil,   nil,   nil,   730,   nil,   730,   730,   730,
   730,   730,   730,   730,   nil,   nil,   nil,   nil,   nil,   730,
   730,   730,   730,   730,   730,   730,   nil,   nil,   730,   nil,
   nil,   nil,   nil,   nil,   nil,   730,   nil,   nil,   730,   730,
   730,   730,   730,   730,   730,   730,   nil,   730,   730,   730,
   nil,   730,   730,   730,   730,   730,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   730,   nil,   nil,   730,   nil,
   nil,   730,   730,   nil,   nil,   730,   nil,   nil,   nil,   nil,
   nil,   730,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   730,   nil,   nil,   nil,   nil,   730,   730,   730,   730,   nil,
   730,   730,   730,   730,   nil,   nil,   nil,   nil,   730,   730,
   nil,   nil,   nil,   732,   732,   732,   730,   732,   730,   730,
   730,   732,   732,   nil,   nil,   nil,   732,   nil,   732,   732,
   732,   732,   732,   732,   732,   nil,   nil,   nil,   nil,   nil,
   732,   732,   732,   732,   732,   732,   732,   nil,   nil,   732,
   nil,   nil,   nil,   nil,   nil,   nil,   732,   nil,   nil,   732,
   732,   732,   732,   732,   732,   732,   732,   nil,   732,   732,
   732,   nil,   732,   732,   732,   732,   732,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   732,   nil,   nil,   732,
   nil,   nil,   732,   732,   nil,   nil,   732,   nil,   nil,   nil,
   nil,   nil,   732,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   732,   nil,   nil,   nil,   nil,   732,   732,   732,   732,
   nil,   732,   732,   732,   732,   nil,   nil,   nil,   nil,   732,
   732,   nil,   nil,   nil,   746,   746,   746,   732,   746,   732,
   732,   732,   746,   746,   nil,   nil,   nil,   746,   nil,   746,
   746,   746,   746,   746,   746,   746,   nil,   nil,   nil,   nil,
   nil,   746,   746,   746,   746,   746,   746,   746,   nil,   nil,
   746,   nil,   nil,   nil,   nil,   nil,   nil,   746,   nil,   nil,
   746,   746,   746,   746,   746,   746,   746,   746,   nil,   746,
   746,   746,   nil,   746,   746,   746,   746,   746,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   746,   nil,   nil,
   746,   nil,   nil,   746,   746,   nil,   nil,   746,   nil,   nil,
   nil,   nil,   nil,   746,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   746,   nil,   nil,   nil,   nil,   746,   746,   746,
   746,   nil,   746,   746,   746,   746,   nil,   nil,   nil,   nil,
   746,   746,   nil,   nil,   nil,   747,   747,   747,   746,   747,
   746,   746,   746,   747,   747,   nil,   nil,   nil,   747,   nil,
   747,   747,   747,   747,   747,   747,   747,   nil,   nil,   nil,
   nil,   nil,   747,   747,   747,   747,   747,   747,   747,   nil,
   nil,   747,   nil,   nil,   nil,   nil,   nil,   nil,   747,   nil,
   nil,   747,   747,   747,   747,   747,   747,   747,   747,   nil,
   747,   747,   747,   nil,   747,   747,   747,   747,   747,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   747,   nil,
   nil,   747,   nil,   nil,   747,   747,   nil,   nil,   747,   nil,
   nil,   nil,   nil,   nil,   747,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   747,   nil,   nil,   nil,   nil,   747,   747,
   747,   747,   nil,   747,   747,   747,   747,   nil,   nil,   nil,
   nil,   747,   747,   nil,   nil,   nil,   748,   748,   748,   747,
   748,   747,   747,   747,   748,   748,   nil,   nil,   nil,   748,
   nil,   748,   748,   748,   748,   748,   748,   748,   nil,   nil,
   nil,   nil,   nil,   748,   748,   748,   748,   748,   748,   748,
   nil,   nil,   748,   nil,   nil,   nil,   nil,   nil,   nil,   748,
   nil,   nil,   748,   748,   748,   748,   748,   748,   748,   748,
   nil,   748,   748,   748,   nil,   748,   748,   748,   748,   748,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   748,
   nil,   nil,   748,   nil,   nil,   748,   748,   nil,   nil,   748,
   nil,   nil,   nil,   nil,   nil,   748,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   748,   nil,   nil,   nil,   nil,   748,
   748,   748,   748,   nil,   748,   748,   748,   748,   nil,   nil,
   nil,   nil,   748,   748,   nil,   nil,   nil,   749,   749,   749,
   748,   749,   748,   748,   748,   749,   749,   nil,   nil,   nil,
   749,   nil,   749,   749,   749,   749,   749,   749,   749,   nil,
   nil,   nil,   nil,   nil,   749,   749,   749,   749,   749,   749,
   749,   nil,   nil,   749,   nil,   nil,   nil,   nil,   nil,   nil,
   749,   nil,   nil,   749,   749,   749,   749,   749,   749,   749,
   749,   nil,   749,   749,   749,   nil,   749,   749,   749,   749,
   749,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   749,   nil,   nil,   749,   nil,   nil,   749,   749,   nil,   nil,
   749,   nil,   nil,   nil,   nil,   nil,   749,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   749,   nil,   nil,   nil,   nil,
   749,   749,   749,   749,   nil,   749,   749,   749,   749,   nil,
   nil,   nil,   nil,   749,   749,   nil,   nil,   nil,   751,   751,
   751,   749,   751,   749,   749,   749,   751,   751,   nil,   nil,
   nil,   751,   nil,   751,   751,   751,   751,   751,   751,   751,
   nil,   nil,   nil,   nil,   nil,   751,   751,   751,   751,   751,
   751,   751,   nil,   nil,   751,   nil,   nil,   nil,   nil,   nil,
   nil,   751,   nil,   nil,   751,   751,   751,   751,   751,   751,
   751,   751,   nil,   751,   751,   751,   nil,   751,   751,   751,
   751,   751,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   751,   nil,   nil,   751,   nil,   nil,   751,   751,   nil,
   nil,   751,   nil,   nil,   nil,   nil,   nil,   751,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   751,   nil,   nil,   nil,
   nil,   751,   751,   751,   751,   nil,   751,   751,   751,   751,
   nil,   nil,   nil,   nil,   751,   751,   nil,   nil,   nil,   763,
   763,   763,   751,   763,   751,   751,   751,   763,   763,   nil,
   nil,   nil,   763,   nil,   763,   763,   763,   763,   763,   763,
   763,   nil,   nil,   nil,   nil,   nil,   763,   763,   763,   763,
   763,   763,   763,   nil,   nil,   763,   nil,   nil,   nil,   nil,
   nil,   nil,   763,   nil,   nil,   763,   763,   763,   763,   763,
   763,   763,   763,   nil,   763,   763,   763,   nil,   763,   763,
   nil,   nil,   763,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   763,   nil,   nil,   763,   nil,   nil,   763,   763,
   nil,   nil,   763,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   763,   763,   763,   763,   nil,   763,   763,   763,
   763,   nil,   nil,   nil,   nil,   763,   763,   nil,   nil,   nil,
   800,   800,   800,   763,   800,   763,   763,   763,   800,   800,
   nil,   nil,   nil,   800,   nil,   800,   800,   800,   800,   800,
   800,   800,   nil,   nil,   nil,   nil,   nil,   800,   800,   800,
   800,   800,   800,   800,   nil,   nil,   800,   nil,   nil,   nil,
   nil,   nil,   nil,   800,   nil,   nil,   800,   800,   800,   800,
   800,   800,   800,   800,   nil,   800,   800,   800,   nil,   800,
   800,   800,   800,   800,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   800,   nil,   nil,   800,   nil,   nil,   800,
   800,   nil,   nil,   800,   nil,   nil,   nil,   nil,   nil,   800,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   800,   nil,
   nil,   nil,   nil,   800,   800,   800,   800,   nil,   800,   800,
   800,   800,   nil,   nil,   nil,   nil,   800,   800,   nil,   nil,
   nil,   813,   813,   813,   800,   813,   800,   800,   800,   813,
   813,   nil,   nil,   nil,   813,   nil,   813,   813,   813,   813,
   813,   813,   813,   nil,   nil,   nil,   nil,   nil,   813,   813,
   813,   813,   813,   813,   813,   nil,   nil,   813,   nil,   nil,
   nil,   nil,   nil,   nil,   813,   nil,   nil,   813,   813,   813,
   813,   813,   813,   813,   813,   nil,   813,   813,   813,   nil,
   813,   813,   813,   813,   813,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   813,   nil,   nil,   813,   nil,   nil,
   813,   813,   nil,   nil,   813,   nil,   nil,   nil,   nil,   nil,
   813,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   813,
   nil,   nil,   nil,   nil,   813,   813,   813,   813,   nil,   813,
   813,   813,   813,   nil,   nil,   nil,   nil,   813,   813,   nil,
   nil,   nil,   818,   818,   818,   813,   818,   813,   813,   813,
   818,   818,   nil,   nil,   nil,   818,   nil,   818,   818,   818,
   818,   818,   818,   818,   nil,   nil,   nil,   nil,   nil,   818,
   818,   818,   818,   818,   818,   818,   nil,   nil,   818,   nil,
   nil,   nil,   nil,   nil,   nil,   818,   nil,   nil,   818,   818,
   818,   818,   818,   818,   818,   818,   nil,   818,   818,   818,
   nil,   818,   818,   818,   818,   818,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   818,   nil,   nil,   818,   nil,
   nil,   818,   818,   nil,   nil,   818,   nil,   818,   nil,   nil,
   nil,   818,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   818,   nil,   nil,   nil,   nil,   818,   818,   818,   818,   nil,
   818,   818,   818,   818,   nil,   nil,   nil,   nil,   818,   818,
   nil,   nil,   nil,   835,   835,   835,   818,   835,   818,   818,
   818,   835,   835,   nil,   nil,   nil,   835,   nil,   835,   835,
   835,   835,   835,   835,   835,   nil,   nil,   nil,   nil,   nil,
   835,   835,   835,   835,   835,   835,   835,   nil,   nil,   835,
   nil,   nil,   nil,   nil,   nil,   nil,   835,   nil,   nil,   835,
   835,   835,   835,   835,   835,   835,   835,   835,   835,   835,
   835,   nil,   835,   835,   835,   835,   835,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   835,   nil,   nil,   835,
   nil,   nil,   835,   835,   nil,   nil,   835,   nil,   nil,   nil,
   835,   nil,   835,   nil,   nil,   835,   nil,   nil,   nil,   nil,
   nil,   835,   nil,   nil,   nil,   nil,   835,   835,   835,   835,
   nil,   835,   835,   835,   835,   nil,   nil,   nil,   nil,   835,
   835,   nil,   nil,   nil,   836,   836,   836,   835,   836,   835,
   835,   835,   836,   836,   nil,   nil,   nil,   836,   nil,   836,
   836,   836,   836,   836,   836,   836,   nil,   nil,   nil,   nil,
   nil,   836,   836,   836,   836,   836,   836,   836,   nil,   nil,
   836,   nil,   nil,   nil,   nil,   nil,   nil,   836,   nil,   nil,
   836,   836,   836,   836,   836,   836,   836,   836,   nil,   836,
   836,   836,   nil,   836,   836,   836,   836,   836,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   836,   nil,   nil,
   836,   nil,   nil,   836,   836,   nil,   nil,   836,   nil,   nil,
   nil,   nil,   nil,   836,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   836,   nil,   nil,   nil,   nil,   836,   836,   836,
   836,   nil,   836,   836,   836,   836,   nil,   nil,   nil,   nil,
   836,   836,   nil,   nil,   nil,   850,   850,   850,   836,   850,
   836,   836,   836,   850,   850,   nil,   nil,   nil,   850,   nil,
   850,   850,   850,   850,   850,   850,   850,   nil,   nil,   nil,
   nil,   nil,   850,   850,   850,   850,   850,   850,   850,   nil,
   nil,   850,   nil,   nil,   nil,   nil,   nil,   nil,   850,   nil,
   nil,   850,   850,   850,   850,   850,   850,   850,   850,   nil,
   850,   850,   850,   nil,   850,   850,   nil,   nil,   850,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   850,   nil,
   nil,   850,   nil,   nil,   850,   850,   nil,   nil,   850,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   850,   850,
   850,   850,   nil,   850,   850,   850,   850,   nil,   nil,   nil,
   nil,   850,   850,   nil,   nil,   nil,   862,   862,   862,   850,
   862,   850,   850,   850,   862,   862,   nil,   nil,   nil,   862,
   nil,   862,   862,   862,   862,   862,   862,   862,   nil,   nil,
   nil,   nil,   nil,   862,   862,   862,   862,   862,   862,   862,
   nil,   nil,   862,   nil,   nil,   nil,   nil,   nil,   nil,   862,
   nil,   nil,   862,   862,   862,   862,   862,   862,   862,   862,
   nil,   862,   862,   862,   nil,   862,   862,   nil,   nil,   862,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   862,
   nil,   nil,   862,   nil,   nil,   862,   862,   nil,   nil,   862,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   862,
   862,   862,   862,   nil,   862,   862,   862,   862,   nil,   nil,
   nil,   nil,   862,   862,   nil,   nil,   nil,   971,   971,   971,
   862,   971,   862,   862,   862,   971,   971,   nil,   nil,   nil,
   971,   nil,   971,   971,   971,   971,   971,   971,   971,   nil,
   nil,   nil,   nil,   nil,   971,   971,   971,   971,   971,   971,
   971,   nil,   nil,   971,   nil,   nil,   nil,   nil,   nil,   nil,
   971,   nil,   nil,   971,   971,   971,   971,   971,   971,   971,
   971,   971,   971,   971,   971,   nil,   971,   971,   971,   971,
   971,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   971,   nil,   nil,   971,   nil,   nil,   971,   971,   nil,   nil,
   971,   nil,   971,   nil,   971,   nil,   971,   nil,   nil,   971,
   nil,   nil,   nil,   nil,   nil,   971,   nil,   nil,   nil,   nil,
   971,   971,   971,   971,   nil,   971,   971,   971,   971,   nil,
   nil,   nil,   nil,   971,   971,   nil,   nil,   nil,   nil,    56,
   nil,   971,   nil,   971,   971,   971,    56,    56,    56,   nil,
   nil,    56,    56,    56,   nil,    56,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    56,    56,    56,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    56,    56,   nil,    56,    56,
    56,    56,    56,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    56,    56,    56,    56,    56,
    56,    56,    56,    56,    56,    56,    56,    56,    56,   nil,
   nil,    56,    56,    56,   nil,   nil,    56,   nil,   nil,    56,
   nil,   nil,    56,    56,   nil,    56,   nil,    56,   nil,    56,
   nil,    56,    56,   nil,    56,    56,    56,    56,    56,   nil,
    56,   nil,    56,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    56,   nil,   nil,    56,
    56,    56,    56,   424,    56,   nil,    56,   nil,   nil,   nil,
   424,   424,   424,   nil,   nil,   424,   424,   424,   nil,   424,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   424,   424,
   424,   424,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   424,
   424,   nil,   424,   424,   424,   424,   424,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   424,
   424,   424,   424,   424,   424,   424,   424,   424,   424,   424,
   424,   424,   424,   nil,   nil,   424,   424,   424,   nil,   nil,
   424,   nil,   nil,   424,   nil,   nil,   424,   424,   nil,   424,
   nil,   424,   nil,   424,   nil,   424,   424,   nil,   424,   424,
   424,   424,   424,   nil,   424,   424,   424,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   424,   nil,   nil,   424,   424,   424,   424,   425,   424,   nil,
   424,   nil,   nil,   nil,   425,   425,   425,   nil,   nil,   425,
   425,   425,   nil,   425,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   425,   425,   425,   425,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   425,   425,   nil,   425,   425,   425,   425,
   425,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   425,   425,   425,   425,   425,   425,   425,
   425,   425,   425,   425,   425,   425,   425,   nil,   nil,   425,
   425,   425,   nil,   nil,   425,   nil,   nil,   425,   nil,   nil,
   425,   425,   nil,   425,   nil,   425,   nil,   425,   nil,   425,
   425,   nil,   425,   425,   425,   425,   425,   nil,   425,   425,
   425,   937,   nil,   937,   937,   937,   937,   937,   nil,   nil,
   nil,   nil,   nil,   nil,   425,   nil,   937,   425,   425,   425,
   425,    27,   425,   nil,   425,   nil,   nil,   nil,    27,    27,
    27,   nil,   nil,    27,    27,    27,   nil,    27,   937,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    27,    27,    27,   937,
   937,   nil,   nil,   nil,   937,   nil,   nil,    27,    27,   nil,
    27,    27,    27,    27,    27,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    27,    27,    27,
    27,    27,    27,    27,    27,    27,    27,    27,    27,    27,
    27,   nil,   nil,    27,    27,    27,   nil,   nil,    27,   nil,
    27,    27,   nil,   nil,    27,    27,   nil,    27,   nil,    27,
   nil,    27,   nil,    27,    27,   nil,    27,    27,    27,    27,
    27,    28,    27,    27,    27,   nil,   nil,   nil,    28,    28,
    28,   nil,   nil,    28,    28,    28,   nil,    28,    27,   nil,
   nil,    27,    27,   nil,    27,   nil,    27,    28,    28,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    28,    28,   nil,
    28,    28,    28,    28,    28,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    28,    28,    28,
    28,    28,    28,    28,    28,    28,    28,    28,    28,    28,
    28,   nil,   nil,    28,    28,    28,   nil,   nil,    28,   nil,
    28,    28,   nil,   nil,    28,    28,   nil,    28,   nil,    28,
   nil,    28,   nil,    28,    28,   nil,    28,    28,    28,    28,
    28,   nil,    28,   415,    28,   nil,   nil,   nil,   nil,   nil,
   415,   415,   415,   nil,   nil,   415,   415,   415,    28,   415,
   nil,    28,    28,   nil,    28,   nil,    28,   nil,   415,   415,
   415,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   415,
   415,   nil,   415,   415,   415,   415,   415,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   415,
   415,   415,   415,   415,   415,   415,   415,   415,   415,   415,
   415,   415,   415,   nil,   nil,   415,   415,   415,   nil,   nil,
   415,   nil,   415,   415,   nil,   nil,   415,   415,   nil,   415,
   nil,   415,   nil,   415,   nil,   415,   415,   nil,   415,   415,
   415,   415,   415,   nil,   415,   415,   415,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   415,   nil,   474,   415,   415,   nil,   415,   nil,   415,   474,
   474,   474,   nil,   nil,   474,   474,   474,   614,   474,   614,
   614,   614,   614,   614,   nil,   nil,   nil,   474,   474,   nil,
   nil,   nil,   614,   nil,   nil,   nil,   nil,   nil,   474,   474,
   nil,   474,   474,   474,   474,   474,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   614,   nil,   533,   nil,   533,   533,
   533,   533,   533,   614,   614,   614,   614,   nil,   nil,   nil,
   614,   533,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   474,
   nil,   nil,   nil,   nil,   nil,   nil,   474,   nil,   nil,   nil,
   nil,   474,   474,   533,   533,   nil,   614,   nil,   nil,   nil,
   nil,   nil,   533,   533,   533,   533,   nil,   nil,   nil,   533,
   nil,   nil,   nil,   nil,   474,   474,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   474,
   nil,   nil,   474,   nil,   nil,   nil,   nil,   474,     8,     8,
     8,     8,     8,     8,     8,     8,     8,     8,     8,     8,
     8,     8,     8,     8,     8,     8,     8,     8,     8,     8,
     8,     8,   nil,   nil,   nil,     8,     8,     8,     8,     8,
     8,     8,     8,     8,     8,   nil,   nil,   nil,   nil,   nil,
     8,     8,     8,     8,     8,     8,     8,     8,     8,     8,
   nil,     8,   nil,   nil,   nil,   nil,   nil,   nil,   nil,     8,
     8,   nil,     8,     8,     8,     8,     8,     8,     8,   nil,
   nil,     8,     8,   nil,   nil,   nil,     8,     8,     8,     8,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,     8,     8,   nil,     8,     8,     8,     8,
     8,     8,     8,     8,     8,     8,     8,     8,   nil,   nil,
     8,     8,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,     8,     9,     9,     9,     9,
     9,     9,     9,     9,     9,     9,     9,     9,     9,     9,
     9,     9,     9,     9,     9,     9,     9,     9,     9,     9,
   nil,   nil,   nil,     9,     9,     9,     9,     9,     9,     9,
     9,     9,     9,   nil,   nil,   nil,   nil,   nil,     9,     9,
     9,     9,     9,     9,     9,     9,     9,   nil,   nil,     9,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,     9,     9,   nil,
     9,     9,     9,     9,     9,     9,     9,   nil,   nil,     9,
     9,   nil,   nil,   nil,     9,     9,     9,     9,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,     9,     9,   nil,     9,     9,     9,     9,     9,     9,
     9,     9,     9,     9,     9,     9,   nil,   nil,     9,     9,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,     9,   395,   395,   395,   395,   395,   395,
   395,   395,   395,   395,   395,   395,   395,   395,   395,   395,
   395,   395,   395,   395,   395,   395,   395,   395,   nil,   nil,
   nil,   395,   395,   395,   395,   395,   395,   395,   395,   395,
   395,   nil,   nil,   nil,   nil,   nil,   395,   395,   395,   395,
   395,   395,   395,   395,   395,   nil,   nil,   395,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   395,   395,   nil,   395,   395,
   395,   395,   395,   395,   395,   nil,   nil,   395,   395,   nil,
   nil,   nil,   395,   395,   395,   395,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   395,
   395,   nil,   395,   395,   395,   395,   395,   395,   395,   395,
   395,   395,   395,   395,   nil,   nil,   395,   395,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   395,   584,   584,   584,   584,   584,   584,   584,   584,
   584,   584,   584,   584,   584,   584,   584,   584,   584,   584,
   584,   584,   584,   584,   584,   584,   nil,   nil,   nil,   584,
   584,   584,   584,   584,   584,   584,   584,   584,   584,   nil,
   nil,   nil,   nil,   nil,   584,   584,   584,   584,   584,   584,
   584,   584,   584,   nil,   nil,   584,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   584,   584,   nil,   584,   584,   584,   584,
   584,   584,   584,   nil,   nil,   584,   584,   nil,   nil,   nil,
   584,   584,   584,   584,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   584,   584,   nil,
   584,   584,   584,   584,   584,   584,   584,   584,   584,   584,
   584,   584,   nil,   nil,   584,   584,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   584,
    71,    71,    71,    71,    71,    71,    71,    71,    71,    71,
    71,    71,    71,    71,    71,    71,    71,    71,    71,    71,
    71,    71,    71,    71,   nil,   nil,   nil,    71,    71,    71,
    71,    71,    71,    71,    71,    71,    71,   nil,   nil,   nil,
   nil,   nil,    71,    71,    71,    71,    71,    71,    71,    71,
    71,    71,    71,    71,   nil,    71,   nil,   nil,   nil,   nil,
   nil,    71,    71,   nil,    71,    71,    71,    71,    71,    71,
    71,   nil,   nil,    71,    71,   nil,   nil,   nil,    71,    71,
    71,    71,   nil,   nil,   nil,   nil,   nil,    71,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    71,    71,   nil,    71,    71,
    71,    71,    71,    71,    71,    71,    71,    71,    71,    71,
   nil,   nil,    71,   714,   714,   714,   714,   714,   714,   714,
   714,   714,   714,   714,   714,   714,   714,   714,   714,   714,
   714,   714,   714,   714,   714,   714,   714,   nil,   nil,   nil,
   714,   714,   714,   714,   714,   714,   714,   714,   714,   714,
   nil,   nil,   nil,   nil,   nil,   714,   714,   714,   714,   714,
   714,   714,   714,   714,   nil,   nil,   714,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   714,   714,   nil,   714,   714,   714,
   714,   714,   714,   714,   nil,   nil,   714,   714,   nil,   nil,
   nil,   714,   714,   714,   714,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   714,   714,
   nil,   714,   714,   714,   714,   714,   714,   714,   714,   714,
   714,   714,   714,   212,   212,   714,   nil,   212,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   212,   212,   nil,   212,   212,
   212,   212,   212,   212,   212,   nil,   nil,   212,   212,   nil,
   nil,   nil,   212,   212,   212,   212,   nil,   nil,   nil,   nil,
   nil,   212,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   212,
   212,   nil,   212,   212,   212,   212,   212,   212,   212,   212,
   212,   212,   212,   212,   213,   213,   212,   nil,   213,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   213,   213,   nil,   213,
   213,   213,   213,   213,   213,   213,   nil,   nil,   213,   213,
   nil,   nil,   nil,   213,   213,   213,   213,   nil,   nil,   nil,
   nil,   nil,   213,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   213,   213,   nil,   213,   213,   213,   213,   213,   213,   213,
   213,   213,   213,   213,   213,   261,   261,   213,   nil,   261,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   261,   261,   nil,
   261,   261,   261,   261,   261,   261,   261,   nil,   nil,   261,
   261,   nil,   nil,   nil,   261,   261,   261,   261,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   261,   261,   nil,   261,   261,   261,   261,   261,   261,
   261,   261,   261,   261,   261,   261,   440,   440,   261,   nil,
   440,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   440,   440,
   nil,   440,   440,   440,   440,   440,   440,   440,   nil,   nil,
   440,   440,   nil,   nil,   nil,   440,   440,   440,   440,   nil,
   nil,   nil,   nil,   nil,   440,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   440,   440,   nil,   440,   440,   440,   440,   440,
   440,   440,   440,   440,   440,   440,   440,   441,   441,   440,
   nil,   441,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   441,
   441,   nil,   441,   441,   441,   441,   441,   441,   441,   nil,
   nil,   441,   441,   nil,   nil,   nil,   441,   441,   441,   441,
   nil,   nil,   nil,   nil,   nil,   441,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   441,   441,   nil,   441,   441,   441,   441,
   441,   441,   441,   441,   441,   441,   441,   441,   506,   506,
   441,   nil,   506,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   506,   506,   nil,   506,   506,   506,   506,   506,   506,   506,
   nil,   nil,   506,   506,   nil,   nil,   nil,   506,   506,   506,
   506,   nil,   nil,   nil,   nil,   nil,   506,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   506,   506,   nil,   506,   506,   506,
   506,   506,   506,   506,   506,   506,   506,   506,   506,   507,
   507,   506,   nil,   507,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   507,   507,   nil,   507,   507,   507,   507,   507,   507,
   507,   nil,   nil,   507,   507,   nil,   nil,   nil,   507,   507,
   507,   507,   nil,   nil,   nil,   nil,   nil,   507,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   507,   507,   nil,   507,   507,
   507,   507,   507,   507,   507,   507,   507,   507,   507,   507,
   516,   516,   507,   nil,   516,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   516,   516,   nil,   516,   516,   516,   516,   516,
   516,   516,   nil,   nil,   516,   516,   nil,   nil,   nil,   516,
   516,   516,   516,   nil,   nil,   nil,   nil,   nil,   516,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   516,   516,   nil,   516,
   516,   516,   516,   516,   516,   516,   516,   516,   516,   516,
   516,   517,   517,   516,   nil,   517,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   517,   517,   nil,   517,   517,   517,   517,
   517,   517,   517,   nil,   nil,   517,   517,   nil,   nil,   nil,
   517,   517,   517,   517,   nil,   nil,   nil,   nil,   nil,   517,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   517,   517,   nil,
   517,   517,   517,   517,   517,   517,   517,   517,   517,   517,
   517,   517,   544,   544,   517,   nil,   544,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   544,   544,   nil,   544,   544,   544,
   544,   544,   544,   544,   nil,   nil,   544,   544,   nil,   nil,
   nil,   544,   544,   544,   544,   nil,   nil,   nil,   nil,   nil,
   544,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   544,   544,
   nil,   544,   544,   544,   544,   544,   544,   544,   544,   544,
   544,   544,   544,   545,   545,   544,   nil,   545,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   545,   545,   nil,   545,   545,
   545,   545,   545,   545,   545,   nil,   nil,   545,   545,   nil,
   nil,   nil,   545,   545,   545,   545,   nil,   nil,   nil,   nil,
   nil,   545,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   545,
   545,   nil,   545,   545,   545,   545,   545,   545,   545,   545,
   545,   545,   545,   545,   551,   551,   545,   nil,   551,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   551,   551,   nil,   551,
   551,   551,   551,   551,   551,   551,   nil,   nil,   551,   551,
   nil,   nil,   nil,   551,   551,   551,   551,   nil,   nil,   nil,
   nil,   nil,   551,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   551,   551,   nil,   551,   551,   551,   551,   551,   551,   551,
   551,   551,   551,   551,   551,   552,   552,   551,   nil,   552,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   552,   552,   nil,
   552,   552,   552,   552,   552,   552,   552,   nil,   nil,   552,
   552,   nil,   nil,   nil,   552,   552,   552,   552,   nil,   nil,
   nil,   nil,   nil,   552,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   552,   552,   nil,   552,   552,   552,   552,   552,   552,
   552,   552,   552,   552,   552,   552,   918,   918,   552,   nil,
   918,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   918,   918,
   nil,   918,   918,   918,   918,   918,   918,   918,   nil,   nil,
   918,   918,   nil,   nil,   nil,   918,   918,   918,   918,   nil,
   nil,   nil,   nil,   nil,   918,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   918,   918,   nil,   918,   918,   918,   918,   918,
   918,   918,   918,   918,   918,   918,   918,   972,   972,   918,
   nil,   972,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   972,
   972,   nil,   972,   972,   972,   972,   972,   972,   972,   nil,
   nil,   972,   972,   nil,   nil,   nil,   972,   972,   972,   972,
   nil,   nil,   nil,   nil,   nil,   972,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   972,   972,   nil,   972,   972,   972,   972,
   972,   972,   972,   972,   972,   972,   972,   972,   973,   973,
   972,   nil,   973,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   973,   973,   nil,   973,   973,   973,   973,   973,   973,   973,
   nil,   nil,   973,   973,   nil,   nil,   nil,   973,   973,   973,
   973,   nil,   nil,   nil,   nil,   nil,   973,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   973,   973,   nil,   973,   973,   973,
   973,   973,   973,   973,   973,   973,   973,   973,   973,   nil,
   655,   973,   655,   655,   655,   655,   655,   nil,   712,   nil,
   712,   712,   712,   712,   712,   655,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   712,   nil,   713,   nil,   713,   713,   713,
   713,   713,   nil,   nil,   nil,   nil,   nil,   655,   nil,   nil,
   713,   nil,   nil,   nil,   nil,   712,   655,   655,   655,   655,
   nil,   nil,   nil,   655,   712,   712,   712,   712,   nil,   nil,
   nil,   712,   713,   nil,   794,   nil,   794,   794,   794,   794,
   794,   713,   713,   713,   713,   nil,   nil,   nil,   713,   794,
   nil,   796,   nil,   796,   796,   796,   796,   796,   nil,   841,
   nil,   841,   841,   841,   841,   841,   796,   nil,   nil,   nil,
   nil,   794,   nil,   nil,   841,   nil,   nil,   nil,   nil,   nil,
   794,   794,   794,   794,   nil,   nil,   nil,   794,   796,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   841,   796,   796,   796,
   796,   nil,   nil,   nil,   796,   841,   841,   841,   841,   nil,
   nil,   843,   841,   843,   843,   843,   843,   843,   nil,   933,
   nil,   933,   933,   933,   933,   933,   843,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   933,   nil,   939,   nil,   939,   939,
   939,   939,   939,   nil,   nil,   nil,   nil,   nil,   843,   nil,
   nil,   939,   nil,   nil,   nil,   nil,   933,   843,   843,   843,
   843,   nil,   nil,   nil,   843,   933,   933,   933,   933,   nil,
   nil,   nil,   933,   939,   nil,   957,   nil,   957,   957,   957,
   957,   957,   nil,   nil,   939,   939,   nil,   nil,   nil,   939,
   957,   nil,   959,   nil,   959,   959,   959,   959,   959,   961,
   nil,   961,   961,   961,   961,   961,   nil,   959,   nil,   nil,
   nil,   nil,   957,   nil,   961,   nil,   nil,   nil,   nil,   nil,
   nil,   957,   957,   957,   957,   nil,   nil,   nil,   957,   959,
   nil,   nil,   nil,   nil,   nil,   nil,   961,   nil,   nil,   nil,
   959,   959,   nil,   nil,   nil,   959,   nil,   961,   961,   nil,
   nil,   963,   961,   963,   963,   963,   963,   963,  1001,   nil,
  1001,  1001,  1001,  1001,  1001,  1011,   963,  1011,  1011,  1011,
  1011,  1011,   nil,  1001,   nil,   nil,   nil,   nil,   nil,   nil,
  1011,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   963,   nil,
   nil,   nil,   nil,   nil,   nil,  1001,   nil,   nil,   nil,   963,
   963,   nil,  1011,   nil,   963,   nil,  1001,  1001,   nil,   nil,
   nil,  1001,   nil,  1011,  1011,   nil,   nil,   nil,  1011 ]

racc_action_pointer = [
   747,    20,   nil,   -73,   nil,  5086,  1447,   -81, 22816, 22944,
   -68,   nil,   -81,    -6,   338,    59,    66,   359,   nil,   -79,
   606,  1167,   197,   nil,   173,   nil,    -8, 22331, 22441,  5217,
  5348,  5479,   nil,   887,  5610,  5741,   nil,   101,   199,   228,
   205,   142,  5880,  6011,  6142,   150,   424,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil, 21929,   nil,   -72,  6273,
  6404,   -20,   nil,  6535,  6666,   nil,   nil,  6797,  6936,  7067,
  7198, 23328,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   377,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,     0,   nil,   nil,
   112,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   319,   nil,  7337,   nil,   nil,   nil,   nil,  7476,  7607,
  7738,  7869,  8008,  1027,   nil,   730,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   190,   nil,  1167,  8139,
  8270,  8401, 23502, 23563,  8532,  8663,  8794,  8925,  9056,  9187,
   nil,   nil,   437,   -40,   -23,   290,   107,   213,   274,   nil,
  9318,  1307,   294,  9449,  9580,  9711,  9842,  9973, 10104, 10235,
 10366, 10497, 10628, 10759, 10890, 11021, 11152, 11283, 11414, 11545,
 11676, 11807, 11938, 12069, 12200, 12331, 12462, 12593, 12724,   nil,
   nil, 23624,   nil,   nil,   301, 12855, 12986,   nil,   nil,   nil,
   nil,   nil,   nil,   nil, 13117,   nil,  1307,   nil,   286,   290,
   nil, 13248,   342, 13379,   nil, 13510, 13641,   nil,   nil,   148,
   nil, 13780,  1572,   332,   315,  1447,   328,   383,   359, 13911,
  1587,   481,   750,   789,   445,   890,   nil,   413,   386,    10,
   nil,   nil,   nil,   461,   225,   428, 14050,   nil,   252,   501,
   929,   nil,   505,   nil, 14181,  1727, 14312,   454,   nil,  -101,
   401,   494,   477,   402,   511,   nil,   nil,   594,    -1,    33,
 14443, 14574,   221,   636,   527,   -18,     9,   959,   611,    10,
   671,   nil,   nil,   337,   373,   158,   nil,  1027,   nil,    37,
 14705,   nil,   nil,   nil,   295,   371,   397,   449,   485,   509,
   512,   626,   641,   nil,   677,   nil, 14836,   nil,   242,   327,
   365,   394,   399,   -41,   -35,   403,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   592, 23072,   nil,   nil,   nil,   nil,
   607,   nil,   nil,   599, 14967,   607,   nil,   nil,   887,   633,
   nil,   638,   642,   258,   347, 22553,   nil,   nil,   nil,   222,
   334,   695,   nil,   nil, 22063, 22197,   nil,  1447,   nil,   662,
   nil,   nil,   747,   nil,   nil,   nil,   nil,   -35,   nil,   717,
 23685, 23746, 15098,   130, 15229, 15360, 15491,  3267,  3407,  3138,
  3435,   762,   767,   771,   775,  5217,  5348,  5479,  3547,  3687,
  3827,  3967,  4107,  4247,  4387,  4527,  4667,  4807,   489,  3295,
  4947,  5086,  1587,   -59, 22682,   nil,   nil,   nil,   nil,   716,
   nil,   -53,   -20,   730,   nil,   nil, 15622,   nil, 15753,   nil,
 15884,   nil,   nil,   nil,   nil, 16023,  1587,  1867,   741,   739,
   nil,   nil,   741, 16162,   747, 16293, 23807, 23868,  1030,   790,
   nil, 16424,   748,   nil, 16555, 16686, 23929, 23990,  1727, 16817,
   877,   887,   650,   810,   nil, 16948,   nil,   nil, 17079,   nil,
   nil,   nil,   nil, 22685,  2007,   889,   nil,  2147,    27,    29,
   887,   902, 17210, 17341, 24051, 24112,    25,   nil,   nil,  1069,
   nil, 24173, 24234, 17472,   nil,   nil,   nil,   450,   140,  2287,
   824,   nil,   -33,   nil,   nil,   nil,   870,   nil,   nil,   nil,
   795,   nil,   nil,   192,   nil,   212,   nil,   nil,   781,   nil,
   792,   nil,   nil,   nil, 23200,   nil,   810, 17603, 17734,   418,
   853, 17865, 17996, 18127, 18258,   872,   nil,   nil, 18389, 18520,
   877,   nil, 18651, 18782,   nil,   nil,   168,   217,   470,   606,
   846,  1027,   845,   nil, 22646,   nil,  2427,   954,     5,   202,
   nil,  2567,  2707,   nil,   853,   nil,   904, 18913,   nil,   nil,
   nil,   887,   180, 19044,   868,   nil,   881,   136,   177,   923,
   410,  1167,   925,   884, 19175,  1867,   953,   -14,  1006, 19306,
   nil,   891,   nil,   520,   nil, 24479,   nil,   891,   901,   nil,
   902,   906,   907,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   900,  3113,   nil,   nil, 19437,   nil,   nil,   nil,  1000,   nil,
   nil,   nil,  1002,   nil,   nil,  1003,   717,   nil,  1041,   nil,
   nil,   nil,   nil,  1054,   nil,    36,   934,    28,    40,   122,
   151, 19568,   747,  1307,   nil,   936,  2847, 19699,   nil,   nil,
  1058,  2987, 24487, 24504, 23441,   nil,   nil,   nil,   nil,   nil,
   nil,  3127,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   935,
 19830,  2007, 19961,   nil,   936,   nil,  2147,   nil,  2287,   nil,
   nil,  2427,   nil,  2567,   nil,  2707, 20092, 20223, 20354, 20485,
   253, 20616,   950,   954,   nil,   975,   980,   983,   nil,  1008,
   991,   991,   990, 20747,   nil,   nil,  1130,   nil,   nil,  3267,
  1027,  1132,   nil,   nil,   nil,  1018,   887,   nil,   nil,  1144,
   nil,  3407,  1021,  1070,   nil,   nil,  1069,   nil,    37,  1030,
   372,   nil,   nil,   674, 24543,   nil, 24560,   nil,  5788,   nil,
 20878,   nil,  3142,   nil,  1028,   327,  1034,   nil,   nil,   nil,
   nil,  1165,   nil, 21009,  1166,  3547,  3687,   nil, 21140,  3827,
    64,   121,   nil,  1170,   469,  3967,   nil,  1171,  1051,   625,
   nil,  1064,  1060,   nil,  2847, 21271, 21402,  2987,   795,   nil,
   nil, 24568,   nil, 24620,   nil,  7245,   nil,   nil,  1085,  1153,
 21533,  1099,  1140,   nil,  1090,   nil,   nil,   nil,  4107,   nil,
   nil,    35, 21664,   nil,   nil,   nil,   nil,   nil,  1119,  1090,
   nil,   nil,   nil,  1094,  1115,   nil,  1120,  1124,   nil,  1128,
   nil,   nil,  1136,  3207,  1138,  3533,   nil,   nil,    98,   nil,
  1270,  1271,   nil,    45,   nil,   nil,   nil,  1272,   nil,   nil,
   nil,  1202,   nil,  1159,   nil,   nil,  1161,  1164,  1166,  1167,
   nil,  1168,   nil,   626,   nil,   nil,   nil,  1170, 24295,   nil,
   nil,   nil,  4247,  1209,  1239,  1310,  1249,  1349,   nil,  4387,
  4527,   nil,   nil, 24628,   nil, 13958,   nil, 22260,   nil, 24645,
   nil,   nil,   nil,   nil,   335,  3627,  1181,  4667,   nil,   nil,
   nil,   nil,   nil,  4807,   nil,  4947,   nil, 24684,   nil, 24701,
   nil, 24708,   nil, 24760,   nil,   nil,   nil,  1293,  1226,  1230,
  1313, 21795, 24356, 24417,  1379,  1208,  1322,  1200,  1201,  1202,
  1210,  1216,  3673,  1230,  3703,   773,  1358,  1379,  1260,  1264,
  1268,  1273,   nil,   nil,  1281,   102,   111,   138,  1447,   nil,
   nil, 24767,   nil,   nil,   nil,   nil,  3711,  1286,   nil,   nil,
   nil, 24774,   nil,   nil,   nil,   nil,   144,  1287,  1288,  1298,
   nil,   nil ]

racc_action_default = [
    -3,  -598,    -1,  -584,    -4,  -598,    -7,  -598,  -598,  -598,
  -598,   -29,  -598,  -598,  -598,  -279,  -598,   -40,   -43,  -586,
  -598,   -48,   -50,   -51,   -52,   -56,  -256,  -256,  -256,  -293,
  -329,  -330,   -68,   -11,   -72,   -80,   -82,  -598,  -491,  -492,
  -598,  -598,  -598,  -598,  -598,  -586,  -237,  -270,  -271,  -272,
  -273,  -274,  -275,  -276,  -277,  -278,  -574,  -281,  -283,  -597,
  -565,  -301,  -303,  -598,  -598,  -307,  -310,  -584,  -598,  -598,
  -598,  -598,  -331,  -332,  -334,  -335,  -432,  -433,  -434,  -435,
  -436,  -457,  -439,  -440,  -459,  -461,  -444,  -449,  -453,  -455,
  -471,  -459,  -473,  -475,  -476,  -477,  -478,  -572,  -480,  -481,
  -573,  -483,  -484,  -485,  -486,  -487,  -488,  -489,  -490,  -495,
  -496,  -598,    -2,  -585,  -593,  -594,  -595,    -6,  -598,  -598,
  -598,  -598,  -598,    -3,   -17,  -598,  -111,  -112,  -113,  -114,
  -115,  -116,  -117,  -118,  -119,  -123,  -124,  -125,  -126,  -127,
  -128,  -129,  -130,  -131,  -132,  -133,  -134,  -135,  -136,  -137,
  -138,  -139,  -140,  -141,  -142,  -143,  -144,  -145,  -146,  -147,
  -148,  -149,  -150,  -151,  -152,  -153,  -154,  -155,  -156,  -157,
  -158,  -159,  -160,  -161,  -162,  -163,  -164,  -165,  -166,  -167,
  -168,  -169,  -170,  -171,  -172,  -173,  -174,  -175,  -176,  -177,
  -178,  -179,  -180,  -181,  -182,  -183,  -184,  -185,  -186,  -187,
  -188,  -189,  -190,  -191,  -192,  -193,   -22,  -120,   -11,  -598,
  -598,  -246,  -598,  -598,  -598,  -598,  -598,  -598,  -598,  -586,
  -587,   -47,  -598,  -491,  -492,  -598,  -279,  -598,  -598,  -229,
  -598,   -11,  -598,  -598,  -598,  -598,  -598,  -598,  -598,  -598,
  -598,  -598,  -598,  -598,  -598,  -598,  -598,  -598,  -598,  -598,
  -598,  -598,  -598,  -598,  -598,  -598,  -598,  -598,  -598,  -401,
  -403,  -598,  -582,  -583,   -57,  -246,  -598,  -300,  -407,  -416,
  -418,   -63,  -413,   -64,  -586,   -65,  -238,  -251,  -260,  -260,
  -255,  -598,  -261,  -598,  -567,  -598,  -598,   -66,   -67,  -584,
   -12,  -598,   -15,  -598,   -70,   -11,  -586,  -598,   -73,   -76,
   -11,   -88,   -89,  -598,  -598,   -96,  -293,  -296,  -586,  -598,
  -329,  -330,  -333,  -414,  -598,   -78,  -598,   -84,  -290,  -474,
  -598,  -214,  -215,  -230,  -598,   -11,  -598,  -586,  -239,  -590,
  -590,  -598,  -598,  -590,  -598,  -302,  -392,   -49,  -598,  -598,
  -598,  -598,  -584,  -598,  -585,  -491,  -492,  -598,  -598,  -279,
  -598,  -345,  -346,  -106,  -107,  -598,  -109,  -598,  -279,  -598,
  -598,  -491,  -492,  -322,  -111,  -112,  -153,  -154,  -155,  -171,
  -176,  -183,  -186,  -324,  -598,  -563,  -598,  -437,  -598,  -598,
  -598,  -598,  -598,  -598,  -598,  -598,  1022,    -5,  -596,   -23,
   -24,   -25,   -26,   -27,  -598,  -598,   -19,   -20,   -21,  -121,
  -598,   -30,   -39,  -266,  -598,  -598,  -265,   -31,  -196,  -586,
  -247,  -260,  -260,  -575,  -576,  -256,  -411,  -577,  -578,  -576,
  -575,  -256,  -410,  -412,  -577,  -578,   -37,  -204,   -38,  -598,
   -41,   -42,  -194,  -261,   -44,   -45,   -46,  -586,  -299,  -598,
  -598,  -598,  -246,  -290,  -598,  -598,  -598,  -205,  -206,  -207,
  -208,  -209,  -210,  -211,  -212,  -216,  -217,  -218,  -219,  -220,
  -221,  -222,  -223,  -224,  -225,  -226,  -227,  -228,  -231,  -232,
  -233,  -234,  -586,  -381,  -256,  -575,  -576,   -54,   -58,  -586,
  -257,  -381,  -381,  -586,  -295,  -252,  -598,  -253,  -598,  -258,
  -598,  -262,  -570,  -571,   -10,  -585,   -14,    -3,  -586,   -69,
  -288,   -85,   -74,  -598,  -586,  -246,  -598,  -598,   -95,  -598,
  -474,  -598,   -81,   -86,  -598,  -598,  -598,  -598,  -235,  -598,
  -424,  -598,  -284,  -598,  -240,  -592,  -591,  -242,  -592,  -291,
  -292,  -566,  -304,  -524,   -11,  -336,  -337,   -11,  -598,  -598,
  -598,  -598,  -598,  -246,  -598,  -598,  -290,  -315,  -106,  -107,
  -108,  -598,  -598,  -246,  -318,  -497,  -498,  -598,  -598,   -11,
  -502,  -326,  -586,  -438,  -458,  -463,  -598,  -465,  -441,  -460,
  -598,  -462,  -443,  -598,  -446,  -598,  -448,  -451,  -598,  -452,
  -598,  -472,    -8,   -18,  -598,   -28,  -269,  -598,  -598,  -415,
  -598,  -248,  -250,  -598,  -598,   -59,  -245,  -408,  -598,  -598,
   -61,  -409,  -598,  -598,  -298,  -588,  -575,  -576,  -575,  -576,
  -586,  -194,  -598,  -382,  -586,  -384,   -11,   -53,  -404,  -381,
  -243,   -11,   -11,  -294,  -260,  -259,  -263,  -598,  -568,  -569,
   -13,  -598,   -71,  -598,   -77,   -83,  -586,  -575,  -576,  -244,
   -92,   -94,  -598,   -79,  -598,  -203,  -213,  -586,  -597,  -597,
  -282,  -586,  -287,  -590,  -393,  -524,  -396,  -562,  -562,  -507,
  -509,  -509,  -509,  -523,  -525,  -526,  -527,  -528,  -529,  -530,
  -531,  -598,  -533,  -535,  -537,  -542,  -544,  -545,  -547,  -552,
  -554,  -555,  -557,  -558,  -559,  -598,  -597,  -338,  -597,  -308,
  -339,  -340,  -311,  -598,  -314,  -598,  -586,  -575,  -576,  -579,
  -289,  -598,  -106,  -107,  -110,  -586,   -11,  -598,  -500,  -320,
  -598,   -11,  -524,  -524,  -598,  -564,  -464,  -467,  -468,  -469,
  -470,   -11,  -442,  -445,  -447,  -450,  -454,  -456,  -122,  -267,
  -598,  -197,  -598,  -589,  -260,   -33,  -199,   -34,  -200,   -60,
   -35,  -202,   -36,  -201,   -62,  -195,  -598,  -598,  -598,  -598,
  -415,  -598,  -562,  -562,  -363,  -365,  -365,  -365,  -380,  -598,
  -586,  -386,  -531,  -539,  -540,  -550,  -598,  -406,  -405,   -11,
  -598,  -598,  -254,  -264,   -16,   -75,   -90,   -87,  -297,  -597,
  -343,   -11,  -425,  -597,  -426,  -427,  -598,  -241,  -598,  -586,
  -598,  -505,  -506,  -598,  -598,  -516,  -598,  -519,  -598,  -521,
  -598,  -347,  -598,  -349,  -351,  -358,  -586,  -536,  -546,  -556,
  -560,  -598,  -341,  -598,  -598,   -11,   -11,  -313,  -598,   -11,
  -415,  -598,  -415,  -598,  -598,   -11,  -323,  -598,  -586,  -598,
  -327,  -598,  -268,   -32,  -198,  -249,  -598,  -236,  -598,  -361,
  -362,  -371,  -373,  -598,  -376,  -598,  -378,  -383,  -598,  -598,
  -598,  -538,  -598,  -402,  -598,  -417,  -419,    -9,   -11,  -431,
  -344,  -598,  -598,  -429,  -285,  -394,  -397,  -399,  -598,  -562,
  -543,  -561,  -508,  -509,  -509,  -534,  -509,  -509,  -553,  -509,
  -531,  -548,  -586,  -598,  -356,  -598,  -532,  -305,  -598,  -306,
  -598,  -598,  -263,  -597,  -316,  -319,  -499,  -598,  -325,  -501,
  -503,  -502,  -466,  -562,  -541,  -364,  -365,  -365,  -365,  -365,
  -551,  -365,  -385,  -586,  -388,  -390,  -391,  -549,  -598,  -290,
   -55,  -430,   -11,   -97,   -98,  -598,  -598,  -105,  -428,   -11,
   -11,  -395,  -504,  -598,  -512,  -598,  -514,  -598,  -517,  -598,
  -520,  -522,  -348,  -350,  -354,  -598,  -359,   -11,  -309,  -312,
  -420,  -421,  -422,   -11,  -321,   -11,  -360,  -598,  -368,  -598,
  -370,  -598,  -374,  -598,  -377,  -379,  -387,  -598,  -289,  -579,
  -424,  -246,  -598,  -598,  -104,  -598,  -598,  -509,  -509,  -509,
  -509,  -352,  -598,  -357,  -598,  -597,  -598,  -598,  -365,  -365,
  -365,  -365,  -389,  -423,  -586,  -575,  -576,  -579,  -103,  -398,
  -400,  -598,  -510,  -513,  -515,  -518,  -598,  -355,  -342,  -317,
  -328,  -598,  -366,  -369,  -372,  -375,  -415,  -509,  -353,  -365,
  -511,  -367 ]

racc_goto_table = [
   218,   541,   328,    14,   260,   409,   221,   487,    14,   373,
   277,   277,   277,   124,   207,     2,   415,   421,   521,   335,
   648,   438,   431,   222,   129,   129,   324,   312,   312,   589,
   261,   112,   222,   222,   222,   478,    14,   303,   303,   811,
   132,   132,   134,   134,   113,   338,   339,   315,   628,   342,
   298,   406,   711,   264,   271,   273,   512,   428,   319,   479,
   759,   312,   312,   312,   756,   474,   222,   222,   689,   692,
   222,   347,   357,   357,   550,   625,   484,   625,   878,   294,
   875,   278,   278,   278,   534,   537,   656,   129,   628,   524,
   527,   296,   806,   531,   779,   343,   910,   117,   499,     1,
   389,   390,   391,   392,   337,   337,   946,   857,   337,   630,
   914,   329,   206,   757,   916,   943,    14,   275,   287,   288,
   352,   222,   222,   222,   222,    14,    14,   875,   616,   359,
   363,   402,   379,   395,   814,   619,   621,   622,   394,   385,
   487,   573,   575,   584,   331,   753,   279,   279,   279,   618,
   375,   332,   628,   532,   663,   350,   554,   374,   325,   337,
   337,   337,   337,   326,   268,   272,   983,   651,   327,   336,
   340,   815,   341,   816,     6,   701,   330,   333,   953,     6,
   625,   625,   706,   825,   791,   792,   559,   560,   714,   901,
   758,   760,   277,   913,   654,   401,   407,   865,   693,   533,
   426,   430,   788,  1007,   929,   387,   848,   930,   789,   473,
   910,    14,   222,   222,   222,   878,   943,   222,   222,   222,
   222,   222,   222,   882,   481,   875,   482,  1018,   992,   425,
   750,   950,   916,   783,    14,   868,   610,   861,   564,   604,
   571,   574,   574,   634,   415,   421,   277,   277,   377,   875,
   378,   643,   710,   380,   381,   277,   776,   434,   435,   436,
   437,   405,   382,   383,   384,   828,   829,   405,   222,   222,
   704,   716,   116,   721,   769,   707,   663,   222,   873,   839,
   840,   620,   870,   904,   nil,   623,   nil,     6,   nil,   509,
   nil,   906,   312,   874,    14,   876,   393,     6,    14,   636,
   632,   nil,   303,    14,   nil,   nil,   635,   nil,   523,   312,
   639,   502,   nil,   nil,   763,   nil,   820,   494,   278,   303,
   639,   869,   538,   539,   nil,   822,   278,   nil,    14,   222,
   495,   510,   513,   663,   663,   nil,   116,   696,  1008,   951,
   907,   294,   908,   222,   222,   nil,   294,   705,   639,   nil,
   416,   894,   772,   498,   480,    26,   639,   nil,   504,   nil,
    26,   nil,   483,   222,   715,   nil,   739,   522,   nil,   903,
   540,   744,   753,   775,   753,    26,   753,   nil,   nil,   222,
   561,   337,   337,   279,    26,    26,    26,   nil,    26,   nil,
   590,   279,   nil,   955,   nil,   628,   932,   nil,   nil,   823,
   583,   558,   nil,   nil,   827,   nil,   nil,    13,   297,   431,
   nil,   129,    13,   787,   nil,   nil,   nil,   562,    26,    26,
   nil,   nil,    26,   277,   625,   nil,   nil,   132,   nil,   134,
   956,   nil,   977,   724,   nil,   724,   nil,   nil,   nil,   nil,
    13,   819,   595,   nil,   nil,   222,   nil,   268,   600,   778,
   nil,   272,   nil,   612,   nil,   nil,   988,   425,   nil,   nil,
   nil,   nil,   772,   nil,   nil,   496,   nil,   nil,    26,   nil,
   993,   nil,   nil,    26,    26,    26,    26,    26,    26,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   277,   nil,   753,   nil,
   753,   784,   753,   nil,   753,   nil,   312,   nil,    14,   nil,
    14,   617,   nil,   nil,   312,   nil,   303,   nil,   222,   nil,
   nil,   nil,   631,   nil,   303,   nil,   nil,   nil,   897,   513,
    13,   nil,   222,   425,   277,   nil,   nil,   513,   nil,    13,
    13,   nil,   nil,   425,   277,   nil,   nil,    14,   763,   nil,
    14,   763,   753,   763,   nil,   763,   222,   nil,   nil,   nil,
   nil,   952,   nil,   596,   nil,   nil,   222,   nil,   116,   601,
   647,   425,    14,    26,    26,    26,    26,   425,   nil,    26,
    26,    26,    26,    26,    26,   nil,   768,   nil,   416,   735,
   737,   nil,   nil,   400,   740,   742,    26,   nil,   430,   728,
   nil,   590,   785,   nil,   695,   761,   222,   222,   nil,   767,
   129,   222,   222,   624,   nil,   222,   297,   922,   886,   nil,
   nil,   116,   596,   nil,  1016,    13,   132,   590,   134,    14,
    26,    26,   nil,   nil,    14,    14,   312,   nil,   555,    26,
   899,   nil,   786,   nil,   947,   nil,   303,   312,    13,   nil,
   nil,   nil,   653,   nil,   416,   nil,    26,   303,   987,   513,
    26,   nil,   830,   nil,   416,    26,   754,   763,   nil,   763,
   777,   763,   nil,   763,   nil,   nil,   nil,   nil,   nil,   nil,
   297,     6,   nil,   nil,   nil,   297,   nil,   590,   nil,   nil,
    26,    26,   416,   821,   942,   nil,   590,   755,   nil,   824,
   416,   nil,   nil,   nil,   nil,    26,    26,   nil,    13,   nil,
   nil,   405,    13,   nil,   222,   nil,   nil,    13,   734,    14,
   222,   763,   nil,   nil,    14,    26,   nil,   nil,   833,   nil,
   nil,   nil,   859,   nil,    14,   nil,   863,   nil,   nil,   nil,
   129,    26,    13,   nil,   nil,   222,   nil,   nil,   nil,   nil,
   nil,   761,   337,   nil,   nil,   nil,   nil,   nil,   337,   nil,
   nil,   nil,   nil,   nil,   nil,    15,   312,   nil,   nil,   nil,
    15,   nil,   nil,   nil,   nil,   994,   851,   nil,   nil,   nil,
   761,   nil,    14,   nil,   nil,   nil,   639,   596,   nil,   nil,
   601,   nil,   nil,   nil,    14,   nil,   nil,   nil,    15,   305,
   305,   nil,   nil,   nil,   nil,   888,   nil,    26,   nil,   nil,
   nil,   nil,   nil,   nil,    16,   nil,   nil,   690,   690,    16,
   nil,   nil,   nil,   nil,   nil,   nil,   222,   nil,    14,    14,
   nil,   nil,    14,   349,   358,   358,   708,   709,    14,   nil,
   nil,   nil,   871,   nil,   nil,   871,   nil,    16,   nil,   nil,
   nil,   nil,   nil,   312,   nil,   nil,   nil,   nil,   nil,   nil,
    26,   nil,    26,   917,   337,   312,   nil,   nil,   nil,   nil,
    26,    14,   nil,   nil,   nil,   925,   nil,   nil,    15,   877,
   nil,   879,   351,   nil,    26,   nil,   nil,    15,    15,   nil,
   871,   nil,   nil,   754,   nil,   754,   nil,   754,   nil,    26,
   nil,   nil,    26,   nil,   966,   nil,   nil,   nil,    26,   nil,
   nil,   nil,    13,   nil,    13,   nil,   nil,   nil,    26,   686,
   nil,   nil,   688,   nil,    26,   nil,   909,    16,   911,   nil,
   nil,   nil,   nil,   nil,   nil,    14,    16,    16,   nil,   nil,
   nil,   nil,    14,    14,   425,   nil,   795,   797,   799,   nil,
   nil,    13,   nil,   nil,    13,   nil,    38,   nil,    26,    26,
    14,    38,   277,    26,    26,   nil,    14,    26,    14,   nil,
   nil,   nil,   nil,    15,   nil,   nil,    13,   nil,   nil,   nil,
   nil,    26,   nil,   nil,   222,   590,    26,    26,   nil,    38,
   301,   301,   nil,   nil,   nil,   nil,    15,   nil,   nil,   425,
   nil,   766,   nil,   nil,   nil,   nil,   770,   771,   nil,   754,
   nil,   754,   nil,   754,   nil,   754,   978,   nil,   979,   nil,
   980,   nil,    16,   nil,   345,   361,   361,   361,   nil,   429,
   nil,   nil,   nil,    13,   nil,   nil,   nil,   nil,    13,    13,
   989,   nil,   990,   nil,   991,    16,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    15,   842,   844,   846,
    15,   nil,   nil,   754,   305,    15,    26,   nil,   nil,    38,
   nil,    26,    26,   nil,   nil,   nil,    26,   nil,    38,    38,
   nil,   305,   nil,   nil,  1017,   nil,    26,   nil,   nil,   nil,
    15,   nil,   nil,   nil,  1019,   nil,   nil,    26,   nil,   nil,
   690,   nil,   nil,   896,   nil,    16,   831,   nil,   900,    16,
   nil,   nil,   nil,   nil,    16,   nil,   nil,   nil,   nil,   nil,
   416,   nil,   nil,    13,   nil,   nil,   nil,   nil,    13,   nil,
   nil,   nil,   nil,   nil,    26,   nil,   nil,   nil,    13,    16,
   nil,   nil,   nil,   nil,   nil,   nil,    26,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   854,   nil,   nil,   nil,   nil,   934,
   936,   nil,   938,   940,    38,   941,   860,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,   334,
    26,    26,   nil,   nil,    26,   nil,    13,    38,   nil,   nil,
    26,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    13,   nil,
   890,   891,   nil,   nil,   893,   nil,   nil,   nil,   958,   960,
   962,   964,   nil,   965,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    26,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    13,    13,   nil,   nil,    13,   nil,   nil,   nil,
   nil,   nil,    13,   921,   nil,   nil,   nil,    38,   nil,   nil,
   nil,    38,   nil,   nil,   nil,   301,    38,   nil,   nil,   nil,
    15,   nil,    15,  1002,  1003,  1004,  1005,   nil,   305,   nil,
   nil,   nil,   301,   nil,   nil,    13,   305,   nil,   nil,   nil,
   nil,    38,   nil,   nil,   nil,   nil,   nil,    26,   nil,   nil,
  1012,  1013,  1014,  1015,    26,    26,   nil,   nil,   nil,    15,
   nil,   nil,    15,  1020,   670,   nil,   nil,   970,   nil,    16,
   nil,    16,    26,   nil,   975,   976,   nil,   nil,    26,   nil,
    26,  1021,   nil,   nil,    15,    39,   nil,   nil,   403,   nil,
    39,   720,   985,   nil,   433,   nil,    26,   nil,   986,    13,
   nil,   nil,   nil,   nil,   nil,   nil,    13,    13,    16,   nil,
   nil,    16,   nil,   nil,   nil,   nil,   nil,   nil,    39,   302,
   302,   nil,   nil,   nil,    13,   nil,   nil,   nil,   nil,   nil,
    13,   nil,    13,    16,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    15,   nil,   nil,   nil,   762,    15,    15,   nil,   nil,
   nil,   nil,   nil,   346,   362,   362,   362,   nil,   305,   nil,
   489,   nil,   491,   nil,   492,   493,   nil,   nil,   nil,   305,
   nil,   nil,   nil,   nil,   nil,   nil,   429,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   670,   nil,   nil,   nil,
    16,   nil,   nil,   nil,   nil,    16,    16,   nil,    39,   nil,
   nil,   nil,   801,   nil,   nil,   nil,   nil,    39,    39,   nil,
   nil,    38,   nil,    38,   nil,   nil,   nil,   nil,   nil,   301,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   301,   nil,   nil,
   nil,    15,   nil,   nil,   nil,   nil,    15,   nil,   nil,   nil,
   nil,   nil,   nil,   670,   670,   nil,    15,   nil,   nil,   nil,
    38,   nil,   nil,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    38,   nil,   nil,   nil,   nil,
    16,   nil,   nil,   586,   nil,    16,   nil,   nil,   358,   nil,
   nil,   nil,   nil,    39,    15,    16,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    15,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    39,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   670,   nil,   670,   nil,   nil,
   nil,   nil,    38,   801,   nil,   nil,   884,    38,    38,   nil,
    15,    15,   nil,    16,    15,   nil,   nil,   nil,   nil,   301,
    15,   nil,   nil,   nil,   nil,    16,   nil,   nil,   nil,   nil,
   301,   nil,   nil,   nil,   nil,   626,   nil,   334,   nil,   629,
   nil,   nil,   762,   nil,   762,   358,    39,   nil,   nil,   nil,
    39,   nil,   nil,    15,   302,    39,   nil,   927,   nil,    16,
    16,   nil,   nil,    16,   nil,   nil,   nil,   nil,   nil,    16,
   nil,   302,   nil,   nil,   626,   nil,   nil,   334,   nil,   nil,
    39,   nil,   nil,   nil,   801,   nil,   801,   nil,   nil,   nil,
   nil,   433,    38,   nil,   nil,   nil,   nil,    38,   nil,   nil,
   nil,   nil,    16,   nil,   nil,   nil,   928,    38,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    15,   nil,   nil,
   nil,   nil,   nil,   nil,    15,    15,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   670,   nil,   729,   nil,   nil,   nil,
   626,   334,    15,   nil,   nil,   981,   801,   nil,    15,   361,
    15,   nil,   nil,   nil,   nil,    38,   nil,   nil,   762,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    16,    38,   nil,   nil,
   nil,   nil,   nil,    16,    16,   nil,   773,   nil,   nil,   nil,
   nil,   nil,   nil,   801,   nil,   801,   nil,   nil,   nil,   nil,
   nil,    16,   nil,   nil,   nil,   nil,   nil,    16,   782,    16,
   nil,    38,    38,   nil,   nil,    38,   229,   801,   nil,   nil,
   nil,    38,   nil,   nil,   nil,   276,   276,   276,   nil,   nil,
   nil,   nil,   nil,   807,   nil,   nil,   nil,   nil,   321,   322,
   323,   nil,   nil,   nil,   nil,   nil,   361,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    38,   276,   276,   nil,   923,   nil,
    39,   nil,    39,   nil,   nil,   nil,   nil,   nil,   302,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   302,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   832,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    39,
   nil,   nil,    39,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    38,   nil,
   nil,   nil,   nil,   nil,    39,    38,    38,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    38,   nil,   nil,   nil,   nil,   nil,    38,
   nil,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   881,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   892,   nil,   nil,
   nil,    39,   nil,   nil,   nil,   nil,    39,    39,   nil,   nil,
   nil,   nil,   nil,   nil,   334,   nil,   nil,   nil,   302,   nil,
   nil,   nil,   nil,   nil,   nil,   276,   408,   276,   nil,   302,
   427,   432,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   229,   nil,   nil,   447,
   448,   449,   450,   451,   452,   453,   454,   455,   456,   457,
   458,   459,   460,   461,   462,   463,   464,   465,   466,   467,
   468,   469,   470,   471,   472,   nil,   nil,   nil,   nil,   nil,
   nil,   276,   276,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   276,    39,   nil,   nil,   nil,   nil,    39,   276,   nil,   276,
   nil,   276,   276,   nil,   nil,   nil,    39,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   518,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   362,   nil,
   nil,   nil,   nil,   nil,    39,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    39,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    39,    39,   nil,   nil,    39,   nil,   nil,   nil,   nil,   nil,
    39,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   276,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   362,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    39,   nil,   nil,   nil,   924,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   276,   nil,
   427,   611,   408,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   276,   nil,   276,   nil,   276,    39,   nil,   nil,
   nil,   nil,   nil,   nil,    39,    39,   nil,   nil,   nil,   nil,
   nil,   276,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   645,   646,    39,   nil,   nil,   nil,   nil,   nil,    39,   nil,
    39,   276,   nil,   nil,   276,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   276,   276,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   276,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   276,   731,   nil,   nil,   276,   276,   736,
   738,   nil,   nil,   nil,   741,   743,   nil,   nil,   611,   745,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   276,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   276,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   276,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   276,   nil,   834,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   736,   738,   743,   741,   nil,   837,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   276,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   276,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   276,   834,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   276 ]

racc_goto_check = [
    31,    92,    67,    22,   141,    23,    20,    73,    22,    56,
    33,    33,    33,    15,    15,     2,    37,    37,     8,    86,
    10,    47,    18,    22,    57,    57,    31,    64,    64,    24,
    36,     4,    22,    22,    22,    39,    22,    22,    22,    90,
    60,    60,    61,    61,     6,    17,    17,    50,   180,    17,
    49,    27,    98,    38,    38,    38,    51,    27,    63,    23,
   132,    64,    64,    64,   129,    37,    22,    22,    91,    91,
    22,    22,    22,    22,    54,    74,    47,    74,   176,    45,
   171,    68,    68,    68,    89,    89,   140,    57,   180,    69,
    69,    46,   119,    69,    11,     4,   177,     5,    47,     1,
    17,    17,    17,    17,    29,    29,   120,    12,    29,    14,
   134,    68,    16,   130,   135,   117,    22,    44,    44,    44,
    19,    22,    22,    22,    22,    22,    22,   171,    40,    55,
    55,    28,   155,    30,    11,    42,    40,    40,     2,   155,
    73,   158,   158,    62,    66,   123,    70,    70,    70,    72,
    84,    85,   180,    88,   166,    93,    95,    99,   100,    29,
    29,    29,    29,   101,    71,    71,   120,   102,   103,   104,
   105,   106,   107,   108,     7,   109,    70,    70,   110,     7,
    74,    74,   111,   112,   124,   124,   113,   114,   115,   116,
   121,   127,    33,   133,   136,    20,    20,   137,    92,   138,
    20,    20,   139,   120,   142,     5,   132,   143,   140,   144,
   177,    22,    22,    22,    22,   176,   117,    22,    22,    22,
    22,    22,    22,   119,   146,   171,   147,   120,   134,    57,
    24,   148,   135,   149,    22,   132,    23,   150,   159,    47,
   159,   159,   159,    51,    37,    37,    33,    33,   153,   171,
   154,    51,     8,   156,   157,    33,    24,    29,    29,    29,
    29,    68,   160,   161,   162,   140,   140,    68,    22,    22,
    54,   163,    96,   164,    40,   165,   166,    22,   170,   124,
   124,    47,   173,   174,   nil,    47,   nil,     7,   nil,    31,
   nil,   129,    64,   130,    22,   130,     7,     7,    22,    23,
    47,   nil,    22,    22,   nil,   nil,    47,   nil,    31,    64,
    37,    50,   nil,   nil,   172,   nil,    24,     4,    68,    22,
    37,   123,    17,    17,   nil,    24,    68,   nil,    22,    22,
     6,    63,    49,   166,   166,   nil,    96,    23,    90,    11,
   130,    45,   130,    22,    22,   nil,    45,    23,    37,   nil,
    71,    91,    73,    46,    44,    41,    37,   nil,    46,   nil,
    41,   nil,    44,    22,    47,   nil,    39,    29,   nil,   123,
     4,    39,   123,    51,   123,    41,   123,   nil,   nil,    22,
    36,    29,    29,    70,    41,    41,    41,   nil,    41,   nil,
    31,    70,   nil,    98,   nil,   180,   124,   nil,   nil,     8,
    15,    29,   nil,   nil,     8,   nil,   nil,    21,     9,    18,
   nil,    57,    21,    69,   nil,   nil,   nil,    29,    41,    41,
   nil,   nil,    41,    33,    74,   nil,   nil,    60,   nil,    61,
   124,   nil,   130,   159,   nil,   159,   nil,   nil,   nil,   nil,
    21,    89,    38,   nil,   nil,    22,   nil,    71,    38,    47,
   nil,    71,   nil,    31,   nil,   nil,   130,    57,   nil,   nil,
   nil,   nil,    73,   nil,   nil,     7,   nil,   nil,    41,   nil,
    10,   nil,   nil,    41,    41,    41,    41,    41,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    33,   nil,   123,   nil,
   123,    27,   123,   nil,   123,   nil,    64,   nil,    22,   nil,
    22,    38,   nil,   nil,    64,   nil,    22,   nil,    22,   nil,
   nil,   nil,     2,   nil,    22,   nil,   nil,   nil,     8,    49,
    21,   nil,    22,    57,    33,   nil,   nil,    49,   nil,    21,
    21,   nil,   nil,    57,    33,   nil,   nil,    22,   172,   nil,
    22,   172,   123,   172,   nil,   172,    22,   nil,   nil,   nil,
   nil,    92,   nil,    71,   nil,   nil,    22,   nil,    96,    71,
    29,    57,    22,    41,    41,    41,    41,    57,   nil,    41,
    41,    41,    41,    41,    41,   nil,    86,   nil,    71,    20,
    20,   nil,   nil,     9,    20,    20,    41,   nil,    20,    15,
   nil,    31,    67,   nil,    68,    31,    22,    22,   nil,   141,
    57,    22,    22,    70,   nil,    22,     9,    89,    47,   nil,
   nil,    96,    71,   nil,    24,    21,    60,    31,    61,    22,
    41,    41,   nil,   nil,    22,    22,    64,   nil,    96,    41,
    47,   nil,    31,   nil,    89,   nil,    22,    64,    21,   nil,
   nil,   nil,    70,   nil,    71,   nil,    41,    22,     8,    49,
    41,   nil,    56,   nil,    71,    41,   125,   172,   nil,   172,
    49,   172,   nil,   172,   nil,   nil,   nil,   nil,   nil,   nil,
     9,     7,   nil,   nil,   nil,     9,   nil,    31,   nil,   nil,
    41,    41,    71,    17,    47,   nil,    31,   128,   nil,    17,
    71,   nil,   nil,   nil,   nil,    41,    41,   nil,    21,   nil,
   nil,    68,    21,   nil,    22,   nil,   nil,    21,    70,    22,
    22,   172,   nil,   nil,    22,    41,   nil,   nil,    20,   nil,
   nil,   nil,    67,   nil,    22,   nil,    67,   nil,   nil,   nil,
    57,    41,    21,   nil,   nil,    22,   nil,   nil,   nil,   nil,
   nil,    31,    29,   nil,   nil,   nil,   nil,   nil,    29,   nil,
   nil,   nil,   nil,   nil,   nil,    25,    64,   nil,   nil,   nil,
    25,   nil,   nil,   nil,   nil,    23,    22,   nil,   nil,   nil,
    31,   nil,    22,   nil,   nil,   nil,    37,    71,   nil,   nil,
    71,   nil,   nil,   nil,    22,   nil,   nil,   nil,    25,    25,
    25,   nil,   nil,   nil,   nil,    17,   nil,    41,   nil,   nil,
   nil,   nil,   nil,   nil,    26,   nil,   nil,    96,    96,    26,
   nil,   nil,   nil,   nil,   nil,   nil,    22,   nil,    22,    22,
   nil,   nil,    22,    25,    25,    25,    96,    96,    22,   nil,
   nil,   nil,   125,   nil,   nil,   125,   nil,    26,   nil,   nil,
   nil,   nil,   nil,    64,   nil,   nil,   nil,   nil,   nil,   nil,
    41,   nil,    41,    22,    29,    64,   nil,   nil,   nil,   nil,
    41,    22,   nil,   nil,   nil,    22,   nil,   nil,    25,   128,
   nil,   128,    26,   nil,    41,   nil,   nil,    25,    25,   nil,
   125,   nil,   nil,   125,   nil,   125,   nil,   125,   nil,    41,
   nil,   nil,    41,   nil,    31,   nil,   nil,   nil,    41,   nil,
   nil,   nil,    21,   nil,    21,   nil,   nil,   nil,    41,     9,
   nil,   nil,     9,   nil,    41,   nil,   128,    26,   128,   nil,
   nil,   nil,   nil,   nil,   nil,    22,    26,    26,   nil,   nil,
   nil,   nil,    22,    22,    57,   nil,   169,   169,   169,   nil,
   nil,    21,   nil,   nil,    21,   nil,    52,   nil,    41,    41,
    22,    52,    33,    41,    41,   nil,    22,    41,    22,   nil,
   nil,   nil,   nil,    25,   nil,   nil,    21,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    22,    31,    41,    41,   nil,    52,
    52,    52,   nil,   nil,   nil,   nil,    25,   nil,   nil,    57,
   nil,     9,   nil,   nil,   nil,   nil,     9,     9,   nil,   125,
   nil,   125,   nil,   125,   nil,   125,   128,   nil,   128,   nil,
   128,   nil,    26,   nil,    52,    52,    52,    52,   nil,    26,
   nil,   nil,   nil,    21,   nil,   nil,   nil,   nil,    21,    21,
   128,   nil,   128,   nil,   128,    26,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    25,   126,   126,   126,
    25,   nil,   nil,   125,    25,    25,    41,   nil,   nil,    52,
   nil,    41,    41,   nil,   nil,   nil,    41,   nil,    52,    52,
   nil,    25,   nil,   nil,   128,   nil,    41,   nil,   nil,   nil,
    25,   nil,   nil,   nil,   128,   nil,   nil,    41,   nil,   nil,
    96,   nil,   nil,    96,   nil,    26,     9,   nil,    96,    26,
   nil,   nil,   nil,   nil,    26,   nil,   nil,   nil,   nil,   nil,
    71,   nil,   nil,    21,   nil,   nil,   nil,   nil,    21,   nil,
   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,    21,    26,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,     9,   nil,   nil,   nil,   nil,   169,
   169,   nil,   169,   169,    52,   169,     9,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    41,    65,
    41,    41,   nil,   nil,    41,   nil,    21,    52,   nil,   nil,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    21,   nil,
     9,     9,   nil,   nil,     9,   nil,   nil,   nil,   126,   126,
   126,   126,   nil,   126,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    21,    21,   nil,   nil,    21,   nil,   nil,   nil,
   nil,   nil,    21,     9,   nil,   nil,   nil,    52,   nil,   nil,
   nil,    52,   nil,   nil,   nil,    52,    52,   nil,   nil,   nil,
    25,   nil,    25,   169,   169,   169,   169,   nil,    25,   nil,
   nil,   nil,    52,   nil,   nil,    21,    25,   nil,   nil,   nil,
   nil,    52,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,
   126,   126,   126,   126,    41,    41,   nil,   nil,   nil,    25,
   nil,   nil,    25,   169,   118,   nil,   nil,     9,   nil,    26,
   nil,    26,    41,   nil,     9,     9,   nil,   nil,    41,   nil,
    41,   126,   nil,   nil,    25,    53,   nil,   nil,    65,   nil,
    53,    25,     9,   nil,    65,   nil,    41,   nil,     9,    21,
   nil,   nil,   nil,   nil,   nil,   nil,    21,    21,    26,   nil,
   nil,    26,   nil,   nil,   nil,   nil,   nil,   nil,    53,    53,
    53,   nil,   nil,   nil,    21,   nil,   nil,   nil,   nil,   nil,
    21,   nil,    21,    26,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    25,   nil,   nil,   nil,   118,    25,    25,   nil,   nil,
   nil,   nil,   nil,    53,    53,    53,    53,   nil,    25,   nil,
    65,   nil,    65,   nil,    65,    65,   nil,   nil,   nil,    25,
   nil,   nil,   nil,   nil,   nil,   nil,    26,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   118,   nil,   nil,   nil,
    26,   nil,   nil,   nil,   nil,    26,    26,   nil,    53,   nil,
   nil,   nil,   118,   nil,   nil,   nil,   nil,    53,    53,   nil,
   nil,    52,   nil,    52,   nil,   nil,   nil,   nil,   nil,    52,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    52,   nil,   nil,
   nil,    25,   nil,   nil,   nil,   nil,    25,   nil,   nil,   nil,
   nil,   nil,   nil,   118,   118,   nil,    25,   nil,   nil,   nil,
    52,   nil,   nil,    52,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    52,   nil,   nil,   nil,   nil,
    26,   nil,   nil,    65,   nil,    26,   nil,   nil,    25,   nil,
   nil,   nil,   nil,    53,    25,    26,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    25,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    53,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   118,   nil,   118,   nil,   nil,
   nil,   nil,    52,   118,   nil,   nil,   118,    52,    52,   nil,
    25,    25,   nil,    26,    25,   nil,   nil,   nil,   nil,    52,
    25,   nil,   nil,   nil,   nil,    26,   nil,   nil,   nil,   nil,
    52,   nil,   nil,   nil,   nil,    65,   nil,    65,   nil,    65,
   nil,   nil,   118,   nil,   118,    25,    53,   nil,   nil,   nil,
    53,   nil,   nil,    25,    53,    53,   nil,    25,   nil,    26,
    26,   nil,   nil,    26,   nil,   nil,   nil,   nil,   nil,    26,
   nil,    53,   nil,   nil,    65,   nil,   nil,    65,   nil,   nil,
    53,   nil,   nil,   nil,   118,   nil,   118,   nil,   nil,   nil,
   nil,    65,    52,   nil,   nil,   nil,   nil,    52,   nil,   nil,
   nil,   nil,    26,   nil,   nil,   nil,    26,    52,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    25,   nil,   nil,
   nil,   nil,   nil,   nil,    25,    25,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   118,   nil,    65,   nil,   nil,   nil,
    65,    65,    25,   nil,   nil,   118,   118,   nil,    25,    52,
    25,   nil,   nil,   nil,   nil,    52,   nil,   nil,   118,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    26,    52,   nil,   nil,
   nil,   nil,   nil,    26,    26,   nil,    65,   nil,   nil,   nil,
   nil,   nil,   nil,   118,   nil,   118,   nil,   nil,   nil,   nil,
   nil,    26,   nil,   nil,   nil,   nil,   nil,    26,    65,    26,
   nil,    52,    52,   nil,   nil,    52,    32,   118,   nil,   nil,
   nil,    52,   nil,   nil,   nil,    32,    32,    32,   nil,   nil,
   nil,   nil,   nil,    65,   nil,   nil,   nil,   nil,    32,    32,
    32,   nil,   nil,   nil,   nil,   nil,    52,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    52,    32,    32,   nil,    52,   nil,
    53,   nil,    53,   nil,   nil,   nil,   nil,   nil,    53,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    53,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    65,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    53,
   nil,   nil,    53,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    52,   nil,
   nil,   nil,   nil,   nil,    53,    52,    52,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    52,   nil,   nil,   nil,   nil,   nil,    52,
   nil,    52,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    65,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    65,   nil,   nil,
   nil,    53,   nil,   nil,   nil,   nil,    53,    53,   nil,   nil,
   nil,   nil,   nil,   nil,    65,   nil,   nil,   nil,    53,   nil,
   nil,   nil,   nil,   nil,   nil,    32,    32,    32,   nil,    53,
    32,    32,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    32,   nil,   nil,    32,
    32,    32,    32,    32,    32,    32,    32,    32,    32,    32,
    32,    32,    32,    32,    32,    32,    32,    32,    32,    32,
    32,    32,    32,    32,    32,   nil,   nil,   nil,   nil,   nil,
   nil,    32,    32,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    32,    53,   nil,   nil,   nil,   nil,    53,    32,   nil,    32,
   nil,    32,    32,   nil,   nil,   nil,    53,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    32,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    53,   nil,
   nil,   nil,   nil,   nil,    53,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    53,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    53,    53,   nil,   nil,    53,   nil,   nil,   nil,   nil,   nil,
    53,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    32,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    53,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    53,   nil,   nil,   nil,    53,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    32,   nil,
    32,    32,    32,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    32,   nil,    32,   nil,    32,    53,   nil,   nil,
   nil,   nil,   nil,   nil,    53,    53,   nil,   nil,   nil,   nil,
   nil,    32,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    32,    32,    53,   nil,   nil,   nil,   nil,   nil,    53,   nil,
    53,    32,   nil,   nil,    32,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    32,    32,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    32,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    32,    32,   nil,   nil,    32,    32,    32,
    32,   nil,   nil,   nil,    32,    32,   nil,   nil,    32,    32,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    32,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    32,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    32,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    32,   nil,    32,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    32,    32,    32,    32,   nil,    32,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    32,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    32,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    32,    32,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    32 ]

racc_goto_pointer = [
   nil,    99,    15,   nil,    28,    92,    41,   174,  -307,   375,
  -500,  -554,  -672,   nil,  -386,     5,   103,   -18,  -193,    52,
   -14,   407,     3,  -206,  -380,   755,   804,  -158,   -78,    41,
     9,   -19,  1746,   -19,   nil,   nil,     6,  -196,    27,  -229,
  -345,   355,  -342,   nil,    88,    46,    58,  -198,   nil,    16,
    12,  -260,   946,  1315,  -281,    60,   -62,    16,   nil,   nil,
    32,    34,  -256,    17,    -7,  1109,    85,   -57,    52,  -240,
   117,   138,  -325,  -272,  -411,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    79,    91,   -42,   nil,  -183,  -254,
  -647,  -470,  -342,    87,   nil,  -203,   269,   nil,  -508,    86,
   101,   105,  -355,   110,   107,   105,  -518,   106,  -519,  -372,
  -716,  -372,  -526,  -177,  -186,  -373,  -641,  -768,   761,  -579,
  -779,  -424,   nil,  -469,  -473,    42,   292,  -423,    73,  -550,
  -501,   nil,  -554,  -656,  -739,  -735,  -339,  -591,  -137,  -452,
  -447,   -18,  -662,  -660,   -50,   nil,   -45,   -44,  -662,  -416,
  -546,   nil,   nil,   169,   169,    48,   168,   168,  -240,  -140,
   175,   175,   175,  -295,  -294,  -281,  -379,   nil,   nil,   276,
  -516,  -714,  -300,  -508,  -555,   nil,  -718,  -747,   nil,   nil,
  -440 ]

racc_goto_default = [
   nil,   nil,   nil,     3,   nil,     4,   344,   292,   nil,   520,
   nil,   812,   nil,   289,   290,   nil,   nil,   nil,    11,    12,
    18,   228,   320,   nil,   nil,   226,   227,   nil,   nil,    17,
   nil,   439,    21,    22,    23,    24,   nil,   642,   nil,   nil,
   nil,   309,   nil,    25,   410,    32,   nil,   nil,    34,    37,
    36,   nil,   223,   224,   356,   nil,   131,   418,   130,   133,
    77,    78,   nil,    92,    46,   282,   nil,   780,   411,   nil,
   412,   423,   597,   485,   280,   266,    47,    48,    49,    50,
    51,    52,    53,    54,    55,   nil,   267,    61,   nil,   nil,
   nil,   nil,   nil,   nil,    69,   nil,   535,    70,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   803,   880,   nil,
   804,   905,   752,   658,   nil,   659,   nil,   nil,   660,   nil,
   662,   613,   nil,   nil,   nil,   668,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   422,   nil,   nil,   nil,   nil,
   nil,    76,    79,    80,   nil,   nil,   nil,   nil,   nil,   569,
   nil,   nil,   nil,   nil,   nil,   nil,   872,   713,   657,   nil,
   661,   672,   674,   675,   764,   678,   679,   765,   682,   685,
   284 ]

racc_reduce_table = [
  0, 0, :racc_error,
  1, 144, :_reduce_none,
  2, 145, :_reduce_2,
  0, 146, :_reduce_3,
  1, 146, :_reduce_4,
  3, 146, :_reduce_5,
  2, 146, :_reduce_6,
  1, 148, :_reduce_none,
  4, 148, :_reduce_8,
  4, 151, :_reduce_9,
  2, 152, :_reduce_10,
  0, 156, :_reduce_11,
  1, 156, :_reduce_12,
  3, 156, :_reduce_13,
  2, 156, :_reduce_14,
  1, 157, :_reduce_none,
  4, 157, :_reduce_16,
  0, 173, :_reduce_17,
  4, 150, :_reduce_18,
  3, 150, :_reduce_19,
  3, 150, :_reduce_20,
  3, 150, :_reduce_21,
  2, 150, :_reduce_22,
  3, 150, :_reduce_23,
  3, 150, :_reduce_24,
  3, 150, :_reduce_25,
  3, 150, :_reduce_26,
  3, 150, :_reduce_27,
  4, 150, :_reduce_28,
  1, 150, :_reduce_none,
  3, 150, :_reduce_30,
  3, 150, :_reduce_31,
  6, 150, :_reduce_32,
  5, 150, :_reduce_33,
  5, 150, :_reduce_34,
  5, 150, :_reduce_35,
  5, 150, :_reduce_36,
  3, 150, :_reduce_37,
  3, 150, :_reduce_38,
  3, 150, :_reduce_39,
  1, 150, :_reduce_none,
  3, 161, :_reduce_41,
  3, 161, :_reduce_42,
  1, 172, :_reduce_none,
  3, 172, :_reduce_44,
  3, 172, :_reduce_45,
  3, 172, :_reduce_46,
  2, 172, :_reduce_47,
  1, 172, :_reduce_none,
  1, 160, :_reduce_none,
  1, 163, :_reduce_none,
  1, 163, :_reduce_none,
  1, 177, :_reduce_none,
  4, 177, :_reduce_53,
  0, 185, :_reduce_54,
  5, 182, :_reduce_55,
  1, 184, :_reduce_none,
  2, 176, :_reduce_57,
  3, 176, :_reduce_58,
  4, 176, :_reduce_59,
  5, 176, :_reduce_60,
  4, 176, :_reduce_61,
  5, 176, :_reduce_62,
  2, 176, :_reduce_63,
  2, 176, :_reduce_64,
  2, 176, :_reduce_65,
  2, 176, :_reduce_66,
  2, 176, :_reduce_67,
  1, 162, :_reduce_68,
  3, 162, :_reduce_69,
  1, 189, :_reduce_70,
  3, 189, :_reduce_71,
  1, 188, :_reduce_none,
  2, 188, :_reduce_73,
  3, 188, :_reduce_74,
  5, 188, :_reduce_75,
  2, 188, :_reduce_76,
  4, 188, :_reduce_77,
  2, 188, :_reduce_78,
  4, 188, :_reduce_79,
  1, 188, :_reduce_80,
  3, 188, :_reduce_81,
  1, 192, :_reduce_none,
  3, 192, :_reduce_83,
  2, 191, :_reduce_84,
  3, 191, :_reduce_85,
  1, 194, :_reduce_86,
  3, 194, :_reduce_87,
  1, 193, :_reduce_88,
  1, 193, :_reduce_89,
  4, 193, :_reduce_90,
  3, 193, :_reduce_91,
  3, 193, :_reduce_92,
  3, 193, :_reduce_93,
  3, 193, :_reduce_94,
  2, 193, :_reduce_95,
  1, 193, :_reduce_96,
  1, 169, :_reduce_97,
  1, 169, :_reduce_98,
  4, 169, :_reduce_99,
  3, 169, :_reduce_100,
  3, 169, :_reduce_101,
  3, 169, :_reduce_102,
  3, 169, :_reduce_103,
  2, 169, :_reduce_104,
  1, 169, :_reduce_105,
  1, 197, :_reduce_106,
  1, 197, :_reduce_none,
  2, 198, :_reduce_108,
  1, 198, :_reduce_109,
  3, 198, :_reduce_110,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 199, :_reduce_none,
  1, 202, :_reduce_116,
  1, 202, :_reduce_none,
  1, 158, :_reduce_none,
  1, 158, :_reduce_none,
  1, 159, :_reduce_120,
  0, 205, :_reduce_121,
  4, 159, :_reduce_122,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  3, 175, :_reduce_194,
  5, 175, :_reduce_195,
  3, 175, :_reduce_196,
  5, 175, :_reduce_197,
  6, 175, :_reduce_198,
  5, 175, :_reduce_199,
  5, 175, :_reduce_200,
  5, 175, :_reduce_201,
  5, 175, :_reduce_202,
  4, 175, :_reduce_203,
  3, 175, :_reduce_204,
  3, 175, :_reduce_205,
  3, 175, :_reduce_206,
  3, 175, :_reduce_207,
  3, 175, :_reduce_208,
  3, 175, :_reduce_209,
  3, 175, :_reduce_210,
  3, 175, :_reduce_211,
  3, 175, :_reduce_212,
  4, 175, :_reduce_213,
  2, 175, :_reduce_214,
  2, 175, :_reduce_215,
  3, 175, :_reduce_216,
  3, 175, :_reduce_217,
  3, 175, :_reduce_218,
  3, 175, :_reduce_219,
  3, 175, :_reduce_220,
  3, 175, :_reduce_221,
  3, 175, :_reduce_222,
  3, 175, :_reduce_223,
  3, 175, :_reduce_224,
  3, 175, :_reduce_225,
  3, 175, :_reduce_226,
  3, 175, :_reduce_227,
  3, 175, :_reduce_228,
  2, 175, :_reduce_229,
  2, 175, :_reduce_230,
  3, 175, :_reduce_231,
  3, 175, :_reduce_232,
  3, 175, :_reduce_233,
  3, 175, :_reduce_234,
  3, 175, :_reduce_235,
  6, 175, :_reduce_236,
  1, 175, :_reduce_none,
  1, 208, :_reduce_none,
  1, 209, :_reduce_none,
  2, 209, :_reduce_none,
  4, 209, :_reduce_241,
  2, 209, :_reduce_242,
  3, 214, :_reduce_243,
  0, 215, :_reduce_244,
  1, 215, :_reduce_none,
  0, 166, :_reduce_246,
  1, 166, :_reduce_none,
  2, 166, :_reduce_none,
  4, 166, :_reduce_249,
  2, 166, :_reduce_250,
  1, 187, :_reduce_251,
  2, 187, :_reduce_252,
  2, 187, :_reduce_253,
  4, 187, :_reduce_254,
  1, 187, :_reduce_255,
  0, 218, :_reduce_256,
  2, 181, :_reduce_257,
  2, 217, :_reduce_258,
  2, 216, :_reduce_259,
  0, 216, :_reduce_260,
  1, 211, :_reduce_261,
  2, 211, :_reduce_262,
  3, 211, :_reduce_263,
  4, 211, :_reduce_264,
  1, 171, :_reduce_265,
  1, 171, :_reduce_none,
  3, 170, :_reduce_267,
  4, 170, :_reduce_268,
  2, 170, :_reduce_269,
  1, 207, :_reduce_none,
  1, 207, :_reduce_none,
  1, 207, :_reduce_none,
  1, 207, :_reduce_none,
  1, 207, :_reduce_none,
  1, 207, :_reduce_none,
  1, 207, :_reduce_none,
  1, 207, :_reduce_none,
  1, 207, :_reduce_none,
  1, 207, :_reduce_none,
  1, 207, :_reduce_280,
  0, 243, :_reduce_281,
  4, 207, :_reduce_282,
  0, 244, :_reduce_283,
  0, 245, :_reduce_284,
  6, 207, :_reduce_285,
  0, 246, :_reduce_286,
  4, 207, :_reduce_287,
  3, 207, :_reduce_288,
  3, 207, :_reduce_289,
  2, 207, :_reduce_290,
  3, 207, :_reduce_291,
  3, 207, :_reduce_292,
  1, 207, :_reduce_293,
  4, 207, :_reduce_294,
  3, 207, :_reduce_295,
  1, 207, :_reduce_296,
  5, 207, :_reduce_297,
  4, 207, :_reduce_298,
  3, 207, :_reduce_299,
  2, 207, :_reduce_300,
  1, 207, :_reduce_none,
  2, 207, :_reduce_302,
  0, 247, :_reduce_303,
  3, 207, :_reduce_304,
  6, 207, :_reduce_305,
  6, 207, :_reduce_306,
  0, 248, :_reduce_307,
  0, 249, :_reduce_308,
  7, 207, :_reduce_309,
  0, 250, :_reduce_310,
  0, 251, :_reduce_311,
  7, 207, :_reduce_312,
  5, 207, :_reduce_313,
  4, 207, :_reduce_314,
  0, 252, :_reduce_315,
  0, 253, :_reduce_316,
  9, 207, :_reduce_317,
  0, 254, :_reduce_318,
  6, 207, :_reduce_319,
  0, 255, :_reduce_320,
  7, 207, :_reduce_321,
  0, 256, :_reduce_322,
  5, 207, :_reduce_323,
  0, 257, :_reduce_324,
  6, 207, :_reduce_325,
  0, 258, :_reduce_326,
  0, 259, :_reduce_327,
  9, 207, :_reduce_328,
  1, 207, :_reduce_329,
  1, 207, :_reduce_330,
  1, 207, :_reduce_331,
  1, 207, :_reduce_332,
  1, 165, :_reduce_none,
  1, 237, :_reduce_334,
  1, 240, :_reduce_335,
  1, 232, :_reduce_none,
  1, 232, :_reduce_none,
  2, 232, :_reduce_338,
  1, 234, :_reduce_none,
  1, 234, :_reduce_none,
  1, 233, :_reduce_none,
  5, 233, :_reduce_342,
  1, 154, :_reduce_none,
  2, 154, :_reduce_344,
  1, 236, :_reduce_none,
  1, 236, :_reduce_none,
  1, 260, :_reduce_347,
  3, 260, :_reduce_348,
  1, 263, :_reduce_349,
  3, 263, :_reduce_350,
  1, 262, :_reduce_none,
  4, 262, :_reduce_352,
  6, 262, :_reduce_353,
  3, 262, :_reduce_354,
  5, 262, :_reduce_355,
  2, 262, :_reduce_356,
  4, 262, :_reduce_357,
  1, 262, :_reduce_358,
  3, 262, :_reduce_359,
  4, 264, :_reduce_360,
  2, 264, :_reduce_361,
  2, 264, :_reduce_362,
  1, 264, :_reduce_363,
  2, 269, :_reduce_364,
  0, 269, :_reduce_365,
  6, 270, :_reduce_366,
  8, 270, :_reduce_367,
  4, 270, :_reduce_368,
  6, 270, :_reduce_369,
  4, 270, :_reduce_370,
  2, 270, :_reduce_none,
  6, 270, :_reduce_372,
  2, 270, :_reduce_373,
  4, 270, :_reduce_374,
  6, 270, :_reduce_375,
  2, 270, :_reduce_376,
  4, 270, :_reduce_377,
  2, 270, :_reduce_378,
  4, 270, :_reduce_379,
  1, 270, :_reduce_none,
  0, 183, :_reduce_381,
  1, 183, :_reduce_382,
  3, 274, :_reduce_383,
  1, 274, :_reduce_384,
  4, 274, :_reduce_385,
  1, 275, :_reduce_386,
  4, 275, :_reduce_387,
  1, 276, :_reduce_388,
  3, 276, :_reduce_389,
  1, 277, :_reduce_390,
  1, 277, :_reduce_none,
  0, 281, :_reduce_392,
  0, 282, :_reduce_393,
  4, 231, :_reduce_394,
  4, 279, :_reduce_395,
  1, 279, :_reduce_396,
  0, 285, :_reduce_397,
  4, 280, :_reduce_398,
  0, 286, :_reduce_399,
  4, 280, :_reduce_400,
  0, 287, :_reduce_401,
  5, 284, :_reduce_402,
  2, 178, :_reduce_403,
  4, 178, :_reduce_404,
  5, 178, :_reduce_405,
  5, 178, :_reduce_406,
  2, 230, :_reduce_407,
  4, 230, :_reduce_408,
  4, 230, :_reduce_409,
  3, 230, :_reduce_410,
  3, 230, :_reduce_411,
  3, 230, :_reduce_412,
  2, 230, :_reduce_413,
  1, 230, :_reduce_414,
  4, 230, :_reduce_415,
  0, 289, :_reduce_416,
  5, 229, :_reduce_417,
  0, 290, :_reduce_418,
  5, 229, :_reduce_419,
  5, 235, :_reduce_420,
  1, 291, :_reduce_421,
  1, 291, :_reduce_none,
  6, 153, :_reduce_423,
  0, 153, :_reduce_424,
  1, 292, :_reduce_425,
  1, 292, :_reduce_none,
  1, 292, :_reduce_none,
  2, 293, :_reduce_428,
  1, 293, :_reduce_none,
  2, 155, :_reduce_430,
  1, 155, :_reduce_none,
  1, 219, :_reduce_none,
  1, 219, :_reduce_none,
  1, 219, :_reduce_none,
  1, 220, :_reduce_435,
  1, 295, :_reduce_436,
  2, 295, :_reduce_437,
  3, 296, :_reduce_438,
  1, 296, :_reduce_439,
  1, 296, :_reduce_440,
  3, 221, :_reduce_441,
  4, 222, :_reduce_442,
  3, 223, :_reduce_443,
  0, 300, :_reduce_444,
  3, 300, :_reduce_445,
  1, 301, :_reduce_446,
  2, 301, :_reduce_447,
  3, 225, :_reduce_448,
  0, 303, :_reduce_449,
  3, 303, :_reduce_450,
  3, 224, :_reduce_451,
  3, 226, :_reduce_452,
  0, 304, :_reduce_453,
  3, 304, :_reduce_454,
  0, 305, :_reduce_455,
  3, 305, :_reduce_456,
  0, 297, :_reduce_457,
  2, 297, :_reduce_458,
  0, 298, :_reduce_459,
  2, 298, :_reduce_460,
  0, 299, :_reduce_461,
  2, 299, :_reduce_462,
  1, 302, :_reduce_463,
  2, 302, :_reduce_464,
  0, 307, :_reduce_465,
  4, 302, :_reduce_466,
  1, 306, :_reduce_467,
  1, 306, :_reduce_468,
  1, 306, :_reduce_469,
  1, 306, :_reduce_none,
  1, 203, :_reduce_471,
  3, 204, :_reduce_472,
  1, 294, :_reduce_473,
  2, 294, :_reduce_474,
  1, 206, :_reduce_475,
  1, 206, :_reduce_476,
  1, 206, :_reduce_477,
  1, 206, :_reduce_478,
  1, 195, :_reduce_479,
  1, 195, :_reduce_480,
  1, 195, :_reduce_481,
  1, 195, :_reduce_482,
  1, 195, :_reduce_483,
  1, 196, :_reduce_484,
  1, 196, :_reduce_485,
  1, 196, :_reduce_486,
  1, 196, :_reduce_487,
  1, 196, :_reduce_488,
  1, 196, :_reduce_489,
  1, 196, :_reduce_490,
  1, 227, :_reduce_491,
  1, 227, :_reduce_492,
  1, 164, :_reduce_493,
  1, 164, :_reduce_494,
  1, 168, :_reduce_495,
  1, 168, :_reduce_496,
  1, 238, :_reduce_497,
  0, 308, :_reduce_498,
  4, 238, :_reduce_499,
  2, 238, :_reduce_500,
  3, 241, :_reduce_501,
  0, 310, :_reduce_502,
  3, 241, :_reduce_503,
  4, 309, :_reduce_504,
  2, 309, :_reduce_505,
  2, 309, :_reduce_506,
  1, 309, :_reduce_507,
  2, 312, :_reduce_508,
  0, 312, :_reduce_509,
  6, 283, :_reduce_510,
  8, 283, :_reduce_511,
  4, 283, :_reduce_512,
  6, 283, :_reduce_513,
  4, 283, :_reduce_514,
  6, 283, :_reduce_515,
  2, 283, :_reduce_516,
  4, 283, :_reduce_517,
  6, 283, :_reduce_518,
  2, 283, :_reduce_519,
  4, 283, :_reduce_520,
  2, 283, :_reduce_521,
  4, 283, :_reduce_522,
  1, 283, :_reduce_523,
  0, 283, :_reduce_524,
  1, 278, :_reduce_525,
  1, 278, :_reduce_526,
  1, 278, :_reduce_527,
  1, 278, :_reduce_528,
  1, 261, :_reduce_none,
  1, 261, :_reduce_530,
  1, 314, :_reduce_531,
  3, 314, :_reduce_532,
  1, 271, :_reduce_533,
  3, 271, :_reduce_534,
  1, 315, :_reduce_535,
  2, 316, :_reduce_536,
  1, 316, :_reduce_537,
  2, 317, :_reduce_538,
  1, 317, :_reduce_539,
  1, 265, :_reduce_540,
  3, 265, :_reduce_541,
  1, 311, :_reduce_542,
  3, 311, :_reduce_543,
  1, 318, :_reduce_none,
  1, 318, :_reduce_none,
  2, 266, :_reduce_546,
  1, 266, :_reduce_547,
  3, 319, :_reduce_548,
  3, 320, :_reduce_549,
  1, 272, :_reduce_550,
  3, 272, :_reduce_551,
  1, 313, :_reduce_552,
  3, 313, :_reduce_553,
  1, 321, :_reduce_none,
  1, 321, :_reduce_none,
  2, 273, :_reduce_556,
  1, 273, :_reduce_557,
  1, 322, :_reduce_none,
  1, 322, :_reduce_none,
  2, 268, :_reduce_560,
  2, 267, :_reduce_561,
  0, 267, :_reduce_562,
  1, 242, :_reduce_none,
  3, 242, :_reduce_564,
  0, 228, :_reduce_565,
  2, 228, :_reduce_none,
  1, 213, :_reduce_567,
  3, 213, :_reduce_568,
  3, 323, :_reduce_569,
  2, 323, :_reduce_570,
  2, 323, :_reduce_571,
  1, 186, :_reduce_none,
  1, 186, :_reduce_none,
  1, 186, :_reduce_none,
  1, 180, :_reduce_none,
  1, 180, :_reduce_none,
  1, 180, :_reduce_none,
  1, 180, :_reduce_none,
  1, 288, :_reduce_none,
  1, 288, :_reduce_none,
  1, 288, :_reduce_none,
  1, 179, :_reduce_none,
  1, 179, :_reduce_none,
  0, 147, :_reduce_none,
  1, 147, :_reduce_none,
  0, 174, :_reduce_none,
  1, 174, :_reduce_none,
  2, 190, :_reduce_588,
  2, 167, :_reduce_589,
  0, 212, :_reduce_none,
  1, 212, :_reduce_none,
  1, 212, :_reduce_none,
  1, 239, :_reduce_593,
  1, 239, :_reduce_none,
  1, 149, :_reduce_none,
  2, 149, :_reduce_none,
  0, 210, :_reduce_597 ]

racc_reduce_n = 598

racc_shift_n = 1022

racc_token_table = {
  false => 0,
  :error => 1,
  :kCLASS => 2,
  :kMODULE => 3,
  :kDEF => 4,
  :kUNDEF => 5,
  :kBEGIN => 6,
  :kRESCUE => 7,
  :kENSURE => 8,
  :kEND => 9,
  :kIF => 10,
  :kUNLESS => 11,
  :kTHEN => 12,
  :kELSIF => 13,
  :kELSE => 14,
  :kCASE => 15,
  :kWHEN => 16,
  :kWHILE => 17,
  :kUNTIL => 18,
  :kFOR => 19,
  :kBREAK => 20,
  :kNEXT => 21,
  :kREDO => 22,
  :kRETRY => 23,
  :kIN => 24,
  :kDO => 25,
  :kDO_COND => 26,
  :kDO_BLOCK => 27,
  :kDO_LAMBDA => 28,
  :kRETURN => 29,
  :kYIELD => 30,
  :kSUPER => 31,
  :kSELF => 32,
  :kNIL => 33,
  :kTRUE => 34,
  :kFALSE => 35,
  :kAND => 36,
  :kOR => 37,
  :kNOT => 38,
  :kIF_MOD => 39,
  :kUNLESS_MOD => 40,
  :kWHILE_MOD => 41,
  :kUNTIL_MOD => 42,
  :kRESCUE_MOD => 43,
  :kALIAS => 44,
  :kDEFINED => 45,
  :klBEGIN => 46,
  :klEND => 47,
  :k__LINE__ => 48,
  :k__FILE__ => 49,
  :k__ENCODING__ => 50,
  :tIDENTIFIER => 51,
  :tFID => 52,
  :tGVAR => 53,
  :tIVAR => 54,
  :tCONSTANT => 55,
  :tLABEL => 56,
  :tCVAR => 57,
  :tNTH_REF => 58,
  :tBACK_REF => 59,
  :tSTRING_CONTENT => 60,
  :tINTEGER => 61,
  :tFLOAT => 62,
  :tUPLUS => 63,
  :tUMINUS => 64,
  :tUNARY_NUM => 65,
  :tPOW => 66,
  :tCMP => 67,
  :tEQ => 68,
  :tEQQ => 69,
  :tNEQ => 70,
  :tGEQ => 71,
  :tLEQ => 72,
  :tANDOP => 73,
  :tOROP => 74,
  :tMATCH => 75,
  :tNMATCH => 76,
  :tDOT => 77,
  :tDOT2 => 78,
  :tDOT3 => 79,
  :tAREF => 80,
  :tASET => 81,
  :tLSHFT => 82,
  :tRSHFT => 83,
  :tCOLON2 => 84,
  :tCOLON3 => 85,
  :tOP_ASGN => 86,
  :tASSOC => 87,
  :tLPAREN => 88,
  :tLPAREN2 => 89,
  :tRPAREN => 90,
  :tLPAREN_ARG => 91,
  :tLBRACK => 92,
  :tLBRACK2 => 93,
  :tRBRACK => 94,
  :tLBRACE => 95,
  :tLBRACE_ARG => 96,
  :tSTAR => 97,
  :tSTAR2 => 98,
  :tAMPER => 99,
  :tAMPER2 => 100,
  :tTILDE => 101,
  :tPERCENT => 102,
  :tDIVIDE => 103,
  :tDSTAR => 104,
  :tPLUS => 105,
  :tMINUS => 106,
  :tLT => 107,
  :tGT => 108,
  :tPIPE => 109,
  :tBANG => 110,
  :tCARET => 111,
  :tLCURLY => 112,
  :tRCURLY => 113,
  :tBACK_REF2 => 114,
  :tSYMBEG => 115,
  :tSTRING_BEG => 116,
  :tXSTRING_BEG => 117,
  :tREGEXP_BEG => 118,
  :tREGEXP_OPT => 119,
  :tWORDS_BEG => 120,
  :tQWORDS_BEG => 121,
  :tSYMBOLS_BEG => 122,
  :tQSYMBOLS_BEG => 123,
  :tSTRING_DBEG => 124,
  :tSTRING_DVAR => 125,
  :tSTRING_END => 126,
  :tSTRING_DEND => 127,
  :tSTRING => 128,
  :tSYMBOL => 129,
  :tNL => 130,
  :tEH => 131,
  :tCOLON => 132,
  :tCOMMA => 133,
  :tSPACE => 134,
  :tSEMI => 135,
  :tLAMBDA => 136,
  :tLAMBEG => 137,
  :tCHARACTER => 138,
  :tRATIONAL => 139,
  :tIMAGINARY => 140,
  :tEQL => 141,
  :tLOWEST => 142 }

racc_nt_base = 143

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]
Ractor.make_shareable(Racc_arg) if defined?(Ractor)

Racc_token_to_s_table = [
  "$end",
  "error",
  "kCLASS",
  "kMODULE",
  "kDEF",
  "kUNDEF",
  "kBEGIN",
  "kRESCUE",
  "kENSURE",
  "kEND",
  "kIF",
  "kUNLESS",
  "kTHEN",
  "kELSIF",
  "kELSE",
  "kCASE",
  "kWHEN",
  "kWHILE",
  "kUNTIL",
  "kFOR",
  "kBREAK",
  "kNEXT",
  "kREDO",
  "kRETRY",
  "kIN",
  "kDO",
  "kDO_COND",
  "kDO_BLOCK",
  "kDO_LAMBDA",
  "kRETURN",
  "kYIELD",
  "kSUPER",
  "kSELF",
  "kNIL",
  "kTRUE",
  "kFALSE",
  "kAND",
  "kOR",
  "kNOT",
  "kIF_MOD",
  "kUNLESS_MOD",
  "kWHILE_MOD",
  "kUNTIL_MOD",
  "kRESCUE_MOD",
  "kALIAS",
  "kDEFINED",
  "klBEGIN",
  "klEND",
  "k__LINE__",
  "k__FILE__",
  "k__ENCODING__",
  "tIDENTIFIER",
  "tFID",
  "tGVAR",
  "tIVAR",
  "tCONSTANT",
  "tLABEL",
  "tCVAR",
  "tNTH_REF",
  "tBACK_REF",
  "tSTRING_CONTENT",
  "tINTEGER",
  "tFLOAT",
  "tUPLUS",
  "tUMINUS",
  "tUNARY_NUM",
  "tPOW",
  "tCMP",
  "tEQ",
  "tEQQ",
  "tNEQ",
  "tGEQ",
  "tLEQ",
  "tANDOP",
  "tOROP",
  "tMATCH",
  "tNMATCH",
  "tDOT",
  "tDOT2",
  "tDOT3",
  "tAREF",
  "tASET",
  "tLSHFT",
  "tRSHFT",
  "tCOLON2",
  "tCOLON3",
  "tOP_ASGN",
  "tASSOC",
  "tLPAREN",
  "tLPAREN2",
  "tRPAREN",
  "tLPAREN_ARG",
  "tLBRACK",
  "tLBRACK2",
  "tRBRACK",
  "tLBRACE",
  "tLBRACE_ARG",
  "tSTAR",
  "tSTAR2",
  "tAMPER",
  "tAMPER2",
  "tTILDE",
  "tPERCENT",
  "tDIVIDE",
  "tDSTAR",
  "tPLUS",
  "tMINUS",
  "tLT",
  "tGT",
  "tPIPE",
  "tBANG",
  "tCARET",
  "tLCURLY",
  "tRCURLY",
  "tBACK_REF2",
  "tSYMBEG",
  "tSTRING_BEG",
  "tXSTRING_BEG",
  "tREGEXP_BEG",
  "tREGEXP_OPT",
  "tWORDS_BEG",
  "tQWORDS_BEG",
  "tSYMBOLS_BEG",
  "tQSYMBOLS_BEG",
  "tSTRING_DBEG",
  "tSTRING_DVAR",
  "tSTRING_END",
  "tSTRING_DEND",
  "tSTRING",
  "tSYMBOL",
  "tNL",
  "tEH",
  "tCOLON",
  "tCOMMA",
  "tSPACE",
  "tSEMI",
  "tLAMBDA",
  "tLAMBEG",
  "tCHARACTER",
  "tRATIONAL",
  "tIMAGINARY",
  "tEQL",
  "tLOWEST",
  "$start",
  "program",
  "top_compstmt",
  "top_stmts",
  "opt_terms",
  "top_stmt",
  "terms",
  "stmt",
  "bodystmt",
  "compstmt",
  "opt_rescue",
  "opt_else",
  "opt_ensure",
  "stmts",
  "stmt_or_begin",
  "fitem",
  "undef_list",
  "expr_value",
  "command_asgn",
  "mlhs",
  "command_call",
  "var_lhs",
  "primary_value",
  "opt_call_args",
  "rbracket",
  "backref",
  "lhs",
  "mrhs",
  "mrhs_arg",
  "expr",
  "@1",
  "opt_nl",
  "arg",
  "command",
  "block_command",
  "block_call",
  "dot_or_colon",
  "operation2",
  "command_args",
  "cmd_brace_block",
  "opt_block_param",
  "fcall",
  "@2",
  "operation",
  "call_args",
  "mlhs_basic",
  "mlhs_inner",
  "rparen",
  "mlhs_head",
  "mlhs_item",
  "mlhs_node",
  "mlhs_post",
  "user_variable",
  "keyword_variable",
  "cname",
  "cpath",
  "fname",
  "op",
  "reswords",
  "fsym",
  "symbol",
  "dsym",
  "@3",
  "simple_numeric",
  "primary",
  "arg_value",
  "aref_args",
  "none",
  "args",
  "trailer",
  "assocs",
  "paren_args",
  "opt_paren_args",
  "opt_block_arg",
  "block_arg",
  "@4",
  "literal",
  "strings",
  "xstring",
  "regexp",
  "words",
  "qwords",
  "symbols",
  "qsymbols",
  "var_ref",
  "assoc_list",
  "brace_block",
  "method_call",
  "lambda",
  "then",
  "if_tail",
  "do",
  "case_body",
  "for_var",
  "k_class",
  "superclass",
  "term",
  "k_module",
  "f_arglist",
  "singleton",
  "@5",
  "@6",
  "@7",
  "@8",
  "@9",
  "@10",
  "@11",
  "@12",
  "@13",
  "@14",
  "@15",
  "@16",
  "@17",
  "@18",
  "@19",
  "@20",
  "@21",
  "f_marg",
  "f_norm_arg",
  "f_margs",
  "f_marg_list",
  "block_args_tail",
  "f_block_kwarg",
  "f_kwrest",
  "opt_f_block_arg",
  "f_block_arg",
  "opt_block_args_tail",
  "block_param",
  "f_arg",
  "f_block_optarg",
  "f_rest_arg",
  "block_param_def",
  "opt_bv_decl",
  "bv_decls",
  "bvar",
  "f_bad_arg",
  "f_larglist",
  "lambda_body",
  "@22",
  "@23",
  "f_args",
  "do_block",
  "@24",
  "@25",
  "@26",
  "operation3",
  "@27",
  "@28",
  "cases",
  "exc_list",
  "exc_var",
  "numeric",
  "string",
  "string1",
  "string_contents",
  "xstring_contents",
  "regexp_contents",
  "word_list",
  "word",
  "string_content",
  "symbol_list",
  "qword_list",
  "qsym_list",
  "string_dvar",
  "@29",
  "@30",
  "args_tail",
  "@31",
  "f_kwarg",
  "opt_args_tail",
  "f_optarg",
  "f_arg_item",
  "f_label",
  "f_kw",
  "f_block_kw",
  "kwrest_mark",
  "f_opt",
  "f_block_opt",
  "restarg_mark",
  "blkarg_mark",
  "assoc" ]
Ractor.make_shareable(Racc_token_to_s_table) if defined?(Ractor)

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

# reduce 1 omitted

def _reduce_2(val, _values, result)
                      result = @builder.compstmt(val[0])

    result
end

def _reduce_3(val, _values, result)
                      result = []

    result
end

def _reduce_4(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_5(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_6(val, _values, result)
                      result = [ val[1] ]

    result
end

# reduce 7 omitted

def _reduce_8(val, _values, result)
                      result = @builder.preexe(val[0], val[1], val[2], val[3])

    result
end

def _reduce_9(val, _values, result)
                      rescue_bodies     = val[1]
                      else_t,   else_   = val[2]
                      ensure_t, ensure_ = val[3]

                      if rescue_bodies.empty? && !else_t.nil?
                        diagnostic :warning, :useless_else, nil, else_t
                      end

                      result = @builder.begin_body(val[0],
                                  rescue_bodies,
                                  else_t,   else_,
                                  ensure_t, ensure_)

    result
end

def _reduce_10(val, _values, result)
                      result = @builder.compstmt(val[0])

    result
end

def _reduce_11(val, _values, result)
                      result = []

    result
end

def _reduce_12(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_13(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_14(val, _values, result)
                      result = [ val[1] ]

    result
end

# reduce 15 omitted

def _reduce_16(val, _values, result)
                      diagnostic :error, :begin_in_method, nil, val[0]

    result
end

def _reduce_17(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_18(val, _values, result)
                      result = @builder.alias(val[0], val[1], val[3])

    result
end

def _reduce_19(val, _values, result)
                      result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.gvar(val[2]))

    result
end

def _reduce_20(val, _values, result)
                      result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.back_ref(val[2]))

    result
end

def _reduce_21(val, _values, result)
                      diagnostic :error, :nth_ref_alias, nil, val[2]

    result
end

def _reduce_22(val, _values, result)
                      result = @builder.undef_method(val[0], val[1])

    result
end

def _reduce_23(val, _values, result)
                      result = @builder.condition_mod(val[0], nil,
                                                      val[1], val[2])

    result
end

def _reduce_24(val, _values, result)
                      result = @builder.condition_mod(nil, val[0],
                                                      val[1], val[2])

    result
end

def _reduce_25(val, _values, result)
                      result = @builder.loop_mod(:while, val[0], val[1], val[2])

    result
end

def _reduce_26(val, _values, result)
                      result = @builder.loop_mod(:until, val[0], val[1], val[2])

    result
end

def _reduce_27(val, _values, result)
                      rescue_body = @builder.rescue_body(val[1],
                                        nil, nil, nil,
                                        nil, val[2])

                      result = @builder.begin_body(val[0], [ rescue_body ])

    result
end

def _reduce_28(val, _values, result)
                      result = @builder.postexe(val[0], val[1], val[2], val[3])

    result
end

# reduce 29 omitted

def _reduce_30(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1], val[2])

    result
end

def _reduce_31(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_32(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])

    result
end

def _reduce_33(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_34(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_35(val, _values, result)
                      const  = @builder.const_op_assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))
                      result = @builder.op_assign(const, val[3], val[4])

    result
end

def _reduce_36(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_37(val, _values, result)
                      @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_38(val, _values, result)
                      result = @builder.assign(val[0], val[1],
                                  @builder.array(nil, val[2], nil))

    result
end

def _reduce_39(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1], val[2])

    result
end

# reduce 40 omitted

def _reduce_41(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

def _reduce_42(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

# reduce 43 omitted

def _reduce_44(val, _values, result)
                      result = @builder.logical_op(:and, val[0], val[1], val[2])

    result
end

def _reduce_45(val, _values, result)
                      result = @builder.logical_op(:or, val[0], val[1], val[2])

    result
end

def _reduce_46(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[2], nil)

    result
end

def _reduce_47(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[1], nil)

    result
end

# reduce 48 omitted

# reduce 49 omitted

# reduce 50 omitted

# reduce 51 omitted

# reduce 52 omitted

def _reduce_53(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
end

def _reduce_54(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_55(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

# reduce 56 omitted

def _reduce_57(val, _values, result)
                      result = @builder.call_method(nil, nil, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_58(val, _values, result)
                      method_call = @builder.call_method(nil, nil, val[0],
                                        nil, val[1], nil)

                      begin_t, args, body, end_t = val[2]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_59(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
end

def _reduce_60(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                        nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_61(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
end

def _reduce_62(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                        nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_63(val, _values, result)
                      result = @builder.keyword_cmd(:super, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_64(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_65(val, _values, result)
                      result = @builder.keyword_cmd(:return, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_66(val, _values, result)
                      result = @builder.keyword_cmd(:break, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_67(val, _values, result)
                      result = @builder.keyword_cmd(:next, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_68(val, _values, result)
                      result = @builder.multi_lhs(nil, val[0], nil)

    result
end

def _reduce_69(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_70(val, _values, result)
                      result = @builder.multi_lhs(nil, val[0], nil)

    result
end

def _reduce_71(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

# reduce 72 omitted

def _reduce_73(val, _values, result)
                      result = val[0].
                                  push(val[1])

    result
end

def _reduce_74(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1], val[2]))

    result
end

def _reduce_75(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1], val[2])).
                                  concat(val[4])

    result
end

def _reduce_76(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1]))

    result
end

def _reduce_77(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1])).
                                  concat(val[3])

    result
end

def _reduce_78(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

def _reduce_79(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]),
                                 *val[3] ]

    result
end

def _reduce_80(val, _values, result)
                      result = [ @builder.splat(val[0]) ]

    result
end

def _reduce_81(val, _values, result)
                      result = [ @builder.splat(val[0]),
                                 *val[2] ]

    result
end

# reduce 82 omitted

def _reduce_83(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_84(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_85(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_86(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_87(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_88(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_89(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_90(val, _values, result)
                      result = @builder.index_asgn(val[0], val[1], val[2], val[3])

    result
end

def _reduce_91(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_92(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_93(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_94(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))

    result
end

def _reduce_95(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))

    result
end

def _reduce_96(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_97(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_98(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_99(val, _values, result)
                      result = @builder.index_asgn(val[0], val[1], val[2], val[3])

    result
end

def _reduce_100(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_101(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_102(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_103(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))

    result
end

def _reduce_104(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))

    result
end

def _reduce_105(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_106(val, _values, result)
                      diagnostic :error, :module_name_const, nil, val[0]

    result
end

# reduce 107 omitted

def _reduce_108(val, _values, result)
                      result = @builder.const_global(val[0], val[1])

    result
end

def _reduce_109(val, _values, result)
                      result = @builder.const(val[0])

    result
end

def _reduce_110(val, _values, result)
                      result = @builder.const_fetch(val[0], val[1], val[2])

    result
end

# reduce 111 omitted

# reduce 112 omitted

# reduce 113 omitted

# reduce 114 omitted

# reduce 115 omitted

def _reduce_116(val, _values, result)
                      result = @builder.symbol_internal(val[0])

    result
end

# reduce 117 omitted

# reduce 118 omitted

# reduce 119 omitted

def _reduce_120(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_121(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_122(val, _values, result)
                      result = val[0] << val[3]

    result
end

# reduce 123 omitted

# reduce 124 omitted

# reduce 125 omitted

# reduce 126 omitted

# reduce 127 omitted

# reduce 128 omitted

# reduce 129 omitted

# reduce 130 omitted

# reduce 131 omitted

# reduce 132 omitted

# reduce 133 omitted

# reduce 134 omitted

# reduce 135 omitted

# reduce 136 omitted

# reduce 137 omitted

# reduce 138 omitted

# reduce 139 omitted

# reduce 140 omitted

# reduce 141 omitted

# reduce 142 omitted

# reduce 143 omitted

# reduce 144 omitted

# reduce 145 omitted

# reduce 146 omitted

# reduce 147 omitted

# reduce 148 omitted

# reduce 149 omitted

# reduce 150 omitted

# reduce 151 omitted

# reduce 152 omitted

# reduce 153 omitted

# reduce 154 omitted

# reduce 155 omitted

# reduce 156 omitted

# reduce 157 omitted

# reduce 158 omitted

# reduce 159 omitted

# reduce 160 omitted

# reduce 161 omitted

# reduce 162 omitted

# reduce 163 omitted

# reduce 164 omitted

# reduce 165 omitted

# reduce 166 omitted

# reduce 167 omitted

# reduce 168 omitted

# reduce 169 omitted

# reduce 170 omitted

# reduce 171 omitted

# reduce 172 omitted

# reduce 173 omitted

# reduce 174 omitted

# reduce 175 omitted

# reduce 176 omitted

# reduce 177 omitted

# reduce 178 omitted

# reduce 179 omitted

# reduce 180 omitted

# reduce 181 omitted

# reduce 182 omitted

# reduce 183 omitted

# reduce 184 omitted

# reduce 185 omitted

# reduce 186 omitted

# reduce 187 omitted

# reduce 188 omitted

# reduce 189 omitted

# reduce 190 omitted

# reduce 191 omitted

# reduce 192 omitted

# reduce 193 omitted

def _reduce_194(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

def _reduce_195(val, _values, result)
                      rescue_body = @builder.rescue_body(val[3],
                                        nil, nil, nil,
                                        nil, val[4])

                      rescue_ = @builder.begin_body(val[2], [ rescue_body ])

                      result  = @builder.assign(val[0], val[1], rescue_)

    result
end

def _reduce_196(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_197(val, _values, result)
                      rescue_body = @builder.rescue_body(val[3],
                                        nil, nil, nil,
                                        nil, val[4])

                      rescue_ = @builder.begin_body(val[2], [ rescue_body ])

                      result = @builder.op_assign(val[0], val[1], rescue_)

    result
end

def _reduce_198(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])

    result
end

def _reduce_199(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_200(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_201(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_202(val, _values, result)
                      const  = @builder.const_op_assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))
                      result = @builder.op_assign(const, val[3], val[4])

    result
end

def _reduce_203(val, _values, result)
                      const  = @builder.const_op_assignable(
                                  @builder.const_global(val[0], val[1]))
                      result = @builder.op_assign(const, val[2], val[3])

    result
end

def _reduce_204(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_205(val, _values, result)
                      result = @builder.range_inclusive(val[0], val[1], val[2])

    result
end

def _reduce_206(val, _values, result)
                      result = @builder.range_exclusive(val[0], val[1], val[2])

    result
end

def _reduce_207(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_208(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_209(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_210(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_211(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_212(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_213(val, _values, result)
                      result = @builder.unary_op(val[0],
                                  @builder.binary_op(
                                    val[1], val[2], val[3]))

    result
end

def _reduce_214(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_215(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_216(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_217(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_218(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_219(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_220(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_221(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_222(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_223(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_224(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_225(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_226(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_227(val, _values, result)
                      result = @builder.match_op(val[0], val[1], val[2])

    result
end

def _reduce_228(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_229(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[1], nil)

    result
end

def _reduce_230(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_231(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_232(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_233(val, _values, result)
                      result = @builder.logical_op(:and, val[0], val[1], val[2])

    result
end

def _reduce_234(val, _values, result)
                      result = @builder.logical_op(:or, val[0], val[1], val[2])

    result
end

def _reduce_235(val, _values, result)
                      result = @builder.keyword_cmd(:defined?, val[0], nil, [ val[2] ], nil)

    result
end

def _reduce_236(val, _values, result)
                      result = @builder.ternary(val[0], val[1],
                                                val[2], val[4], val[5])

    result
end

# reduce 237 omitted

# reduce 238 omitted

# reduce 239 omitted

# reduce 240 omitted

def _reduce_241(val, _values, result)
                      result = val[0] << @builder.associate(nil, val[2], nil)

    result
end

def _reduce_242(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]

    result
end

def _reduce_243(val, _values, result)
                      result = val

    result
end

def _reduce_244(val, _values, result)
                      result = [ nil, [], nil ]

    result
end

# reduce 245 omitted

def _reduce_246(val, _values, result)
                      result = []

    result
end

# reduce 247 omitted

# reduce 248 omitted

def _reduce_249(val, _values, result)
                      result = val[0] << @builder.associate(nil, val[2], nil)

    result
end

def _reduce_250(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]

    result
end

def _reduce_251(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_252(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_253(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]
                      result.concat(val[1])

    result
end

def _reduce_254(val, _values, result)
                      assocs = @builder.associate(nil, val[2], nil)
                      result = val[0] << assocs
                      result.concat(val[3])

    result
end

def _reduce_255(val, _values, result)
                      result =  [ val[0] ]

    result
end

def _reduce_256(val, _values, result)
                      result = @lexer.cmdarg.dup
                      @lexer.cmdarg.push(true)

    result
end

def _reduce_257(val, _values, result)
                      @lexer.cmdarg = val[0]

                      result = val[1]

    result
end

def _reduce_258(val, _values, result)
                      result = @builder.block_pass(val[0], val[1])

    result
end

def _reduce_259(val, _values, result)
                      result = [ val[1] ]

    result
end

def _reduce_260(val, _values, result)
                      result = []

    result
end

def _reduce_261(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_262(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

def _reduce_263(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_264(val, _values, result)
                      result = val[0] << @builder.splat(val[2], val[3])

    result
end

def _reduce_265(val, _values, result)
                      result = @builder.array(nil, val[0], nil)

    result
end

# reduce 266 omitted

def _reduce_267(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_268(val, _values, result)
                      result = val[0] << @builder.splat(val[2], val[3])

    result
end

def _reduce_269(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

# reduce 270 omitted

# reduce 271 omitted

# reduce 272 omitted

# reduce 273 omitted

# reduce 274 omitted

# reduce 275 omitted

# reduce 276 omitted

# reduce 277 omitted

# reduce 278 omitted

# reduce 279 omitted

def _reduce_280(val, _values, result)
                      result = @builder.call_method(nil, nil, val[0])

    result
end

def _reduce_281(val, _values, result)
                      result = @lexer.cmdarg.dup
                      @lexer.cmdarg.clear

    result
end

def _reduce_282(val, _values, result)
                      @lexer.cmdarg = val[1]

                      result = @builder.begin_keyword(val[0], val[2], val[3])

    result
end

def _reduce_283(val, _values, result)
                      result = @lexer.cmdarg.dup
                      @lexer.cmdarg.clear

    result
end

def _reduce_284(val, _values, result)
                      @lexer.state = :expr_endarg

    result
end

def _reduce_285(val, _values, result)
                      @lexer.cmdarg = val[1]

                      result = @builder.begin(val[0], val[2], val[5])

    result
end

def _reduce_286(val, _values, result)
                      @lexer.state = :expr_endarg

    result
end

def _reduce_287(val, _values, result)
                      result = @builder.begin(val[0], nil, val[3])

    result
end

def _reduce_288(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_289(val, _values, result)
                      result = @builder.const_fetch(val[0], val[1], val[2])

    result
end

def _reduce_290(val, _values, result)
                      result = @builder.const_global(val[0], val[1])

    result
end

def _reduce_291(val, _values, result)
                      result = @builder.array(val[0], val[1], val[2])

    result
end

def _reduce_292(val, _values, result)
                      result = @builder.associate(val[0], val[1], val[2])

    result
end

def _reduce_293(val, _values, result)
                      result = @builder.keyword_cmd(:return, val[0])

    result
end

def _reduce_294(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0], val[1], val[2], val[3])

    result
end

def _reduce_295(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0], val[1], [], val[2])

    result
end

def _reduce_296(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0])

    result
end

def _reduce_297(val, _values, result)
                      result = @builder.keyword_cmd(:defined?, val[0],
                                                    val[2], [ val[3] ], val[4])

    result
end

def _reduce_298(val, _values, result)
                      result = @builder.not_op(val[0], val[1], val[2], val[3])

    result
end

def _reduce_299(val, _values, result)
                      result = @builder.not_op(val[0], val[1], nil, val[2])

    result
end

def _reduce_300(val, _values, result)
                      method_call = @builder.call_method(nil, nil, val[0])

                      begin_t, args, body, end_t = val[1]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

# reduce 301 omitted

def _reduce_302(val, _values, result)
                      begin_t, args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, args, body, end_t)

    result
end

def _reduce_303(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_304(val, _values, result)
                      lambda_call = @builder.call_lambda(val[0])

                      args, (begin_t, body, end_t) = val[2]
                      result      = @builder.block(lambda_call,
                                      begin_t, args, body, end_t)

                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_305(val, _values, result)
                      else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  val[3], else_t,
                                                  else_,  val[5])

    result
end

def _reduce_306(val, _values, result)
                      else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  else_,  else_t,
                                                  val[3], val[5])

    result
end

def _reduce_307(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_308(val, _values, result)
                      @lexer.cond.pop

    result
end

def _reduce_309(val, _values, result)
                      result = @builder.loop(:while, val[0], val[2], val[3],
                                             val[5], val[6])

    result
end

def _reduce_310(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_311(val, _values, result)
                      @lexer.cond.pop

    result
end

def _reduce_312(val, _values, result)
                      result = @builder.loop(:until, val[0], val[2], val[3],
                                             val[5], val[6])

    result
end

def _reduce_313(val, _values, result)
                      *when_bodies, (else_t, else_body) = *val[3]

                      result = @builder.case(val[0], val[1],
                                             when_bodies, else_t, else_body,
                                             val[4])

    result
end

def _reduce_314(val, _values, result)
                      *when_bodies, (else_t, else_body) = *val[2]

                      result = @builder.case(val[0], nil,
                                             when_bodies, else_t, else_body,
                                             val[3])

    result
end

def _reduce_315(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_316(val, _values, result)
                      @lexer.cond.pop

    result
end

def _reduce_317(val, _values, result)
                      result = @builder.for(val[0], val[1],
                                            val[2], val[4],
                                            val[5], val[7], val[8])

    result
end

def _reduce_318(val, _values, result)
                      local_push
                      @context.in_class = true

    result
end

def _reduce_319(val, _values, result)
                      k_class, ctx = val[0]
                      if @context.in_def
                        diagnostic :error, :class_in_def, nil, k_class
                      end

                      lt_t, superclass = val[2]
                      result = @builder.def_class(k_class, val[1],
                                                  lt_t, superclass,
                                                  val[4], val[5])

                      local_pop
                      @context.in_class = ctx.in_class

    result
end

def _reduce_320(val, _values, result)
                      @context.in_def = false
                      @context.in_class = false
                      local_push

    result
end

def _reduce_321(val, _values, result)
                      k_class, ctx = val[0]
                      result = @builder.def_sclass(k_class, val[1], val[2],
                                                   val[5], val[6])

                      local_pop
                      @context.in_def = ctx.in_def
                      @context.in_class = ctx.in_class

    result
end

def _reduce_322(val, _values, result)
                      @context.in_class = true
                      local_push

    result
end

def _reduce_323(val, _values, result)
                      k_mod, ctx = val[0]
                      if @context.in_def
                        diagnostic :error, :module_in_def, nil, k_mod
                      end

                      result = @builder.def_module(k_mod, val[1],
                                                   val[3], val[4])

                      local_pop
                      @context.in_class = ctx.in_class

    result
end

def _reduce_324(val, _values, result)
                      local_push
                      result = context.dup
                      @context.in_def = true

    result
end

def _reduce_325(val, _values, result)
                      result = @builder.def_method(val[0], val[1],
                                  val[3], val[4], val[5])

                      local_pop
                      @context.in_def = val[2].in_def

    result
end

def _reduce_326(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_327(val, _values, result)
                      local_push
                      result = context.dup
                      @context.in_def = true

    result
end

def _reduce_328(val, _values, result)
                      result = @builder.def_singleton(val[0], val[1], val[2],
                                  val[4], val[6], val[7], val[8])

                      local_pop
                      @context.in_def = val[5].in_def

    result
end

def _reduce_329(val, _values, result)
                      result = @builder.keyword_cmd(:break, val[0])

    result
end

def _reduce_330(val, _values, result)
                      result = @builder.keyword_cmd(:next, val[0])

    result
end

def _reduce_331(val, _values, result)
                      result = @builder.keyword_cmd(:redo, val[0])

    result
end

def _reduce_332(val, _values, result)
                      result = @builder.keyword_cmd(:retry, val[0])

    result
end

# reduce 333 omitted

def _reduce_334(val, _values, result)
                      result = [ val[0], @context.dup ]

    result
end

def _reduce_335(val, _values, result)
                      result = [ val[0], @context.dup ]

    result
end

# reduce 336 omitted

# reduce 337 omitted

def _reduce_338(val, _values, result)
                      result = val[1]

    result
end

# reduce 339 omitted

# reduce 340 omitted

# reduce 341 omitted

def _reduce_342(val, _values, result)
                      else_t, else_ = val[4]
                      result = [ val[0],
                                 @builder.condition(val[0], val[1], val[2],
                                                    val[3], else_t,
                                                    else_,  nil),
                               ]

    result
end

# reduce 343 omitted

def _reduce_344(val, _values, result)
                      result = val

    result
end

# reduce 345 omitted

# reduce 346 omitted

def _reduce_347(val, _values, result)
                      result = @builder.arg(val[0])

    result
end

def _reduce_348(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

def _reduce_349(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_350(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 351 omitted

def _reduce_352(val, _values, result)
                      result = val[0].
                                  push(@builder.restarg(val[2], val[3]))

    result
end

def _reduce_353(val, _values, result)
                      result = val[0].
                                  push(@builder.restarg(val[2], val[3])).
                                  concat(val[5])

    result
end

def _reduce_354(val, _values, result)
                      result = val[0].
                                  push(@builder.restarg(val[2]))

    result
end

def _reduce_355(val, _values, result)
                      result = val[0].
                                  push(@builder.restarg(val[2])).
                                  concat(val[4])

    result
end

def _reduce_356(val, _values, result)
                      result = [ @builder.restarg(val[0], val[1]) ]

    result
end

def _reduce_357(val, _values, result)
                      result = [ @builder.restarg(val[0], val[1]),
                                 *val[3] ]

    result
end

def _reduce_358(val, _values, result)
                      result = [ @builder.restarg(val[0]) ]

    result
end

def _reduce_359(val, _values, result)
                      result = [ @builder.restarg(val[0]),
                                 *val[2] ]

    result
end

def _reduce_360(val, _values, result)
                      result = val[0].concat(val[2]).concat(val[3])

    result
end

def _reduce_361(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_362(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_363(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_364(val, _values, result)
                      result = val[1]

    result
end

def _reduce_365(val, _values, result)
                      result = []

    result
end

def _reduce_366(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_367(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[6]).
                                  concat(val[7])

    result
end

def _reduce_368(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_369(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_370(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

# reduce 371 omitted

def _reduce_372(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_373(val, _values, result)
                      if val[1].empty? && val[0].size == 1
                        result = [@builder.procarg0(val[0][0])]
                      else
                        result = val[0].concat(val[1])
                      end

    result
end

def _reduce_374(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_375(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_376(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_377(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_378(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_379(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

# reduce 380 omitted

def _reduce_381(val, _values, result)
                      result = @builder.args(nil, [], nil)

    result
end

def _reduce_382(val, _values, result)
                      @lexer.state = :expr_value

    result
end

def _reduce_383(val, _values, result)
                      result = @builder.args(val[0], val[1], val[2])

    result
end

def _reduce_384(val, _values, result)
                      result = @builder.args(val[0], [], val[0])

    result
end

def _reduce_385(val, _values, result)
                      result = @builder.args(val[0], val[1].concat(val[2]), val[3])

    result
end

def _reduce_386(val, _values, result)
                      result = []

    result
end

def _reduce_387(val, _values, result)
                      result = val[2]

    result
end

def _reduce_388(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_389(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_390(val, _values, result)
                      @static_env.declare val[0][0]
                      result = @builder.shadowarg(val[0])

    result
end

# reduce 391 omitted

def _reduce_392(val, _values, result)
                      @static_env.extend_dynamic

    result
end

def _reduce_393(val, _values, result)
                      result = @lexer.cmdarg.dup
                      @lexer.cmdarg.clear

    result
end

def _reduce_394(val, _values, result)
                      @lexer.cmdarg = val[2]
                      @lexer.cmdarg.lexpop

                      result = [ val[1], val[3] ]

                      @static_env.unextend

    result
end

def _reduce_395(val, _values, result)
                      result = @builder.args(val[0], val[1].concat(val[2]), val[3])

    result
end

def _reduce_396(val, _values, result)
                      result = @builder.args(nil, val[0], nil)

    result
end

def _reduce_397(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_398(val, _values, result)
                      result = [ val[0], val[2], val[3] ]
                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_399(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_400(val, _values, result)
                      result = [ val[0], val[2], val[3] ]
                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_401(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_402(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_403(val, _values, result)
                      begin_t, block_args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, block_args, body, end_t)

    result
end

def _reduce_404(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_405(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                      lparen_t, args, rparen_t)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_406(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                      nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_407(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.call_method(nil, nil, val[0],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_408(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_409(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_410(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2])

    result
end

def _reduce_411(val, _values, result)
                      lparen_t, args, rparen_t = val[2]
                      result = @builder.call_method(val[0], val[1], nil,
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_412(val, _values, result)
                      lparen_t, args, rparen_t = val[2]
                      result = @builder.call_method(val[0], val[1], nil,
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_413(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.keyword_cmd(:super, val[0],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_414(val, _values, result)
                      result = @builder.keyword_cmd(:zsuper, val[0])

    result
end

def _reduce_415(val, _values, result)
                      result = @builder.index(val[0], val[1], val[2], val[3])

    result
end

def _reduce_416(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_417(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_418(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_419(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_420(val, _values, result)
                      result = [ @builder.when(val[0], val[1], val[2], val[3]),
                                 *val[4] ]

    result
end

def _reduce_421(val, _values, result)
                      result = [ val[0] ]

    result
end

# reduce 422 omitted

def _reduce_423(val, _values, result)
                      assoc_t, exc_var = val[2]

                      if val[1]
                        exc_list = @builder.array(nil, val[1], nil)
                      end

                      result = [ @builder.rescue_body(val[0],
                                      exc_list, assoc_t, exc_var,
                                      val[3], val[4]),
                                 *val[5] ]

    result
end

def _reduce_424(val, _values, result)
                      result = []

    result
end

def _reduce_425(val, _values, result)
                      result = [ val[0] ]

    result
end

# reduce 426 omitted

# reduce 427 omitted

def _reduce_428(val, _values, result)
                      result = [ val[0], val[1] ]

    result
end

# reduce 429 omitted

def _reduce_430(val, _values, result)
                      result = [ val[0], val[1] ]

    result
end

# reduce 431 omitted

# reduce 432 omitted

# reduce 433 omitted

# reduce 434 omitted

def _reduce_435(val, _values, result)
                      result = @builder.string_compose(nil, val[0], nil)

    result
end

def _reduce_436(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_437(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_438(val, _values, result)
                      result = @builder.string_compose(val[0], val[1], val[2])

    result
end

def _reduce_439(val, _values, result)
                      result = @builder.string(val[0])

    result
end

def _reduce_440(val, _values, result)
                      result = @builder.character(val[0])

    result
end

def _reduce_441(val, _values, result)
                      result = @builder.xstring_compose(val[0], val[1], val[2])

    result
end

def _reduce_442(val, _values, result)
                      opts   = @builder.regexp_options(val[3])
                      result = @builder.regexp_compose(val[0], val[1], val[2], opts)

    result
end

def _reduce_443(val, _values, result)
                      result = @builder.words_compose(val[0], val[1], val[2])

    result
end

def _reduce_444(val, _values, result)
                      result = []

    result
end

def _reduce_445(val, _values, result)
                      result = val[0] << @builder.word(val[1])

    result
end

def _reduce_446(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_447(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_448(val, _values, result)
                      result = @builder.symbols_compose(val[0], val[1], val[2])

    result
end

def _reduce_449(val, _values, result)
                      result = []

    result
end

def _reduce_450(val, _values, result)
                      result = val[0] << @builder.word(val[1])

    result
end

def _reduce_451(val, _values, result)
                      result = @builder.words_compose(val[0], val[1], val[2])

    result
end

def _reduce_452(val, _values, result)
                      result = @builder.symbols_compose(val[0], val[1], val[2])

    result
end

def _reduce_453(val, _values, result)
                      result = []

    result
end

def _reduce_454(val, _values, result)
                      result = val[0] << @builder.string_internal(val[1])

    result
end

def _reduce_455(val, _values, result)
                      result = []

    result
end

def _reduce_456(val, _values, result)
                      result = val[0] << @builder.symbol_internal(val[1])

    result
end

def _reduce_457(val, _values, result)
                      result = []

    result
end

def _reduce_458(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_459(val, _values, result)
                      result = []

    result
end

def _reduce_460(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_461(val, _values, result)
                      result = []

    result
end

def _reduce_462(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_463(val, _values, result)
                      result = @builder.string_internal(val[0])

    result
end

def _reduce_464(val, _values, result)
                      result = val[1]

    result
end

def _reduce_465(val, _values, result)
                      @lexer.cond.push(false)
                      @lexer.cmdarg.push(false)

    result
end

def _reduce_466(val, _values, result)
                      @lexer.cond.lexpop
                      @lexer.cmdarg.lexpop

                      result = @builder.begin(val[0], val[2], val[3])

    result
end

def _reduce_467(val, _values, result)
                      result = @builder.gvar(val[0])

    result
end

def _reduce_468(val, _values, result)
                      result = @builder.ivar(val[0])

    result
end

def _reduce_469(val, _values, result)
                      result = @builder.cvar(val[0])

    result
end

# reduce 470 omitted

def _reduce_471(val, _values, result)
                      result = @builder.symbol(val[0])

    result
end

def _reduce_472(val, _values, result)
                      result = @builder.symbol_compose(val[0], val[1], val[2])

    result
end

def _reduce_473(val, _values, result)
                      result = val[0]

    result
end

def _reduce_474(val, _values, result)
                      if @builder.respond_to? :negate
                        # AST builder interface compatibility
                        result = @builder.negate(val[0], val[1])
                      else
                        result = @builder.unary_num(val[0], val[1])
                      end

    result
end

def _reduce_475(val, _values, result)
                      result = @builder.integer(val[0])

    result
end

def _reduce_476(val, _values, result)
                      result = @builder.float(val[0])

    result
end

def _reduce_477(val, _values, result)
                      result = @builder.rational(val[0])

    result
end

def _reduce_478(val, _values, result)
                      result = @builder.complex(val[0])

    result
end

def _reduce_479(val, _values, result)
                      result = @builder.ident(val[0])

    result
end

def _reduce_480(val, _values, result)
                      result = @builder.ivar(val[0])

    result
end

def _reduce_481(val, _values, result)
                      result = @builder.gvar(val[0])

    result
end

def _reduce_482(val, _values, result)
                      result = @builder.const(val[0])

    result
end

def _reduce_483(val, _values, result)
                      result = @builder.cvar(val[0])

    result
end

def _reduce_484(val, _values, result)
                      result = @builder.nil(val[0])

    result
end

def _reduce_485(val, _values, result)
                      result = @builder.self(val[0])

    result
end

def _reduce_486(val, _values, result)
                      result = @builder.true(val[0])

    result
end

def _reduce_487(val, _values, result)
                      result = @builder.false(val[0])

    result
end

def _reduce_488(val, _values, result)
                      result = @builder.__FILE__(val[0])

    result
end

def _reduce_489(val, _values, result)
                      result = @builder.__LINE__(val[0])

    result
end

def _reduce_490(val, _values, result)
                      result = @builder.__ENCODING__(val[0])

    result
end

def _reduce_491(val, _values, result)
                      result = @builder.accessible(val[0])

    result
end

def _reduce_492(val, _values, result)
                      result = @builder.accessible(val[0])

    result
end

def _reduce_493(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_494(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_495(val, _values, result)
                      result = @builder.nth_ref(val[0])

    result
end

def _reduce_496(val, _values, result)
                      result = @builder.back_ref(val[0])

    result
end

def _reduce_497(val, _values, result)
                      result = nil

    result
end

def _reduce_498(val, _values, result)
                      @lexer.state = :expr_value

    result
end

def _reduce_499(val, _values, result)
                      result = [ val[0], val[2] ]

    result
end

def _reduce_500(val, _values, result)
                      yyerrok
                      result = nil

    result
end

def _reduce_501(val, _values, result)
                      result = @builder.args(val[0], val[1], val[2])

                      @lexer.state = :expr_value

    result
end

def _reduce_502(val, _values, result)
                      result = @context.in_kwarg
                      @context.in_kwarg = true

    result
end

def _reduce_503(val, _values, result)
                      @context.in_kwarg = val[0]
                      result = @builder.args(nil, val[1], nil)

    result
end

def _reduce_504(val, _values, result)
                      result = val[0].concat(val[2]).concat(val[3])

    result
end

def _reduce_505(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_506(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_507(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_508(val, _values, result)
                      result = val[1]

    result
end

def _reduce_509(val, _values, result)
                      result = []

    result
end

def _reduce_510(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_511(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[6]).
                                  concat(val[7])

    result
end

def _reduce_512(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_513(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_514(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_515(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_516(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_517(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_518(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_519(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_520(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_521(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_522(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_523(val, _values, result)
                      result = val[0]

    result
end

def _reduce_524(val, _values, result)
                      result = []

    result
end

def _reduce_525(val, _values, result)
                      diagnostic :error, :argument_const, nil, val[0]

    result
end

def _reduce_526(val, _values, result)
                      diagnostic :error, :argument_ivar, nil, val[0]

    result
end

def _reduce_527(val, _values, result)
                      diagnostic :error, :argument_gvar, nil, val[0]

    result
end

def _reduce_528(val, _values, result)
                      diagnostic :error, :argument_cvar, nil, val[0]

    result
end

# reduce 529 omitted

def _reduce_530(val, _values, result)
                      @static_env.declare val[0][0]

                      result = val[0]

    result
end

def _reduce_531(val, _values, result)
                      result = @builder.arg(val[0])

    result
end

def _reduce_532(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

def _reduce_533(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_534(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_535(val, _values, result)
                      check_kwarg_name(val[0])

                      @static_env.declare val[0][0]

                      result = val[0]

    result
end

def _reduce_536(val, _values, result)
                      result = @builder.kwoptarg(val[0], val[1])

    result
end

def _reduce_537(val, _values, result)
                      result = @builder.kwarg(val[0])

    result
end

def _reduce_538(val, _values, result)
                      result = @builder.kwoptarg(val[0], val[1])

    result
end

def _reduce_539(val, _values, result)
                      result = @builder.kwarg(val[0])

    result
end

def _reduce_540(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_541(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_542(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_543(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 544 omitted

# reduce 545 omitted

def _reduce_546(val, _values, result)
                      @static_env.declare val[1][0]

                      result = [ @builder.kwrestarg(val[0], val[1]) ]

    result
end

def _reduce_547(val, _values, result)
                      result = [ @builder.kwrestarg(val[0]) ]

    result
end

def _reduce_548(val, _values, result)
                      result = @builder.optarg(val[0], val[1], val[2])

    result
end

def _reduce_549(val, _values, result)
                      result = @builder.optarg(val[0], val[1], val[2])

    result
end

def _reduce_550(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_551(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_552(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_553(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 554 omitted

# reduce 555 omitted

def _reduce_556(val, _values, result)
                      @static_env.declare val[1][0]

                      result = [ @builder.restarg(val[0], val[1]) ]

    result
end

def _reduce_557(val, _values, result)
                      result = [ @builder.restarg(val[0]) ]

    result
end

# reduce 558 omitted

# reduce 559 omitted

def _reduce_560(val, _values, result)
                      @static_env.declare val[1][0]

                      result = @builder.blockarg(val[0], val[1])

    result
end

def _reduce_561(val, _values, result)
                      result = [ val[1] ]

    result
end

def _reduce_562(val, _values, result)
                      result = []

    result
end

# reduce 563 omitted

def _reduce_564(val, _values, result)
                      result = val[1]

    result
end

def _reduce_565(val, _values, result)
                      result = []

    result
end

# reduce 566 omitted

def _reduce_567(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_568(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_569(val, _values, result)
                      result = @builder.pair(val[0], val[1], val[2])

    result
end

def _reduce_570(val, _values, result)
                      result = @builder.pair_keyword(val[0], val[1])

    result
end

def _reduce_571(val, _values, result)
                      result = @builder.kwsplat(val[0], val[1])

    result
end

# reduce 572 omitted

# reduce 573 omitted

# reduce 574 omitted

# reduce 575 omitted

# reduce 576 omitted

# reduce 577 omitted

# reduce 578 omitted

# reduce 579 omitted

# reduce 580 omitted

# reduce 581 omitted

# reduce 582 omitted

# reduce 583 omitted

# reduce 584 omitted

# reduce 585 omitted

# reduce 586 omitted

# reduce 587 omitted

def _reduce_588(val, _values, result)
                      result = val[1]

    result
end

def _reduce_589(val, _values, result)
                      result = val[1]

    result
end

# reduce 590 omitted

# reduce 591 omitted

# reduce 592 omitted

def _reduce_593(val, _values, result)
                    yyerrok

    result
end

# reduce 594 omitted

# reduce 595 omitted

# reduce 596 omitted

def _reduce_597(val, _values, result)
                    result = nil

    result
end

def _reduce_none(val, _values, result)
  val[0]
end

  end   # class Ruby21
end   # module Parser
