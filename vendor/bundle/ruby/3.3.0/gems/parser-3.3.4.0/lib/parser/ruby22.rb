# -*- encoding:utf-8; warn-indent:false; frozen_string_literal: true  -*-
#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.7.3
# from Racc grammar file "ruby22.y".
#

require 'racc/parser.rb'


require_relative '../parser'

module Parser
  class Ruby22 < Parser::Base


  def version
    22
  end

  def default_encoding
    Encoding::UTF_8
  end

  def local_push
    @static_env.extend_static
    @lexer.cmdarg.push(false)
    @lexer.cond.push(false)
  end

  def local_pop
    @static_env.unextend
    @lexer.cmdarg.pop
    @lexer.cond.pop
  end
##### State transition tables begin ###

racc_action_table = [
  -479,   111,   270,   216,   217,   -97,   -98,  -479,  -479,  -479,
   219,   538,  -479,  -479,  -479,  -286,  -479,   270,   617,   580,
   -83,   123,   216,   217,  -479,   582,  -479,  -479,  -479,   -69,
   270,   786,   208,   544,  -105,   559,  -479,  -479,   538,  -479,
  -479,  -479,  -479,  -479,   538,   538,   216,   217,   538,  -104,
  -100,   220,  -100,   616,  -102,  -286,   216,   217,   270,   550,
   216,   217,  -493,   551,  -102,   872,  -479,  -479,  -479,  -479,
  -479,  -479,  -479,  -479,  -479,  -479,  -479,  -479,  -479,  -479,
   209,   265,  -479,  -479,  -479,   579,  -479,  -479,   695,   269,
  -479,   581,   262,  -479,  -479,   -99,  -479,   220,  -479,   263,
  -479,  -494,  -479,  -479,   269,  -479,  -479,  -479,  -479,  -479,
   -99,  -479,  -482,  -479,   -88,   -89,   220,   269,   -97,  -482,
  -482,  -482,   265,  -101,  -482,  -482,  -482,  -479,  -482,   115,
  -479,  -479,  -479,  -479,   114,  -479,  -482,  -479,  -482,  -482,
  -482,   558,  -479,   -96,   695,   269,  -101,   695,  -482,  -482,
   220,  -482,  -482,  -482,  -482,  -482,   115,   -98,   -95,   -91,
   -91,   114,   115,   115,   115,   823,   115,   114,   114,   114,
  -100,   114,  -102,   -93,   871,  -100,   445,  -102,  -482,  -482,
  -482,  -482,  -482,  -482,  -482,  -482,  -482,  -482,  -482,  -482,
  -482,  -482,   115,   210,  -482,  -482,  -482,   114,  -482,  -482,
  -577,   -93,  -482,   215,   -90,  -482,  -482,  -103,  -482,   259,
  -482,  -577,  -482,   318,  -482,  -482,  -574,  -482,  -482,  -482,
  -482,  -482,  -289,  -482,   319,  -482,   -91,  -479,   -99,  -289,
  -289,  -289,  -105,   -99,  -479,  -289,  -289,   528,  -289,  -482,
   527,  -101,  -482,  -482,  -482,  -482,  -101,  -482,   115,  -482,
  -575,   115,   617,   114,  -482,   -92,   114,   528,  -289,  -289,
   530,  -289,  -289,  -289,  -289,  -289,   -91,   -93,  -479,   -91,
   214,   220,  -578,   216,   217,  -479,  -493,   387,   -91,   672,
  -574,   669,   668,   667,  -479,   670,   400,   616,  -289,  -289,
  -289,  -289,  -289,  -289,  -289,  -289,  -289,  -289,  -289,  -289,
  -289,  -289,  -482,  -574,  -289,  -289,  -289,   -93,   600,  -482,
   -93,   617,  -289,  -494,  -575,  -289,   -94,   -96,  -482,   -93,
  -289,   567,  -289,   -88,  -289,  -289,  -105,  -289,  -289,  -289,
  -289,  -289,   -97,  -289,  -581,  -289,   528,  -575,   516,   530,
   750,  -581,  -581,  -581,    93,    94,   616,  -581,  -581,  -289,
  -581,  -482,  -289,  -289,   617,   -94,   444,  -289,  -482,  -581,
   -89,   890,  -581,   751,  -103,   528,   446,   115,   530,   -98,
  -581,  -581,   114,  -581,  -581,  -581,  -581,  -581,   672,   447,
   669,   668,   667,   219,   670,   569,   568,   567,   516,   616,
   567,   478,    93,    94,  -104,   727,  -100,   487,   567,   489,
  -581,  -581,  -581,  -581,  -581,  -581,  -581,  -581,  -581,  -581,
  -581,  -581,  -581,  -581,  -581,   491,  -581,  -581,  -581,  -102,
   601,  -581,    95,    96,  -581,   499,  -577,  -581,   841,  -415,
  -581,  -581,  -581,   115,  -581,   -95,  -581,  -581,   114,  -581,
  -581,  -581,  -581,  -581,  -104,  -581,  -581,  -581,   567,  -581,
   -68,   569,   568,   565,   569,   568,   565,   567,   216,   217,
   987,  -581,   569,   568,  -581,  -581,  -581,   -92,   632,  -581,
    95,    96,   729,  -581,  -581,  -581,  -101,   220,  -581,  -581,
  -581,  -415,  -581,  -581,   -99,   818,   786,   502,  -415,  -489,
  -581,  -581,  -581,  -581,  -581,  -577,  -489,  -415,   220,  -581,
   503,   972,  -581,  -581,   510,  -581,  -581,  -581,  -581,  -581,
   115,   595,   569,   568,   570,   114,  -415,   567,  -581,  -488,
   567,   569,   568,   572,   567,   397,  -488,   688,   687,   274,
   399,   398,  -581,  -581,  -581,  -581,  -581,  -581,  -581,  -581,
  -581,  -581,  -581,  -581,  -581,  -581,   115,   212,  -581,  -581,
  -581,   114,   752,  -581,   213,   240,  -581,   596,   -91,  -581,
  -581,   220,  -581,   211,  -581,   736,  -581,  -100,  -581,  -581,
   265,  -581,  -581,  -581,  -581,  -581,   513,  -581,  -581,  -581,
   517,   569,   568,   574,   569,   568,   578,   237,   569,   568,
   583,   239,   238,  -581,   235,   236,  -581,  -581,  -581,  -581,
   240,  -581,  -289,  -581,   -93,   818,   786,   677,  -101,  -289,
  -289,  -289,   -90,  -102,  -289,  -289,  -289,   680,  -289,   240,
   672,   -99,   669,   668,   667,   220,   670,   240,  -289,  -289,
  -289,   531,   237,  -333,    81,   532,   239,   238,  -289,  -289,
  -333,  -289,  -289,  -289,  -289,  -289,    82,  -490,   491,  -333,
   688,   687,   544,  -487,  -490,   681,    83,   807,  -484,   237,
  -487,   389,   115,   239,   238,  -484,   810,   114,  -289,  -289,
  -289,  -289,  -289,  -289,  -289,  -289,  -289,  -289,  -289,  -289,
  -289,  -289,   548,   677,  -289,  -289,  -289,   441,   753,  -289,
  -491,   549,  -289,   680,   442,  -289,  -289,  -491,  -289,   584,
  -289,   587,  -289,   443,  -289,  -289,  -491,  -289,  -289,  -289,
  -289,  -289,  -261,  -289,   672,  -289,   669,   668,   667,   672,
   670,   669,   668,   667,   589,   670,   688,   687,   220,  -289,
   593,   681,  -289,  -289,  -289,  -289,  -492,  -289,   594,  -289,
   265,   604,   607,  -492,  -103,     5,    74,    75,    71,     9,
    57,   807,  -492,   115,    63,    64,   807,   240,   114,    67,
   810,    65,    66,    68,    30,    31,    72,    73,   118,   119,
   120,   121,   122,    29,    28,    27,   103,   102,   104,   105,
   721,   722,    19,   240,   723,   109,   110,   605,     8,    45,
     7,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   240,   101,   109,   110,   240,    93,    94,    42,    43,    41,
   240,   244,   249,   250,   251,   246,   248,   256,   257,   252,
   253,   508,   233,   234,  -279,   115,   254,   255,   509,    40,
   114,  -279,    33,   220,   220,    58,    59,   507,   220,    60,
  -279,    35,   237,   -83,   243,    44,   239,   238,   636,   235,
   236,   247,   245,   241,    20,   242,   220,   521,   647,    91,
    81,    84,    85,   518,    86,    88,    87,    89,   652,  -485,
   519,  -486,    82,    90,   262,   258,  -485,  -238,  -486,   443,
    62,   263,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   653,   655,   546,    63,    64,   691,   544,   698,    67,
   547,    65,    66,    68,    30,    31,    72,    73,   716,   545,
   726,   730,   731,    29,    28,    27,   103,   102,   104,   105,
  -262,   737,    19,   478,   478,   220,   755,   590,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   259,   101,   109,   110,   489,    93,    94,    42,    43,    41,
   240,   244,   249,   250,   251,   246,   248,   256,   257,   252,
   253,   554,   233,   234,  -290,   491,   254,   255,   553,    40,
   779,  -290,   296,   647,   220,    58,    59,   555,   265,    60,
  -290,    35,   237,   265,   243,    44,   239,   238,   647,   235,
   236,   247,   245,   241,    20,   242,   240,   786,   220,    91,
    81,    84,    85,  -290,    86,    88,    87,    89,   795,   798,
  -290,   799,    82,    90,   801,   258,   803,   805,   813,  -290,
    62,   814,    83,    95,    96,     5,    74,    75,    71,     9,
    57,   815,   786,   554,    63,    64,   822,   220,   220,    67,
   923,    65,    66,    68,    30,    31,    72,    73,   831,   555,
  -263,   840,   843,    29,    28,    27,   103,   102,   104,   105,
   798,   846,    19,   848,   850,   852,   220,   605,     8,    45,
     7,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   854,   101,   109,   110,   855,    93,    94,    42,    43,    41,
   240,   244,   249,   250,   251,   246,   248,   256,   257,   252,
   253,  -289,   233,   234,   554,   858,   254,   255,  -289,    40,
   860,   923,    33,  -578,   861,    58,    59,  -289,   647,    60,
   555,    35,   237,   863,   243,    44,   239,   238,  -261,   235,
   236,   247,   245,   241,    20,   242,   867,   869,   220,    91,
    81,    84,    85,  -491,    86,    88,    87,    89,   888,   220,
  -491,   892,    82,    90,   894,   258,   900,   903,   220,  -491,
    62,   907,    83,    95,    96,   292,    74,    75,    71,     9,
    57,  -264,   917,  -492,    63,    64,   924,   925,   936,    67,
  -492,    65,    66,    68,    30,    31,    72,    73,   798,  -492,
   938,   940,   942,    29,    28,    27,   103,   102,   104,   105,
   944,   920,    19,   669,   668,   667,   944,   670,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   220,   101,   109,   110,   950,    93,    94,    42,    43,    41,
   240,   244,   249,   250,   251,   246,   248,   256,   257,   252,
   253,  -289,   233,   234,   977,   953,   254,   255,  -289,    40,
   954,   978,    33,  -578,   959,    58,    59,  -289,   716,    60,
   976,    35,   237,   798,   243,    44,   239,   238,   962,   235,
   236,   247,   245,   241,    20,   242,   964,   966,   968,    91,
    81,    84,    85,  -279,    86,    88,    87,    89,   968,   979,
  -279,   989,    82,    90,  -578,   258,  -577,   652,  1004,  -279,
    62,  1005,    83,    95,    96,   292,    74,    75,    71,     9,
    57,  1006,   944,  -290,    63,    64,   944,   944,  1011,    67,
  -290,    65,    66,    68,    30,    31,    72,    73,   989,  -290,
  1014,  1015,  1016,    29,    28,    27,   103,   102,   104,   105,
   968,   920,    19,   669,   668,   667,   968,   670,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   968,   101,   109,   110,   220,    93,    94,    42,    43,    41,
   240,   244,   249,   250,   251,   246,   248,   256,   257,   252,
   253,  -289,   233,   234,   989,   944,   254,   255,  -289,    40,
   989,   968,    33,  -578,   nil,    58,    59,  -289,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   247,   245,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   258,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   118,   119,
   120,   121,   122,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   118,   119,   120,   121,   122,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,   244,   249,   250,   251,   246,   248,   256,   257,   252,
   253,   nil,   233,   234,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,   296,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   247,   245,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   258,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,   244,   249,   250,   251,   246,   248,   256,   257,   252,
   253,   nil,   233,   234,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,   296,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   247,   245,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   220,   258,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,   244,   249,   250,   251,   246,   248,   256,   257,   252,
   253,   nil,   233,   234,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   247,   245,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   258,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,     5,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
     7,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,   244,   249,   250,   251,   246,   248,   256,   257,   252,
   253,   nil,   233,   234,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   247,   245,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   258,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,   244,   249,   250,   251,   246,   248,   256,   257,   252,
   253,   nil,   233,   234,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   247,   245,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   258,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,   244,   249,   250,   251,   246,   248,   256,   257,   252,
   253,   nil,   233,   234,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   247,   245,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   258,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,   244,   249,   250,   251,   246,   248,   256,   257,   252,
   253,   nil,   233,   234,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   247,   245,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   258,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,   244,   249,   250,   251,   246,   248,   256,   257,   252,
   253,   nil,   233,   234,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   247,   245,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   258,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,   244,   249,   250,   251,   246,   248,   256,   257,   252,
   253,   nil,   233,   234,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   247,   245,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   258,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,   244,   249,   250,   251,   246,   248,   256,   257,   252,
   253,   nil,   233,   234,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   247,   245,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   258,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,   244,   249,   250,   251,   246,   248,   256,   257,   252,
   253,   nil,   233,   234,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   247,   245,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   258,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,   244,   249,   250,   251,   246,   248,   256,   257,   252,
   253,   nil,   233,   234,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   247,   245,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   258,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   240,   672,    19,   669,   668,   667,   nil,   670,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   237,   nil,   nil,   nil,   239,   238,   807,   235,
   236,   nil,   nil,   nil,   nil,   nil,   nil,   949,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   672,    35,   669,   668,   667,    44,   670,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,   807,   nil,   nil,
    62,   nil,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,   244,   249,   250,   251,   246,   248,   256,   257,   252,
   253,   nil,  -600,  -600,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   247,   245,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   672,   nil,   669,   668,   667,
    62,   670,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   807,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,   244,   249,   250,   251,   246,   248,   256,   257,   252,
   253,   nil,  -600,  -600,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   247,   245,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   672,   nil,   669,   668,   667,
    62,   670,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   807,    29,    28,    27,   103,   102,   104,   105,
   nil,   672,    19,   669,   668,   667,   nil,   670,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,  -600,  -600,  -600,  -600,   246,   248,   nil,   807,  -600,
  -600,   nil,   nil,   nil,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   247,   245,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   nil,   nil,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   nil,   nil,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   nil,   nil,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   nil,   nil,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,  -600,  -600,  -600,  -600,   246,   248,   nil,   nil,  -600,
  -600,   nil,   nil,   nil,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   247,   245,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,  -600,  -600,  -600,  -600,   246,   248,   nil,   nil,  -600,
  -600,   nil,   nil,   nil,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   247,   245,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,  -600,  -600,  -600,  -600,   246,   248,   nil,   nil,  -600,
  -600,   nil,   nil,   nil,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   247,   245,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,  -600,  -600,  -600,  -600,   246,   248,   nil,   nil,  -600,
  -600,   nil,   nil,   nil,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   247,   245,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,  -600,  -600,  -600,  -600,   246,   248,   nil,   nil,  -600,
  -600,   nil,   nil,   nil,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   247,   245,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,   292,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   294,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,   244,   249,   250,   251,   246,   248,   nil,   nil,   252,
   253,   nil,   nil,   nil,   nil,   nil,   254,   255,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   247,   245,   241,    20,   242,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,   nil,   nil,   nil,
    62,   nil,    83,    95,    96,    74,    75,    71,     9,    57,
   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,
   nil,   nil,    29,    28,    27,   103,   102,   104,   105,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,     7,
    10,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   240,
   244,   249,   250,   251,   246,   248,   256,   nil,   252,   253,
   nil,   nil,   nil,   nil,   nil,   254,   255,   nil,    40,   nil,
   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
    35,   237,   nil,   243,    44,   239,   238,   nil,   235,   236,
   247,   245,   241,    20,   242,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   240,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   254,   255,   nil,   225,
   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   237,   nil,   243,    44,   239,   238,   nil,   235,
   236,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,    30,    31,    72,    73,   nil,
   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,
   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   286,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   240,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   254,   255,   nil,
   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   283,   237,   281,   243,    44,   239,   238,   287,
   235,   236,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,
    91,   284,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,    30,    31,    72,    73,
   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,
   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   286,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   240,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   254,   255,
   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   283,   237,   281,   nil,    44,   239,   238,
   287,   235,   236,   nil,   nil,   nil,   230,   nil,   nil,   nil,
   nil,    91,   284,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   286,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   283,   nil,   281,   nil,    44,   nil,
   nil,   287,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   nil,   nil,    91,   284,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   311,   312,
    72,    73,   nil,   nil,   nil,   nil,   nil,   307,   308,   314,
   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,
   nil,   nil,   nil,   309,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,   nil,   nil,   315,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   305,   nil,   nil,   301,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   300,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   311,
   312,    72,    73,   nil,   nil,   nil,   nil,   nil,   307,   308,
   314,   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,   309,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,   nil,   nil,   315,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   305,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   672,   nil,   669,
   668,   667,   677,   670,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   680,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,   317,   nil,   675,    62,   nil,    83,    95,    96,
    74,    75,    71,   nil,    57,   688,   687,   nil,    63,    64,
   681,   nil,   nil,    67,   nil,    65,    66,    68,   311,   312,
    72,    73,   nil,   nil,   nil,   nil,   nil,   307,   308,   314,
   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   311,
   312,    72,    73,   nil,   nil,   nil,   nil,   nil,   307,   308,
   314,   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   311,   312,    72,    73,   nil,   nil,   nil,   nil,   nil,   307,
   308,   314,   103,   102,   104,   105,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   311,   312,    72,    73,   nil,   nil,   nil,   nil,   nil,
   307,   308,   314,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   286,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   283,   nil,
   nil,   nil,    44,   nil,   nil,   287,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   nil,   nil,    91,   284,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   311,   312,    72,    73,   nil,   nil,   nil,   nil,
   nil,   307,   308,   314,   103,   102,   104,   105,   nil,   nil,
   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   286,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,
   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   287,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,   284,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,
   nil,   nil,    29,    28,    27,   103,   102,   104,   105,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,
   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,    30,    31,    72,    73,   nil,
   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,
   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   115,   nil,   nil,   nil,   nil,
   114,    62,   nil,    83,    95,    96,    74,    75,    71,   nil,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   311,   312,    72,    73,   nil,   nil,
   nil,   nil,   nil,   307,   308,   314,   103,   102,   104,   105,
   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,   309,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,   nil,   nil,   315,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   349,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   311,   312,    72,    73,   nil,
   nil,   nil,   nil,   nil,   307,   308,   314,   103,   102,   104,
   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,
   309,   nil,   nil,   107,   106,   108,   354,    56,    99,    98,
   355,   nil,   101,   109,   110,   nil,    93,    94,   nil,   nil,
   315,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   361,   nil,   nil,
   356,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   311,   312,    72,    73,
   nil,   nil,   nil,   nil,   nil,   307,   308,   314,   103,   102,
   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,
   nil,   309,   nil,   nil,   107,   106,   108,   354,    56,    99,
    98,   355,   nil,   101,   109,   110,   nil,    93,    94,   nil,
   nil,   315,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   356,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   672,   nil,   669,   668,   667,   677,
   670,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   680,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,   nil,
   nil,   675,    62,   nil,    83,    95,    96,    74,    75,    71,
     9,    57,   688,   687,   nil,    63,    64,   681,   nil,   nil,
    67,   nil,    65,    66,    68,    30,    31,    72,    73,   nil,
   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,
   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,
    45,     7,    10,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    40,   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,    35,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,   nil,   nil,
   389,    62,   nil,    83,    95,    96,    74,    75,    71,   nil,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,
   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,    30,    31,    72,    73,   nil,
   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,
   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,    30,    31,    72,    73,
   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,
   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,    74,    75,
    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,    30,    31,    72,    73,
   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,
   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,
     8,    45,   nil,    10,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,    35,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   405,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,
    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,
    27,   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   286,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   283,   nil,   281,   nil,
    44,   nil,   nil,   287,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,    91,   284,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,
    28,    27,   103,   102,   104,   105,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,
    29,    28,    27,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   405,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,
   nil,    29,    28,    27,   103,   102,   104,   105,   nil,   nil,
    19,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,
   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,
   nil,   nil,    29,    28,    27,   103,   102,   104,   105,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,
   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,    30,    31,    72,    73,   nil,
   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,
   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   220,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   311,   312,    72,    73,
   nil,   nil,   nil,   nil,   nil,   307,   308,   314,   103,   102,
   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   311,   312,    72,
    73,   nil,   nil,   nil,   nil,   nil,   307,   308,   314,   103,
   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   311,   312,
    72,    73,   nil,   nil,   nil,   nil,   nil,   307,   308,   314,
   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   311,
   312,    72,    73,   nil,   nil,   nil,   nil,   nil,   307,   308,
   314,   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   311,   312,    72,    73,   nil,   nil,   nil,   nil,   nil,   307,
   308,   314,   103,   102,   104,   105,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   311,   312,    72,    73,   nil,   nil,   nil,   nil,   nil,
   307,   308,   314,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   311,   312,    72,    73,   nil,   nil,   nil,   nil,
   nil,   307,   308,   314,   103,   102,   104,   105,   nil,   nil,
   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,
   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   311,   312,    72,    73,   nil,   nil,   nil,
   nil,   nil,   307,   308,   314,   103,   102,   104,   105,   nil,
   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   311,   312,    72,    73,   nil,   nil,
   nil,   nil,   nil,   307,   308,   314,   103,   102,   104,   105,
   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,
   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   311,   312,    72,    73,   nil,
   nil,   nil,   nil,   nil,   307,   308,   314,   103,   102,   104,
   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   311,   312,    72,    73,
   nil,   nil,   nil,   nil,   nil,   307,   308,   314,   103,   102,
   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   311,   312,    72,
    73,   nil,   nil,   nil,   nil,   nil,   307,   308,   314,   103,
   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   311,   312,
    72,    73,   nil,   nil,   nil,   nil,   nil,   307,   308,   314,
   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   311,
   312,    72,    73,   nil,   nil,   nil,   nil,   nil,   307,   308,
   314,   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   311,   312,    72,    73,   nil,   nil,   nil,   nil,   nil,   307,
   308,   314,   103,   102,   104,   105,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   311,   312,    72,    73,   nil,   nil,   nil,   nil,   nil,
   307,   308,   314,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   311,   312,    72,    73,   nil,   nil,   nil,   nil,
   nil,   307,   308,   314,   103,   102,   104,   105,   nil,   nil,
   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,
   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   311,   312,    72,    73,   nil,   nil,   nil,
   nil,   nil,   307,   308,   314,   103,   102,   104,   105,   nil,
   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   311,   312,    72,    73,   nil,   nil,
   nil,   nil,   nil,   307,   308,   314,   103,   102,   104,   105,
   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,
   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   311,   312,    72,    73,   nil,
   nil,   nil,   nil,   nil,   307,   308,   314,   103,   102,   104,
   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   311,   312,    72,    73,
   nil,   nil,   nil,   nil,   nil,   307,   308,   314,   103,   102,
   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   311,   312,    72,
    73,   nil,   nil,   nil,   nil,   nil,   307,   308,   314,   103,
   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   311,   312,
    72,    73,   nil,   nil,   nil,   nil,   nil,   307,   308,   314,
   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   311,
   312,    72,    73,   nil,   nil,   nil,   nil,   nil,   307,   308,
   314,   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   311,   312,    72,    73,   nil,   nil,   nil,   nil,   nil,   307,
   308,   314,   103,   102,   104,   105,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   311,   312,    72,    73,   nil,   nil,   nil,   nil,   nil,
   307,   308,   314,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   311,   312,    72,    73,   nil,   nil,   nil,   nil,
   nil,   307,   308,   314,   103,   102,   104,   105,   nil,   nil,
   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,
   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,
   nil,   nil,    29,    28,    27,   103,   102,   104,   105,   nil,
   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   286,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   283,   nil,   281,   nil,    44,   nil,   nil,   287,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,   284,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   286,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,
   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   283,   nil,   281,   nil,    44,   nil,   nil,   287,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,
   284,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,    30,    31,    72,    73,   nil,
   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,
   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   286,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   283,   nil,   281,   nil,    44,   nil,   nil,   287,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,
    91,   284,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   220,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   311,   312,    72,    73,
   nil,   nil,   nil,   nil,   nil,   307,   308,   314,   103,   102,
   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   311,   312,    72,
    73,   nil,   nil,   nil,   nil,   nil,   307,   308,   314,   103,
   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   311,   312,
    72,    73,   nil,   nil,   nil,   nil,   nil,   307,   308,   314,
   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   311,
   312,    72,    73,   nil,   nil,   nil,   nil,   nil,   307,   308,
   314,   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,   nil,   nil,   nil,    62,   nil,    83,    95,    96,
    74,    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   103,   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,     8,    45,   nil,    10,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,    35,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   311,
   312,    72,    73,   nil,   nil,   nil,   nil,   nil,   307,   308,
   314,   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,   309,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,   nil,   nil,   315,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   305,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   672,   nil,   669,
   668,   667,   677,   670,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   680,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,   505,   nil,   675,    62,   nil,    83,    95,    96,
    74,    75,    71,   nil,    57,   688,   687,   nil,    63,    64,
   681,   nil,   nil,    67,   nil,    65,    66,    68,   311,   312,
    72,    73,   nil,   nil,   nil,   nil,   nil,   307,   308,   314,
   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,
   nil,   nil,   nil,   309,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,   nil,   nil,   315,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   305,   nil,   nil,   301,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   311,
   312,    72,    73,   nil,   nil,   nil,   nil,   nil,   307,   308,
   314,   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   521,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,
    28,    27,   103,   102,   104,   105,   nil,   nil,    19,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    20,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,
    29,    28,    27,   103,   102,   104,   105,   nil,   nil,    19,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    20,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,
   nil,    29,    28,    27,   103,   102,   104,   105,   nil,   nil,
    19,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,
   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,
   nil,   nil,    29,    28,    27,   103,   102,   104,   105,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,
   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   311,   312,    72,    73,   nil,
   nil,   nil,   nil,   nil,   307,   308,   314,   103,   102,   104,
   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,    30,    31,    72,    73,
   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,
   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   286,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   283,   nil,   281,   nil,    44,   nil,   nil,
   287,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,
   nil,    91,   284,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   311,   312,    72,
    73,   nil,   nil,   nil,   nil,   nil,   307,   308,   314,   103,
   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   311,   312,
    72,    73,   nil,   nil,   nil,   nil,   nil,   307,   308,   314,
   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   311,
   312,    72,    73,   nil,   nil,   nil,   nil,   nil,   307,   308,
   314,   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   311,   312,    72,    73,   nil,   nil,   nil,   nil,   nil,   307,
   308,   314,   103,   102,   104,   105,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   286,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   629,   nil,   281,
   nil,    44,   nil,   nil,   287,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   nil,   nil,    91,   284,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   311,   312,    72,    73,   nil,   nil,   nil,   nil,   nil,
   307,   308,   314,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   286,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   281,   nil,    44,   nil,   nil,   287,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   nil,   nil,    91,   284,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   311,   312,    72,    73,   nil,   nil,   nil,   nil,
   nil,   307,   308,   314,   103,   102,   104,   105,   nil,   nil,
   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,
   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,   nil,   nil,   nil,    62,   nil,
    83,    95,    96,    74,    75,    71,     9,    57,   nil,   nil,
   nil,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,
    29,    28,    27,   103,   102,   104,   105,   nil,   nil,    19,
   nil,   nil,   nil,   nil,   nil,     8,    45,   294,    10,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,    33,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,    35,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    20,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,   nil,   nil,   389,    62,   nil,    83,
    95,    96,    74,    75,    71,   nil,    57,   nil,   nil,   nil,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   311,   312,    72,    73,   nil,   nil,   nil,   nil,   nil,   307,
   308,   314,   103,   102,   104,   105,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,   309,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,   nil,   nil,   315,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   305,   nil,   nil,   301,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,
    29,    28,    27,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   286,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   283,   nil,
   281,   nil,    44,   nil,   nil,   287,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   nil,   nil,    91,   284,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   311,   312,    72,    73,   nil,   nil,   nil,   nil,
   nil,   307,   308,   314,   103,   102,   104,   105,   nil,   nil,
   232,   nil,   nil,   nil,   nil,   nil,   nil,   309,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,   nil,   nil,   315,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   305,   nil,   nil,
   301,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   311,   312,    72,    73,   nil,   nil,   nil,
   nil,   nil,   307,   308,   314,   103,   102,   104,   105,   nil,
   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   311,   312,    72,    73,   nil,   nil,
   nil,   nil,   nil,   307,   308,   314,   103,   102,   104,   105,
   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,
   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,    30,    31,    72,    73,   nil,
   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,
   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   311,   312,    72,    73,
   nil,   nil,   nil,   nil,   nil,   307,   308,   314,   103,   102,
   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   286,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   629,   nil,   nil,   nil,    44,   nil,   nil,
   287,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,
   nil,    91,   284,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   311,   312,    72,
    73,   nil,   nil,   nil,   nil,   nil,   307,   308,   314,   103,
   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   286,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   287,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   nil,   nil,    91,   284,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   311,   312,
    72,    73,   nil,   nil,   nil,   nil,   nil,   307,   308,   314,
   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   283,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,
    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,
    27,   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   286,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   283,   nil,   281,   nil,
    44,   nil,   nil,   287,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,    91,   284,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,
    28,    27,   103,   102,   104,   105,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   286,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   283,   nil,   281,
   nil,    44,   nil,   nil,   287,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   nil,   nil,    91,   284,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   311,   312,    72,    73,   nil,   nil,   nil,   nil,   nil,
   307,   308,   314,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   734,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   311,   312,    72,    73,   nil,   nil,   nil,   nil,
   nil,   307,   308,   314,   103,   102,   104,   105,   nil,   nil,
   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,
   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   311,   312,    72,    73,   nil,   nil,   nil,
   nil,   nil,   307,   308,   314,   103,   102,   104,   105,   nil,
   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   286,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   629,   nil,   281,   nil,    44,   nil,   nil,   287,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,   284,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   311,   312,    72,    73,   nil,   nil,
   nil,   nil,   nil,   307,   308,   314,   103,   102,   104,   105,
   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   286,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,
   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   281,   nil,    44,   nil,   nil,   287,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,
   284,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,    30,    31,    72,    73,   nil,
   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,
   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,    30,    31,    72,    73,
   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,
   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,
    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,
    27,   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   311,   312,    72,    73,   nil,   nil,   nil,   nil,   nil,   307,
   308,   314,   103,   102,   104,   105,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   311,   312,    72,    73,   nil,   nil,   nil,   nil,   nil,
   307,   308,   314,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   311,   312,    72,    73,   nil,   nil,   nil,   nil,
   nil,   307,   308,   314,   103,   102,   104,   105,   nil,   nil,
   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,
   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   311,   312,    72,    73,   nil,   nil,   nil,
   nil,   nil,   307,   308,   314,   103,   102,   104,   105,   nil,
   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,   309,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,   nil,   nil,   315,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   305,   nil,
   nil,   301,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   311,   312,    72,    73,   nil,   nil,
   nil,   nil,   nil,   307,   308,   314,   103,   102,   104,   105,
   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,   309,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,   nil,   nil,   315,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   305,
   nil,   nil,   301,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   311,   312,    72,    73,   nil,
   nil,   nil,   nil,   nil,   307,   308,   314,   103,   102,   104,
   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   405,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   311,   312,    72,    73,
   nil,   nil,   nil,   nil,   nil,   307,   308,   314,   103,   102,
   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   103,   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   311,
   312,    72,    73,   nil,   nil,   nil,   nil,   nil,   307,   308,
   314,   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,
    28,    27,   103,   102,   104,   105,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   311,   312,    72,    73,   nil,   nil,   nil,   nil,   nil,
   307,   308,   314,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   311,   312,    72,    73,   nil,   nil,   nil,   nil,
   nil,   307,   308,   314,   103,   102,   104,   105,   nil,   nil,
   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,
   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   311,   312,    72,    73,   nil,   nil,   nil,
   nil,   nil,   307,   308,   314,   103,   102,   104,   105,   nil,
   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   311,   312,    72,    73,   nil,   nil,
   nil,   nil,   nil,   307,   308,   314,   103,   102,   104,   105,
   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,
   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   311,   312,    72,    73,   nil,
   nil,   nil,   nil,   nil,   307,   308,   314,   103,   102,   104,
   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   225,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   311,   312,    72,    73,
   nil,   nil,   nil,   nil,   nil,   307,   308,   314,   103,   102,
   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,
   nil,   309,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,   nil,
   nil,   315,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   857,   nil,   nil,   231,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   311,   312,    72,
    73,   nil,   nil,   nil,   nil,   nil,   307,   308,   314,   103,
   102,   104,   105,   nil,   nil,   232,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   103,   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   311,
   312,    72,    73,   nil,   nil,   nil,   nil,   nil,   307,   308,
   314,   103,   102,   104,   105,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   629,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   311,   312,    72,    73,   nil,   nil,   nil,   nil,   nil,   307,
   308,   314,   103,   102,   104,   105,   nil,   nil,   232,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   286,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   281,
   nil,    44,   nil,   nil,   287,   nil,   nil,   nil,   nil,   nil,
   230,   nil,   nil,   nil,   nil,    91,   284,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   311,   312,    72,    73,   nil,   nil,   nil,   nil,   nil,
   307,   308,   314,   103,   102,   104,   105,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,   nil,   231,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   311,   312,    72,    73,   nil,   nil,   nil,   nil,
   nil,   307,   308,   314,   103,   102,   104,   105,   nil,   nil,
   232,   nil,   nil,   nil,   nil,   nil,   nil,   309,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,   nil,   nil,   315,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   857,   nil,   nil,
   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   311,   312,    72,    73,   nil,   nil,   nil,
   nil,   nil,   307,   308,   314,   103,   102,   104,   105,   nil,
   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,   309,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,   nil,   nil,   315,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   931,   nil,
   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   286,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,
   nil,   nil,   231,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   283,   nil,   281,   nil,    44,   nil,   nil,   287,   nil,
   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,   nil,    91,
   284,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,   nil,  -280,   nil,
    62,   nil,    83,    95,    96,  -280,  -280,  -280,   nil,   nil,
  -280,  -280,  -280,   nil,  -280,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,  -280,  -280,  -280,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,  -280,  -280,   nil,  -280,  -280,  -280,
  -280,  -280,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,  -280,  -280,  -280,  -280,  -280,  -280,
  -280,  -280,  -280,  -280,  -280,  -280,  -280,  -280,   nil,   nil,
  -280,  -280,  -280,   nil,   nil,  -280,   nil,   nil,  -280,   nil,
   nil,  -280,  -280,   nil,  -280,   nil,  -280,   nil,  -280,   nil,
  -280,  -280,   nil,  -280,  -280,  -280,  -280,  -280,   nil,  -280,
   nil,  -280,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,  -280,   nil,   nil,  -280,  -280,
  -280,  -280,  -582,  -280,   nil,  -280,   nil,   nil,   nil,  -582,
  -582,  -582,   nil,   nil,  -582,  -582,  -582,   nil,  -582,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -582,  -582,  -582,
  -582,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -582,  -582,
   nil,  -582,  -582,  -582,  -582,  -582,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -582,  -582,
  -582,  -582,  -582,  -582,  -582,  -582,  -582,  -582,  -582,  -582,
  -582,  -582,   nil,   nil,  -582,  -582,  -582,   nil,   nil,  -582,
   nil,   nil,  -582,   nil,   nil,  -582,  -582,   nil,  -582,   nil,
  -582,   nil,  -582,   nil,  -582,  -582,   nil,  -582,  -582,  -582,
  -582,  -582,   nil,  -582,  -582,  -582,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -582,
   nil,   nil,  -582,  -582,  -582,  -582,  -583,  -582,   nil,  -582,
   nil,   nil,   nil,  -583,  -583,  -583,   nil,   nil,  -583,  -583,
  -583,   nil,  -583,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,  -583,  -583,  -583,  -583,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,  -583,  -583,   nil,  -583,  -583,  -583,  -583,  -583,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,  -583,  -583,  -583,  -583,  -583,  -583,  -583,  -583,
  -583,  -583,  -583,  -583,  -583,  -583,   nil,   nil,  -583,  -583,
  -583,   nil,   nil,  -583,   nil,   nil,  -583,   nil,   nil,  -583,
  -583,   nil,  -583,   nil,  -583,   nil,  -583,   nil,  -583,  -583,
   nil,  -583,  -583,  -583,  -583,  -583,   nil,  -583,  -583,  -583,
   672,   nil,   669,   668,   667,   677,   670,   nil,   nil,   nil,
   nil,   nil,   nil,  -583,   nil,   680,  -583,  -583,  -583,  -583,
  -414,  -583,   nil,  -583,   nil,   nil,   nil,  -414,  -414,  -414,
   nil,   nil,  -414,  -414,  -414,   nil,  -414,   675,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,  -414,  -414,  -414,   688,   687,
   nil,   nil,   nil,   681,   nil,   nil,  -414,  -414,   nil,  -414,
  -414,  -414,  -414,  -414,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,  -414,  -414,  -414,  -414,
  -414,  -414,  -414,  -414,  -414,  -414,  -414,  -414,  -414,  -414,
   nil,   nil,  -414,  -414,  -414,   nil,   nil,  -414,   nil,   265,
  -414,   nil,   nil,  -414,  -414,   nil,  -414,   nil,  -414,   nil,
  -414,   nil,  -414,  -414,   nil,  -414,  -414,  -414,  -414,  -414,
  -296,  -414,  -414,  -414,   nil,   nil,   nil,  -296,  -296,  -296,
   nil,   nil,  -296,  -296,  -296,   nil,  -296,  -414,   nil,   nil,
  -414,  -414,   nil,  -414,   nil,  -414,  -296,  -296,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,  -296,  -296,   nil,  -296,
  -296,  -296,  -296,  -296,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,  -296,  -296,  -296,  -296,
  -296,  -296,  -296,  -296,  -296,  -296,  -296,  -296,  -296,  -296,
   nil,   nil,  -296,  -296,  -296,   nil,   nil,  -296,   nil,   274,
  -296,   nil,   nil,  -296,  -296,   nil,  -296,   nil,  -296,   nil,
  -296,   nil,  -296,  -296,   nil,  -296,  -296,  -296,  -296,  -296,
   nil,  -296,  -244,  -296,   nil,   nil,   nil,   nil,   nil,  -244,
  -244,  -244,   nil,   nil,  -244,  -244,  -244,  -296,  -244,   nil,
  -296,  -296,   nil,  -296,   nil,  -296,   nil,  -244,  -244,  -244,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -244,  -244,
   nil,  -244,  -244,  -244,  -244,  -244,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -244,  -244,
  -244,  -244,  -244,  -244,  -244,  -244,  -244,  -244,  -244,  -244,
  -244,  -244,   nil,   nil,  -244,  -244,  -244,   nil,   nil,  -244,
   nil,   265,  -244,   nil,   nil,  -244,  -244,   nil,  -244,   nil,
  -244,   nil,  -244,   nil,  -244,  -244,   nil,  -244,  -244,  -244,
  -244,  -244,   nil,  -244,  -244,  -244,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -244,
   nil,  -244,  -244,  -244,   nil,  -244,   nil,  -244,  -244,  -244,
  -244,   nil,   nil,  -244,  -244,  -244,   672,  -244,   669,   668,
   667,   677,   670,   nil,   nil,   nil,  -244,  -244,   nil,   nil,
   nil,   680,   nil,   nil,   nil,   nil,   nil,  -244,  -244,   nil,
  -244,  -244,  -244,  -244,  -244,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   675,   nil,   672,   nil,   669,   668,   667,
   677,   670,   685,   684,   688,   687,   nil,   nil,   nil,   681,
   680,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -244,   nil,
   nil,   nil,   nil,   nil,   nil,  -244,   nil,   nil,   nil,   nil,
   265,  -244,   675,   658,   nil,   220,   nil,   nil,   nil,   nil,
   nil,   685,   684,   688,   687,   nil,   nil,   nil,   681,   nil,
   nil,   nil,   nil,  -244,  -244,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -244,   nil,
   nil,  -244,   nil,   nil,   nil,   nil,  -244,   175,   186,   176,
   199,   172,   192,   182,   181,   202,   203,   197,   180,   179,
   174,   200,   204,   205,   184,   173,   187,   191,   193,   185,
   178,   nil,   nil,   nil,   194,   201,   196,   195,   188,   198,
   183,   171,   190,   189,   nil,   nil,   nil,   nil,   nil,   170,
   177,   168,   169,   165,   166,   167,   126,   128,   125,   nil,
   127,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   159,   160,
   nil,   156,   138,   139,   140,   147,   144,   146,   nil,   nil,
   141,   142,   nil,   nil,   nil,   161,   162,   148,   149,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   153,   152,   nil,   137,   158,   155,   154,   163,
   150,   151,   145,   143,   135,   157,   136,   nil,   nil,   164,
    91,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    90,   175,   186,   176,   199,   172,
   192,   182,   181,   202,   203,   197,   180,   179,   174,   200,
   204,   205,   184,   173,   187,   191,   193,   185,   178,   nil,
   nil,   nil,   194,   201,   196,   195,   188,   198,   183,   171,
   190,   189,   nil,   nil,   nil,   nil,   nil,   170,   177,   168,
   169,   165,   166,   167,   126,   128,   nil,   nil,   127,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   159,   160,   nil,   156,
   138,   139,   140,   147,   144,   146,   nil,   nil,   141,   142,
   nil,   nil,   nil,   161,   162,   148,   149,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   153,   152,   nil,   137,   158,   155,   154,   163,   150,   151,
   145,   143,   135,   157,   136,   nil,   nil,   164,    91,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    90,   175,   186,   176,   199,   172,   192,   182,
   181,   202,   203,   197,   180,   179,   174,   200,   204,   205,
   184,   173,   187,   191,   193,   185,   178,   nil,   nil,   nil,
   194,   201,   196,   195,   188,   198,   183,   171,   190,   189,
   nil,   nil,   nil,   nil,   nil,   170,   177,   168,   169,   165,
   166,   167,   126,   128,   nil,   nil,   127,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   159,   160,   nil,   156,   138,   139,
   140,   147,   144,   146,   nil,   nil,   141,   142,   nil,   nil,
   nil,   161,   162,   148,   149,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   153,   152,
   nil,   137,   158,   155,   154,   163,   150,   151,   145,   143,
   135,   157,   136,   nil,   nil,   164,    91,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    90,   175,   186,   176,   199,   172,   192,   182,   181,   202,
   203,   197,   180,   179,   174,   200,   204,   205,   184,   173,
   187,   191,   193,   185,   178,   nil,   nil,   nil,   194,   201,
   196,   195,   188,   198,   183,   171,   190,   189,   nil,   nil,
   nil,   nil,   nil,   170,   177,   168,   169,   165,   166,   167,
   126,   128,   nil,   nil,   127,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   159,   160,   nil,   156,   138,   139,   140,   147,
   144,   146,   nil,   nil,   141,   142,   nil,   nil,   nil,   161,
   162,   148,   149,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   153,   152,   nil,   137,
   158,   155,   154,   163,   150,   151,   145,   143,   135,   157,
   136,   nil,   nil,   164,    91,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    90,   175,
   186,   176,   199,   172,   192,   182,   181,   202,   203,   197,
   180,   179,   174,   200,   204,   205,   184,   173,   187,   191,
   193,   185,   178,   nil,   nil,   nil,   194,   201,   196,   372,
   371,   373,   370,   171,   190,   189,   nil,   nil,   nil,   nil,
   nil,   170,   177,   168,   169,   367,   368,   369,   365,   128,
    99,    98,   366,   nil,   101,   nil,   nil,   nil,   nil,   nil,
   159,   160,   nil,   156,   138,   139,   140,   147,   144,   146,
   nil,   nil,   141,   142,   nil,   nil,   nil,   161,   162,   148,
   149,   nil,   nil,   nil,   nil,   nil,   377,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   153,   152,   nil,   137,   158,   155,
   154,   163,   150,   151,   145,   143,   135,   157,   136,   nil,
   nil,   164,   175,   186,   176,   199,   172,   192,   182,   181,
   202,   203,   197,   180,   179,   174,   200,   204,   205,   184,
   173,   187,   191,   193,   185,   178,   nil,   nil,   nil,   194,
   201,   196,   195,   188,   198,   183,   171,   190,   189,   nil,
   nil,   nil,   nil,   nil,   170,   177,   168,   169,   165,   166,
   167,   126,   128,   nil,   nil,   127,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   159,   160,   nil,   156,   138,   139,   140,
   147,   144,   146,   nil,   nil,   141,   142,   nil,   nil,   nil,
   161,   162,   148,   149,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   153,   152,   nil,
   137,   158,   155,   154,   163,   150,   151,   145,   143,   135,
   157,   136,   414,   418,   164,   nil,   415,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   159,   160,   nil,   156,   138,   139,
   140,   147,   144,   146,   nil,   nil,   141,   142,   nil,   nil,
   nil,   161,   162,   148,   149,   nil,   nil,   nil,   nil,   nil,
   265,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   153,   152,
   nil,   137,   158,   155,   154,   163,   150,   151,   145,   143,
   135,   157,   136,   421,   425,   164,   nil,   420,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   159,   160,   nil,   156,   138,
   139,   140,   147,   144,   146,   nil,   nil,   141,   142,   nil,
   nil,   nil,   161,   162,   148,   149,   nil,   nil,   nil,   nil,
   nil,   265,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   153,
   152,   nil,   137,   158,   155,   154,   163,   150,   151,   145,
   143,   135,   157,   136,   476,   418,   164,   nil,   477,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   159,   160,   nil,   156,
   138,   139,   140,   147,   144,   146,   nil,   nil,   141,   142,
   nil,   nil,   nil,   161,   162,   148,   149,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   153,   152,   nil,   137,   158,   155,   154,   163,   150,   151,
   145,   143,   135,   157,   136,   608,   418,   164,   nil,   609,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   159,   160,   nil,
   156,   138,   139,   140,   147,   144,   146,   nil,   nil,   141,
   142,   nil,   nil,   nil,   161,   162,   148,   149,   nil,   nil,
   nil,   nil,   nil,   265,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   153,   152,   nil,   137,   158,   155,   154,   163,   150,
   151,   145,   143,   135,   157,   136,   610,   425,   164,   nil,
   611,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   159,   160,
   nil,   156,   138,   139,   140,   147,   144,   146,   nil,   nil,
   141,   142,   nil,   nil,   nil,   161,   162,   148,   149,   nil,
   nil,   nil,   nil,   nil,   265,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   153,   152,   nil,   137,   158,   155,   154,   163,
   150,   151,   145,   143,   135,   157,   136,   640,   418,   164,
   nil,   641,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   159,
   160,   nil,   156,   138,   139,   140,   147,   144,   146,   nil,
   nil,   141,   142,   nil,   nil,   nil,   161,   162,   148,   149,
   nil,   nil,   nil,   nil,   nil,   265,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   153,   152,   nil,   137,   158,   155,   154,
   163,   150,   151,   145,   143,   135,   157,   136,   643,   425,
   164,   nil,   644,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   159,   160,   nil,   156,   138,   139,   140,   147,   144,   146,
   nil,   nil,   141,   142,   nil,   nil,   nil,   161,   162,   148,
   149,   nil,   nil,   nil,   nil,   nil,   265,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   153,   152,   nil,   137,   158,   155,
   154,   163,   150,   151,   145,   143,   135,   157,   136,   608,
   418,   164,   nil,   609,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   159,   160,   nil,   156,   138,   139,   140,   147,   144,
   146,   nil,   nil,   141,   142,   nil,   nil,   nil,   161,   162,
   148,   149,   nil,   nil,   nil,   nil,   nil,   265,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   153,   152,   nil,   137,   158,
   155,   154,   163,   150,   151,   145,   143,   135,   157,   136,
   610,   425,   164,   nil,   611,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   159,   160,   nil,   156,   138,   139,   140,   147,
   144,   146,   nil,   nil,   141,   142,   nil,   nil,   nil,   161,
   162,   148,   149,   nil,   nil,   nil,   nil,   nil,   265,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   153,   152,   nil,   137,
   158,   155,   154,   163,   150,   151,   145,   143,   135,   157,
   136,   701,   418,   164,   nil,   702,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   159,   160,   nil,   156,   138,   139,   140,
   147,   144,   146,   nil,   nil,   141,   142,   nil,   nil,   nil,
   161,   162,   148,   149,   nil,   nil,   nil,   nil,   nil,   265,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   153,   152,   nil,
   137,   158,   155,   154,   163,   150,   151,   145,   143,   135,
   157,   136,   703,   425,   164,   nil,   704,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   159,   160,   nil,   156,   138,   139,
   140,   147,   144,   146,   nil,   nil,   141,   142,   nil,   nil,
   nil,   161,   162,   148,   149,   nil,   nil,   nil,   nil,   nil,
   265,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   153,   152,
   nil,   137,   158,   155,   154,   163,   150,   151,   145,   143,
   135,   157,   136,   706,   425,   164,   nil,   707,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   159,   160,   nil,   156,   138,
   139,   140,   147,   144,   146,   nil,   nil,   141,   142,   nil,
   nil,   nil,   161,   162,   148,   149,   nil,   nil,   nil,   nil,
   nil,   265,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   153,
   152,   nil,   137,   158,   155,   154,   163,   150,   151,   145,
   143,   135,   157,   136,   476,   418,   164,   nil,   477,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   159,   160,   nil,   156,
   138,   139,   140,   147,   144,   146,   nil,   nil,   141,   142,
   nil,   nil,   nil,   161,   162,   148,   149,   nil,   nil,   nil,
   nil,   nil,   265,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   153,   152,   nil,   137,   158,   155,   154,   163,   150,   151,
   145,   143,   135,   157,   136,   974,   425,   164,   nil,   973,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   159,   160,   nil,
   156,   138,   139,   140,   147,   144,   146,   nil,   nil,   141,
   142,   nil,   nil,   nil,   161,   162,   148,   149,   nil,   nil,
   nil,   nil,   nil,   265,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   153,   152,   nil,   137,   158,   155,   154,   163,   150,
   151,   145,   143,   135,   157,   136,  1000,   418,   164,   nil,
  1001,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   159,   160,
   nil,   156,   138,   139,   140,   147,   144,   146,   nil,   nil,
   141,   142,   nil,   nil,   nil,   161,   162,   148,   149,   nil,
   nil,   nil,   nil,   nil,   265,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   153,   152,   nil,   137,   158,   155,   154,   163,
   150,   151,   145,   143,   135,   157,   136,  1002,   425,   164,
   nil,  1003,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   159,
   160,   nil,   156,   138,   139,   140,   147,   144,   146,   nil,
   nil,   141,   142,   nil,   nil,   nil,   161,   162,   148,   149,
   nil,   nil,   nil,   nil,   nil,   265,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   153,   152,   nil,   137,   158,   155,   154,
   163,   150,   151,   145,   143,   135,   157,   136,   nil,   672,
   164,   669,   668,   667,   677,   670,   nil,   672,   nil,   669,
   668,   667,   677,   670,   680,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   680,   nil,   672,   nil,   669,   668,   667,   677,
   670,   nil,   nil,   nil,   nil,   nil,   675,   nil,   nil,   680,
   nil,   nil,   nil,   nil,   675,   685,   684,   688,   687,   nil,
   nil,   nil,   681,   685,   684,   688,   687,   nil,   nil,   nil,
   681,   675,   nil,   672,   nil,   669,   668,   667,   677,   670,
   685,   684,   688,   687,   nil,   nil,   nil,   681,   680,   nil,
   672,   nil,   669,   668,   667,   677,   670,   nil,   672,   nil,
   669,   668,   667,   677,   670,   680,   nil,   nil,   nil,   nil,
   675,   nil,   nil,   680,   nil,   nil,   nil,   nil,   nil,   685,
   684,   688,   687,   nil,   nil,   nil,   681,   675,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   675,   685,   684,   688,   687,
   nil,   nil,   nil,   681,   685,   684,   688,   687,   nil,   nil,
   672,   681,   669,   668,   667,   677,   670,   nil,   672,   nil,
   669,   668,   667,   677,   670,   680,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   680,   nil,   672,   nil,   669,   668,   667,
   677,   670,   nil,   nil,   nil,   nil,   nil,   675,   nil,   nil,
   680,   nil,   nil,   nil,   nil,   675,   685,   684,   688,   687,
   nil,   nil,   nil,   681,   685,   684,   688,   687,   nil,   nil,
   nil,   681,   675,   nil,   672,   nil,   669,   668,   667,   677,
   670,   nil,   nil,   688,   687,   nil,   nil,   nil,   681,   680,
   nil,   672,   nil,   669,   668,   667,   677,   670,   672,   nil,
   669,   668,   667,   677,   670,   nil,   680,   nil,   nil,   nil,
   nil,   675,   nil,   680,   nil,   nil,   nil,   nil,   nil,   nil,
   685,   684,   688,   687,   nil,   nil,   nil,   681,   675,   nil,
   nil,   nil,   nil,   nil,   nil,   675,   nil,   nil,   nil,   688,
   687,   nil,   nil,   nil,   681,   nil,   688,   687,   nil,   nil,
   672,   681,   669,   668,   667,   677,   670,   672,   nil,   669,
   668,   667,   677,   670,   672,   680,   669,   668,   667,   677,
   670,   nil,   680,   nil,   nil,   nil,   nil,   nil,   nil,   680,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   675,   nil,   nil,
   nil,   nil,   nil,   nil,   675,   nil,   nil,   nil,   688,   687,
   nil,   675,   nil,   681,   nil,   688,   687,   nil,   nil,   nil,
   681,   nil,   688,   687,   nil,   nil,   nil,   681 ]

racc_action_check = [
    97,     1,    61,   438,   438,   346,   347,    97,    97,    97,
    19,   339,    97,    97,    97,    58,    97,    26,   474,   384,
   635,     7,   564,   564,    97,   385,    97,    97,    97,   635,
   620,   898,    10,   898,   350,   360,    97,    97,   340,    97,
    97,    97,    97,    97,   699,   866,    17,    17,   893,   548,
   701,    19,  1000,   474,  1001,    58,   650,   650,   310,   356,
   338,   338,   223,   356,   702,   793,    97,    97,    97,    97,
    97,    97,    97,    97,    97,    97,    97,    97,    97,    97,
    12,    26,    97,    97,    97,   384,    97,    97,   540,    61,
    97,   385,    24,    97,    97,   825,    97,   438,    97,    24,
    97,   224,    97,    97,    26,    97,    97,    97,    97,    97,
  1021,    97,   100,    97,   346,   347,   564,   620,   223,   100,
   100,   100,   310,  1002,   100,   100,   100,    97,   100,   339,
    97,    97,    97,    97,   339,    97,   100,    97,   100,   100,
   100,   360,    97,   350,   541,   310,   703,   826,   100,   100,
   650,   100,   100,   100,   100,   100,   340,   224,   548,   701,
   640,   340,   699,   866,   360,   699,   893,   699,   866,   360,
  1000,   893,  1001,   702,   793,  1000,   226,  1001,   100,   100,
   100,   100,   100,   100,   100,   100,   100,   100,   100,   100,
   100,   100,   540,    13,   100,   100,   100,   540,   100,   100,
  1002,   641,   100,    16,   825,   100,   100,   704,   100,    22,
   100,   703,   100,    37,   100,   100,   354,   100,   100,   100,
   100,   100,   420,   100,    40,   100,   640,   365,  1021,   420,
   420,   420,   226,  1021,   365,   420,   420,   330,   420,   100,
   330,  1002,   100,   100,   100,   100,  1002,   100,   541,   100,
   355,   826,   482,   541,   100,   703,   826,   331,   420,   420,
   331,   420,   420,   420,   420,   420,   640,   641,   354,   640,
    15,    45,   704,   560,   560,   354,    38,   111,   640,   810,
   354,   810,   810,   810,   354,   810,   206,   482,   420,   420,
   420,   420,   420,   420,   420,   420,   420,   420,   420,   420,
   420,   420,   355,   354,   420,   420,   420,   641,   420,   355,
   641,   483,   420,    39,   355,   420,   704,    15,   355,   641,
   420,   575,   420,    38,   420,   420,    15,   420,   420,   420,
   420,   420,    38,   420,   421,   420,   334,   355,   444,   334,
   608,   421,   421,   421,    41,    41,   483,   421,   421,   420,
   421,   366,   420,   420,   621,   420,   225,   420,   366,   421,
    39,   810,   643,   609,   420,   656,   227,   560,   656,    39,
   421,   421,   560,   421,   421,   421,   421,   421,   949,   228,
   949,   949,   949,   232,   949,   575,   575,   493,   319,   621,
   379,   264,   315,   315,   444,   575,   608,   278,   577,   279,
   421,   421,   421,   421,   421,   421,   421,   421,   421,   421,
   421,   421,   421,   421,   643,   282,   421,   421,   421,   609,
   421,   643,    41,    41,   421,   294,   643,   421,   754,   781,
   643,   706,   421,     3,   421,   319,   421,   421,     3,   421,
   421,   421,   421,   421,   319,   421,   421,   421,   380,   643,
   295,   493,   493,   493,   379,   379,   379,   381,   524,   524,
   949,   421,   577,   577,   421,   421,   610,   421,   493,   421,
   315,   315,   577,   610,   610,   610,   421,   297,   610,   610,
   610,   781,   610,   706,   754,   690,   690,   298,   781,   367,
   706,   610,   610,   610,   610,   706,   367,   781,   918,   706,
   299,   918,   610,   610,   305,   610,   610,   610,   610,   610,
   290,   414,   380,   380,   380,   290,   781,   382,   706,   368,
   383,   381,   381,   381,   386,   125,   368,   798,   798,   308,
   125,   125,   610,   610,   610,   610,   610,   610,   610,   610,
   610,   610,   610,   610,   610,   610,   343,    14,   610,   610,
   610,   343,   610,   610,    14,   469,   610,   415,   414,   610,
   610,   309,   610,    14,   610,   591,   610,   414,   610,   610,
   314,   610,   610,   610,   610,   610,   316,   610,   610,   610,
   320,   382,   382,   382,   383,   383,   383,   469,   386,   386,
   386,   469,   469,   610,   469,   469,   610,   610,   610,   610,
   450,   610,   611,   610,   415,   990,   990,   795,   610,   611,
   611,   611,   591,   415,   611,   611,   611,   795,   611,   323,
   675,   591,   675,   675,   675,   328,   675,   451,   611,   611,
   611,   332,   450,    46,    79,   333,   450,   450,   611,   611,
    46,   611,   611,   611,   611,   611,    79,   369,   335,    46,
   795,   795,   344,   370,   369,   795,    79,   675,   371,   451,
   370,   345,   559,   451,   451,   371,   675,   559,   611,   611,
   611,   611,   611,   611,   611,   611,   611,   611,   611,   611,
   611,   611,   349,   843,   611,   611,   611,   222,   611,   611,
   302,   351,   611,   843,   222,   611,   611,   302,   611,   395,
   611,   401,   611,   222,   611,   611,   302,   611,   611,   611,
   611,   611,   404,   611,   807,   611,   807,   807,   807,   890,
   807,   890,   890,   890,   406,   890,   843,   843,   410,   611,
   412,   843,   611,   611,   611,   611,   303,   611,   413,   611,
   422,   430,   440,   303,   611,     0,     0,     0,     0,     0,
     0,   807,   303,   829,     0,     0,   890,   452,   829,     0,
   807,     0,     0,     0,     0,     0,     0,     0,     6,     6,
     6,     6,     6,     0,     0,     0,     0,     0,     0,     0,
   568,   568,     0,   453,   568,   568,   568,   433,     0,     0,
     0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
   454,     0,     0,     0,   455,     0,     0,     0,     0,     0,
   433,   433,   433,   433,   433,   433,   433,   433,   433,   433,
   433,   304,   433,   433,   306,   834,   433,   433,   304,     0,
   834,   306,     0,   480,   484,     0,     0,   304,   500,     0,
   306,     0,   433,   501,   433,     0,   433,   433,   504,   433,
   433,   433,   433,   433,     0,   433,   506,   511,   514,     0,
     0,     0,     0,   321,     0,     0,     0,     0,   522,   372,
   321,   373,     0,     0,   375,   433,   372,   433,   373,   321,
     0,   375,     0,     0,     0,    33,    33,    33,    33,    33,
    33,   523,   525,   348,    33,    33,   537,   542,   543,    33,
   348,    33,    33,    33,    33,    33,    33,    33,   562,   348,
   572,   580,   582,    33,    33,    33,    33,    33,    33,    33,
   588,   592,    33,   597,   602,   612,   614,   409,    33,    33,
    33,    33,    33,    33,    33,    33,    33,    33,    33,    33,
   619,    33,    33,    33,   626,    33,    33,    33,    33,    33,
   409,   409,   409,   409,   409,   409,   409,   409,   409,   409,
   409,   358,   409,   409,   510,   628,   409,   409,   358,    33,
   634,   510,    33,   637,   639,    33,    33,   358,   642,    33,
   510,    33,   409,   645,   409,    33,   409,   409,   646,   409,
   409,   409,   409,   409,    33,   409,   649,   651,   654,    33,
    33,    33,    33,   551,    33,    33,    33,    33,   660,   661,
   551,   663,    33,    33,   664,   409,   665,   674,   682,   551,
    33,   686,    33,    33,    33,   123,   123,   123,   123,   123,
   123,   689,   692,   856,   123,   123,   697,   700,   709,   123,
   856,   123,   123,   123,   123,   123,   123,   123,   714,   856,
   733,   738,   756,   123,   123,   123,   123,   123,   123,   123,
   757,   759,   123,   760,   761,   763,   764,   613,   123,   123,
   123,   123,   123,   123,   123,   123,   123,   123,   123,   123,
   765,   123,   123,   123,   766,   123,   123,   123,   123,   123,
   613,   613,   613,   613,   613,   613,   613,   613,   613,   613,
   613,   644,   613,   613,   922,   770,   613,   613,   644,   123,
   774,   922,   123,   644,   775,   123,   123,   644,   780,   123,
   922,   123,   613,   784,   613,   123,   613,   613,   787,   613,
   613,   613,   613,   613,   123,   613,   788,   791,   794,   123,
   123,   123,   123,   928,   123,   123,   123,   123,   809,   811,
   928,   816,   123,   123,   819,   613,   828,   832,   833,   928,
   123,   836,   123,   123,   123,   208,   208,   208,   208,   208,
   208,   837,   853,   929,   208,   208,   857,   859,   873,   208,
   929,   208,   208,   208,   208,   208,   208,   208,   874,   929,
   878,   879,   881,   208,   208,   208,   208,   208,   208,   208,
   882,   854,   208,   854,   854,   854,   884,   854,   208,   208,
   208,   208,   208,   208,   208,   208,   208,   208,   208,   208,
   887,   208,   208,   208,   889,   208,   208,   208,   208,   208,
    21,    21,    21,    21,    21,    21,    21,    21,    21,    21,
    21,   707,    21,    21,   930,   895,    21,    21,   707,   208,
   896,   930,   208,   707,   902,   208,   208,   707,   906,   208,
   930,   208,    21,   908,    21,   208,    21,    21,   911,    21,
    21,    21,    21,    21,   208,    21,   912,   913,   914,   208,
   208,   208,   208,   932,   208,   208,   208,   208,   916,   931,
   932,   951,   208,   208,   973,    21,   974,   975,   980,   932,
   208,   981,   208,   208,   208,   231,   231,   231,   231,   231,
   231,   982,   983,   979,   231,   231,   984,   985,   986,   231,
   979,   231,   231,   231,   231,   231,   231,   231,   988,   979,
   991,   992,   993,   231,   231,   231,   231,   231,   231,   231,
   994,   972,   231,   972,   972,   972,   995,   972,   231,   231,
   231,   231,   231,   231,   231,   231,   231,   231,   231,   231,
   996,   231,   231,   231,   999,   231,   231,   231,   231,   231,
   276,   276,   276,   276,   276,   276,   276,   276,   276,   276,
   276,  1003,   276,   276,  1012,  1022,   276,   276,  1003,   231,
  1023,  1024,   231,  1003,   nil,   231,   231,  1003,   nil,   231,
   nil,   231,   276,   nil,   276,   231,   276,   276,   nil,   276,
   276,   276,   276,   276,   231,   276,   nil,   nil,   nil,   231,
   231,   231,   231,   nil,   231,   231,   231,   231,   nil,   nil,
   nil,   nil,   231,   231,   nil,   276,   nil,   nil,   nil,   nil,
   231,   nil,   231,   231,   231,   296,   296,   296,   296,   296,
   296,   nil,   nil,   nil,   296,   296,   nil,   nil,   nil,   296,
   nil,   296,   296,   296,   296,   296,   296,   296,   293,   293,
   293,   293,   293,   296,   296,   296,   296,   296,   296,   296,
   nil,   nil,   296,   498,   498,   498,   498,   498,   296,   296,
   296,   296,   296,   296,   296,   296,   296,   296,   296,   296,
   nil,   296,   296,   296,   nil,   296,   296,   296,   296,   296,
   428,   428,   428,   428,   428,   428,   428,   428,   428,   428,
   428,   nil,   428,   428,   nil,   nil,   428,   428,   nil,   296,
   nil,   nil,   296,   nil,   nil,   296,   296,   nil,   nil,   296,
   nil,   296,   428,   nil,   428,   296,   428,   428,   nil,   428,
   428,   428,   428,   428,   296,   428,   nil,   nil,   nil,   296,
   296,   296,   296,   nil,   296,   296,   296,   296,   nil,   nil,
   nil,   nil,   296,   296,   nil,   428,   nil,   nil,   nil,   nil,
   296,   nil,   296,   296,   296,   301,   301,   301,   301,   301,
   301,   nil,   nil,   nil,   301,   301,   nil,   nil,   nil,   301,
   nil,   301,   301,   301,   301,   301,   301,   301,   nil,   nil,
   nil,   nil,   nil,   301,   301,   301,   301,   301,   301,   301,
   nil,   nil,   301,   nil,   nil,   nil,   nil,   nil,   301,   301,
   301,   301,   301,   301,   301,   301,   301,   301,   301,   301,
   nil,   301,   301,   301,   nil,   301,   301,   301,   301,   301,
   473,   473,   473,   473,   473,   473,   473,   473,   473,   473,
   473,   nil,   473,   473,   nil,   nil,   473,   473,   nil,   301,
   nil,   nil,   301,   nil,   nil,   301,   301,   nil,   nil,   301,
   nil,   301,   473,   nil,   473,   301,   473,   473,   nil,   473,
   473,   473,   473,   473,   301,   473,   nil,   nil,   nil,   301,
   301,   301,   301,   nil,   301,   301,   301,   301,   nil,   nil,
   nil,   nil,   301,   301,   473,   473,   nil,   nil,   nil,   nil,
   301,   nil,   301,   301,   301,   326,   326,   326,   326,   326,
   326,   nil,   nil,   nil,   326,   326,   nil,   nil,   nil,   326,
   nil,   326,   326,   326,   326,   326,   326,   326,   nil,   nil,
   nil,   nil,   nil,   326,   326,   326,   326,   326,   326,   326,
   nil,   nil,   326,   nil,   nil,   nil,   nil,   nil,   326,   326,
   326,   326,   326,   326,   326,   326,   326,   326,   326,   326,
   nil,   326,   326,   326,   nil,   326,   326,   326,   326,   326,
   520,   520,   520,   520,   520,   520,   520,   520,   520,   520,
   520,   nil,   520,   520,   nil,   nil,   520,   520,   nil,   326,
   nil,   nil,   326,   nil,   nil,   326,   326,   nil,   nil,   326,
   nil,   326,   520,   nil,   520,   326,   520,   520,   nil,   520,
   520,   520,   520,   520,   326,   520,   nil,   nil,   nil,   326,
   326,   326,   326,   nil,   326,   326,   326,   326,   nil,   nil,
   nil,   nil,   326,   326,   nil,   520,   nil,   nil,   nil,   nil,
   326,   nil,   326,   326,   326,   499,   499,   499,   499,   499,
   499,   nil,   nil,   nil,   499,   499,   nil,   nil,   nil,   499,
   nil,   499,   499,   499,   499,   499,   499,   499,   nil,   nil,
   nil,   nil,   nil,   499,   499,   499,   499,   499,   499,   499,
   nil,   nil,   499,   nil,   nil,   nil,   nil,   nil,   499,   499,
   499,   499,   499,   499,   499,   499,   499,   499,   499,   499,
   nil,   499,   499,   499,   nil,   499,   499,   499,   499,   499,
   648,   648,   648,   648,   648,   648,   648,   648,   648,   648,
   648,   nil,   648,   648,   nil,   nil,   648,   648,   nil,   499,
   nil,   nil,   499,   nil,   nil,   499,   499,   nil,   nil,   499,
   nil,   499,   648,   nil,   648,   499,   648,   648,   nil,   648,
   648,   648,   648,   648,   499,   648,   nil,   nil,   nil,   499,
   499,   499,   499,   nil,   499,   499,   499,   499,   nil,   nil,
   nil,   nil,   499,   499,   nil,   648,   nil,   nil,   nil,   nil,
   499,   nil,   499,   499,   499,   536,   536,   536,   536,   536,
   536,   nil,   nil,   nil,   536,   536,   nil,   nil,   nil,   536,
   nil,   536,   536,   536,   536,   536,   536,   536,   nil,   nil,
   nil,   nil,   nil,   536,   536,   536,   536,   536,   536,   536,
   nil,   nil,   536,   nil,   nil,   nil,   nil,   nil,   536,   536,
   536,   536,   536,   536,   536,   536,   536,   536,   536,   536,
   nil,   536,   536,   536,   nil,   536,   536,   536,   536,   536,
   735,   735,   735,   735,   735,   735,   735,   735,   735,   735,
   735,   nil,   735,   735,   nil,   nil,   735,   735,   nil,   536,
   nil,   nil,   536,   nil,   nil,   536,   536,   nil,   nil,   536,
   nil,   536,   735,   nil,   735,   536,   735,   735,   nil,   735,
   735,   735,   735,   735,   536,   735,   nil,   nil,   nil,   536,
   536,   536,   536,   nil,   536,   536,   536,   536,   nil,   nil,
   nil,   nil,   536,   536,   nil,   735,   nil,   nil,   nil,   nil,
   536,   nil,   536,   536,   536,   539,   539,   539,   539,   539,
   539,   nil,   nil,   nil,   539,   539,   nil,   nil,   nil,   539,
   nil,   539,   539,   539,   539,   539,   539,   539,   nil,   nil,
   nil,   nil,   nil,   539,   539,   539,   539,   539,   539,   539,
   nil,   nil,   539,   nil,   nil,   nil,   nil,   nil,   539,   539,
   539,   539,   539,   539,   539,   539,   539,   539,   539,   539,
   nil,   539,   539,   539,   nil,   539,   539,   539,   539,   539,
   740,   740,   740,   740,   740,   740,   740,   740,   740,   740,
   740,   nil,   740,   740,   nil,   nil,   740,   740,   nil,   539,
   nil,   nil,   539,   nil,   nil,   539,   539,   nil,   nil,   539,
   nil,   539,   740,   nil,   740,   539,   740,   740,   nil,   740,
   740,   740,   740,   740,   539,   740,   nil,   nil,   nil,   539,
   539,   539,   539,   nil,   539,   539,   539,   539,   nil,   nil,
   nil,   nil,   539,   539,   nil,   740,   nil,   nil,   nil,   nil,
   539,   nil,   539,   539,   539,   561,   561,   561,   561,   561,
   561,   nil,   nil,   nil,   561,   561,   nil,   nil,   nil,   561,
   nil,   561,   561,   561,   561,   561,   561,   561,   nil,   nil,
   nil,   nil,   nil,   561,   561,   561,   561,   561,   561,   561,
   nil,   nil,   561,   nil,   nil,   nil,   nil,   nil,   561,   561,
   561,   561,   561,   561,   561,   561,   561,   561,   561,   561,
   nil,   561,   561,   561,   nil,   561,   561,   561,   561,   561,
   742,   742,   742,   742,   742,   742,   742,   742,   742,   742,
   742,   nil,   742,   742,   nil,   nil,   742,   742,   nil,   561,
   nil,   nil,   561,   nil,   nil,   561,   561,   nil,   nil,   561,
   nil,   561,   742,   nil,   742,   561,   742,   742,   nil,   742,
   742,   742,   742,   742,   561,   742,   nil,   nil,   nil,   561,
   561,   561,   561,   nil,   561,   561,   561,   561,   nil,   nil,
   nil,   nil,   561,   561,   nil,   742,   nil,   nil,   nil,   nil,
   561,   nil,   561,   561,   561,   618,   618,   618,   618,   618,
   618,   nil,   nil,   nil,   618,   618,   nil,   nil,   nil,   618,
   nil,   618,   618,   618,   618,   618,   618,   618,   nil,   nil,
   nil,   nil,   nil,   618,   618,   618,   618,   618,   618,   618,
   nil,   nil,   618,   nil,   nil,   nil,   nil,   nil,   618,   618,
   618,   618,   618,   618,   618,   618,   618,   618,   618,   618,
   nil,   618,   618,   618,   nil,   618,   618,   618,   618,   618,
   745,   745,   745,   745,   745,   745,   745,   745,   745,   745,
   745,   nil,   745,   745,   nil,   nil,   745,   745,   nil,   618,
   nil,   nil,   618,   nil,   nil,   618,   618,   nil,   nil,   618,
   nil,   618,   745,   nil,   745,   618,   745,   745,   nil,   745,
   745,   745,   745,   745,   618,   745,   nil,   nil,   nil,   618,
   618,   618,   618,   nil,   618,   618,   618,   618,   nil,   nil,
   nil,   nil,   618,   618,   nil,   745,   nil,   nil,   nil,   nil,
   618,   nil,   618,   618,   618,   623,   623,   623,   623,   623,
   623,   nil,   nil,   nil,   623,   623,   nil,   nil,   nil,   623,
   nil,   623,   623,   623,   623,   623,   623,   623,   nil,   nil,
   nil,   nil,   nil,   623,   623,   623,   623,   623,   623,   623,
   nil,   nil,   623,   nil,   nil,   nil,   nil,   nil,   623,   623,
   623,   623,   623,   623,   623,   623,   623,   623,   623,   623,
   nil,   623,   623,   623,   nil,   623,   623,   623,   623,   623,
   747,   747,   747,   747,   747,   747,   747,   747,   747,   747,
   747,   nil,   747,   747,   nil,   nil,   747,   747,   nil,   623,
   nil,   nil,   623,   nil,   nil,   623,   623,   nil,   nil,   623,
   nil,   623,   747,   nil,   747,   623,   747,   747,   nil,   747,
   747,   747,   747,   747,   623,   747,   nil,   nil,   nil,   623,
   623,   623,   623,   nil,   623,   623,   623,   623,   nil,   nil,
   nil,   nil,   623,   623,   nil,   747,   nil,   nil,   nil,   nil,
   623,   nil,   623,   623,   623,   624,   624,   624,   624,   624,
   624,   nil,   nil,   nil,   624,   624,   nil,   nil,   nil,   624,
   nil,   624,   624,   624,   624,   624,   624,   624,   nil,   nil,
   nil,   nil,   nil,   624,   624,   624,   624,   624,   624,   624,
   nil,   nil,   624,   nil,   nil,   nil,   nil,   nil,   624,   624,
   624,   624,   624,   624,   624,   624,   624,   624,   624,   624,
   nil,   624,   624,   624,   nil,   624,   624,   624,   624,   624,
   749,   749,   749,   749,   749,   749,   749,   749,   749,   749,
   749,   nil,   749,   749,   nil,   nil,   749,   749,   nil,   624,
   nil,   nil,   624,   nil,   nil,   624,   624,   nil,   nil,   624,
   nil,   624,   749,   nil,   749,   624,   749,   749,   nil,   749,
   749,   749,   749,   749,   624,   749,   nil,   nil,   nil,   624,
   624,   624,   624,   nil,   624,   624,   624,   624,   nil,   nil,
   nil,   nil,   624,   624,   nil,   749,   nil,   nil,   nil,   nil,
   624,   nil,   624,   624,   624,   710,   710,   710,   710,   710,
   710,   nil,   nil,   nil,   710,   710,   nil,   nil,   nil,   710,
   nil,   710,   710,   710,   710,   710,   710,   710,   nil,   nil,
   nil,   nil,   nil,   710,   710,   710,   710,   710,   710,   710,
   nil,   nil,   710,   nil,   nil,   nil,   nil,   nil,   710,   710,
   710,   710,   710,   710,   710,   710,   710,   710,   710,   710,
   nil,   710,   710,   710,   nil,   710,   710,   710,   710,   710,
   839,   839,   839,   839,   839,   839,   839,   839,   839,   839,
   839,   nil,   839,   839,   nil,   nil,   839,   839,   nil,   710,
   nil,   nil,   710,   nil,   nil,   710,   710,   nil,   nil,   710,
   nil,   710,   839,   nil,   839,   710,   839,   839,   nil,   839,
   839,   839,   839,   839,   710,   839,   nil,   nil,   nil,   710,
   710,   710,   710,   nil,   710,   710,   710,   710,   nil,   nil,
   nil,   nil,   710,   710,   nil,   839,   nil,   nil,   nil,   nil,
   710,   nil,   710,   710,   710,   715,   715,   715,   715,   715,
   715,   nil,   nil,   nil,   715,   715,   nil,   nil,   nil,   715,
   nil,   715,   715,   715,   715,   715,   715,   715,   nil,   nil,
   nil,   nil,   nil,   715,   715,   715,   715,   715,   715,   715,
   nil,   nil,   715,   nil,   nil,   nil,   nil,   nil,   715,   715,
   715,   715,   715,   715,   715,   715,   715,   715,   715,   715,
   nil,   715,   715,   715,   nil,   715,   715,   715,   715,   715,
   842,   842,   842,   842,   842,   842,   842,   842,   842,   842,
   842,   nil,   842,   842,   nil,   nil,   842,   842,   nil,   715,
   nil,   nil,   715,   nil,   nil,   715,   715,   nil,   nil,   715,
   nil,   715,   842,   nil,   842,   715,   842,   842,   nil,   842,
   842,   842,   842,   842,   715,   842,   nil,   nil,   nil,   715,
   715,   715,   715,   nil,   715,   715,   715,   715,   nil,   nil,
   nil,   nil,   715,   715,   nil,   842,   nil,   nil,   nil,   nil,
   715,   nil,   715,   715,   715,   725,   725,   725,   725,   725,
   725,   nil,   nil,   nil,   725,   725,   nil,   nil,   nil,   725,
   nil,   725,   725,   725,   725,   725,   725,   725,   nil,   nil,
   nil,   nil,   nil,   725,   725,   725,   725,   725,   725,   725,
   470,   888,   725,   888,   888,   888,   nil,   888,   725,   725,
   725,   725,   725,   725,   725,   725,   725,   725,   725,   725,
   nil,   725,   725,   725,   nil,   725,   725,   725,   725,   725,
   nil,   nil,   470,   nil,   nil,   nil,   470,   470,   888,   470,
   470,   nil,   nil,   nil,   nil,   nil,   nil,   888,   nil,   725,
   nil,   nil,   725,   nil,   nil,   725,   725,   nil,   nil,   725,
   950,   725,   950,   950,   950,   725,   950,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   725,   nil,   nil,   nil,   nil,   725,
   725,   725,   725,   nil,   725,   725,   725,   725,   nil,   nil,
   nil,   nil,   725,   725,   nil,   nil,   nil,   950,   nil,   nil,
   725,   nil,   725,   725,   725,   773,   773,   773,   773,   773,
   773,   nil,   nil,   nil,   773,   773,   nil,   nil,   nil,   773,
   nil,   773,   773,   773,   773,   773,   773,   773,   nil,   nil,
   nil,   nil,   nil,   773,   773,   773,   773,   773,   773,   773,
   nil,   nil,   773,   nil,   nil,   nil,   nil,   nil,   773,   773,
   773,   773,   773,   773,   773,   773,   773,   773,   773,   773,
   nil,   773,   773,   773,   nil,   773,   773,   773,   773,   773,
   448,   448,   448,   448,   448,   448,   448,   448,   448,   448,
   448,   nil,   448,   448,   nil,   nil,   448,   448,   nil,   773,
   nil,   nil,   773,   nil,   nil,   773,   773,   nil,   nil,   773,
   nil,   773,   448,   nil,   448,   773,   448,   448,   nil,   448,
   448,   448,   448,   448,   773,   448,   nil,   nil,   nil,   773,
   773,   773,   773,   nil,   773,   773,   773,   773,   nil,   nil,
   nil,   nil,   773,   773,   nil,   987,   nil,   987,   987,   987,
   773,   987,   773,   773,   773,   786,   786,   786,   786,   786,
   786,   nil,   nil,   nil,   786,   786,   nil,   nil,   nil,   786,
   nil,   786,   786,   786,   786,   786,   786,   786,   nil,   nil,
   nil,   nil,   987,   786,   786,   786,   786,   786,   786,   786,
   nil,   nil,   786,   nil,   nil,   nil,   nil,   nil,   786,   786,
   786,   786,   786,   786,   786,   786,   786,   786,   786,   786,
   nil,   786,   786,   786,   nil,   786,   786,   786,   786,   786,
   449,   449,   449,   449,   449,   449,   449,   449,   449,   449,
   449,   nil,   449,   449,   nil,   nil,   449,   449,   nil,   786,
   nil,   nil,   786,   nil,   nil,   786,   786,   nil,   nil,   786,
   nil,   786,   449,   nil,   449,   786,   449,   449,   nil,   449,
   449,   449,   449,   449,   786,   449,   nil,   nil,   nil,   786,
   786,   786,   786,   nil,   786,   786,   786,   786,   nil,   nil,
   nil,   nil,   786,   786,   nil,   989,   nil,   989,   989,   989,
   786,   989,   786,   786,   786,   820,   820,   820,   820,   820,
   820,   nil,   nil,   nil,   820,   820,   nil,   nil,   nil,   820,
   nil,   820,   820,   820,   820,   820,   820,   820,   nil,   nil,
   nil,   nil,   989,   820,   820,   820,   820,   820,   820,   820,
   nil,  1011,   820,  1011,  1011,  1011,   nil,  1011,   820,   820,
   820,   820,   820,   820,   820,   820,   820,   820,   820,   820,
   nil,   820,   820,   820,   nil,   820,   820,   820,   820,   820,
   459,   459,   459,   459,   459,   459,   459,   nil,  1011,   459,
   459,   nil,   nil,   nil,   nil,   nil,   459,   459,   nil,   820,
   nil,   nil,   820,   nil,   nil,   820,   820,   nil,   nil,   820,
   nil,   820,   459,   nil,   459,   820,   459,   459,   nil,   459,
   459,   459,   459,   459,   820,   459,   nil,   nil,   nil,   820,
   820,   820,   820,   nil,   820,   820,   820,   820,   nil,   nil,
   nil,   nil,   820,   820,   nil,   nil,   nil,   nil,   nil,   nil,
   820,   nil,   820,   820,   820,   821,   821,   821,   821,   821,
   821,   nil,   nil,   nil,   821,   821,   nil,   nil,   nil,   821,
   nil,   821,   821,   821,   821,   821,   821,   821,   nil,   nil,
   nil,   nil,   nil,   821,   821,   821,   821,   821,   821,   821,
   nil,   nil,   821,   nil,   nil,   nil,   nil,   nil,   821,   821,
   821,   821,   821,   821,   821,   821,   821,   821,   821,   821,
   nil,   821,   821,   821,   nil,   821,   821,   821,   821,   821,
   460,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   460,   460,   nil,   821,
   nil,   nil,   821,   nil,   nil,   821,   821,   nil,   nil,   821,
   nil,   821,   460,   nil,   460,   821,   460,   460,   nil,   460,
   460,   nil,   nil,   460,   821,   460,   nil,   nil,   nil,   821,
   821,   821,   821,   nil,   821,   821,   821,   821,   nil,   nil,
   nil,   nil,   821,   821,   nil,   nil,   nil,   nil,   nil,   nil,
   821,   nil,   821,   821,   821,   824,   824,   824,   824,   824,
   824,   nil,   nil,   nil,   824,   824,   nil,   nil,   nil,   824,
   nil,   824,   824,   824,   824,   824,   824,   824,   nil,   nil,
   nil,   nil,   nil,   824,   824,   824,   824,   824,   824,   824,
   nil,   nil,   824,   nil,   nil,   nil,   nil,   nil,   824,   824,
   824,   824,   824,   824,   824,   824,   824,   824,   824,   824,
   nil,   824,   824,   824,   nil,   824,   824,   824,   824,   824,
   461,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   461,   461,   nil,   824,
   nil,   nil,   824,   nil,   nil,   824,   824,   nil,   nil,   824,
   nil,   824,   461,   nil,   461,   824,   461,   461,   nil,   461,
   461,   nil,   nil,   461,   824,   461,   nil,   nil,   nil,   824,
   824,   824,   824,   nil,   824,   824,   824,   824,   nil,   nil,
   nil,   nil,   824,   824,   nil,   nil,   nil,   nil,   nil,   nil,
   824,   nil,   824,   824,   824,   830,   830,   830,   830,   830,
   830,   nil,   nil,   nil,   830,   830,   nil,   nil,   nil,   830,
   nil,   830,   830,   830,   830,   830,   830,   830,   nil,   nil,
   nil,   nil,   nil,   830,   830,   830,   830,   830,   830,   830,
   nil,   nil,   830,   nil,   nil,   nil,   nil,   nil,   830,   830,
   830,   830,   830,   830,   830,   830,   830,   830,   830,   830,
   nil,   830,   830,   830,   nil,   830,   830,   830,   830,   830,
   462,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   462,   462,   nil,   830,
   nil,   nil,   830,   nil,   nil,   830,   830,   nil,   nil,   830,
   nil,   830,   462,   nil,   462,   830,   462,   462,   nil,   462,
   462,   nil,   nil,   462,   830,   462,   nil,   nil,   nil,   830,
   830,   830,   830,   nil,   830,   830,   830,   830,   nil,   nil,
   nil,   nil,   830,   830,   nil,   nil,   nil,   nil,   nil,   nil,
   830,   nil,   830,   830,   830,   863,   863,   863,   863,   863,
   863,   nil,   nil,   nil,   863,   863,   nil,   nil,   nil,   863,
   nil,   863,   863,   863,   863,   863,   863,   863,   nil,   nil,
   nil,   nil,   nil,   863,   863,   863,   863,   863,   863,   863,
   nil,   nil,   863,   nil,   nil,   nil,   nil,   nil,   863,   863,
   863,   863,   863,   863,   863,   863,   863,   863,   863,   863,
   nil,   863,   863,   863,   nil,   863,   863,   863,   863,   863,
   463,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   463,   463,   nil,   863,
   nil,   nil,   863,   nil,   nil,   863,   863,   nil,   nil,   863,
   nil,   863,   463,   nil,   463,   863,   463,   463,   nil,   463,
   463,   nil,   nil,   463,   863,   463,   nil,   nil,   nil,   863,
   863,   863,   863,   nil,   863,   863,   863,   863,   nil,   nil,
   nil,   nil,   863,   863,   nil,   nil,   nil,   nil,   nil,   nil,
   863,   nil,   863,   863,   863,   927,   927,   927,   927,   927,
   927,   nil,   nil,   nil,   927,   927,   nil,   nil,   nil,   927,
   nil,   927,   927,   927,   927,   927,   927,   927,   nil,   nil,
   nil,   nil,   nil,   927,   927,   927,   927,   927,   927,   927,
   nil,   nil,   927,   nil,   nil,   nil,   nil,   nil,   927,   927,
   927,   927,   927,   927,   927,   927,   927,   927,   927,   927,
   nil,   927,   927,   927,   nil,   927,   927,   927,   927,   927,
   464,   464,   464,   464,   464,   464,   464,   nil,   nil,   464,
   464,   nil,   nil,   nil,   nil,   nil,   464,   464,   nil,   927,
   nil,   nil,   927,   nil,   nil,   927,   927,   nil,   nil,   927,
   nil,   927,   464,   nil,   464,   927,   464,   464,   nil,   464,
   464,   464,   464,   464,   927,   464,   nil,   nil,   nil,   927,
   927,   927,   927,   nil,   927,   927,   927,   927,   nil,   nil,
   nil,   nil,   927,   927,   nil,   nil,   nil,   nil,   nil,   nil,
   927,   nil,   927,   927,   927,   934,   934,   934,   934,   934,
   934,   nil,   nil,   nil,   934,   934,   nil,   nil,   nil,   934,
   nil,   934,   934,   934,   934,   934,   934,   934,   nil,   nil,
   nil,   nil,   nil,   934,   934,   934,   934,   934,   934,   934,
   nil,   nil,   934,   nil,   nil,   nil,   nil,   nil,   934,   934,
   934,   934,   934,   934,   934,   934,   934,   934,   934,   934,
   nil,   934,   934,   934,   nil,   934,   934,   934,   934,   934,
   465,   465,   465,   465,   465,   465,   465,   nil,   nil,   465,
   465,   nil,   nil,   nil,   nil,   nil,   465,   465,   nil,   934,
   nil,   nil,   934,   nil,   nil,   934,   934,   nil,   nil,   934,
   nil,   934,   465,   nil,   465,   934,   465,   465,   nil,   465,
   465,   465,   465,   465,   934,   465,   nil,   nil,   nil,   934,
   934,   934,   934,   nil,   934,   934,   934,   934,   nil,   nil,
   nil,   nil,   934,   934,   nil,   nil,   nil,   nil,   nil,   nil,
   934,   nil,   934,   934,   934,   935,   935,   935,   935,   935,
   935,   nil,   nil,   nil,   935,   935,   nil,   nil,   nil,   935,
   nil,   935,   935,   935,   935,   935,   935,   935,   nil,   nil,
   nil,   nil,   nil,   935,   935,   935,   935,   935,   935,   935,
   nil,   nil,   935,   nil,   nil,   nil,   nil,   nil,   935,   935,
   935,   935,   935,   935,   935,   935,   935,   935,   935,   935,
   nil,   935,   935,   935,   nil,   935,   935,   935,   935,   935,
   466,   466,   466,   466,   466,   466,   466,   nil,   nil,   466,
   466,   nil,   nil,   nil,   nil,   nil,   466,   466,   nil,   935,
   nil,   nil,   935,   nil,   nil,   935,   935,   nil,   nil,   935,
   nil,   935,   466,   nil,   466,   935,   466,   466,   nil,   466,
   466,   466,   466,   466,   935,   466,   nil,   nil,   nil,   935,
   935,   935,   935,   nil,   935,   935,   935,   935,   nil,   nil,
   nil,   nil,   935,   935,   nil,   nil,   nil,   nil,   nil,   nil,
   935,   nil,   935,   935,   935,   952,   952,   952,   952,   952,
   952,   nil,   nil,   nil,   952,   952,   nil,   nil,   nil,   952,
   nil,   952,   952,   952,   952,   952,   952,   952,   nil,   nil,
   nil,   nil,   nil,   952,   952,   952,   952,   952,   952,   952,
   nil,   nil,   952,   nil,   nil,   nil,   nil,   nil,   952,   952,
   952,   952,   952,   952,   952,   952,   952,   952,   952,   952,
   nil,   952,   952,   952,   nil,   952,   952,   952,   952,   952,
   467,   467,   467,   467,   467,   467,   467,   nil,   nil,   467,
   467,   nil,   nil,   nil,   nil,   nil,   467,   467,   nil,   952,
   nil,   nil,   952,   nil,   nil,   952,   952,   nil,   nil,   952,
   nil,   952,   467,   nil,   467,   952,   467,   467,   nil,   467,
   467,   467,   467,   467,   952,   467,   nil,   nil,   nil,   952,
   952,   952,   952,   nil,   952,   952,   952,   952,   nil,   nil,
   nil,   nil,   952,   952,   nil,   nil,   nil,   nil,   nil,   nil,
   952,   nil,   952,   952,   952,   958,   958,   958,   958,   958,
   958,   nil,   nil,   nil,   958,   958,   nil,   nil,   nil,   958,
   nil,   958,   958,   958,   958,   958,   958,   958,   nil,   nil,
   nil,   nil,   nil,   958,   958,   958,   958,   958,   958,   958,
   nil,   nil,   958,   nil,   nil,   nil,   nil,   nil,   958,   958,
   958,   958,   958,   958,   958,   958,   958,   958,   958,   958,
   nil,   958,   958,   958,   nil,   958,   958,   958,   958,   958,
   468,   468,   468,   468,   468,   468,   468,   nil,   nil,   468,
   468,   nil,   nil,   nil,   nil,   nil,   468,   468,   nil,   958,
   nil,   nil,   958,   nil,   nil,   958,   958,   nil,   nil,   958,
   nil,   958,   468,   nil,   468,   958,   468,   468,   nil,   468,
   468,   468,   468,   468,   958,   468,   nil,   nil,   nil,   958,
   958,   958,   958,   nil,   958,   958,   958,   958,   nil,   nil,
   nil,   nil,   958,   958,   nil,   nil,   nil,   nil,   nil,   nil,
   958,   nil,   958,   958,   958,   960,   960,   960,   960,   960,
   960,   nil,   nil,   nil,   960,   960,   nil,   nil,   nil,   960,
   nil,   960,   960,   960,   960,   960,   960,   960,   nil,   nil,
   nil,   nil,   nil,   960,   960,   960,   960,   960,   960,   960,
   nil,   nil,   960,   nil,   nil,   nil,   nil,   nil,   960,   960,
   960,   960,   960,   960,   960,   960,   960,   960,   960,   960,
   nil,   960,   960,   960,   nil,   960,   960,   960,   960,   960,
   471,   471,   471,   471,   471,   471,   471,   nil,   nil,   471,
   471,   nil,   nil,   nil,   nil,   nil,   471,   471,   nil,   960,
   nil,   nil,   960,   nil,   nil,   960,   960,   nil,   nil,   960,
   nil,   960,   471,   nil,   471,   960,   471,   471,   nil,   471,
   471,   471,   471,   471,   960,   471,   nil,   nil,   nil,   960,
   960,   960,   960,   nil,   960,   960,   960,   960,   nil,   nil,
   nil,   nil,   960,   960,   nil,   nil,   nil,   nil,   nil,   nil,
   960,   nil,   960,   960,   960,     5,     5,     5,     5,     5,
   nil,   nil,   nil,     5,     5,   nil,   nil,   nil,     5,   nil,
     5,     5,     5,     5,     5,     5,     5,   nil,   nil,   nil,
   nil,   nil,     5,     5,     5,     5,     5,     5,     5,   nil,
   nil,     5,   nil,   nil,   nil,   nil,   nil,     5,     5,     5,
     5,     5,     5,     5,     5,     5,     5,     5,     5,   nil,
     5,     5,     5,   nil,     5,     5,     5,     5,     5,   472,
   472,   472,   472,   472,   472,   472,   472,   nil,   472,   472,
   nil,   nil,   nil,   nil,   nil,   472,   472,   nil,     5,   nil,
   nil,     5,   nil,   nil,     5,     5,   nil,   nil,     5,   nil,
     5,   472,   nil,   472,     5,   472,   472,   nil,   472,   472,
   472,   472,   472,     5,   472,   nil,   nil,   nil,     5,     5,
     5,     5,   nil,     5,     5,     5,     5,   nil,   nil,   nil,
   nil,     5,     5,   nil,   nil,   nil,    20,    20,    20,     5,
    20,     5,     5,     5,    20,    20,   nil,   nil,   nil,    20,
   nil,    20,    20,    20,    20,    20,    20,    20,   nil,   nil,
   nil,   nil,   nil,    20,    20,    20,    20,    20,    20,    20,
   nil,   nil,    20,   nil,   nil,   nil,   nil,   nil,   nil,    20,
   nil,   nil,    20,    20,    20,    20,    20,    20,    20,    20,
   nil,    20,    20,    20,   nil,    20,    20,    20,    20,    20,
   456,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   456,   456,   nil,    20,
   nil,   nil,    20,   nil,   nil,    20,    20,   nil,   nil,    20,
   nil,   nil,   456,   nil,   456,    20,   456,   456,   nil,   456,
   456,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    20,
    20,    20,    20,   nil,    20,    20,    20,    20,   nil,   nil,
   nil,   nil,    20,    20,   nil,   nil,   nil,    29,    29,    29,
    20,    29,    20,    20,    20,    29,    29,   nil,   nil,   nil,
    29,   nil,    29,    29,    29,    29,    29,    29,    29,   nil,
   nil,   nil,   nil,   nil,    29,    29,    29,    29,    29,    29,
    29,   nil,   nil,    29,   nil,   nil,   nil,   nil,   nil,   nil,
    29,   nil,   nil,    29,    29,    29,    29,    29,    29,    29,
    29,    29,    29,    29,    29,   nil,    29,    29,    29,    29,
    29,   457,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   457,   457,   nil,
    29,   nil,   nil,    29,   nil,   nil,    29,    29,   nil,   nil,
    29,   nil,    29,   457,    29,   457,    29,   457,   457,    29,
   457,   457,   nil,   nil,   nil,    29,   nil,   nil,   nil,   nil,
    29,    29,    29,    29,   nil,    29,    29,    29,    29,   nil,
   nil,   nil,   nil,    29,    29,   nil,   nil,   nil,    30,    30,
    30,    29,    30,    29,    29,    29,    30,    30,   nil,   nil,
   nil,    30,   nil,    30,    30,    30,    30,    30,    30,    30,
   nil,   nil,   nil,   nil,   nil,    30,    30,    30,    30,    30,
    30,    30,   nil,   nil,    30,   nil,   nil,   nil,   nil,   nil,
   nil,    30,   nil,   nil,    30,    30,    30,    30,    30,    30,
    30,    30,    30,    30,    30,    30,   nil,    30,    30,    30,
    30,    30,   458,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   458,   458,
   nil,    30,   nil,   nil,    30,   nil,   nil,    30,    30,   nil,
   nil,    30,   nil,    30,   458,    30,   nil,    30,   458,   458,
    30,   458,   458,   nil,   nil,   nil,    30,   nil,   nil,   nil,
   nil,    30,    30,    30,    30,   nil,    30,    30,    30,    30,
   nil,   nil,   nil,   nil,    30,    30,   nil,   nil,   nil,    31,
    31,    31,    30,    31,    30,    30,    30,    31,    31,   nil,
   nil,   nil,    31,   nil,    31,    31,    31,    31,    31,    31,
    31,   nil,   nil,   nil,   nil,   nil,    31,    31,    31,    31,
    31,    31,    31,   nil,   nil,    31,   nil,   nil,   nil,   nil,
   nil,   nil,    31,   nil,   nil,    31,    31,    31,    31,    31,
    31,    31,    31,    31,    31,    31,    31,   nil,    31,    31,
    31,    31,    31,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    31,   nil,   nil,    31,   nil,   nil,    31,    31,
   nil,   nil,    31,   nil,    31,   nil,    31,   nil,    31,   nil,
   nil,    31,   nil,   nil,   nil,   nil,   nil,    31,   nil,   nil,
   nil,   nil,    31,    31,    31,    31,   nil,    31,    31,    31,
    31,   nil,   nil,   nil,   nil,    31,    31,   nil,   nil,   nil,
    34,    34,    34,    31,    34,    31,    31,    31,    34,    34,
   nil,   nil,   nil,    34,   nil,    34,    34,    34,    34,    34,
    34,    34,   nil,   nil,   nil,   nil,   nil,    34,    34,    34,
    34,    34,    34,    34,   nil,   nil,    34,   nil,   nil,   nil,
   nil,   nil,   nil,    34,   nil,   nil,    34,    34,    34,    34,
    34,    34,    34,    34,   nil,    34,    34,    34,   nil,    34,
    34,   nil,   nil,    34,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    34,   nil,   nil,    34,   nil,   nil,    34,
    34,   nil,   nil,    34,   nil,    34,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    34,    34,    34,    34,   nil,    34,    34,
    34,    34,   nil,   nil,   nil,   nil,    34,    34,   nil,   nil,
   nil,    35,    35,    35,    34,    35,    34,    34,    34,    35,
    35,   nil,   nil,   nil,    35,   nil,    35,    35,    35,    35,
    35,    35,    35,   nil,   nil,   nil,   nil,   nil,    35,    35,
    35,    35,    35,    35,    35,   nil,   nil,    35,   nil,   nil,
   nil,   nil,   nil,   nil,    35,   nil,   nil,    35,    35,    35,
    35,    35,    35,    35,    35,   nil,    35,    35,    35,   nil,
    35,    35,   nil,   nil,    35,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    35,   nil,   nil,    35,   nil,   nil,
    35,    35,   nil,   nil,    35,   nil,   nil,   803,   nil,   803,
   803,   803,   803,   803,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   803,   nil,    35,    35,    35,    35,   nil,    35,
    35,    35,    35,   nil,   nil,   nil,   nil,    35,    35,   nil,
   nil,   nil,    35,   nil,   803,    35,   nil,    35,    35,    35,
    42,    42,    42,   nil,    42,   803,   803,   nil,    42,    42,
   803,   nil,   nil,    42,   nil,    42,    42,    42,    42,    42,
    42,    42,   nil,   nil,   nil,   nil,   nil,    42,    42,    42,
    42,    42,    42,    42,   nil,   nil,    42,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    42,    42,    42,    42,
    42,    42,    42,    42,   nil,    42,    42,    42,   nil,    42,
    42,    42,    42,    42,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,    42,   nil,   nil,    42,
    42,   nil,   nil,    42,   nil,   nil,   nil,   nil,   nil,    42,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,   nil,   nil,    42,    42,    42,    42,   nil,    42,    42,
    42,    42,   nil,   nil,   nil,   nil,    42,    42,   nil,   nil,
   nil,    43,    43,    43,    42,    43,    42,    42,    42,    43,
    43,   nil,   nil,   nil,    43,   nil,    43,    43,    43,    43,
    43,    43,    43,   nil,   nil,   nil,   nil,   nil,    43,    43,
    43,    43,    43,    43,    43,   nil,   nil,    43,   nil,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   nil,    43,    43,    43,
    43,    43,    43,    43,    43,   nil,    43,    43,    43,   nil,
    43,    43,    43,    43,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   nil,    43,   nil,   nil,
    43,    43,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,    43,    43,    43,    43,   nil,    43,
    43,    43,    43,   nil,   nil,   nil,   nil,    43,    43,   nil,
   nil,   nil,    44,    44,    44,    43,    44,    43,    43,    43,
    44,    44,   nil,   nil,   nil,    44,   nil,    44,    44,    44,
    44,    44,    44,    44,   nil,   nil,   nil,   nil,   nil,    44,
    44,    44,    44,    44,    44,    44,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,    44,    44,
    44,    44,    44,    44,    44,    44,   nil,    44,    44,    44,
   nil,    44,    44,    44,    44,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,    44,   nil,
   nil,    44,    44,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,    44,    44,    44,    44,   nil,
    44,    44,    44,    44,   nil,   nil,   nil,   nil,    44,    44,
   nil,   nil,   nil,    59,    59,    59,    44,    59,    44,    44,
    44,    59,    59,   nil,   nil,   nil,    59,   nil,    59,    59,
    59,    59,    59,    59,    59,   nil,   nil,   nil,   nil,   nil,
    59,    59,    59,    59,    59,    59,    59,   nil,   nil,    59,
   nil,   nil,   nil,   nil,   nil,   nil,    59,   nil,   nil,    59,
    59,    59,    59,    59,    59,    59,    59,    59,    59,    59,
    59,   nil,    59,    59,    59,    59,    59,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    59,   nil,   nil,    59,
   nil,   nil,    59,    59,   nil,   nil,    59,   nil,    59,   nil,
   nil,   nil,    59,   nil,   nil,    59,   nil,   nil,   nil,   nil,
   nil,    59,   nil,   nil,   nil,   nil,    59,    59,    59,    59,
   nil,    59,    59,    59,    59,   nil,   nil,   nil,   nil,    59,
    59,   nil,   nil,   nil,    60,    60,    60,    59,    60,    59,
    59,    59,    60,    60,   nil,   nil,   nil,    60,   nil,    60,
    60,    60,    60,    60,    60,    60,   nil,   nil,   nil,   nil,
   nil,    60,    60,    60,    60,    60,    60,    60,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,   nil,    60,   nil,   nil,
    60,    60,    60,    60,    60,    60,    60,    60,    60,    60,
    60,    60,   nil,    60,    60,    60,    60,    60,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    60,   nil,   nil,
    60,   nil,   nil,    60,    60,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    60,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    60,   nil,   nil,   nil,   nil,    60,    60,    60,
    60,   nil,    60,    60,    60,    60,   nil,   nil,   nil,   nil,
    60,    60,   nil,   nil,   nil,    63,    63,    63,    60,    63,
    60,    60,    60,    63,    63,   nil,   nil,   nil,    63,   nil,
    63,    63,    63,    63,    63,    63,    63,   nil,   nil,   nil,
   nil,   nil,    63,    63,    63,    63,    63,    63,    63,   nil,
   nil,    63,   nil,   nil,   nil,   nil,   nil,   nil,    63,   nil,
   nil,    63,    63,    63,    63,    63,    63,    63,    63,   nil,
    63,    63,    63,   nil,    63,    63,    63,    63,    63,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    63,   nil,
   nil,    63,   nil,   nil,    63,    63,   nil,   nil,    63,   nil,
   nil,   nil,   nil,   nil,    63,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    63,   nil,   nil,   nil,   nil,    63,    63,
    63,    63,   nil,    63,    63,    63,    63,   nil,   nil,   nil,
   nil,    63,    63,   nil,   nil,   nil,    64,    64,    64,    63,
    64,    63,    63,    63,    64,    64,   nil,   nil,   nil,    64,
   nil,    64,    64,    64,    64,    64,    64,    64,   nil,   nil,
   nil,   nil,   nil,    64,    64,    64,    64,    64,    64,    64,
   nil,   nil,    64,   nil,   nil,   nil,   nil,   nil,   nil,    64,
   nil,   nil,    64,    64,    64,    64,    64,    64,    64,    64,
   nil,    64,    64,    64,   nil,    64,    64,    64,    64,    64,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    64,
   nil,   nil,    64,   nil,   nil,    64,    64,   nil,   nil,    64,
   nil,   nil,   nil,   nil,   nil,    64,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    64,   nil,   nil,   nil,   nil,    64,
    64,    64,    64,   nil,    64,    64,    64,    64,   nil,   nil,
   nil,   nil,    64,    64,   nil,   nil,   nil,    67,    67,    67,
    64,    67,    64,    64,    64,    67,    67,   nil,   nil,   nil,
    67,   nil,    67,    67,    67,    67,    67,    67,    67,   nil,
   nil,   nil,   nil,   nil,    67,    67,    67,    67,    67,    67,
    67,   nil,   nil,    67,   nil,   nil,   nil,   nil,   nil,   nil,
    67,   nil,   nil,    67,    67,    67,    67,    67,    67,    67,
    67,   nil,    67,    67,    67,   nil,    67,    67,    67,    67,
    67,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    67,   nil,   nil,    67,   nil,   nil,    67,    67,   nil,   nil,
    67,   nil,   nil,   nil,   nil,   nil,    67,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    67,   nil,   nil,   nil,   nil,
    67,    67,    67,    67,   nil,    67,    67,    67,    67,   nil,
   nil,   nil,   nil,    67,    67,    67,   nil,   nil,   nil,   nil,
    67,    67,   nil,    67,    67,    67,    68,    68,    68,   nil,
    68,   nil,   nil,   nil,    68,    68,   nil,   nil,   nil,    68,
   nil,    68,    68,    68,    68,    68,    68,    68,   nil,   nil,
   nil,   nil,   nil,    68,    68,    68,    68,    68,    68,    68,
   nil,   nil,    68,   nil,   nil,   nil,   nil,   nil,   nil,    68,
   nil,   nil,    68,    68,    68,    68,    68,    68,    68,    68,
   nil,    68,    68,    68,   nil,    68,    68,   nil,   nil,    68,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    68,
   nil,   nil,    68,   nil,   nil,    68,    68,   nil,   nil,    68,
   nil,    68,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    68,
    68,    68,    68,   nil,    68,    68,    68,    68,   nil,   nil,
   nil,   nil,    68,    68,   nil,   nil,   nil,    69,    69,    69,
    68,    69,    68,    68,    68,    69,    69,   nil,   nil,   nil,
    69,   nil,    69,    69,    69,    69,    69,    69,    69,   nil,
   nil,   nil,   nil,   nil,    69,    69,    69,    69,    69,    69,
    69,   nil,   nil,    69,   nil,   nil,   nil,   nil,   nil,   nil,
    69,   nil,   nil,    69,    69,    69,    69,    69,    69,    69,
    69,   nil,    69,    69,    69,   nil,    69,    69,   nil,   nil,
    69,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    69,   nil,   nil,
    69,   nil,   nil,    69,   nil,   nil,    69,    69,   nil,   nil,
    69,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    69,    69,    69,    69,   nil,    69,    69,    69,    69,   nil,
   nil,   nil,   nil,    69,    69,   nil,   nil,   nil,    70,    70,
    70,    69,    70,    69,    69,    69,    70,    70,   nil,   nil,
   nil,    70,   nil,    70,    70,    70,    70,    70,    70,    70,
   nil,   nil,   nil,   nil,   nil,    70,    70,    70,    70,    70,
    70,    70,   nil,   nil,    70,   nil,   nil,   nil,   nil,   nil,
   nil,    70,   nil,   nil,    70,    70,    70,    70,    70,    70,
    70,    70,   nil,    70,    70,    70,   nil,    70,    70,   nil,
   nil,    70,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    70,   nil,   nil,    70,   nil,   nil,    70,    70,   nil,
   nil,    70,   nil,   nil,   850,   nil,   850,   850,   850,   850,
   850,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   850,
   nil,    70,    70,    70,    70,   nil,    70,    70,    70,    70,
   nil,   nil,   nil,   nil,    70,    70,   nil,   nil,   nil,   nil,
   nil,   850,    70,   nil,    70,    70,    70,   113,   113,   113,
   113,   113,   850,   850,   nil,   113,   113,   850,   nil,   nil,
   113,   nil,   113,   113,   113,   113,   113,   113,   113,   nil,
   nil,   nil,   nil,   nil,   113,   113,   113,   113,   113,   113,
   113,   nil,   nil,   113,   nil,   nil,   nil,   nil,   nil,   113,
   113,   113,   113,   113,   113,   113,   113,   113,   113,   113,
   113,   nil,   113,   113,   113,   nil,   113,   113,   113,   113,
   113,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   113,   nil,   nil,   113,   nil,   nil,   113,   113,   nil,   nil,
   113,   nil,   113,   nil,   nil,   nil,   113,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   113,   nil,   nil,   nil,   nil,
   113,   113,   113,   113,   nil,   113,   113,   113,   113,   nil,
   nil,   nil,   nil,   113,   113,   nil,   nil,   nil,   nil,   nil,
   113,   113,   nil,   113,   113,   113,   118,   118,   118,   nil,
   118,   nil,   nil,   nil,   118,   118,   nil,   nil,   nil,   118,
   nil,   118,   118,   118,   118,   118,   118,   118,   nil,   nil,
   nil,   nil,   nil,   118,   118,   118,   118,   118,   118,   118,
   nil,   nil,   118,   nil,   nil,   nil,   nil,   nil,   nil,   118,
   nil,   nil,   118,   118,   118,   118,   118,   118,   118,   118,
   nil,   118,   118,   118,   nil,   118,   118,   118,   118,   118,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   118,
   nil,   nil,   118,   nil,   nil,   118,   118,   nil,   nil,   118,
   nil,   nil,   nil,   nil,   nil,   118,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   118,   nil,   nil,   nil,   nil,   118,
   118,   118,   118,   nil,   118,   118,   118,   118,   nil,   nil,
   nil,   nil,   118,   118,   nil,   nil,   nil,   119,   119,   119,
   118,   119,   118,   118,   118,   119,   119,   nil,   nil,   nil,
   119,   nil,   119,   119,   119,   119,   119,   119,   119,   nil,
   nil,   nil,   nil,   nil,   119,   119,   119,   119,   119,   119,
   119,   nil,   nil,   119,   nil,   nil,   nil,   nil,   nil,   nil,
   119,   nil,   nil,   119,   119,   119,   119,   119,   119,   119,
   119,   nil,   119,   119,   119,   nil,   119,   119,   119,   119,
   119,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   119,   nil,   nil,   119,   nil,   nil,   119,   119,   nil,   nil,
   119,   nil,   nil,   nil,   nil,   nil,   119,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   119,   nil,   nil,   nil,   nil,
   119,   119,   119,   119,   nil,   119,   119,   119,   119,   nil,
   nil,   nil,   nil,   119,   119,   nil,   nil,   nil,   120,   120,
   120,   119,   120,   119,   119,   119,   120,   120,   nil,   nil,
   nil,   120,   nil,   120,   120,   120,   120,   120,   120,   120,
   nil,   nil,   nil,   nil,   nil,   120,   120,   120,   120,   120,
   120,   120,   nil,   nil,   120,   nil,   nil,   nil,   nil,   nil,
   nil,   120,   nil,   nil,   120,   120,   120,   120,   120,   120,
   120,   120,   nil,   120,   120,   120,   nil,   120,   120,   120,
   120,   120,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   120,   nil,   nil,   120,   nil,   nil,   120,   120,   nil,
   nil,   120,   nil,   nil,   nil,   nil,   nil,   120,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   120,   nil,   nil,   nil,
   nil,   120,   120,   120,   120,   nil,   120,   120,   120,   120,
   nil,   nil,   nil,   nil,   120,   120,   nil,   nil,   nil,   121,
   121,   121,   120,   121,   120,   120,   120,   121,   121,   nil,
   nil,   nil,   121,   nil,   121,   121,   121,   121,   121,   121,
   121,   nil,   nil,   nil,   nil,   nil,   121,   121,   121,   121,
   121,   121,   121,   nil,   nil,   121,   nil,   nil,   nil,   nil,
   nil,   nil,   121,   nil,   nil,   121,   121,   121,   121,   121,
   121,   121,   121,   nil,   121,   121,   121,   nil,   121,   121,
   121,   121,   121,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   121,   nil,   nil,   121,   nil,   nil,   121,   121,
   nil,   nil,   121,   nil,   nil,   nil,   nil,   nil,   121,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   121,   nil,   nil,
   nil,   nil,   121,   121,   121,   121,   nil,   121,   121,   121,
   121,   nil,   nil,   nil,   nil,   121,   121,   nil,   nil,   nil,
   nil,   nil,   nil,   121,   nil,   121,   121,   121,   122,   122,
   122,   122,   122,   nil,   nil,   nil,   122,   122,   nil,   nil,
   nil,   122,   nil,   122,   122,   122,   122,   122,   122,   122,
   nil,   nil,   nil,   nil,   nil,   122,   122,   122,   122,   122,
   122,   122,   nil,   nil,   122,   nil,   nil,   nil,   nil,   nil,
   122,   122,   nil,   122,   122,   122,   122,   122,   122,   122,
   122,   122,   nil,   122,   122,   122,   nil,   122,   122,   122,
   122,   122,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   122,   nil,   nil,   122,   nil,   nil,   122,   122,   nil,
   nil,   122,   nil,   122,   nil,   nil,   nil,   122,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   122,   nil,   nil,   nil,
   nil,   122,   122,   122,   122,   nil,   122,   122,   122,   122,
   nil,   nil,   nil,   nil,   122,   122,   nil,   nil,   nil,   209,
   209,   209,   122,   209,   122,   122,   122,   209,   209,   nil,
   nil,   nil,   209,   nil,   209,   209,   209,   209,   209,   209,
   209,   nil,   nil,   nil,   nil,   nil,   209,   209,   209,   209,
   209,   209,   209,   nil,   nil,   209,   nil,   nil,   nil,   nil,
   nil,   nil,   209,   nil,   nil,   209,   209,   209,   209,   209,
   209,   209,   209,   nil,   209,   209,   209,   nil,   209,   209,
   209,   209,   209,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   209,   nil,   nil,   209,   nil,   nil,   209,   209,
   nil,   nil,   209,   nil,   209,   nil,   nil,   nil,   209,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,
   nil,   nil,   209,   209,   209,   209,   nil,   209,   209,   209,
   209,   nil,   nil,   nil,   nil,   209,   209,   nil,   nil,   nil,
   210,   210,   210,   209,   210,   209,   209,   209,   210,   210,
   nil,   nil,   nil,   210,   nil,   210,   210,   210,   210,   210,
   210,   210,   nil,   nil,   nil,   nil,   nil,   210,   210,   210,
   210,   210,   210,   210,   nil,   nil,   210,   nil,   nil,   nil,
   nil,   nil,   nil,   210,   nil,   nil,   210,   210,   210,   210,
   210,   210,   210,   210,   nil,   210,   210,   210,   nil,   210,
   210,   210,   210,   210,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   210,   nil,   nil,   210,   nil,   nil,   210,
   210,   nil,   nil,   210,   nil,   nil,   nil,   nil,   nil,   210,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   210,   nil,
   nil,   nil,   nil,   210,   210,   210,   210,   nil,   210,   210,
   210,   210,   nil,   nil,   nil,   nil,   210,   210,   nil,   nil,
   nil,   211,   211,   211,   210,   211,   210,   210,   210,   211,
   211,   nil,   nil,   nil,   211,   nil,   211,   211,   211,   211,
   211,   211,   211,   nil,   nil,   nil,   nil,   nil,   211,   211,
   211,   211,   211,   211,   211,   nil,   nil,   211,   nil,   nil,
   nil,   nil,   nil,   nil,   211,   nil,   nil,   211,   211,   211,
   211,   211,   211,   211,   211,   211,   211,   211,   211,   nil,
   211,   211,   211,   211,   211,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   211,   nil,   nil,   211,   nil,   nil,
   211,   211,   nil,   nil,   211,   nil,   211,   nil,   211,   nil,
   211,   nil,   nil,   211,   nil,   nil,   nil,   nil,   nil,   211,
   nil,   nil,   nil,   nil,   211,   211,   211,   211,   nil,   211,
   211,   211,   211,   nil,   nil,   nil,   nil,   211,   211,   nil,
   nil,   nil,   214,   214,   214,   211,   214,   211,   211,   211,
   214,   214,   nil,   nil,   nil,   214,   nil,   214,   214,   214,
   214,   214,   214,   214,   nil,   nil,   nil,   nil,   nil,   214,
   214,   214,   214,   214,   214,   214,   nil,   nil,   214,   nil,
   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   214,   214,
   214,   214,   214,   214,   214,   214,   nil,   214,   214,   214,
   nil,   214,   214,   214,   214,   214,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   214,   nil,
   nil,   214,   214,   nil,   nil,   214,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   214,   nil,   nil,   nil,   nil,   214,   214,   214,   214,   nil,
   214,   214,   214,   214,   nil,   nil,   nil,   nil,   214,   214,
   nil,   nil,   nil,   215,   215,   215,   214,   215,   214,   214,
   214,   215,   215,   nil,   nil,   nil,   215,   nil,   215,   215,
   215,   215,   215,   215,   215,   nil,   nil,   nil,   nil,   nil,
   215,   215,   215,   215,   215,   215,   215,   nil,   nil,   215,
   nil,   nil,   nil,   nil,   nil,   nil,   215,   nil,   nil,   215,
   215,   215,   215,   215,   215,   215,   215,   nil,   215,   215,
   215,   nil,   215,   215,   215,   215,   215,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   215,   nil,   nil,   215,
   nil,   nil,   215,   215,   nil,   nil,   215,   nil,   215,   nil,
   nil,   nil,   215,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   215,   nil,   nil,   nil,   nil,   215,   215,   215,   215,
   nil,   215,   215,   215,   215,   nil,   nil,   nil,   nil,   215,
   215,   nil,   nil,   nil,   216,   216,   216,   215,   216,   215,
   215,   215,   216,   216,   nil,   nil,   nil,   216,   nil,   216,
   216,   216,   216,   216,   216,   216,   nil,   nil,   nil,   nil,
   nil,   216,   216,   216,   216,   216,   216,   216,   nil,   nil,
   216,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,   nil,
   216,   216,   216,   216,   216,   216,   216,   216,   nil,   216,
   216,   216,   nil,   216,   216,   216,   216,   216,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,   nil,
   216,   nil,   nil,   216,   216,   nil,   nil,   216,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   216,   nil,   nil,   nil,   nil,   216,   216,   216,
   216,   nil,   216,   216,   216,   216,   nil,   nil,   nil,   nil,
   216,   216,   nil,   nil,   nil,   217,   217,   217,   216,   217,
   216,   216,   216,   217,   217,   nil,   nil,   nil,   217,   nil,
   217,   217,   217,   217,   217,   217,   217,   nil,   nil,   nil,
   nil,   nil,   217,   217,   217,   217,   217,   217,   217,   nil,
   nil,   217,   nil,   nil,   nil,   nil,   nil,   nil,   217,   nil,
   nil,   217,   217,   217,   217,   217,   217,   217,   217,   nil,
   217,   217,   217,   nil,   217,   217,   217,   217,   217,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   217,   nil,
   nil,   217,   nil,   nil,   217,   217,   nil,   nil,   217,   nil,
   nil,   nil,   nil,   nil,   217,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   217,   nil,   nil,   nil,   nil,   217,   217,
   217,   217,   nil,   217,   217,   217,   217,   nil,   nil,   nil,
   nil,   217,   217,   nil,   nil,   nil,   218,   218,   218,   217,
   218,   217,   217,   217,   218,   218,   nil,   nil,   nil,   218,
   nil,   218,   218,   218,   218,   218,   218,   218,   nil,   nil,
   nil,   nil,   nil,   218,   218,   218,   218,   218,   218,   218,
   nil,   nil,   218,   nil,   nil,   nil,   nil,   nil,   nil,   218,
   nil,   nil,   218,   218,   218,   218,   218,   218,   218,   218,
   nil,   218,   218,   218,   nil,   218,   218,   218,   218,   218,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   218,
   nil,   nil,   218,   nil,   nil,   218,   218,   nil,   nil,   218,
   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,   218,
   218,   218,   218,   nil,   218,   218,   218,   218,   nil,   nil,
   nil,   nil,   218,   218,   nil,   nil,   nil,   219,   219,   219,
   218,   219,   218,   218,   218,   219,   219,   nil,   nil,   nil,
   219,   nil,   219,   219,   219,   219,   219,   219,   219,   nil,
   nil,   nil,   nil,   nil,   219,   219,   219,   219,   219,   219,
   219,   nil,   nil,   219,   nil,   nil,   nil,   nil,   nil,   nil,
   219,   nil,   nil,   219,   219,   219,   219,   219,   219,   219,
   219,   nil,   219,   219,   219,   nil,   219,   219,   219,   219,
   219,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   219,   nil,   nil,   219,   nil,   nil,   219,   219,   nil,   nil,
   219,   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,   nil,
   219,   219,   219,   219,   nil,   219,   219,   219,   219,   nil,
   nil,   nil,   nil,   219,   219,   219,   nil,   nil,   230,   230,
   230,   219,   230,   219,   219,   219,   230,   230,   nil,   nil,
   nil,   230,   nil,   230,   230,   230,   230,   230,   230,   230,
   nil,   nil,   nil,   nil,   nil,   230,   230,   230,   230,   230,
   230,   230,   nil,   nil,   230,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   230,   230,   230,   230,   230,   230,
   230,   230,   nil,   230,   230,   230,   nil,   230,   230,   230,
   230,   230,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   230,   nil,   nil,   230,   nil,   nil,   230,   230,   nil,
   nil,   230,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,   nil,
   nil,   230,   230,   230,   230,   nil,   230,   230,   230,   230,
   nil,   nil,   nil,   nil,   230,   230,   nil,   nil,   nil,   233,
   233,   233,   230,   233,   230,   230,   230,   233,   233,   nil,
   nil,   nil,   233,   nil,   233,   233,   233,   233,   233,   233,
   233,   nil,   nil,   nil,   nil,   nil,   233,   233,   233,   233,
   233,   233,   233,   nil,   nil,   233,   nil,   nil,   nil,   nil,
   nil,   nil,   233,   nil,   nil,   233,   233,   233,   233,   233,
   233,   233,   233,   nil,   233,   233,   233,   nil,   233,   233,
   233,   233,   233,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   233,   nil,   nil,   233,   nil,   nil,   233,   233,
   nil,   nil,   233,   nil,   nil,   nil,   nil,   nil,   233,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   233,   nil,   nil,
   nil,   nil,   233,   233,   233,   233,   nil,   233,   233,   233,
   233,   nil,   nil,   nil,   nil,   233,   233,   nil,   nil,   nil,
   234,   234,   234,   233,   234,   233,   233,   233,   234,   234,
   nil,   nil,   nil,   234,   nil,   234,   234,   234,   234,   234,
   234,   234,   nil,   nil,   nil,   nil,   nil,   234,   234,   234,
   234,   234,   234,   234,   nil,   nil,   234,   nil,   nil,   nil,
   nil,   nil,   nil,   234,   nil,   nil,   234,   234,   234,   234,
   234,   234,   234,   234,   nil,   234,   234,   234,   nil,   234,
   234,   234,   234,   234,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   234,   nil,   nil,   234,   nil,   nil,   234,
   234,   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   234,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   234,   nil,
   nil,   nil,   nil,   234,   234,   234,   234,   nil,   234,   234,
   234,   234,   nil,   nil,   nil,   nil,   234,   234,   nil,   nil,
   nil,   235,   235,   235,   234,   235,   234,   234,   234,   235,
   235,   nil,   nil,   nil,   235,   nil,   235,   235,   235,   235,
   235,   235,   235,   nil,   nil,   nil,   nil,   nil,   235,   235,
   235,   235,   235,   235,   235,   nil,   nil,   235,   nil,   nil,
   nil,   nil,   nil,   nil,   235,   nil,   nil,   235,   235,   235,
   235,   235,   235,   235,   235,   nil,   235,   235,   235,   nil,
   235,   235,   235,   235,   235,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   235,   nil,   nil,   235,   nil,   nil,
   235,   235,   nil,   nil,   235,   nil,   nil,   nil,   nil,   nil,
   235,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,
   nil,   nil,   nil,   nil,   235,   235,   235,   235,   nil,   235,
   235,   235,   235,   nil,   nil,   nil,   nil,   235,   235,   nil,
   nil,   nil,   236,   236,   236,   235,   236,   235,   235,   235,
   236,   236,   nil,   nil,   nil,   236,   nil,   236,   236,   236,
   236,   236,   236,   236,   nil,   nil,   nil,   nil,   nil,   236,
   236,   236,   236,   236,   236,   236,   nil,   nil,   236,   nil,
   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   236,   236,
   236,   236,   236,   236,   236,   236,   nil,   236,   236,   236,
   nil,   236,   236,   236,   236,   236,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   236,   nil,
   nil,   236,   236,   nil,   nil,   236,   nil,   nil,   nil,   nil,
   nil,   236,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   236,   nil,   nil,   nil,   nil,   236,   236,   236,   236,   nil,
   236,   236,   236,   236,   nil,   nil,   nil,   nil,   236,   236,
   nil,   nil,   nil,   237,   237,   237,   236,   237,   236,   236,
   236,   237,   237,   nil,   nil,   nil,   237,   nil,   237,   237,
   237,   237,   237,   237,   237,   nil,   nil,   nil,   nil,   nil,
   237,   237,   237,   237,   237,   237,   237,   nil,   nil,   237,
   nil,   nil,   nil,   nil,   nil,   nil,   237,   nil,   nil,   237,
   237,   237,   237,   237,   237,   237,   237,   nil,   237,   237,
   237,   nil,   237,   237,   237,   237,   237,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   237,   nil,   nil,   237,
   nil,   nil,   237,   237,   nil,   nil,   237,   nil,   nil,   nil,
   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   237,   nil,   nil,   nil,   nil,   237,   237,   237,   237,
   nil,   237,   237,   237,   237,   nil,   nil,   nil,   nil,   237,
   237,   nil,   nil,   nil,   238,   238,   238,   237,   238,   237,
   237,   237,   238,   238,   nil,   nil,   nil,   238,   nil,   238,
   238,   238,   238,   238,   238,   238,   nil,   nil,   nil,   nil,
   nil,   238,   238,   238,   238,   238,   238,   238,   nil,   nil,
   238,   nil,   nil,   nil,   nil,   nil,   nil,   238,   nil,   nil,
   238,   238,   238,   238,   238,   238,   238,   238,   nil,   238,
   238,   238,   nil,   238,   238,   238,   238,   238,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   238,   nil,   nil,
   238,   nil,   nil,   238,   238,   nil,   nil,   238,   nil,   nil,
   nil,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   238,   nil,   nil,   nil,   nil,   238,   238,   238,
   238,   nil,   238,   238,   238,   238,   nil,   nil,   nil,   nil,
   238,   238,   nil,   nil,   nil,   239,   239,   239,   238,   239,
   238,   238,   238,   239,   239,   nil,   nil,   nil,   239,   nil,
   239,   239,   239,   239,   239,   239,   239,   nil,   nil,   nil,
   nil,   nil,   239,   239,   239,   239,   239,   239,   239,   nil,
   nil,   239,   nil,   nil,   nil,   nil,   nil,   nil,   239,   nil,
   nil,   239,   239,   239,   239,   239,   239,   239,   239,   nil,
   239,   239,   239,   nil,   239,   239,   239,   239,   239,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   239,   nil,
   nil,   239,   nil,   nil,   239,   239,   nil,   nil,   239,   nil,
   nil,   nil,   nil,   nil,   239,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   239,   nil,   nil,   nil,   nil,   239,   239,
   239,   239,   nil,   239,   239,   239,   239,   nil,   nil,   nil,
   nil,   239,   239,   nil,   nil,   nil,   240,   240,   240,   239,
   240,   239,   239,   239,   240,   240,   nil,   nil,   nil,   240,
   nil,   240,   240,   240,   240,   240,   240,   240,   nil,   nil,
   nil,   nil,   nil,   240,   240,   240,   240,   240,   240,   240,
   nil,   nil,   240,   nil,   nil,   nil,   nil,   nil,   nil,   240,
   nil,   nil,   240,   240,   240,   240,   240,   240,   240,   240,
   nil,   240,   240,   240,   nil,   240,   240,   240,   240,   240,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   240,
   nil,   nil,   240,   nil,   nil,   240,   240,   nil,   nil,   240,
   nil,   nil,   nil,   nil,   nil,   240,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   240,   nil,   nil,   nil,   nil,   240,
   240,   240,   240,   nil,   240,   240,   240,   240,   nil,   nil,
   nil,   nil,   240,   240,   nil,   nil,   nil,   241,   241,   241,
   240,   241,   240,   240,   240,   241,   241,   nil,   nil,   nil,
   241,   nil,   241,   241,   241,   241,   241,   241,   241,   nil,
   nil,   nil,   nil,   nil,   241,   241,   241,   241,   241,   241,
   241,   nil,   nil,   241,   nil,   nil,   nil,   nil,   nil,   nil,
   241,   nil,   nil,   241,   241,   241,   241,   241,   241,   241,
   241,   nil,   241,   241,   241,   nil,   241,   241,   241,   241,
   241,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   241,   nil,   nil,   241,   nil,   nil,   241,   241,   nil,   nil,
   241,   nil,   nil,   nil,   nil,   nil,   241,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   241,   nil,   nil,   nil,   nil,
   241,   241,   241,   241,   nil,   241,   241,   241,   241,   nil,
   nil,   nil,   nil,   241,   241,   nil,   nil,   nil,   242,   242,
   242,   241,   242,   241,   241,   241,   242,   242,   nil,   nil,
   nil,   242,   nil,   242,   242,   242,   242,   242,   242,   242,
   nil,   nil,   nil,   nil,   nil,   242,   242,   242,   242,   242,
   242,   242,   nil,   nil,   242,   nil,   nil,   nil,   nil,   nil,
   nil,   242,   nil,   nil,   242,   242,   242,   242,   242,   242,
   242,   242,   nil,   242,   242,   242,   nil,   242,   242,   242,
   242,   242,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   242,   nil,   nil,   242,   nil,   nil,   242,   242,   nil,
   nil,   242,   nil,   nil,   nil,   nil,   nil,   242,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   242,   nil,   nil,   nil,
   nil,   242,   242,   242,   242,   nil,   242,   242,   242,   242,
   nil,   nil,   nil,   nil,   242,   242,   nil,   nil,   nil,   243,
   243,   243,   242,   243,   242,   242,   242,   243,   243,   nil,
   nil,   nil,   243,   nil,   243,   243,   243,   243,   243,   243,
   243,   nil,   nil,   nil,   nil,   nil,   243,   243,   243,   243,
   243,   243,   243,   nil,   nil,   243,   nil,   nil,   nil,   nil,
   nil,   nil,   243,   nil,   nil,   243,   243,   243,   243,   243,
   243,   243,   243,   nil,   243,   243,   243,   nil,   243,   243,
   243,   243,   243,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   243,   nil,   nil,   243,   nil,   nil,   243,   243,
   nil,   nil,   243,   nil,   nil,   nil,   nil,   nil,   243,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   243,   nil,   nil,
   nil,   nil,   243,   243,   243,   243,   nil,   243,   243,   243,
   243,   nil,   nil,   nil,   nil,   243,   243,   nil,   nil,   nil,
   244,   244,   244,   243,   244,   243,   243,   243,   244,   244,
   nil,   nil,   nil,   244,   nil,   244,   244,   244,   244,   244,
   244,   244,   nil,   nil,   nil,   nil,   nil,   244,   244,   244,
   244,   244,   244,   244,   nil,   nil,   244,   nil,   nil,   nil,
   nil,   nil,   nil,   244,   nil,   nil,   244,   244,   244,   244,
   244,   244,   244,   244,   nil,   244,   244,   244,   nil,   244,
   244,   244,   244,   244,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   244,   nil,   nil,   244,   nil,   nil,   244,
   244,   nil,   nil,   244,   nil,   nil,   nil,   nil,   nil,   244,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   244,   nil,
   nil,   nil,   nil,   244,   244,   244,   244,   nil,   244,   244,
   244,   244,   nil,   nil,   nil,   nil,   244,   244,   nil,   nil,
   nil,   245,   245,   245,   244,   245,   244,   244,   244,   245,
   245,   nil,   nil,   nil,   245,   nil,   245,   245,   245,   245,
   245,   245,   245,   nil,   nil,   nil,   nil,   nil,   245,   245,
   245,   245,   245,   245,   245,   nil,   nil,   245,   nil,   nil,
   nil,   nil,   nil,   nil,   245,   nil,   nil,   245,   245,   245,
   245,   245,   245,   245,   245,   nil,   245,   245,   245,   nil,
   245,   245,   245,   245,   245,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   245,   nil,   nil,   245,   nil,   nil,
   245,   245,   nil,   nil,   245,   nil,   nil,   nil,   nil,   nil,
   245,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   245,
   nil,   nil,   nil,   nil,   245,   245,   245,   245,   nil,   245,
   245,   245,   245,   nil,   nil,   nil,   nil,   245,   245,   nil,
   nil,   nil,   246,   246,   246,   245,   246,   245,   245,   245,
   246,   246,   nil,   nil,   nil,   246,   nil,   246,   246,   246,
   246,   246,   246,   246,   nil,   nil,   nil,   nil,   nil,   246,
   246,   246,   246,   246,   246,   246,   nil,   nil,   246,   nil,
   nil,   nil,   nil,   nil,   nil,   246,   nil,   nil,   246,   246,
   246,   246,   246,   246,   246,   246,   nil,   246,   246,   246,
   nil,   246,   246,   246,   246,   246,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   246,   nil,   nil,   246,   nil,
   nil,   246,   246,   nil,   nil,   246,   nil,   nil,   nil,   nil,
   nil,   246,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   246,   nil,   nil,   nil,   nil,   246,   246,   246,   246,   nil,
   246,   246,   246,   246,   nil,   nil,   nil,   nil,   246,   246,
   nil,   nil,   nil,   247,   247,   247,   246,   247,   246,   246,
   246,   247,   247,   nil,   nil,   nil,   247,   nil,   247,   247,
   247,   247,   247,   247,   247,   nil,   nil,   nil,   nil,   nil,
   247,   247,   247,   247,   247,   247,   247,   nil,   nil,   247,
   nil,   nil,   nil,   nil,   nil,   nil,   247,   nil,   nil,   247,
   247,   247,   247,   247,   247,   247,   247,   nil,   247,   247,
   247,   nil,   247,   247,   247,   247,   247,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   247,   nil,   nil,   247,
   nil,   nil,   247,   247,   nil,   nil,   247,   nil,   nil,   nil,
   nil,   nil,   247,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   247,   nil,   nil,   nil,   nil,   247,   247,   247,   247,
   nil,   247,   247,   247,   247,   nil,   nil,   nil,   nil,   247,
   247,   nil,   nil,   nil,   248,   248,   248,   247,   248,   247,
   247,   247,   248,   248,   nil,   nil,   nil,   248,   nil,   248,
   248,   248,   248,   248,   248,   248,   nil,   nil,   nil,   nil,
   nil,   248,   248,   248,   248,   248,   248,   248,   nil,   nil,
   248,   nil,   nil,   nil,   nil,   nil,   nil,   248,   nil,   nil,
   248,   248,   248,   248,   248,   248,   248,   248,   nil,   248,
   248,   248,   nil,   248,   248,   248,   248,   248,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   248,   nil,   nil,
   248,   nil,   nil,   248,   248,   nil,   nil,   248,   nil,   nil,
   nil,   nil,   nil,   248,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   248,   nil,   nil,   nil,   nil,   248,   248,   248,
   248,   nil,   248,   248,   248,   248,   nil,   nil,   nil,   nil,
   248,   248,   nil,   nil,   nil,   249,   249,   249,   248,   249,
   248,   248,   248,   249,   249,   nil,   nil,   nil,   249,   nil,
   249,   249,   249,   249,   249,   249,   249,   nil,   nil,   nil,
   nil,   nil,   249,   249,   249,   249,   249,   249,   249,   nil,
   nil,   249,   nil,   nil,   nil,   nil,   nil,   nil,   249,   nil,
   nil,   249,   249,   249,   249,   249,   249,   249,   249,   nil,
   249,   249,   249,   nil,   249,   249,   249,   249,   249,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   249,   nil,
   nil,   249,   nil,   nil,   249,   249,   nil,   nil,   249,   nil,
   nil,   nil,   nil,   nil,   249,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   249,   nil,   nil,   nil,   nil,   249,   249,
   249,   249,   nil,   249,   249,   249,   249,   nil,   nil,   nil,
   nil,   249,   249,   nil,   nil,   nil,   250,   250,   250,   249,
   250,   249,   249,   249,   250,   250,   nil,   nil,   nil,   250,
   nil,   250,   250,   250,   250,   250,   250,   250,   nil,   nil,
   nil,   nil,   nil,   250,   250,   250,   250,   250,   250,   250,
   nil,   nil,   250,   nil,   nil,   nil,   nil,   nil,   nil,   250,
   nil,   nil,   250,   250,   250,   250,   250,   250,   250,   250,
   nil,   250,   250,   250,   nil,   250,   250,   250,   250,   250,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   250,
   nil,   nil,   250,   nil,   nil,   250,   250,   nil,   nil,   250,
   nil,   nil,   nil,   nil,   nil,   250,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   250,   nil,   nil,   nil,   nil,   250,
   250,   250,   250,   nil,   250,   250,   250,   250,   nil,   nil,
   nil,   nil,   250,   250,   nil,   nil,   nil,   251,   251,   251,
   250,   251,   250,   250,   250,   251,   251,   nil,   nil,   nil,
   251,   nil,   251,   251,   251,   251,   251,   251,   251,   nil,
   nil,   nil,   nil,   nil,   251,   251,   251,   251,   251,   251,
   251,   nil,   nil,   251,   nil,   nil,   nil,   nil,   nil,   nil,
   251,   nil,   nil,   251,   251,   251,   251,   251,   251,   251,
   251,   nil,   251,   251,   251,   nil,   251,   251,   251,   251,
   251,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   251,   nil,   nil,   251,   nil,   nil,   251,   251,   nil,   nil,
   251,   nil,   nil,   nil,   nil,   nil,   251,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   251,   nil,   nil,   nil,   nil,
   251,   251,   251,   251,   nil,   251,   251,   251,   251,   nil,
   nil,   nil,   nil,   251,   251,   nil,   nil,   nil,   252,   252,
   252,   251,   252,   251,   251,   251,   252,   252,   nil,   nil,
   nil,   252,   nil,   252,   252,   252,   252,   252,   252,   252,
   nil,   nil,   nil,   nil,   nil,   252,   252,   252,   252,   252,
   252,   252,   nil,   nil,   252,   nil,   nil,   nil,   nil,   nil,
   nil,   252,   nil,   nil,   252,   252,   252,   252,   252,   252,
   252,   252,   nil,   252,   252,   252,   nil,   252,   252,   252,
   252,   252,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   252,   nil,   nil,   252,   nil,   nil,   252,   252,   nil,
   nil,   252,   nil,   nil,   nil,   nil,   nil,   252,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   252,   nil,   nil,   nil,
   nil,   252,   252,   252,   252,   nil,   252,   252,   252,   252,
   nil,   nil,   nil,   nil,   252,   252,   nil,   nil,   nil,   253,
   253,   253,   252,   253,   252,   252,   252,   253,   253,   nil,
   nil,   nil,   253,   nil,   253,   253,   253,   253,   253,   253,
   253,   nil,   nil,   nil,   nil,   nil,   253,   253,   253,   253,
   253,   253,   253,   nil,   nil,   253,   nil,   nil,   nil,   nil,
   nil,   nil,   253,   nil,   nil,   253,   253,   253,   253,   253,
   253,   253,   253,   nil,   253,   253,   253,   nil,   253,   253,
   253,   253,   253,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   253,   nil,   nil,   253,   nil,   nil,   253,   253,
   nil,   nil,   253,   nil,   nil,   nil,   nil,   nil,   253,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   253,   nil,   nil,
   nil,   nil,   253,   253,   253,   253,   nil,   253,   253,   253,
   253,   nil,   nil,   nil,   nil,   253,   253,   nil,   nil,   nil,
   254,   254,   254,   253,   254,   253,   253,   253,   254,   254,
   nil,   nil,   nil,   254,   nil,   254,   254,   254,   254,   254,
   254,   254,   nil,   nil,   nil,   nil,   nil,   254,   254,   254,
   254,   254,   254,   254,   nil,   nil,   254,   nil,   nil,   nil,
   nil,   nil,   nil,   254,   nil,   nil,   254,   254,   254,   254,
   254,   254,   254,   254,   nil,   254,   254,   254,   nil,   254,
   254,   254,   254,   254,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   254,   nil,   nil,   254,   nil,   nil,   254,
   254,   nil,   nil,   254,   nil,   nil,   nil,   nil,   nil,   254,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   254,   nil,
   nil,   nil,   nil,   254,   254,   254,   254,   nil,   254,   254,
   254,   254,   nil,   nil,   nil,   nil,   254,   254,   nil,   nil,
   nil,   255,   255,   255,   254,   255,   254,   254,   254,   255,
   255,   nil,   nil,   nil,   255,   nil,   255,   255,   255,   255,
   255,   255,   255,   nil,   nil,   nil,   nil,   nil,   255,   255,
   255,   255,   255,   255,   255,   nil,   nil,   255,   nil,   nil,
   nil,   nil,   nil,   nil,   255,   nil,   nil,   255,   255,   255,
   255,   255,   255,   255,   255,   nil,   255,   255,   255,   nil,
   255,   255,   255,   255,   255,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   255,   nil,   nil,   255,   nil,   nil,
   255,   255,   nil,   nil,   255,   nil,   nil,   nil,   nil,   nil,
   255,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   255,
   nil,   nil,   nil,   nil,   255,   255,   255,   255,   nil,   255,
   255,   255,   255,   nil,   nil,   nil,   nil,   255,   255,   nil,
   nil,   nil,   256,   256,   256,   255,   256,   255,   255,   255,
   256,   256,   nil,   nil,   nil,   256,   nil,   256,   256,   256,
   256,   256,   256,   256,   nil,   nil,   nil,   nil,   nil,   256,
   256,   256,   256,   256,   256,   256,   nil,   nil,   256,   nil,
   nil,   nil,   nil,   nil,   nil,   256,   nil,   nil,   256,   256,
   256,   256,   256,   256,   256,   256,   nil,   256,   256,   256,
   nil,   256,   256,   256,   256,   256,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   256,   nil,   nil,   256,   nil,
   nil,   256,   256,   nil,   nil,   256,   nil,   nil,   nil,   nil,
   nil,   256,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   256,   nil,   nil,   nil,   nil,   256,   256,   256,   256,   nil,
   256,   256,   256,   256,   nil,   nil,   nil,   nil,   256,   256,
   nil,   nil,   nil,   257,   257,   257,   256,   257,   256,   256,
   256,   257,   257,   nil,   nil,   nil,   257,   nil,   257,   257,
   257,   257,   257,   257,   257,   nil,   nil,   nil,   nil,   nil,
   257,   257,   257,   257,   257,   257,   257,   nil,   nil,   257,
   nil,   nil,   nil,   nil,   nil,   nil,   257,   nil,   nil,   257,
   257,   257,   257,   257,   257,   257,   257,   nil,   257,   257,
   257,   nil,   257,   257,   257,   257,   257,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   257,   nil,   nil,   257,
   nil,   nil,   257,   257,   nil,   nil,   257,   nil,   nil,   nil,
   nil,   nil,   257,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   257,   nil,   nil,   nil,   nil,   257,   257,   257,   257,
   nil,   257,   257,   257,   257,   nil,   nil,   nil,   nil,   257,
   257,   nil,   nil,   nil,   258,   258,   258,   257,   258,   257,
   257,   257,   258,   258,   nil,   nil,   nil,   258,   nil,   258,
   258,   258,   258,   258,   258,   258,   nil,   nil,   nil,   nil,
   nil,   258,   258,   258,   258,   258,   258,   258,   nil,   nil,
   258,   nil,   nil,   nil,   nil,   nil,   nil,   258,   nil,   nil,
   258,   258,   258,   258,   258,   258,   258,   258,   nil,   258,
   258,   258,   nil,   258,   258,   258,   258,   258,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   258,   nil,   nil,
   258,   nil,   nil,   258,   258,   nil,   nil,   258,   nil,   nil,
   nil,   nil,   nil,   258,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   258,   nil,   nil,   nil,   nil,   258,   258,   258,
   258,   nil,   258,   258,   258,   258,   nil,   nil,   nil,   nil,
   258,   258,   nil,   nil,   nil,   265,   265,   265,   258,   265,
   258,   258,   258,   265,   265,   nil,   nil,   nil,   265,   nil,
   265,   265,   265,   265,   265,   265,   265,   nil,   nil,   nil,
   nil,   nil,   265,   265,   265,   265,   265,   265,   265,   nil,
   nil,   265,   nil,   nil,   nil,   nil,   nil,   nil,   265,   nil,
   nil,   265,   265,   265,   265,   265,   265,   265,   265,   265,
   265,   265,   265,   nil,   265,   265,   265,   265,   265,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   265,   nil,
   nil,   265,   nil,   nil,   265,   265,   nil,   nil,   265,   nil,
   265,   nil,   265,   nil,   265,   nil,   nil,   265,   nil,   nil,
   nil,   nil,   nil,   265,   nil,   nil,   nil,   nil,   265,   265,
   265,   265,   nil,   265,   265,   265,   265,   nil,   nil,   nil,
   nil,   265,   265,   nil,   nil,   nil,   266,   266,   266,   265,
   266,   265,   265,   265,   266,   266,   nil,   nil,   nil,   266,
   nil,   266,   266,   266,   266,   266,   266,   266,   nil,   nil,
   nil,   nil,   nil,   266,   266,   266,   266,   266,   266,   266,
   nil,   nil,   266,   nil,   nil,   nil,   nil,   nil,   nil,   266,
   nil,   nil,   266,   266,   266,   266,   266,   266,   266,   266,
   266,   266,   266,   266,   nil,   266,   266,   266,   266,   266,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   266,
   nil,   nil,   266,   nil,   nil,   266,   266,   nil,   nil,   266,
   nil,   266,   nil,   266,   nil,   266,   nil,   nil,   266,   nil,
   nil,   nil,   nil,   nil,   266,   nil,   nil,   nil,   nil,   266,
   266,   266,   266,   nil,   266,   266,   266,   266,   nil,   nil,
   nil,   nil,   266,   266,   nil,   nil,   nil,   274,   274,   274,
   266,   274,   266,   266,   266,   274,   274,   nil,   nil,   nil,
   274,   nil,   274,   274,   274,   274,   274,   274,   274,   nil,
   nil,   nil,   nil,   nil,   274,   274,   274,   274,   274,   274,
   274,   nil,   nil,   274,   nil,   nil,   nil,   nil,   nil,   nil,
   274,   nil,   nil,   274,   274,   274,   274,   274,   274,   274,
   274,   274,   274,   274,   274,   nil,   274,   274,   274,   274,
   274,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   274,   nil,   nil,   274,   nil,   nil,   274,   274,   nil,   nil,
   274,   nil,   274,   nil,   274,   nil,   274,   nil,   nil,   274,
   nil,   nil,   nil,   nil,   nil,   274,   nil,   nil,   nil,   nil,
   274,   274,   274,   274,   nil,   274,   274,   274,   274,   nil,
   nil,   nil,   nil,   274,   274,   274,   nil,   nil,   281,   281,
   281,   274,   281,   274,   274,   274,   281,   281,   nil,   nil,
   nil,   281,   nil,   281,   281,   281,   281,   281,   281,   281,
   nil,   nil,   nil,   nil,   nil,   281,   281,   281,   281,   281,
   281,   281,   nil,   nil,   281,   nil,   nil,   nil,   nil,   nil,
   nil,   281,   nil,   nil,   281,   281,   281,   281,   281,   281,
   281,   281,   nil,   281,   281,   281,   nil,   281,   281,   281,
   281,   281,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   281,   nil,   nil,   281,   nil,   nil,   281,   281,   nil,
   nil,   281,   nil,   nil,   nil,   nil,   nil,   281,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   281,   nil,   nil,   nil,
   nil,   281,   281,   281,   281,   nil,   281,   281,   281,   281,
   nil,   nil,   nil,   nil,   281,   281,   nil,   nil,   nil,   283,
   283,   283,   281,   283,   281,   281,   281,   283,   283,   nil,
   nil,   nil,   283,   nil,   283,   283,   283,   283,   283,   283,
   283,   nil,   nil,   nil,   nil,   nil,   283,   283,   283,   283,
   283,   283,   283,   nil,   nil,   283,   nil,   nil,   nil,   nil,
   nil,   nil,   283,   nil,   nil,   283,   283,   283,   283,   283,
   283,   283,   283,   nil,   283,   283,   283,   nil,   283,   283,
   283,   283,   283,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   283,   nil,   nil,   283,   nil,   nil,   283,   283,
   nil,   nil,   283,   nil,   nil,   nil,   nil,   nil,   283,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   283,   nil,   nil,
   nil,   nil,   283,   283,   283,   283,   nil,   283,   283,   283,
   283,   nil,   nil,   nil,   nil,   283,   283,   nil,   nil,   nil,
   286,   286,   286,   283,   286,   283,   283,   283,   286,   286,
   nil,   nil,   nil,   286,   nil,   286,   286,   286,   286,   286,
   286,   286,   nil,   nil,   nil,   nil,   nil,   286,   286,   286,
   286,   286,   286,   286,   nil,   nil,   286,   nil,   nil,   nil,
   nil,   nil,   nil,   286,   nil,   nil,   286,   286,   286,   286,
   286,   286,   286,   286,   nil,   286,   286,   286,   nil,   286,
   286,   286,   286,   286,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   286,   nil,   nil,   286,   nil,   nil,   286,
   286,   nil,   nil,   286,   nil,   nil,   nil,   nil,   nil,   286,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   286,   nil,
   nil,   nil,   nil,   286,   286,   286,   286,   nil,   286,   286,
   286,   286,   nil,   nil,   nil,   nil,   286,   286,   nil,   nil,
   nil,   287,   287,   287,   286,   287,   286,   286,   286,   287,
   287,   nil,   nil,   nil,   287,   nil,   287,   287,   287,   287,
   287,   287,   287,   nil,   nil,   nil,   nil,   nil,   287,   287,
   287,   287,   287,   287,   287,   nil,   nil,   287,   nil,   nil,
   nil,   nil,   nil,   nil,   287,   nil,   nil,   287,   287,   287,
   287,   287,   287,   287,   287,   nil,   287,   287,   287,   nil,
   287,   287,   287,   287,   287,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   287,   nil,   nil,   287,   nil,   nil,
   287,   287,   nil,   nil,   287,   nil,   nil,   nil,   nil,   nil,
   287,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   287,
   nil,   nil,   nil,   nil,   287,   287,   287,   287,   nil,   287,
   287,   287,   287,   nil,   nil,   nil,   nil,   287,   287,   nil,
   nil,   nil,   nil,   nil,   nil,   287,   nil,   287,   287,   287,
   292,   292,   292,   292,   292,   nil,   nil,   nil,   292,   292,
   nil,   nil,   nil,   292,   nil,   292,   292,   292,   292,   292,
   292,   292,   nil,   nil,   nil,   nil,   nil,   292,   292,   292,
   292,   292,   292,   292,   nil,   nil,   292,   nil,   nil,   nil,
   nil,   nil,   292,   292,   nil,   292,   292,   292,   292,   292,
   292,   292,   292,   292,   nil,   292,   292,   292,   nil,   292,
   292,   292,   292,   292,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   292,   nil,   nil,   292,   nil,   nil,   292,
   292,   nil,   nil,   292,   nil,   292,   nil,   nil,   nil,   292,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   292,   nil,
   nil,   nil,   nil,   292,   292,   292,   292,   nil,   292,   292,
   292,   292,   nil,   nil,   nil,   nil,   292,   292,   nil,   nil,
   nil,   300,   300,   300,   292,   300,   292,   292,   292,   300,
   300,   nil,   nil,   nil,   300,   nil,   300,   300,   300,   300,
   300,   300,   300,   nil,   nil,   nil,   nil,   nil,   300,   300,
   300,   300,   300,   300,   300,   nil,   nil,   300,   nil,   nil,
   nil,   nil,   nil,   nil,   300,   nil,   nil,   300,   300,   300,
   300,   300,   300,   300,   300,   nil,   300,   300,   300,   nil,
   300,   300,   nil,   nil,   300,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   300,   nil,   nil,   300,   nil,   nil,
   300,   300,   nil,   nil,   300,   nil,   nil,   940,   nil,   940,
   940,   940,   940,   940,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   940,   nil,   300,   300,   300,   300,   nil,   300,
   300,   300,   300,   nil,   nil,   nil,   nil,   300,   300,   nil,
   nil,   nil,   300,   nil,   940,   300,   nil,   300,   300,   300,
   317,   317,   317,   nil,   317,   940,   940,   nil,   317,   317,
   940,   nil,   nil,   317,   nil,   317,   317,   317,   317,   317,
   317,   317,   nil,   nil,   nil,   nil,   nil,   317,   317,   317,
   317,   317,   317,   317,   nil,   nil,   317,   nil,   nil,   nil,
   nil,   nil,   nil,   317,   nil,   nil,   317,   317,   317,   317,
   317,   317,   317,   317,   nil,   317,   317,   317,   nil,   317,
   317,   nil,   nil,   317,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   317,   nil,   nil,   317,   nil,   nil,   317,
   317,   nil,   nil,   317,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   317,   317,   317,   317,   nil,   317,   317,
   317,   317,   nil,   nil,   nil,   nil,   317,   317,   nil,   nil,
   nil,   325,   325,   325,   317,   325,   317,   317,   317,   325,
   325,   nil,   nil,   nil,   325,   nil,   325,   325,   325,   325,
   325,   325,   325,   nil,   nil,   nil,   nil,   nil,   325,   325,
   325,   325,   325,   325,   325,   nil,   nil,   325,   nil,   nil,
   nil,   nil,   nil,   nil,   325,   nil,   nil,   325,   325,   325,
   325,   325,   325,   325,   325,   nil,   325,   325,   325,   nil,
   325,   325,   325,   325,   325,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   325,   nil,   nil,   325,   325,   nil,
   325,   325,   nil,   nil,   325,   nil,   nil,   nil,   nil,   nil,
   325,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   325,
   nil,   nil,   nil,   nil,   325,   325,   325,   325,   nil,   325,
   325,   325,   325,   nil,   nil,   nil,   nil,   325,   325,   nil,
   nil,   nil,   327,   327,   327,   325,   327,   325,   325,   325,
   327,   327,   nil,   nil,   nil,   327,   nil,   327,   327,   327,
   327,   327,   327,   327,   nil,   nil,   nil,   nil,   nil,   327,
   327,   327,   327,   327,   327,   327,   nil,   nil,   327,   nil,
   nil,   nil,   nil,   nil,   nil,   327,   nil,   nil,   327,   327,
   327,   327,   327,   327,   327,   327,   nil,   327,   327,   327,
   nil,   327,   327,   327,   327,   327,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   327,   nil,   nil,   327,   nil,
   nil,   327,   327,   nil,   nil,   327,   nil,   nil,   nil,   nil,
   nil,   327,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   327,   nil,   nil,   nil,   nil,   327,   327,   327,   327,   nil,
   327,   327,   327,   327,   nil,   nil,   nil,   nil,   327,   327,
   nil,   nil,   nil,   341,   341,   341,   327,   341,   327,   327,
   327,   341,   341,   nil,   nil,   nil,   341,   nil,   341,   341,
   341,   341,   341,   341,   341,   nil,   nil,   nil,   nil,   nil,
   341,   341,   341,   341,   341,   341,   341,   nil,   nil,   341,
   nil,   nil,   nil,   nil,   nil,   nil,   341,   nil,   nil,   341,
   341,   341,   341,   341,   341,   341,   341,   nil,   341,   341,
   341,   nil,   341,   341,   341,   341,   341,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   341,   nil,   nil,   341,
   nil,   nil,   341,   341,   nil,   nil,   341,   nil,   nil,   nil,
   nil,   nil,   341,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   341,   nil,   nil,   nil,   nil,   341,   341,   341,   341,
   nil,   341,   341,   341,   341,   nil,   nil,   nil,   nil,   341,
   341,   nil,   nil,   nil,   342,   342,   342,   341,   342,   341,
   341,   341,   342,   342,   nil,   nil,   nil,   342,   nil,   342,
   342,   342,   342,   342,   342,   342,   nil,   nil,   nil,   nil,
   nil,   342,   342,   342,   342,   342,   342,   342,   nil,   nil,
   342,   nil,   nil,   nil,   nil,   nil,   nil,   342,   nil,   nil,
   342,   342,   342,   342,   342,   342,   342,   342,   nil,   342,
   342,   342,   nil,   342,   342,   342,   342,   342,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   342,   nil,   nil,
   342,   nil,   nil,   342,   342,   nil,   nil,   342,   nil,   nil,
   nil,   nil,   nil,   342,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   342,   nil,   nil,   nil,   nil,   342,   342,   342,
   342,   nil,   342,   342,   342,   342,   nil,   nil,   nil,   nil,
   342,   342,   nil,   nil,   nil,   361,   361,   361,   342,   361,
   342,   342,   342,   361,   361,   nil,   nil,   nil,   361,   nil,
   361,   361,   361,   361,   361,   361,   361,   nil,   nil,   nil,
   nil,   nil,   361,   361,   361,   361,   361,   361,   361,   nil,
   nil,   361,   nil,   nil,   nil,   nil,   nil,   nil,   361,   nil,
   nil,   361,   361,   361,   361,   361,   361,   361,   361,   nil,
   361,   361,   361,   nil,   361,   361,   361,   361,   361,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   361,   nil,
   nil,   361,   nil,   nil,   361,   361,   nil,   nil,   361,   nil,
   nil,   nil,   nil,   nil,   361,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   361,   nil,   nil,   nil,   nil,   361,   361,
   361,   361,   nil,   361,   361,   361,   361,   nil,   nil,   nil,
   nil,   361,   361,   nil,   nil,   nil,   377,   377,   377,   361,
   377,   361,   361,   361,   377,   377,   nil,   nil,   nil,   377,
   nil,   377,   377,   377,   377,   377,   377,   377,   nil,   nil,
   nil,   nil,   nil,   377,   377,   377,   377,   377,   377,   377,
   nil,   nil,   377,   nil,   nil,   nil,   nil,   nil,   nil,   377,
   nil,   nil,   377,   377,   377,   377,   377,   377,   377,   377,
   nil,   377,   377,   377,   nil,   377,   377,   377,   377,   377,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   377,
   nil,   nil,   377,   nil,   nil,   377,   377,   nil,   nil,   377,
   nil,   nil,   nil,   nil,   nil,   377,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   377,   nil,   nil,   nil,   nil,   377,
   377,   377,   377,   nil,   377,   377,   377,   377,   nil,   nil,
   nil,   nil,   377,   377,   nil,   nil,   nil,   405,   405,   405,
   377,   405,   377,   377,   377,   405,   405,   nil,   nil,   nil,
   405,   nil,   405,   405,   405,   405,   405,   405,   405,   nil,
   nil,   nil,   nil,   nil,   405,   405,   405,   405,   405,   405,
   405,   nil,   nil,   405,   nil,   nil,   nil,   nil,   nil,   nil,
   405,   nil,   nil,   405,   405,   405,   405,   405,   405,   405,
   405,   nil,   405,   405,   405,   nil,   405,   405,   405,   405,
   405,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   405,   nil,   nil,   405,   nil,   nil,   405,   405,   nil,   nil,
   405,   nil,   nil,   nil,   nil,   nil,   405,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   405,   nil,   nil,   nil,   nil,
   405,   405,   405,   405,   nil,   405,   405,   405,   405,   nil,
   nil,   nil,   nil,   405,   405,   nil,   nil,   nil,   443,   443,
   443,   405,   443,   405,   405,   405,   443,   443,   nil,   nil,
   nil,   443,   nil,   443,   443,   443,   443,   443,   443,   443,
   nil,   nil,   nil,   nil,   nil,   443,   443,   443,   443,   443,
   443,   443,   nil,   nil,   443,   nil,   nil,   nil,   nil,   nil,
   nil,   443,   nil,   nil,   443,   443,   443,   443,   443,   443,
   443,   443,   443,   443,   443,   443,   nil,   443,   443,   443,
   443,   443,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   443,   nil,   nil,   443,   nil,   nil,   443,   443,   nil,
   nil,   443,   nil,   443,   nil,   443,   nil,   443,   nil,   nil,
   443,   nil,   nil,   nil,   nil,   nil,   443,   nil,   nil,   nil,
   nil,   443,   443,   443,   443,   nil,   443,   443,   443,   443,
   nil,   nil,   nil,   nil,   443,   443,   nil,   nil,   nil,   445,
   445,   445,   443,   445,   443,   443,   443,   445,   445,   nil,
   nil,   nil,   445,   nil,   445,   445,   445,   445,   445,   445,
   445,   nil,   nil,   nil,   nil,   nil,   445,   445,   445,   445,
   445,   445,   445,   nil,   nil,   445,   nil,   nil,   nil,   nil,
   nil,   nil,   445,   nil,   nil,   445,   445,   445,   445,   445,
   445,   445,   445,   nil,   445,   445,   445,   nil,   445,   445,
   445,   445,   445,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   445,   nil,   nil,   445,   nil,   nil,   445,   445,
   nil,   nil,   445,   nil,   nil,   nil,   nil,   nil,   445,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   445,   nil,   nil,
   nil,   nil,   445,   445,   445,   445,   nil,   445,   445,   445,
   445,   nil,   nil,   nil,   nil,   445,   445,   nil,   nil,   nil,
   446,   446,   446,   445,   446,   445,   445,   445,   446,   446,
   nil,   nil,   nil,   446,   nil,   446,   446,   446,   446,   446,
   446,   446,   nil,   nil,   nil,   nil,   nil,   446,   446,   446,
   446,   446,   446,   446,   nil,   nil,   446,   nil,   nil,   nil,
   nil,   nil,   nil,   446,   nil,   nil,   446,   446,   446,   446,
   446,   446,   446,   446,   nil,   446,   446,   446,   nil,   446,
   446,   446,   446,   446,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   446,   nil,   nil,   446,   nil,   nil,   446,
   446,   nil,   nil,   446,   nil,   nil,   nil,   nil,   nil,   446,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   446,   nil,
   nil,   nil,   nil,   446,   446,   446,   446,   nil,   446,   446,
   446,   446,   nil,   nil,   nil,   nil,   446,   446,   nil,   nil,
   nil,   447,   447,   447,   446,   447,   446,   446,   446,   447,
   447,   nil,   nil,   nil,   447,   nil,   447,   447,   447,   447,
   447,   447,   447,   nil,   nil,   nil,   nil,   nil,   447,   447,
   447,   447,   447,   447,   447,   nil,   nil,   447,   nil,   nil,
   nil,   nil,   nil,   nil,   447,   nil,   nil,   447,   447,   447,
   447,   447,   447,   447,   447,   nil,   447,   447,   447,   nil,
   447,   447,   447,   447,   447,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   447,   nil,   nil,   447,   nil,   nil,
   447,   447,   nil,   nil,   447,   nil,   nil,   nil,   nil,   nil,
   447,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   447,
   nil,   nil,   nil,   nil,   447,   447,   447,   447,   nil,   447,
   447,   447,   447,   nil,   nil,   nil,   nil,   447,   447,   nil,
   nil,   nil,   487,   487,   487,   447,   487,   447,   447,   447,
   487,   487,   nil,   nil,   nil,   487,   nil,   487,   487,   487,
   487,   487,   487,   487,   nil,   nil,   nil,   nil,   nil,   487,
   487,   487,   487,   487,   487,   487,   nil,   nil,   487,   nil,
   nil,   nil,   nil,   nil,   nil,   487,   nil,   nil,   487,   487,
   487,   487,   487,   487,   487,   487,   487,   487,   487,   487,
   nil,   487,   487,   487,   487,   487,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   487,   nil,   nil,   487,   nil,
   nil,   487,   487,   nil,   nil,   487,   nil,   487,   nil,   487,
   nil,   487,   nil,   nil,   487,   nil,   nil,   nil,   nil,   nil,
   487,   nil,   nil,   nil,   nil,   487,   487,   487,   487,   nil,
   487,   487,   487,   487,   nil,   nil,   nil,   nil,   487,   487,
   nil,   nil,   nil,   489,   489,   489,   487,   489,   487,   487,
   487,   489,   489,   nil,   nil,   nil,   489,   nil,   489,   489,
   489,   489,   489,   489,   489,   nil,   nil,   nil,   nil,   nil,
   489,   489,   489,   489,   489,   489,   489,   nil,   nil,   489,
   nil,   nil,   nil,   nil,   nil,   nil,   489,   nil,   nil,   489,
   489,   489,   489,   489,   489,   489,   489,   489,   489,   489,
   489,   nil,   489,   489,   489,   489,   489,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   489,   nil,   nil,   489,
   nil,   nil,   489,   489,   nil,   nil,   489,   nil,   nil,   nil,
   489,   nil,   489,   nil,   nil,   489,   nil,   nil,   nil,   nil,
   nil,   489,   nil,   nil,   nil,   nil,   489,   489,   489,   489,
   nil,   489,   489,   489,   489,   nil,   nil,   nil,   nil,   489,
   489,   nil,   nil,   nil,   491,   491,   491,   489,   491,   489,
   489,   489,   491,   491,   nil,   nil,   nil,   491,   nil,   491,
   491,   491,   491,   491,   491,   491,   nil,   nil,   nil,   nil,
   nil,   491,   491,   491,   491,   491,   491,   491,   nil,   nil,
   491,   nil,   nil,   nil,   nil,   nil,   nil,   491,   nil,   nil,
   491,   491,   491,   491,   491,   491,   491,   491,   nil,   491,
   491,   491,   nil,   491,   491,   491,   491,   491,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   491,   nil,   nil,
   491,   nil,   nil,   491,   491,   nil,   nil,   491,   nil,   nil,
   nil,   nil,   nil,   491,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   491,   nil,   nil,   nil,   nil,   491,   491,   491,
   491,   nil,   491,   491,   491,   491,   nil,   nil,   nil,   nil,
   491,   491,   nil,   nil,   nil,   nil,   nil,   nil,   491,   nil,
   491,   491,   491,   497,   497,   497,   497,   497,   nil,   nil,
   nil,   497,   497,   nil,   nil,   nil,   497,   nil,   497,   497,
   497,   497,   497,   497,   497,   nil,   nil,   nil,   nil,   nil,
   497,   497,   497,   497,   497,   497,   497,   nil,   nil,   497,
   nil,   nil,   nil,   nil,   nil,   497,   497,   497,   497,   497,
   497,   497,   497,   497,   497,   497,   497,   nil,   497,   497,
   497,   nil,   497,   497,   497,   497,   497,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   497,   nil,   nil,   497,
   nil,   nil,   497,   497,   nil,   nil,   497,   nil,   497,   nil,
   nil,   nil,   497,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   497,   nil,   nil,   nil,   nil,   497,   497,   497,   497,
   nil,   497,   497,   497,   497,   nil,   nil,   nil,   nil,   497,
   497,   nil,   nil,   nil,   nil,   nil,   497,   497,   nil,   497,
   497,   497,   505,   505,   505,   nil,   505,   nil,   nil,   nil,
   505,   505,   nil,   nil,   nil,   505,   nil,   505,   505,   505,
   505,   505,   505,   505,   nil,   nil,   nil,   nil,   nil,   505,
   505,   505,   505,   505,   505,   505,   nil,   nil,   505,   nil,
   nil,   nil,   nil,   nil,   nil,   505,   nil,   nil,   505,   505,
   505,   505,   505,   505,   505,   505,   nil,   505,   505,   505,
   nil,   505,   505,   nil,   nil,   505,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   505,   nil,   nil,   505,   nil,
   nil,   505,   505,   nil,   nil,   505,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   505,   505,   505,   505,   nil,
   505,   505,   505,   505,   nil,   nil,   nil,   nil,   505,   505,
   nil,   nil,   nil,   507,   507,   507,   505,   507,   505,   505,
   505,   507,   507,   nil,   nil,   nil,   507,   nil,   507,   507,
   507,   507,   507,   507,   507,   nil,   nil,   nil,   nil,   nil,
   507,   507,   507,   507,   507,   507,   507,   nil,   nil,   507,
   nil,   nil,   nil,   nil,   nil,   nil,   507,   nil,   nil,   507,
   507,   507,   507,   507,   507,   507,   507,   507,   507,   507,
   507,   nil,   507,   507,   507,   507,   507,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   507,   nil,   nil,   507,
   nil,   nil,   507,   507,   nil,   nil,   507,   nil,   507,   nil,
   507,   nil,   507,   nil,   nil,   507,   nil,   nil,   nil,   nil,
   nil,   507,   nil,   nil,   nil,   nil,   507,   507,   507,   507,
   nil,   507,   507,   507,   507,   nil,   nil,   nil,   nil,   507,
   507,   nil,   nil,   nil,   513,   513,   513,   507,   513,   507,
   507,   507,   513,   513,   nil,   nil,   nil,   513,   nil,   513,
   513,   513,   513,   513,   513,   513,   nil,   nil,   nil,   nil,
   nil,   513,   513,   513,   513,   513,   513,   513,   nil,   nil,
   513,   nil,   nil,   nil,   nil,   nil,   nil,   513,   nil,   nil,
   513,   513,   513,   513,   513,   513,   513,   513,   nil,   513,
   513,   513,   nil,   513,   513,   nil,   nil,   513,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   513,   nil,   nil,
   513,   nil,   nil,   513,   513,   nil,   nil,   513,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   513,   513,   513,
   513,   nil,   513,   513,   513,   513,   nil,   nil,   nil,   nil,
   513,   513,   nil,   nil,   nil,   516,   516,   516,   513,   516,
   513,   513,   513,   516,   516,   nil,   nil,   nil,   516,   nil,
   516,   516,   516,   516,   516,   516,   516,   nil,   nil,   nil,
   nil,   nil,   516,   516,   516,   516,   516,   516,   516,   nil,
   nil,   516,   nil,   nil,   nil,   nil,   nil,   nil,   516,   nil,
   nil,   516,   516,   516,   516,   516,   516,   516,   516,   nil,
   516,   516,   516,   nil,   516,   516,   516,   516,   516,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   516,   nil,
   nil,   516,   nil,   nil,   516,   516,   nil,   nil,   516,   nil,
   nil,   nil,   nil,   nil,   516,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   516,   nil,   nil,   nil,   nil,   516,   516,
   516,   516,   nil,   516,   516,   516,   516,   nil,   nil,   nil,
   nil,   516,   516,   nil,   nil,   nil,   517,   517,   517,   516,
   517,   516,   516,   516,   517,   517,   nil,   nil,   nil,   517,
   nil,   517,   517,   517,   517,   517,   517,   517,   nil,   nil,
   nil,   nil,   nil,   517,   517,   517,   517,   517,   517,   517,
   nil,   nil,   517,   nil,   nil,   nil,   nil,   nil,   nil,   517,
   nil,   nil,   517,   517,   517,   517,   517,   517,   517,   517,
   nil,   517,   517,   517,   nil,   517,   517,   517,   517,   517,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   517,
   nil,   nil,   517,   nil,   nil,   517,   517,   nil,   nil,   517,
   nil,   nil,   nil,   nil,   nil,   517,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   517,   nil,   nil,   nil,   nil,   517,
   517,   517,   517,   nil,   517,   517,   517,   517,   nil,   nil,
   nil,   nil,   517,   517,   nil,   nil,   nil,   521,   521,   521,
   517,   521,   517,   517,   517,   521,   521,   nil,   nil,   nil,
   521,   nil,   521,   521,   521,   521,   521,   521,   521,   nil,
   nil,   nil,   nil,   nil,   521,   521,   521,   521,   521,   521,
   521,   nil,   nil,   521,   nil,   nil,   nil,   nil,   nil,   nil,
   521,   nil,   nil,   521,   521,   521,   521,   521,   521,   521,
   521,   nil,   521,   521,   521,   nil,   521,   521,   521,   521,
   521,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   521,   nil,   nil,   521,   nil,   nil,   521,   521,   nil,   nil,
   521,   nil,   nil,   nil,   nil,   nil,   521,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   521,   nil,   nil,   nil,   nil,
   521,   521,   521,   521,   nil,   521,   521,   521,   521,   nil,
   nil,   nil,   nil,   521,   521,   nil,   nil,   nil,   527,   527,
   527,   521,   527,   521,   521,   521,   527,   527,   nil,   nil,
   nil,   527,   nil,   527,   527,   527,   527,   527,   527,   527,
   nil,   nil,   nil,   nil,   nil,   527,   527,   527,   527,   527,
   527,   527,   nil,   nil,   527,   nil,   nil,   nil,   nil,   nil,
   nil,   527,   nil,   nil,   527,   527,   527,   527,   527,   527,
   527,   527,   527,   527,   527,   527,   nil,   527,   527,   527,
   527,   527,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   527,   nil,   nil,   527,   nil,   nil,   527,   527,   nil,
   nil,   527,   nil,   527,   nil,   nil,   nil,   527,   nil,   nil,
   527,   nil,   nil,   nil,   nil,   nil,   527,   nil,   nil,   nil,
   nil,   527,   527,   527,   527,   nil,   527,   527,   527,   527,
   nil,   nil,   nil,   nil,   527,   527,   nil,   nil,   nil,   530,
   530,   530,   527,   530,   527,   527,   527,   530,   530,   nil,
   nil,   nil,   530,   nil,   530,   530,   530,   530,   530,   530,
   530,   nil,   nil,   nil,   nil,   nil,   530,   530,   530,   530,
   530,   530,   530,   nil,   nil,   530,   nil,   nil,   nil,   nil,
   nil,   nil,   530,   nil,   nil,   530,   530,   530,   530,   530,
   530,   530,   530,   530,   530,   530,   530,   nil,   530,   530,
   530,   530,   530,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   530,   nil,   nil,   530,   nil,   nil,   530,   530,
   nil,   nil,   530,   nil,   nil,   nil,   nil,   nil,   530,   nil,
   nil,   530,   nil,   nil,   nil,   nil,   nil,   530,   nil,   nil,
   nil,   nil,   530,   530,   530,   530,   nil,   530,   530,   530,
   530,   nil,   nil,   nil,   nil,   530,   530,   nil,   nil,   nil,
   544,   544,   544,   530,   544,   530,   530,   530,   544,   544,
   nil,   nil,   nil,   544,   nil,   544,   544,   544,   544,   544,
   544,   544,   nil,   nil,   nil,   nil,   nil,   544,   544,   544,
   544,   544,   544,   544,   nil,   nil,   544,   nil,   nil,   nil,
   nil,   nil,   nil,   544,   nil,   nil,   544,   544,   544,   544,
   544,   544,   544,   544,   nil,   544,   544,   544,   nil,   544,
   544,   544,   544,   544,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   544,   nil,   nil,   544,   nil,   nil,   544,
   544,   nil,   nil,   544,   nil,   544,   nil,   nil,   nil,   544,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   544,   nil,
   nil,   nil,   nil,   544,   544,   544,   544,   nil,   544,   544,
   544,   544,   nil,   nil,   nil,   nil,   544,   544,   nil,   nil,
   nil,   545,   545,   545,   544,   545,   544,   544,   544,   545,
   545,   nil,   nil,   nil,   545,   nil,   545,   545,   545,   545,
   545,   545,   545,   nil,   nil,   nil,   nil,   nil,   545,   545,
   545,   545,   545,   545,   545,   nil,   nil,   545,   nil,   nil,
   nil,   nil,   nil,   nil,   545,   nil,   nil,   545,   545,   545,
   545,   545,   545,   545,   545,   545,   545,   545,   545,   nil,
   545,   545,   545,   545,   545,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   545,   nil,   nil,   545,   nil,   nil,
   545,   545,   nil,   nil,   545,   nil,   545,   nil,   545,   nil,
   545,   nil,   nil,   545,   nil,   nil,   nil,   nil,   nil,   545,
   nil,   nil,   nil,   nil,   545,   545,   545,   545,   nil,   545,
   545,   545,   545,   nil,   nil,   nil,   nil,   545,   545,   nil,
   nil,   nil,   555,   555,   555,   545,   555,   545,   545,   545,
   555,   555,   nil,   nil,   nil,   555,   nil,   555,   555,   555,
   555,   555,   555,   555,   nil,   nil,   nil,   nil,   nil,   555,
   555,   555,   555,   555,   555,   555,   nil,   nil,   555,   nil,
   nil,   nil,   nil,   nil,   nil,   555,   nil,   nil,   555,   555,
   555,   555,   555,   555,   555,   555,   555,   555,   555,   555,
   nil,   555,   555,   555,   555,   555,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   555,   nil,   nil,   555,   nil,
   nil,   555,   555,   nil,   nil,   555,   nil,   555,   nil,   555,
   nil,   555,   nil,   nil,   555,   nil,   nil,   nil,   nil,   nil,
   555,   nil,   nil,   nil,   nil,   555,   555,   555,   555,   nil,
   555,   555,   555,   555,   nil,   nil,   nil,   nil,   555,   555,
   nil,   nil,   nil,   589,   589,   589,   555,   589,   555,   555,
   555,   589,   589,   nil,   nil,   nil,   589,   nil,   589,   589,
   589,   589,   589,   589,   589,   nil,   nil,   nil,   nil,   nil,
   589,   589,   589,   589,   589,   589,   589,   nil,   nil,   589,
   nil,   nil,   nil,   nil,   nil,   nil,   589,   nil,   nil,   589,
   589,   589,   589,   589,   589,   589,   589,   nil,   589,   589,
   589,   nil,   589,   589,   589,   589,   589,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   589,   nil,   nil,   589,
   nil,   nil,   589,   589,   nil,   nil,   589,   nil,   589,   nil,
   nil,   nil,   589,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   589,   nil,   nil,   nil,   nil,   589,   589,   589,   589,
   nil,   589,   589,   589,   589,   nil,   nil,   nil,   nil,   589,
   589,   nil,   nil,   nil,   590,   590,   590,   589,   590,   589,
   589,   589,   590,   590,   nil,   nil,   nil,   590,   nil,   590,
   590,   590,   590,   590,   590,   590,   nil,   nil,   nil,   nil,
   nil,   590,   590,   590,   590,   590,   590,   590,   nil,   nil,
   590,   nil,   nil,   nil,   nil,   nil,   nil,   590,   nil,   nil,
   590,   590,   590,   590,   590,   590,   590,   590,   nil,   590,
   590,   590,   nil,   590,   590,   590,   590,   590,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   590,   nil,   nil,
   590,   nil,   nil,   590,   590,   nil,   nil,   590,   nil,   nil,
   nil,   nil,   nil,   590,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   590,   nil,   nil,   nil,   nil,   590,   590,   590,
   590,   nil,   590,   590,   590,   590,   nil,   nil,   nil,   nil,
   590,   590,   nil,   nil,   nil,   593,   593,   593,   590,   593,
   590,   590,   590,   593,   593,   nil,   nil,   nil,   593,   nil,
   593,   593,   593,   593,   593,   593,   593,   nil,   nil,   nil,
   nil,   nil,   593,   593,   593,   593,   593,   593,   593,   nil,
   nil,   593,   nil,   nil,   nil,   nil,   nil,   nil,   593,   nil,
   nil,   593,   593,   593,   593,   593,   593,   593,   593,   593,
   593,   593,   593,   nil,   593,   593,   593,   593,   593,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   593,   nil,
   nil,   593,   nil,   nil,   593,   593,   nil,   nil,   593,   nil,
   593,   nil,   593,   nil,   593,   nil,   nil,   593,   nil,   nil,
   nil,   nil,   nil,   593,   nil,   nil,   nil,   nil,   593,   593,
   593,   593,   nil,   593,   593,   593,   593,   nil,   nil,   nil,
   nil,   593,   593,   nil,   nil,   nil,   594,   594,   594,   593,
   594,   593,   593,   593,   594,   594,   nil,   nil,   nil,   594,
   nil,   594,   594,   594,   594,   594,   594,   594,   nil,   nil,
   nil,   nil,   nil,   594,   594,   594,   594,   594,   594,   594,
   nil,   nil,   594,   nil,   nil,   nil,   nil,   nil,   nil,   594,
   nil,   nil,   594,   594,   594,   594,   594,   594,   594,   594,
   594,   594,   594,   594,   nil,   594,   594,   594,   594,   594,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   594,
   nil,   nil,   594,   nil,   nil,   594,   594,   nil,   nil,   594,
   nil,   nil,   nil,   594,   nil,   594,   nil,   nil,   594,   nil,
   nil,   nil,   nil,   nil,   594,   nil,   nil,   nil,   nil,   594,
   594,   594,   594,   nil,   594,   594,   594,   594,   nil,   nil,
   nil,   nil,   594,   594,   nil,   nil,   nil,   595,   595,   595,
   594,   595,   594,   594,   594,   595,   595,   nil,   nil,   nil,
   595,   nil,   595,   595,   595,   595,   595,   595,   595,   nil,
   nil,   nil,   nil,   nil,   595,   595,   595,   595,   595,   595,
   595,   nil,   nil,   595,   nil,   nil,   nil,   nil,   nil,   nil,
   595,   nil,   nil,   595,   595,   595,   595,   595,   595,   595,
   595,   nil,   595,   595,   595,   nil,   595,   595,   595,   595,
   595,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   595,   nil,   nil,   595,   nil,   nil,   595,   595,   nil,   nil,
   595,   nil,   nil,   nil,   nil,   nil,   595,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   595,   nil,   nil,   nil,   nil,
   595,   595,   595,   595,   nil,   595,   595,   595,   595,   nil,
   nil,   nil,   nil,   595,   595,   nil,   nil,   nil,   596,   596,
   596,   595,   596,   595,   595,   595,   596,   596,   nil,   nil,
   nil,   596,   nil,   596,   596,   596,   596,   596,   596,   596,
   nil,   nil,   nil,   nil,   nil,   596,   596,   596,   596,   596,
   596,   596,   nil,   nil,   596,   nil,   nil,   nil,   nil,   nil,
   nil,   596,   nil,   nil,   596,   596,   596,   596,   596,   596,
   596,   596,   nil,   596,   596,   596,   nil,   596,   596,   596,
   596,   596,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   596,   nil,   nil,   596,   nil,   nil,   596,   596,   nil,
   nil,   596,   nil,   nil,   nil,   nil,   nil,   596,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   596,   nil,   nil,   nil,
   nil,   596,   596,   596,   596,   nil,   596,   596,   596,   596,
   nil,   nil,   nil,   nil,   596,   596,   nil,   nil,   nil,   600,
   600,   600,   596,   600,   596,   596,   596,   600,   600,   nil,
   nil,   nil,   600,   nil,   600,   600,   600,   600,   600,   600,
   600,   nil,   nil,   nil,   nil,   nil,   600,   600,   600,   600,
   600,   600,   600,   nil,   nil,   600,   nil,   nil,   nil,   nil,
   nil,   nil,   600,   nil,   nil,   600,   600,   600,   600,   600,
   600,   600,   600,   nil,   600,   600,   600,   nil,   600,   600,
   600,   600,   600,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   600,   nil,   nil,   600,   nil,   nil,   600,   600,
   nil,   nil,   600,   nil,   nil,   nil,   nil,   nil,   600,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   600,   nil,   nil,
   nil,   nil,   600,   600,   600,   600,   nil,   600,   600,   600,
   600,   nil,   nil,   nil,   nil,   600,   600,   nil,   nil,   nil,
   601,   601,   601,   600,   601,   600,   600,   600,   601,   601,
   nil,   nil,   nil,   601,   nil,   601,   601,   601,   601,   601,
   601,   601,   nil,   nil,   nil,   nil,   nil,   601,   601,   601,
   601,   601,   601,   601,   nil,   nil,   601,   nil,   nil,   nil,
   nil,   nil,   nil,   601,   nil,   nil,   601,   601,   601,   601,
   601,   601,   601,   601,   nil,   601,   601,   601,   nil,   601,
   601,   601,   601,   601,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   601,   nil,   nil,   601,   nil,   nil,   601,
   601,   nil,   nil,   601,   nil,   nil,   nil,   nil,   nil,   601,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   601,   nil,
   nil,   nil,   nil,   601,   601,   601,   601,   nil,   601,   601,
   601,   601,   nil,   nil,   nil,   nil,   601,   601,   nil,   nil,
   nil,   604,   604,   604,   601,   604,   601,   601,   601,   604,
   604,   nil,   nil,   nil,   604,   nil,   604,   604,   604,   604,
   604,   604,   604,   nil,   nil,   nil,   nil,   nil,   604,   604,
   604,   604,   604,   604,   604,   nil,   nil,   604,   nil,   nil,
   nil,   nil,   nil,   nil,   604,   nil,   nil,   604,   604,   604,
   604,   604,   604,   604,   604,   nil,   604,   604,   604,   nil,
   604,   604,   604,   604,   604,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   604,   nil,   nil,   604,   nil,   nil,
   604,   604,   nil,   nil,   604,   nil,   nil,   nil,   nil,   nil,
   604,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   604,
   nil,   nil,   nil,   nil,   604,   604,   604,   604,   nil,   604,
   604,   604,   604,   nil,   nil,   nil,   nil,   604,   604,   nil,
   nil,   nil,   605,   605,   605,   604,   605,   604,   604,   604,
   605,   605,   nil,   nil,   nil,   605,   nil,   605,   605,   605,
   605,   605,   605,   605,   nil,   nil,   nil,   nil,   nil,   605,
   605,   605,   605,   605,   605,   605,   nil,   nil,   605,   nil,
   nil,   nil,   nil,   nil,   nil,   605,   nil,   nil,   605,   605,
   605,   605,   605,   605,   605,   605,   nil,   605,   605,   605,
   nil,   605,   605,   605,   605,   605,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   605,   nil,   nil,   605,   nil,
   nil,   605,   605,   nil,   nil,   605,   nil,   nil,   nil,   nil,
   nil,   605,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   605,   nil,   nil,   nil,   nil,   605,   605,   605,   605,   nil,
   605,   605,   605,   605,   nil,   nil,   nil,   nil,   605,   605,
   nil,   nil,   nil,   629,   629,   629,   605,   629,   605,   605,
   605,   629,   629,   nil,   nil,   nil,   629,   nil,   629,   629,
   629,   629,   629,   629,   629,   nil,   nil,   nil,   nil,   nil,
   629,   629,   629,   629,   629,   629,   629,   nil,   nil,   629,
   nil,   nil,   nil,   nil,   nil,   nil,   629,   nil,   nil,   629,
   629,   629,   629,   629,   629,   629,   629,   nil,   629,   629,
   629,   nil,   629,   629,   629,   629,   629,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   629,   nil,   nil,   629,
   nil,   nil,   629,   629,   nil,   nil,   629,   nil,   nil,   nil,
   nil,   nil,   629,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   629,   nil,   nil,   nil,   nil,   629,   629,   629,   629,
   nil,   629,   629,   629,   629,   nil,   nil,   nil,   nil,   629,
   629,   nil,   nil,   nil,   632,   632,   632,   629,   632,   629,
   629,   629,   632,   632,   nil,   nil,   nil,   632,   nil,   632,
   632,   632,   632,   632,   632,   632,   nil,   nil,   nil,   nil,
   nil,   632,   632,   632,   632,   632,   632,   632,   nil,   nil,
   632,   nil,   nil,   nil,   nil,   nil,   nil,   632,   nil,   nil,
   632,   632,   632,   632,   632,   632,   632,   632,   nil,   632,
   632,   632,   nil,   632,   632,   632,   632,   632,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   632,   nil,   nil,
   632,   nil,   nil,   632,   632,   nil,   nil,   632,   nil,   nil,
   nil,   nil,   nil,   632,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   632,   nil,   nil,   nil,   nil,   632,   632,   632,
   632,   nil,   632,   632,   632,   632,   nil,   nil,   nil,   nil,
   632,   632,   nil,   nil,   nil,   636,   636,   636,   632,   636,
   632,   632,   632,   636,   636,   nil,   nil,   nil,   636,   nil,
   636,   636,   636,   636,   636,   636,   636,   nil,   nil,   nil,
   nil,   nil,   636,   636,   636,   636,   636,   636,   636,   nil,
   nil,   636,   nil,   nil,   nil,   nil,   nil,   nil,   636,   nil,
   nil,   636,   636,   636,   636,   636,   636,   636,   636,   nil,
   636,   636,   636,   nil,   636,   636,   nil,   nil,   636,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   636,   nil,
   nil,   636,   nil,   nil,   636,   636,   nil,   nil,   636,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   636,   636,
   636,   636,   nil,   636,   636,   636,   636,   nil,   nil,   nil,
   nil,   636,   636,   nil,   nil,   nil,   647,   647,   647,   636,
   647,   636,   636,   636,   647,   647,   nil,   nil,   nil,   647,
   nil,   647,   647,   647,   647,   647,   647,   647,   nil,   nil,
   nil,   nil,   nil,   647,   647,   647,   647,   647,   647,   647,
   nil,   nil,   647,   nil,   nil,   nil,   nil,   nil,   nil,   647,
   nil,   nil,   647,   647,   647,   647,   647,   647,   647,   647,
   nil,   647,   647,   647,   nil,   647,   647,   nil,   nil,   647,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   647,
   nil,   nil,   647,   nil,   nil,   647,   647,   nil,   nil,   647,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   647,
   647,   647,   647,   nil,   647,   647,   647,   647,   nil,   nil,
   nil,   nil,   647,   647,   nil,   nil,   nil,   652,   652,   652,
   647,   652,   647,   647,   647,   652,   652,   nil,   nil,   nil,
   652,   nil,   652,   652,   652,   652,   652,   652,   652,   nil,
   nil,   nil,   nil,   nil,   652,   652,   652,   652,   652,   652,
   652,   nil,   nil,   652,   nil,   nil,   nil,   nil,   nil,   nil,
   652,   nil,   nil,   652,   652,   652,   652,   652,   652,   652,
   652,   nil,   652,   652,   652,   nil,   652,   652,   652,   652,
   652,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   652,   nil,   nil,   652,   nil,   nil,   652,   652,   nil,   nil,
   652,   nil,   652,   nil,   nil,   nil,   652,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   652,   nil,   nil,   nil,   nil,
   652,   652,   652,   652,   nil,   652,   652,   652,   652,   nil,
   nil,   nil,   nil,   652,   652,   nil,   nil,   nil,   678,   678,
   678,   652,   678,   652,   652,   652,   678,   678,   nil,   nil,
   nil,   678,   nil,   678,   678,   678,   678,   678,   678,   678,
   nil,   nil,   nil,   nil,   nil,   678,   678,   678,   678,   678,
   678,   678,   nil,   nil,   678,   nil,   nil,   nil,   nil,   nil,
   nil,   678,   nil,   nil,   678,   678,   678,   678,   678,   678,
   678,   678,   nil,   678,   678,   678,   nil,   678,   678,   678,
   678,   678,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   678,   nil,   nil,   678,   nil,   nil,   678,   678,   nil,
   nil,   678,   nil,   nil,   nil,   nil,   nil,   678,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   678,   nil,   nil,   nil,
   nil,   678,   678,   678,   678,   nil,   678,   678,   678,   678,
   nil,   nil,   nil,   nil,   678,   678,   nil,   nil,   nil,   705,
   705,   705,   678,   705,   678,   678,   678,   705,   705,   nil,
   nil,   nil,   705,   nil,   705,   705,   705,   705,   705,   705,
   705,   nil,   nil,   nil,   nil,   nil,   705,   705,   705,   705,
   705,   705,   705,   nil,   nil,   705,   nil,   nil,   nil,   nil,
   nil,   nil,   705,   nil,   nil,   705,   705,   705,   705,   705,
   705,   705,   705,   nil,   705,   705,   705,   nil,   705,   705,
   705,   705,   705,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   705,   nil,   nil,   705,   nil,   nil,   705,   705,
   nil,   nil,   705,   nil,   nil,   nil,   nil,   nil,   705,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   705,   nil,   nil,
   nil,   nil,   705,   705,   705,   705,   nil,   705,   705,   705,
   705,   nil,   nil,   nil,   nil,   705,   705,   nil,   nil,   nil,
   711,   711,   711,   705,   711,   705,   705,   705,   711,   711,
   nil,   nil,   nil,   711,   nil,   711,   711,   711,   711,   711,
   711,   711,   nil,   nil,   nil,   nil,   nil,   711,   711,   711,
   711,   711,   711,   711,   nil,   nil,   711,   nil,   nil,   nil,
   nil,   nil,   nil,   711,   nil,   nil,   711,   711,   711,   711,
   711,   711,   711,   711,   nil,   711,   711,   711,   nil,   711,
   711,   711,   711,   711,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   711,   nil,   nil,   711,   nil,   nil,   711,
   711,   nil,   nil,   711,   nil,   nil,   nil,   nil,   nil,   711,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   711,   nil,
   nil,   nil,   nil,   711,   711,   711,   711,   nil,   711,   711,
   711,   711,   nil,   nil,   nil,   nil,   711,   711,   nil,   nil,
   nil,   734,   734,   734,   711,   734,   711,   711,   711,   734,
   734,   nil,   nil,   nil,   734,   nil,   734,   734,   734,   734,
   734,   734,   734,   nil,   nil,   nil,   nil,   nil,   734,   734,
   734,   734,   734,   734,   734,   nil,   nil,   734,   nil,   nil,
   nil,   nil,   nil,   nil,   734,   nil,   nil,   734,   734,   734,
   734,   734,   734,   734,   734,   nil,   734,   734,   734,   nil,
   734,   734,   734,   734,   734,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   734,   nil,   nil,   734,   nil,   nil,
   734,   734,   nil,   nil,   734,   nil,   nil,   nil,   nil,   nil,
   734,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   734,
   nil,   nil,   nil,   nil,   734,   734,   734,   734,   nil,   734,
   734,   734,   734,   nil,   nil,   nil,   nil,   734,   734,   nil,
   nil,   nil,   736,   736,   736,   734,   736,   734,   734,   734,
   736,   736,   nil,   nil,   nil,   736,   nil,   736,   736,   736,
   736,   736,   736,   736,   nil,   nil,   nil,   nil,   nil,   736,
   736,   736,   736,   736,   736,   736,   nil,   nil,   736,   nil,
   nil,   nil,   nil,   nil,   nil,   736,   nil,   nil,   736,   736,
   736,   736,   736,   736,   736,   736,   nil,   736,   736,   736,
   nil,   736,   736,   736,   736,   736,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   736,   nil,   nil,   736,   nil,
   nil,   736,   736,   nil,   nil,   736,   nil,   nil,   nil,   nil,
   nil,   736,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   736,   nil,   nil,   nil,   nil,   736,   736,   736,   736,   nil,
   736,   736,   736,   736,   nil,   nil,   nil,   nil,   736,   736,
   nil,   nil,   nil,   750,   750,   750,   736,   750,   736,   736,
   736,   750,   750,   nil,   nil,   nil,   750,   nil,   750,   750,
   750,   750,   750,   750,   750,   nil,   nil,   nil,   nil,   nil,
   750,   750,   750,   750,   750,   750,   750,   nil,   nil,   750,
   nil,   nil,   nil,   nil,   nil,   nil,   750,   nil,   nil,   750,
   750,   750,   750,   750,   750,   750,   750,   nil,   750,   750,
   750,   nil,   750,   750,   750,   750,   750,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   750,   nil,   nil,   750,
   nil,   nil,   750,   750,   nil,   nil,   750,   nil,   nil,   nil,
   nil,   nil,   750,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   750,   nil,   nil,   nil,   nil,   750,   750,   750,   750,
   nil,   750,   750,   750,   750,   nil,   nil,   nil,   nil,   750,
   750,   nil,   nil,   nil,   751,   751,   751,   750,   751,   750,
   750,   750,   751,   751,   nil,   nil,   nil,   751,   nil,   751,
   751,   751,   751,   751,   751,   751,   nil,   nil,   nil,   nil,
   nil,   751,   751,   751,   751,   751,   751,   751,   nil,   nil,
   751,   nil,   nil,   nil,   nil,   nil,   nil,   751,   nil,   nil,
   751,   751,   751,   751,   751,   751,   751,   751,   nil,   751,
   751,   751,   nil,   751,   751,   751,   751,   751,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   751,   nil,   nil,
   751,   nil,   nil,   751,   751,   nil,   nil,   751,   nil,   nil,
   nil,   nil,   nil,   751,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   751,   nil,   nil,   nil,   nil,   751,   751,   751,
   751,   nil,   751,   751,   751,   751,   nil,   nil,   nil,   nil,
   751,   751,   nil,   nil,   nil,   752,   752,   752,   751,   752,
   751,   751,   751,   752,   752,   nil,   nil,   nil,   752,   nil,
   752,   752,   752,   752,   752,   752,   752,   nil,   nil,   nil,
   nil,   nil,   752,   752,   752,   752,   752,   752,   752,   nil,
   nil,   752,   nil,   nil,   nil,   nil,   nil,   nil,   752,   nil,
   nil,   752,   752,   752,   752,   752,   752,   752,   752,   nil,
   752,   752,   752,   nil,   752,   752,   752,   752,   752,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   752,   nil,
   nil,   752,   nil,   nil,   752,   752,   nil,   nil,   752,   nil,
   nil,   nil,   nil,   nil,   752,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   752,   nil,   nil,   nil,   nil,   752,   752,
   752,   752,   nil,   752,   752,   752,   752,   nil,   nil,   nil,
   nil,   752,   752,   nil,   nil,   nil,   753,   753,   753,   752,
   753,   752,   752,   752,   753,   753,   nil,   nil,   nil,   753,
   nil,   753,   753,   753,   753,   753,   753,   753,   nil,   nil,
   nil,   nil,   nil,   753,   753,   753,   753,   753,   753,   753,
   nil,   nil,   753,   nil,   nil,   nil,   nil,   nil,   nil,   753,
   nil,   nil,   753,   753,   753,   753,   753,   753,   753,   753,
   nil,   753,   753,   753,   nil,   753,   753,   753,   753,   753,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   753,
   nil,   nil,   753,   nil,   nil,   753,   753,   nil,   nil,   753,
   nil,   nil,   nil,   nil,   nil,   753,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   753,   nil,   nil,   nil,   nil,   753,
   753,   753,   753,   nil,   753,   753,   753,   753,   nil,   nil,
   nil,   nil,   753,   753,   nil,   nil,   nil,   755,   755,   755,
   753,   755,   753,   753,   753,   755,   755,   nil,   nil,   nil,
   755,   nil,   755,   755,   755,   755,   755,   755,   755,   nil,
   nil,   nil,   nil,   nil,   755,   755,   755,   755,   755,   755,
   755,   nil,   nil,   755,   nil,   nil,   nil,   nil,   nil,   nil,
   755,   nil,   nil,   755,   755,   755,   755,   755,   755,   755,
   755,   nil,   755,   755,   755,   nil,   755,   755,   755,   755,
   755,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   755,   nil,   nil,   755,   nil,   nil,   755,   755,   nil,   nil,
   755,   nil,   nil,   nil,   nil,   nil,   755,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   755,   nil,   nil,   nil,   nil,
   755,   755,   755,   755,   nil,   755,   755,   755,   755,   nil,
   nil,   nil,   nil,   755,   755,   nil,   nil,   nil,   767,   767,
   767,   755,   767,   755,   755,   755,   767,   767,   nil,   nil,
   nil,   767,   nil,   767,   767,   767,   767,   767,   767,   767,
   nil,   nil,   nil,   nil,   nil,   767,   767,   767,   767,   767,
   767,   767,   nil,   nil,   767,   nil,   nil,   nil,   nil,   nil,
   nil,   767,   nil,   nil,   767,   767,   767,   767,   767,   767,
   767,   767,   nil,   767,   767,   767,   nil,   767,   767,   nil,
   nil,   767,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   767,   nil,   nil,   767,   nil,   nil,   767,   767,   nil,
   nil,   767,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   767,   767,   767,   767,   nil,   767,   767,   767,   767,
   nil,   nil,   nil,   nil,   767,   767,   nil,   nil,   nil,   805,
   805,   805,   767,   805,   767,   767,   767,   805,   805,   nil,
   nil,   nil,   805,   nil,   805,   805,   805,   805,   805,   805,
   805,   nil,   nil,   nil,   nil,   nil,   805,   805,   805,   805,
   805,   805,   805,   nil,   nil,   805,   nil,   nil,   nil,   nil,
   nil,   nil,   805,   nil,   nil,   805,   805,   805,   805,   805,
   805,   805,   805,   nil,   805,   805,   805,   nil,   805,   805,
   805,   805,   805,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   805,   nil,   nil,   805,   nil,   nil,   805,   805,
   nil,   nil,   805,   nil,   nil,   nil,   nil,   nil,   805,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   805,   nil,   nil,
   nil,   nil,   805,   805,   805,   805,   nil,   805,   805,   805,
   805,   nil,   nil,   nil,   nil,   805,   805,   nil,   nil,   nil,
   818,   818,   818,   805,   818,   805,   805,   805,   818,   818,
   nil,   nil,   nil,   818,   nil,   818,   818,   818,   818,   818,
   818,   818,   nil,   nil,   nil,   nil,   nil,   818,   818,   818,
   818,   818,   818,   818,   nil,   nil,   818,   nil,   nil,   nil,
   nil,   nil,   nil,   818,   nil,   nil,   818,   818,   818,   818,
   818,   818,   818,   818,   nil,   818,   818,   818,   nil,   818,
   818,   818,   818,   818,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   818,   nil,   nil,   818,   nil,   nil,   818,
   818,   nil,   nil,   818,   nil,   nil,   nil,   nil,   nil,   818,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   818,   nil,
   nil,   nil,   nil,   818,   818,   818,   818,   nil,   818,   818,
   818,   818,   nil,   nil,   nil,   nil,   818,   818,   nil,   nil,
   nil,   823,   823,   823,   818,   823,   818,   818,   818,   823,
   823,   nil,   nil,   nil,   823,   nil,   823,   823,   823,   823,
   823,   823,   823,   nil,   nil,   nil,   nil,   nil,   823,   823,
   823,   823,   823,   823,   823,   nil,   nil,   823,   nil,   nil,
   nil,   nil,   nil,   nil,   823,   nil,   nil,   823,   823,   823,
   823,   823,   823,   823,   823,   nil,   823,   823,   823,   nil,
   823,   823,   823,   823,   823,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   823,   nil,   nil,   823,   nil,   nil,
   823,   823,   nil,   nil,   823,   nil,   823,   nil,   nil,   nil,
   823,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   823,
   nil,   nil,   nil,   nil,   823,   823,   823,   823,   nil,   823,
   823,   823,   823,   nil,   nil,   nil,   nil,   823,   823,   nil,
   nil,   nil,   840,   840,   840,   823,   840,   823,   823,   823,
   840,   840,   nil,   nil,   nil,   840,   nil,   840,   840,   840,
   840,   840,   840,   840,   nil,   nil,   nil,   nil,   nil,   840,
   840,   840,   840,   840,   840,   840,   nil,   nil,   840,   nil,
   nil,   nil,   nil,   nil,   nil,   840,   nil,   nil,   840,   840,
   840,   840,   840,   840,   840,   840,   840,   840,   840,   840,
   nil,   840,   840,   840,   840,   840,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   840,   nil,   nil,   840,   nil,
   nil,   840,   840,   nil,   nil,   840,   nil,   nil,   nil,   840,
   nil,   840,   nil,   nil,   840,   nil,   nil,   nil,   nil,   nil,
   840,   nil,   nil,   nil,   nil,   840,   840,   840,   840,   nil,
   840,   840,   840,   840,   nil,   nil,   nil,   nil,   840,   840,
   nil,   nil,   nil,   841,   841,   841,   840,   841,   840,   840,
   840,   841,   841,   nil,   nil,   nil,   841,   nil,   841,   841,
   841,   841,   841,   841,   841,   nil,   nil,   nil,   nil,   nil,
   841,   841,   841,   841,   841,   841,   841,   nil,   nil,   841,
   nil,   nil,   nil,   nil,   nil,   nil,   841,   nil,   nil,   841,
   841,   841,   841,   841,   841,   841,   841,   nil,   841,   841,
   841,   nil,   841,   841,   841,   841,   841,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   841,   nil,   nil,   841,
   nil,   nil,   841,   841,   nil,   nil,   841,   nil,   nil,   nil,
   nil,   nil,   841,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   841,   nil,   nil,   nil,   nil,   841,   841,   841,   841,
   nil,   841,   841,   841,   841,   nil,   nil,   nil,   nil,   841,
   841,   nil,   nil,   nil,   855,   855,   855,   841,   855,   841,
   841,   841,   855,   855,   nil,   nil,   nil,   855,   nil,   855,
   855,   855,   855,   855,   855,   855,   nil,   nil,   nil,   nil,
   nil,   855,   855,   855,   855,   855,   855,   855,   nil,   nil,
   855,   nil,   nil,   nil,   nil,   nil,   nil,   855,   nil,   nil,
   855,   855,   855,   855,   855,   855,   855,   855,   nil,   855,
   855,   855,   nil,   855,   855,   nil,   nil,   855,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   855,   nil,   nil,
   855,   nil,   nil,   855,   855,   nil,   nil,   855,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   855,   855,   855,
   855,   nil,   855,   855,   855,   855,   nil,   nil,   nil,   nil,
   855,   855,   nil,   nil,   nil,   867,   867,   867,   855,   867,
   855,   855,   855,   867,   867,   nil,   nil,   nil,   867,   nil,
   867,   867,   867,   867,   867,   867,   867,   nil,   nil,   nil,
   nil,   nil,   867,   867,   867,   867,   867,   867,   867,   nil,
   nil,   867,   nil,   nil,   nil,   nil,   nil,   nil,   867,   nil,
   nil,   867,   867,   867,   867,   867,   867,   867,   867,   nil,
   867,   867,   867,   nil,   867,   867,   nil,   nil,   867,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   867,   nil,
   nil,   867,   nil,   nil,   867,   867,   nil,   nil,   867,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   867,   867,
   867,   867,   nil,   867,   867,   867,   867,   nil,   nil,   nil,
   nil,   867,   867,   nil,   nil,   nil,   976,   976,   976,   867,
   976,   867,   867,   867,   976,   976,   nil,   nil,   nil,   976,
   nil,   976,   976,   976,   976,   976,   976,   976,   nil,   nil,
   nil,   nil,   nil,   976,   976,   976,   976,   976,   976,   976,
   nil,   nil,   976,   nil,   nil,   nil,   nil,   nil,   nil,   976,
   nil,   nil,   976,   976,   976,   976,   976,   976,   976,   976,
   976,   976,   976,   976,   nil,   976,   976,   976,   976,   976,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   976,
   nil,   nil,   976,   nil,   nil,   976,   976,   nil,   nil,   976,
   nil,   976,   nil,   976,   nil,   976,   nil,   nil,   976,   nil,
   nil,   nil,   nil,   nil,   976,   nil,   nil,   nil,   nil,   976,
   976,   976,   976,   nil,   976,   976,   976,   976,   nil,   nil,
   nil,   nil,   976,   976,   nil,   nil,   nil,   nil,    56,   nil,
   976,   nil,   976,   976,   976,    56,    56,    56,   nil,   nil,
    56,    56,    56,   nil,    56,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    56,    56,    56,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    56,    56,   nil,    56,    56,    56,
    56,    56,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    56,    56,    56,    56,    56,    56,
    56,    56,    56,    56,    56,    56,    56,    56,   nil,   nil,
    56,    56,    56,   nil,   nil,    56,   nil,   nil,    56,   nil,
   nil,    56,    56,   nil,    56,   nil,    56,   nil,    56,   nil,
    56,    56,   nil,    56,    56,    56,    56,    56,   nil,    56,
   nil,    56,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    56,   nil,   nil,    56,    56,
    56,    56,   425,    56,   nil,    56,   nil,   nil,   nil,   425,
   425,   425,   nil,   nil,   425,   425,   425,   nil,   425,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   425,   425,   425,
   425,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   425,   425,
   nil,   425,   425,   425,   425,   425,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   425,   425,
   425,   425,   425,   425,   425,   425,   425,   425,   425,   425,
   425,   425,   nil,   nil,   425,   425,   425,   nil,   nil,   425,
   nil,   nil,   425,   nil,   nil,   425,   425,   nil,   425,   nil,
   425,   nil,   425,   nil,   425,   425,   nil,   425,   425,   425,
   425,   425,   nil,   425,   425,   425,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   425,
   nil,   nil,   425,   425,   425,   425,   426,   425,   nil,   425,
   nil,   nil,   nil,   426,   426,   426,   nil,   nil,   426,   426,
   426,   nil,   426,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   426,   426,   426,   426,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   426,   426,   nil,   426,   426,   426,   426,   426,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   426,   426,   426,   426,   426,   426,   426,   426,
   426,   426,   426,   426,   426,   426,   nil,   nil,   426,   426,
   426,   nil,   nil,   426,   nil,   nil,   426,   nil,   nil,   426,
   426,   nil,   426,   nil,   426,   nil,   426,   nil,   426,   426,
   nil,   426,   426,   426,   426,   426,   nil,   426,   426,   426,
   942,   nil,   942,   942,   942,   942,   942,   nil,   nil,   nil,
   nil,   nil,   nil,   426,   nil,   942,   426,   426,   426,   426,
    27,   426,   nil,   426,   nil,   nil,   nil,    27,    27,    27,
   nil,   nil,    27,    27,    27,   nil,    27,   942,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    27,    27,    27,   942,   942,
   nil,   nil,   nil,   942,   nil,   nil,    27,    27,   nil,    27,
    27,    27,    27,    27,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    27,    27,    27,    27,
    27,    27,    27,    27,    27,    27,    27,    27,    27,    27,
   nil,   nil,    27,    27,    27,   nil,   nil,    27,   nil,    27,
    27,   nil,   nil,    27,    27,   nil,    27,   nil,    27,   nil,
    27,   nil,    27,    27,   nil,    27,    27,    27,    27,    27,
    28,    27,    27,    27,   nil,   nil,   nil,    28,    28,    28,
   nil,   nil,    28,    28,    28,   nil,    28,    27,   nil,   nil,
    27,    27,   nil,    27,   nil,    27,    28,    28,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    28,    28,   nil,    28,
    28,    28,    28,    28,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    28,    28,    28,    28,
    28,    28,    28,    28,    28,    28,    28,    28,    28,    28,
   nil,   nil,    28,    28,    28,   nil,   nil,    28,   nil,    28,
    28,   nil,   nil,    28,    28,   nil,    28,   nil,    28,   nil,
    28,   nil,    28,    28,   nil,    28,    28,    28,    28,    28,
   nil,    28,   416,    28,   nil,   nil,   nil,   nil,   nil,   416,
   416,   416,   nil,   nil,   416,   416,   416,    28,   416,   nil,
    28,    28,   nil,    28,   nil,    28,   nil,   416,   416,   416,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   416,   416,
   nil,   416,   416,   416,   416,   416,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   416,   416,
   416,   416,   416,   416,   416,   416,   416,   416,   416,   416,
   416,   416,   nil,   nil,   416,   416,   416,   nil,   nil,   416,
   nil,   416,   416,   nil,   nil,   416,   416,   nil,   416,   nil,
   416,   nil,   416,   nil,   416,   416,   nil,   416,   416,   416,
   416,   416,   nil,   416,   416,   416,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   416,
   nil,   475,   416,   416,   nil,   416,   nil,   416,   475,   475,
   475,   nil,   nil,   475,   475,   475,   616,   475,   616,   616,
   616,   616,   616,   nil,   nil,   nil,   475,   475,   nil,   nil,
   nil,   616,   nil,   nil,   nil,   nil,   nil,   475,   475,   nil,
   475,   475,   475,   475,   475,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   616,   nil,   535,   nil,   535,   535,   535,
   535,   535,   616,   616,   616,   616,   nil,   nil,   nil,   616,
   535,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   475,   nil,
   nil,   nil,   nil,   nil,   nil,   475,   nil,   nil,   nil,   nil,
   475,   475,   535,   535,   nil,   616,   nil,   nil,   nil,   nil,
   nil,   535,   535,   535,   535,   nil,   nil,   nil,   535,   nil,
   nil,   nil,   nil,   475,   475,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   475,   nil,
   nil,   475,   nil,   nil,   nil,   nil,   475,     8,     8,     8,
     8,     8,     8,     8,     8,     8,     8,     8,     8,     8,
     8,     8,     8,     8,     8,     8,     8,     8,     8,     8,
     8,   nil,   nil,   nil,     8,     8,     8,     8,     8,     8,
     8,     8,     8,     8,   nil,   nil,   nil,   nil,   nil,     8,
     8,     8,     8,     8,     8,     8,     8,     8,     8,   nil,
     8,   nil,   nil,   nil,   nil,   nil,   nil,   nil,     8,     8,
   nil,     8,     8,     8,     8,     8,     8,     8,   nil,   nil,
     8,     8,   nil,   nil,   nil,     8,     8,     8,     8,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,     8,     8,   nil,     8,     8,     8,     8,     8,
     8,     8,     8,     8,     8,     8,     8,   nil,   nil,     8,
     8,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,     8,     9,     9,     9,     9,     9,
     9,     9,     9,     9,     9,     9,     9,     9,     9,     9,
     9,     9,     9,     9,     9,     9,     9,     9,     9,   nil,
   nil,   nil,     9,     9,     9,     9,     9,     9,     9,     9,
     9,     9,   nil,   nil,   nil,   nil,   nil,     9,     9,     9,
     9,     9,     9,     9,     9,     9,   nil,   nil,     9,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,     9,     9,   nil,     9,
     9,     9,     9,     9,     9,     9,   nil,   nil,     9,     9,
   nil,   nil,   nil,     9,     9,     9,     9,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
     9,     9,   nil,     9,     9,     9,     9,     9,     9,     9,
     9,     9,     9,     9,     9,   nil,   nil,     9,     9,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,     9,   396,   396,   396,   396,   396,   396,   396,
   396,   396,   396,   396,   396,   396,   396,   396,   396,   396,
   396,   396,   396,   396,   396,   396,   396,   nil,   nil,   nil,
   396,   396,   396,   396,   396,   396,   396,   396,   396,   396,
   nil,   nil,   nil,   nil,   nil,   396,   396,   396,   396,   396,
   396,   396,   396,   396,   nil,   nil,   396,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   396,   396,   nil,   396,   396,   396,
   396,   396,   396,   396,   nil,   nil,   396,   396,   nil,   nil,
   nil,   396,   396,   396,   396,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   396,   396,
   nil,   396,   396,   396,   396,   396,   396,   396,   396,   396,
   396,   396,   396,   nil,   nil,   396,   396,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   396,   586,   586,   586,   586,   586,   586,   586,   586,   586,
   586,   586,   586,   586,   586,   586,   586,   586,   586,   586,
   586,   586,   586,   586,   586,   nil,   nil,   nil,   586,   586,
   586,   586,   586,   586,   586,   586,   586,   586,   nil,   nil,
   nil,   nil,   nil,   586,   586,   586,   586,   586,   586,   586,
   586,   586,   nil,   nil,   586,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   586,   586,   nil,   586,   586,   586,   586,   586,
   586,   586,   nil,   nil,   586,   586,   nil,   nil,   nil,   586,
   586,   586,   586,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   586,   586,   nil,   586,
   586,   586,   586,   586,   586,   586,   586,   586,   586,   586,
   586,   nil,   nil,   586,   586,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   586,    71,
    71,    71,    71,    71,    71,    71,    71,    71,    71,    71,
    71,    71,    71,    71,    71,    71,    71,    71,    71,    71,
    71,    71,    71,   nil,   nil,   nil,    71,    71,    71,    71,
    71,    71,    71,    71,    71,    71,   nil,   nil,   nil,   nil,
   nil,    71,    71,    71,    71,    71,    71,    71,    71,    71,
    71,    71,    71,   nil,    71,   nil,   nil,   nil,   nil,   nil,
    71,    71,   nil,    71,    71,    71,    71,    71,    71,    71,
   nil,   nil,    71,    71,   nil,   nil,   nil,    71,    71,    71,
    71,   nil,   nil,   nil,   nil,   nil,    71,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    71,    71,   nil,    71,    71,    71,
    71,    71,    71,    71,    71,    71,    71,    71,    71,   nil,
   nil,    71,   718,   718,   718,   718,   718,   718,   718,   718,
   718,   718,   718,   718,   718,   718,   718,   718,   718,   718,
   718,   718,   718,   718,   718,   718,   nil,   nil,   nil,   718,
   718,   718,   718,   718,   718,   718,   718,   718,   718,   nil,
   nil,   nil,   nil,   nil,   718,   718,   718,   718,   718,   718,
   718,   718,   718,   nil,   nil,   718,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   718,   718,   nil,   718,   718,   718,   718,
   718,   718,   718,   nil,   nil,   718,   718,   nil,   nil,   nil,
   718,   718,   718,   718,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   718,   718,   nil,
   718,   718,   718,   718,   718,   718,   718,   718,   718,   718,
   718,   718,   212,   212,   718,   nil,   212,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   212,   212,   nil,   212,   212,   212,
   212,   212,   212,   212,   nil,   nil,   212,   212,   nil,   nil,
   nil,   212,   212,   212,   212,   nil,   nil,   nil,   nil,   nil,
   212,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   212,   212,
   nil,   212,   212,   212,   212,   212,   212,   212,   212,   212,
   212,   212,   212,   213,   213,   212,   nil,   213,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   213,   213,   nil,   213,   213,
   213,   213,   213,   213,   213,   nil,   nil,   213,   213,   nil,
   nil,   nil,   213,   213,   213,   213,   nil,   nil,   nil,   nil,
   nil,   213,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   213,
   213,   nil,   213,   213,   213,   213,   213,   213,   213,   213,
   213,   213,   213,   213,   261,   261,   213,   nil,   261,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   261,   261,   nil,   261,
   261,   261,   261,   261,   261,   261,   nil,   nil,   261,   261,
   nil,   nil,   nil,   261,   261,   261,   261,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   261,   261,   nil,   261,   261,   261,   261,   261,   261,   261,
   261,   261,   261,   261,   261,   441,   441,   261,   nil,   441,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   441,   441,   nil,
   441,   441,   441,   441,   441,   441,   441,   nil,   nil,   441,
   441,   nil,   nil,   nil,   441,   441,   441,   441,   nil,   nil,
   nil,   nil,   nil,   441,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   441,   441,   nil,   441,   441,   441,   441,   441,   441,
   441,   441,   441,   441,   441,   441,   442,   442,   441,   nil,
   442,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   442,   442,
   nil,   442,   442,   442,   442,   442,   442,   442,   nil,   nil,
   442,   442,   nil,   nil,   nil,   442,   442,   442,   442,   nil,
   nil,   nil,   nil,   nil,   442,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   442,   442,   nil,   442,   442,   442,   442,   442,
   442,   442,   442,   442,   442,   442,   442,   508,   508,   442,
   nil,   508,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   508,
   508,   nil,   508,   508,   508,   508,   508,   508,   508,   nil,
   nil,   508,   508,   nil,   nil,   nil,   508,   508,   508,   508,
   nil,   nil,   nil,   nil,   nil,   508,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   508,   508,   nil,   508,   508,   508,   508,
   508,   508,   508,   508,   508,   508,   508,   508,   509,   509,
   508,   nil,   509,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   509,   509,   nil,   509,   509,   509,   509,   509,   509,   509,
   nil,   nil,   509,   509,   nil,   nil,   nil,   509,   509,   509,
   509,   nil,   nil,   nil,   nil,   nil,   509,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   509,   509,   nil,   509,   509,   509,
   509,   509,   509,   509,   509,   509,   509,   509,   509,   518,
   518,   509,   nil,   518,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   518,   518,   nil,   518,   518,   518,   518,   518,   518,
   518,   nil,   nil,   518,   518,   nil,   nil,   nil,   518,   518,
   518,   518,   nil,   nil,   nil,   nil,   nil,   518,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   518,   518,   nil,   518,   518,
   518,   518,   518,   518,   518,   518,   518,   518,   518,   518,
   519,   519,   518,   nil,   519,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   519,   519,   nil,   519,   519,   519,   519,   519,
   519,   519,   nil,   nil,   519,   519,   nil,   nil,   nil,   519,
   519,   519,   519,   nil,   nil,   nil,   nil,   nil,   519,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   519,   519,   nil,   519,
   519,   519,   519,   519,   519,   519,   519,   519,   519,   519,
   519,   546,   546,   519,   nil,   546,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   546,   546,   nil,   546,   546,   546,   546,
   546,   546,   546,   nil,   nil,   546,   546,   nil,   nil,   nil,
   546,   546,   546,   546,   nil,   nil,   nil,   nil,   nil,   546,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   546,   546,   nil,
   546,   546,   546,   546,   546,   546,   546,   546,   546,   546,
   546,   546,   547,   547,   546,   nil,   547,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   547,   547,   nil,   547,   547,   547,
   547,   547,   547,   547,   nil,   nil,   547,   547,   nil,   nil,
   nil,   547,   547,   547,   547,   nil,   nil,   nil,   nil,   nil,
   547,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   547,   547,
   nil,   547,   547,   547,   547,   547,   547,   547,   547,   547,
   547,   547,   547,   553,   553,   547,   nil,   553,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   553,   553,   nil,   553,   553,
   553,   553,   553,   553,   553,   nil,   nil,   553,   553,   nil,
   nil,   nil,   553,   553,   553,   553,   nil,   nil,   nil,   nil,
   nil,   553,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   553,
   553,   nil,   553,   553,   553,   553,   553,   553,   553,   553,
   553,   553,   553,   553,   554,   554,   553,   nil,   554,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   554,   554,   nil,   554,
   554,   554,   554,   554,   554,   554,   nil,   nil,   554,   554,
   nil,   nil,   nil,   554,   554,   554,   554,   nil,   nil,   nil,
   nil,   nil,   554,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   554,   554,   nil,   554,   554,   554,   554,   554,   554,   554,
   554,   554,   554,   554,   554,   923,   923,   554,   nil,   923,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   923,   923,   nil,
   923,   923,   923,   923,   923,   923,   923,   nil,   nil,   923,
   923,   nil,   nil,   nil,   923,   923,   923,   923,   nil,   nil,
   nil,   nil,   nil,   923,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   923,   923,   nil,   923,   923,   923,   923,   923,   923,
   923,   923,   923,   923,   923,   923,   977,   977,   923,   nil,
   977,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   977,   977,
   nil,   977,   977,   977,   977,   977,   977,   977,   nil,   nil,
   977,   977,   nil,   nil,   nil,   977,   977,   977,   977,   nil,
   nil,   nil,   nil,   nil,   977,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   977,   977,   nil,   977,   977,   977,   977,   977,
   977,   977,   977,   977,   977,   977,   977,   978,   978,   977,
   nil,   978,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   978,
   978,   nil,   978,   978,   978,   978,   978,   978,   978,   nil,
   nil,   978,   978,   nil,   nil,   nil,   978,   978,   978,   978,
   nil,   nil,   nil,   nil,   nil,   978,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   978,   978,   nil,   978,   978,   978,   978,
   978,   978,   978,   978,   978,   978,   978,   978,   nil,   658,
   978,   658,   658,   658,   658,   658,   nil,   716,   nil,   716,
   716,   716,   716,   716,   658,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   716,   nil,   717,   nil,   717,   717,   717,   717,
   717,   nil,   nil,   nil,   nil,   nil,   658,   nil,   nil,   717,
   nil,   nil,   nil,   nil,   716,   658,   658,   658,   658,   nil,
   nil,   nil,   658,   716,   716,   716,   716,   nil,   nil,   nil,
   716,   717,   nil,   799,   nil,   799,   799,   799,   799,   799,
   717,   717,   717,   717,   nil,   nil,   nil,   717,   799,   nil,
   801,   nil,   801,   801,   801,   801,   801,   nil,   846,   nil,
   846,   846,   846,   846,   846,   801,   nil,   nil,   nil,   nil,
   799,   nil,   nil,   846,   nil,   nil,   nil,   nil,   nil,   799,
   799,   799,   799,   nil,   nil,   nil,   799,   801,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   846,   801,   801,   801,   801,
   nil,   nil,   nil,   801,   846,   846,   846,   846,   nil,   nil,
   848,   846,   848,   848,   848,   848,   848,   nil,   938,   nil,
   938,   938,   938,   938,   938,   848,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   938,   nil,   944,   nil,   944,   944,   944,
   944,   944,   nil,   nil,   nil,   nil,   nil,   848,   nil,   nil,
   944,   nil,   nil,   nil,   nil,   938,   848,   848,   848,   848,
   nil,   nil,   nil,   848,   938,   938,   938,   938,   nil,   nil,
   nil,   938,   944,   nil,   962,   nil,   962,   962,   962,   962,
   962,   nil,   nil,   944,   944,   nil,   nil,   nil,   944,   962,
   nil,   964,   nil,   964,   964,   964,   964,   964,   966,   nil,
   966,   966,   966,   966,   966,   nil,   964,   nil,   nil,   nil,
   nil,   962,   nil,   966,   nil,   nil,   nil,   nil,   nil,   nil,
   962,   962,   962,   962,   nil,   nil,   nil,   962,   964,   nil,
   nil,   nil,   nil,   nil,   nil,   966,   nil,   nil,   nil,   964,
   964,   nil,   nil,   nil,   964,   nil,   966,   966,   nil,   nil,
   968,   966,   968,   968,   968,   968,   968,  1006,   nil,  1006,
  1006,  1006,  1006,  1006,  1016,   968,  1016,  1016,  1016,  1016,
  1016,   nil,  1006,   nil,   nil,   nil,   nil,   nil,   nil,  1016,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   968,   nil,   nil,
   nil,   nil,   nil,   nil,  1006,   nil,   nil,   nil,   968,   968,
   nil,  1016,   nil,   968,   nil,  1006,  1006,   nil,   nil,   nil,
  1006,   nil,  1016,  1016,   nil,   nil,   nil,  1016 ]

racc_action_pointer = [
   744,     1,   nil,   303,   nil,  5083,   729,   -91, 23075, 23203,
   -80,   nil,   -62,   107,   470,   184,    61,    10,   nil,   -79,
  5214,  1164,   182,   nil,    15,   nil,    -8, 22590, 22700,  5345,
  5476,  5607,   nil,   884,  5738,  5869,   nil,    80,   190,   227,
   169,   283,  6008,  6139,  6270,   141,   556,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil, 22188,   nil,   -75,  6401,
  6532,   -23,   nil,  6663,  6794,   nil,   nil,  6925,  7064,  7195,
  7326, 23587,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   518,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,     0,   nil,   nil,
   112,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   277,   nil,  7465,   nil,   nil,   nil,   nil,  7604,  7735,
  7866,  7997,  8136,  1024,   nil,   472,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   153,   nil,  1164,  8267,
  8398,  8529, 23761, 23822,  8660,  8791,  8922,  9053,  9184,  9315,
   nil,   nil,   610,   -24,    15,   301,    90,   224,   293,   nil,
  9446,  1304,   294,  9577,  9708,  9839,  9970, 10101, 10232, 10363,
 10494, 10625, 10756, 10887, 11018, 11149, 11280, 11411, 11542, 11673,
 11804, 11935, 12066, 12197, 12328, 12459, 12590, 12721, 12852,   nil,
   nil, 23883,   nil,   nil,   295, 12983, 13114,   nil,   nil,   nil,
   nil,   nil,   nil,   nil, 13245,   nil,  1304,   nil,   264,   266,
   nil, 13376,   328, 13507,   nil,   nil, 13638, 13769,   nil,   nil,
   380,   nil, 13908,  1429,   313,   308,  1444,   347,   397,   367,
 14039,  1584,   613,   659,   744,   449,   747,   nil,   440,   431,
    33,   nil,   nil,   nil,   481,   331,   443, 14178,   nil,   302,
   514,   786,   nil,   553,   nil, 14309,  1724, 14440,   495,   nil,
   107,   127,   537,   522,   206,   561,   nil,   nil,    24,    -1,
    26, 14571, 14702,   416,   636,   526,   -19,   -18,   816,   627,
    10,   667,   nil,   nil,   191,   225,     8,   nil,   884,   nil,
    34, 14833,   nil,   nil,   nil,   150,   274,   412,   442,   570,
   576,   581,   792,   794,   nil,   797,   nil, 14964,   nil,   330,
   388,   397,   457,   460,   -41,   -35,   464,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   586, 23331,   nil,   nil,   nil,
   nil,   588,   nil,   nil,   579, 15095,   591,   nil,   nil,   884,
   598,   nil,   597,   605,   425,   471, 22812,   nil,   nil,   nil,
   222,   334,   651,   nil,   nil, 22322, 22456,   nil,  1444,   nil,
   599,   nil,   nil,   744,   nil,   nil,   nil,   nil,   -33,   nil,
   652, 23944, 24005, 15226,   252, 15357, 15488, 15619,  3264,  3404,
   534,   561,   691,   717,   734,   738,  5214,  5345,  5476,  3544,
  3684,  3824,  3964,  4104,  4244,  4384,  4524,  4664,  4804,   489,
  3094,  4944,  5083,  1584,   -56, 22941,   nil,   nil,   nil,   nil,
   703,   nil,   178,   237,   704,   nil,   nil, 15750,   nil, 15881,
   nil, 16012,   nil,   327,   nil,   nil,   nil, 16151,  1444,  1864,
   708,   710,   nil,   nil,   715, 16290,   726, 16421, 24066, 24127,
   887,   768,   nil, 16552,   725,   nil, 16683, 16814, 24188, 24249,
  1724, 16945,   861,   882,   422,   802,   nil, 17076,   nil,   nil,
 17207,   nil,   nil,   nil,   nil, 22944,  2004,   884,   nil,  2144,
    62,   118,   881,   889, 17338, 17469, 24310, 24371,    25,   nil,
   nil,   926,   nil, 24432, 24493, 17600,   nil,   nil,   nil,   532,
   237,  2284,   819,   nil,   -14,   nil,   nil,   nil,   727,   nil,
   nil,   nil,   791,   nil,   nil,   261,   nil,   338,   nil,   nil,
   777,   nil,   778,   nil,   nil,   nil, 23459,   nil,   787, 17731,
 17862,   479,   827, 17993, 18124, 18255, 18386,   827,   nil,   nil,
 18517, 18648,   828,   nil, 18779, 18910,   nil,   nil,   254,   277,
   466,   602,   795,  1024,   794,   nil, 22905,   nil,  2424,   913,
     5,   280,   nil,  2564,  2704,   nil,   811,   nil,   878, 19041,
   nil,   nil, 19172,   nil,   857,  -113, 19303,   840,   nil,   844,
   136,   177,   889,   337,  1024,   894,   855, 19434,  1864,   930,
    20,   983, 19565,   nil,   868,   nil,   235,   nil, 24738,   nil,
   875,   876,   nil,   878,   881,   883,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   875,   569,   nil,   nil, 19696,   nil,
   nil,   nil,   967,   nil,   nil,   nil,   970,   nil,   nil,   980,
   472,   nil,  1018,   nil,   nil,   nil,   nil,  1027,   nil,    32,
   907,    26,    40,   122,   183, 19827,   406,  1164,   nil,   908,
  2844, 19958,   nil,   nil,  1039,  2984, 24746, 24763, 23700,   nil,
   nil,   nil,   nil,   nil,   nil,  3124,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   917, 20089,  2004, 20220,   nil,   918,   nil,
  2144,   nil,  2284,   nil,   nil,  2424,   nil,  2564,   nil,  2704,
 20351, 20482, 20613, 20744,   342, 20875,   919,   927,   nil,   928,
   930,   931,   nil,   956,   936,   945,   942, 21006,   nil,   nil,
  1096,   nil,   nil,  3264,   997,  1105,   nil,   nil,   nil,   nil,
   985,   404,   nil,   nil,  1115,   nil,  3404,   995,  1049,   nil,
   nil,  1047,   nil,    37,  1008,   551,   nil,   nil,   428, 24802,
   nil, 24819,   nil,  5916,   nil, 21137,   nil,   663,   nil,  1015,
   228,  1019,   nil,   nil,   nil,   nil,  1142,   nil, 21268,  1145,
  3544,  3684,   nil, 21399,  3824,    71,   121,   nil,  1147,   623,
  3964,   nil,  1148,  1028,   695,   nil,  1034,  1038,   nil,  2844,
 21530, 21661,  2984,   627,   nil,   nil, 24827,   nil, 24879,   nil,
  7373,   nil,   nil,  1063,  1150, 21792,   956,  1121,   nil,  1064,
   nil,   nil,   nil,  4104,   nil,   nil,    33, 21923,   nil,   nil,
   nil,   nil,   nil,  1088,  1055,   nil,   nil,   nil,  1057,  1058,
   nil,  1059,  1067,   nil,  1073,   nil,   nil,  1090,  3110,  1091,
   668,   nil,   nil,    36,   nil,  1236,  1241,   nil,    17,   nil,
   nil,   nil,  1245,   nil,   nil,   nil,  1169,   nil,  1130,   nil,
   nil,  1135,  1143,  1144,  1145,   nil,  1155,   nil,   368,   nil,
   nil,   nil,  1027, 24554,   nil,   nil,   nil,  4244,  1066,  1096,
  1167,  1234,  1206,   nil,  4384,  4524,   nil,   nil, 24887,   nil,
 14086,   nil, 22519,   nil, 24904,   nil,   nil,   nil,   nil,   327,
  3169,  1158,  4664,   nil,   nil,   nil,   nil,   nil,  4804,   nil,
  4944,   nil, 24943,   nil, 24960,   nil, 24967,   nil, 25019,   nil,
   nil,   nil,  1290,  1205,  1207,  1290, 22054, 24615, 24676,  1236,
  1185,  1292,  1178,  1179,  1183,  1184,  1185,  3344,  1195,  3484,
   592,  1321,  1322,  1199,  1207,  1213,  1227,   nil,   nil,  1234,
    40,    42,   111,  1304,   nil,   nil, 25026,   nil,   nil,   nil,
   nil,  3530,  1251,   nil,   nil,   nil, 25033,   nil,   nil,   nil,
   nil,    98,  1252,  1257,  1258,   nil,   nil ]

racc_action_default = [
    -3,  -600,    -1,  -586,    -4,  -600,    -7,  -600,  -600,  -600,
  -600,   -29,  -600,  -600,  -600,  -279,  -600,   -40,   -43,  -588,
  -600,   -48,   -50,   -51,   -52,   -56,  -256,  -256,  -256,  -293,
  -329,  -330,   -68,   -11,   -72,   -80,   -82,  -600,  -491,  -492,
  -600,  -600,  -600,  -600,  -600,  -588,  -237,  -270,  -271,  -272,
  -273,  -274,  -275,  -276,  -277,  -278,  -576,  -281,  -283,  -599,
  -566,  -301,  -303,  -600,  -600,  -307,  -310,  -586,  -600,  -600,
  -600,  -600,  -331,  -332,  -334,  -335,  -432,  -433,  -434,  -435,
  -436,  -457,  -439,  -440,  -459,  -461,  -444,  -449,  -453,  -455,
  -471,  -459,  -473,  -475,  -476,  -477,  -478,  -574,  -480,  -481,
  -575,  -483,  -484,  -485,  -486,  -487,  -488,  -489,  -490,  -495,
  -496,  -600,    -2,  -587,  -595,  -596,  -597,    -6,  -600,  -600,
  -600,  -600,  -600,    -3,   -17,  -600,  -111,  -112,  -113,  -114,
  -115,  -116,  -117,  -118,  -119,  -123,  -124,  -125,  -126,  -127,
  -128,  -129,  -130,  -131,  -132,  -133,  -134,  -135,  -136,  -137,
  -138,  -139,  -140,  -141,  -142,  -143,  -144,  -145,  -146,  -147,
  -148,  -149,  -150,  -151,  -152,  -153,  -154,  -155,  -156,  -157,
  -158,  -159,  -160,  -161,  -162,  -163,  -164,  -165,  -166,  -167,
  -168,  -169,  -170,  -171,  -172,  -173,  -174,  -175,  -176,  -177,
  -178,  -179,  -180,  -181,  -182,  -183,  -184,  -185,  -186,  -187,
  -188,  -189,  -190,  -191,  -192,  -193,   -22,  -120,   -11,  -600,
  -600,  -246,  -600,  -600,  -600,  -600,  -600,  -600,  -600,  -588,
  -589,   -47,  -600,  -491,  -492,  -600,  -279,  -600,  -600,  -229,
  -600,   -11,  -600,  -600,  -600,  -600,  -600,  -600,  -600,  -600,
  -600,  -600,  -600,  -600,  -600,  -600,  -600,  -600,  -600,  -600,
  -600,  -600,  -600,  -600,  -600,  -600,  -600,  -600,  -600,  -401,
  -403,  -600,  -584,  -585,   -57,  -246,  -600,  -300,  -407,  -416,
  -418,   -63,  -413,   -64,  -588,   -65,  -238,  -251,  -260,  -260,
  -255,  -600,  -261,  -600,  -457,  -568,  -600,  -600,   -66,   -67,
  -586,   -12,  -600,   -15,  -600,   -70,   -11,  -588,  -600,   -73,
   -76,   -11,   -88,   -89,  -600,  -600,   -96,  -293,  -296,  -588,
  -600,  -329,  -330,  -333,  -414,  -600,   -78,  -600,   -84,  -290,
  -474,  -600,  -214,  -215,  -230,  -600,   -11,  -600,  -588,  -239,
  -592,  -592,  -600,  -600,  -592,  -600,  -302,  -392,   -49,  -600,
  -600,  -600,  -600,  -586,  -600,  -587,  -491,  -492,  -600,  -600,
  -279,  -600,  -345,  -346,  -106,  -107,  -600,  -109,  -600,  -279,
  -600,  -600,  -491,  -492,  -322,  -111,  -112,  -153,  -154,  -155,
  -171,  -176,  -183,  -186,  -324,  -600,  -564,  -600,  -437,  -600,
  -600,  -600,  -600,  -600,  -600,  -600,  -600,  1027,    -5,  -598,
   -23,   -24,   -25,   -26,   -27,  -600,  -600,   -19,   -20,   -21,
  -121,  -600,   -30,   -39,  -266,  -600,  -600,  -265,   -31,  -196,
  -588,  -247,  -260,  -260,  -577,  -578,  -256,  -411,  -579,  -580,
  -578,  -577,  -256,  -410,  -412,  -579,  -580,   -37,  -204,   -38,
  -600,   -41,   -42,  -194,  -261,   -44,   -45,   -46,  -588,  -299,
  -600,  -600,  -600,  -246,  -290,  -600,  -600,  -600,  -205,  -206,
  -207,  -208,  -209,  -210,  -211,  -212,  -216,  -217,  -218,  -219,
  -220,  -221,  -222,  -223,  -224,  -225,  -226,  -227,  -228,  -231,
  -232,  -233,  -234,  -588,  -381,  -256,  -577,  -578,   -54,   -58,
  -588,  -257,  -381,  -381,  -588,  -295,  -252,  -600,  -253,  -600,
  -258,  -600,  -262,  -600,  -571,  -573,   -10,  -587,   -14,    -3,
  -588,   -69,  -288,   -85,   -74,  -600,  -588,  -246,  -600,  -600,
   -95,  -600,  -474,  -600,   -81,   -86,  -600,  -600,  -600,  -600,
  -235,  -600,  -424,  -600,  -284,  -600,  -240,  -594,  -593,  -242,
  -594,  -291,  -292,  -567,  -304,  -524,   -11,  -336,  -337,   -11,
  -600,  -600,  -600,  -600,  -600,  -246,  -600,  -600,  -290,  -315,
  -106,  -107,  -108,  -600,  -600,  -246,  -318,  -497,  -498,  -600,
  -600,   -11,  -502,  -326,  -588,  -438,  -458,  -463,  -600,  -465,
  -441,  -460,  -600,  -462,  -443,  -600,  -446,  -600,  -448,  -451,
  -600,  -452,  -600,  -472,    -8,   -18,  -600,   -28,  -269,  -600,
  -600,  -415,  -600,  -248,  -250,  -600,  -600,   -59,  -245,  -408,
  -600,  -600,   -61,  -409,  -600,  -600,  -298,  -590,  -577,  -578,
  -577,  -578,  -588,  -194,  -600,  -382,  -588,  -384,   -11,   -53,
  -404,  -381,  -243,   -11,   -11,  -294,  -260,  -259,  -263,  -600,
  -569,  -570,  -600,   -13,  -600,   -71,  -600,   -77,   -83,  -588,
  -577,  -578,  -244,   -92,   -94,  -600,   -79,  -600,  -203,  -213,
  -588,  -599,  -599,  -282,  -588,  -287,  -592,  -393,  -524,  -396,
  -563,  -563,  -507,  -509,  -509,  -509,  -523,  -525,  -526,  -527,
  -528,  -529,  -530,  -531,  -532,  -600,  -534,  -536,  -538,  -543,
  -545,  -546,  -548,  -553,  -555,  -556,  -558,  -559,  -560,  -600,
  -599,  -338,  -599,  -308,  -339,  -340,  -311,  -600,  -314,  -600,
  -588,  -577,  -578,  -581,  -289,  -600,  -106,  -107,  -110,  -588,
   -11,  -600,  -500,  -320,  -600,   -11,  -524,  -524,  -600,  -565,
  -464,  -467,  -468,  -469,  -470,   -11,  -442,  -445,  -447,  -450,
  -454,  -456,  -122,  -267,  -600,  -197,  -600,  -591,  -260,   -33,
  -199,   -34,  -200,   -60,   -35,  -202,   -36,  -201,   -62,  -195,
  -600,  -600,  -600,  -600,  -415,  -600,  -563,  -563,  -363,  -365,
  -365,  -365,  -380,  -600,  -588,  -386,  -532,  -540,  -541,  -551,
  -600,  -406,  -405,   -11,  -600,  -600,  -254,  -264,  -572,   -16,
   -75,   -90,   -87,  -297,  -599,  -343,   -11,  -425,  -599,  -426,
  -427,  -600,  -241,  -600,  -588,  -600,  -505,  -506,  -600,  -600,
  -516,  -600,  -519,  -600,  -521,  -600,  -347,  -600,  -349,  -351,
  -358,  -588,  -537,  -547,  -557,  -561,  -600,  -341,  -600,  -600,
   -11,   -11,  -313,  -600,   -11,  -415,  -600,  -415,  -600,  -600,
   -11,  -323,  -600,  -588,  -600,  -327,  -600,  -268,   -32,  -198,
  -249,  -600,  -236,  -600,  -361,  -362,  -371,  -373,  -600,  -376,
  -600,  -378,  -383,  -600,  -600,  -600,  -539,  -600,  -402,  -600,
  -417,  -419,    -9,   -11,  -431,  -344,  -600,  -600,  -429,  -285,
  -394,  -397,  -399,  -600,  -563,  -544,  -562,  -508,  -509,  -509,
  -535,  -509,  -509,  -554,  -509,  -532,  -549,  -588,  -600,  -356,
  -600,  -533,  -305,  -600,  -306,  -600,  -600,  -263,  -599,  -316,
  -319,  -499,  -600,  -325,  -501,  -503,  -502,  -466,  -563,  -542,
  -364,  -365,  -365,  -365,  -365,  -552,  -365,  -385,  -588,  -388,
  -390,  -391,  -550,  -600,  -290,   -55,  -430,   -11,   -97,   -98,
  -600,  -600,  -105,  -428,   -11,   -11,  -395,  -504,  -600,  -512,
  -600,  -514,  -600,  -517,  -600,  -520,  -522,  -348,  -350,  -354,
  -600,  -359,   -11,  -309,  -312,  -420,  -421,  -422,   -11,  -321,
   -11,  -360,  -600,  -368,  -600,  -370,  -600,  -374,  -600,  -377,
  -379,  -387,  -600,  -289,  -581,  -424,  -246,  -600,  -600,  -104,
  -600,  -600,  -509,  -509,  -509,  -509,  -352,  -600,  -357,  -600,
  -599,  -600,  -600,  -365,  -365,  -365,  -365,  -389,  -423,  -588,
  -577,  -578,  -581,  -103,  -398,  -400,  -600,  -510,  -513,  -515,
  -518,  -600,  -355,  -342,  -317,  -328,  -600,  -366,  -369,  -372,
  -375,  -415,  -509,  -353,  -365,  -511,  -367 ]

racc_goto_table = [
   218,   329,   374,    14,   277,   277,   277,   543,    14,   313,
   313,   260,   336,   523,     2,   410,   416,   422,   488,   536,
   539,   261,   715,   222,   407,   651,   325,   339,   340,   379,
   429,   343,   222,   222,   222,   479,    14,   304,   304,   129,
   129,   124,   207,   313,   313,   313,   132,   132,   432,   268,
   272,   278,   278,   278,   627,   816,   627,   806,   295,   113,
   297,   221,   134,   134,   299,   475,   222,   222,   630,   480,
   222,   348,   358,   358,   320,   439,   264,   271,   273,   552,
   760,   330,   390,   391,   392,   393,     6,   948,   316,   117,
   112,     6,   784,   883,     1,   116,   591,   279,   279,   279,
   659,   811,   129,   862,   693,   696,   275,   288,   289,   630,
   338,   338,   380,   763,   338,   915,    14,   360,   364,   386,
   919,   222,   222,   222,   222,    14,    14,   331,   334,   633,
   485,   526,   529,   819,   921,   533,   206,   395,   880,   514,
   618,   575,   577,   353,   403,   396,   796,   797,   623,   624,
   621,   586,   488,   501,   344,   332,   620,   376,   333,   116,
   627,   627,   534,   351,   556,   338,   338,   338,   338,   375,
   326,   327,   654,   630,   328,   337,   951,   341,   820,   342,
   821,   705,   958,   710,   830,   880,   277,   561,   948,   806,
   562,   718,   889,   906,   762,    26,   764,   388,   918,     6,
    26,   571,   573,   576,   576,   697,   657,   571,   394,     6,
   870,    14,   222,   222,   222,    26,   535,   222,   222,   222,
   222,   222,   222,   794,    26,    26,    26,   793,    26,   915,
   883,   406,   493,   887,    14,   417,   988,   406,   997,   934,
   277,   277,   844,   845,   426,   416,   422,   612,   714,   277,
   402,   408,   921,   935,   474,   427,   431,   482,    26,    26,
   666,   853,    26,   435,   436,   437,   438,   483,   222,   222,
   806,   955,   806,  1012,   788,   313,   708,   222,   866,   378,
   381,   833,   834,   880,   382,   383,   384,   773,   278,   385,
   511,   873,   313,   720,   606,    14,   278,  1023,   754,    14,
   725,   711,   878,   304,    14,   540,   541,   880,    26,   525,
   911,   639,   642,    26,    26,    26,    26,    26,    26,   875,
   304,   295,   642,   500,   909,   781,   295,   637,   506,    14,
   222,   986,   806,   268,   279,   646,   622,   272,   nil,   956,
   625,   nil,   279,   481,   222,   222,   497,   515,   512,   700,
   642,   484,    13,   504,   nil,  1013,   635,    13,   642,   709,
   937,   nil,   638,   nil,   222,   776,   960,   nil,   743,   806,
   nil,   806,   563,   748,   524,   nil,   nil,   496,   498,   824,
   222,   nil,   116,   666,   nil,    13,   825,   nil,   338,   338,
   899,   592,   nil,   806,   961,   827,   728,   828,   728,   nil,
   nil,   nil,   832,    26,    26,    26,    26,   627,   560,    26,
    26,    26,    26,    26,    26,   nil,   nil,   nil,   277,   630,
   719,   nil,   nil,   nil,   564,   nil,    26,   129,   nil,   585,
   542,   nil,   nil,   nil,   132,   116,   nil,   432,   nil,   598,
   nil,   666,   666,   nil,   nil,   603,   222,   nil,   nil,   nil,
   134,   nil,   557,   nil,   614,   nil,   nil,   792,   780,   298,
    26,    26,   nil,   nil,   417,    13,   597,   789,   nil,    26,
   nil,   761,   602,   426,    13,    13,   nil,   776,   998,   nil,
   313,   nil,   277,   nil,   nil,   nil,   nil,    26,   313,   nil,
   nil,    26,   nil,   nil,   nil,   nil,    26,   nil,   598,   nil,
    14,   nil,    14,   nil,   nil,   nil,   783,   nil,   304,   nil,
   222,   nil,   nil,   634,   nil,   nil,   304,   902,   nil,   nil,
   277,    26,    26,   nil,   222,   619,   nil,   nil,   nil,   nil,
   277,   417,   757,   nil,   nil,   515,    26,    26,   nil,    14,
   426,   417,    14,   515,   nil,   nil,   927,   nil,   222,   nil,
   426,   nil,   nil,   nil,   nil,   626,    26,   nil,   222,   nil,
    13,   957,   nil,   nil,    14,   nil,   699,   nil,   650,   417,
   nil,   772,    26,   952,   nil,   nil,   nil,   417,   426,   nil,
   nil,   nil,   nil,    13,   426,     6,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   592,   790,   656,   nil,   765,   222,   222,
   nil,   nil,   nil,   222,   222,   nil,   nil,   222,   771,   nil,
   nil,   313,   nil,   nil,   nil,   nil,   nil,   129,   nil,   732,
   592,    14,   313,   nil,   132,   nil,    14,    14,   nil,   nil,
   nil,   nil,   694,   694,   401,   791,   739,   741,    26,   304,
   134,   744,   746,   nil,    13,   431,   nil,   992,    13,   835,
   304,   712,   713,    13,   879,   nil,   881,   298,   nil,   nil,
   nil,   738,   nil,   nil,   nil,   598,   515,   891,   603,   826,
   nil,   nil,   nil,   nil,   406,   829,   nil,   782,    13,   nil,
   nil,   592,   674,   nil,   nil,  1021,   nil,   nil,   nil,   904,
   592,   nil,    26,   nil,    26,   nil,   nil,   nil,   nil,   nil,
   nil,   912,    26,   913,   nil,   nil,   nil,   nil,   222,   nil,
   nil,   874,   nil,    14,   222,   nil,    26,   nil,    14,   nil,
   nil,   nil,   298,   nil,   nil,   nil,   864,   298,    14,   nil,
   868,    26,   nil,   nil,    26,   nil,   nil,   nil,   nil,   222,
    26,   nil,   313,   947,   nil,   765,   nil,   nil,   nil,   129,
    26,   nil,   338,   nil,   nil,   nil,    26,   nil,   338,   908,
   nil,   nil,   757,   766,   757,   nil,   757,   nil,   nil,   767,
   856,   nil,   nil,   nil,   nil,   765,    14,   838,   nil,    16,
   999,   642,   893,   nil,    16,   nil,   nil,   nil,   nil,    14,
    26,    26,   nil,   982,   nil,    26,    26,   nil,   nil,    26,
   nil,   nil,   nil,   nil,   758,   674,    15,   nil,   nil,   nil,
   nil,    15,    16,    26,   nil,   nil,   nil,   993,    26,    26,
   759,   222,   nil,    14,    14,   nil,   nil,    14,   nil,   nil,
   313,   nil,   nil,    14,   nil,   nil,   nil,   nil,   nil,    15,
   306,   306,   313,   nil,   nil,   nil,   nil,   352,   nil,    13,
   nil,    13,   nil,   nil,   nil,   nil,   nil,   nil,   922,   nil,
   nil,   nil,   nil,   674,   674,   338,    14,   nil,   nil,   nil,
   930,   nil,   nil,   nil,   350,   359,   359,   nil,   757,   nil,
   757,   nil,   757,   nil,   757,   nil,   nil,   nil,    13,   nil,
   nil,    13,    16,   nil,   nil,   nil,   nil,   nil,   nil,   971,
    26,    16,    16,   nil,   nil,    26,    26,   nil,   nil,   nil,
    26,   nil,   nil,    13,   nil,   nil,   nil,   nil,   694,    15,
    26,   901,   nil,   nil,   nil,   nil,   905,   nil,    15,    15,
    14,    26,   757,   nil,   nil,   nil,   nil,    14,    14,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   674,   nil,   674,   nil,
   nil,   277,   nil,   nil,   426,    14,   nil,   nil,   nil,   nil,
   nil,    14,   690,    14,   nil,   692,   nil,   nil,    26,   nil,
    13,   nil,   nil,   nil,   nil,    13,    13,   nil,   nil,   222,
   592,    26,   nil,   876,   nil,   nil,   876,    16,   nil,   nil,
   335,   nil,   nil,   766,   430,   766,   767,   nil,   nil,   767,
   417,   767,   nil,   767,   nil,   882,   nil,   884,   nil,   426,
    16,   nil,   nil,    26,    15,    26,    26,   nil,   nil,    26,
   nil,   nil,   nil,   nil,   nil,    26,   847,   849,   851,   nil,
   nil,   876,   nil,   nil,   758,   nil,   758,    15,   758,   nil,
   nil,   nil,   nil,   nil,   770,   nil,   nil,   nil,   nil,   774,
   775,   nil,   914,   nil,   916,   nil,   nil,   nil,    26,   nil,
   nil,   nil,    13,   nil,   nil,   nil,   nil,    13,   nil,   nil,
   nil,    16,   nil,   nil,   nil,    16,   nil,    13,   nil,   nil,
    16,   nil,   nil,   nil,   nil,   674,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    15,   nil,
   nil,   nil,    15,   nil,   nil,    16,   306,    15,   nil,   766,
   nil,   nil,   800,   802,   804,   767,   nil,   767,   nil,   767,
   nil,   767,    26,   306,   nil,    13,    38,   nil,   nil,    26,
    26,    38,    15,   nil,   nil,   nil,   nil,   nil,    13,   404,
   nil,   nil,   983,   nil,   984,   434,   985,    26,   nil,   nil,
   758,   836,   758,    26,   758,    26,   758,   nil,    39,    38,
   302,   302,   nil,    39,   nil,   nil,   994,   nil,   995,   767,
   996,    26,    13,    13,   nil,   nil,    13,   nil,   963,   965,
   967,   969,    13,   970,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    39,   303,   303,   346,   362,   362,   362,   nil,   859,
   nil,   nil,   nil,   nil,   758,   nil,   nil,   nil,   nil,   nil,
  1022,   490,   865,   492,   nil,    13,   494,   495,   nil,   nil,
  1024,   nil,   nil,   nil,   nil,   nil,   347,   363,   363,   363,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   895,   896,    38,    38,
   898,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
  1017,  1018,  1019,  1020,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    39,   nil,   nil,   nil,   nil,    16,   nil,    16,    13,
    39,    39,   nil,   nil,   nil,   nil,    13,    13,   nil,   926,
   nil,  1026,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    15,    13,    15,   nil,   nil,   nil,   nil,
    13,   306,    13,   nil,   nil,    16,   nil,   nil,    16,   306,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   939,   941,   nil,
   943,   945,   nil,   946,    38,   588,   nil,   nil,   nil,   nil,
    16,   nil,    15,   nil,   nil,    15,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   975,   nil,   nil,   nil,    38,   nil,   nil,
   980,   981,   nil,   nil,   nil,   nil,    39,    15,   nil,   nil,
   nil,   nil,   nil,   nil,   724,   nil,   nil,   nil,   990,   nil,
   nil,   nil,   nil,   430,   991,   nil,   nil,   nil,   nil,    39,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    16,   nil,   nil,
   nil,   nil,    16,    16,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   628,    38,   335,
   nil,   631,    38,   nil,    15,   nil,   302,    38,   nil,    15,
    15,  1007,  1008,  1009,  1010,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   306,   302,   nil,   nil,   nil,   nil,   nil,   nil,
    39,   nil,    38,   306,    39,   nil,   nil,   628,   303,    39,
   335,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,  1025,   nil,   nil,   434,   303,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    39,   nil,   nil,   nil,   nil,    16,
   nil,   nil,   nil,   nil,    16,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    16,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    15,   nil,   nil,   733,
   nil,    15,   nil,   628,   335,   nil,   nil,   nil,   nil,   nil,
   nil,    15,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    16,   nil,   nil,   nil,   nil,   nil,   nil,   777,
   nil,   nil,   778,   nil,   nil,    16,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   359,   nil,   nil,   nil,   nil,   nil,    15,
   nil,   nil,   787,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    15,   nil,   nil,   nil,   nil,   nil,   nil,    16,
    16,   nil,   nil,    16,   nil,   nil,   nil,   nil,   812,    16,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    38,   nil,    38,    15,    15,   nil,   nil,
    15,   302,   nil,   nil,   nil,   nil,    15,   nil,   nil,   302,
   nil,   nil,    16,   nil,   nil,   nil,   933,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    39,   nil,    39,   nil,   nil,
   nil,   359,    38,   303,   837,    38,   nil,   nil,   nil,    15,
   nil,   303,   nil,   932,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    38,   nil,   nil,
   nil,   nil,   nil,   nil,    39,   nil,   nil,    39,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    16,   nil,   nil,   nil,
   nil,   nil,   nil,    16,    16,   nil,   nil,   nil,   nil,    39,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    16,   nil,    15,   nil,   886,   nil,    16,   nil,    16,
    15,    15,   nil,   nil,    38,   nil,   nil,   nil,   nil,    38,
    38,   nil,   nil,   897,   nil,   nil,   nil,   nil,    15,   nil,
   nil,   nil,   302,   nil,    15,   nil,    15,   nil,   nil,   nil,
   335,   nil,   nil,   302,   nil,   nil,    39,   nil,   nil,   nil,
   nil,    39,    39,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   303,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   303,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    38,   nil,   nil,   nil,
   nil,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    39,   nil,
   nil,   nil,   nil,    39,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    39,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   362,   nil,   nil,   nil,   nil,   nil,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    38,   229,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   276,   276,   276,   363,   nil,   nil,   nil,   nil,
   nil,    39,   nil,   nil,   nil,   322,   323,   324,   nil,   nil,
   nil,   nil,   nil,   nil,    39,   nil,    38,    38,   nil,   nil,
    38,   nil,   276,   276,   nil,   nil,    38,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    39,    39,
   nil,   362,    39,   nil,   nil,   nil,   nil,   nil,    39,    38,
   nil,   nil,   nil,   928,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   363,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    39,   nil,   nil,   nil,   929,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    38,   nil,   nil,   nil,   nil,   nil,   nil,
    38,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    38,   nil,
   nil,   nil,   nil,   nil,    38,    39,    38,   nil,   nil,   nil,
   nil,   nil,    39,    39,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   276,   409,   276,   nil,   nil,   428,   433,   nil,
    39,   nil,   nil,   nil,   nil,   nil,    39,   nil,    39,   nil,
   nil,   nil,   nil,   229,   nil,   nil,   448,   449,   450,   451,
   452,   453,   454,   455,   456,   457,   458,   459,   460,   461,
   462,   463,   464,   465,   466,   467,   468,   469,   470,   471,
   472,   473,   nil,   nil,   nil,   nil,   nil,   nil,   276,   276,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   276,   nil,   nil,
   nil,   nil,   nil,   nil,   276,   nil,   276,   nil,   nil,   276,
   276,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   520,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   276,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   276,   nil,   428,   613,
   409,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   276,   nil,   276,   nil,   276,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   276,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   648,
   649,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   276,   nil,   nil,   276,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   276,   276,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   276,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   276,   735,   nil,   nil,   276,   276,   740,   742,
   nil,   nil,   nil,   745,   747,   nil,   nil,   613,   749,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   276,   nil,   nil,   276,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   276,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   276,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   276,   nil,   839,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   740,   742,   747,   745,   nil,   842,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   276,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   276,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   276,   839,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   276 ]

racc_goto_check = [
    31,    67,    56,    22,    33,    33,    33,    92,    22,    64,
    64,   141,    86,     8,     2,    23,    37,    37,    73,    89,
    89,    36,    98,    22,    27,    10,    31,    17,    17,   154,
    27,    17,    22,    22,    22,    39,    22,    22,    22,    57,
    57,    15,    15,    64,    64,    64,    60,    60,    18,    71,
    71,    68,    68,    68,    74,    90,    74,   118,    45,     6,
    46,    20,    61,    61,    49,    37,    22,    22,   181,    23,
    22,    22,    22,    22,    63,    47,    38,    38,    38,    54,
   129,    68,    17,    17,    17,    17,     7,   117,    50,     5,
     4,     7,    11,   177,     1,    96,    24,    70,    70,    70,
   140,   119,    57,    12,    91,    91,    44,    44,    44,   181,
    29,    29,   155,   132,    29,   178,    22,    55,    55,   155,
   134,    22,    22,    22,    22,    22,    22,    70,    70,    14,
    47,    69,    69,    11,   135,    69,    16,     2,   172,    51,
    40,   158,   158,    19,    28,    30,   124,   124,    40,    40,
    42,    62,    73,    47,     4,    66,    72,    84,    85,    96,
    74,    74,    88,    93,    95,    29,    29,    29,    29,    99,
   100,   101,   102,   181,   103,   104,   120,   105,   106,   107,
   108,   109,   110,   111,   112,   172,    33,   113,   117,   118,
   114,   115,   118,   116,   121,    41,   127,     5,   133,     7,
    41,   159,   159,   159,   159,    92,   136,   159,     7,     7,
   137,    22,    22,    22,    22,    41,   138,    22,    22,    22,
    22,    22,    22,   140,    41,    41,    41,   139,    41,   178,
   177,    68,   154,   119,    22,    71,   120,    68,   134,   142,
    33,    33,   124,   124,    57,    37,    37,    23,     8,    33,
    20,    20,   135,   143,   144,    20,    20,   146,    41,    41,
   166,   132,    41,    29,    29,    29,    29,   147,    22,    22,
   118,   148,   118,   120,   149,    64,    54,    22,   150,   153,
   156,   140,   140,   172,   157,   160,   161,    40,    68,   162,
    31,   132,    64,   163,    47,    22,    68,   120,    24,    22,
   164,   165,   170,    22,    22,    17,    17,   172,    41,    31,
   129,    23,    37,    41,    41,    41,    41,    41,    41,   174,
    22,    45,    37,    46,   175,    24,    45,    51,    46,    22,
    22,   118,   118,    71,    70,    51,    47,    71,   nil,    11,
    47,   nil,    70,    44,    22,    22,     6,    49,    63,    23,
    37,    44,    21,    50,   nil,    90,    47,    21,    37,    23,
   124,   nil,    47,   nil,    22,    73,    98,   nil,    39,   118,
   nil,   118,    36,    39,    29,   nil,   nil,     4,     7,    89,
    22,   nil,    96,   166,   nil,    21,    24,   nil,    29,    29,
    91,    31,   nil,   118,   124,    24,   159,     8,   159,   nil,
   nil,   nil,     8,    41,    41,    41,    41,    74,    29,    41,
    41,    41,    41,    41,    41,   nil,   nil,   nil,    33,   181,
    47,   nil,   nil,   nil,    29,   nil,    41,    57,   nil,    15,
     4,   nil,   nil,   nil,    60,    96,   nil,    18,   nil,    71,
   nil,   166,   166,   nil,   nil,    71,    22,   nil,   nil,   nil,
    61,   nil,    96,   nil,    31,   nil,   nil,    69,    51,     9,
    41,    41,   nil,   nil,    71,    21,    38,    27,   nil,    41,
   nil,   130,    38,    57,    21,    21,   nil,    73,    10,   nil,
    64,   nil,    33,   nil,   nil,   nil,   nil,    41,    64,   nil,
   nil,    41,   nil,   nil,   nil,   nil,    41,   nil,    71,   nil,
    22,   nil,    22,   nil,   nil,   nil,    47,   nil,    22,   nil,
    22,   nil,   nil,     2,   nil,   nil,    22,     8,   nil,   nil,
    33,    41,    41,   nil,    22,    38,   nil,   nil,   nil,   nil,
    33,    71,   123,   nil,   nil,    49,    41,    41,   nil,    22,
    57,    71,    22,    49,   nil,   nil,    89,   nil,    22,   nil,
    57,   nil,   nil,   nil,   nil,    70,    41,   nil,    22,   nil,
    21,    92,   nil,   nil,    22,   nil,    68,   nil,    29,    71,
   nil,    86,    41,    89,   nil,   nil,   nil,    71,    57,   nil,
   nil,   nil,   nil,    21,    57,     7,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    31,    67,    70,   nil,    31,    22,    22,
   nil,   nil,   nil,    22,    22,   nil,   nil,    22,   141,   nil,
   nil,    64,   nil,   nil,   nil,   nil,   nil,    57,   nil,    15,
    31,    22,    64,   nil,    60,   nil,    22,    22,   nil,   nil,
   nil,   nil,    96,    96,     9,    31,    20,    20,    41,    22,
    61,    20,    20,   nil,    21,    20,   nil,     8,    21,    56,
    22,    96,    96,    21,   130,   nil,   130,     9,   nil,   nil,
   nil,    70,   nil,   nil,   nil,    71,    49,    47,    71,    17,
   nil,   nil,   nil,   nil,    68,    17,   nil,    49,    21,   nil,
   nil,    31,   171,   nil,   nil,    24,   nil,   nil,   nil,    47,
    31,   nil,    41,   nil,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   130,    41,   130,   nil,   nil,   nil,   nil,    22,   nil,
   nil,   123,   nil,    22,    22,   nil,    41,   nil,    22,   nil,
   nil,   nil,     9,   nil,   nil,   nil,    67,     9,    22,   nil,
    67,    41,   nil,   nil,    41,   nil,   nil,   nil,   nil,    22,
    41,   nil,    64,    47,   nil,    31,   nil,   nil,   nil,    57,
    41,   nil,    29,   nil,   nil,   nil,    41,   nil,    29,   123,
   nil,   nil,   123,   171,   123,   nil,   123,   nil,   nil,   173,
    22,   nil,   nil,   nil,   nil,    31,    22,    20,   nil,    26,
    23,    37,    17,   nil,    26,   nil,   nil,   nil,   nil,    22,
    41,    41,   nil,   130,   nil,    41,    41,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   125,   171,    25,   nil,   nil,   nil,
   nil,    25,    26,    41,   nil,   nil,   nil,   130,    41,    41,
   128,    22,   nil,    22,    22,   nil,   nil,    22,   nil,   nil,
    64,   nil,   nil,    22,   nil,   nil,   nil,   nil,   nil,    25,
    25,    25,    64,   nil,   nil,   nil,   nil,    26,   nil,    21,
   nil,    21,   nil,   nil,   nil,   nil,   nil,   nil,    22,   nil,
   nil,   nil,   nil,   171,   171,    29,    22,   nil,   nil,   nil,
    22,   nil,   nil,   nil,    25,    25,    25,   nil,   123,   nil,
   123,   nil,   123,   nil,   123,   nil,   nil,   nil,    21,   nil,
   nil,    21,    26,   nil,   nil,   nil,   nil,   nil,   nil,    31,
    41,    26,    26,   nil,   nil,    41,    41,   nil,   nil,   nil,
    41,   nil,   nil,    21,   nil,   nil,   nil,   nil,    96,    25,
    41,    96,   nil,   nil,   nil,   nil,    96,   nil,    25,    25,
    22,    41,   123,   nil,   nil,   nil,   nil,    22,    22,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   171,   nil,   171,   nil,
   nil,    33,   nil,   nil,    57,    22,   nil,   nil,   nil,   nil,
   nil,    22,     9,    22,   nil,     9,   nil,   nil,    41,   nil,
    21,   nil,   nil,   nil,   nil,    21,    21,   nil,   nil,    22,
    31,    41,   nil,   125,   nil,   nil,   125,    26,   nil,   nil,
    65,   nil,   nil,   171,    26,   171,   173,   nil,   nil,   173,
    71,   173,   nil,   173,   nil,   128,   nil,   128,   nil,    57,
    26,   nil,   nil,    41,    25,    41,    41,   nil,   nil,    41,
   nil,   nil,   nil,   nil,   nil,    41,   126,   126,   126,   nil,
   nil,   125,   nil,   nil,   125,   nil,   125,    25,   125,   nil,
   nil,   nil,   nil,   nil,     9,   nil,   nil,   nil,   nil,     9,
     9,   nil,   128,   nil,   128,   nil,   nil,   nil,    41,   nil,
   nil,   nil,    21,   nil,   nil,   nil,   nil,    21,   nil,   nil,
   nil,    26,   nil,   nil,   nil,    26,   nil,    21,   nil,   nil,
    26,   nil,   nil,   nil,   nil,   171,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    25,   nil,
   nil,   nil,    25,   nil,   nil,    26,    25,    25,   nil,   171,
   nil,   nil,   169,   169,   169,   173,   nil,   173,   nil,   173,
   nil,   173,    41,    25,   nil,    21,    52,   nil,   nil,    41,
    41,    52,    25,   nil,   nil,   nil,   nil,   nil,    21,    65,
   nil,   nil,   128,   nil,   128,    65,   128,    41,   nil,   nil,
   125,     9,   125,    41,   125,    41,   125,   nil,    53,    52,
    52,    52,   nil,    53,   nil,   nil,   128,   nil,   128,   173,
   128,    41,    21,    21,   nil,   nil,    21,   nil,   126,   126,
   126,   126,    21,   126,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    53,    53,    53,    52,    52,    52,    52,   nil,     9,
   nil,   nil,   nil,   nil,   125,   nil,   nil,   nil,   nil,   nil,
   128,    65,     9,    65,   nil,    21,    65,    65,   nil,   nil,
   128,   nil,   nil,   nil,   nil,   nil,    53,    53,    53,    53,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    52,
   nil,   nil,   nil,   nil,   nil,   nil,     9,     9,    52,    52,
     9,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   126,   126,   126,   126,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    53,   nil,   nil,   nil,   nil,    26,   nil,    26,    21,
    53,    53,   nil,   nil,   nil,   nil,    21,    21,   nil,     9,
   nil,   126,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    25,    21,    25,   nil,   nil,   nil,   nil,
    21,    25,    21,   nil,   nil,    26,   nil,   nil,    26,    25,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   169,   169,   nil,
   169,   169,   nil,   169,    52,    65,   nil,   nil,   nil,   nil,
    26,   nil,    25,   nil,   nil,    25,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,     9,   nil,   nil,   nil,    52,   nil,   nil,
     9,     9,   nil,   nil,   nil,   nil,    53,    25,   nil,   nil,
   nil,   nil,   nil,   nil,    25,   nil,   nil,   nil,     9,   nil,
   nil,   nil,   nil,    26,     9,   nil,   nil,   nil,   nil,    53,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,   nil,   nil,
   nil,   nil,    26,    26,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    65,    52,    65,
   nil,    65,    52,   nil,    25,   nil,    52,    52,   nil,    25,
    25,   169,   169,   169,   169,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    25,    52,   nil,   nil,   nil,   nil,   nil,   nil,
    53,   nil,    52,    25,    53,   nil,   nil,    65,    53,    53,
    65,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   169,   nil,   nil,    65,    53,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    53,   nil,   nil,   nil,   nil,    26,
   nil,   nil,   nil,   nil,    26,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    26,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    25,   nil,   nil,    65,
   nil,    25,   nil,    65,    65,   nil,   nil,   nil,   nil,   nil,
   nil,    25,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    26,   nil,   nil,   nil,   nil,   nil,   nil,    65,
   nil,   nil,    65,   nil,   nil,    26,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    25,   nil,   nil,   nil,   nil,   nil,    25,
   nil,   nil,    65,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    25,   nil,   nil,   nil,   nil,   nil,   nil,    26,
    26,   nil,   nil,    26,   nil,   nil,   nil,   nil,    65,    26,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    52,   nil,    52,    25,    25,   nil,   nil,
    25,    52,   nil,   nil,   nil,   nil,    25,   nil,   nil,    52,
   nil,   nil,    26,   nil,   nil,   nil,    26,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    53,   nil,    53,   nil,   nil,
   nil,    25,    52,    53,    65,    52,   nil,   nil,   nil,    25,
   nil,    53,   nil,    25,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    52,   nil,   nil,
   nil,   nil,   nil,   nil,    53,   nil,   nil,    53,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    26,   nil,   nil,   nil,
   nil,   nil,   nil,    26,    26,   nil,   nil,   nil,   nil,    53,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    26,   nil,    25,   nil,    65,   nil,    26,   nil,    26,
    25,    25,   nil,   nil,    52,   nil,   nil,   nil,   nil,    52,
    52,   nil,   nil,    65,   nil,   nil,   nil,   nil,    25,   nil,
   nil,   nil,    52,   nil,    25,   nil,    25,   nil,   nil,   nil,
    65,   nil,   nil,    52,   nil,   nil,    53,   nil,   nil,   nil,
   nil,    53,    53,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    53,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    53,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    52,   nil,   nil,   nil,
   nil,    52,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    52,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    53,   nil,
   nil,   nil,   nil,    53,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    53,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    52,   nil,   nil,   nil,   nil,   nil,    52,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    52,    32,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    32,    32,    32,    53,   nil,   nil,   nil,   nil,
   nil,    53,   nil,   nil,   nil,    32,    32,    32,   nil,   nil,
   nil,   nil,   nil,   nil,    53,   nil,    52,    52,   nil,   nil,
    52,   nil,    32,    32,   nil,   nil,    52,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    53,    53,
   nil,    52,    53,   nil,   nil,   nil,   nil,   nil,    53,    52,
   nil,   nil,   nil,    52,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    53,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    53,   nil,   nil,   nil,    53,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    52,   nil,   nil,   nil,   nil,   nil,   nil,
    52,    52,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    52,   nil,
   nil,   nil,   nil,   nil,    52,    53,    52,   nil,   nil,   nil,
   nil,   nil,    53,    53,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    32,    32,    32,   nil,   nil,    32,    32,   nil,
    53,   nil,   nil,   nil,   nil,   nil,    53,   nil,    53,   nil,
   nil,   nil,   nil,    32,   nil,   nil,    32,    32,    32,    32,
    32,    32,    32,    32,    32,    32,    32,    32,    32,    32,
    32,    32,    32,    32,    32,    32,    32,    32,    32,    32,
    32,    32,   nil,   nil,   nil,   nil,   nil,   nil,    32,    32,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    32,   nil,   nil,
   nil,   nil,   nil,   nil,    32,   nil,    32,   nil,   nil,    32,
    32,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    32,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    32,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    32,   nil,    32,    32,
    32,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    32,   nil,    32,   nil,    32,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    32,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    32,
    32,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    32,   nil,   nil,    32,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    32,    32,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    32,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    32,    32,   nil,   nil,    32,    32,    32,    32,
   nil,   nil,   nil,    32,    32,   nil,   nil,    32,    32,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    32,   nil,   nil,    32,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    32,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    32,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    32,   nil,    32,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    32,    32,    32,    32,   nil,    32,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    32,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    32,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    32,    32,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    32 ]

racc_goto_pointer = [
   nil,    94,    14,   nil,    87,    84,    56,    86,  -313,   426,
  -497,  -559,  -681,   nil,  -368,    33,   127,   -36,  -167,    75,
    41,   352,     3,  -196,  -314,   806,   779,  -185,   -65,    47,
    21,   -19,  1893,   -25,   nil,   nil,    -3,  -196,    50,  -229,
  -334,   195,  -328,   nil,    77,    25,    27,  -144,   nil,    30,
    53,  -178,  1126,  1158,  -277,    48,   -69,    31,   nil,   nil,
    38,    54,  -249,    33,   -25,   930,    96,   -58,    22,  -199,
    68,    23,  -319,  -261,  -433,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    86,    98,   -49,   nil,  -175,  -320,
  -635,  -436,  -337,    95,   nil,  -196,    92,   nil,  -540,    98,
   113,   113,  -352,   116,   113,   112,  -515,   113,  -516,  -368,
  -717,  -373,  -529,  -177,  -184,  -372,  -642,  -801,  -618,  -574,
  -714,  -422,   nil,   -84,  -514,   188,   267,  -420,   204,  -536,
  -145,   nil,  -503,  -656,  -734,  -720,  -329,  -583,  -121,  -430,
  -435,   -11,  -632,  -619,    -5,   nil,   -12,    -3,  -627,  -378,
  -510,   nil,   nil,   200,   -52,    28,   195,   198,  -241,  -179,
   198,   198,   200,  -275,  -269,  -257,  -275,   nil,   nil,   449,
  -497,   147,  -661,   153,  -476,  -519,   nil,  -708,  -733,   nil,
   nil,  -421 ]

racc_goto_default = [
   nil,   nil,   nil,     3,   nil,     4,   345,   293,   nil,   522,
   nil,   817,   nil,   290,   291,   nil,   nil,   nil,    11,    12,
    18,   228,   321,   nil,   nil,   226,   227,   nil,   nil,    17,
   nil,   440,    21,    22,    23,    24,   nil,   645,   nil,   nil,
   nil,   310,   nil,    25,   411,    32,   nil,   nil,    34,    37,
    36,   nil,   223,   224,   357,   nil,   131,   419,   130,   133,
    77,    78,   nil,    92,    46,   282,   nil,   785,   412,   nil,
   413,   424,   599,   486,   280,   266,    47,    48,    49,    50,
    51,    52,    53,    54,    55,   nil,   267,    61,   nil,   nil,
   nil,   nil,   nil,   nil,    69,   nil,   537,    70,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   808,   673,   nil,
   809,   910,   756,   661,   nil,   662,   nil,   nil,   663,   nil,
   665,   615,   nil,   nil,   nil,   671,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   423,   nil,   nil,   nil,   nil,
   nil,    76,    79,    80,   nil,   nil,   nil,   nil,   nil,   566,
   nil,   nil,   nil,   nil,   nil,   nil,   877,   717,   660,   nil,
   664,   885,   676,   678,   679,   768,   682,   683,   769,   686,
   689,   285 ]

racc_reduce_table = [
  0, 0, :racc_error,
  1, 145, :_reduce_none,
  2, 146, :_reduce_2,
  0, 147, :_reduce_3,
  1, 147, :_reduce_4,
  3, 147, :_reduce_5,
  2, 147, :_reduce_6,
  1, 149, :_reduce_none,
  4, 149, :_reduce_8,
  4, 152, :_reduce_9,
  2, 153, :_reduce_10,
  0, 157, :_reduce_11,
  1, 157, :_reduce_12,
  3, 157, :_reduce_13,
  2, 157, :_reduce_14,
  1, 158, :_reduce_none,
  4, 158, :_reduce_16,
  0, 174, :_reduce_17,
  4, 151, :_reduce_18,
  3, 151, :_reduce_19,
  3, 151, :_reduce_20,
  3, 151, :_reduce_21,
  2, 151, :_reduce_22,
  3, 151, :_reduce_23,
  3, 151, :_reduce_24,
  3, 151, :_reduce_25,
  3, 151, :_reduce_26,
  3, 151, :_reduce_27,
  4, 151, :_reduce_28,
  1, 151, :_reduce_none,
  3, 151, :_reduce_30,
  3, 151, :_reduce_31,
  6, 151, :_reduce_32,
  5, 151, :_reduce_33,
  5, 151, :_reduce_34,
  5, 151, :_reduce_35,
  5, 151, :_reduce_36,
  3, 151, :_reduce_37,
  3, 151, :_reduce_38,
  3, 151, :_reduce_39,
  1, 151, :_reduce_none,
  3, 162, :_reduce_41,
  3, 162, :_reduce_42,
  1, 173, :_reduce_none,
  3, 173, :_reduce_44,
  3, 173, :_reduce_45,
  3, 173, :_reduce_46,
  2, 173, :_reduce_47,
  1, 173, :_reduce_none,
  1, 161, :_reduce_none,
  1, 164, :_reduce_none,
  1, 164, :_reduce_none,
  1, 178, :_reduce_none,
  4, 178, :_reduce_53,
  0, 186, :_reduce_54,
  5, 183, :_reduce_55,
  1, 185, :_reduce_none,
  2, 177, :_reduce_57,
  3, 177, :_reduce_58,
  4, 177, :_reduce_59,
  5, 177, :_reduce_60,
  4, 177, :_reduce_61,
  5, 177, :_reduce_62,
  2, 177, :_reduce_63,
  2, 177, :_reduce_64,
  2, 177, :_reduce_65,
  2, 177, :_reduce_66,
  2, 177, :_reduce_67,
  1, 163, :_reduce_68,
  3, 163, :_reduce_69,
  1, 190, :_reduce_70,
  3, 190, :_reduce_71,
  1, 189, :_reduce_none,
  2, 189, :_reduce_73,
  3, 189, :_reduce_74,
  5, 189, :_reduce_75,
  2, 189, :_reduce_76,
  4, 189, :_reduce_77,
  2, 189, :_reduce_78,
  4, 189, :_reduce_79,
  1, 189, :_reduce_80,
  3, 189, :_reduce_81,
  1, 193, :_reduce_none,
  3, 193, :_reduce_83,
  2, 192, :_reduce_84,
  3, 192, :_reduce_85,
  1, 195, :_reduce_86,
  3, 195, :_reduce_87,
  1, 194, :_reduce_88,
  1, 194, :_reduce_89,
  4, 194, :_reduce_90,
  3, 194, :_reduce_91,
  3, 194, :_reduce_92,
  3, 194, :_reduce_93,
  3, 194, :_reduce_94,
  2, 194, :_reduce_95,
  1, 194, :_reduce_96,
  1, 170, :_reduce_97,
  1, 170, :_reduce_98,
  4, 170, :_reduce_99,
  3, 170, :_reduce_100,
  3, 170, :_reduce_101,
  3, 170, :_reduce_102,
  3, 170, :_reduce_103,
  2, 170, :_reduce_104,
  1, 170, :_reduce_105,
  1, 198, :_reduce_106,
  1, 198, :_reduce_none,
  2, 199, :_reduce_108,
  1, 199, :_reduce_109,
  3, 199, :_reduce_110,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 203, :_reduce_116,
  1, 203, :_reduce_none,
  1, 159, :_reduce_none,
  1, 159, :_reduce_none,
  1, 160, :_reduce_120,
  0, 206, :_reduce_121,
  4, 160, :_reduce_122,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  3, 176, :_reduce_194,
  5, 176, :_reduce_195,
  3, 176, :_reduce_196,
  5, 176, :_reduce_197,
  6, 176, :_reduce_198,
  5, 176, :_reduce_199,
  5, 176, :_reduce_200,
  5, 176, :_reduce_201,
  5, 176, :_reduce_202,
  4, 176, :_reduce_203,
  3, 176, :_reduce_204,
  3, 176, :_reduce_205,
  3, 176, :_reduce_206,
  3, 176, :_reduce_207,
  3, 176, :_reduce_208,
  3, 176, :_reduce_209,
  3, 176, :_reduce_210,
  3, 176, :_reduce_211,
  3, 176, :_reduce_212,
  4, 176, :_reduce_213,
  2, 176, :_reduce_214,
  2, 176, :_reduce_215,
  3, 176, :_reduce_216,
  3, 176, :_reduce_217,
  3, 176, :_reduce_218,
  3, 176, :_reduce_219,
  3, 176, :_reduce_220,
  3, 176, :_reduce_221,
  3, 176, :_reduce_222,
  3, 176, :_reduce_223,
  3, 176, :_reduce_224,
  3, 176, :_reduce_225,
  3, 176, :_reduce_226,
  3, 176, :_reduce_227,
  3, 176, :_reduce_228,
  2, 176, :_reduce_229,
  2, 176, :_reduce_230,
  3, 176, :_reduce_231,
  3, 176, :_reduce_232,
  3, 176, :_reduce_233,
  3, 176, :_reduce_234,
  3, 176, :_reduce_235,
  6, 176, :_reduce_236,
  1, 176, :_reduce_none,
  1, 209, :_reduce_none,
  1, 210, :_reduce_none,
  2, 210, :_reduce_none,
  4, 210, :_reduce_241,
  2, 210, :_reduce_242,
  3, 215, :_reduce_243,
  0, 216, :_reduce_244,
  1, 216, :_reduce_none,
  0, 167, :_reduce_246,
  1, 167, :_reduce_none,
  2, 167, :_reduce_none,
  4, 167, :_reduce_249,
  2, 167, :_reduce_250,
  1, 188, :_reduce_251,
  2, 188, :_reduce_252,
  2, 188, :_reduce_253,
  4, 188, :_reduce_254,
  1, 188, :_reduce_255,
  0, 219, :_reduce_256,
  2, 182, :_reduce_257,
  2, 218, :_reduce_258,
  2, 217, :_reduce_259,
  0, 217, :_reduce_260,
  1, 212, :_reduce_261,
  2, 212, :_reduce_262,
  3, 212, :_reduce_263,
  4, 212, :_reduce_264,
  1, 172, :_reduce_265,
  1, 172, :_reduce_none,
  3, 171, :_reduce_267,
  4, 171, :_reduce_268,
  2, 171, :_reduce_269,
  1, 208, :_reduce_none,
  1, 208, :_reduce_none,
  1, 208, :_reduce_none,
  1, 208, :_reduce_none,
  1, 208, :_reduce_none,
  1, 208, :_reduce_none,
  1, 208, :_reduce_none,
  1, 208, :_reduce_none,
  1, 208, :_reduce_none,
  1, 208, :_reduce_none,
  1, 208, :_reduce_280,
  0, 244, :_reduce_281,
  4, 208, :_reduce_282,
  0, 245, :_reduce_283,
  0, 246, :_reduce_284,
  6, 208, :_reduce_285,
  0, 247, :_reduce_286,
  4, 208, :_reduce_287,
  3, 208, :_reduce_288,
  3, 208, :_reduce_289,
  2, 208, :_reduce_290,
  3, 208, :_reduce_291,
  3, 208, :_reduce_292,
  1, 208, :_reduce_293,
  4, 208, :_reduce_294,
  3, 208, :_reduce_295,
  1, 208, :_reduce_296,
  5, 208, :_reduce_297,
  4, 208, :_reduce_298,
  3, 208, :_reduce_299,
  2, 208, :_reduce_300,
  1, 208, :_reduce_none,
  2, 208, :_reduce_302,
  0, 248, :_reduce_303,
  3, 208, :_reduce_304,
  6, 208, :_reduce_305,
  6, 208, :_reduce_306,
  0, 249, :_reduce_307,
  0, 250, :_reduce_308,
  7, 208, :_reduce_309,
  0, 251, :_reduce_310,
  0, 252, :_reduce_311,
  7, 208, :_reduce_312,
  5, 208, :_reduce_313,
  4, 208, :_reduce_314,
  0, 253, :_reduce_315,
  0, 254, :_reduce_316,
  9, 208, :_reduce_317,
  0, 255, :_reduce_318,
  6, 208, :_reduce_319,
  0, 256, :_reduce_320,
  7, 208, :_reduce_321,
  0, 257, :_reduce_322,
  5, 208, :_reduce_323,
  0, 258, :_reduce_324,
  6, 208, :_reduce_325,
  0, 259, :_reduce_326,
  0, 260, :_reduce_327,
  9, 208, :_reduce_328,
  1, 208, :_reduce_329,
  1, 208, :_reduce_330,
  1, 208, :_reduce_331,
  1, 208, :_reduce_332,
  1, 166, :_reduce_none,
  1, 238, :_reduce_334,
  1, 241, :_reduce_335,
  1, 233, :_reduce_none,
  1, 233, :_reduce_none,
  2, 233, :_reduce_338,
  1, 235, :_reduce_none,
  1, 235, :_reduce_none,
  1, 234, :_reduce_none,
  5, 234, :_reduce_342,
  1, 155, :_reduce_none,
  2, 155, :_reduce_344,
  1, 237, :_reduce_none,
  1, 237, :_reduce_none,
  1, 261, :_reduce_347,
  3, 261, :_reduce_348,
  1, 264, :_reduce_349,
  3, 264, :_reduce_350,
  1, 263, :_reduce_none,
  4, 263, :_reduce_352,
  6, 263, :_reduce_353,
  3, 263, :_reduce_354,
  5, 263, :_reduce_355,
  2, 263, :_reduce_356,
  4, 263, :_reduce_357,
  1, 263, :_reduce_358,
  3, 263, :_reduce_359,
  4, 265, :_reduce_360,
  2, 265, :_reduce_361,
  2, 265, :_reduce_362,
  1, 265, :_reduce_363,
  2, 270, :_reduce_364,
  0, 270, :_reduce_365,
  6, 271, :_reduce_366,
  8, 271, :_reduce_367,
  4, 271, :_reduce_368,
  6, 271, :_reduce_369,
  4, 271, :_reduce_370,
  2, 271, :_reduce_none,
  6, 271, :_reduce_372,
  2, 271, :_reduce_373,
  4, 271, :_reduce_374,
  6, 271, :_reduce_375,
  2, 271, :_reduce_376,
  4, 271, :_reduce_377,
  2, 271, :_reduce_378,
  4, 271, :_reduce_379,
  1, 271, :_reduce_none,
  0, 184, :_reduce_381,
  1, 184, :_reduce_382,
  3, 275, :_reduce_383,
  1, 275, :_reduce_384,
  4, 275, :_reduce_385,
  1, 276, :_reduce_386,
  4, 276, :_reduce_387,
  1, 277, :_reduce_388,
  3, 277, :_reduce_389,
  1, 278, :_reduce_390,
  1, 278, :_reduce_none,
  0, 282, :_reduce_392,
  0, 283, :_reduce_393,
  4, 232, :_reduce_394,
  4, 280, :_reduce_395,
  1, 280, :_reduce_396,
  0, 286, :_reduce_397,
  4, 281, :_reduce_398,
  0, 287, :_reduce_399,
  4, 281, :_reduce_400,
  0, 288, :_reduce_401,
  5, 285, :_reduce_402,
  2, 179, :_reduce_403,
  4, 179, :_reduce_404,
  5, 179, :_reduce_405,
  5, 179, :_reduce_406,
  2, 231, :_reduce_407,
  4, 231, :_reduce_408,
  4, 231, :_reduce_409,
  3, 231, :_reduce_410,
  3, 231, :_reduce_411,
  3, 231, :_reduce_412,
  2, 231, :_reduce_413,
  1, 231, :_reduce_414,
  4, 231, :_reduce_415,
  0, 290, :_reduce_416,
  5, 230, :_reduce_417,
  0, 291, :_reduce_418,
  5, 230, :_reduce_419,
  5, 236, :_reduce_420,
  1, 292, :_reduce_421,
  1, 292, :_reduce_none,
  6, 154, :_reduce_423,
  0, 154, :_reduce_424,
  1, 293, :_reduce_425,
  1, 293, :_reduce_none,
  1, 293, :_reduce_none,
  2, 294, :_reduce_428,
  1, 294, :_reduce_none,
  2, 156, :_reduce_430,
  1, 156, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 221, :_reduce_435,
  1, 296, :_reduce_436,
  2, 296, :_reduce_437,
  3, 297, :_reduce_438,
  1, 297, :_reduce_439,
  1, 297, :_reduce_440,
  3, 222, :_reduce_441,
  4, 223, :_reduce_442,
  3, 224, :_reduce_443,
  0, 301, :_reduce_444,
  3, 301, :_reduce_445,
  1, 302, :_reduce_446,
  2, 302, :_reduce_447,
  3, 226, :_reduce_448,
  0, 304, :_reduce_449,
  3, 304, :_reduce_450,
  3, 225, :_reduce_451,
  3, 227, :_reduce_452,
  0, 305, :_reduce_453,
  3, 305, :_reduce_454,
  0, 306, :_reduce_455,
  3, 306, :_reduce_456,
  0, 298, :_reduce_457,
  2, 298, :_reduce_458,
  0, 299, :_reduce_459,
  2, 299, :_reduce_460,
  0, 300, :_reduce_461,
  2, 300, :_reduce_462,
  1, 303, :_reduce_463,
  2, 303, :_reduce_464,
  0, 308, :_reduce_465,
  4, 303, :_reduce_466,
  1, 307, :_reduce_467,
  1, 307, :_reduce_468,
  1, 307, :_reduce_469,
  1, 307, :_reduce_none,
  1, 204, :_reduce_471,
  3, 205, :_reduce_472,
  1, 295, :_reduce_473,
  2, 295, :_reduce_474,
  1, 207, :_reduce_475,
  1, 207, :_reduce_476,
  1, 207, :_reduce_477,
  1, 207, :_reduce_478,
  1, 196, :_reduce_479,
  1, 196, :_reduce_480,
  1, 196, :_reduce_481,
  1, 196, :_reduce_482,
  1, 196, :_reduce_483,
  1, 197, :_reduce_484,
  1, 197, :_reduce_485,
  1, 197, :_reduce_486,
  1, 197, :_reduce_487,
  1, 197, :_reduce_488,
  1, 197, :_reduce_489,
  1, 197, :_reduce_490,
  1, 228, :_reduce_491,
  1, 228, :_reduce_492,
  1, 165, :_reduce_493,
  1, 165, :_reduce_494,
  1, 169, :_reduce_495,
  1, 169, :_reduce_496,
  1, 239, :_reduce_497,
  0, 309, :_reduce_498,
  4, 239, :_reduce_499,
  2, 239, :_reduce_500,
  3, 242, :_reduce_501,
  0, 311, :_reduce_502,
  3, 242, :_reduce_503,
  4, 310, :_reduce_504,
  2, 310, :_reduce_505,
  2, 310, :_reduce_506,
  1, 310, :_reduce_507,
  2, 313, :_reduce_508,
  0, 313, :_reduce_509,
  6, 284, :_reduce_510,
  8, 284, :_reduce_511,
  4, 284, :_reduce_512,
  6, 284, :_reduce_513,
  4, 284, :_reduce_514,
  6, 284, :_reduce_515,
  2, 284, :_reduce_516,
  4, 284, :_reduce_517,
  6, 284, :_reduce_518,
  2, 284, :_reduce_519,
  4, 284, :_reduce_520,
  2, 284, :_reduce_521,
  4, 284, :_reduce_522,
  1, 284, :_reduce_523,
  0, 284, :_reduce_524,
  1, 279, :_reduce_525,
  1, 279, :_reduce_526,
  1, 279, :_reduce_527,
  1, 279, :_reduce_528,
  1, 262, :_reduce_none,
  1, 262, :_reduce_530,
  1, 315, :_reduce_531,
  1, 316, :_reduce_532,
  3, 316, :_reduce_533,
  1, 272, :_reduce_534,
  3, 272, :_reduce_535,
  1, 317, :_reduce_536,
  2, 318, :_reduce_537,
  1, 318, :_reduce_538,
  2, 319, :_reduce_539,
  1, 319, :_reduce_540,
  1, 266, :_reduce_541,
  3, 266, :_reduce_542,
  1, 312, :_reduce_543,
  3, 312, :_reduce_544,
  1, 320, :_reduce_none,
  1, 320, :_reduce_none,
  2, 267, :_reduce_547,
  1, 267, :_reduce_548,
  3, 321, :_reduce_549,
  3, 322, :_reduce_550,
  1, 273, :_reduce_551,
  3, 273, :_reduce_552,
  1, 314, :_reduce_553,
  3, 314, :_reduce_554,
  1, 323, :_reduce_none,
  1, 323, :_reduce_none,
  2, 274, :_reduce_557,
  1, 274, :_reduce_558,
  1, 324, :_reduce_none,
  1, 324, :_reduce_none,
  2, 269, :_reduce_561,
  2, 268, :_reduce_562,
  0, 268, :_reduce_563,
  1, 243, :_reduce_none,
  3, 243, :_reduce_565,
  0, 229, :_reduce_566,
  2, 229, :_reduce_none,
  1, 214, :_reduce_568,
  3, 214, :_reduce_569,
  3, 325, :_reduce_570,
  2, 325, :_reduce_571,
  4, 325, :_reduce_572,
  2, 325, :_reduce_573,
  1, 187, :_reduce_none,
  1, 187, :_reduce_none,
  1, 187, :_reduce_none,
  1, 181, :_reduce_none,
  1, 181, :_reduce_none,
  1, 181, :_reduce_none,
  1, 181, :_reduce_none,
  1, 289, :_reduce_none,
  1, 289, :_reduce_none,
  1, 289, :_reduce_none,
  1, 180, :_reduce_none,
  1, 180, :_reduce_none,
  0, 148, :_reduce_none,
  1, 148, :_reduce_none,
  0, 175, :_reduce_none,
  1, 175, :_reduce_none,
  2, 191, :_reduce_590,
  2, 168, :_reduce_591,
  0, 213, :_reduce_none,
  1, 213, :_reduce_none,
  1, 213, :_reduce_none,
  1, 240, :_reduce_595,
  1, 240, :_reduce_none,
  1, 150, :_reduce_none,
  2, 150, :_reduce_none,
  0, 211, :_reduce_599 ]

racc_reduce_n = 600

racc_shift_n = 1027

racc_token_table = {
  false => 0,
  :error => 1,
  :kCLASS => 2,
  :kMODULE => 3,
  :kDEF => 4,
  :kUNDEF => 5,
  :kBEGIN => 6,
  :kRESCUE => 7,
  :kENSURE => 8,
  :kEND => 9,
  :kIF => 10,
  :kUNLESS => 11,
  :kTHEN => 12,
  :kELSIF => 13,
  :kELSE => 14,
  :kCASE => 15,
  :kWHEN => 16,
  :kWHILE => 17,
  :kUNTIL => 18,
  :kFOR => 19,
  :kBREAK => 20,
  :kNEXT => 21,
  :kREDO => 22,
  :kRETRY => 23,
  :kIN => 24,
  :kDO => 25,
  :kDO_COND => 26,
  :kDO_BLOCK => 27,
  :kDO_LAMBDA => 28,
  :kRETURN => 29,
  :kYIELD => 30,
  :kSUPER => 31,
  :kSELF => 32,
  :kNIL => 33,
  :kTRUE => 34,
  :kFALSE => 35,
  :kAND => 36,
  :kOR => 37,
  :kNOT => 38,
  :kIF_MOD => 39,
  :kUNLESS_MOD => 40,
  :kWHILE_MOD => 41,
  :kUNTIL_MOD => 42,
  :kRESCUE_MOD => 43,
  :kALIAS => 44,
  :kDEFINED => 45,
  :klBEGIN => 46,
  :klEND => 47,
  :k__LINE__ => 48,
  :k__FILE__ => 49,
  :k__ENCODING__ => 50,
  :tIDENTIFIER => 51,
  :tFID => 52,
  :tGVAR => 53,
  :tIVAR => 54,
  :tCONSTANT => 55,
  :tLABEL => 56,
  :tCVAR => 57,
  :tNTH_REF => 58,
  :tBACK_REF => 59,
  :tSTRING_CONTENT => 60,
  :tINTEGER => 61,
  :tFLOAT => 62,
  :tUPLUS => 63,
  :tUMINUS => 64,
  :tUNARY_NUM => 65,
  :tPOW => 66,
  :tCMP => 67,
  :tEQ => 68,
  :tEQQ => 69,
  :tNEQ => 70,
  :tGEQ => 71,
  :tLEQ => 72,
  :tANDOP => 73,
  :tOROP => 74,
  :tMATCH => 75,
  :tNMATCH => 76,
  :tDOT => 77,
  :tDOT2 => 78,
  :tDOT3 => 79,
  :tAREF => 80,
  :tASET => 81,
  :tLSHFT => 82,
  :tRSHFT => 83,
  :tCOLON2 => 84,
  :tCOLON3 => 85,
  :tOP_ASGN => 86,
  :tASSOC => 87,
  :tLPAREN => 88,
  :tLPAREN2 => 89,
  :tRPAREN => 90,
  :tLPAREN_ARG => 91,
  :tLBRACK => 92,
  :tLBRACK2 => 93,
  :tRBRACK => 94,
  :tLBRACE => 95,
  :tLBRACE_ARG => 96,
  :tSTAR => 97,
  :tSTAR2 => 98,
  :tAMPER => 99,
  :tAMPER2 => 100,
  :tTILDE => 101,
  :tPERCENT => 102,
  :tDIVIDE => 103,
  :tDSTAR => 104,
  :tPLUS => 105,
  :tMINUS => 106,
  :tLT => 107,
  :tGT => 108,
  :tPIPE => 109,
  :tBANG => 110,
  :tCARET => 111,
  :tLCURLY => 112,
  :tRCURLY => 113,
  :tBACK_REF2 => 114,
  :tSYMBEG => 115,
  :tSTRING_BEG => 116,
  :tXSTRING_BEG => 117,
  :tREGEXP_BEG => 118,
  :tREGEXP_OPT => 119,
  :tWORDS_BEG => 120,
  :tQWORDS_BEG => 121,
  :tSYMBOLS_BEG => 122,
  :tQSYMBOLS_BEG => 123,
  :tSTRING_DBEG => 124,
  :tSTRING_DVAR => 125,
  :tSTRING_END => 126,
  :tSTRING_DEND => 127,
  :tSTRING => 128,
  :tSYMBOL => 129,
  :tNL => 130,
  :tEH => 131,
  :tCOLON => 132,
  :tCOMMA => 133,
  :tSPACE => 134,
  :tSEMI => 135,
  :tLAMBDA => 136,
  :tLAMBEG => 137,
  :tCHARACTER => 138,
  :tRATIONAL => 139,
  :tIMAGINARY => 140,
  :tLABEL_END => 141,
  :tEQL => 142,
  :tLOWEST => 143 }

racc_nt_base = 144

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]
Ractor.make_shareable(Racc_arg) if defined?(Ractor)

Racc_token_to_s_table = [
  "$end",
  "error",
  "kCLASS",
  "kMODULE",
  "kDEF",
  "kUNDEF",
  "kBEGIN",
  "kRESCUE",
  "kENSURE",
  "kEND",
  "kIF",
  "kUNLESS",
  "kTHEN",
  "kELSIF",
  "kELSE",
  "kCASE",
  "kWHEN",
  "kWHILE",
  "kUNTIL",
  "kFOR",
  "kBREAK",
  "kNEXT",
  "kREDO",
  "kRETRY",
  "kIN",
  "kDO",
  "kDO_COND",
  "kDO_BLOCK",
  "kDO_LAMBDA",
  "kRETURN",
  "kYIELD",
  "kSUPER",
  "kSELF",
  "kNIL",
  "kTRUE",
  "kFALSE",
  "kAND",
  "kOR",
  "kNOT",
  "kIF_MOD",
  "kUNLESS_MOD",
  "kWHILE_MOD",
  "kUNTIL_MOD",
  "kRESCUE_MOD",
  "kALIAS",
  "kDEFINED",
  "klBEGIN",
  "klEND",
  "k__LINE__",
  "k__FILE__",
  "k__ENCODING__",
  "tIDENTIFIER",
  "tFID",
  "tGVAR",
  "tIVAR",
  "tCONSTANT",
  "tLABEL",
  "tCVAR",
  "tNTH_REF",
  "tBACK_REF",
  "tSTRING_CONTENT",
  "tINTEGER",
  "tFLOAT",
  "tUPLUS",
  "tUMINUS",
  "tUNARY_NUM",
  "tPOW",
  "tCMP",
  "tEQ",
  "tEQQ",
  "tNEQ",
  "tGEQ",
  "tLEQ",
  "tANDOP",
  "tOROP",
  "tMATCH",
  "tNMATCH",
  "tDOT",
  "tDOT2",
  "tDOT3",
  "tAREF",
  "tASET",
  "tLSHFT",
  "tRSHFT",
  "tCOLON2",
  "tCOLON3",
  "tOP_ASGN",
  "tASSOC",
  "tLPAREN",
  "tLPAREN2",
  "tRPAREN",
  "tLPAREN_ARG",
  "tLBRACK",
  "tLBRACK2",
  "tRBRACK",
  "tLBRACE",
  "tLBRACE_ARG",
  "tSTAR",
  "tSTAR2",
  "tAMPER",
  "tAMPER2",
  "tTILDE",
  "tPERCENT",
  "tDIVIDE",
  "tDSTAR",
  "tPLUS",
  "tMINUS",
  "tLT",
  "tGT",
  "tPIPE",
  "tBANG",
  "tCARET",
  "tLCURLY",
  "tRCURLY",
  "tBACK_REF2",
  "tSYMBEG",
  "tSTRING_BEG",
  "tXSTRING_BEG",
  "tREGEXP_BEG",
  "tREGEXP_OPT",
  "tWORDS_BEG",
  "tQWORDS_BEG",
  "tSYMBOLS_BEG",
  "tQSYMBOLS_BEG",
  "tSTRING_DBEG",
  "tSTRING_DVAR",
  "tSTRING_END",
  "tSTRING_DEND",
  "tSTRING",
  "tSYMBOL",
  "tNL",
  "tEH",
  "tCOLON",
  "tCOMMA",
  "tSPACE",
  "tSEMI",
  "tLAMBDA",
  "tLAMBEG",
  "tCHARACTER",
  "tRATIONAL",
  "tIMAGINARY",
  "tLABEL_END",
  "tEQL",
  "tLOWEST",
  "$start",
  "program",
  "top_compstmt",
  "top_stmts",
  "opt_terms",
  "top_stmt",
  "terms",
  "stmt",
  "bodystmt",
  "compstmt",
  "opt_rescue",
  "opt_else",
  "opt_ensure",
  "stmts",
  "stmt_or_begin",
  "fitem",
  "undef_list",
  "expr_value",
  "command_asgn",
  "mlhs",
  "command_call",
  "var_lhs",
  "primary_value",
  "opt_call_args",
  "rbracket",
  "backref",
  "lhs",
  "mrhs",
  "mrhs_arg",
  "expr",
  "@1",
  "opt_nl",
  "arg",
  "command",
  "block_command",
  "block_call",
  "dot_or_colon",
  "operation2",
  "command_args",
  "cmd_brace_block",
  "opt_block_param",
  "fcall",
  "@2",
  "operation",
  "call_args",
  "mlhs_basic",
  "mlhs_inner",
  "rparen",
  "mlhs_head",
  "mlhs_item",
  "mlhs_node",
  "mlhs_post",
  "user_variable",
  "keyword_variable",
  "cname",
  "cpath",
  "fname",
  "op",
  "reswords",
  "fsym",
  "symbol",
  "dsym",
  "@3",
  "simple_numeric",
  "primary",
  "arg_value",
  "aref_args",
  "none",
  "args",
  "trailer",
  "assocs",
  "paren_args",
  "opt_paren_args",
  "opt_block_arg",
  "block_arg",
  "@4",
  "literal",
  "strings",
  "xstring",
  "regexp",
  "words",
  "qwords",
  "symbols",
  "qsymbols",
  "var_ref",
  "assoc_list",
  "brace_block",
  "method_call",
  "lambda",
  "then",
  "if_tail",
  "do",
  "case_body",
  "for_var",
  "k_class",
  "superclass",
  "term",
  "k_module",
  "f_arglist",
  "singleton",
  "@5",
  "@6",
  "@7",
  "@8",
  "@9",
  "@10",
  "@11",
  "@12",
  "@13",
  "@14",
  "@15",
  "@16",
  "@17",
  "@18",
  "@19",
  "@20",
  "@21",
  "f_marg",
  "f_norm_arg",
  "f_margs",
  "f_marg_list",
  "block_args_tail",
  "f_block_kwarg",
  "f_kwrest",
  "opt_f_block_arg",
  "f_block_arg",
  "opt_block_args_tail",
  "block_param",
  "f_arg",
  "f_block_optarg",
  "f_rest_arg",
  "block_param_def",
  "opt_bv_decl",
  "bv_decls",
  "bvar",
  "f_bad_arg",
  "f_larglist",
  "lambda_body",
  "@22",
  "@23",
  "f_args",
  "do_block",
  "@24",
  "@25",
  "@26",
  "operation3",
  "@27",
  "@28",
  "cases",
  "exc_list",
  "exc_var",
  "numeric",
  "string",
  "string1",
  "string_contents",
  "xstring_contents",
  "regexp_contents",
  "word_list",
  "word",
  "string_content",
  "symbol_list",
  "qword_list",
  "qsym_list",
  "string_dvar",
  "@29",
  "@30",
  "args_tail",
  "@31",
  "f_kwarg",
  "opt_args_tail",
  "f_optarg",
  "f_arg_asgn",
  "f_arg_item",
  "f_label",
  "f_kw",
  "f_block_kw",
  "kwrest_mark",
  "f_opt",
  "f_block_opt",
  "restarg_mark",
  "blkarg_mark",
  "assoc" ]
Ractor.make_shareable(Racc_token_to_s_table) if defined?(Ractor)

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

# reduce 1 omitted

def _reduce_2(val, _values, result)
                      result = @builder.compstmt(val[0])

    result
end

def _reduce_3(val, _values, result)
                      result = []

    result
end

def _reduce_4(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_5(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_6(val, _values, result)
                      result = [ val[1] ]

    result
end

# reduce 7 omitted

def _reduce_8(val, _values, result)
                      result = @builder.preexe(val[0], val[1], val[2], val[3])

    result
end

def _reduce_9(val, _values, result)
                      rescue_bodies     = val[1]
                      else_t,   else_   = val[2]
                      ensure_t, ensure_ = val[3]

                      if rescue_bodies.empty? && !else_t.nil?
                        diagnostic :warning, :useless_else, nil, else_t
                      end

                      result = @builder.begin_body(val[0],
                                  rescue_bodies,
                                  else_t,   else_,
                                  ensure_t, ensure_)

    result
end

def _reduce_10(val, _values, result)
                      result = @builder.compstmt(val[0])

    result
end

def _reduce_11(val, _values, result)
                      result = []

    result
end

def _reduce_12(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_13(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_14(val, _values, result)
                      result = [ val[1] ]

    result
end

# reduce 15 omitted

def _reduce_16(val, _values, result)
                      diagnostic :error, :begin_in_method, nil, val[0]

    result
end

def _reduce_17(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_18(val, _values, result)
                      result = @builder.alias(val[0], val[1], val[3])

    result
end

def _reduce_19(val, _values, result)
                      result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.gvar(val[2]))

    result
end

def _reduce_20(val, _values, result)
                      result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.back_ref(val[2]))

    result
end

def _reduce_21(val, _values, result)
                      diagnostic :error, :nth_ref_alias, nil, val[2]

    result
end

def _reduce_22(val, _values, result)
                      result = @builder.undef_method(val[0], val[1])

    result
end

def _reduce_23(val, _values, result)
                      result = @builder.condition_mod(val[0], nil,
                                                      val[1], val[2])

    result
end

def _reduce_24(val, _values, result)
                      result = @builder.condition_mod(nil, val[0],
                                                      val[1], val[2])

    result
end

def _reduce_25(val, _values, result)
                      result = @builder.loop_mod(:while, val[0], val[1], val[2])

    result
end

def _reduce_26(val, _values, result)
                      result = @builder.loop_mod(:until, val[0], val[1], val[2])

    result
end

def _reduce_27(val, _values, result)
                      rescue_body = @builder.rescue_body(val[1],
                                        nil, nil, nil,
                                        nil, val[2])

                      result = @builder.begin_body(val[0], [ rescue_body ])

    result
end

def _reduce_28(val, _values, result)
                      result = @builder.postexe(val[0], val[1], val[2], val[3])

    result
end

# reduce 29 omitted

def _reduce_30(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1], val[2])

    result
end

def _reduce_31(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_32(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])

    result
end

def _reduce_33(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_34(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_35(val, _values, result)
                      const  = @builder.const_op_assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))
                      result = @builder.op_assign(const, val[3], val[4])

    result
end

def _reduce_36(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_37(val, _values, result)
                      @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_38(val, _values, result)
                      result = @builder.assign(val[0], val[1],
                                  @builder.array(nil, val[2], nil))

    result
end

def _reduce_39(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1], val[2])

    result
end

# reduce 40 omitted

def _reduce_41(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

def _reduce_42(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

# reduce 43 omitted

def _reduce_44(val, _values, result)
                      result = @builder.logical_op(:and, val[0], val[1], val[2])

    result
end

def _reduce_45(val, _values, result)
                      result = @builder.logical_op(:or, val[0], val[1], val[2])

    result
end

def _reduce_46(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[2], nil)

    result
end

def _reduce_47(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[1], nil)

    result
end

# reduce 48 omitted

# reduce 49 omitted

# reduce 50 omitted

# reduce 51 omitted

# reduce 52 omitted

def _reduce_53(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
end

def _reduce_54(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_55(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

# reduce 56 omitted

def _reduce_57(val, _values, result)
                      result = @builder.call_method(nil, nil, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_58(val, _values, result)
                      method_call = @builder.call_method(nil, nil, val[0],
                                        nil, val[1], nil)

                      begin_t, args, body, end_t = val[2]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_59(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
end

def _reduce_60(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                        nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_61(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
end

def _reduce_62(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                        nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_63(val, _values, result)
                      result = @builder.keyword_cmd(:super, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_64(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_65(val, _values, result)
                      result = @builder.keyword_cmd(:return, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_66(val, _values, result)
                      result = @builder.keyword_cmd(:break, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_67(val, _values, result)
                      result = @builder.keyword_cmd(:next, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_68(val, _values, result)
                      result = @builder.multi_lhs(nil, val[0], nil)

    result
end

def _reduce_69(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_70(val, _values, result)
                      result = @builder.multi_lhs(nil, val[0], nil)

    result
end

def _reduce_71(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

# reduce 72 omitted

def _reduce_73(val, _values, result)
                      result = val[0].
                                  push(val[1])

    result
end

def _reduce_74(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1], val[2]))

    result
end

def _reduce_75(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1], val[2])).
                                  concat(val[4])

    result
end

def _reduce_76(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1]))

    result
end

def _reduce_77(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1])).
                                  concat(val[3])

    result
end

def _reduce_78(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

def _reduce_79(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]),
                                 *val[3] ]

    result
end

def _reduce_80(val, _values, result)
                      result = [ @builder.splat(val[0]) ]

    result
end

def _reduce_81(val, _values, result)
                      result = [ @builder.splat(val[0]),
                                 *val[2] ]

    result
end

# reduce 82 omitted

def _reduce_83(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_84(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_85(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_86(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_87(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_88(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_89(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_90(val, _values, result)
                      result = @builder.index_asgn(val[0], val[1], val[2], val[3])

    result
end

def _reduce_91(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_92(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_93(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_94(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))

    result
end

def _reduce_95(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))

    result
end

def _reduce_96(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_97(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_98(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_99(val, _values, result)
                      result = @builder.index_asgn(val[0], val[1], val[2], val[3])

    result
end

def _reduce_100(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_101(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_102(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_103(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))

    result
end

def _reduce_104(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))

    result
end

def _reduce_105(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_106(val, _values, result)
                      diagnostic :error, :module_name_const, nil, val[0]

    result
end

# reduce 107 omitted

def _reduce_108(val, _values, result)
                      result = @builder.const_global(val[0], val[1])

    result
end

def _reduce_109(val, _values, result)
                      result = @builder.const(val[0])

    result
end

def _reduce_110(val, _values, result)
                      result = @builder.const_fetch(val[0], val[1], val[2])

    result
end

# reduce 111 omitted

# reduce 112 omitted

# reduce 113 omitted

# reduce 114 omitted

# reduce 115 omitted

def _reduce_116(val, _values, result)
                      result = @builder.symbol_internal(val[0])

    result
end

# reduce 117 omitted

# reduce 118 omitted

# reduce 119 omitted

def _reduce_120(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_121(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_122(val, _values, result)
                      result = val[0] << val[3]

    result
end

# reduce 123 omitted

# reduce 124 omitted

# reduce 125 omitted

# reduce 126 omitted

# reduce 127 omitted

# reduce 128 omitted

# reduce 129 omitted

# reduce 130 omitted

# reduce 131 omitted

# reduce 132 omitted

# reduce 133 omitted

# reduce 134 omitted

# reduce 135 omitted

# reduce 136 omitted

# reduce 137 omitted

# reduce 138 omitted

# reduce 139 omitted

# reduce 140 omitted

# reduce 141 omitted

# reduce 142 omitted

# reduce 143 omitted

# reduce 144 omitted

# reduce 145 omitted

# reduce 146 omitted

# reduce 147 omitted

# reduce 148 omitted

# reduce 149 omitted

# reduce 150 omitted

# reduce 151 omitted

# reduce 152 omitted

# reduce 153 omitted

# reduce 154 omitted

# reduce 155 omitted

# reduce 156 omitted

# reduce 157 omitted

# reduce 158 omitted

# reduce 159 omitted

# reduce 160 omitted

# reduce 161 omitted

# reduce 162 omitted

# reduce 163 omitted

# reduce 164 omitted

# reduce 165 omitted

# reduce 166 omitted

# reduce 167 omitted

# reduce 168 omitted

# reduce 169 omitted

# reduce 170 omitted

# reduce 171 omitted

# reduce 172 omitted

# reduce 173 omitted

# reduce 174 omitted

# reduce 175 omitted

# reduce 176 omitted

# reduce 177 omitted

# reduce 178 omitted

# reduce 179 omitted

# reduce 180 omitted

# reduce 181 omitted

# reduce 182 omitted

# reduce 183 omitted

# reduce 184 omitted

# reduce 185 omitted

# reduce 186 omitted

# reduce 187 omitted

# reduce 188 omitted

# reduce 189 omitted

# reduce 190 omitted

# reduce 191 omitted

# reduce 192 omitted

# reduce 193 omitted

def _reduce_194(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

def _reduce_195(val, _values, result)
                      rescue_body = @builder.rescue_body(val[3],
                                        nil, nil, nil,
                                        nil, val[4])

                      rescue_ = @builder.begin_body(val[2], [ rescue_body ])

                      result  = @builder.assign(val[0], val[1], rescue_)

    result
end

def _reduce_196(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_197(val, _values, result)
                      rescue_body = @builder.rescue_body(val[3],
                                        nil, nil, nil,
                                        nil, val[4])

                      rescue_ = @builder.begin_body(val[2], [ rescue_body ])

                      result = @builder.op_assign(val[0], val[1], rescue_)

    result
end

def _reduce_198(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])

    result
end

def _reduce_199(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_200(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_201(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_202(val, _values, result)
                      const  = @builder.const_op_assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))
                      result = @builder.op_assign(const, val[3], val[4])

    result
end

def _reduce_203(val, _values, result)
                      const  = @builder.const_op_assignable(
                                  @builder.const_global(val[0], val[1]))
                      result = @builder.op_assign(const, val[2], val[3])

    result
end

def _reduce_204(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_205(val, _values, result)
                      result = @builder.range_inclusive(val[0], val[1], val[2])

    result
end

def _reduce_206(val, _values, result)
                      result = @builder.range_exclusive(val[0], val[1], val[2])

    result
end

def _reduce_207(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_208(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_209(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_210(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_211(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_212(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_213(val, _values, result)
                      result = @builder.unary_op(val[0],
                                  @builder.binary_op(
                                    val[1], val[2], val[3]))

    result
end

def _reduce_214(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_215(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_216(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_217(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_218(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_219(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_220(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_221(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_222(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_223(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_224(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_225(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_226(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_227(val, _values, result)
                      result = @builder.match_op(val[0], val[1], val[2])

    result
end

def _reduce_228(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_229(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[1], nil)

    result
end

def _reduce_230(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_231(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_232(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_233(val, _values, result)
                      result = @builder.logical_op(:and, val[0], val[1], val[2])

    result
end

def _reduce_234(val, _values, result)
                      result = @builder.logical_op(:or, val[0], val[1], val[2])

    result
end

def _reduce_235(val, _values, result)
                      result = @builder.keyword_cmd(:defined?, val[0], nil, [ val[2] ], nil)

    result
end

def _reduce_236(val, _values, result)
                      result = @builder.ternary(val[0], val[1],
                                                val[2], val[4], val[5])

    result
end

# reduce 237 omitted

# reduce 238 omitted

# reduce 239 omitted

# reduce 240 omitted

def _reduce_241(val, _values, result)
                      result = val[0] << @builder.associate(nil, val[2], nil)

    result
end

def _reduce_242(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]

    result
end

def _reduce_243(val, _values, result)
                      result = val

    result
end

def _reduce_244(val, _values, result)
                      result = [ nil, [], nil ]

    result
end

# reduce 245 omitted

def _reduce_246(val, _values, result)
                      result = []

    result
end

# reduce 247 omitted

# reduce 248 omitted

def _reduce_249(val, _values, result)
                      result = val[0] << @builder.associate(nil, val[2], nil)

    result
end

def _reduce_250(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]

    result
end

def _reduce_251(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_252(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_253(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]
                      result.concat(val[1])

    result
end

def _reduce_254(val, _values, result)
                      assocs = @builder.associate(nil, val[2], nil)
                      result = val[0] << assocs
                      result.concat(val[3])

    result
end

def _reduce_255(val, _values, result)
                      result =  [ val[0] ]

    result
end

def _reduce_256(val, _values, result)
                      result = @lexer.cmdarg.dup
                      @lexer.cmdarg.push(true)

    result
end

def _reduce_257(val, _values, result)
                      @lexer.cmdarg = val[0]

                      result = val[1]

    result
end

def _reduce_258(val, _values, result)
                      result = @builder.block_pass(val[0], val[1])

    result
end

def _reduce_259(val, _values, result)
                      result = [ val[1] ]

    result
end

def _reduce_260(val, _values, result)
                      result = []

    result
end

def _reduce_261(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_262(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

def _reduce_263(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_264(val, _values, result)
                      result = val[0] << @builder.splat(val[2], val[3])

    result
end

def _reduce_265(val, _values, result)
                      result = @builder.array(nil, val[0], nil)

    result
end

# reduce 266 omitted

def _reduce_267(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_268(val, _values, result)
                      result = val[0] << @builder.splat(val[2], val[3])

    result
end

def _reduce_269(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

# reduce 270 omitted

# reduce 271 omitted

# reduce 272 omitted

# reduce 273 omitted

# reduce 274 omitted

# reduce 275 omitted

# reduce 276 omitted

# reduce 277 omitted

# reduce 278 omitted

# reduce 279 omitted

def _reduce_280(val, _values, result)
                      result = @builder.call_method(nil, nil, val[0])

    result
end

def _reduce_281(val, _values, result)
                      result = @lexer.cmdarg.dup
                      @lexer.cmdarg.clear

    result
end

def _reduce_282(val, _values, result)
                      @lexer.cmdarg = val[1]

                      result = @builder.begin_keyword(val[0], val[2], val[3])

    result
end

def _reduce_283(val, _values, result)
                      result = @lexer.cmdarg.dup
                      @lexer.cmdarg.clear

    result
end

def _reduce_284(val, _values, result)
                      @lexer.state = :expr_endarg

    result
end

def _reduce_285(val, _values, result)
                      @lexer.cmdarg = val[1]

                      result = @builder.begin(val[0], val[2], val[5])

    result
end

def _reduce_286(val, _values, result)
                      @lexer.state = :expr_endarg

    result
end

def _reduce_287(val, _values, result)
                      result = @builder.begin(val[0], nil, val[3])

    result
end

def _reduce_288(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_289(val, _values, result)
                      result = @builder.const_fetch(val[0], val[1], val[2])

    result
end

def _reduce_290(val, _values, result)
                      result = @builder.const_global(val[0], val[1])

    result
end

def _reduce_291(val, _values, result)
                      result = @builder.array(val[0], val[1], val[2])

    result
end

def _reduce_292(val, _values, result)
                      result = @builder.associate(val[0], val[1], val[2])

    result
end

def _reduce_293(val, _values, result)
                      result = @builder.keyword_cmd(:return, val[0])

    result
end

def _reduce_294(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0], val[1], val[2], val[3])

    result
end

def _reduce_295(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0], val[1], [], val[2])

    result
end

def _reduce_296(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0])

    result
end

def _reduce_297(val, _values, result)
                      result = @builder.keyword_cmd(:defined?, val[0],
                                                    val[2], [ val[3] ], val[4])

    result
end

def _reduce_298(val, _values, result)
                      result = @builder.not_op(val[0], val[1], val[2], val[3])

    result
end

def _reduce_299(val, _values, result)
                      result = @builder.not_op(val[0], val[1], nil, val[2])

    result
end

def _reduce_300(val, _values, result)
                      method_call = @builder.call_method(nil, nil, val[0])

                      begin_t, args, body, end_t = val[1]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

# reduce 301 omitted

def _reduce_302(val, _values, result)
                      begin_t, args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, args, body, end_t)

    result
end

def _reduce_303(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_304(val, _values, result)
                      lambda_call = @builder.call_lambda(val[0])

                      args, (begin_t, body, end_t) = val[2]
                      result      = @builder.block(lambda_call,
                                      begin_t, args, body, end_t)

                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_305(val, _values, result)
                      else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  val[3], else_t,
                                                  else_,  val[5])

    result
end

def _reduce_306(val, _values, result)
                      else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  else_,  else_t,
                                                  val[3], val[5])

    result
end

def _reduce_307(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_308(val, _values, result)
                      @lexer.cond.pop

    result
end

def _reduce_309(val, _values, result)
                      result = @builder.loop(:while, val[0], val[2], val[3],
                                             val[5], val[6])

    result
end

def _reduce_310(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_311(val, _values, result)
                      @lexer.cond.pop

    result
end

def _reduce_312(val, _values, result)
                      result = @builder.loop(:until, val[0], val[2], val[3],
                                             val[5], val[6])

    result
end

def _reduce_313(val, _values, result)
                      *when_bodies, (else_t, else_body) = *val[3]

                      result = @builder.case(val[0], val[1],
                                             when_bodies, else_t, else_body,
                                             val[4])

    result
end

def _reduce_314(val, _values, result)
                      *when_bodies, (else_t, else_body) = *val[2]

                      result = @builder.case(val[0], nil,
                                             when_bodies, else_t, else_body,
                                             val[3])

    result
end

def _reduce_315(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_316(val, _values, result)
                      @lexer.cond.pop

    result
end

def _reduce_317(val, _values, result)
                      result = @builder.for(val[0], val[1],
                                            val[2], val[4],
                                            val[5], val[7], val[8])

    result
end

def _reduce_318(val, _values, result)
                      local_push
                      @context.in_class = true

    result
end

def _reduce_319(val, _values, result)
                      k_class, ctx = val[0]
                      if @context.in_def
                        diagnostic :error, :class_in_def, nil, k_class
                      end

                      lt_t, superclass = val[2]
                      result = @builder.def_class(k_class, val[1],
                                                  lt_t, superclass,
                                                  val[4], val[5])

                      local_pop
                      @context.in_class = ctx.in_class

    result
end

def _reduce_320(val, _values, result)
                      @context.in_def = false
                      @context.in_class = false
                      local_push

    result
end

def _reduce_321(val, _values, result)
                      k_class, ctx = val[0]
                      result = @builder.def_sclass(k_class, val[1], val[2],
                                                   val[5], val[6])

                      local_pop
                      @context.in_def = ctx.in_def
                      @context.in_class = ctx.in_class

    result
end

def _reduce_322(val, _values, result)
                      @context.in_class = true
                      local_push

    result
end

def _reduce_323(val, _values, result)
                      k_mod, ctx = val[0]
                      if @context.in_def
                        diagnostic :error, :module_in_def, nil, k_mod
                      end

                      result = @builder.def_module(k_mod, val[1],
                                                   val[3], val[4])

                      local_pop
                      @context.in_class = ctx.in_class

    result
end

def _reduce_324(val, _values, result)
                      local_push
                      result = context.dup
                      @context.in_def = true

    result
end

def _reduce_325(val, _values, result)
                      result = @builder.def_method(val[0], val[1],
                                  val[3], val[4], val[5])

                      local_pop
                      @context.in_def = val[2].in_def

    result
end

def _reduce_326(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_327(val, _values, result)
                      local_push
                      result = context.dup
                      @context.in_def = true

    result
end

def _reduce_328(val, _values, result)
                      result = @builder.def_singleton(val[0], val[1], val[2],
                                  val[4], val[6], val[7], val[8])

                      local_pop
                      @context.in_def = val[5].in_def

    result
end

def _reduce_329(val, _values, result)
                      result = @builder.keyword_cmd(:break, val[0])

    result
end

def _reduce_330(val, _values, result)
                      result = @builder.keyword_cmd(:next, val[0])

    result
end

def _reduce_331(val, _values, result)
                      result = @builder.keyword_cmd(:redo, val[0])

    result
end

def _reduce_332(val, _values, result)
                      result = @builder.keyword_cmd(:retry, val[0])

    result
end

# reduce 333 omitted

def _reduce_334(val, _values, result)
                      result = [ val[0], @context.dup ]

    result
end

def _reduce_335(val, _values, result)
                      result = [ val[0], @context.dup ]

    result
end

# reduce 336 omitted

# reduce 337 omitted

def _reduce_338(val, _values, result)
                      result = val[1]

    result
end

# reduce 339 omitted

# reduce 340 omitted

# reduce 341 omitted

def _reduce_342(val, _values, result)
                      else_t, else_ = val[4]
                      result = [ val[0],
                                 @builder.condition(val[0], val[1], val[2],
                                                    val[3], else_t,
                                                    else_,  nil),
                               ]

    result
end

# reduce 343 omitted

def _reduce_344(val, _values, result)
                      result = val

    result
end

# reduce 345 omitted

# reduce 346 omitted

def _reduce_347(val, _values, result)
                      result = @builder.arg(val[0])

    result
end

def _reduce_348(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

def _reduce_349(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_350(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 351 omitted

def _reduce_352(val, _values, result)
                      result = val[0].
                                  push(@builder.restarg(val[2], val[3]))

    result
end

def _reduce_353(val, _values, result)
                      result = val[0].
                                  push(@builder.restarg(val[2], val[3])).
                                  concat(val[5])

    result
end

def _reduce_354(val, _values, result)
                      result = val[0].
                                  push(@builder.restarg(val[2]))

    result
end

def _reduce_355(val, _values, result)
                      result = val[0].
                                  push(@builder.restarg(val[2])).
                                  concat(val[4])

    result
end

def _reduce_356(val, _values, result)
                      result = [ @builder.restarg(val[0], val[1]) ]

    result
end

def _reduce_357(val, _values, result)
                      result = [ @builder.restarg(val[0], val[1]),
                                 *val[3] ]

    result
end

def _reduce_358(val, _values, result)
                      result = [ @builder.restarg(val[0]) ]

    result
end

def _reduce_359(val, _values, result)
                      result = [ @builder.restarg(val[0]),
                                 *val[2] ]

    result
end

def _reduce_360(val, _values, result)
                      result = val[0].concat(val[2]).concat(val[3])

    result
end

def _reduce_361(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_362(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_363(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_364(val, _values, result)
                      result = val[1]

    result
end

def _reduce_365(val, _values, result)
                      result = []

    result
end

def _reduce_366(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_367(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[6]).
                                  concat(val[7])

    result
end

def _reduce_368(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_369(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_370(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

# reduce 371 omitted

def _reduce_372(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_373(val, _values, result)
                      if val[1].empty? && val[0].size == 1
                        result = [@builder.procarg0(val[0][0])]
                      else
                        result = val[0].concat(val[1])
                      end

    result
end

def _reduce_374(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_375(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_376(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_377(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_378(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_379(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

# reduce 380 omitted

def _reduce_381(val, _values, result)
                      result = @builder.args(nil, [], nil)

    result
end

def _reduce_382(val, _values, result)
                      @lexer.state = :expr_value

    result
end

def _reduce_383(val, _values, result)
                      result = @builder.args(val[0], val[1], val[2])

    result
end

def _reduce_384(val, _values, result)
                      result = @builder.args(val[0], [], val[0])

    result
end

def _reduce_385(val, _values, result)
                      result = @builder.args(val[0], val[1].concat(val[2]), val[3])

    result
end

def _reduce_386(val, _values, result)
                      result = []

    result
end

def _reduce_387(val, _values, result)
                      result = val[2]

    result
end

def _reduce_388(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_389(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_390(val, _values, result)
                      @static_env.declare val[0][0]
                      result = @builder.shadowarg(val[0])

    result
end

# reduce 391 omitted

def _reduce_392(val, _values, result)
                      @static_env.extend_dynamic

    result
end

def _reduce_393(val, _values, result)
                      result = @lexer.cmdarg.dup
                      @lexer.cmdarg.clear

    result
end

def _reduce_394(val, _values, result)
                      @lexer.cmdarg = val[2]
                      @lexer.cmdarg.lexpop

                      result = [ val[1], val[3] ]

                      @static_env.unextend

    result
end

def _reduce_395(val, _values, result)
                      result = @builder.args(val[0], val[1].concat(val[2]), val[3])

    result
end

def _reduce_396(val, _values, result)
                      result = @builder.args(nil, val[0], nil)

    result
end

def _reduce_397(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_398(val, _values, result)
                      result = [ val[0], val[2], val[3] ]
                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_399(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_400(val, _values, result)
                      result = [ val[0], val[2], val[3] ]
                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_401(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_402(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_403(val, _values, result)
                      begin_t, block_args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, block_args, body, end_t)

    result
end

def _reduce_404(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_405(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                      lparen_t, args, rparen_t)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_406(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                      nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_407(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.call_method(nil, nil, val[0],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_408(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_409(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_410(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2])

    result
end

def _reduce_411(val, _values, result)
                      lparen_t, args, rparen_t = val[2]
                      result = @builder.call_method(val[0], val[1], nil,
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_412(val, _values, result)
                      lparen_t, args, rparen_t = val[2]
                      result = @builder.call_method(val[0], val[1], nil,
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_413(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.keyword_cmd(:super, val[0],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_414(val, _values, result)
                      result = @builder.keyword_cmd(:zsuper, val[0])

    result
end

def _reduce_415(val, _values, result)
                      result = @builder.index(val[0], val[1], val[2], val[3])

    result
end

def _reduce_416(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_417(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_418(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_419(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_420(val, _values, result)
                      result = [ @builder.when(val[0], val[1], val[2], val[3]),
                                 *val[4] ]

    result
end

def _reduce_421(val, _values, result)
                      result = [ val[0] ]

    result
end

# reduce 422 omitted

def _reduce_423(val, _values, result)
                      assoc_t, exc_var = val[2]

                      if val[1]
                        exc_list = @builder.array(nil, val[1], nil)
                      end

                      result = [ @builder.rescue_body(val[0],
                                      exc_list, assoc_t, exc_var,
                                      val[3], val[4]),
                                 *val[5] ]

    result
end

def _reduce_424(val, _values, result)
                      result = []

    result
end

def _reduce_425(val, _values, result)
                      result = [ val[0] ]

    result
end

# reduce 426 omitted

# reduce 427 omitted

def _reduce_428(val, _values, result)
                      result = [ val[0], val[1] ]

    result
end

# reduce 429 omitted

def _reduce_430(val, _values, result)
                      result = [ val[0], val[1] ]

    result
end

# reduce 431 omitted

# reduce 432 omitted

# reduce 433 omitted

# reduce 434 omitted

def _reduce_435(val, _values, result)
                      result = @builder.string_compose(nil, val[0], nil)

    result
end

def _reduce_436(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_437(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_438(val, _values, result)
                      result = @builder.string_compose(val[0], val[1], val[2])

    result
end

def _reduce_439(val, _values, result)
                      result = @builder.string(val[0])

    result
end

def _reduce_440(val, _values, result)
                      result = @builder.character(val[0])

    result
end

def _reduce_441(val, _values, result)
                      result = @builder.xstring_compose(val[0], val[1], val[2])

    result
end

def _reduce_442(val, _values, result)
                      opts   = @builder.regexp_options(val[3])
                      result = @builder.regexp_compose(val[0], val[1], val[2], opts)

    result
end

def _reduce_443(val, _values, result)
                      result = @builder.words_compose(val[0], val[1], val[2])

    result
end

def _reduce_444(val, _values, result)
                      result = []

    result
end

def _reduce_445(val, _values, result)
                      result = val[0] << @builder.word(val[1])

    result
end

def _reduce_446(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_447(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_448(val, _values, result)
                      result = @builder.symbols_compose(val[0], val[1], val[2])

    result
end

def _reduce_449(val, _values, result)
                      result = []

    result
end

def _reduce_450(val, _values, result)
                      result = val[0] << @builder.word(val[1])

    result
end

def _reduce_451(val, _values, result)
                      result = @builder.words_compose(val[0], val[1], val[2])

    result
end

def _reduce_452(val, _values, result)
                      result = @builder.symbols_compose(val[0], val[1], val[2])

    result
end

def _reduce_453(val, _values, result)
                      result = []

    result
end

def _reduce_454(val, _values, result)
                      result = val[0] << @builder.string_internal(val[1])

    result
end

def _reduce_455(val, _values, result)
                      result = []

    result
end

def _reduce_456(val, _values, result)
                      result = val[0] << @builder.symbol_internal(val[1])

    result
end

def _reduce_457(val, _values, result)
                      result = []

    result
end

def _reduce_458(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_459(val, _values, result)
                      result = []

    result
end

def _reduce_460(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_461(val, _values, result)
                      result = []

    result
end

def _reduce_462(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_463(val, _values, result)
                      result = @builder.string_internal(val[0])

    result
end

def _reduce_464(val, _values, result)
                      result = val[1]

    result
end

def _reduce_465(val, _values, result)
                      @lexer.cond.push(false)
                      @lexer.cmdarg.push(false)

    result
end

def _reduce_466(val, _values, result)
                      @lexer.cond.lexpop
                      @lexer.cmdarg.lexpop

                      result = @builder.begin(val[0], val[2], val[3])

    result
end

def _reduce_467(val, _values, result)
                      result = @builder.gvar(val[0])

    result
end

def _reduce_468(val, _values, result)
                      result = @builder.ivar(val[0])

    result
end

def _reduce_469(val, _values, result)
                      result = @builder.cvar(val[0])

    result
end

# reduce 470 omitted

def _reduce_471(val, _values, result)
                      result = @builder.symbol(val[0])

    result
end

def _reduce_472(val, _values, result)
                      result = @builder.symbol_compose(val[0], val[1], val[2])

    result
end

def _reduce_473(val, _values, result)
                      result = val[0]

    result
end

def _reduce_474(val, _values, result)
                      if @builder.respond_to? :negate
                        # AST builder interface compatibility
                        result = @builder.negate(val[0], val[1])
                      else
                        result = @builder.unary_num(val[0], val[1])
                      end

    result
end

def _reduce_475(val, _values, result)
                      result = @builder.integer(val[0])

    result
end

def _reduce_476(val, _values, result)
                      result = @builder.float(val[0])

    result
end

def _reduce_477(val, _values, result)
                      result = @builder.rational(val[0])

    result
end

def _reduce_478(val, _values, result)
                      result = @builder.complex(val[0])

    result
end

def _reduce_479(val, _values, result)
                      result = @builder.ident(val[0])

    result
end

def _reduce_480(val, _values, result)
                      result = @builder.ivar(val[0])

    result
end

def _reduce_481(val, _values, result)
                      result = @builder.gvar(val[0])

    result
end

def _reduce_482(val, _values, result)
                      result = @builder.const(val[0])

    result
end

def _reduce_483(val, _values, result)
                      result = @builder.cvar(val[0])

    result
end

def _reduce_484(val, _values, result)
                      result = @builder.nil(val[0])

    result
end

def _reduce_485(val, _values, result)
                      result = @builder.self(val[0])

    result
end

def _reduce_486(val, _values, result)
                      result = @builder.true(val[0])

    result
end

def _reduce_487(val, _values, result)
                      result = @builder.false(val[0])

    result
end

def _reduce_488(val, _values, result)
                      result = @builder.__FILE__(val[0])

    result
end

def _reduce_489(val, _values, result)
                      result = @builder.__LINE__(val[0])

    result
end

def _reduce_490(val, _values, result)
                      result = @builder.__ENCODING__(val[0])

    result
end

def _reduce_491(val, _values, result)
                      result = @builder.accessible(val[0])

    result
end

def _reduce_492(val, _values, result)
                      result = @builder.accessible(val[0])

    result
end

def _reduce_493(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_494(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_495(val, _values, result)
                      result = @builder.nth_ref(val[0])

    result
end

def _reduce_496(val, _values, result)
                      result = @builder.back_ref(val[0])

    result
end

def _reduce_497(val, _values, result)
                      result = nil

    result
end

def _reduce_498(val, _values, result)
                      @lexer.state = :expr_value

    result
end

def _reduce_499(val, _values, result)
                      result = [ val[0], val[2] ]

    result
end

def _reduce_500(val, _values, result)
                      yyerrok
                      result = nil

    result
end

def _reduce_501(val, _values, result)
                      result = @builder.args(val[0], val[1], val[2])

                      @lexer.state = :expr_value

    result
end

def _reduce_502(val, _values, result)
                      result = @context.in_kwarg
                      @context.in_kwarg = true

    result
end

def _reduce_503(val, _values, result)
                      @context.in_kwarg = val[0]
                      result = @builder.args(nil, val[1], nil)

    result
end

def _reduce_504(val, _values, result)
                      result = val[0].concat(val[2]).concat(val[3])

    result
end

def _reduce_505(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_506(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_507(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_508(val, _values, result)
                      result = val[1]

    result
end

def _reduce_509(val, _values, result)
                      result = []

    result
end

def _reduce_510(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_511(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[6]).
                                  concat(val[7])

    result
end

def _reduce_512(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_513(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_514(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_515(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_516(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_517(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_518(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_519(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_520(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_521(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_522(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_523(val, _values, result)
                      result = val[0]

    result
end

def _reduce_524(val, _values, result)
                      result = []

    result
end

def _reduce_525(val, _values, result)
                      diagnostic :error, :argument_const, nil, val[0]

    result
end

def _reduce_526(val, _values, result)
                      diagnostic :error, :argument_ivar, nil, val[0]

    result
end

def _reduce_527(val, _values, result)
                      diagnostic :error, :argument_gvar, nil, val[0]

    result
end

def _reduce_528(val, _values, result)
                      diagnostic :error, :argument_cvar, nil, val[0]

    result
end

# reduce 529 omitted

def _reduce_530(val, _values, result)
                      @static_env.declare val[0][0]

                      result = val[0]

    result
end

def _reduce_531(val, _values, result)
                      result = val[0]

    result
end

def _reduce_532(val, _values, result)
                      result = @builder.arg(val[0])

    result
end

def _reduce_533(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

def _reduce_534(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_535(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_536(val, _values, result)
                      check_kwarg_name(val[0])

                      @static_env.declare val[0][0]

                      result = val[0]

    result
end

def _reduce_537(val, _values, result)
                      result = @builder.kwoptarg(val[0], val[1])

    result
end

def _reduce_538(val, _values, result)
                      result = @builder.kwarg(val[0])

    result
end

def _reduce_539(val, _values, result)
                      result = @builder.kwoptarg(val[0], val[1])

    result
end

def _reduce_540(val, _values, result)
                      result = @builder.kwarg(val[0])

    result
end

def _reduce_541(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_542(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_543(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_544(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 545 omitted

# reduce 546 omitted

def _reduce_547(val, _values, result)
                      @static_env.declare val[1][0]

                      result = [ @builder.kwrestarg(val[0], val[1]) ]

    result
end

def _reduce_548(val, _values, result)
                      result = [ @builder.kwrestarg(val[0]) ]

    result
end

def _reduce_549(val, _values, result)
                      result = @builder.optarg(val[0], val[1], val[2])

    result
end

def _reduce_550(val, _values, result)
                      result = @builder.optarg(val[0], val[1], val[2])

    result
end

def _reduce_551(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_552(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_553(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_554(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 555 omitted

# reduce 556 omitted

def _reduce_557(val, _values, result)
                      @static_env.declare val[1][0]

                      result = [ @builder.restarg(val[0], val[1]) ]

    result
end

def _reduce_558(val, _values, result)
                      result = [ @builder.restarg(val[0]) ]

    result
end

# reduce 559 omitted

# reduce 560 omitted

def _reduce_561(val, _values, result)
                      @static_env.declare val[1][0]

                      result = @builder.blockarg(val[0], val[1])

    result
end

def _reduce_562(val, _values, result)
                      result = [ val[1] ]

    result
end

def _reduce_563(val, _values, result)
                      result = []

    result
end

# reduce 564 omitted

def _reduce_565(val, _values, result)
                      result = val[1]

    result
end

def _reduce_566(val, _values, result)
                      result = []

    result
end

# reduce 567 omitted

def _reduce_568(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_569(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_570(val, _values, result)
                      result = @builder.pair(val[0], val[1], val[2])

    result
end

def _reduce_571(val, _values, result)
                      result = @builder.pair_keyword(val[0], val[1])

    result
end

def _reduce_572(val, _values, result)
                      result = @builder.pair_quoted(val[0], val[1], val[2], val[3])

    result
end

def _reduce_573(val, _values, result)
                      result = @builder.kwsplat(val[0], val[1])

    result
end

# reduce 574 omitted

# reduce 575 omitted

# reduce 576 omitted

# reduce 577 omitted

# reduce 578 omitted

# reduce 579 omitted

# reduce 580 omitted

# reduce 581 omitted

# reduce 582 omitted

# reduce 583 omitted

# reduce 584 omitted

# reduce 585 omitted

# reduce 586 omitted

# reduce 587 omitted

# reduce 588 omitted

# reduce 589 omitted

def _reduce_590(val, _values, result)
                      result = val[1]

    result
end

def _reduce_591(val, _values, result)
                      result = val[1]

    result
end

# reduce 592 omitted

# reduce 593 omitted

# reduce 594 omitted

def _reduce_595(val, _values, result)
                    yyerrok

    result
end

# reduce 596 omitted

# reduce 597 omitted

# reduce 598 omitted

def _reduce_599(val, _values, result)
                    result = nil

    result
end

def _reduce_none(val, _values, result)
  val[0]
end

  end   # class Ruby22
end   # module Parser
