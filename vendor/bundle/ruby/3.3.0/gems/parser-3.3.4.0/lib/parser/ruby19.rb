# -*- encoding:utf-8; warn-indent:false; frozen_string_literal: true  -*-
#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.7.3
# from Racc grammar file "ruby19.y".
#

require 'racc/parser.rb'


require_relative '../parser'

module Parser
  class Ruby19 < Parser::Base


  def version
    19
  end

  def default_encoding
    Encoding::BINARY
  end

  def local_push
    @static_env.extend_static
    @lexer.cmdarg.push(false)
    @lexer.cond.push(false)
  end

  def local_pop
    @static_env.unextend
    @lexer.cmdarg.pop
    @lexer.cond.pop
  end
##### State transition tables begin ###

racc_action_table = [
  -455,     5,    71,    72,    68,     9,    54,  -455,  -455,  -455,
    60,    61,  -455,  -455,  -455,    64,  -455,    62,    63,    65,
    29,    30,    69,    70,  -455,   260,  -455,  -455,  -455,    28,
    27,    26,    95,    94,    96,    97,  -455,  -455,    19,  -455,
  -455,  -455,  -455,  -455,     8,    44,     7,    10,    99,    98,
   100,    89,    53,    91,    90,    92,   543,    93,   101,   102,
   522,    87,    88,    41,    42,    40,  -455,  -455,  -455,  -455,
  -455,  -455,  -455,  -455,  -455,  -455,  -455,  -455,  -455,  -455,
   103,   210,  -455,  -455,  -455,    39,  -455,  -455,    32,   -96,
  -455,    55,    56,  -455,  -455,    57,  -455,    34,  -455,   564,
  -455,    43,  -455,  -455,  -455,  -455,  -455,  -455,  -455,    20,
  -455,   259,  -455,   -97,    86,    78,    81,    82,   211,    83,
    84,   636,  -104,   528,    79,    85,  -455,  -455,  -455,  -455,
  -458,  -455,    59,  -455,    80,  -455,  -103,  -458,  -458,  -458,
  -469,   205,  -458,  -458,  -458,   671,  -458,   534,   522,   -90,
   115,   535,   522,   252,  -458,   231,  -458,  -458,  -458,   760,
   253,   542,   563,   207,   208,   -99,  -458,  -458,   671,  -458,
  -458,  -458,  -458,  -458,   107,   522,   522,   -99,  -101,   106,
  -101,   107,   207,   208,   -95,   -98,   106,   228,   107,   -96,
  -104,   230,   229,   106,   -87,   -98,  -458,  -458,  -458,  -458,
  -458,  -458,  -458,  -458,  -458,  -458,  -458,  -458,  -458,  -458,
   599,   199,  -458,  -458,  -458,   -90,  -458,  -458,   -88,   200,
  -458,  -100,   -92,  -458,  -458,  -470,  -458,   -95,  -458,   201,
  -458,   671,  -458,  -458,  -458,  -458,  -458,  -458,  -458,  -281,
  -458,   -94,  -458,   206,   598,   107,  -281,  -281,  -281,   599,
   106,   -90,  -281,  -281,   -90,  -281,  -458,  -458,  -458,  -458,
   -90,  -458,   107,  -458,   759,  -458,   107,   106,   107,   785,
   -90,   106,   107,   106,   -97,  -281,  -281,   106,  -281,  -281,
  -281,  -281,  -281,   598,   250,   -92,   260,  -100,   -92,   107,
   107,   -99,  -101,  -102,   106,   106,   -99,  -101,  -529,   -98,
   -89,   306,   307,   304,   -98,  -281,  -281,  -281,  -281,  -281,
  -281,  -281,  -281,  -281,  -281,  -281,  -281,  -281,  -281,   207,
   208,  -281,  -281,  -281,   -92,   582,  -469,   -92,   305,  -281,
   429,   107,  -281,   -92,   499,  -100,   106,  -281,   211,  -281,
  -100,  -281,  -281,  -281,  -281,  -281,  -281,  -281,  -533,  -281,
   255,  -281,  -529,   207,   208,  -533,  -533,  -533,  -530,   207,
   208,  -533,  -533,   724,  -533,  -281,  -281,   599,   -93,   -87,
  -281,   260,   259,  -533,  -102,   -96,   599,  -399,   512,  -104,
   -82,   511,   372,  -103,  -533,  -533,   -68,  -533,  -533,  -533,
  -533,  -533,   -91,   774,   385,   652,   651,   650,   -93,   653,
   774,   598,   652,   651,   650,   428,   653,   430,   107,   211,
   598,   431,   -99,   106,  -533,  -533,  -533,  -533,  -533,  -533,
  -533,  -533,  -533,  -533,  -533,  -533,  -533,  -533,   210,  -399,
  -533,  -533,  -533,   553,   583,   255,  -399,  -455,  -533,   725,
   463,  -533,   803,   211,  -455,  -399,  -533,  -470,  -533,   211,
  -533,  -533,  -533,  -533,  -533,  -533,  -533,   259,  -533,  -533,
  -533,   553,   499,  -399,   472,  -458,   474,  -526,  -527,  -465,
   476,   846,  -458,  -533,  -533,  -533,  -465,   -91,   928,  -533,
  -533,  -533,  -533,  -100,   -67,  -533,  -533,  -533,  -101,  -533,
   -88,   -98,   494,   495,   555,   554,   -97,  -464,  -533,  -533,
  -533,  -533,   382,   703,  -464,   -94,  -533,   384,   383,  -533,
  -533,  -103,  -533,  -533,  -533,  -533,  -533,   211,   512,  -455,
  -458,   514,   555,   554,   551,   553,  -455,  -458,   553,   107,
  -466,  -526,  -527,   553,   106,  -455,  -458,  -466,   484,  -533,
  -533,  -533,  -533,  -533,  -533,  -533,  -533,  -533,  -533,  -533,
  -533,  -533,  -533,  -526,  -527,  -533,  -533,  -533,  -533,   726,
  -533,   553,   485,  -533,   577,  -533,  -533,  -533,  -533,  -533,
  -529,  -533,   492,  -533,  -533,  -533,  -533,  -533,  -533,  -533,
  -533,  -533,   264,  -533,  -533,  -533,   555,   554,   556,   555,
   554,   558,  -533,   211,   555,   554,   560,   255,   578,  -533,
  -533,  -533,  -533,  -281,  -533,   496,  -533,   -90,  -100,   710,
  -281,  -281,  -281,   -99,   500,  -281,  -281,  -281,   501,  -281,
  -533,   203,   555,   554,   565,   231,  -463,  -533,   204,  -281,
  -281,  -281,  -529,  -463,   231,   512,  -533,   202,   514,  -281,
  -281,   -92,  -281,  -281,  -281,  -281,  -281,  -101,  -460,  -325,
   207,   208,   -89,  -461,  -533,  -460,  -325,   228,   -98,  -462,
  -461,   230,   229,   226,   227,  -325,  -462,   207,   208,  -281,
  -281,  -281,  -281,  -281,  -281,  -281,  -281,  -281,  -281,  -281,
  -281,  -281,  -281,    78,   425,  -281,  -281,  -281,  -467,   727,
  -281,   426,    79,  -281,   507,  -467,  -281,  -281,   508,  -281,
   427,  -281,    80,  -281,  -467,  -281,  -281,  -281,  -281,  -281,
  -281,  -281,   515,  -281,   231,  -281,   516,   655,   548,   652,
   651,   650,   107,   653,   476,   549,   528,   106,   107,  -281,
  -281,  -281,  -281,   106,  -281,   374,  -281,   532,  -102,   280,
    71,    72,    68,     9,    54,   533,   228,   107,    60,    61,
   230,   229,   106,    64,   657,    62,    63,    65,    29,    30,
    69,    70,   566,   661,   660,   664,   663,    28,    27,    26,
    95,    94,    96,    97,   697,   698,    19,   569,   699,   101,
   102,   587,     8,    44,  -260,    10,    99,    98,   100,    89,
    53,    91,    90,    92,   571,    93,   101,   102,   211,    87,
    88,    41,    42,    40,   231,   235,   240,   241,   242,   237,
   239,   247,   248,   243,   244,  -468,   224,   225,   490,   575,
   245,   246,  -468,    39,   512,   491,   282,   514,   576,    55,
    56,  -468,   255,    57,   489,    34,   228,   586,   234,    43,
   230,   229,   226,   227,   238,   236,   232,    20,   233,   780,
   636,   589,    86,    78,    81,    82,   231,    83,    84,   231,
   231,  -274,    79,    85,   231,   249,  -275,  -237,  -274,   231,
    59,   211,    80,  -275,  -275,  -275,   211,  -274,  -275,  -275,
  -275,   737,  -275,   652,   651,   650,   211,   653,   780,   636,
   -82,   228,  -275,  -275,  -275,   230,   229,   226,   227,   618,
   211,   505,  -275,  -275,   629,  -275,  -275,  -275,  -275,  -275,
   774,   636,   652,   651,   650,   502,   653,   774,   657,   652,
   651,   650,   503,   653,   667,   528,   674,   661,   660,   664,
   663,   427,  -275,  -275,  -275,  -275,  -275,  -275,  -275,  -275,
  -275,  -275,  -275,  -275,  -275,  -275,   702,   770,  -275,  -275,
  -275,   530,   705,  -275,   770,  -261,  -275,   711,   531,  -275,
  -275,   736,  -275,   773,  -275,   463,  -275,   529,  -275,  -275,
  -275,  -275,  -275,  -275,  -275,   538,  -275,   463,  -275,   211,
  -282,   729,   537,   474,   476,   629,   211,  -282,   255,   255,
   629,   539,  -275,  -275,  -275,  -275,  -282,  -275,   231,  -275,
   280,    71,    72,    68,     9,    54,   231,   750,  -282,    60,
    61,  -260,   754,   211,    64,  -282,    62,    63,    65,    29,
    30,    69,    70,   762,  -282,   764,   767,   768,    28,    27,
    26,    95,    94,    96,    97,   776,   815,    19,   652,   651,
   650,   777,   653,     8,    44,   636,    10,    99,    98,   100,
    89,    53,    91,    90,    92,   784,    93,   101,   102,   211,
    87,    88,    41,    42,    40,   774,   211,   652,   651,   650,
   793,   653,  -262,   774,   802,   652,   651,   650,   805,   653,
   807,   810,   811,   736,    39,   817,   818,    32,   820,   821,
    55,    56,   629,   830,    57,   774,    34,   652,   651,   650,
    43,   653,   770,   655,   736,   652,   651,   650,    20,   653,
   657,   773,   844,    86,    78,    81,    82,   211,    83,    84,
   848,   664,   663,    79,    85,     5,    71,    72,    68,     9,
    54,    59,   770,    80,    60,    61,   850,   856,   858,    64,
   657,    62,    63,    65,    29,    30,    69,    70,   211,   661,
   660,   664,   663,    28,    27,    26,    95,    94,    96,    97,
   861,   815,    19,   652,   651,   650,  -263,   653,     8,    44,
     7,    10,    99,    98,   100,    89,    53,    91,    90,    92,
   868,    93,   101,   102,   869,    87,    88,    41,    42,    40,
   774,   872,   652,   651,   650,   877,   653,   211,   774,   881,
   652,   651,   650,   884,   653,   886,   888,   888,   211,    39,
   893,   896,    32,   897,   902,    55,    56,   904,   907,    57,
   774,    34,   652,   651,   650,    43,   653,   770,   655,   909,
   652,   651,   650,    20,   653,   657,   892,   888,    86,    78,
    81,    82,   888,    83,    84,   914,   664,   663,    79,    85,
   280,    71,    72,    68,     9,    54,    59,   770,    80,    60,
    61,   507,   921,   922,    64,   657,    62,    63,    65,    29,
    30,    69,    70,   930,   661,   660,   664,   663,    28,    27,
    26,    95,    94,    96,    97,   211,   942,    19,   110,   111,
   112,   113,   114,     8,    44,   888,    10,    99,    98,   100,
    89,    53,    91,    90,    92,   888,    93,   101,   102,   888,
    87,    88,    41,    42,    40,   774,   946,   652,   651,   650,
   930,   653,   949,   774,   950,   652,   651,   650,   952,   653,
   888,   888,   888,  -530,    39,  -529,   930,    32,   888,   930,
    55,    56,   888,   nil,    57,   774,    34,   652,   651,   650,
    43,   653,   770,   655,  -467,   652,   651,   650,    20,   653,
   657,  -467,   nil,    86,    78,    81,    82,   nil,    83,    84,
  -467,   664,   663,    79,    85,   280,    71,    72,    68,     9,
    54,    59,   770,    80,    60,    61,   nil,   nil,   nil,    64,
   657,    62,    63,    65,    29,    30,    69,    70,   nil,   661,
   660,   664,   663,    28,    27,    26,    95,    94,    96,    97,
   nil,   nil,    19,   110,   111,   112,   113,   114,     8,    44,
   nil,    10,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,  -468,   110,   111,   112,   113,   114,   774,  -468,
   652,   651,   650,   nil,   653,   nil,  -281,   875,  -468,    39,
  -274,   nil,    32,  -281,   876,    55,    56,  -274,  -530,    57,
   538,    34,  -281,   874,   nil,    43,  -274,   913,   737,   nil,
   652,   651,   650,    20,   653,   657,   539,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   664,   663,    79,    85,
   280,    71,    72,    68,     9,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   657,    62,    63,    65,    29,
    30,    69,    70,   nil,   661,   660,   664,   663,    28,    27,
    26,    95,    94,    96,    97,   nil,   nil,    19,   110,   111,
   112,   113,   114,     8,    44,   nil,    10,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   nil,   nil,  -282,   nil,   nil,
   nil,   nil,   nil,   774,  -282,   652,   651,   650,   nil,   653,
   nil,  -281,   nil,  -282,    39,   nil,   nil,   282,  -281,   nil,
    55,    56,   nil,  -530,    57,   nil,    34,  -281,   nil,   nil,
    43,   nil,   nil,   737,   nil,   652,   651,   650,    20,   653,
   657,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   664,   663,    79,    85,   280,    71,    72,    68,     9,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   657,    62,    63,    65,    29,    30,    69,    70,   nil,   661,
   660,   664,   663,    28,    27,    26,    95,    94,    96,    97,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   572,     8,    44,
   nil,    10,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   231,   235,   240,   241,   242,   237,   239,   247,   248,   243,
   244,  -281,   224,   225,   nil,   nil,   245,   246,  -281,    39,
   nil,   nil,   282,  -530,   nil,    55,    56,  -281,   nil,    57,
   nil,    34,   228,   nil,   234,    43,   230,   229,   226,   227,
   238,   236,   232,    20,   233,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,   249,  -534,   nil,   nil,   nil,    59,   nil,    80,  -534,
  -534,  -534,   nil,   nil,  -534,  -534,  -534,   655,  -534,   652,
   651,   650,   nil,   653,   nil,   nil,   nil,  -534,  -534,  -534,
  -534,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -534,  -534,
   nil,  -534,  -534,  -534,  -534,  -534,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   657,   644,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   661,   660,   664,   663,   nil,  -534,  -534,
  -534,  -534,  -534,  -534,  -534,  -534,  -534,  -534,  -534,  -534,
  -534,  -534,   nil,   nil,  -534,  -534,  -534,   nil,   nil,  -534,
   nil,   nil,  -534,   nil,   nil,  -534,  -534,   nil,  -534,   nil,
  -534,   nil,  -534,   nil,  -534,  -534,  -534,  -534,  -534,  -534,
  -534,  -535,  -534,  -534,  -534,   nil,   nil,   nil,  -535,  -535,
  -535,   nil,   nil,  -535,  -535,  -535,   231,  -535,  -534,  -534,
  -534,  -534,   nil,  -534,   nil,  -534,  -535,  -535,  -535,  -535,
   nil,   nil,   245,   246,   nil,   nil,   nil,  -535,  -535,   nil,
  -535,  -535,  -535,  -535,  -535,   nil,   nil,   nil,   228,   nil,
   234,   nil,   230,   229,   226,   227,   nil,   nil,   232,   nil,
   233,   nil,   nil,   nil,   nil,   nil,   nil,  -535,  -535,  -535,
  -535,  -535,  -535,  -535,  -535,  -535,  -535,  -535,  -535,  -535,
  -535,   nil,   nil,  -535,  -535,  -535,   nil,   nil,  -535,   nil,
   nil,  -535,   nil,   nil,  -535,  -535,   nil,  -535,   nil,  -535,
   nil,  -535,   nil,  -535,  -535,  -535,  -535,  -535,  -535,  -535,
   nil,  -535,  -535,  -535,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -535,  -535,  -535,
  -535,   nil,  -535,   nil,  -535,   280,    71,    72,    68,     9,
    54,   nil,   nil,   nil,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,    29,    30,    69,    70,   nil,   nil,
   nil,   nil,   nil,    28,    27,    26,    95,    94,    96,    97,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    44,
   nil,    10,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   774,   nil,
   652,   651,   650,   nil,   653,   nil,   nil,   nil,   nil,    39,
   nil,   nil,    32,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,    34,   nil,   nil,   nil,    43,   nil,   nil,   655,   nil,
   652,   651,   650,    20,   653,   657,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   664,   663,    79,    85,
   280,    71,    72,    68,     9,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   657,    62,    63,    65,    29,
    30,    69,    70,   nil,   661,   660,   664,   663,    28,    27,
    26,    95,    94,    96,    97,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,     8,    44,   nil,    10,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   774,   nil,   652,   651,   650,   nil,   653,
   nil,   nil,   nil,   nil,    39,   nil,   nil,    32,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,    34,   nil,   nil,   nil,
    43,   nil,   nil,   737,   nil,   652,   651,   650,    20,   653,
   657,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   664,   663,    79,    85,   280,    71,    72,    68,     9,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   657,    62,    63,    65,    29,    30,    69,    70,   nil,   661,
   660,   664,   663,    28,    27,    26,    95,    94,    96,    97,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    44,
   nil,    10,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   774,   nil,   652,
   651,   650,   nil,   653,   nil,   nil,   nil,   nil,   nil,    39,
   nil,   nil,    32,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,    34,   nil,   nil,   nil,    43,   774,   nil,   652,   651,
   650,   nil,   653,    20,   657,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   664,   663,   nil,    79,    85,
   280,    71,    72,    68,     9,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   657,    64,   nil,    62,    63,    65,    29,
    30,    69,    70,   nil,   664,   663,   nil,   nil,    28,    27,
    26,    95,    94,    96,    97,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,     8,    44,   nil,    10,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   231,   235,   240,   241,   242,
   237,   239,   247,   248,   243,   244,   nil,  -552,  -552,   nil,
   nil,   245,   246,   nil,    39,   nil,   nil,    32,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,    34,   228,   nil,   234,
    43,   230,   229,   226,   227,   238,   236,   232,    20,   233,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   280,    71,    72,    68,     9,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,    29,    30,    69,    70,   nil,   nil,
   nil,   nil,   nil,    28,    27,    26,    95,    94,    96,    97,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    44,
   nil,    10,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   231,   235,   240,   241,   242,   237,   239,   247,   248,   243,
   244,   nil,  -552,  -552,   nil,   nil,   245,   246,   nil,    39,
   nil,   nil,    32,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,    34,   228,   nil,   234,    43,   230,   229,   226,   227,
   238,   236,   232,    20,   233,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   280,    71,    72,    68,     9,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,
    30,    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,
    26,    95,    94,    96,    97,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,     8,    44,   nil,    10,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   231,  -552,  -552,  -552,  -552,
   237,   239,   nil,   nil,  -552,  -552,   nil,   nil,   nil,   nil,
   nil,   245,   246,   nil,    39,   nil,   nil,    32,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,    34,   228,   nil,   234,
    43,   230,   229,   226,   227,   238,   236,   232,    20,   233,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   280,    71,    72,    68,     9,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,    29,    30,    69,    70,   nil,   nil,
   nil,   nil,   nil,    28,    27,    26,    95,    94,    96,    97,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    44,
   nil,    10,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   231,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   245,   246,   nil,    39,
   nil,   nil,    32,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,    34,   228,   nil,   234,    43,   230,   229,   226,   227,
   nil,   nil,   232,    20,   233,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   280,    71,    72,    68,     9,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,
    30,    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,
    26,    95,    94,    96,    97,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,     8,    44,   nil,    10,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   231,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   245,   246,   nil,    39,   nil,   nil,    32,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,    34,   228,   nil,   234,
    43,   230,   229,   226,   227,   nil,   nil,   232,    20,   233,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   280,    71,    72,    68,     9,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,    29,    30,    69,    70,   nil,   nil,
   nil,   nil,   nil,    28,    27,    26,    95,    94,    96,    97,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    44,
   nil,    10,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   231,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   245,   246,   nil,    39,
   nil,   nil,    32,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,    34,   228,   nil,   234,    43,   230,   229,   226,   227,
   nil,   nil,   232,    20,   233,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   280,    71,    72,    68,     9,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,
    30,    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,
    26,    95,    94,    96,    97,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,     8,    44,   nil,    10,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   231,  -552,  -552,  -552,  -552,
   237,   239,   nil,   nil,  -552,  -552,   nil,   nil,   nil,   nil,
   nil,   245,   246,   nil,    39,   nil,   nil,    32,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,    34,   228,   nil,   234,
    43,   230,   229,   226,   227,   238,   236,   232,    20,   233,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   280,    71,    72,    68,     9,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,    29,    30,    69,    70,   nil,   nil,
   nil,   nil,   nil,    28,    27,    26,    95,    94,    96,    97,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    44,
   nil,    10,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   231,  -552,  -552,  -552,  -552,   237,   239,   nil,   nil,  -552,
  -552,   nil,   nil,   nil,   nil,   nil,   245,   246,   nil,    39,
   nil,   nil,    32,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,    34,   228,   nil,   234,    43,   230,   229,   226,   227,
   238,   236,   232,    20,   233,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   280,    71,    72,    68,     9,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,
    30,    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,
    26,    95,    94,    96,    97,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,     8,    44,   nil,    10,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   231,  -552,  -552,  -552,  -552,
   237,   239,   nil,   nil,  -552,  -552,   nil,   nil,   nil,   nil,
   nil,   245,   246,   nil,    39,   nil,   nil,    32,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,    34,   228,   nil,   234,
    43,   230,   229,   226,   227,   238,   236,   232,    20,   233,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   280,    71,    72,    68,     9,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,    29,    30,    69,    70,   nil,   nil,
   nil,   nil,   nil,    28,    27,    26,    95,    94,    96,    97,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    44,
   nil,    10,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   231,  -552,  -552,  -552,  -552,   237,   239,   nil,   nil,  -552,
  -552,   nil,   nil,   nil,   nil,   nil,   245,   246,   nil,    39,
   nil,   nil,    32,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,    34,   228,   nil,   234,    43,   230,   229,   226,   227,
   238,   236,   232,    20,   233,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   280,    71,    72,    68,     9,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,
    30,    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,
    26,    95,    94,    96,    97,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,     8,    44,   nil,    10,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   231,  -552,  -552,  -552,  -552,
   237,   239,   nil,   nil,  -552,  -552,   nil,   nil,   nil,   nil,
   nil,   245,   246,   nil,    39,   nil,   nil,    32,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,    34,   228,   nil,   234,
    43,   230,   229,   226,   227,   238,   236,   232,    20,   233,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   280,    71,    72,    68,     9,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,    29,    30,    69,    70,   nil,   nil,
   nil,   nil,   nil,    28,    27,    26,    95,    94,    96,    97,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    44,
   nil,    10,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   231,   235,   240,   241,   242,   237,   239,   nil,   nil,   243,
   244,   nil,   nil,   nil,   nil,   nil,   245,   246,   nil,    39,
   nil,   nil,    32,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,    34,   228,   nil,   234,    43,   230,   229,   226,   227,
   238,   236,   232,    20,   233,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   280,    71,    72,    68,     9,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,
    30,    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,
    26,    95,    94,    96,    97,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,     8,    44,   nil,    10,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   231,   235,   240,   241,   242,
   237,   239,   247,   nil,   243,   244,   nil,   nil,   nil,   nil,
   nil,   245,   246,   nil,    39,   nil,   nil,    32,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,    34,   228,   nil,   234,
    43,   230,   229,   226,   227,   238,   236,   232,    20,   233,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   280,    71,    72,    68,     9,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,    29,    30,    69,    70,   nil,   nil,
   nil,   nil,   nil,    28,    27,    26,    95,    94,    96,    97,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    44,
   nil,    10,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   231,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   245,   246,   nil,    39,
   nil,   nil,    32,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,    34,   228,   nil,   234,    43,   230,   229,   226,   227,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   280,    71,    72,    68,     9,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,
    30,    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,
    26,    95,    94,    96,    97,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,     8,    44,   nil,    10,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   231,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   245,   246,   nil,    39,   nil,   nil,    32,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,    34,   228,   nil,   234,
    43,   230,   229,   226,   227,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   280,    71,    72,    68,     9,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,    29,    30,    69,    70,   nil,   nil,
   nil,   nil,   nil,    28,    27,    26,    95,    94,    96,    97,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    44,
   nil,    10,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   231,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   245,   246,   nil,    39,
   nil,   nil,    32,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,    34,   228,   nil,   nil,    43,   230,   229,   226,   227,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   280,    71,    72,    68,     9,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,
    30,    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,
    26,    95,    94,    96,    97,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,     8,    44,   nil,    10,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    39,   nil,   nil,    32,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,    34,   nil,   nil,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   280,    71,    72,    68,     9,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,    29,    30,    69,    70,   nil,   nil,
   nil,   nil,   nil,    28,    27,    26,    95,    94,    96,    97,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    44,
   nil,    10,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    39,
   nil,   nil,    32,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,    34,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   280,    71,    72,    68,     9,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,
    30,    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,
    26,    95,    94,    96,    97,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,     8,    44,   nil,    10,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    39,   nil,   nil,    32,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,    34,   nil,   nil,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,     9,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,    29,    30,    69,    70,   nil,   nil,
   nil,   nil,   nil,    28,    27,    26,    95,    94,    96,    97,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    44,
     7,    10,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    39,
   nil,   nil,    32,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,    34,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,
    30,    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,
    26,    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,    29,    30,    69,    70,   nil,   nil,
   nil,   nil,   nil,    28,    27,    26,    95,    94,    96,    97,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   275,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   273,   nil,   271,   nil,    43,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,
    30,    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,
    26,    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,
    89,    53,    91,    90,    92,   275,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,   273,   nil,   271,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,    29,    30,    69,    70,   nil,   nil,
   nil,   nil,   nil,    28,    27,    26,    95,    94,    96,    97,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   275,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   273,   nil,   271,   nil,    43,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,
   298,    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,
   300,    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,
   nil,   nil,   nil,   nil,   295,   nil,   nil,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,   nil,   655,   301,   652,   651,   650,   nil,   653,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   291,   nil,   nil,   287,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,   286,   nil,   nil,   nil,
   657,   692,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   661,
   660,   664,   663,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,   297,   298,    69,    70,   nil,   nil,
   nil,   nil,   nil,   293,   294,   300,    95,    94,    96,    97,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   587,   nil,   295,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,   nil,   nil,   301,
   231,   235,   240,   241,   242,   237,   239,   247,   248,   243,
   244,   nil,   224,   225,   nil,   nil,   245,   246,   nil,   291,
   nil,   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   nil,   228,   nil,   234,   nil,   230,   229,   226,   227,
   238,   236,   232,   nil,   233,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,   249,   nil,   303,   nil,   nil,    59,   nil,    80,    71,
    72,    68,   nil,    54,   nil,   nil,   nil,    60,    61,   nil,
   nil,   nil,    64,   nil,    62,    63,    65,   297,   298,    69,
    70,   nil,   nil,   nil,   nil,   nil,   293,   294,   300,    95,
    94,    96,    97,   nil,   nil,   223,   nil,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,    53,
    91,    90,    92,   nil,    93,   101,   102,   nil,    87,    88,
    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,    56,
   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,   nil,
   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,   nil,
   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,    59,
   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,    62,
    63,    65,   297,   298,    69,    70,   nil,   nil,   nil,   nil,
   nil,   293,   294,   300,    95,    94,    96,    97,   nil,   nil,
   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
    99,    98,   100,    89,    53,    91,    90,    92,   nil,    93,
   101,   102,   nil,    87,    88,    41,    42,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,   nil,
   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,   nil,
   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   221,   nil,   nil,   nil,   nil,    86,    78,    81,    82,
   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,    71,
    72,    68,   nil,    54,    59,   nil,    80,    60,    61,   nil,
   nil,   nil,    64,   nil,    62,    63,    65,   297,   298,    69,
    70,   nil,   nil,   nil,   nil,   nil,   293,   294,   300,    95,
    94,    96,    97,   nil,   nil,   223,   nil,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,    53,
    91,    90,    92,   nil,    93,   101,   102,   nil,    87,    88,
    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,    56,
   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,   nil,
   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,   nil,
   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,    59,
   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,    62,
    63,    65,   297,   298,    69,    70,   nil,   nil,   nil,   nil,
   nil,   293,   294,   300,    95,    94,    96,    97,   nil,   nil,
   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
    99,    98,   100,    89,    53,    91,    90,    92,   275,    93,
   101,   102,   nil,    87,    88,    41,    42,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,   nil,
   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,   273,
   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   221,   nil,   nil,   nil,   nil,    86,    78,    81,    82,
   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,    71,
    72,    68,   nil,    54,    59,   nil,    80,    60,    61,   nil,
   nil,   nil,    64,   nil,    62,    63,    65,   297,   298,    69,
    70,   nil,   nil,   nil,   nil,   nil,   293,   294,   300,    95,
    94,    96,    97,   nil,   nil,   223,   nil,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,    53,
    91,    90,    92,   275,    93,   101,   102,   nil,    87,    88,
    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,    56,
   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,   nil,
   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,   nil,
   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,    59,
   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,    62,
    63,    65,    29,    30,    69,    70,   nil,   nil,   nil,   nil,
   nil,    28,    27,    26,    95,    94,    96,    97,   nil,   nil,
    19,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
    99,    98,   100,    89,    53,    91,    90,    92,   nil,    93,
   101,   102,   nil,    87,    88,    41,    42,    40,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,   nil,
   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,   nil,
   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    20,   nil,   nil,   nil,   nil,    86,    78,    81,    82,
   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,    71,
    72,    68,   nil,    54,    59,   nil,    80,    60,    61,   nil,
   nil,   nil,    64,   nil,    62,    63,    65,    29,    30,    69,
    70,   nil,   nil,   nil,   nil,   nil,    28,    27,    26,    95,
    94,    96,    97,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,    53,
    91,    90,    92,   nil,    93,   101,   102,   nil,    87,    88,
    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,    56,
   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,
   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,   nil,
   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,    59,
   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,    62,
    63,    65,    29,    30,    69,    70,   nil,   nil,   nil,   nil,
   nil,    28,    27,    26,    95,    94,    96,    97,   nil,   nil,
    19,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
    99,    98,   100,    89,    53,    91,    90,    92,   nil,    93,
   101,   102,   nil,    87,    88,    41,    42,    40,   231,   235,
   240,   241,   242,   237,   239,   247,   248,   243,   244,   nil,
   224,   225,   nil,   nil,   245,   246,   nil,   216,   nil,   nil,
   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,   nil,
   228,   nil,   234,    43,   230,   229,   226,   227,   238,   236,
   232,    20,   233,   nil,   nil,   nil,    86,    78,    81,    82,
   nil,    83,    84,   nil,   nil,   nil,    79,    85,   107,   249,
   nil,   nil,   nil,   106,    59,   nil,    80,    71,    72,    68,
   nil,    54,   nil,   nil,   nil,    60,    61,   nil,   nil,   nil,
    64,   nil,    62,    63,    65,   297,   298,    69,    70,   nil,
   nil,   nil,   nil,   nil,   293,   294,   300,    95,    94,    96,
    97,   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,
   295,   nil,   nil,    99,    98,   100,    89,    53,    91,    90,
    92,   nil,    93,   101,   102,   nil,    87,    88,   nil,   655,
   301,   652,   651,   650,   nil,   653,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   336,   nil,   nil,    32,   nil,   nil,    55,    56,   nil,   nil,
    57,   nil,    34,   nil,   nil,   nil,   657,   692,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   661,   660,   664,   663,    86,
    78,    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,
    85,   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,
    60,    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,
   297,   298,    69,    70,   nil,   nil,   nil,   nil,   nil,   293,
   294,   300,    95,    94,    96,    97,   nil,   nil,   223,   nil,
   nil,   nil,   nil,   nil,   nil,   295,   nil,   nil,    99,    98,
   100,   341,    53,    91,    90,   342,   nil,    93,   101,   102,
   nil,    87,    88,   nil,   nil,   301,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   348,   nil,   nil,   343,   nil,   nil,   222,   nil,
   nil,    55,    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,
    84,   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,
   nil,    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,
    64,   nil,    62,    63,    65,   297,   298,    69,    70,   nil,
   nil,   nil,   nil,   nil,   293,   294,   300,    95,    94,    96,
    97,   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,
   295,   nil,   nil,    99,    98,   100,   341,    53,    91,    90,
   342,   nil,    93,   101,   102,   nil,    87,    88,   nil,   nil,
   301,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   343,   nil,   nil,   222,   nil,   nil,    55,    56,   nil,   nil,
    57,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    86,
    78,    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,
    85,   nil,    71,    72,    68,     9,    54,    59,   nil,    80,
    60,    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,
    29,    30,    69,    70,   nil,   nil,   nil,   nil,   nil,    28,
    27,    26,    95,    94,    96,    97,   nil,   nil,    19,   nil,
   nil,   nil,   nil,   nil,     8,    44,     7,    10,    99,    98,
   100,    89,    53,    91,    90,    92,   nil,    93,   101,   102,
   nil,    87,    88,    41,    42,    40,   231,   235,   240,   241,
   242,   237,   239,   247,   248,   243,   244,   nil,   224,   225,
   nil,   nil,   245,   246,   nil,    39,   nil,   nil,    32,   nil,
   nil,    55,    56,   nil,   nil,    57,   nil,    34,   228,   nil,
   234,    43,   230,   229,   226,   227,   238,   236,   232,    20,
   233,   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,
    84,   nil,   nil,   nil,    79,    85,   nil,   249,   nil,   nil,
   nil,   374,    59,   nil,    80,    71,    72,    68,   nil,    54,
   nil,   nil,   nil,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,    29,    30,    69,    70,   nil,   nil,   nil,
   nil,   nil,    28,    27,    26,    95,    94,    96,    97,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    99,    98,   100,    89,    53,    91,    90,    92,   nil,
    93,   101,   102,   nil,    87,    88,    41,    42,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    20,   nil,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
    71,    72,    68,   nil,    54,    59,   nil,    80,    60,    61,
   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,    30,
    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,    26,
    95,    94,    96,    97,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,
    53,    91,    90,    92,   nil,    93,   101,   102,   nil,    87,
    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,
    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,
   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,
    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,    29,    30,    69,    70,   nil,   nil,   nil,
   nil,   nil,    28,    27,    26,    95,    94,    96,    97,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    99,    98,   100,    89,    53,    91,    90,    92,   nil,
    93,   101,   102,   nil,    87,    88,    41,    42,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    20,   nil,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
    71,    72,    68,   nil,    54,    59,   nil,    80,    60,    61,
   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,    30,
    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,    26,
    95,    94,    96,    97,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,
    53,    91,    90,    92,   nil,    93,   101,   102,   nil,    87,
    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,
    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,
   nil,   nil,    79,    85,   nil,    71,    72,    68,     9,    54,
    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,    29,    30,    69,    70,   nil,   nil,   nil,
   nil,   nil,    28,    27,    26,    95,    94,    96,    97,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    44,   nil,
    10,    99,    98,   100,    89,    53,    91,    90,    92,   nil,
    93,   101,   102,   nil,    87,    88,    41,    42,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    39,   nil,
   nil,    32,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
    34,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    20,   nil,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
    71,    72,    68,   nil,    54,    59,   nil,    80,    60,    61,
   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,    30,
    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,    26,
    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,
    53,    91,    90,    92,   nil,    93,   101,   102,   nil,    87,
    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,
    56,   nil,   nil,    57,   nil,   390,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,
   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,
   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,
    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,    29,    30,    69,    70,   nil,   nil,   nil,
   nil,   nil,    28,    27,    26,    95,    94,    96,    97,   nil,
   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    99,    98,   100,    89,    53,    91,    90,    92,   nil,
    93,   101,   102,   nil,    87,    88,    41,    42,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
    71,    72,    68,   nil,    54,    59,   nil,    80,    60,    61,
   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,    30,
    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,    26,
    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,
    53,    91,    90,    92,   275,    93,   101,   102,   nil,    87,
    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,
    56,   nil,   nil,    57,   nil,   273,   nil,   271,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,
   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,
   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,
    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,    29,    30,    69,    70,   nil,   nil,   nil,
   nil,   nil,    28,    27,    26,    95,    94,    96,    97,   nil,
   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    99,    98,   100,    89,    53,    91,    90,    92,   nil,
    93,   101,   102,   nil,    87,    88,    41,    42,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
    71,    72,    68,   nil,    54,    59,   nil,    80,    60,    61,
   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,    30,
    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,    26,
    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,
    53,    91,    90,    92,   nil,    93,   101,   102,   nil,    87,
    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,
    56,   nil,   nil,    57,   nil,   390,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,
   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,
   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,
    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,    29,    30,    69,    70,   nil,   nil,   nil,
   nil,   nil,    28,    27,    26,    95,    94,    96,    97,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    99,    98,   100,    89,    53,    91,    90,    92,   nil,
    93,   101,   102,   nil,    87,    88,    41,    42,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    20,   nil,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
    71,    72,    68,   nil,    54,    59,   nil,    80,    60,    61,
   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,    30,
    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,    26,
    95,    94,    96,    97,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,
    53,    91,    90,    92,   nil,    93,   101,   102,   nil,    87,
    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,
    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,
   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,
    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,    29,    30,    69,    70,   nil,   nil,   nil,
   nil,   nil,    28,    27,    26,    95,    94,    96,    97,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    99,    98,   100,    89,    53,    91,    90,    92,   nil,
    93,   101,   102,   nil,    87,    88,    41,    42,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    20,   nil,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
    71,    72,    68,   nil,    54,    59,   nil,    80,    60,    61,
   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,    30,
    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,    26,
    95,    94,    96,    97,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,
    53,    91,    90,    92,   nil,    93,   101,   102,   nil,    87,
    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,
    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,
   nil,   nil,    79,    85,   211,    71,    72,    68,   nil,    54,
    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,   297,   298,    69,    70,   nil,   nil,   nil,
   nil,   nil,   293,   294,   300,    95,    94,    96,    97,   nil,
   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    99,    98,   100,    89,    53,    91,    90,    92,   nil,
    93,   101,   102,   nil,    87,    88,    41,    42,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
    71,    72,    68,   nil,    54,    59,   nil,    80,    60,    61,
   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,   298,
    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,   300,
    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,
    53,    91,    90,    92,   nil,    93,   101,   102,   nil,    87,
    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,
    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,
   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,
   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,
    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,   297,   298,    69,    70,   nil,   nil,   nil,
   nil,   nil,   293,   294,   300,    95,    94,    96,    97,   nil,
   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    99,    98,   100,    89,    53,    91,    90,    92,   nil,
    93,   101,   102,   nil,    87,    88,    41,    42,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
    71,    72,    68,   nil,    54,    59,   nil,    80,    60,    61,
   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,   298,
    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,   300,
    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,
    53,    91,    90,    92,   nil,    93,   101,   102,   nil,    87,
    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,
    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,
   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,
   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,
    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,   297,   298,    69,    70,   nil,   nil,   nil,
   nil,   nil,   293,   294,   300,    95,    94,    96,    97,   nil,
   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    99,    98,   100,    89,    53,    91,    90,    92,   nil,
    93,   101,   102,   nil,    87,    88,    41,    42,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
    71,    72,    68,   nil,    54,    59,   nil,    80,    60,    61,
   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,   298,
    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,   300,
    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,
    53,    91,    90,    92,   nil,    93,   101,   102,   nil,    87,
    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,
    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,
   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,
   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,
    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,   297,   298,    69,    70,   nil,   nil,   nil,
   nil,   nil,   293,   294,   300,    95,    94,    96,    97,   nil,
   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    99,    98,   100,    89,    53,    91,    90,    92,   nil,
    93,   101,   102,   nil,    87,    88,    41,    42,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
    71,    72,    68,   nil,    54,    59,   nil,    80,    60,    61,
   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,   298,
    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,   300,
    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,
    53,    91,    90,    92,   nil,    93,   101,   102,   nil,    87,
    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,
    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,
   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,
   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,
    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,   297,   298,    69,    70,   nil,   nil,   nil,
   nil,   nil,   293,   294,   300,    95,    94,    96,    97,   nil,
   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    99,    98,   100,    89,    53,    91,    90,    92,   nil,
    93,   101,   102,   nil,    87,    88,    41,    42,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
    71,    72,    68,   nil,    54,    59,   nil,    80,    60,    61,
   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,   298,
    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,   300,
    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,
    53,    91,    90,    92,   nil,    93,   101,   102,   nil,    87,
    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,
    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,
   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,
   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,
    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,   297,   298,    69,    70,   nil,   nil,   nil,
   nil,   nil,   293,   294,   300,    95,    94,    96,    97,   nil,
   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    99,    98,   100,    89,    53,    91,    90,    92,   nil,
    93,   101,   102,   nil,    87,    88,    41,    42,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
    71,    72,    68,   nil,    54,    59,   nil,    80,    60,    61,
   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,   298,
    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,   300,
    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,
    53,    91,    90,    92,   nil,    93,   101,   102,   nil,    87,
    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,
    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,
   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,
   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,
    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,   297,   298,    69,    70,   nil,   nil,   nil,
   nil,   nil,   293,   294,   300,    95,    94,    96,    97,   nil,
   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    99,    98,   100,    89,    53,    91,    90,    92,   nil,
    93,   101,   102,   nil,    87,    88,    41,    42,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
    71,    72,    68,   nil,    54,    59,   nil,    80,    60,    61,
   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,   298,
    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,   300,
    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,
    53,    91,    90,    92,   nil,    93,   101,   102,   nil,    87,
    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,
    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,
   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,
   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,
    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,   297,   298,    69,    70,   nil,   nil,   nil,
   nil,   nil,   293,   294,   300,    95,    94,    96,    97,   nil,
   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    99,    98,   100,    89,    53,    91,    90,    92,   nil,
    93,   101,   102,   nil,    87,    88,    41,    42,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
    71,    72,    68,   nil,    54,    59,   nil,    80,    60,    61,
   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,   298,
    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,   300,
    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,
    53,    91,    90,    92,   nil,    93,   101,   102,   nil,    87,
    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,
    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,
   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,
   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,
    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,   297,   298,    69,    70,   nil,   nil,   nil,
   nil,   nil,   293,   294,   300,    95,    94,    96,    97,   nil,
   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    99,    98,   100,    89,    53,    91,    90,    92,   nil,
    93,   101,   102,   nil,    87,    88,    41,    42,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
    71,    72,    68,   nil,    54,    59,   nil,    80,    60,    61,
   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,   298,
    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,   300,
    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,
    53,    91,    90,    92,   nil,    93,   101,   102,   nil,    87,
    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,
    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,
   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,
   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,
    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,   297,   298,    69,    70,   nil,   nil,   nil,
   nil,   nil,   293,   294,   300,    95,    94,    96,    97,   nil,
   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    99,    98,   100,    89,    53,    91,    90,    92,   nil,
    93,   101,   102,   nil,    87,    88,    41,    42,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
    71,    72,    68,   nil,    54,    59,   nil,    80,    60,    61,
   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,   298,
    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,   300,
    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,
    53,    91,    90,    92,   nil,    93,   101,   102,   nil,    87,
    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,
    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,
   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,
   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,
    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,   297,   298,    69,    70,   nil,   nil,   nil,
   nil,   nil,   293,   294,   300,    95,    94,    96,    97,   nil,
   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    99,    98,   100,    89,    53,    91,    90,    92,   nil,
    93,   101,   102,   nil,    87,    88,    41,    42,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
    71,    72,    68,   nil,    54,    59,   nil,    80,    60,    61,
   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,   298,
    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,   300,
    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,
    53,    91,    90,    92,   nil,    93,   101,   102,   nil,    87,
    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,
    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,
   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,
   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,
    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,   297,   298,    69,    70,   nil,   nil,   nil,
   nil,   nil,   293,   294,   300,    95,    94,    96,    97,   nil,
   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    99,    98,   100,    89,    53,    91,    90,    92,   nil,
    93,   101,   102,   nil,    87,    88,    41,    42,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
    71,    72,    68,   nil,    54,    59,   nil,    80,    60,    61,
   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,   298,
    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,   300,
    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,
    53,    91,    90,    92,   nil,    93,   101,   102,   nil,    87,
    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,
    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,
   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,
   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,
    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,   297,   298,    69,    70,   nil,   nil,   nil,
   nil,   nil,   293,   294,   300,    95,    94,    96,    97,   nil,
   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    99,    98,   100,    89,    53,    91,    90,    92,   nil,
    93,   101,   102,   nil,    87,    88,    41,    42,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
    71,    72,    68,   nil,    54,    59,   nil,    80,    60,    61,
   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,   298,
    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,   300,
    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,
    53,    91,    90,    92,   nil,    93,   101,   102,   nil,    87,
    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,
    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,
   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,
   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,
    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,   297,   298,    69,    70,   nil,   nil,   nil,
   nil,   nil,   293,   294,   300,    95,    94,    96,    97,   nil,
   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    99,    98,   100,    89,    53,    91,    90,    92,   nil,
    93,   101,   102,   nil,    87,    88,    41,    42,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
    71,    72,    68,   nil,    54,    59,   nil,    80,    60,    61,
   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,    30,
    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,    26,
    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,
    53,    91,    90,    92,   275,    93,   101,   102,   nil,    87,
    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,
    56,   nil,   nil,    57,   nil,   273,   nil,   271,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,
   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,
   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,
    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,    29,    30,    69,    70,   nil,   nil,   nil,
   nil,   nil,    28,    27,    26,    95,    94,    96,    97,   nil,
   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    99,    98,   100,    89,    53,    91,    90,    92,   275,
    93,   101,   102,   nil,    87,    88,    41,    42,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
   273,   nil,   271,   nil,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
    71,    72,    68,   nil,    54,    59,   nil,    80,    60,    61,
   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,    30,
    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,    26,
    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,
    53,    91,    90,    92,   275,    93,   101,   102,   nil,    87,
    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,
    56,   nil,   nil,    57,   nil,   273,   nil,   271,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,
   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,
   nil,   nil,    79,    85,   211,    71,    72,    68,   nil,    54,
    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,   297,   298,    69,    70,   nil,   nil,   nil,
   nil,   nil,   293,   294,   300,    95,    94,    96,    97,   nil,
   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    99,    98,   100,    89,    53,    91,    90,    92,   nil,
    93,   101,   102,   nil,    87,    88,    41,    42,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
    71,    72,    68,   nil,    54,    59,   nil,    80,    60,    61,
   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,   298,
    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,   300,
    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,    89,
    53,    91,    90,    92,   nil,    93,   101,   102,   nil,    87,
    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,    55,
    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,
   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,
   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,
    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,   297,   298,    69,    70,   nil,   nil,   nil,
   nil,   nil,   293,   294,   300,    95,    94,    96,    97,   nil,
   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    99,    98,   100,    89,    53,    91,    90,    92,   nil,
    93,   101,   102,   nil,    87,    88,    41,    42,    40,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
    71,    72,    68,     9,    54,    59,   nil,    80,    60,    61,
   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,    30,
    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,    26,
    95,    94,    96,    97,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,     8,    44,   nil,    10,    99,    98,   100,    89,
    53,    91,    90,    92,   nil,    93,   101,   102,   nil,    87,
    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    39,   nil,   nil,    32,   nil,   nil,    55,
    56,   nil,   nil,    57,   nil,    34,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,   nil,
   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,    54,
    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,   nil,
    62,    63,    65,   297,   298,    69,    70,   nil,   nil,   nil,
   nil,   nil,   293,   294,   300,    95,    94,    96,    97,   nil,
   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,   295,   nil,
   nil,    99,    98,   100,    89,    53,    91,    90,    92,   nil,
    93,   101,   102,   nil,    87,    88,   nil,   nil,   301,   231,
   235,   240,   241,   242,   237,   239,   247,   248,   243,   244,
   nil,   224,   225,   nil,   nil,   245,   246,   nil,   291,   nil,
   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,   nil,
   nil,   228,   nil,   234,   nil,   230,   229,   226,   227,   238,
   236,   232,   nil,   233,   nil,   nil,   nil,    86,    78,    81,
    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,   nil,
   249,   nil,   487,   nil,   nil,    59,   nil,    80,    71,    72,
    68,   nil,    54,   nil,   nil,   nil,    60,    61,   nil,   nil,
   nil,    64,   nil,    62,    63,    65,   297,   298,    69,    70,
   nil,   nil,   nil,   nil,   nil,   293,   294,   300,    95,    94,
    96,    97,   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,
   nil,   295,   nil,   nil,    99,    98,   100,    89,    53,    91,
    90,    92,   nil,    93,   101,   102,   nil,    87,    88,   nil,
   nil,   301,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   291,   nil,   nil,   287,   nil,   nil,    55,    56,   nil,
   nil,    57,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    86,    78,    81,    82,   nil,    83,    84,   nil,   nil,   nil,
    79,    85,   nil,    71,    72,    68,   nil,    54,    59,   nil,
    80,    60,    61,   nil,   nil,   nil,    64,   nil,    62,    63,
    65,   297,   298,    69,    70,   nil,   nil,   nil,   nil,   nil,
   293,   294,   300,    95,    94,    96,    97,   nil,   nil,   223,
   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,
    98,   100,    89,    53,    91,    90,    92,   nil,    93,   101,
   102,   nil,    87,    88,    41,    42,    40,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,
   505,   nil,    55,    56,   nil,   nil,    57,   nil,   nil,   nil,
   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   221,   nil,   nil,   nil,   nil,    86,    78,    81,    82,   nil,
    83,    84,   nil,   nil,   nil,    79,    85,   nil,    71,    72,
    68,   nil,    54,    59,   nil,    80,    60,    61,   nil,   nil,
   nil,    64,   nil,    62,    63,    65,    29,    30,    69,    70,
   nil,   nil,   nil,   nil,   nil,    28,    27,    26,    95,    94,
    96,    97,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,    99,    98,   100,    89,    53,    91,
    90,    92,   nil,    93,   101,   102,   nil,    87,    88,    41,
    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   216,   nil,   nil,   222,   nil,   nil,    55,    56,   nil,
   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,
    86,    78,    81,    82,   nil,    83,    84,   nil,   nil,   nil,
    79,    85,   nil,    71,    72,    68,   nil,    54,    59,   nil,
    80,    60,    61,   nil,   nil,   nil,    64,   nil,    62,    63,
    65,    29,    30,    69,    70,   nil,   nil,   nil,   nil,   nil,
    28,    27,    26,    95,    94,    96,    97,   nil,   nil,    19,
   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,
    98,   100,    89,    53,    91,    90,    92,   nil,    93,   101,
   102,   nil,    87,    88,    41,    42,    40,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,
   nil,   nil,    55,    56,   nil,   nil,    57,   nil,   nil,   nil,
   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    20,   nil,   nil,   nil,   nil,    86,    78,    81,    82,   nil,
    83,    84,   nil,   nil,   nil,    79,    85,   nil,    71,    72,
    68,   nil,    54,    59,   nil,    80,    60,    61,   nil,   nil,
   nil,    64,   nil,    62,    63,    65,    29,    30,    69,    70,
   nil,   nil,   nil,   nil,   nil,    28,    27,    26,    95,    94,
    96,    97,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,    99,    98,   100,    89,    53,    91,
    90,    92,   nil,    93,   101,   102,   nil,    87,    88,    41,
    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   216,   nil,   nil,   222,   nil,   nil,    55,    56,   nil,
   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,
    86,    78,    81,    82,   nil,    83,    84,   nil,   nil,   nil,
    79,    85,   nil,    71,    72,    68,   nil,    54,    59,   nil,
    80,    60,    61,   nil,   nil,   nil,    64,   nil,    62,    63,
    65,    29,    30,    69,    70,   nil,   nil,   nil,   nil,   nil,
    28,    27,    26,    95,    94,    96,    97,   nil,   nil,    19,
   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,
    98,   100,    89,    53,    91,    90,    92,   nil,    93,   101,
   102,   nil,    87,    88,    41,    42,    40,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,
   nil,   nil,    55,    56,   nil,   nil,    57,   nil,   nil,   nil,
   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    20,   nil,   nil,   nil,   nil,    86,    78,    81,    82,   nil,
    83,    84,   nil,   nil,   nil,    79,    85,   nil,    71,    72,
    68,   nil,    54,    59,   nil,    80,    60,    61,   nil,   nil,
   nil,    64,   nil,    62,    63,    65,    29,    30,    69,    70,
   nil,   nil,   nil,   nil,   nil,    28,    27,    26,    95,    94,
    96,    97,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,    99,    98,   100,    89,    53,    91,
    90,    92,   nil,    93,   101,   102,   nil,    87,    88,    41,
    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   216,   nil,   nil,   222,   nil,   nil,    55,    56,   nil,
   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,
    86,    78,    81,    82,   nil,    83,    84,   nil,   nil,   nil,
    79,    85,   nil,    71,    72,    68,   nil,    54,    59,   nil,
    80,    60,    61,   nil,   nil,   nil,    64,   nil,    62,    63,
    65,   297,   298,    69,    70,   nil,   nil,   nil,   nil,   nil,
   293,   294,   300,    95,    94,    96,    97,   nil,   nil,   223,
   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,
    98,   100,    89,    53,    91,    90,    92,   nil,    93,   101,
   102,   nil,    87,    88,    41,    42,    40,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,
   nil,   nil,    55,    56,   nil,   nil,    57,   nil,   nil,   nil,
   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   221,   nil,   nil,   nil,   nil,    86,    78,    81,    82,   nil,
    83,    84,   nil,   nil,   nil,    79,    85,   nil,    71,    72,
    68,   nil,    54,    59,   nil,    80,    60,    61,   nil,   nil,
   nil,    64,   nil,    62,    63,    65,    29,    30,    69,    70,
   nil,   nil,   nil,   nil,   nil,    28,    27,    26,    95,    94,
    96,    97,   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,    99,    98,   100,    89,    53,    91,
    90,    92,   275,    93,   101,   102,   nil,    87,    88,    41,
    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   216,   nil,   nil,   222,   nil,   nil,    55,    56,   nil,
   nil,    57,   nil,   273,   nil,   271,   nil,    43,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,
    86,    78,    81,    82,   nil,    83,    84,   nil,   nil,   nil,
    79,    85,   nil,    71,    72,    68,   nil,    54,    59,   nil,
    80,    60,    61,   nil,   nil,   nil,    64,   nil,    62,    63,
    65,   297,   298,    69,    70,   nil,   nil,   nil,   nil,   nil,
   293,   294,   300,    95,    94,    96,    97,   nil,   nil,   223,
   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,
    98,   100,    89,    53,    91,    90,    92,   nil,    93,   101,
   102,   nil,    87,    88,    41,    42,    40,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,
   nil,   nil,    55,    56,   nil,   nil,    57,   nil,   nil,   nil,
   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   221,   nil,   nil,   nil,   nil,    86,    78,    81,    82,   nil,
    83,    84,   nil,   nil,   nil,    79,    85,   nil,    71,    72,
    68,   nil,    54,    59,   nil,    80,    60,    61,   nil,   nil,
   nil,    64,   nil,    62,    63,    65,   297,   298,    69,    70,
   nil,   nil,   nil,   nil,   nil,   293,   294,   300,    95,    94,
    96,    97,   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,    99,    98,   100,    89,    53,    91,
    90,    92,   nil,    93,   101,   102,   nil,    87,    88,    41,
    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   216,   nil,   nil,   222,   nil,   nil,    55,    56,   nil,
   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,
    86,    78,    81,    82,   nil,    83,    84,   nil,   nil,   nil,
    79,    85,   nil,    71,    72,    68,   nil,    54,    59,   nil,
    80,    60,    61,   nil,   nil,   nil,    64,   nil,    62,    63,
    65,   297,   298,    69,    70,   nil,   nil,   nil,   nil,   nil,
   293,   294,   300,    95,    94,    96,    97,   nil,   nil,   223,
   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,
    98,   100,    89,    53,    91,    90,    92,   nil,    93,   101,
   102,   nil,    87,    88,    41,    42,    40,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,
   nil,   nil,    55,    56,   nil,   nil,    57,   nil,   nil,   nil,
   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   221,   nil,   nil,   nil,   nil,    86,    78,    81,    82,   nil,
    83,    84,   nil,   nil,   nil,    79,    85,   nil,    71,    72,
    68,   nil,    54,    59,   nil,    80,    60,    61,   nil,   nil,
   nil,    64,   nil,    62,    63,    65,   297,   298,    69,    70,
   nil,   nil,   nil,   nil,   nil,   293,   294,   300,    95,    94,
    96,    97,   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,    99,    98,   100,    89,    53,    91,
    90,    92,   275,    93,   101,   102,   nil,    87,    88,    41,
    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   216,   nil,   nil,   222,   nil,   nil,    55,    56,   nil,
   nil,    57,   nil,   613,   nil,   271,   nil,    43,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,
    86,    78,    81,    82,   nil,    83,    84,   nil,   nil,   nil,
    79,    85,   nil,    71,    72,    68,   nil,    54,    59,   nil,
    80,    60,    61,   nil,   nil,   nil,    64,   nil,    62,    63,
    65,   297,   298,    69,    70,   nil,   nil,   nil,   nil,   nil,
   293,   294,   300,    95,    94,    96,    97,   nil,   nil,   223,
   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,
    98,   100,    89,    53,    91,    90,    92,   275,    93,   101,
   102,   nil,    87,    88,    41,    42,    40,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,
   nil,   nil,    55,    56,   nil,   nil,    57,   nil,   nil,   nil,
   271,   nil,    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   221,   nil,   nil,   nil,   nil,    86,    78,    81,    82,   nil,
    83,    84,   nil,   nil,   nil,    79,    85,   nil,    71,    72,
    68,   nil,    54,    59,   nil,    80,    60,    61,   nil,   nil,
   nil,    64,   nil,    62,    63,    65,   297,   298,    69,    70,
   nil,   nil,   nil,   nil,   nil,   293,   294,   300,    95,    94,
    96,    97,   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,    99,    98,   100,    89,    53,    91,
    90,    92,   nil,    93,   101,   102,   nil,    87,    88,    41,
    42,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   216,   nil,   nil,   222,   nil,   nil,    55,    56,   nil,
   nil,    57,   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,
    86,    78,    81,    82,   nil,    83,    84,   nil,   nil,   nil,
    79,    85,   nil,    71,    72,    68,     9,    54,    59,   nil,
    80,    60,    61,   nil,   nil,   nil,    64,   nil,    62,    63,
    65,    29,    30,    69,    70,   nil,   nil,   nil,   nil,   nil,
    28,    27,    26,    95,    94,    96,    97,   nil,   nil,    19,
   nil,   nil,   nil,   nil,   nil,     8,    44,   nil,    10,    99,
    98,   100,    89,    53,    91,    90,    92,   nil,    93,   101,
   102,   nil,    87,    88,    41,    42,    40,   231,   235,   240,
   241,   242,   237,   239,   247,   248,   243,   244,   nil,   224,
   225,   nil,   nil,   245,   246,   nil,    39,   nil,   nil,    32,
   nil,   nil,    55,    56,   nil,   nil,    57,   nil,    34,   228,
   nil,   234,    43,   230,   229,   226,   227,   238,   236,   232,
    20,   233,   nil,   nil,   nil,    86,    78,    81,    82,   nil,
    83,    84,   nil,   nil,   nil,    79,    85,   211,   249,   nil,
   nil,   nil,   374,    59,   nil,    80,    71,    72,    68,   nil,
    54,   nil,   nil,   nil,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,   297,   298,    69,    70,   nil,   nil,
   nil,   nil,   nil,   293,   294,   300,    95,    94,    96,    97,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,   295,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,   nil,   nil,   301,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   291,
   nil,   nil,   287,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,
    30,    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,
    26,    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,
    89,    53,    91,    90,    92,   275,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,   273,   nil,   271,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,   297,   298,    69,    70,   nil,   nil,
   nil,   nil,   nil,   293,   294,   300,    95,    94,    96,    97,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,   295,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,   nil,   nil,   301,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   291,
   nil,   nil,   287,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,
   298,    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,
   300,    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,   297,   298,    69,    70,   nil,   nil,
   nil,   nil,   nil,   293,   294,   300,    95,    94,    96,    97,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,
   298,    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,
   300,    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,    29,    30,    69,    70,   nil,   nil,
   nil,   nil,   nil,    28,    27,    26,    95,    94,    96,    97,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,
   298,    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,
   300,    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,   390,   nil,   nil,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,   297,   298,    69,    70,   nil,   nil,
   nil,   nil,   nil,   293,   294,   300,    95,    94,    96,    97,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   275,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   613,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,
   298,    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,
   300,    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,
    89,    53,    91,    90,    92,   275,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,   297,   298,    69,    70,   nil,   nil,
   nil,   nil,   nil,   293,   294,   300,    95,    94,    96,    97,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   273,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,
    30,    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,
    26,    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,
    89,    53,    91,    90,    92,   275,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,   273,   nil,   271,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,    29,    30,    69,    70,   nil,   nil,
   nil,   nil,   nil,    28,    27,    26,    95,    94,    96,    97,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   275,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   273,   nil,   271,   nil,    43,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,
    30,    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,
    26,    95,    94,    96,    97,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,   297,   298,    69,    70,   nil,   nil,
   nil,   nil,   nil,   293,   294,   300,    95,    94,    96,    97,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   708,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,
   298,    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,
   300,    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,   297,   298,    69,    70,   nil,   nil,
   nil,   nil,   nil,   293,   294,   300,    95,    94,    96,    97,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   275,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   613,   nil,   271,   nil,    43,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,
   298,    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,
   300,    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,
    89,    53,    91,    90,    92,   275,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,   nil,   nil,   271,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,    29,    30,    69,    70,   nil,   nil,
   nil,   nil,   nil,    28,    27,    26,    95,    94,    96,    97,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,
    30,    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,
    26,    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,    29,    30,    69,    70,   nil,   nil,
   nil,   nil,   nil,    28,    27,    26,    95,    94,    96,    97,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,
    30,    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,
    26,    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,    29,    30,    69,    70,   nil,   nil,
   nil,   nil,   nil,    28,    27,    26,    95,    94,    96,    97,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,
   298,    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,
   300,    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,   297,   298,    69,    70,   nil,   nil,
   nil,   nil,   nil,   293,   294,   300,    95,    94,    96,    97,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,
   298,    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,
   300,    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,
   nil,   nil,   nil,   nil,   295,   nil,   nil,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,   nil,   nil,   301,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   291,   nil,   nil,   287,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,   297,   298,    69,    70,   nil,   nil,
   nil,   nil,   nil,   293,   294,   300,    95,    94,    96,    97,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,   295,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,   nil,   nil,   301,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   291,
   nil,   nil,   287,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,
    30,    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,
    26,    95,    94,    96,    97,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,   297,   298,    69,    70,   nil,   nil,
   nil,   nil,   nil,   293,   294,   300,    95,    94,    96,    97,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,
    30,    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,
    26,    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,   297,   298,    69,    70,   nil,   nil,
   nil,   nil,   nil,   293,   294,   300,    95,    94,    96,    97,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,
   298,    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,
   300,    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,   297,   298,    69,    70,   nil,   nil,
   nil,   nil,   nil,   293,   294,   300,    95,    94,    96,    97,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,
   298,    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,
   300,    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,   297,   298,    69,    70,   nil,   nil,
   nil,   nil,   nil,   293,   294,   300,    95,    94,    96,    97,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,
   298,    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,
   300,    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,
   nil,   nil,   nil,   nil,   295,   nil,   nil,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,   nil,   nil,   301,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   827,   nil,   nil,   222,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,   297,   298,    69,    70,   nil,   nil,
   nil,   nil,   nil,   293,   294,   300,    95,    94,    96,    97,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,    29,
    30,    69,    70,   nil,   nil,   nil,   nil,   nil,    28,    27,
    26,    95,    94,    96,    97,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,   297,   298,    69,    70,   nil,   nil,
   nil,   nil,   nil,   293,   294,   300,    95,    94,    96,    97,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   613,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,
   298,    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,
   300,    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,    99,    98,   100,
    89,    53,    91,    90,    92,   275,    93,   101,   102,   nil,
    87,    88,    41,    42,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   222,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,   nil,   nil,   271,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,   297,   298,    69,    70,   nil,   nil,
   nil,   nil,   nil,   293,   294,   300,    95,    94,    96,    97,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   nil,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,    71,    72,    68,   nil,    54,    59,   nil,    80,    60,
    61,   nil,   nil,   nil,    64,   nil,    62,    63,    65,   297,
   298,    69,    70,   nil,   nil,   nil,   nil,   nil,   293,   294,
   300,    95,    94,    96,    97,   nil,   nil,   223,   nil,   nil,
   nil,   nil,   nil,   nil,   295,   nil,   nil,    99,    98,   100,
    89,    53,    91,    90,    92,   nil,    93,   101,   102,   nil,
    87,    88,   nil,   nil,   301,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   871,   nil,   nil,   222,   nil,   nil,
    55,    56,   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    86,    78,    81,    82,   nil,    83,    84,
   nil,   nil,   nil,    79,    85,   nil,    71,    72,    68,   nil,
    54,    59,   nil,    80,    60,    61,   nil,   nil,   nil,    64,
   nil,    62,    63,    65,    29,    30,    69,    70,   nil,   nil,
   nil,   nil,   nil,    28,    27,    26,    95,    94,    96,    97,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,    99,    98,   100,    89,    53,    91,    90,    92,
   275,    93,   101,   102,   nil,    87,    88,    41,    42,    40,
   231,   235,   240,   241,   242,   237,   239,   247,   248,   243,
   244,   nil,   224,   225,   nil,   nil,   245,   246,   nil,   216,
   nil,   nil,   222,   nil,   nil,    55,    56,   nil,   nil,    57,
   nil,   273,   228,   271,   234,    43,   230,   229,   226,   227,
   238,   236,   232,   221,   233,   nil,   nil,   nil,    86,    78,
    81,    82,   nil,    83,    84,   nil,   nil,   nil,    79,    85,
   nil,   249,  -398,   nil,   nil,   nil,    59,   nil,    80,  -398,
  -398,  -398,   nil,   nil,  -398,  -398,  -398,   nil,  -398,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -398,  -398,  -398,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -398,  -398,
   nil,  -398,  -398,  -398,  -398,  -398,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -398,  -398,
  -398,  -398,  -398,  -398,  -398,  -398,  -398,  -398,  -398,  -398,
  -398,  -398,   nil,   nil,  -398,  -398,  -398,   nil,   nil,  -398,
   nil,   255,  -398,   nil,   nil,  -398,  -398,   nil,  -398,   nil,
  -398,   nil,  -398,   nil,  -398,  -398,  -398,  -398,  -398,  -398,
  -398,  -288,  -398,  -398,  -398,   nil,   nil,   nil,  -288,  -288,
  -288,   nil,   nil,  -288,  -288,  -288,   nil,  -288,  -398,  -398,
   nil,  -398,   nil,  -398,   nil,   nil,   nil,  -288,  -288,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -288,  -288,   nil,
  -288,  -288,  -288,  -288,  -288,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -288,  -288,  -288,
  -288,  -288,  -288,  -288,  -288,  -288,  -288,  -288,  -288,  -288,
  -288,   nil,   nil,  -288,  -288,  -288,   nil,   nil,  -288,   nil,
   264,  -288,   nil,   nil,  -288,  -288,   nil,  -288,   nil,  -288,
   nil,  -288,   nil,  -288,  -288,  -288,  -288,  -288,  -288,  -288,
  -243,  -288,   nil,  -288,   nil,   nil,   nil,  -243,  -243,  -243,
   nil,   nil,  -243,  -243,  -243,   nil,  -243,  -288,  -288,   nil,
  -288,   nil,  -288,   nil,   nil,  -243,  -243,  -243,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,  -243,  -243,   nil,  -243,
  -243,  -243,  -243,  -243,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,  -243,  -243,  -243,  -243,
  -243,  -243,  -243,  -243,  -243,  -243,  -243,  -243,  -243,  -243,
   nil,   nil,  -243,  -243,  -243,   nil,   nil,  -243,   nil,   255,
  -243,   nil,   nil,  -243,  -243,   nil,  -243,   nil,  -243,   nil,
  -243,   nil,  -243,  -243,  -243,  -243,  -243,  -243,  -243,  -243,
  -243,  -243,  -243,   nil,   nil,   nil,  -243,  -243,  -243,   nil,
   nil,  -243,  -243,  -243,   nil,  -243,  -243,  -243,   nil,  -243,
   nil,  -243,   nil,   nil,   nil,  -243,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,  -243,  -243,   nil,  -243,  -243,
  -243,  -243,  -243,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,  -243,   nil,   nil,   nil,   nil,   nil,
   nil,  -243,  -243,  -243,   nil,   nil,  -243,  -243,  -243,   nil,
  -243,   nil,   nil,   nil,   nil,   nil,  -243,   nil,   nil,   nil,
  -243,   nil,   nil,  -243,   nil,   nil,   nil,   nil,   255,  -243,
  -243,  -243,   nil,  -243,  -243,  -243,  -243,  -243,   nil,   nil,
   nil,   nil,   nil,   398,   402,   nil,   nil,   399,   nil,   nil,
   nil,  -243,   nil,   nil,   nil,   151,   152,   nil,   148,   130,
   131,   132,   139,   136,   138,  -243,   nil,   133,   134,   nil,
  -243,  -243,   153,   154,   140,   141,   nil,   nil,  -243,   nil,
   nil,   255,   nil,   255,  -243,   nil,   nil,   nil,   nil,   145,
   144,   nil,   129,   150,   147,   146,   142,   143,   137,   135,
   127,   149,   128,   nil,   nil,   155,  -243,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
  -243,   nil,   nil,   nil,   nil,  -243,   166,   177,   167,   190,
   163,   183,   173,   172,   193,   194,   188,   171,   170,   165,
   191,   195,   196,   175,   164,   178,   182,   184,   176,   169,
   nil,   nil,   nil,   185,   192,   187,   186,   179,   189,   174,
   162,   181,   180,   nil,   nil,   nil,   nil,   nil,   161,   168,
   159,   160,   156,   157,   158,   118,   120,   117,   nil,   119,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   151,   152,   nil,
   148,   130,   131,   132,   139,   136,   138,   nil,   nil,   133,
   134,   nil,   nil,   nil,   153,   154,   140,   141,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   145,   144,   nil,   129,   150,   147,   146,   142,   143,
   137,   135,   127,   149,   128,   nil,   nil,   155,    86,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    85,
   166,   177,   167,   190,   163,   183,   173,   172,   193,   194,
   188,   171,   170,   165,   191,   195,   196,   175,   164,   178,
   182,   184,   176,   169,   nil,   nil,   nil,   185,   192,   187,
   186,   179,   189,   174,   162,   181,   180,   nil,   nil,   nil,
   nil,   nil,   161,   168,   159,   160,   156,   157,   158,   118,
   120,   nil,   nil,   119,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   151,   152,   nil,   148,   130,   131,   132,   139,   136,
   138,   nil,   nil,   133,   134,   nil,   nil,   nil,   153,   154,
   140,   141,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   145,   144,   nil,   129,   150,
   147,   146,   142,   143,   137,   135,   127,   149,   128,   nil,
   nil,   155,    86,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    85,   166,   177,   167,   190,   163,   183,
   173,   172,   193,   194,   188,   171,   170,   165,   191,   195,
   196,   175,   164,   178,   182,   184,   176,   169,   nil,   nil,
   nil,   185,   192,   187,   186,   179,   189,   174,   162,   181,
   180,   nil,   nil,   nil,   nil,   nil,   161,   168,   159,   160,
   156,   157,   158,   118,   120,   nil,   nil,   119,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   151,   152,   nil,   148,   130,
   131,   132,   139,   136,   138,   nil,   nil,   133,   134,   nil,
   nil,   nil,   153,   154,   140,   141,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   145,
   144,   nil,   129,   150,   147,   146,   142,   143,   137,   135,
   127,   149,   128,   nil,   nil,   155,    86,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    85,   166,   177,
   167,   190,   163,   183,   173,   172,   193,   194,   188,   171,
   170,   165,   191,   195,   196,   175,   164,   178,   182,   184,
   176,   169,   nil,   nil,   nil,   185,   192,   187,   186,   179,
   189,   174,   162,   181,   180,   nil,   nil,   nil,   nil,   nil,
   161,   168,   159,   160,   156,   157,   158,   118,   120,   nil,
   nil,   119,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   151,
   152,   nil,   148,   130,   131,   132,   139,   136,   138,   nil,
   nil,   133,   134,   nil,   nil,   nil,   153,   154,   140,   141,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   145,   144,   nil,   129,   150,   147,   146,
   142,   143,   137,   135,   127,   149,   128,   nil,   nil,   155,
    86,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    85,   166,   177,   167,   190,   163,   183,   173,   172,
   193,   194,   188,   171,   170,   165,   191,   195,   196,   175,
   164,   178,   182,   184,   176,   169,   nil,   nil,   nil,   185,
   192,   187,   359,   358,   360,   357,   162,   181,   180,   nil,
   nil,   nil,   nil,   nil,   161,   168,   159,   160,   354,   355,
   356,   352,   120,    91,    90,   353,   nil,    93,   nil,   nil,
   nil,   nil,   nil,   151,   152,   nil,   148,   130,   131,   132,
   139,   136,   138,   nil,   nil,   133,   134,   nil,   nil,   nil,
   153,   154,   140,   141,   nil,   nil,   nil,   nil,   nil,   364,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   145,   144,   nil,
   129,   150,   147,   146,   142,   143,   137,   135,   127,   149,
   128,   nil,   nil,   155,   166,   177,   167,   190,   163,   183,
   173,   172,   193,   194,   188,   171,   170,   165,   191,   195,
   196,   175,   164,   178,   182,   184,   176,   169,   nil,   nil,
   nil,   185,   192,   187,   186,   179,   189,   174,   162,   181,
   180,   nil,   nil,   nil,   nil,   nil,   161,   168,   159,   160,
   156,   157,   158,   118,   120,   nil,   nil,   119,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   151,   152,   nil,   148,   130,
   131,   132,   139,   136,   138,   nil,   nil,   133,   134,   nil,
   nil,   nil,   153,   154,   140,   141,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   145,
   144,   nil,   129,   150,   147,   146,   142,   143,   137,   135,
   127,   149,   128,   405,   409,   155,   nil,   404,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   151,   152,   nil,   148,   130,
   131,   132,   139,   136,   138,   nil,   nil,   133,   134,   nil,
   nil,   nil,   153,   154,   140,   141,   nil,   nil,   nil,   nil,
   nil,   255,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   145,
   144,   nil,   129,   150,   147,   146,   142,   143,   137,   135,
   127,   149,   128,   460,   402,   155,   nil,   461,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   151,   152,   nil,   148,   130,
   131,   132,   139,   136,   138,   nil,   nil,   133,   134,   nil,
   nil,   nil,   153,   154,   140,   141,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   145,
   144,   nil,   129,   150,   147,   146,   142,   143,   137,   135,
   127,   149,   128,   460,   402,   155,   nil,   461,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   151,   152,   nil,   148,   130,
   131,   132,   139,   136,   138,   nil,   nil,   133,   134,   nil,
   nil,   nil,   153,   154,   140,   141,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   145,
   144,   nil,   129,   150,   147,   146,   142,   143,   137,   135,
   127,   149,   128,   590,   402,   155,   nil,   591,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   151,   152,   nil,   148,   130,
   131,   132,   139,   136,   138,   nil,   nil,   133,   134,   nil,
   nil,   nil,   153,   154,   140,   141,   nil,   nil,   nil,   nil,
   nil,   255,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   145,
   144,   nil,   129,   150,   147,   146,   142,   143,   137,   135,
   127,   149,   128,   592,   409,   155,   nil,   593,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   151,   152,   nil,   148,   130,
   131,   132,   139,   136,   138,   nil,   nil,   133,   134,   nil,
   nil,   nil,   153,   154,   140,   141,   nil,   nil,   nil,   nil,
   nil,   255,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   145,
   144,   nil,   129,   150,   147,   146,   142,   143,   137,   135,
   127,   149,   128,   622,   402,   155,   nil,   623,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   151,   152,   nil,   148,   130,
   131,   132,   139,   136,   138,   nil,   nil,   133,   134,   nil,
   nil,   nil,   153,   154,   140,   141,   nil,   nil,   nil,   nil,
   nil,   255,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   145,
   144,   nil,   129,   150,   147,   146,   142,   143,   137,   135,
   127,   149,   128,   625,   409,   155,   nil,   626,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   151,   152,   nil,   148,   130,
   131,   132,   139,   136,   138,   nil,   nil,   133,   134,   nil,
   nil,   nil,   153,   154,   140,   141,   nil,   nil,   nil,   nil,
   nil,   255,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   145,
   144,   nil,   129,   150,   147,   146,   142,   143,   137,   135,
   127,   149,   128,   590,   402,   155,   nil,   591,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   151,   152,   nil,   148,   130,
   131,   132,   139,   136,   138,   nil,   nil,   133,   134,   nil,
   nil,   nil,   153,   154,   140,   141,   nil,   nil,   nil,   nil,
   nil,   255,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   145,
   144,   nil,   129,   150,   147,   146,   142,   143,   137,   135,
   127,   149,   128,   592,   409,   155,   nil,   593,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   151,   152,   nil,   148,   130,
   131,   132,   139,   136,   138,   nil,   nil,   133,   134,   nil,
   nil,   nil,   153,   154,   140,   141,   nil,   nil,   nil,   nil,
   nil,   255,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   145,
   144,   nil,   129,   150,   147,   146,   142,   143,   137,   135,
   127,   149,   128,   677,   402,   155,   nil,   678,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   151,   152,   nil,   148,   130,
   131,   132,   139,   136,   138,   nil,   nil,   133,   134,   nil,
   nil,   nil,   153,   154,   140,   141,   nil,   nil,   nil,   nil,
   nil,   255,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   145,
   144,   nil,   129,   150,   147,   146,   142,   143,   137,   135,
   127,   149,   128,   679,   409,   155,   nil,   680,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   151,   152,   nil,   148,   130,
   131,   132,   139,   136,   138,   nil,   nil,   133,   134,   nil,
   nil,   nil,   153,   154,   140,   141,   nil,   nil,   nil,   nil,
   nil,   255,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   145,
   144,   nil,   129,   150,   147,   146,   142,   143,   137,   135,
   127,   149,   128,   682,   409,   155,   nil,   683,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   151,   152,   nil,   148,   130,
   131,   132,   139,   136,   138,   nil,   nil,   133,   134,   nil,
   nil,   nil,   153,   154,   140,   141,   nil,   nil,   nil,   nil,
   nil,   255,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   145,
   144,   nil,   129,   150,   147,   146,   142,   143,   137,   135,
   127,   149,   128,   460,   402,   155,   nil,   461,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   151,   152,   nil,   148,   130,
   131,   132,   139,   136,   138,   nil,   nil,   133,   134,   nil,
   nil,   nil,   153,   154,   140,   141,   nil,   nil,   nil,   nil,
   nil,   255,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   145,
   144,   nil,   129,   150,   147,   146,   142,   143,   137,   135,
   127,   149,   128,   917,   402,   155,   nil,   918,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   151,   152,   nil,   148,   130,
   131,   132,   139,   136,   138,   nil,   nil,   133,   134,   nil,
   nil,   nil,   153,   154,   140,   141,   nil,   nil,   nil,   nil,
   nil,   255,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   145,
   144,   nil,   129,   150,   147,   146,   142,   143,   137,   135,
   127,   149,   128,   919,   409,   155,   nil,   920,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   151,   152,   nil,   148,   130,
   131,   132,   139,   136,   138,   nil,   nil,   133,   134,   nil,
   nil,   nil,   153,   154,   140,   141,   nil,   nil,   nil,   nil,
   nil,   255,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   145,
   144,   nil,   129,   150,   147,   146,   142,   143,   137,   135,
   127,   149,   128,   939,   409,   155,   nil,   938,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   151,   152,   nil,   148,   130,
   131,   132,   139,   136,   138,   nil,   nil,   133,   134,   nil,
   nil,   nil,   153,   154,   140,   141,   nil,   nil,   nil,   nil,
   nil,   255,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   145,
   144,   nil,   129,   150,   147,   146,   142,   143,   137,   135,
   127,   149,   128,   nil,   nil,   155,   231,   235,   240,   241,
   242,   237,   239,   247,   248,   243,   244,   nil,   224,   225,
   nil,   nil,   245,   246,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   228,   nil,
   234,   nil,   230,   229,   226,   227,   238,   236,   232,   nil,
   233,   nil,   231,   235,   240,   241,   242,   237,   239,   247,
   248,   243,   244,   nil,   224,   225,   nil,   249,   245,   246,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   228,   nil,   234,   nil,   230,   229,
   226,   227,   238,   236,   232,   nil,   233,   nil,   231,   235,
   240,   241,   242,   237,   239,   247,   248,   243,   244,   nil,
   224,   225,   nil,   249,   245,   246,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   228,   nil,   234,   nil,   230,   229,   226,   227,   238,   236,
   232,   nil,   233,   nil,   231,   235,   240,   241,   242,   237,
   239,   247,   248,   243,   244,   nil,   224,   225,   nil,   249,
   245,   246,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   228,   nil,   234,   nil,
   230,   229,   226,   227,   238,   236,   232,   nil,   233,   nil,
   231,   235,   240,   241,   242,   237,   239,   247,   248,   243,
   244,   nil,   224,   225,   nil,   249,   245,   246,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   228,   nil,   234,   nil,   230,   229,   226,   227,
   238,   236,   232,   nil,   233,   nil,   231,   235,   240,   241,
   242,   237,   239,   247,   248,   243,   244,   nil,   224,   225,
   nil,   249,   245,   246,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   228,   nil,
   234,   nil,   230,   229,   226,   227,   238,   236,   232,   nil,
   233,   nil,   231,   235,   240,   241,   242,   237,   239,   247,
   248,   243,   244,   nil,   224,   225,   nil,   249,   245,   246,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   228,   nil,   234,   nil,   230,   229,
   226,   227,   238,   236,   232,   nil,   233,   nil,   231,   235,
   240,   241,   242,   237,   239,   247,   248,   243,   244,   nil,
   224,   225,   nil,   249,   245,   246,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   228,   nil,   234,   nil,   230,   229,   226,   227,   238,   236,
   232,   nil,   233,   nil,   231,   235,   240,   241,   242,   237,
   239,   247,   248,   243,   244,   nil,   224,   225,   nil,   249,
   245,   246,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   228,   nil,   234,   nil,
   230,   229,   226,   227,   238,   236,   232,   nil,   233,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   249 ]

racc_action_check = [
    89,     0,     0,     0,     0,     0,     0,    89,    89,    89,
     0,     0,    89,    89,    89,     0,    89,     0,     0,     0,
     0,     0,     0,     0,    89,    58,    89,    89,    89,     0,
     0,     0,     0,     0,     0,     0,    89,    89,     0,    89,
    89,    89,    89,    89,     0,     0,     0,     0,     0,     0,
     0,     0,     0,     0,     0,     0,   347,     0,     0,     0,
   326,     0,     0,     0,     0,     0,    89,    89,    89,    89,
    89,    89,    89,    89,    89,    89,    89,    89,    89,    89,
     1,    19,    89,    89,    89,     0,    89,    89,     0,   333,
    89,     0,     0,    89,    89,     0,    89,     0,    89,   370,
    89,     0,    89,    89,    89,    89,    89,    89,    89,     0,
    89,    58,    89,   334,     0,     0,     0,     0,    19,     0,
     0,   854,   337,   854,     0,     0,    89,    89,    89,    89,
    92,    89,     0,    89,     0,    89,   532,    92,    92,    92,
   214,    15,    92,    92,    92,   524,    92,   343,   327,   622,
     7,   343,   675,    24,    92,   434,    92,    92,    92,   643,
    24,   347,   370,    17,    17,   677,    92,    92,   525,    92,
    92,    92,    92,    92,   326,   753,   849,   917,   918,   326,
   678,   347,   544,   544,    15,   940,   347,   434,     3,   214,
    15,   434,   434,     3,   333,   787,    92,    92,    92,    92,
    92,    92,    92,    92,    92,    92,    92,    92,    92,    92,
   458,    10,    92,    92,    92,   622,    92,    92,   334,    12,
    92,   919,   623,    92,    92,   215,    92,   337,    92,    13,
    92,   788,    92,    92,    92,    92,    92,    92,    92,   404,
    92,   532,    92,    16,   458,   524,   404,   404,   404,   467,
   524,   622,   404,   404,   622,   404,    92,    92,    92,    92,
   622,    92,   327,    92,   643,    92,   675,   327,   525,   675,
   677,   675,   544,   525,   215,   404,   404,   544,   404,   404,
   404,   404,   404,   467,    22,   678,    25,   679,   623,   753,
   849,   917,   918,   680,   753,   849,   917,   918,   919,   940,
   787,    40,    40,    36,   940,   404,   404,   404,   404,   404,
   404,   404,   404,   404,   404,   404,   404,   404,   404,   422,
   422,   404,   404,   404,   623,   404,    37,   623,    39,   404,
   217,   788,   404,   623,   428,   919,   788,   404,    44,   404,
   919,   404,   404,   404,   404,   404,   404,   404,   405,   404,
    25,   404,   679,   550,   550,   405,   405,   405,   680,   633,
   633,   405,   405,   590,   405,   404,   404,   468,   404,    37,
   404,   296,    25,   405,   404,    37,   605,   746,   317,   217,
   617,   317,   103,   428,   405,   405,   617,   405,   405,   405,
   405,   405,   679,   773,   197,   773,   773,   773,   680,   773,
   892,   468,   892,   892,   892,   216,   892,   218,   278,   422,
   605,   219,   590,   278,   405,   405,   405,   405,   405,   405,
   405,   405,   405,   405,   405,   405,   405,   405,   223,   746,
   405,   405,   405,   561,   405,   296,   746,   352,   405,   591,
   254,   405,   728,   550,   352,   746,   405,    38,   405,   633,
   405,   405,   405,   405,   405,   405,   405,   296,   405,   405,
   405,   366,   305,   746,   268,   353,   269,   341,   342,   354,
   272,   773,   353,   592,   405,   405,   354,   405,   892,   405,
   592,   592,   592,   405,   281,   592,   592,   592,   591,   592,
    38,   728,   301,   301,   561,   561,    38,   355,   592,   592,
   592,   592,   117,   561,   355,   305,   625,   117,   117,   592,
   592,   305,   592,   592,   592,   592,   592,   283,   318,   341,
   342,   318,   366,   366,   366,   367,   341,   342,   368,   330,
   356,   341,   342,   369,   330,   341,   342,   356,   284,   592,
   592,   592,   592,   592,   592,   592,   592,   592,   592,   592,
   592,   592,   592,   341,   342,   592,   592,   592,   625,   592,
   592,   371,   285,   592,   398,   625,   592,   592,   682,   592,
   625,   592,   291,   592,   625,   592,   592,   592,   592,   592,
   592,   592,   294,   592,   592,   592,   367,   367,   367,   368,
   368,   368,   625,   295,   369,   369,   369,   300,   399,   592,
   592,   592,   592,   593,   592,   302,   592,   398,   592,   573,
   593,   593,   593,   398,   306,   593,   593,   593,   307,   593,
   682,    14,   371,   371,   371,   453,   357,   682,    14,   593,
   593,   593,   682,   357,   310,   321,   682,    14,   321,   593,
   593,   399,   593,   593,   593,   593,   593,   399,   358,    45,
   325,   325,   573,   359,   682,   358,    45,   453,   573,   360,
   359,   453,   453,   453,   453,    45,   360,   509,   509,   593,
   593,   593,   593,   593,   593,   593,   593,   593,   593,   593,
   593,   593,   593,    76,   213,   593,   593,   593,   288,   593,
   593,   213,    76,   593,   313,   288,   593,   593,   314,   593,
   213,   593,    76,   593,   288,   593,   593,   593,   593,   593,
   593,   593,   319,   593,   435,   593,   320,   644,   362,   644,
   644,   644,   543,   644,   322,   362,   331,   543,   687,   593,
   593,   593,   593,   687,   593,   332,   593,   336,   593,    32,
    32,    32,    32,    32,    32,   338,   435,   693,    32,    32,
   435,   435,   693,    32,   644,    32,    32,    32,    32,    32,
    32,    32,   380,   644,   644,   644,   644,    32,    32,    32,
    32,    32,    32,    32,   554,   554,    32,   386,   554,   554,
   554,   417,    32,    32,   388,    32,    32,    32,    32,    32,
    32,    32,    32,    32,   391,    32,    32,    32,   394,    32,
    32,    32,    32,    32,   417,   417,   417,   417,   417,   417,
   417,   417,   417,   417,   417,   289,   417,   417,   290,   396,
   417,   417,   289,    32,   642,   290,    32,   642,   397,    32,
    32,   289,   406,    32,   290,    32,   417,   414,   417,    32,
   417,   417,   417,   417,   417,   417,   417,    32,   417,   666,
   666,   424,    32,    32,    32,    32,   436,    32,    32,   454,
   437,   292,    32,    32,   438,   417,    53,   417,   292,   439,
    32,   465,    32,    53,    53,    53,   469,   292,    53,    53,
    53,   598,    53,   598,   598,   598,   482,   598,   931,   931,
   483,   454,    53,    53,    53,   454,   454,   454,   454,   486,
   488,   493,    53,    53,   497,    53,    53,    53,    53,    53,
   846,   506,   846,   846,   846,   308,   846,   657,   598,   657,
   657,   657,   308,   657,   521,   526,   527,   598,   598,   598,
   598,   308,    53,    53,    53,    53,    53,    53,    53,    53,
    53,    53,    53,    53,    53,    53,   558,   846,    53,    53,
    53,   335,   564,    53,   657,   570,    53,   574,   335,    53,
    53,   598,    53,   657,    53,   579,    53,   335,    53,    53,
    53,    53,    53,    53,    53,   345,    53,   584,    53,   594,
   492,   596,   345,   610,   612,   619,   621,   492,   624,   627,
   628,   345,    53,    53,    53,    53,   492,    53,   631,    53,
    54,    54,    54,    54,    54,    54,   632,   634,   535,    54,
    54,   637,   638,   641,    54,   535,    54,    54,    54,    54,
    54,    54,    54,   646,   535,   647,   648,   655,    54,    54,
    54,    54,    54,    54,    54,   662,   736,    54,   736,   736,
   736,   665,   736,    54,    54,   668,    54,    54,    54,    54,
    54,    54,    54,    54,    54,   673,    54,    54,    54,   676,
    54,    54,    54,    54,    54,   770,   685,   770,   770,   770,
   690,   770,   707,   767,   712,   767,   767,   767,   730,   767,
   731,   732,   734,   735,    54,   737,   739,    54,   741,   742,
    54,    54,   745,   756,    54,   893,    54,   893,   893,   893,
    54,   893,   770,   692,   761,   692,   692,   692,    54,   692,
   767,   770,   772,    54,    54,    54,    54,   775,    54,    54,
   778,   767,   767,    54,    54,   115,   115,   115,   115,   115,
   115,    54,   893,    54,   115,   115,   781,   790,   794,   115,
   692,   115,   115,   115,   115,   115,   115,   115,   795,   692,
   692,   692,   692,   115,   115,   115,   115,   115,   115,   115,
   798,   869,   115,   869,   869,   869,   799,   869,   115,   115,
   115,   115,   115,   115,   115,   115,   115,   115,   115,   115,
   812,   115,   115,   115,   813,   115,   115,   115,   115,   115,
   844,   819,   844,   844,   844,   827,   844,   833,   810,   834,
   810,   810,   810,   835,   810,   838,   839,   841,   843,   115,
   845,   851,   115,   852,   857,   115,   115,   862,   863,   115,
   928,   115,   928,   928,   928,   115,   928,   844,   762,   864,
   762,   762,   762,   115,   762,   810,   844,   865,   115,   115,
   115,   115,   867,   115,   115,   871,   810,   810,   115,   115,
   199,   199,   199,   199,   199,   199,   115,   928,   115,   199,
   199,   873,   878,   879,   199,   762,   199,   199,   199,   199,
   199,   199,   199,   894,   762,   762,   762,   762,   199,   199,
   199,   199,   199,   199,   199,   916,   923,   199,     6,     6,
     6,     6,     6,   199,   199,   924,   199,   199,   199,   199,
   199,   199,   199,   199,   199,   925,   199,   199,   199,   926,
   199,   199,   199,   199,   199,   930,   927,   930,   930,   930,
   929,   930,   932,   884,   933,   884,   884,   884,   934,   884,
   935,   936,   937,   938,   199,   939,   947,   199,   956,   957,
   199,   199,   958,   nil,   199,   946,   199,   946,   946,   946,
   199,   946,   930,   764,   824,   764,   764,   764,   199,   764,
   884,   824,   nil,   199,   199,   199,   199,   nil,   199,   199,
   824,   884,   884,   199,   199,   222,   222,   222,   222,   222,
   222,   199,   946,   199,   222,   222,   nil,   nil,   nil,   222,
   764,   222,   222,   222,   222,   222,   222,   222,   nil,   764,
   764,   764,   764,   222,   222,   222,   222,   222,   222,   222,
   nil,   nil,   222,   279,   279,   279,   279,   279,   222,   222,
   nil,   222,   222,   222,   222,   222,   222,   222,   222,   222,
   nil,   222,   222,   222,   nil,   222,   222,   222,   222,   222,
   nil,   nil,   825,   481,   481,   481,   481,   481,   886,   825,
   886,   886,   886,   nil,   886,   nil,   626,   826,   825,   222,
   828,   nil,   222,   626,   826,   222,   222,   828,   626,   222,
   870,   222,   626,   826,   nil,   222,   828,   870,   805,   nil,
   805,   805,   805,   222,   805,   886,   870,   nil,   222,   222,
   222,   222,   nil,   222,   222,   nil,   886,   886,   222,   222,
   282,   282,   282,   282,   282,   282,   222,   nil,   222,   282,
   282,   nil,   nil,   nil,   282,   805,   282,   282,   282,   282,
   282,   282,   282,   nil,   805,   805,   805,   805,   282,   282,
   282,   282,   282,   282,   282,   nil,   nil,   282,   616,   616,
   616,   616,   616,   282,   282,   nil,   282,   282,   282,   282,
   282,   282,   282,   282,   282,   nil,   282,   282,   282,   nil,
   282,   282,   282,   282,   282,   nil,   nil,   877,   nil,   nil,
   nil,   nil,   nil,   888,   877,   888,   888,   888,   nil,   888,
   nil,   683,   nil,   877,   282,   nil,   nil,   282,   683,   nil,
   282,   282,   nil,   683,   282,   nil,   282,   683,   nil,   nil,
   282,   nil,   nil,   807,   nil,   807,   807,   807,   282,   807,
   888,   nil,   nil,   282,   282,   282,   282,   nil,   282,   282,
   nil,   888,   888,   282,   282,   287,   287,   287,   287,   287,
   287,   282,   nil,   282,   287,   287,   nil,   nil,   nil,   287,
   807,   287,   287,   287,   287,   287,   287,   287,   nil,   807,
   807,   807,   807,   287,   287,   287,   287,   287,   287,   287,
   nil,   nil,   287,   nil,   nil,   nil,   nil,   393,   287,   287,
   nil,   287,   287,   287,   287,   287,   287,   287,   287,   287,
   nil,   287,   287,   287,   nil,   287,   287,   287,   287,   287,
   393,   393,   393,   393,   393,   393,   393,   393,   393,   393,
   393,   920,   393,   393,   nil,   nil,   393,   393,   920,   287,
   nil,   nil,   287,   920,   nil,   287,   287,   920,   nil,   287,
   nil,   287,   393,   nil,   393,   287,   393,   393,   393,   393,
   393,   393,   393,   287,   393,   nil,   nil,   nil,   287,   287,
   287,   287,   nil,   287,   287,   nil,   nil,   nil,   287,   287,
   nil,   393,   409,   nil,   nil,   nil,   287,   nil,   287,   409,
   409,   409,   nil,   nil,   409,   409,   409,   519,   409,   519,
   519,   519,   nil,   519,   nil,   nil,   nil,   409,   409,   409,
   409,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   409,   409,
   nil,   409,   409,   409,   409,   409,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   519,   519,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   519,   519,   519,   519,   nil,   409,   409,
   409,   409,   409,   409,   409,   409,   409,   409,   409,   409,
   409,   409,   nil,   nil,   409,   409,   409,   nil,   nil,   409,
   nil,   nil,   409,   nil,   nil,   409,   409,   nil,   409,   nil,
   409,   nil,   409,   nil,   409,   409,   409,   409,   409,   409,
   409,   410,   409,   409,   409,   nil,   nil,   nil,   410,   410,
   410,   nil,   nil,   410,   410,   410,   444,   410,   409,   409,
   409,   409,   nil,   409,   nil,   409,   410,   410,   410,   410,
   nil,   nil,   444,   444,   nil,   nil,   nil,   410,   410,   nil,
   410,   410,   410,   410,   410,   nil,   nil,   nil,   444,   nil,
   444,   nil,   444,   444,   444,   444,   nil,   nil,   444,   nil,
   444,   nil,   nil,   nil,   nil,   nil,   nil,   410,   410,   410,
   410,   410,   410,   410,   410,   410,   410,   410,   410,   410,
   410,   nil,   nil,   410,   410,   410,   nil,   nil,   410,   nil,
   nil,   410,   nil,   nil,   410,   410,   nil,   410,   nil,   410,
   nil,   410,   nil,   410,   410,   410,   410,   410,   410,   410,
   nil,   410,   410,   410,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   410,   410,   410,
   410,   nil,   410,   nil,   410,   520,   520,   520,   520,   520,
   520,   nil,   nil,   nil,   520,   520,   nil,   nil,   nil,   520,
   nil,   520,   520,   520,   520,   520,   520,   520,   nil,   nil,
   nil,   nil,   nil,   520,   520,   520,   520,   520,   520,   520,
   nil,   nil,   520,   nil,   nil,   nil,   nil,   nil,   520,   520,
   nil,   520,   520,   520,   520,   520,   520,   520,   520,   520,
   nil,   520,   520,   520,   nil,   520,   520,   520,   520,   520,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   907,   nil,
   907,   907,   907,   nil,   907,   nil,   nil,   nil,   nil,   520,
   nil,   nil,   520,   nil,   nil,   520,   520,   nil,   nil,   520,
   nil,   520,   nil,   nil,   nil,   520,   nil,   nil,   881,   nil,
   881,   881,   881,   520,   881,   907,   nil,   nil,   520,   520,
   520,   520,   nil,   520,   520,   nil,   907,   907,   520,   520,
   523,   523,   523,   523,   523,   523,   520,   nil,   520,   523,
   523,   nil,   nil,   nil,   523,   881,   523,   523,   523,   523,
   523,   523,   523,   nil,   881,   881,   881,   881,   523,   523,
   523,   523,   523,   523,   523,   nil,   nil,   523,   nil,   nil,
   nil,   nil,   nil,   523,   523,   nil,   523,   523,   523,   523,
   523,   523,   523,   523,   523,   nil,   523,   523,   523,   nil,
   523,   523,   523,   523,   523,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   909,   nil,   909,   909,   909,   nil,   909,
   nil,   nil,   nil,   nil,   523,   nil,   nil,   523,   nil,   nil,
   523,   523,   nil,   nil,   523,   nil,   523,   nil,   nil,   nil,
   523,   nil,   nil,   904,   nil,   904,   904,   904,   523,   904,
   909,   nil,   nil,   523,   523,   523,   523,   nil,   523,   523,
   nil,   909,   909,   523,   523,   545,   545,   545,   545,   545,
   545,   523,   nil,   523,   545,   545,   nil,   nil,   nil,   545,
   904,   545,   545,   545,   545,   545,   545,   545,   nil,   904,
   904,   904,   904,   545,   545,   545,   545,   545,   545,   545,
   nil,   nil,   545,   nil,   nil,   nil,   nil,   nil,   545,   545,
   nil,   545,   545,   545,   545,   545,   545,   545,   545,   545,
   nil,   545,   545,   545,   nil,   545,   545,   545,   545,   545,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   942,   nil,   942,
   942,   942,   nil,   942,   nil,   nil,   nil,   nil,   nil,   545,
   nil,   nil,   545,   nil,   nil,   545,   545,   nil,   nil,   545,
   nil,   545,   nil,   nil,   nil,   545,   952,   nil,   952,   952,
   952,   nil,   952,   545,   942,   nil,   nil,   nil,   545,   545,
   545,   545,   nil,   545,   545,   942,   942,   nil,   545,   545,
   600,   600,   600,   600,   600,   600,   545,   nil,   545,   600,
   600,   nil,   nil,   952,   600,   nil,   600,   600,   600,   600,
   600,   600,   600,   nil,   952,   952,   nil,   nil,   600,   600,
   600,   600,   600,   600,   600,   nil,   nil,   600,   nil,   nil,
   nil,   nil,   nil,   600,   600,   nil,   600,   600,   600,   600,
   600,   600,   600,   600,   600,   nil,   600,   600,   600,   nil,
   600,   600,   600,   600,   600,   432,   432,   432,   432,   432,
   432,   432,   432,   432,   432,   432,   nil,   432,   432,   nil,
   nil,   432,   432,   nil,   600,   nil,   nil,   600,   nil,   nil,
   600,   600,   nil,   nil,   600,   nil,   600,   432,   nil,   432,
   600,   432,   432,   432,   432,   432,   432,   432,   600,   432,
   nil,   nil,   nil,   600,   600,   600,   600,   nil,   600,   600,
   nil,   nil,   nil,   600,   600,   607,   607,   607,   607,   607,
   607,   600,   nil,   600,   607,   607,   nil,   nil,   nil,   607,
   nil,   607,   607,   607,   607,   607,   607,   607,   nil,   nil,
   nil,   nil,   nil,   607,   607,   607,   607,   607,   607,   607,
   nil,   nil,   607,   nil,   nil,   nil,   nil,   nil,   607,   607,
   nil,   607,   607,   607,   607,   607,   607,   607,   607,   607,
   nil,   607,   607,   607,   nil,   607,   607,   607,   607,   607,
   433,   433,   433,   433,   433,   433,   433,   433,   433,   433,
   433,   nil,   433,   433,   nil,   nil,   433,   433,   nil,   607,
   nil,   nil,   607,   nil,   nil,   607,   607,   nil,   nil,   607,
   nil,   607,   433,   nil,   433,   607,   433,   433,   433,   433,
   433,   433,   433,   607,   433,   nil,   nil,   nil,   607,   607,
   607,   607,   nil,   607,   607,   nil,   nil,   nil,   607,   607,
   608,   608,   608,   608,   608,   608,   607,   nil,   607,   608,
   608,   nil,   nil,   nil,   608,   nil,   608,   608,   608,   608,
   608,   608,   608,   nil,   nil,   nil,   nil,   nil,   608,   608,
   608,   608,   608,   608,   608,   nil,   nil,   608,   nil,   nil,
   nil,   nil,   nil,   608,   608,   nil,   608,   608,   608,   608,
   608,   608,   608,   608,   608,   nil,   608,   608,   608,   nil,
   608,   608,   608,   608,   608,   443,   443,   443,   443,   443,
   443,   443,   nil,   nil,   443,   443,   nil,   nil,   nil,   nil,
   nil,   443,   443,   nil,   608,   nil,   nil,   608,   nil,   nil,
   608,   608,   nil,   nil,   608,   nil,   608,   443,   nil,   443,
   608,   443,   443,   443,   443,   443,   443,   443,   608,   443,
   nil,   nil,   nil,   608,   608,   608,   608,   nil,   608,   608,
   nil,   nil,   nil,   608,   608,   636,   636,   636,   636,   636,
   636,   608,   nil,   608,   636,   636,   nil,   nil,   nil,   636,
   nil,   636,   636,   636,   636,   636,   636,   636,   nil,   nil,
   nil,   nil,   nil,   636,   636,   636,   636,   636,   636,   636,
   nil,   nil,   636,   nil,   nil,   nil,   nil,   nil,   636,   636,
   nil,   636,   636,   636,   636,   636,   636,   636,   636,   636,
   nil,   636,   636,   636,   nil,   636,   636,   636,   636,   636,
   445,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   445,   445,   nil,   636,
   nil,   nil,   636,   nil,   nil,   636,   636,   nil,   nil,   636,
   nil,   636,   445,   nil,   445,   636,   445,   445,   445,   445,
   nil,   nil,   445,   636,   445,   nil,   nil,   nil,   636,   636,
   636,   636,   nil,   636,   636,   nil,   nil,   nil,   636,   636,
   686,   686,   686,   686,   686,   686,   636,   nil,   636,   686,
   686,   nil,   nil,   nil,   686,   nil,   686,   686,   686,   686,
   686,   686,   686,   nil,   nil,   nil,   nil,   nil,   686,   686,
   686,   686,   686,   686,   686,   nil,   nil,   686,   nil,   nil,
   nil,   nil,   nil,   686,   686,   nil,   686,   686,   686,   686,
   686,   686,   686,   686,   686,   nil,   686,   686,   686,   nil,
   686,   686,   686,   686,   686,   446,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   446,   446,   nil,   686,   nil,   nil,   686,   nil,   nil,
   686,   686,   nil,   nil,   686,   nil,   686,   446,   nil,   446,
   686,   446,   446,   446,   446,   nil,   nil,   446,   686,   446,
   nil,   nil,   nil,   686,   686,   686,   686,   nil,   686,   686,
   nil,   nil,   nil,   686,   686,   691,   691,   691,   691,   691,
   691,   686,   nil,   686,   691,   691,   nil,   nil,   nil,   691,
   nil,   691,   691,   691,   691,   691,   691,   691,   nil,   nil,
   nil,   nil,   nil,   691,   691,   691,   691,   691,   691,   691,
   nil,   nil,   691,   nil,   nil,   nil,   nil,   nil,   691,   691,
   nil,   691,   691,   691,   691,   691,   691,   691,   691,   691,
   nil,   691,   691,   691,   nil,   691,   691,   691,   691,   691,
   447,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   447,   447,   nil,   691,
   nil,   nil,   691,   nil,   nil,   691,   691,   nil,   nil,   691,
   nil,   691,   447,   nil,   447,   691,   447,   447,   447,   447,
   nil,   nil,   447,   691,   447,   nil,   nil,   nil,   691,   691,
   691,   691,   nil,   691,   691,   nil,   nil,   nil,   691,   691,
   701,   701,   701,   701,   701,   701,   691,   nil,   691,   701,
   701,   nil,   nil,   nil,   701,   nil,   701,   701,   701,   701,
   701,   701,   701,   nil,   nil,   nil,   nil,   nil,   701,   701,
   701,   701,   701,   701,   701,   nil,   nil,   701,   nil,   nil,
   nil,   nil,   nil,   701,   701,   nil,   701,   701,   701,   701,
   701,   701,   701,   701,   701,   nil,   701,   701,   701,   nil,
   701,   701,   701,   701,   701,   448,   448,   448,   448,   448,
   448,   448,   nil,   nil,   448,   448,   nil,   nil,   nil,   nil,
   nil,   448,   448,   nil,   701,   nil,   nil,   701,   nil,   nil,
   701,   701,   nil,   nil,   701,   nil,   701,   448,   nil,   448,
   701,   448,   448,   448,   448,   448,   448,   448,   701,   448,
   nil,   nil,   nil,   701,   701,   701,   701,   nil,   701,   701,
   nil,   nil,   nil,   701,   701,   740,   740,   740,   740,   740,
   740,   701,   nil,   701,   740,   740,   nil,   nil,   nil,   740,
   nil,   740,   740,   740,   740,   740,   740,   740,   nil,   nil,
   nil,   nil,   nil,   740,   740,   740,   740,   740,   740,   740,
   nil,   nil,   740,   nil,   nil,   nil,   nil,   nil,   740,   740,
   nil,   740,   740,   740,   740,   740,   740,   740,   740,   740,
   nil,   740,   740,   740,   nil,   740,   740,   740,   740,   740,
   449,   449,   449,   449,   449,   449,   449,   nil,   nil,   449,
   449,   nil,   nil,   nil,   nil,   nil,   449,   449,   nil,   740,
   nil,   nil,   740,   nil,   nil,   740,   740,   nil,   nil,   740,
   nil,   740,   449,   nil,   449,   740,   449,   449,   449,   449,
   449,   449,   449,   740,   449,   nil,   nil,   nil,   740,   740,
   740,   740,   nil,   740,   740,   nil,   nil,   nil,   740,   740,
   750,   750,   750,   750,   750,   750,   740,   nil,   740,   750,
   750,   nil,   nil,   nil,   750,   nil,   750,   750,   750,   750,
   750,   750,   750,   nil,   nil,   nil,   nil,   nil,   750,   750,
   750,   750,   750,   750,   750,   nil,   nil,   750,   nil,   nil,
   nil,   nil,   nil,   750,   750,   nil,   750,   750,   750,   750,
   750,   750,   750,   750,   750,   nil,   750,   750,   750,   nil,
   750,   750,   750,   750,   750,   450,   450,   450,   450,   450,
   450,   450,   nil,   nil,   450,   450,   nil,   nil,   nil,   nil,
   nil,   450,   450,   nil,   750,   nil,   nil,   750,   nil,   nil,
   750,   750,   nil,   nil,   750,   nil,   750,   450,   nil,   450,
   750,   450,   450,   450,   450,   450,   450,   450,   750,   450,
   nil,   nil,   nil,   750,   750,   750,   750,   nil,   750,   750,
   nil,   nil,   nil,   750,   750,   782,   782,   782,   782,   782,
   782,   750,   nil,   750,   782,   782,   nil,   nil,   nil,   782,
   nil,   782,   782,   782,   782,   782,   782,   782,   nil,   nil,
   nil,   nil,   nil,   782,   782,   782,   782,   782,   782,   782,
   nil,   nil,   782,   nil,   nil,   nil,   nil,   nil,   782,   782,
   nil,   782,   782,   782,   782,   782,   782,   782,   782,   782,
   nil,   782,   782,   782,   nil,   782,   782,   782,   782,   782,
   451,   451,   451,   451,   451,   451,   451,   nil,   nil,   451,
   451,   nil,   nil,   nil,   nil,   nil,   451,   451,   nil,   782,
   nil,   nil,   782,   nil,   nil,   782,   782,   nil,   nil,   782,
   nil,   782,   451,   nil,   451,   782,   451,   451,   451,   451,
   451,   451,   451,   782,   451,   nil,   nil,   nil,   782,   782,
   782,   782,   nil,   782,   782,   nil,   nil,   nil,   782,   782,
   783,   783,   783,   783,   783,   783,   782,   nil,   782,   783,
   783,   nil,   nil,   nil,   783,   nil,   783,   783,   783,   783,
   783,   783,   783,   nil,   nil,   nil,   nil,   nil,   783,   783,
   783,   783,   783,   783,   783,   nil,   nil,   783,   nil,   nil,
   nil,   nil,   nil,   783,   783,   nil,   783,   783,   783,   783,
   783,   783,   783,   783,   783,   nil,   783,   783,   783,   nil,
   783,   783,   783,   783,   783,   452,   452,   452,   452,   452,
   452,   452,   nil,   nil,   452,   452,   nil,   nil,   nil,   nil,
   nil,   452,   452,   nil,   783,   nil,   nil,   783,   nil,   nil,
   783,   783,   nil,   nil,   783,   nil,   783,   452,   nil,   452,
   783,   452,   452,   452,   452,   452,   452,   452,   783,   452,
   nil,   nil,   nil,   783,   783,   783,   783,   nil,   783,   783,
   nil,   nil,   nil,   783,   783,   786,   786,   786,   786,   786,
   786,   783,   nil,   783,   786,   786,   nil,   nil,   nil,   786,
   nil,   786,   786,   786,   786,   786,   786,   786,   nil,   nil,
   nil,   nil,   nil,   786,   786,   786,   786,   786,   786,   786,
   nil,   nil,   786,   nil,   nil,   nil,   nil,   nil,   786,   786,
   nil,   786,   786,   786,   786,   786,   786,   786,   786,   786,
   nil,   786,   786,   786,   nil,   786,   786,   786,   786,   786,
   455,   455,   455,   455,   455,   455,   455,   nil,   nil,   455,
   455,   nil,   nil,   nil,   nil,   nil,   455,   455,   nil,   786,
   nil,   nil,   786,   nil,   nil,   786,   786,   nil,   nil,   786,
   nil,   786,   455,   nil,   455,   786,   455,   455,   455,   455,
   455,   455,   455,   786,   455,   nil,   nil,   nil,   786,   786,
   786,   786,   nil,   786,   786,   nil,   nil,   nil,   786,   786,
   792,   792,   792,   792,   792,   792,   786,   nil,   786,   792,
   792,   nil,   nil,   nil,   792,   nil,   792,   792,   792,   792,
   792,   792,   792,   nil,   nil,   nil,   nil,   nil,   792,   792,
   792,   792,   792,   792,   792,   nil,   nil,   792,   nil,   nil,
   nil,   nil,   nil,   792,   792,   nil,   792,   792,   792,   792,
   792,   792,   792,   792,   792,   nil,   792,   792,   792,   nil,
   792,   792,   792,   792,   792,   456,   456,   456,   456,   456,
   456,   456,   456,   nil,   456,   456,   nil,   nil,   nil,   nil,
   nil,   456,   456,   nil,   792,   nil,   nil,   792,   nil,   nil,
   792,   792,   nil,   nil,   792,   nil,   792,   456,   nil,   456,
   792,   456,   456,   456,   456,   456,   456,   456,   792,   456,
   nil,   nil,   nil,   792,   792,   792,   792,   nil,   792,   792,
   nil,   nil,   nil,   792,   792,   823,   823,   823,   823,   823,
   823,   792,   nil,   792,   823,   823,   nil,   nil,   nil,   823,
   nil,   823,   823,   823,   823,   823,   823,   823,   nil,   nil,
   nil,   nil,   nil,   823,   823,   823,   823,   823,   823,   823,
   nil,   nil,   823,   nil,   nil,   nil,   nil,   nil,   823,   823,
   nil,   823,   823,   823,   823,   823,   823,   823,   823,   823,
   nil,   823,   823,   823,   nil,   823,   823,   823,   823,   823,
   440,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   440,   440,   nil,   823,
   nil,   nil,   823,   nil,   nil,   823,   823,   nil,   nil,   823,
   nil,   823,   440,   nil,   440,   823,   440,   440,   440,   440,
   nil,   nil,   nil,   823,   nil,   nil,   nil,   nil,   823,   823,
   823,   823,   nil,   823,   823,   nil,   nil,   nil,   823,   823,
   831,   831,   831,   831,   831,   831,   823,   nil,   823,   831,
   831,   nil,   nil,   nil,   831,   nil,   831,   831,   831,   831,
   831,   831,   831,   nil,   nil,   nil,   nil,   nil,   831,   831,
   831,   831,   831,   831,   831,   nil,   nil,   831,   nil,   nil,
   nil,   nil,   nil,   831,   831,   nil,   831,   831,   831,   831,
   831,   831,   831,   831,   831,   nil,   831,   831,   831,   nil,
   831,   831,   831,   831,   831,   441,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   441,   441,   nil,   831,   nil,   nil,   831,   nil,   nil,
   831,   831,   nil,   nil,   831,   nil,   831,   441,   nil,   441,
   831,   441,   441,   441,   441,   nil,   nil,   nil,   831,   nil,
   nil,   nil,   nil,   831,   831,   831,   831,   nil,   831,   831,
   nil,   nil,   nil,   831,   831,   832,   832,   832,   832,   832,
   832,   831,   nil,   831,   832,   832,   nil,   nil,   nil,   832,
   nil,   832,   832,   832,   832,   832,   832,   832,   nil,   nil,
   nil,   nil,   nil,   832,   832,   832,   832,   832,   832,   832,
   nil,   nil,   832,   nil,   nil,   nil,   nil,   nil,   832,   832,
   nil,   832,   832,   832,   832,   832,   832,   832,   832,   832,
   nil,   832,   832,   832,   nil,   832,   832,   832,   832,   832,
   442,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   442,   442,   nil,   832,
   nil,   nil,   832,   nil,   nil,   832,   832,   nil,   nil,   832,
   nil,   832,   442,   nil,   nil,   832,   442,   442,   442,   442,
   nil,   nil,   nil,   832,   nil,   nil,   nil,   nil,   832,   832,
   832,   832,   nil,   832,   832,   nil,   nil,   nil,   832,   832,
   895,   895,   895,   895,   895,   895,   832,   nil,   832,   895,
   895,   nil,   nil,   nil,   895,   nil,   895,   895,   895,   895,
   895,   895,   895,   nil,   nil,   nil,   nil,   nil,   895,   895,
   895,   895,   895,   895,   895,   nil,   nil,   895,   nil,   nil,
   nil,   nil,   nil,   895,   895,   nil,   895,   895,   895,   895,
   895,   895,   895,   895,   895,   nil,   895,   895,   895,   nil,
   895,   895,   895,   895,   895,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   895,   nil,   nil,   895,   nil,   nil,
   895,   895,   nil,   nil,   895,   nil,   895,   nil,   nil,   nil,
   895,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   895,   nil,
   nil,   nil,   nil,   895,   895,   895,   895,   nil,   895,   895,
   nil,   nil,   nil,   895,   895,   901,   901,   901,   901,   901,
   901,   895,   nil,   895,   901,   901,   nil,   nil,   nil,   901,
   nil,   901,   901,   901,   901,   901,   901,   901,   nil,   nil,
   nil,   nil,   nil,   901,   901,   901,   901,   901,   901,   901,
   nil,   nil,   901,   nil,   nil,   nil,   nil,   nil,   901,   901,
   nil,   901,   901,   901,   901,   901,   901,   901,   901,   901,
   nil,   901,   901,   901,   nil,   901,   901,   901,   901,   901,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   901,
   nil,   nil,   901,   nil,   nil,   901,   901,   nil,   nil,   901,
   nil,   901,   nil,   nil,   nil,   901,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   901,   nil,   nil,   nil,   nil,   901,   901,
   901,   901,   nil,   901,   901,   nil,   nil,   nil,   901,   901,
   903,   903,   903,   903,   903,   903,   901,   nil,   901,   903,
   903,   nil,   nil,   nil,   903,   nil,   903,   903,   903,   903,
   903,   903,   903,   nil,   nil,   nil,   nil,   nil,   903,   903,
   903,   903,   903,   903,   903,   nil,   nil,   903,   nil,   nil,
   nil,   nil,   nil,   903,   903,   nil,   903,   903,   903,   903,
   903,   903,   903,   903,   903,   nil,   903,   903,   903,   nil,
   903,   903,   903,   903,   903,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   903,   nil,   nil,   903,   nil,   nil,
   903,   903,   nil,   nil,   903,   nil,   903,   nil,   nil,   nil,
   903,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   903,   nil,
   nil,   nil,   nil,   903,   903,   903,   903,   nil,   903,   903,
   nil,   nil,   nil,   903,   903,   nil,     5,     5,     5,     5,
     5,   903,   nil,   903,     5,     5,   nil,   nil,   nil,     5,
   nil,     5,     5,     5,     5,     5,     5,     5,   nil,   nil,
   nil,   nil,   nil,     5,     5,     5,     5,     5,     5,     5,
   nil,   nil,     5,   nil,   nil,   nil,   nil,   nil,     5,     5,
     5,     5,     5,     5,     5,     5,     5,     5,     5,     5,
   nil,     5,     5,     5,   nil,     5,     5,     5,     5,     5,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,     5,
   nil,   nil,     5,   nil,   nil,     5,     5,   nil,   nil,     5,
   nil,     5,   nil,   nil,   nil,     5,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,     5,   nil,   nil,   nil,   nil,     5,     5,
     5,     5,   nil,     5,     5,   nil,   nil,   nil,     5,     5,
   nil,    20,    20,    20,   nil,    20,     5,   nil,     5,    20,
    20,   nil,   nil,   nil,    20,   nil,    20,    20,    20,    20,
    20,    20,    20,   nil,   nil,   nil,   nil,   nil,    20,    20,
    20,    20,    20,    20,    20,   nil,   nil,    20,   nil,   nil,
   nil,   nil,   nil,   nil,    20,   nil,   nil,    20,    20,    20,
    20,    20,    20,    20,    20,   nil,    20,    20,    20,   nil,
    20,    20,    20,    20,    20,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    20,   nil,   nil,    20,   nil,   nil,
    20,    20,   nil,   nil,    20,   nil,   nil,   nil,   nil,   nil,
    20,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    20,    20,    20,    20,   nil,    20,    20,
   nil,   nil,   nil,    20,    20,   nil,    28,    28,    28,   nil,
    28,    20,   nil,    20,    28,    28,   nil,   nil,   nil,    28,
   nil,    28,    28,    28,    28,    28,    28,    28,   nil,   nil,
   nil,   nil,   nil,    28,    28,    28,    28,    28,    28,    28,
   nil,   nil,    28,   nil,   nil,   nil,   nil,   nil,   nil,    28,
   nil,   nil,    28,    28,    28,    28,    28,    28,    28,    28,
    28,    28,    28,    28,   nil,    28,    28,    28,    28,    28,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    28,
   nil,   nil,    28,   nil,   nil,    28,    28,   nil,   nil,    28,
   nil,    28,   nil,    28,   nil,    28,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    28,   nil,   nil,   nil,   nil,    28,    28,
    28,    28,   nil,    28,    28,   nil,   nil,   nil,    28,    28,
   nil,    29,    29,    29,   nil,    29,    28,   nil,    28,    29,
    29,   nil,   nil,   nil,    29,   nil,    29,    29,    29,    29,
    29,    29,    29,   nil,   nil,   nil,   nil,   nil,    29,    29,
    29,    29,    29,    29,    29,   nil,   nil,    29,   nil,   nil,
   nil,   nil,   nil,   nil,    29,   nil,   nil,    29,    29,    29,
    29,    29,    29,    29,    29,    29,    29,    29,    29,   nil,
    29,    29,    29,    29,    29,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    29,   nil,   nil,    29,   nil,   nil,
    29,    29,   nil,   nil,    29,   nil,    29,   nil,    29,   nil,
    29,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    29,   nil,
   nil,   nil,   nil,    29,    29,    29,    29,   nil,    29,    29,
   nil,   nil,   nil,    29,    29,   nil,    30,    30,    30,   nil,
    30,    29,   nil,    29,    30,    30,   nil,   nil,   nil,    30,
   nil,    30,    30,    30,    30,    30,    30,    30,   nil,   nil,
   nil,   nil,   nil,    30,    30,    30,    30,    30,    30,    30,
   nil,   nil,    30,   nil,   nil,   nil,   nil,   nil,   nil,    30,
   nil,   nil,    30,    30,    30,    30,    30,    30,    30,    30,
    30,    30,    30,    30,   nil,    30,    30,    30,    30,    30,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    30,
   nil,   nil,    30,   nil,   nil,    30,    30,   nil,   nil,    30,
   nil,    30,   nil,    30,   nil,    30,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    30,   nil,   nil,   nil,   nil,    30,    30,
    30,    30,   nil,    30,    30,   nil,   nil,   nil,    30,    30,
   nil,    33,    33,    33,   nil,    33,    30,   nil,    30,    33,
    33,   nil,   nil,   nil,    33,   nil,    33,    33,    33,    33,
    33,    33,    33,   nil,   nil,   nil,   nil,   nil,    33,    33,
    33,    33,    33,    33,    33,   nil,   nil,    33,   nil,   nil,
   nil,   nil,   nil,   nil,    33,   nil,   nil,    33,    33,    33,
    33,    33,    33,    33,    33,   nil,    33,    33,    33,   nil,
    33,    33,   nil,   546,    33,   546,   546,   546,   nil,   546,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    33,   nil,   nil,    33,   nil,   nil,
    33,    33,   nil,   nil,    33,   nil,    33,   nil,   nil,   nil,
   546,   546,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   546,
   546,   546,   546,    33,    33,    33,    33,   nil,    33,    33,
   nil,   nil,   nil,    33,    33,   nil,    34,    34,    34,   nil,
    34,    33,   nil,    33,    34,    34,   nil,   nil,   nil,    34,
   nil,    34,    34,    34,    34,    34,    34,    34,   nil,   nil,
   nil,   nil,   nil,    34,    34,    34,    34,    34,    34,    34,
   nil,   nil,    34,   nil,   nil,   nil,   nil,   595,   nil,    34,
   nil,   nil,    34,    34,    34,    34,    34,    34,    34,    34,
   nil,    34,    34,    34,   nil,    34,    34,   nil,   nil,    34,
   595,   595,   595,   595,   595,   595,   595,   595,   595,   595,
   595,   nil,   595,   595,   nil,   nil,   595,   595,   nil,    34,
   nil,   nil,    34,   nil,   nil,    34,    34,   nil,   nil,    34,
   nil,   nil,   595,   nil,   595,   nil,   595,   595,   595,   595,
   595,   595,   595,   nil,   595,   nil,   nil,   nil,    34,    34,
    34,    34,   nil,    34,    34,   nil,   nil,   nil,    34,    34,
   nil,   595,   nil,    34,   nil,   nil,    34,   nil,    34,    41,
    41,    41,   nil,    41,   nil,   nil,   nil,    41,    41,   nil,
   nil,   nil,    41,   nil,    41,    41,    41,    41,    41,    41,
    41,   nil,   nil,   nil,   nil,   nil,    41,    41,    41,    41,
    41,    41,    41,   nil,   nil,    41,   nil,   nil,   nil,   nil,
   nil,   nil,    41,   nil,   nil,    41,    41,    41,    41,    41,
    41,    41,    41,   nil,    41,    41,    41,   nil,    41,    41,
    41,    41,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    41,   nil,   nil,    41,   nil,   nil,    41,    41,
   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    41,   nil,   nil,   nil,
   nil,    41,    41,    41,    41,   nil,    41,    41,   nil,   nil,
   nil,    41,    41,   nil,    42,    42,    42,   nil,    42,    41,
   nil,    41,    42,    42,   nil,   nil,   nil,    42,   nil,    42,
    42,    42,    42,    42,    42,    42,   nil,   nil,   nil,   nil,
   nil,    42,    42,    42,    42,    42,    42,    42,   nil,   nil,
    42,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,   nil,
    42,    42,    42,    42,    42,    42,    42,    42,   nil,    42,
    42,    42,   nil,    42,    42,    42,    42,    42,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,   nil,
    42,   nil,   nil,    42,    42,   nil,   nil,    42,   nil,   nil,
   nil,   nil,   nil,    42,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    42,   nil,   nil,   nil,   nil,    42,    42,    42,    42,
   nil,    42,    42,   nil,   nil,   nil,    42,    42,   nil,    43,
    43,    43,   nil,    43,    42,   nil,    42,    43,    43,   nil,
   nil,   nil,    43,   nil,    43,    43,    43,    43,    43,    43,
    43,   nil,   nil,   nil,   nil,   nil,    43,    43,    43,    43,
    43,    43,    43,   nil,   nil,    43,   nil,   nil,   nil,   nil,
   nil,   nil,    43,   nil,   nil,    43,    43,    43,    43,    43,
    43,    43,    43,   nil,    43,    43,    43,   nil,    43,    43,
    43,    43,    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    43,   nil,   nil,    43,   nil,   nil,    43,    43,
   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,    43,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,
   nil,    43,    43,    43,    43,   nil,    43,    43,   nil,   nil,
   nil,    43,    43,   nil,    56,    56,    56,   nil,    56,    43,
   nil,    43,    56,    56,   nil,   nil,   nil,    56,   nil,    56,
    56,    56,    56,    56,    56,    56,   nil,   nil,   nil,   nil,
   nil,    56,    56,    56,    56,    56,    56,    56,   nil,   nil,
    56,   nil,   nil,   nil,   nil,   nil,   nil,    56,   nil,   nil,
    56,    56,    56,    56,    56,    56,    56,    56,    56,    56,
    56,    56,   nil,    56,    56,    56,    56,    56,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    56,   nil,   nil,
    56,   nil,   nil,    56,    56,   nil,   nil,    56,   nil,    56,
   nil,   nil,   nil,    56,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    56,   nil,   nil,   nil,   nil,    56,    56,    56,    56,
   nil,    56,    56,   nil,   nil,   nil,    56,    56,   nil,    57,
    57,    57,   nil,    57,    56,   nil,    56,    57,    57,   nil,
   nil,   nil,    57,   nil,    57,    57,    57,    57,    57,    57,
    57,   nil,   nil,   nil,   nil,   nil,    57,    57,    57,    57,
    57,    57,    57,   nil,   nil,    57,   nil,   nil,   nil,   nil,
   nil,   nil,    57,   nil,   nil,    57,    57,    57,    57,    57,
    57,    57,    57,    57,    57,    57,    57,   nil,    57,    57,
    57,    57,    57,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    57,   nil,   nil,    57,   nil,   nil,    57,    57,
   nil,   nil,    57,   nil,   nil,   nil,   nil,   nil,    57,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    57,   nil,   nil,   nil,
   nil,    57,    57,    57,    57,   nil,    57,    57,   nil,   nil,
   nil,    57,    57,   nil,    60,    60,    60,   nil,    60,    57,
   nil,    57,    60,    60,   nil,   nil,   nil,    60,   nil,    60,
    60,    60,    60,    60,    60,    60,   nil,   nil,   nil,   nil,
   nil,    60,    60,    60,    60,    60,    60,    60,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,   nil,    60,   nil,   nil,
    60,    60,    60,    60,    60,    60,    60,    60,   nil,    60,
    60,    60,   nil,    60,    60,    60,    60,    60,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    60,   nil,   nil,
    60,   nil,   nil,    60,    60,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    60,   nil,   nil,   nil,   nil,    60,    60,    60,    60,
   nil,    60,    60,   nil,   nil,   nil,    60,    60,   nil,    61,
    61,    61,   nil,    61,    60,   nil,    60,    61,    61,   nil,
   nil,   nil,    61,   nil,    61,    61,    61,    61,    61,    61,
    61,   nil,   nil,   nil,   nil,   nil,    61,    61,    61,    61,
    61,    61,    61,   nil,   nil,    61,   nil,   nil,   nil,   nil,
   nil,   nil,    61,   nil,   nil,    61,    61,    61,    61,    61,
    61,    61,    61,   nil,    61,    61,    61,   nil,    61,    61,
    61,    61,    61,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    61,   nil,   nil,    61,   nil,   nil,    61,    61,
   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    61,   nil,   nil,   nil,
   nil,    61,    61,    61,    61,   nil,    61,    61,   nil,   nil,
   nil,    61,    61,   nil,    64,    64,    64,   nil,    64,    61,
   nil,    61,    64,    64,   nil,   nil,   nil,    64,   nil,    64,
    64,    64,    64,    64,    64,    64,   nil,   nil,   nil,   nil,
   nil,    64,    64,    64,    64,    64,    64,    64,   nil,   nil,
    64,   nil,   nil,   nil,   nil,   nil,   nil,    64,   nil,   nil,
    64,    64,    64,    64,    64,    64,    64,    64,   nil,    64,
    64,    64,   nil,    64,    64,    64,    64,    64,    21,    21,
    21,    21,    21,    21,    21,    21,    21,    21,    21,   nil,
    21,    21,   nil,   nil,    21,    21,   nil,    64,   nil,   nil,
    64,   nil,   nil,    64,    64,   nil,   nil,    64,   nil,   nil,
    21,   nil,    21,    64,    21,    21,    21,    21,    21,    21,
    21,    64,    21,   nil,   nil,   nil,    64,    64,    64,    64,
   nil,    64,    64,   nil,   nil,   nil,    64,    64,    64,    21,
   nil,   nil,   nil,    64,    64,   nil,    64,    65,    65,    65,
   nil,    65,   nil,   nil,   nil,    65,    65,   nil,   nil,   nil,
    65,   nil,    65,    65,    65,    65,    65,    65,    65,   nil,
   nil,   nil,   nil,   nil,    65,    65,    65,    65,    65,    65,
    65,   nil,   nil,    65,   nil,   nil,   nil,   nil,   nil,   nil,
    65,   nil,   nil,    65,    65,    65,    65,    65,    65,    65,
    65,   nil,    65,    65,    65,   nil,    65,    65,   nil,   860,
    65,   860,   860,   860,   nil,   860,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    65,   nil,   nil,    65,   nil,   nil,    65,    65,   nil,   nil,
    65,   nil,    65,   nil,   nil,   nil,   860,   860,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   860,   860,   860,   860,    65,
    65,    65,    65,   nil,    65,    65,   nil,   nil,   nil,    65,
    65,   nil,    66,    66,    66,   nil,    66,    65,   nil,    65,
    66,    66,   nil,   nil,   nil,    66,   nil,    66,    66,    66,
    66,    66,    66,    66,   nil,   nil,   nil,   nil,   nil,    66,
    66,    66,    66,    66,    66,    66,   nil,   nil,    66,   nil,
   nil,   nil,   nil,   nil,   nil,    66,   nil,   nil,    66,    66,
    66,    66,    66,    66,    66,    66,   nil,    66,    66,    66,
   nil,    66,    66,   nil,   nil,    66,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    66,   nil,   nil,    66,   nil,   nil,    66,   nil,
   nil,    66,    66,   nil,   nil,    66,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    66,    66,    66,    66,   nil,    66,
    66,   nil,   nil,   nil,    66,    66,   nil,    67,    67,    67,
   nil,    67,    66,   nil,    66,    67,    67,   nil,   nil,   nil,
    67,   nil,    67,    67,    67,    67,    67,    67,    67,   nil,
   nil,   nil,   nil,   nil,    67,    67,    67,    67,    67,    67,
    67,   nil,   nil,    67,   nil,   nil,   nil,   nil,   nil,   nil,
    67,   nil,   nil,    67,    67,    67,    67,    67,    67,    67,
    67,   nil,    67,    67,    67,   nil,    67,    67,   nil,   nil,
    67,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    67,   nil,   nil,    67,   nil,   nil,    67,    67,   nil,   nil,
    67,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    67,
    67,    67,    67,   nil,    67,    67,   nil,   nil,   nil,    67,
    67,   nil,   105,   105,   105,   105,   105,    67,   nil,    67,
   105,   105,   nil,   nil,   nil,   105,   nil,   105,   105,   105,
   105,   105,   105,   105,   nil,   nil,   nil,   nil,   nil,   105,
   105,   105,   105,   105,   105,   105,   nil,   nil,   105,   nil,
   nil,   nil,   nil,   nil,   105,   105,   105,   105,   105,   105,
   105,   105,   105,   105,   105,   105,   nil,   105,   105,   105,
   nil,   105,   105,   105,   105,   105,   266,   266,   266,   266,
   266,   266,   266,   266,   266,   266,   266,   nil,   266,   266,
   nil,   nil,   266,   266,   nil,   105,   nil,   nil,   105,   nil,
   nil,   105,   105,   nil,   nil,   105,   nil,   105,   266,   nil,
   266,   105,   266,   266,   266,   266,   266,   266,   266,   105,
   266,   nil,   nil,   nil,   105,   105,   105,   105,   nil,   105,
   105,   nil,   nil,   nil,   105,   105,   nil,   266,   nil,   nil,
   nil,   105,   105,   nil,   105,   110,   110,   110,   nil,   110,
   nil,   nil,   nil,   110,   110,   nil,   nil,   nil,   110,   nil,
   110,   110,   110,   110,   110,   110,   110,   nil,   nil,   nil,
   nil,   nil,   110,   110,   110,   110,   110,   110,   110,   nil,
   nil,   110,   nil,   nil,   nil,   nil,   nil,   nil,   110,   nil,
   nil,   110,   110,   110,   110,   110,   110,   110,   110,   nil,
   110,   110,   110,   nil,   110,   110,   110,   110,   110,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   110,   nil,
   nil,   110,   nil,   nil,   110,   110,   nil,   nil,   110,   nil,
   nil,   nil,   nil,   nil,   110,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   110,   nil,   nil,   nil,   nil,   110,   110,   110,
   110,   nil,   110,   110,   nil,   nil,   nil,   110,   110,   nil,
   111,   111,   111,   nil,   111,   110,   nil,   110,   111,   111,
   nil,   nil,   nil,   111,   nil,   111,   111,   111,   111,   111,
   111,   111,   nil,   nil,   nil,   nil,   nil,   111,   111,   111,
   111,   111,   111,   111,   nil,   nil,   111,   nil,   nil,   nil,
   nil,   nil,   nil,   111,   nil,   nil,   111,   111,   111,   111,
   111,   111,   111,   111,   nil,   111,   111,   111,   nil,   111,
   111,   111,   111,   111,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   111,   nil,   nil,   111,   nil,   nil,   111,
   111,   nil,   nil,   111,   nil,   nil,   nil,   nil,   nil,   111,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   111,   nil,   nil,
   nil,   nil,   111,   111,   111,   111,   nil,   111,   111,   nil,
   nil,   nil,   111,   111,   nil,   112,   112,   112,   nil,   112,
   111,   nil,   111,   112,   112,   nil,   nil,   nil,   112,   nil,
   112,   112,   112,   112,   112,   112,   112,   nil,   nil,   nil,
   nil,   nil,   112,   112,   112,   112,   112,   112,   112,   nil,
   nil,   112,   nil,   nil,   nil,   nil,   nil,   nil,   112,   nil,
   nil,   112,   112,   112,   112,   112,   112,   112,   112,   nil,
   112,   112,   112,   nil,   112,   112,   112,   112,   112,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   112,   nil,
   nil,   112,   nil,   nil,   112,   112,   nil,   nil,   112,   nil,
   nil,   nil,   nil,   nil,   112,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   112,   nil,   nil,   nil,   nil,   112,   112,   112,
   112,   nil,   112,   112,   nil,   nil,   nil,   112,   112,   nil,
   113,   113,   113,   nil,   113,   112,   nil,   112,   113,   113,
   nil,   nil,   nil,   113,   nil,   113,   113,   113,   113,   113,
   113,   113,   nil,   nil,   nil,   nil,   nil,   113,   113,   113,
   113,   113,   113,   113,   nil,   nil,   113,   nil,   nil,   nil,
   nil,   nil,   nil,   113,   nil,   nil,   113,   113,   113,   113,
   113,   113,   113,   113,   nil,   113,   113,   113,   nil,   113,
   113,   113,   113,   113,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   113,   nil,   nil,   113,   nil,   nil,   113,
   113,   nil,   nil,   113,   nil,   nil,   nil,   nil,   nil,   113,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   113,   nil,   nil,
   nil,   nil,   113,   113,   113,   113,   nil,   113,   113,   nil,
   nil,   nil,   113,   113,   nil,   114,   114,   114,   114,   114,
   113,   nil,   113,   114,   114,   nil,   nil,   nil,   114,   nil,
   114,   114,   114,   114,   114,   114,   114,   nil,   nil,   nil,
   nil,   nil,   114,   114,   114,   114,   114,   114,   114,   nil,
   nil,   114,   nil,   nil,   nil,   nil,   nil,   114,   114,   nil,
   114,   114,   114,   114,   114,   114,   114,   114,   114,   nil,
   114,   114,   114,   nil,   114,   114,   114,   114,   114,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   114,   nil,
   nil,   114,   nil,   nil,   114,   114,   nil,   nil,   114,   nil,
   114,   nil,   nil,   nil,   114,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   114,   nil,   nil,   nil,   nil,   114,   114,   114,
   114,   nil,   114,   114,   nil,   nil,   nil,   114,   114,   nil,
   200,   200,   200,   nil,   200,   114,   nil,   114,   200,   200,
   nil,   nil,   nil,   200,   nil,   200,   200,   200,   200,   200,
   200,   200,   nil,   nil,   nil,   nil,   nil,   200,   200,   200,
   200,   200,   200,   200,   nil,   nil,   200,   nil,   nil,   nil,
   nil,   nil,   nil,   200,   nil,   nil,   200,   200,   200,   200,
   200,   200,   200,   200,   nil,   200,   200,   200,   nil,   200,
   200,   200,   200,   200,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   200,   nil,   nil,   200,   nil,   nil,   200,
   200,   nil,   nil,   200,   nil,   200,   nil,   nil,   nil,   200,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   200,   nil,   nil,
   nil,   nil,   200,   200,   200,   200,   nil,   200,   200,   nil,
   nil,   nil,   200,   200,   nil,   201,   201,   201,   nil,   201,
   200,   nil,   200,   201,   201,   nil,   nil,   nil,   201,   nil,
   201,   201,   201,   201,   201,   201,   201,   nil,   nil,   nil,
   nil,   nil,   201,   201,   201,   201,   201,   201,   201,   nil,
   nil,   201,   nil,   nil,   nil,   nil,   nil,   nil,   201,   nil,
   nil,   201,   201,   201,   201,   201,   201,   201,   201,   nil,
   201,   201,   201,   nil,   201,   201,   201,   201,   201,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   201,   nil,
   nil,   201,   nil,   nil,   201,   201,   nil,   nil,   201,   nil,
   nil,   nil,   nil,   nil,   201,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   201,   nil,   nil,   nil,   nil,   201,   201,   201,
   201,   nil,   201,   201,   nil,   nil,   nil,   201,   201,   nil,
   202,   202,   202,   nil,   202,   201,   nil,   201,   202,   202,
   nil,   nil,   nil,   202,   nil,   202,   202,   202,   202,   202,
   202,   202,   nil,   nil,   nil,   nil,   nil,   202,   202,   202,
   202,   202,   202,   202,   nil,   nil,   202,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   202,   202,   202,   202,
   202,   202,   202,   202,   202,   202,   202,   202,   nil,   202,
   202,   202,   202,   202,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   202,   nil,   nil,   202,   nil,   nil,   202,
   202,   nil,   nil,   202,   nil,   202,   nil,   202,   nil,   202,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   202,   nil,   nil,
   nil,   nil,   202,   202,   202,   202,   nil,   202,   202,   nil,
   nil,   nil,   202,   202,   nil,   205,   205,   205,   nil,   205,
   202,   nil,   202,   205,   205,   nil,   nil,   nil,   205,   nil,
   205,   205,   205,   205,   205,   205,   205,   nil,   nil,   nil,
   nil,   nil,   205,   205,   205,   205,   205,   205,   205,   nil,
   nil,   205,   nil,   nil,   nil,   nil,   nil,   nil,   205,   nil,
   nil,   205,   205,   205,   205,   205,   205,   205,   205,   nil,
   205,   205,   205,   nil,   205,   205,   205,   205,   205,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   205,   nil,
   nil,   205,   nil,   nil,   205,   205,   nil,   nil,   205,   nil,
   nil,   nil,   nil,   nil,   205,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   205,   nil,   nil,   nil,   nil,   205,   205,   205,
   205,   nil,   205,   205,   nil,   nil,   nil,   205,   205,   nil,
   206,   206,   206,   nil,   206,   205,   nil,   205,   206,   206,
   nil,   nil,   nil,   206,   nil,   206,   206,   206,   206,   206,
   206,   206,   nil,   nil,   nil,   nil,   nil,   206,   206,   206,
   206,   206,   206,   206,   nil,   nil,   206,   nil,   nil,   nil,
   nil,   nil,   nil,   206,   nil,   nil,   206,   206,   206,   206,
   206,   206,   206,   206,   nil,   206,   206,   206,   nil,   206,
   206,   206,   206,   206,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   206,   nil,   nil,   206,   nil,   nil,   206,
   206,   nil,   nil,   206,   nil,   206,   nil,   nil,   nil,   206,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   206,   nil,   nil,
   nil,   nil,   206,   206,   206,   206,   nil,   206,   206,   nil,
   nil,   nil,   206,   206,   nil,   207,   207,   207,   nil,   207,
   206,   nil,   206,   207,   207,   nil,   nil,   nil,   207,   nil,
   207,   207,   207,   207,   207,   207,   207,   nil,   nil,   nil,
   nil,   nil,   207,   207,   207,   207,   207,   207,   207,   nil,
   nil,   207,   nil,   nil,   nil,   nil,   nil,   nil,   207,   nil,
   nil,   207,   207,   207,   207,   207,   207,   207,   207,   nil,
   207,   207,   207,   nil,   207,   207,   207,   207,   207,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   207,   nil,
   nil,   207,   nil,   nil,   207,   207,   nil,   nil,   207,   nil,
   nil,   nil,   nil,   nil,   207,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   207,   nil,   nil,   nil,   nil,   207,   207,   207,
   207,   nil,   207,   207,   nil,   nil,   nil,   207,   207,   nil,
   208,   208,   208,   nil,   208,   207,   nil,   207,   208,   208,
   nil,   nil,   nil,   208,   nil,   208,   208,   208,   208,   208,
   208,   208,   nil,   nil,   nil,   nil,   nil,   208,   208,   208,
   208,   208,   208,   208,   nil,   nil,   208,   nil,   nil,   nil,
   nil,   nil,   nil,   208,   nil,   nil,   208,   208,   208,   208,
   208,   208,   208,   208,   nil,   208,   208,   208,   nil,   208,
   208,   208,   208,   208,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   208,   nil,   nil,   208,   nil,   nil,   208,
   208,   nil,   nil,   208,   nil,   nil,   nil,   nil,   nil,   208,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   208,   nil,   nil,
   nil,   nil,   208,   208,   208,   208,   nil,   208,   208,   nil,
   nil,   nil,   208,   208,   nil,   209,   209,   209,   nil,   209,
   208,   nil,   208,   209,   209,   nil,   nil,   nil,   209,   nil,
   209,   209,   209,   209,   209,   209,   209,   nil,   nil,   nil,
   nil,   nil,   209,   209,   209,   209,   209,   209,   209,   nil,
   nil,   209,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,
   nil,   209,   209,   209,   209,   209,   209,   209,   209,   nil,
   209,   209,   209,   nil,   209,   209,   209,   209,   209,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   209,   nil,
   nil,   209,   nil,   nil,   209,   209,   nil,   nil,   209,   nil,
   nil,   nil,   nil,   nil,   209,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   209,   nil,   nil,   nil,   nil,   209,   209,   209,
   209,   nil,   209,   209,   nil,   nil,   nil,   209,   209,   nil,
   210,   210,   210,   nil,   210,   209,   nil,   209,   210,   210,
   nil,   nil,   nil,   210,   nil,   210,   210,   210,   210,   210,
   210,   210,   nil,   nil,   nil,   nil,   nil,   210,   210,   210,
   210,   210,   210,   210,   nil,   nil,   210,   nil,   nil,   nil,
   nil,   nil,   nil,   210,   nil,   nil,   210,   210,   210,   210,
   210,   210,   210,   210,   nil,   210,   210,   210,   nil,   210,
   210,   210,   210,   210,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   210,   nil,   nil,   210,   nil,   nil,   210,
   210,   nil,   nil,   210,   nil,   nil,   nil,   nil,   nil,   210,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   210,   nil,   nil,
   nil,   nil,   210,   210,   210,   210,   nil,   210,   210,   nil,
   nil,   nil,   210,   210,   210,   221,   221,   221,   nil,   221,
   210,   nil,   210,   221,   221,   nil,   nil,   nil,   221,   nil,
   221,   221,   221,   221,   221,   221,   221,   nil,   nil,   nil,
   nil,   nil,   221,   221,   221,   221,   221,   221,   221,   nil,
   nil,   221,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,
   nil,   221,   221,   221,   221,   221,   221,   221,   221,   nil,
   221,   221,   221,   nil,   221,   221,   221,   221,   221,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,
   nil,   221,   nil,   nil,   221,   221,   nil,   nil,   221,   nil,
   nil,   nil,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   221,   nil,   nil,   nil,   nil,   221,   221,   221,
   221,   nil,   221,   221,   nil,   nil,   nil,   221,   221,   nil,
   224,   224,   224,   nil,   224,   221,   nil,   221,   224,   224,
   nil,   nil,   nil,   224,   nil,   224,   224,   224,   224,   224,
   224,   224,   nil,   nil,   nil,   nil,   nil,   224,   224,   224,
   224,   224,   224,   224,   nil,   nil,   224,   nil,   nil,   nil,
   nil,   nil,   nil,   224,   nil,   nil,   224,   224,   224,   224,
   224,   224,   224,   224,   nil,   224,   224,   224,   nil,   224,
   224,   224,   224,   224,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   224,   nil,   nil,   224,   nil,   nil,   224,
   224,   nil,   nil,   224,   nil,   nil,   nil,   nil,   nil,   224,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   224,   nil,   nil,
   nil,   nil,   224,   224,   224,   224,   nil,   224,   224,   nil,
   nil,   nil,   224,   224,   nil,   225,   225,   225,   nil,   225,
   224,   nil,   224,   225,   225,   nil,   nil,   nil,   225,   nil,
   225,   225,   225,   225,   225,   225,   225,   nil,   nil,   nil,
   nil,   nil,   225,   225,   225,   225,   225,   225,   225,   nil,
   nil,   225,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   225,   225,   225,   225,   225,   225,   225,   225,   nil,
   225,   225,   225,   nil,   225,   225,   225,   225,   225,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   225,   nil,   nil,   225,   225,   nil,   nil,   225,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   225,   nil,   nil,   nil,   nil,   225,   225,   225,
   225,   nil,   225,   225,   nil,   nil,   nil,   225,   225,   nil,
   226,   226,   226,   nil,   226,   225,   nil,   225,   226,   226,
   nil,   nil,   nil,   226,   nil,   226,   226,   226,   226,   226,
   226,   226,   nil,   nil,   nil,   nil,   nil,   226,   226,   226,
   226,   226,   226,   226,   nil,   nil,   226,   nil,   nil,   nil,
   nil,   nil,   nil,   226,   nil,   nil,   226,   226,   226,   226,
   226,   226,   226,   226,   nil,   226,   226,   226,   nil,   226,
   226,   226,   226,   226,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   226,   nil,   nil,   226,   nil,   nil,   226,
   226,   nil,   nil,   226,   nil,   nil,   nil,   nil,   nil,   226,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   226,   nil,   nil,
   nil,   nil,   226,   226,   226,   226,   nil,   226,   226,   nil,
   nil,   nil,   226,   226,   nil,   227,   227,   227,   nil,   227,
   226,   nil,   226,   227,   227,   nil,   nil,   nil,   227,   nil,
   227,   227,   227,   227,   227,   227,   227,   nil,   nil,   nil,
   nil,   nil,   227,   227,   227,   227,   227,   227,   227,   nil,
   nil,   227,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   227,   227,   227,   227,   227,   227,   227,   227,   nil,
   227,   227,   227,   nil,   227,   227,   227,   227,   227,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   227,   nil,   nil,   227,   227,   nil,   nil,   227,   nil,
   nil,   nil,   nil,   nil,   227,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   nil,   nil,   227,   227,   227,
   227,   nil,   227,   227,   nil,   nil,   nil,   227,   227,   nil,
   228,   228,   228,   nil,   228,   227,   nil,   227,   228,   228,
   nil,   nil,   nil,   228,   nil,   228,   228,   228,   228,   228,
   228,   228,   nil,   nil,   nil,   nil,   nil,   228,   228,   228,
   228,   228,   228,   228,   nil,   nil,   228,   nil,   nil,   nil,
   nil,   nil,   nil,   228,   nil,   nil,   228,   228,   228,   228,
   228,   228,   228,   228,   nil,   228,   228,   228,   nil,   228,
   228,   228,   228,   228,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   228,   nil,   nil,   228,   nil,   nil,   228,
   228,   nil,   nil,   228,   nil,   nil,   nil,   nil,   nil,   228,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   228,   nil,   nil,
   nil,   nil,   228,   228,   228,   228,   nil,   228,   228,   nil,
   nil,   nil,   228,   228,   nil,   229,   229,   229,   nil,   229,
   228,   nil,   228,   229,   229,   nil,   nil,   nil,   229,   nil,
   229,   229,   229,   229,   229,   229,   229,   nil,   nil,   nil,
   nil,   nil,   229,   229,   229,   229,   229,   229,   229,   nil,
   nil,   229,   nil,   nil,   nil,   nil,   nil,   nil,   229,   nil,
   nil,   229,   229,   229,   229,   229,   229,   229,   229,   nil,
   229,   229,   229,   nil,   229,   229,   229,   229,   229,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   229,   nil,
   nil,   229,   nil,   nil,   229,   229,   nil,   nil,   229,   nil,
   nil,   nil,   nil,   nil,   229,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   229,   nil,   nil,   nil,   nil,   229,   229,   229,
   229,   nil,   229,   229,   nil,   nil,   nil,   229,   229,   nil,
   230,   230,   230,   nil,   230,   229,   nil,   229,   230,   230,
   nil,   nil,   nil,   230,   nil,   230,   230,   230,   230,   230,
   230,   230,   nil,   nil,   nil,   nil,   nil,   230,   230,   230,
   230,   230,   230,   230,   nil,   nil,   230,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   230,   230,   230,   230,
   230,   230,   230,   230,   nil,   230,   230,   230,   nil,   230,
   230,   230,   230,   230,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   230,   nil,   nil,   230,   nil,   nil,   230,
   230,   nil,   nil,   230,   nil,   nil,   nil,   nil,   nil,   230,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   230,   nil,   nil,
   nil,   nil,   230,   230,   230,   230,   nil,   230,   230,   nil,
   nil,   nil,   230,   230,   nil,   231,   231,   231,   nil,   231,
   230,   nil,   230,   231,   231,   nil,   nil,   nil,   231,   nil,
   231,   231,   231,   231,   231,   231,   231,   nil,   nil,   nil,
   nil,   nil,   231,   231,   231,   231,   231,   231,   231,   nil,
   nil,   231,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,
   nil,   231,   231,   231,   231,   231,   231,   231,   231,   nil,
   231,   231,   231,   nil,   231,   231,   231,   231,   231,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,
   nil,   231,   nil,   nil,   231,   231,   nil,   nil,   231,   nil,
   nil,   nil,   nil,   nil,   231,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   231,   nil,   nil,   nil,   nil,   231,   231,   231,
   231,   nil,   231,   231,   nil,   nil,   nil,   231,   231,   nil,
   232,   232,   232,   nil,   232,   231,   nil,   231,   232,   232,
   nil,   nil,   nil,   232,   nil,   232,   232,   232,   232,   232,
   232,   232,   nil,   nil,   nil,   nil,   nil,   232,   232,   232,
   232,   232,   232,   232,   nil,   nil,   232,   nil,   nil,   nil,
   nil,   nil,   nil,   232,   nil,   nil,   232,   232,   232,   232,
   232,   232,   232,   232,   nil,   232,   232,   232,   nil,   232,
   232,   232,   232,   232,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   232,   nil,   nil,   232,   nil,   nil,   232,
   232,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   232,   232,   232,   232,   nil,   232,   232,   nil,
   nil,   nil,   232,   232,   nil,   233,   233,   233,   nil,   233,
   232,   nil,   232,   233,   233,   nil,   nil,   nil,   233,   nil,
   233,   233,   233,   233,   233,   233,   233,   nil,   nil,   nil,
   nil,   nil,   233,   233,   233,   233,   233,   233,   233,   nil,
   nil,   233,   nil,   nil,   nil,   nil,   nil,   nil,   233,   nil,
   nil,   233,   233,   233,   233,   233,   233,   233,   233,   nil,
   233,   233,   233,   nil,   233,   233,   233,   233,   233,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   233,   nil,
   nil,   233,   nil,   nil,   233,   233,   nil,   nil,   233,   nil,
   nil,   nil,   nil,   nil,   233,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   233,   nil,   nil,   nil,   nil,   233,   233,   233,
   233,   nil,   233,   233,   nil,   nil,   nil,   233,   233,   nil,
   234,   234,   234,   nil,   234,   233,   nil,   233,   234,   234,
   nil,   nil,   nil,   234,   nil,   234,   234,   234,   234,   234,
   234,   234,   nil,   nil,   nil,   nil,   nil,   234,   234,   234,
   234,   234,   234,   234,   nil,   nil,   234,   nil,   nil,   nil,
   nil,   nil,   nil,   234,   nil,   nil,   234,   234,   234,   234,
   234,   234,   234,   234,   nil,   234,   234,   234,   nil,   234,
   234,   234,   234,   234,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   234,   nil,   nil,   234,   nil,   nil,   234,
   234,   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   234,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   234,   nil,   nil,
   nil,   nil,   234,   234,   234,   234,   nil,   234,   234,   nil,
   nil,   nil,   234,   234,   nil,   235,   235,   235,   nil,   235,
   234,   nil,   234,   235,   235,   nil,   nil,   nil,   235,   nil,
   235,   235,   235,   235,   235,   235,   235,   nil,   nil,   nil,
   nil,   nil,   235,   235,   235,   235,   235,   235,   235,   nil,
   nil,   235,   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,
   nil,   235,   235,   235,   235,   235,   235,   235,   235,   nil,
   235,   235,   235,   nil,   235,   235,   235,   235,   235,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,
   nil,   235,   nil,   nil,   235,   235,   nil,   nil,   235,   nil,
   nil,   nil,   nil,   nil,   235,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   235,   nil,   nil,   nil,   nil,   235,   235,   235,
   235,   nil,   235,   235,   nil,   nil,   nil,   235,   235,   nil,
   236,   236,   236,   nil,   236,   235,   nil,   235,   236,   236,
   nil,   nil,   nil,   236,   nil,   236,   236,   236,   236,   236,
   236,   236,   nil,   nil,   nil,   nil,   nil,   236,   236,   236,
   236,   236,   236,   236,   nil,   nil,   236,   nil,   nil,   nil,
   nil,   nil,   nil,   236,   nil,   nil,   236,   236,   236,   236,
   236,   236,   236,   236,   nil,   236,   236,   236,   nil,   236,
   236,   236,   236,   236,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   236,   nil,   nil,   236,   nil,   nil,   236,
   236,   nil,   nil,   236,   nil,   nil,   nil,   nil,   nil,   236,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,
   nil,   nil,   236,   236,   236,   236,   nil,   236,   236,   nil,
   nil,   nil,   236,   236,   nil,   237,   237,   237,   nil,   237,
   236,   nil,   236,   237,   237,   nil,   nil,   nil,   237,   nil,
   237,   237,   237,   237,   237,   237,   237,   nil,   nil,   nil,
   nil,   nil,   237,   237,   237,   237,   237,   237,   237,   nil,
   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,   237,   nil,
   nil,   237,   237,   237,   237,   237,   237,   237,   237,   nil,
   237,   237,   237,   nil,   237,   237,   237,   237,   237,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   237,   nil,
   nil,   237,   nil,   nil,   237,   237,   nil,   nil,   237,   nil,
   nil,   nil,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   237,   nil,   nil,   nil,   nil,   237,   237,   237,
   237,   nil,   237,   237,   nil,   nil,   nil,   237,   237,   nil,
   238,   238,   238,   nil,   238,   237,   nil,   237,   238,   238,
   nil,   nil,   nil,   238,   nil,   238,   238,   238,   238,   238,
   238,   238,   nil,   nil,   nil,   nil,   nil,   238,   238,   238,
   238,   238,   238,   238,   nil,   nil,   238,   nil,   nil,   nil,
   nil,   nil,   nil,   238,   nil,   nil,   238,   238,   238,   238,
   238,   238,   238,   238,   nil,   238,   238,   238,   nil,   238,
   238,   238,   238,   238,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   238,   nil,   nil,   238,   nil,   nil,   238,
   238,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   238,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   238,   nil,   nil,
   nil,   nil,   238,   238,   238,   238,   nil,   238,   238,   nil,
   nil,   nil,   238,   238,   nil,   239,   239,   239,   nil,   239,
   238,   nil,   238,   239,   239,   nil,   nil,   nil,   239,   nil,
   239,   239,   239,   239,   239,   239,   239,   nil,   nil,   nil,
   nil,   nil,   239,   239,   239,   239,   239,   239,   239,   nil,
   nil,   239,   nil,   nil,   nil,   nil,   nil,   nil,   239,   nil,
   nil,   239,   239,   239,   239,   239,   239,   239,   239,   nil,
   239,   239,   239,   nil,   239,   239,   239,   239,   239,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   239,   nil,
   nil,   239,   nil,   nil,   239,   239,   nil,   nil,   239,   nil,
   nil,   nil,   nil,   nil,   239,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   239,   nil,   nil,   nil,   nil,   239,   239,   239,
   239,   nil,   239,   239,   nil,   nil,   nil,   239,   239,   nil,
   240,   240,   240,   nil,   240,   239,   nil,   239,   240,   240,
   nil,   nil,   nil,   240,   nil,   240,   240,   240,   240,   240,
   240,   240,   nil,   nil,   nil,   nil,   nil,   240,   240,   240,
   240,   240,   240,   240,   nil,   nil,   240,   nil,   nil,   nil,
   nil,   nil,   nil,   240,   nil,   nil,   240,   240,   240,   240,
   240,   240,   240,   240,   nil,   240,   240,   240,   nil,   240,
   240,   240,   240,   240,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   240,   nil,   nil,   240,   nil,   nil,   240,
   240,   nil,   nil,   240,   nil,   nil,   nil,   nil,   nil,   240,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   240,   nil,   nil,
   nil,   nil,   240,   240,   240,   240,   nil,   240,   240,   nil,
   nil,   nil,   240,   240,   nil,   241,   241,   241,   nil,   241,
   240,   nil,   240,   241,   241,   nil,   nil,   nil,   241,   nil,
   241,   241,   241,   241,   241,   241,   241,   nil,   nil,   nil,
   nil,   nil,   241,   241,   241,   241,   241,   241,   241,   nil,
   nil,   241,   nil,   nil,   nil,   nil,   nil,   nil,   241,   nil,
   nil,   241,   241,   241,   241,   241,   241,   241,   241,   nil,
   241,   241,   241,   nil,   241,   241,   241,   241,   241,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   241,   nil,
   nil,   241,   nil,   nil,   241,   241,   nil,   nil,   241,   nil,
   nil,   nil,   nil,   nil,   241,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   241,   nil,   nil,   nil,   nil,   241,   241,   241,
   241,   nil,   241,   241,   nil,   nil,   nil,   241,   241,   nil,
   242,   242,   242,   nil,   242,   241,   nil,   241,   242,   242,
   nil,   nil,   nil,   242,   nil,   242,   242,   242,   242,   242,
   242,   242,   nil,   nil,   nil,   nil,   nil,   242,   242,   242,
   242,   242,   242,   242,   nil,   nil,   242,   nil,   nil,   nil,
   nil,   nil,   nil,   242,   nil,   nil,   242,   242,   242,   242,
   242,   242,   242,   242,   nil,   242,   242,   242,   nil,   242,
   242,   242,   242,   242,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   242,   nil,   nil,   242,   nil,   nil,   242,
   242,   nil,   nil,   242,   nil,   nil,   nil,   nil,   nil,   242,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   242,   nil,   nil,
   nil,   nil,   242,   242,   242,   242,   nil,   242,   242,   nil,
   nil,   nil,   242,   242,   nil,   243,   243,   243,   nil,   243,
   242,   nil,   242,   243,   243,   nil,   nil,   nil,   243,   nil,
   243,   243,   243,   243,   243,   243,   243,   nil,   nil,   nil,
   nil,   nil,   243,   243,   243,   243,   243,   243,   243,   nil,
   nil,   243,   nil,   nil,   nil,   nil,   nil,   nil,   243,   nil,
   nil,   243,   243,   243,   243,   243,   243,   243,   243,   nil,
   243,   243,   243,   nil,   243,   243,   243,   243,   243,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   243,   nil,
   nil,   243,   nil,   nil,   243,   243,   nil,   nil,   243,   nil,
   nil,   nil,   nil,   nil,   243,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   243,   nil,   nil,   nil,   nil,   243,   243,   243,
   243,   nil,   243,   243,   nil,   nil,   nil,   243,   243,   nil,
   244,   244,   244,   nil,   244,   243,   nil,   243,   244,   244,
   nil,   nil,   nil,   244,   nil,   244,   244,   244,   244,   244,
   244,   244,   nil,   nil,   nil,   nil,   nil,   244,   244,   244,
   244,   244,   244,   244,   nil,   nil,   244,   nil,   nil,   nil,
   nil,   nil,   nil,   244,   nil,   nil,   244,   244,   244,   244,
   244,   244,   244,   244,   nil,   244,   244,   244,   nil,   244,
   244,   244,   244,   244,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   244,   nil,   nil,   244,   nil,   nil,   244,
   244,   nil,   nil,   244,   nil,   nil,   nil,   nil,   nil,   244,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   244,   nil,   nil,
   nil,   nil,   244,   244,   244,   244,   nil,   244,   244,   nil,
   nil,   nil,   244,   244,   nil,   245,   245,   245,   nil,   245,
   244,   nil,   244,   245,   245,   nil,   nil,   nil,   245,   nil,
   245,   245,   245,   245,   245,   245,   245,   nil,   nil,   nil,
   nil,   nil,   245,   245,   245,   245,   245,   245,   245,   nil,
   nil,   245,   nil,   nil,   nil,   nil,   nil,   nil,   245,   nil,
   nil,   245,   245,   245,   245,   245,   245,   245,   245,   nil,
   245,   245,   245,   nil,   245,   245,   245,   245,   245,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   245,   nil,
   nil,   245,   nil,   nil,   245,   245,   nil,   nil,   245,   nil,
   nil,   nil,   nil,   nil,   245,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   245,   nil,   nil,   nil,   nil,   245,   245,   245,
   245,   nil,   245,   245,   nil,   nil,   nil,   245,   245,   nil,
   246,   246,   246,   nil,   246,   245,   nil,   245,   246,   246,
   nil,   nil,   nil,   246,   nil,   246,   246,   246,   246,   246,
   246,   246,   nil,   nil,   nil,   nil,   nil,   246,   246,   246,
   246,   246,   246,   246,   nil,   nil,   246,   nil,   nil,   nil,
   nil,   nil,   nil,   246,   nil,   nil,   246,   246,   246,   246,
   246,   246,   246,   246,   nil,   246,   246,   246,   nil,   246,
   246,   246,   246,   246,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   246,   nil,   nil,   246,   nil,   nil,   246,
   246,   nil,   nil,   246,   nil,   nil,   nil,   nil,   nil,   246,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   246,   nil,   nil,
   nil,   nil,   246,   246,   246,   246,   nil,   246,   246,   nil,
   nil,   nil,   246,   246,   nil,   247,   247,   247,   nil,   247,
   246,   nil,   246,   247,   247,   nil,   nil,   nil,   247,   nil,
   247,   247,   247,   247,   247,   247,   247,   nil,   nil,   nil,
   nil,   nil,   247,   247,   247,   247,   247,   247,   247,   nil,
   nil,   247,   nil,   nil,   nil,   nil,   nil,   nil,   247,   nil,
   nil,   247,   247,   247,   247,   247,   247,   247,   247,   nil,
   247,   247,   247,   nil,   247,   247,   247,   247,   247,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   247,   nil,
   nil,   247,   nil,   nil,   247,   247,   nil,   nil,   247,   nil,
   nil,   nil,   nil,   nil,   247,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   247,   nil,   nil,   nil,   nil,   247,   247,   247,
   247,   nil,   247,   247,   nil,   nil,   nil,   247,   247,   nil,
   248,   248,   248,   nil,   248,   247,   nil,   247,   248,   248,
   nil,   nil,   nil,   248,   nil,   248,   248,   248,   248,   248,
   248,   248,   nil,   nil,   nil,   nil,   nil,   248,   248,   248,
   248,   248,   248,   248,   nil,   nil,   248,   nil,   nil,   nil,
   nil,   nil,   nil,   248,   nil,   nil,   248,   248,   248,   248,
   248,   248,   248,   248,   nil,   248,   248,   248,   nil,   248,
   248,   248,   248,   248,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   248,   nil,   nil,   248,   nil,   nil,   248,
   248,   nil,   nil,   248,   nil,   nil,   nil,   nil,   nil,   248,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   248,   nil,   nil,
   nil,   nil,   248,   248,   248,   248,   nil,   248,   248,   nil,
   nil,   nil,   248,   248,   nil,   249,   249,   249,   nil,   249,
   248,   nil,   248,   249,   249,   nil,   nil,   nil,   249,   nil,
   249,   249,   249,   249,   249,   249,   249,   nil,   nil,   nil,
   nil,   nil,   249,   249,   249,   249,   249,   249,   249,   nil,
   nil,   249,   nil,   nil,   nil,   nil,   nil,   nil,   249,   nil,
   nil,   249,   249,   249,   249,   249,   249,   249,   249,   nil,
   249,   249,   249,   nil,   249,   249,   249,   249,   249,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   249,   nil,
   nil,   249,   nil,   nil,   249,   249,   nil,   nil,   249,   nil,
   nil,   nil,   nil,   nil,   249,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   249,   nil,   nil,   nil,   nil,   249,   249,   249,
   249,   nil,   249,   249,   nil,   nil,   nil,   249,   249,   nil,
   255,   255,   255,   nil,   255,   249,   nil,   249,   255,   255,
   nil,   nil,   nil,   255,   nil,   255,   255,   255,   255,   255,
   255,   255,   nil,   nil,   nil,   nil,   nil,   255,   255,   255,
   255,   255,   255,   255,   nil,   nil,   255,   nil,   nil,   nil,
   nil,   nil,   nil,   255,   nil,   nil,   255,   255,   255,   255,
   255,   255,   255,   255,   255,   255,   255,   255,   nil,   255,
   255,   255,   255,   255,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   255,   nil,   nil,   255,   nil,   nil,   255,
   255,   nil,   nil,   255,   nil,   255,   nil,   255,   nil,   255,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   255,   nil,   nil,
   nil,   nil,   255,   255,   255,   255,   nil,   255,   255,   nil,
   nil,   nil,   255,   255,   nil,   256,   256,   256,   nil,   256,
   255,   nil,   255,   256,   256,   nil,   nil,   nil,   256,   nil,
   256,   256,   256,   256,   256,   256,   256,   nil,   nil,   nil,
   nil,   nil,   256,   256,   256,   256,   256,   256,   256,   nil,
   nil,   256,   nil,   nil,   nil,   nil,   nil,   nil,   256,   nil,
   nil,   256,   256,   256,   256,   256,   256,   256,   256,   256,
   256,   256,   256,   nil,   256,   256,   256,   256,   256,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   256,   nil,
   nil,   256,   nil,   nil,   256,   256,   nil,   nil,   256,   nil,
   256,   nil,   256,   nil,   256,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   256,   nil,   nil,   nil,   nil,   256,   256,   256,
   256,   nil,   256,   256,   nil,   nil,   nil,   256,   256,   nil,
   264,   264,   264,   nil,   264,   256,   nil,   256,   264,   264,
   nil,   nil,   nil,   264,   nil,   264,   264,   264,   264,   264,
   264,   264,   nil,   nil,   nil,   nil,   nil,   264,   264,   264,
   264,   264,   264,   264,   nil,   nil,   264,   nil,   nil,   nil,
   nil,   nil,   nil,   264,   nil,   nil,   264,   264,   264,   264,
   264,   264,   264,   264,   264,   264,   264,   264,   nil,   264,
   264,   264,   264,   264,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   264,   nil,   nil,   264,   nil,   nil,   264,
   264,   nil,   nil,   264,   nil,   264,   nil,   264,   nil,   264,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   264,   nil,   nil,
   nil,   nil,   264,   264,   264,   264,   nil,   264,   264,   nil,
   nil,   nil,   264,   264,   264,   271,   271,   271,   nil,   271,
   264,   nil,   264,   271,   271,   nil,   nil,   nil,   271,   nil,
   271,   271,   271,   271,   271,   271,   271,   nil,   nil,   nil,
   nil,   nil,   271,   271,   271,   271,   271,   271,   271,   nil,
   nil,   271,   nil,   nil,   nil,   nil,   nil,   nil,   271,   nil,
   nil,   271,   271,   271,   271,   271,   271,   271,   271,   nil,
   271,   271,   271,   nil,   271,   271,   271,   271,   271,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   271,   nil,
   nil,   271,   nil,   nil,   271,   271,   nil,   nil,   271,   nil,
   nil,   nil,   nil,   nil,   271,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   271,   nil,   nil,   nil,   nil,   271,   271,   271,
   271,   nil,   271,   271,   nil,   nil,   nil,   271,   271,   nil,
   273,   273,   273,   nil,   273,   271,   nil,   271,   273,   273,
   nil,   nil,   nil,   273,   nil,   273,   273,   273,   273,   273,
   273,   273,   nil,   nil,   nil,   nil,   nil,   273,   273,   273,
   273,   273,   273,   273,   nil,   nil,   273,   nil,   nil,   nil,
   nil,   nil,   nil,   273,   nil,   nil,   273,   273,   273,   273,
   273,   273,   273,   273,   nil,   273,   273,   273,   nil,   273,
   273,   273,   273,   273,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   273,   nil,   nil,   273,   nil,   nil,   273,
   273,   nil,   nil,   273,   nil,   nil,   nil,   nil,   nil,   273,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   273,   nil,   nil,
   nil,   nil,   273,   273,   273,   273,   nil,   273,   273,   nil,
   nil,   nil,   273,   273,   nil,   275,   275,   275,   nil,   275,
   273,   nil,   273,   275,   275,   nil,   nil,   nil,   275,   nil,
   275,   275,   275,   275,   275,   275,   275,   nil,   nil,   nil,
   nil,   nil,   275,   275,   275,   275,   275,   275,   275,   nil,
   nil,   275,   nil,   nil,   nil,   nil,   nil,   nil,   275,   nil,
   nil,   275,   275,   275,   275,   275,   275,   275,   275,   nil,
   275,   275,   275,   nil,   275,   275,   275,   275,   275,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   275,   nil,
   nil,   275,   nil,   nil,   275,   275,   nil,   nil,   275,   nil,
   nil,   nil,   nil,   nil,   275,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   275,   nil,   nil,   nil,   nil,   275,   275,   275,
   275,   nil,   275,   275,   nil,   nil,   nil,   275,   275,   nil,
   280,   280,   280,   280,   280,   275,   nil,   275,   280,   280,
   nil,   nil,   nil,   280,   nil,   280,   280,   280,   280,   280,
   280,   280,   nil,   nil,   nil,   nil,   nil,   280,   280,   280,
   280,   280,   280,   280,   nil,   nil,   280,   nil,   nil,   nil,
   nil,   nil,   280,   280,   nil,   280,   280,   280,   280,   280,
   280,   280,   280,   280,   nil,   280,   280,   280,   nil,   280,
   280,   280,   280,   280,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   280,   nil,   nil,   280,   nil,   nil,   280,
   280,   nil,   nil,   280,   nil,   280,   nil,   nil,   nil,   280,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   280,   nil,   nil,
   nil,   nil,   280,   280,   280,   280,   nil,   280,   280,   nil,
   nil,   nil,   280,   280,   nil,   286,   286,   286,   nil,   286,
   280,   nil,   280,   286,   286,   nil,   nil,   nil,   286,   nil,
   286,   286,   286,   286,   286,   286,   286,   nil,   nil,   nil,
   nil,   nil,   286,   286,   286,   286,   286,   286,   286,   nil,
   nil,   286,   nil,   nil,   nil,   nil,   nil,   nil,   286,   nil,
   nil,   286,   286,   286,   286,   286,   286,   286,   286,   nil,
   286,   286,   286,   nil,   286,   286,   nil,   nil,   286,   412,
   412,   412,   412,   412,   412,   412,   412,   412,   412,   412,
   nil,   412,   412,   nil,   nil,   412,   412,   nil,   286,   nil,
   nil,   286,   nil,   nil,   286,   286,   nil,   nil,   286,   nil,
   nil,   412,   nil,   412,   nil,   412,   412,   412,   412,   412,
   412,   412,   nil,   412,   nil,   nil,   nil,   286,   286,   286,
   286,   nil,   286,   286,   nil,   nil,   nil,   286,   286,   nil,
   412,   nil,   286,   nil,   nil,   286,   nil,   286,   303,   303,
   303,   nil,   303,   nil,   nil,   nil,   303,   303,   nil,   nil,
   nil,   303,   nil,   303,   303,   303,   303,   303,   303,   303,
   nil,   nil,   nil,   nil,   nil,   303,   303,   303,   303,   303,
   303,   303,   nil,   nil,   303,   nil,   nil,   nil,   nil,   nil,
   nil,   303,   nil,   nil,   303,   303,   303,   303,   303,   303,
   303,   303,   nil,   303,   303,   303,   nil,   303,   303,   nil,
   nil,   303,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   303,   nil,   nil,   303,   nil,   nil,   303,   303,   nil,
   nil,   303,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   303,   303,   303,   303,   nil,   303,   303,   nil,   nil,   nil,
   303,   303,   nil,   312,   312,   312,   nil,   312,   303,   nil,
   303,   312,   312,   nil,   nil,   nil,   312,   nil,   312,   312,
   312,   312,   312,   312,   312,   nil,   nil,   nil,   nil,   nil,
   312,   312,   312,   312,   312,   312,   312,   nil,   nil,   312,
   nil,   nil,   nil,   nil,   nil,   nil,   312,   nil,   nil,   312,
   312,   312,   312,   312,   312,   312,   312,   nil,   312,   312,
   312,   nil,   312,   312,   312,   312,   312,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   312,   nil,   nil,   312,
   312,   nil,   312,   312,   nil,   nil,   312,   nil,   nil,   nil,
   nil,   nil,   312,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   312,   nil,   nil,   nil,   nil,   312,   312,   312,   312,   nil,
   312,   312,   nil,   nil,   nil,   312,   312,   nil,   315,   315,
   315,   nil,   315,   312,   nil,   312,   315,   315,   nil,   nil,
   nil,   315,   nil,   315,   315,   315,   315,   315,   315,   315,
   nil,   nil,   nil,   nil,   nil,   315,   315,   315,   315,   315,
   315,   315,   nil,   nil,   315,   nil,   nil,   nil,   nil,   nil,
   nil,   315,   nil,   nil,   315,   315,   315,   315,   315,   315,
   315,   315,   nil,   315,   315,   315,   nil,   315,   315,   315,
   315,   315,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   315,   nil,   nil,   315,   nil,   nil,   315,   315,   nil,
   nil,   315,   nil,   nil,   nil,   nil,   nil,   315,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   315,   nil,   nil,   nil,   nil,
   315,   315,   315,   315,   nil,   315,   315,   nil,   nil,   nil,
   315,   315,   nil,   328,   328,   328,   nil,   328,   315,   nil,
   315,   328,   328,   nil,   nil,   nil,   328,   nil,   328,   328,
   328,   328,   328,   328,   328,   nil,   nil,   nil,   nil,   nil,
   328,   328,   328,   328,   328,   328,   328,   nil,   nil,   328,
   nil,   nil,   nil,   nil,   nil,   nil,   328,   nil,   nil,   328,
   328,   328,   328,   328,   328,   328,   328,   nil,   328,   328,
   328,   nil,   328,   328,   328,   328,   328,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   328,   nil,   nil,   328,
   nil,   nil,   328,   328,   nil,   nil,   328,   nil,   nil,   nil,
   nil,   nil,   328,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   328,   nil,   nil,   nil,   nil,   328,   328,   328,   328,   nil,
   328,   328,   nil,   nil,   nil,   328,   328,   nil,   329,   329,
   329,   nil,   329,   328,   nil,   328,   329,   329,   nil,   nil,
   nil,   329,   nil,   329,   329,   329,   329,   329,   329,   329,
   nil,   nil,   nil,   nil,   nil,   329,   329,   329,   329,   329,
   329,   329,   nil,   nil,   329,   nil,   nil,   nil,   nil,   nil,
   nil,   329,   nil,   nil,   329,   329,   329,   329,   329,   329,
   329,   329,   nil,   329,   329,   329,   nil,   329,   329,   329,
   329,   329,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   329,   nil,   nil,   329,   nil,   nil,   329,   329,   nil,
   nil,   329,   nil,   nil,   nil,   nil,   nil,   329,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   329,   nil,   nil,   nil,   nil,
   329,   329,   329,   329,   nil,   329,   329,   nil,   nil,   nil,
   329,   329,   nil,   348,   348,   348,   nil,   348,   329,   nil,
   329,   348,   348,   nil,   nil,   nil,   348,   nil,   348,   348,
   348,   348,   348,   348,   348,   nil,   nil,   nil,   nil,   nil,
   348,   348,   348,   348,   348,   348,   348,   nil,   nil,   348,
   nil,   nil,   nil,   nil,   nil,   nil,   348,   nil,   nil,   348,
   348,   348,   348,   348,   348,   348,   348,   nil,   348,   348,
   348,   nil,   348,   348,   348,   348,   348,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   348,   nil,   nil,   348,
   nil,   nil,   348,   348,   nil,   nil,   348,   nil,   nil,   nil,
   nil,   nil,   348,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   348,   nil,   nil,   nil,   nil,   348,   348,   348,   348,   nil,
   348,   348,   nil,   nil,   nil,   348,   348,   nil,   364,   364,
   364,   nil,   364,   348,   nil,   348,   364,   364,   nil,   nil,
   nil,   364,   nil,   364,   364,   364,   364,   364,   364,   364,
   nil,   nil,   nil,   nil,   nil,   364,   364,   364,   364,   364,
   364,   364,   nil,   nil,   364,   nil,   nil,   nil,   nil,   nil,
   nil,   364,   nil,   nil,   364,   364,   364,   364,   364,   364,
   364,   364,   nil,   364,   364,   364,   nil,   364,   364,   364,
   364,   364,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   364,   nil,   nil,   364,   nil,   nil,   364,   364,   nil,
   nil,   364,   nil,   nil,   nil,   nil,   nil,   364,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   364,   nil,   nil,   nil,   nil,
   364,   364,   364,   364,   nil,   364,   364,   nil,   nil,   nil,
   364,   364,   nil,   390,   390,   390,   nil,   390,   364,   nil,
   364,   390,   390,   nil,   nil,   nil,   390,   nil,   390,   390,
   390,   390,   390,   390,   390,   nil,   nil,   nil,   nil,   nil,
   390,   390,   390,   390,   390,   390,   390,   nil,   nil,   390,
   nil,   nil,   nil,   nil,   nil,   nil,   390,   nil,   nil,   390,
   390,   390,   390,   390,   390,   390,   390,   nil,   390,   390,
   390,   nil,   390,   390,   390,   390,   390,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   390,   nil,   nil,   390,
   nil,   nil,   390,   390,   nil,   nil,   390,   nil,   nil,   nil,
   nil,   nil,   390,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   390,   nil,   nil,   nil,   nil,   390,   390,   390,   390,   nil,
   390,   390,   nil,   nil,   nil,   390,   390,   nil,   427,   427,
   427,   nil,   427,   390,   nil,   390,   427,   427,   nil,   nil,
   nil,   427,   nil,   427,   427,   427,   427,   427,   427,   427,
   nil,   nil,   nil,   nil,   nil,   427,   427,   427,   427,   427,
   427,   427,   nil,   nil,   427,   nil,   nil,   nil,   nil,   nil,
   nil,   427,   nil,   nil,   427,   427,   427,   427,   427,   427,
   427,   427,   427,   427,   427,   427,   nil,   427,   427,   427,
   427,   427,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   427,   nil,   nil,   427,   nil,   nil,   427,   427,   nil,
   nil,   427,   nil,   427,   nil,   427,   nil,   427,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   427,   nil,   nil,   nil,   nil,
   427,   427,   427,   427,   nil,   427,   427,   nil,   nil,   nil,
   427,   427,   nil,   429,   429,   429,   nil,   429,   427,   nil,
   427,   429,   429,   nil,   nil,   nil,   429,   nil,   429,   429,
   429,   429,   429,   429,   429,   nil,   nil,   nil,   nil,   nil,
   429,   429,   429,   429,   429,   429,   429,   nil,   nil,   429,
   nil,   nil,   nil,   nil,   nil,   nil,   429,   nil,   nil,   429,
   429,   429,   429,   429,   429,   429,   429,   nil,   429,   429,
   429,   nil,   429,   429,   429,   429,   429,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   429,   nil,   nil,   429,
   nil,   nil,   429,   429,   nil,   nil,   429,   nil,   nil,   nil,
   nil,   nil,   429,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   429,   nil,   nil,   nil,   nil,   429,   429,   429,   429,   nil,
   429,   429,   nil,   nil,   nil,   429,   429,   nil,   430,   430,
   430,   nil,   430,   429,   nil,   429,   430,   430,   nil,   nil,
   nil,   430,   nil,   430,   430,   430,   430,   430,   430,   430,
   nil,   nil,   nil,   nil,   nil,   430,   430,   430,   430,   430,
   430,   430,   nil,   nil,   430,   nil,   nil,   nil,   nil,   nil,
   nil,   430,   nil,   nil,   430,   430,   430,   430,   430,   430,
   430,   430,   nil,   430,   430,   430,   nil,   430,   430,   430,
   430,   430,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   430,   nil,   nil,   430,   nil,   nil,   430,   430,   nil,
   nil,   430,   nil,   nil,   nil,   nil,   nil,   430,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   430,   nil,   nil,   nil,   nil,
   430,   430,   430,   430,   nil,   430,   430,   nil,   nil,   nil,
   430,   430,   nil,   431,   431,   431,   nil,   431,   430,   nil,
   430,   431,   431,   nil,   nil,   nil,   431,   nil,   431,   431,
   431,   431,   431,   431,   431,   nil,   nil,   nil,   nil,   nil,
   431,   431,   431,   431,   431,   431,   431,   nil,   nil,   431,
   nil,   nil,   nil,   nil,   nil,   nil,   431,   nil,   nil,   431,
   431,   431,   431,   431,   431,   431,   431,   nil,   431,   431,
   431,   nil,   431,   431,   431,   431,   431,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   431,   nil,   nil,   431,
   nil,   nil,   431,   431,   nil,   nil,   431,   nil,   nil,   nil,
   nil,   nil,   431,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   431,   nil,   nil,   nil,   nil,   431,   431,   431,   431,   nil,
   431,   431,   nil,   nil,   nil,   431,   431,   nil,   472,   472,
   472,   nil,   472,   431,   nil,   431,   472,   472,   nil,   nil,
   nil,   472,   nil,   472,   472,   472,   472,   472,   472,   472,
   nil,   nil,   nil,   nil,   nil,   472,   472,   472,   472,   472,
   472,   472,   nil,   nil,   472,   nil,   nil,   nil,   nil,   nil,
   nil,   472,   nil,   nil,   472,   472,   472,   472,   472,   472,
   472,   472,   472,   472,   472,   472,   nil,   472,   472,   472,
   472,   472,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   472,   nil,   nil,   472,   nil,   nil,   472,   472,   nil,
   nil,   472,   nil,   472,   nil,   472,   nil,   472,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   472,   nil,   nil,   nil,   nil,
   472,   472,   472,   472,   nil,   472,   472,   nil,   nil,   nil,
   472,   472,   nil,   474,   474,   474,   nil,   474,   472,   nil,
   472,   474,   474,   nil,   nil,   nil,   474,   nil,   474,   474,
   474,   474,   474,   474,   474,   nil,   nil,   nil,   nil,   nil,
   474,   474,   474,   474,   474,   474,   474,   nil,   nil,   474,
   nil,   nil,   nil,   nil,   nil,   nil,   474,   nil,   nil,   474,
   474,   474,   474,   474,   474,   474,   474,   474,   474,   474,
   474,   nil,   474,   474,   474,   474,   474,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   474,   nil,   nil,   474,
   nil,   nil,   474,   474,   nil,   nil,   474,   nil,   nil,   nil,
   474,   nil,   474,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   474,   nil,   nil,   nil,   nil,   474,   474,   474,   474,   nil,
   474,   474,   nil,   nil,   nil,   474,   474,   nil,   476,   476,
   476,   nil,   476,   474,   nil,   474,   476,   476,   nil,   nil,
   nil,   476,   nil,   476,   476,   476,   476,   476,   476,   476,
   nil,   nil,   nil,   nil,   nil,   476,   476,   476,   476,   476,
   476,   476,   nil,   nil,   476,   nil,   nil,   nil,   nil,   nil,
   nil,   476,   nil,   nil,   476,   476,   476,   476,   476,   476,
   476,   476,   nil,   476,   476,   476,   nil,   476,   476,   476,
   476,   476,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   476,   nil,   nil,   476,   nil,   nil,   476,   476,   nil,
   nil,   476,   nil,   nil,   nil,   nil,   nil,   476,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   476,   nil,   nil,   nil,   nil,
   476,   476,   476,   476,   nil,   476,   476,   nil,   nil,   nil,
   476,   476,   nil,   480,   480,   480,   480,   480,   476,   nil,
   476,   480,   480,   nil,   nil,   nil,   480,   nil,   480,   480,
   480,   480,   480,   480,   480,   nil,   nil,   nil,   nil,   nil,
   480,   480,   480,   480,   480,   480,   480,   nil,   nil,   480,
   nil,   nil,   nil,   nil,   nil,   480,   480,   nil,   480,   480,
   480,   480,   480,   480,   480,   480,   480,   nil,   480,   480,
   480,   nil,   480,   480,   480,   480,   480,   457,   457,   457,
   457,   457,   457,   457,   457,   457,   457,   457,   nil,   457,
   457,   nil,   nil,   457,   457,   nil,   480,   nil,   nil,   480,
   nil,   nil,   480,   480,   nil,   nil,   480,   nil,   480,   457,
   nil,   457,   480,   457,   457,   457,   457,   457,   457,   457,
   480,   457,   nil,   nil,   nil,   480,   480,   480,   480,   nil,
   480,   480,   nil,   nil,   nil,   480,   480,   457,   457,   nil,
   nil,   nil,   480,   480,   nil,   480,   487,   487,   487,   nil,
   487,   nil,   nil,   nil,   487,   487,   nil,   nil,   nil,   487,
   nil,   487,   487,   487,   487,   487,   487,   487,   nil,   nil,
   nil,   nil,   nil,   487,   487,   487,   487,   487,   487,   487,
   nil,   nil,   487,   nil,   nil,   nil,   nil,   nil,   nil,   487,
   nil,   nil,   487,   487,   487,   487,   487,   487,   487,   487,
   nil,   487,   487,   487,   nil,   487,   487,   nil,   nil,   487,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   487,
   nil,   nil,   487,   nil,   nil,   487,   487,   nil,   nil,   487,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   487,   487,
   487,   487,   nil,   487,   487,   nil,   nil,   nil,   487,   487,
   nil,   489,   489,   489,   nil,   489,   487,   nil,   487,   489,
   489,   nil,   nil,   nil,   489,   nil,   489,   489,   489,   489,
   489,   489,   489,   nil,   nil,   nil,   nil,   nil,   489,   489,
   489,   489,   489,   489,   489,   nil,   nil,   489,   nil,   nil,
   nil,   nil,   nil,   nil,   489,   nil,   nil,   489,   489,   489,
   489,   489,   489,   489,   489,   489,   489,   489,   489,   nil,
   489,   489,   489,   489,   489,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   489,   nil,   nil,   489,   nil,   nil,
   489,   489,   nil,   nil,   489,   nil,   489,   nil,   489,   nil,
   489,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   489,   nil,
   nil,   nil,   nil,   489,   489,   489,   489,   nil,   489,   489,
   nil,   nil,   nil,   489,   489,   nil,   496,   496,   496,   nil,
   496,   489,   nil,   489,   496,   496,   nil,   nil,   nil,   496,
   nil,   496,   496,   496,   496,   496,   496,   496,   nil,   nil,
   nil,   nil,   nil,   496,   496,   496,   496,   496,   496,   496,
   nil,   nil,   496,   nil,   nil,   nil,   nil,   nil,   nil,   496,
   nil,   nil,   496,   496,   496,   496,   496,   496,   496,   496,
   nil,   496,   496,   496,   nil,   496,   496,   nil,   nil,   496,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   496,
   nil,   nil,   496,   nil,   nil,   496,   496,   nil,   nil,   496,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   496,   496,
   496,   496,   nil,   496,   496,   nil,   nil,   nil,   496,   496,
   nil,   499,   499,   499,   nil,   499,   496,   nil,   496,   499,
   499,   nil,   nil,   nil,   499,   nil,   499,   499,   499,   499,
   499,   499,   499,   nil,   nil,   nil,   nil,   nil,   499,   499,
   499,   499,   499,   499,   499,   nil,   nil,   499,   nil,   nil,
   nil,   nil,   nil,   nil,   499,   nil,   nil,   499,   499,   499,
   499,   499,   499,   499,   499,   nil,   499,   499,   499,   nil,
   499,   499,   499,   499,   499,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   499,   nil,   nil,   499,   nil,   nil,
   499,   499,   nil,   nil,   499,   nil,   nil,   nil,   nil,   nil,
   499,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   499,   nil,
   nil,   nil,   nil,   499,   499,   499,   499,   nil,   499,   499,
   nil,   nil,   nil,   499,   499,   nil,   500,   500,   500,   nil,
   500,   499,   nil,   499,   500,   500,   nil,   nil,   nil,   500,
   nil,   500,   500,   500,   500,   500,   500,   500,   nil,   nil,
   nil,   nil,   nil,   500,   500,   500,   500,   500,   500,   500,
   nil,   nil,   500,   nil,   nil,   nil,   nil,   nil,   nil,   500,
   nil,   nil,   500,   500,   500,   500,   500,   500,   500,   500,
   nil,   500,   500,   500,   nil,   500,   500,   500,   500,   500,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   500,
   nil,   nil,   500,   nil,   nil,   500,   500,   nil,   nil,   500,
   nil,   nil,   nil,   nil,   nil,   500,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   500,   nil,   nil,   nil,   nil,   500,   500,
   500,   500,   nil,   500,   500,   nil,   nil,   nil,   500,   500,
   nil,   501,   501,   501,   nil,   501,   500,   nil,   500,   501,
   501,   nil,   nil,   nil,   501,   nil,   501,   501,   501,   501,
   501,   501,   501,   nil,   nil,   nil,   nil,   nil,   501,   501,
   501,   501,   501,   501,   501,   nil,   nil,   501,   nil,   nil,
   nil,   nil,   nil,   nil,   501,   nil,   nil,   501,   501,   501,
   501,   501,   501,   501,   501,   nil,   501,   501,   501,   nil,
   501,   501,   501,   501,   501,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   501,   nil,   nil,   501,   nil,   nil,
   501,   501,   nil,   nil,   501,   nil,   nil,   nil,   nil,   nil,
   501,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   501,   nil,
   nil,   nil,   nil,   501,   501,   501,   501,   nil,   501,   501,
   nil,   nil,   nil,   501,   501,   nil,   505,   505,   505,   nil,
   505,   501,   nil,   501,   505,   505,   nil,   nil,   nil,   505,
   nil,   505,   505,   505,   505,   505,   505,   505,   nil,   nil,
   nil,   nil,   nil,   505,   505,   505,   505,   505,   505,   505,
   nil,   nil,   505,   nil,   nil,   nil,   nil,   nil,   nil,   505,
   nil,   nil,   505,   505,   505,   505,   505,   505,   505,   505,
   nil,   505,   505,   505,   nil,   505,   505,   505,   505,   505,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   505,
   nil,   nil,   505,   nil,   nil,   505,   505,   nil,   nil,   505,
   nil,   nil,   nil,   nil,   nil,   505,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   505,   nil,   nil,   nil,   nil,   505,   505,
   505,   505,   nil,   505,   505,   nil,   nil,   nil,   505,   505,
   nil,   507,   507,   507,   nil,   507,   505,   nil,   505,   507,
   507,   nil,   nil,   nil,   507,   nil,   507,   507,   507,   507,
   507,   507,   507,   nil,   nil,   nil,   nil,   nil,   507,   507,
   507,   507,   507,   507,   507,   nil,   nil,   507,   nil,   nil,
   nil,   nil,   nil,   nil,   507,   nil,   nil,   507,   507,   507,
   507,   507,   507,   507,   507,   nil,   507,   507,   507,   nil,
   507,   507,   507,   507,   507,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   507,   nil,   nil,   507,   nil,   nil,
   507,   507,   nil,   nil,   507,   nil,   507,   nil,   nil,   nil,
   507,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   507,   nil,
   nil,   nil,   nil,   507,   507,   507,   507,   nil,   507,   507,
   nil,   nil,   nil,   507,   507,   nil,   511,   511,   511,   nil,
   511,   507,   nil,   507,   511,   511,   nil,   nil,   nil,   511,
   nil,   511,   511,   511,   511,   511,   511,   511,   nil,   nil,
   nil,   nil,   nil,   511,   511,   511,   511,   511,   511,   511,
   nil,   nil,   511,   nil,   nil,   nil,   nil,   nil,   nil,   511,
   nil,   nil,   511,   511,   511,   511,   511,   511,   511,   511,
   511,   511,   511,   511,   nil,   511,   511,   511,   511,   511,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   511,
   nil,   nil,   511,   nil,   nil,   511,   511,   nil,   nil,   511,
   nil,   511,   nil,   nil,   nil,   511,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   511,   nil,   nil,   nil,   nil,   511,   511,
   511,   511,   nil,   511,   511,   nil,   nil,   nil,   511,   511,
   nil,   514,   514,   514,   nil,   514,   511,   nil,   511,   514,
   514,   nil,   nil,   nil,   514,   nil,   514,   514,   514,   514,
   514,   514,   514,   nil,   nil,   nil,   nil,   nil,   514,   514,
   514,   514,   514,   514,   514,   nil,   nil,   514,   nil,   nil,
   nil,   nil,   nil,   nil,   514,   nil,   nil,   514,   514,   514,
   514,   514,   514,   514,   514,   514,   514,   514,   514,   nil,
   514,   514,   514,   514,   514,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   514,   nil,   nil,   514,   nil,   nil,
   514,   514,   nil,   nil,   514,   nil,   nil,   nil,   nil,   nil,
   514,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   514,   nil,
   nil,   nil,   nil,   514,   514,   514,   514,   nil,   514,   514,
   nil,   nil,   nil,   514,   514,   nil,   528,   528,   528,   nil,
   528,   514,   nil,   514,   528,   528,   nil,   nil,   nil,   528,
   nil,   528,   528,   528,   528,   528,   528,   528,   nil,   nil,
   nil,   nil,   nil,   528,   528,   528,   528,   528,   528,   528,
   nil,   nil,   528,   nil,   nil,   nil,   nil,   nil,   nil,   528,
   nil,   nil,   528,   528,   528,   528,   528,   528,   528,   528,
   nil,   528,   528,   528,   nil,   528,   528,   528,   528,   528,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   528,
   nil,   nil,   528,   nil,   nil,   528,   528,   nil,   nil,   528,
   nil,   528,   nil,   nil,   nil,   528,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   528,   nil,   nil,   nil,   nil,   528,   528,
   528,   528,   nil,   528,   528,   nil,   nil,   nil,   528,   528,
   nil,   529,   529,   529,   nil,   529,   528,   nil,   528,   529,
   529,   nil,   nil,   nil,   529,   nil,   529,   529,   529,   529,
   529,   529,   529,   nil,   nil,   nil,   nil,   nil,   529,   529,
   529,   529,   529,   529,   529,   nil,   nil,   529,   nil,   nil,
   nil,   nil,   nil,   nil,   529,   nil,   nil,   529,   529,   529,
   529,   529,   529,   529,   529,   529,   529,   529,   529,   nil,
   529,   529,   529,   529,   529,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   529,   nil,   nil,   529,   nil,   nil,
   529,   529,   nil,   nil,   529,   nil,   529,   nil,   529,   nil,
   529,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   529,   nil,
   nil,   nil,   nil,   529,   529,   529,   529,   nil,   529,   529,
   nil,   nil,   nil,   529,   529,   nil,   539,   539,   539,   nil,
   539,   529,   nil,   529,   539,   539,   nil,   nil,   nil,   539,
   nil,   539,   539,   539,   539,   539,   539,   539,   nil,   nil,
   nil,   nil,   nil,   539,   539,   539,   539,   539,   539,   539,
   nil,   nil,   539,   nil,   nil,   nil,   nil,   nil,   nil,   539,
   nil,   nil,   539,   539,   539,   539,   539,   539,   539,   539,
   539,   539,   539,   539,   nil,   539,   539,   539,   539,   539,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   539,
   nil,   nil,   539,   nil,   nil,   539,   539,   nil,   nil,   539,
   nil,   539,   nil,   539,   nil,   539,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   539,   nil,   nil,   nil,   nil,   539,   539,
   539,   539,   nil,   539,   539,   nil,   nil,   nil,   539,   539,
   nil,   542,   542,   542,   nil,   542,   539,   nil,   539,   542,
   542,   nil,   nil,   nil,   542,   nil,   542,   542,   542,   542,
   542,   542,   542,   nil,   nil,   nil,   nil,   nil,   542,   542,
   542,   542,   542,   542,   542,   nil,   nil,   542,   nil,   nil,
   nil,   nil,   nil,   nil,   542,   nil,   nil,   542,   542,   542,
   542,   542,   542,   542,   542,   nil,   542,   542,   542,   nil,
   542,   542,   542,   542,   542,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   542,   nil,   nil,   542,   nil,   nil,
   542,   542,   nil,   nil,   542,   nil,   nil,   nil,   nil,   nil,
   542,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   542,   nil,
   nil,   nil,   nil,   542,   542,   542,   542,   nil,   542,   542,
   nil,   nil,   nil,   542,   542,   nil,   571,   571,   571,   nil,
   571,   542,   nil,   542,   571,   571,   nil,   nil,   nil,   571,
   nil,   571,   571,   571,   571,   571,   571,   571,   nil,   nil,
   nil,   nil,   nil,   571,   571,   571,   571,   571,   571,   571,
   nil,   nil,   571,   nil,   nil,   nil,   nil,   nil,   nil,   571,
   nil,   nil,   571,   571,   571,   571,   571,   571,   571,   571,
   nil,   571,   571,   571,   nil,   571,   571,   571,   571,   571,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   571,
   nil,   nil,   571,   nil,   nil,   571,   571,   nil,   nil,   571,
   nil,   571,   nil,   nil,   nil,   571,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   571,   nil,   nil,   nil,   nil,   571,   571,
   571,   571,   nil,   571,   571,   nil,   nil,   nil,   571,   571,
   nil,   572,   572,   572,   nil,   572,   571,   nil,   571,   572,
   572,   nil,   nil,   nil,   572,   nil,   572,   572,   572,   572,
   572,   572,   572,   nil,   nil,   nil,   nil,   nil,   572,   572,
   572,   572,   572,   572,   572,   nil,   nil,   572,   nil,   nil,
   nil,   nil,   nil,   nil,   572,   nil,   nil,   572,   572,   572,
   572,   572,   572,   572,   572,   nil,   572,   572,   572,   nil,
   572,   572,   572,   572,   572,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   572,   nil,   nil,   572,   nil,   nil,
   572,   572,   nil,   nil,   572,   nil,   nil,   nil,   nil,   nil,
   572,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   572,   nil,
   nil,   nil,   nil,   572,   572,   572,   572,   nil,   572,   572,
   nil,   nil,   nil,   572,   572,   nil,   575,   575,   575,   nil,
   575,   572,   nil,   572,   575,   575,   nil,   nil,   nil,   575,
   nil,   575,   575,   575,   575,   575,   575,   575,   nil,   nil,
   nil,   nil,   nil,   575,   575,   575,   575,   575,   575,   575,
   nil,   nil,   575,   nil,   nil,   nil,   nil,   nil,   nil,   575,
   nil,   nil,   575,   575,   575,   575,   575,   575,   575,   575,
   575,   575,   575,   575,   nil,   575,   575,   575,   575,   575,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   575,
   nil,   nil,   575,   nil,   nil,   575,   575,   nil,   nil,   575,
   nil,   575,   nil,   575,   nil,   575,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   575,   nil,   nil,   nil,   nil,   575,   575,
   575,   575,   nil,   575,   575,   nil,   nil,   nil,   575,   575,
   nil,   576,   576,   576,   nil,   576,   575,   nil,   575,   576,
   576,   nil,   nil,   nil,   576,   nil,   576,   576,   576,   576,
   576,   576,   576,   nil,   nil,   nil,   nil,   nil,   576,   576,
   576,   576,   576,   576,   576,   nil,   nil,   576,   nil,   nil,
   nil,   nil,   nil,   nil,   576,   nil,   nil,   576,   576,   576,
   576,   576,   576,   576,   576,   576,   576,   576,   576,   nil,
   576,   576,   576,   576,   576,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   576,   nil,   nil,   576,   nil,   nil,
   576,   576,   nil,   nil,   576,   nil,   nil,   nil,   576,   nil,
   576,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   576,   nil,
   nil,   nil,   nil,   576,   576,   576,   576,   nil,   576,   576,
   nil,   nil,   nil,   576,   576,   nil,   577,   577,   577,   nil,
   577,   576,   nil,   576,   577,   577,   nil,   nil,   nil,   577,
   nil,   577,   577,   577,   577,   577,   577,   577,   nil,   nil,
   nil,   nil,   nil,   577,   577,   577,   577,   577,   577,   577,
   nil,   nil,   577,   nil,   nil,   nil,   nil,   nil,   nil,   577,
   nil,   nil,   577,   577,   577,   577,   577,   577,   577,   577,
   nil,   577,   577,   577,   nil,   577,   577,   577,   577,   577,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   577,
   nil,   nil,   577,   nil,   nil,   577,   577,   nil,   nil,   577,
   nil,   nil,   nil,   nil,   nil,   577,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   577,   nil,   nil,   nil,   nil,   577,   577,
   577,   577,   nil,   577,   577,   nil,   nil,   nil,   577,   577,
   nil,   578,   578,   578,   nil,   578,   577,   nil,   577,   578,
   578,   nil,   nil,   nil,   578,   nil,   578,   578,   578,   578,
   578,   578,   578,   nil,   nil,   nil,   nil,   nil,   578,   578,
   578,   578,   578,   578,   578,   nil,   nil,   578,   nil,   nil,
   nil,   nil,   nil,   nil,   578,   nil,   nil,   578,   578,   578,
   578,   578,   578,   578,   578,   nil,   578,   578,   578,   nil,
   578,   578,   578,   578,   578,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   578,   nil,   nil,   578,   nil,   nil,
   578,   578,   nil,   nil,   578,   nil,   nil,   nil,   nil,   nil,
   578,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   578,   nil,
   nil,   nil,   nil,   578,   578,   578,   578,   nil,   578,   578,
   nil,   nil,   nil,   578,   578,   nil,   582,   582,   582,   nil,
   582,   578,   nil,   578,   582,   582,   nil,   nil,   nil,   582,
   nil,   582,   582,   582,   582,   582,   582,   582,   nil,   nil,
   nil,   nil,   nil,   582,   582,   582,   582,   582,   582,   582,
   nil,   nil,   582,   nil,   nil,   nil,   nil,   nil,   nil,   582,
   nil,   nil,   582,   582,   582,   582,   582,   582,   582,   582,
   nil,   582,   582,   582,   nil,   582,   582,   582,   582,   582,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   582,
   nil,   nil,   582,   nil,   nil,   582,   582,   nil,   nil,   582,
   nil,   nil,   nil,   nil,   nil,   582,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   582,   nil,   nil,   nil,   nil,   582,   582,
   582,   582,   nil,   582,   582,   nil,   nil,   nil,   582,   582,
   nil,   583,   583,   583,   nil,   583,   582,   nil,   582,   583,
   583,   nil,   nil,   nil,   583,   nil,   583,   583,   583,   583,
   583,   583,   583,   nil,   nil,   nil,   nil,   nil,   583,   583,
   583,   583,   583,   583,   583,   nil,   nil,   583,   nil,   nil,
   nil,   nil,   nil,   nil,   583,   nil,   nil,   583,   583,   583,
   583,   583,   583,   583,   583,   nil,   583,   583,   583,   nil,
   583,   583,   583,   583,   583,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   583,   nil,   nil,   583,   nil,   nil,
   583,   583,   nil,   nil,   583,   nil,   nil,   nil,   nil,   nil,
   583,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   583,   nil,
   nil,   nil,   nil,   583,   583,   583,   583,   nil,   583,   583,
   nil,   nil,   nil,   583,   583,   nil,   586,   586,   586,   nil,
   586,   583,   nil,   583,   586,   586,   nil,   nil,   nil,   586,
   nil,   586,   586,   586,   586,   586,   586,   586,   nil,   nil,
   nil,   nil,   nil,   586,   586,   586,   586,   586,   586,   586,
   nil,   nil,   586,   nil,   nil,   nil,   nil,   nil,   nil,   586,
   nil,   nil,   586,   586,   586,   586,   586,   586,   586,   586,
   nil,   586,   586,   586,   nil,   586,   586,   586,   586,   586,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   586,
   nil,   nil,   586,   nil,   nil,   586,   586,   nil,   nil,   586,
   nil,   nil,   nil,   nil,   nil,   586,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   586,   nil,   nil,   nil,   nil,   586,   586,
   586,   586,   nil,   586,   586,   nil,   nil,   nil,   586,   586,
   nil,   587,   587,   587,   nil,   587,   586,   nil,   586,   587,
   587,   nil,   nil,   nil,   587,   nil,   587,   587,   587,   587,
   587,   587,   587,   nil,   nil,   nil,   nil,   nil,   587,   587,
   587,   587,   587,   587,   587,   nil,   nil,   587,   nil,   nil,
   nil,   nil,   nil,   nil,   587,   nil,   nil,   587,   587,   587,
   587,   587,   587,   587,   587,   nil,   587,   587,   587,   nil,
   587,   587,   587,   587,   587,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   587,   nil,   nil,   587,   nil,   nil,
   587,   587,   nil,   nil,   587,   nil,   nil,   nil,   nil,   nil,
   587,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   587,   nil,
   nil,   nil,   nil,   587,   587,   587,   587,   nil,   587,   587,
   nil,   nil,   nil,   587,   587,   nil,   613,   613,   613,   nil,
   613,   587,   nil,   587,   613,   613,   nil,   nil,   nil,   613,
   nil,   613,   613,   613,   613,   613,   613,   613,   nil,   nil,
   nil,   nil,   nil,   613,   613,   613,   613,   613,   613,   613,
   nil,   nil,   613,   nil,   nil,   nil,   nil,   nil,   nil,   613,
   nil,   nil,   613,   613,   613,   613,   613,   613,   613,   613,
   nil,   613,   613,   613,   nil,   613,   613,   613,   613,   613,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   613,
   nil,   nil,   613,   nil,   nil,   613,   613,   nil,   nil,   613,
   nil,   nil,   nil,   nil,   nil,   613,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   613,   nil,   nil,   nil,   nil,   613,   613,
   613,   613,   nil,   613,   613,   nil,   nil,   nil,   613,   613,
   nil,   618,   618,   618,   nil,   618,   613,   nil,   613,   618,
   618,   nil,   nil,   nil,   618,   nil,   618,   618,   618,   618,
   618,   618,   618,   nil,   nil,   nil,   nil,   nil,   618,   618,
   618,   618,   618,   618,   618,   nil,   nil,   618,   nil,   nil,
   nil,   nil,   nil,   nil,   618,   nil,   nil,   618,   618,   618,
   618,   618,   618,   618,   618,   nil,   618,   618,   618,   nil,
   618,   618,   nil,   nil,   618,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   618,   nil,   nil,   618,   nil,   nil,
   618,   618,   nil,   nil,   618,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   618,   618,   618,   618,   nil,   618,   618,
   nil,   nil,   nil,   618,   618,   nil,   629,   629,   629,   nil,
   629,   618,   nil,   618,   629,   629,   nil,   nil,   nil,   629,
   nil,   629,   629,   629,   629,   629,   629,   629,   nil,   nil,
   nil,   nil,   nil,   629,   629,   629,   629,   629,   629,   629,
   nil,   nil,   629,   nil,   nil,   nil,   nil,   nil,   nil,   629,
   nil,   nil,   629,   629,   629,   629,   629,   629,   629,   629,
   nil,   629,   629,   629,   nil,   629,   629,   nil,   nil,   629,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   629,
   nil,   nil,   629,   nil,   nil,   629,   629,   nil,   nil,   629,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   629,   629,
   629,   629,   nil,   629,   629,   nil,   nil,   nil,   629,   629,
   nil,   681,   681,   681,   nil,   681,   629,   nil,   629,   681,
   681,   nil,   nil,   nil,   681,   nil,   681,   681,   681,   681,
   681,   681,   681,   nil,   nil,   nil,   nil,   nil,   681,   681,
   681,   681,   681,   681,   681,   nil,   nil,   681,   nil,   nil,
   nil,   nil,   nil,   nil,   681,   nil,   nil,   681,   681,   681,
   681,   681,   681,   681,   681,   nil,   681,   681,   681,   nil,
   681,   681,   681,   681,   681,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   681,   nil,   nil,   681,   nil,   nil,
   681,   681,   nil,   nil,   681,   nil,   nil,   nil,   nil,   nil,
   681,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   681,   nil,
   nil,   nil,   nil,   681,   681,   681,   681,   nil,   681,   681,
   nil,   nil,   nil,   681,   681,   nil,   708,   708,   708,   nil,
   708,   681,   nil,   681,   708,   708,   nil,   nil,   nil,   708,
   nil,   708,   708,   708,   708,   708,   708,   708,   nil,   nil,
   nil,   nil,   nil,   708,   708,   708,   708,   708,   708,   708,
   nil,   nil,   708,   nil,   nil,   nil,   nil,   nil,   nil,   708,
   nil,   nil,   708,   708,   708,   708,   708,   708,   708,   708,
   nil,   708,   708,   708,   nil,   708,   708,   708,   708,   708,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   708,
   nil,   nil,   708,   nil,   nil,   708,   708,   nil,   nil,   708,
   nil,   nil,   nil,   nil,   nil,   708,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   708,   nil,   nil,   nil,   nil,   708,   708,
   708,   708,   nil,   708,   708,   nil,   nil,   nil,   708,   708,
   nil,   710,   710,   710,   nil,   710,   708,   nil,   708,   710,
   710,   nil,   nil,   nil,   710,   nil,   710,   710,   710,   710,
   710,   710,   710,   nil,   nil,   nil,   nil,   nil,   710,   710,
   710,   710,   710,   710,   710,   nil,   nil,   710,   nil,   nil,
   nil,   nil,   nil,   nil,   710,   nil,   nil,   710,   710,   710,
   710,   710,   710,   710,   710,   nil,   710,   710,   710,   nil,
   710,   710,   710,   710,   710,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   710,   nil,   nil,   710,   nil,   nil,
   710,   710,   nil,   nil,   710,   nil,   nil,   nil,   nil,   nil,
   710,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   710,   nil,
   nil,   nil,   nil,   710,   710,   710,   710,   nil,   710,   710,
   nil,   nil,   nil,   710,   710,   nil,   724,   724,   724,   nil,
   724,   710,   nil,   710,   724,   724,   nil,   nil,   nil,   724,
   nil,   724,   724,   724,   724,   724,   724,   724,   nil,   nil,
   nil,   nil,   nil,   724,   724,   724,   724,   724,   724,   724,
   nil,   nil,   724,   nil,   nil,   nil,   nil,   nil,   nil,   724,
   nil,   nil,   724,   724,   724,   724,   724,   724,   724,   724,
   nil,   724,   724,   724,   nil,   724,   724,   724,   724,   724,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   724,
   nil,   nil,   724,   nil,   nil,   724,   724,   nil,   nil,   724,
   nil,   nil,   nil,   nil,   nil,   724,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   724,   nil,   nil,   nil,   nil,   724,   724,
   724,   724,   nil,   724,   724,   nil,   nil,   nil,   724,   724,
   nil,   725,   725,   725,   nil,   725,   724,   nil,   724,   725,
   725,   nil,   nil,   nil,   725,   nil,   725,   725,   725,   725,
   725,   725,   725,   nil,   nil,   nil,   nil,   nil,   725,   725,
   725,   725,   725,   725,   725,   nil,   nil,   725,   nil,   nil,
   nil,   nil,   nil,   nil,   725,   nil,   nil,   725,   725,   725,
   725,   725,   725,   725,   725,   nil,   725,   725,   725,   nil,
   725,   725,   725,   725,   725,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   725,   nil,   nil,   725,   nil,   nil,
   725,   725,   nil,   nil,   725,   nil,   nil,   nil,   nil,   nil,
   725,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   725,   nil,
   nil,   nil,   nil,   725,   725,   725,   725,   nil,   725,   725,
   nil,   nil,   nil,   725,   725,   nil,   726,   726,   726,   nil,
   726,   725,   nil,   725,   726,   726,   nil,   nil,   nil,   726,
   nil,   726,   726,   726,   726,   726,   726,   726,   nil,   nil,
   nil,   nil,   nil,   726,   726,   726,   726,   726,   726,   726,
   nil,   nil,   726,   nil,   nil,   nil,   nil,   nil,   nil,   726,
   nil,   nil,   726,   726,   726,   726,   726,   726,   726,   726,
   nil,   726,   726,   726,   nil,   726,   726,   726,   726,   726,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   726,
   nil,   nil,   726,   nil,   nil,   726,   726,   nil,   nil,   726,
   nil,   nil,   nil,   nil,   nil,   726,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   726,   nil,   nil,   nil,   nil,   726,   726,
   726,   726,   nil,   726,   726,   nil,   nil,   nil,   726,   726,
   nil,   727,   727,   727,   nil,   727,   726,   nil,   726,   727,
   727,   nil,   nil,   nil,   727,   nil,   727,   727,   727,   727,
   727,   727,   727,   nil,   nil,   nil,   nil,   nil,   727,   727,
   727,   727,   727,   727,   727,   nil,   nil,   727,   nil,   nil,
   nil,   nil,   nil,   nil,   727,   nil,   nil,   727,   727,   727,
   727,   727,   727,   727,   727,   nil,   727,   727,   727,   nil,
   727,   727,   727,   727,   727,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   727,   nil,   nil,   727,   nil,   nil,
   727,   727,   nil,   nil,   727,   nil,   nil,   nil,   nil,   nil,
   727,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   727,   nil,
   nil,   nil,   nil,   727,   727,   727,   727,   nil,   727,   727,
   nil,   nil,   nil,   727,   727,   nil,   729,   729,   729,   nil,
   729,   727,   nil,   727,   729,   729,   nil,   nil,   nil,   729,
   nil,   729,   729,   729,   729,   729,   729,   729,   nil,   nil,
   nil,   nil,   nil,   729,   729,   729,   729,   729,   729,   729,
   nil,   nil,   729,   nil,   nil,   nil,   nil,   nil,   nil,   729,
   nil,   nil,   729,   729,   729,   729,   729,   729,   729,   729,
   nil,   729,   729,   729,   nil,   729,   729,   729,   729,   729,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   729,
   nil,   nil,   729,   nil,   nil,   729,   729,   nil,   nil,   729,
   nil,   nil,   nil,   nil,   nil,   729,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   729,   nil,   nil,   nil,   nil,   729,   729,
   729,   729,   nil,   729,   729,   nil,   nil,   nil,   729,   729,
   nil,   754,   754,   754,   nil,   754,   729,   nil,   729,   754,
   754,   nil,   nil,   nil,   754,   nil,   754,   754,   754,   754,
   754,   754,   754,   nil,   nil,   nil,   nil,   nil,   754,   754,
   754,   754,   754,   754,   754,   nil,   nil,   754,   nil,   nil,
   nil,   nil,   nil,   nil,   754,   nil,   nil,   754,   754,   754,
   754,   754,   754,   754,   754,   nil,   754,   754,   754,   nil,
   754,   754,   nil,   nil,   754,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   754,   nil,   nil,   754,   nil,   nil,
   754,   754,   nil,   nil,   754,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   754,   754,   754,   754,   nil,   754,   754,
   nil,   nil,   nil,   754,   754,   nil,   768,   768,   768,   nil,
   768,   754,   nil,   754,   768,   768,   nil,   nil,   nil,   768,
   nil,   768,   768,   768,   768,   768,   768,   768,   nil,   nil,
   nil,   nil,   nil,   768,   768,   768,   768,   768,   768,   768,
   nil,   nil,   768,   nil,   nil,   nil,   nil,   nil,   nil,   768,
   nil,   nil,   768,   768,   768,   768,   768,   768,   768,   768,
   nil,   768,   768,   768,   nil,   768,   768,   768,   768,   768,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   768,
   nil,   nil,   768,   nil,   nil,   768,   768,   nil,   nil,   768,
   nil,   nil,   nil,   nil,   nil,   768,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   768,   nil,   nil,   nil,   nil,   768,   768,
   768,   768,   nil,   768,   768,   nil,   nil,   nil,   768,   768,
   nil,   780,   780,   780,   nil,   780,   768,   nil,   768,   780,
   780,   nil,   nil,   nil,   780,   nil,   780,   780,   780,   780,
   780,   780,   780,   nil,   nil,   nil,   nil,   nil,   780,   780,
   780,   780,   780,   780,   780,   nil,   nil,   780,   nil,   nil,
   nil,   nil,   nil,   nil,   780,   nil,   nil,   780,   780,   780,
   780,   780,   780,   780,   780,   nil,   780,   780,   780,   nil,
   780,   780,   780,   780,   780,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   780,   nil,   nil,   780,   nil,   nil,
   780,   780,   nil,   nil,   780,   nil,   nil,   nil,   nil,   nil,
   780,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   780,   nil,
   nil,   nil,   nil,   780,   780,   780,   780,   nil,   780,   780,
   nil,   nil,   nil,   780,   780,   nil,   785,   785,   785,   nil,
   785,   780,   nil,   780,   785,   785,   nil,   nil,   nil,   785,
   nil,   785,   785,   785,   785,   785,   785,   785,   nil,   nil,
   nil,   nil,   nil,   785,   785,   785,   785,   785,   785,   785,
   nil,   nil,   785,   nil,   nil,   nil,   nil,   nil,   nil,   785,
   nil,   nil,   785,   785,   785,   785,   785,   785,   785,   785,
   nil,   785,   785,   785,   nil,   785,   785,   785,   785,   785,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   785,
   nil,   nil,   785,   nil,   nil,   785,   785,   nil,   nil,   785,
   nil,   785,   nil,   nil,   nil,   785,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   785,   nil,   nil,   nil,   nil,   785,   785,
   785,   785,   nil,   785,   785,   nil,   nil,   nil,   785,   785,
   nil,   802,   802,   802,   nil,   802,   785,   nil,   785,   802,
   802,   nil,   nil,   nil,   802,   nil,   802,   802,   802,   802,
   802,   802,   802,   nil,   nil,   nil,   nil,   nil,   802,   802,
   802,   802,   802,   802,   802,   nil,   nil,   802,   nil,   nil,
   nil,   nil,   nil,   nil,   802,   nil,   nil,   802,   802,   802,
   802,   802,   802,   802,   802,   802,   802,   802,   802,   nil,
   802,   802,   802,   802,   802,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   802,   nil,   nil,   802,   nil,   nil,
   802,   802,   nil,   nil,   802,   nil,   nil,   nil,   802,   nil,
   802,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   802,   nil,
   nil,   nil,   nil,   802,   802,   802,   802,   nil,   802,   802,
   nil,   nil,   nil,   802,   802,   nil,   803,   803,   803,   nil,
   803,   802,   nil,   802,   803,   803,   nil,   nil,   nil,   803,
   nil,   803,   803,   803,   803,   803,   803,   803,   nil,   nil,
   nil,   nil,   nil,   803,   803,   803,   803,   803,   803,   803,
   nil,   nil,   803,   nil,   nil,   nil,   nil,   nil,   nil,   803,
   nil,   nil,   803,   803,   803,   803,   803,   803,   803,   803,
   nil,   803,   803,   803,   nil,   803,   803,   803,   803,   803,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   803,
   nil,   nil,   803,   nil,   nil,   803,   803,   nil,   nil,   803,
   nil,   nil,   nil,   nil,   nil,   803,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   803,   nil,   nil,   nil,   nil,   803,   803,
   803,   803,   nil,   803,   803,   nil,   nil,   nil,   803,   803,
   nil,   817,   817,   817,   nil,   817,   803,   nil,   803,   817,
   817,   nil,   nil,   nil,   817,   nil,   817,   817,   817,   817,
   817,   817,   817,   nil,   nil,   nil,   nil,   nil,   817,   817,
   817,   817,   817,   817,   817,   nil,   nil,   817,   nil,   nil,
   nil,   nil,   nil,   nil,   817,   nil,   nil,   817,   817,   817,
   817,   817,   817,   817,   817,   nil,   817,   817,   817,   nil,
   817,   817,   nil,   nil,   817,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   817,   nil,   nil,   817,   nil,   nil,
   817,   817,   nil,   nil,   817,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   817,   817,   817,   817,   nil,   817,   817,
   nil,   nil,   nil,   817,   817,   nil,   874,   874,   874,   nil,
   874,   817,   nil,   817,   874,   874,   nil,   nil,   nil,   874,
   nil,   874,   874,   874,   874,   874,   874,   874,   nil,   nil,
   nil,   nil,   nil,   874,   874,   874,   874,   874,   874,   874,
   nil,   nil,   874,   nil,   nil,   nil,   nil,   nil,   nil,   874,
   nil,   nil,   874,   874,   874,   874,   874,   874,   874,   874,
   874,   874,   874,   874,   nil,   874,   874,   874,   874,   874,
   504,   504,   504,   504,   504,   504,   504,   504,   504,   504,
   504,   nil,   504,   504,   nil,   nil,   504,   504,   nil,   874,
   nil,   nil,   874,   nil,   nil,   874,   874,   nil,   nil,   874,
   nil,   874,   504,   874,   504,   874,   504,   504,   504,   504,
   504,   504,   504,   874,   504,   nil,   nil,   nil,   874,   874,
   874,   874,   nil,   874,   874,   nil,   nil,   nil,   874,   874,
   nil,   504,    26,   nil,   nil,   nil,   874,   nil,   874,    26,
    26,    26,   nil,   nil,    26,    26,    26,   nil,    26,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,    26,    26,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,    26,
   nil,    26,    26,    26,    26,    26,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,    26,
    26,    26,    26,    26,    26,    26,    26,    26,    26,    26,
    26,    26,   nil,   nil,    26,    26,    26,   nil,   nil,    26,
   nil,    26,    26,   nil,   nil,    26,    26,   nil,    26,   nil,
    26,   nil,    26,   nil,    26,    26,    26,    26,    26,    26,
    26,    27,    26,    26,    26,   nil,   nil,   nil,    27,    27,
    27,   nil,   nil,    27,    27,    27,   nil,    27,    26,    26,
   nil,    26,   nil,    26,   nil,   nil,   nil,    27,    27,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    27,    27,   nil,
    27,    27,    27,    27,    27,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    27,    27,    27,
    27,    27,    27,    27,    27,    27,    27,    27,    27,    27,
    27,   nil,   nil,    27,    27,    27,   nil,   nil,    27,   nil,
    27,    27,   nil,   nil,    27,    27,   nil,    27,   nil,    27,
   nil,    27,   nil,    27,    27,    27,    27,    27,    27,    27,
   400,    27,   nil,    27,   nil,   nil,   nil,   400,   400,   400,
   nil,   nil,   400,   400,   400,   nil,   400,    27,    27,   nil,
    27,   nil,    27,   nil,   nil,   400,   400,   400,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   400,   400,   nil,   400,
   400,   400,   400,   400,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   400,   400,   400,   400,
   400,   400,   400,   400,   400,   400,   400,   400,   400,   400,
   nil,   nil,   400,   400,   400,   nil,   nil,   400,   nil,   400,
   400,   nil,   nil,   400,   400,   nil,   400,   nil,   400,   nil,
   400,   nil,   400,   400,   400,   400,   400,   400,   400,   459,
   400,   400,   400,   nil,   nil,   nil,   459,   459,   459,   nil,
   nil,   459,   459,   459,   nil,   459,   400,   400,   nil,   400,
   nil,   400,   nil,   nil,   nil,   459,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   459,   459,   nil,   459,   459,
   459,   459,   459,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   462,   nil,   nil,   nil,   nil,   nil,
   nil,   462,   462,   462,   nil,   nil,   462,   462,   462,   nil,
   462,   nil,   nil,   nil,   nil,   nil,   459,   nil,   nil,   nil,
   462,   nil,   nil,   459,   nil,   nil,   nil,   nil,   459,   459,
   462,   462,   nil,   462,   462,   462,   462,   462,   nil,   nil,
   nil,   nil,   nil,   203,   203,   nil,   nil,   203,   nil,   nil,
   nil,   459,   nil,   nil,   nil,   203,   203,   nil,   203,   203,
   203,   203,   203,   203,   203,   459,   nil,   203,   203,   nil,
   459,   462,   203,   203,   203,   203,   nil,   nil,   462,   nil,
   nil,   203,   nil,   462,   462,   nil,   nil,   nil,   nil,   203,
   203,   nil,   203,   203,   203,   203,   203,   203,   203,   203,
   203,   203,   203,   nil,   nil,   203,   462,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   462,   nil,   nil,   nil,   nil,   462,     8,     8,     8,     8,
     8,     8,     8,     8,     8,     8,     8,     8,     8,     8,
     8,     8,     8,     8,     8,     8,     8,     8,     8,     8,
   nil,   nil,   nil,     8,     8,     8,     8,     8,     8,     8,
     8,     8,     8,   nil,   nil,   nil,   nil,   nil,     8,     8,
     8,     8,     8,     8,     8,     8,     8,     8,   nil,     8,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,     8,     8,   nil,
     8,     8,     8,     8,     8,     8,     8,   nil,   nil,     8,
     8,   nil,   nil,   nil,     8,     8,     8,     8,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,     8,     8,   nil,     8,     8,     8,     8,     8,     8,
     8,     8,     8,     8,     8,   nil,   nil,     8,     8,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,     8,
     9,     9,     9,     9,     9,     9,     9,     9,     9,     9,
     9,     9,     9,     9,     9,     9,     9,     9,     9,     9,
     9,     9,     9,     9,   nil,   nil,   nil,     9,     9,     9,
     9,     9,     9,     9,     9,     9,     9,   nil,   nil,   nil,
   nil,   nil,     9,     9,     9,     9,     9,     9,     9,     9,
     9,   nil,   nil,     9,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,     9,     9,   nil,     9,     9,     9,     9,     9,     9,
     9,   nil,   nil,     9,     9,   nil,   nil,   nil,     9,     9,
     9,     9,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,     9,     9,   nil,     9,     9,
     9,     9,     9,     9,     9,     9,     9,     9,     9,   nil,
   nil,     9,     9,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,     9,   381,   381,   381,   381,   381,   381,
   381,   381,   381,   381,   381,   381,   381,   381,   381,   381,
   381,   381,   381,   381,   381,   381,   381,   381,   nil,   nil,
   nil,   381,   381,   381,   381,   381,   381,   381,   381,   381,
   381,   nil,   nil,   nil,   nil,   nil,   381,   381,   381,   381,
   381,   381,   381,   381,   381,   nil,   nil,   381,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   381,   381,   nil,   381,   381,
   381,   381,   381,   381,   381,   nil,   nil,   381,   381,   nil,
   nil,   nil,   381,   381,   381,   381,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   381,
   381,   nil,   381,   381,   381,   381,   381,   381,   381,   381,
   381,   381,   381,   nil,   nil,   381,   381,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   381,   568,   568,
   568,   568,   568,   568,   568,   568,   568,   568,   568,   568,
   568,   568,   568,   568,   568,   568,   568,   568,   568,   568,
   568,   568,   nil,   nil,   nil,   568,   568,   568,   568,   568,
   568,   568,   568,   568,   568,   nil,   nil,   nil,   nil,   nil,
   568,   568,   568,   568,   568,   568,   568,   568,   568,   nil,
   nil,   568,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   568,
   568,   nil,   568,   568,   568,   568,   568,   568,   568,   nil,
   nil,   568,   568,   nil,   nil,   nil,   568,   568,   568,   568,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   568,   568,   nil,   568,   568,   568,   568,
   568,   568,   568,   568,   568,   568,   568,   nil,   nil,   568,
   568,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   568,    68,    68,    68,    68,    68,    68,    68,    68,
    68,    68,    68,    68,    68,    68,    68,    68,    68,    68,
    68,    68,    68,    68,    68,    68,   nil,   nil,   nil,    68,
    68,    68,    68,    68,    68,    68,    68,    68,    68,   nil,
   nil,   nil,   nil,   nil,    68,    68,    68,    68,    68,    68,
    68,    68,    68,    68,    68,    68,   nil,    68,   nil,   nil,
   nil,   nil,   nil,    68,    68,   nil,    68,    68,    68,    68,
    68,    68,    68,   nil,   nil,    68,    68,   nil,   nil,   nil,
    68,    68,    68,    68,   nil,   nil,   nil,   nil,   nil,    68,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    68,    68,   nil,
    68,    68,    68,    68,    68,    68,    68,    68,    68,    68,
    68,   nil,   nil,    68,   694,   694,   694,   694,   694,   694,
   694,   694,   694,   694,   694,   694,   694,   694,   694,   694,
   694,   694,   694,   694,   694,   694,   694,   694,   nil,   nil,
   nil,   694,   694,   694,   694,   694,   694,   694,   694,   694,
   694,   nil,   nil,   nil,   nil,   nil,   694,   694,   694,   694,
   694,   694,   694,   694,   694,   nil,   nil,   694,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   694,   694,   nil,   694,   694,
   694,   694,   694,   694,   694,   nil,   nil,   694,   694,   nil,
   nil,   nil,   694,   694,   694,   694,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   694,
   694,   nil,   694,   694,   694,   694,   694,   694,   694,   694,
   694,   694,   694,   204,   204,   694,   nil,   204,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   204,   204,   nil,   204,   204,
   204,   204,   204,   204,   204,   nil,   nil,   204,   204,   nil,
   nil,   nil,   204,   204,   204,   204,   nil,   nil,   nil,   nil,
   nil,   204,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   204,
   204,   nil,   204,   204,   204,   204,   204,   204,   204,   204,
   204,   204,   204,   252,   252,   204,   nil,   252,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   252,   252,   nil,   252,   252,
   252,   252,   252,   252,   252,   nil,   nil,   252,   252,   nil,
   nil,   nil,   252,   252,   252,   252,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   252,
   252,   nil,   252,   252,   252,   252,   252,   252,   252,   252,
   252,   252,   252,   253,   253,   252,   nil,   253,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   253,   253,   nil,   253,   253,
   253,   253,   253,   253,   253,   nil,   nil,   253,   253,   nil,
   nil,   nil,   253,   253,   253,   253,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   253,
   253,   nil,   253,   253,   253,   253,   253,   253,   253,   253,
   253,   253,   253,   425,   425,   253,   nil,   425,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   425,   425,   nil,   425,   425,
   425,   425,   425,   425,   425,   nil,   nil,   425,   425,   nil,
   nil,   nil,   425,   425,   425,   425,   nil,   nil,   nil,   nil,
   nil,   425,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   425,
   425,   nil,   425,   425,   425,   425,   425,   425,   425,   425,
   425,   425,   425,   426,   426,   425,   nil,   426,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   426,   426,   nil,   426,   426,
   426,   426,   426,   426,   426,   nil,   nil,   426,   426,   nil,
   nil,   nil,   426,   426,   426,   426,   nil,   nil,   nil,   nil,
   nil,   426,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   426,
   426,   nil,   426,   426,   426,   426,   426,   426,   426,   426,
   426,   426,   426,   490,   490,   426,   nil,   490,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   490,   490,   nil,   490,   490,
   490,   490,   490,   490,   490,   nil,   nil,   490,   490,   nil,
   nil,   nil,   490,   490,   490,   490,   nil,   nil,   nil,   nil,
   nil,   490,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   490,
   490,   nil,   490,   490,   490,   490,   490,   490,   490,   490,
   490,   490,   490,   491,   491,   490,   nil,   491,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   491,   491,   nil,   491,   491,
   491,   491,   491,   491,   491,   nil,   nil,   491,   491,   nil,
   nil,   nil,   491,   491,   491,   491,   nil,   nil,   nil,   nil,
   nil,   491,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   491,
   491,   nil,   491,   491,   491,   491,   491,   491,   491,   491,
   491,   491,   491,   502,   502,   491,   nil,   502,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   502,   502,   nil,   502,   502,
   502,   502,   502,   502,   502,   nil,   nil,   502,   502,   nil,
   nil,   nil,   502,   502,   502,   502,   nil,   nil,   nil,   nil,
   nil,   502,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   502,
   502,   nil,   502,   502,   502,   502,   502,   502,   502,   502,
   502,   502,   502,   503,   503,   502,   nil,   503,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   503,   503,   nil,   503,   503,
   503,   503,   503,   503,   503,   nil,   nil,   503,   503,   nil,
   nil,   nil,   503,   503,   503,   503,   nil,   nil,   nil,   nil,
   nil,   503,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   503,
   503,   nil,   503,   503,   503,   503,   503,   503,   503,   503,
   503,   503,   503,   530,   530,   503,   nil,   530,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   530,   530,   nil,   530,   530,
   530,   530,   530,   530,   530,   nil,   nil,   530,   530,   nil,
   nil,   nil,   530,   530,   530,   530,   nil,   nil,   nil,   nil,
   nil,   530,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   530,
   530,   nil,   530,   530,   530,   530,   530,   530,   530,   530,
   530,   530,   530,   531,   531,   530,   nil,   531,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   531,   531,   nil,   531,   531,
   531,   531,   531,   531,   531,   nil,   nil,   531,   531,   nil,
   nil,   nil,   531,   531,   531,   531,   nil,   nil,   nil,   nil,
   nil,   531,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   531,
   531,   nil,   531,   531,   531,   531,   531,   531,   531,   531,
   531,   531,   531,   537,   537,   531,   nil,   537,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   537,   537,   nil,   537,   537,
   537,   537,   537,   537,   537,   nil,   nil,   537,   537,   nil,
   nil,   nil,   537,   537,   537,   537,   nil,   nil,   nil,   nil,
   nil,   537,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   537,
   537,   nil,   537,   537,   537,   537,   537,   537,   537,   537,
   537,   537,   537,   538,   538,   537,   nil,   538,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   538,   538,   nil,   538,   538,
   538,   538,   538,   538,   538,   nil,   nil,   538,   538,   nil,
   nil,   nil,   538,   538,   538,   538,   nil,   nil,   nil,   nil,
   nil,   538,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   538,
   538,   nil,   538,   538,   538,   538,   538,   538,   538,   538,
   538,   538,   538,   875,   875,   538,   nil,   875,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   875,   875,   nil,   875,   875,
   875,   875,   875,   875,   875,   nil,   nil,   875,   875,   nil,
   nil,   nil,   875,   875,   875,   875,   nil,   nil,   nil,   nil,
   nil,   875,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   875,
   875,   nil,   875,   875,   875,   875,   875,   875,   875,   875,
   875,   875,   875,   876,   876,   875,   nil,   876,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   876,   876,   nil,   876,   876,
   876,   876,   876,   876,   876,   nil,   nil,   876,   876,   nil,
   nil,   nil,   876,   876,   876,   876,   nil,   nil,   nil,   nil,
   nil,   876,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   876,
   876,   nil,   876,   876,   876,   876,   876,   876,   876,   876,
   876,   876,   876,   913,   913,   876,   nil,   913,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   913,   913,   nil,   913,   913,
   913,   913,   913,   913,   913,   nil,   nil,   913,   913,   nil,
   nil,   nil,   913,   913,   913,   913,   nil,   nil,   nil,   nil,
   nil,   913,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   913,
   913,   nil,   913,   913,   913,   913,   913,   913,   913,   913,
   913,   913,   913,   nil,   nil,   913,   630,   630,   630,   630,
   630,   630,   630,   630,   630,   630,   630,   nil,   630,   630,
   nil,   nil,   630,   630,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   630,   nil,
   630,   nil,   630,   630,   630,   630,   630,   630,   630,   nil,
   630,   nil,   709,   709,   709,   709,   709,   709,   709,   709,
   709,   709,   709,   nil,   709,   709,   nil,   630,   709,   709,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   709,   nil,   709,   nil,   709,   709,
   709,   709,   709,   709,   709,   nil,   709,   nil,   714,   714,
   714,   714,   714,   714,   714,   714,   714,   714,   714,   nil,
   714,   714,   nil,   709,   714,   714,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   714,   nil,   714,   nil,   714,   714,   714,   714,   714,   714,
   714,   nil,   714,   nil,   716,   716,   716,   716,   716,   716,
   716,   716,   716,   716,   716,   nil,   716,   716,   nil,   714,
   716,   716,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   716,   nil,   716,   nil,
   716,   716,   716,   716,   716,   716,   716,   nil,   716,   nil,
   719,   719,   719,   719,   719,   719,   719,   719,   719,   719,
   719,   nil,   719,   719,   nil,   716,   719,   719,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   719,   nil,   719,   nil,   719,   719,   719,   719,
   719,   719,   719,   nil,   719,   nil,   721,   721,   721,   721,
   721,   721,   721,   721,   721,   721,   721,   nil,   721,   721,
   nil,   719,   721,   721,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   721,   nil,
   721,   nil,   721,   721,   721,   721,   721,   721,   721,   nil,
   721,   nil,   723,   723,   723,   723,   723,   723,   723,   723,
   723,   723,   723,   nil,   723,   723,   nil,   721,   723,   723,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   723,   nil,   723,   nil,   723,   723,
   723,   723,   723,   723,   723,   nil,   723,   nil,   801,   801,
   801,   801,   801,   801,   801,   801,   801,   801,   801,   nil,
   801,   801,   nil,   723,   801,   801,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   801,   nil,   801,   nil,   801,   801,   801,   801,   801,   801,
   801,   nil,   801,   nil,   804,   804,   804,   804,   804,   804,
   804,   804,   804,   804,   804,   nil,   804,   804,   nil,   801,
   804,   804,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   804,   nil,   804,   nil,
   804,   804,   804,   804,   804,   804,   804,   nil,   804,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   804 ]

racc_action_pointer = [
     0,    80,   nil,    62,   nil,  4744,  1249,    39, 21174, 21298,
   100,   nil,    84,   143,   544,    55,   108,   127,   nil,    -8,
  4869,  6502,   257,   nil,    76,   261, 20662, 20771,  4994,  5119,
  5244,   nil,   738,  5369,  5494,   nil,   174,   240,   361,   273,
   240,  5627,  5752,  5877,   212,   572,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   866,   999,   nil,  6002,  6127,     0,   nil,
  6252,  6377,   nil,   nil,  6502,  6635,  6760,  6885, 21670,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   568,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,     0,
   nil,   nil,   130,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   382,   nil,  7010,   nil,   nil,   nil,   nil,
  7143,  7268,  7393,  7518,  7643,  1124,   nil,   449,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   265,   nil,  1249,
  7768,  7893,  8018, 21042, 21842,  8143,  8268,  8393,  8518,  8643,
  8768,   nil,   nil,   607,    54,   139,   350,   244,   272,   325,
   nil,  8893,  1374,   339,  9018,  9143,  9268,  9393,  9518,  9643,
  9768,  9893, 10018, 10143, 10268, 10393, 10518, 10643, 10768, 10893,
 11018, 11143, 11268, 11393, 11518, 11643, 11768, 11893, 12018, 12143,
   nil,   nil, 21902, 21962,   344, 12268, 12393,   nil,   nil,   nil,
   nil,   nil,   nil,   nil, 12518,   nil,  7010,   nil,   335,   337,
   nil, 12643,   383, 12768,   nil, 12893,   nil,   nil,   282,  1374,
 13018,   349,  1499,   391,   448,   433, 13143,  1624,   611,   738,
   741,   517,   784,   nil,   493,   467,   346,   nil,   nil,   nil,
   508,   431,   476, 13276,   nil,   376,   548,   552,   838,   nil,
   568,   nil, 13401,   687,   689, 13526,   nil,   252,   392,   618,
   604,   509,   637,   nil,   nil,   614,    48,   136, 13651, 13776,
   403,   710,   604,    65,    89,   874,   682,    98,   721,   nil,
   nil,   442,   443,    96,   nil,   898,   nil,    55, 13901,   nil,
   nil,   nil,   360,   388,   392,   420,   453,   549,   571,   576,
   582,   nil,   641,   nil, 14026,   nil,   401,   465,   468,   473,
    39,   501,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   650, 21422,   nil,   nil,   nil,   nil,   665,   nil,   655,   nil,
 14151,   665,   nil,  1624,   672,   nil,   690,   699,   478,   512,
 20880,   nil,   nil,   nil,   239,   348,   743,   nil,   nil,  1752,
  1861,   nil, 13143,   nil,   702,   nil,   nil,   738,   nil,   nil,
   nil,   nil,   283,   nil,   761, 22022, 22082, 14276,   248, 14401,
 14526, 14651,  2369,  2494,    89,   648,   790,   794,   798,   803,
  3994,  4119,  4244,  2619,  1810,  2744,  2869,  2994,  3119,  3244,
  3369,  3494,  3619,   559,   793,  3744,  3869, 15151,   136, 20989,
   nil,   nil, 21044,   nil,   nil,   745,   nil,   175,   293,   750,
   nil,   nil, 14776,   nil, 14901,   nil, 15026,   nil,   nil,   nil,
 15151,  1404,   760,   761,   nil,   nil,   770, 15284,   774, 15409,
 22142, 22202,   903,   812,   nil,   nil, 15534,   775,   nil, 15659,
 15784, 15909, 22262, 22322, 20534, 16034,   897, 16159,   nil,   631,
   nil, 16284,   nil,   nil, 16409,   nil,   nil,   nil,   nil,  1716,
  1994,   912,   nil,  2119,   119,   142,   909,   917, 16534, 16659,
 22382, 22442,   112,   nil,   nil,   931,   nil, 22502, 22562, 16784,
   nil,   nil, 16909,   596,   146,  2244,  5382,   nil,   nil,   nil,
   317,   nil,   nil,   nil,   721,   nil,   nil,   nil,   828,   nil,
   nil,   373,   nil,   nil,   822,   nil,   nil,   nil, 21546,   nil,
   826, 17034, 17159,   523,   863, 17284, 17409, 17534, 17659,   869,
   nil,   nil, 17784, 17909,   881,   nil, 18034, 18159,   nil,   nil,
   277,   353,   473,   603,   853,  5494,   853,   nil,   830,   nil,
  2369,   nil,   nil,   nil,   nil,   302,   nil,  2494,  2619,   nil,
   854,   nil,   897, 18284,   nil,   nil,  1499,   251, 18409,   856,
   nil,   860,   125,   198,   899,   481,  1379,   900,   861, 18534,
 22790,   932,   940,   323,   999,   nil,  2744,   882,   925,   nil,
   nil,   887,   698,   131,   666,   nil,   894,   896,   897,   nil,
   nil,   nil,   nil,   nil,   nil,   892,   nil,   866,   nil,   nil,
   nil,   nil,   984,   nil,   nil,   990,   836,   nil,  1031,   nil,
   nil,   nil,   nil,  1046,   nil,   140,   933,   141,   156,   263,
   269, 18659,   543,  1504,   nil,   940,  2869,   602,   nil,   nil,
  1061,  2994,  1052,   621, 21782,   nil,   nil,   nil,   nil,   nil,
   nil,  3119,   nil,   nil,   nil,   nil,   nil,   943, 18784, 22836,
 18909,   nil,   945,   nil, 22882,   nil, 22928,   nil,   nil, 22974,
   nil, 23020,   nil, 23066, 19034, 19159, 19284, 19409,   356, 19534,
   949,   951,   952,   nil,   974,   952,   985,   950,   nil,  1077,
  3244,   976,  1080,   nil,   nil,   963,   352,   nil,   nil,   nil,
  3369,   nil,   nil,   163, 19659,   nil,  1003,   nil,   nil,   nil,
   nil,   973,  1177,   nil,  1302,   nil,   nil,  1022, 19784,   nil,
  1014,   nil,   983,   342,   nil,   991,   nil,   nil,  1111,   nil,
 19909,  1127,  3494,  3619,   nil, 20034,  3744,   171,   205,   nil,
  1128,   nil,  3869,   nil,  1129,  1022,   nil,   nil,  1048,  1037,
   nil, 23112, 20159, 20284, 23158,  1427,   nil,  1552,   nil,   nil,
  1147,   nil,  1072,  1055,   nil,   nil,   nil, 20409,   nil,  1079,
   nil,   nil,   nil,  3994,  1277,  1365,  1380,  1140,  1383,   nil,
   nil,  4119,  4244,  1071,  1070,  1074,   nil,   nil,  1076,  1077,
   nil,  1078,   nil,  1082,  1139,  1081,   859,   nil,   nil,   164,
   nil,  1202,  1204,   nil,   107,   nil,   nil,  1205,   nil,   nil,
  6648,   nil,  1088,  1089,  1100,  1108,   nil,  1113,   nil,  1110,
  1393,  1190,   nil,  1254, 20534, 22622, 22682,  1490,  1150,  1254,
   nil,  2047,   nil,   nil,  1272,   nil,  1397,   nil,  1522,   nil,
   nil,   nil,   349,  1044,  1144,  4369,   nil,   nil,   nil,   nil,
   nil,  4494,   nil,  4619,  2172,   nil,   nil,  2017,   nil,  2142,
   nil,   nil,   nil, 22742,   nil,   nil,  1159,   165,   166,   209,
  1624,   nil,   nil,  1157,  1166,  1176,  1180,  1187,  1169,  1191,
  1264,   875,  1313,  1315,  1199,  1201,  1202,  1203,  1244,  1246,
   173,   nil,  2266,   nil,   nil,   nil,  1294,  1207,   nil,   nil,
   nil,   nil,  2295,   nil,   nil,   nil,  1209,  1210,  1213,   nil,
   nil ]

racc_action_default = [
    -3,  -552,    -1,  -538,    -4,  -552,    -7,  -552,  -552,  -552,
  -552,   -27,  -552,  -552,  -552,  -274,  -552,   -39,   -42,  -540,
  -552,   -47,   -49,   -50,   -51,  -255,  -255,  -255,  -285,  -321,
  -322,   -67,   -11,   -71,   -79,   -81,  -552,  -467,  -468,  -552,
  -552,  -552,  -552,  -552,  -540,  -236,  -267,  -268,  -269,  -270,
  -271,  -272,  -273,  -528,   -11,  -277,  -551,  -520,  -293,  -295,
  -552,  -552,  -299,  -302,  -538,  -552,  -552,  -552,  -552,  -323,
  -324,  -326,  -327,  -416,  -417,  -418,  -419,  -420,  -435,  -423,
  -424,  -437,  -439,  -428,  -433,  -449,  -437,  -451,  -452,  -526,
  -456,  -457,  -527,  -459,  -460,  -461,  -462,  -463,  -464,  -465,
  -466,  -471,  -472,  -552,    -2,  -539,  -547,  -548,  -549,    -6,
  -552,  -552,  -552,  -552,  -552,    -3,   -15,  -552,  -110,  -111,
  -112,  -113,  -114,  -115,  -116,  -117,  -118,  -122,  -123,  -124,
  -125,  -126,  -127,  -128,  -129,  -130,  -131,  -132,  -133,  -134,
  -135,  -136,  -137,  -138,  -139,  -140,  -141,  -142,  -143,  -144,
  -145,  -146,  -147,  -148,  -149,  -150,  -151,  -152,  -153,  -154,
  -155,  -156,  -157,  -158,  -159,  -160,  -161,  -162,  -163,  -164,
  -165,  -166,  -167,  -168,  -169,  -170,  -171,  -172,  -173,  -174,
  -175,  -176,  -177,  -178,  -179,  -180,  -181,  -182,  -183,  -184,
  -185,  -186,  -187,  -188,  -189,  -190,  -191,   -20,  -119,   -11,
  -552,  -552,  -245,  -552,  -552,  -552,  -552,  -552,  -552,  -552,
  -540,  -541,   -46,  -552,  -467,  -468,  -552,  -274,  -552,  -552,
  -228,  -552,   -11,  -552,  -552,  -552,  -552,  -552,  -552,  -552,
  -552,  -552,  -552,  -552,  -552,  -552,  -552,  -552,  -552,  -552,
  -552,  -552,  -552,  -552,  -552,  -552,  -552,  -552,  -552,  -552,
  -386,  -388,  -552,  -552,   -56,  -245,  -552,  -292,  -391,  -400,
  -402,   -62,  -397,   -63,  -540,   -64,  -237,  -250,  -259,  -259,
  -254,  -552,  -260,  -552,  -522,  -552,   -65,   -66,  -538,   -12,
  -552,   -69,   -11,  -540,  -552,   -72,   -75,   -11,   -87,   -88,
  -552,  -552,   -95,  -285,  -288,  -540,  -552,  -321,  -322,  -325,
  -398,  -552,   -77,  -552,   -83,  -282,  -453,  -454,  -552,  -213,
  -214,  -229,  -552,  -408,  -552,  -552,  -238,  -544,  -544,  -552,
  -552,  -544,  -552,  -294,  -378,   -48,  -552,  -552,  -552,  -552,
  -538,  -552,  -539,  -467,  -468,  -552,  -552,  -274,  -552,  -337,
  -338,  -105,  -106,  -552,  -108,  -552,  -274,  -552,  -552,  -467,
  -468,  -314,  -110,  -111,  -151,  -152,  -153,  -169,  -174,  -181,
  -184,  -316,  -552,  -518,  -552,  -421,  -552,  -552,  -552,  -552,
  -552,  -552,   961,    -5,  -550,   -21,   -22,   -23,   -24,   -25,
  -552,  -552,   -17,   -18,   -19,  -120,  -552,   -28,   -37,   -38,
  -552,  -552,   -29,  -194,  -540,  -246,  -259,  -259,  -529,  -530,
  -255,  -395,  -531,  -532,  -530,  -529,  -255,  -394,  -396,  -531,
  -532,   -35,  -202,   -36,  -552,   -40,   -41,  -192,  -260,   -43,
   -44,   -45,  -540,  -291,  -552,  -552,  -552,  -245,  -282,  -552,
  -552,  -552,  -203,  -204,  -205,  -206,  -207,  -208,  -209,  -210,
  -215,  -216,  -217,  -218,  -219,  -220,  -221,  -222,  -223,  -224,
  -225,  -226,  -227,  -230,  -231,  -232,  -233,  -540,  -367,  -255,
  -529,  -530,  -255,   -54,   -57,  -540,  -256,  -367,  -367,  -540,
  -287,  -251,  -552,  -252,  -552,  -257,  -552,  -261,  -525,   -10,
  -539,   -14,  -540,   -68,  -280,   -84,   -73,  -552,  -540,  -245,
  -552,  -552,   -94,  -552,  -453,  -454,  -552,   -80,   -85,  -552,
  -552,  -552,  -552,  -552,  -234,  -552,  -551,  -551,  -276,  -278,
  -239,  -546,  -545,  -241,  -546,  -283,  -284,  -521,  -296,  -492,
   -11,  -328,  -329,   -11,  -552,  -552,  -552,  -552,  -552,  -245,
  -552,  -552,  -282,  -307,  -105,  -106,  -107,  -552,  -552,  -245,
  -310,  -473,  -552,  -552,  -552,   -11,  -492,  -318,  -536,  -537,
  -540,  -422,  -436,  -441,  -552,  -443,  -425,  -438,  -552,  -440,
  -427,  -552,  -430,  -432,  -552,  -450,    -8,   -16,  -552,   -26,
  -266,  -552,  -552,  -399,  -552,  -247,  -249,  -552,  -552,   -58,
  -244,  -392,  -552,  -552,   -60,  -393,  -552,  -552,  -290,  -542,
  -529,  -530,  -529,  -530,  -540,  -192,  -552,  -368,  -372,  -370,
   -11,   -52,  -389,   -53,  -390,  -367,  -242,   -11,   -11,  -286,
  -259,  -258,  -262,  -552,  -523,  -524,   -13,   -70,  -552,   -76,
   -82,  -540,  -529,  -530,  -243,   -91,   -93,  -552,   -78,  -552,
  -201,  -211,  -212,  -540,  -551,  -335,   -11,  -409,  -551,  -410,
  -411,  -540,  -544,  -552,  -492,  -381,  -517,  -517,  -517,  -491,
  -493,  -494,  -495,  -496,  -497,  -498,  -499,  -552,  -501,  -507,
  -509,  -510,  -512,  -513,  -514,  -552,  -551,  -330,  -551,  -300,
  -331,  -332,  -303,  -552,  -306,  -552,  -540,  -529,  -530,  -533,
  -281,  -552,  -105,  -106,  -109,  -540,   -11,  -552,  -475,  -312,
  -552,   -11,  -492,  -552,  -552,  -519,  -442,  -445,  -446,  -447,
  -448,   -11,  -426,  -429,  -431,  -434,  -121,  -264,  -552,  -195,
  -552,  -543,  -259,   -31,  -197,   -32,  -198,   -59,   -33,  -200,
   -34,  -199,   -61,  -193,  -552,  -552,  -552,  -552,  -399,  -552,
  -517,  -517,  -517,  -366,  -552,  -372,  -552,  -498,  -505,  -552,
   -11,  -552,  -552,  -253,  -263,   -74,   -89,   -86,  -289,    -9,
   -11,  -415,  -336,  -552,  -552,  -413,  -552,  -240,  -379,  -382,
  -384,  -372,  -552,  -484,  -552,  -487,  -489,  -552,  -552,  -339,
  -552,  -341,  -343,  -350,  -498,  -540,  -511,  -515,  -552,  -333,
  -552,  -552,   -11,   -11,  -305,  -552,   -11,  -399,  -552,  -399,
  -552,  -474,   -11,  -315,  -552,  -540,  -477,  -319,  -552,  -265,
   -30,  -196,  -248,  -552,  -235,  -357,  -359,  -552,  -362,  -364,
  -552,  -369,  -552,  -373,  -374,  -376,  -377,  -552,  -387,  -552,
  -401,  -403,  -414,   -11,   -96,   -97,  -552,  -552,  -104,  -412,
  -279,   -11,   -11,  -540,  -517,  -517,  -502,  -516,  -517,  -517,
  -508,  -517,  -503,  -540,  -552,  -348,  -552,  -500,  -297,  -552,
  -298,  -552,  -552,  -262,  -551,  -308,  -311,  -552,  -317,  -476,
  -492,  -444,  -517,  -517,  -517,  -517,  -506,  -517,  -371,  -552,
  -504,  -552,   -55,  -408,  -245,  -552,  -552,  -103,  -552,  -552,
  -380,  -552,  -480,  -482,  -552,  -485,  -552,  -488,  -552,  -490,
  -340,  -342,  -346,  -552,  -351,   -11,  -301,  -304,  -404,  -405,
  -406,   -11,  -313,   -11,  -552,  -354,  -356,  -552,  -360,  -552,
  -363,  -365,  -375,  -552,  -282,  -407,  -540,  -529,  -530,  -533,
  -102,  -383,  -385,  -517,  -517,  -517,  -517,  -344,  -552,  -349,
  -552,  -551,  -552,  -552,  -517,  -517,  -517,  -517,  -281,  -533,
  -399,  -478,  -552,  -481,  -483,  -486,  -552,  -347,  -334,  -309,
  -320,  -352,  -552,  -355,  -358,  -361,  -517,  -345,  -517,  -479,
  -353 ]

racc_goto_table = [
   121,   121,   361,   506,   285,    13,   316,   209,   116,   198,
    13,   314,   394,   573,   124,   124,    14,   126,   126,   527,
   473,    14,   416,   299,   299,   108,   105,   634,   731,   281,
   302,   283,   312,   691,   520,   523,   213,    13,   734,   400,
   406,   389,   464,   814,   213,   213,   213,   413,    14,   290,
   290,   778,   536,   669,   672,   299,   299,   299,   614,    13,
   121,   497,   611,   284,   611,   465,   645,   254,   261,   263,
    14,     2,   816,     6,   109,   649,   213,   213,     6,   775,
   213,   335,   345,   345,   600,   840,   108,   866,   459,   462,
   104,   326,   327,   607,   608,   330,   510,   513,   614,   891,
   517,     1,   649,   836,   269,   269,   269,   367,   602,   749,
    13,   604,   371,   347,   351,   552,   197,   559,   562,    13,
    13,    14,   265,   276,   277,   340,   213,   213,   213,   213,
    14,    14,   318,   321,   381,   267,   267,   267,   605,   568,
   319,   375,   376,   377,   378,   363,   836,   320,   473,   258,
   262,   331,   894,   323,   733,   518,   212,   338,   325,   325,
   614,   540,   325,   362,   547,   611,   611,   315,   641,   324,
   328,   782,   329,   783,   373,   812,   912,   681,     6,   268,
   268,   268,   901,   686,   866,   891,   380,   379,     6,   781,
   792,   761,   843,   545,   546,   694,   410,   423,   860,   929,
   649,   833,   840,   735,    13,   816,   813,   317,   325,   325,
   325,   325,   643,   728,   673,    14,   213,   213,   213,   758,
   519,   213,   213,   213,   213,   213,   213,    13,   251,   836,
   386,   740,   732,   831,   947,   862,   832,   594,    14,   795,
   746,   458,   467,   468,   898,   619,   684,   638,   649,   753,
   365,   470,   957,   284,   628,   366,   368,   369,   561,   370,
   696,   400,   406,   701,   834,   nil,   nil,   nil,   nil,   nil,
   483,   213,   213,   nil,   498,   nil,   299,   nil,   nil,   281,
   213,   482,   486,   493,   281,    13,   488,    13,   nil,    15,
   nil,   nil,    13,   299,    15,   787,    14,   nil,    14,   621,
   108,   480,   290,    14,   789,   419,   420,   421,   422,   267,
   704,   nil,   nil,   284,   nil,   nil,   948,   855,   284,   290,
   nil,    15,   292,   292,   nil,   nil,   624,   401,   nil,   nil,
   nil,   213,   269,   nil,   nil,   nil,   387,   392,   624,   676,
   269,   411,   415,    15,   213,   213,   nil,   903,   639,   685,
   466,   391,   108,   481,   337,   346,   346,   391,   469,   524,
   525,   743,   267,   267,   213,   479,   624,   717,   nil,   541,
   nil,   267,   722,   121,   624,   899,   745,   nil,   nil,    16,
   213,   567,   574,   786,    16,   nil,   614,   124,   nil,   nil,
   126,   nil,   611,   nil,    15,   nil,   835,   nil,   838,   nil,
   nil,   nil,   416,    15,    15,   nil,   nil,   268,   nil,   588,
   nil,    16,   nil,   509,   nil,   268,   649,   526,   410,   nil,
   258,   757,   nil,   nil,   262,   nil,   325,   325,   nil,   nil,
   nil,   nil,   nil,    16,   nil,   nil,   nil,   nil,   nil,   863,
   nil,   864,   579,   213,   339,   596,   544,   nil,   584,   nil,
   nil,   nil,   606,   nil,   nil,   nil,   609,   640,   498,   nil,
   nil,   823,   550,   743,   nil,   nil,   nil,   498,   nil,   617,
   nil,   nil,   nil,   nil,   nil,   620,   nil,   299,   nil,   nil,
   nil,   nil,   nil,   410,    16,    13,   299,   nil,    15,   nil,
   nil,   nil,   nil,    16,    16,   410,    14,   nil,   nil,   nil,
   nil,   601,   690,   290,   603,   213,   nil,   nil,   nil,   nil,
   nil,    15,   290,   nil,   nil,   923,   nil,   nil,   nil,   nil,
   nil,   213,   nil,   410,   580,    13,   nil,   nil,    13,   410,
   585,   nil,   nil,   nil,   267,   940,    14,   695,   934,    14,
   nil,   nil,   900,   nil,   nil,   213,   670,   670,   610,   401,
    13,   666,   nil,   616,   668,   213,   nil,   895,   213,   nil,
   121,    14,   nil,   915,   nil,   688,   689,   nil,   706,    15,
   nil,    15,   nil,   687,   124,   292,    15,   126,    16,   nil,
   nil,   nil,   574,   580,   751,   414,   580,   642,   755,   498,
   nil,   nil,   292,   213,   213,   nil,   267,   nil,   213,   213,
   747,    16,   213,   633,   nil,    13,   nil,   nil,   299,   574,
   nil,   nil,    13,    13,   401,   nil,    14,   nil,   nil,   299,
   748,   nil,   nil,    14,    14,   nil,   401,   nil,   797,   756,
   nil,   739,   nil,   nil,   290,   nil,   267,   nil,   741,   742,
   325,    13,   nil,   790,   nil,   290,   267,   nil,   794,   nil,
   nil,   712,    14,   nil,   401,   nil,   nil,   nil,   391,    16,
   nil,    16,   401,   nil,   574,   nil,    16,   752,   nil,   nil,
   nil,   nil,   nil,   574,   nil,   nil,   nil,   769,   nil,   675,
   nil,   nil,   nil,   nil,   916,   nil,   121,   nil,   nil,   nil,
   nil,    13,   nil,   nil,   nil,   nil,    13,   213,   nil,   nil,
   nil,   nil,    14,   nil,   nil,   nil,    13,    14,   nil,   791,
   nil,   624,   788,   713,   715,   796,   nil,    14,   718,   720,
   nil,   nil,   415,   nil,   nil,   730,   213,   nil,   nil,   nil,
   nil,   nil,   798,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   299,    13,   nil,   nil,   580,   857,
   nil,   585,   nil,   nil,   nil,    13,    14,   nil,   nil,   nil,
   nil,   nil,   847,   nil,   nil,   nil,    14,   nil,   nil,    15,
   826,   819,   nil,   nil,   nil,   nil,   292,   nil,   nil,   325,
   nil,   822,   859,   nil,   nil,   292,   nil,    13,    13,   nil,
   769,    13,   nil,   845,   nil,   nil,   213,    13,    14,    14,
   nil,   nil,    14,   nil,   nil,   nil,   nil,   299,    14,    15,
   670,   849,    15,   851,   852,   nil,   nil,   854,   nil,   nil,
   880,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    13,   nil,
   890,   nil,   nil,   870,    15,   nil,    13,    13,   nil,    14,
   nil,   nil,   nil,   700,   nil,   nil,   800,    14,    14,   nil,
   nil,   nil,   nil,   nil,   873,   nil,   nil,   nil,   nil,    16,
   933,   nil,   878,   879,   769,   nil,   769,   nil,   410,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   325,   nil,
   nil,   nil,   nil,   nil,   322,   nil,   nil,   nil,   nil,    15,
   213,   839,   nil,   nil,   841,   nil,    15,    15,   nil,    16,
    13,   nil,    16,   nil,   574,   410,    13,   292,    13,   nil,
   nil,    14,   927,   769,   nil,   nil,   nil,    14,   292,    14,
   nil,   nil,   nil,   nil,    16,    15,   931,   nil,   nil,   nil,
   nil,   nil,   932,   nil,   865,   nil,   nil,   867,   nil,   nil,
   nil,    25,   nil,   nil,   nil,   nil,    25,   nil,   769,   nil,
   769,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    25,   nil,   nil,   nil,   414,   769,   nil,   nil,    25,
    25,    25,   nil,    25,   nil,    15,   nil,   nil,   nil,    16,
    15,   267,   nil,   nil,   nil,   nil,    16,    16,   nil,   nil,
    15,   nil,   nil,   nil,   nil,    25,   nil,   nil,   nil,   401,
   nil,    25,    25,   nil,   nil,    25,   nil,   nil,   924,   nil,
   nil,   925,   nil,   926,   nil,    16,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   388,   nil,    15,
   nil,   935,   nil,   418,   936,   nil,   937,   nil,   nil,    15,
   nil,   nil,   nil,   828,   nil,   nil,    25,   nil,   nil,   nil,
   nil,    25,    25,    25,    25,    25,    25,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    16,   nil,   nil,   nil,   956,
    16,    15,    15,   nil,   nil,    15,   nil,   nil,   nil,   958,
    16,    15,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   475,   nil,
   477,   nil,   478,   nil,   nil,   nil,   346,   nil,   nil,   nil,
   nil,   nil,    15,   nil,   nil,   nil,   nil,   nil,   nil,    16,
    15,    15,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    16,
   nil,   nil,   nil,   829,   nil,   nil,   nil,   nil,   nil,   nil,
    25,    25,    25,    25,   nil,   nil,    25,    25,    25,    25,
    25,    25,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    16,    16,    25,   nil,    16,   nil,   nil,   nil,   nil,
   nil,    16,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    15,   nil,   nil,   nil,   nil,   nil,
    15,   nil,    15,   nil,   nil,   nil,    25,    25,   nil,   nil,
   nil,   nil,    16,   nil,   nil,    25,   nil,   nil,   nil,   nil,
    16,    16,   nil,   nil,   nil,   nil,   nil,   570,   nil,   nil,
   nil,    25,   nil,    25,   nil,   nil,   nil,   nil,    25,   nil,
    37,   nil,   nil,   nil,   nil,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    25,   nil,   nil,   nil,
    38,   nil,    37,   288,   288,    38,   nil,   nil,   nil,    25,
    25,   nil,   nil,   nil,    16,   nil,   nil,   nil,   nil,   nil,
    16,   nil,    16,   nil,    37,   nil,   nil,   nil,   nil,    25,
   nil,   nil,    38,   289,   289,   333,   349,   349,   349,   612,
   nil,   322,   nil,   615,   nil,    25,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   334,   350,   350,   350,   nil,
   nil,   nil,   nil,   nil,   637,    37,   nil,   nil,   612,   nil,
   nil,   322,   nil,   nil,    37,    37,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   418,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    38,   nil,   nil,    25,   nil,
   nil,   nil,   nil,   nil,    38,    38,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   763,   765,   766,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   707,   nil,
   nil,   nil,   612,   322,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    25,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    37,
    25,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   744,   nil,   nil,   nil,   nil,   nil,    25,   nil,   nil,   nil,
   nil,   nil,    37,   nil,   nil,   nil,   nil,   nil,   nil,    38,
   nil,    25,   nil,   nil,    25,   nil,   nil,   806,   808,   809,
    25,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    25,   nil,    38,    25,   nil,   nil,    25,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    37,   nil,    37,   nil,   nil,   nil,   288,    37,    25,    25,
   nil,   nil,   nil,    25,    25,   nil,   nil,    25,   nil,   nil,
   nil,   nil,   nil,   288,   nil,   799,   nil,   nil,   nil,   nil,
    38,    25,    38,   nil,   nil,   nil,   289,    38,    25,    25,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   289,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   882,   883,   nil,   nil,   885,   887,    25,   889,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   842,   nil,   nil,   nil,   905,
   906,   908,   910,   nil,   911,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   853,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    25,   nil,   nil,   nil,   nil,    25,   nil,   322,
   nil,   nil,    25,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    25,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    25,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   941,   943,   944,   945,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   951,   953,   954,   955,   nil,   nil,   nil,   nil,   nil,
   nil,    25,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    25,   nil,   959,   nil,   960,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    37,   nil,   nil,   nil,   nil,   nil,   nil,   288,   nil,   nil,
   nil,    25,   nil,    25,    25,   nil,   288,    25,   nil,   nil,
   nil,   nil,   nil,    25,   nil,   nil,   nil,   nil,   nil,   nil,
    38,   nil,   nil,   nil,   nil,   nil,   nil,   289,   nil,   nil,
    37,   nil,   nil,    37,   nil,   nil,   289,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    25,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    25,    25,   nil,    37,   nil,   nil,   nil,   nil,
    38,   nil,   nil,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    38,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    25,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    37,   nil,   nil,   nil,   nil,   nil,    25,    37,    37,   nil,
   nil,   nil,    25,   nil,    25,   nil,   nil,   nil,   288,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   288,
    38,   nil,   nil,   nil,   nil,   nil,    37,    38,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   289,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   289,
   nil,   nil,   nil,   nil,   nil,   nil,    38,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    37,   nil,   nil,   nil,
   nil,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    38,   nil,   nil,   nil,
   nil,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    38,   nil,   nil,   nil,   220,   nil,   nil,   nil,   nil,
    37,   nil,   nil,   266,   266,   266,   nil,   nil,   nil,   nil,
    37,   nil,   nil,   nil,   824,   nil,   309,   310,   311,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    38,   266,   266,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    38,   nil,    37,    37,   825,   nil,    37,   nil,   nil,   nil,
   nil,   nil,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    38,    38,   nil,   nil,    38,   349,   nil,   nil,
   nil,   nil,    38,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    37,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   350,   nil,   nil,
   nil,   nil,   nil,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    38,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    37,   nil,   nil,   nil,   nil,
   nil,    37,   nil,    37,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   266,   393,   266,   nil,   nil,
   412,   417,   nil,   nil,   nil,    38,   nil,   nil,   nil,   nil,
   nil,    38,   nil,    38,   nil,   nil,   220,   nil,   nil,   432,
   433,   434,   435,   436,   437,   438,   439,   440,   441,   442,
   443,   444,   445,   446,   447,   448,   449,   450,   451,   452,
   453,   454,   455,   456,   457,   nil,   nil,   nil,   nil,   nil,
   266,   266,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   266,
   nil,   nil,   nil,   nil,   nil,   nil,   266,   nil,   266,   nil,
   266,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   504,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   266,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   266,   nil,   412,   595,   393,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   266,   nil,   266,
   nil,   266,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   266,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   630,   631,   632,   nil,   nil,   nil,
   nil,   nil,   266,   nil,   nil,   nil,   266,   nil,   nil,   266,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   266,   266,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   266,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   266,   709,   nil,   nil,
   266,   266,   714,   716,   nil,   nil,   nil,   719,   721,   nil,
   nil,   595,   723,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   266,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   266,   nil,   801,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   714,
   716,   721,   719,   nil,   804,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   266,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   266,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   266,   801,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   266 ]

racc_goto_check = [
    54,    54,    53,    10,    46,    20,    62,    30,    14,    14,
    20,     8,    22,    23,    57,    57,    21,    58,    58,    85,
    68,    21,    17,    60,    60,    89,     6,    11,   115,    42,
    47,    43,    30,    91,    82,    82,    21,    20,   120,    35,
    35,    26,    37,   122,    21,    21,    21,    26,    21,    21,
    21,    83,    51,    84,    84,    60,    60,    60,   156,    20,
    54,    48,    69,     9,    69,    22,   127,    36,    36,    36,
    21,     2,   123,     7,     5,   118,    21,    21,     7,   111,
    21,    21,    21,    21,    38,   152,    89,   153,    35,    35,
     4,    16,    16,    38,    38,    16,    64,    64,   156,   109,
    64,     1,   118,   151,    65,    65,    65,   142,    67,    12,
    20,    67,   142,    52,    52,   146,    15,   146,   146,    20,
    20,    21,    41,    41,    41,    18,    21,    21,    21,    21,
    21,    21,    65,    65,    29,    32,    32,    32,    39,    59,
    61,    16,    16,    16,    16,    77,   151,    78,    68,    66,
    66,     4,   112,    79,   118,    81,    19,    86,    28,    28,
   156,    88,    28,    92,    93,    69,    69,    94,    95,    96,
    97,    98,    99,   100,     5,   120,   122,   101,     7,    63,
    63,    63,   102,   103,   153,   109,     2,     7,     7,    11,
   104,   127,   111,   105,   106,   107,    54,    44,   108,   112,
   118,   120,   152,   113,    20,   123,   121,    63,    28,    28,
    28,    28,   124,    23,    85,    21,    21,    21,    21,   125,
   126,    21,    21,    21,    21,    21,    21,    20,   128,   151,
     9,    38,   116,   129,   112,   115,   130,    22,    21,   127,
    23,   131,   133,   134,   135,    48,    51,   136,   118,   137,
   140,    44,   112,     9,    48,   141,   143,   144,   145,   147,
   148,    35,    35,   149,   150,   nil,   nil,   nil,   nil,   nil,
    44,    21,    21,   nil,    46,   nil,    60,   nil,   nil,    42,
    21,    43,    47,    30,    42,    20,    43,    20,   nil,    24,
   nil,   nil,    20,    60,    24,    23,    21,   nil,    21,    22,
    89,     6,    21,    21,    23,    28,    28,    28,    28,    32,
   146,   nil,   nil,     9,   nil,   nil,    83,    84,     9,    21,
   nil,    24,    24,    24,   nil,   nil,    35,    66,   nil,   nil,
   nil,    21,    65,   nil,   nil,   nil,    19,    19,    35,    22,
    65,    19,    19,    24,    21,    21,   nil,    91,    26,    22,
    41,    63,    89,     7,    24,    24,    24,    63,    41,    16,
    16,    68,    32,    32,    21,     4,    35,    37,   nil,    89,
   nil,    32,    37,    54,    35,    11,    48,   nil,   nil,    25,
    21,    14,    30,    82,    25,   nil,   156,    57,   nil,   nil,
    58,   nil,    69,   nil,    24,   nil,   116,   nil,   116,   nil,
   nil,   nil,    17,    24,    24,   nil,   nil,    63,   nil,    44,
   nil,    25,   nil,    28,   nil,    63,   118,     4,    54,   nil,
    66,    64,   nil,   nil,    66,   nil,    28,    28,   nil,   nil,
   nil,   nil,   nil,    25,   nil,   nil,   nil,   nil,   nil,   116,
   nil,   116,    36,    21,    25,    30,    28,   nil,    36,   nil,
   nil,   nil,    44,   nil,   nil,   nil,    44,    62,    46,   nil,
   nil,    82,    28,    68,   nil,   nil,   nil,    46,   nil,    44,
   nil,   nil,   nil,   nil,   nil,    44,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    54,    25,    20,    60,   nil,    24,   nil,
   nil,   nil,   nil,    25,    25,    54,    21,   nil,   nil,   nil,
   nil,    36,     8,    21,    36,    21,   nil,   nil,   nil,   nil,
   nil,    24,    21,   nil,   nil,   116,   nil,   nil,   nil,   nil,
   nil,    21,   nil,    54,    66,    20,   nil,   nil,    20,    54,
    66,   nil,   nil,   nil,    32,    23,    21,    44,   116,    21,
   nil,   nil,    85,   nil,   nil,    21,    89,    89,    65,    66,
    20,     9,   nil,     7,     9,    21,   nil,    82,    21,   nil,
    54,    21,   nil,    10,   nil,    89,    89,   nil,    14,    24,
   nil,    24,   nil,    16,    57,    24,    24,    58,    25,   nil,
   nil,   nil,    30,    66,    62,    25,    66,    65,    62,    46,
   nil,   nil,    24,    21,    21,   nil,    32,   nil,    21,    21,
    46,    25,    21,    28,   nil,    20,   nil,   nil,    60,    30,
   nil,   nil,    20,    20,    66,   nil,    21,   nil,   nil,    60,
    44,   nil,   nil,    21,    21,   nil,    66,   nil,    53,    30,
   nil,     9,   nil,   nil,    21,   nil,    32,   nil,     9,     9,
    28,    20,   nil,     8,   nil,    21,    32,   nil,     8,   nil,
   nil,    65,    21,   nil,    66,   nil,   nil,   nil,    63,    25,
   nil,    25,    66,   nil,    30,   nil,    25,     9,   nil,   nil,
   nil,   nil,   nil,    30,   nil,   nil,   nil,   110,   nil,    63,
   nil,   nil,   nil,   nil,    22,   nil,    54,   nil,   nil,   nil,
   nil,    20,   nil,   nil,   nil,   nil,    20,    21,   nil,   nil,
   nil,   nil,    21,   nil,   nil,   nil,    20,    21,   nil,    89,
   nil,    35,    16,    19,    19,    89,   nil,    21,    19,    19,
   nil,   nil,    19,   nil,   nil,   114,    21,   nil,   nil,   nil,
   nil,   nil,     9,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    60,    20,   nil,   nil,    66,     8,
   nil,    66,   nil,   nil,   nil,    20,    21,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,    21,   nil,   nil,    24,
    21,     9,   nil,   nil,   nil,   nil,    24,   nil,   nil,    28,
   nil,     9,    44,   nil,   nil,    24,   nil,    20,    20,   nil,
   110,    20,   nil,   110,   nil,   nil,    21,    20,    21,    21,
   nil,   nil,    21,   nil,   nil,   nil,   nil,    60,    21,    24,
    89,    16,    24,     9,     9,   nil,   nil,     9,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
    44,   nil,   nil,    21,    24,   nil,    20,    20,   nil,    21,
   nil,   nil,   nil,    24,   nil,   nil,    19,    21,    21,   nil,
   nil,   nil,   nil,   nil,     9,   nil,   nil,   nil,   nil,    25,
     8,   nil,     9,     9,   110,   nil,   110,   nil,    54,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    28,   nil,
   nil,   nil,   nil,   nil,    27,   nil,   nil,   nil,   nil,    24,
    21,   114,   nil,   nil,   114,   nil,    24,    24,   nil,    25,
    20,   nil,    25,   nil,    30,    54,    20,    24,    20,   nil,
   nil,    21,   110,   110,   nil,   nil,   nil,    21,    24,    21,
   nil,   nil,   nil,   nil,    25,    24,     9,   nil,   nil,   nil,
   nil,   nil,     9,   nil,   114,   nil,   nil,   114,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,    40,   nil,   110,   nil,
   110,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,   nil,    25,   110,   nil,   nil,    40,
    40,    40,   nil,    40,   nil,    24,   nil,   nil,   nil,    25,
    24,    32,   nil,   nil,   nil,   nil,    25,    25,   nil,   nil,
    24,   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,    66,
   nil,    40,    40,   nil,   nil,    40,   nil,   nil,   114,   nil,
   nil,   114,   nil,   114,   nil,    25,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    27,   nil,    24,
   nil,   114,   nil,    27,   114,   nil,   114,   nil,   nil,    24,
   nil,   nil,   nil,    24,   nil,   nil,    40,   nil,   nil,   nil,
   nil,    40,    40,    40,    40,    40,    40,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    25,   nil,   nil,   nil,   114,
    25,    24,    24,   nil,   nil,    24,   nil,   nil,   nil,   114,
    25,    24,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    27,   nil,
    27,   nil,    27,   nil,   nil,   nil,    24,   nil,   nil,   nil,
   nil,   nil,    24,   nil,   nil,   nil,   nil,   nil,   nil,    25,
    24,    24,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    25,
   nil,   nil,   nil,    25,   nil,   nil,   nil,   nil,   nil,   nil,
    40,    40,    40,    40,   nil,   nil,    40,    40,    40,    40,
    40,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    25,    25,    40,   nil,    25,   nil,   nil,   nil,   nil,
   nil,    25,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    24,   nil,   nil,   nil,   nil,   nil,
    24,   nil,    24,   nil,   nil,   nil,    40,    40,   nil,   nil,
   nil,   nil,    25,   nil,   nil,    40,   nil,   nil,   nil,   nil,
    25,    25,   nil,   nil,   nil,   nil,   nil,    27,   nil,   nil,
   nil,    40,   nil,    40,   nil,   nil,   nil,   nil,    40,   nil,
    49,   nil,   nil,   nil,   nil,    49,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,
    50,   nil,    49,    49,    49,    50,   nil,   nil,   nil,    40,
    40,   nil,   nil,   nil,    25,   nil,   nil,   nil,   nil,   nil,
    25,   nil,    25,   nil,    49,   nil,   nil,   nil,   nil,    40,
   nil,   nil,    50,    50,    50,    49,    49,    49,    49,    27,
   nil,    27,   nil,    27,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    50,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    50,    50,    50,    50,   nil,
   nil,   nil,   nil,   nil,    27,    49,   nil,   nil,    27,   nil,
   nil,    27,   nil,   nil,    49,    49,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    27,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    50,   nil,   nil,    40,   nil,
   nil,   nil,   nil,   nil,    50,    50,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   117,   117,   117,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    27,   nil,
   nil,   nil,    27,    27,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    49,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    27,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,
   nil,   nil,    49,   nil,   nil,   nil,   nil,   nil,   nil,    50,
   nil,    40,   nil,   nil,    40,   nil,   nil,   117,   117,   117,
    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    40,   nil,    50,    40,   nil,   nil,    40,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    49,   nil,    49,   nil,   nil,   nil,    49,    49,    40,    40,
   nil,   nil,   nil,    40,    40,   nil,   nil,    40,   nil,   nil,
   nil,   nil,   nil,    49,   nil,    27,   nil,   nil,   nil,   nil,
    50,    40,    50,   nil,   nil,   nil,    50,    50,    40,    40,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    50,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   117,   117,   nil,   nil,   117,   117,    40,   117,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    27,   nil,   nil,   nil,   117,
   117,   117,   117,   nil,   117,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    27,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    40,   nil,   nil,   nil,   nil,    40,   nil,    27,
   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   117,   117,   117,   117,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   117,   117,   117,   117,   nil,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   117,   nil,   117,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    49,   nil,   nil,   nil,   nil,   nil,   nil,    49,   nil,   nil,
   nil,    40,   nil,    40,    40,   nil,    49,    40,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
    50,   nil,   nil,   nil,   nil,   nil,   nil,    50,   nil,   nil,
    49,   nil,   nil,    49,   nil,   nil,    50,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    40,    40,   nil,    49,   nil,   nil,   nil,   nil,
    50,   nil,   nil,    50,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    50,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    49,   nil,   nil,   nil,   nil,   nil,    40,    49,    49,   nil,
   nil,   nil,    40,   nil,    40,   nil,   nil,   nil,    49,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    49,
    50,   nil,   nil,   nil,   nil,   nil,    49,    50,    50,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    50,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    50,
   nil,   nil,   nil,   nil,   nil,   nil,    50,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    49,   nil,   nil,   nil,
   nil,    49,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    49,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    50,   nil,   nil,   nil,
   nil,    50,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    50,   nil,   nil,   nil,    31,   nil,   nil,   nil,   nil,
    49,   nil,   nil,    31,    31,    31,   nil,   nil,   nil,   nil,
    49,   nil,   nil,   nil,    49,   nil,    31,    31,    31,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    50,    31,    31,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    50,   nil,    49,    49,    50,   nil,    49,   nil,   nil,   nil,
   nil,   nil,    49,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    50,    50,   nil,   nil,    50,    49,   nil,   nil,
   nil,   nil,    50,    49,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    49,    49,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    50,   nil,   nil,
   nil,   nil,   nil,    50,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    50,    50,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    49,   nil,   nil,   nil,   nil,
   nil,    49,   nil,    49,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    31,    31,    31,   nil,   nil,
    31,    31,   nil,   nil,   nil,    50,   nil,   nil,   nil,   nil,
   nil,    50,   nil,    50,   nil,   nil,    31,   nil,   nil,    31,
    31,    31,    31,    31,    31,    31,    31,    31,    31,    31,
    31,    31,    31,    31,    31,    31,    31,    31,    31,    31,
    31,    31,    31,    31,    31,   nil,   nil,   nil,   nil,   nil,
    31,    31,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    31,
   nil,   nil,   nil,   nil,   nil,   nil,    31,   nil,    31,   nil,
    31,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    31,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    31,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    31,   nil,    31,    31,    31,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    31,   nil,    31,
   nil,    31,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    31,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    31,    31,    31,   nil,   nil,   nil,
   nil,   nil,    31,   nil,   nil,   nil,    31,   nil,   nil,    31,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    31,    31,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    31,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    31,    31,   nil,   nil,
    31,    31,    31,    31,   nil,   nil,   nil,    31,    31,   nil,
   nil,    31,    31,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    31,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    31,   nil,    31,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    31,
    31,    31,    31,   nil,    31,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    31,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    31,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    31,    31,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    31 ]

racc_goto_pointer = [
   nil,   101,    71,   nil,    87,    69,    23,    73,   -43,    31,
  -310,  -479,  -525,   nil,     0,   107,    31,  -184,    60,   136,
     5,    16,  -190,  -381,   289,   379,  -159,   827,    98,    18,
   -12,  1945,   107,   nil,   nil,  -164,    42,  -212,  -374,  -325,
   941,    94,    -3,    -1,   -13,   nil,   -29,    -4,  -242,  1230,
  1260,  -291,    47,   -66,    -8,   nil,   nil,     6,     9,  -246,
   -10,    84,   -50,   151,  -221,    76,   124,  -351,  -249,  -410,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    77,    90,    95,
   nil,  -169,  -292,  -615,  -471,  -312,    92,   nil,  -186,    22,
   nil,  -513,    95,  -198,   112,  -341,   110,   108,  -498,   109,
  -499,  -356,  -673,  -357,  -499,  -158,  -167,  -352,  -599,  -745,
    20,  -578,  -694,  -395,   127,  -570,  -366,   737,  -444,   nil,
  -560,  -530,  -693,  -664,  -307,  -424,  -104,  -453,   206,  -526,
  -524,    -9,   nil,   -17,   -17,  -610,  -260,  -389,   nil,   nil,
   174,   177,    26,   174,   174,  -111,  -251,   175,  -294,  -292,
  -498,  -659,  -679,  -720,   nil,   nil,  -416 ]

racc_goto_default = [
   nil,   nil,   nil,     3,   nil,     4,   332,   279,   nil,   313,
   nil,   779,   nil,   278,   nil,   nil,   nil,    11,    12,    18,
   219,   308,   nil,   nil,   217,   218,   nil,   272,    17,   nil,
   424,    21,    22,    23,    24,   627,   nil,   nil,   nil,   nil,
   296,   395,    31,   nil,   nil,    33,    36,    35,   nil,   214,
   215,   344,   nil,   123,   403,   122,   125,    74,    75,   nil,
    45,   nil,   635,   396,   nil,   397,   408,   581,   471,   270,
   256,    46,    47,    48,    49,    50,    51,    52,   nil,   257,
    58,   nil,   nil,   nil,   nil,   nil,   nil,    66,   nil,   521,
    67,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   771,
   656,   nil,   772,   nil,   646,   nil,   648,   nil,   837,   597,
   nil,   nil,   nil,   654,   nil,   nil,   nil,   693,   nil,   nil,
   nil,   nil,   407,   nil,   nil,   nil,   nil,   nil,    73,    76,
    77,   nil,   nil,   nil,   nil,   nil,   557,   nil,   nil,   nil,
   647,   658,   659,   738,   662,   665,   274 ]

racc_reduce_table = [
  0, 0, :racc_error,
  1, 138, :_reduce_none,
  2, 139, :_reduce_2,
  0, 140, :_reduce_3,
  1, 140, :_reduce_4,
  3, 140, :_reduce_5,
  2, 140, :_reduce_6,
  1, 142, :_reduce_none,
  4, 142, :_reduce_8,
  4, 145, :_reduce_9,
  2, 146, :_reduce_10,
  0, 150, :_reduce_11,
  1, 150, :_reduce_12,
  3, 150, :_reduce_13,
  2, 150, :_reduce_14,
  0, 166, :_reduce_15,
  4, 144, :_reduce_16,
  3, 144, :_reduce_17,
  3, 144, :_reduce_18,
  3, 144, :_reduce_19,
  2, 144, :_reduce_20,
  3, 144, :_reduce_21,
  3, 144, :_reduce_22,
  3, 144, :_reduce_23,
  3, 144, :_reduce_24,
  3, 144, :_reduce_25,
  4, 144, :_reduce_26,
  1, 144, :_reduce_none,
  3, 144, :_reduce_28,
  3, 144, :_reduce_29,
  6, 144, :_reduce_30,
  5, 144, :_reduce_31,
  5, 144, :_reduce_32,
  5, 144, :_reduce_33,
  5, 144, :_reduce_34,
  3, 144, :_reduce_35,
  3, 144, :_reduce_36,
  3, 144, :_reduce_37,
  3, 144, :_reduce_38,
  1, 144, :_reduce_none,
  3, 154, :_reduce_40,
  3, 154, :_reduce_41,
  1, 165, :_reduce_none,
  3, 165, :_reduce_43,
  3, 165, :_reduce_44,
  3, 165, :_reduce_45,
  2, 165, :_reduce_46,
  1, 165, :_reduce_none,
  1, 153, :_reduce_none,
  1, 156, :_reduce_none,
  1, 156, :_reduce_none,
  1, 170, :_reduce_none,
  4, 170, :_reduce_52,
  4, 170, :_reduce_53,
  0, 176, :_reduce_54,
  5, 174, :_reduce_55,
  2, 169, :_reduce_56,
  3, 169, :_reduce_57,
  4, 169, :_reduce_58,
  5, 169, :_reduce_59,
  4, 169, :_reduce_60,
  5, 169, :_reduce_61,
  2, 169, :_reduce_62,
  2, 169, :_reduce_63,
  2, 169, :_reduce_64,
  2, 169, :_reduce_65,
  2, 169, :_reduce_66,
  1, 155, :_reduce_67,
  3, 155, :_reduce_68,
  1, 180, :_reduce_69,
  3, 180, :_reduce_70,
  1, 179, :_reduce_none,
  2, 179, :_reduce_72,
  3, 179, :_reduce_73,
  5, 179, :_reduce_74,
  2, 179, :_reduce_75,
  4, 179, :_reduce_76,
  2, 179, :_reduce_77,
  4, 179, :_reduce_78,
  1, 179, :_reduce_79,
  3, 179, :_reduce_80,
  1, 183, :_reduce_none,
  3, 183, :_reduce_82,
  2, 182, :_reduce_83,
  3, 182, :_reduce_84,
  1, 185, :_reduce_85,
  3, 185, :_reduce_86,
  1, 184, :_reduce_87,
  1, 184, :_reduce_88,
  4, 184, :_reduce_89,
  3, 184, :_reduce_90,
  3, 184, :_reduce_91,
  3, 184, :_reduce_92,
  3, 184, :_reduce_93,
  2, 184, :_reduce_94,
  1, 184, :_reduce_95,
  1, 162, :_reduce_96,
  1, 162, :_reduce_97,
  4, 162, :_reduce_98,
  3, 162, :_reduce_99,
  3, 162, :_reduce_100,
  3, 162, :_reduce_101,
  3, 162, :_reduce_102,
  2, 162, :_reduce_103,
  1, 162, :_reduce_104,
  1, 188, :_reduce_105,
  1, 188, :_reduce_none,
  2, 189, :_reduce_107,
  1, 189, :_reduce_108,
  3, 189, :_reduce_109,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 193, :_reduce_115,
  1, 193, :_reduce_none,
  1, 151, :_reduce_none,
  1, 151, :_reduce_none,
  1, 152, :_reduce_119,
  0, 196, :_reduce_120,
  4, 152, :_reduce_121,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  1, 192, :_reduce_none,
  3, 168, :_reduce_192,
  5, 168, :_reduce_193,
  3, 168, :_reduce_194,
  5, 168, :_reduce_195,
  6, 168, :_reduce_196,
  5, 168, :_reduce_197,
  5, 168, :_reduce_198,
  5, 168, :_reduce_199,
  5, 168, :_reduce_200,
  4, 168, :_reduce_201,
  3, 168, :_reduce_202,
  3, 168, :_reduce_203,
  3, 168, :_reduce_204,
  3, 168, :_reduce_205,
  3, 168, :_reduce_206,
  3, 168, :_reduce_207,
  3, 168, :_reduce_208,
  3, 168, :_reduce_209,
  3, 168, :_reduce_210,
  4, 168, :_reduce_211,
  4, 168, :_reduce_212,
  2, 168, :_reduce_213,
  2, 168, :_reduce_214,
  3, 168, :_reduce_215,
  3, 168, :_reduce_216,
  3, 168, :_reduce_217,
  3, 168, :_reduce_218,
  3, 168, :_reduce_219,
  3, 168, :_reduce_220,
  3, 168, :_reduce_221,
  3, 168, :_reduce_222,
  3, 168, :_reduce_223,
  3, 168, :_reduce_224,
  3, 168, :_reduce_225,
  3, 168, :_reduce_226,
  3, 168, :_reduce_227,
  2, 168, :_reduce_228,
  2, 168, :_reduce_229,
  3, 168, :_reduce_230,
  3, 168, :_reduce_231,
  3, 168, :_reduce_232,
  3, 168, :_reduce_233,
  3, 168, :_reduce_234,
  6, 168, :_reduce_235,
  1, 168, :_reduce_none,
  1, 164, :_reduce_none,
  1, 198, :_reduce_none,
  2, 198, :_reduce_none,
  4, 198, :_reduce_240,
  2, 198, :_reduce_241,
  3, 203, :_reduce_242,
  0, 204, :_reduce_243,
  1, 204, :_reduce_none,
  0, 159, :_reduce_245,
  1, 159, :_reduce_none,
  2, 159, :_reduce_none,
  4, 159, :_reduce_248,
  2, 159, :_reduce_249,
  1, 178, :_reduce_250,
  2, 178, :_reduce_251,
  2, 178, :_reduce_252,
  4, 178, :_reduce_253,
  1, 178, :_reduce_254,
  0, 207, :_reduce_255,
  2, 173, :_reduce_256,
  2, 206, :_reduce_257,
  2, 205, :_reduce_258,
  0, 205, :_reduce_259,
  1, 200, :_reduce_260,
  2, 200, :_reduce_261,
  3, 200, :_reduce_262,
  4, 200, :_reduce_263,
  3, 163, :_reduce_264,
  4, 163, :_reduce_265,
  2, 163, :_reduce_266,
  1, 197, :_reduce_none,
  1, 197, :_reduce_none,
  1, 197, :_reduce_none,
  1, 197, :_reduce_none,
  1, 197, :_reduce_none,
  1, 197, :_reduce_none,
  1, 197, :_reduce_none,
  1, 197, :_reduce_none,
  1, 197, :_reduce_275,
  3, 197, :_reduce_276,
  0, 231, :_reduce_277,
  0, 232, :_reduce_278,
  6, 197, :_reduce_279,
  3, 197, :_reduce_280,
  3, 197, :_reduce_281,
  2, 197, :_reduce_282,
  3, 197, :_reduce_283,
  3, 197, :_reduce_284,
  1, 197, :_reduce_285,
  4, 197, :_reduce_286,
  3, 197, :_reduce_287,
  1, 197, :_reduce_288,
  5, 197, :_reduce_289,
  4, 197, :_reduce_290,
  3, 197, :_reduce_291,
  2, 197, :_reduce_292,
  1, 197, :_reduce_none,
  2, 197, :_reduce_294,
  0, 233, :_reduce_295,
  3, 197, :_reduce_296,
  6, 197, :_reduce_297,
  6, 197, :_reduce_298,
  0, 234, :_reduce_299,
  0, 235, :_reduce_300,
  7, 197, :_reduce_301,
  0, 236, :_reduce_302,
  0, 237, :_reduce_303,
  7, 197, :_reduce_304,
  5, 197, :_reduce_305,
  4, 197, :_reduce_306,
  0, 238, :_reduce_307,
  0, 239, :_reduce_308,
  9, 197, :_reduce_309,
  0, 240, :_reduce_310,
  6, 197, :_reduce_311,
  0, 241, :_reduce_312,
  7, 197, :_reduce_313,
  0, 242, :_reduce_314,
  5, 197, :_reduce_315,
  0, 243, :_reduce_316,
  6, 197, :_reduce_317,
  0, 244, :_reduce_318,
  0, 245, :_reduce_319,
  9, 197, :_reduce_320,
  1, 197, :_reduce_321,
  1, 197, :_reduce_322,
  1, 197, :_reduce_323,
  1, 197, :_reduce_324,
  1, 158, :_reduce_none,
  1, 224, :_reduce_326,
  1, 227, :_reduce_327,
  1, 219, :_reduce_none,
  1, 219, :_reduce_none,
  2, 219, :_reduce_330,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 220, :_reduce_none,
  5, 220, :_reduce_334,
  1, 148, :_reduce_none,
  2, 148, :_reduce_336,
  1, 223, :_reduce_none,
  1, 223, :_reduce_none,
  1, 246, :_reduce_339,
  3, 246, :_reduce_340,
  1, 249, :_reduce_341,
  3, 249, :_reduce_342,
  1, 248, :_reduce_none,
  4, 248, :_reduce_344,
  6, 248, :_reduce_345,
  3, 248, :_reduce_346,
  5, 248, :_reduce_347,
  2, 248, :_reduce_348,
  4, 248, :_reduce_349,
  1, 248, :_reduce_350,
  3, 248, :_reduce_351,
  6, 250, :_reduce_352,
  8, 250, :_reduce_353,
  4, 250, :_reduce_354,
  6, 250, :_reduce_355,
  4, 250, :_reduce_356,
  2, 250, :_reduce_none,
  6, 250, :_reduce_358,
  2, 250, :_reduce_359,
  4, 250, :_reduce_360,
  6, 250, :_reduce_361,
  2, 250, :_reduce_362,
  4, 250, :_reduce_363,
  2, 250, :_reduce_364,
  4, 250, :_reduce_365,
  1, 250, :_reduce_366,
  0, 175, :_reduce_367,
  1, 175, :_reduce_368,
  3, 256, :_reduce_369,
  1, 256, :_reduce_370,
  4, 256, :_reduce_371,
  0, 257, :_reduce_372,
  2, 257, :_reduce_373,
  1, 258, :_reduce_374,
  3, 258, :_reduce_375,
  1, 259, :_reduce_376,
  1, 259, :_reduce_none,
  0, 263, :_reduce_378,
  3, 218, :_reduce_379,
  4, 261, :_reduce_380,
  1, 261, :_reduce_381,
  0, 266, :_reduce_382,
  4, 262, :_reduce_383,
  0, 267, :_reduce_384,
  4, 262, :_reduce_385,
  0, 268, :_reduce_386,
  5, 265, :_reduce_387,
  2, 171, :_reduce_388,
  4, 171, :_reduce_389,
  4, 171, :_reduce_390,
  2, 217, :_reduce_391,
  4, 217, :_reduce_392,
  4, 217, :_reduce_393,
  3, 217, :_reduce_394,
  3, 217, :_reduce_395,
  3, 217, :_reduce_396,
  2, 217, :_reduce_397,
  1, 217, :_reduce_398,
  4, 217, :_reduce_399,
  0, 270, :_reduce_400,
  5, 216, :_reduce_401,
  0, 271, :_reduce_402,
  5, 216, :_reduce_403,
  5, 222, :_reduce_404,
  1, 272, :_reduce_405,
  1, 272, :_reduce_none,
  6, 147, :_reduce_407,
  0, 147, :_reduce_408,
  1, 273, :_reduce_409,
  1, 273, :_reduce_none,
  1, 273, :_reduce_none,
  2, 274, :_reduce_412,
  1, 274, :_reduce_none,
  2, 149, :_reduce_414,
  1, 149, :_reduce_none,
  1, 208, :_reduce_none,
  1, 208, :_reduce_none,
  1, 208, :_reduce_none,
  1, 209, :_reduce_419,
  1, 276, :_reduce_420,
  2, 276, :_reduce_421,
  3, 277, :_reduce_422,
  1, 277, :_reduce_423,
  1, 277, :_reduce_424,
  3, 210, :_reduce_425,
  4, 211, :_reduce_426,
  3, 212, :_reduce_427,
  0, 281, :_reduce_428,
  3, 281, :_reduce_429,
  1, 282, :_reduce_430,
  2, 282, :_reduce_431,
  3, 213, :_reduce_432,
  0, 284, :_reduce_433,
  3, 284, :_reduce_434,
  0, 278, :_reduce_435,
  2, 278, :_reduce_436,
  0, 279, :_reduce_437,
  2, 279, :_reduce_438,
  0, 280, :_reduce_439,
  2, 280, :_reduce_440,
  1, 283, :_reduce_441,
  2, 283, :_reduce_442,
  0, 286, :_reduce_443,
  4, 283, :_reduce_444,
  1, 285, :_reduce_445,
  1, 285, :_reduce_446,
  1, 285, :_reduce_447,
  1, 285, :_reduce_none,
  1, 194, :_reduce_449,
  3, 195, :_reduce_450,
  1, 275, :_reduce_451,
  1, 275, :_reduce_452,
  2, 275, :_reduce_453,
  2, 275, :_reduce_454,
  1, 186, :_reduce_455,
  1, 186, :_reduce_456,
  1, 186, :_reduce_457,
  1, 186, :_reduce_458,
  1, 186, :_reduce_459,
  1, 187, :_reduce_460,
  1, 187, :_reduce_461,
  1, 187, :_reduce_462,
  1, 187, :_reduce_463,
  1, 187, :_reduce_464,
  1, 187, :_reduce_465,
  1, 187, :_reduce_466,
  1, 214, :_reduce_467,
  1, 214, :_reduce_468,
  1, 157, :_reduce_469,
  1, 157, :_reduce_470,
  1, 161, :_reduce_471,
  1, 161, :_reduce_472,
  1, 225, :_reduce_473,
  3, 225, :_reduce_474,
  2, 225, :_reduce_475,
  3, 228, :_reduce_476,
  2, 228, :_reduce_477,
  6, 264, :_reduce_478,
  8, 264, :_reduce_479,
  4, 264, :_reduce_480,
  6, 264, :_reduce_481,
  4, 264, :_reduce_482,
  6, 264, :_reduce_483,
  2, 264, :_reduce_484,
  4, 264, :_reduce_485,
  6, 264, :_reduce_486,
  2, 264, :_reduce_487,
  4, 264, :_reduce_488,
  2, 264, :_reduce_489,
  4, 264, :_reduce_490,
  1, 264, :_reduce_491,
  0, 264, :_reduce_492,
  1, 260, :_reduce_493,
  1, 260, :_reduce_494,
  1, 260, :_reduce_495,
  1, 260, :_reduce_496,
  1, 247, :_reduce_none,
  1, 247, :_reduce_none,
  1, 288, :_reduce_499,
  3, 288, :_reduce_500,
  1, 251, :_reduce_501,
  3, 251, :_reduce_502,
  3, 289, :_reduce_503,
  3, 290, :_reduce_504,
  1, 252, :_reduce_505,
  3, 252, :_reduce_506,
  1, 287, :_reduce_507,
  3, 287, :_reduce_508,
  1, 291, :_reduce_none,
  1, 291, :_reduce_none,
  2, 253, :_reduce_511,
  1, 253, :_reduce_512,
  1, 292, :_reduce_none,
  1, 292, :_reduce_none,
  2, 255, :_reduce_515,
  2, 254, :_reduce_516,
  0, 254, :_reduce_517,
  1, 229, :_reduce_none,
  3, 229, :_reduce_519,
  0, 215, :_reduce_520,
  2, 215, :_reduce_none,
  1, 202, :_reduce_522,
  3, 202, :_reduce_523,
  3, 293, :_reduce_524,
  2, 293, :_reduce_525,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 172, :_reduce_none,
  1, 172, :_reduce_none,
  1, 172, :_reduce_none,
  1, 172, :_reduce_none,
  1, 269, :_reduce_none,
  1, 269, :_reduce_none,
  1, 269, :_reduce_none,
  1, 230, :_reduce_none,
  1, 230, :_reduce_none,
  0, 141, :_reduce_none,
  1, 141, :_reduce_none,
  0, 167, :_reduce_none,
  1, 167, :_reduce_none,
  2, 181, :_reduce_542,
  2, 160, :_reduce_543,
  0, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 226, :_reduce_547,
  1, 226, :_reduce_none,
  1, 143, :_reduce_none,
  2, 143, :_reduce_none,
  0, 199, :_reduce_551 ]

racc_reduce_n = 552

racc_shift_n = 961

racc_token_table = {
  false => 0,
  :error => 1,
  :kCLASS => 2,
  :kMODULE => 3,
  :kDEF => 4,
  :kUNDEF => 5,
  :kBEGIN => 6,
  :kRESCUE => 7,
  :kENSURE => 8,
  :kEND => 9,
  :kIF => 10,
  :kUNLESS => 11,
  :kTHEN => 12,
  :kELSIF => 13,
  :kELSE => 14,
  :kCASE => 15,
  :kWHEN => 16,
  :kWHILE => 17,
  :kUNTIL => 18,
  :kFOR => 19,
  :kBREAK => 20,
  :kNEXT => 21,
  :kREDO => 22,
  :kRETRY => 23,
  :kIN => 24,
  :kDO => 25,
  :kDO_COND => 26,
  :kDO_BLOCK => 27,
  :kDO_LAMBDA => 28,
  :kRETURN => 29,
  :kYIELD => 30,
  :kSUPER => 31,
  :kSELF => 32,
  :kNIL => 33,
  :kTRUE => 34,
  :kFALSE => 35,
  :kAND => 36,
  :kOR => 37,
  :kNOT => 38,
  :kIF_MOD => 39,
  :kUNLESS_MOD => 40,
  :kWHILE_MOD => 41,
  :kUNTIL_MOD => 42,
  :kRESCUE_MOD => 43,
  :kALIAS => 44,
  :kDEFINED => 45,
  :klBEGIN => 46,
  :klEND => 47,
  :k__LINE__ => 48,
  :k__FILE__ => 49,
  :k__ENCODING__ => 50,
  :tIDENTIFIER => 51,
  :tFID => 52,
  :tGVAR => 53,
  :tIVAR => 54,
  :tCONSTANT => 55,
  :tLABEL => 56,
  :tCVAR => 57,
  :tNTH_REF => 58,
  :tBACK_REF => 59,
  :tSTRING_CONTENT => 60,
  :tINTEGER => 61,
  :tFLOAT => 62,
  :tUPLUS => 63,
  :tUMINUS => 64,
  :tUNARY_NUM => 65,
  :tPOW => 66,
  :tCMP => 67,
  :tEQ => 68,
  :tEQQ => 69,
  :tNEQ => 70,
  :tGEQ => 71,
  :tLEQ => 72,
  :tANDOP => 73,
  :tOROP => 74,
  :tMATCH => 75,
  :tNMATCH => 76,
  :tDOT => 77,
  :tDOT2 => 78,
  :tDOT3 => 79,
  :tAREF => 80,
  :tASET => 81,
  :tLSHFT => 82,
  :tRSHFT => 83,
  :tCOLON2 => 84,
  :tCOLON3 => 85,
  :tOP_ASGN => 86,
  :tASSOC => 87,
  :tLPAREN => 88,
  :tLPAREN2 => 89,
  :tRPAREN => 90,
  :tLPAREN_ARG => 91,
  :tLBRACK => 92,
  :tLBRACK2 => 93,
  :tRBRACK => 94,
  :tLBRACE => 95,
  :tLBRACE_ARG => 96,
  :tSTAR => 97,
  :tSTAR2 => 98,
  :tAMPER => 99,
  :tAMPER2 => 100,
  :tTILDE => 101,
  :tPERCENT => 102,
  :tDIVIDE => 103,
  :tPLUS => 104,
  :tMINUS => 105,
  :tLT => 106,
  :tGT => 107,
  :tPIPE => 108,
  :tBANG => 109,
  :tCARET => 110,
  :tLCURLY => 111,
  :tRCURLY => 112,
  :tBACK_REF2 => 113,
  :tSYMBEG => 114,
  :tSTRING_BEG => 115,
  :tXSTRING_BEG => 116,
  :tREGEXP_BEG => 117,
  :tREGEXP_OPT => 118,
  :tWORDS_BEG => 119,
  :tQWORDS_BEG => 120,
  :tSTRING_DBEG => 121,
  :tSTRING_DVAR => 122,
  :tSTRING_END => 123,
  :tSTRING => 124,
  :tSYMBOL => 125,
  :tNL => 126,
  :tEH => 127,
  :tCOLON => 128,
  :tCOMMA => 129,
  :tSPACE => 130,
  :tSEMI => 131,
  :tLAMBDA => 132,
  :tLAMBEG => 133,
  :tCHARACTER => 134,
  :tEQL => 135,
  :tLOWEST => 136 }

racc_nt_base = 137

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]
Ractor.make_shareable(Racc_arg) if defined?(Ractor)

Racc_token_to_s_table = [
  "$end",
  "error",
  "kCLASS",
  "kMODULE",
  "kDEF",
  "kUNDEF",
  "kBEGIN",
  "kRESCUE",
  "kENSURE",
  "kEND",
  "kIF",
  "kUNLESS",
  "kTHEN",
  "kELSIF",
  "kELSE",
  "kCASE",
  "kWHEN",
  "kWHILE",
  "kUNTIL",
  "kFOR",
  "kBREAK",
  "kNEXT",
  "kREDO",
  "kRETRY",
  "kIN",
  "kDO",
  "kDO_COND",
  "kDO_BLOCK",
  "kDO_LAMBDA",
  "kRETURN",
  "kYIELD",
  "kSUPER",
  "kSELF",
  "kNIL",
  "kTRUE",
  "kFALSE",
  "kAND",
  "kOR",
  "kNOT",
  "kIF_MOD",
  "kUNLESS_MOD",
  "kWHILE_MOD",
  "kUNTIL_MOD",
  "kRESCUE_MOD",
  "kALIAS",
  "kDEFINED",
  "klBEGIN",
  "klEND",
  "k__LINE__",
  "k__FILE__",
  "k__ENCODING__",
  "tIDENTIFIER",
  "tFID",
  "tGVAR",
  "tIVAR",
  "tCONSTANT",
  "tLABEL",
  "tCVAR",
  "tNTH_REF",
  "tBACK_REF",
  "tSTRING_CONTENT",
  "tINTEGER",
  "tFLOAT",
  "tUPLUS",
  "tUMINUS",
  "tUNARY_NUM",
  "tPOW",
  "tCMP",
  "tEQ",
  "tEQQ",
  "tNEQ",
  "tGEQ",
  "tLEQ",
  "tANDOP",
  "tOROP",
  "tMATCH",
  "tNMATCH",
  "tDOT",
  "tDOT2",
  "tDOT3",
  "tAREF",
  "tASET",
  "tLSHFT",
  "tRSHFT",
  "tCOLON2",
  "tCOLON3",
  "tOP_ASGN",
  "tASSOC",
  "tLPAREN",
  "tLPAREN2",
  "tRPAREN",
  "tLPAREN_ARG",
  "tLBRACK",
  "tLBRACK2",
  "tRBRACK",
  "tLBRACE",
  "tLBRACE_ARG",
  "tSTAR",
  "tSTAR2",
  "tAMPER",
  "tAMPER2",
  "tTILDE",
  "tPERCENT",
  "tDIVIDE",
  "tPLUS",
  "tMINUS",
  "tLT",
  "tGT",
  "tPIPE",
  "tBANG",
  "tCARET",
  "tLCURLY",
  "tRCURLY",
  "tBACK_REF2",
  "tSYMBEG",
  "tSTRING_BEG",
  "tXSTRING_BEG",
  "tREGEXP_BEG",
  "tREGEXP_OPT",
  "tWORDS_BEG",
  "tQWORDS_BEG",
  "tSTRING_DBEG",
  "tSTRING_DVAR",
  "tSTRING_END",
  "tSTRING",
  "tSYMBOL",
  "tNL",
  "tEH",
  "tCOLON",
  "tCOMMA",
  "tSPACE",
  "tSEMI",
  "tLAMBDA",
  "tLAMBEG",
  "tCHARACTER",
  "tEQL",
  "tLOWEST",
  "$start",
  "program",
  "top_compstmt",
  "top_stmts",
  "opt_terms",
  "top_stmt",
  "terms",
  "stmt",
  "bodystmt",
  "compstmt",
  "opt_rescue",
  "opt_else",
  "opt_ensure",
  "stmts",
  "fitem",
  "undef_list",
  "expr_value",
  "command_asgn",
  "mlhs",
  "command_call",
  "var_lhs",
  "primary_value",
  "opt_call_args",
  "rbracket",
  "backref",
  "lhs",
  "mrhs",
  "arg_value",
  "expr",
  "@1",
  "opt_nl",
  "arg",
  "command",
  "block_command",
  "block_call",
  "operation2",
  "command_args",
  "cmd_brace_block",
  "opt_block_param",
  "@2",
  "operation",
  "call_args",
  "mlhs_basic",
  "mlhs_inner",
  "rparen",
  "mlhs_head",
  "mlhs_item",
  "mlhs_node",
  "mlhs_post",
  "user_variable",
  "keyword_variable",
  "cname",
  "cpath",
  "fname",
  "op",
  "reswords",
  "fsym",
  "symbol",
  "dsym",
  "@3",
  "primary",
  "aref_args",
  "none",
  "args",
  "trailer",
  "assocs",
  "paren_args",
  "opt_paren_args",
  "opt_block_arg",
  "block_arg",
  "@4",
  "literal",
  "strings",
  "xstring",
  "regexp",
  "words",
  "qwords",
  "var_ref",
  "assoc_list",
  "brace_block",
  "method_call",
  "lambda",
  "then",
  "if_tail",
  "do",
  "case_body",
  "for_var",
  "k_class",
  "superclass",
  "term",
  "k_module",
  "f_arglist",
  "singleton",
  "dot_or_colon",
  "@5",
  "@6",
  "@7",
  "@8",
  "@9",
  "@10",
  "@11",
  "@12",
  "@13",
  "@14",
  "@15",
  "@16",
  "@17",
  "@18",
  "@19",
  "f_marg",
  "f_norm_arg",
  "f_margs",
  "f_marg_list",
  "block_param",
  "f_arg",
  "f_block_optarg",
  "f_rest_arg",
  "opt_f_block_arg",
  "f_block_arg",
  "block_param_def",
  "opt_bv_decl",
  "bv_decls",
  "bvar",
  "f_bad_arg",
  "f_larglist",
  "lambda_body",
  "@20",
  "f_args",
  "do_block",
  "@21",
  "@22",
  "@23",
  "operation3",
  "@24",
  "@25",
  "cases",
  "exc_list",
  "exc_var",
  "numeric",
  "string",
  "string1",
  "string_contents",
  "xstring_contents",
  "regexp_contents",
  "word_list",
  "word",
  "string_content",
  "qword_list",
  "string_dvar",
  "@26",
  "f_optarg",
  "f_arg_item",
  "f_opt",
  "f_block_opt",
  "restarg_mark",
  "blkarg_mark",
  "assoc" ]
Ractor.make_shareable(Racc_token_to_s_table) if defined?(Ractor)

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

# reduce 1 omitted

def _reduce_2(val, _values, result)
                      result = @builder.compstmt(val[0])

    result
end

def _reduce_3(val, _values, result)
                      result = []

    result
end

def _reduce_4(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_5(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_6(val, _values, result)
                      result = [ val[1] ]

    result
end

# reduce 7 omitted

def _reduce_8(val, _values, result)
                      result = @builder.preexe(val[0], val[1], val[2], val[3])

    result
end

def _reduce_9(val, _values, result)
                      rescue_bodies     = val[1]
                      else_t,   else_   = val[2]
                      ensure_t, ensure_ = val[3]

                      if rescue_bodies.empty? && !else_t.nil?
                        diagnostic :warning, :useless_else, nil, else_t
                      end

                      result = @builder.begin_body(val[0],
                                  rescue_bodies,
                                  else_t,   else_,
                                  ensure_t, ensure_)

    result
end

def _reduce_10(val, _values, result)
                      result = @builder.compstmt(val[0])

    result
end

def _reduce_11(val, _values, result)
                      result = []

    result
end

def _reduce_12(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_13(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_14(val, _values, result)
                      result = [ val[1] ]

    result
end

def _reduce_15(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_16(val, _values, result)
                      result = @builder.alias(val[0], val[1], val[3])

    result
end

def _reduce_17(val, _values, result)
                      result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.gvar(val[2]))

    result
end

def _reduce_18(val, _values, result)
                      result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.back_ref(val[2]))

    result
end

def _reduce_19(val, _values, result)
                      diagnostic :error, :nth_ref_alias, nil, val[2]

    result
end

def _reduce_20(val, _values, result)
                      result = @builder.undef_method(val[0], val[1])

    result
end

def _reduce_21(val, _values, result)
                      result = @builder.condition_mod(val[0], nil,
                                                      val[1], val[2])

    result
end

def _reduce_22(val, _values, result)
                      result = @builder.condition_mod(nil, val[0],
                                                      val[1], val[2])

    result
end

def _reduce_23(val, _values, result)
                      result = @builder.loop_mod(:while, val[0], val[1], val[2])

    result
end

def _reduce_24(val, _values, result)
                      result = @builder.loop_mod(:until, val[0], val[1], val[2])

    result
end

def _reduce_25(val, _values, result)
                      rescue_body = @builder.rescue_body(val[1],
                                        nil, nil, nil,
                                        nil, val[2])

                      result = @builder.begin_body(val[0], [ rescue_body ])

    result
end

def _reduce_26(val, _values, result)
                      result = @builder.postexe(val[0], val[1], val[2], val[3])

    result
end

# reduce 27 omitted

def _reduce_28(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1], val[2])

    result
end

def _reduce_29(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_30(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])

    result
end

def _reduce_31(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_32(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_33(val, _values, result)
                      diagnostic :error, :const_reassignment, nil, val[3]

    result
end

def _reduce_34(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_35(val, _values, result)
                      @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_36(val, _values, result)
                      result = @builder.assign(val[0], val[1],
                                  @builder.array(nil, val[2], nil))

    result
end

def _reduce_37(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1], val[2])

    result
end

def _reduce_38(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1],
                                  @builder.array(nil, val[2], nil))

    result
end

# reduce 39 omitted

def _reduce_40(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

def _reduce_41(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

# reduce 42 omitted

def _reduce_43(val, _values, result)
                      result = @builder.logical_op(:and, val[0], val[1], val[2])

    result
end

def _reduce_44(val, _values, result)
                      result = @builder.logical_op(:or, val[0], val[1], val[2])

    result
end

def _reduce_45(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[2], nil)

    result
end

def _reduce_46(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[1], nil)

    result
end

# reduce 47 omitted

# reduce 48 omitted

# reduce 49 omitted

# reduce 50 omitted

# reduce 51 omitted

def _reduce_52(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
end

def _reduce_53(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
end

def _reduce_54(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_55(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_56(val, _values, result)
                      result = @builder.call_method(nil, nil, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_57(val, _values, result)
                      method_call = @builder.call_method(nil, nil, val[0],
                                        nil, val[1], nil)

                      begin_t, args, body, end_t = val[2]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_58(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
end

def _reduce_59(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                        nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_60(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
end

def _reduce_61(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                        nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_62(val, _values, result)
                      result = @builder.keyword_cmd(:super, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_63(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_64(val, _values, result)
                      result = @builder.keyword_cmd(:return, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_65(val, _values, result)
                      result = @builder.keyword_cmd(:break, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_66(val, _values, result)
                      result = @builder.keyword_cmd(:next, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_67(val, _values, result)
                      result = @builder.multi_lhs(nil, val[0], nil)

    result
end

def _reduce_68(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_69(val, _values, result)
                      result = @builder.multi_lhs(nil, val[0], nil)

    result
end

def _reduce_70(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

# reduce 71 omitted

def _reduce_72(val, _values, result)
                      result = val[0].
                                  push(val[1])

    result
end

def _reduce_73(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1], val[2]))

    result
end

def _reduce_74(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1], val[2])).
                                  concat(val[4])

    result
end

def _reduce_75(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1]))

    result
end

def _reduce_76(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1])).
                                  concat(val[3])

    result
end

def _reduce_77(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

def _reduce_78(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]),
                                 *val[3] ]

    result
end

def _reduce_79(val, _values, result)
                      result = [ @builder.splat(val[0]) ]

    result
end

def _reduce_80(val, _values, result)
                      result = [ @builder.splat(val[0]),
                                 *val[2] ]

    result
end

# reduce 81 omitted

def _reduce_82(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_83(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_84(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_85(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_86(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_87(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_88(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_89(val, _values, result)
                      result = @builder.index_asgn(val[0], val[1], val[2], val[3])

    result
end

def _reduce_90(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_91(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_92(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_93(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))

    result
end

def _reduce_94(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))

    result
end

def _reduce_95(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_96(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_97(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_98(val, _values, result)
                      result = @builder.index_asgn(val[0], val[1], val[2], val[3])

    result
end

def _reduce_99(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_100(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_101(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_102(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))

    result
end

def _reduce_103(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))

    result
end

def _reduce_104(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_105(val, _values, result)
                      diagnostic :error, :module_name_const, nil, val[0]

    result
end

# reduce 106 omitted

def _reduce_107(val, _values, result)
                      result = @builder.const_global(val[0], val[1])

    result
end

def _reduce_108(val, _values, result)
                      result = @builder.const(val[0])

    result
end

def _reduce_109(val, _values, result)
                      result = @builder.const_fetch(val[0], val[1], val[2])

    result
end

# reduce 110 omitted

# reduce 111 omitted

# reduce 112 omitted

# reduce 113 omitted

# reduce 114 omitted

def _reduce_115(val, _values, result)
                      result = @builder.symbol_internal(val[0])

    result
end

# reduce 116 omitted

# reduce 117 omitted

# reduce 118 omitted

def _reduce_119(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_120(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_121(val, _values, result)
                      result = val[0] << val[3]

    result
end

# reduce 122 omitted

# reduce 123 omitted

# reduce 124 omitted

# reduce 125 omitted

# reduce 126 omitted

# reduce 127 omitted

# reduce 128 omitted

# reduce 129 omitted

# reduce 130 omitted

# reduce 131 omitted

# reduce 132 omitted

# reduce 133 omitted

# reduce 134 omitted

# reduce 135 omitted

# reduce 136 omitted

# reduce 137 omitted

# reduce 138 omitted

# reduce 139 omitted

# reduce 140 omitted

# reduce 141 omitted

# reduce 142 omitted

# reduce 143 omitted

# reduce 144 omitted

# reduce 145 omitted

# reduce 146 omitted

# reduce 147 omitted

# reduce 148 omitted

# reduce 149 omitted

# reduce 150 omitted

# reduce 151 omitted

# reduce 152 omitted

# reduce 153 omitted

# reduce 154 omitted

# reduce 155 omitted

# reduce 156 omitted

# reduce 157 omitted

# reduce 158 omitted

# reduce 159 omitted

# reduce 160 omitted

# reduce 161 omitted

# reduce 162 omitted

# reduce 163 omitted

# reduce 164 omitted

# reduce 165 omitted

# reduce 166 omitted

# reduce 167 omitted

# reduce 168 omitted

# reduce 169 omitted

# reduce 170 omitted

# reduce 171 omitted

# reduce 172 omitted

# reduce 173 omitted

# reduce 174 omitted

# reduce 175 omitted

# reduce 176 omitted

# reduce 177 omitted

# reduce 178 omitted

# reduce 179 omitted

# reduce 180 omitted

# reduce 181 omitted

# reduce 182 omitted

# reduce 183 omitted

# reduce 184 omitted

# reduce 185 omitted

# reduce 186 omitted

# reduce 187 omitted

# reduce 188 omitted

# reduce 189 omitted

# reduce 190 omitted

# reduce 191 omitted

def _reduce_192(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

def _reduce_193(val, _values, result)
                      rescue_body = @builder.rescue_body(val[3],
                                        nil, nil, nil,
                                        nil, val[4])

                      rescue_ = @builder.begin_body(val[2], [ rescue_body ])

                      result  = @builder.assign(val[0], val[1], rescue_)

    result
end

def _reduce_194(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_195(val, _values, result)
                      rescue_body = @builder.rescue_body(val[3],
                                        nil, nil, nil,
                                        nil, val[4])

                      rescue_ = @builder.begin_body(val[2], [ rescue_body ])

                      result = @builder.op_assign(val[0], val[1], rescue_)

    result
end

def _reduce_196(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])

    result
end

def _reduce_197(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_198(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_199(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_200(val, _values, result)
                      diagnostic :error, :dynamic_const, nil, val[2], [ val[3] ]

    result
end

def _reduce_201(val, _values, result)
                      diagnostic :error, :dynamic_const, nil, val[1], [ val[2] ]

    result
end

def _reduce_202(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_203(val, _values, result)
                      result = @builder.range_inclusive(val[0], val[1], val[2])

    result
end

def _reduce_204(val, _values, result)
                      result = @builder.range_exclusive(val[0], val[1], val[2])

    result
end

def _reduce_205(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_206(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_207(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_208(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_209(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_210(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_211(val, _values, result)
                      result = @builder.unary_op(val[0],
                                  @builder.binary_op(
                                    @builder.integer(val[1]),
                                      val[2], val[3]))

    result
end

def _reduce_212(val, _values, result)
                      result = @builder.unary_op(val[0],
                                  @builder.binary_op(
                                    @builder.float(val[1]),
                                      val[2], val[3]))

    result
end

def _reduce_213(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_214(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_215(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_216(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_217(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_218(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_219(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_220(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_221(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_222(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_223(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_224(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_225(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_226(val, _values, result)
                      result = @builder.match_op(val[0], val[1], val[2])

    result
end

def _reduce_227(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_228(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[1], nil)

    result
end

def _reduce_229(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_230(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_231(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_232(val, _values, result)
                      result = @builder.logical_op(:and, val[0], val[1], val[2])

    result
end

def _reduce_233(val, _values, result)
                      result = @builder.logical_op(:or, val[0], val[1], val[2])

    result
end

def _reduce_234(val, _values, result)
                      result = @builder.keyword_cmd(:defined?, val[0], nil, [ val[2] ], nil)

    result
end

def _reduce_235(val, _values, result)
                      result = @builder.ternary(val[0], val[1],
                                                val[2], val[4], val[5])

    result
end

# reduce 236 omitted

# reduce 237 omitted

# reduce 238 omitted

# reduce 239 omitted

def _reduce_240(val, _values, result)
                      result = val[0] << @builder.associate(nil, val[2], nil)

    result
end

def _reduce_241(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]

    result
end

def _reduce_242(val, _values, result)
                      result = val

    result
end

def _reduce_243(val, _values, result)
                      result = [ nil, [], nil ]

    result
end

# reduce 244 omitted

def _reduce_245(val, _values, result)
                      result = []

    result
end

# reduce 246 omitted

# reduce 247 omitted

def _reduce_248(val, _values, result)
                      result = val[0] << @builder.associate(nil, val[2], nil)

    result
end

def _reduce_249(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]

    result
end

def _reduce_250(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_251(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_252(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]
                      result.concat(val[1])

    result
end

def _reduce_253(val, _values, result)
                      assocs = @builder.associate(nil, val[2], nil)
                      result = val[0] << assocs
                      result.concat(val[3])

    result
end

def _reduce_254(val, _values, result)
                      result =  [ val[0] ]

    result
end

def _reduce_255(val, _values, result)
                      result = @lexer.cmdarg.dup
                      @lexer.cmdarg.push(true)

    result
end

def _reduce_256(val, _values, result)
                      @lexer.cmdarg = val[0]

                      result = val[1]

    result
end

def _reduce_257(val, _values, result)
                      result = @builder.block_pass(val[0], val[1])

    result
end

def _reduce_258(val, _values, result)
                      result = [ val[1] ]

    result
end

def _reduce_259(val, _values, result)
                      result = []

    result
end

def _reduce_260(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_261(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

def _reduce_262(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_263(val, _values, result)
                      result = val[0] << @builder.splat(val[2], val[3])

    result
end

def _reduce_264(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_265(val, _values, result)
                      result = val[0] << @builder.splat(val[2], val[3])

    result
end

def _reduce_266(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

# reduce 267 omitted

# reduce 268 omitted

# reduce 269 omitted

# reduce 270 omitted

# reduce 271 omitted

# reduce 272 omitted

# reduce 273 omitted

# reduce 274 omitted

def _reduce_275(val, _values, result)
                      result = @builder.call_method(nil, nil, val[0])

    result
end

def _reduce_276(val, _values, result)
                      result = @builder.begin_keyword(val[0], val[1], val[2])

    result
end

def _reduce_277(val, _values, result)
                      result = @lexer.cmdarg.dup
                      @lexer.cmdarg.clear

    result
end

def _reduce_278(val, _values, result)
                      @lexer.state = :expr_endarg

    result
end

def _reduce_279(val, _values, result)
                      @lexer.cmdarg = val[1]

                      result = @builder.begin(val[0], val[2], val[5])

    result
end

def _reduce_280(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_281(val, _values, result)
                      result = @builder.const_fetch(val[0], val[1], val[2])

    result
end

def _reduce_282(val, _values, result)
                      result = @builder.const_global(val[0], val[1])

    result
end

def _reduce_283(val, _values, result)
                      result = @builder.array(val[0], val[1], val[2])

    result
end

def _reduce_284(val, _values, result)
                      result = @builder.associate(val[0], val[1], val[2])

    result
end

def _reduce_285(val, _values, result)
                      result = @builder.keyword_cmd(:return, val[0])

    result
end

def _reduce_286(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0], val[1], val[2], val[3])

    result
end

def _reduce_287(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0], val[1], [], val[2])

    result
end

def _reduce_288(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0])

    result
end

def _reduce_289(val, _values, result)
                      result = @builder.keyword_cmd(:defined?, val[0],
                                                    val[2], [ val[3] ], val[4])

    result
end

def _reduce_290(val, _values, result)
                      result = @builder.not_op(val[0], val[1], val[2], val[3])

    result
end

def _reduce_291(val, _values, result)
                      result = @builder.not_op(val[0], val[1], nil, val[2])

    result
end

def _reduce_292(val, _values, result)
                      method_call = @builder.call_method(nil, nil, val[0])

                      begin_t, args, body, end_t = val[1]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

# reduce 293 omitted

def _reduce_294(val, _values, result)
                      begin_t, args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, args, body, end_t)

    result
end

def _reduce_295(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_296(val, _values, result)
                      lambda_call = @builder.call_lambda(val[0])

                      args, (begin_t, body, end_t) = val[2]
                      result      = @builder.block(lambda_call,
                                      begin_t, args, body, end_t)

                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_297(val, _values, result)
                      else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  val[3], else_t,
                                                  else_,  val[5])

    result
end

def _reduce_298(val, _values, result)
                      else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  else_,  else_t,
                                                  val[3], val[5])

    result
end

def _reduce_299(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_300(val, _values, result)
                      @lexer.cond.pop

    result
end

def _reduce_301(val, _values, result)
                      result = @builder.loop(:while, val[0], val[2], val[3],
                                             val[5], val[6])

    result
end

def _reduce_302(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_303(val, _values, result)
                      @lexer.cond.pop

    result
end

def _reduce_304(val, _values, result)
                      result = @builder.loop(:until, val[0], val[2], val[3],
                                             val[5], val[6])

    result
end

def _reduce_305(val, _values, result)
                      *when_bodies, (else_t, else_body) = *val[3]

                      result = @builder.case(val[0], val[1],
                                             when_bodies, else_t, else_body,
                                             val[4])

    result
end

def _reduce_306(val, _values, result)
                      *when_bodies, (else_t, else_body) = *val[2]

                      result = @builder.case(val[0], nil,
                                             when_bodies, else_t, else_body,
                                             val[3])

    result
end

def _reduce_307(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_308(val, _values, result)
                      @lexer.cond.pop

    result
end

def _reduce_309(val, _values, result)
                      result = @builder.for(val[0], val[1],
                                            val[2], val[4],
                                            val[5], val[7], val[8])

    result
end

def _reduce_310(val, _values, result)
                      local_push
                      @context.in_class = true

    result
end

def _reduce_311(val, _values, result)
                      k_class, ctx = val[0]
                      if @context.in_def
                        diagnostic :error, :class_in_def, nil, k_class
                      end

                      lt_t, superclass = val[2]
                      result = @builder.def_class(k_class, val[1],
                                                  lt_t, superclass,
                                                  val[4], val[5])

                      local_pop
                      @context.in_class = ctx.in_class

    result
end

def _reduce_312(val, _values, result)
                      @context.in_def = false
                      @context.in_class = false
                      local_push

    result
end

def _reduce_313(val, _values, result)
                      k_class, ctx = val[0]
                      result = @builder.def_sclass(k_class, val[1], val[2],
                                                   val[5], val[6])

                      local_pop
                      @context.in_def = ctx.in_def
                      @context.in_class = ctx.in_class

    result
end

def _reduce_314(val, _values, result)
                      @context.in_class = true
                      local_push

    result
end

def _reduce_315(val, _values, result)
                      k_mod, ctx = val[0]
                      if @context.in_def
                        diagnostic :error, :module_in_def, nil, k_mod
                      end

                      result = @builder.def_module(k_mod, val[1],
                                                   val[3], val[4])

                      local_pop
                      @context.in_class = ctx.in_class

    result
end

def _reduce_316(val, _values, result)
                      local_push
                      result = context.dup
                      @context.in_def = true

    result
end

def _reduce_317(val, _values, result)
                      result = @builder.def_method(val[0], val[1],
                                  val[3], val[4], val[5])

                      local_pop
                      @context.in_def = val[2].in_def

    result
end

def _reduce_318(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_319(val, _values, result)
                      local_push
                      result = context.dup
                      @context.in_def = true

    result
end

def _reduce_320(val, _values, result)
                      result = @builder.def_singleton(val[0], val[1], val[2],
                                  val[4], val[6], val[7], val[8])

                      local_pop
                      @context.in_def = val[5].in_def

    result
end

def _reduce_321(val, _values, result)
                      result = @builder.keyword_cmd(:break, val[0])

    result
end

def _reduce_322(val, _values, result)
                      result = @builder.keyword_cmd(:next, val[0])

    result
end

def _reduce_323(val, _values, result)
                      result = @builder.keyword_cmd(:redo, val[0])

    result
end

def _reduce_324(val, _values, result)
                      result = @builder.keyword_cmd(:retry, val[0])

    result
end

# reduce 325 omitted

def _reduce_326(val, _values, result)
                      result = [ val[0], @context.dup ]

    result
end

def _reduce_327(val, _values, result)
                      result = [ val[0], @context.dup ]

    result
end

# reduce 328 omitted

# reduce 329 omitted

def _reduce_330(val, _values, result)
                      result = val[1]

    result
end

# reduce 331 omitted

# reduce 332 omitted

# reduce 333 omitted

def _reduce_334(val, _values, result)
                      else_t, else_ = val[4]
                      result = [ val[0],
                                 @builder.condition(val[0], val[1], val[2],
                                                    val[3], else_t,
                                                    else_,  nil),
                               ]

    result
end

# reduce 335 omitted

def _reduce_336(val, _values, result)
                      result = val

    result
end

# reduce 337 omitted

# reduce 338 omitted

def _reduce_339(val, _values, result)
                      @static_env.declare val[0][0]

                      result = @builder.arg(val[0])

    result
end

def _reduce_340(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

def _reduce_341(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_342(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 343 omitted

def _reduce_344(val, _values, result)
                      @static_env.declare val[3][0]

                      result = val[0].
                                  push(@builder.restarg(val[2], val[3]))

    result
end

def _reduce_345(val, _values, result)
                      @static_env.declare val[3][0]

                      result = val[0].
                                  push(@builder.restarg(val[2], val[3])).
                                  concat(val[5])

    result
end

def _reduce_346(val, _values, result)
                      result = val[0].
                                  push(@builder.restarg(val[2]))

    result
end

def _reduce_347(val, _values, result)
                      result = val[0].
                                  push(@builder.restarg(val[2])).
                                  concat(val[4])

    result
end

def _reduce_348(val, _values, result)
                      @static_env.declare val[1][0]

                      result = [ @builder.restarg(val[0], val[1]) ]

    result
end

def _reduce_349(val, _values, result)
                      @static_env.declare val[1][0]

                      result = [ @builder.restarg(val[0], val[1]),
                                 *val[3] ]

    result
end

def _reduce_350(val, _values, result)
                      result = [ @builder.restarg(val[0]) ]

    result
end

def _reduce_351(val, _values, result)
                      result = [ @builder.restarg(val[0]),
                                 *val[2] ]

    result
end

def _reduce_352(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_353(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[6]).
                                  concat(val[7])

    result
end

def _reduce_354(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_355(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_356(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

# reduce 357 omitted

def _reduce_358(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_359(val, _values, result)
                      if val[1].empty? && val[0].size == 1
                        result = [@builder.procarg0(val[0][0])]
                      else
                        result = val[0].concat(val[1])
                      end

    result
end

def _reduce_360(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_361(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_362(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_363(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_364(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_365(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_366(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_367(val, _values, result)
                      result = @builder.args(nil, [], nil)

    result
end

def _reduce_368(val, _values, result)
                      @lexer.state = :expr_value

    result
end

def _reduce_369(val, _values, result)
                      result = @builder.args(val[0], val[1], val[2])

    result
end

def _reduce_370(val, _values, result)
                      result = @builder.args(val[0], [], val[0])

    result
end

def _reduce_371(val, _values, result)
                      result = @builder.args(val[0], val[1].concat(val[2]), val[3])

    result
end

def _reduce_372(val, _values, result)
                      result = []

    result
end

def _reduce_373(val, _values, result)
                      result = val[1]

    result
end

def _reduce_374(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_375(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_376(val, _values, result)
                      @static_env.declare val[0][0]
                      result = @builder.shadowarg(val[0])

    result
end

# reduce 377 omitted

def _reduce_378(val, _values, result)
                      @static_env.extend_dynamic

    result
end

def _reduce_379(val, _values, result)
                      result = [ val[1], val[2] ]

                      @static_env.unextend

    result
end

def _reduce_380(val, _values, result)
                      result = @builder.args(val[0], val[1].concat(val[2]), val[3])

    result
end

def _reduce_381(val, _values, result)
                      result = @builder.args(nil, val[0], nil)

    result
end

def _reduce_382(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_383(val, _values, result)
                      result = [ val[0], val[2], val[3] ]
                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_384(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_385(val, _values, result)
                      result = [ val[0], val[2], val[3] ]
                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_386(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_387(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_388(val, _values, result)
                      begin_t, block_args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, block_args, body, end_t)

    result
end

def _reduce_389(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_390(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_391(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.call_method(nil, nil, val[0],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_392(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_393(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_394(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2])

    result
end

def _reduce_395(val, _values, result)
                      lparen_t, args, rparen_t = val[2]
                      result = @builder.call_method(val[0], val[1], nil,
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_396(val, _values, result)
                      lparen_t, args, rparen_t = val[2]
                      result = @builder.call_method(val[0], val[1], nil,
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_397(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.keyword_cmd(:super, val[0],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_398(val, _values, result)
                      result = @builder.keyword_cmd(:zsuper, val[0])

    result
end

def _reduce_399(val, _values, result)
                      result = @builder.index(val[0], val[1], val[2], val[3])

    result
end

def _reduce_400(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_401(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_402(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_403(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_404(val, _values, result)
                      result = [ @builder.when(val[0], val[1], val[2], val[3]),
                                 *val[4] ]

    result
end

def _reduce_405(val, _values, result)
                      result = [ val[0] ]

    result
end

# reduce 406 omitted

def _reduce_407(val, _values, result)
                      assoc_t, exc_var = val[2]

                      if val[1]
                        exc_list = @builder.array(nil, val[1], nil)
                      end

                      result = [ @builder.rescue_body(val[0],
                                      exc_list, assoc_t, exc_var,
                                      val[3], val[4]),
                                 *val[5] ]

    result
end

def _reduce_408(val, _values, result)
                      result = []

    result
end

def _reduce_409(val, _values, result)
                      result = [ val[0] ]

    result
end

# reduce 410 omitted

# reduce 411 omitted

def _reduce_412(val, _values, result)
                      result = [ val[0], val[1] ]

    result
end

# reduce 413 omitted

def _reduce_414(val, _values, result)
                      result = [ val[0], val[1] ]

    result
end

# reduce 415 omitted

# reduce 416 omitted

# reduce 417 omitted

# reduce 418 omitted

def _reduce_419(val, _values, result)
                      result = @builder.string_compose(nil, val[0], nil)

    result
end

def _reduce_420(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_421(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_422(val, _values, result)
                      result = @builder.string_compose(val[0], val[1], val[2])

    result
end

def _reduce_423(val, _values, result)
                      result = @builder.string(val[0])

    result
end

def _reduce_424(val, _values, result)
                      result = @builder.character(val[0])

    result
end

def _reduce_425(val, _values, result)
                      result = @builder.xstring_compose(val[0], val[1], val[2])

    result
end

def _reduce_426(val, _values, result)
                      opts   = @builder.regexp_options(val[3])
                      result = @builder.regexp_compose(val[0], val[1], val[2], opts)

    result
end

def _reduce_427(val, _values, result)
                      result = @builder.words_compose(val[0], val[1], val[2])

    result
end

def _reduce_428(val, _values, result)
                      result = []

    result
end

def _reduce_429(val, _values, result)
                      result = val[0] << @builder.word(val[1])

    result
end

def _reduce_430(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_431(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_432(val, _values, result)
                      result = @builder.words_compose(val[0], val[1], val[2])

    result
end

def _reduce_433(val, _values, result)
                      result = []

    result
end

def _reduce_434(val, _values, result)
                      result = val[0] << @builder.string_internal(val[1])

    result
end

def _reduce_435(val, _values, result)
                      result = []

    result
end

def _reduce_436(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_437(val, _values, result)
                      result = []

    result
end

def _reduce_438(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_439(val, _values, result)
                      result = []

    result
end

def _reduce_440(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_441(val, _values, result)
                      result = @builder.string_internal(val[0])

    result
end

def _reduce_442(val, _values, result)
                      result = val[1]

    result
end

def _reduce_443(val, _values, result)
                      @lexer.cond.push(false)
                      @lexer.cmdarg.push(false)

    result
end

def _reduce_444(val, _values, result)
                      @lexer.cond.lexpop
                      @lexer.cmdarg.lexpop

                      result = @builder.begin(val[0], val[2], val[3])

    result
end

def _reduce_445(val, _values, result)
                      result = @builder.gvar(val[0])

    result
end

def _reduce_446(val, _values, result)
                      result = @builder.ivar(val[0])

    result
end

def _reduce_447(val, _values, result)
                      result = @builder.cvar(val[0])

    result
end

# reduce 448 omitted

def _reduce_449(val, _values, result)
                      result = @builder.symbol(val[0])

    result
end

def _reduce_450(val, _values, result)
                      result = @builder.symbol_compose(val[0], val[1], val[2])

    result
end

def _reduce_451(val, _values, result)
                      result = @builder.integer(val[0])

    result
end

def _reduce_452(val, _values, result)
                      result = @builder.float(val[0])

    result
end

def _reduce_453(val, _values, result)
                      num = @builder.integer(val[1])
                      if @builder.respond_to? :negate
                        # AST builder interface compatibility
                        result = @builder.negate(val[0], num)
                      else
                        result = @builder.unary_num(val[0], num)
                      end

    result
end

def _reduce_454(val, _values, result)
                      num = @builder.float(val[1])
                      if @builder.respond_to? :negate
                        # AST builder interface compatibility
                        result = @builder.negate(val[0], num)
                      else
                        result = @builder.unary_num(val[0], num)
                      end

    result
end

def _reduce_455(val, _values, result)
                      result = @builder.ident(val[0])

    result
end

def _reduce_456(val, _values, result)
                      result = @builder.ivar(val[0])

    result
end

def _reduce_457(val, _values, result)
                      result = @builder.gvar(val[0])

    result
end

def _reduce_458(val, _values, result)
                      result = @builder.const(val[0])

    result
end

def _reduce_459(val, _values, result)
                      result = @builder.cvar(val[0])

    result
end

def _reduce_460(val, _values, result)
                      result = @builder.nil(val[0])

    result
end

def _reduce_461(val, _values, result)
                      result = @builder.self(val[0])

    result
end

def _reduce_462(val, _values, result)
                      result = @builder.true(val[0])

    result
end

def _reduce_463(val, _values, result)
                      result = @builder.false(val[0])

    result
end

def _reduce_464(val, _values, result)
                      result = @builder.__FILE__(val[0])

    result
end

def _reduce_465(val, _values, result)
                      result = @builder.__LINE__(val[0])

    result
end

def _reduce_466(val, _values, result)
                      result = @builder.__ENCODING__(val[0])

    result
end

def _reduce_467(val, _values, result)
                      result = @builder.accessible(val[0])

    result
end

def _reduce_468(val, _values, result)
                      result = @builder.accessible(val[0])

    result
end

def _reduce_469(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_470(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_471(val, _values, result)
                      result = @builder.nth_ref(val[0])

    result
end

def _reduce_472(val, _values, result)
                      result = @builder.back_ref(val[0])

    result
end

def _reduce_473(val, _values, result)
                      result = nil

    result
end

def _reduce_474(val, _values, result)
                      result = [ val[0], val[1] ]

    result
end

def _reduce_475(val, _values, result)
                      yyerrok
                      result = nil

    result
end

def _reduce_476(val, _values, result)
                      result = @builder.args(val[0], val[1], val[2])

                      @lexer.state = :expr_value

    result
end

def _reduce_477(val, _values, result)
                      result = @builder.args(nil, val[0], nil)

    result
end

def _reduce_478(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_479(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[6]).
                                  concat(val[7])

    result
end

def _reduce_480(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_481(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_482(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_483(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_484(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_485(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_486(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_487(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_488(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_489(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_490(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_491(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_492(val, _values, result)
                      result = []

    result
end

def _reduce_493(val, _values, result)
                      diagnostic :error, :argument_const, nil, val[0]

    result
end

def _reduce_494(val, _values, result)
                      diagnostic :error, :argument_ivar, nil, val[0]

    result
end

def _reduce_495(val, _values, result)
                      diagnostic :error, :argument_gvar, nil, val[0]

    result
end

def _reduce_496(val, _values, result)
                      diagnostic :error, :argument_cvar, nil, val[0]

    result
end

# reduce 497 omitted

# reduce 498 omitted

def _reduce_499(val, _values, result)
                      @static_env.declare val[0][0]

                      result = @builder.arg(val[0])

    result
end

def _reduce_500(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

def _reduce_501(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_502(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_503(val, _values, result)
                      @static_env.declare val[0][0]

                      result = @builder.optarg(val[0], val[1], val[2])

    result
end

def _reduce_504(val, _values, result)
                      @static_env.declare val[0][0]

                      result = @builder.optarg(val[0], val[1], val[2])

    result
end

def _reduce_505(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_506(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_507(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_508(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 509 omitted

# reduce 510 omitted

def _reduce_511(val, _values, result)
                      @static_env.declare val[1][0]

                      result = [ @builder.restarg(val[0], val[1]) ]

    result
end

def _reduce_512(val, _values, result)
                      result = [ @builder.restarg(val[0]) ]

    result
end

# reduce 513 omitted

# reduce 514 omitted

def _reduce_515(val, _values, result)
                      @static_env.declare val[1][0]

                      result = @builder.blockarg(val[0], val[1])

    result
end

def _reduce_516(val, _values, result)
                      result = [ val[1] ]

    result
end

def _reduce_517(val, _values, result)
                      result = []

    result
end

# reduce 518 omitted

def _reduce_519(val, _values, result)
                      result = val[1]

    result
end

def _reduce_520(val, _values, result)
                      result = []

    result
end

# reduce 521 omitted

def _reduce_522(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_523(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_524(val, _values, result)
                      result = @builder.pair(val[0], val[1], val[2])

    result
end

def _reduce_525(val, _values, result)
                      result = @builder.pair_keyword(val[0], val[1])

    result
end

# reduce 526 omitted

# reduce 527 omitted

# reduce 528 omitted

# reduce 529 omitted

# reduce 530 omitted

# reduce 531 omitted

# reduce 532 omitted

# reduce 533 omitted

# reduce 534 omitted

# reduce 535 omitted

# reduce 536 omitted

# reduce 537 omitted

# reduce 538 omitted

# reduce 539 omitted

# reduce 540 omitted

# reduce 541 omitted

def _reduce_542(val, _values, result)
                      result = val[1]

    result
end

def _reduce_543(val, _values, result)
                      result = val[1]

    result
end

# reduce 544 omitted

# reduce 545 omitted

# reduce 546 omitted

def _reduce_547(val, _values, result)
                    yyerrok

    result
end

# reduce 548 omitted

# reduce 549 omitted

# reduce 550 omitted

def _reduce_551(val, _values, result)
                    result = nil

    result
end

def _reduce_none(val, _values, result)
  val[0]
end

  end   # class Ruby19
end   # module Parser
