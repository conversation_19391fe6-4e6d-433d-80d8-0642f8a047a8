# -*- encoding:utf-8; warn-indent:false; frozen_string_literal: true  -*-
#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.7.3
# from Racc grammar file "ruby23.y".
#

require 'racc/parser.rb'


require_relative '../parser'

module Parser
  class Ruby23 < Parser::Base


  def version
    23
  end

  def default_encoding
    Encoding::UTF_8
  end

  def local_push
    @static_env.extend_static
    @lexer.cmdarg.push(false)
    @lexer.cond.push(false)
  end

  def local_pop
    @static_env.unextend
    @lexer.cmdarg.pop
    @lexer.cond.pop
  end
##### State transition tables begin ###

racc_action_table = [
  -479,   218,   219,   218,   219,   216,   -97,  -479,  -479,  -479,
  -286,   540,  -479,  -479,  -479,   214,  -479,   272,   221,   580,
   617,   617,   265,   540,  -479,   582,  -479,  -479,  -479,   272,
   552,   272,  -493,   111,   553,   -98,  -479,  -479,   540,  -479,
  -479,  -479,  -479,  -479,   540,   540,   -97,   -98,  -105,  -105,
  -286,  -104,   -96,   -83,  -104,   616,   616,   530,   272,   222,
   529,   123,  -105,   -69,   617,  -100,  -479,  -479,  -479,  -479,
  -479,  -479,  -479,  -479,  -479,  -479,  -479,  -479,  -479,  -479,
   215,   267,  -479,  -479,  -479,   579,  -479,  -479,   695,   -97,
  -479,   581,  -102,  -479,  -479,   222,  -479,   222,  -479,   616,
  -479,   208,  -479,  -479,   271,  -479,  -479,  -479,  -479,  -479,
  -100,  -479,  -482,  -479,  -102,   -88,   271,   871,   271,  -482,
  -482,  -482,   267,  -101,  -482,  -482,  -482,  -479,  -482,   115,
  -479,  -479,  -479,  -479,   114,  -479,  -482,  -479,  -482,  -482,
  -482,   115,  -479,  -479,   -89,   271,   114,   -91,  -482,  -482,
  -103,  -482,  -482,  -482,  -482,  -482,   115,   -99,   -96,   822,
   -95,   114,   115,   115,   -97,   -98,  -105,   114,   114,   -97,
   -98,  -105,  -104,   695,   -91,  -101,   -99,  -104,  -482,  -482,
  -482,  -482,  -482,  -482,  -482,  -482,  -482,  -482,  -482,  -482,
  -482,  -482,   115,   209,  -482,  -482,  -482,   114,  -482,  -482,
  -576,   -93,  -482,   210,   -93,  -482,  -482,   695,  -482,  -103,
  -482,   115,  -482,   -91,  -482,  -482,   114,  -482,  -482,  -482,
  -482,  -482,  -289,  -482,  -494,  -482,   870,  -577,  -100,  -289,
  -289,  -289,  -102,  -100,   617,  -289,  -289,  -102,  -289,  -482,
  -576,  -101,  -482,  -482,  -482,  -482,  -101,  -482,   217,  -482,
   218,   219,   447,   -91,  -482,  -482,   -91,   261,  -289,  -289,
   -90,  -289,  -289,  -289,  -289,  -289,   -91,   320,  -103,   616,
   -93,  -479,   -92,  -103,  -577,   -99,   530,   115,  -479,   532,
   -99,   -98,   114,   518,   -92,   -90,   218,   219,  -289,  -289,
  -289,  -289,  -289,  -289,  -289,  -289,  -289,  -289,  -289,  -289,
  -289,  -289,   321,   749,  -289,  -289,  -289,  -482,   600,  -105,
   -93,   115,  -289,   -93,  -482,  -289,   114,   785,   -94,   546,
  -289,   222,  -289,   -93,  -289,  -289,   -90,  -289,  -289,  -289,
  -289,  -289,   567,  -289,  -580,  -289,  -479,  -576,   -92,    81,
  -104,  -580,  -580,  -580,   222,   218,   219,  -580,  -580,  -289,
  -580,    82,  -289,  -289,   389,   -94,   402,  -289,   115,  -580,
  -100,    83,   446,   114,  -289,  -103,   -90,  -573,  -493,   -90,
  -580,  -580,  -482,  -580,  -580,  -580,  -580,  -580,   -92,   -90,
   115,   -92,   218,   219,   448,   114,   672,   750,   669,   668,
   667,   -92,   670,    93,    94,   449,   569,   568,   565,   221,
  -580,  -580,  -580,  -580,  -580,  -580,  -580,  -580,  -580,  -580,
  -580,  -580,  -580,  -580,   480,   -88,  -580,  -580,  -580,  -479,
   601,    93,    94,   567,  -580,   -97,  -479,  -580,   115,   840,
   489,  -573,  -580,   114,  -580,  -479,  -580,  -580,  -494,  -580,
  -580,  -580,  -580,  -580,  -102,  -580,  -580,  -580,   567,   672,
   567,   669,   668,   667,  -573,   670,  -489,   115,   491,  -574,
   493,  -580,   114,  -489,  -580,  -580,  -580,   -92,   889,  -580,
   501,    95,    96,  -580,  -580,  -580,  -580,  -101,  -580,  -580,
  -580,   -68,  -580,   222,  -479,   -89,   -99,   569,   568,   565,
  -488,  -580,  -580,  -580,  -580,   -98,   115,  -488,   504,    95,
    96,   114,  -580,  -580,   632,  -580,  -580,  -580,  -580,  -580,
   505,  -482,   569,   568,   569,   568,   567,   530,  -482,   567,
   532,  -489,   726,  -574,   728,   567,   530,  -482,   512,   532,
   276,   986,  -580,  -580,  -580,  -580,  -580,  -580,  -580,  -580,
  -580,  -580,  -580,  -580,  -580,  -580,  -574,   222,  -580,  -580,
  -580,   222,   751,  -580,   971,  -488,  -580,   567,   518,  -580,
  -580,   567,  -580,  -490,  -580,   267,  -580,   595,  -580,  -580,
  -490,  -580,  -580,  -580,  -580,  -580,  -482,  -580,  -580,  -580,
   569,   568,   570,   569,   568,   572,   515,  -487,   519,   569,
   568,   574,   242,  -580,  -487,   222,  -580,  -580,  -580,  -580,
   533,  -580,   534,  -580,  -289,   -95,   218,   219,  -580,  -101,
   493,  -289,  -289,  -289,   -91,  -104,  -289,  -289,  -289,   546,
  -289,   569,   568,   578,  -100,   569,   568,   583,  -490,   399,
  -289,  -289,  -289,   391,   401,   400,   817,   785,   596,   550,
  -289,  -289,   551,  -289,  -289,  -289,  -289,  -289,   559,   214,
   584,  -333,  -487,   214,  -484,  -485,   213,   587,  -333,  -486,
   444,  -484,  -485,   688,   687,   211,  -486,  -333,  -261,   445,
  -289,  -289,  -289,  -289,  -289,  -289,  -289,  -289,  -289,  -289,
  -289,  -289,  -289,  -289,   589,   -93,  -289,  -289,  -289,   242,
   752,  -289,  -491,   222,  -289,  -102,   214,  -289,  -289,  -491,
  -289,   593,  -289,   265,  -289,   735,  -289,  -289,  -491,  -289,
  -289,  -289,  -289,  -289,   215,  -289,  -333,  -289,   215,  -484,
  -485,   239,  -492,   594,  -486,   241,   240,   267,   242,  -492,
   604,  -289,   817,   785,  -289,  -289,  -289,  -289,  -492,  -289,
   607,  -289,  -414,   242,   242,   242,  -289,  -103,   242,  -414,
  -414,  -414,   -90,   677,  -414,  -414,  -414,  -491,  -414,   222,
   239,   215,   -99,   680,   241,   240,   222,  -414,  -414,  -414,
   720,   721,   222,   -83,   722,   109,   110,   636,  -414,  -414,
   222,  -414,  -414,  -414,  -414,  -414,   672,  -492,   669,   668,
   667,   214,   670,   523,   647,   652,   688,   687,   511,   653,
   672,   681,   669,   668,   667,   655,   670,   509,  -414,  -414,
  -414,  -414,  -414,  -414,  -414,  -414,  -414,  -414,  -414,  -414,
  -414,  -414,   691,   806,  -414,  -414,  -414,   546,   698,  -414,
   715,   267,  -414,   725,   729,  -414,  -414,   806,  -414,   730,
  -414,  -262,  -414,   736,  -414,  -414,   809,  -414,  -414,  -414,
  -414,  -414,  -296,  -414,  -414,  -414,   215,   480,   480,  -296,
  -296,  -296,   222,   754,  -296,  -296,  -296,  -279,  -296,  -414,
   261,   491,  -414,  -414,  -279,  -414,   493,  -414,  -296,  -296,
   778,   647,   222,  -279,  -414,   267,   267,   647,  -296,  -296,
   242,  -296,  -296,  -296,  -296,  -296,   785,   222,   794,   214,
   797,   214,   798,   800,   802,   804,   521,   672,   549,   669,
   668,   667,   812,   670,   813,   445,   814,   547,  -296,  -296,
  -296,  -296,  -296,  -296,  -296,  -296,  -296,  -296,  -296,  -296,
  -296,  -296,  -279,   785,  -296,  -296,  -296,   821,   222,  -296,
   222,   276,  -296,   830,   806,  -296,  -296,  -263,  -296,   839,
  -296,   842,  -296,   809,  -296,  -296,   797,  -296,  -296,  -296,
  -296,  -296,  -280,  -296,   215,  -296,   215,   845,   847,  -280,
  -280,  -280,   849,   851,  -280,  -280,  -280,   214,  -280,  -296,
   222,   853,  -296,  -296,   555,  -296,   854,  -296,  -280,  -280,
  -280,   857,   859,   557,  -296,   860,   647,   862,  -280,  -280,
  -261,  -280,  -280,  -280,  -280,  -280,   866,  -290,   868,  -290,
   222,   214,   887,   222,  -290,   891,  -290,   893,   922,   899,
   902,   222,   906,  -290,  -264,  -290,   916,   557,  -280,  -280,
  -280,  -280,  -280,  -280,  -280,  -280,  -280,  -280,  -280,  -280,
  -280,  -280,   215,   214,  -280,  -280,  -280,   923,   924,  -280,
   922,   935,  -280,   797,   937,  -280,  -280,   939,  -280,   557,
  -280,   941,  -280,   943,  -280,  -280,   943,  -280,  -280,  -280,
  -280,  -280,  -290,  -280,  -290,  -280,   215,   222,   677,   672,
   214,   669,   668,   667,   949,   670,   952,   977,   680,  -280,
   953,  -580,  -280,  -280,  -280,  -280,   975,  -280,  -244,  -280,
   958,   715,   797,   961,  -280,  -244,  -244,  -244,   215,   963,
  -244,  -244,  -244,   965,  -244,   967,   806,   242,   967,   978,
   988,   688,   687,  -244,  -244,  -244,   681,   919,  -577,   669,
   668,   667,  -576,   670,  -244,  -244,   652,  -244,  -244,  -244,
  -244,  -244,  1003,  -580,  1004,   215,  1005,   943,   943,   239,
  -580,   943,  1010,   241,   240,  -576,   237,   238,   988,  -580,
  1013,  1014,  1015,   967,  -244,  -244,  -244,  -244,  -244,  -244,
  -244,  -244,  -244,  -244,  -244,  -244,  -244,  -244,  -580,   967,
  -244,  -244,  -244,  -289,   967,  -244,   222,   267,  -244,   988,
  -289,  -244,  -244,   943,  -244,  -577,  -244,   988,  -244,  -289,
  -244,  -244,   967,  -244,  -244,  -244,  -244,  -244,  -580,  -244,
  -244,  -244,   nil,   672,   nil,   669,   668,   667,   nil,   670,
  -289,   nil,   nil,   nil,   nil,  -244,   nil,  -289,  -244,  -244,
  -581,  -244,  -577,  -244,   nil,   nil,  -289,  -581,  -581,  -581,
  -244,   nil,  -581,  -581,  -581,   nil,  -581,   242,  -289,   nil,
   806,   nil,   nil,   nil,   nil,  -581,  -581,  -581,  -581,   948,
   nil,   nil,   nil,   256,   257,   nil,  -581,  -581,   nil,  -581,
  -581,  -581,  -581,  -581,   672,   nil,   669,   668,   667,   239,
   670,   245,   nil,   241,   240,  -289,   237,   238,   nil,   nil,
   243,   nil,   244,   nil,   nil,   nil,  -581,  -581,  -581,  -581,
  -581,  -581,  -581,  -581,  -581,  -581,  -581,  -581,  -581,  -581,
   nil,   806,  -581,  -581,  -581,   242,   nil,  -581,   nil,   nil,
  -581,   nil,   nil,  -581,  -581,   nil,  -581,   nil,  -581,   nil,
  -581,   nil,  -581,  -581,   nil,  -581,  -581,  -581,  -581,  -581,
   nil,  -581,  -581,  -581,   nil,   nil,   nil,   239,   nil,   nil,
   nil,   241,   240,   nil,   237,   238,   nil,  -581,   nil,   nil,
  -581,  -581,  -581,  -581,   nil,  -581,  -582,  -581,   nil,   nil,
   nil,   nil,  -581,  -582,  -582,  -582,   nil,   nil,  -582,  -582,
  -582,   242,  -582,   919,   nil,   669,   668,   667,   nil,   670,
   nil,  -582,  -582,  -582,  -582,   nil,   nil,   256,   257,   nil,
   nil,   nil,  -582,  -582,   nil,  -582,  -582,  -582,  -582,  -582,
   nil,   nil,   nil,   239,   nil,   245,   nil,   241,   240,   nil,
   237,   238,   nil,   nil,   243,   nil,   244,   118,   119,   120,
   121,   122,  -582,  -582,  -582,  -582,  -582,  -582,  -582,  -582,
  -582,  -582,  -582,  -582,  -582,  -582,   nil,   nil,  -582,  -582,
  -582,   nil,   nil,  -582,   nil,   nil,  -582,   nil,   nil,  -582,
  -582,   nil,  -582,   nil,  -582,   nil,  -582,   nil,  -582,  -582,
   nil,  -582,  -582,  -582,  -582,  -582,   nil,  -582,  -582,  -582,
   118,   119,   120,   121,   122,   nil,   nil,   nil,   672,   nil,
   669,   668,   667,  -582,   670,   nil,  -582,  -582,  -582,  -582,
   nil,  -582,  -244,  -582,   nil,   nil,   nil,   nil,  -582,  -244,
  -244,  -244,   nil,   nil,  -244,  -244,  -244,   672,  -244,   669,
   668,   667,   677,   670,   nil,   806,   nil,  -244,  -244,   nil,
   nil,   nil,   680,   nil,   nil,   nil,   242,   nil,  -244,  -244,
   nil,  -244,  -244,  -244,  -244,  -244,   118,   119,   120,   121,
   122,   nil,   256,   257,   675,   nil,   672,   nil,   669,   668,
   667,   677,   670,   685,   684,   688,   687,   nil,   239,   nil,
   681,   680,   241,   240,   nil,   237,   238,   nil,   nil,  -244,
   nil,   nil,   nil,   nil,   242,   nil,  -244,   nil,   nil,   nil,
   nil,   267,  -244,   675,   658,   nil,   222,   nil,   nil,   nil,
   256,   257,   685,   684,   688,   687,   nil,   nil,   nil,   681,
   nil,   nil,   nil,   nil,  -244,  -244,   239,   nil,   245,   nil,
   241,   240,   nil,   237,   238,   nil,   nil,   nil,   nil,  -244,
   nil,   nil,  -244,   nil,   nil,   nil,   nil,  -244,     5,    74,
    75,    71,     9,    57,  -244,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   605,     8,    45,     7,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,   246,   251,   252,   253,   248,   250,
   258,   259,   254,   255,   nil,   235,   236,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   260,   nil,
  -238,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   590,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,   246,   251,   252,   253,   248,   250,
   258,   259,   254,   255,   nil,   235,   236,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,   298,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   260,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,     5,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   605,     8,    45,     7,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,   246,   251,   252,   253,   248,   250,
   258,   259,   254,   255,   nil,   235,   236,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   260,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,   246,   251,   252,   253,   248,   250,
   258,   259,   254,   255,   nil,   235,   236,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   260,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,   246,   251,   252,   253,   248,   250,
   258,   259,   254,   255,   nil,   235,   236,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   260,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,   246,   251,   252,   253,   248,   250,
   258,   259,   254,   255,   nil,   235,   236,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,   298,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   260,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,   246,   251,   252,   253,   248,   250,
   258,   259,   254,   255,   nil,   235,   236,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,   298,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   222,   260,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,   246,   251,   252,   253,   248,   250,
   258,   259,   254,   255,   nil,   235,   236,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   260,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,     5,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,     7,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,   246,   251,   252,   253,   248,   250,
   258,   259,   254,   255,   nil,   235,   236,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   260,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,   246,   251,   252,   253,   248,   250,
   258,   259,   254,   255,   nil,   235,   236,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   260,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,   246,   251,   252,   253,   248,   250,
   258,   259,   254,   255,   nil,   235,   236,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   260,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,   246,   251,   252,   253,   248,   250,
   258,   259,   254,   255,   nil,   235,   236,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   260,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,   246,   251,   252,   253,   248,   250,
   258,   259,   254,   255,   nil,   235,   236,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   260,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,   246,   251,   252,   253,   248,   250,
   258,   259,   254,   255,   nil,   235,   236,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   260,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,   246,   251,   252,   253,   248,   250,
   258,   259,   254,   255,   nil,   235,   236,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   260,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,   246,   251,   252,   253,   248,   250,
   258,   259,   254,   255,   nil,   235,   236,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   260,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,   246,   251,   252,   253,   248,   250,
   258,   259,   254,   255,   nil,   235,   236,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   260,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,   246,   251,   252,   253,   248,   250,
   258,   259,   254,   255,   nil,  -601,  -601,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   672,   nil,
   669,   668,   667,    62,   670,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   806,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,   246,   251,   252,   253,   248,   250,
   258,   259,   254,   255,   nil,  -601,  -601,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,  -601,  -601,  -601,  -601,   248,   250,
   nil,   nil,  -601,  -601,   nil,   nil,   nil,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   nil,   nil,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   nil,   nil,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,  -601,  -601,  -601,  -601,   248,   250,
   nil,   nil,  -601,  -601,   nil,   nil,   nil,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,  -601,  -601,  -601,  -601,   248,   250,
   nil,   nil,  -601,  -601,   nil,   nil,   nil,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,  -601,  -601,  -601,  -601,   248,   250,
   nil,   nil,  -601,  -601,   nil,   nil,   nil,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,  -601,  -601,  -601,  -601,   248,   250,
   nil,   nil,  -601,  -601,   nil,   nil,   nil,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,  -601,  -601,  -601,  -601,   248,   250,
   nil,   nil,  -601,  -601,   nil,   nil,   nil,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,   246,   251,   252,   253,   248,   250,
   nil,   nil,   254,   255,   nil,   nil,   nil,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,   246,   251,   252,   253,   248,   250,
   258,   nil,   254,   255,   nil,   nil,   nil,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   249,   247,   243,    20,   244,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   242,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   256,
   257,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   239,   nil,   245,    44,   241,
   240,   nil,   237,   238,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   294,    74,
    75,    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,     8,    45,   296,    10,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,    74,    75,
    71,     9,    57,   nil,   nil,   nil,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,    30,    31,    72,    73,
   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,
   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,
     8,    45,     7,    10,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,    33,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,    35,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   103,   102,   104,   105,   nil,   nil,   234,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   288,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   285,   nil,   283,   nil,    44,
   nil,   nil,   289,   nil,   nil,   nil,   nil,   nil,   232,   nil,
   nil,   nil,   nil,    91,   286,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,
    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,
    27,   103,   102,   104,   105,   nil,   nil,   234,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   288,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   285,   nil,   283,   nil,
    44,   nil,   nil,   289,   nil,   nil,   nil,   nil,   nil,   232,
   nil,   nil,   nil,   nil,    91,   286,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,
    28,    27,   103,   102,   104,   105,   nil,   nil,   234,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   288,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   285,   nil,   283,
   nil,    44,   nil,   nil,   289,   nil,   nil,   nil,   nil,   nil,
   232,   nil,   nil,   nil,   nil,    91,   286,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   313,   314,    72,    73,   nil,   nil,   nil,   nil,   nil,
   309,   310,   316,   103,   102,   104,   105,   nil,   nil,   234,
   nil,   nil,   nil,   nil,   nil,   nil,   311,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,   nil,   nil,   317,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   307,   nil,   nil,   303,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   302,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   313,   314,    72,    73,   nil,   nil,   nil,   nil,
   nil,   309,   310,   316,   103,   102,   104,   105,   nil,   nil,
   234,   nil,   nil,   nil,   nil,   nil,   nil,   311,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,   nil,   nil,   317,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   307,   nil,   nil,
   233,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   672,   nil,   669,   668,   667,   677,   670,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   680,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,   319,   nil,   675,    62,   nil,
    83,    95,    96,    74,    75,    71,   nil,    57,   688,   687,
   nil,    63,    64,   681,   nil,   nil,    67,   nil,    65,    66,
    68,   313,   314,    72,    73,   nil,   nil,   nil,   nil,   nil,
   309,   310,   316,   103,   102,   104,   105,   nil,   nil,   234,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   232,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   313,   314,    72,    73,   nil,   nil,   nil,   nil,
   nil,   309,   310,   316,   103,   102,   104,   105,   nil,   nil,
   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,
   233,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   313,   314,    72,    73,   nil,   nil,   nil,
   nil,   nil,   309,   310,   316,   103,   102,   104,   105,   nil,
   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   233,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   313,   314,    72,    73,   nil,   nil,
   nil,   nil,   nil,   309,   310,   316,   103,   102,   104,   105,
   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   288,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,
   nil,   nil,   233,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   285,   nil,   nil,   nil,    44,   nil,   nil,   289,   nil,
   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,
   286,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   313,   314,    72,    73,   nil,
   nil,   nil,   nil,   nil,   309,   310,   316,   103,   102,   104,
   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   288,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   227,   nil,   nil,   233,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   289,
   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,
    91,   286,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,    30,    31,    72,    73,
   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,
   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   227,   nil,   nil,   233,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   103,   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   115,   nil,
   nil,   nil,   nil,   114,    62,   nil,    83,    95,    96,    74,
    75,    71,   nil,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   313,   314,    72,
    73,   nil,   nil,   nil,   nil,   nil,   309,   310,   316,   103,
   102,   104,   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,
   nil,   nil,   311,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
   nil,   nil,   317,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   351,   nil,   nil,    33,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,    35,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   313,   314,
    72,    73,   nil,   nil,   nil,   nil,   nil,   309,   310,   316,
   103,   102,   104,   105,   nil,   nil,   234,   nil,   nil,   nil,
   nil,   nil,   nil,   311,   nil,   nil,   107,   106,   108,   356,
    56,    99,    98,   357,   nil,   101,   109,   110,   nil,    93,
    94,   nil,   nil,   317,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   363,   nil,   nil,   358,   nil,   nil,   233,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   313,
   314,    72,    73,   nil,   nil,   nil,   nil,   nil,   309,   310,
   316,   103,   102,   104,   105,   nil,   nil,   234,   nil,   nil,
   nil,   nil,   nil,   nil,   311,   nil,   nil,   107,   106,   108,
   356,    56,    99,    98,   357,   nil,   101,   109,   110,   nil,
    93,    94,   nil,   nil,   317,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   358,   nil,   nil,   233,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   672,   nil,   669,
   668,   667,   677,   670,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   680,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,   nil,   nil,   675,    62,   nil,    83,    95,    96,
    74,    75,    71,     9,    57,   688,   687,   nil,    63,    64,
   681,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   103,   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,     8,    45,     7,    10,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,    33,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,    35,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,   nil,   nil,   391,    62,   nil,    83,    95,    96,    74,
    75,    71,   nil,    57,   nil,   nil,   nil,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   103,   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,
    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,
    27,   103,   102,   104,   105,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,
    28,    27,   103,   102,   104,   105,   nil,   nil,    19,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    20,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,   nil,   nil,   nil,    62,   nil,    83,    95,
    96,    74,    75,    71,     9,    57,   nil,   nil,   nil,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,
    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,
    27,   103,   102,   104,   105,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,     8,    45,   nil,    10,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    40,   nil,   nil,    33,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,    35,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,
    28,    27,   103,   102,   104,   105,   nil,   nil,   234,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   407,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   232,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,
    29,    28,    27,   103,   102,   104,   105,   nil,   nil,   234,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   232,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,
   nil,    29,    28,    27,   103,   102,   104,   105,   nil,   nil,
   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   288,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,
   233,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   285,
   nil,   283,   nil,    44,   nil,   nil,   289,   nil,   nil,   nil,
   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,   286,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,
   nil,   nil,    29,    28,    27,   103,   102,   104,   105,   nil,
   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   233,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,
   nil,   nil,   233,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   407,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,    30,    31,    72,    73,   nil,
   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,
   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   227,   nil,   nil,   233,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,    30,    31,    72,    73,
   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,
   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   227,   nil,   nil,   233,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   103,   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   222,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   313,
   314,    72,    73,   nil,   nil,   nil,   nil,   nil,   309,   310,
   316,   103,   102,   104,   105,   nil,   nil,   234,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   232,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   313,   314,    72,    73,   nil,   nil,   nil,   nil,   nil,   309,
   310,   316,   103,   102,   104,   105,   nil,   nil,   234,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   232,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   313,   314,    72,    73,   nil,   nil,   nil,   nil,   nil,
   309,   310,   316,   103,   102,   104,   105,   nil,   nil,   234,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   232,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   313,   314,    72,    73,   nil,   nil,   nil,   nil,
   nil,   309,   310,   316,   103,   102,   104,   105,   nil,   nil,
   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,
   233,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   313,   314,    72,    73,   nil,   nil,   nil,
   nil,   nil,   309,   310,   316,   103,   102,   104,   105,   nil,
   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   233,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   313,   314,    72,    73,   nil,   nil,
   nil,   nil,   nil,   309,   310,   316,   103,   102,   104,   105,
   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,
   nil,   nil,   233,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   313,   314,    72,    73,   nil,
   nil,   nil,   nil,   nil,   309,   310,   316,   103,   102,   104,
   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   227,   nil,   nil,   233,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   313,   314,    72,    73,
   nil,   nil,   nil,   nil,   nil,   309,   310,   316,   103,   102,
   104,   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   227,   nil,   nil,   233,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   313,   314,    72,
    73,   nil,   nil,   nil,   nil,   nil,   309,   310,   316,   103,
   102,   104,   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   313,   314,
    72,    73,   nil,   nil,   nil,   nil,   nil,   309,   310,   316,
   103,   102,   104,   105,   nil,   nil,   234,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   232,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   313,
   314,    72,    73,   nil,   nil,   nil,   nil,   nil,   309,   310,
   316,   103,   102,   104,   105,   nil,   nil,   234,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   232,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   313,   314,    72,    73,   nil,   nil,   nil,   nil,   nil,   309,
   310,   316,   103,   102,   104,   105,   nil,   nil,   234,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   232,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   313,   314,    72,    73,   nil,   nil,   nil,   nil,   nil,
   309,   310,   316,   103,   102,   104,   105,   nil,   nil,   234,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   232,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   313,   314,    72,    73,   nil,   nil,   nil,   nil,
   nil,   309,   310,   316,   103,   102,   104,   105,   nil,   nil,
   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,
   233,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   313,   314,    72,    73,   nil,   nil,   nil,
   nil,   nil,   309,   310,   316,   103,   102,   104,   105,   nil,
   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   233,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   313,   314,    72,    73,   nil,   nil,
   nil,   nil,   nil,   309,   310,   316,   103,   102,   104,   105,
   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,
   nil,   nil,   233,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   313,   314,    72,    73,   nil,
   nil,   nil,   nil,   nil,   309,   310,   316,   103,   102,   104,
   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   227,   nil,   nil,   233,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   313,   314,    72,    73,
   nil,   nil,   nil,   nil,   nil,   309,   310,   316,   103,   102,
   104,   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   227,   nil,   nil,   233,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   313,   314,    72,
    73,   nil,   nil,   nil,   nil,   nil,   309,   310,   316,   103,
   102,   104,   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   313,   314,
    72,    73,   nil,   nil,   nil,   nil,   nil,   309,   310,   316,
   103,   102,   104,   105,   nil,   nil,   234,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   232,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   313,
   314,    72,    73,   nil,   nil,   nil,   nil,   nil,   309,   310,
   316,   103,   102,   104,   105,   nil,   nil,   234,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   232,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   313,   314,    72,    73,   nil,   nil,   nil,   nil,   nil,   309,
   310,   316,   103,   102,   104,   105,   nil,   nil,   234,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   232,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   313,   314,    72,    73,   nil,   nil,   nil,   nil,   nil,
   309,   310,   316,   103,   102,   104,   105,   nil,   nil,   234,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   232,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   313,   314,    72,    73,   nil,   nil,   nil,   nil,
   nil,   309,   310,   316,   103,   102,   104,   105,   nil,   nil,
   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,
   233,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   313,   314,    72,    73,   nil,   nil,   nil,
   nil,   nil,   309,   310,   316,   103,   102,   104,   105,   nil,
   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   233,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   313,   314,    72,    73,   nil,   nil,
   nil,   nil,   nil,   309,   310,   316,   103,   102,   104,   105,
   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,
   nil,   nil,   233,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   313,   314,    72,    73,   nil,
   nil,   nil,   nil,   nil,   309,   310,   316,   103,   102,   104,
   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   227,   nil,   nil,   233,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,    30,    31,    72,    73,
   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,
   104,   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   288,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   227,   nil,   nil,   233,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   285,   nil,   283,   nil,    44,   nil,   nil,
   289,   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,
   nil,    91,   286,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   288,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   285,   nil,   283,   nil,    44,   nil,
   nil,   289,   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,
   nil,   nil,    91,   286,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   103,   102,   104,   105,   nil,   nil,   234,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   288,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   285,   nil,   283,   nil,    44,
   nil,   nil,   289,   nil,   nil,   nil,   nil,   nil,   232,   nil,
   nil,   nil,   nil,    91,   286,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   222,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   313,
   314,    72,    73,   nil,   nil,   nil,   nil,   nil,   309,   310,
   316,   103,   102,   104,   105,   nil,   nil,   234,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   232,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   313,   314,    72,    73,   nil,   nil,   nil,   nil,   nil,   309,
   310,   316,   103,   102,   104,   105,   nil,   nil,   234,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   232,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   313,   314,    72,    73,   nil,   nil,   nil,   nil,   nil,
   309,   310,   316,   103,   102,   104,   105,   nil,   nil,   234,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   232,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   313,   314,    72,    73,   nil,   nil,   nil,   nil,
   nil,   309,   310,   316,   103,   102,   104,   105,   nil,   nil,
   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,
   233,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,   nil,   nil,   nil,    62,   nil,
    83,    95,    96,    74,    75,    71,     9,    57,   nil,   nil,
   nil,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,
    29,    28,    27,   103,   102,   104,   105,   nil,   nil,    19,
   nil,   nil,   nil,   nil,   nil,     8,    45,   nil,    10,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,    33,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,    35,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    20,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   313,   314,    72,    73,   nil,   nil,   nil,   nil,
   nil,   309,   310,   316,   103,   102,   104,   105,   nil,   nil,
   234,   nil,   nil,   nil,   nil,   nil,   nil,   311,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,   nil,   nil,   317,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   307,   nil,   nil,
   233,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   672,   nil,   669,   668,   667,   677,   670,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   680,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,   507,   nil,   675,    62,   nil,
    83,    95,    96,    74,    75,    71,   nil,    57,   688,   687,
   nil,    63,    64,   681,   nil,   nil,    67,   nil,    65,    66,
    68,   313,   314,    72,    73,   nil,   nil,   nil,   nil,   nil,
   309,   310,   316,   103,   102,   104,   105,   nil,   nil,   234,
   nil,   nil,   nil,   nil,   nil,   nil,   311,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,   nil,   nil,   317,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   307,   nil,   nil,   303,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   313,   314,    72,    73,   nil,   nil,   nil,   nil,
   nil,   309,   310,   316,   103,   102,   104,   105,   nil,   nil,
   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,
   233,   523,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,
   nil,   nil,    29,    28,    27,   103,   102,   104,   105,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   233,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,
   nil,   nil,   233,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,    30,    31,    72,    73,   nil,
   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,
   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   227,   nil,   nil,   233,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,    30,    31,    72,    73,
   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,   102,
   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   227,   nil,   nil,   233,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   313,   314,
    72,    73,   nil,   nil,   nil,   nil,   nil,   309,   310,   316,
   103,   102,   104,   105,   nil,   nil,   234,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   232,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,
    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,
    27,   103,   102,   104,   105,   nil,   nil,   234,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   288,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   285,   nil,   283,   nil,
    44,   nil,   nil,   289,   nil,   nil,   nil,   nil,   nil,   232,
   nil,   nil,   nil,   nil,    91,   286,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   313,   314,    72,    73,   nil,   nil,   nil,   nil,   nil,   309,
   310,   316,   103,   102,   104,   105,   nil,   nil,   234,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   232,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   313,   314,    72,    73,   nil,   nil,   nil,   nil,   nil,
   309,   310,   316,   103,   102,   104,   105,   nil,   nil,   234,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   232,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   313,   314,    72,    73,   nil,   nil,   nil,   nil,
   nil,   309,   310,   316,   103,   102,   104,   105,   nil,   nil,
   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,
   233,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   313,   314,    72,    73,   nil,   nil,   nil,
   nil,   nil,   309,   310,   316,   103,   102,   104,   105,   nil,
   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   288,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   233,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   629,   nil,   283,   nil,    44,   nil,   nil,   289,   nil,   nil,
   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,   286,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   313,   314,    72,    73,   nil,   nil,
   nil,   nil,   nil,   309,   310,   316,   103,   102,   104,   105,
   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   288,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,
   nil,   nil,   233,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   283,   nil,    44,   nil,   nil,   289,   nil,
   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,
   286,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   313,   314,    72,    73,   nil,
   nil,   nil,   nil,   nil,   309,   310,   316,   103,   102,   104,
   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   227,   nil,   nil,   233,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,   nil,   nil,
   nil,    62,   nil,    83,    95,    96,    74,    75,    71,     9,
    57,   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    45,
   296,    10,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,    33,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,    35,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,   nil,   nil,   391,
    62,   nil,    83,    95,    96,    74,    75,    71,   nil,    57,
   nil,   nil,   nil,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   313,   314,    72,    73,   nil,   nil,   nil,
   nil,   nil,   309,   310,   316,   103,   102,   104,   105,   nil,
   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,   311,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,   nil,   nil,   317,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   307,   nil,
   nil,   303,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,    30,    31,    72,    73,   nil,   nil,
   nil,   nil,   nil,    29,    28,    27,   103,   102,   104,   105,
   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   288,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,
   nil,   nil,   233,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   285,   nil,   283,   nil,    44,   nil,   nil,   289,   nil,
   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,
   286,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   313,   314,    72,    73,   nil,
   nil,   nil,   nil,   nil,   309,   310,   316,   103,   102,   104,
   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,
   311,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,   nil,   nil,
   317,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   307,   nil,   nil,   303,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   313,   314,    72,    73,
   nil,   nil,   nil,   nil,   nil,   309,   310,   316,   103,   102,
   104,   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   227,   nil,   nil,   233,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   313,   314,    72,
    73,   nil,   nil,   nil,   nil,   nil,   309,   310,   316,   103,
   102,   104,   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   103,   102,   104,   105,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   313,
   314,    72,    73,   nil,   nil,   nil,   nil,   nil,   309,   310,
   316,   103,   102,   104,   105,   nil,   nil,   234,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   288,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   629,   nil,   nil,   nil,
    44,   nil,   nil,   289,   nil,   nil,   nil,   nil,   nil,   232,
   nil,   nil,   nil,   nil,    91,   286,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   313,   314,    72,    73,   nil,   nil,   nil,   nil,   nil,   309,
   310,   316,   103,   102,   104,   105,   nil,   nil,   234,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   288,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   289,   nil,   nil,   nil,   nil,   nil,
   232,   nil,   nil,   nil,   nil,    91,   286,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,   313,   314,    72,    73,   nil,   nil,   nil,   nil,   nil,
   309,   310,   316,   103,   102,   104,   105,   nil,   nil,   234,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   285,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   232,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,
   nil,    29,    28,    27,   103,   102,   104,   105,   nil,   nil,
   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   288,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,
   233,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   285,
   nil,   283,   nil,    44,   nil,   nil,   289,   nil,   nil,   nil,
   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,   286,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,
   nil,   nil,    29,    28,    27,   103,   102,   104,   105,   nil,
   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   288,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   233,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   285,   nil,   283,   nil,    44,   nil,   nil,   289,   nil,   nil,
   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,   286,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   313,   314,    72,    73,   nil,   nil,
   nil,   nil,   nil,   309,   310,   316,   103,   102,   104,   105,
   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,
   nil,   nil,   233,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   733,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   313,   314,    72,    73,   nil,
   nil,   nil,   nil,   nil,   309,   310,   316,   103,   102,   104,
   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   227,   nil,   nil,   233,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   313,   314,    72,    73,
   nil,   nil,   nil,   nil,   nil,   309,   310,   316,   103,   102,
   104,   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   288,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   227,   nil,   nil,   233,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   629,   nil,   283,   nil,    44,   nil,   nil,
   289,   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,
   nil,    91,   286,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   313,   314,    72,
    73,   nil,   nil,   nil,   nil,   nil,   309,   310,   316,   103,
   102,   104,   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   288,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   283,   nil,    44,   nil,
   nil,   289,   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,
   nil,   nil,    91,   286,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,
    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,
   103,   102,   104,   105,   nil,   nil,   234,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   232,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,    30,
    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,    28,
    27,   103,   102,   104,   105,   nil,   nil,   234,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   232,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,
    28,    27,   103,   102,   104,   105,   nil,   nil,   234,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   232,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,
    29,    28,    27,   103,   102,   104,   105,   nil,   nil,   234,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   232,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,
   nil,    29,    28,    27,   103,   102,   104,   105,   nil,   nil,
   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,
   233,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   313,   314,    72,    73,   nil,   nil,   nil,
   nil,   nil,   309,   310,   316,   103,   102,   104,   105,   nil,
   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   233,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   313,   314,    72,    73,   nil,   nil,
   nil,   nil,   nil,   309,   310,   316,   103,   102,   104,   105,
   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,
   nil,   nil,   233,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   313,   314,    72,    73,   nil,
   nil,   nil,   nil,   nil,   309,   310,   316,   103,   102,   104,
   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   227,   nil,   nil,   233,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   313,   314,    72,    73,
   nil,   nil,   nil,   nil,   nil,   309,   310,   316,   103,   102,
   104,   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,
   nil,   311,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,   nil,
   nil,   317,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   307,   nil,   nil,   303,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   313,   314,    72,
    73,   nil,   nil,   nil,   nil,   nil,   309,   310,   316,   103,
   102,   104,   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,
   nil,   nil,   311,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
   nil,   nil,   317,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   307,   nil,   nil,   303,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   313,   314,
    72,    73,   nil,   nil,   nil,   nil,   nil,   309,   310,   316,
   103,   102,   104,   105,   nil,   nil,   234,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   407,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   232,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   313,
   314,    72,    73,   nil,   nil,   nil,   nil,   nil,   309,   310,
   316,   103,   102,   104,   105,   nil,   nil,   234,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   232,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,    29,
    28,    27,   103,   102,   104,   105,   nil,   nil,    19,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    20,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,
    29,    28,    27,   103,   102,   104,   105,   nil,   nil,    19,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    20,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   313,   314,    72,    73,   nil,   nil,   nil,   nil,
   nil,   309,   310,   316,   103,   102,   104,   105,   nil,   nil,
   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,
   233,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,    30,    31,    72,    73,   nil,   nil,   nil,
   nil,   nil,    29,    28,    27,   103,   102,   104,   105,   nil,
   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   nil,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   233,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,    81,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   313,   314,    72,    73,   nil,   nil,
   nil,   nil,   nil,   309,   310,   316,   103,   102,   104,   105,
   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,
   nil,   nil,   233,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   313,   314,    72,    73,   nil,
   nil,   nil,   nil,   nil,   309,   310,   316,   103,   102,   104,
   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   227,   nil,   nil,   233,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   313,   314,    72,    73,
   nil,   nil,   nil,   nil,   nil,   309,   310,   316,   103,   102,
   104,   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   227,   nil,   nil,   233,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,   313,   314,    72,
    73,   nil,   nil,   nil,   nil,   nil,   309,   310,   316,   103,
   102,   104,   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   nil,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,
   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
    74,    75,    71,    62,    57,    83,    95,    96,    63,    64,
   nil,   nil,   nil,    67,   nil,    65,    66,    68,   313,   314,
    72,    73,   nil,   nil,   nil,   nil,   nil,   309,   310,   316,
   103,   102,   104,   105,   nil,   nil,   234,   nil,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,
    56,    99,    98,   100,   nil,   101,   109,   110,   nil,    93,
    94,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,    58,
    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    44,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   232,   nil,
   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,    88,
    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,
   nil,    74,    75,    71,    62,    57,    83,    95,    96,    63,
    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,   313,
   314,    72,    73,   nil,   nil,   nil,   nil,   nil,   309,   310,
   316,   103,   102,   104,   105,   nil,   nil,   234,   nil,   nil,
   nil,   nil,   nil,   nil,   311,   nil,   nil,   107,   106,   108,
    97,    56,    99,    98,   100,   nil,   101,   109,   110,   nil,
    93,    94,   nil,   nil,   317,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   856,   nil,   nil,   233,   nil,   nil,
    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,    86,
    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,   nil,
   nil,   nil,    74,    75,    71,    62,    57,    83,    95,    96,
    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,    68,
   313,   314,    72,    73,   nil,   nil,   nil,   nil,   nil,   309,
   310,   316,   103,   102,   104,   105,   nil,   nil,   234,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,   106,
   108,    97,    56,    99,    98,   100,   nil,   101,   109,   110,
   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,   nil,
   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   232,   nil,   nil,   nil,   nil,    91,    81,    84,    85,   nil,
    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,    90,
   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,    95,
    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,    66,
    68,    30,    31,    72,    73,   nil,   nil,   nil,   nil,   nil,
    29,    28,    27,   103,   102,   104,   105,   nil,   nil,    19,
   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   107,
   106,   108,    97,    56,    99,    98,   100,   nil,   101,   109,
   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,   233,
   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    20,   nil,   nil,   nil,   nil,    91,    81,    84,    85,
   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,    82,
    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,    83,
    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,    65,
    66,    68,   313,   314,    72,    73,   nil,   nil,   nil,   nil,
   nil,   309,   310,   316,   103,   102,   104,   105,   nil,   nil,
   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   107,   106,   108,    97,    56,    99,    98,   100,   nil,   101,
   109,   110,   nil,    93,    94,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,   nil,
   233,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,   629,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,    81,    84,
    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,   nil,
    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,    57,
    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,   nil,
    65,    66,    68,   313,   314,    72,    73,   nil,   nil,   nil,
   nil,   nil,   309,   310,   316,   103,   102,   104,   105,   nil,
   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   107,   106,   108,    97,    56,    99,    98,   100,   288,
   101,   109,   110,   nil,    93,    94,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,   nil,
   nil,   233,   nil,   nil,    58,    59,   nil,   nil,    60,   nil,
   nil,   nil,   283,   nil,    44,   nil,   nil,   289,   nil,   nil,
   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,   286,
    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,   nil,
   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,    62,
    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,    67,
   nil,    65,    66,    68,   313,   314,    72,    73,   nil,   nil,
   nil,   nil,   nil,   309,   310,   316,   103,   102,   104,   105,
   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   107,   106,   108,    97,    56,    99,    98,   100,
   nil,   101,   109,   110,   nil,    93,    94,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   227,
   nil,   nil,   233,   nil,   nil,    58,    59,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   232,   nil,   nil,   nil,   nil,    91,
    81,    84,    85,   nil,    86,    88,    87,    89,   nil,   nil,
   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,    71,
    62,    57,    83,    95,    96,    63,    64,   nil,   nil,   nil,
    67,   nil,    65,    66,    68,   313,   314,    72,    73,   nil,
   nil,   nil,   nil,   nil,   309,   310,   316,   103,   102,   104,
   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,   nil,
   311,   nil,   nil,   107,   106,   108,    97,    56,    99,    98,
   100,   nil,   101,   109,   110,   nil,    93,    94,   nil,   nil,
   317,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   856,   nil,   nil,   233,   nil,   nil,    58,    59,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    91,    81,    84,    85,   nil,    86,    88,    87,    89,   nil,
   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,    75,
    71,    62,    57,    83,    95,    96,    63,    64,   nil,   nil,
   nil,    67,   nil,    65,    66,    68,   313,   314,    72,    73,
   nil,   nil,   nil,   nil,   nil,   309,   310,   316,   103,   102,
   104,   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,   nil,
   nil,   311,   nil,   nil,   107,   106,   108,    97,    56,    99,
    98,   100,   nil,   101,   109,   110,   nil,    93,    94,   nil,
   nil,   317,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   930,   nil,   nil,   233,   nil,   nil,    58,    59,   nil,
   nil,    60,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    91,    81,    84,    85,   nil,    86,    88,    87,    89,
   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,    74,
    75,    71,    62,    57,    83,    95,    96,    63,    64,   nil,
   nil,   nil,    67,   nil,    65,    66,    68,    30,    31,    72,
    73,   nil,   nil,   nil,   nil,   nil,    29,    28,    27,   103,
   102,   104,   105,   nil,   nil,   234,   nil,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   107,   106,   108,    97,    56,
    99,    98,   100,   288,   101,   109,   110,   nil,    93,    94,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   227,   nil,   nil,   233,   nil,   nil,    58,    59,
   nil,   nil,    60,   nil,   285,   nil,   283,   nil,    44,   nil,
   nil,   289,   nil,   nil,   nil,   nil,   nil,   232,   nil,   nil,
   nil,   nil,    91,   286,    84,    85,   nil,    86,    88,    87,
    89,   nil,   nil,   nil,   nil,    82,    90,   nil,   nil,   nil,
   nil,   nil,   nil,    62,   nil,    83,    95,    96,   175,   186,
   176,   199,   172,   192,   182,   181,   202,   203,   197,   180,
   179,   174,   200,   204,   205,   184,   173,   187,   191,   193,
   185,   178,   nil,   nil,   nil,   194,   201,   196,   195,   188,
   198,   183,   171,   190,   189,   nil,   nil,   nil,   nil,   nil,
   170,   177,   168,   169,   165,   166,   167,   126,   128,   125,
   nil,   127,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   159,
   160,   nil,   156,   138,   139,   140,   147,   144,   146,   nil,
   nil,   141,   142,   nil,   nil,   nil,   161,   162,   148,   149,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   153,   152,   nil,   137,   158,   155,   154,
   163,   150,   151,   145,   143,   135,   157,   136,   nil,   nil,
   164,    91,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    90,   175,   186,   176,   199,
   172,   192,   182,   181,   202,   203,   197,   180,   179,   174,
   200,   204,   205,   184,   173,   187,   191,   193,   185,   178,
   nil,   nil,   nil,   194,   201,   196,   195,   188,   198,   183,
   171,   190,   189,   nil,   nil,   nil,   nil,   nil,   170,   177,
   168,   169,   165,   166,   167,   126,   128,   nil,   nil,   127,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   159,   160,   nil,
   156,   138,   139,   140,   147,   144,   146,   nil,   nil,   141,
   142,   nil,   nil,   nil,   161,   162,   148,   149,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   153,   152,   nil,   137,   158,   155,   154,   163,   150,
   151,   145,   143,   135,   157,   136,   nil,   nil,   164,    91,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    90,   175,   186,   176,   199,   172,   192,
   182,   181,   202,   203,   197,   180,   179,   174,   200,   204,
   205,   184,   173,   187,   191,   193,   185,   178,   nil,   nil,
   nil,   194,   201,   196,   195,   188,   198,   183,   171,   190,
   189,   nil,   nil,   nil,   nil,   nil,   170,   177,   168,   169,
   165,   166,   167,   126,   128,   nil,   nil,   127,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   159,   160,   nil,   156,   138,
   139,   140,   147,   144,   146,   nil,   nil,   141,   142,   nil,
   nil,   nil,   161,   162,   148,   149,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   153,
   152,   nil,   137,   158,   155,   154,   163,   150,   151,   145,
   143,   135,   157,   136,   nil,   nil,   164,    91,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    90,   175,   186,   176,   199,   172,   192,   182,   181,
   202,   203,   197,   180,   179,   174,   200,   204,   205,   184,
   173,   187,   191,   193,   185,   178,   nil,   nil,   nil,   194,
   201,   196,   195,   188,   198,   183,   171,   190,   189,   nil,
   nil,   nil,   nil,   nil,   170,   177,   168,   169,   165,   166,
   167,   126,   128,   nil,   nil,   127,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   159,   160,   nil,   156,   138,   139,   140,
   147,   144,   146,   nil,   nil,   141,   142,   nil,   nil,   nil,
   161,   162,   148,   149,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   153,   152,   nil,
   137,   158,   155,   154,   163,   150,   151,   145,   143,   135,
   157,   136,   nil,   nil,   164,    91,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    90,
   175,   186,   176,   199,   172,   192,   182,   181,   202,   203,
   197,   180,   179,   174,   200,   204,   205,   184,   173,   187,
   191,   193,   185,   178,   nil,   nil,   nil,   194,   201,   196,
   374,   373,   375,   372,   171,   190,   189,   nil,   nil,   nil,
   nil,   nil,   170,   177,   168,   169,   369,   370,   371,   367,
   128,    99,    98,   368,   nil,   101,   nil,   nil,   nil,   nil,
   nil,   159,   160,   nil,   156,   138,   139,   140,   147,   144,
   146,   nil,   nil,   141,   142,   nil,   nil,   nil,   161,   162,
   148,   149,   nil,   nil,   nil,   nil,   nil,   379,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   153,   152,   nil,   137,   158,
   155,   154,   163,   150,   151,   145,   143,   135,   157,   136,
   nil,   nil,   164,   175,   186,   176,   199,   172,   192,   182,
   181,   202,   203,   197,   180,   179,   174,   200,   204,   205,
   184,   173,   187,   191,   193,   185,   178,   nil,   nil,   nil,
   194,   201,   196,   195,   188,   198,   183,   171,   190,   189,
   nil,   nil,   nil,   nil,   nil,   170,   177,   168,   169,   165,
   166,   167,   126,   128,   nil,   nil,   127,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   159,   160,   nil,   156,   138,   139,
   140,   147,   144,   146,   nil,   nil,   141,   142,   nil,   nil,
   nil,   161,   162,   148,   149,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   153,   152,
   nil,   137,   158,   155,   154,   163,   150,   151,   145,   143,
   135,   157,   136,   416,   420,   164,   nil,   417,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   159,   160,   nil,   156,   138,
   139,   140,   147,   144,   146,   nil,   nil,   141,   142,   nil,
   nil,   nil,   161,   162,   148,   149,   nil,   nil,   nil,   nil,
   nil,   267,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   153,
   152,   nil,   137,   158,   155,   154,   163,   150,   151,   145,
   143,   135,   157,   136,   423,   427,   164,   nil,   422,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   159,   160,   nil,   156,
   138,   139,   140,   147,   144,   146,   nil,   nil,   141,   142,
   nil,   nil,   nil,   161,   162,   148,   149,   nil,   nil,   nil,
   nil,   nil,   267,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   153,   152,   nil,   137,   158,   155,   154,   163,   150,   151,
   145,   143,   135,   157,   136,   478,   420,   164,   nil,   479,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   159,   160,   nil,
   156,   138,   139,   140,   147,   144,   146,   nil,   nil,   141,
   142,   nil,   nil,   nil,   161,   162,   148,   149,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   153,   152,   nil,   137,   158,   155,   154,   163,   150,
   151,   145,   143,   135,   157,   136,   608,   420,   164,   nil,
   609,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   159,   160,
   nil,   156,   138,   139,   140,   147,   144,   146,   nil,   nil,
   141,   142,   nil,   nil,   nil,   161,   162,   148,   149,   nil,
   nil,   nil,   nil,   nil,   267,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   153,   152,   nil,   137,   158,   155,   154,   163,
   150,   151,   145,   143,   135,   157,   136,   610,   427,   164,
   nil,   611,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   159,
   160,   nil,   156,   138,   139,   140,   147,   144,   146,   nil,
   nil,   141,   142,   nil,   nil,   nil,   161,   162,   148,   149,
   nil,   nil,   nil,   nil,   nil,   267,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   153,   152,   nil,   137,   158,   155,   154,
   163,   150,   151,   145,   143,   135,   157,   136,   640,   420,
   164,   nil,   641,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   159,   160,   nil,   156,   138,   139,   140,   147,   144,   146,
   nil,   nil,   141,   142,   nil,   nil,   nil,   161,   162,   148,
   149,   nil,   nil,   nil,   nil,   nil,   267,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   153,   152,   nil,   137,   158,   155,
   154,   163,   150,   151,   145,   143,   135,   157,   136,   643,
   427,   164,   nil,   644,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   159,   160,   nil,   156,   138,   139,   140,   147,   144,
   146,   nil,   nil,   141,   142,   nil,   nil,   nil,   161,   162,
   148,   149,   nil,   nil,   nil,   nil,   nil,   267,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   153,   152,   nil,   137,   158,
   155,   154,   163,   150,   151,   145,   143,   135,   157,   136,
   608,   420,   164,   nil,   609,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   159,   160,   nil,   156,   138,   139,   140,   147,
   144,   146,   nil,   nil,   141,   142,   nil,   nil,   nil,   161,
   162,   148,   149,   nil,   nil,   nil,   nil,   nil,   267,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   153,   152,   nil,   137,
   158,   155,   154,   163,   150,   151,   145,   143,   135,   157,
   136,   610,   427,   164,   nil,   611,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   159,   160,   nil,   156,   138,   139,   140,
   147,   144,   146,   nil,   nil,   141,   142,   nil,   nil,   nil,
   161,   162,   148,   149,   nil,   nil,   nil,   nil,   nil,   267,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   153,   152,   nil,
   137,   158,   155,   154,   163,   150,   151,   145,   143,   135,
   157,   136,   701,   420,   164,   nil,   702,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   159,   160,   nil,   156,   138,   139,
   140,   147,   144,   146,   nil,   nil,   141,   142,   nil,   nil,
   nil,   161,   162,   148,   149,   nil,   nil,   nil,   nil,   nil,
   267,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   153,   152,
   nil,   137,   158,   155,   154,   163,   150,   151,   145,   143,
   135,   157,   136,   703,   427,   164,   nil,   704,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   159,   160,   nil,   156,   138,
   139,   140,   147,   144,   146,   nil,   nil,   141,   142,   nil,
   nil,   nil,   161,   162,   148,   149,   nil,   nil,   nil,   nil,
   nil,   267,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   153,
   152,   nil,   137,   158,   155,   154,   163,   150,   151,   145,
   143,   135,   157,   136,   706,   427,   164,   nil,   707,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   159,   160,   nil,   156,
   138,   139,   140,   147,   144,   146,   nil,   nil,   141,   142,
   nil,   nil,   nil,   161,   162,   148,   149,   nil,   nil,   nil,
   nil,   nil,   267,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   153,   152,   nil,   137,   158,   155,   154,   163,   150,   151,
   145,   143,   135,   157,   136,   478,   420,   164,   nil,   479,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   159,   160,   nil,
   156,   138,   139,   140,   147,   144,   146,   nil,   nil,   141,
   142,   nil,   nil,   nil,   161,   162,   148,   149,   nil,   nil,
   nil,   nil,   nil,   267,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   153,   152,   nil,   137,   158,   155,   154,   163,   150,
   151,   145,   143,   135,   157,   136,   973,   427,   164,   nil,
   972,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   159,   160,
   nil,   156,   138,   139,   140,   147,   144,   146,   nil,   nil,
   141,   142,   nil,   nil,   nil,   161,   162,   148,   149,   nil,
   nil,   nil,   nil,   nil,   267,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   153,   152,   nil,   137,   158,   155,   154,   163,
   150,   151,   145,   143,   135,   157,   136,   999,   420,   164,
   nil,  1000,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   159,
   160,   nil,   156,   138,   139,   140,   147,   144,   146,   nil,
   nil,   141,   142,   nil,   nil,   nil,   161,   162,   148,   149,
   nil,   nil,   nil,   nil,   nil,   267,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   153,   152,   nil,   137,   158,   155,   154,
   163,   150,   151,   145,   143,   135,   157,   136,  1001,   427,
   164,   nil,  1002,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   159,   160,   nil,   156,   138,   139,   140,   147,   144,   146,
   nil,   nil,   141,   142,   nil,   nil,   nil,   161,   162,   148,
   149,   nil,   nil,   nil,   nil,   nil,   267,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   153,   152,   nil,   137,   158,   155,
   154,   163,   150,   151,   145,   143,   135,   157,   136,   nil,
   672,   164,   669,   668,   667,   677,   670,   nil,   672,   nil,
   669,   668,   667,   677,   670,   680,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   680,   nil,   672,   nil,   669,   668,   667,
   677,   670,   nil,   nil,   nil,   nil,   nil,   675,   nil,   nil,
   680,   nil,   nil,   nil,   nil,   675,   685,   684,   688,   687,
   nil,   nil,   nil,   681,   685,   684,   688,   687,   nil,   nil,
   nil,   681,   675,   nil,   672,   nil,   669,   668,   667,   677,
   670,   685,   684,   688,   687,   nil,   nil,   nil,   681,   680,
   nil,   672,   nil,   669,   668,   667,   677,   670,   nil,   672,
   nil,   669,   668,   667,   677,   670,   680,   nil,   nil,   nil,
   nil,   675,   nil,   nil,   680,   nil,   nil,   nil,   nil,   nil,
   685,   684,   688,   687,   nil,   nil,   nil,   681,   675,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   675,   685,   684,   688,
   687,   nil,   nil,   nil,   681,   685,   684,   688,   687,   nil,
   nil,   672,   681,   669,   668,   667,   677,   670,   nil,   672,
   nil,   669,   668,   667,   677,   670,   680,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   680,   nil,   672,   nil,   669,   668,
   667,   677,   670,   nil,   nil,   nil,   nil,   nil,   675,   nil,
   nil,   680,   nil,   nil,   nil,   nil,   675,   685,   684,   688,
   687,   nil,   nil,   nil,   681,   685,   684,   688,   687,   nil,
   nil,   nil,   681,   675,   nil,   672,   nil,   669,   668,   667,
   677,   670,   nil,   nil,   688,   687,   nil,   nil,   nil,   681,
   680,   nil,   672,   nil,   669,   668,   667,   677,   670,   672,
   nil,   669,   668,   667,   677,   670,   nil,   680,   nil,   nil,
   nil,   nil,   675,   nil,   680,   nil,   672,   nil,   669,   668,
   667,   677,   670,   688,   687,   nil,   nil,   nil,   681,   675,
   nil,   680,   nil,   nil,   nil,   nil,   675,   nil,   685,   684,
   688,   687,   nil,   nil,   nil,   681,   nil,   688,   687,   nil,
   nil,   nil,   681,   675,   nil,   672,   nil,   669,   668,   667,
   677,   670,   nil,   nil,   688,   687,   nil,   nil,   nil,   681,
   680,   nil,   672,   nil,   669,   668,   667,   677,   670,   672,
   nil,   669,   668,   667,   677,   670,   nil,   680,   nil,   nil,
   nil,   nil,   675,   nil,   680,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   688,   687,   nil,   nil,   nil,   681,   675,
   nil,   nil,   nil,   nil,   nil,   nil,   675,   nil,   nil,   nil,
   688,   687,   nil,   nil,   nil,   681,   nil,   688,   687,   nil,
   nil,   nil,   681 ]

racc_action_check = [
    97,   440,   440,   564,   564,    15,   348,    97,    97,    97,
    58,   341,    97,    97,    97,    24,    97,    26,    19,   386,
   476,   484,    24,   342,    97,   387,    97,    97,    97,    61,
   358,   620,   225,     1,   358,   349,    97,    97,   699,    97,
    97,    97,    97,    97,   865,   892,   927,   928,   931,   352,
    58,   550,    15,   635,   978,   476,   484,   332,   312,    19,
   332,     7,    15,   635,   485,   701,    97,    97,    97,    97,
    97,    97,    97,    97,    97,    97,    97,    97,    97,    97,
    24,    26,    97,    97,    97,   386,    97,    97,   542,   225,
    97,   387,   702,    97,    97,   440,    97,   564,    97,   485,
    97,    10,    97,    97,    26,    97,    97,    97,    97,    97,
   999,    97,   100,    97,  1000,   348,    61,   792,   620,   100,
   100,   100,   312,  1001,   100,   100,   100,    97,   100,   341,
    97,    97,    97,    97,   341,    97,   100,    97,   100,   100,
   100,   342,    97,    97,   349,   312,   342,   640,   100,   100,
  1002,   100,   100,   100,   100,   100,   699,  1020,   352,   699,
   550,   699,   865,   892,   927,   928,   931,   865,   892,   927,
   928,   931,   978,   543,   701,   703,   824,   978,   100,   100,
   100,   100,   100,   100,   100,   100,   100,   100,   100,   100,
   100,   100,   542,    12,   100,   100,   100,   542,   100,   100,
  1001,   702,   100,    13,   641,   100,   100,   825,   100,   704,
   100,     3,   100,   640,   100,   100,     3,   100,   100,   100,
   100,   100,   422,   100,   226,   100,   792,  1002,   999,   422,
   422,   422,  1000,   999,   621,   422,   422,  1000,   422,   100,
   703,  1001,   100,   100,   100,   100,  1001,   100,    16,   100,
   650,   650,   228,   640,   100,   100,   640,    22,   422,   422,
   780,   422,   422,   422,   422,   422,   640,    37,  1002,   621,
   641,   367,   643,  1002,   704,  1020,   333,   543,   367,   333,
  1020,   226,   543,   446,   703,   824,   560,   560,   422,   422,
   422,   422,   422,   422,   422,   422,   422,   422,   422,   422,
   422,   422,    40,   608,   422,   422,   422,   368,   422,   228,
   641,   825,   422,   641,   368,   422,   825,   897,   704,   897,
   422,    45,   422,   641,   422,   422,   780,   422,   422,   422,
   422,   422,   381,   422,   423,   422,   367,   643,   643,    79,
   446,   423,   423,   423,   650,    17,    17,   423,   423,   422,
   423,    79,   422,   422,   111,   422,   206,   422,   292,   423,
   608,    79,   227,   292,   422,   422,   780,   356,    38,   780,
   423,   423,   368,   423,   423,   423,   423,   423,   643,   780,
   560,   643,   340,   340,   229,   560,   809,   609,   809,   809,
   809,   643,   809,    41,    41,   230,   381,   381,   381,   234,
   423,   423,   423,   423,   423,   423,   423,   423,   423,   423,
   423,   423,   423,   423,   266,    38,   423,   423,   423,   356,
   423,   317,   317,   495,   423,    38,   356,   423,   345,   753,
   280,   356,   423,   345,   423,   356,   423,   423,    39,   423,
   423,   423,   423,   423,   609,   423,   423,   423,   575,   948,
   577,   948,   948,   948,   356,   948,   369,   828,   281,   357,
   284,   423,   828,   369,   423,   423,   610,   423,   809,   423,
   296,    41,    41,   610,   610,   610,   423,   423,   610,   610,
   610,   297,   610,   299,   356,    39,   753,   495,   495,   495,
   370,   610,   610,   610,   610,    39,   833,   370,   300,   317,
   317,   833,   610,   610,   495,   610,   610,   610,   610,   610,
   301,   357,   575,   575,   577,   577,   382,   336,   357,   383,
   336,   369,   575,   357,   577,   384,   656,   357,   307,   656,
   310,   948,   610,   610,   610,   610,   610,   610,   610,   610,
   610,   610,   610,   610,   610,   610,   357,   311,   610,   610,
   610,   917,   610,   610,   917,   370,   610,   385,   321,   610,
   610,   388,   610,   371,   610,   316,   610,   416,   610,   610,
   371,   610,   610,   610,   610,   610,   357,   610,   610,   610,
   382,   382,   382,   383,   383,   383,   318,   372,   322,   384,
   384,   384,   325,   610,   372,   330,   610,   610,   610,   610,
   334,   610,   335,   610,   611,   321,   526,   526,   610,   610,
   337,   611,   611,   611,   416,   321,   611,   611,   611,   346,
   611,   385,   385,   385,   416,   388,   388,   388,   371,   125,
   611,   611,   611,   347,   125,   125,   690,   690,   417,   351,
   611,   611,   353,   611,   611,   611,   611,   611,   362,    14,
   397,    46,   372,   224,   373,   374,    14,   403,    46,   375,
   224,   373,   374,   797,   797,    14,   375,    46,   406,   224,
   611,   611,   611,   611,   611,   611,   611,   611,   611,   611,
   611,   611,   611,   611,   408,   417,   611,   611,   611,   452,
   611,   611,   304,   412,   611,   417,   377,   611,   611,   304,
   611,   414,   611,   377,   611,   591,   611,   611,   304,   611,
   611,   611,   611,   611,    14,   611,    46,   611,   224,   373,
   374,   452,   305,   415,   375,   452,   452,   424,   453,   305,
   432,   611,   989,   989,   611,   611,   611,   611,   305,   611,
   442,   611,    27,   454,   455,   456,   611,   611,   457,    27,
    27,    27,   591,   794,    27,    27,    27,   304,    27,   482,
   453,   377,   591,   794,   453,   453,   486,    27,    27,    27,
   568,   568,   502,   503,   568,   568,   568,   506,    27,    27,
   508,    27,    27,    27,    27,    27,   889,   305,   889,   889,
   889,   306,   889,   513,   516,   524,   794,   794,   306,   525,
   675,   794,   675,   675,   675,   527,   675,   306,    27,    27,
    27,    27,    27,    27,    27,    27,    27,    27,    27,    27,
    27,    27,   539,   889,    27,    27,    27,   544,   545,    27,
   562,    27,    27,   572,   580,    27,    27,   675,    27,   582,
    27,   588,    27,   592,    27,    27,   675,    27,    27,    27,
    27,    27,    28,    27,    27,    27,   306,   597,   602,    28,
    28,    28,   612,   614,    28,    28,    28,   308,    28,    27,
   619,   626,    27,    27,   308,    27,   628,    27,    28,    28,
   634,   637,   639,   308,    27,   642,   645,   646,    28,    28,
   649,    28,    28,    28,    28,    28,   651,   654,   660,   323,
   661,   350,   663,   664,   665,   674,   323,   806,   350,   806,
   806,   806,   682,   806,   686,   323,   689,   350,    28,    28,
    28,    28,    28,    28,    28,    28,    28,    28,    28,    28,
    28,    28,   308,   692,    28,    28,    28,   697,   700,    28,
   709,    28,    28,   713,   806,    28,    28,   732,    28,   737,
    28,   755,    28,   806,    28,    28,   756,    28,    28,    28,
    28,    28,    56,    28,   323,    28,   350,   758,   759,    56,
    56,    56,   760,   762,    56,    56,    56,   360,    56,    28,
   763,   764,    28,    28,   360,    28,   765,    28,    56,    56,
    56,   769,   773,   360,    28,   774,   779,   783,    56,    56,
   786,    56,    56,    56,    56,    56,   787,   512,   790,   553,
   793,   855,   808,   810,   512,   815,   553,   818,   855,   827,
   831,   832,   835,   512,   836,   553,   852,   855,    56,    56,
    56,    56,    56,    56,    56,    56,    56,    56,    56,    56,
    56,    56,   360,   921,    56,    56,    56,   856,   858,    56,
   921,   872,    56,   873,   877,    56,    56,   878,    56,   921,
    56,   880,    56,   881,    56,    56,   883,    56,    56,    56,
    56,    56,   512,    56,   553,    56,   855,   886,   842,   949,
   929,   949,   949,   949,   888,   949,   894,   929,   842,    56,
   895,   706,    56,    56,    56,    56,   929,    56,   418,    56,
   901,   905,   907,   910,    56,   418,   418,   418,   921,   911,
   418,   418,   418,   912,   418,   913,   949,   471,   915,   930,
   950,   842,   842,   418,   418,   418,   842,   853,   972,   853,
   853,   853,   973,   853,   418,   418,   974,   418,   418,   418,
   418,   418,   979,   706,   980,   929,   981,   982,   983,   471,
   706,   984,   985,   471,   471,   706,   471,   471,   987,   706,
   990,   991,   992,   993,   418,   418,   418,   418,   418,   418,
   418,   418,   418,   418,   418,   418,   418,   418,   706,   994,
   418,   418,   418,   644,   995,   418,   998,   418,   418,  1011,
   644,   418,   418,  1021,   418,   644,   418,  1022,   418,   644,
   418,   418,  1023,   418,   418,   418,   418,   418,   706,   418,
   418,   418,   nil,   887,   nil,   887,   887,   887,   nil,   887,
   707,   nil,   nil,   nil,   nil,   418,   nil,   707,   418,   418,
   427,   418,   707,   418,   nil,   nil,   707,   427,   427,   427,
   418,   nil,   427,   427,   427,   nil,   427,   462,   644,   nil,
   887,   nil,   nil,   nil,   nil,   427,   427,   427,   427,   887,
   nil,   nil,   nil,   462,   462,   nil,   427,   427,   nil,   427,
   427,   427,   427,   427,   986,   nil,   986,   986,   986,   462,
   986,   462,   nil,   462,   462,   707,   462,   462,   nil,   nil,
   462,   nil,   462,   nil,   nil,   nil,   427,   427,   427,   427,
   427,   427,   427,   427,   427,   427,   427,   427,   427,   427,
   nil,   986,   427,   427,   427,   472,   nil,   427,   nil,   nil,
   427,   nil,   nil,   427,   427,   nil,   427,   nil,   427,   nil,
   427,   nil,   427,   427,   nil,   427,   427,   427,   427,   427,
   nil,   427,   427,   427,   nil,   nil,   nil,   472,   nil,   nil,
   nil,   472,   472,   nil,   472,   472,   nil,   427,   nil,   nil,
   427,   427,   427,   427,   nil,   427,   428,   427,   nil,   nil,
   nil,   nil,   427,   428,   428,   428,   nil,   nil,   428,   428,
   428,   463,   428,   971,   nil,   971,   971,   971,   nil,   971,
   nil,   428,   428,   428,   428,   nil,   nil,   463,   463,   nil,
   nil,   nil,   428,   428,   nil,   428,   428,   428,   428,   428,
   nil,   nil,   nil,   463,   nil,   463,   nil,   463,   463,   nil,
   463,   463,   nil,   nil,   463,   nil,   463,     6,     6,     6,
     6,     6,   428,   428,   428,   428,   428,   428,   428,   428,
   428,   428,   428,   428,   428,   428,   nil,   nil,   428,   428,
   428,   nil,   nil,   428,   nil,   nil,   428,   nil,   nil,   428,
   428,   nil,   428,   nil,   428,   nil,   428,   nil,   428,   428,
   nil,   428,   428,   428,   428,   428,   nil,   428,   428,   428,
   295,   295,   295,   295,   295,   nil,   nil,   nil,   988,   nil,
   988,   988,   988,   428,   988,   nil,   428,   428,   428,   428,
   nil,   428,   477,   428,   nil,   nil,   nil,   nil,   428,   477,
   477,   477,   nil,   nil,   477,   477,   477,   616,   477,   616,
   616,   616,   616,   616,   nil,   988,   nil,   477,   477,   nil,
   nil,   nil,   616,   nil,   nil,   nil,   460,   nil,   477,   477,
   nil,   477,   477,   477,   477,   477,   500,   500,   500,   500,
   500,   nil,   460,   460,   616,   nil,   537,   nil,   537,   537,
   537,   537,   537,   616,   616,   616,   616,   nil,   460,   nil,
   616,   537,   460,   460,   nil,   460,   460,   nil,   nil,   477,
   nil,   nil,   nil,   nil,   458,   nil,   477,   nil,   nil,   nil,
   nil,   477,   477,   537,   537,   nil,   616,   nil,   nil,   nil,
   458,   458,   537,   537,   537,   537,   nil,   nil,   nil,   537,
   nil,   nil,   nil,   nil,   477,   477,   458,   nil,   458,   nil,
   458,   458,   nil,   458,   458,   nil,   nil,   nil,   nil,   477,
   nil,   nil,   477,   nil,   nil,   nil,   nil,   477,     0,     0,
     0,     0,     0,     0,   477,   nil,   nil,     0,     0,   nil,
   nil,   nil,     0,   nil,     0,     0,     0,     0,     0,     0,
     0,   nil,   nil,   nil,   nil,   nil,     0,     0,     0,     0,
     0,     0,     0,   nil,   nil,     0,   nil,   nil,   nil,   nil,
   435,     0,     0,     0,     0,     0,     0,     0,     0,     0,
     0,     0,     0,   nil,     0,     0,     0,   nil,     0,     0,
     0,     0,     0,   435,   435,   435,   435,   435,   435,   435,
   435,   435,   435,   435,   nil,   435,   435,   nil,   nil,   435,
   435,   nil,     0,   nil,   nil,     0,   nil,   nil,     0,     0,
   nil,   nil,     0,   nil,     0,   435,   nil,   435,     0,   435,
   435,   nil,   435,   435,   435,   435,   435,     0,   435,   nil,
   nil,   nil,     0,     0,     0,     0,   nil,     0,     0,     0,
     0,   nil,   nil,   nil,   nil,     0,     0,   nil,   435,   nil,
   435,   nil,   nil,     0,   nil,     0,     0,     0,    33,    33,
    33,    33,    33,    33,   nil,   nil,   nil,    33,    33,   nil,
   nil,   nil,    33,   nil,    33,    33,    33,    33,    33,    33,
    33,   nil,   nil,   nil,   nil,   nil,    33,    33,    33,    33,
    33,    33,    33,   nil,   nil,    33,   nil,   nil,   nil,   nil,
   411,    33,    33,    33,    33,    33,    33,    33,    33,    33,
    33,    33,    33,   nil,    33,    33,    33,   nil,    33,    33,
    33,    33,    33,   411,   411,   411,   411,   411,   411,   411,
   411,   411,   411,   411,   nil,   411,   411,   nil,   nil,   411,
   411,   nil,    33,   nil,   nil,    33,   nil,   nil,    33,    33,
   nil,   nil,    33,   nil,    33,   411,   nil,   411,    33,   411,
   411,   nil,   411,   411,   411,   411,   411,    33,   411,   nil,
   nil,   nil,    33,    33,    33,    33,   nil,    33,    33,    33,
    33,   nil,   nil,   nil,   nil,    33,    33,   nil,   411,   nil,
   nil,   nil,   nil,    33,   nil,    33,    33,    33,   123,   123,
   123,   123,   123,   123,   nil,   nil,   nil,   123,   123,   nil,
   nil,   nil,   123,   nil,   123,   123,   123,   123,   123,   123,
   123,   nil,   nil,   nil,   nil,   nil,   123,   123,   123,   123,
   123,   123,   123,   nil,   nil,   123,   nil,   nil,   nil,   nil,
   613,   123,   123,   123,   123,   123,   123,   123,   123,   123,
   123,   123,   123,   nil,   123,   123,   123,   nil,   123,   123,
   123,   123,   123,   613,   613,   613,   613,   613,   613,   613,
   613,   613,   613,   613,   nil,   613,   613,   nil,   nil,   613,
   613,   nil,   123,   nil,   nil,   123,   nil,   nil,   123,   123,
   nil,   nil,   123,   nil,   123,   613,   nil,   613,   123,   613,
   613,   nil,   613,   613,   613,   613,   613,   123,   613,   nil,
   nil,   nil,   123,   123,   123,   123,   nil,   123,   123,   123,
   123,   nil,   nil,   nil,   nil,   123,   123,   nil,   613,   nil,
   nil,   nil,   nil,   123,   nil,   123,   123,   123,   208,   208,
   208,   208,   208,   208,   nil,   nil,   nil,   208,   208,   nil,
   nil,   nil,   208,   nil,   208,   208,   208,   208,   208,   208,
   208,   nil,   nil,   nil,   nil,   nil,   208,   208,   208,   208,
   208,   208,   208,   nil,   nil,   208,   nil,   nil,   nil,   nil,
   nil,   208,   208,   208,   208,   208,   208,   208,   208,   208,
   208,   208,   208,   nil,   208,   208,   208,   nil,   208,   208,
   208,   208,   208,    21,    21,    21,    21,    21,    21,    21,
    21,    21,    21,    21,   nil,    21,    21,   nil,   nil,    21,
    21,   nil,   208,   nil,   nil,   208,   nil,   nil,   208,   208,
   nil,   nil,   208,   nil,   208,    21,   nil,    21,   208,    21,
    21,   nil,    21,    21,    21,    21,    21,   208,    21,   nil,
   nil,   nil,   208,   208,   208,   208,   nil,   208,   208,   208,
   208,   nil,   nil,   nil,   nil,   208,   208,   nil,    21,   nil,
   nil,   nil,   nil,   208,   nil,   208,   208,   208,   233,   233,
   233,   233,   233,   233,   nil,   nil,   nil,   233,   233,   nil,
   nil,   nil,   233,   nil,   233,   233,   233,   233,   233,   233,
   233,   nil,   nil,   nil,   nil,   nil,   233,   233,   233,   233,
   233,   233,   233,   nil,   nil,   233,   nil,   nil,   nil,   nil,
   nil,   233,   233,   233,   233,   233,   233,   233,   233,   233,
   233,   233,   233,   nil,   233,   233,   233,   nil,   233,   233,
   233,   233,   233,   278,   278,   278,   278,   278,   278,   278,
   278,   278,   278,   278,   nil,   278,   278,   nil,   nil,   278,
   278,   nil,   233,   nil,   nil,   233,   nil,   nil,   233,   233,
   nil,   nil,   233,   nil,   233,   278,   nil,   278,   233,   278,
   278,   nil,   278,   278,   278,   278,   278,   233,   278,   nil,
   nil,   nil,   233,   233,   233,   233,   nil,   233,   233,   233,
   233,   nil,   nil,   nil,   nil,   233,   233,   nil,   278,   nil,
   nil,   nil,   nil,   233,   nil,   233,   233,   233,   298,   298,
   298,   298,   298,   298,   nil,   nil,   nil,   298,   298,   nil,
   nil,   nil,   298,   nil,   298,   298,   298,   298,   298,   298,
   298,   nil,   nil,   nil,   nil,   nil,   298,   298,   298,   298,
   298,   298,   298,   nil,   nil,   298,   nil,   nil,   nil,   nil,
   nil,   298,   298,   298,   298,   298,   298,   298,   298,   298,
   298,   298,   298,   nil,   298,   298,   298,   nil,   298,   298,
   298,   298,   298,   430,   430,   430,   430,   430,   430,   430,
   430,   430,   430,   430,   nil,   430,   430,   nil,   nil,   430,
   430,   nil,   298,   nil,   nil,   298,   nil,   nil,   298,   298,
   nil,   nil,   298,   nil,   298,   430,   nil,   430,   298,   430,
   430,   nil,   430,   430,   430,   430,   430,   298,   430,   nil,
   nil,   nil,   298,   298,   298,   298,   nil,   298,   298,   298,
   298,   nil,   nil,   nil,   nil,   298,   298,   nil,   430,   nil,
   nil,   nil,   nil,   298,   nil,   298,   298,   298,   303,   303,
   303,   303,   303,   303,   nil,   nil,   nil,   303,   303,   nil,
   nil,   nil,   303,   nil,   303,   303,   303,   303,   303,   303,
   303,   nil,   nil,   nil,   nil,   nil,   303,   303,   303,   303,
   303,   303,   303,   nil,   nil,   303,   nil,   nil,   nil,   nil,
   nil,   303,   303,   303,   303,   303,   303,   303,   303,   303,
   303,   303,   303,   nil,   303,   303,   303,   nil,   303,   303,
   303,   303,   303,   475,   475,   475,   475,   475,   475,   475,
   475,   475,   475,   475,   nil,   475,   475,   nil,   nil,   475,
   475,   nil,   303,   nil,   nil,   303,   nil,   nil,   303,   303,
   nil,   nil,   303,   nil,   303,   475,   nil,   475,   303,   475,
   475,   nil,   475,   475,   475,   475,   475,   303,   475,   nil,
   nil,   nil,   303,   303,   303,   303,   nil,   303,   303,   303,
   303,   nil,   nil,   nil,   nil,   303,   303,   475,   475,   nil,
   nil,   nil,   nil,   303,   nil,   303,   303,   303,   328,   328,
   328,   328,   328,   328,   nil,   nil,   nil,   328,   328,   nil,
   nil,   nil,   328,   nil,   328,   328,   328,   328,   328,   328,
   328,   nil,   nil,   nil,   nil,   nil,   328,   328,   328,   328,
   328,   328,   328,   nil,   nil,   328,   nil,   nil,   nil,   nil,
   nil,   328,   328,   328,   328,   328,   328,   328,   328,   328,
   328,   328,   328,   nil,   328,   328,   328,   nil,   328,   328,
   328,   328,   328,   522,   522,   522,   522,   522,   522,   522,
   522,   522,   522,   522,   nil,   522,   522,   nil,   nil,   522,
   522,   nil,   328,   nil,   nil,   328,   nil,   nil,   328,   328,
   nil,   nil,   328,   nil,   328,   522,   nil,   522,   328,   522,
   522,   nil,   522,   522,   522,   522,   522,   328,   522,   nil,
   nil,   nil,   328,   328,   328,   328,   nil,   328,   328,   328,
   328,   nil,   nil,   nil,   nil,   328,   328,   nil,   522,   nil,
   nil,   nil,   nil,   328,   nil,   328,   328,   328,   501,   501,
   501,   501,   501,   501,   nil,   nil,   nil,   501,   501,   nil,
   nil,   nil,   501,   nil,   501,   501,   501,   501,   501,   501,
   501,   nil,   nil,   nil,   nil,   nil,   501,   501,   501,   501,
   501,   501,   501,   nil,   nil,   501,   nil,   nil,   nil,   nil,
   nil,   501,   501,   501,   501,   501,   501,   501,   501,   501,
   501,   501,   501,   nil,   501,   501,   501,   nil,   501,   501,
   501,   501,   501,   648,   648,   648,   648,   648,   648,   648,
   648,   648,   648,   648,   nil,   648,   648,   nil,   nil,   648,
   648,   nil,   501,   nil,   nil,   501,   nil,   nil,   501,   501,
   nil,   nil,   501,   nil,   501,   648,   nil,   648,   501,   648,
   648,   nil,   648,   648,   648,   648,   648,   501,   648,   nil,
   nil,   nil,   501,   501,   501,   501,   nil,   501,   501,   501,
   501,   nil,   nil,   nil,   nil,   501,   501,   nil,   648,   nil,
   nil,   nil,   nil,   501,   nil,   501,   501,   501,   538,   538,
   538,   538,   538,   538,   nil,   nil,   nil,   538,   538,   nil,
   nil,   nil,   538,   nil,   538,   538,   538,   538,   538,   538,
   538,   nil,   nil,   nil,   nil,   nil,   538,   538,   538,   538,
   538,   538,   538,   nil,   nil,   538,   nil,   nil,   nil,   nil,
   nil,   538,   538,   538,   538,   538,   538,   538,   538,   538,
   538,   538,   538,   nil,   538,   538,   538,   nil,   538,   538,
   538,   538,   538,   734,   734,   734,   734,   734,   734,   734,
   734,   734,   734,   734,   nil,   734,   734,   nil,   nil,   734,
   734,   nil,   538,   nil,   nil,   538,   nil,   nil,   538,   538,
   nil,   nil,   538,   nil,   538,   734,   nil,   734,   538,   734,
   734,   nil,   734,   734,   734,   734,   734,   538,   734,   nil,
   nil,   nil,   538,   538,   538,   538,   nil,   538,   538,   538,
   538,   nil,   nil,   nil,   nil,   538,   538,   nil,   734,   nil,
   nil,   nil,   nil,   538,   nil,   538,   538,   538,   541,   541,
   541,   541,   541,   541,   nil,   nil,   nil,   541,   541,   nil,
   nil,   nil,   541,   nil,   541,   541,   541,   541,   541,   541,
   541,   nil,   nil,   nil,   nil,   nil,   541,   541,   541,   541,
   541,   541,   541,   nil,   nil,   541,   nil,   nil,   nil,   nil,
   nil,   541,   541,   541,   541,   541,   541,   541,   541,   541,
   541,   541,   541,   nil,   541,   541,   541,   nil,   541,   541,
   541,   541,   541,   739,   739,   739,   739,   739,   739,   739,
   739,   739,   739,   739,   nil,   739,   739,   nil,   nil,   739,
   739,   nil,   541,   nil,   nil,   541,   nil,   nil,   541,   541,
   nil,   nil,   541,   nil,   541,   739,   nil,   739,   541,   739,
   739,   nil,   739,   739,   739,   739,   739,   541,   739,   nil,
   nil,   nil,   541,   541,   541,   541,   nil,   541,   541,   541,
   541,   nil,   nil,   nil,   nil,   541,   541,   nil,   739,   nil,
   nil,   nil,   nil,   541,   nil,   541,   541,   541,   561,   561,
   561,   561,   561,   561,   nil,   nil,   nil,   561,   561,   nil,
   nil,   nil,   561,   nil,   561,   561,   561,   561,   561,   561,
   561,   nil,   nil,   nil,   nil,   nil,   561,   561,   561,   561,
   561,   561,   561,   nil,   nil,   561,   nil,   nil,   nil,   nil,
   nil,   561,   561,   561,   561,   561,   561,   561,   561,   561,
   561,   561,   561,   nil,   561,   561,   561,   nil,   561,   561,
   561,   561,   561,   741,   741,   741,   741,   741,   741,   741,
   741,   741,   741,   741,   nil,   741,   741,   nil,   nil,   741,
   741,   nil,   561,   nil,   nil,   561,   nil,   nil,   561,   561,
   nil,   nil,   561,   nil,   561,   741,   nil,   741,   561,   741,
   741,   nil,   741,   741,   741,   741,   741,   561,   741,   nil,
   nil,   nil,   561,   561,   561,   561,   nil,   561,   561,   561,
   561,   nil,   nil,   nil,   nil,   561,   561,   nil,   741,   nil,
   nil,   nil,   nil,   561,   nil,   561,   561,   561,   618,   618,
   618,   618,   618,   618,   nil,   nil,   nil,   618,   618,   nil,
   nil,   nil,   618,   nil,   618,   618,   618,   618,   618,   618,
   618,   nil,   nil,   nil,   nil,   nil,   618,   618,   618,   618,
   618,   618,   618,   nil,   nil,   618,   nil,   nil,   nil,   nil,
   nil,   618,   618,   618,   618,   618,   618,   618,   618,   618,
   618,   618,   618,   nil,   618,   618,   618,   nil,   618,   618,
   618,   618,   618,   744,   744,   744,   744,   744,   744,   744,
   744,   744,   744,   744,   nil,   744,   744,   nil,   nil,   744,
   744,   nil,   618,   nil,   nil,   618,   nil,   nil,   618,   618,
   nil,   nil,   618,   nil,   618,   744,   nil,   744,   618,   744,
   744,   nil,   744,   744,   744,   744,   744,   618,   744,   nil,
   nil,   nil,   618,   618,   618,   618,   nil,   618,   618,   618,
   618,   nil,   nil,   nil,   nil,   618,   618,   nil,   744,   nil,
   nil,   nil,   nil,   618,   nil,   618,   618,   618,   623,   623,
   623,   623,   623,   623,   nil,   nil,   nil,   623,   623,   nil,
   nil,   nil,   623,   nil,   623,   623,   623,   623,   623,   623,
   623,   nil,   nil,   nil,   nil,   nil,   623,   623,   623,   623,
   623,   623,   623,   nil,   nil,   623,   nil,   nil,   nil,   nil,
   nil,   623,   623,   623,   623,   623,   623,   623,   623,   623,
   623,   623,   623,   nil,   623,   623,   623,   nil,   623,   623,
   623,   623,   623,   746,   746,   746,   746,   746,   746,   746,
   746,   746,   746,   746,   nil,   746,   746,   nil,   nil,   746,
   746,   nil,   623,   nil,   nil,   623,   nil,   nil,   623,   623,
   nil,   nil,   623,   nil,   623,   746,   nil,   746,   623,   746,
   746,   nil,   746,   746,   746,   746,   746,   623,   746,   nil,
   nil,   nil,   623,   623,   623,   623,   nil,   623,   623,   623,
   623,   nil,   nil,   nil,   nil,   623,   623,   nil,   746,   nil,
   nil,   nil,   nil,   623,   nil,   623,   623,   623,   624,   624,
   624,   624,   624,   624,   nil,   nil,   nil,   624,   624,   nil,
   nil,   nil,   624,   nil,   624,   624,   624,   624,   624,   624,
   624,   nil,   nil,   nil,   nil,   nil,   624,   624,   624,   624,
   624,   624,   624,   nil,   nil,   624,   nil,   nil,   nil,   nil,
   nil,   624,   624,   624,   624,   624,   624,   624,   624,   624,
   624,   624,   624,   nil,   624,   624,   624,   nil,   624,   624,
   624,   624,   624,   748,   748,   748,   748,   748,   748,   748,
   748,   748,   748,   748,   nil,   748,   748,   nil,   nil,   748,
   748,   nil,   624,   nil,   nil,   624,   nil,   nil,   624,   624,
   nil,   nil,   624,   nil,   624,   748,   nil,   748,   624,   748,
   748,   nil,   748,   748,   748,   748,   748,   624,   748,   nil,
   nil,   nil,   624,   624,   624,   624,   nil,   624,   624,   624,
   624,   nil,   nil,   nil,   nil,   624,   624,   nil,   748,   nil,
   nil,   nil,   nil,   624,   nil,   624,   624,   624,   710,   710,
   710,   710,   710,   710,   nil,   nil,   nil,   710,   710,   nil,
   nil,   nil,   710,   nil,   710,   710,   710,   710,   710,   710,
   710,   nil,   nil,   nil,   nil,   nil,   710,   710,   710,   710,
   710,   710,   710,   nil,   nil,   710,   nil,   nil,   nil,   nil,
   nil,   710,   710,   710,   710,   710,   710,   710,   710,   710,
   710,   710,   710,   nil,   710,   710,   710,   nil,   710,   710,
   710,   710,   710,   838,   838,   838,   838,   838,   838,   838,
   838,   838,   838,   838,   nil,   838,   838,   nil,   nil,   838,
   838,   nil,   710,   nil,   nil,   710,   nil,   nil,   710,   710,
   nil,   nil,   710,   nil,   710,   838,   nil,   838,   710,   838,
   838,   nil,   838,   838,   838,   838,   838,   710,   838,   nil,
   nil,   nil,   710,   710,   710,   710,   nil,   710,   710,   710,
   710,   nil,   nil,   nil,   nil,   710,   710,   nil,   838,   nil,
   nil,   nil,   nil,   710,   nil,   710,   710,   710,   714,   714,
   714,   714,   714,   714,   nil,   nil,   nil,   714,   714,   nil,
   nil,   nil,   714,   nil,   714,   714,   714,   714,   714,   714,
   714,   nil,   nil,   nil,   nil,   nil,   714,   714,   714,   714,
   714,   714,   714,   nil,   nil,   714,   nil,   nil,   nil,   nil,
   nil,   714,   714,   714,   714,   714,   714,   714,   714,   714,
   714,   714,   714,   nil,   714,   714,   714,   nil,   714,   714,
   714,   714,   714,   841,   841,   841,   841,   841,   841,   841,
   841,   841,   841,   841,   nil,   841,   841,   nil,   nil,   841,
   841,   nil,   714,   nil,   nil,   714,   nil,   nil,   714,   714,
   nil,   nil,   714,   nil,   714,   841,   nil,   841,   714,   841,
   841,   nil,   841,   841,   841,   841,   841,   714,   841,   nil,
   nil,   nil,   714,   714,   714,   714,   nil,   714,   714,   714,
   714,   nil,   nil,   nil,   nil,   714,   714,   nil,   841,   nil,
   nil,   nil,   nil,   714,   nil,   714,   714,   714,   724,   724,
   724,   724,   724,   724,   nil,   nil,   nil,   724,   724,   nil,
   nil,   nil,   724,   nil,   724,   724,   724,   724,   724,   724,
   724,   nil,   nil,   nil,   nil,   nil,   724,   724,   724,   724,
   724,   724,   724,   nil,   nil,   724,   nil,   nil,   nil,   nil,
   nil,   724,   724,   724,   724,   724,   724,   724,   724,   724,
   724,   724,   724,   nil,   724,   724,   724,   nil,   724,   724,
   724,   724,   724,   450,   450,   450,   450,   450,   450,   450,
   450,   450,   450,   450,   nil,   450,   450,   nil,   nil,   450,
   450,   nil,   724,   nil,   nil,   724,   nil,   nil,   724,   724,
   nil,   nil,   724,   nil,   724,   450,   nil,   450,   724,   450,
   450,   nil,   450,   450,   450,   450,   450,   724,   450,   nil,
   nil,   nil,   724,   724,   724,   724,   nil,   724,   724,   724,
   724,   nil,   nil,   nil,   nil,   724,   724,   nil,  1010,   nil,
  1010,  1010,  1010,   724,  1010,   724,   724,   724,   772,   772,
   772,   772,   772,   772,   nil,   nil,   nil,   772,   772,   nil,
   nil,   nil,   772,   nil,   772,   772,   772,   772,   772,   772,
   772,   nil,   nil,   nil,   nil,  1010,   772,   772,   772,   772,
   772,   772,   772,   nil,   nil,   772,   nil,   nil,   nil,   nil,
   nil,   772,   772,   772,   772,   772,   772,   772,   772,   772,
   772,   772,   772,   nil,   772,   772,   772,   nil,   772,   772,
   772,   772,   772,   451,   451,   451,   451,   451,   451,   451,
   451,   451,   451,   451,   nil,   451,   451,   nil,   nil,   451,
   451,   nil,   772,   nil,   nil,   772,   nil,   nil,   772,   772,
   nil,   nil,   772,   nil,   772,   451,   nil,   451,   772,   451,
   451,   nil,   451,   451,   451,   451,   451,   772,   451,   nil,
   nil,   nil,   772,   772,   772,   772,   nil,   772,   772,   772,
   772,   nil,   nil,   nil,   nil,   772,   772,   nil,   nil,   nil,
   nil,   nil,   nil,   772,   nil,   772,   772,   772,   785,   785,
   785,   785,   785,   785,   nil,   nil,   nil,   785,   785,   nil,
   nil,   nil,   785,   nil,   785,   785,   785,   785,   785,   785,
   785,   nil,   nil,   nil,   nil,   nil,   785,   785,   785,   785,
   785,   785,   785,   nil,   nil,   785,   nil,   nil,   nil,   nil,
   nil,   785,   785,   785,   785,   785,   785,   785,   785,   785,
   785,   785,   785,   nil,   785,   785,   785,   nil,   785,   785,
   785,   785,   785,   461,   461,   461,   461,   461,   461,   461,
   nil,   nil,   461,   461,   nil,   nil,   nil,   nil,   nil,   461,
   461,   nil,   785,   nil,   nil,   785,   nil,   nil,   785,   785,
   nil,   nil,   785,   nil,   785,   461,   nil,   461,   785,   461,
   461,   nil,   461,   461,   461,   461,   461,   785,   461,   nil,
   nil,   nil,   785,   785,   785,   785,   nil,   785,   785,   785,
   785,   nil,   nil,   nil,   nil,   785,   785,   nil,   nil,   nil,
   nil,   nil,   nil,   785,   nil,   785,   785,   785,   819,   819,
   819,   819,   819,   819,   nil,   nil,   nil,   819,   819,   nil,
   nil,   nil,   819,   nil,   819,   819,   819,   819,   819,   819,
   819,   nil,   nil,   nil,   nil,   nil,   819,   819,   819,   819,
   819,   819,   819,   nil,   nil,   819,   nil,   nil,   nil,   nil,
   nil,   819,   819,   819,   819,   819,   819,   819,   819,   819,
   819,   819,   819,   nil,   819,   819,   819,   nil,   819,   819,
   819,   819,   819,   464,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   464,
   464,   nil,   819,   nil,   nil,   819,   nil,   nil,   819,   819,
   nil,   nil,   819,   nil,   819,   464,   nil,   464,   819,   464,
   464,   nil,   464,   464,   nil,   nil,   464,   819,   464,   nil,
   nil,   nil,   819,   819,   819,   819,   nil,   819,   819,   819,
   819,   nil,   nil,   nil,   nil,   819,   819,   nil,   nil,   nil,
   nil,   nil,   nil,   819,   nil,   819,   819,   819,   820,   820,
   820,   820,   820,   820,   nil,   nil,   nil,   820,   820,   nil,
   nil,   nil,   820,   nil,   820,   820,   820,   820,   820,   820,
   820,   nil,   nil,   nil,   nil,   nil,   820,   820,   820,   820,
   820,   820,   820,   nil,   nil,   820,   nil,   nil,   nil,   nil,
   nil,   820,   820,   820,   820,   820,   820,   820,   820,   820,
   820,   820,   820,   nil,   820,   820,   820,   nil,   820,   820,
   820,   820,   820,   465,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   465,
   465,   nil,   820,   nil,   nil,   820,   nil,   nil,   820,   820,
   nil,   nil,   820,   nil,   820,   465,   nil,   465,   820,   465,
   465,   nil,   465,   465,   nil,   nil,   465,   820,   465,   nil,
   nil,   nil,   820,   820,   820,   820,   nil,   820,   820,   820,
   820,   nil,   nil,   nil,   nil,   820,   820,   nil,   nil,   nil,
   nil,   nil,   nil,   820,   nil,   820,   820,   820,   823,   823,
   823,   823,   823,   823,   nil,   nil,   nil,   823,   823,   nil,
   nil,   nil,   823,   nil,   823,   823,   823,   823,   823,   823,
   823,   nil,   nil,   nil,   nil,   nil,   823,   823,   823,   823,
   823,   823,   823,   nil,   nil,   823,   nil,   nil,   nil,   nil,
   nil,   823,   823,   823,   823,   823,   823,   823,   823,   823,
   823,   823,   823,   nil,   823,   823,   823,   nil,   823,   823,
   823,   823,   823,   466,   466,   466,   466,   466,   466,   466,
   nil,   nil,   466,   466,   nil,   nil,   nil,   nil,   nil,   466,
   466,   nil,   823,   nil,   nil,   823,   nil,   nil,   823,   823,
   nil,   nil,   823,   nil,   823,   466,   nil,   466,   823,   466,
   466,   nil,   466,   466,   466,   466,   466,   823,   466,   nil,
   nil,   nil,   823,   823,   823,   823,   nil,   823,   823,   823,
   823,   nil,   nil,   nil,   nil,   823,   823,   nil,   nil,   nil,
   nil,   nil,   nil,   823,   nil,   823,   823,   823,   829,   829,
   829,   829,   829,   829,   nil,   nil,   nil,   829,   829,   nil,
   nil,   nil,   829,   nil,   829,   829,   829,   829,   829,   829,
   829,   nil,   nil,   nil,   nil,   nil,   829,   829,   829,   829,
   829,   829,   829,   nil,   nil,   829,   nil,   nil,   nil,   nil,
   nil,   829,   829,   829,   829,   829,   829,   829,   829,   829,
   829,   829,   829,   nil,   829,   829,   829,   nil,   829,   829,
   829,   829,   829,   467,   467,   467,   467,   467,   467,   467,
   nil,   nil,   467,   467,   nil,   nil,   nil,   nil,   nil,   467,
   467,   nil,   829,   nil,   nil,   829,   nil,   nil,   829,   829,
   nil,   nil,   829,   nil,   829,   467,   nil,   467,   829,   467,
   467,   nil,   467,   467,   467,   467,   467,   829,   467,   nil,
   nil,   nil,   829,   829,   829,   829,   nil,   829,   829,   829,
   829,   nil,   nil,   nil,   nil,   829,   829,   nil,   nil,   nil,
   nil,   nil,   nil,   829,   nil,   829,   829,   829,   862,   862,
   862,   862,   862,   862,   nil,   nil,   nil,   862,   862,   nil,
   nil,   nil,   862,   nil,   862,   862,   862,   862,   862,   862,
   862,   nil,   nil,   nil,   nil,   nil,   862,   862,   862,   862,
   862,   862,   862,   nil,   nil,   862,   nil,   nil,   nil,   nil,
   nil,   862,   862,   862,   862,   862,   862,   862,   862,   862,
   862,   862,   862,   nil,   862,   862,   862,   nil,   862,   862,
   862,   862,   862,   468,   468,   468,   468,   468,   468,   468,
   nil,   nil,   468,   468,   nil,   nil,   nil,   nil,   nil,   468,
   468,   nil,   862,   nil,   nil,   862,   nil,   nil,   862,   862,
   nil,   nil,   862,   nil,   862,   468,   nil,   468,   862,   468,
   468,   nil,   468,   468,   468,   468,   468,   862,   468,   nil,
   nil,   nil,   862,   862,   862,   862,   nil,   862,   862,   862,
   862,   nil,   nil,   nil,   nil,   862,   862,   nil,   nil,   nil,
   nil,   nil,   nil,   862,   nil,   862,   862,   862,   926,   926,
   926,   926,   926,   926,   nil,   nil,   nil,   926,   926,   nil,
   nil,   nil,   926,   nil,   926,   926,   926,   926,   926,   926,
   926,   nil,   nil,   nil,   nil,   nil,   926,   926,   926,   926,
   926,   926,   926,   nil,   nil,   926,   nil,   nil,   nil,   nil,
   nil,   926,   926,   926,   926,   926,   926,   926,   926,   926,
   926,   926,   926,   nil,   926,   926,   926,   nil,   926,   926,
   926,   926,   926,   469,   469,   469,   469,   469,   469,   469,
   nil,   nil,   469,   469,   nil,   nil,   nil,   nil,   nil,   469,
   469,   nil,   926,   nil,   nil,   926,   nil,   nil,   926,   926,
   nil,   nil,   926,   nil,   926,   469,   nil,   469,   926,   469,
   469,   nil,   469,   469,   469,   469,   469,   926,   469,   nil,
   nil,   nil,   926,   926,   926,   926,   nil,   926,   926,   926,
   926,   nil,   nil,   nil,   nil,   926,   926,   nil,   nil,   nil,
   nil,   nil,   nil,   926,   nil,   926,   926,   926,   933,   933,
   933,   933,   933,   933,   nil,   nil,   nil,   933,   933,   nil,
   nil,   nil,   933,   nil,   933,   933,   933,   933,   933,   933,
   933,   nil,   nil,   nil,   nil,   nil,   933,   933,   933,   933,
   933,   933,   933,   nil,   nil,   933,   nil,   nil,   nil,   nil,
   nil,   933,   933,   933,   933,   933,   933,   933,   933,   933,
   933,   933,   933,   nil,   933,   933,   933,   nil,   933,   933,
   933,   933,   933,   470,   470,   470,   470,   470,   470,   470,
   nil,   nil,   470,   470,   nil,   nil,   nil,   nil,   nil,   470,
   470,   nil,   933,   nil,   nil,   933,   nil,   nil,   933,   933,
   nil,   nil,   933,   nil,   933,   470,   nil,   470,   933,   470,
   470,   nil,   470,   470,   470,   470,   470,   933,   470,   nil,
   nil,   nil,   933,   933,   933,   933,   nil,   933,   933,   933,
   933,   nil,   nil,   nil,   nil,   933,   933,   nil,   nil,   nil,
   nil,   nil,   nil,   933,   nil,   933,   933,   933,   934,   934,
   934,   934,   934,   934,   nil,   nil,   nil,   934,   934,   nil,
   nil,   nil,   934,   nil,   934,   934,   934,   934,   934,   934,
   934,   nil,   nil,   nil,   nil,   nil,   934,   934,   934,   934,
   934,   934,   934,   nil,   nil,   934,   nil,   nil,   nil,   nil,
   nil,   934,   934,   934,   934,   934,   934,   934,   934,   934,
   934,   934,   934,   nil,   934,   934,   934,   nil,   934,   934,
   934,   934,   934,   473,   473,   473,   473,   473,   473,   473,
   nil,   nil,   473,   473,   nil,   nil,   nil,   nil,   nil,   473,
   473,   nil,   934,   nil,   nil,   934,   nil,   nil,   934,   934,
   nil,   nil,   934,   nil,   934,   473,   nil,   473,   934,   473,
   473,   nil,   473,   473,   473,   473,   473,   934,   473,   nil,
   nil,   nil,   934,   934,   934,   934,   nil,   934,   934,   934,
   934,   nil,   nil,   nil,   nil,   934,   934,   nil,   nil,   nil,
   nil,   nil,   nil,   934,   nil,   934,   934,   934,   951,   951,
   951,   951,   951,   951,   nil,   nil,   nil,   951,   951,   nil,
   nil,   nil,   951,   nil,   951,   951,   951,   951,   951,   951,
   951,   nil,   nil,   nil,   nil,   nil,   951,   951,   951,   951,
   951,   951,   951,   nil,   nil,   951,   nil,   nil,   nil,   nil,
   nil,   951,   951,   951,   951,   951,   951,   951,   951,   951,
   951,   951,   951,   nil,   951,   951,   951,   nil,   951,   951,
   951,   951,   951,   474,   474,   474,   474,   474,   474,   474,
   474,   nil,   474,   474,   nil,   nil,   nil,   nil,   nil,   474,
   474,   nil,   951,   nil,   nil,   951,   nil,   nil,   951,   951,
   nil,   nil,   951,   nil,   951,   474,   nil,   474,   951,   474,
   474,   nil,   474,   474,   474,   474,   474,   951,   474,   nil,
   nil,   nil,   951,   951,   951,   951,   nil,   951,   951,   951,
   951,   nil,   nil,   nil,   nil,   951,   951,   nil,   nil,   nil,
   nil,   nil,   nil,   951,   nil,   951,   951,   951,   957,   957,
   957,   957,   957,   957,   nil,   nil,   nil,   957,   957,   nil,
   nil,   nil,   957,   nil,   957,   957,   957,   957,   957,   957,
   957,   nil,   nil,   nil,   nil,   nil,   957,   957,   957,   957,
   957,   957,   957,   nil,   nil,   957,   nil,   nil,   nil,   nil,
   nil,   957,   957,   957,   957,   957,   957,   957,   957,   957,
   957,   957,   957,   nil,   957,   957,   957,   nil,   957,   957,
   957,   957,   957,   459,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   459,
   459,   nil,   957,   nil,   nil,   957,   nil,   nil,   957,   957,
   nil,   nil,   957,   nil,   957,   459,   nil,   459,   957,   459,
   459,   nil,   459,   459,   nil,   nil,   nil,   957,   nil,   nil,
   nil,   nil,   957,   957,   957,   957,   nil,   957,   957,   957,
   957,   nil,   nil,   nil,   nil,   957,   957,   nil,   nil,   nil,
   nil,   nil,   nil,   957,   nil,   957,   957,   957,   959,   959,
   959,   959,   959,   959,   nil,   nil,   nil,   959,   959,   nil,
   nil,   nil,   959,   nil,   959,   959,   959,   959,   959,   959,
   959,   nil,   nil,   nil,   nil,   nil,   959,   959,   959,   959,
   959,   959,   959,   nil,   nil,   959,   nil,   nil,   nil,   nil,
   nil,   959,   959,   959,   959,   959,   959,   959,   959,   959,
   959,   959,   959,   nil,   959,   959,   959,   nil,   959,   959,
   959,   959,   959,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   959,   nil,   nil,   959,   nil,   nil,   959,   959,
   nil,   nil,   959,   nil,   959,   nil,   nil,   nil,   959,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   959,   nil,   nil,
   nil,   nil,   959,   959,   959,   959,   nil,   959,   959,   959,
   959,   nil,   nil,   nil,   nil,   959,   959,   nil,   nil,   nil,
   nil,   nil,   nil,   959,   nil,   959,   959,   959,     5,     5,
     5,     5,     5,   nil,   nil,   nil,     5,     5,   nil,   nil,
   nil,     5,   nil,     5,     5,     5,     5,     5,     5,     5,
   nil,   nil,   nil,   nil,   nil,     5,     5,     5,     5,     5,
     5,     5,   nil,   nil,     5,   nil,   nil,   nil,   nil,   nil,
     5,     5,     5,     5,     5,     5,     5,     5,     5,     5,
     5,     5,   nil,     5,     5,     5,   nil,     5,     5,     5,
     5,     5,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,     5,   nil,   nil,     5,   nil,   nil,     5,     5,   nil,
   nil,     5,   nil,     5,   nil,   nil,   nil,     5,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,     5,   nil,   nil,   nil,
   nil,     5,     5,     5,     5,   nil,     5,     5,     5,     5,
   nil,   nil,   nil,   nil,     5,     5,   nil,   nil,   nil,    20,
    20,    20,     5,    20,     5,     5,     5,    20,    20,   nil,
   nil,   nil,    20,   nil,    20,    20,    20,    20,    20,    20,
    20,   nil,   nil,   nil,   nil,   nil,    20,    20,    20,    20,
    20,    20,    20,   nil,   nil,    20,   nil,   nil,   nil,   nil,
   nil,   nil,    20,   nil,   nil,    20,    20,    20,    20,    20,
    20,    20,    20,   nil,    20,    20,    20,   nil,    20,    20,
    20,    20,    20,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    20,   nil,   nil,    20,   nil,   nil,    20,    20,
   nil,   nil,    20,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    20,    20,    20,    20,   nil,    20,    20,    20,
    20,   nil,   nil,   nil,   nil,    20,    20,   nil,   nil,   nil,
    29,    29,    29,    20,    29,    20,    20,    20,    29,    29,
   nil,   nil,   nil,    29,   nil,    29,    29,    29,    29,    29,
    29,    29,   nil,   nil,   nil,   nil,   nil,    29,    29,    29,
    29,    29,    29,    29,   nil,   nil,    29,   nil,   nil,   nil,
   nil,   nil,   nil,    29,   nil,   nil,    29,    29,    29,    29,
    29,    29,    29,    29,    29,    29,    29,    29,   nil,    29,
    29,    29,    29,    29,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    29,   nil,   nil,    29,   nil,   nil,    29,
    29,   nil,   nil,    29,   nil,    29,   nil,    29,   nil,    29,
   nil,   nil,    29,   nil,   nil,   nil,   nil,   nil,    29,   nil,
   nil,   nil,   nil,    29,    29,    29,    29,   nil,    29,    29,
    29,    29,   nil,   nil,   nil,   nil,    29,    29,   nil,   nil,
   nil,    30,    30,    30,    29,    30,    29,    29,    29,    30,
    30,   nil,   nil,   nil,    30,   nil,    30,    30,    30,    30,
    30,    30,    30,   nil,   nil,   nil,   nil,   nil,    30,    30,
    30,    30,    30,    30,    30,   nil,   nil,    30,   nil,   nil,
   nil,   nil,   nil,   nil,    30,   nil,   nil,    30,    30,    30,
    30,    30,    30,    30,    30,    30,    30,    30,    30,   nil,
    30,    30,    30,    30,    30,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    30,   nil,   nil,    30,   nil,   nil,
    30,    30,   nil,   nil,    30,   nil,    30,   nil,    30,   nil,
    30,   nil,   nil,    30,   nil,   nil,   nil,   nil,   nil,    30,
   nil,   nil,   nil,   nil,    30,    30,    30,    30,   nil,    30,
    30,    30,    30,   nil,   nil,   nil,   nil,    30,    30,   nil,
   nil,   nil,    31,    31,    31,    30,    31,    30,    30,    30,
    31,    31,   nil,   nil,   nil,    31,   nil,    31,    31,    31,
    31,    31,    31,    31,   nil,   nil,   nil,   nil,   nil,    31,
    31,    31,    31,    31,    31,    31,   nil,   nil,    31,   nil,
   nil,   nil,   nil,   nil,   nil,    31,   nil,   nil,    31,    31,
    31,    31,    31,    31,    31,    31,    31,    31,    31,    31,
   nil,    31,    31,    31,    31,    31,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    31,   nil,   nil,    31,   nil,
   nil,    31,    31,   nil,   nil,    31,   nil,    31,   nil,    31,
   nil,    31,   nil,   nil,    31,   nil,   nil,   nil,   nil,   nil,
    31,   nil,   nil,   nil,   nil,    31,    31,    31,    31,   nil,
    31,    31,    31,    31,   nil,   nil,   nil,   nil,    31,    31,
   nil,   nil,   nil,    34,    34,    34,    31,    34,    31,    31,
    31,    34,    34,   nil,   nil,   nil,    34,   nil,    34,    34,
    34,    34,    34,    34,    34,   nil,   nil,   nil,   nil,   nil,
    34,    34,    34,    34,    34,    34,    34,   nil,   nil,    34,
   nil,   nil,   nil,   nil,   nil,   nil,    34,   nil,   nil,    34,
    34,    34,    34,    34,    34,    34,    34,   nil,    34,    34,
    34,   nil,    34,    34,   nil,   nil,    34,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    34,   nil,   nil,    34,
   nil,   nil,    34,    34,   nil,   nil,    34,   nil,    34,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    34,    34,    34,    34,
   nil,    34,    34,    34,    34,   nil,   nil,   nil,   nil,    34,
    34,   nil,   nil,   nil,    35,    35,    35,    34,    35,    34,
    34,    34,    35,    35,   nil,   nil,   nil,    35,   nil,    35,
    35,    35,    35,    35,    35,    35,   nil,   nil,   nil,   nil,
   nil,    35,    35,    35,    35,    35,    35,    35,   nil,   nil,
    35,   nil,   nil,   nil,   nil,   nil,   nil,    35,   nil,   nil,
    35,    35,    35,    35,    35,    35,    35,    35,   nil,    35,
    35,    35,   nil,    35,    35,   nil,   nil,    35,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,   nil,   nil,
    35,   nil,   nil,    35,    35,   nil,   nil,    35,   nil,   nil,
   802,   nil,   802,   802,   802,   802,   802,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   802,   nil,    35,    35,    35,
    35,   nil,    35,    35,    35,    35,   nil,   nil,   nil,   nil,
    35,    35,   nil,   nil,   nil,    35,   nil,   802,    35,   nil,
    35,    35,    35,    42,    42,    42,   nil,    42,   802,   802,
   nil,    42,    42,   802,   nil,   nil,    42,   nil,    42,    42,
    42,    42,    42,    42,    42,   nil,   nil,   nil,   nil,   nil,
    42,    42,    42,    42,    42,    42,    42,   nil,   nil,    42,
   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,   nil,    42,
    42,    42,    42,    42,    42,    42,    42,   nil,    42,    42,
    42,   nil,    42,    42,    42,    42,    42,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,   nil,    42,
   nil,   nil,    42,    42,   nil,   nil,    42,   nil,   nil,   nil,
   nil,   nil,    42,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    42,   nil,   nil,   nil,   nil,    42,    42,    42,    42,
   nil,    42,    42,    42,    42,   nil,   nil,   nil,   nil,    42,
    42,   nil,   nil,   nil,    43,    43,    43,    42,    43,    42,
    42,    42,    43,    43,   nil,   nil,   nil,    43,   nil,    43,
    43,    43,    43,    43,    43,    43,   nil,   nil,   nil,   nil,
   nil,    43,    43,    43,    43,    43,    43,    43,   nil,   nil,
    43,   nil,   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,
    43,    43,    43,    43,    43,    43,    43,    43,   nil,    43,
    43,    43,   nil,    43,    43,    43,    43,    43,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,
    43,   nil,   nil,    43,    43,   nil,   nil,    43,   nil,   nil,
   nil,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    43,   nil,   nil,   nil,   nil,    43,    43,    43,
    43,   nil,    43,    43,    43,    43,   nil,   nil,   nil,   nil,
    43,    43,   nil,   nil,   nil,    44,    44,    44,    43,    44,
    43,    43,    43,    44,    44,   nil,   nil,   nil,    44,   nil,
    44,    44,    44,    44,    44,    44,    44,   nil,   nil,   nil,
   nil,   nil,    44,    44,    44,    44,    44,    44,    44,   nil,
   nil,    44,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    44,    44,    44,    44,    44,    44,    44,    44,   nil,
    44,    44,    44,   nil,    44,    44,    44,    44,    44,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    44,   nil,
   nil,    44,   nil,   nil,    44,    44,   nil,   nil,    44,   nil,
   nil,   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    44,   nil,   nil,   nil,   nil,    44,    44,
    44,    44,   nil,    44,    44,    44,    44,   nil,   nil,   nil,
   nil,    44,    44,   nil,   nil,   nil,    59,    59,    59,    44,
    59,    44,    44,    44,    59,    59,   nil,   nil,   nil,    59,
   nil,    59,    59,    59,    59,    59,    59,    59,   nil,   nil,
   nil,   nil,   nil,    59,    59,    59,    59,    59,    59,    59,
   nil,   nil,    59,   nil,   nil,   nil,   nil,   nil,   nil,    59,
   nil,   nil,    59,    59,    59,    59,    59,    59,    59,    59,
    59,    59,    59,    59,   nil,    59,    59,    59,    59,    59,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    59,
   nil,   nil,    59,   nil,   nil,    59,    59,   nil,   nil,    59,
   nil,    59,   nil,   nil,   nil,    59,   nil,   nil,    59,   nil,
   nil,   nil,   nil,   nil,    59,   nil,   nil,   nil,   nil,    59,
    59,    59,    59,   nil,    59,    59,    59,    59,   nil,   nil,
   nil,   nil,    59,    59,   nil,   nil,   nil,    60,    60,    60,
    59,    60,    59,    59,    59,    60,    60,   nil,   nil,   nil,
    60,   nil,    60,    60,    60,    60,    60,    60,    60,   nil,
   nil,   nil,   nil,   nil,    60,    60,    60,    60,    60,    60,
    60,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,   nil,
    60,   nil,   nil,    60,    60,    60,    60,    60,    60,    60,
    60,    60,    60,    60,    60,   nil,    60,    60,    60,    60,
    60,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    60,   nil,   nil,    60,   nil,   nil,    60,    60,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,    60,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    60,   nil,   nil,   nil,   nil,
    60,    60,    60,    60,   nil,    60,    60,    60,    60,   nil,
   nil,   nil,   nil,    60,    60,   nil,   nil,   nil,    63,    63,
    63,    60,    63,    60,    60,    60,    63,    63,   nil,   nil,
   nil,    63,   nil,    63,    63,    63,    63,    63,    63,    63,
   nil,   nil,   nil,   nil,   nil,    63,    63,    63,    63,    63,
    63,    63,   nil,   nil,    63,   nil,   nil,   nil,   nil,   nil,
   nil,    63,   nil,   nil,    63,    63,    63,    63,    63,    63,
    63,    63,   nil,    63,    63,    63,   nil,    63,    63,    63,
    63,    63,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    63,   nil,   nil,    63,   nil,   nil,    63,    63,   nil,
   nil,    63,   nil,   nil,   nil,   nil,   nil,    63,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    63,   nil,   nil,   nil,
   nil,    63,    63,    63,    63,   nil,    63,    63,    63,    63,
   nil,   nil,   nil,   nil,    63,    63,   nil,   nil,   nil,    64,
    64,    64,    63,    64,    63,    63,    63,    64,    64,   nil,
   nil,   nil,    64,   nil,    64,    64,    64,    64,    64,    64,
    64,   nil,   nil,   nil,   nil,   nil,    64,    64,    64,    64,
    64,    64,    64,   nil,   nil,    64,   nil,   nil,   nil,   nil,
   nil,   nil,    64,   nil,   nil,    64,    64,    64,    64,    64,
    64,    64,    64,   nil,    64,    64,    64,   nil,    64,    64,
    64,    64,    64,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    64,   nil,   nil,    64,   nil,   nil,    64,    64,
   nil,   nil,    64,   nil,   nil,   nil,   nil,   nil,    64,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    64,   nil,   nil,
   nil,   nil,    64,    64,    64,    64,   nil,    64,    64,    64,
    64,   nil,   nil,   nil,   nil,    64,    64,   nil,   nil,   nil,
    67,    67,    67,    64,    67,    64,    64,    64,    67,    67,
   nil,   nil,   nil,    67,   nil,    67,    67,    67,    67,    67,
    67,    67,   nil,   nil,   nil,   nil,   nil,    67,    67,    67,
    67,    67,    67,    67,   nil,   nil,    67,   nil,   nil,   nil,
   nil,   nil,   nil,    67,   nil,   nil,    67,    67,    67,    67,
    67,    67,    67,    67,   nil,    67,    67,    67,   nil,    67,
    67,    67,    67,    67,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    67,   nil,   nil,    67,   nil,   nil,    67,
    67,   nil,   nil,    67,   nil,   nil,   nil,   nil,   nil,    67,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    67,   nil,
   nil,   nil,   nil,    67,    67,    67,    67,   nil,    67,    67,
    67,    67,   nil,   nil,   nil,   nil,    67,    67,    67,   nil,
   nil,   nil,   nil,    67,    67,   nil,    67,    67,    67,    68,
    68,    68,   nil,    68,   nil,   nil,   nil,    68,    68,   nil,
   nil,   nil,    68,   nil,    68,    68,    68,    68,    68,    68,
    68,   nil,   nil,   nil,   nil,   nil,    68,    68,    68,    68,
    68,    68,    68,   nil,   nil,    68,   nil,   nil,   nil,   nil,
   nil,   nil,    68,   nil,   nil,    68,    68,    68,    68,    68,
    68,    68,    68,   nil,    68,    68,    68,   nil,    68,    68,
   nil,   nil,    68,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    68,   nil,   nil,    68,   nil,   nil,    68,    68,
   nil,   nil,    68,   nil,    68,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    68,    68,    68,    68,   nil,    68,    68,    68,
    68,   nil,   nil,   nil,   nil,    68,    68,   nil,   nil,   nil,
    69,    69,    69,    68,    69,    68,    68,    68,    69,    69,
   nil,   nil,   nil,    69,   nil,    69,    69,    69,    69,    69,
    69,    69,   nil,   nil,   nil,   nil,   nil,    69,    69,    69,
    69,    69,    69,    69,   nil,   nil,    69,   nil,   nil,   nil,
   nil,   nil,   nil,    69,   nil,   nil,    69,    69,    69,    69,
    69,    69,    69,    69,   nil,    69,    69,    69,   nil,    69,
    69,   nil,   nil,    69,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    69,   nil,   nil,    69,   nil,   nil,    69,   nil,   nil,    69,
    69,   nil,   nil,    69,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    69,    69,    69,    69,   nil,    69,    69,
    69,    69,   nil,   nil,   nil,   nil,    69,    69,   nil,   nil,
   nil,    70,    70,    70,    69,    70,    69,    69,    69,    70,
    70,   nil,   nil,   nil,    70,   nil,    70,    70,    70,    70,
    70,    70,    70,   nil,   nil,   nil,   nil,   nil,    70,    70,
    70,    70,    70,    70,    70,   nil,   nil,    70,   nil,   nil,
   nil,   nil,   nil,   nil,    70,   nil,   nil,    70,    70,    70,
    70,    70,    70,    70,    70,   nil,    70,    70,    70,   nil,
    70,    70,   nil,   nil,    70,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    70,   nil,   nil,    70,   nil,   nil,
    70,    70,   nil,   nil,    70,   nil,   nil,   849,   nil,   849,
   849,   849,   849,   849,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   849,   nil,    70,    70,    70,    70,   nil,    70,
    70,    70,    70,   nil,   nil,   nil,   nil,    70,    70,   nil,
   nil,   nil,   nil,   nil,   849,    70,   nil,    70,    70,    70,
   113,   113,   113,   113,   113,   849,   849,   nil,   113,   113,
   849,   nil,   nil,   113,   nil,   113,   113,   113,   113,   113,
   113,   113,   nil,   nil,   nil,   nil,   nil,   113,   113,   113,
   113,   113,   113,   113,   nil,   nil,   113,   nil,   nil,   nil,
   nil,   nil,   113,   113,   113,   113,   113,   113,   113,   113,
   113,   113,   113,   113,   nil,   113,   113,   113,   nil,   113,
   113,   113,   113,   113,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   113,   nil,   nil,   113,   nil,   nil,   113,
   113,   nil,   nil,   113,   nil,   113,   nil,   nil,   nil,   113,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   113,   nil,
   nil,   nil,   nil,   113,   113,   113,   113,   nil,   113,   113,
   113,   113,   nil,   nil,   nil,   nil,   113,   113,   nil,   nil,
   nil,   nil,   nil,   113,   113,   nil,   113,   113,   113,   118,
   118,   118,   nil,   118,   nil,   nil,   nil,   118,   118,   nil,
   nil,   nil,   118,   nil,   118,   118,   118,   118,   118,   118,
   118,   nil,   nil,   nil,   nil,   nil,   118,   118,   118,   118,
   118,   118,   118,   nil,   nil,   118,   nil,   nil,   nil,   nil,
   nil,   nil,   118,   nil,   nil,   118,   118,   118,   118,   118,
   118,   118,   118,   nil,   118,   118,   118,   nil,   118,   118,
   118,   118,   118,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   118,   nil,   nil,   118,   nil,   nil,   118,   118,
   nil,   nil,   118,   nil,   nil,   nil,   nil,   nil,   118,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   118,   nil,   nil,
   nil,   nil,   118,   118,   118,   118,   nil,   118,   118,   118,
   118,   nil,   nil,   nil,   nil,   118,   118,   nil,   nil,   nil,
   119,   119,   119,   118,   119,   118,   118,   118,   119,   119,
   nil,   nil,   nil,   119,   nil,   119,   119,   119,   119,   119,
   119,   119,   nil,   nil,   nil,   nil,   nil,   119,   119,   119,
   119,   119,   119,   119,   nil,   nil,   119,   nil,   nil,   nil,
   nil,   nil,   nil,   119,   nil,   nil,   119,   119,   119,   119,
   119,   119,   119,   119,   nil,   119,   119,   119,   nil,   119,
   119,   119,   119,   119,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   119,   nil,   nil,   119,   nil,   nil,   119,
   119,   nil,   nil,   119,   nil,   nil,   nil,   nil,   nil,   119,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   119,   nil,
   nil,   nil,   nil,   119,   119,   119,   119,   nil,   119,   119,
   119,   119,   nil,   nil,   nil,   nil,   119,   119,   nil,   nil,
   nil,   120,   120,   120,   119,   120,   119,   119,   119,   120,
   120,   nil,   nil,   nil,   120,   nil,   120,   120,   120,   120,
   120,   120,   120,   nil,   nil,   nil,   nil,   nil,   120,   120,
   120,   120,   120,   120,   120,   nil,   nil,   120,   nil,   nil,
   nil,   nil,   nil,   nil,   120,   nil,   nil,   120,   120,   120,
   120,   120,   120,   120,   120,   nil,   120,   120,   120,   nil,
   120,   120,   120,   120,   120,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   120,   nil,   nil,   120,   nil,   nil,
   120,   120,   nil,   nil,   120,   nil,   nil,   nil,   nil,   nil,
   120,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   120,
   nil,   nil,   nil,   nil,   120,   120,   120,   120,   nil,   120,
   120,   120,   120,   nil,   nil,   nil,   nil,   120,   120,   nil,
   nil,   nil,   121,   121,   121,   120,   121,   120,   120,   120,
   121,   121,   nil,   nil,   nil,   121,   nil,   121,   121,   121,
   121,   121,   121,   121,   nil,   nil,   nil,   nil,   nil,   121,
   121,   121,   121,   121,   121,   121,   nil,   nil,   121,   nil,
   nil,   nil,   nil,   nil,   nil,   121,   nil,   nil,   121,   121,
   121,   121,   121,   121,   121,   121,   nil,   121,   121,   121,
   nil,   121,   121,   121,   121,   121,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   121,   nil,   nil,   121,   nil,
   nil,   121,   121,   nil,   nil,   121,   nil,   nil,   nil,   nil,
   nil,   121,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   121,   nil,   nil,   nil,   nil,   121,   121,   121,   121,   nil,
   121,   121,   121,   121,   nil,   nil,   nil,   nil,   121,   121,
   nil,   nil,   nil,   nil,   nil,   nil,   121,   nil,   121,   121,
   121,   122,   122,   122,   122,   122,   nil,   nil,   nil,   122,
   122,   nil,   nil,   nil,   122,   nil,   122,   122,   122,   122,
   122,   122,   122,   nil,   nil,   nil,   nil,   nil,   122,   122,
   122,   122,   122,   122,   122,   nil,   nil,   122,   nil,   nil,
   nil,   nil,   nil,   122,   122,   nil,   122,   122,   122,   122,
   122,   122,   122,   122,   122,   nil,   122,   122,   122,   nil,
   122,   122,   122,   122,   122,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   122,   nil,   nil,   122,   nil,   nil,
   122,   122,   nil,   nil,   122,   nil,   122,   nil,   nil,   nil,
   122,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   122,
   nil,   nil,   nil,   nil,   122,   122,   122,   122,   nil,   122,
   122,   122,   122,   nil,   nil,   nil,   nil,   122,   122,   nil,
   nil,   nil,   209,   209,   209,   122,   209,   122,   122,   122,
   209,   209,   nil,   nil,   nil,   209,   nil,   209,   209,   209,
   209,   209,   209,   209,   nil,   nil,   nil,   nil,   nil,   209,
   209,   209,   209,   209,   209,   209,   nil,   nil,   209,   nil,
   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,   209,   209,
   209,   209,   209,   209,   209,   209,   nil,   209,   209,   209,
   nil,   209,   209,   209,   209,   209,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   209,   nil,   nil,   209,   nil,
   nil,   209,   209,   nil,   nil,   209,   nil,   209,   nil,   nil,
   nil,   209,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   209,   nil,   nil,   nil,   nil,   209,   209,   209,   209,   nil,
   209,   209,   209,   209,   nil,   nil,   nil,   nil,   209,   209,
   nil,   nil,   nil,   210,   210,   210,   209,   210,   209,   209,
   209,   210,   210,   nil,   nil,   nil,   210,   nil,   210,   210,
   210,   210,   210,   210,   210,   nil,   nil,   nil,   nil,   nil,
   210,   210,   210,   210,   210,   210,   210,   nil,   nil,   210,
   nil,   nil,   nil,   nil,   nil,   nil,   210,   nil,   nil,   210,
   210,   210,   210,   210,   210,   210,   210,   nil,   210,   210,
   210,   nil,   210,   210,   210,   210,   210,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   210,   nil,   nil,   210,
   nil,   nil,   210,   210,   nil,   nil,   210,   nil,   nil,   nil,
   nil,   nil,   210,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   210,   nil,   nil,   nil,   nil,   210,   210,   210,   210,
   nil,   210,   210,   210,   210,   nil,   nil,   nil,   nil,   210,
   210,   nil,   nil,   nil,   211,   211,   211,   210,   211,   210,
   210,   210,   211,   211,   nil,   nil,   nil,   211,   nil,   211,
   211,   211,   211,   211,   211,   211,   nil,   nil,   nil,   nil,
   nil,   211,   211,   211,   211,   211,   211,   211,   nil,   nil,
   211,   nil,   nil,   nil,   nil,   nil,   nil,   211,   nil,   nil,
   211,   211,   211,   211,   211,   211,   211,   211,   211,   211,
   211,   211,   nil,   211,   211,   211,   211,   211,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   211,   nil,   nil,
   211,   nil,   nil,   211,   211,   nil,   nil,   211,   nil,   211,
   nil,   211,   nil,   211,   nil,   nil,   211,   nil,   nil,   nil,
   nil,   nil,   211,   nil,   nil,   nil,   nil,   211,   211,   211,
   211,   nil,   211,   211,   211,   211,   nil,   nil,   nil,   nil,
   211,   211,   nil,   nil,   nil,   216,   216,   216,   211,   216,
   211,   211,   211,   216,   216,   nil,   nil,   nil,   216,   nil,
   216,   216,   216,   216,   216,   216,   216,   nil,   nil,   nil,
   nil,   nil,   216,   216,   216,   216,   216,   216,   216,   nil,
   nil,   216,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   216,   216,   216,   216,   216,   216,   216,   216,   nil,
   216,   216,   216,   nil,   216,   216,   216,   216,   216,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   216,   nil,   nil,   216,   216,   nil,   nil,   216,   nil,
   nil,   nil,   nil,   nil,   216,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   nil,   nil,   216,   216,
   216,   216,   nil,   216,   216,   216,   216,   nil,   nil,   nil,
   nil,   216,   216,   nil,   nil,   nil,   217,   217,   217,   216,
   217,   216,   216,   216,   217,   217,   nil,   nil,   nil,   217,
   nil,   217,   217,   217,   217,   217,   217,   217,   nil,   nil,
   nil,   nil,   nil,   217,   217,   217,   217,   217,   217,   217,
   nil,   nil,   217,   nil,   nil,   nil,   nil,   nil,   nil,   217,
   nil,   nil,   217,   217,   217,   217,   217,   217,   217,   217,
   nil,   217,   217,   217,   nil,   217,   217,   217,   217,   217,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   217,
   nil,   nil,   217,   nil,   nil,   217,   217,   nil,   nil,   217,
   nil,   217,   nil,   nil,   nil,   217,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   217,   nil,   nil,   nil,   nil,   217,
   217,   217,   217,   nil,   217,   217,   217,   217,   nil,   nil,
   nil,   nil,   217,   217,   nil,   nil,   nil,   218,   218,   218,
   217,   218,   217,   217,   217,   218,   218,   nil,   nil,   nil,
   218,   nil,   218,   218,   218,   218,   218,   218,   218,   nil,
   nil,   nil,   nil,   nil,   218,   218,   218,   218,   218,   218,
   218,   nil,   nil,   218,   nil,   nil,   nil,   nil,   nil,   nil,
   218,   nil,   nil,   218,   218,   218,   218,   218,   218,   218,
   218,   nil,   218,   218,   218,   nil,   218,   218,   218,   218,
   218,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   218,   nil,   nil,   218,   nil,   nil,   218,   218,   nil,   nil,
   218,   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,   nil,   nil,
   218,   218,   218,   218,   nil,   218,   218,   218,   218,   nil,
   nil,   nil,   nil,   218,   218,   nil,   nil,   nil,   219,   219,
   219,   218,   219,   218,   218,   218,   219,   219,   nil,   nil,
   nil,   219,   nil,   219,   219,   219,   219,   219,   219,   219,
   nil,   nil,   nil,   nil,   nil,   219,   219,   219,   219,   219,
   219,   219,   nil,   nil,   219,   nil,   nil,   nil,   nil,   nil,
   nil,   219,   nil,   nil,   219,   219,   219,   219,   219,   219,
   219,   219,   nil,   219,   219,   219,   nil,   219,   219,   219,
   219,   219,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   219,   nil,   nil,   219,   nil,   nil,   219,   219,   nil,
   nil,   219,   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   219,   nil,   nil,   nil,
   nil,   219,   219,   219,   219,   nil,   219,   219,   219,   219,
   nil,   nil,   nil,   nil,   219,   219,   nil,   nil,   nil,   220,
   220,   220,   219,   220,   219,   219,   219,   220,   220,   nil,
   nil,   nil,   220,   nil,   220,   220,   220,   220,   220,   220,
   220,   nil,   nil,   nil,   nil,   nil,   220,   220,   220,   220,
   220,   220,   220,   nil,   nil,   220,   nil,   nil,   nil,   nil,
   nil,   nil,   220,   nil,   nil,   220,   220,   220,   220,   220,
   220,   220,   220,   nil,   220,   220,   220,   nil,   220,   220,
   220,   220,   220,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   220,   nil,   nil,   220,   nil,   nil,   220,   220,
   nil,   nil,   220,   nil,   nil,   nil,   nil,   nil,   220,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   220,   nil,   nil,
   nil,   nil,   220,   220,   220,   220,   nil,   220,   220,   220,
   220,   nil,   nil,   nil,   nil,   220,   220,   nil,   nil,   nil,
   221,   221,   221,   220,   221,   220,   220,   220,   221,   221,
   nil,   nil,   nil,   221,   nil,   221,   221,   221,   221,   221,
   221,   221,   nil,   nil,   nil,   nil,   nil,   221,   221,   221,
   221,   221,   221,   221,   nil,   nil,   221,   nil,   nil,   nil,
   nil,   nil,   nil,   221,   nil,   nil,   221,   221,   221,   221,
   221,   221,   221,   221,   nil,   221,   221,   221,   nil,   221,
   221,   221,   221,   221,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   221,   nil,   nil,   221,   nil,   nil,   221,
   221,   nil,   nil,   221,   nil,   nil,   nil,   nil,   nil,   221,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   221,   nil,
   nil,   nil,   nil,   221,   221,   221,   221,   nil,   221,   221,
   221,   221,   nil,   nil,   nil,   nil,   221,   221,   221,   nil,
   nil,   232,   232,   232,   221,   232,   221,   221,   221,   232,
   232,   nil,   nil,   nil,   232,   nil,   232,   232,   232,   232,
   232,   232,   232,   nil,   nil,   nil,   nil,   nil,   232,   232,
   232,   232,   232,   232,   232,   nil,   nil,   232,   nil,   nil,
   nil,   nil,   nil,   nil,   232,   nil,   nil,   232,   232,   232,
   232,   232,   232,   232,   232,   nil,   232,   232,   232,   nil,
   232,   232,   232,   232,   232,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   232,   nil,   nil,   232,   nil,   nil,
   232,   232,   nil,   nil,   232,   nil,   nil,   nil,   nil,   nil,
   232,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   232,
   nil,   nil,   nil,   nil,   232,   232,   232,   232,   nil,   232,
   232,   232,   232,   nil,   nil,   nil,   nil,   232,   232,   nil,
   nil,   nil,   235,   235,   235,   232,   235,   232,   232,   232,
   235,   235,   nil,   nil,   nil,   235,   nil,   235,   235,   235,
   235,   235,   235,   235,   nil,   nil,   nil,   nil,   nil,   235,
   235,   235,   235,   235,   235,   235,   nil,   nil,   235,   nil,
   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   235,   235,
   235,   235,   235,   235,   235,   235,   nil,   235,   235,   235,
   nil,   235,   235,   235,   235,   235,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,   235,   nil,
   nil,   235,   235,   nil,   nil,   235,   nil,   nil,   nil,   nil,
   nil,   235,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   235,   nil,   nil,   nil,   nil,   235,   235,   235,   235,   nil,
   235,   235,   235,   235,   nil,   nil,   nil,   nil,   235,   235,
   nil,   nil,   nil,   236,   236,   236,   235,   236,   235,   235,
   235,   236,   236,   nil,   nil,   nil,   236,   nil,   236,   236,
   236,   236,   236,   236,   236,   nil,   nil,   nil,   nil,   nil,
   236,   236,   236,   236,   236,   236,   236,   nil,   nil,   236,
   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   236,
   236,   236,   236,   236,   236,   236,   236,   nil,   236,   236,
   236,   nil,   236,   236,   236,   236,   236,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   236,
   nil,   nil,   236,   236,   nil,   nil,   236,   nil,   nil,   nil,
   nil,   nil,   236,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   236,   nil,   nil,   nil,   nil,   236,   236,   236,   236,
   nil,   236,   236,   236,   236,   nil,   nil,   nil,   nil,   236,
   236,   nil,   nil,   nil,   237,   237,   237,   236,   237,   236,
   236,   236,   237,   237,   nil,   nil,   nil,   237,   nil,   237,
   237,   237,   237,   237,   237,   237,   nil,   nil,   nil,   nil,
   nil,   237,   237,   237,   237,   237,   237,   237,   nil,   nil,
   237,   nil,   nil,   nil,   nil,   nil,   nil,   237,   nil,   nil,
   237,   237,   237,   237,   237,   237,   237,   237,   nil,   237,
   237,   237,   nil,   237,   237,   237,   237,   237,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   237,   nil,   nil,
   237,   nil,   nil,   237,   237,   nil,   nil,   237,   nil,   nil,
   nil,   nil,   nil,   237,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   237,   nil,   nil,   nil,   nil,   237,   237,   237,
   237,   nil,   237,   237,   237,   237,   nil,   nil,   nil,   nil,
   237,   237,   nil,   nil,   nil,   238,   238,   238,   237,   238,
   237,   237,   237,   238,   238,   nil,   nil,   nil,   238,   nil,
   238,   238,   238,   238,   238,   238,   238,   nil,   nil,   nil,
   nil,   nil,   238,   238,   238,   238,   238,   238,   238,   nil,
   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,   238,   nil,
   nil,   238,   238,   238,   238,   238,   238,   238,   238,   nil,
   238,   238,   238,   nil,   238,   238,   238,   238,   238,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   238,   nil,
   nil,   238,   nil,   nil,   238,   238,   nil,   nil,   238,   nil,
   nil,   nil,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   238,   nil,   nil,   nil,   nil,   238,   238,
   238,   238,   nil,   238,   238,   238,   238,   nil,   nil,   nil,
   nil,   238,   238,   nil,   nil,   nil,   239,   239,   239,   238,
   239,   238,   238,   238,   239,   239,   nil,   nil,   nil,   239,
   nil,   239,   239,   239,   239,   239,   239,   239,   nil,   nil,
   nil,   nil,   nil,   239,   239,   239,   239,   239,   239,   239,
   nil,   nil,   239,   nil,   nil,   nil,   nil,   nil,   nil,   239,
   nil,   nil,   239,   239,   239,   239,   239,   239,   239,   239,
   nil,   239,   239,   239,   nil,   239,   239,   239,   239,   239,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   239,
   nil,   nil,   239,   nil,   nil,   239,   239,   nil,   nil,   239,
   nil,   nil,   nil,   nil,   nil,   239,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   239,   nil,   nil,   nil,   nil,   239,
   239,   239,   239,   nil,   239,   239,   239,   239,   nil,   nil,
   nil,   nil,   239,   239,   nil,   nil,   nil,   240,   240,   240,
   239,   240,   239,   239,   239,   240,   240,   nil,   nil,   nil,
   240,   nil,   240,   240,   240,   240,   240,   240,   240,   nil,
   nil,   nil,   nil,   nil,   240,   240,   240,   240,   240,   240,
   240,   nil,   nil,   240,   nil,   nil,   nil,   nil,   nil,   nil,
   240,   nil,   nil,   240,   240,   240,   240,   240,   240,   240,
   240,   nil,   240,   240,   240,   nil,   240,   240,   240,   240,
   240,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   240,   nil,   nil,   240,   nil,   nil,   240,   240,   nil,   nil,
   240,   nil,   nil,   nil,   nil,   nil,   240,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   240,   nil,   nil,   nil,   nil,
   240,   240,   240,   240,   nil,   240,   240,   240,   240,   nil,
   nil,   nil,   nil,   240,   240,   nil,   nil,   nil,   241,   241,
   241,   240,   241,   240,   240,   240,   241,   241,   nil,   nil,
   nil,   241,   nil,   241,   241,   241,   241,   241,   241,   241,
   nil,   nil,   nil,   nil,   nil,   241,   241,   241,   241,   241,
   241,   241,   nil,   nil,   241,   nil,   nil,   nil,   nil,   nil,
   nil,   241,   nil,   nil,   241,   241,   241,   241,   241,   241,
   241,   241,   nil,   241,   241,   241,   nil,   241,   241,   241,
   241,   241,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   241,   nil,   nil,   241,   nil,   nil,   241,   241,   nil,
   nil,   241,   nil,   nil,   nil,   nil,   nil,   241,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   241,   nil,   nil,   nil,
   nil,   241,   241,   241,   241,   nil,   241,   241,   241,   241,
   nil,   nil,   nil,   nil,   241,   241,   nil,   nil,   nil,   242,
   242,   242,   241,   242,   241,   241,   241,   242,   242,   nil,
   nil,   nil,   242,   nil,   242,   242,   242,   242,   242,   242,
   242,   nil,   nil,   nil,   nil,   nil,   242,   242,   242,   242,
   242,   242,   242,   nil,   nil,   242,   nil,   nil,   nil,   nil,
   nil,   nil,   242,   nil,   nil,   242,   242,   242,   242,   242,
   242,   242,   242,   nil,   242,   242,   242,   nil,   242,   242,
   242,   242,   242,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   242,   nil,   nil,   242,   nil,   nil,   242,   242,
   nil,   nil,   242,   nil,   nil,   nil,   nil,   nil,   242,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   242,   nil,   nil,
   nil,   nil,   242,   242,   242,   242,   nil,   242,   242,   242,
   242,   nil,   nil,   nil,   nil,   242,   242,   nil,   nil,   nil,
   243,   243,   243,   242,   243,   242,   242,   242,   243,   243,
   nil,   nil,   nil,   243,   nil,   243,   243,   243,   243,   243,
   243,   243,   nil,   nil,   nil,   nil,   nil,   243,   243,   243,
   243,   243,   243,   243,   nil,   nil,   243,   nil,   nil,   nil,
   nil,   nil,   nil,   243,   nil,   nil,   243,   243,   243,   243,
   243,   243,   243,   243,   nil,   243,   243,   243,   nil,   243,
   243,   243,   243,   243,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   243,   nil,   nil,   243,   nil,   nil,   243,
   243,   nil,   nil,   243,   nil,   nil,   nil,   nil,   nil,   243,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   243,   nil,
   nil,   nil,   nil,   243,   243,   243,   243,   nil,   243,   243,
   243,   243,   nil,   nil,   nil,   nil,   243,   243,   nil,   nil,
   nil,   244,   244,   244,   243,   244,   243,   243,   243,   244,
   244,   nil,   nil,   nil,   244,   nil,   244,   244,   244,   244,
   244,   244,   244,   nil,   nil,   nil,   nil,   nil,   244,   244,
   244,   244,   244,   244,   244,   nil,   nil,   244,   nil,   nil,
   nil,   nil,   nil,   nil,   244,   nil,   nil,   244,   244,   244,
   244,   244,   244,   244,   244,   nil,   244,   244,   244,   nil,
   244,   244,   244,   244,   244,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   244,   nil,   nil,   244,   nil,   nil,
   244,   244,   nil,   nil,   244,   nil,   nil,   nil,   nil,   nil,
   244,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   244,
   nil,   nil,   nil,   nil,   244,   244,   244,   244,   nil,   244,
   244,   244,   244,   nil,   nil,   nil,   nil,   244,   244,   nil,
   nil,   nil,   245,   245,   245,   244,   245,   244,   244,   244,
   245,   245,   nil,   nil,   nil,   245,   nil,   245,   245,   245,
   245,   245,   245,   245,   nil,   nil,   nil,   nil,   nil,   245,
   245,   245,   245,   245,   245,   245,   nil,   nil,   245,   nil,
   nil,   nil,   nil,   nil,   nil,   245,   nil,   nil,   245,   245,
   245,   245,   245,   245,   245,   245,   nil,   245,   245,   245,
   nil,   245,   245,   245,   245,   245,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   245,   nil,   nil,   245,   nil,
   nil,   245,   245,   nil,   nil,   245,   nil,   nil,   nil,   nil,
   nil,   245,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   245,   nil,   nil,   nil,   nil,   245,   245,   245,   245,   nil,
   245,   245,   245,   245,   nil,   nil,   nil,   nil,   245,   245,
   nil,   nil,   nil,   246,   246,   246,   245,   246,   245,   245,
   245,   246,   246,   nil,   nil,   nil,   246,   nil,   246,   246,
   246,   246,   246,   246,   246,   nil,   nil,   nil,   nil,   nil,
   246,   246,   246,   246,   246,   246,   246,   nil,   nil,   246,
   nil,   nil,   nil,   nil,   nil,   nil,   246,   nil,   nil,   246,
   246,   246,   246,   246,   246,   246,   246,   nil,   246,   246,
   246,   nil,   246,   246,   246,   246,   246,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   246,   nil,   nil,   246,
   nil,   nil,   246,   246,   nil,   nil,   246,   nil,   nil,   nil,
   nil,   nil,   246,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   246,   nil,   nil,   nil,   nil,   246,   246,   246,   246,
   nil,   246,   246,   246,   246,   nil,   nil,   nil,   nil,   246,
   246,   nil,   nil,   nil,   247,   247,   247,   246,   247,   246,
   246,   246,   247,   247,   nil,   nil,   nil,   247,   nil,   247,
   247,   247,   247,   247,   247,   247,   nil,   nil,   nil,   nil,
   nil,   247,   247,   247,   247,   247,   247,   247,   nil,   nil,
   247,   nil,   nil,   nil,   nil,   nil,   nil,   247,   nil,   nil,
   247,   247,   247,   247,   247,   247,   247,   247,   nil,   247,
   247,   247,   nil,   247,   247,   247,   247,   247,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   247,   nil,   nil,
   247,   nil,   nil,   247,   247,   nil,   nil,   247,   nil,   nil,
   nil,   nil,   nil,   247,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   247,   nil,   nil,   nil,   nil,   247,   247,   247,
   247,   nil,   247,   247,   247,   247,   nil,   nil,   nil,   nil,
   247,   247,   nil,   nil,   nil,   248,   248,   248,   247,   248,
   247,   247,   247,   248,   248,   nil,   nil,   nil,   248,   nil,
   248,   248,   248,   248,   248,   248,   248,   nil,   nil,   nil,
   nil,   nil,   248,   248,   248,   248,   248,   248,   248,   nil,
   nil,   248,   nil,   nil,   nil,   nil,   nil,   nil,   248,   nil,
   nil,   248,   248,   248,   248,   248,   248,   248,   248,   nil,
   248,   248,   248,   nil,   248,   248,   248,   248,   248,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   248,   nil,
   nil,   248,   nil,   nil,   248,   248,   nil,   nil,   248,   nil,
   nil,   nil,   nil,   nil,   248,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   248,   nil,   nil,   nil,   nil,   248,   248,
   248,   248,   nil,   248,   248,   248,   248,   nil,   nil,   nil,
   nil,   248,   248,   nil,   nil,   nil,   249,   249,   249,   248,
   249,   248,   248,   248,   249,   249,   nil,   nil,   nil,   249,
   nil,   249,   249,   249,   249,   249,   249,   249,   nil,   nil,
   nil,   nil,   nil,   249,   249,   249,   249,   249,   249,   249,
   nil,   nil,   249,   nil,   nil,   nil,   nil,   nil,   nil,   249,
   nil,   nil,   249,   249,   249,   249,   249,   249,   249,   249,
   nil,   249,   249,   249,   nil,   249,   249,   249,   249,   249,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   249,
   nil,   nil,   249,   nil,   nil,   249,   249,   nil,   nil,   249,
   nil,   nil,   nil,   nil,   nil,   249,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   249,   nil,   nil,   nil,   nil,   249,
   249,   249,   249,   nil,   249,   249,   249,   249,   nil,   nil,
   nil,   nil,   249,   249,   nil,   nil,   nil,   250,   250,   250,
   249,   250,   249,   249,   249,   250,   250,   nil,   nil,   nil,
   250,   nil,   250,   250,   250,   250,   250,   250,   250,   nil,
   nil,   nil,   nil,   nil,   250,   250,   250,   250,   250,   250,
   250,   nil,   nil,   250,   nil,   nil,   nil,   nil,   nil,   nil,
   250,   nil,   nil,   250,   250,   250,   250,   250,   250,   250,
   250,   nil,   250,   250,   250,   nil,   250,   250,   250,   250,
   250,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   250,   nil,   nil,   250,   nil,   nil,   250,   250,   nil,   nil,
   250,   nil,   nil,   nil,   nil,   nil,   250,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   250,   nil,   nil,   nil,   nil,
   250,   250,   250,   250,   nil,   250,   250,   250,   250,   nil,
   nil,   nil,   nil,   250,   250,   nil,   nil,   nil,   251,   251,
   251,   250,   251,   250,   250,   250,   251,   251,   nil,   nil,
   nil,   251,   nil,   251,   251,   251,   251,   251,   251,   251,
   nil,   nil,   nil,   nil,   nil,   251,   251,   251,   251,   251,
   251,   251,   nil,   nil,   251,   nil,   nil,   nil,   nil,   nil,
   nil,   251,   nil,   nil,   251,   251,   251,   251,   251,   251,
   251,   251,   nil,   251,   251,   251,   nil,   251,   251,   251,
   251,   251,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   251,   nil,   nil,   251,   nil,   nil,   251,   251,   nil,
   nil,   251,   nil,   nil,   nil,   nil,   nil,   251,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   251,   nil,   nil,   nil,
   nil,   251,   251,   251,   251,   nil,   251,   251,   251,   251,
   nil,   nil,   nil,   nil,   251,   251,   nil,   nil,   nil,   252,
   252,   252,   251,   252,   251,   251,   251,   252,   252,   nil,
   nil,   nil,   252,   nil,   252,   252,   252,   252,   252,   252,
   252,   nil,   nil,   nil,   nil,   nil,   252,   252,   252,   252,
   252,   252,   252,   nil,   nil,   252,   nil,   nil,   nil,   nil,
   nil,   nil,   252,   nil,   nil,   252,   252,   252,   252,   252,
   252,   252,   252,   nil,   252,   252,   252,   nil,   252,   252,
   252,   252,   252,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   252,   nil,   nil,   252,   nil,   nil,   252,   252,
   nil,   nil,   252,   nil,   nil,   nil,   nil,   nil,   252,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   252,   nil,   nil,
   nil,   nil,   252,   252,   252,   252,   nil,   252,   252,   252,
   252,   nil,   nil,   nil,   nil,   252,   252,   nil,   nil,   nil,
   253,   253,   253,   252,   253,   252,   252,   252,   253,   253,
   nil,   nil,   nil,   253,   nil,   253,   253,   253,   253,   253,
   253,   253,   nil,   nil,   nil,   nil,   nil,   253,   253,   253,
   253,   253,   253,   253,   nil,   nil,   253,   nil,   nil,   nil,
   nil,   nil,   nil,   253,   nil,   nil,   253,   253,   253,   253,
   253,   253,   253,   253,   nil,   253,   253,   253,   nil,   253,
   253,   253,   253,   253,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   253,   nil,   nil,   253,   nil,   nil,   253,
   253,   nil,   nil,   253,   nil,   nil,   nil,   nil,   nil,   253,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   253,   nil,
   nil,   nil,   nil,   253,   253,   253,   253,   nil,   253,   253,
   253,   253,   nil,   nil,   nil,   nil,   253,   253,   nil,   nil,
   nil,   254,   254,   254,   253,   254,   253,   253,   253,   254,
   254,   nil,   nil,   nil,   254,   nil,   254,   254,   254,   254,
   254,   254,   254,   nil,   nil,   nil,   nil,   nil,   254,   254,
   254,   254,   254,   254,   254,   nil,   nil,   254,   nil,   nil,
   nil,   nil,   nil,   nil,   254,   nil,   nil,   254,   254,   254,
   254,   254,   254,   254,   254,   nil,   254,   254,   254,   nil,
   254,   254,   254,   254,   254,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   254,   nil,   nil,   254,   nil,   nil,
   254,   254,   nil,   nil,   254,   nil,   nil,   nil,   nil,   nil,
   254,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   254,
   nil,   nil,   nil,   nil,   254,   254,   254,   254,   nil,   254,
   254,   254,   254,   nil,   nil,   nil,   nil,   254,   254,   nil,
   nil,   nil,   255,   255,   255,   254,   255,   254,   254,   254,
   255,   255,   nil,   nil,   nil,   255,   nil,   255,   255,   255,
   255,   255,   255,   255,   nil,   nil,   nil,   nil,   nil,   255,
   255,   255,   255,   255,   255,   255,   nil,   nil,   255,   nil,
   nil,   nil,   nil,   nil,   nil,   255,   nil,   nil,   255,   255,
   255,   255,   255,   255,   255,   255,   nil,   255,   255,   255,
   nil,   255,   255,   255,   255,   255,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   255,   nil,   nil,   255,   nil,
   nil,   255,   255,   nil,   nil,   255,   nil,   nil,   nil,   nil,
   nil,   255,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   255,   nil,   nil,   nil,   nil,   255,   255,   255,   255,   nil,
   255,   255,   255,   255,   nil,   nil,   nil,   nil,   255,   255,
   nil,   nil,   nil,   256,   256,   256,   255,   256,   255,   255,
   255,   256,   256,   nil,   nil,   nil,   256,   nil,   256,   256,
   256,   256,   256,   256,   256,   nil,   nil,   nil,   nil,   nil,
   256,   256,   256,   256,   256,   256,   256,   nil,   nil,   256,
   nil,   nil,   nil,   nil,   nil,   nil,   256,   nil,   nil,   256,
   256,   256,   256,   256,   256,   256,   256,   nil,   256,   256,
   256,   nil,   256,   256,   256,   256,   256,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   256,   nil,   nil,   256,
   nil,   nil,   256,   256,   nil,   nil,   256,   nil,   nil,   nil,
   nil,   nil,   256,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   256,   nil,   nil,   nil,   nil,   256,   256,   256,   256,
   nil,   256,   256,   256,   256,   nil,   nil,   nil,   nil,   256,
   256,   nil,   nil,   nil,   257,   257,   257,   256,   257,   256,
   256,   256,   257,   257,   nil,   nil,   nil,   257,   nil,   257,
   257,   257,   257,   257,   257,   257,   nil,   nil,   nil,   nil,
   nil,   257,   257,   257,   257,   257,   257,   257,   nil,   nil,
   257,   nil,   nil,   nil,   nil,   nil,   nil,   257,   nil,   nil,
   257,   257,   257,   257,   257,   257,   257,   257,   nil,   257,
   257,   257,   nil,   257,   257,   257,   257,   257,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   257,   nil,   nil,
   257,   nil,   nil,   257,   257,   nil,   nil,   257,   nil,   nil,
   nil,   nil,   nil,   257,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   257,   nil,   nil,   nil,   nil,   257,   257,   257,
   257,   nil,   257,   257,   257,   257,   nil,   nil,   nil,   nil,
   257,   257,   nil,   nil,   nil,   258,   258,   258,   257,   258,
   257,   257,   257,   258,   258,   nil,   nil,   nil,   258,   nil,
   258,   258,   258,   258,   258,   258,   258,   nil,   nil,   nil,
   nil,   nil,   258,   258,   258,   258,   258,   258,   258,   nil,
   nil,   258,   nil,   nil,   nil,   nil,   nil,   nil,   258,   nil,
   nil,   258,   258,   258,   258,   258,   258,   258,   258,   nil,
   258,   258,   258,   nil,   258,   258,   258,   258,   258,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   258,   nil,
   nil,   258,   nil,   nil,   258,   258,   nil,   nil,   258,   nil,
   nil,   nil,   nil,   nil,   258,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   258,   nil,   nil,   nil,   nil,   258,   258,
   258,   258,   nil,   258,   258,   258,   258,   nil,   nil,   nil,
   nil,   258,   258,   nil,   nil,   nil,   259,   259,   259,   258,
   259,   258,   258,   258,   259,   259,   nil,   nil,   nil,   259,
   nil,   259,   259,   259,   259,   259,   259,   259,   nil,   nil,
   nil,   nil,   nil,   259,   259,   259,   259,   259,   259,   259,
   nil,   nil,   259,   nil,   nil,   nil,   nil,   nil,   nil,   259,
   nil,   nil,   259,   259,   259,   259,   259,   259,   259,   259,
   nil,   259,   259,   259,   nil,   259,   259,   259,   259,   259,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   259,
   nil,   nil,   259,   nil,   nil,   259,   259,   nil,   nil,   259,
   nil,   nil,   nil,   nil,   nil,   259,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   259,   nil,   nil,   nil,   nil,   259,
   259,   259,   259,   nil,   259,   259,   259,   259,   nil,   nil,
   nil,   nil,   259,   259,   nil,   nil,   nil,   260,   260,   260,
   259,   260,   259,   259,   259,   260,   260,   nil,   nil,   nil,
   260,   nil,   260,   260,   260,   260,   260,   260,   260,   nil,
   nil,   nil,   nil,   nil,   260,   260,   260,   260,   260,   260,
   260,   nil,   nil,   260,   nil,   nil,   nil,   nil,   nil,   nil,
   260,   nil,   nil,   260,   260,   260,   260,   260,   260,   260,
   260,   nil,   260,   260,   260,   nil,   260,   260,   260,   260,
   260,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   260,   nil,   nil,   260,   nil,   nil,   260,   260,   nil,   nil,
   260,   nil,   nil,   nil,   nil,   nil,   260,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   260,   nil,   nil,   nil,   nil,
   260,   260,   260,   260,   nil,   260,   260,   260,   260,   nil,
   nil,   nil,   nil,   260,   260,   nil,   nil,   nil,   267,   267,
   267,   260,   267,   260,   260,   260,   267,   267,   nil,   nil,
   nil,   267,   nil,   267,   267,   267,   267,   267,   267,   267,
   nil,   nil,   nil,   nil,   nil,   267,   267,   267,   267,   267,
   267,   267,   nil,   nil,   267,   nil,   nil,   nil,   nil,   nil,
   nil,   267,   nil,   nil,   267,   267,   267,   267,   267,   267,
   267,   267,   267,   267,   267,   267,   nil,   267,   267,   267,
   267,   267,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   267,   nil,   nil,   267,   nil,   nil,   267,   267,   nil,
   nil,   267,   nil,   267,   nil,   267,   nil,   267,   nil,   nil,
   267,   nil,   nil,   nil,   nil,   nil,   267,   nil,   nil,   nil,
   nil,   267,   267,   267,   267,   nil,   267,   267,   267,   267,
   nil,   nil,   nil,   nil,   267,   267,   nil,   nil,   nil,   268,
   268,   268,   267,   268,   267,   267,   267,   268,   268,   nil,
   nil,   nil,   268,   nil,   268,   268,   268,   268,   268,   268,
   268,   nil,   nil,   nil,   nil,   nil,   268,   268,   268,   268,
   268,   268,   268,   nil,   nil,   268,   nil,   nil,   nil,   nil,
   nil,   nil,   268,   nil,   nil,   268,   268,   268,   268,   268,
   268,   268,   268,   268,   268,   268,   268,   nil,   268,   268,
   268,   268,   268,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   268,   nil,   nil,   268,   nil,   nil,   268,   268,
   nil,   nil,   268,   nil,   268,   nil,   268,   nil,   268,   nil,
   nil,   268,   nil,   nil,   nil,   nil,   nil,   268,   nil,   nil,
   nil,   nil,   268,   268,   268,   268,   nil,   268,   268,   268,
   268,   nil,   nil,   nil,   nil,   268,   268,   nil,   nil,   nil,
   276,   276,   276,   268,   276,   268,   268,   268,   276,   276,
   nil,   nil,   nil,   276,   nil,   276,   276,   276,   276,   276,
   276,   276,   nil,   nil,   nil,   nil,   nil,   276,   276,   276,
   276,   276,   276,   276,   nil,   nil,   276,   nil,   nil,   nil,
   nil,   nil,   nil,   276,   nil,   nil,   276,   276,   276,   276,
   276,   276,   276,   276,   276,   276,   276,   276,   nil,   276,
   276,   276,   276,   276,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   276,   nil,   nil,   276,   nil,   nil,   276,
   276,   nil,   nil,   276,   nil,   276,   nil,   276,   nil,   276,
   nil,   nil,   276,   nil,   nil,   nil,   nil,   nil,   276,   nil,
   nil,   nil,   nil,   276,   276,   276,   276,   nil,   276,   276,
   276,   276,   nil,   nil,   nil,   nil,   276,   276,   276,   nil,
   nil,   283,   283,   283,   276,   283,   276,   276,   276,   283,
   283,   nil,   nil,   nil,   283,   nil,   283,   283,   283,   283,
   283,   283,   283,   nil,   nil,   nil,   nil,   nil,   283,   283,
   283,   283,   283,   283,   283,   nil,   nil,   283,   nil,   nil,
   nil,   nil,   nil,   nil,   283,   nil,   nil,   283,   283,   283,
   283,   283,   283,   283,   283,   nil,   283,   283,   283,   nil,
   283,   283,   283,   283,   283,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   283,   nil,   nil,   283,   nil,   nil,
   283,   283,   nil,   nil,   283,   nil,   nil,   nil,   nil,   nil,
   283,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   283,
   nil,   nil,   nil,   nil,   283,   283,   283,   283,   nil,   283,
   283,   283,   283,   nil,   nil,   nil,   nil,   283,   283,   nil,
   nil,   nil,   285,   285,   285,   283,   285,   283,   283,   283,
   285,   285,   nil,   nil,   nil,   285,   nil,   285,   285,   285,
   285,   285,   285,   285,   nil,   nil,   nil,   nil,   nil,   285,
   285,   285,   285,   285,   285,   285,   nil,   nil,   285,   nil,
   nil,   nil,   nil,   nil,   nil,   285,   nil,   nil,   285,   285,
   285,   285,   285,   285,   285,   285,   nil,   285,   285,   285,
   nil,   285,   285,   285,   285,   285,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   285,   nil,   nil,   285,   nil,
   nil,   285,   285,   nil,   nil,   285,   nil,   nil,   nil,   nil,
   nil,   285,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   285,   nil,   nil,   nil,   nil,   285,   285,   285,   285,   nil,
   285,   285,   285,   285,   nil,   nil,   nil,   nil,   285,   285,
   nil,   nil,   nil,   288,   288,   288,   285,   288,   285,   285,
   285,   288,   288,   nil,   nil,   nil,   288,   nil,   288,   288,
   288,   288,   288,   288,   288,   nil,   nil,   nil,   nil,   nil,
   288,   288,   288,   288,   288,   288,   288,   nil,   nil,   288,
   nil,   nil,   nil,   nil,   nil,   nil,   288,   nil,   nil,   288,
   288,   288,   288,   288,   288,   288,   288,   nil,   288,   288,
   288,   nil,   288,   288,   288,   288,   288,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   288,   nil,   nil,   288,
   nil,   nil,   288,   288,   nil,   nil,   288,   nil,   nil,   nil,
   nil,   nil,   288,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   288,   nil,   nil,   nil,   nil,   288,   288,   288,   288,
   nil,   288,   288,   288,   288,   nil,   nil,   nil,   nil,   288,
   288,   nil,   nil,   nil,   289,   289,   289,   288,   289,   288,
   288,   288,   289,   289,   nil,   nil,   nil,   289,   nil,   289,
   289,   289,   289,   289,   289,   289,   nil,   nil,   nil,   nil,
   nil,   289,   289,   289,   289,   289,   289,   289,   nil,   nil,
   289,   nil,   nil,   nil,   nil,   nil,   nil,   289,   nil,   nil,
   289,   289,   289,   289,   289,   289,   289,   289,   nil,   289,
   289,   289,   nil,   289,   289,   289,   289,   289,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   289,   nil,   nil,
   289,   nil,   nil,   289,   289,   nil,   nil,   289,   nil,   nil,
   nil,   nil,   nil,   289,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   289,   nil,   nil,   nil,   nil,   289,   289,   289,
   289,   nil,   289,   289,   289,   289,   nil,   nil,   nil,   nil,
   289,   289,   nil,   nil,   nil,   nil,   nil,   nil,   289,   nil,
   289,   289,   289,   294,   294,   294,   294,   294,   nil,   nil,
   nil,   294,   294,   nil,   nil,   nil,   294,   nil,   294,   294,
   294,   294,   294,   294,   294,   nil,   nil,   nil,   nil,   nil,
   294,   294,   294,   294,   294,   294,   294,   nil,   nil,   294,
   nil,   nil,   nil,   nil,   nil,   294,   294,   nil,   294,   294,
   294,   294,   294,   294,   294,   294,   294,   nil,   294,   294,
   294,   nil,   294,   294,   294,   294,   294,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   294,   nil,   nil,   294,
   nil,   nil,   294,   294,   nil,   nil,   294,   nil,   294,   nil,
   nil,   nil,   294,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   294,   nil,   nil,   nil,   nil,   294,   294,   294,   294,
   nil,   294,   294,   294,   294,   nil,   nil,   nil,   nil,   294,
   294,   nil,   nil,   nil,   302,   302,   302,   294,   302,   294,
   294,   294,   302,   302,   nil,   nil,   nil,   302,   nil,   302,
   302,   302,   302,   302,   302,   302,   nil,   nil,   nil,   nil,
   nil,   302,   302,   302,   302,   302,   302,   302,   nil,   nil,
   302,   nil,   nil,   nil,   nil,   nil,   nil,   302,   nil,   nil,
   302,   302,   302,   302,   302,   302,   302,   302,   nil,   302,
   302,   302,   nil,   302,   302,   nil,   nil,   302,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   302,   nil,   nil,
   302,   nil,   nil,   302,   302,   nil,   nil,   302,   nil,   nil,
   939,   nil,   939,   939,   939,   939,   939,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   939,   nil,   302,   302,   302,
   302,   nil,   302,   302,   302,   302,   nil,   nil,   nil,   nil,
   302,   302,   nil,   nil,   nil,   302,   nil,   939,   302,   nil,
   302,   302,   302,   319,   319,   319,   nil,   319,   939,   939,
   nil,   319,   319,   939,   nil,   nil,   319,   nil,   319,   319,
   319,   319,   319,   319,   319,   nil,   nil,   nil,   nil,   nil,
   319,   319,   319,   319,   319,   319,   319,   nil,   nil,   319,
   nil,   nil,   nil,   nil,   nil,   nil,   319,   nil,   nil,   319,
   319,   319,   319,   319,   319,   319,   319,   nil,   319,   319,
   319,   nil,   319,   319,   nil,   nil,   319,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   319,   nil,   nil,   319,
   nil,   nil,   319,   319,   nil,   nil,   319,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   319,   319,   319,   319,
   nil,   319,   319,   319,   319,   nil,   nil,   nil,   nil,   319,
   319,   nil,   nil,   nil,   327,   327,   327,   319,   327,   319,
   319,   319,   327,   327,   nil,   nil,   nil,   327,   nil,   327,
   327,   327,   327,   327,   327,   327,   nil,   nil,   nil,   nil,
   nil,   327,   327,   327,   327,   327,   327,   327,   nil,   nil,
   327,   nil,   nil,   nil,   nil,   nil,   nil,   327,   nil,   nil,
   327,   327,   327,   327,   327,   327,   327,   327,   nil,   327,
   327,   327,   nil,   327,   327,   327,   327,   327,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   327,   nil,   nil,
   327,   327,   nil,   327,   327,   nil,   nil,   327,   nil,   nil,
   nil,   nil,   nil,   327,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   327,   nil,   nil,   nil,   nil,   327,   327,   327,
   327,   nil,   327,   327,   327,   327,   nil,   nil,   nil,   nil,
   327,   327,   nil,   nil,   nil,   329,   329,   329,   327,   329,
   327,   327,   327,   329,   329,   nil,   nil,   nil,   329,   nil,
   329,   329,   329,   329,   329,   329,   329,   nil,   nil,   nil,
   nil,   nil,   329,   329,   329,   329,   329,   329,   329,   nil,
   nil,   329,   nil,   nil,   nil,   nil,   nil,   nil,   329,   nil,
   nil,   329,   329,   329,   329,   329,   329,   329,   329,   nil,
   329,   329,   329,   nil,   329,   329,   329,   329,   329,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   329,   nil,
   nil,   329,   nil,   nil,   329,   329,   nil,   nil,   329,   nil,
   nil,   nil,   nil,   nil,   329,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   329,   nil,   nil,   nil,   nil,   329,   329,
   329,   329,   nil,   329,   329,   329,   329,   nil,   nil,   nil,
   nil,   329,   329,   nil,   nil,   nil,   343,   343,   343,   329,
   343,   329,   329,   329,   343,   343,   nil,   nil,   nil,   343,
   nil,   343,   343,   343,   343,   343,   343,   343,   nil,   nil,
   nil,   nil,   nil,   343,   343,   343,   343,   343,   343,   343,
   nil,   nil,   343,   nil,   nil,   nil,   nil,   nil,   nil,   343,
   nil,   nil,   343,   343,   343,   343,   343,   343,   343,   343,
   nil,   343,   343,   343,   nil,   343,   343,   343,   343,   343,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   343,
   nil,   nil,   343,   nil,   nil,   343,   343,   nil,   nil,   343,
   nil,   nil,   nil,   nil,   nil,   343,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   343,   nil,   nil,   nil,   nil,   343,
   343,   343,   343,   nil,   343,   343,   343,   343,   nil,   nil,
   nil,   nil,   343,   343,   nil,   nil,   nil,   344,   344,   344,
   343,   344,   343,   343,   343,   344,   344,   nil,   nil,   nil,
   344,   nil,   344,   344,   344,   344,   344,   344,   344,   nil,
   nil,   nil,   nil,   nil,   344,   344,   344,   344,   344,   344,
   344,   nil,   nil,   344,   nil,   nil,   nil,   nil,   nil,   nil,
   344,   nil,   nil,   344,   344,   344,   344,   344,   344,   344,
   344,   nil,   344,   344,   344,   nil,   344,   344,   344,   344,
   344,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   344,   nil,   nil,   344,   nil,   nil,   344,   344,   nil,   nil,
   344,   nil,   nil,   nil,   nil,   nil,   344,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   344,   nil,   nil,   nil,   nil,
   344,   344,   344,   344,   nil,   344,   344,   344,   344,   nil,
   nil,   nil,   nil,   344,   344,   nil,   nil,   nil,   363,   363,
   363,   344,   363,   344,   344,   344,   363,   363,   nil,   nil,
   nil,   363,   nil,   363,   363,   363,   363,   363,   363,   363,
   nil,   nil,   nil,   nil,   nil,   363,   363,   363,   363,   363,
   363,   363,   nil,   nil,   363,   nil,   nil,   nil,   nil,   nil,
   nil,   363,   nil,   nil,   363,   363,   363,   363,   363,   363,
   363,   363,   nil,   363,   363,   363,   nil,   363,   363,   363,
   363,   363,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   363,   nil,   nil,   363,   nil,   nil,   363,   363,   nil,
   nil,   363,   nil,   nil,   nil,   nil,   nil,   363,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   363,   nil,   nil,   nil,
   nil,   363,   363,   363,   363,   nil,   363,   363,   363,   363,
   nil,   nil,   nil,   nil,   363,   363,   nil,   nil,   nil,   379,
   379,   379,   363,   379,   363,   363,   363,   379,   379,   nil,
   nil,   nil,   379,   nil,   379,   379,   379,   379,   379,   379,
   379,   nil,   nil,   nil,   nil,   nil,   379,   379,   379,   379,
   379,   379,   379,   nil,   nil,   379,   nil,   nil,   nil,   nil,
   nil,   nil,   379,   nil,   nil,   379,   379,   379,   379,   379,
   379,   379,   379,   nil,   379,   379,   379,   nil,   379,   379,
   379,   379,   379,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   379,   nil,   nil,   379,   nil,   nil,   379,   379,
   nil,   nil,   379,   nil,   nil,   nil,   nil,   nil,   379,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   379,   nil,   nil,
   nil,   nil,   379,   379,   379,   379,   nil,   379,   379,   379,
   379,   nil,   nil,   nil,   nil,   379,   379,   nil,   nil,   nil,
   407,   407,   407,   379,   407,   379,   379,   379,   407,   407,
   nil,   nil,   nil,   407,   nil,   407,   407,   407,   407,   407,
   407,   407,   nil,   nil,   nil,   nil,   nil,   407,   407,   407,
   407,   407,   407,   407,   nil,   nil,   407,   nil,   nil,   nil,
   nil,   nil,   nil,   407,   nil,   nil,   407,   407,   407,   407,
   407,   407,   407,   407,   nil,   407,   407,   407,   nil,   407,
   407,   407,   407,   407,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   407,   nil,   nil,   407,   nil,   nil,   407,
   407,   nil,   nil,   407,   nil,   nil,   nil,   nil,   nil,   407,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   407,   nil,
   nil,   nil,   nil,   407,   407,   407,   407,   nil,   407,   407,
   407,   407,   nil,   nil,   nil,   nil,   407,   407,   nil,   nil,
   nil,   445,   445,   445,   407,   445,   407,   407,   407,   445,
   445,   nil,   nil,   nil,   445,   nil,   445,   445,   445,   445,
   445,   445,   445,   nil,   nil,   nil,   nil,   nil,   445,   445,
   445,   445,   445,   445,   445,   nil,   nil,   445,   nil,   nil,
   nil,   nil,   nil,   nil,   445,   nil,   nil,   445,   445,   445,
   445,   445,   445,   445,   445,   445,   445,   445,   445,   nil,
   445,   445,   445,   445,   445,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   445,   nil,   nil,   445,   nil,   nil,
   445,   445,   nil,   nil,   445,   nil,   445,   nil,   445,   nil,
   445,   nil,   nil,   445,   nil,   nil,   nil,   nil,   nil,   445,
   nil,   nil,   nil,   nil,   445,   445,   445,   445,   nil,   445,
   445,   445,   445,   nil,   nil,   nil,   nil,   445,   445,   nil,
   nil,   nil,   447,   447,   447,   445,   447,   445,   445,   445,
   447,   447,   nil,   nil,   nil,   447,   nil,   447,   447,   447,
   447,   447,   447,   447,   nil,   nil,   nil,   nil,   nil,   447,
   447,   447,   447,   447,   447,   447,   nil,   nil,   447,   nil,
   nil,   nil,   nil,   nil,   nil,   447,   nil,   nil,   447,   447,
   447,   447,   447,   447,   447,   447,   nil,   447,   447,   447,
   nil,   447,   447,   447,   447,   447,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   447,   nil,   nil,   447,   nil,
   nil,   447,   447,   nil,   nil,   447,   nil,   nil,   nil,   nil,
   nil,   447,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   447,   nil,   nil,   nil,   nil,   447,   447,   447,   447,   nil,
   447,   447,   447,   447,   nil,   nil,   nil,   nil,   447,   447,
   nil,   nil,   nil,   448,   448,   448,   447,   448,   447,   447,
   447,   448,   448,   nil,   nil,   nil,   448,   nil,   448,   448,
   448,   448,   448,   448,   448,   nil,   nil,   nil,   nil,   nil,
   448,   448,   448,   448,   448,   448,   448,   nil,   nil,   448,
   nil,   nil,   nil,   nil,   nil,   nil,   448,   nil,   nil,   448,
   448,   448,   448,   448,   448,   448,   448,   nil,   448,   448,
   448,   nil,   448,   448,   448,   448,   448,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   448,   nil,   nil,   448,
   nil,   nil,   448,   448,   nil,   nil,   448,   nil,   nil,   nil,
   nil,   nil,   448,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   448,   nil,   nil,   nil,   nil,   448,   448,   448,   448,
   nil,   448,   448,   448,   448,   nil,   nil,   nil,   nil,   448,
   448,   nil,   nil,   nil,   449,   449,   449,   448,   449,   448,
   448,   448,   449,   449,   nil,   nil,   nil,   449,   nil,   449,
   449,   449,   449,   449,   449,   449,   nil,   nil,   nil,   nil,
   nil,   449,   449,   449,   449,   449,   449,   449,   nil,   nil,
   449,   nil,   nil,   nil,   nil,   nil,   nil,   449,   nil,   nil,
   449,   449,   449,   449,   449,   449,   449,   449,   nil,   449,
   449,   449,   nil,   449,   449,   449,   449,   449,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   449,   nil,   nil,
   449,   nil,   nil,   449,   449,   nil,   nil,   449,   nil,   nil,
   nil,   nil,   nil,   449,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   449,   nil,   nil,   nil,   nil,   449,   449,   449,
   449,   nil,   449,   449,   449,   449,   nil,   nil,   nil,   nil,
   449,   449,   nil,   nil,   nil,   489,   489,   489,   449,   489,
   449,   449,   449,   489,   489,   nil,   nil,   nil,   489,   nil,
   489,   489,   489,   489,   489,   489,   489,   nil,   nil,   nil,
   nil,   nil,   489,   489,   489,   489,   489,   489,   489,   nil,
   nil,   489,   nil,   nil,   nil,   nil,   nil,   nil,   489,   nil,
   nil,   489,   489,   489,   489,   489,   489,   489,   489,   489,
   489,   489,   489,   nil,   489,   489,   489,   489,   489,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   489,   nil,
   nil,   489,   nil,   nil,   489,   489,   nil,   nil,   489,   nil,
   489,   nil,   489,   nil,   489,   nil,   nil,   489,   nil,   nil,
   nil,   nil,   nil,   489,   nil,   nil,   nil,   nil,   489,   489,
   489,   489,   nil,   489,   489,   489,   489,   nil,   nil,   nil,
   nil,   489,   489,   nil,   nil,   nil,   491,   491,   491,   489,
   491,   489,   489,   489,   491,   491,   nil,   nil,   nil,   491,
   nil,   491,   491,   491,   491,   491,   491,   491,   nil,   nil,
   nil,   nil,   nil,   491,   491,   491,   491,   491,   491,   491,
   nil,   nil,   491,   nil,   nil,   nil,   nil,   nil,   nil,   491,
   nil,   nil,   491,   491,   491,   491,   491,   491,   491,   491,
   491,   491,   491,   491,   nil,   491,   491,   491,   491,   491,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   491,
   nil,   nil,   491,   nil,   nil,   491,   491,   nil,   nil,   491,
   nil,   nil,   nil,   491,   nil,   491,   nil,   nil,   491,   nil,
   nil,   nil,   nil,   nil,   491,   nil,   nil,   nil,   nil,   491,
   491,   491,   491,   nil,   491,   491,   491,   491,   nil,   nil,
   nil,   nil,   491,   491,   nil,   nil,   nil,   493,   493,   493,
   491,   493,   491,   491,   491,   493,   493,   nil,   nil,   nil,
   493,   nil,   493,   493,   493,   493,   493,   493,   493,   nil,
   nil,   nil,   nil,   nil,   493,   493,   493,   493,   493,   493,
   493,   nil,   nil,   493,   nil,   nil,   nil,   nil,   nil,   nil,
   493,   nil,   nil,   493,   493,   493,   493,   493,   493,   493,
   493,   nil,   493,   493,   493,   nil,   493,   493,   493,   493,
   493,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   493,   nil,   nil,   493,   nil,   nil,   493,   493,   nil,   nil,
   493,   nil,   nil,   nil,   nil,   nil,   493,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   493,   nil,   nil,   nil,   nil,
   493,   493,   493,   493,   nil,   493,   493,   493,   493,   nil,
   nil,   nil,   nil,   493,   493,   nil,   nil,   nil,   nil,   nil,
   nil,   493,   nil,   493,   493,   493,   499,   499,   499,   499,
   499,   nil,   nil,   nil,   499,   499,   nil,   nil,   nil,   499,
   nil,   499,   499,   499,   499,   499,   499,   499,   nil,   nil,
   nil,   nil,   nil,   499,   499,   499,   499,   499,   499,   499,
   nil,   nil,   499,   nil,   nil,   nil,   nil,   nil,   499,   499,
   499,   499,   499,   499,   499,   499,   499,   499,   499,   499,
   nil,   499,   499,   499,   nil,   499,   499,   499,   499,   499,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   499,
   nil,   nil,   499,   nil,   nil,   499,   499,   nil,   nil,   499,
   nil,   499,   nil,   nil,   nil,   499,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   499,   nil,   nil,   nil,   nil,   499,
   499,   499,   499,   nil,   499,   499,   499,   499,   nil,   nil,
   nil,   nil,   499,   499,   nil,   nil,   nil,   nil,   nil,   499,
   499,   nil,   499,   499,   499,   507,   507,   507,   nil,   507,
   nil,   nil,   nil,   507,   507,   nil,   nil,   nil,   507,   nil,
   507,   507,   507,   507,   507,   507,   507,   nil,   nil,   nil,
   nil,   nil,   507,   507,   507,   507,   507,   507,   507,   nil,
   nil,   507,   nil,   nil,   nil,   nil,   nil,   nil,   507,   nil,
   nil,   507,   507,   507,   507,   507,   507,   507,   507,   nil,
   507,   507,   507,   nil,   507,   507,   nil,   nil,   507,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   507,   nil,
   nil,   507,   nil,   nil,   507,   507,   nil,   nil,   507,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   507,   507,
   507,   507,   nil,   507,   507,   507,   507,   nil,   nil,   nil,
   nil,   507,   507,   nil,   nil,   nil,   509,   509,   509,   507,
   509,   507,   507,   507,   509,   509,   nil,   nil,   nil,   509,
   nil,   509,   509,   509,   509,   509,   509,   509,   nil,   nil,
   nil,   nil,   nil,   509,   509,   509,   509,   509,   509,   509,
   nil,   nil,   509,   nil,   nil,   nil,   nil,   nil,   nil,   509,
   nil,   nil,   509,   509,   509,   509,   509,   509,   509,   509,
   509,   509,   509,   509,   nil,   509,   509,   509,   509,   509,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   509,
   nil,   nil,   509,   nil,   nil,   509,   509,   nil,   nil,   509,
   nil,   509,   nil,   509,   nil,   509,   nil,   nil,   509,   nil,
   nil,   nil,   nil,   nil,   509,   nil,   nil,   nil,   nil,   509,
   509,   509,   509,   nil,   509,   509,   509,   509,   nil,   nil,
   nil,   nil,   509,   509,   nil,   nil,   nil,   515,   515,   515,
   509,   515,   509,   509,   509,   515,   515,   nil,   nil,   nil,
   515,   nil,   515,   515,   515,   515,   515,   515,   515,   nil,
   nil,   nil,   nil,   nil,   515,   515,   515,   515,   515,   515,
   515,   nil,   nil,   515,   nil,   nil,   nil,   nil,   nil,   nil,
   515,   nil,   nil,   515,   515,   515,   515,   515,   515,   515,
   515,   nil,   515,   515,   515,   nil,   515,   515,   nil,   nil,
   515,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   515,   nil,   nil,   515,   nil,   nil,   515,   515,   nil,   nil,
   515,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   515,   515,   515,   515,   nil,   515,   515,   515,   515,   nil,
   nil,   nil,   nil,   515,   515,   nil,   nil,   nil,   518,   518,
   518,   515,   518,   515,   515,   515,   518,   518,   nil,   nil,
   nil,   518,   nil,   518,   518,   518,   518,   518,   518,   518,
   nil,   nil,   nil,   nil,   nil,   518,   518,   518,   518,   518,
   518,   518,   nil,   nil,   518,   nil,   nil,   nil,   nil,   nil,
   nil,   518,   nil,   nil,   518,   518,   518,   518,   518,   518,
   518,   518,   nil,   518,   518,   518,   nil,   518,   518,   518,
   518,   518,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   518,   nil,   nil,   518,   nil,   nil,   518,   518,   nil,
   nil,   518,   nil,   nil,   nil,   nil,   nil,   518,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   518,   nil,   nil,   nil,
   nil,   518,   518,   518,   518,   nil,   518,   518,   518,   518,
   nil,   nil,   nil,   nil,   518,   518,   nil,   nil,   nil,   519,
   519,   519,   518,   519,   518,   518,   518,   519,   519,   nil,
   nil,   nil,   519,   nil,   519,   519,   519,   519,   519,   519,
   519,   nil,   nil,   nil,   nil,   nil,   519,   519,   519,   519,
   519,   519,   519,   nil,   nil,   519,   nil,   nil,   nil,   nil,
   nil,   nil,   519,   nil,   nil,   519,   519,   519,   519,   519,
   519,   519,   519,   nil,   519,   519,   519,   nil,   519,   519,
   519,   519,   519,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   519,   nil,   nil,   519,   nil,   nil,   519,   519,
   nil,   nil,   519,   nil,   nil,   nil,   nil,   nil,   519,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   519,   nil,   nil,
   nil,   nil,   519,   519,   519,   519,   nil,   519,   519,   519,
   519,   nil,   nil,   nil,   nil,   519,   519,   nil,   nil,   nil,
   523,   523,   523,   519,   523,   519,   519,   519,   523,   523,
   nil,   nil,   nil,   523,   nil,   523,   523,   523,   523,   523,
   523,   523,   nil,   nil,   nil,   nil,   nil,   523,   523,   523,
   523,   523,   523,   523,   nil,   nil,   523,   nil,   nil,   nil,
   nil,   nil,   nil,   523,   nil,   nil,   523,   523,   523,   523,
   523,   523,   523,   523,   nil,   523,   523,   523,   nil,   523,
   523,   523,   523,   523,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   523,   nil,   nil,   523,   nil,   nil,   523,
   523,   nil,   nil,   523,   nil,   nil,   nil,   nil,   nil,   523,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   523,   nil,
   nil,   nil,   nil,   523,   523,   523,   523,   nil,   523,   523,
   523,   523,   nil,   nil,   nil,   nil,   523,   523,   nil,   nil,
   nil,   529,   529,   529,   523,   529,   523,   523,   523,   529,
   529,   nil,   nil,   nil,   529,   nil,   529,   529,   529,   529,
   529,   529,   529,   nil,   nil,   nil,   nil,   nil,   529,   529,
   529,   529,   529,   529,   529,   nil,   nil,   529,   nil,   nil,
   nil,   nil,   nil,   nil,   529,   nil,   nil,   529,   529,   529,
   529,   529,   529,   529,   529,   529,   529,   529,   529,   nil,
   529,   529,   529,   529,   529,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   529,   nil,   nil,   529,   nil,   nil,
   529,   529,   nil,   nil,   529,   nil,   529,   nil,   nil,   nil,
   529,   nil,   nil,   529,   nil,   nil,   nil,   nil,   nil,   529,
   nil,   nil,   nil,   nil,   529,   529,   529,   529,   nil,   529,
   529,   529,   529,   nil,   nil,   nil,   nil,   529,   529,   nil,
   nil,   nil,   532,   532,   532,   529,   532,   529,   529,   529,
   532,   532,   nil,   nil,   nil,   532,   nil,   532,   532,   532,
   532,   532,   532,   532,   nil,   nil,   nil,   nil,   nil,   532,
   532,   532,   532,   532,   532,   532,   nil,   nil,   532,   nil,
   nil,   nil,   nil,   nil,   nil,   532,   nil,   nil,   532,   532,
   532,   532,   532,   532,   532,   532,   532,   532,   532,   532,
   nil,   532,   532,   532,   532,   532,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   532,   nil,   nil,   532,   nil,
   nil,   532,   532,   nil,   nil,   532,   nil,   nil,   nil,   nil,
   nil,   532,   nil,   nil,   532,   nil,   nil,   nil,   nil,   nil,
   532,   nil,   nil,   nil,   nil,   532,   532,   532,   532,   nil,
   532,   532,   532,   532,   nil,   nil,   nil,   nil,   532,   532,
   nil,   nil,   nil,   546,   546,   546,   532,   546,   532,   532,
   532,   546,   546,   nil,   nil,   nil,   546,   nil,   546,   546,
   546,   546,   546,   546,   546,   nil,   nil,   nil,   nil,   nil,
   546,   546,   546,   546,   546,   546,   546,   nil,   nil,   546,
   nil,   nil,   nil,   nil,   nil,   nil,   546,   nil,   nil,   546,
   546,   546,   546,   546,   546,   546,   546,   nil,   546,   546,
   546,   nil,   546,   546,   546,   546,   546,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   546,   nil,   nil,   546,
   nil,   nil,   546,   546,   nil,   nil,   546,   nil,   546,   nil,
   nil,   nil,   546,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   546,   nil,   nil,   nil,   nil,   546,   546,   546,   546,
   nil,   546,   546,   546,   546,   nil,   nil,   nil,   nil,   546,
   546,   nil,   nil,   nil,   547,   547,   547,   546,   547,   546,
   546,   546,   547,   547,   nil,   nil,   nil,   547,   nil,   547,
   547,   547,   547,   547,   547,   547,   nil,   nil,   nil,   nil,
   nil,   547,   547,   547,   547,   547,   547,   547,   nil,   nil,
   547,   nil,   nil,   nil,   nil,   nil,   nil,   547,   nil,   nil,
   547,   547,   547,   547,   547,   547,   547,   547,   547,   547,
   547,   547,   nil,   547,   547,   547,   547,   547,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   547,   nil,   nil,
   547,   nil,   nil,   547,   547,   nil,   nil,   547,   nil,   547,
   nil,   547,   nil,   547,   nil,   nil,   547,   nil,   nil,   nil,
   nil,   nil,   547,   nil,   nil,   nil,   nil,   547,   547,   547,
   547,   nil,   547,   547,   547,   547,   nil,   nil,   nil,   nil,
   547,   547,   nil,   nil,   nil,   557,   557,   557,   547,   557,
   547,   547,   547,   557,   557,   nil,   nil,   nil,   557,   nil,
   557,   557,   557,   557,   557,   557,   557,   nil,   nil,   nil,
   nil,   nil,   557,   557,   557,   557,   557,   557,   557,   nil,
   nil,   557,   nil,   nil,   nil,   nil,   nil,   nil,   557,   nil,
   nil,   557,   557,   557,   557,   557,   557,   557,   557,   557,
   557,   557,   557,   nil,   557,   557,   557,   557,   557,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   557,   nil,
   nil,   557,   nil,   nil,   557,   557,   nil,   nil,   557,   nil,
   557,   nil,   557,   nil,   557,   nil,   nil,   557,   nil,   nil,
   nil,   nil,   nil,   557,   nil,   nil,   nil,   nil,   557,   557,
   557,   557,   nil,   557,   557,   557,   557,   nil,   nil,   nil,
   nil,   557,   557,   nil,   nil,   nil,   589,   589,   589,   557,
   589,   557,   557,   557,   589,   589,   nil,   nil,   nil,   589,
   nil,   589,   589,   589,   589,   589,   589,   589,   nil,   nil,
   nil,   nil,   nil,   589,   589,   589,   589,   589,   589,   589,
   nil,   nil,   589,   nil,   nil,   nil,   nil,   nil,   nil,   589,
   nil,   nil,   589,   589,   589,   589,   589,   589,   589,   589,
   nil,   589,   589,   589,   nil,   589,   589,   589,   589,   589,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   589,
   nil,   nil,   589,   nil,   nil,   589,   589,   nil,   nil,   589,
   nil,   589,   nil,   nil,   nil,   589,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   589,   nil,   nil,   nil,   nil,   589,
   589,   589,   589,   nil,   589,   589,   589,   589,   nil,   nil,
   nil,   nil,   589,   589,   nil,   nil,   nil,   590,   590,   590,
   589,   590,   589,   589,   589,   590,   590,   nil,   nil,   nil,
   590,   nil,   590,   590,   590,   590,   590,   590,   590,   nil,
   nil,   nil,   nil,   nil,   590,   590,   590,   590,   590,   590,
   590,   nil,   nil,   590,   nil,   nil,   nil,   nil,   nil,   nil,
   590,   nil,   nil,   590,   590,   590,   590,   590,   590,   590,
   590,   nil,   590,   590,   590,   nil,   590,   590,   590,   590,
   590,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   590,   nil,   nil,   590,   nil,   nil,   590,   590,   nil,   nil,
   590,   nil,   nil,   nil,   nil,   nil,   590,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   590,   nil,   nil,   nil,   nil,
   590,   590,   590,   590,   nil,   590,   590,   590,   590,   nil,
   nil,   nil,   nil,   590,   590,   nil,   nil,   nil,   593,   593,
   593,   590,   593,   590,   590,   590,   593,   593,   nil,   nil,
   nil,   593,   nil,   593,   593,   593,   593,   593,   593,   593,
   nil,   nil,   nil,   nil,   nil,   593,   593,   593,   593,   593,
   593,   593,   nil,   nil,   593,   nil,   nil,   nil,   nil,   nil,
   nil,   593,   nil,   nil,   593,   593,   593,   593,   593,   593,
   593,   593,   593,   593,   593,   593,   nil,   593,   593,   593,
   593,   593,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   593,   nil,   nil,   593,   nil,   nil,   593,   593,   nil,
   nil,   593,   nil,   593,   nil,   593,   nil,   593,   nil,   nil,
   593,   nil,   nil,   nil,   nil,   nil,   593,   nil,   nil,   nil,
   nil,   593,   593,   593,   593,   nil,   593,   593,   593,   593,
   nil,   nil,   nil,   nil,   593,   593,   nil,   nil,   nil,   594,
   594,   594,   593,   594,   593,   593,   593,   594,   594,   nil,
   nil,   nil,   594,   nil,   594,   594,   594,   594,   594,   594,
   594,   nil,   nil,   nil,   nil,   nil,   594,   594,   594,   594,
   594,   594,   594,   nil,   nil,   594,   nil,   nil,   nil,   nil,
   nil,   nil,   594,   nil,   nil,   594,   594,   594,   594,   594,
   594,   594,   594,   594,   594,   594,   594,   nil,   594,   594,
   594,   594,   594,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   594,   nil,   nil,   594,   nil,   nil,   594,   594,
   nil,   nil,   594,   nil,   nil,   nil,   594,   nil,   594,   nil,
   nil,   594,   nil,   nil,   nil,   nil,   nil,   594,   nil,   nil,
   nil,   nil,   594,   594,   594,   594,   nil,   594,   594,   594,
   594,   nil,   nil,   nil,   nil,   594,   594,   nil,   nil,   nil,
   595,   595,   595,   594,   595,   594,   594,   594,   595,   595,
   nil,   nil,   nil,   595,   nil,   595,   595,   595,   595,   595,
   595,   595,   nil,   nil,   nil,   nil,   nil,   595,   595,   595,
   595,   595,   595,   595,   nil,   nil,   595,   nil,   nil,   nil,
   nil,   nil,   nil,   595,   nil,   nil,   595,   595,   595,   595,
   595,   595,   595,   595,   nil,   595,   595,   595,   nil,   595,
   595,   595,   595,   595,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   595,   nil,   nil,   595,   nil,   nil,   595,
   595,   nil,   nil,   595,   nil,   nil,   nil,   nil,   nil,   595,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   595,   nil,
   nil,   nil,   nil,   595,   595,   595,   595,   nil,   595,   595,
   595,   595,   nil,   nil,   nil,   nil,   595,   595,   nil,   nil,
   nil,   596,   596,   596,   595,   596,   595,   595,   595,   596,
   596,   nil,   nil,   nil,   596,   nil,   596,   596,   596,   596,
   596,   596,   596,   nil,   nil,   nil,   nil,   nil,   596,   596,
   596,   596,   596,   596,   596,   nil,   nil,   596,   nil,   nil,
   nil,   nil,   nil,   nil,   596,   nil,   nil,   596,   596,   596,
   596,   596,   596,   596,   596,   nil,   596,   596,   596,   nil,
   596,   596,   596,   596,   596,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   596,   nil,   nil,   596,   nil,   nil,
   596,   596,   nil,   nil,   596,   nil,   nil,   nil,   nil,   nil,
   596,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   596,
   nil,   nil,   nil,   nil,   596,   596,   596,   596,   nil,   596,
   596,   596,   596,   nil,   nil,   nil,   nil,   596,   596,   nil,
   nil,   nil,   600,   600,   600,   596,   600,   596,   596,   596,
   600,   600,   nil,   nil,   nil,   600,   nil,   600,   600,   600,
   600,   600,   600,   600,   nil,   nil,   nil,   nil,   nil,   600,
   600,   600,   600,   600,   600,   600,   nil,   nil,   600,   nil,
   nil,   nil,   nil,   nil,   nil,   600,   nil,   nil,   600,   600,
   600,   600,   600,   600,   600,   600,   nil,   600,   600,   600,
   nil,   600,   600,   600,   600,   600,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   600,   nil,   nil,   600,   nil,
   nil,   600,   600,   nil,   nil,   600,   nil,   nil,   nil,   nil,
   nil,   600,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   600,   nil,   nil,   nil,   nil,   600,   600,   600,   600,   nil,
   600,   600,   600,   600,   nil,   nil,   nil,   nil,   600,   600,
   nil,   nil,   nil,   601,   601,   601,   600,   601,   600,   600,
   600,   601,   601,   nil,   nil,   nil,   601,   nil,   601,   601,
   601,   601,   601,   601,   601,   nil,   nil,   nil,   nil,   nil,
   601,   601,   601,   601,   601,   601,   601,   nil,   nil,   601,
   nil,   nil,   nil,   nil,   nil,   nil,   601,   nil,   nil,   601,
   601,   601,   601,   601,   601,   601,   601,   nil,   601,   601,
   601,   nil,   601,   601,   601,   601,   601,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   601,   nil,   nil,   601,
   nil,   nil,   601,   601,   nil,   nil,   601,   nil,   nil,   nil,
   nil,   nil,   601,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   601,   nil,   nil,   nil,   nil,   601,   601,   601,   601,
   nil,   601,   601,   601,   601,   nil,   nil,   nil,   nil,   601,
   601,   nil,   nil,   nil,   604,   604,   604,   601,   604,   601,
   601,   601,   604,   604,   nil,   nil,   nil,   604,   nil,   604,
   604,   604,   604,   604,   604,   604,   nil,   nil,   nil,   nil,
   nil,   604,   604,   604,   604,   604,   604,   604,   nil,   nil,
   604,   nil,   nil,   nil,   nil,   nil,   nil,   604,   nil,   nil,
   604,   604,   604,   604,   604,   604,   604,   604,   nil,   604,
   604,   604,   nil,   604,   604,   604,   604,   604,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   604,   nil,   nil,
   604,   nil,   nil,   604,   604,   nil,   nil,   604,   nil,   nil,
   nil,   nil,   nil,   604,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   604,   nil,   nil,   nil,   nil,   604,   604,   604,
   604,   nil,   604,   604,   604,   604,   nil,   nil,   nil,   nil,
   604,   604,   nil,   nil,   nil,   605,   605,   605,   604,   605,
   604,   604,   604,   605,   605,   nil,   nil,   nil,   605,   nil,
   605,   605,   605,   605,   605,   605,   605,   nil,   nil,   nil,
   nil,   nil,   605,   605,   605,   605,   605,   605,   605,   nil,
   nil,   605,   nil,   nil,   nil,   nil,   nil,   nil,   605,   nil,
   nil,   605,   605,   605,   605,   605,   605,   605,   605,   nil,
   605,   605,   605,   nil,   605,   605,   605,   605,   605,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   605,   nil,
   nil,   605,   nil,   nil,   605,   605,   nil,   nil,   605,   nil,
   nil,   nil,   nil,   nil,   605,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   605,   nil,   nil,   nil,   nil,   605,   605,
   605,   605,   nil,   605,   605,   605,   605,   nil,   nil,   nil,
   nil,   605,   605,   nil,   nil,   nil,   629,   629,   629,   605,
   629,   605,   605,   605,   629,   629,   nil,   nil,   nil,   629,
   nil,   629,   629,   629,   629,   629,   629,   629,   nil,   nil,
   nil,   nil,   nil,   629,   629,   629,   629,   629,   629,   629,
   nil,   nil,   629,   nil,   nil,   nil,   nil,   nil,   nil,   629,
   nil,   nil,   629,   629,   629,   629,   629,   629,   629,   629,
   nil,   629,   629,   629,   nil,   629,   629,   629,   629,   629,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   629,
   nil,   nil,   629,   nil,   nil,   629,   629,   nil,   nil,   629,
   nil,   nil,   nil,   nil,   nil,   629,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   629,   nil,   nil,   nil,   nil,   629,
   629,   629,   629,   nil,   629,   629,   629,   629,   nil,   nil,
   nil,   nil,   629,   629,   nil,   nil,   nil,   632,   632,   632,
   629,   632,   629,   629,   629,   632,   632,   nil,   nil,   nil,
   632,   nil,   632,   632,   632,   632,   632,   632,   632,   nil,
   nil,   nil,   nil,   nil,   632,   632,   632,   632,   632,   632,
   632,   nil,   nil,   632,   nil,   nil,   nil,   nil,   nil,   nil,
   632,   nil,   nil,   632,   632,   632,   632,   632,   632,   632,
   632,   nil,   632,   632,   632,   nil,   632,   632,   632,   632,
   632,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   632,   nil,   nil,   632,   nil,   nil,   632,   632,   nil,   nil,
   632,   nil,   nil,   nil,   nil,   nil,   632,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   632,   nil,   nil,   nil,   nil,
   632,   632,   632,   632,   nil,   632,   632,   632,   632,   nil,
   nil,   nil,   nil,   632,   632,   nil,   nil,   nil,   636,   636,
   636,   632,   636,   632,   632,   632,   636,   636,   nil,   nil,
   nil,   636,   nil,   636,   636,   636,   636,   636,   636,   636,
   nil,   nil,   nil,   nil,   nil,   636,   636,   636,   636,   636,
   636,   636,   nil,   nil,   636,   nil,   nil,   nil,   nil,   nil,
   nil,   636,   nil,   nil,   636,   636,   636,   636,   636,   636,
   636,   636,   nil,   636,   636,   636,   nil,   636,   636,   nil,
   nil,   636,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   636,   nil,   nil,   636,   nil,   nil,   636,   636,   nil,
   nil,   636,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   636,   636,   636,   636,   nil,   636,   636,   636,   636,
   nil,   nil,   nil,   nil,   636,   636,   nil,   nil,   nil,   647,
   647,   647,   636,   647,   636,   636,   636,   647,   647,   nil,
   nil,   nil,   647,   nil,   647,   647,   647,   647,   647,   647,
   647,   nil,   nil,   nil,   nil,   nil,   647,   647,   647,   647,
   647,   647,   647,   nil,   nil,   647,   nil,   nil,   nil,   nil,
   nil,   nil,   647,   nil,   nil,   647,   647,   647,   647,   647,
   647,   647,   647,   nil,   647,   647,   647,   nil,   647,   647,
   nil,   nil,   647,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   647,   nil,   nil,   647,   nil,   nil,   647,   647,
   nil,   nil,   647,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   647,   647,   647,   647,   nil,   647,   647,   647,
   647,   nil,   nil,   nil,   nil,   647,   647,   nil,   nil,   nil,
   652,   652,   652,   647,   652,   647,   647,   647,   652,   652,
   nil,   nil,   nil,   652,   nil,   652,   652,   652,   652,   652,
   652,   652,   nil,   nil,   nil,   nil,   nil,   652,   652,   652,
   652,   652,   652,   652,   nil,   nil,   652,   nil,   nil,   nil,
   nil,   nil,   nil,   652,   nil,   nil,   652,   652,   652,   652,
   652,   652,   652,   652,   nil,   652,   652,   652,   nil,   652,
   652,   652,   652,   652,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   652,   nil,   nil,   652,   nil,   nil,   652,
   652,   nil,   nil,   652,   nil,   652,   nil,   nil,   nil,   652,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   652,   nil,
   nil,   nil,   nil,   652,   652,   652,   652,   nil,   652,   652,
   652,   652,   nil,   nil,   nil,   nil,   652,   652,   nil,   nil,
   nil,   678,   678,   678,   652,   678,   652,   652,   652,   678,
   678,   nil,   nil,   nil,   678,   nil,   678,   678,   678,   678,
   678,   678,   678,   nil,   nil,   nil,   nil,   nil,   678,   678,
   678,   678,   678,   678,   678,   nil,   nil,   678,   nil,   nil,
   nil,   nil,   nil,   nil,   678,   nil,   nil,   678,   678,   678,
   678,   678,   678,   678,   678,   nil,   678,   678,   678,   nil,
   678,   678,   678,   678,   678,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   678,   nil,   nil,   678,   nil,   nil,
   678,   678,   nil,   nil,   678,   nil,   nil,   nil,   nil,   nil,
   678,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   678,
   nil,   nil,   nil,   nil,   678,   678,   678,   678,   nil,   678,
   678,   678,   678,   nil,   nil,   nil,   nil,   678,   678,   nil,
   nil,   nil,   705,   705,   705,   678,   705,   678,   678,   678,
   705,   705,   nil,   nil,   nil,   705,   nil,   705,   705,   705,
   705,   705,   705,   705,   nil,   nil,   nil,   nil,   nil,   705,
   705,   705,   705,   705,   705,   705,   nil,   nil,   705,   nil,
   nil,   nil,   nil,   nil,   nil,   705,   nil,   nil,   705,   705,
   705,   705,   705,   705,   705,   705,   nil,   705,   705,   705,
   nil,   705,   705,   705,   705,   705,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   705,   nil,   nil,   705,   nil,
   nil,   705,   705,   nil,   nil,   705,   nil,   nil,   nil,   nil,
   nil,   705,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   705,   nil,   nil,   nil,   nil,   705,   705,   705,   705,   nil,
   705,   705,   705,   705,   nil,   nil,   nil,   nil,   705,   705,
   nil,   nil,   nil,   711,   711,   711,   705,   711,   705,   705,
   705,   711,   711,   nil,   nil,   nil,   711,   nil,   711,   711,
   711,   711,   711,   711,   711,   nil,   nil,   nil,   nil,   nil,
   711,   711,   711,   711,   711,   711,   711,   nil,   nil,   711,
   nil,   nil,   nil,   nil,   nil,   nil,   711,   nil,   nil,   711,
   711,   711,   711,   711,   711,   711,   711,   nil,   711,   711,
   711,   nil,   711,   711,   711,   711,   711,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   711,   nil,   nil,   711,
   nil,   nil,   711,   711,   nil,   nil,   711,   nil,   nil,   nil,
   nil,   nil,   711,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   711,   nil,   nil,   nil,   nil,   711,   711,   711,   711,
   nil,   711,   711,   711,   711,   nil,   nil,   nil,   nil,   711,
   711,   nil,   nil,   nil,   733,   733,   733,   711,   733,   711,
   711,   711,   733,   733,   nil,   nil,   nil,   733,   nil,   733,
   733,   733,   733,   733,   733,   733,   nil,   nil,   nil,   nil,
   nil,   733,   733,   733,   733,   733,   733,   733,   nil,   nil,
   733,   nil,   nil,   nil,   nil,   nil,   nil,   733,   nil,   nil,
   733,   733,   733,   733,   733,   733,   733,   733,   nil,   733,
   733,   733,   nil,   733,   733,   733,   733,   733,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   733,   nil,   nil,
   733,   nil,   nil,   733,   733,   nil,   nil,   733,   nil,   nil,
   nil,   nil,   nil,   733,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   733,   nil,   nil,   nil,   nil,   733,   733,   733,
   733,   nil,   733,   733,   733,   733,   nil,   nil,   nil,   nil,
   733,   733,   nil,   nil,   nil,   735,   735,   735,   733,   735,
   733,   733,   733,   735,   735,   nil,   nil,   nil,   735,   nil,
   735,   735,   735,   735,   735,   735,   735,   nil,   nil,   nil,
   nil,   nil,   735,   735,   735,   735,   735,   735,   735,   nil,
   nil,   735,   nil,   nil,   nil,   nil,   nil,   nil,   735,   nil,
   nil,   735,   735,   735,   735,   735,   735,   735,   735,   nil,
   735,   735,   735,   nil,   735,   735,   735,   735,   735,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   735,   nil,
   nil,   735,   nil,   nil,   735,   735,   nil,   nil,   735,   nil,
   nil,   nil,   nil,   nil,   735,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   735,   nil,   nil,   nil,   nil,   735,   735,
   735,   735,   nil,   735,   735,   735,   735,   nil,   nil,   nil,
   nil,   735,   735,   nil,   nil,   nil,   749,   749,   749,   735,
   749,   735,   735,   735,   749,   749,   nil,   nil,   nil,   749,
   nil,   749,   749,   749,   749,   749,   749,   749,   nil,   nil,
   nil,   nil,   nil,   749,   749,   749,   749,   749,   749,   749,
   nil,   nil,   749,   nil,   nil,   nil,   nil,   nil,   nil,   749,
   nil,   nil,   749,   749,   749,   749,   749,   749,   749,   749,
   nil,   749,   749,   749,   nil,   749,   749,   749,   749,   749,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   749,
   nil,   nil,   749,   nil,   nil,   749,   749,   nil,   nil,   749,
   nil,   nil,   nil,   nil,   nil,   749,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   749,   nil,   nil,   nil,   nil,   749,
   749,   749,   749,   nil,   749,   749,   749,   749,   nil,   nil,
   nil,   nil,   749,   749,   nil,   nil,   nil,   750,   750,   750,
   749,   750,   749,   749,   749,   750,   750,   nil,   nil,   nil,
   750,   nil,   750,   750,   750,   750,   750,   750,   750,   nil,
   nil,   nil,   nil,   nil,   750,   750,   750,   750,   750,   750,
   750,   nil,   nil,   750,   nil,   nil,   nil,   nil,   nil,   nil,
   750,   nil,   nil,   750,   750,   750,   750,   750,   750,   750,
   750,   nil,   750,   750,   750,   nil,   750,   750,   750,   750,
   750,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   750,   nil,   nil,   750,   nil,   nil,   750,   750,   nil,   nil,
   750,   nil,   nil,   nil,   nil,   nil,   750,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   750,   nil,   nil,   nil,   nil,
   750,   750,   750,   750,   nil,   750,   750,   750,   750,   nil,
   nil,   nil,   nil,   750,   750,   nil,   nil,   nil,   751,   751,
   751,   750,   751,   750,   750,   750,   751,   751,   nil,   nil,
   nil,   751,   nil,   751,   751,   751,   751,   751,   751,   751,
   nil,   nil,   nil,   nil,   nil,   751,   751,   751,   751,   751,
   751,   751,   nil,   nil,   751,   nil,   nil,   nil,   nil,   nil,
   nil,   751,   nil,   nil,   751,   751,   751,   751,   751,   751,
   751,   751,   nil,   751,   751,   751,   nil,   751,   751,   751,
   751,   751,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   751,   nil,   nil,   751,   nil,   nil,   751,   751,   nil,
   nil,   751,   nil,   nil,   nil,   nil,   nil,   751,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   751,   nil,   nil,   nil,
   nil,   751,   751,   751,   751,   nil,   751,   751,   751,   751,
   nil,   nil,   nil,   nil,   751,   751,   nil,   nil,   nil,   752,
   752,   752,   751,   752,   751,   751,   751,   752,   752,   nil,
   nil,   nil,   752,   nil,   752,   752,   752,   752,   752,   752,
   752,   nil,   nil,   nil,   nil,   nil,   752,   752,   752,   752,
   752,   752,   752,   nil,   nil,   752,   nil,   nil,   nil,   nil,
   nil,   nil,   752,   nil,   nil,   752,   752,   752,   752,   752,
   752,   752,   752,   nil,   752,   752,   752,   nil,   752,   752,
   752,   752,   752,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   752,   nil,   nil,   752,   nil,   nil,   752,   752,
   nil,   nil,   752,   nil,   nil,   nil,   nil,   nil,   752,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   752,   nil,   nil,
   nil,   nil,   752,   752,   752,   752,   nil,   752,   752,   752,
   752,   nil,   nil,   nil,   nil,   752,   752,   nil,   nil,   nil,
   754,   754,   754,   752,   754,   752,   752,   752,   754,   754,
   nil,   nil,   nil,   754,   nil,   754,   754,   754,   754,   754,
   754,   754,   nil,   nil,   nil,   nil,   nil,   754,   754,   754,
   754,   754,   754,   754,   nil,   nil,   754,   nil,   nil,   nil,
   nil,   nil,   nil,   754,   nil,   nil,   754,   754,   754,   754,
   754,   754,   754,   754,   nil,   754,   754,   754,   nil,   754,
   754,   754,   754,   754,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   754,   nil,   nil,   754,   nil,   nil,   754,
   754,   nil,   nil,   754,   nil,   nil,   nil,   nil,   nil,   754,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   754,   nil,
   nil,   nil,   nil,   754,   754,   754,   754,   nil,   754,   754,
   754,   754,   nil,   nil,   nil,   nil,   754,   754,   nil,   nil,
   nil,   766,   766,   766,   754,   766,   754,   754,   754,   766,
   766,   nil,   nil,   nil,   766,   nil,   766,   766,   766,   766,
   766,   766,   766,   nil,   nil,   nil,   nil,   nil,   766,   766,
   766,   766,   766,   766,   766,   nil,   nil,   766,   nil,   nil,
   nil,   nil,   nil,   nil,   766,   nil,   nil,   766,   766,   766,
   766,   766,   766,   766,   766,   nil,   766,   766,   766,   nil,
   766,   766,   nil,   nil,   766,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   766,   nil,   nil,   766,   nil,   nil,
   766,   766,   nil,   nil,   766,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   766,   766,   766,   766,   nil,   766,
   766,   766,   766,   nil,   nil,   nil,   nil,   766,   766,   nil,
   nil,   nil,   804,   804,   804,   766,   804,   766,   766,   766,
   804,   804,   nil,   nil,   nil,   804,   nil,   804,   804,   804,
   804,   804,   804,   804,   nil,   nil,   nil,   nil,   nil,   804,
   804,   804,   804,   804,   804,   804,   nil,   nil,   804,   nil,
   nil,   nil,   nil,   nil,   nil,   804,   nil,   nil,   804,   804,
   804,   804,   804,   804,   804,   804,   nil,   804,   804,   804,
   nil,   804,   804,   804,   804,   804,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   804,   nil,   nil,   804,   nil,
   nil,   804,   804,   nil,   nil,   804,   nil,   nil,   nil,   nil,
   nil,   804,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   804,   nil,   nil,   nil,   nil,   804,   804,   804,   804,   nil,
   804,   804,   804,   804,   nil,   nil,   nil,   nil,   804,   804,
   nil,   nil,   nil,   817,   817,   817,   804,   817,   804,   804,
   804,   817,   817,   nil,   nil,   nil,   817,   nil,   817,   817,
   817,   817,   817,   817,   817,   nil,   nil,   nil,   nil,   nil,
   817,   817,   817,   817,   817,   817,   817,   nil,   nil,   817,
   nil,   nil,   nil,   nil,   nil,   nil,   817,   nil,   nil,   817,
   817,   817,   817,   817,   817,   817,   817,   nil,   817,   817,
   817,   nil,   817,   817,   817,   817,   817,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   817,   nil,   nil,   817,
   nil,   nil,   817,   817,   nil,   nil,   817,   nil,   nil,   nil,
   nil,   nil,   817,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   817,   nil,   nil,   nil,   nil,   817,   817,   817,   817,
   nil,   817,   817,   817,   817,   nil,   nil,   nil,   nil,   817,
   817,   nil,   nil,   nil,   822,   822,   822,   817,   822,   817,
   817,   817,   822,   822,   nil,   nil,   nil,   822,   nil,   822,
   822,   822,   822,   822,   822,   822,   nil,   nil,   nil,   nil,
   nil,   822,   822,   822,   822,   822,   822,   822,   nil,   nil,
   822,   nil,   nil,   nil,   nil,   nil,   nil,   822,   nil,   nil,
   822,   822,   822,   822,   822,   822,   822,   822,   nil,   822,
   822,   822,   nil,   822,   822,   822,   822,   822,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   822,   nil,   nil,
   822,   nil,   nil,   822,   822,   nil,   nil,   822,   nil,   822,
   nil,   nil,   nil,   822,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   822,   nil,   nil,   nil,   nil,   822,   822,   822,
   822,   nil,   822,   822,   822,   822,   nil,   nil,   nil,   nil,
   822,   822,   nil,   nil,   nil,   839,   839,   839,   822,   839,
   822,   822,   822,   839,   839,   nil,   nil,   nil,   839,   nil,
   839,   839,   839,   839,   839,   839,   839,   nil,   nil,   nil,
   nil,   nil,   839,   839,   839,   839,   839,   839,   839,   nil,
   nil,   839,   nil,   nil,   nil,   nil,   nil,   nil,   839,   nil,
   nil,   839,   839,   839,   839,   839,   839,   839,   839,   839,
   839,   839,   839,   nil,   839,   839,   839,   839,   839,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   839,   nil,
   nil,   839,   nil,   nil,   839,   839,   nil,   nil,   839,   nil,
   nil,   nil,   839,   nil,   839,   nil,   nil,   839,   nil,   nil,
   nil,   nil,   nil,   839,   nil,   nil,   nil,   nil,   839,   839,
   839,   839,   nil,   839,   839,   839,   839,   nil,   nil,   nil,
   nil,   839,   839,   nil,   nil,   nil,   840,   840,   840,   839,
   840,   839,   839,   839,   840,   840,   nil,   nil,   nil,   840,
   nil,   840,   840,   840,   840,   840,   840,   840,   nil,   nil,
   nil,   nil,   nil,   840,   840,   840,   840,   840,   840,   840,
   nil,   nil,   840,   nil,   nil,   nil,   nil,   nil,   nil,   840,
   nil,   nil,   840,   840,   840,   840,   840,   840,   840,   840,
   nil,   840,   840,   840,   nil,   840,   840,   840,   840,   840,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   840,
   nil,   nil,   840,   nil,   nil,   840,   840,   nil,   nil,   840,
   nil,   nil,   nil,   nil,   nil,   840,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   840,   nil,   nil,   nil,   nil,   840,
   840,   840,   840,   nil,   840,   840,   840,   840,   nil,   nil,
   nil,   nil,   840,   840,   nil,   nil,   nil,   854,   854,   854,
   840,   854,   840,   840,   840,   854,   854,   nil,   nil,   nil,
   854,   nil,   854,   854,   854,   854,   854,   854,   854,   nil,
   nil,   nil,   nil,   nil,   854,   854,   854,   854,   854,   854,
   854,   nil,   nil,   854,   nil,   nil,   nil,   nil,   nil,   nil,
   854,   nil,   nil,   854,   854,   854,   854,   854,   854,   854,
   854,   nil,   854,   854,   854,   nil,   854,   854,   nil,   nil,
   854,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   854,   nil,   nil,   854,   nil,   nil,   854,   854,   nil,   nil,
   854,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   854,   854,   854,   854,   nil,   854,   854,   854,   854,   nil,
   nil,   nil,   nil,   854,   854,   nil,   nil,   nil,   866,   866,
   866,   854,   866,   854,   854,   854,   866,   866,   nil,   nil,
   nil,   866,   nil,   866,   866,   866,   866,   866,   866,   866,
   nil,   nil,   nil,   nil,   nil,   866,   866,   866,   866,   866,
   866,   866,   nil,   nil,   866,   nil,   nil,   nil,   nil,   nil,
   nil,   866,   nil,   nil,   866,   866,   866,   866,   866,   866,
   866,   866,   nil,   866,   866,   866,   nil,   866,   866,   nil,
   nil,   866,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   866,   nil,   nil,   866,   nil,   nil,   866,   866,   nil,
   nil,   866,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   866,   866,   866,   866,   nil,   866,   866,   866,   866,
   nil,   nil,   nil,   nil,   866,   866,   nil,   nil,   nil,   975,
   975,   975,   866,   975,   866,   866,   866,   975,   975,   nil,
   nil,   nil,   975,   nil,   975,   975,   975,   975,   975,   975,
   975,   nil,   nil,   nil,   nil,   nil,   975,   975,   975,   975,
   975,   975,   975,   nil,   nil,   975,   nil,   nil,   nil,   nil,
   nil,   nil,   975,   nil,   nil,   975,   975,   975,   975,   975,
   975,   975,   975,   975,   975,   975,   975,   nil,   975,   975,
   975,   975,   975,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   975,   nil,   nil,   975,   nil,   nil,   975,   975,
   nil,   nil,   975,   nil,   975,   nil,   975,   nil,   975,   nil,
   nil,   975,   nil,   nil,   nil,   nil,   nil,   975,   nil,   nil,
   nil,   nil,   975,   975,   975,   975,   nil,   975,   975,   975,
   975,   nil,   nil,   nil,   nil,   975,   975,   nil,   nil,   nil,
   nil,   nil,   nil,   975,   nil,   975,   975,   975,     8,     8,
     8,     8,     8,     8,     8,     8,     8,     8,     8,     8,
     8,     8,     8,     8,     8,     8,     8,     8,     8,     8,
     8,     8,   nil,   nil,   nil,     8,     8,     8,     8,     8,
     8,     8,     8,     8,     8,   nil,   nil,   nil,   nil,   nil,
     8,     8,     8,     8,     8,     8,     8,     8,     8,     8,
   nil,     8,   nil,   nil,   nil,   nil,   nil,   nil,   nil,     8,
     8,   nil,     8,     8,     8,     8,     8,     8,     8,   nil,
   nil,     8,     8,   nil,   nil,   nil,     8,     8,     8,     8,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,     8,     8,   nil,     8,     8,     8,     8,
     8,     8,     8,     8,     8,     8,     8,     8,   nil,   nil,
     8,     8,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,     8,     9,     9,     9,     9,
     9,     9,     9,     9,     9,     9,     9,     9,     9,     9,
     9,     9,     9,     9,     9,     9,     9,     9,     9,     9,
   nil,   nil,   nil,     9,     9,     9,     9,     9,     9,     9,
     9,     9,     9,   nil,   nil,   nil,   nil,   nil,     9,     9,
     9,     9,     9,     9,     9,     9,     9,   nil,   nil,     9,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,     9,     9,   nil,
     9,     9,     9,     9,     9,     9,     9,   nil,   nil,     9,
     9,   nil,   nil,   nil,     9,     9,     9,     9,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,     9,     9,   nil,     9,     9,     9,     9,     9,     9,
     9,     9,     9,     9,     9,     9,   nil,   nil,     9,     9,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,     9,   398,   398,   398,   398,   398,   398,
   398,   398,   398,   398,   398,   398,   398,   398,   398,   398,
   398,   398,   398,   398,   398,   398,   398,   398,   nil,   nil,
   nil,   398,   398,   398,   398,   398,   398,   398,   398,   398,
   398,   nil,   nil,   nil,   nil,   nil,   398,   398,   398,   398,
   398,   398,   398,   398,   398,   nil,   nil,   398,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   398,   398,   nil,   398,   398,
   398,   398,   398,   398,   398,   nil,   nil,   398,   398,   nil,
   nil,   nil,   398,   398,   398,   398,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   398,
   398,   nil,   398,   398,   398,   398,   398,   398,   398,   398,
   398,   398,   398,   398,   nil,   nil,   398,   398,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   398,   586,   586,   586,   586,   586,   586,   586,   586,
   586,   586,   586,   586,   586,   586,   586,   586,   586,   586,
   586,   586,   586,   586,   586,   586,   nil,   nil,   nil,   586,
   586,   586,   586,   586,   586,   586,   586,   586,   586,   nil,
   nil,   nil,   nil,   nil,   586,   586,   586,   586,   586,   586,
   586,   586,   586,   nil,   nil,   586,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   586,   586,   nil,   586,   586,   586,   586,
   586,   586,   586,   nil,   nil,   586,   586,   nil,   nil,   nil,
   586,   586,   586,   586,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   586,   586,   nil,
   586,   586,   586,   586,   586,   586,   586,   586,   586,   586,
   586,   586,   nil,   nil,   586,   586,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   586,
    71,    71,    71,    71,    71,    71,    71,    71,    71,    71,
    71,    71,    71,    71,    71,    71,    71,    71,    71,    71,
    71,    71,    71,    71,   nil,   nil,   nil,    71,    71,    71,
    71,    71,    71,    71,    71,    71,    71,   nil,   nil,   nil,
   nil,   nil,    71,    71,    71,    71,    71,    71,    71,    71,
    71,    71,    71,    71,   nil,    71,   nil,   nil,   nil,   nil,
   nil,    71,    71,   nil,    71,    71,    71,    71,    71,    71,
    71,   nil,   nil,    71,    71,   nil,   nil,   nil,    71,    71,
    71,    71,   nil,   nil,   nil,   nil,   nil,    71,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    71,    71,   nil,    71,    71,
    71,    71,    71,    71,    71,    71,    71,    71,    71,    71,
   nil,   nil,    71,   717,   717,   717,   717,   717,   717,   717,
   717,   717,   717,   717,   717,   717,   717,   717,   717,   717,
   717,   717,   717,   717,   717,   717,   717,   nil,   nil,   nil,
   717,   717,   717,   717,   717,   717,   717,   717,   717,   717,
   nil,   nil,   nil,   nil,   nil,   717,   717,   717,   717,   717,
   717,   717,   717,   717,   nil,   nil,   717,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   717,   717,   nil,   717,   717,   717,
   717,   717,   717,   717,   nil,   nil,   717,   717,   nil,   nil,
   nil,   717,   717,   717,   717,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   717,   717,
   nil,   717,   717,   717,   717,   717,   717,   717,   717,   717,
   717,   717,   717,   212,   212,   717,   nil,   212,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   212,   212,   nil,   212,   212,
   212,   212,   212,   212,   212,   nil,   nil,   212,   212,   nil,
   nil,   nil,   212,   212,   212,   212,   nil,   nil,   nil,   nil,
   nil,   212,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   212,
   212,   nil,   212,   212,   212,   212,   212,   212,   212,   212,
   212,   212,   212,   212,   213,   213,   212,   nil,   213,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   213,   213,   nil,   213,
   213,   213,   213,   213,   213,   213,   nil,   nil,   213,   213,
   nil,   nil,   nil,   213,   213,   213,   213,   nil,   nil,   nil,
   nil,   nil,   213,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   213,   213,   nil,   213,   213,   213,   213,   213,   213,   213,
   213,   213,   213,   213,   213,   263,   263,   213,   nil,   263,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   263,   263,   nil,
   263,   263,   263,   263,   263,   263,   263,   nil,   nil,   263,
   263,   nil,   nil,   nil,   263,   263,   263,   263,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   263,   263,   nil,   263,   263,   263,   263,   263,   263,
   263,   263,   263,   263,   263,   263,   443,   443,   263,   nil,
   443,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   443,   443,
   nil,   443,   443,   443,   443,   443,   443,   443,   nil,   nil,
   443,   443,   nil,   nil,   nil,   443,   443,   443,   443,   nil,
   nil,   nil,   nil,   nil,   443,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   443,   443,   nil,   443,   443,   443,   443,   443,
   443,   443,   443,   443,   443,   443,   443,   444,   444,   443,
   nil,   444,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   444,
   444,   nil,   444,   444,   444,   444,   444,   444,   444,   nil,
   nil,   444,   444,   nil,   nil,   nil,   444,   444,   444,   444,
   nil,   nil,   nil,   nil,   nil,   444,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   444,   444,   nil,   444,   444,   444,   444,
   444,   444,   444,   444,   444,   444,   444,   444,   510,   510,
   444,   nil,   510,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   510,   510,   nil,   510,   510,   510,   510,   510,   510,   510,
   nil,   nil,   510,   510,   nil,   nil,   nil,   510,   510,   510,
   510,   nil,   nil,   nil,   nil,   nil,   510,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   510,   510,   nil,   510,   510,   510,
   510,   510,   510,   510,   510,   510,   510,   510,   510,   511,
   511,   510,   nil,   511,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   511,   511,   nil,   511,   511,   511,   511,   511,   511,
   511,   nil,   nil,   511,   511,   nil,   nil,   nil,   511,   511,
   511,   511,   nil,   nil,   nil,   nil,   nil,   511,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   511,   511,   nil,   511,   511,
   511,   511,   511,   511,   511,   511,   511,   511,   511,   511,
   520,   520,   511,   nil,   520,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   520,   520,   nil,   520,   520,   520,   520,   520,
   520,   520,   nil,   nil,   520,   520,   nil,   nil,   nil,   520,
   520,   520,   520,   nil,   nil,   nil,   nil,   nil,   520,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   520,   520,   nil,   520,
   520,   520,   520,   520,   520,   520,   520,   520,   520,   520,
   520,   521,   521,   520,   nil,   521,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   521,   521,   nil,   521,   521,   521,   521,
   521,   521,   521,   nil,   nil,   521,   521,   nil,   nil,   nil,
   521,   521,   521,   521,   nil,   nil,   nil,   nil,   nil,   521,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   521,   521,   nil,
   521,   521,   521,   521,   521,   521,   521,   521,   521,   521,
   521,   521,   548,   548,   521,   nil,   548,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   548,   548,   nil,   548,   548,   548,
   548,   548,   548,   548,   nil,   nil,   548,   548,   nil,   nil,
   nil,   548,   548,   548,   548,   nil,   nil,   nil,   nil,   nil,
   548,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   548,   548,
   nil,   548,   548,   548,   548,   548,   548,   548,   548,   548,
   548,   548,   548,   549,   549,   548,   nil,   549,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   549,   549,   nil,   549,   549,
   549,   549,   549,   549,   549,   nil,   nil,   549,   549,   nil,
   nil,   nil,   549,   549,   549,   549,   nil,   nil,   nil,   nil,
   nil,   549,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   549,
   549,   nil,   549,   549,   549,   549,   549,   549,   549,   549,
   549,   549,   549,   549,   555,   555,   549,   nil,   555,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   555,   555,   nil,   555,
   555,   555,   555,   555,   555,   555,   nil,   nil,   555,   555,
   nil,   nil,   nil,   555,   555,   555,   555,   nil,   nil,   nil,
   nil,   nil,   555,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   555,   555,   nil,   555,   555,   555,   555,   555,   555,   555,
   555,   555,   555,   555,   555,   556,   556,   555,   nil,   556,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   556,   556,   nil,
   556,   556,   556,   556,   556,   556,   556,   nil,   nil,   556,
   556,   nil,   nil,   nil,   556,   556,   556,   556,   nil,   nil,
   nil,   nil,   nil,   556,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   556,   556,   nil,   556,   556,   556,   556,   556,   556,
   556,   556,   556,   556,   556,   556,   922,   922,   556,   nil,
   922,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   922,   922,
   nil,   922,   922,   922,   922,   922,   922,   922,   nil,   nil,
   922,   922,   nil,   nil,   nil,   922,   922,   922,   922,   nil,
   nil,   nil,   nil,   nil,   922,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   922,   922,   nil,   922,   922,   922,   922,   922,
   922,   922,   922,   922,   922,   922,   922,   976,   976,   922,
   nil,   976,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   976,
   976,   nil,   976,   976,   976,   976,   976,   976,   976,   nil,
   nil,   976,   976,   nil,   nil,   nil,   976,   976,   976,   976,
   nil,   nil,   nil,   nil,   nil,   976,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   976,   976,   nil,   976,   976,   976,   976,
   976,   976,   976,   976,   976,   976,   976,   976,   977,   977,
   976,   nil,   977,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   977,   977,   nil,   977,   977,   977,   977,   977,   977,   977,
   nil,   nil,   977,   977,   nil,   nil,   nil,   977,   977,   977,
   977,   nil,   nil,   nil,   nil,   nil,   977,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   977,   977,   nil,   977,   977,   977,
   977,   977,   977,   977,   977,   977,   977,   977,   977,   nil,
   658,   977,   658,   658,   658,   658,   658,   nil,   715,   nil,
   715,   715,   715,   715,   715,   658,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   715,   nil,   716,   nil,   716,   716,   716,
   716,   716,   nil,   nil,   nil,   nil,   nil,   658,   nil,   nil,
   716,   nil,   nil,   nil,   nil,   715,   658,   658,   658,   658,
   nil,   nil,   nil,   658,   715,   715,   715,   715,   nil,   nil,
   nil,   715,   716,   nil,   798,   nil,   798,   798,   798,   798,
   798,   716,   716,   716,   716,   nil,   nil,   nil,   716,   798,
   nil,   800,   nil,   800,   800,   800,   800,   800,   nil,   845,
   nil,   845,   845,   845,   845,   845,   800,   nil,   nil,   nil,
   nil,   798,   nil,   nil,   845,   nil,   nil,   nil,   nil,   nil,
   798,   798,   798,   798,   nil,   nil,   nil,   798,   800,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   845,   800,   800,   800,
   800,   nil,   nil,   nil,   800,   845,   845,   845,   845,   nil,
   nil,   847,   845,   847,   847,   847,   847,   847,   nil,   937,
   nil,   937,   937,   937,   937,   937,   847,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   937,   nil,   941,   nil,   941,   941,
   941,   941,   941,   nil,   nil,   nil,   nil,   nil,   847,   nil,
   nil,   941,   nil,   nil,   nil,   nil,   937,   847,   847,   847,
   847,   nil,   nil,   nil,   847,   937,   937,   937,   937,   nil,
   nil,   nil,   937,   941,   nil,   943,   nil,   943,   943,   943,
   943,   943,   nil,   nil,   941,   941,   nil,   nil,   nil,   941,
   943,   nil,   961,   nil,   961,   961,   961,   961,   961,   963,
   nil,   963,   963,   963,   963,   963,   nil,   961,   nil,   nil,
   nil,   nil,   943,   nil,   963,   nil,   965,   nil,   965,   965,
   965,   965,   965,   943,   943,   nil,   nil,   nil,   943,   961,
   nil,   965,   nil,   nil,   nil,   nil,   963,   nil,   961,   961,
   961,   961,   nil,   nil,   nil,   961,   nil,   963,   963,   nil,
   nil,   nil,   963,   965,   nil,   967,   nil,   967,   967,   967,
   967,   967,   nil,   nil,   965,   965,   nil,   nil,   nil,   965,
   967,   nil,  1005,   nil,  1005,  1005,  1005,  1005,  1005,  1015,
   nil,  1015,  1015,  1015,  1015,  1015,   nil,  1005,   nil,   nil,
   nil,   nil,   967,   nil,  1015,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   967,   967,   nil,   nil,   nil,   967,  1005,
   nil,   nil,   nil,   nil,   nil,   nil,  1015,   nil,   nil,   nil,
  1005,  1005,   nil,   nil,   nil,  1005,   nil,  1015,  1015,   nil,
   nil,   nil,  1015 ]

racc_action_pointer = [
  1637,    33,   nil,    81,   nil,  5976,  1388,   -51, 23086, 23214,
   -11,   nil,    50,   117,   572,   -81,   105,   309,   nil,   -71,
  6107,  2057,   230,   nil,   -62,   nil,    -8,   742,   852,  6238,
  6369,  6500,   nil,  1777,  6631,  6762,   nil,   134,   282,   352,
   247,   332,  6901,  7032,  7163,   191,   574,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   962,   nil,   -80,  7294,
  7425,     4,   nil,  7556,  7687,   nil,   nil,  7818,  7957,  8088,
  8219, 23598,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   223,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,     0,   nil,   nil,
   112,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   354,   nil,  8358,   nil,   nil,   nil,   nil,  8497,  8628,
  8759,  8890,  9029,  1917,   nil,   576,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   223,   nil,  2057,  9160,
  9291,  9422, 23772, 23833,   nil,   nil,  9553,  9684,  9815,  9946,
 10077, 10208,   nil,   nil,   576,   -54,   138,   307,   166,   241,
   309,   nil, 10339,  2197,   310, 10470, 10601, 10732, 10863, 10994,
 11125, 11256, 11387, 11518, 11649, 11780, 11911, 12042, 12173, 12304,
 12435, 12566, 12697, 12828, 12959, 13090, 13221, 13352, 13483, 13614,
 13745,   nil,   nil, 23894,   nil,   nil,   318, 13876, 14007,   nil,
   nil,   nil,   nil,   nil,   nil,   nil, 14138,   nil,  2197,   nil,
   297,   325,   nil, 14269,   373, 14400,   nil,   nil, 14531, 14662,
   nil,   nil,   228,   nil, 14801,  1441,   358,   338,  2337,   353,
   408,   377, 14932,  2477,   615,   645,   714,   473,   790,   nil,
   441,   417,    33,   nil,   nil,   nil,   476,   360,   453, 15071,
   nil,   472,   522,   822,   nil,   526,   nil, 15202,  2617, 15333,
   465,   nil,   -73,   146,   506,   489,   387,   523,   nil,   nil,
   346,    -1,    11, 15464, 15595,   298,   603,   498,   -18,    11,
   824,   584,    25,   618,   nil,   nil,   342,   434,   -21,   nil,
   900,   nil,   541, 15726,   nil,   nil,   nil,   194,   230,   379,
   413,   486,   510,   577,   578,   582,   nil,   619,   nil, 15857,
   nil,   272,   456,   459,   465,   497,   -41,   -35,   501,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   537, 23342,   nil,
   nil,   nil,   nil,   544,   nil,   nil,   535, 15988,   551,   nil,
   nil,  1777,   563,   nil,   568,   590,   481,   552,  1098,   nil,
   nil,   nil,   222,   334,   638,   nil,   nil,  1230,  1366,   nil,
  2337,   nil,   587,   nil,   nil,  1637,   nil,   nil,   nil,   nil,
   -35,   nil,   650, 23955, 24016, 16119,   197, 16250, 16381, 16512,
  4017,  4157,   623,   662,   677,   678,   679,   682,  1518,  5697,
  1470,  4297,  1181,  1315,  4437,  4577,  4717,  4857,  4997,  5137,
  5277,  1051,  1249,  5417,  5557,  2477,   -54,  1502,   nil,   nil,
   nil,   nil,   629,   nil,   -53,   -10,   636,   nil,   nil, 16643,
   nil, 16774,   nil, 16905,   nil,   363,   nil,   nil,   nil, 17044,
  1507,  2757,   642,   640,   nil,   nil,   644, 17183,   650, 17314,
 24077, 24138,   930,   704,   nil, 17445,   661,   nil, 17576, 17707,
 24199, 24260,  2617, 17838,   788,   790,   570,   715,   nil, 17969,
   nil,   nil, 18100,   nil,   nil,   nil,   nil,  1505,  2897,   810,
   nil,  3037,    62,   147,   811,   819, 18231, 18362, 24321, 24382,
    27,   nil,   nil,   932,   nil, 24443, 24504, 18493,   nil,   nil,
   250,  3177,   741,   nil,   -33,   nil,   nil,   nil,   717,   nil,
   nil,   nil,   714,   nil,   nil,   388,   nil,   390,   nil,   nil,
   700,   nil,   705,   nil,   nil,   nil, 23470,   nil,   708, 18624,
 18755,   619,   749, 18886, 19017, 19148, 19279,   761,   nil,   nil,
 19410, 19541,   762,   nil, 19672, 19803,   nil,   nil,   217,   301,
   466,   604,   732,  1917,   731,   nil,  1466,   nil,  3317,   843,
     6,   160,   nil,  3457,  3597,   nil,   738,   nil,   789, 19934,
   nil,   nil, 20065,   nil,   767,   -80, 20196,   748,   nil,   752,
   123,   180,   796,   248,  1106,   797,   754, 20327,  2757,   824,
   214,   882, 20458,   nil,   767,   nil,   396,   nil, 24749,   nil,
   765,   767,   nil,   769,   770,   771,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   762,   749,   nil,   nil, 20589,   nil,
   nil,   nil,   861,   nil,   nil,   nil,   863,   nil,   nil,   865,
   623,   nil,   919,   nil,   nil,   nil,   nil,   928,   nil,    26,
   808,    41,    68,   151,   185, 20720,  1066,  1143,   nil,   810,
  3737, 20851,   nil,   934,  3877, 24757, 24774, 23711,   nil,   nil,
   nil,   nil,   nil,   nil,  4017,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   814, 20982,  2897, 21113,   nil,   816,   nil,  3037,
   nil,  3177,   nil,   nil,  3317,   nil,  3457,   nil,  3597, 21244,
 21375, 21506, 21637,   343, 21768,   818,   823,   nil,   834,   835,
   839,   nil,   864,   850,   846,   843, 21899,   nil,   nil,   982,
   nil,   nil,  4157,   879,   986,   nil,   nil,   nil,   nil,   863,
   236,   nil,   nil,   989,   nil,  4297,   867,   919,   nil,   nil,
   918,   nil,    89,   880,   697,   nil,   nil,   564, 24813,   nil,
 24830,   nil,  6809,   nil, 22030,   nil,   856,   nil,   879,   335,
   883,   nil,   nil,   nil,   nil,  1006,   nil, 22161,  1008,  4437,
  4577,   nil, 22292,  4717,   152,   181,   nil,  1010,   327,  4857,
   nil,  1011,   891,   366,   nil,   895,   891,   nil,  3737, 22423,
 22554,  3877,  1022,   nil,   nil, 24838,   nil, 24890,   nil,  8266,
   nil,   nil,   917,  1076, 22685,   934,   992,   nil,   935,   nil,
   nil,   nil,  4997,   nil,   nil,    32, 22816,   nil,   nil,   nil,
   nil,   nil,   961,   920,   nil,   nil,   nil,   921,   924,   nil,
   928,   930,   nil,   933,   nil,   nil,   947,  1162,   951,   735,
   nil,   nil,    33,   nil,  1077,  1081,   nil,   303,   nil,   nil,
   nil,  1091,   nil,   nil,   nil,  1012,   nil,   969,   nil,   nil,
   970,   976,   980,   982,   nil,   985,   nil,   421,   nil,   nil,
   nil,   966, 24565,   nil,   nil,   nil,  5137,    34,    35,  1003,
  1064,    36,   nil,  5277,  5417,   nil,   nil, 24898,   nil, 14979,
   nil, 24915,   nil, 24954,   nil,   nil,   nil,   nil,   398,  1028,
   987,  5557,   nil,   nil,   nil,   nil,   nil,  5697,   nil,  5837,
   nil, 24971,   nil, 24978,   nil, 24995,   nil, 25034,   nil,   nil,
   nil,  1332,  1039,  1043,  1129, 22947, 24626, 24687,    42,  1029,
  1135,  1013,  1014,  1015,  1018,  1019,  1223,  1025,  1437,   719,
  1151,  1152,  1029,  1030,  1046,  1051,   nil,   nil,  1056,    98,
   102,   111,   138,   nil,   nil, 25051,   nil,   nil,   nil,   nil,
  4097,  1056,   nil,   nil,   nil, 25058,   nil,   nil,   nil,   nil,
   145,  1060,  1064,  1069,   nil,   nil ]

racc_action_default = [
    -3,  -601,    -1,  -587,    -4,  -601,    -7,  -601,  -601,  -601,
  -601,   -29,  -601,  -601,  -601,  -279,  -601,   -40,   -43,  -589,
  -601,   -48,   -50,   -51,   -52,   -56,  -256,  -256,  -256,  -293,
  -329,  -330,   -68,   -11,   -72,   -80,   -82,  -601,  -491,  -492,
  -601,  -601,  -601,  -601,  -601,  -589,  -237,  -270,  -271,  -272,
  -273,  -274,  -275,  -276,  -277,  -278,  -575,  -281,  -283,  -600,
  -565,  -301,  -303,  -601,  -601,  -307,  -310,  -587,  -601,  -601,
  -601,  -601,  -331,  -332,  -334,  -335,  -432,  -433,  -434,  -435,
  -436,  -457,  -439,  -440,  -459,  -461,  -444,  -449,  -453,  -455,
  -471,  -459,  -473,  -475,  -476,  -477,  -478,  -573,  -480,  -481,
  -574,  -483,  -484,  -485,  -486,  -487,  -488,  -489,  -490,  -495,
  -496,  -601,    -2,  -588,  -596,  -597,  -598,    -6,  -601,  -601,
  -601,  -601,  -601,    -3,   -17,  -601,  -111,  -112,  -113,  -114,
  -115,  -116,  -117,  -118,  -119,  -123,  -124,  -125,  -126,  -127,
  -128,  -129,  -130,  -131,  -132,  -133,  -134,  -135,  -136,  -137,
  -138,  -139,  -140,  -141,  -142,  -143,  -144,  -145,  -146,  -147,
  -148,  -149,  -150,  -151,  -152,  -153,  -154,  -155,  -156,  -157,
  -158,  -159,  -160,  -161,  -162,  -163,  -164,  -165,  -166,  -167,
  -168,  -169,  -170,  -171,  -172,  -173,  -174,  -175,  -176,  -177,
  -178,  -179,  -180,  -181,  -182,  -183,  -184,  -185,  -186,  -187,
  -188,  -189,  -190,  -191,  -192,  -193,   -22,  -120,   -11,  -601,
  -601,  -246,  -601,  -601,  -585,  -586,  -601,  -601,  -601,  -601,
  -601,  -589,  -590,   -47,  -601,  -491,  -492,  -601,  -279,  -601,
  -601,  -229,  -601,   -11,  -601,  -601,  -601,  -601,  -601,  -601,
  -601,  -601,  -601,  -601,  -601,  -601,  -601,  -601,  -601,  -601,
  -601,  -601,  -601,  -601,  -601,  -601,  -601,  -601,  -601,  -601,
  -601,  -401,  -403,  -601,  -583,  -584,   -57,  -246,  -601,  -300,
  -407,  -416,  -418,   -63,  -413,   -64,  -589,   -65,  -238,  -251,
  -260,  -260,  -255,  -601,  -261,  -601,  -457,  -567,  -601,  -601,
   -66,   -67,  -587,   -12,  -601,   -15,  -601,   -70,   -11,  -589,
  -601,   -73,   -76,   -11,   -88,   -89,  -601,  -601,   -96,  -293,
  -296,  -589,  -601,  -329,  -330,  -333,  -414,  -601,   -78,  -601,
   -84,  -290,  -474,  -601,  -214,  -215,  -230,  -601,   -11,  -601,
  -589,  -239,  -593,  -593,  -601,  -601,  -593,  -601,  -302,  -392,
   -49,  -601,  -601,  -601,  -601,  -587,  -601,  -588,  -491,  -492,
  -601,  -601,  -279,  -601,  -345,  -346,  -106,  -107,  -601,  -109,
  -601,  -279,  -499,  -601,  -491,  -492,  -322,  -111,  -112,  -153,
  -154,  -155,  -171,  -176,  -183,  -186,  -324,  -601,  -563,  -601,
  -437,  -601,  -601,  -601,  -601,  -601,  -601,  -601,  -601,  1026,
    -5,  -599,   -23,   -24,   -25,   -26,   -27,  -601,  -601,   -19,
   -20,   -21,  -121,  -601,   -30,   -39,  -266,  -601,  -601,  -265,
   -31,  -196,  -589,  -247,  -260,  -260,  -576,  -577,  -256,  -411,
  -578,  -579,  -577,  -576,  -256,  -410,  -412,  -578,  -579,   -37,
  -204,   -38,  -601,   -41,   -42,  -194,  -261,   -44,   -45,   -46,
  -589,  -299,  -601,  -601,  -601,  -246,  -290,  -601,  -601,  -601,
  -205,  -206,  -207,  -208,  -209,  -210,  -211,  -212,  -216,  -217,
  -218,  -219,  -220,  -221,  -222,  -223,  -224,  -225,  -226,  -227,
  -228,  -231,  -232,  -233,  -234,  -589,  -381,  -256,  -576,  -577,
   -54,   -58,  -589,  -257,  -381,  -381,  -589,  -295,  -252,  -601,
  -253,  -601,  -258,  -601,  -262,  -601,  -570,  -572,   -10,  -588,
   -14,    -3,  -589,   -69,  -288,   -85,   -74,  -601,  -589,  -246,
  -601,  -601,   -95,  -601,  -474,  -601,   -81,   -86,  -601,  -601,
  -601,  -601,  -235,  -601,  -424,  -601,  -284,  -601,  -240,  -595,
  -594,  -242,  -595,  -291,  -292,  -566,  -304,  -523,   -11,  -336,
  -337,   -11,  -601,  -601,  -601,  -601,  -601,  -246,  -601,  -601,
  -290,  -315,  -106,  -107,  -108,  -601,  -601,  -246,  -318,  -497,
  -601,   -11,  -501,  -326,  -589,  -438,  -458,  -463,  -601,  -465,
  -441,  -460,  -601,  -462,  -443,  -601,  -446,  -601,  -448,  -451,
  -601,  -452,  -601,  -472,    -8,   -18,  -601,   -28,  -269,  -601,
  -601,  -415,  -601,  -248,  -250,  -601,  -601,   -59,  -245,  -408,
  -601,  -601,   -61,  -409,  -601,  -601,  -298,  -591,  -576,  -577,
  -576,  -577,  -589,  -194,  -601,  -382,  -589,  -384,   -11,   -53,
  -404,  -381,  -243,   -11,   -11,  -294,  -260,  -259,  -263,  -601,
  -568,  -569,  -601,   -13,  -601,   -71,  -601,   -77,   -83,  -589,
  -576,  -577,  -244,  -580,   -94,  -601,   -79,  -601,  -203,  -213,
  -589,  -600,  -600,  -282,  -589,  -287,  -593,  -393,  -523,  -396,
  -562,  -562,  -506,  -508,  -508,  -508,  -522,  -524,  -525,  -526,
  -527,  -528,  -529,  -530,  -531,  -601,  -533,  -535,  -537,  -542,
  -544,  -545,  -547,  -552,  -554,  -555,  -557,  -558,  -559,  -601,
  -600,  -338,  -600,  -308,  -339,  -340,  -311,  -601,  -314,  -601,
  -589,  -576,  -577,  -580,  -289,  -601,  -106,  -107,  -110,  -589,
   -11,  -601,  -320,  -601,   -11,  -523,  -523,  -601,  -564,  -464,
  -467,  -468,  -469,  -470,   -11,  -442,  -445,  -447,  -450,  -454,
  -456,  -122,  -267,  -601,  -197,  -601,  -592,  -260,   -33,  -199,
   -34,  -200,   -60,   -35,  -202,   -36,  -201,   -62,  -195,  -601,
  -601,  -601,  -601,  -415,  -601,  -562,  -562,  -363,  -365,  -365,
  -365,  -380,  -601,  -589,  -386,  -531,  -539,  -540,  -550,  -601,
  -406,  -405,   -11,  -601,  -601,  -254,  -264,  -571,   -16,   -75,
  -415,   -87,  -297,  -600,  -343,   -11,  -425,  -600,  -426,  -427,
  -601,  -241,  -601,  -589,  -601,  -504,  -505,  -601,  -601,  -515,
  -601,  -518,  -601,  -520,  -601,  -347,  -601,  -349,  -351,  -358,
  -589,  -536,  -546,  -556,  -560,  -601,  -341,  -601,  -601,   -11,
   -11,  -313,  -601,   -11,  -415,  -601,  -415,  -601,  -601,   -11,
  -323,  -601,  -589,  -601,  -327,  -601,  -268,   -32,  -198,  -249,
  -601,  -236,  -601,  -361,  -362,  -371,  -373,  -601,  -376,  -601,
  -378,  -383,  -601,  -601,  -601,  -538,  -601,  -402,  -601,  -417,
  -419,    -9,   -11,  -431,  -344,  -601,  -601,  -429,  -285,  -394,
  -397,  -399,  -601,  -562,  -543,  -561,  -507,  -508,  -508,  -534,
  -508,  -508,  -553,  -508,  -531,  -548,  -589,  -601,  -356,  -601,
  -532,  -305,  -601,  -306,  -601,  -601,  -263,  -600,  -316,  -319,
  -498,  -601,  -325,  -500,  -502,  -501,  -466,  -562,  -541,  -364,
  -365,  -365,  -365,  -365,  -551,  -365,  -385,  -589,  -388,  -390,
  -391,  -549,  -601,  -290,   -55,  -430,   -11,  -491,  -492,  -601,
  -601,  -279,  -428,   -11,   -11,  -395,  -503,  -601,  -511,  -601,
  -513,  -601,  -516,  -601,  -519,  -521,  -348,  -350,  -354,  -601,
  -359,   -11,  -309,  -312,  -420,  -421,  -422,   -11,  -321,   -11,
  -360,  -601,  -368,  -601,  -370,  -601,  -374,  -601,  -377,  -379,
  -387,  -601,  -289,  -580,  -424,  -246,  -601,  -601,  -290,  -601,
  -601,  -508,  -508,  -508,  -508,  -352,  -601,  -357,  -601,  -600,
  -601,  -601,  -365,  -365,  -365,  -365,  -389,  -423,  -589,  -576,
  -577,  -580,  -289,  -398,  -400,  -601,  -509,  -512,  -514,  -517,
  -601,  -355,  -342,  -317,  -328,  -601,  -366,  -369,  -372,  -375,
  -415,  -508,  -353,  -365,  -510,  -367 ]

racc_goto_table = [
   220,   376,   525,    14,   279,   279,   279,   212,    14,   338,
   591,   262,   124,   207,   490,   315,   315,   264,   538,   541,
   481,   412,   263,   224,     2,   331,   327,   301,   545,   418,
   424,   409,   224,   224,   224,   434,    14,   306,   306,   431,
   341,   342,   651,   714,   345,   116,   132,   132,   322,   315,
   315,   315,   134,   134,   129,   129,   815,   516,     6,   113,
   766,   759,   627,     6,   627,   630,   224,   224,   223,   381,
   224,   350,   360,   360,   297,   693,   696,   482,   318,   112,
   477,   554,   117,   340,   340,   762,   918,   340,   882,   280,
   280,   280,   914,   947,   783,   392,   393,   394,   395,   810,
   920,   362,   366,   266,   273,   275,   630,   528,   531,   116,
     1,   535,   950,   575,   577,   861,    14,   129,   879,   332,
   299,   224,   224,   224,   224,    14,    14,   633,   618,   270,
   274,   659,   277,   290,   291,   818,   623,   624,   340,   340,
   340,   340,   382,   346,   206,   355,   405,   397,   490,   388,
   398,   666,   621,   586,   334,   620,   378,   335,   536,   353,
   558,   377,   328,   329,   654,   879,   627,   627,   630,   330,
   339,     6,   987,   343,   819,   344,   820,   281,   281,   281,
   396,     6,   705,   957,   710,   829,   279,   561,   562,   717,
   390,   905,   761,   763,   947,    13,   571,   573,   576,   576,
    13,   917,   571,   657,   996,   869,   914,   333,   336,  1011,
   753,    14,   224,   224,   224,   537,   760,   443,   920,   224,
   224,   224,   224,   224,   224,   882,   697,   792,    13,   933,
   886,   934,   852,  1022,   476,   713,    14,   780,   437,   438,
   439,   440,   279,   279,   484,   637,   485,   954,   787,   865,
   380,   279,   793,   646,   383,   612,   384,   404,   410,   428,
   418,   424,   872,   879,   429,   433,   795,   796,   300,   408,
   224,   224,   666,   772,   495,   385,   386,   408,   708,   224,
   387,   719,   724,   315,   711,   877,   766,   879,   874,   766,
   910,   766,   513,   766,   908,   nil,   nil,    14,   824,   510,
   315,    14,   nil,   nil,   nil,   306,    14,   826,    13,   832,
   833,   527,   517,   nil,   nil,   419,   520,    13,    13,   639,
   542,   543,   306,   nil,   514,   nil,   nil,   642,   280,   666,
   666,    14,   224,   nil,   116,   nil,   280,   642,   nil,   297,
   955,   nil,   nil,   548,   297,   506,   224,   224,   499,   526,
   nil,   742,   500,   nil,   nil,  1012,   747,   700,   898,   775,
   nil,   843,   844,   340,   340,   642,   224,   709,   498,   nil,
   264,   483,   nil,   642,   779,   563,   823,   nil,   nil,   486,
   805,   nil,   224,   560,   827,   502,   959,   116,   831,   727,
   508,   727,   nil,   592,   nil,   nil,   nil,   nil,   878,   564,
   880,   nil,   585,    13,   nil,   766,   nil,   766,   nil,   766,
   nil,   766,   627,   630,   nil,   270,   281,   nil,   nil,   274,
   279,   544,   434,   nil,   281,   nil,   nil,   nil,    13,   nil,
   nil,   791,   nil,   nil,   nil,   nil,   132,   nil,   nil,   nil,
   nil,   nil,   134,   403,   129,   911,   nil,   912,   224,   nil,
   nil,   nil,    26,   nil,   nil,   nil,   614,    26,   nil,   766,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   300,   nil,
   775,   nil,    26,   nil,   788,   nil,   nil,   nil,   nil,   936,
   nil,    26,    26,    26,   279,    26,   nil,   nil,   315,    13,
   428,   nil,   997,    13,   nil,   597,   315,   nil,    13,   nil,
   517,   602,    14,   901,    14,   nil,   nil,   nil,   517,   nil,
   306,   805,   224,   960,   888,    26,    26,   nil,   306,    26,
   nil,   598,   279,    13,   nil,   634,   224,   603,   nil,   nil,
   nil,   756,   279,   300,   nil,   nil,   nil,   981,   300,   nil,
   nil,    14,   926,   650,    14,   nil,   419,   nil,   nil,   nil,
   224,   nil,   nil,   nil,   619,   nil,   nil,   428,   nil,     6,
   224,   992,   nil,   nil,    14,    26,   nil,   428,   771,   951,
    26,    26,    26,    26,    26,    26,   nil,   nil,   nil,   956,
   598,   nil,   nil,   nil,   694,   694,   nil,   nil,   nil,   nil,
   731,   nil,   805,   592,   805,   428,  1020,   764,   224,   224,
   nil,   428,   712,   224,   224,   nil,   699,   224,   770,   nil,
   nil,   nil,   nil,   419,   nil,   nil,   nil,   315,   789,   nil,
   592,    14,   nil,   419,   132,   nil,    14,    14,   315,   517,
   134,   nil,   129,   991,   nil,   790,   nil,   626,   nil,   306,
   781,   nil,   nil,   738,   740,   nil,   nil,   834,   743,   745,
   306,   419,   433,   985,   805,   846,   848,   850,   nil,   419,
    26,    26,    26,    26,   nil,   nil,   nil,   nil,    26,    26,
    26,    26,    26,    26,   nil,   nil,   nil,   656,   nil,   nil,
   nil,   592,   825,   nil,   nil,    26,   nil,   nil,   828,   nil,
   592,   805,   nil,   805,    13,   nil,    13,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   224,   873,
   nil,   nil,   408,    14,   224,   805,   nil,    14,   nil,    26,
    26,   nil,   nil,   nil,   nil,   340,   nil,    14,    26,   nil,
   nil,   340,    15,    13,   nil,   674,    13,    15,   224,   nil,
   nil,   737,   nil,   nil,   764,   598,    26,   315,   603,   863,
    26,   441,   nil,   867,   nil,    26,    13,   907,   nil,   nil,
   756,   nil,   756,   129,   756,    15,   308,   308,   nil,   855,
   nil,   757,   nil,   690,   764,    14,   692,   nil,   nil,   nil,
    26,    26,   nil,   837,   nil,   998,   nil,   nil,    14,   nil,
   nil,   nil,   nil,   642,   892,    26,    26,   nil,   nil,   nil,
   352,   361,   361,   nil,   nil,   nil,   487,   962,   964,   966,
   968,    16,   969,    13,   765,    26,    16,   nil,    13,    13,
   224,   nil,    14,    14,   nil,   nil,    14,   nil,   nil,   503,
   nil,    26,    14,   nil,   nil,   315,   nil,   340,   nil,   nil,
   nil,   nil,   nil,   nil,    16,    15,   nil,   315,   nil,   nil,
   nil,   nil,   nil,   769,    15,    15,   674,   921,   773,   774,
   nil,   nil,   nil,   nil,   nil,    14,   nil,   694,   nil,   929,
   900,   nil,   nil,   nil,   nil,   904,   756,   nil,   756,   354,
   756,   nil,   756,   nil,   799,   801,   803,   nil,   nil,  1016,
  1017,  1018,  1019,   nil,   nil,   nil,   nil,    26,   970,   nil,
   nil,   nil,   nil,   nil,   nil,    13,   nil,   nil,   nil,    13,
   nil,   nil,   nil,   674,   674,   nil,   nil,   nil,   nil,    13,
  1025,   nil,   976,   nil,    16,   nil,   nil,   nil,   nil,    14,
   756,   nil,   nil,    16,    16,   nil,    14,    14,   nil,   nil,
    15,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   875,
   279,    26,   875,    26,    14,   nil,   nil,   nil,   nil,   835,
    14,    26,    14,   nil,   nil,    15,   nil,    13,   428,   nil,
   606,   nil,   nil,   nil,   nil,    26,   nil,   nil,   224,   592,
    13,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    26,   nil,   nil,    26,   nil,   nil,   674,   875,   674,    26,
   757,   nil,   757,   nil,   757,   nil,   nil,   858,   nil,    26,
   nil,   nil,   622,    26,    13,    13,   625,   nil,    13,    16,
   864,   nil,   nil,   428,    13,   nil,    15,   nil,   432,   nil,
    15,   nil,   635,   nil,   308,    15,   nil,   nil,   638,   nil,
   nil,   nil,   nil,   765,    16,   765,   nil,    26,    26,   nil,
   nil,   308,    26,    26,   894,   895,    26,    13,   897,   nil,
    15,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    26,   nil,   nil,   nil,   nil,    26,    26,   nil,   nil,   419,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   718,   nil,   758,   925,   938,   940,
   nil,   942,   944,    38,   945,    16,   nil,   nil,    38,    16,
   nil,   nil,   nil,   nil,    16,   nil,   757,   nil,   757,   nil,
   757,    13,   757,   nil,   nil,   nil,   nil,   nil,    13,    13,
   nil,   nil,   nil,   nil,   nil,   674,    38,   304,   304,    16,
   nil,   nil,   337,   nil,   nil,   nil,    13,   nil,   nil,   nil,
   nil,   nil,    13,   nil,    13,   nil,   nil,    26,   nil,   765,
   nil,   974,    26,    26,   nil,   nil,    26,   nil,   979,   980,
   757,   348,   364,   364,   364,   nil,    26,   nil,   nil,   nil,
   782,   nil,   nil,   nil,   nil,   nil,   989,    26,   nil,   nil,
   nil,   nil,   990,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,  1006,  1007,  1008,  1009,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    38,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    26,    38,    38,   nil,   nil,   nil,
   nil,    15,   nil,    15,   nil,   nil,   nil,    26,   nil,   308,
   nil,   nil,  1024,   nil,   nil,   nil,   nil,   308,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,
    15,    26,    26,    15,   nil,    26,   nil,   nil,   nil,   nil,
   881,    26,   883,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   406,   nil,    15,   nil,   nil,   nil,   nil,   nil,   436,
   723,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    16,    38,    16,   nil,    26,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   913,   nil,   915,
   nil,   nil,   nil,   nil,   nil,   nil,    38,   nil,   nil,   nil,
   890,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    16,
    15,   nil,    16,   nil,   nil,    15,    15,   nil,   nil,   nil,
   nil,   nil,   903,   nil,   nil,   492,   nil,   494,   308,   nil,
   496,   497,    16,   nil,   nil,   nil,   nil,   nil,    26,   308,
   nil,   nil,   nil,   nil,   nil,    26,    26,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    38,   nil,   nil,
   nil,    38,   nil,    26,   nil,   304,    38,   nil,   nil,    26,
   nil,    26,   nil,   nil,   nil,   432,   946,   982,   nil,   983,
   nil,   984,   304,   nil,   nil,   nil,   nil,    26,   nil,    16,
   nil,    38,   nil,   nil,    16,    16,   nil,   nil,   nil,   nil,
   nil,   993,    15,   994,   nil,   995,    15,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    15,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    39,   nil,   nil,
   nil,   nil,    39,   nil,   nil,  1021,   nil,   nil,   nil,   588,
   nil,   nil,   nil,   nil,   nil,  1023,   nil,   nil,   361,   nil,
   nil,   nil,   nil,   nil,    15,   nil,   nil,   nil,   nil,   nil,
    39,   305,   305,   nil,   nil,   nil,   nil,    15,   nil,   nil,
   nil,    16,   nil,   nil,   nil,    16,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    16,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   349,   365,   365,   365,   nil,
   nil,    15,    15,   nil,   nil,    15,   nil,   nil,   nil,   nil,
   nil,    15,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   628,   nil,   337,   nil,   631,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    16,   nil,   nil,   361,   nil,   nil,   nil,
    39,   nil,   nil,   nil,    15,   nil,    16,   nil,   931,    39,
    39,   nil,    38,   nil,    38,   nil,   nil,   nil,   nil,   nil,
   304,   628,   nil,   nil,   337,   nil,   nil,   nil,   304,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   436,   nil,
    16,    16,   nil,   nil,    16,   nil,   nil,   nil,   nil,   nil,
    16,    38,   nil,   nil,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    15,   nil,
   nil,   nil,   nil,   nil,    38,    15,    15,   nil,   nil,   nil,
   nil,   732,   nil,    16,   nil,   628,   337,   932,   nil,   nil,
   nil,   nil,   nil,    15,   nil,    39,   nil,   nil,   nil,    15,
   nil,    15,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    39,   776,   nil,   nil,   777,   nil,   nil,   nil,   nil,   nil,
   nil,    38,   nil,   nil,   nil,   nil,    38,    38,   nil,   nil,
   nil,   nil,   nil,   nil,   786,   nil,   nil,    16,   nil,   304,
   nil,   nil,   nil,   nil,    16,    16,   nil,   nil,   nil,   nil,
   304,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   811,   nil,    16,   nil,   nil,   nil,   nil,   nil,    16,   nil,
    16,    39,   nil,   nil,   nil,    39,   nil,   nil,   nil,   305,
    39,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   305,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    39,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    38,   nil,   836,   nil,    38,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    38,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   364,
   nil,   nil,   nil,   nil,   nil,    38,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   885,   nil,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   896,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   231,   337,    38,    38,   nil,   nil,    38,   nil,   nil,   278,
   278,   278,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   324,   325,   326,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   364,   nil,   278,
   278,   nil,   nil,   nil,   nil,    38,   nil,   nil,   nil,   927,
   nil,   nil,   nil,   nil,   nil,   nil,    39,   nil,    39,   nil,
   nil,   nil,   nil,   nil,   305,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   305,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    39,   nil,   nil,    39,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    38,
   nil,   nil,   nil,   nil,   nil,   nil,    38,    38,    39,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    38,   nil,   nil,   nil,   nil,   nil,
    38,   nil,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    39,   nil,   nil,   nil,   nil,
    39,    39,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   278,
   411,   278,   nil,   305,   nil,   nil,   430,   435,   nil,   nil,
   nil,   nil,   nil,   nil,   305,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   231,   nil,   nil,   450,   451,   452,   453,   454,
   455,   456,   457,   458,   459,   460,   461,   462,   463,   464,
   465,   466,   467,   468,   469,   470,   471,   472,   473,   474,
   475,   nil,   nil,   nil,   nil,   nil,   nil,   278,   278,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   278,   nil,   nil,   nil,
   nil,   nil,   nil,   278,   nil,   278,   nil,    39,   278,   278,
   nil,    39,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    39,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   522,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   365,   nil,   nil,   nil,   nil,   nil,    39,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    39,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    39,    39,   nil,   nil,
    39,   nil,   nil,   nil,   nil,   nil,    39,   278,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   365,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    39,
   nil,   nil,   nil,   928,   nil,   278,   nil,   430,   613,   411,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   278,
   nil,   278,   nil,   278,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    39,   nil,   nil,   nil,   nil,   nil,   278,
    39,    39,   nil,   nil,   nil,   nil,   nil,   nil,   648,   649,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    39,   278,
   nil,   nil,   278,   nil,    39,   nil,    39,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   278,   278,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   278,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   278,
   734,   nil,   nil,   278,   278,   739,   741,   nil,   nil,   nil,
   744,   746,   nil,   nil,   613,   748,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   278,
   nil,   nil,   278,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   278,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   278,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   278,   nil,   838,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   739,
   741,   746,   744,   nil,   841,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   278,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   278,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   278,
   838,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   278 ]

racc_goto_check = [
    32,    57,     8,    22,    34,    34,    34,    25,    22,    87,
    24,   142,    15,    15,    74,    65,    65,    25,    90,    90,
    40,    23,    37,    22,     2,    68,    32,    50,    93,    38,
    38,    28,    22,    22,    22,    18,    22,    22,    22,    28,
    17,    17,    10,    99,    17,    97,    61,    61,    64,    65,
    65,    65,    62,    62,    58,    58,    91,    52,     7,     6,
   174,   130,    75,     7,    75,   182,    22,    22,    20,   155,
    22,    22,    22,    22,    46,    92,    92,    23,    51,     4,
    38,    55,     5,    30,    30,   133,   135,    30,   178,    69,
    69,    69,   179,   118,    11,    17,    17,    17,    17,   120,
   136,    56,    56,    39,    39,    39,   182,    70,    70,    97,
     1,    70,   121,   159,   159,    12,    22,    58,   173,    69,
    47,    22,    22,    22,    22,    22,    22,    14,    41,    72,
    72,   141,    45,    45,    45,    11,    41,    41,    30,    30,
    30,    30,   156,     4,    16,    19,    29,     2,    74,   156,
    31,   167,    43,    63,    67,    73,    85,    86,    89,    94,
    96,   100,   101,   102,   103,   173,    75,    75,   182,   104,
   105,     7,   121,   106,   107,   108,   109,    71,    71,    71,
     7,     7,   110,   111,   112,   113,    34,   114,   115,   116,
     5,   117,   122,   128,   118,    21,   160,   160,   160,   160,
    21,   134,   160,   137,   135,   138,   179,    71,    71,   121,
    24,    22,    22,    22,    22,   139,   131,    25,   136,    22,
    22,    22,    22,    22,    22,   178,    93,   140,    21,   143,
   120,   144,   133,   121,   145,     8,    22,    24,    30,    30,
    30,    30,    34,    34,   147,    52,   148,   149,   150,   151,
   154,    34,   141,    52,   157,    23,   158,    20,    20,    58,
    38,    38,   133,   173,    20,    20,   125,   125,     9,    69,
    22,    22,   167,    41,   155,   161,   162,    69,    55,    22,
   163,   164,   165,    65,   166,   171,   174,   173,   175,   174,
   130,   174,    32,   174,   176,   nil,   nil,    22,    24,    25,
    65,    22,   nil,   nil,   nil,    22,    22,    24,    21,   141,
   141,    32,    50,   nil,   nil,    72,    25,    21,    21,    23,
    17,    17,    22,   nil,    64,   nil,   nil,    38,    69,   167,
   167,    22,    22,   nil,    97,   nil,    69,    38,   nil,    46,
    11,   nil,   nil,    25,    46,    51,    22,    22,     6,    30,
   nil,    40,     7,   nil,   nil,    91,    40,    23,    92,    74,
   nil,   125,   125,    30,    30,    38,    22,    23,     4,   nil,
    25,    45,   nil,    38,    52,    37,    90,   nil,   nil,    45,
   119,   nil,    22,    30,     8,    47,    99,    97,     8,   160,
    47,   160,   nil,    32,   nil,   nil,   nil,   nil,   131,    30,
   131,   nil,    15,    21,   nil,   174,   nil,   174,   nil,   174,
   nil,   174,    75,   182,   nil,    72,    71,   nil,   nil,    72,
    34,     4,    18,   nil,    71,   nil,   nil,   nil,    21,   nil,
   nil,    70,   nil,   nil,   nil,   nil,    61,   nil,   nil,   nil,
   nil,   nil,    62,     9,    58,   131,   nil,   131,    22,   nil,
   nil,   nil,    42,   nil,   nil,   nil,    32,    42,   nil,   174,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,     9,   nil,
    74,   nil,    42,   nil,    28,   nil,   nil,   nil,   nil,   125,
   nil,    42,    42,    42,    34,    42,   nil,   nil,    65,    21,
    58,   nil,    10,    21,   nil,    39,    65,   nil,    21,   nil,
    50,    39,    22,     8,    22,   nil,   nil,   nil,    50,   nil,
    22,   119,    22,   125,   119,    42,    42,   nil,    22,    42,
   nil,    72,    34,    21,   nil,     2,    22,    72,   nil,   nil,
   nil,   124,    34,     9,   nil,   nil,   nil,   131,     9,   nil,
   nil,    22,    90,    30,    22,   nil,    72,   nil,   nil,   nil,
    22,   nil,   nil,   nil,    39,   nil,   nil,    58,   nil,     7,
    22,   131,   nil,   nil,    22,    42,   nil,    58,    87,    90,
    42,    42,    42,    42,    42,    42,   nil,   nil,   nil,    93,
    72,   nil,   nil,   nil,    97,    97,   nil,   nil,   nil,   nil,
    15,   nil,   119,    32,   119,    58,    24,    32,    22,    22,
   nil,    58,    97,    22,    22,   nil,    69,    22,   142,   nil,
   nil,   nil,   nil,    72,   nil,   nil,   nil,    65,    68,   nil,
    32,    22,   nil,    72,    61,   nil,    22,    22,    65,    50,
    62,   nil,    58,     8,   nil,    32,   nil,    71,   nil,    22,
    50,   nil,   nil,    20,    20,   nil,   nil,    57,    20,    20,
    22,    72,    20,   119,   119,   127,   127,   127,   nil,    72,
    42,    42,    42,    42,   nil,   nil,   nil,   nil,    42,    42,
    42,    42,    42,    42,   nil,   nil,   nil,    71,   nil,   nil,
   nil,    32,    17,   nil,   nil,    42,   nil,   nil,    17,   nil,
    32,   119,   nil,   119,    21,   nil,    21,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    22,   124,
   nil,   nil,    69,    22,    22,   119,   nil,    22,   nil,    42,
    42,   nil,   nil,   nil,   nil,    30,   nil,    22,    42,   nil,
   nil,    30,    26,    21,   nil,   172,    21,    26,    22,   nil,
   nil,    71,   nil,   nil,    32,    72,    42,    65,    72,    68,
    42,    48,   nil,    68,   nil,    42,    21,   124,   nil,   nil,
   124,   nil,   124,    58,   124,    26,    26,    26,   nil,    22,
   nil,   126,   nil,     9,    32,    22,     9,   nil,   nil,   nil,
    42,    42,   nil,    20,   nil,    23,   nil,   nil,    22,   nil,
   nil,   nil,   nil,    38,    17,    42,    42,   nil,   nil,   nil,
    26,    26,    26,   nil,   nil,   nil,    48,   127,   127,   127,
   127,    27,   127,    21,   172,    42,    27,   nil,    21,    21,
    22,   nil,    22,    22,   nil,   nil,    22,   nil,   nil,    48,
   nil,    42,    22,   nil,   nil,    65,   nil,    30,   nil,   nil,
   nil,   nil,   nil,   nil,    27,    26,   nil,    65,   nil,   nil,
   nil,   nil,   nil,     9,    26,    26,   172,    22,     9,     9,
   nil,   nil,   nil,   nil,   nil,    22,   nil,    97,   nil,    22,
    97,   nil,   nil,   nil,   nil,    97,   124,   nil,   124,    27,
   124,   nil,   124,   nil,   170,   170,   170,   nil,   nil,   127,
   127,   127,   127,   nil,   nil,   nil,   nil,    42,    32,   nil,
   nil,   nil,   nil,   nil,   nil,    21,   nil,   nil,   nil,    21,
   nil,   nil,   nil,   172,   172,   nil,   nil,   nil,   nil,    21,
   127,   nil,    25,   nil,    27,   nil,   nil,   nil,   nil,    22,
   124,   nil,   nil,    27,    27,   nil,    22,    22,   nil,   nil,
    26,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   126,
    34,    42,   126,    42,    22,   nil,   nil,   nil,   nil,     9,
    22,    42,    22,   nil,   nil,    26,   nil,    21,    58,   nil,
    48,   nil,   nil,   nil,   nil,    42,   nil,   nil,    22,    32,
    21,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    42,   nil,   nil,    42,   nil,   nil,   172,   126,   172,    42,
   126,   nil,   126,   nil,   126,   nil,   nil,     9,   nil,    42,
   nil,   nil,    48,    42,    21,    21,    48,   nil,    21,    27,
     9,   nil,   nil,    58,    21,   nil,    26,   nil,    27,   nil,
    26,   nil,    48,   nil,    26,    26,   nil,   nil,    48,   nil,
   nil,   nil,   nil,   172,    27,   172,   nil,    42,    42,   nil,
   nil,    26,    42,    42,     9,     9,    42,    21,     9,   nil,
    26,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    42,   nil,   nil,   nil,   nil,    42,    42,   nil,   nil,    72,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    48,   nil,   129,     9,   170,   170,
   nil,   170,   170,    53,   170,    27,   nil,   nil,    53,    27,
   nil,   nil,   nil,   nil,    27,   nil,   126,   nil,   126,   nil,
   126,    21,   126,   nil,   nil,   nil,   nil,   nil,    21,    21,
   nil,   nil,   nil,   nil,   nil,   172,    53,    53,    53,    27,
   nil,   nil,    66,   nil,   nil,   nil,    21,   nil,   nil,   nil,
   nil,   nil,    21,   nil,    21,   nil,   nil,    42,   nil,   172,
   nil,     9,    42,    42,   nil,   nil,    42,   nil,     9,     9,
   126,    53,    53,    53,    53,   nil,    42,   nil,   nil,   nil,
    48,   nil,   nil,   nil,   nil,   nil,     9,    42,   nil,   nil,
   nil,   nil,     9,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   170,   170,   170,   170,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    53,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    42,    53,    53,   nil,   nil,   nil,
   nil,    26,   nil,    26,   nil,   nil,   nil,    42,   nil,    26,
   nil,   nil,   170,   nil,   nil,   nil,   nil,    26,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    42,
    26,    42,    42,    26,   nil,    42,   nil,   nil,   nil,   nil,
   129,    42,   129,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    66,   nil,    26,   nil,   nil,   nil,   nil,   nil,    66,
    26,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    27,    53,    27,   nil,    42,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   129,   nil,   129,
   nil,   nil,   nil,   nil,   nil,   nil,    53,   nil,   nil,   nil,
    48,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    27,
    26,   nil,    27,   nil,   nil,    26,    26,   nil,   nil,   nil,
   nil,   nil,    48,   nil,   nil,    66,   nil,    66,    26,   nil,
    66,    66,    27,   nil,   nil,   nil,   nil,   nil,    42,    26,
   nil,   nil,   nil,   nil,   nil,    42,    42,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    53,   nil,   nil,
   nil,    53,   nil,    42,   nil,    53,    53,   nil,   nil,    42,
   nil,    42,   nil,   nil,   nil,    27,    48,   129,   nil,   129,
   nil,   129,    53,   nil,   nil,   nil,   nil,    42,   nil,    27,
   nil,    53,   nil,   nil,    27,    27,   nil,   nil,   nil,   nil,
   nil,   129,    26,   129,   nil,   129,    26,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    26,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    54,   nil,   nil,
   nil,   nil,    54,   nil,   nil,   129,   nil,   nil,   nil,    66,
   nil,   nil,   nil,   nil,   nil,   129,   nil,   nil,    26,   nil,
   nil,   nil,   nil,   nil,    26,   nil,   nil,   nil,   nil,   nil,
    54,    54,    54,   nil,   nil,   nil,   nil,    26,   nil,   nil,
   nil,    27,   nil,   nil,   nil,    27,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    27,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    54,    54,    54,    54,   nil,
   nil,    26,    26,   nil,   nil,    26,   nil,   nil,   nil,   nil,
   nil,    26,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    66,   nil,    66,   nil,    66,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    27,   nil,   nil,    26,   nil,   nil,   nil,
    54,   nil,   nil,   nil,    26,   nil,    27,   nil,    26,    54,
    54,   nil,    53,   nil,    53,   nil,   nil,   nil,   nil,   nil,
    53,    66,   nil,   nil,    66,   nil,   nil,   nil,    53,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    66,   nil,
    27,    27,   nil,   nil,    27,   nil,   nil,   nil,   nil,   nil,
    27,    53,   nil,   nil,    53,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,   nil,
   nil,   nil,   nil,   nil,    53,    26,    26,   nil,   nil,   nil,
   nil,    66,   nil,    27,   nil,    66,    66,    27,   nil,   nil,
   nil,   nil,   nil,    26,   nil,    54,   nil,   nil,   nil,    26,
   nil,    26,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    54,    66,   nil,   nil,    66,   nil,   nil,   nil,   nil,   nil,
   nil,    53,   nil,   nil,   nil,   nil,    53,    53,   nil,   nil,
   nil,   nil,   nil,   nil,    66,   nil,   nil,    27,   nil,    53,
   nil,   nil,   nil,   nil,    27,    27,   nil,   nil,   nil,   nil,
    53,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    66,   nil,    27,   nil,   nil,   nil,   nil,   nil,    27,   nil,
    27,    54,   nil,   nil,   nil,    54,   nil,   nil,   nil,    54,
    54,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    54,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    54,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    53,   nil,    66,   nil,    53,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    53,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    53,
   nil,   nil,   nil,   nil,   nil,    53,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    66,   nil,    53,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    66,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    33,    66,    53,    53,   nil,   nil,    53,   nil,   nil,    33,
    33,    33,    53,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    33,    33,    33,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    53,   nil,    33,
    33,   nil,   nil,   nil,   nil,    53,   nil,   nil,   nil,    53,
   nil,   nil,   nil,   nil,   nil,   nil,    54,   nil,    54,   nil,
   nil,   nil,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    54,   nil,   nil,    54,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    53,
   nil,   nil,   nil,   nil,   nil,   nil,    53,    53,    54,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    53,   nil,   nil,   nil,   nil,   nil,
    53,   nil,    53,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    54,   nil,   nil,   nil,   nil,
    54,    54,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    33,
    33,    33,   nil,    54,   nil,   nil,    33,    33,   nil,   nil,
   nil,   nil,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    33,   nil,   nil,    33,    33,    33,    33,    33,
    33,    33,    33,    33,    33,    33,    33,    33,    33,    33,
    33,    33,    33,    33,    33,    33,    33,    33,    33,    33,
    33,   nil,   nil,   nil,   nil,   nil,   nil,    33,    33,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    33,   nil,   nil,   nil,
   nil,   nil,   nil,    33,   nil,    33,   nil,    54,    33,    33,
   nil,    54,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    54,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    33,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    54,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    54,    54,   nil,   nil,
    54,   nil,   nil,   nil,   nil,   nil,    54,    33,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    54,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    54,
   nil,   nil,   nil,    54,   nil,    33,   nil,    33,    33,    33,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    33,
   nil,    33,   nil,    33,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    54,   nil,   nil,   nil,   nil,   nil,    33,
    54,    54,   nil,   nil,   nil,   nil,   nil,   nil,    33,    33,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    54,    33,
   nil,   nil,    33,   nil,    54,   nil,    54,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    33,    33,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    33,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    33,
    33,   nil,   nil,    33,    33,    33,    33,   nil,   nil,   nil,
    33,    33,   nil,   nil,    33,    33,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    33,
   nil,   nil,    33,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    33,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    33,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    33,   nil,    33,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    33,
    33,    33,    33,   nil,    33,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    33,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    33,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    33,
    33,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    33 ]

racc_goto_pointer = [
   nil,   110,    24,   nil,    76,    77,    56,    58,  -326,   235,
  -482,  -557,  -668,   nil,  -372,     4,   135,   -23,  -182,    77,
    48,   195,     3,  -190,  -402,    -7,   732,   811,  -178,   -63,
    20,    26,   -19,  1900,   -25,   nil,   nil,    -2,  -183,    77,
  -246,  -348,   452,  -328,   nil,   103,    41,    87,   530,   nil,
    -7,    43,  -262,  1103,  1477,  -277,    32,   -70,    46,   nil,
   nil,    38,    44,  -249,     7,   -19,  1082,    95,   -34,    60,
  -225,   148,   103,  -322,  -267,  -427,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    85,    97,   -52,   nil,  -181,
  -323,  -634,  -467,  -318,    91,   nil,  -202,    42,   nil,  -519,
    90,   105,   105,  -362,   111,   108,   108,  -519,   109,  -520,
  -369,  -715,  -374,  -527,  -179,  -188,  -374,  -643,  -794,  -295,
  -576,  -777,  -424,   nil,   -85,  -394,   155,  -103,  -423,   480,
  -555,  -400,   nil,  -531,  -652,  -767,  -753,  -334,  -587,  -124,
  -430,  -406,   -11,  -641,  -640,   -27,   nil,   -27,   -26,  -650,
  -404,  -538,   nil,   nil,   171,   -12,    58,   169,   170,  -271,
  -186,   188,   188,   191,  -287,  -287,  -275,  -386,   nil,   nil,
   221,  -513,   198,  -680,  -556,  -506,  -548,   nil,  -712,  -755,
   nil,   nil,  -426 ]

racc_goto_default = [
   nil,   nil,   nil,     3,   nil,     4,   347,   295,   nil,   524,
   nil,   816,   nil,   292,   293,   nil,   nil,   nil,    11,    12,
    18,   230,   323,   nil,   nil,   556,   228,   229,   nil,   nil,
    17,   nil,   442,    21,    22,    23,    24,   nil,   645,   nil,
   nil,   nil,   312,   nil,    25,   413,    32,   nil,   nil,    34,
    37,    36,   nil,   225,   226,   359,   nil,   131,   421,   130,
   133,    77,    78,   nil,    92,    46,   284,   nil,   784,   414,
   nil,   415,   426,   599,   488,   282,   268,    47,    48,    49,
    50,    51,    52,    53,    54,    55,   nil,   269,    61,   nil,
   nil,   nil,   nil,   nil,   nil,    69,   nil,   539,    70,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   807,   673,
   nil,   808,   909,   755,   661,   nil,   662,   nil,   nil,   663,
   nil,   665,   615,   nil,   nil,   nil,   671,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   425,   nil,   nil,   nil,
   nil,   nil,    76,    79,    80,   nil,   nil,   nil,   nil,   nil,
   566,   nil,   nil,   nil,   nil,   nil,   nil,   876,   716,   660,
   nil,   664,   884,   676,   678,   679,   767,   682,   683,   768,
   686,   689,   287 ]

racc_reduce_table = [
  0, 0, :racc_error,
  1, 146, :_reduce_none,
  2, 147, :_reduce_2,
  0, 148, :_reduce_3,
  1, 148, :_reduce_4,
  3, 148, :_reduce_5,
  2, 148, :_reduce_6,
  1, 150, :_reduce_none,
  4, 150, :_reduce_8,
  4, 153, :_reduce_9,
  2, 154, :_reduce_10,
  0, 158, :_reduce_11,
  1, 158, :_reduce_12,
  3, 158, :_reduce_13,
  2, 158, :_reduce_14,
  1, 159, :_reduce_none,
  4, 159, :_reduce_16,
  0, 176, :_reduce_17,
  4, 152, :_reduce_18,
  3, 152, :_reduce_19,
  3, 152, :_reduce_20,
  3, 152, :_reduce_21,
  2, 152, :_reduce_22,
  3, 152, :_reduce_23,
  3, 152, :_reduce_24,
  3, 152, :_reduce_25,
  3, 152, :_reduce_26,
  3, 152, :_reduce_27,
  4, 152, :_reduce_28,
  1, 152, :_reduce_none,
  3, 152, :_reduce_30,
  3, 152, :_reduce_31,
  6, 152, :_reduce_32,
  5, 152, :_reduce_33,
  5, 152, :_reduce_34,
  5, 152, :_reduce_35,
  5, 152, :_reduce_36,
  3, 152, :_reduce_37,
  3, 152, :_reduce_38,
  3, 152, :_reduce_39,
  1, 152, :_reduce_none,
  3, 163, :_reduce_41,
  3, 163, :_reduce_42,
  1, 175, :_reduce_none,
  3, 175, :_reduce_44,
  3, 175, :_reduce_45,
  3, 175, :_reduce_46,
  2, 175, :_reduce_47,
  1, 175, :_reduce_none,
  1, 162, :_reduce_none,
  1, 165, :_reduce_none,
  1, 165, :_reduce_none,
  1, 180, :_reduce_none,
  4, 180, :_reduce_53,
  0, 188, :_reduce_54,
  5, 185, :_reduce_55,
  1, 187, :_reduce_none,
  2, 179, :_reduce_57,
  3, 179, :_reduce_58,
  4, 179, :_reduce_59,
  5, 179, :_reduce_60,
  4, 179, :_reduce_61,
  5, 179, :_reduce_62,
  2, 179, :_reduce_63,
  2, 179, :_reduce_64,
  2, 179, :_reduce_65,
  2, 179, :_reduce_66,
  2, 179, :_reduce_67,
  1, 164, :_reduce_68,
  3, 164, :_reduce_69,
  1, 192, :_reduce_70,
  3, 192, :_reduce_71,
  1, 191, :_reduce_none,
  2, 191, :_reduce_73,
  3, 191, :_reduce_74,
  5, 191, :_reduce_75,
  2, 191, :_reduce_76,
  4, 191, :_reduce_77,
  2, 191, :_reduce_78,
  4, 191, :_reduce_79,
  1, 191, :_reduce_80,
  3, 191, :_reduce_81,
  1, 195, :_reduce_none,
  3, 195, :_reduce_83,
  2, 194, :_reduce_84,
  3, 194, :_reduce_85,
  1, 197, :_reduce_86,
  3, 197, :_reduce_87,
  1, 196, :_reduce_88,
  1, 196, :_reduce_89,
  4, 196, :_reduce_90,
  3, 196, :_reduce_91,
  3, 196, :_reduce_92,
  3, 196, :_reduce_93,
  3, 196, :_reduce_94,
  2, 196, :_reduce_95,
  1, 196, :_reduce_96,
  1, 172, :_reduce_97,
  1, 172, :_reduce_98,
  4, 172, :_reduce_99,
  3, 172, :_reduce_100,
  3, 172, :_reduce_101,
  3, 172, :_reduce_102,
  3, 172, :_reduce_103,
  2, 172, :_reduce_104,
  1, 172, :_reduce_105,
  1, 200, :_reduce_106,
  1, 200, :_reduce_none,
  2, 201, :_reduce_108,
  1, 201, :_reduce_109,
  3, 201, :_reduce_110,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 205, :_reduce_116,
  1, 205, :_reduce_none,
  1, 160, :_reduce_none,
  1, 160, :_reduce_none,
  1, 161, :_reduce_120,
  0, 208, :_reduce_121,
  4, 161, :_reduce_122,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  1, 204, :_reduce_none,
  3, 178, :_reduce_194,
  5, 178, :_reduce_195,
  3, 178, :_reduce_196,
  5, 178, :_reduce_197,
  6, 178, :_reduce_198,
  5, 178, :_reduce_199,
  5, 178, :_reduce_200,
  5, 178, :_reduce_201,
  5, 178, :_reduce_202,
  4, 178, :_reduce_203,
  3, 178, :_reduce_204,
  3, 178, :_reduce_205,
  3, 178, :_reduce_206,
  3, 178, :_reduce_207,
  3, 178, :_reduce_208,
  3, 178, :_reduce_209,
  3, 178, :_reduce_210,
  3, 178, :_reduce_211,
  3, 178, :_reduce_212,
  4, 178, :_reduce_213,
  2, 178, :_reduce_214,
  2, 178, :_reduce_215,
  3, 178, :_reduce_216,
  3, 178, :_reduce_217,
  3, 178, :_reduce_218,
  3, 178, :_reduce_219,
  3, 178, :_reduce_220,
  3, 178, :_reduce_221,
  3, 178, :_reduce_222,
  3, 178, :_reduce_223,
  3, 178, :_reduce_224,
  3, 178, :_reduce_225,
  3, 178, :_reduce_226,
  3, 178, :_reduce_227,
  3, 178, :_reduce_228,
  2, 178, :_reduce_229,
  2, 178, :_reduce_230,
  3, 178, :_reduce_231,
  3, 178, :_reduce_232,
  3, 178, :_reduce_233,
  3, 178, :_reduce_234,
  3, 178, :_reduce_235,
  6, 178, :_reduce_236,
  1, 178, :_reduce_none,
  1, 211, :_reduce_none,
  1, 212, :_reduce_none,
  2, 212, :_reduce_none,
  4, 212, :_reduce_241,
  2, 212, :_reduce_242,
  3, 217, :_reduce_243,
  0, 218, :_reduce_244,
  1, 218, :_reduce_none,
  0, 168, :_reduce_246,
  1, 168, :_reduce_none,
  2, 168, :_reduce_none,
  4, 168, :_reduce_249,
  2, 168, :_reduce_250,
  1, 190, :_reduce_251,
  2, 190, :_reduce_252,
  2, 190, :_reduce_253,
  4, 190, :_reduce_254,
  1, 190, :_reduce_255,
  0, 221, :_reduce_256,
  2, 184, :_reduce_257,
  2, 220, :_reduce_258,
  2, 219, :_reduce_259,
  0, 219, :_reduce_260,
  1, 214, :_reduce_261,
  2, 214, :_reduce_262,
  3, 214, :_reduce_263,
  4, 214, :_reduce_264,
  1, 174, :_reduce_265,
  1, 174, :_reduce_none,
  3, 173, :_reduce_267,
  4, 173, :_reduce_268,
  2, 173, :_reduce_269,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_280,
  0, 246, :_reduce_281,
  4, 210, :_reduce_282,
  0, 247, :_reduce_283,
  0, 248, :_reduce_284,
  6, 210, :_reduce_285,
  0, 249, :_reduce_286,
  4, 210, :_reduce_287,
  3, 210, :_reduce_288,
  3, 210, :_reduce_289,
  2, 210, :_reduce_290,
  3, 210, :_reduce_291,
  3, 210, :_reduce_292,
  1, 210, :_reduce_293,
  4, 210, :_reduce_294,
  3, 210, :_reduce_295,
  1, 210, :_reduce_296,
  5, 210, :_reduce_297,
  4, 210, :_reduce_298,
  3, 210, :_reduce_299,
  2, 210, :_reduce_300,
  1, 210, :_reduce_none,
  2, 210, :_reduce_302,
  0, 250, :_reduce_303,
  3, 210, :_reduce_304,
  6, 210, :_reduce_305,
  6, 210, :_reduce_306,
  0, 251, :_reduce_307,
  0, 252, :_reduce_308,
  7, 210, :_reduce_309,
  0, 253, :_reduce_310,
  0, 254, :_reduce_311,
  7, 210, :_reduce_312,
  5, 210, :_reduce_313,
  4, 210, :_reduce_314,
  0, 255, :_reduce_315,
  0, 256, :_reduce_316,
  9, 210, :_reduce_317,
  0, 257, :_reduce_318,
  6, 210, :_reduce_319,
  0, 258, :_reduce_320,
  7, 210, :_reduce_321,
  0, 259, :_reduce_322,
  5, 210, :_reduce_323,
  0, 260, :_reduce_324,
  6, 210, :_reduce_325,
  0, 261, :_reduce_326,
  0, 262, :_reduce_327,
  9, 210, :_reduce_328,
  1, 210, :_reduce_329,
  1, 210, :_reduce_330,
  1, 210, :_reduce_331,
  1, 210, :_reduce_332,
  1, 167, :_reduce_none,
  1, 240, :_reduce_334,
  1, 243, :_reduce_335,
  1, 235, :_reduce_none,
  1, 235, :_reduce_none,
  2, 235, :_reduce_338,
  1, 237, :_reduce_none,
  1, 237, :_reduce_none,
  1, 236, :_reduce_none,
  5, 236, :_reduce_342,
  1, 156, :_reduce_none,
  2, 156, :_reduce_344,
  1, 239, :_reduce_none,
  1, 239, :_reduce_none,
  1, 263, :_reduce_347,
  3, 263, :_reduce_348,
  1, 266, :_reduce_349,
  3, 266, :_reduce_350,
  1, 265, :_reduce_none,
  4, 265, :_reduce_352,
  6, 265, :_reduce_353,
  3, 265, :_reduce_354,
  5, 265, :_reduce_355,
  2, 265, :_reduce_356,
  4, 265, :_reduce_357,
  1, 265, :_reduce_358,
  3, 265, :_reduce_359,
  4, 267, :_reduce_360,
  2, 267, :_reduce_361,
  2, 267, :_reduce_362,
  1, 267, :_reduce_363,
  2, 272, :_reduce_364,
  0, 272, :_reduce_365,
  6, 273, :_reduce_366,
  8, 273, :_reduce_367,
  4, 273, :_reduce_368,
  6, 273, :_reduce_369,
  4, 273, :_reduce_370,
  2, 273, :_reduce_none,
  6, 273, :_reduce_372,
  2, 273, :_reduce_373,
  4, 273, :_reduce_374,
  6, 273, :_reduce_375,
  2, 273, :_reduce_376,
  4, 273, :_reduce_377,
  2, 273, :_reduce_378,
  4, 273, :_reduce_379,
  1, 273, :_reduce_none,
  0, 186, :_reduce_381,
  1, 186, :_reduce_382,
  3, 277, :_reduce_383,
  1, 277, :_reduce_384,
  4, 277, :_reduce_385,
  1, 278, :_reduce_386,
  4, 278, :_reduce_387,
  1, 279, :_reduce_388,
  3, 279, :_reduce_389,
  1, 280, :_reduce_390,
  1, 280, :_reduce_none,
  0, 284, :_reduce_392,
  0, 285, :_reduce_393,
  4, 234, :_reduce_394,
  4, 282, :_reduce_395,
  1, 282, :_reduce_396,
  0, 288, :_reduce_397,
  4, 283, :_reduce_398,
  0, 289, :_reduce_399,
  4, 283, :_reduce_400,
  0, 290, :_reduce_401,
  5, 287, :_reduce_402,
  2, 181, :_reduce_403,
  4, 181, :_reduce_404,
  5, 181, :_reduce_405,
  5, 181, :_reduce_406,
  2, 233, :_reduce_407,
  4, 233, :_reduce_408,
  4, 233, :_reduce_409,
  3, 233, :_reduce_410,
  3, 233, :_reduce_411,
  3, 233, :_reduce_412,
  2, 233, :_reduce_413,
  1, 233, :_reduce_414,
  4, 233, :_reduce_415,
  0, 292, :_reduce_416,
  5, 232, :_reduce_417,
  0, 293, :_reduce_418,
  5, 232, :_reduce_419,
  5, 238, :_reduce_420,
  1, 294, :_reduce_421,
  1, 294, :_reduce_none,
  6, 155, :_reduce_423,
  0, 155, :_reduce_424,
  1, 295, :_reduce_425,
  1, 295, :_reduce_none,
  1, 295, :_reduce_none,
  2, 296, :_reduce_428,
  1, 296, :_reduce_none,
  2, 157, :_reduce_430,
  1, 157, :_reduce_none,
  1, 222, :_reduce_none,
  1, 222, :_reduce_none,
  1, 222, :_reduce_none,
  1, 223, :_reduce_435,
  1, 298, :_reduce_436,
  2, 298, :_reduce_437,
  3, 299, :_reduce_438,
  1, 299, :_reduce_439,
  1, 299, :_reduce_440,
  3, 224, :_reduce_441,
  4, 225, :_reduce_442,
  3, 226, :_reduce_443,
  0, 303, :_reduce_444,
  3, 303, :_reduce_445,
  1, 304, :_reduce_446,
  2, 304, :_reduce_447,
  3, 228, :_reduce_448,
  0, 306, :_reduce_449,
  3, 306, :_reduce_450,
  3, 227, :_reduce_451,
  3, 229, :_reduce_452,
  0, 307, :_reduce_453,
  3, 307, :_reduce_454,
  0, 308, :_reduce_455,
  3, 308, :_reduce_456,
  0, 300, :_reduce_457,
  2, 300, :_reduce_458,
  0, 301, :_reduce_459,
  2, 301, :_reduce_460,
  0, 302, :_reduce_461,
  2, 302, :_reduce_462,
  1, 305, :_reduce_463,
  2, 305, :_reduce_464,
  0, 310, :_reduce_465,
  4, 305, :_reduce_466,
  1, 309, :_reduce_467,
  1, 309, :_reduce_468,
  1, 309, :_reduce_469,
  1, 309, :_reduce_none,
  1, 206, :_reduce_471,
  3, 207, :_reduce_472,
  1, 297, :_reduce_473,
  2, 297, :_reduce_474,
  1, 209, :_reduce_475,
  1, 209, :_reduce_476,
  1, 209, :_reduce_477,
  1, 209, :_reduce_478,
  1, 198, :_reduce_479,
  1, 198, :_reduce_480,
  1, 198, :_reduce_481,
  1, 198, :_reduce_482,
  1, 198, :_reduce_483,
  1, 199, :_reduce_484,
  1, 199, :_reduce_485,
  1, 199, :_reduce_486,
  1, 199, :_reduce_487,
  1, 199, :_reduce_488,
  1, 199, :_reduce_489,
  1, 199, :_reduce_490,
  1, 230, :_reduce_491,
  1, 230, :_reduce_492,
  1, 166, :_reduce_493,
  1, 166, :_reduce_494,
  1, 171, :_reduce_495,
  1, 171, :_reduce_496,
  0, 311, :_reduce_497,
  4, 241, :_reduce_498,
  0, 241, :_reduce_499,
  3, 244, :_reduce_500,
  0, 313, :_reduce_501,
  3, 244, :_reduce_502,
  4, 312, :_reduce_503,
  2, 312, :_reduce_504,
  2, 312, :_reduce_505,
  1, 312, :_reduce_506,
  2, 315, :_reduce_507,
  0, 315, :_reduce_508,
  6, 286, :_reduce_509,
  8, 286, :_reduce_510,
  4, 286, :_reduce_511,
  6, 286, :_reduce_512,
  4, 286, :_reduce_513,
  6, 286, :_reduce_514,
  2, 286, :_reduce_515,
  4, 286, :_reduce_516,
  6, 286, :_reduce_517,
  2, 286, :_reduce_518,
  4, 286, :_reduce_519,
  2, 286, :_reduce_520,
  4, 286, :_reduce_521,
  1, 286, :_reduce_522,
  0, 286, :_reduce_523,
  1, 281, :_reduce_524,
  1, 281, :_reduce_525,
  1, 281, :_reduce_526,
  1, 281, :_reduce_527,
  1, 264, :_reduce_none,
  1, 264, :_reduce_529,
  1, 317, :_reduce_530,
  1, 318, :_reduce_531,
  3, 318, :_reduce_532,
  1, 274, :_reduce_533,
  3, 274, :_reduce_534,
  1, 319, :_reduce_535,
  2, 320, :_reduce_536,
  1, 320, :_reduce_537,
  2, 321, :_reduce_538,
  1, 321, :_reduce_539,
  1, 268, :_reduce_540,
  3, 268, :_reduce_541,
  1, 314, :_reduce_542,
  3, 314, :_reduce_543,
  1, 322, :_reduce_none,
  1, 322, :_reduce_none,
  2, 269, :_reduce_546,
  1, 269, :_reduce_547,
  3, 323, :_reduce_548,
  3, 324, :_reduce_549,
  1, 275, :_reduce_550,
  3, 275, :_reduce_551,
  1, 316, :_reduce_552,
  3, 316, :_reduce_553,
  1, 325, :_reduce_none,
  1, 325, :_reduce_none,
  2, 276, :_reduce_556,
  1, 276, :_reduce_557,
  1, 326, :_reduce_none,
  1, 326, :_reduce_none,
  2, 271, :_reduce_560,
  2, 270, :_reduce_561,
  0, 270, :_reduce_562,
  1, 245, :_reduce_none,
  3, 245, :_reduce_564,
  0, 231, :_reduce_565,
  2, 231, :_reduce_none,
  1, 216, :_reduce_567,
  3, 216, :_reduce_568,
  3, 327, :_reduce_569,
  2, 327, :_reduce_570,
  4, 327, :_reduce_571,
  2, 327, :_reduce_572,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 183, :_reduce_none,
  1, 183, :_reduce_none,
  1, 183, :_reduce_none,
  1, 183, :_reduce_none,
  1, 291, :_reduce_none,
  1, 291, :_reduce_none,
  1, 291, :_reduce_none,
  1, 182, :_reduce_none,
  1, 182, :_reduce_none,
  1, 170, :_reduce_585,
  1, 170, :_reduce_586,
  0, 149, :_reduce_none,
  1, 149, :_reduce_none,
  0, 177, :_reduce_none,
  1, 177, :_reduce_none,
  2, 193, :_reduce_591,
  2, 169, :_reduce_592,
  0, 215, :_reduce_none,
  1, 215, :_reduce_none,
  1, 215, :_reduce_none,
  1, 242, :_reduce_596,
  1, 242, :_reduce_none,
  1, 151, :_reduce_none,
  2, 151, :_reduce_none,
  0, 213, :_reduce_600 ]

racc_reduce_n = 601

racc_shift_n = 1026

racc_token_table = {
  false => 0,
  :error => 1,
  :kCLASS => 2,
  :kMODULE => 3,
  :kDEF => 4,
  :kUNDEF => 5,
  :kBEGIN => 6,
  :kRESCUE => 7,
  :kENSURE => 8,
  :kEND => 9,
  :kIF => 10,
  :kUNLESS => 11,
  :kTHEN => 12,
  :kELSIF => 13,
  :kELSE => 14,
  :kCASE => 15,
  :kWHEN => 16,
  :kWHILE => 17,
  :kUNTIL => 18,
  :kFOR => 19,
  :kBREAK => 20,
  :kNEXT => 21,
  :kREDO => 22,
  :kRETRY => 23,
  :kIN => 24,
  :kDO => 25,
  :kDO_COND => 26,
  :kDO_BLOCK => 27,
  :kDO_LAMBDA => 28,
  :kRETURN => 29,
  :kYIELD => 30,
  :kSUPER => 31,
  :kSELF => 32,
  :kNIL => 33,
  :kTRUE => 34,
  :kFALSE => 35,
  :kAND => 36,
  :kOR => 37,
  :kNOT => 38,
  :kIF_MOD => 39,
  :kUNLESS_MOD => 40,
  :kWHILE_MOD => 41,
  :kUNTIL_MOD => 42,
  :kRESCUE_MOD => 43,
  :kALIAS => 44,
  :kDEFINED => 45,
  :klBEGIN => 46,
  :klEND => 47,
  :k__LINE__ => 48,
  :k__FILE__ => 49,
  :k__ENCODING__ => 50,
  :tIDENTIFIER => 51,
  :tFID => 52,
  :tGVAR => 53,
  :tIVAR => 54,
  :tCONSTANT => 55,
  :tLABEL => 56,
  :tCVAR => 57,
  :tNTH_REF => 58,
  :tBACK_REF => 59,
  :tSTRING_CONTENT => 60,
  :tINTEGER => 61,
  :tFLOAT => 62,
  :tUPLUS => 63,
  :tUMINUS => 64,
  :tUNARY_NUM => 65,
  :tPOW => 66,
  :tCMP => 67,
  :tEQ => 68,
  :tEQQ => 69,
  :tNEQ => 70,
  :tGEQ => 71,
  :tLEQ => 72,
  :tANDOP => 73,
  :tOROP => 74,
  :tMATCH => 75,
  :tNMATCH => 76,
  :tDOT => 77,
  :tDOT2 => 78,
  :tDOT3 => 79,
  :tAREF => 80,
  :tASET => 81,
  :tLSHFT => 82,
  :tRSHFT => 83,
  :tCOLON2 => 84,
  :tCOLON3 => 85,
  :tOP_ASGN => 86,
  :tASSOC => 87,
  :tLPAREN => 88,
  :tLPAREN2 => 89,
  :tRPAREN => 90,
  :tLPAREN_ARG => 91,
  :tLBRACK => 92,
  :tLBRACK2 => 93,
  :tRBRACK => 94,
  :tLBRACE => 95,
  :tLBRACE_ARG => 96,
  :tSTAR => 97,
  :tSTAR2 => 98,
  :tAMPER => 99,
  :tAMPER2 => 100,
  :tTILDE => 101,
  :tPERCENT => 102,
  :tDIVIDE => 103,
  :tDSTAR => 104,
  :tPLUS => 105,
  :tMINUS => 106,
  :tLT => 107,
  :tGT => 108,
  :tPIPE => 109,
  :tBANG => 110,
  :tCARET => 111,
  :tLCURLY => 112,
  :tRCURLY => 113,
  :tBACK_REF2 => 114,
  :tSYMBEG => 115,
  :tSTRING_BEG => 116,
  :tXSTRING_BEG => 117,
  :tREGEXP_BEG => 118,
  :tREGEXP_OPT => 119,
  :tWORDS_BEG => 120,
  :tQWORDS_BEG => 121,
  :tSYMBOLS_BEG => 122,
  :tQSYMBOLS_BEG => 123,
  :tSTRING_DBEG => 124,
  :tSTRING_DVAR => 125,
  :tSTRING_END => 126,
  :tSTRING_DEND => 127,
  :tSTRING => 128,
  :tSYMBOL => 129,
  :tNL => 130,
  :tEH => 131,
  :tCOLON => 132,
  :tCOMMA => 133,
  :tSPACE => 134,
  :tSEMI => 135,
  :tLAMBDA => 136,
  :tLAMBEG => 137,
  :tCHARACTER => 138,
  :tRATIONAL => 139,
  :tIMAGINARY => 140,
  :tLABEL_END => 141,
  :tANDDOT => 142,
  :tEQL => 143,
  :tLOWEST => 144 }

racc_nt_base = 145

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]
Ractor.make_shareable(Racc_arg) if defined?(Ractor)

Racc_token_to_s_table = [
  "$end",
  "error",
  "kCLASS",
  "kMODULE",
  "kDEF",
  "kUNDEF",
  "kBEGIN",
  "kRESCUE",
  "kENSURE",
  "kEND",
  "kIF",
  "kUNLESS",
  "kTHEN",
  "kELSIF",
  "kELSE",
  "kCASE",
  "kWHEN",
  "kWHILE",
  "kUNTIL",
  "kFOR",
  "kBREAK",
  "kNEXT",
  "kREDO",
  "kRETRY",
  "kIN",
  "kDO",
  "kDO_COND",
  "kDO_BLOCK",
  "kDO_LAMBDA",
  "kRETURN",
  "kYIELD",
  "kSUPER",
  "kSELF",
  "kNIL",
  "kTRUE",
  "kFALSE",
  "kAND",
  "kOR",
  "kNOT",
  "kIF_MOD",
  "kUNLESS_MOD",
  "kWHILE_MOD",
  "kUNTIL_MOD",
  "kRESCUE_MOD",
  "kALIAS",
  "kDEFINED",
  "klBEGIN",
  "klEND",
  "k__LINE__",
  "k__FILE__",
  "k__ENCODING__",
  "tIDENTIFIER",
  "tFID",
  "tGVAR",
  "tIVAR",
  "tCONSTANT",
  "tLABEL",
  "tCVAR",
  "tNTH_REF",
  "tBACK_REF",
  "tSTRING_CONTENT",
  "tINTEGER",
  "tFLOAT",
  "tUPLUS",
  "tUMINUS",
  "tUNARY_NUM",
  "tPOW",
  "tCMP",
  "tEQ",
  "tEQQ",
  "tNEQ",
  "tGEQ",
  "tLEQ",
  "tANDOP",
  "tOROP",
  "tMATCH",
  "tNMATCH",
  "tDOT",
  "tDOT2",
  "tDOT3",
  "tAREF",
  "tASET",
  "tLSHFT",
  "tRSHFT",
  "tCOLON2",
  "tCOLON3",
  "tOP_ASGN",
  "tASSOC",
  "tLPAREN",
  "tLPAREN2",
  "tRPAREN",
  "tLPAREN_ARG",
  "tLBRACK",
  "tLBRACK2",
  "tRBRACK",
  "tLBRACE",
  "tLBRACE_ARG",
  "tSTAR",
  "tSTAR2",
  "tAMPER",
  "tAMPER2",
  "tTILDE",
  "tPERCENT",
  "tDIVIDE",
  "tDSTAR",
  "tPLUS",
  "tMINUS",
  "tLT",
  "tGT",
  "tPIPE",
  "tBANG",
  "tCARET",
  "tLCURLY",
  "tRCURLY",
  "tBACK_REF2",
  "tSYMBEG",
  "tSTRING_BEG",
  "tXSTRING_BEG",
  "tREGEXP_BEG",
  "tREGEXP_OPT",
  "tWORDS_BEG",
  "tQWORDS_BEG",
  "tSYMBOLS_BEG",
  "tQSYMBOLS_BEG",
  "tSTRING_DBEG",
  "tSTRING_DVAR",
  "tSTRING_END",
  "tSTRING_DEND",
  "tSTRING",
  "tSYMBOL",
  "tNL",
  "tEH",
  "tCOLON",
  "tCOMMA",
  "tSPACE",
  "tSEMI",
  "tLAMBDA",
  "tLAMBEG",
  "tCHARACTER",
  "tRATIONAL",
  "tIMAGINARY",
  "tLABEL_END",
  "tANDDOT",
  "tEQL",
  "tLOWEST",
  "$start",
  "program",
  "top_compstmt",
  "top_stmts",
  "opt_terms",
  "top_stmt",
  "terms",
  "stmt",
  "bodystmt",
  "compstmt",
  "opt_rescue",
  "opt_else",
  "opt_ensure",
  "stmts",
  "stmt_or_begin",
  "fitem",
  "undef_list",
  "expr_value",
  "command_asgn",
  "mlhs",
  "command_call",
  "var_lhs",
  "primary_value",
  "opt_call_args",
  "rbracket",
  "call_op",
  "backref",
  "lhs",
  "mrhs",
  "mrhs_arg",
  "expr",
  "@1",
  "opt_nl",
  "arg",
  "command",
  "block_command",
  "block_call",
  "dot_or_colon",
  "operation2",
  "command_args",
  "cmd_brace_block",
  "opt_block_param",
  "fcall",
  "@2",
  "operation",
  "call_args",
  "mlhs_basic",
  "mlhs_inner",
  "rparen",
  "mlhs_head",
  "mlhs_item",
  "mlhs_node",
  "mlhs_post",
  "user_variable",
  "keyword_variable",
  "cname",
  "cpath",
  "fname",
  "op",
  "reswords",
  "fsym",
  "symbol",
  "dsym",
  "@3",
  "simple_numeric",
  "primary",
  "arg_value",
  "aref_args",
  "none",
  "args",
  "trailer",
  "assocs",
  "paren_args",
  "opt_paren_args",
  "opt_block_arg",
  "block_arg",
  "@4",
  "literal",
  "strings",
  "xstring",
  "regexp",
  "words",
  "qwords",
  "symbols",
  "qsymbols",
  "var_ref",
  "assoc_list",
  "brace_block",
  "method_call",
  "lambda",
  "then",
  "if_tail",
  "do",
  "case_body",
  "for_var",
  "k_class",
  "superclass",
  "term",
  "k_module",
  "f_arglist",
  "singleton",
  "@5",
  "@6",
  "@7",
  "@8",
  "@9",
  "@10",
  "@11",
  "@12",
  "@13",
  "@14",
  "@15",
  "@16",
  "@17",
  "@18",
  "@19",
  "@20",
  "@21",
  "f_marg",
  "f_norm_arg",
  "f_margs",
  "f_marg_list",
  "block_args_tail",
  "f_block_kwarg",
  "f_kwrest",
  "opt_f_block_arg",
  "f_block_arg",
  "opt_block_args_tail",
  "block_param",
  "f_arg",
  "f_block_optarg",
  "f_rest_arg",
  "block_param_def",
  "opt_bv_decl",
  "bv_decls",
  "bvar",
  "f_bad_arg",
  "f_larglist",
  "lambda_body",
  "@22",
  "@23",
  "f_args",
  "do_block",
  "@24",
  "@25",
  "@26",
  "operation3",
  "@27",
  "@28",
  "cases",
  "exc_list",
  "exc_var",
  "numeric",
  "string",
  "string1",
  "string_contents",
  "xstring_contents",
  "regexp_contents",
  "word_list",
  "word",
  "string_content",
  "symbol_list",
  "qword_list",
  "qsym_list",
  "string_dvar",
  "@29",
  "@30",
  "args_tail",
  "@31",
  "f_kwarg",
  "opt_args_tail",
  "f_optarg",
  "f_arg_asgn",
  "f_arg_item",
  "f_label",
  "f_kw",
  "f_block_kw",
  "kwrest_mark",
  "f_opt",
  "f_block_opt",
  "restarg_mark",
  "blkarg_mark",
  "assoc" ]
Ractor.make_shareable(Racc_token_to_s_table) if defined?(Ractor)

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

# reduce 1 omitted

def _reduce_2(val, _values, result)
                      result = @builder.compstmt(val[0])

    result
end

def _reduce_3(val, _values, result)
                      result = []

    result
end

def _reduce_4(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_5(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_6(val, _values, result)
                      result = [ val[1] ]

    result
end

# reduce 7 omitted

def _reduce_8(val, _values, result)
                      result = @builder.preexe(val[0], val[1], val[2], val[3])

    result
end

def _reduce_9(val, _values, result)
                      rescue_bodies     = val[1]
                      else_t,   else_   = val[2]
                      ensure_t, ensure_ = val[3]

                      if rescue_bodies.empty? && !else_t.nil?
                        diagnostic :warning, :useless_else, nil, else_t
                      end

                      result = @builder.begin_body(val[0],
                                  rescue_bodies,
                                  else_t,   else_,
                                  ensure_t, ensure_)

    result
end

def _reduce_10(val, _values, result)
                      result = @builder.compstmt(val[0])

    result
end

def _reduce_11(val, _values, result)
                      result = []

    result
end

def _reduce_12(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_13(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_14(val, _values, result)
                      result = [ val[1] ]

    result
end

# reduce 15 omitted

def _reduce_16(val, _values, result)
                      diagnostic :error, :begin_in_method, nil, val[0]

    result
end

def _reduce_17(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_18(val, _values, result)
                      result = @builder.alias(val[0], val[1], val[3])

    result
end

def _reduce_19(val, _values, result)
                      result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.gvar(val[2]))

    result
end

def _reduce_20(val, _values, result)
                      result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.back_ref(val[2]))

    result
end

def _reduce_21(val, _values, result)
                      diagnostic :error, :nth_ref_alias, nil, val[2]

    result
end

def _reduce_22(val, _values, result)
                      result = @builder.undef_method(val[0], val[1])

    result
end

def _reduce_23(val, _values, result)
                      result = @builder.condition_mod(val[0], nil,
                                                      val[1], val[2])

    result
end

def _reduce_24(val, _values, result)
                      result = @builder.condition_mod(nil, val[0],
                                                      val[1], val[2])

    result
end

def _reduce_25(val, _values, result)
                      result = @builder.loop_mod(:while, val[0], val[1], val[2])

    result
end

def _reduce_26(val, _values, result)
                      result = @builder.loop_mod(:until, val[0], val[1], val[2])

    result
end

def _reduce_27(val, _values, result)
                      rescue_body = @builder.rescue_body(val[1],
                                        nil, nil, nil,
                                        nil, val[2])

                      result = @builder.begin_body(val[0], [ rescue_body ])

    result
end

def _reduce_28(val, _values, result)
                      result = @builder.postexe(val[0], val[1], val[2], val[3])

    result
end

# reduce 29 omitted

def _reduce_30(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1], val[2])

    result
end

def _reduce_31(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_32(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])

    result
end

def _reduce_33(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_34(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_35(val, _values, result)
                      const  = @builder.const_op_assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))
                      result = @builder.op_assign(const, val[3], val[4])

    result
end

def _reduce_36(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_37(val, _values, result)
                      @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_38(val, _values, result)
                      result = @builder.assign(val[0], val[1],
                                  @builder.array(nil, val[2], nil))

    result
end

def _reduce_39(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1], val[2])

    result
end

# reduce 40 omitted

def _reduce_41(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

def _reduce_42(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

# reduce 43 omitted

def _reduce_44(val, _values, result)
                      result = @builder.logical_op(:and, val[0], val[1], val[2])

    result
end

def _reduce_45(val, _values, result)
                      result = @builder.logical_op(:or, val[0], val[1], val[2])

    result
end

def _reduce_46(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[2], nil)

    result
end

def _reduce_47(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[1], nil)

    result
end

# reduce 48 omitted

# reduce 49 omitted

# reduce 50 omitted

# reduce 51 omitted

# reduce 52 omitted

def _reduce_53(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
end

def _reduce_54(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_55(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

# reduce 56 omitted

def _reduce_57(val, _values, result)
                      result = @builder.call_method(nil, nil, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_58(val, _values, result)
                      method_call = @builder.call_method(nil, nil, val[0],
                                        nil, val[1], nil)

                      begin_t, args, body, end_t = val[2]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_59(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
end

def _reduce_60(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                        nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_61(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
end

def _reduce_62(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                        nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_63(val, _values, result)
                      result = @builder.keyword_cmd(:super, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_64(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_65(val, _values, result)
                      result = @builder.keyword_cmd(:return, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_66(val, _values, result)
                      result = @builder.keyword_cmd(:break, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_67(val, _values, result)
                      result = @builder.keyword_cmd(:next, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_68(val, _values, result)
                      result = @builder.multi_lhs(nil, val[0], nil)

    result
end

def _reduce_69(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_70(val, _values, result)
                      result = @builder.multi_lhs(nil, val[0], nil)

    result
end

def _reduce_71(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

# reduce 72 omitted

def _reduce_73(val, _values, result)
                      result = val[0].
                                  push(val[1])

    result
end

def _reduce_74(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1], val[2]))

    result
end

def _reduce_75(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1], val[2])).
                                  concat(val[4])

    result
end

def _reduce_76(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1]))

    result
end

def _reduce_77(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1])).
                                  concat(val[3])

    result
end

def _reduce_78(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

def _reduce_79(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]),
                                 *val[3] ]

    result
end

def _reduce_80(val, _values, result)
                      result = [ @builder.splat(val[0]) ]

    result
end

def _reduce_81(val, _values, result)
                      result = [ @builder.splat(val[0]),
                                 *val[2] ]

    result
end

# reduce 82 omitted

def _reduce_83(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_84(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_85(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_86(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_87(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_88(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_89(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_90(val, _values, result)
                      result = @builder.index_asgn(val[0], val[1], val[2], val[3])

    result
end

def _reduce_91(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_92(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_93(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_94(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))

    result
end

def _reduce_95(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))

    result
end

def _reduce_96(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_97(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_98(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_99(val, _values, result)
                      result = @builder.index_asgn(val[0], val[1], val[2], val[3])

    result
end

def _reduce_100(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_101(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_102(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_103(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))

    result
end

def _reduce_104(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))

    result
end

def _reduce_105(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_106(val, _values, result)
                      diagnostic :error, :module_name_const, nil, val[0]

    result
end

# reduce 107 omitted

def _reduce_108(val, _values, result)
                      result = @builder.const_global(val[0], val[1])

    result
end

def _reduce_109(val, _values, result)
                      result = @builder.const(val[0])

    result
end

def _reduce_110(val, _values, result)
                      result = @builder.const_fetch(val[0], val[1], val[2])

    result
end

# reduce 111 omitted

# reduce 112 omitted

# reduce 113 omitted

# reduce 114 omitted

# reduce 115 omitted

def _reduce_116(val, _values, result)
                      result = @builder.symbol_internal(val[0])

    result
end

# reduce 117 omitted

# reduce 118 omitted

# reduce 119 omitted

def _reduce_120(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_121(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_122(val, _values, result)
                      result = val[0] << val[3]

    result
end

# reduce 123 omitted

# reduce 124 omitted

# reduce 125 omitted

# reduce 126 omitted

# reduce 127 omitted

# reduce 128 omitted

# reduce 129 omitted

# reduce 130 omitted

# reduce 131 omitted

# reduce 132 omitted

# reduce 133 omitted

# reduce 134 omitted

# reduce 135 omitted

# reduce 136 omitted

# reduce 137 omitted

# reduce 138 omitted

# reduce 139 omitted

# reduce 140 omitted

# reduce 141 omitted

# reduce 142 omitted

# reduce 143 omitted

# reduce 144 omitted

# reduce 145 omitted

# reduce 146 omitted

# reduce 147 omitted

# reduce 148 omitted

# reduce 149 omitted

# reduce 150 omitted

# reduce 151 omitted

# reduce 152 omitted

# reduce 153 omitted

# reduce 154 omitted

# reduce 155 omitted

# reduce 156 omitted

# reduce 157 omitted

# reduce 158 omitted

# reduce 159 omitted

# reduce 160 omitted

# reduce 161 omitted

# reduce 162 omitted

# reduce 163 omitted

# reduce 164 omitted

# reduce 165 omitted

# reduce 166 omitted

# reduce 167 omitted

# reduce 168 omitted

# reduce 169 omitted

# reduce 170 omitted

# reduce 171 omitted

# reduce 172 omitted

# reduce 173 omitted

# reduce 174 omitted

# reduce 175 omitted

# reduce 176 omitted

# reduce 177 omitted

# reduce 178 omitted

# reduce 179 omitted

# reduce 180 omitted

# reduce 181 omitted

# reduce 182 omitted

# reduce 183 omitted

# reduce 184 omitted

# reduce 185 omitted

# reduce 186 omitted

# reduce 187 omitted

# reduce 188 omitted

# reduce 189 omitted

# reduce 190 omitted

# reduce 191 omitted

# reduce 192 omitted

# reduce 193 omitted

def _reduce_194(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

def _reduce_195(val, _values, result)
                      rescue_body = @builder.rescue_body(val[3],
                                        nil, nil, nil,
                                        nil, val[4])

                      rescue_ = @builder.begin_body(val[2], [ rescue_body ])

                      result  = @builder.assign(val[0], val[1], rescue_)

    result
end

def _reduce_196(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_197(val, _values, result)
                      rescue_body = @builder.rescue_body(val[3],
                                        nil, nil, nil,
                                        nil, val[4])

                      rescue_ = @builder.begin_body(val[2], [ rescue_body ])

                      result = @builder.op_assign(val[0], val[1], rescue_)

    result
end

def _reduce_198(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])

    result
end

def _reduce_199(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_200(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_201(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_202(val, _values, result)
                      const  = @builder.const_op_assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))
                      result = @builder.op_assign(const, val[3], val[4])

    result
end

def _reduce_203(val, _values, result)
                      const  = @builder.const_op_assignable(
                                  @builder.const_global(val[0], val[1]))
                      result = @builder.op_assign(const, val[2], val[3])

    result
end

def _reduce_204(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_205(val, _values, result)
                      result = @builder.range_inclusive(val[0], val[1], val[2])

    result
end

def _reduce_206(val, _values, result)
                      result = @builder.range_exclusive(val[0], val[1], val[2])

    result
end

def _reduce_207(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_208(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_209(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_210(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_211(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_212(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_213(val, _values, result)
                      result = @builder.unary_op(val[0],
                                  @builder.binary_op(
                                    val[1], val[2], val[3]))

    result
end

def _reduce_214(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_215(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_216(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_217(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_218(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_219(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_220(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_221(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_222(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_223(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_224(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_225(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_226(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_227(val, _values, result)
                      result = @builder.match_op(val[0], val[1], val[2])

    result
end

def _reduce_228(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_229(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[1], nil)

    result
end

def _reduce_230(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_231(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_232(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_233(val, _values, result)
                      result = @builder.logical_op(:and, val[0], val[1], val[2])

    result
end

def _reduce_234(val, _values, result)
                      result = @builder.logical_op(:or, val[0], val[1], val[2])

    result
end

def _reduce_235(val, _values, result)
                      result = @builder.keyword_cmd(:defined?, val[0], nil, [ val[2] ], nil)

    result
end

def _reduce_236(val, _values, result)
                      result = @builder.ternary(val[0], val[1],
                                                val[2], val[4], val[5])

    result
end

# reduce 237 omitted

# reduce 238 omitted

# reduce 239 omitted

# reduce 240 omitted

def _reduce_241(val, _values, result)
                      result = val[0] << @builder.associate(nil, val[2], nil)

    result
end

def _reduce_242(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]

    result
end

def _reduce_243(val, _values, result)
                      result = val

    result
end

def _reduce_244(val, _values, result)
                      result = [ nil, [], nil ]

    result
end

# reduce 245 omitted

def _reduce_246(val, _values, result)
                      result = []

    result
end

# reduce 247 omitted

# reduce 248 omitted

def _reduce_249(val, _values, result)
                      result = val[0] << @builder.associate(nil, val[2], nil)

    result
end

def _reduce_250(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]

    result
end

def _reduce_251(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_252(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_253(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]
                      result.concat(val[1])

    result
end

def _reduce_254(val, _values, result)
                      assocs = @builder.associate(nil, val[2], nil)
                      result = val[0] << assocs
                      result.concat(val[3])

    result
end

def _reduce_255(val, _values, result)
                      result =  [ val[0] ]

    result
end

def _reduce_256(val, _values, result)
                      result = @lexer.cmdarg.dup
                      @lexer.cmdarg.push(true)

    result
end

def _reduce_257(val, _values, result)
                      @lexer.cmdarg = val[0]

                      result = val[1]

    result
end

def _reduce_258(val, _values, result)
                      result = @builder.block_pass(val[0], val[1])

    result
end

def _reduce_259(val, _values, result)
                      result = [ val[1] ]

    result
end

def _reduce_260(val, _values, result)
                      result = []

    result
end

def _reduce_261(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_262(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

def _reduce_263(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_264(val, _values, result)
                      result = val[0] << @builder.splat(val[2], val[3])

    result
end

def _reduce_265(val, _values, result)
                      result = @builder.array(nil, val[0], nil)

    result
end

# reduce 266 omitted

def _reduce_267(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_268(val, _values, result)
                      result = val[0] << @builder.splat(val[2], val[3])

    result
end

def _reduce_269(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

# reduce 270 omitted

# reduce 271 omitted

# reduce 272 omitted

# reduce 273 omitted

# reduce 274 omitted

# reduce 275 omitted

# reduce 276 omitted

# reduce 277 omitted

# reduce 278 omitted

# reduce 279 omitted

def _reduce_280(val, _values, result)
                      result = @builder.call_method(nil, nil, val[0])

    result
end

def _reduce_281(val, _values, result)
                      result = @lexer.cmdarg.dup
                      @lexer.cmdarg.clear

    result
end

def _reduce_282(val, _values, result)
                      @lexer.cmdarg = val[1]

                      result = @builder.begin_keyword(val[0], val[2], val[3])

    result
end

def _reduce_283(val, _values, result)
                      result = @lexer.cmdarg.dup
                      @lexer.cmdarg.clear

    result
end

def _reduce_284(val, _values, result)
                      @lexer.state = :expr_endarg

    result
end

def _reduce_285(val, _values, result)
                      @lexer.cmdarg = val[1]

                      result = @builder.begin(val[0], val[2], val[5])

    result
end

def _reduce_286(val, _values, result)
                      @lexer.state = :expr_endarg

    result
end

def _reduce_287(val, _values, result)
                      result = @builder.begin(val[0], nil, val[3])

    result
end

def _reduce_288(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_289(val, _values, result)
                      result = @builder.const_fetch(val[0], val[1], val[2])

    result
end

def _reduce_290(val, _values, result)
                      result = @builder.const_global(val[0], val[1])

    result
end

def _reduce_291(val, _values, result)
                      result = @builder.array(val[0], val[1], val[2])

    result
end

def _reduce_292(val, _values, result)
                      result = @builder.associate(val[0], val[1], val[2])

    result
end

def _reduce_293(val, _values, result)
                      result = @builder.keyword_cmd(:return, val[0])

    result
end

def _reduce_294(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0], val[1], val[2], val[3])

    result
end

def _reduce_295(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0], val[1], [], val[2])

    result
end

def _reduce_296(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0])

    result
end

def _reduce_297(val, _values, result)
                      result = @builder.keyword_cmd(:defined?, val[0],
                                                    val[2], [ val[3] ], val[4])

    result
end

def _reduce_298(val, _values, result)
                      result = @builder.not_op(val[0], val[1], val[2], val[3])

    result
end

def _reduce_299(val, _values, result)
                      result = @builder.not_op(val[0], val[1], nil, val[2])

    result
end

def _reduce_300(val, _values, result)
                      method_call = @builder.call_method(nil, nil, val[0])

                      begin_t, args, body, end_t = val[1]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

# reduce 301 omitted

def _reduce_302(val, _values, result)
                      begin_t, args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, args, body, end_t)

    result
end

def _reduce_303(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_304(val, _values, result)
                      lambda_call = @builder.call_lambda(val[0])

                      args, (begin_t, body, end_t) = val[2]
                      result      = @builder.block(lambda_call,
                                      begin_t, args, body, end_t)

                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_305(val, _values, result)
                      else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  val[3], else_t,
                                                  else_,  val[5])

    result
end

def _reduce_306(val, _values, result)
                      else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  else_,  else_t,
                                                  val[3], val[5])

    result
end

def _reduce_307(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_308(val, _values, result)
                      @lexer.cond.pop

    result
end

def _reduce_309(val, _values, result)
                      result = @builder.loop(:while, val[0], val[2], val[3],
                                             val[5], val[6])

    result
end

def _reduce_310(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_311(val, _values, result)
                      @lexer.cond.pop

    result
end

def _reduce_312(val, _values, result)
                      result = @builder.loop(:until, val[0], val[2], val[3],
                                             val[5], val[6])

    result
end

def _reduce_313(val, _values, result)
                      *when_bodies, (else_t, else_body) = *val[3]

                      result = @builder.case(val[0], val[1],
                                             when_bodies, else_t, else_body,
                                             val[4])

    result
end

def _reduce_314(val, _values, result)
                      *when_bodies, (else_t, else_body) = *val[2]

                      result = @builder.case(val[0], nil,
                                             when_bodies, else_t, else_body,
                                             val[3])

    result
end

def _reduce_315(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_316(val, _values, result)
                      @lexer.cond.pop

    result
end

def _reduce_317(val, _values, result)
                      result = @builder.for(val[0], val[1],
                                            val[2], val[4],
                                            val[5], val[7], val[8])

    result
end

def _reduce_318(val, _values, result)
                      local_push
                      @context.in_class = true

    result
end

def _reduce_319(val, _values, result)
                      k_class, ctx = val[0]
                      if @context.in_def
                        diagnostic :error, :class_in_def, nil, k_class
                      end

                      lt_t, superclass = val[2]
                      result = @builder.def_class(k_class, val[1],
                                                  lt_t, superclass,
                                                  val[4], val[5])

                      local_pop
                      @context.in_class = ctx.in_class

    result
end

def _reduce_320(val, _values, result)
                      @context.in_def = false
                      @context.in_class = false
                      local_push

    result
end

def _reduce_321(val, _values, result)
                      k_class, ctx = val[0]
                      result = @builder.def_sclass(k_class, val[1], val[2],
                                                   val[5], val[6])

                      local_pop
                      @context.in_def = ctx.in_def
                      @context.in_class = ctx.in_class

    result
end

def _reduce_322(val, _values, result)
                      @context.in_class = true
                      local_push

    result
end

def _reduce_323(val, _values, result)
                      k_mod, ctx = val[0]
                      if @context.in_def
                        diagnostic :error, :module_in_def, nil, k_mod
                      end

                      result = @builder.def_module(k_mod, val[1],
                                                   val[3], val[4])

                      local_pop
                      @context.in_class = ctx.in_class

    result
end

def _reduce_324(val, _values, result)
                      local_push
                      result = context.dup
                      @context.in_def = true

    result
end

def _reduce_325(val, _values, result)
                      result = @builder.def_method(val[0], val[1],
                                  val[3], val[4], val[5])

                      local_pop
                      @context.in_def = val[2].in_def

    result
end

def _reduce_326(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_327(val, _values, result)
                      local_push
                      result = context.dup
                      @context.in_def = true

    result
end

def _reduce_328(val, _values, result)
                      result = @builder.def_singleton(val[0], val[1], val[2],
                                  val[4], val[6], val[7], val[8])

                      local_pop
                      @context.in_def = val[5].in_def

    result
end

def _reduce_329(val, _values, result)
                      result = @builder.keyword_cmd(:break, val[0])

    result
end

def _reduce_330(val, _values, result)
                      result = @builder.keyword_cmd(:next, val[0])

    result
end

def _reduce_331(val, _values, result)
                      result = @builder.keyword_cmd(:redo, val[0])

    result
end

def _reduce_332(val, _values, result)
                      result = @builder.keyword_cmd(:retry, val[0])

    result
end

# reduce 333 omitted

def _reduce_334(val, _values, result)
                      result = [ val[0], @context.dup ]

    result
end

def _reduce_335(val, _values, result)
                      result = [ val[0], @context.dup ]

    result
end

# reduce 336 omitted

# reduce 337 omitted

def _reduce_338(val, _values, result)
                      result = val[1]

    result
end

# reduce 339 omitted

# reduce 340 omitted

# reduce 341 omitted

def _reduce_342(val, _values, result)
                      else_t, else_ = val[4]
                      result = [ val[0],
                                 @builder.condition(val[0], val[1], val[2],
                                                    val[3], else_t,
                                                    else_,  nil),
                               ]

    result
end

# reduce 343 omitted

def _reduce_344(val, _values, result)
                      result = val

    result
end

# reduce 345 omitted

# reduce 346 omitted

def _reduce_347(val, _values, result)
                      result = @builder.arg(val[0])

    result
end

def _reduce_348(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

def _reduce_349(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_350(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 351 omitted

def _reduce_352(val, _values, result)
                      result = val[0].
                                  push(@builder.restarg(val[2], val[3]))

    result
end

def _reduce_353(val, _values, result)
                      result = val[0].
                                  push(@builder.restarg(val[2], val[3])).
                                  concat(val[5])

    result
end

def _reduce_354(val, _values, result)
                      result = val[0].
                                  push(@builder.restarg(val[2]))

    result
end

def _reduce_355(val, _values, result)
                      result = val[0].
                                  push(@builder.restarg(val[2])).
                                  concat(val[4])

    result
end

def _reduce_356(val, _values, result)
                      result = [ @builder.restarg(val[0], val[1]) ]

    result
end

def _reduce_357(val, _values, result)
                      result = [ @builder.restarg(val[0], val[1]),
                                 *val[3] ]

    result
end

def _reduce_358(val, _values, result)
                      result = [ @builder.restarg(val[0]) ]

    result
end

def _reduce_359(val, _values, result)
                      result = [ @builder.restarg(val[0]),
                                 *val[2] ]

    result
end

def _reduce_360(val, _values, result)
                      result = val[0].concat(val[2]).concat(val[3])

    result
end

def _reduce_361(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_362(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_363(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_364(val, _values, result)
                      result = val[1]

    result
end

def _reduce_365(val, _values, result)
                      result = []

    result
end

def _reduce_366(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_367(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[6]).
                                  concat(val[7])

    result
end

def _reduce_368(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_369(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_370(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

# reduce 371 omitted

def _reduce_372(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_373(val, _values, result)
                      if val[1].empty? && val[0].size == 1
                        result = [@builder.procarg0(val[0][0])]
                      else
                        result = val[0].concat(val[1])
                      end

    result
end

def _reduce_374(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_375(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_376(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_377(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_378(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_379(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

# reduce 380 omitted

def _reduce_381(val, _values, result)
                      result = @builder.args(nil, [], nil)

    result
end

def _reduce_382(val, _values, result)
                      @lexer.state = :expr_value

    result
end

def _reduce_383(val, _values, result)
                      result = @builder.args(val[0], val[1], val[2])

    result
end

def _reduce_384(val, _values, result)
                      result = @builder.args(val[0], [], val[0])

    result
end

def _reduce_385(val, _values, result)
                      result = @builder.args(val[0], val[1].concat(val[2]), val[3])

    result
end

def _reduce_386(val, _values, result)
                      result = []

    result
end

def _reduce_387(val, _values, result)
                      result = val[2]

    result
end

def _reduce_388(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_389(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_390(val, _values, result)
                      @static_env.declare val[0][0]
                      result = @builder.shadowarg(val[0])

    result
end

# reduce 391 omitted

def _reduce_392(val, _values, result)
                      @static_env.extend_dynamic

    result
end

def _reduce_393(val, _values, result)
                      result = @lexer.cmdarg.dup
                      @lexer.cmdarg.clear

    result
end

def _reduce_394(val, _values, result)
                      @lexer.cmdarg = val[2]
                      @lexer.cmdarg.lexpop

                      result = [ val[1], val[3] ]

                      @static_env.unextend

    result
end

def _reduce_395(val, _values, result)
                      result = @builder.args(val[0], val[1].concat(val[2]), val[3])

    result
end

def _reduce_396(val, _values, result)
                      result = @builder.args(nil, val[0], nil)

    result
end

def _reduce_397(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_398(val, _values, result)
                      result = [ val[0], val[2], val[3] ]
                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_399(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_400(val, _values, result)
                      result = [ val[0], val[2], val[3] ]
                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_401(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_402(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_403(val, _values, result)
                      begin_t, block_args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, block_args, body, end_t)

    result
end

def _reduce_404(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_405(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                      lparen_t, args, rparen_t)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_406(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                      nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_407(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.call_method(nil, nil, val[0],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_408(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_409(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_410(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2])

    result
end

def _reduce_411(val, _values, result)
                      lparen_t, args, rparen_t = val[2]
                      result = @builder.call_method(val[0], val[1], nil,
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_412(val, _values, result)
                      lparen_t, args, rparen_t = val[2]
                      result = @builder.call_method(val[0], val[1], nil,
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_413(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.keyword_cmd(:super, val[0],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_414(val, _values, result)
                      result = @builder.keyword_cmd(:zsuper, val[0])

    result
end

def _reduce_415(val, _values, result)
                      result = @builder.index(val[0], val[1], val[2], val[3])

    result
end

def _reduce_416(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_417(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_418(val, _values, result)
                      @static_env.extend_dynamic
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_419(val, _values, result)
                      result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend
                      @context.in_block = val[1].in_block

    result
end

def _reduce_420(val, _values, result)
                      result = [ @builder.when(val[0], val[1], val[2], val[3]),
                                 *val[4] ]

    result
end

def _reduce_421(val, _values, result)
                      result = [ val[0] ]

    result
end

# reduce 422 omitted

def _reduce_423(val, _values, result)
                      assoc_t, exc_var = val[2]

                      if val[1]
                        exc_list = @builder.array(nil, val[1], nil)
                      end

                      result = [ @builder.rescue_body(val[0],
                                      exc_list, assoc_t, exc_var,
                                      val[3], val[4]),
                                 *val[5] ]

    result
end

def _reduce_424(val, _values, result)
                      result = []

    result
end

def _reduce_425(val, _values, result)
                      result = [ val[0] ]

    result
end

# reduce 426 omitted

# reduce 427 omitted

def _reduce_428(val, _values, result)
                      result = [ val[0], val[1] ]

    result
end

# reduce 429 omitted

def _reduce_430(val, _values, result)
                      result = [ val[0], val[1] ]

    result
end

# reduce 431 omitted

# reduce 432 omitted

# reduce 433 omitted

# reduce 434 omitted

def _reduce_435(val, _values, result)
                      result = @builder.string_compose(nil, val[0], nil)

    result
end

def _reduce_436(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_437(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_438(val, _values, result)
                      string = @builder.string_compose(val[0], val[1], val[2])
                      result = @builder.dedent_string(string, @lexer.dedent_level)

    result
end

def _reduce_439(val, _values, result)
                      string = @builder.string(val[0])
                      result = @builder.dedent_string(string, @lexer.dedent_level)

    result
end

def _reduce_440(val, _values, result)
                      result = @builder.character(val[0])

    result
end

def _reduce_441(val, _values, result)
                      string = @builder.xstring_compose(val[0], val[1], val[2])
                      result = @builder.dedent_string(string, @lexer.dedent_level)

    result
end

def _reduce_442(val, _values, result)
                      opts   = @builder.regexp_options(val[3])
                      result = @builder.regexp_compose(val[0], val[1], val[2], opts)

    result
end

def _reduce_443(val, _values, result)
                      result = @builder.words_compose(val[0], val[1], val[2])

    result
end

def _reduce_444(val, _values, result)
                      result = []

    result
end

def _reduce_445(val, _values, result)
                      result = val[0] << @builder.word(val[1])

    result
end

def _reduce_446(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_447(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_448(val, _values, result)
                      result = @builder.symbols_compose(val[0], val[1], val[2])

    result
end

def _reduce_449(val, _values, result)
                      result = []

    result
end

def _reduce_450(val, _values, result)
                      result = val[0] << @builder.word(val[1])

    result
end

def _reduce_451(val, _values, result)
                      result = @builder.words_compose(val[0], val[1], val[2])

    result
end

def _reduce_452(val, _values, result)
                      result = @builder.symbols_compose(val[0], val[1], val[2])

    result
end

def _reduce_453(val, _values, result)
                      result = []

    result
end

def _reduce_454(val, _values, result)
                      result = val[0] << @builder.string_internal(val[1])

    result
end

def _reduce_455(val, _values, result)
                      result = []

    result
end

def _reduce_456(val, _values, result)
                      result = val[0] << @builder.symbol_internal(val[1])

    result
end

def _reduce_457(val, _values, result)
                      result = []

    result
end

def _reduce_458(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_459(val, _values, result)
                      result = []

    result
end

def _reduce_460(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_461(val, _values, result)
                      result = []

    result
end

def _reduce_462(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_463(val, _values, result)
                      result = @builder.string_internal(val[0])

    result
end

def _reduce_464(val, _values, result)
                      result = val[1]

    result
end

def _reduce_465(val, _values, result)
                      @lexer.cond.push(false)
                      @lexer.cmdarg.push(false)

    result
end

def _reduce_466(val, _values, result)
                      @lexer.cond.lexpop
                      @lexer.cmdarg.lexpop

                      result = @builder.begin(val[0], val[2], val[3])

    result
end

def _reduce_467(val, _values, result)
                      result = @builder.gvar(val[0])

    result
end

def _reduce_468(val, _values, result)
                      result = @builder.ivar(val[0])

    result
end

def _reduce_469(val, _values, result)
                      result = @builder.cvar(val[0])

    result
end

# reduce 470 omitted

def _reduce_471(val, _values, result)
                      result = @builder.symbol(val[0])

    result
end

def _reduce_472(val, _values, result)
                      result = @builder.symbol_compose(val[0], val[1], val[2])

    result
end

def _reduce_473(val, _values, result)
                      result = val[0]

    result
end

def _reduce_474(val, _values, result)
                      if @builder.respond_to? :negate
                        # AST builder interface compatibility
                        result = @builder.negate(val[0], val[1])
                      else
                        result = @builder.unary_num(val[0], val[1])
                      end

    result
end

def _reduce_475(val, _values, result)
                      result = @builder.integer(val[0])

    result
end

def _reduce_476(val, _values, result)
                      result = @builder.float(val[0])

    result
end

def _reduce_477(val, _values, result)
                      result = @builder.rational(val[0])

    result
end

def _reduce_478(val, _values, result)
                      result = @builder.complex(val[0])

    result
end

def _reduce_479(val, _values, result)
                      result = @builder.ident(val[0])

    result
end

def _reduce_480(val, _values, result)
                      result = @builder.ivar(val[0])

    result
end

def _reduce_481(val, _values, result)
                      result = @builder.gvar(val[0])

    result
end

def _reduce_482(val, _values, result)
                      result = @builder.const(val[0])

    result
end

def _reduce_483(val, _values, result)
                      result = @builder.cvar(val[0])

    result
end

def _reduce_484(val, _values, result)
                      result = @builder.nil(val[0])

    result
end

def _reduce_485(val, _values, result)
                      result = @builder.self(val[0])

    result
end

def _reduce_486(val, _values, result)
                      result = @builder.true(val[0])

    result
end

def _reduce_487(val, _values, result)
                      result = @builder.false(val[0])

    result
end

def _reduce_488(val, _values, result)
                      result = @builder.__FILE__(val[0])

    result
end

def _reduce_489(val, _values, result)
                      result = @builder.__LINE__(val[0])

    result
end

def _reduce_490(val, _values, result)
                      result = @builder.__ENCODING__(val[0])

    result
end

def _reduce_491(val, _values, result)
                      result = @builder.accessible(val[0])

    result
end

def _reduce_492(val, _values, result)
                      result = @builder.accessible(val[0])

    result
end

def _reduce_493(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_494(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_495(val, _values, result)
                      result = @builder.nth_ref(val[0])

    result
end

def _reduce_496(val, _values, result)
                      result = @builder.back_ref(val[0])

    result
end

def _reduce_497(val, _values, result)
                      @lexer.state = :expr_value

    result
end

def _reduce_498(val, _values, result)
                      result = [ val[0], val[2] ]

    result
end

def _reduce_499(val, _values, result)
                      result = nil

    result
end

def _reduce_500(val, _values, result)
                      result = @builder.args(val[0], val[1], val[2])

                      @lexer.state = :expr_value

    result
end

def _reduce_501(val, _values, result)
                      result = @context.in_kwarg
                      @context.in_kwarg = true

    result
end

def _reduce_502(val, _values, result)
                      @context.in_kwarg = val[0]
                      result = @builder.args(nil, val[1], nil)

    result
end

def _reduce_503(val, _values, result)
                      result = val[0].concat(val[2]).concat(val[3])

    result
end

def _reduce_504(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_505(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_506(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_507(val, _values, result)
                      result = val[1]

    result
end

def _reduce_508(val, _values, result)
                      result = []

    result
end

def _reduce_509(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_510(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[6]).
                                  concat(val[7])

    result
end

def _reduce_511(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_512(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_513(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_514(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_515(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_516(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_517(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_518(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_519(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_520(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_521(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_522(val, _values, result)
                      result = val[0]

    result
end

def _reduce_523(val, _values, result)
                      result = []

    result
end

def _reduce_524(val, _values, result)
                      diagnostic :error, :argument_const, nil, val[0]

    result
end

def _reduce_525(val, _values, result)
                      diagnostic :error, :argument_ivar, nil, val[0]

    result
end

def _reduce_526(val, _values, result)
                      diagnostic :error, :argument_gvar, nil, val[0]

    result
end

def _reduce_527(val, _values, result)
                      diagnostic :error, :argument_cvar, nil, val[0]

    result
end

# reduce 528 omitted

def _reduce_529(val, _values, result)
                      @static_env.declare val[0][0]

                      result = val[0]

    result
end

def _reduce_530(val, _values, result)
                      result = val[0]

    result
end

def _reduce_531(val, _values, result)
                      result = @builder.arg(val[0])

    result
end

def _reduce_532(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

def _reduce_533(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_534(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_535(val, _values, result)
                      check_kwarg_name(val[0])

                      @static_env.declare val[0][0]

                      result = val[0]

    result
end

def _reduce_536(val, _values, result)
                      result = @builder.kwoptarg(val[0], val[1])

    result
end

def _reduce_537(val, _values, result)
                      result = @builder.kwarg(val[0])

    result
end

def _reduce_538(val, _values, result)
                      result = @builder.kwoptarg(val[0], val[1])

    result
end

def _reduce_539(val, _values, result)
                      result = @builder.kwarg(val[0])

    result
end

def _reduce_540(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_541(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_542(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_543(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 544 omitted

# reduce 545 omitted

def _reduce_546(val, _values, result)
                      @static_env.declare val[1][0]

                      result = [ @builder.kwrestarg(val[0], val[1]) ]

    result
end

def _reduce_547(val, _values, result)
                      result = [ @builder.kwrestarg(val[0]) ]

    result
end

def _reduce_548(val, _values, result)
                      result = @builder.optarg(val[0], val[1], val[2])

    result
end

def _reduce_549(val, _values, result)
                      result = @builder.optarg(val[0], val[1], val[2])

    result
end

def _reduce_550(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_551(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_552(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_553(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 554 omitted

# reduce 555 omitted

def _reduce_556(val, _values, result)
                      @static_env.declare val[1][0]

                      result = [ @builder.restarg(val[0], val[1]) ]

    result
end

def _reduce_557(val, _values, result)
                      result = [ @builder.restarg(val[0]) ]

    result
end

# reduce 558 omitted

# reduce 559 omitted

def _reduce_560(val, _values, result)
                      @static_env.declare val[1][0]

                      result = @builder.blockarg(val[0], val[1])

    result
end

def _reduce_561(val, _values, result)
                      result = [ val[1] ]

    result
end

def _reduce_562(val, _values, result)
                      result = []

    result
end

# reduce 563 omitted

def _reduce_564(val, _values, result)
                      result = val[1]

    result
end

def _reduce_565(val, _values, result)
                      result = []

    result
end

# reduce 566 omitted

def _reduce_567(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_568(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_569(val, _values, result)
                      result = @builder.pair(val[0], val[1], val[2])

    result
end

def _reduce_570(val, _values, result)
                      result = @builder.pair_keyword(val[0], val[1])

    result
end

def _reduce_571(val, _values, result)
                      result = @builder.pair_quoted(val[0], val[1], val[2], val[3])

    result
end

def _reduce_572(val, _values, result)
                      result = @builder.kwsplat(val[0], val[1])

    result
end

# reduce 573 omitted

# reduce 574 omitted

# reduce 575 omitted

# reduce 576 omitted

# reduce 577 omitted

# reduce 578 omitted

# reduce 579 omitted

# reduce 580 omitted

# reduce 581 omitted

# reduce 582 omitted

# reduce 583 omitted

# reduce 584 omitted

def _reduce_585(val, _values, result)
                      result = [:dot, val[0][1]]

    result
end

def _reduce_586(val, _values, result)
                      result = [:anddot, val[0][1]]

    result
end

# reduce 587 omitted

# reduce 588 omitted

# reduce 589 omitted

# reduce 590 omitted

def _reduce_591(val, _values, result)
                      result = val[1]

    result
end

def _reduce_592(val, _values, result)
                      result = val[1]

    result
end

# reduce 593 omitted

# reduce 594 omitted

# reduce 595 omitted

def _reduce_596(val, _values, result)
                    yyerrok

    result
end

# reduce 597 omitted

# reduce 598 omitted

# reduce 599 omitted

def _reduce_600(val, _values, result)
                    result = nil

    result
end

def _reduce_none(val, _values, result)
  val[0]
end

  end   # class Ruby23
end   # module Parser
