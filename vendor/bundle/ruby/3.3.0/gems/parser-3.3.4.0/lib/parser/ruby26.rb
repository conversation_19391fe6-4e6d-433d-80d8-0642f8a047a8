# -*- encoding:utf-8; warn-indent:false; frozen_string_literal: true  -*-
#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.7.3
# from Racc grammar file "ruby26.y".
#

require 'racc/parser.rb'


require_relative '../parser'

module Parser
  class Ruby26 < Parser::Base


  def version
    26
  end

  def default_encoding
    Encoding::UTF_8
  end

  def local_push
    @static_env.extend_static
    @lexer.cmdarg.push(false)
    @lexer.cond.push(false)
  end

  def local_pop
    @static_env.unextend
    @lexer.cmdarg.pop
    @lexer.cond.pop
  end
##### State transition tables begin ###

racc_action_table = [
  -489,   215,   216,   215,   216,   223,  -102,  -489,  -489,  -489,
   114,   550,  -489,  -489,  -489,   221,  -489,   277,   225,   591,
   263,   264,   270,   550,  -489,   593,  -489,  -489,  -489,   277,
   563,   277,  -503,   127,   564,  -103,  -489,  -489,   550,  -489,
  -489,  -489,  -489,  -489,   550,   550,  -102,  -103,  -110,  -110,
   -88,  -109,  -101,   792,  -109,   557,   262,   261,   277,   226,
   -74,   212,  -110,   213,  -105,  -107,  -489,  -489,  -489,  -489,
  -489,  -489,  -489,  -489,  -489,  -489,  -489,  -489,  -489,  -489,
   222,   272,  -489,  -489,  -489,   590,  -489,  -489,   706,  -102,
  -489,   592,   873,  -489,  -489,   226,  -489,   226,  -489,   214,
  -489,   217,  -489,  -489,   276,  -489,  -489,  -489,  -489,  -489,
  -105,  -489,  -492,  -489,  -107,   -93,   276,  -104,   276,  -492,
  -492,  -492,   272,  -106,  -492,  -492,  -492,  -489,  -492,   118,
  -489,  -489,  -489,  -489,   117,  -489,  -492,  -489,  -492,  -492,
  -492,   118,  -489,  -489,   -94,   276,   117,   -96,  -492,  -492,
  -108,  -492,  -492,  -492,  -492,  -492,   118,  -104,  -101,   826,
  -100,   117,   118,   118,  -102,  -103,  -110,   117,   117,  -102,
  -103,  -110,  -109,   -96,   -98,  -106,   780,  -109,  -492,  -492,
  -492,  -492,  -492,  -492,  -492,  -492,  -492,  -492,  -492,  -492,
  -492,  -492,   118,   266,  -492,  -492,  -492,   117,  -492,  -492,
  -586,   872,  -492,   325,   -98,  -492,  -492,   326,  -492,  -108,
  -492,   779,  -492,   -96,  -492,  -492,   226,  -492,  -492,  -492,
  -492,  -492,  -298,  -492,   396,  -492,   -95,  -587,  -105,  -298,
  -298,  -298,  -107,  -105,   780,  -298,  -298,  -107,  -298,  -492,
  -586,  -106,  -492,  -492,  -492,  -492,  -106,  -492,   409,  -492,
   215,   216,  -504,   -96,  -492,  -492,   -96,   458,  -298,  -298,
   -95,  -298,  -298,  -298,  -298,  -298,   -96,   460,  -108,   779,
   -98,  -489,   -97,  -108,  -587,  -104,   215,   216,  -489,   683,
  -104,   680,   679,   678,   -97,   681,   215,   216,  -298,  -298,
  -298,  -298,  -298,  -298,  -298,  -298,  -298,  -298,  -298,  -298,
  -298,  -298,   461,   459,  -298,  -298,  -298,  -492,   616,  -103,
   -98,  -503,  -298,   -98,  -492,  -298,    96,    97,   -99,   578,
  -298,   225,  -298,   -98,  -298,  -298,   -95,  -298,  -298,  -298,
  -298,  -298,  -499,  -298,  -590,  -298,  -489,  -586,   -97,  -499,
   406,  -590,  -590,  -590,   226,   408,   407,  -590,  -590,  -298,
  -590,   118,  -298,  -298,   489,   -99,   117,  -298,   -93,  -590,
  -110,   891,   215,   216,  -298,  -108,   -95,  -583,  -102,   -95,
  -590,  -590,  -492,  -590,  -590,  -590,  -590,  -590,   -97,   -95,
   118,   -97,   498,   580,   579,   117,   683,   578,   680,   679,
   678,   -97,   681,   738,    98,    99,   500,  -499,   578,   223,
  -590,  -590,  -590,  -590,  -590,  -590,  -590,  -590,  -590,  -590,
  -590,  -590,  -590,  -590,   527,   502,  -590,  -590,  -590,  -489,
   617,    96,    97,   578,  -590,   118,  -489,  -590,   118,   766,
   117,  -583,  -590,   117,  -590,  -489,  -590,  -590,  -504,  -590,
  -590,  -590,  -590,  -590,   767,  -590,  -590,  -590,   578,   127,
  -498,   580,   579,   576,  -583,   578,  -110,  -498,   -73,  -584,
   578,  -590,   580,   579,  -590,  -590,  -590,   -97,   984,  -590,
   226,  -109,   740,  -590,  -590,  -590,  -590,  -106,  -590,  -590,
  -590,   513,  -590,   514,  -489,   -94,  -105,   580,   579,   576,
  -500,  -590,  -590,  -590,  -590,  -103,   521,  -500,   281,    98,
    99,  -107,  -590,  -590,   644,  -590,  -590,  -590,  -590,  -590,
   527,  -492,   580,   579,   581,  -498,   578,   226,  -492,   580,
   579,   583,   578,  -584,   580,   579,   585,  -492,   540,   823,
   792,   539,  -590,  -590,  -590,  -590,  -590,  -590,  -590,  -590,
  -590,  -590,  -590,  -590,  -590,  -590,  -584,   272,  -590,  -590,
  -590,   524,   768,  -590,   221,  -500,  -590,  -100,  -497,  -590,
  -590,   220,  -590,   528,  -590,  -497,  -590,  -109,  -590,  -590,
   218,  -590,  -590,  -590,  -590,  -590,  -492,  -590,  -590,  -590,
   580,   579,   589,  -494,  -336,  -495,   580,   579,   594,   246,
  -494,  -336,  -495,  -590,   246,   611,  -590,  -590,  -590,  -590,
  -336,  -590,   611,  -590,  -298,   612,   845,   612,  -590,  -106,
   118,  -298,  -298,  -298,   226,   117,  -298,  -298,  -298,   222,
  -298,   243,   543,  -497,   544,   245,   244,  -496,   221,   246,
  -298,  -298,  -298,   540,  -496,   270,   542,   502,   753,   753,
  -298,  -298,   -96,  -298,  -298,  -298,  -298,  -298,  -494,  -336,
  -495,   221,  -105,  -501,   -98,   699,   698,   557,   456,  -105,
  -501,   243,  -107,  -104,  -107,   245,   244,   457,   398,  -501,
  -298,  -298,  -298,  -298,  -298,  -298,  -298,  -298,  -298,  -298,
  -298,  -298,  -298,  -298,   118,   -95,  -298,  -298,  -298,   117,
   769,  -298,  -496,   222,  -298,  -104,  -104,  -298,  -298,   561,
  -298,   562,  -298,   570,  -298,   540,  -298,  -298,   542,  -298,
  -298,  -298,  -298,  -298,  -298,  -298,   222,  -298,  -501,   823,
   792,  -298,  -298,  -298,   595,   598,   540,  -298,  -298,   542,
  -298,  -298,   246,  -271,  -298,  -298,  -298,  -298,   600,  -298,
    84,  -298,  -590,   601,   605,   226,  -298,  -108,   256,   257,
  -298,  -298,    85,  -298,  -298,  -298,  -298,  -298,   226,  -502,
   609,  1000,    86,   221,   243,   610,  -502,   272,   245,   244,
   520,   241,   242,   621,   246,  -502,   246,   246,   246,   518,
  -298,  -298,  -298,  -298,  -298,  -298,  -298,  -298,  -298,  -298,
  -298,  -298,  -298,  -298,  -590,  -289,  -298,  -298,  -298,   226,
   616,  -590,  -289,   226,  -298,   226,  -586,  -298,   -88,   647,
  -590,  -289,  -298,   226,  -298,   533,  -298,  -298,   658,  -298,
  -298,  -298,  -298,  -298,  -502,  -298,  -590,  -298,   222,  -590,
   663,   664,   226,  -590,  -590,  -590,   688,   666,   702,  -590,
  -590,  -298,  -590,   707,  -298,  -298,   691,  -298,   221,  -298,
   708,  -590,   557,   710,   727,   530,  -298,  -108,   737,  -590,
  -289,   741,  -590,  -590,   457,  -590,  -590,  -590,  -590,  -590,
   683,   221,   680,   679,   678,   742,   681,  -272,   560,   699,
   698,   754,   489,   489,   692,   732,   733,   558,   226,   734,
   112,   113,  -590,  -590,  -590,  -590,  -590,  -590,  -590,  -590,
  -590,  -590,  -590,  -590,  -590,  -590,   771,   812,  -590,  -590,
  -590,   221,   617,   222,   221,   772,  -590,   266,   566,  -590,
   777,   604,   782,   500,  -590,   502,  -590,   568,  -590,  -590,
   602,  -590,  -590,  -590,  -590,  -590,   222,  -590,  -590,  -590,
   683,   658,   680,   679,   678,   688,   681,  -299,   121,   122,
   123,   124,   125,  -590,  -299,   691,  -590,  -590,  -419,  -590,
   226,  -590,   272,  -299,   272,  -419,  -419,  -419,  -590,  -106,
  -419,  -419,  -419,   658,  -419,   246,   222,   686,   792,   222,
   800,   246,   803,  -419,  -419,  -419,   696,   695,   699,   698,
   804,   806,   808,   692,  -419,  -419,   810,  -419,  -419,  -419,
  -419,  -419,   683,   818,   680,   679,   678,  -299,   681,   819,
   820,   792,  -299,   243,  -299,   825,   226,   245,   244,   226,
   241,   242,   226,  -299,  -419,  -419,  -419,  -419,  -419,  -419,
  -419,  -419,  -419,  -419,  -419,  -419,  -419,  -419,   834,   812,
  -419,  -419,  -419,  -273,   226,  -419,   844,   272,  -419,   848,
   658,  -419,  -419,   865,  -419,  -271,  -419,   869,  -419,   226,
  -419,  -419,   889,  -419,  -419,  -419,  -419,  -419,  -305,  -419,
  -419,  -419,  -299,   226,   893,  -305,  -305,  -305,   895,   898,
  -305,  -305,  -305,   221,  -305,  -419,   899,   902,  -419,  -419,
   970,  -419,   226,  -419,  -305,  -305,   906,  -274,   908,   568,
  -419,   803,   911,   913,  -305,  -305,   915,  -305,  -305,  -305,
  -305,  -305,   917,   226,   919,  -298,   920,  -298,   933,   803,
   935,   937,  -298,   939,  -298,   941,   941,  -587,   226,  -587,
   947,  -298,   953,  -298,  -305,  -305,  -305,  -305,  -305,  -305,
  -305,  -305,  -305,  -305,  -305,  -305,  -305,  -305,   222,   727,
  -305,  -305,  -305,   964,   971,  -305,   976,   281,  -305,   986,
   803,  -305,  -305,   990,  -305,   992,  -305,   994,  -305,   996,
  -305,  -305,   996,  -305,  -305,  -305,  -305,  -305,  -290,  -305,
  -298,  -305,  -298,   663,  1009,  -290,  -290,  -290,  1010,  1011,
  -290,  -290,  -290,   221,  -290,  -305,   941,   941,  -305,  -305,
   975,  -305,   941,  -305,  -290,  -290,  -290,  1016,   986,   973,
  -305,  1019,  -587,  -586,  -290,  -290,   226,  -290,  -290,  -290,
  -290,  -290,   967,   221,   680,   679,   678,   986,   681,   683,
   970,   680,   679,   678,   967,   681,   680,   679,   678,   568,
   681,  1028,   996,   996,  -290,  -290,  -290,  -290,  -290,  -290,
  -290,  -290,  -290,  -290,  -290,  -290,  -290,  -290,   222,   688,
  -290,  -290,  -290,   996,   941,  -290,   812,   986,  -290,   691,
   996,  -290,  -290,   nil,  -290,   815,  -290,   nil,  -290,   nil,
  -290,  -290,   nil,  -290,  -290,  -290,  -290,  -290,   222,  -290,
   nil,  -290,   683,   nil,   680,   679,   678,   nil,   681,   nil,
   nil,   nil,   699,   698,   nil,  -290,   nil,   692,  -290,  -290,
  -290,  -290,   nil,  -290,  -254,  -290,   nil,   nil,   nil,   nil,
  -290,  -254,  -254,  -254,   nil,   nil,  -254,  -254,  -254,   812,
  -254,   683,   nil,   680,   679,   678,   nil,   681,   815,  -254,
  -254,  -254,   683,   nil,   680,   679,   678,   nil,   681,   nil,
  -254,  -254,   nil,  -254,  -254,  -254,  -254,  -254,   683,   nil,
   680,   679,   678,   683,   681,   680,   679,   678,   812,   681,
   121,   122,   123,   124,   125,   nil,   nil,   946,   nil,   812,
  -254,  -254,  -254,  -254,  -254,  -254,  -254,  -254,  -254,  -254,
  -254,  -254,  -254,  -254,   nil,   812,  -254,  -254,  -254,   nil,
   812,  -254,   nil,   272,  -254,   nil,   nil,  -254,  -254,   nil,
  -254,   nil,  -254,   nil,  -254,   nil,  -254,  -254,   nil,  -254,
  -254,  -254,  -254,  -254,   nil,  -254,  -254,  -254,   683,   nil,
   680,   679,   678,   688,   681,   121,   122,   123,   124,   125,
   nil,  -254,   nil,   691,  -254,  -254,  -591,  -254,   nil,  -254,
   nil,   nil,   nil,  -591,  -591,  -591,  -254,   nil,  -591,  -591,
  -591,   nil,  -591,   246,   nil,   686,   121,   122,   123,   124,
   125,  -591,  -591,  -591,  -591,   nil,   699,   698,   nil,   256,
   257,   692,  -591,  -591,   nil,  -591,  -591,  -591,  -591,  -591,
   nil,   nil,   nil,   nil,   nil,   243,   nil,   249,   nil,   245,
   244,   nil,   241,   242,   nil,   nil,   247,   nil,   248,   nil,
   nil,   nil,  -591,  -591,  -591,  -591,  -591,  -591,  -591,  -591,
  -591,  -591,  -591,  -591,  -591,  -591,   nil,   nil,  -591,  -591,
  -591,   246,   nil,  -591,   nil,   nil,  -591,   nil,   nil,  -591,
  -591,   nil,  -591,   nil,  -591,   nil,  -591,   nil,  -591,  -591,
   nil,  -591,  -591,  -591,  -591,  -591,   nil,  -591,  -591,  -591,
   nil,   nil,   nil,   243,   nil,   nil,   nil,   245,   244,   nil,
   241,   242,   nil,  -591,   nil,   nil,  -591,  -591,  -591,  -591,
   nil,  -591,  -592,  -591,   nil,   nil,   nil,   nil,  -591,  -592,
  -592,  -592,   nil,   nil,  -592,  -592,  -592,   246,  -592,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -592,  -592,  -592,
  -592,   nil,   nil,   256,   257,   nil,   nil,   nil,  -592,  -592,
   nil,  -592,  -592,  -592,  -592,  -592,   nil,   nil,   nil,   243,
   nil,   249,   nil,   245,   244,   nil,   241,   242,   nil,   nil,
   247,   nil,   248,   nil,   nil,   nil,   nil,   nil,  -592,  -592,
  -592,  -592,  -592,  -592,  -592,  -592,  -592,  -592,  -592,  -592,
  -592,  -592,   nil,   nil,  -592,  -592,  -592,   nil,   nil,  -592,
   nil,   nil,  -592,   nil,   nil,  -592,  -592,   nil,  -592,   nil,
  -592,   nil,  -592,   nil,  -592,  -592,   nil,  -592,  -592,  -592,
  -592,  -592,   nil,  -592,  -592,  -592,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  -592,
   nil,   nil,  -592,  -592,  -592,  -592,   nil,  -592,  -254,  -592,
   nil,   nil,   nil,   nil,  -592,  -254,  -254,  -254,   nil,   nil,
  -254,  -254,  -254,   246,  -254,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,  -254,  -254,   nil,   nil,   nil,   nil,   256,
   257,   nil,   nil,   nil,  -254,  -254,   nil,  -254,  -254,  -254,
  -254,  -254,   nil,   nil,   nil,   243,   nil,   249,   nil,   245,
   244,   nil,   241,   242,   nil,   nil,   nil,   246,   250,   251,
   252,   253,   263,   264,   258,   259,   254,   255,   nil,   239,
   240,   nil,   nil,   256,   257,  -254,   nil,   nil,   nil,   nil,
   nil,   nil,  -254,   nil,   nil,   nil,   nil,   272,  -254,   243,
   nil,   249,   nil,   245,   244,   nil,   241,   242,   262,   261,
   247,   nil,   248,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
  -254,  -254,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   260,   nil,   nil,  -254,   nil,   nil,  -254,   nil,
   nil,   nil,   nil,  -254,     5,    75,    76,    77,     9,    58,
  -254,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   606,     8,    46,     7,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   246,
   250,   251,   252,   253,   263,   264,   258,   259,   254,   255,
   nil,   239,   240,   nil,   nil,   256,   257,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   243,   nil,   249,    45,   245,   244,   nil,   241,   242,
   262,   261,   247,    20,   248,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   260,   nil,  -246,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   606,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   246,
   250,   251,   252,   253,   263,   264,   258,   259,   254,   255,
   nil,   239,   240,   nil,   nil,   256,   257,   nil,    40,   nil,
   nil,   303,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   243,   nil,   249,    45,   245,   244,   nil,   241,   242,
   262,   261,   247,    20,   248,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   260,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,     5,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,     7,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   246,
   250,   251,   252,   253,   263,   264,   258,   259,   254,   255,
   nil,   239,   240,   nil,   nil,   256,   257,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   243,   nil,   249,    45,   245,   244,   nil,   241,   242,
   262,   261,   247,    20,   248,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   260,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   246,
   250,   251,   252,   253,   263,   264,   258,   259,   254,   255,
   nil,   239,   240,   nil,   nil,   256,   257,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   243,   nil,   249,    45,   245,   244,   nil,   241,   242,
   262,   261,   247,    20,   248,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   226,   260,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   246,
   250,   251,   252,   253,   263,   264,   258,   259,   254,   255,
   nil,   239,   240,   nil,   nil,   256,   257,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   243,   nil,   249,    45,   245,   244,   nil,   241,   242,
   262,   261,   247,    20,   248,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   260,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   246,
   250,   251,   252,   253,   263,   264,   258,   259,   254,   255,
   nil,   239,   240,   nil,   nil,   256,   257,   nil,    40,   nil,
   nil,   303,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   243,   nil,   249,    45,   245,   244,   nil,   241,   242,
   262,   261,   247,    20,   248,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   260,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   246,
   250,   251,   252,   253,   263,   264,   258,   259,   254,   255,
   nil,   239,   240,   nil,   nil,   256,   257,   nil,    40,   nil,
   nil,   303,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   243,   nil,   249,    45,   245,   244,   nil,   241,   242,
   262,   261,   247,    20,   248,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   260,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   246,
   250,   251,   252,   253,   263,   264,   258,   259,   254,   255,
   nil,  -611,  -611,   nil,   nil,   256,   257,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   243,   nil,   249,    45,   245,   244,   nil,   241,   242,
   262,   261,   247,    20,   248,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   246,
   250,   251,   252,   253,   263,   264,   258,   259,   254,   255,
   nil,  -611,  -611,   nil,   nil,   256,   257,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   243,   nil,   249,    45,   245,   244,   nil,   241,   242,
   262,   261,   247,    20,   248,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   246,
  -611,  -611,  -611,  -611,   263,   264,   nil,   nil,  -611,  -611,
   nil,   nil,   nil,   nil,   nil,   256,   257,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   243,   nil,   249,    45,   245,   244,   nil,   241,   242,
   262,   261,   247,    20,   248,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   246,
  -611,  -611,  -611,  -611,   263,   264,   nil,   nil,  -611,  -611,
   nil,   nil,   nil,   nil,   nil,   256,   257,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   243,   nil,   249,    45,   245,   244,   nil,   241,   242,
   262,   261,   247,    20,   248,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   246,
  -611,  -611,  -611,  -611,   263,   264,   nil,   nil,  -611,  -611,
   nil,   nil,   nil,   nil,   nil,   256,   257,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   243,   nil,   249,    45,   245,   244,   nil,   241,   242,
   262,   261,   247,    20,   248,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   246,
  -611,  -611,  -611,  -611,   263,   264,   nil,   nil,  -611,  -611,
   nil,   nil,   nil,   nil,   nil,   256,   257,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   243,   nil,   249,    45,   245,   244,   nil,   241,   242,
   262,   261,   247,    20,   248,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   246,
  -611,  -611,  -611,  -611,   263,   264,   nil,   nil,  -611,  -611,
   nil,   nil,   nil,   nil,   nil,   256,   257,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   243,   nil,   249,    45,   245,   244,   nil,   241,   242,
   262,   261,   247,    20,   248,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   246,
  -611,  -611,  -611,  -611,   263,   264,   nil,   nil,  -611,  -611,
   nil,   nil,   nil,   nil,   nil,   256,   257,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   243,   nil,   249,    45,   245,   244,   nil,   241,   242,
   262,   261,   247,    20,   248,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   246,
   250,   251,   252,   253,   263,   264,   nil,   nil,   254,   255,
   nil,   nil,   nil,   nil,   nil,   256,   257,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   243,   nil,   249,    45,   245,   244,   nil,   241,   242,
   262,   261,   247,    20,   248,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   246,
   250,   251,   252,   253,   263,   264,   258,   nil,   254,   255,
   nil,   nil,   nil,   nil,   nil,   256,   257,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   243,   nil,   249,    45,   245,   244,   nil,   241,   242,
   262,   261,   247,    20,   248,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   246,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   256,   257,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   243,   nil,   249,    45,   245,   244,   nil,   241,   242,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,   299,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   301,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,   nil,    63,
   nil,    86,    98,    99,    75,    76,    77,     9,    58,   nil,
   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,   nil,
   nil,    78,    28,    27,   106,   105,   107,   108,   nil,   nil,
    19,   nil,   nil,   nil,   nil,   nil,     8,    46,     7,    10,
   110,   109,   111,   100,    57,   102,   101,   103,   nil,   104,
   112,   113,   nil,    96,    97,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,   nil,
    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,    35,
   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    20,   nil,   nil,   nil,   nil,    94,    84,    87,
    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,   nil,
    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,    58,
    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,
   nil,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,
   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,
    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,    30,    31,    73,    74,   nil,   nil,
   nil,   nil,   nil,    78,    28,    27,   106,   105,   107,   108,
   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,
   nil,   nil,   110,   109,   111,   100,    57,   102,   101,   103,
   293,   104,   112,   113,   nil,    96,    97,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,
   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   290,   nil,   288,   nil,    45,   nil,   nil,   294,   nil,
   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,
   291,    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,
   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,
    63,    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,    30,    31,    73,    74,   nil,
   nil,   nil,   nil,   nil,    78,    28,    27,   106,   105,   107,
   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   110,   109,   111,   100,    57,   102,   101,
   103,   293,   104,   112,   113,   nil,    96,    97,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   231,   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,   290,   nil,   288,   nil,    45,   nil,   nil,   294,
   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,
    94,   291,    87,    88,   nil,    89,    91,    90,    92,   nil,
   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,
    77,    63,    58,    86,    98,    99,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,    30,    31,    73,    74,
   nil,   nil,   nil,   nil,   nil,    78,    28,    27,   106,   105,
   107,   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,
   nil,    46,   nil,   nil,   110,   109,   111,   100,    57,   102,
   101,   103,   293,   104,   112,   113,   nil,    96,    97,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   231,   nil,   nil,   237,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,   290,   nil,   288,   nil,    45,   nil,   nil,
   294,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,
   nil,    94,   291,    87,    88,   nil,    89,    91,    90,    92,
   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,    75,
    76,    77,    63,    58,    86,    98,    99,    64,    65,   nil,
   nil,   nil,    68,   nil,    66,    67,    69,   318,   319,    73,
    74,   nil,   nil,   nil,   nil,   nil,    78,   315,   321,   106,
   105,   107,   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,
   nil,   nil,   316,   nil,   nil,   110,   109,   111,   100,    57,
   102,   101,   103,   nil,   104,   112,   113,   nil,    96,    97,
   nil,   nil,   322,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   312,   nil,   nil,   308,   nil,   nil,    59,    60,
   nil,   nil,    61,   nil,   307,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    94,    84,    87,    88,   nil,    89,    91,    90,
    92,   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,
    75,    76,    77,    63,    58,    86,    98,    99,    64,    65,
   nil,   nil,   nil,    68,   nil,    66,    67,    69,   318,   319,
    73,    74,   nil,   nil,   nil,   nil,   nil,    78,   315,   321,
   106,   105,   107,   108,   nil,   nil,   238,   nil,   nil,   nil,
   nil,   nil,   nil,   316,   nil,   nil,   110,   109,   111,   100,
    57,   102,   101,   103,   nil,   104,   112,   113,   nil,    96,
    97,   nil,   nil,   322,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   312,   nil,   nil,   237,   nil,   nil,    59,
    60,   nil,   nil,    61,   nil,   nil,   683,   nil,   680,   679,
   678,   688,   681,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   691,   nil,    94,    84,    87,    88,   nil,    89,    91,
    90,    92,   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,
   nil,   324,   nil,   686,    63,   nil,    86,    98,    99,    75,
    76,    77,   nil,    58,   699,   698,   nil,    64,    65,   692,
   nil,   nil,    68,   nil,    66,    67,    69,   318,   319,    73,
    74,   nil,   nil,   nil,   nil,   nil,    78,   315,   321,   106,
   105,   107,   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,
   nil,   nil,    46,   nil,   nil,   110,   109,   111,   100,    57,
   102,   101,   103,   nil,   104,   112,   113,   nil,    96,    97,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,    59,    60,
   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,
   nil,   nil,    94,    84,    87,    88,   nil,    89,    91,    90,
    92,   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,
    75,    76,    77,    63,    58,    86,    98,    99,    64,    65,
   nil,   nil,   nil,    68,   nil,    66,    67,    69,   318,   319,
    73,    74,   nil,   nil,   nil,   nil,   nil,    78,   315,   321,
   106,   105,   107,   108,   nil,   nil,   238,   nil,   nil,   nil,
   nil,   nil,   nil,    46,   nil,   nil,   110,   109,   111,   100,
    57,   102,   101,   103,   nil,   104,   112,   113,   nil,    96,
    97,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,    59,
    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,
   nil,   nil,   nil,    94,    84,    87,    88,   nil,    89,    91,
    90,    92,   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,
   nil,    75,    76,    77,    63,    58,    86,    98,    99,    64,
    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,   318,
   319,    73,    74,   nil,   nil,   nil,   nil,   nil,    78,   315,
   321,   106,   105,   107,   108,   nil,   nil,   238,   nil,   nil,
   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,   109,   111,
   100,    57,   102,   101,   103,   nil,   104,   112,   113,   nil,
    96,    97,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,
    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   236,
   nil,   nil,   nil,   nil,    94,    84,    87,    88,   nil,    89,
    91,    90,    92,   nil,   nil,   nil,   nil,    85,    93,   nil,
   nil,   nil,   nil,   nil,   nil,    63,   nil,    86,    98,    99,
    75,    76,    77,     9,    58,   nil,   nil,   nil,    64,    65,
   nil,   nil,   nil,    68,   nil,    66,    67,    69,    30,    31,
    73,    74,   nil,   nil,   nil,   nil,   nil,    78,    28,    27,
   106,   105,   107,   108,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,     8,    46,   nil,    10,   110,   109,   111,   100,
    57,   102,   101,   103,   nil,   104,   112,   113,   nil,    96,
    97,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,    33,   nil,   nil,    59,
    60,   nil,   nil,    61,   nil,    35,   nil,   nil,   nil,    45,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    94,    84,    87,    88,   nil,    89,    91,
    90,    92,   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,
   nil,    75,    76,    77,    63,    58,    86,    98,    99,    64,
    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,   318,
   319,    73,    74,   nil,   nil,   nil,   nil,   nil,    78,   315,
   321,   106,   105,   107,   108,   nil,   nil,   238,   nil,   nil,
   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,   109,   111,
   100,    57,   102,   101,   103,   293,   104,   112,   113,   nil,
    96,    97,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,
    59,    60,   nil,   nil,    61,   nil,   290,   nil,   nil,   nil,
    45,   nil,   nil,   294,   nil,   nil,   nil,   nil,   nil,   236,
   nil,   nil,   nil,   nil,    94,   291,    87,    88,   nil,    89,
    91,    90,    92,   nil,   nil,   nil,   nil,    85,    93,   nil,
   nil,   nil,    75,    76,    77,    63,    58,    86,    98,    99,
    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,
   318,   319,    73,    74,   nil,   nil,   nil,   nil,   nil,    78,
   315,   321,   106,   105,   107,   108,   nil,   nil,   238,   nil,
   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,   109,
   111,   100,    57,   102,   101,   103,   293,   104,   112,   113,
   nil,    96,    97,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,
   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   294,   nil,   nil,   nil,   nil,   nil,
   236,   nil,   nil,   nil,   nil,    94,   291,    87,    88,   nil,
    89,    91,    90,    92,   nil,   nil,   nil,   nil,    85,    93,
   nil,   nil,   nil,    75,    76,    77,    63,    58,    86,    98,
    99,    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,
    69,    30,    31,    73,    74,   nil,   nil,   nil,   nil,   nil,
    78,    28,    27,   106,   105,   107,   108,   nil,   nil,    19,
   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,
   109,   111,   100,    57,   102,   101,   103,   nil,   104,   112,
   113,   nil,    96,    97,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,
   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    20,   nil,   nil,   nil,   nil,    94,    84,    87,    88,
   nil,    89,    91,    90,    92,   nil,   nil,   nil,   nil,    85,
    93,   nil,   nil,   nil,    75,    76,    77,    63,    58,    86,
    98,    99,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,   nil,
   nil,    78,    28,    27,   106,   105,   107,   108,   nil,   nil,
    19,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,
   110,   109,   111,   100,    57,   102,   101,   103,   nil,   104,
   112,   113,   nil,    96,    97,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,
   237,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    20,   nil,   nil,   nil,   nil,    94,    84,    87,
    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,   nil,
    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,    58,
    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,
   nil,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,
   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   118,   nil,   nil,   nil,   nil,   117,    63,
   nil,    86,    98,    99,    75,    76,    77,   nil,    58,   nil,
   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,   318,   319,    73,    74,   nil,   nil,   nil,   nil,
   nil,    78,   315,   321,   106,   105,   107,   108,   nil,   nil,
   238,   nil,   nil,   nil,   nil,   nil,   nil,   316,   nil,   nil,
   110,   109,   111,   100,    57,   102,   101,   103,   nil,   104,
   112,   113,   nil,    96,    97,   nil,   nil,   322,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   358,   nil,   nil,
    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,    35,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    94,    84,    87,
    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,   nil,
    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,    58,
    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,   318,   319,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,   315,   321,   106,   105,   107,   108,   nil,
   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,   316,   nil,
   nil,   110,   109,   111,   363,    57,   102,   101,   364,   nil,
   104,   112,   113,   nil,    96,    97,   nil,   nil,   322,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   370,   nil,   nil,   365,   nil,
   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,
    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,   318,   319,    73,    74,   nil,   nil,
   nil,   nil,   nil,    78,   315,   321,   106,   105,   107,   108,
   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,   316,
   nil,   nil,   110,   109,   111,   363,    57,   102,   101,   364,
   nil,   104,   112,   113,   nil,    96,    97,   nil,   nil,   322,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   365,
   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   nil,   683,   nil,   680,   679,   678,   688,   681,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   691,   nil,    94,
    84,    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,
   nil,   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,   686,
    63,   nil,    86,    98,    99,    75,    76,    77,     9,    58,
   699,   698,   nil,    64,    65,   692,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,     7,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,   398,    63,
   nil,    86,    98,    99,    75,    76,    77,   nil,    58,   nil,
   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,   nil,
   nil,    78,    28,    27,   106,   105,   107,   108,   nil,   nil,
    19,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,
   110,   109,   111,   100,    57,   102,   101,   103,   nil,   104,
   112,   113,   nil,    96,    97,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,
   237,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    20,   nil,   nil,   nil,   nil,    94,    84,    87,
    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,   nil,
    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,    58,
    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,
   nil,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,
   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,
    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,    30,    31,    73,    74,   nil,   nil,
   nil,   nil,   nil,    78,    28,    27,   106,   105,   107,   108,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    46,
   nil,   nil,   110,   109,   111,   100,    57,   102,   101,   103,
   nil,   104,   112,   113,   nil,    96,    97,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,
   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    94,
    84,    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,
   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,
    63,    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,    30,    31,    73,    74,   nil,
   nil,   nil,   nil,   nil,    78,    28,    27,   106,   105,   107,
   108,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   110,   109,   111,   100,    57,   102,   101,
   103,   nil,   104,   112,   113,   nil,    96,    97,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   231,   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,
    94,    84,    87,    88,   nil,    89,    91,    90,    92,   nil,
   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,
   nil,    63,   nil,    86,    98,    99,    75,    76,    77,     9,
    58,   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,    30,    31,    73,    74,   nil,   nil,
   nil,   nil,   nil,    78,    28,    27,   106,   105,   107,   108,
   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,
   nil,    10,   110,   109,   111,   100,    57,   102,   101,   103,
   nil,   104,   112,   113,   nil,    96,    97,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,
   nil,   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    94,
    84,    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,
   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,
    63,    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,    30,    31,    73,    74,   nil,
   nil,   nil,   nil,   nil,    78,    28,    27,   106,   105,   107,
   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   110,   109,   111,   100,    57,   102,   101,
   103,   nil,   104,   112,   113,   nil,    96,    97,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   231,   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,   414,   nil,   nil,   nil,    45,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,
    94,    84,    87,    88,   nil,    89,    91,    90,    92,   nil,
   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,
    77,    63,    58,    86,    98,    99,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,    30,    31,    73,    74,
   nil,   nil,   nil,   nil,   nil,    78,    28,    27,   106,   105,
   107,   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,
   nil,    46,   nil,   nil,   110,   109,   111,   100,    57,   102,
   101,   103,   nil,   104,   112,   113,   nil,    96,    97,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   231,   nil,   nil,   237,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,   414,   nil,   nil,   nil,    45,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,
   nil,    94,    84,    87,    88,   nil,    89,    91,    90,    92,
   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,    75,
    76,    77,    63,    58,    86,    98,    99,    64,    65,   nil,
   nil,   nil,    68,   nil,    66,    67,    69,    30,    31,    73,
    74,   nil,   nil,   nil,   nil,   nil,    78,    28,    27,   106,
   105,   107,   108,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,   nil,    46,   nil,   nil,   110,   109,   111,   100,    57,
   102,   101,   103,   nil,   104,   112,   113,   nil,    96,    97,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,    59,    60,
   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    94,    84,    87,    88,   nil,    89,    91,    90,
    92,   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,
    75,    76,    77,    63,    58,    86,    98,    99,    64,    65,
   nil,   nil,   nil,    68,   nil,    66,    67,    69,    30,    31,
    73,    74,   nil,   nil,   nil,   nil,   nil,    78,    28,    27,
   106,   105,   107,   108,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,   nil,    46,   nil,   nil,   110,   109,   111,   100,
    57,   102,   101,   103,   nil,   104,   112,   113,   nil,    96,
    97,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,    59,
    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    94,    84,    87,    88,   nil,    89,    91,
    90,    92,   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,
   nil,    75,    76,    77,    63,    58,    86,    98,    99,    64,
    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,    30,
    31,    73,    74,   nil,   nil,   nil,   nil,   nil,    78,    28,
    27,   106,   105,   107,   108,   nil,   nil,   238,   nil,   nil,
   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,   109,   111,
   100,    57,   102,   101,   103,   nil,   104,   112,   113,   nil,
    96,    97,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,
    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   236,
   nil,   nil,   nil,   nil,    94,    84,    87,    88,   nil,    89,
    91,    90,    92,   nil,   nil,   nil,   nil,    85,    93,   nil,
   nil,   nil,    75,    76,    77,    63,    58,    86,    98,    99,
    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,
    30,    31,    73,    74,   nil,   nil,   nil,   nil,   nil,    78,
    28,    27,   106,   105,   107,   108,   nil,   nil,   238,   nil,
   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,   109,
   111,   100,    57,   102,   101,   103,   293,   104,   112,   113,
   nil,    96,    97,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,
   nil,    59,    60,   nil,   nil,    61,   nil,   290,   nil,   288,
   nil,    45,   nil,   nil,   294,   nil,   nil,   nil,   nil,   nil,
   236,   nil,   nil,   nil,   nil,    94,   291,    87,    88,   nil,
    89,    91,    90,    92,   nil,   nil,   nil,   nil,    85,    93,
   nil,   nil,   nil,    75,    76,    77,    63,    58,    86,    98,
    99,    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,
    69,    30,    31,    73,    74,   nil,   nil,   nil,   nil,   nil,
    78,    28,    27,   106,   105,   107,   108,   nil,   nil,   238,
   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,
   109,   111,   100,    57,   102,   101,   103,   nil,   104,   112,
   113,   nil,    96,    97,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,
   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   236,   nil,   nil,   nil,   nil,    94,    84,    87,    88,
   nil,    89,    91,    90,    92,   nil,   nil,   nil,   nil,    85,
    93,   nil,   nil,   nil,    75,    76,    77,    63,    58,    86,
    98,    99,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,   nil,
   nil,    78,    28,    27,   106,   105,   107,   108,   nil,   nil,
    19,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,
   110,   109,   111,   100,    57,   102,   101,   103,   nil,   104,
   112,   113,   nil,    96,    97,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,
   237,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    20,   nil,   nil,   nil,   nil,    94,    84,    87,
    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,   nil,
    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,    58,
    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,
   nil,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,
   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   226,   nil,   nil,    75,    76,    77,    63,
    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,   318,   319,    73,    74,   nil,   nil,
   nil,   nil,   nil,    78,   315,   321,   106,   105,   107,   108,
   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,
   nil,   nil,   110,   109,   111,   100,    57,   102,   101,   103,
   nil,   104,   112,   113,   nil,    96,    97,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,
   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,
    84,    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,
   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,
    63,    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,   318,   319,    73,    74,   nil,
   nil,   nil,   nil,   nil,    78,   315,   321,   106,   105,   107,
   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   110,   109,   111,   100,    57,   102,   101,
   103,   nil,   104,   112,   113,   nil,    96,    97,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   231,   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,
    94,    84,    87,    88,   nil,    89,    91,    90,    92,   nil,
   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,
    77,    63,    58,    86,    98,    99,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,   318,   319,    73,    74,
   nil,   nil,   nil,   nil,   nil,    78,   315,   321,   106,   105,
   107,   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,
   nil,    46,   nil,   nil,   110,   109,   111,   100,    57,   102,
   101,   103,   nil,   104,   112,   113,   nil,    96,    97,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   231,   nil,   nil,   237,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,
   nil,    94,    84,    87,    88,   nil,    89,    91,    90,    92,
   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,    75,
    76,    77,    63,    58,    86,    98,    99,    64,    65,   nil,
   nil,   nil,    68,   nil,    66,    67,    69,   318,   319,    73,
    74,   nil,   nil,   nil,   nil,   nil,    78,   315,   321,   106,
   105,   107,   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,
   nil,   nil,    46,   nil,   nil,   110,   109,   111,   100,    57,
   102,   101,   103,   nil,   104,   112,   113,   nil,    96,    97,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,    59,    60,
   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,
   nil,   nil,    94,    84,    87,    88,   nil,    89,    91,    90,
    92,   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,
    75,    76,    77,    63,    58,    86,    98,    99,    64,    65,
   nil,   nil,   nil,    68,   nil,    66,    67,    69,   318,   319,
    73,    74,   nil,   nil,   nil,   nil,   nil,    78,   315,   321,
   106,   105,   107,   108,   nil,   nil,   238,   nil,   nil,   nil,
   nil,   nil,   nil,    46,   nil,   nil,   110,   109,   111,   100,
    57,   102,   101,   103,   nil,   104,   112,   113,   nil,    96,
    97,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,    59,
    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,
   nil,   nil,   nil,    94,    84,    87,    88,   nil,    89,    91,
    90,    92,   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,
   nil,    75,    76,    77,    63,    58,    86,    98,    99,    64,
    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,   318,
   319,    73,    74,   nil,   nil,   nil,   nil,   nil,    78,   315,
   321,   106,   105,   107,   108,   nil,   nil,   238,   nil,   nil,
   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,   109,   111,
   100,    57,   102,   101,   103,   nil,   104,   112,   113,   nil,
    96,    97,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,
    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   236,
   nil,   nil,   nil,   nil,    94,    84,    87,    88,   nil,    89,
    91,    90,    92,   nil,   nil,   nil,   nil,    85,    93,   nil,
   nil,   nil,    75,    76,    77,    63,    58,    86,    98,    99,
    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,
   318,   319,    73,    74,   nil,   nil,   nil,   nil,   nil,    78,
   315,   321,   106,   105,   107,   108,   nil,   nil,   238,   nil,
   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,   109,
   111,   100,    57,   102,   101,   103,   nil,   104,   112,   113,
   nil,    96,    97,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,
   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   236,   nil,   nil,   nil,   nil,    94,    84,    87,    88,   nil,
    89,    91,    90,    92,   nil,   nil,   nil,   nil,    85,    93,
   nil,   nil,   nil,    75,    76,    77,    63,    58,    86,    98,
    99,    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,
    69,   318,   319,    73,    74,   nil,   nil,   nil,   nil,   nil,
    78,   315,   321,   106,   105,   107,   108,   nil,   nil,   238,
   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,
   109,   111,   100,    57,   102,   101,   103,   nil,   104,   112,
   113,   nil,    96,    97,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,
   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   236,   nil,   nil,   nil,   nil,    94,    84,    87,    88,
   nil,    89,    91,    90,    92,   nil,   nil,   nil,   nil,    85,
    93,   nil,   nil,   nil,    75,    76,    77,    63,    58,    86,
    98,    99,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,   318,   319,    73,    74,   nil,   nil,   nil,   nil,
   nil,    78,   315,   321,   106,   105,   107,   108,   nil,   nil,
   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,
   110,   109,   111,   100,    57,   102,   101,   103,   nil,   104,
   112,   113,   nil,    96,    97,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,
   237,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,    84,    87,
    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,   nil,
    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,    58,
    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,   318,   319,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,   315,   321,   106,   105,   107,   108,   nil,
   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,
   nil,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,
   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,
    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,   318,   319,    73,    74,   nil,   nil,
   nil,   nil,   nil,    78,   315,   321,   106,   105,   107,   108,
   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,
   nil,   nil,   110,   109,   111,   100,    57,   102,   101,   103,
   nil,   104,   112,   113,   nil,    96,    97,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,
   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,
    84,    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,
   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,
    63,    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,   318,   319,    73,    74,   nil,
   nil,   nil,   nil,   nil,    78,   315,   321,   106,   105,   107,
   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   110,   109,   111,   100,    57,   102,   101,
   103,   nil,   104,   112,   113,   nil,    96,    97,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   231,   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,
    94,    84,    87,    88,   nil,    89,    91,    90,    92,   nil,
   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,
    77,    63,    58,    86,    98,    99,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,   318,   319,    73,    74,
   nil,   nil,   nil,   nil,   nil,    78,   315,   321,   106,   105,
   107,   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,
   nil,    46,   nil,   nil,   110,   109,   111,   100,    57,   102,
   101,   103,   nil,   104,   112,   113,   nil,    96,    97,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   231,   nil,   nil,   237,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,
   nil,    94,    84,    87,    88,   nil,    89,    91,    90,    92,
   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,    75,
    76,    77,    63,    58,    86,    98,    99,    64,    65,   nil,
   nil,   nil,    68,   nil,    66,    67,    69,   318,   319,    73,
    74,   nil,   nil,   nil,   nil,   nil,    78,   315,   321,   106,
   105,   107,   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,
   nil,   nil,    46,   nil,   nil,   110,   109,   111,   100,    57,
   102,   101,   103,   nil,   104,   112,   113,   nil,    96,    97,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,    59,    60,
   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,
   nil,   nil,    94,    84,    87,    88,   nil,    89,    91,    90,
    92,   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,
    75,    76,    77,    63,    58,    86,    98,    99,    64,    65,
   nil,   nil,   nil,    68,   nil,    66,    67,    69,   318,   319,
    73,    74,   nil,   nil,   nil,   nil,   nil,    78,   315,   321,
   106,   105,   107,   108,   nil,   nil,   238,   nil,   nil,   nil,
   nil,   nil,   nil,    46,   nil,   nil,   110,   109,   111,   100,
    57,   102,   101,   103,   nil,   104,   112,   113,   nil,    96,
    97,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,    59,
    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,
   nil,   nil,   nil,    94,    84,    87,    88,   nil,    89,    91,
    90,    92,   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,
   nil,    75,    76,    77,    63,    58,    86,    98,    99,    64,
    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,   318,
   319,    73,    74,   nil,   nil,   nil,   nil,   nil,    78,   315,
   321,   106,   105,   107,   108,   nil,   nil,   238,   nil,   nil,
   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,   109,   111,
   100,    57,   102,   101,   103,   nil,   104,   112,   113,   nil,
    96,    97,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,
    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   236,
   nil,   nil,   nil,   nil,    94,    84,    87,    88,   nil,    89,
    91,    90,    92,   nil,   nil,   nil,   nil,    85,    93,   nil,
   nil,   nil,    75,    76,    77,    63,    58,    86,    98,    99,
    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,
   318,   319,    73,    74,   nil,   nil,   nil,   nil,   nil,    78,
   315,   321,   106,   105,   107,   108,   nil,   nil,   238,   nil,
   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,   109,
   111,   100,    57,   102,   101,   103,   nil,   104,   112,   113,
   nil,    96,    97,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,
   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   236,   nil,   nil,   nil,   nil,    94,    84,    87,    88,   nil,
    89,    91,    90,    92,   nil,   nil,   nil,   nil,    85,    93,
   nil,   nil,   nil,    75,    76,    77,    63,    58,    86,    98,
    99,    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,
    69,   318,   319,    73,    74,   nil,   nil,   nil,   nil,   nil,
    78,   315,   321,   106,   105,   107,   108,   nil,   nil,   238,
   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,
   109,   111,   100,    57,   102,   101,   103,   nil,   104,   112,
   113,   nil,    96,    97,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,
   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   236,   nil,   nil,   nil,   nil,    94,    84,    87,    88,
   nil,    89,    91,    90,    92,   nil,   nil,   nil,   nil,    85,
    93,   nil,   nil,   nil,    75,    76,    77,    63,    58,    86,
    98,    99,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,   318,   319,    73,    74,   nil,   nil,   nil,   nil,
   nil,    78,   315,   321,   106,   105,   107,   108,   nil,   nil,
   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,
   110,   109,   111,   100,    57,   102,   101,   103,   nil,   104,
   112,   113,   nil,    96,    97,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,
   237,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,    84,    87,
    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,   nil,
    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,    58,
    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,   318,   319,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,   315,   321,   106,   105,   107,   108,   nil,
   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,
   nil,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,
   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,
    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,   318,   319,    73,    74,   nil,   nil,
   nil,   nil,   nil,    78,   315,   321,   106,   105,   107,   108,
   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,
   nil,   nil,   110,   109,   111,   100,    57,   102,   101,   103,
   nil,   104,   112,   113,   nil,    96,    97,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,
   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,
    84,    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,
   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,
    63,    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,   318,   319,    73,    74,   nil,
   nil,   nil,   nil,   nil,    78,   315,   321,   106,   105,   107,
   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   110,   109,   111,   100,    57,   102,   101,
   103,   nil,   104,   112,   113,   nil,    96,    97,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   231,   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,
    94,    84,    87,    88,   nil,    89,    91,    90,    92,   nil,
   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,
    77,    63,    58,    86,    98,    99,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,   318,   319,    73,    74,
   nil,   nil,   nil,   nil,   nil,    78,   315,   321,   106,   105,
   107,   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,
   nil,    46,   nil,   nil,   110,   109,   111,   100,    57,   102,
   101,   103,   nil,   104,   112,   113,   nil,    96,    97,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   231,   nil,   nil,   237,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,
   nil,    94,    84,    87,    88,   nil,    89,    91,    90,    92,
   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,    75,
    76,    77,    63,    58,    86,    98,    99,    64,    65,   nil,
   nil,   nil,    68,   nil,    66,    67,    69,   318,   319,    73,
    74,   nil,   nil,   nil,   nil,   nil,    78,   315,   321,   106,
   105,   107,   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,
   nil,   nil,    46,   nil,   nil,   110,   109,   111,   100,    57,
   102,   101,   103,   nil,   104,   112,   113,   nil,    96,    97,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,    59,    60,
   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,
   nil,   nil,    94,    84,    87,    88,   nil,    89,    91,    90,
    92,   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,
    75,    76,    77,    63,    58,    86,    98,    99,    64,    65,
   nil,   nil,   nil,    68,   nil,    66,    67,    69,    30,    31,
    73,    74,   nil,   nil,   nil,   nil,   nil,    78,    28,    27,
   106,   105,   107,   108,   nil,   nil,   238,   nil,   nil,   nil,
   nil,   nil,   nil,    46,   nil,   nil,   110,   109,   111,   100,
    57,   102,   101,   103,   293,   104,   112,   113,   nil,    96,
    97,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,    59,
    60,   nil,   nil,    61,   nil,   290,   nil,   288,   nil,    45,
   nil,   nil,   294,   nil,   nil,   nil,   nil,   nil,   236,   nil,
   nil,   nil,   nil,    94,   291,    87,    88,   nil,    89,    91,
    90,    92,   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,
   nil,    75,    76,    77,    63,    58,    86,    98,    99,    64,
    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,    30,
    31,    73,    74,   nil,   nil,   nil,   nil,   nil,    78,    28,
    27,   106,   105,   107,   108,   nil,   nil,   238,   nil,   nil,
   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,   109,   111,
   100,    57,   102,   101,   103,   293,   104,   112,   113,   nil,
    96,    97,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,
    59,    60,   nil,   nil,    61,   nil,   290,   nil,   288,   nil,
    45,   nil,   nil,   294,   nil,   nil,   nil,   nil,   nil,   236,
   nil,   nil,   nil,   nil,    94,   291,    87,    88,   nil,    89,
    91,    90,    92,   nil,   nil,   nil,   nil,    85,    93,   nil,
   nil,   nil,    75,    76,    77,    63,    58,    86,    98,    99,
    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,
    30,    31,    73,    74,   nil,   nil,   nil,   nil,   nil,    78,
    28,    27,   106,   105,   107,   108,   nil,   nil,   238,   nil,
   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,   109,
   111,   100,    57,   102,   101,   103,   293,   104,   112,   113,
   nil,    96,    97,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,
   nil,    59,    60,   nil,   nil,    61,   nil,   290,   nil,   288,
   nil,    45,   nil,   nil,   294,   nil,   nil,   nil,   nil,   nil,
   236,   nil,   nil,   nil,   nil,    94,   291,    87,    88,   nil,
    89,    91,    90,    92,   nil,   nil,   nil,   nil,    85,    93,
   226,   nil,   nil,    75,    76,    77,    63,    58,    86,    98,
    99,    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,
    69,   318,   319,    73,    74,   nil,   nil,   nil,   nil,   nil,
    78,   315,   321,   106,   105,   107,   108,   nil,   nil,   238,
   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,
   109,   111,   100,    57,   102,   101,   103,   nil,   104,   112,
   113,   nil,    96,    97,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,
   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   236,   nil,   nil,   nil,   nil,    94,    84,    87,    88,
   nil,    89,    91,    90,    92,   nil,   nil,   nil,   nil,    85,
    93,   nil,   nil,   nil,    75,    76,    77,    63,    58,    86,
    98,    99,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,   318,   319,    73,    74,   nil,   nil,   nil,   nil,
   nil,    78,   315,   321,   106,   105,   107,   108,   nil,   nil,
   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,
   110,   109,   111,   100,    57,   102,   101,   103,   nil,   104,
   112,   113,   nil,    96,    97,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,
   237,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,    84,    87,
    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,   nil,
    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,    58,
    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,   318,   319,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,   315,   321,   106,   105,   107,   108,   nil,
   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,
   nil,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,
   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,
    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,   318,   319,    73,    74,   nil,   nil,
   nil,   nil,   nil,    78,   315,   321,   106,   105,   107,   108,
   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,
   nil,   nil,   110,   109,   111,   100,    57,   102,   101,   103,
   nil,   104,   112,   113,   nil,    96,    97,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,
   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,
    84,    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,
   nil,   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,   nil,
    63,   nil,    86,    98,    99,    75,    76,    77,     9,    58,
   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,    46,   nil,
    10,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    40,   nil,
   nil,    33,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,
    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,   318,   319,    73,    74,   nil,   nil,
   nil,   nil,   nil,    78,   315,   321,   106,   105,   107,   108,
   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,   316,
   nil,   nil,   110,   109,   111,   100,    57,   102,   101,   103,
   nil,   104,   112,   113,   nil,    96,    97,   nil,   nil,   322,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   312,
   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   nil,   683,   nil,   680,   679,   678,   688,   681,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   691,   nil,    94,
    84,    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,
   nil,   nil,    85,    93,   nil,   nil,   nil,   516,   nil,   686,
    63,   nil,    86,    98,    99,    75,    76,    77,   nil,    58,
   699,   698,   nil,    64,    65,   692,   nil,   nil,    68,   nil,
    66,    67,    69,   318,   319,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,   315,   321,   106,   105,   107,   108,   nil,
   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,   316,   nil,
   nil,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,   nil,   nil,   322,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   312,   nil,
   nil,   308,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,
    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,   318,   319,    73,    74,   nil,   nil,
   nil,   nil,   nil,    78,   315,   321,   106,   105,   107,   108,
   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,
   nil,   nil,   110,   109,   111,   100,    57,   102,   101,   103,
   nil,   104,   112,   113,   nil,    96,    97,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,
   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,
    84,    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,
   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,
    63,    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,   318,   319,    73,    74,   nil,
   nil,   nil,   nil,   nil,    78,   315,   321,   106,   105,   107,
   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   110,   109,   111,   100,    57,   102,   101,
   103,   nil,   104,   112,   113,   nil,    96,    97,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   231,   nil,   nil,   237,   533,   nil,    59,    60,   nil,   nil,
    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,
    94,    84,    87,    88,   nil,    89,    91,    90,    92,   nil,
   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,
    77,    63,    58,    86,    98,    99,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,    30,    31,    73,    74,
   nil,   nil,   nil,   nil,   nil,    78,    28,    27,   106,   105,
   107,   108,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,
   nil,    46,   nil,   nil,   110,   109,   111,   100,    57,   102,
   101,   103,   nil,   104,   112,   113,   nil,    96,    97,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   231,   nil,   nil,   237,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,
   nil,    94,    84,    87,    88,   nil,    89,    91,    90,    92,
   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,    75,
    76,    77,    63,    58,    86,    98,    99,    64,    65,   nil,
   nil,   nil,    68,   nil,    66,    67,    69,    30,    31,    73,
    74,   nil,   nil,   nil,   nil,   nil,    78,    28,    27,   106,
   105,   107,   108,   nil,   nil,    19,   nil,   nil,   nil,   nil,
   nil,   nil,    46,   nil,   nil,   110,   109,   111,   100,    57,
   102,   101,   103,   nil,   104,   112,   113,   nil,    96,    97,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,    59,    60,
   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,
   nil,   nil,    94,    84,    87,    88,   nil,    89,    91,    90,
    92,   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,
    75,    76,    77,    63,    58,    86,    98,    99,    64,    65,
   nil,   nil,   nil,    68,   nil,    66,    67,    69,    30,    31,
    73,    74,   nil,   nil,   nil,   nil,   nil,    78,    28,    27,
   106,   105,   107,   108,   nil,   nil,    19,   nil,   nil,   nil,
   nil,   nil,   nil,    46,   nil,   nil,   110,   109,   111,   100,
    57,   102,   101,   103,   nil,   104,   112,   113,   nil,    96,
    97,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,    59,
    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,   nil,   nil,    94,    84,    87,    88,   nil,    89,    91,
    90,    92,   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,
   nil,    75,    76,    77,    63,    58,    86,    98,    99,    64,
    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,   318,
   319,    73,    74,   nil,   nil,   nil,   nil,   nil,    78,   315,
   321,   106,   105,   107,   108,   nil,   nil,   238,   nil,   nil,
   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,   109,   111,
   100,    57,   102,   101,   103,   nil,   104,   112,   113,   nil,
    96,    97,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,
    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   236,
   nil,   nil,   nil,   nil,    94,    84,    87,    88,   nil,    89,
    91,    90,    92,   nil,   nil,   nil,   nil,    85,    93,   nil,
   nil,   nil,    75,    76,    77,    63,    58,    86,    98,    99,
    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,
    30,    31,    73,    74,   nil,   nil,   nil,   nil,   nil,    78,
    28,    27,   106,   105,   107,   108,   nil,   nil,   238,   nil,
   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,   109,
   111,   100,    57,   102,   101,   103,   293,   104,   112,   113,
   nil,    96,    97,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,
   nil,    59,    60,   nil,   nil,    61,   nil,   290,   nil,   288,
   nil,    45,   nil,   nil,   294,   nil,   nil,   nil,   nil,   nil,
   236,   nil,   nil,   nil,   nil,    94,   291,    87,    88,   nil,
    89,    91,    90,    92,   nil,   nil,   nil,   nil,    85,    93,
   nil,   nil,   nil,    75,    76,    77,    63,    58,    86,    98,
    99,    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,
    69,   318,   319,    73,    74,   nil,   nil,   nil,   nil,   nil,
    78,   315,   321,   106,   105,   107,   108,   nil,   nil,   238,
   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,
   109,   111,   100,    57,   102,   101,   103,   nil,   104,   112,
   113,   nil,    96,    97,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,
   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   236,   nil,   nil,   nil,   nil,    94,    84,    87,    88,
   nil,    89,    91,    90,    92,   nil,   nil,   nil,   nil,    85,
    93,   nil,   nil,   nil,    75,    76,    77,    63,    58,    86,
    98,    99,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,   318,   319,    73,    74,   nil,   nil,   nil,   nil,
   nil,    78,   315,   321,   106,   105,   107,   108,   nil,   nil,
   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,
   110,   109,   111,   100,    57,   102,   101,   103,   nil,   104,
   112,   113,   nil,    96,    97,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,
   237,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,    84,    87,
    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,   nil,
    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,    58,
    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,   318,   319,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,   315,   321,   106,   105,   107,   108,   nil,
   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,
   nil,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,
   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,
    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,   318,   319,    73,    74,   nil,   nil,
   nil,   nil,   nil,    78,   315,   321,   106,   105,   107,   108,
   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,
   nil,   nil,   110,   109,   111,   100,    57,   102,   101,   103,
   293,   104,   112,   113,   nil,    96,    97,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,
   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   641,   nil,   288,   nil,    45,   nil,   nil,   294,   nil,
   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,
   291,    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,
   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,
    63,    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,   318,   319,    73,    74,   nil,
   nil,   nil,   nil,   nil,    78,   315,   321,   106,   105,   107,
   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   110,   109,   111,   100,    57,   102,   101,
   103,   293,   104,   112,   113,   nil,    96,    97,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   231,   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,   nil,   nil,   288,   nil,    45,   nil,   nil,   294,
   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,
    94,   291,    87,    88,   nil,    89,    91,    90,    92,   nil,
   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,
    77,    63,    58,    86,    98,    99,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,   318,   319,    73,    74,
   nil,   nil,   nil,   nil,   nil,    78,   315,   321,   106,   105,
   107,   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,
   nil,    46,   nil,   nil,   110,   109,   111,   100,    57,   102,
   101,   103,   nil,   104,   112,   113,   nil,    96,    97,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   231,   nil,   nil,   237,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,
   nil,    94,    84,    87,    88,   nil,    89,    91,    90,    92,
   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,   nil,
   nil,   nil,    63,   nil,    86,    98,    99,    75,    76,    77,
     9,    58,   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,    30,    31,    73,    74,   nil,
   nil,   nil,   nil,   nil,    78,    28,    27,   106,   105,   107,
   108,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,     8,
    46,   301,    10,   110,   109,   111,   100,    57,   102,   101,
   103,   nil,   104,   112,   113,   nil,    96,    97,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    40,   nil,   nil,    33,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,    35,   nil,   nil,   nil,    45,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,
    94,    84,    87,    88,   nil,    89,    91,    90,    92,   nil,
   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,   nil,   nil,
   398,    63,   nil,    86,    98,    99,    75,    76,    77,   nil,
    58,   nil,   nil,   nil,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,   318,   319,    73,    74,   nil,   nil,
   nil,   nil,   nil,    78,   315,   321,   106,   105,   107,   108,
   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,   316,
   nil,   nil,   110,   109,   111,   100,    57,   102,   101,   103,
   nil,   104,   112,   113,   nil,    96,    97,   nil,   nil,   322,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   312,
   nil,   nil,   308,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    94,
    84,    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,
   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,
    63,    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,    30,    31,    73,    74,   nil,
   nil,   nil,   nil,   nil,    78,    28,    27,   106,   105,   107,
   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   110,   109,   111,   100,    57,   102,   101,
   103,   293,   104,   112,   113,   nil,    96,    97,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   231,   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,   290,   nil,   288,   nil,    45,   nil,   nil,   294,
   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,
    94,   291,    87,    88,   nil,    89,    91,    90,    92,   nil,
   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,
    77,    63,    58,    86,    98,    99,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,   318,   319,    73,    74,
   nil,   nil,   nil,   nil,   nil,    78,   315,   321,   106,   105,
   107,   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,
   nil,   316,   nil,   nil,   110,   109,   111,   100,    57,   102,
   101,   103,   nil,   104,   112,   113,   nil,    96,    97,   nil,
   nil,   322,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   312,   nil,   nil,   308,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    94,    84,    87,    88,   nil,    89,    91,    90,    92,
   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,    75,
    76,    77,    63,    58,    86,    98,    99,    64,    65,   nil,
   nil,   nil,    68,   nil,    66,    67,    69,   318,   319,    73,
    74,   nil,   nil,   nil,   nil,   nil,    78,   315,   321,   106,
   105,   107,   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,
   nil,   nil,    46,   nil,   nil,   110,   109,   111,   100,    57,
   102,   101,   103,   nil,   104,   112,   113,   nil,    96,    97,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,    59,    60,
   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,
   nil,   nil,    94,    84,    87,    88,   nil,    89,    91,    90,
    92,   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,
    75,    76,    77,    63,    58,    86,    98,    99,    64,    65,
   nil,   nil,   nil,    68,   nil,    66,    67,    69,   318,   319,
    73,    74,   nil,   nil,   nil,   nil,   nil,    78,   315,   321,
   106,   105,   107,   108,   nil,   nil,   238,   nil,   nil,   nil,
   nil,   nil,   nil,    46,   nil,   nil,   110,   109,   111,   100,
    57,   102,   101,   103,   nil,   104,   112,   113,   nil,    96,
    97,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,    59,
    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,
   nil,   nil,   nil,    94,    84,    87,    88,   nil,    89,    91,
    90,    92,   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,
   nil,    75,    76,    77,    63,    58,    86,    98,    99,    64,
    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,    30,
    31,    73,    74,   nil,   nil,   nil,   nil,   nil,    78,    28,
    27,   106,   105,   107,   108,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,   109,   111,
   100,    57,   102,   101,   103,   nil,   104,   112,   113,   nil,
    96,    97,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,
    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,
   nil,   nil,   nil,   nil,    94,    84,    87,    88,   nil,    89,
    91,    90,    92,   nil,   nil,   nil,   nil,    85,    93,   nil,
   nil,   nil,    75,    76,    77,    63,    58,    86,    98,    99,
    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,
   318,   319,    73,    74,   nil,   nil,   nil,   nil,   nil,    78,
   315,   321,   106,   105,   107,   108,   nil,   nil,   238,   nil,
   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,   109,
   111,   100,    57,   102,   101,   103,   293,   104,   112,   113,
   nil,    96,    97,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,
   nil,    59,    60,   nil,   nil,    61,   nil,   641,   nil,   nil,
   nil,    45,   nil,   nil,   294,   nil,   nil,   nil,   nil,   nil,
   236,   nil,   nil,   nil,   nil,    94,   291,    87,    88,   nil,
    89,    91,    90,    92,   nil,   nil,   nil,   nil,    85,    93,
   nil,   nil,   nil,    75,    76,    77,    63,    58,    86,    98,
    99,    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,
    69,   318,   319,    73,    74,   nil,   nil,   nil,   nil,   nil,
    78,   315,   321,   106,   105,   107,   108,   nil,   nil,   238,
   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,
   109,   111,   100,    57,   102,   101,   103,   293,   104,   112,
   113,   nil,    96,    97,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,
   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   294,   nil,   nil,   nil,   nil,
   nil,   236,   nil,   nil,   nil,   nil,    94,   291,    87,    88,
   nil,    89,    91,    90,    92,   nil,   nil,   nil,   nil,    85,
    93,   nil,   nil,   nil,    75,    76,    77,    63,    58,    86,
    98,    99,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,   318,   319,    73,    74,   nil,   nil,   nil,   nil,
   nil,    78,   315,   321,   106,   105,   107,   108,   nil,   nil,
   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,
   110,   109,   111,   100,    57,   102,   101,   103,   nil,   104,
   112,   113,   nil,    96,    97,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,
   237,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   290,
   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,    84,    87,
    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,   nil,
    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,    58,
    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,
   nil,   110,   109,   111,   100,    57,   102,   101,   103,   293,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,
   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   290,   nil,   288,   nil,    45,   nil,   nil,   294,   nil,   nil,
   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,   291,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,
    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,    30,    31,    73,    74,   nil,   nil,
   nil,   nil,   nil,    78,    28,    27,   106,   105,   107,   108,
   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,
   nil,   nil,   110,   109,   111,   100,    57,   102,   101,   103,
   293,   104,   112,   113,   nil,    96,    97,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,
   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   290,   nil,   288,   nil,    45,   nil,   nil,   294,   nil,
   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,
   291,    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,
   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,
    63,    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,   318,   319,    73,    74,   nil,
   nil,   nil,   nil,   nil,    78,   315,   321,   106,   105,   107,
   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   110,   109,   111,   100,    57,   102,   101,
   103,   nil,   104,   112,   113,   nil,    96,    97,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   231,   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,   745,   nil,   nil,   nil,    45,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,
    94,    84,    87,    88,   nil,    89,    91,    90,    92,   nil,
   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,
    77,    63,    58,    86,    98,    99,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,    30,    31,    73,    74,
   nil,   nil,   nil,   nil,   nil,    78,    28,    27,   106,   105,
   107,   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,
   nil,    46,   nil,   nil,   110,   109,   111,   100,    57,   102,
   101,   103,   nil,   104,   112,   113,   nil,    96,    97,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   231,   nil,   nil,   237,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,
   nil,    94,    84,    87,    88,   nil,    89,    91,    90,    92,
   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,    75,
    76,    77,    63,    58,    86,    98,    99,    64,    65,   nil,
   nil,   nil,    68,   nil,    66,    67,    69,    30,    31,    73,
    74,   nil,   nil,   nil,   nil,   nil,    78,    28,    27,   106,
   105,   107,   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,
   nil,   nil,    46,   nil,   nil,   110,   109,   111,   100,    57,
   102,   101,   103,   293,   104,   112,   113,   nil,    96,    97,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,    59,    60,
   nil,   nil,    61,   nil,   290,   nil,   288,   nil,    45,   nil,
   nil,   294,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,
   nil,   nil,    94,   291,    87,    88,   nil,    89,    91,    90,
    92,   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,
   nil,   nil,   nil,    63,   nil,    86,    98,    99,    75,    76,
    77,     9,    58,   nil,   nil,   nil,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,    30,    31,    73,    74,
   nil,   nil,   nil,   nil,   nil,    78,    28,    27,   106,   105,
   107,   108,   nil,   nil,    19,   nil,   nil,   nil,   nil,   nil,
     8,    46,   nil,    10,   110,   109,   111,   100,    57,   102,
   101,   103,   nil,   104,   112,   113,   nil,    96,    97,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    40,   nil,   nil,    33,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,    35,   nil,   nil,   nil,    45,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,
   nil,    94,    84,    87,    88,   nil,    89,    91,    90,    92,
   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,    75,
    76,    77,    63,    58,    86,    98,    99,    64,    65,   nil,
   nil,   nil,    68,   nil,    66,    67,    69,   318,   319,    73,
    74,   nil,   nil,   nil,   nil,   nil,    78,   315,   321,   106,
   105,   107,   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,
   nil,   nil,    46,   nil,   nil,   110,   109,   111,   100,    57,
   102,   101,   103,   nil,   104,   112,   113,   nil,    96,    97,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,    59,    60,
   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,
   nil,   nil,    94,    84,    87,    88,   nil,    89,    91,    90,
    92,   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,
    75,    76,    77,    63,    58,    86,    98,    99,    64,    65,
   nil,   nil,   nil,    68,   nil,    66,    67,    69,   318,   319,
    73,    74,   nil,   nil,   nil,   nil,   nil,    78,   315,   321,
   106,   105,   107,   108,   nil,   nil,   238,   nil,   nil,   nil,
   nil,   nil,   nil,    46,   nil,   nil,   110,   109,   111,   100,
    57,   102,   101,   103,   293,   104,   112,   113,   nil,    96,
    97,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,    59,
    60,   nil,   nil,    61,   nil,   641,   nil,   288,   nil,    45,
   nil,   nil,   294,   nil,   nil,   nil,   nil,   nil,   236,   nil,
   nil,   nil,   nil,    94,   291,    87,    88,   nil,    89,    91,
    90,    92,   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,
   nil,    75,    76,    77,    63,    58,    86,    98,    99,    64,
    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,   318,
   319,    73,    74,   nil,   nil,   nil,   nil,   nil,    78,   315,
   321,   106,   105,   107,   108,   nil,   nil,   238,   nil,   nil,
   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,   109,   111,
   100,    57,   102,   101,   103,   293,   104,   112,   113,   nil,
    96,    97,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,
    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   288,   nil,
    45,   nil,   nil,   294,   nil,   nil,   nil,   nil,   nil,   236,
   nil,   nil,   nil,   nil,    94,   291,    87,    88,   nil,    89,
    91,    90,    92,   nil,   nil,   nil,   nil,    85,    93,   nil,
   nil,   nil,    75,    76,    77,    63,    58,    86,    98,    99,
    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,
    30,    31,    73,    74,   nil,   nil,   nil,   nil,   nil,    78,
    28,    27,   106,   105,   107,   108,   nil,   nil,   238,   nil,
   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,   109,
   111,   100,    57,   102,   101,   103,   nil,   104,   112,   113,
   nil,    96,    97,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,
   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   236,   nil,   nil,   nil,   nil,    94,    84,    87,    88,   nil,
    89,    91,    90,    92,   nil,   nil,   nil,   nil,    85,    93,
   nil,   nil,   nil,    75,    76,    77,    63,    58,    86,    98,
    99,    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,
    69,    30,    31,    73,    74,   nil,   nil,   nil,   nil,   nil,
    78,    28,    27,   106,   105,   107,   108,   nil,   nil,   238,
   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,
   109,   111,   100,    57,   102,   101,   103,   nil,   104,   112,
   113,   nil,    96,    97,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,
   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   236,   nil,   nil,   nil,   nil,    94,    84,    87,    88,
   nil,    89,    91,    90,    92,   nil,   nil,   nil,   nil,    85,
    93,   nil,   nil,   nil,    75,    76,    77,    63,    58,    86,
    98,    99,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,   nil,
   nil,    78,    28,    27,   106,   105,   107,   108,   nil,   nil,
   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,
   110,   109,   111,   100,    57,   102,   101,   103,   nil,   104,
   112,   113,   nil,    96,    97,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,
   237,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,    84,    87,
    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,   nil,
    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,    58,
    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,    28,    27,   106,   105,   107,   108,   nil,
   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,
   nil,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,
   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,
    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,   318,   319,    73,    74,   nil,   nil,
   nil,   nil,   nil,    78,   315,   321,   106,   105,   107,   108,
   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,
   nil,   nil,   110,   109,   111,   100,    57,   102,   101,   103,
   nil,   104,   112,   113,   nil,    96,    97,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,
   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,
    84,    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,
   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,
    63,    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,   318,   319,    73,    74,   nil,
   nil,   nil,   nil,   nil,    78,   315,   321,   106,   105,   107,
   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   110,   109,   111,   100,    57,   102,   101,
   103,   nil,   104,   112,   113,   nil,    96,    97,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   231,   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,
    94,    84,    87,    88,   nil,    89,    91,    90,    92,   nil,
   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,
    77,    63,    58,    86,    98,    99,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,   318,   319,    73,    74,
   nil,   nil,   nil,   nil,   nil,    78,   315,   321,   106,   105,
   107,   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,
   nil,   316,   nil,   nil,   110,   109,   111,   100,    57,   102,
   101,   103,   nil,   104,   112,   113,   nil,    96,    97,   nil,
   nil,   322,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   312,   nil,   nil,   308,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    94,    84,    87,    88,   nil,    89,    91,    90,    92,
   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,    75,
    76,    77,    63,    58,    86,    98,    99,    64,    65,   nil,
   nil,   nil,    68,   nil,    66,    67,    69,   318,   319,    73,
    74,   nil,   nil,   nil,   nil,   nil,    78,   315,   321,   106,
   105,   107,   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,
   nil,   nil,   316,   nil,   nil,   110,   109,   111,   100,    57,
   102,   101,   103,   nil,   104,   112,   113,   nil,    96,    97,
   nil,   nil,   322,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   312,   nil,   nil,   308,   nil,   nil,    59,    60,
   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    94,    84,    87,    88,   nil,    89,    91,    90,
    92,   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,
    75,    76,    77,    63,    58,    86,    98,    99,    64,    65,
   nil,   nil,   nil,    68,   nil,    66,    67,    69,   318,   319,
    73,    74,   nil,   nil,   nil,   nil,   nil,    78,   315,   321,
   106,   105,   107,   108,   nil,   nil,   238,   nil,   nil,   nil,
   nil,   nil,   nil,    46,   nil,   nil,   110,   109,   111,   100,
    57,   102,   101,   103,   nil,   104,   112,   113,   nil,    96,
    97,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,    59,
    60,   nil,   nil,    61,   nil,   414,   nil,   nil,   nil,    45,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,
   nil,   nil,   nil,    94,    84,    87,    88,   nil,    89,    91,
    90,    92,   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,
   nil,    75,    76,    77,    63,    58,    86,    98,    99,    64,
    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,   318,
   319,    73,    74,   nil,   nil,   nil,   nil,   nil,    78,   315,
   321,   106,   105,   107,   108,   nil,   nil,   238,   nil,   nil,
   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,   109,   111,
   100,    57,   102,   101,   103,   nil,   104,   112,   113,   nil,
    96,    97,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,
    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   236,
   nil,   nil,   nil,   nil,    94,    84,    87,    88,   nil,    89,
    91,    90,    92,   nil,   nil,   nil,   nil,    85,    93,   nil,
   nil,   nil,    75,    76,    77,    63,    58,    86,    98,    99,
    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,
    30,    31,    73,    74,   nil,   nil,   nil,   nil,   nil,    78,
    28,    27,   106,   105,   107,   108,   nil,   nil,    19,   nil,
   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,   109,
   111,   100,    57,   102,   101,   103,   nil,   104,   112,   113,
   nil,    96,    97,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,
   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,
   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    20,   nil,   nil,   nil,   nil,    94,    84,    87,    88,   nil,
    89,    91,    90,    92,   nil,   nil,   nil,   nil,    85,    93,
   nil,   nil,   nil,    75,    76,    77,    63,    58,    86,    98,
    99,    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,
    69,   318,   319,    73,    74,   nil,   nil,   nil,   nil,   nil,
    78,   315,   321,   106,   105,   107,   108,   nil,   nil,   238,
   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,
   109,   111,   100,    57,   102,   101,   103,   nil,   104,   112,
   113,   nil,    96,    97,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,
   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,
   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   236,   nil,   nil,   nil,   nil,    94,    84,    87,    88,
   nil,    89,    91,    90,    92,   nil,   nil,   nil,   nil,    85,
    93,   nil,   nil,   nil,    75,    76,    77,    63,    58,    86,
    98,    99,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,    30,    31,    73,    74,   nil,   nil,   nil,   nil,
   nil,    78,    28,    27,   106,   105,   107,   108,   nil,   nil,
   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,
   110,   109,   111,   100,    57,   102,   101,   103,   nil,   104,
   112,   113,   nil,    96,    97,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,
   237,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,    84,    87,
    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,   nil,
    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,    58,
    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,   318,   319,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,   315,   321,   106,   105,   107,   108,   nil,
   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,
   nil,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,    42,    43,    41,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,
   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,
    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,   318,   319,    73,    74,   nil,   nil,
   nil,   nil,   nil,    78,   315,   321,   106,   105,   107,   108,
   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,
   nil,   nil,   110,   109,   111,   100,    57,   102,   101,   103,
   nil,   104,   112,   113,   nil,    96,    97,    42,    43,    41,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,
   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,
    84,    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,
   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,
    63,    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,   318,   319,    73,    74,   nil,
   nil,   nil,   nil,   nil,    78,   315,   321,   106,   105,   107,
   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,
    46,   nil,   nil,   110,   109,   111,   100,    57,   102,   101,
   103,   nil,   104,   112,   113,   nil,    96,    97,    42,    43,
    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   231,   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,
    94,    84,    87,    88,   nil,    89,    91,    90,    92,   nil,
   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,
    77,    63,    58,    86,    98,    99,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,   318,   319,    73,    74,
   nil,   nil,   nil,   nil,   nil,    78,   315,   321,   106,   105,
   107,   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,
   nil,    46,   nil,   nil,   110,   109,   111,   100,    57,   102,
   101,   103,   nil,   104,   112,   113,   nil,    96,    97,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   231,   nil,   nil,   237,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,
   nil,    94,    84,    87,    88,   nil,    89,    91,    90,    92,
   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,    75,
    76,    77,    63,    58,    86,    98,    99,    64,    65,   nil,
   nil,   nil,    68,   nil,    66,    67,    69,   318,   319,    73,
    74,   nil,   nil,   nil,   nil,   nil,    78,   315,   321,   106,
   105,   107,   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,
   nil,   nil,    46,   nil,   nil,   110,   109,   111,   100,    57,
   102,   101,   103,   nil,   104,   112,   113,   nil,    96,    97,
    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,    59,    60,
   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,
   nil,   nil,    94,    84,    87,    88,   nil,    89,    91,    90,
    92,   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,
    75,    76,    77,    63,    58,    86,    98,    99,    64,    65,
   nil,   nil,   nil,    68,   nil,    66,    67,    69,   318,   319,
    73,    74,   nil,   nil,   nil,   nil,   nil,    78,   315,   321,
   106,   105,   107,   108,   nil,   nil,   238,   nil,   nil,   nil,
   nil,   nil,   nil,    46,   nil,   nil,   110,   109,   111,   100,
    57,   102,   101,   103,   nil,   104,   112,   113,   nil,    96,
    97,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,    59,
    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   236,   nil,
   nil,   nil,   nil,    94,    84,    87,    88,   nil,    89,    91,
    90,    92,   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,
   nil,    75,    76,    77,    63,    58,    86,    98,    99,    64,
    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,    30,
    31,    73,    74,   nil,   nil,   nil,   nil,   nil,    78,    28,
    27,   106,   105,   107,   108,   nil,   nil,    19,   nil,   nil,
   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,   109,   111,
   100,    57,   102,   101,   103,   nil,   104,   112,   113,   nil,
    96,    97,    42,    43,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,   nil,
    59,    60,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,
   nil,   nil,   nil,   nil,    94,    84,    87,    88,   nil,    89,
    91,    90,    92,   nil,   nil,   nil,   nil,    85,    93,   nil,
   nil,   nil,    75,    76,    77,    63,    58,    86,    98,    99,
    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,    69,
   318,   319,    73,    74,   nil,   nil,   nil,   nil,   nil,    78,
   315,   321,   106,   105,   107,   108,   nil,   nil,   238,   nil,
   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,   109,
   111,   100,    57,   102,   101,   103,   nil,   104,   112,   113,
   nil,    96,    97,    42,    43,    41,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,   nil,
   nil,    59,    60,   nil,   nil,    61,   nil,   641,   nil,   nil,
   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   236,   nil,   nil,   nil,   nil,    94,    84,    87,    88,   nil,
    89,    91,    90,    92,   nil,   nil,   nil,   nil,    85,    93,
   nil,   nil,   nil,    75,    76,    77,    63,    58,    86,    98,
    99,    64,    65,   nil,   nil,   nil,    68,   nil,    66,    67,
    69,   318,   319,    73,    74,   nil,   nil,   nil,   nil,   nil,
    78,   315,   321,   106,   105,   107,   108,   nil,   nil,   238,
   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,   110,
   109,   111,   100,    57,   102,   101,   103,   293,   104,   112,
   113,   nil,    96,    97,    42,    43,    41,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,   237,
   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,   nil,
   288,   nil,    45,   nil,   nil,   294,   nil,   nil,   nil,   nil,
   nil,   236,   nil,   nil,   nil,   nil,    94,   291,    87,    88,
   nil,    89,    91,    90,    92,   nil,   nil,   nil,   nil,    85,
    93,   nil,   nil,   nil,    75,    76,    77,    63,    58,    86,
    98,    99,    64,    65,   nil,   nil,   nil,    68,   nil,    66,
    67,    69,   318,   319,    73,    74,   nil,   nil,   nil,   nil,
   nil,    78,   315,   321,   106,   105,   107,   108,   nil,   nil,
   238,   nil,   nil,   nil,   nil,   nil,   nil,    46,   nil,   nil,
   110,   109,   111,   100,    57,   102,   101,   103,   nil,   104,
   112,   113,   nil,    96,    97,    42,    43,    41,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   231,   nil,   nil,
   237,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,   nil,
   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   236,   nil,   nil,   nil,   nil,    94,    84,    87,
    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,   nil,
    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,    58,
    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,   nil,
    66,    67,    69,   318,   319,    73,    74,   nil,   nil,   nil,
   nil,   nil,    78,   315,   321,   106,   105,   107,   108,   nil,
   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,   316,   nil,
   nil,   110,   109,   111,   100,    57,   102,   101,   103,   nil,
   104,   112,   113,   nil,    96,    97,   nil,   nil,   322,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   922,   nil,
   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    94,    84,
    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,   nil,
   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,    63,
    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,    68,
   nil,    66,    67,    69,   318,   319,    73,    74,   nil,   nil,
   nil,   nil,   nil,    78,   315,   321,   106,   105,   107,   108,
   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,   316,
   nil,   nil,   110,   109,   111,   100,    57,   102,   101,   103,
   nil,   104,   112,   113,   nil,    96,    97,   nil,   nil,   322,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   928,
   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,    61,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    94,
    84,    87,    88,   nil,    89,    91,    90,    92,   nil,   nil,
   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,    77,
    63,    58,    86,    98,    99,    64,    65,   nil,   nil,   nil,
    68,   nil,    66,    67,    69,   318,   319,    73,    74,   nil,
   nil,   nil,   nil,   nil,    78,   315,   321,   106,   105,   107,
   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,   nil,
   316,   nil,   nil,   110,   109,   111,   100,    57,   102,   101,
   103,   nil,   104,   112,   113,   nil,    96,    97,   nil,   nil,
   322,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   922,   nil,   nil,   237,   nil,   nil,    59,    60,   nil,   nil,
    61,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    94,    84,    87,    88,   nil,    89,    91,    90,    92,   nil,
   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,    75,    76,
    77,    63,    58,    86,    98,    99,    64,    65,   nil,   nil,
   nil,    68,   nil,    66,    67,    69,    30,    31,    73,    74,
   nil,   nil,   nil,   nil,   nil,    78,    28,    27,   106,   105,
   107,   108,   nil,   nil,   238,   nil,   nil,   nil,   nil,   nil,
   nil,    46,   nil,   nil,   110,   109,   111,   100,    57,   102,
   101,   103,   293,   104,   112,   113,   nil,    96,    97,    42,
    43,    41,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   231,   nil,   nil,   237,   nil,   nil,    59,    60,   nil,
   nil,    61,   nil,   290,   nil,   288,   nil,    45,   nil,   nil,
   294,   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,
   nil,    94,   291,    87,    88,   nil,    89,    91,    90,    92,
   nil,   nil,   nil,   nil,    85,    93,   nil,   nil,   nil,   nil,
   nil,   nil,    63,   nil,    86,    98,    99,   179,   190,   180,
   203,   176,   196,   186,   185,   206,   207,   201,   184,   183,
   178,   204,   208,   209,   188,   177,   191,   195,   197,   189,
   182,   nil,   nil,   nil,   198,   205,   200,   199,   192,   202,
   187,   175,   194,   193,   nil,   nil,   nil,   nil,   nil,   174,
   181,   172,   173,   169,   170,   171,   130,   132,   129,   nil,
   131,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   163,   164,
   nil,   160,   142,   143,   144,   151,   148,   150,   nil,   nil,
   145,   146,   nil,   nil,   nil,   165,   166,   152,   153,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   157,   156,   nil,   141,   162,   159,   158,   167,
   154,   155,   149,   147,   139,   161,   140,   nil,   nil,   168,
    94,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    93,   179,   190,   180,   203,   176,
   196,   186,   185,   206,   207,   201,   184,   183,   178,   204,
   208,   209,   188,   177,   191,   195,   197,   189,   182,   nil,
   nil,   nil,   198,   205,   200,   199,   192,   202,   187,   175,
   194,   193,   nil,   nil,   nil,   nil,   nil,   174,   181,   172,
   173,   169,   170,   171,   130,   132,   nil,   nil,   131,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   163,   164,   nil,   160,
   142,   143,   144,   151,   148,   150,   nil,   nil,   145,   146,
   nil,   nil,   nil,   165,   166,   152,   153,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   157,   156,   nil,   141,   162,   159,   158,   167,   154,   155,
   149,   147,   139,   161,   140,   nil,   nil,   168,    94,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    93,   179,   190,   180,   203,   176,   196,   186,
   185,   206,   207,   201,   184,   183,   178,   204,   208,   209,
   188,   177,   191,   195,   197,   189,   182,   nil,   nil,   nil,
   198,   205,   200,   199,   192,   202,   187,   175,   194,   193,
   nil,   nil,   nil,   nil,   nil,   174,   181,   172,   173,   169,
   170,   171,   130,   132,   nil,   nil,   131,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   163,   164,   nil,   160,   142,   143,
   144,   151,   148,   150,   nil,   nil,   145,   146,   nil,   nil,
   nil,   165,   166,   152,   153,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   157,   156,
   nil,   141,   162,   159,   158,   167,   154,   155,   149,   147,
   139,   161,   140,   nil,   nil,   168,    94,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    93,   179,   190,   180,   203,   176,   196,   186,   185,   206,
   207,   201,   184,   183,   178,   204,   208,   209,   188,   177,
   191,   195,   197,   189,   182,   nil,   nil,   nil,   198,   205,
   200,   199,   192,   202,   187,   175,   194,   193,   nil,   nil,
   nil,   nil,   nil,   174,   181,   172,   173,   169,   170,   171,
   130,   132,   nil,   nil,   131,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   163,   164,   nil,   160,   142,   143,   144,   151,
   148,   150,   nil,   nil,   145,   146,   nil,   nil,   nil,   165,
   166,   152,   153,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   157,   156,   nil,   141,
   162,   159,   158,   167,   154,   155,   149,   147,   139,   161,
   140,   nil,   nil,   168,    94,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    93,   179,
   190,   180,   203,   176,   196,   186,   185,   206,   207,   201,
   184,   183,   178,   204,   208,   209,   188,   177,   191,   195,
   197,   189,   182,   nil,   nil,   nil,   198,   205,   200,   381,
   380,   382,   379,   175,   194,   193,   nil,   nil,   nil,   nil,
   nil,   174,   181,   172,   173,   376,   377,   378,   374,   132,
   102,   101,   375,   nil,   104,   nil,   nil,   nil,   nil,   nil,
   163,   164,   nil,   160,   142,   143,   144,   151,   148,   150,
   nil,   nil,   145,   146,   nil,   nil,   nil,   165,   166,   152,
   153,   nil,   nil,   nil,   nil,   nil,   386,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   157,   156,   nil,   141,   162,   159,
   158,   167,   154,   155,   149,   147,   139,   161,   140,   nil,
   nil,   168,   179,   190,   180,   203,   176,   196,   186,   185,
   206,   207,   201,   184,   183,   178,   204,   208,   209,   188,
   177,   191,   195,   197,   189,   182,   nil,   nil,   nil,   198,
   205,   200,   199,   192,   202,   187,   175,   194,   193,   nil,
   nil,   nil,   nil,   nil,   174,   181,   172,   173,   169,   170,
   171,   130,   132,   nil,   nil,   131,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   163,   164,   nil,   160,   142,   143,   144,
   151,   148,   150,   nil,   nil,   145,   146,   nil,   nil,   nil,
   165,   166,   152,   153,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   157,   156,   nil,
   141,   162,   159,   158,   167,   154,   155,   149,   147,   139,
   161,   140,   436,   440,   168,   nil,   437,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   163,   164,   nil,   160,   142,   143,
   144,   151,   148,   150,   nil,   nil,   145,   146,   nil,   nil,
   nil,   165,   166,   152,   153,   nil,   nil,   nil,   nil,   nil,
   272,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   157,   156,
   nil,   141,   162,   159,   158,   167,   154,   155,   149,   147,
   139,   161,   140,   443,   447,   168,   nil,   442,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   163,   164,   nil,   160,   142,
   143,   144,   151,   148,   150,   nil,   nil,   145,   146,   nil,
   nil,   nil,   165,   166,   152,   153,   nil,   nil,   nil,   nil,
   nil,   272,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   157,
   156,   nil,   141,   162,   159,   158,   167,   154,   155,   149,
   147,   139,   161,   140,   487,   440,   168,   nil,   488,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   163,   164,   nil,   160,
   142,   143,   144,   151,   148,   150,   nil,   nil,   145,   146,
   nil,   nil,   nil,   165,   166,   152,   153,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   157,   156,   nil,   141,   162,   159,   158,   167,   154,   155,
   149,   147,   139,   161,   140,   622,   440,   168,   nil,   623,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   163,   164,   nil,
   160,   142,   143,   144,   151,   148,   150,   nil,   nil,   145,
   146,   nil,   nil,   nil,   165,   166,   152,   153,   nil,   nil,
   nil,   nil,   nil,   272,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   157,   156,   nil,   141,   162,   159,   158,   167,   154,
   155,   149,   147,   139,   161,   140,   624,   447,   168,   nil,
   625,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   163,   164,
   nil,   160,   142,   143,   144,   151,   148,   150,   nil,   nil,
   145,   146,   nil,   nil,   nil,   165,   166,   152,   153,   nil,
   nil,   nil,   nil,   nil,   272,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   157,   156,   nil,   141,   162,   159,   158,   167,
   154,   155,   149,   147,   139,   161,   140,   651,   440,   168,
   nil,   652,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   163,
   164,   nil,   160,   142,   143,   144,   151,   148,   150,   nil,
   nil,   145,   146,   nil,   nil,   nil,   165,   166,   152,   153,
   nil,   nil,   nil,   nil,   nil,   272,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   157,   156,   nil,   141,   162,   159,   158,
   167,   154,   155,   149,   147,   139,   161,   140,   654,   447,
   168,   nil,   655,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   163,   164,   nil,   160,   142,   143,   144,   151,   148,   150,
   nil,   nil,   145,   146,   nil,   nil,   nil,   165,   166,   152,
   153,   nil,   nil,   nil,   nil,   nil,   272,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   157,   156,   nil,   141,   162,   159,
   158,   167,   154,   155,   149,   147,   139,   161,   140,   622,
   440,   168,   nil,   623,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   163,   164,   nil,   160,   142,   143,   144,   151,   148,
   150,   nil,   nil,   145,   146,   nil,   nil,   nil,   165,   166,
   152,   153,   nil,   nil,   nil,   nil,   nil,   272,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   157,   156,   nil,   141,   162,
   159,   158,   167,   154,   155,   149,   147,   139,   161,   140,
   624,   447,   168,   nil,   625,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   163,   164,   nil,   160,   142,   143,   144,   151,
   148,   150,   nil,   nil,   145,   146,   nil,   nil,   nil,   165,
   166,   152,   153,   nil,   nil,   nil,   nil,   nil,   272,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   157,   156,   nil,   141,
   162,   159,   158,   167,   154,   155,   149,   147,   139,   161,
   140,   713,   440,   168,   nil,   714,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   163,   164,   nil,   160,   142,   143,   144,
   151,   148,   150,   nil,   nil,   145,   146,   nil,   nil,   nil,
   165,   166,   152,   153,   nil,   nil,   nil,   nil,   nil,   272,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   157,   156,   nil,
   141,   162,   159,   158,   167,   154,   155,   149,   147,   139,
   161,   140,   715,   447,   168,   nil,   716,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   163,   164,   nil,   160,   142,   143,
   144,   151,   148,   150,   nil,   nil,   145,   146,   nil,   nil,
   nil,   165,   166,   152,   153,   nil,   nil,   nil,   nil,   nil,
   272,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   157,   156,
   nil,   141,   162,   159,   158,   167,   154,   155,   149,   147,
   139,   161,   140,   718,   447,   168,   nil,   719,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   163,   164,   nil,   160,   142,
   143,   144,   151,   148,   150,   nil,   nil,   145,   146,   nil,
   nil,   nil,   165,   166,   152,   153,   nil,   nil,   nil,   nil,
   nil,   272,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   157,
   156,   nil,   141,   162,   159,   158,   167,   154,   155,   149,
   147,   139,   161,   140,   487,   440,   168,   nil,   488,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   163,   164,   nil,   160,
   142,   143,   144,   151,   148,   150,   nil,   nil,   145,   146,
   nil,   nil,   nil,   165,   166,   152,   153,   nil,   nil,   nil,
   nil,   nil,   272,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   157,   156,   nil,   141,   162,   159,   158,   167,   154,   155,
   149,   147,   139,   161,   140,   747,   440,   168,   nil,   748,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   163,   164,   nil,
   160,   142,   143,   144,   151,   148,   150,   nil,   nil,   145,
   146,   nil,   nil,   nil,   165,   166,   152,   153,   nil,   nil,
   nil,   nil,   nil,   272,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   157,   156,   nil,   141,   162,   159,   158,   167,   154,
   155,   149,   147,   139,   161,   140,   750,   447,   168,   nil,
   749,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   163,   164,
   nil,   160,   142,   143,   144,   151,   148,   150,   nil,   nil,
   145,   146,   nil,   nil,   nil,   165,   166,   152,   153,   nil,
   nil,   nil,   nil,   nil,   272,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   157,   156,   nil,   141,   162,   159,   158,   167,
   154,   155,   149,   147,   139,   161,   140,  1002,   447,   168,
   nil,  1001,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   163,
   164,   nil,   160,   142,   143,   144,   151,   148,   150,   nil,
   nil,   145,   146,   nil,   nil,   nil,   165,   166,   152,   153,
   nil,   nil,   nil,   nil,   nil,   272,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   157,   156,   nil,   141,   162,   159,   158,
   167,   154,   155,   149,   147,   139,   161,   140,  1005,   440,
   168,   nil,  1006,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   163,   164,   nil,   160,   142,   143,   144,   151,   148,   150,
   nil,   nil,   145,   146,   nil,   nil,   nil,   165,   166,   152,
   153,   nil,   nil,   nil,   nil,   nil,   272,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   157,   156,   nil,   141,   162,   159,
   158,   167,   154,   155,   149,   147,   139,   161,   140,  1007,
   447,   168,   nil,  1008,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   163,   164,   nil,   160,   142,   143,   144,   151,   148,
   150,   nil,   nil,   145,   146,   nil,   nil,   nil,   165,   166,
   152,   153,   nil,   nil,   nil,   nil,   nil,   272,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   157,   156,   nil,   141,   162,
   159,   158,   167,   154,   155,   149,   147,   139,   161,   140,
   nil,   683,   168,   680,   679,   678,   688,   681,   nil,   683,
   nil,   680,   679,   678,   688,   681,   691,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   691,   nil,   683,   nil,   680,   679,
   678,   688,   681,   nil,   nil,   nil,   nil,   nil,   686,   669,
   nil,   691,   nil,   nil,   nil,   nil,   686,   696,   695,   699,
   698,   nil,   nil,   nil,   692,   696,   695,   699,   698,   nil,
   nil,   nil,   692,   686,   nil,   683,   nil,   680,   679,   678,
   688,   681,   696,   695,   699,   698,   nil,   nil,   nil,   692,
   691,   nil,   683,   nil,   680,   679,   678,   688,   681,   nil,
   683,   nil,   680,   679,   678,   688,   681,   691,   nil,   nil,
   nil,   nil,   686,   nil,   nil,   691,   nil,   nil,   nil,   nil,
   nil,   696,   695,   699,   698,   nil,   nil,   nil,   692,   686,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   686,   696,   695,
   699,   698,   nil,   nil,   nil,   692,   696,   695,   699,   698,
   nil,   nil,   683,   692,   680,   679,   678,   688,   681,   nil,
   683,   nil,   680,   679,   678,   688,   681,   691,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   691,   nil,   683,   nil,   680,
   679,   678,   688,   681,   nil,   nil,   nil,   nil,   nil,   686,
   nil,   nil,   691,   nil,   nil,   nil,   nil,   686,   696,   695,
   699,   698,   nil,   nil,   nil,   692,   696,   695,   699,   698,
   nil,   nil,   nil,   692,   686,   nil,   683,   nil,   680,   679,
   678,   688,   681,   696,   695,   699,   698,   nil,   nil,   nil,
   692,   691,   nil,   683,   nil,   680,   679,   678,   688,   681,
   683,   nil,   680,   679,   678,   688,   681,   nil,   691,   nil,
   nil,   nil,   nil,   686,   nil,   691,   nil,   683,   nil,   680,
   679,   678,   688,   681,   699,   698,   nil,   nil,   nil,   692,
   686,   nil,   691,   nil,   nil,   nil,   nil,   686,   nil,   696,
   695,   699,   698,   nil,   nil,   nil,   692,   nil,   699,   698,
   nil,   nil,   nil,   692,   686,   nil,   683,   nil,   680,   679,
   678,   688,   681,   nil,   nil,   699,   698,   nil,   nil,   nil,
   692,   691,   nil,   683,   nil,   680,   679,   678,   688,   681,
   683,   nil,   680,   679,   678,   688,   681,   nil,   691,   nil,
   nil,   nil,   nil,   686,   nil,   691,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   699,   698,   nil,   nil,   nil,   692,
   686,   nil,   nil,   nil,   nil,   nil,   nil,   686,   nil,   nil,
   nil,   699,   698,   nil,   nil,   nil,   692,   nil,   699,   698,
   nil,   nil,   nil,   692 ]

racc_action_check = [
   100,   452,   452,   575,   575,    17,   355,   100,   100,   100,
     1,   347,   100,   100,   100,    24,   100,    26,    19,   393,
    44,    44,    24,   348,   100,   394,   100,   100,   100,    62,
   365,   631,   229,     7,   365,   356,   100,   100,   711,   100,
   100,   100,   100,   100,   868,   894,   925,   926,   929,   359,
   646,   561,    17,   897,   976,   897,    44,    44,   317,    19,
   646,    10,    17,    12,   713,   714,   100,   100,   100,   100,
   100,   100,   100,   100,   100,   100,   100,   100,   100,   100,
    24,    26,   100,   100,   100,   393,   100,   100,   552,   229,
   100,   394,   798,   100,   100,   452,   100,   575,   100,    13,
   100,    15,   100,   100,    26,   100,   100,   100,   100,   100,
  1005,   100,   103,   100,  1006,   355,    62,   828,   631,   103,
   103,   103,   317,  1007,   103,   103,   103,   100,   103,   347,
   100,   100,   100,   100,   347,   100,   103,   100,   103,   103,
   103,   348,   100,   100,   356,   317,   348,   651,   103,   103,
  1008,   103,   103,   103,   103,   103,   711,  1025,   359,   711,
   561,   711,   868,   894,   925,   926,   929,   868,   894,   925,
   926,   929,   976,   713,   714,   715,   635,   976,   103,   103,
   103,   103,   103,   103,   103,   103,   103,   103,   103,   103,
   103,   103,   552,    22,   103,   103,   103,   552,   103,   103,
  1007,   798,   103,    37,   652,   103,   103,    40,   103,   716,
   103,   635,   103,   651,   103,   103,    46,   103,   103,   103,
   103,   103,   442,   103,   114,   103,   828,  1008,  1005,   442,
   442,   442,  1006,  1005,   773,   442,   442,  1006,   442,   103,
   715,  1007,   103,   103,   103,   103,  1007,   103,   210,   103,
   661,   661,   230,   651,   103,   103,   651,   231,   442,   442,
   787,   442,   442,   442,   442,   442,   651,   233,  1008,   773,
   652,   374,   654,  1008,   716,  1025,    14,    14,   374,   815,
  1025,   815,   815,   815,   715,   815,   571,   571,   442,   442,
   442,   442,   442,   442,   442,   442,   442,   442,   442,   442,
   442,   442,   234,   232,   442,   442,   442,   375,   442,   230,
   652,    38,   442,   652,   375,   442,    41,    41,   716,   586,
   442,   238,   442,   652,   442,   442,   787,   442,   442,   442,
   442,   442,   376,   442,   443,   442,   374,   654,   654,   376,
   129,   443,   443,   443,   661,   129,   129,   443,   443,   442,
   443,     3,   442,   442,   271,   442,     3,   442,    38,   443,
   232,   815,   346,   346,   442,   442,   787,   363,    38,   787,
   443,   443,   375,   443,   443,   443,   443,   443,   654,   787,
   571,   654,   285,   586,   586,   571,   946,   388,   946,   946,
   946,   654,   946,   586,    41,    41,   286,   376,   588,   421,
   443,   443,   443,   443,   443,   443,   443,   443,   443,   443,
   443,   443,   443,   443,   458,   289,   443,   443,   443,   363,
   443,   322,   322,   504,   443,   297,   363,   443,   352,   622,
   297,   363,   443,   352,   443,   363,   443,   443,    39,   443,
   443,   443,   443,   443,   623,   443,   443,   443,   389,   301,
   377,   388,   388,   388,   363,   390,   421,   377,   302,   364,
   391,   443,   588,   588,   443,   443,   624,   443,   946,   443,
   304,   458,   588,   624,   624,   624,   443,   443,   624,   624,
   624,   305,   624,   306,   363,    39,   622,   504,   504,   504,
   378,   624,   624,   624,   624,    39,   312,   378,   315,   322,
   322,   623,   624,   624,   504,   624,   624,   624,   624,   624,
   326,   364,   389,   389,   389,   377,   392,   316,   364,   390,
   390,   390,   395,   364,   391,   391,   391,   364,   338,   701,
   701,   338,   624,   624,   624,   624,   624,   624,   624,   624,
   624,   624,   624,   624,   624,   624,   364,   321,   624,   624,
   624,   323,   624,   624,    16,   378,   624,   326,   379,   624,
   624,    16,   624,   327,   624,   379,   624,   326,   624,   624,
    16,   624,   624,   624,   624,   624,   364,   624,   624,   624,
   392,   392,   392,   380,    47,   381,   395,   395,   395,   464,
   380,    47,   381,   624,   330,   436,   624,   624,   624,   624,
    47,   624,   747,   624,   625,   748,   770,   437,   624,   624,
   832,   625,   625,   625,   336,   832,   625,   625,   625,    16,
   625,   464,   340,   379,   341,   464,   464,   382,   384,   465,
   625,   625,   625,   339,   382,   384,   339,   343,   607,   841,
   625,   625,   436,   625,   625,   625,   625,   625,   380,    47,
   381,   228,   436,   309,   437,   803,   803,   353,   228,   747,
   309,   465,   748,   770,   437,   465,   465,   228,   354,   309,
   625,   625,   625,   625,   625,   625,   625,   625,   625,   625,
   625,   625,   625,   625,   837,   607,   625,   625,   625,   837,
   625,   625,   382,   384,   625,   607,   841,   625,   625,   358,
   625,   360,   625,   369,   625,   342,   625,   625,   342,   625,
   625,   625,   625,   625,   749,   625,   228,   625,   309,   987,
   987,   749,   749,   749,   404,   410,   667,   749,   749,   667,
   749,   625,   472,   413,   625,   625,   625,   625,   415,   625,
    82,   625,   718,   418,   422,   432,   625,   625,   472,   472,
   749,   749,    82,   749,   749,   749,   749,   749,   965,   310,
   434,   965,    82,   311,   472,   435,   310,   444,   472,   472,
   311,   472,   472,   454,   466,   310,   467,   468,   469,   311,
   749,   749,   749,   749,   749,   749,   749,   749,   749,   749,
   749,   749,   749,   749,   718,   313,   749,   749,   749,   491,
   749,   718,   313,   495,   749,   511,   718,   749,   512,   515,
   718,   313,   749,   517,   749,   522,   749,   749,   525,   749,
   749,   749,   749,   749,   310,   749,   750,   749,   311,   718,
   534,   535,   536,   750,   750,   750,   800,   537,   549,   750,
   750,   749,   750,   553,   749,   749,   800,   749,   328,   749,
   554,   750,   555,   556,   573,   328,   749,   749,   583,   718,
   313,   591,   750,   750,   328,   750,   750,   750,   750,   750,
   891,   357,   891,   891,   891,   593,   891,   599,   357,   800,
   800,   608,   613,   618,   800,   579,   579,   357,   626,   579,
   579,   579,   750,   750,   750,   750,   750,   750,   750,   750,
   750,   750,   750,   750,   750,   750,   627,   891,   750,   750,
   750,   367,   750,   328,   420,   628,   750,   630,   367,   750,
   634,   420,   636,   638,   750,   640,   750,   367,   750,   750,
   420,   750,   750,   750,   750,   750,   357,   750,   750,   750,
   779,   648,   779,   779,   779,   779,   779,   521,     6,     6,
     6,     6,     6,   750,   521,   779,   750,   750,    27,   750,
   650,   750,   653,   521,   656,    27,    27,    27,   750,   750,
    27,    27,    27,   657,    27,   660,   367,   779,   662,   420,
   671,   479,   672,    27,    27,    27,   779,   779,   779,   779,
   674,   675,   676,   779,    27,    27,   685,    27,    27,    27,
    27,    27,   947,   693,   947,   947,   947,   564,   947,   697,
   700,   703,   521,   479,   564,   709,   712,   479,   479,   779,
   479,   479,   721,   564,    27,    27,    27,    27,    27,    27,
    27,    27,    27,    27,    27,    27,    27,    27,   725,   947,
    27,    27,    27,   744,   746,    27,   755,    27,    27,   776,
   786,    27,    27,   790,    27,   793,    27,   794,    27,   799,
    27,    27,   814,    27,    27,    27,    27,    27,    28,    27,
    27,    27,   564,   816,   821,    28,    28,    28,   824,   829,
    28,    28,    28,   921,    28,    27,   831,   835,    27,    27,
   921,    27,   836,    27,    28,    28,   839,   840,   849,   921,
    27,   850,   852,   853,    28,    28,   854,    28,    28,    28,
    28,    28,   856,   857,   858,   655,   859,   719,   874,   875,
   879,   880,   655,   882,   719,   883,   885,   655,   888,   719,
   890,   655,   901,   719,    28,    28,    28,    28,    28,    28,
    28,    28,    28,    28,    28,    28,    28,    28,   921,   905,
    28,    28,    28,   918,   922,    28,   928,    28,    28,   948,
   955,    28,    28,   958,    28,   959,    28,   960,    28,   961,
    28,    28,   963,    28,    28,    28,    28,    28,    57,    28,
   655,    28,   719,   972,   977,    57,    57,    57,   978,   979,
    57,    57,    57,   927,    57,    28,   980,   981,    28,    28,
   927,    28,   982,    28,    57,    57,    57,   983,   985,   927,
    28,   988,  1001,  1002,    57,    57,  1004,    57,    57,    57,
    57,    57,   919,   969,   919,   919,   919,  1017,   919,   686,
   969,   686,   686,   686,  1000,   686,  1000,  1000,  1000,   969,
  1000,  1020,  1021,  1022,    57,    57,    57,    57,    57,    57,
    57,    57,    57,    57,    57,    57,    57,    57,   927,   908,
    57,    57,    57,  1023,  1026,    57,   686,  1027,    57,   908,
  1034,    57,    57,   nil,    57,   686,    57,   nil,    57,   nil,
    57,    57,   nil,    57,    57,    57,    57,    57,   969,    57,
   nil,    57,   812,   nil,   812,   812,   812,   nil,   812,   nil,
   nil,   nil,   908,   908,   nil,    57,   nil,   908,    57,    57,
    57,    57,   nil,    57,   438,    57,   nil,   nil,   nil,   nil,
    57,   438,   438,   438,   nil,   nil,   438,   438,   438,   812,
   438,   889,   nil,   889,   889,   889,   nil,   889,   812,   438,
   438,   438,   984,   nil,   984,   984,   984,   nil,   984,   nil,
   438,   438,   nil,   438,   438,   438,   438,   438,   986,   nil,
   986,   986,   986,  1016,   986,  1016,  1016,  1016,   889,  1016,
   300,   300,   300,   300,   300,   nil,   nil,   889,   nil,   984,
   438,   438,   438,   438,   438,   438,   438,   438,   438,   438,
   438,   438,   438,   438,   nil,   986,   438,   438,   438,   nil,
  1016,   438,   nil,   438,   438,   nil,   nil,   438,   438,   nil,
   438,   nil,   438,   nil,   438,   nil,   438,   438,   nil,   438,
   438,   438,   438,   438,   nil,   438,   438,   438,   808,   nil,
   808,   808,   808,   808,   808,   335,   335,   335,   335,   335,
   nil,   438,   nil,   808,   438,   438,   447,   438,   nil,   438,
   nil,   nil,   nil,   447,   447,   447,   438,   nil,   447,   447,
   447,   nil,   447,   484,   nil,   808,   509,   509,   509,   509,
   509,   447,   447,   447,   447,   nil,   808,   808,   nil,   484,
   484,   808,   447,   447,   nil,   447,   447,   447,   447,   447,
   nil,   nil,   nil,   nil,   nil,   484,   nil,   484,   nil,   484,
   484,   nil,   484,   484,   nil,   nil,   484,   nil,   484,   nil,
   nil,   nil,   447,   447,   447,   447,   447,   447,   447,   447,
   447,   447,   447,   447,   447,   447,   nil,   nil,   447,   447,
   447,   480,   nil,   447,   nil,   nil,   447,   nil,   nil,   447,
   447,   nil,   447,   nil,   447,   nil,   447,   nil,   447,   447,
   nil,   447,   447,   447,   447,   447,   nil,   447,   447,   447,
   nil,   nil,   nil,   480,   nil,   nil,   nil,   480,   480,   nil,
   480,   480,   nil,   447,   nil,   nil,   447,   447,   447,   447,
   nil,   447,   448,   447,   nil,   nil,   nil,   nil,   447,   448,
   448,   448,   nil,   nil,   448,   448,   448,   531,   448,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   448,   448,   448,
   448,   nil,   nil,   531,   531,   nil,   nil,   nil,   448,   448,
   nil,   448,   448,   448,   448,   448,   nil,   nil,   nil,   531,
   nil,   531,   nil,   531,   531,   nil,   531,   531,   nil,   nil,
   531,   nil,   531,   nil,   nil,   nil,   nil,   nil,   448,   448,
   448,   448,   448,   448,   448,   448,   448,   448,   448,   448,
   448,   448,   nil,   nil,   448,   448,   448,   nil,   nil,   448,
   nil,   nil,   448,   nil,   nil,   448,   448,   nil,   448,   nil,
   448,   nil,   448,   nil,   448,   448,   nil,   448,   448,   448,
   448,   448,   nil,   448,   448,   448,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   448,
   nil,   nil,   448,   448,   448,   448,   nil,   448,   486,   448,
   nil,   nil,   nil,   nil,   448,   486,   486,   486,   nil,   nil,
   486,   486,   486,   470,   486,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   486,   486,   nil,   nil,   nil,   nil,   470,
   470,   nil,   nil,   nil,   486,   486,   nil,   486,   486,   486,
   486,   486,   nil,   nil,   nil,   470,   nil,   470,   nil,   470,
   470,   nil,   470,   470,   nil,   nil,   nil,    21,    21,    21,
    21,    21,    21,    21,    21,    21,    21,    21,   nil,    21,
    21,   nil,   nil,    21,    21,   486,   nil,   nil,   nil,   nil,
   nil,   nil,   486,   nil,   nil,   nil,   nil,   486,   486,    21,
   nil,    21,   nil,    21,    21,   nil,    21,    21,    21,    21,
    21,   nil,    21,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   486,   486,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    21,   nil,   nil,   486,   nil,   nil,   486,   nil,
   nil,   nil,   nil,   486,     0,     0,     0,     0,     0,     0,
   486,   nil,   nil,     0,     0,   nil,   nil,   nil,     0,   nil,
     0,     0,     0,     0,     0,     0,     0,   nil,   nil,   nil,
   nil,   nil,     0,     0,     0,     0,     0,     0,     0,   nil,
   nil,     0,   nil,   nil,   nil,   nil,   425,     0,     0,     0,
     0,     0,     0,     0,     0,     0,     0,     0,     0,   nil,
     0,     0,     0,   nil,     0,     0,     0,     0,     0,   425,
   425,   425,   425,   425,   425,   425,   425,   425,   425,   425,
   nil,   425,   425,   nil,   nil,   425,   425,   nil,     0,   nil,
   nil,     0,   nil,   nil,     0,     0,   nil,   nil,     0,   nil,
     0,   425,   nil,   425,     0,   425,   425,   nil,   425,   425,
   425,   425,   425,     0,   425,   nil,   nil,   nil,     0,     0,
     0,     0,   nil,     0,     0,     0,     0,   nil,   nil,   nil,
   nil,     0,     0,   nil,   425,   nil,   425,   nil,   nil,     0,
   nil,     0,     0,     0,    33,    33,    33,    33,    33,    33,
   nil,   nil,   nil,    33,    33,   nil,   nil,   nil,    33,   nil,
    33,    33,    33,    33,    33,    33,    33,   nil,   nil,   nil,
   nil,   nil,    33,    33,    33,    33,    33,    33,    33,   nil,
   nil,    33,   nil,   nil,   nil,   nil,   431,    33,    33,    33,
    33,    33,    33,    33,    33,    33,    33,    33,    33,   nil,
    33,    33,    33,   nil,    33,    33,    33,    33,    33,   431,
   431,   431,   431,   431,   431,   431,   431,   431,   431,   431,
   nil,   431,   431,   nil,   nil,   431,   431,   nil,    33,   nil,
   nil,    33,   nil,   nil,    33,    33,   nil,   nil,    33,   nil,
    33,   431,   nil,   431,    33,   431,   431,   nil,   431,   431,
   431,   431,   431,    33,   431,   nil,   nil,   nil,    33,    33,
    33,    33,   nil,    33,    33,    33,    33,   nil,   nil,   nil,
   nil,    33,    33,   nil,   431,   nil,   nil,   nil,   nil,    33,
   nil,    33,    33,    33,   127,   127,   127,   127,   127,   127,
   nil,   nil,   nil,   127,   127,   nil,   nil,   nil,   127,   nil,
   127,   127,   127,   127,   127,   127,   127,   nil,   nil,   nil,
   nil,   nil,   127,   127,   127,   127,   127,   127,   127,   nil,
   nil,   127,   nil,   nil,   nil,   nil,   nil,   127,   127,   127,
   127,   127,   127,   127,   127,   127,   127,   127,   127,   nil,
   127,   127,   127,   nil,   127,   127,   127,   127,   127,   283,
   283,   283,   283,   283,   283,   283,   283,   283,   283,   283,
   nil,   283,   283,   nil,   nil,   283,   283,   nil,   127,   nil,
   nil,   127,   nil,   nil,   127,   127,   nil,   nil,   127,   nil,
   127,   283,   nil,   283,   127,   283,   283,   nil,   283,   283,
   283,   283,   283,   127,   283,   nil,   nil,   nil,   127,   127,
   127,   127,   nil,   127,   127,   127,   127,   nil,   nil,   nil,
   nil,   127,   127,   nil,   283,   nil,   nil,   nil,   nil,   127,
   nil,   127,   127,   127,   212,   212,   212,   212,   212,   212,
   nil,   nil,   nil,   212,   212,   nil,   nil,   nil,   212,   nil,
   212,   212,   212,   212,   212,   212,   212,   nil,   nil,   nil,
   nil,   nil,   212,   212,   212,   212,   212,   212,   212,   nil,
   nil,   212,   nil,   nil,   nil,   nil,   nil,   212,   212,   212,
   212,   212,   212,   212,   212,   212,   212,   212,   212,   nil,
   212,   212,   212,   nil,   212,   212,   212,   212,   212,   483,
   483,   483,   483,   483,   483,   483,   483,   483,   483,   483,
   nil,   483,   483,   nil,   nil,   483,   483,   nil,   212,   nil,
   nil,   212,   nil,   nil,   212,   212,   nil,   nil,   212,   nil,
   212,   483,   nil,   483,   212,   483,   483,   nil,   483,   483,
   483,   483,   483,   212,   483,   nil,   nil,   nil,   212,   212,
   212,   212,   nil,   212,   212,   212,   212,   nil,   nil,   nil,
   nil,   212,   212,   483,   483,   nil,   nil,   nil,   nil,   212,
   nil,   212,   212,   212,   237,   237,   237,   237,   237,   237,
   nil,   nil,   nil,   237,   237,   nil,   nil,   nil,   237,   nil,
   237,   237,   237,   237,   237,   237,   237,   nil,   nil,   nil,
   nil,   nil,   237,   237,   237,   237,   237,   237,   237,   nil,
   nil,   237,   nil,   nil,   nil,   nil,   nil,   237,   237,   237,
   237,   237,   237,   237,   237,   237,   237,   237,   237,   nil,
   237,   237,   237,   nil,   237,   237,   237,   237,   237,   532,
   532,   532,   532,   532,   532,   532,   532,   532,   532,   532,
   nil,   532,   532,   nil,   nil,   532,   532,   nil,   237,   nil,
   nil,   237,   nil,   nil,   237,   237,   nil,   nil,   237,   nil,
   237,   532,   nil,   532,   237,   532,   532,   nil,   532,   532,
   532,   532,   532,   237,   532,   nil,   nil,   nil,   237,   237,
   237,   237,   nil,   237,   237,   237,   237,   nil,   nil,   nil,
   nil,   237,   237,   nil,   532,   nil,   nil,   nil,   nil,   237,
   nil,   237,   237,   237,   303,   303,   303,   303,   303,   303,
   nil,   nil,   nil,   303,   303,   nil,   nil,   nil,   303,   nil,
   303,   303,   303,   303,   303,   303,   303,   nil,   nil,   nil,
   nil,   nil,   303,   303,   303,   303,   303,   303,   303,   nil,
   nil,   303,   nil,   nil,   nil,   nil,   nil,   303,   303,   303,
   303,   303,   303,   303,   303,   303,   303,   303,   303,   nil,
   303,   303,   303,   nil,   303,   303,   303,   303,   303,   752,
   752,   752,   752,   752,   752,   752,   752,   752,   752,   752,
   nil,   752,   752,   nil,   nil,   752,   752,   nil,   303,   nil,
   nil,   303,   nil,   nil,   303,   303,   nil,   nil,   303,   nil,
   303,   752,   nil,   752,   303,   752,   752,   nil,   752,   752,
   752,   752,   752,   303,   752,   nil,   nil,   nil,   303,   303,
   303,   303,   nil,   303,   303,   303,   303,   nil,   nil,   nil,
   nil,   303,   303,   nil,   752,   nil,   nil,   nil,   nil,   303,
   nil,   303,   303,   303,   308,   308,   308,   308,   308,   308,
   nil,   nil,   nil,   308,   308,   nil,   nil,   nil,   308,   nil,
   308,   308,   308,   308,   308,   308,   308,   nil,   nil,   nil,
   nil,   nil,   308,   308,   308,   308,   308,   308,   308,   nil,
   nil,   308,   nil,   nil,   nil,   nil,   nil,   308,   308,   308,
   308,   308,   308,   308,   308,   308,   308,   308,   308,   nil,
   308,   308,   308,   nil,   308,   308,   308,   308,   308,   846,
   846,   846,   846,   846,   846,   846,   846,   846,   846,   846,
   nil,   846,   846,   nil,   nil,   846,   846,   nil,   308,   nil,
   nil,   308,   nil,   nil,   308,   308,   nil,   nil,   308,   nil,
   308,   846,   nil,   846,   308,   846,   846,   nil,   846,   846,
   846,   846,   846,   308,   846,   nil,   nil,   nil,   308,   308,
   308,   308,   nil,   308,   308,   308,   308,   nil,   nil,   nil,
   nil,   308,   308,   nil,   846,   nil,   nil,   nil,   nil,   308,
   nil,   308,   308,   308,   334,   334,   334,   334,   334,   334,
   nil,   nil,   nil,   334,   334,   nil,   nil,   nil,   334,   nil,
   334,   334,   334,   334,   334,   334,   334,   nil,   nil,   nil,
   nil,   nil,   334,   334,   334,   334,   334,   334,   334,   nil,
   nil,   334,   nil,   nil,   nil,   nil,   nil,   334,   334,   334,
   334,   334,   334,   334,   334,   334,   334,   334,   334,   nil,
   334,   334,   334,   nil,   334,   334,   334,   334,   334,   462,
   462,   462,   462,   462,   462,   462,   462,   462,   462,   462,
   nil,   462,   462,   nil,   nil,   462,   462,   nil,   334,   nil,
   nil,   334,   nil,   nil,   334,   334,   nil,   nil,   334,   nil,
   334,   462,   nil,   462,   334,   462,   462,   nil,   462,   462,
   462,   462,   462,   334,   462,   nil,   nil,   nil,   334,   334,
   334,   334,   nil,   334,   334,   334,   334,   nil,   nil,   nil,
   nil,   334,   334,   nil,   nil,   nil,   nil,   nil,   nil,   334,
   nil,   334,   334,   334,   350,   350,   350,   350,   350,   350,
   nil,   nil,   nil,   350,   350,   nil,   nil,   nil,   350,   nil,
   350,   350,   350,   350,   350,   350,   350,   nil,   nil,   nil,
   nil,   nil,   350,   350,   350,   350,   350,   350,   350,   nil,
   nil,   350,   nil,   nil,   nil,   nil,   nil,   350,   350,   350,
   350,   350,   350,   350,   350,   350,   350,   350,   350,   nil,
   350,   350,   350,   nil,   350,   350,   350,   350,   350,   463,
   463,   463,   463,   463,   463,   463,   463,   463,   463,   463,
   nil,   463,   463,   nil,   nil,   463,   463,   nil,   350,   nil,
   nil,   350,   nil,   nil,   350,   350,   nil,   nil,   350,   nil,
   350,   463,   nil,   463,   350,   463,   463,   nil,   463,   463,
   463,   463,   463,   350,   463,   nil,   nil,   nil,   350,   350,
   350,   350,   nil,   350,   350,   350,   350,   nil,   nil,   nil,
   nil,   350,   350,   nil,   nil,   nil,   nil,   nil,   nil,   350,
   nil,   350,   350,   350,   351,   351,   351,   351,   351,   351,
   nil,   nil,   nil,   351,   351,   nil,   nil,   nil,   351,   nil,
   351,   351,   351,   351,   351,   351,   351,   nil,   nil,   nil,
   nil,   nil,   351,   351,   351,   351,   351,   351,   351,   nil,
   nil,   351,   nil,   nil,   nil,   nil,   nil,   351,   351,   351,
   351,   351,   351,   351,   351,   351,   351,   351,   351,   nil,
   351,   351,   351,   nil,   351,   351,   351,   351,   351,   473,
   473,   473,   473,   473,   473,   473,   nil,   nil,   473,   473,
   nil,   nil,   nil,   nil,   nil,   473,   473,   nil,   351,   nil,
   nil,   351,   nil,   nil,   351,   351,   nil,   nil,   351,   nil,
   351,   473,   nil,   473,   351,   473,   473,   nil,   473,   473,
   473,   473,   473,   351,   473,   nil,   nil,   nil,   351,   351,
   351,   351,   nil,   351,   351,   351,   351,   nil,   nil,   nil,
   nil,   351,   351,   nil,   nil,   nil,   nil,   nil,   nil,   351,
   nil,   351,   351,   351,   548,   548,   548,   548,   548,   548,
   nil,   nil,   nil,   548,   548,   nil,   nil,   nil,   548,   nil,
   548,   548,   548,   548,   548,   548,   548,   nil,   nil,   nil,
   nil,   nil,   548,   548,   548,   548,   548,   548,   548,   nil,
   nil,   548,   nil,   nil,   nil,   nil,   nil,   548,   548,   548,
   548,   548,   548,   548,   548,   548,   548,   548,   548,   nil,
   548,   548,   548,   nil,   548,   548,   548,   548,   548,   474,
   474,   474,   474,   474,   474,   474,   nil,   nil,   474,   474,
   nil,   nil,   nil,   nil,   nil,   474,   474,   nil,   548,   nil,
   nil,   548,   nil,   nil,   548,   548,   nil,   nil,   548,   nil,
   548,   474,   nil,   474,   548,   474,   474,   nil,   474,   474,
   474,   474,   474,   548,   474,   nil,   nil,   nil,   548,   548,
   548,   548,   nil,   548,   548,   548,   548,   nil,   nil,   nil,
   nil,   548,   548,   nil,   nil,   nil,   nil,   nil,   nil,   548,
   nil,   548,   548,   548,   551,   551,   551,   551,   551,   551,
   nil,   nil,   nil,   551,   551,   nil,   nil,   nil,   551,   nil,
   551,   551,   551,   551,   551,   551,   551,   nil,   nil,   nil,
   nil,   nil,   551,   551,   551,   551,   551,   551,   551,   nil,
   nil,   551,   nil,   nil,   nil,   nil,   nil,   551,   551,   551,
   551,   551,   551,   551,   551,   551,   551,   551,   551,   nil,
   551,   551,   551,   nil,   551,   551,   551,   551,   551,   475,
   475,   475,   475,   475,   475,   475,   nil,   nil,   475,   475,
   nil,   nil,   nil,   nil,   nil,   475,   475,   nil,   551,   nil,
   nil,   551,   nil,   nil,   551,   551,   nil,   nil,   551,   nil,
   551,   475,   nil,   475,   551,   475,   475,   nil,   475,   475,
   475,   475,   475,   551,   475,   nil,   nil,   nil,   551,   551,
   551,   551,   nil,   551,   551,   551,   551,   nil,   nil,   nil,
   nil,   551,   551,   nil,   nil,   nil,   nil,   nil,   nil,   551,
   nil,   551,   551,   551,   572,   572,   572,   572,   572,   572,
   nil,   nil,   nil,   572,   572,   nil,   nil,   nil,   572,   nil,
   572,   572,   572,   572,   572,   572,   572,   nil,   nil,   nil,
   nil,   nil,   572,   572,   572,   572,   572,   572,   572,   nil,
   nil,   572,   nil,   nil,   nil,   nil,   nil,   572,   572,   572,
   572,   572,   572,   572,   572,   572,   572,   572,   572,   nil,
   572,   572,   572,   nil,   572,   572,   572,   572,   572,   476,
   476,   476,   476,   476,   476,   476,   nil,   nil,   476,   476,
   nil,   nil,   nil,   nil,   nil,   476,   476,   nil,   572,   nil,
   nil,   572,   nil,   nil,   572,   572,   nil,   nil,   572,   nil,
   572,   476,   nil,   476,   572,   476,   476,   nil,   476,   476,
   476,   476,   476,   572,   476,   nil,   nil,   nil,   572,   572,
   572,   572,   nil,   572,   572,   572,   572,   nil,   nil,   nil,
   nil,   572,   572,   nil,   nil,   nil,   nil,   nil,   nil,   572,
   nil,   572,   572,   572,   717,   717,   717,   717,   717,   717,
   nil,   nil,   nil,   717,   717,   nil,   nil,   nil,   717,   nil,
   717,   717,   717,   717,   717,   717,   717,   nil,   nil,   nil,
   nil,   nil,   717,   717,   717,   717,   717,   717,   717,   nil,
   nil,   717,   nil,   nil,   nil,   nil,   nil,   717,   717,   717,
   717,   717,   717,   717,   717,   717,   717,   717,   717,   nil,
   717,   717,   717,   nil,   717,   717,   717,   717,   717,   477,
   477,   477,   477,   477,   477,   477,   nil,   nil,   477,   477,
   nil,   nil,   nil,   nil,   nil,   477,   477,   nil,   717,   nil,
   nil,   717,   nil,   nil,   717,   717,   nil,   nil,   717,   nil,
   717,   477,   nil,   477,   717,   477,   477,   nil,   477,   477,
   477,   477,   477,   717,   477,   nil,   nil,   nil,   717,   717,
   717,   717,   nil,   717,   717,   717,   717,   nil,   nil,   nil,
   nil,   717,   717,   nil,   nil,   nil,   nil,   nil,   nil,   717,
   nil,   717,   717,   717,   722,   722,   722,   722,   722,   722,
   nil,   nil,   nil,   722,   722,   nil,   nil,   nil,   722,   nil,
   722,   722,   722,   722,   722,   722,   722,   nil,   nil,   nil,
   nil,   nil,   722,   722,   722,   722,   722,   722,   722,   nil,
   nil,   722,   nil,   nil,   nil,   nil,   nil,   722,   722,   722,
   722,   722,   722,   722,   722,   722,   722,   722,   722,   nil,
   722,   722,   722,   nil,   722,   722,   722,   722,   722,   478,
   478,   478,   478,   478,   478,   478,   nil,   nil,   478,   478,
   nil,   nil,   nil,   nil,   nil,   478,   478,   nil,   722,   nil,
   nil,   722,   nil,   nil,   722,   722,   nil,   nil,   722,   nil,
   722,   478,   nil,   478,   722,   478,   478,   nil,   478,   478,
   478,   478,   478,   722,   478,   nil,   nil,   nil,   722,   722,
   722,   722,   nil,   722,   722,   722,   722,   nil,   nil,   nil,
   nil,   722,   722,   nil,   nil,   nil,   nil,   nil,   nil,   722,
   nil,   722,   722,   722,   726,   726,   726,   726,   726,   726,
   nil,   nil,   nil,   726,   726,   nil,   nil,   nil,   726,   nil,
   726,   726,   726,   726,   726,   726,   726,   nil,   nil,   nil,
   nil,   nil,   726,   726,   726,   726,   726,   726,   726,   nil,
   nil,   726,   nil,   nil,   nil,   nil,   nil,   726,   726,   726,
   726,   726,   726,   726,   726,   726,   726,   726,   726,   nil,
   726,   726,   726,   nil,   726,   726,   726,   726,   726,   481,
   481,   481,   481,   481,   481,   481,   nil,   nil,   481,   481,
   nil,   nil,   nil,   nil,   nil,   481,   481,   nil,   726,   nil,
   nil,   726,   nil,   nil,   726,   726,   nil,   nil,   726,   nil,
   726,   481,   nil,   481,   726,   481,   481,   nil,   481,   481,
   481,   481,   481,   726,   481,   nil,   nil,   nil,   726,   726,
   726,   726,   nil,   726,   726,   726,   726,   nil,   nil,   nil,
   nil,   726,   726,   nil,   nil,   nil,   nil,   nil,   nil,   726,
   nil,   726,   726,   726,   736,   736,   736,   736,   736,   736,
   nil,   nil,   nil,   736,   736,   nil,   nil,   nil,   736,   nil,
   736,   736,   736,   736,   736,   736,   736,   nil,   nil,   nil,
   nil,   nil,   736,   736,   736,   736,   736,   736,   736,   nil,
   nil,   736,   nil,   nil,   nil,   nil,   nil,   736,   736,   736,
   736,   736,   736,   736,   736,   736,   736,   736,   736,   nil,
   736,   736,   736,   nil,   736,   736,   736,   736,   736,   482,
   482,   482,   482,   482,   482,   482,   482,   nil,   482,   482,
   nil,   nil,   nil,   nil,   nil,   482,   482,   nil,   736,   nil,
   nil,   736,   nil,   nil,   736,   736,   nil,   nil,   736,   nil,
   736,   482,   nil,   482,   736,   482,   482,   nil,   482,   482,
   482,   482,   482,   736,   482,   nil,   nil,   nil,   736,   736,
   736,   736,   nil,   736,   736,   736,   736,   nil,   nil,   nil,
   nil,   736,   736,   nil,   nil,   nil,   nil,   nil,   nil,   736,
   nil,   736,   736,   736,   781,   781,   781,   781,   781,   781,
   nil,   nil,   nil,   781,   781,   nil,   nil,   nil,   781,   nil,
   781,   781,   781,   781,   781,   781,   781,   nil,   nil,   nil,
   nil,   nil,   781,   781,   781,   781,   781,   781,   781,   nil,
   nil,   781,   nil,   nil,   nil,   nil,   nil,   781,   781,   781,
   781,   781,   781,   781,   781,   781,   781,   781,   781,   nil,
   781,   781,   781,   nil,   781,   781,   781,   781,   781,   471,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   471,   471,   nil,   781,   nil,
   nil,   781,   nil,   nil,   781,   781,   nil,   nil,   781,   nil,
   781,   471,   nil,   471,   781,   471,   471,   nil,   471,   471,
   nil,   nil,   nil,   781,   nil,   nil,   nil,   nil,   781,   781,
   781,   781,   nil,   781,   781,   781,   781,   nil,   nil,   nil,
   nil,   781,   781,   nil,   nil,   nil,   nil,   nil,   nil,   781,
   nil,   781,   781,   781,   792,   792,   792,   792,   792,   792,
   nil,   nil,   nil,   792,   792,   nil,   nil,   nil,   792,   nil,
   792,   792,   792,   792,   792,   792,   792,   nil,   nil,   nil,
   nil,   nil,   792,   792,   792,   792,   792,   792,   792,   nil,
   nil,   792,   nil,   nil,   nil,   nil,   nil,   792,   792,   792,
   792,   792,   792,   792,   792,   792,   792,   792,   792,   nil,
   792,   792,   792,   nil,   792,   792,   792,   792,   792,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   792,   nil,
   nil,   792,   nil,   nil,   792,   792,   nil,   nil,   792,   nil,
   792,   nil,   nil,   nil,   792,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   792,   nil,   nil,   nil,   nil,   792,   792,
   792,   792,   nil,   792,   792,   792,   792,   nil,   nil,   nil,
   nil,   792,   792,   nil,   nil,   nil,   nil,   nil,   nil,   792,
   nil,   792,   792,   792,   827,   827,   827,   827,   827,   827,
   nil,   nil,   nil,   827,   827,   nil,   nil,   nil,   827,   nil,
   827,   827,   827,   827,   827,   827,   827,   nil,   nil,   nil,
   nil,   nil,   827,   827,   827,   827,   827,   827,   827,   nil,
   nil,   827,   nil,   nil,   nil,   nil,   nil,   827,   827,   827,
   827,   827,   827,   827,   827,   827,   827,   827,   827,   nil,
   827,   827,   827,   nil,   827,   827,   827,   827,   827,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   827,   nil,
   nil,   827,   nil,   nil,   827,   827,   nil,   nil,   827,   nil,
   827,   nil,   nil,   nil,   827,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   827,   nil,   nil,   nil,   nil,   827,   827,
   827,   827,   nil,   827,   827,   827,   827,   nil,   nil,   nil,
   nil,   827,   827,   nil,   nil,   nil,   nil,   nil,   nil,   827,
   nil,   827,   827,   827,   833,   833,   833,   833,   833,   833,
   nil,   nil,   nil,   833,   833,   nil,   nil,   nil,   833,   nil,
   833,   833,   833,   833,   833,   833,   833,   nil,   nil,   nil,
   nil,   nil,   833,   833,   833,   833,   833,   833,   833,   nil,
   nil,   833,   nil,   nil,   nil,   nil,   nil,   833,   833,   833,
   833,   833,   833,   833,   833,   833,   833,   833,   833,   nil,
   833,   833,   833,   nil,   833,   833,   833,   833,   833,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   833,   nil,
   nil,   833,   nil,   nil,   833,   833,   nil,   nil,   833,   nil,
   833,   nil,   nil,   nil,   833,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   833,   nil,   nil,   nil,   nil,   833,   833,
   833,   833,   nil,   833,   833,   833,   833,   nil,   nil,   nil,
   nil,   833,   833,   nil,   nil,   nil,   nil,   nil,   nil,   833,
   nil,   833,   833,   833,   847,   847,   847,   847,   847,   847,
   nil,   nil,   nil,   847,   847,   nil,   nil,   nil,   847,   nil,
   847,   847,   847,   847,   847,   847,   847,   nil,   nil,   nil,
   nil,   nil,   847,   847,   847,   847,   847,   847,   847,   nil,
   nil,   847,   nil,   nil,   nil,   nil,   nil,   847,   847,   847,
   847,   847,   847,   847,   847,   847,   847,   847,   847,   nil,
   847,   847,   847,   nil,   847,   847,   847,   847,   847,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   847,   nil,
   nil,   847,   nil,   nil,   847,   847,   nil,   nil,   847,   nil,
   847,   nil,   nil,   nil,   847,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   847,   nil,   nil,   nil,   nil,   847,   847,
   847,   847,   nil,   847,   847,   847,   847,   nil,   nil,   nil,
   nil,   847,   847,   nil,   nil,   nil,   nil,   nil,   nil,   847,
   nil,   847,   847,   847,   865,   865,   865,   865,   865,   865,
   nil,   nil,   nil,   865,   865,   nil,   nil,   nil,   865,   nil,
   865,   865,   865,   865,   865,   865,   865,   nil,   nil,   nil,
   nil,   nil,   865,   865,   865,   865,   865,   865,   865,   nil,
   nil,   865,   nil,   nil,   nil,   nil,   nil,   865,   865,   865,
   865,   865,   865,   865,   865,   865,   865,   865,   865,   nil,
   865,   865,   865,   nil,   865,   865,   865,   865,   865,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   865,   nil,
   nil,   865,   nil,   nil,   865,   865,   nil,   nil,   865,   nil,
   865,   nil,   nil,   nil,   865,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   865,   nil,   nil,   nil,   nil,   865,   865,
   865,   865,   nil,   865,   865,   865,   865,   nil,   nil,   nil,
   nil,   865,   865,   nil,   nil,   nil,   nil,   nil,   nil,   865,
   nil,   865,   865,   865,   924,   924,   924,   924,   924,   924,
   nil,   nil,   nil,   924,   924,   nil,   nil,   nil,   924,   nil,
   924,   924,   924,   924,   924,   924,   924,   nil,   nil,   nil,
   nil,   nil,   924,   924,   924,   924,   924,   924,   924,   nil,
   nil,   924,   nil,   nil,   nil,   nil,   nil,   924,   924,   924,
   924,   924,   924,   924,   924,   924,   924,   924,   924,   nil,
   924,   924,   924,   nil,   924,   924,   924,   924,   924,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   924,   nil,
   nil,   924,   nil,   nil,   924,   924,   nil,   nil,   924,   nil,
   924,   nil,   nil,   nil,   924,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   924,   nil,   nil,   nil,   nil,   924,   924,
   924,   924,   nil,   924,   924,   924,   924,   nil,   nil,   nil,
   nil,   924,   924,   nil,   nil,   nil,   nil,   nil,   nil,   924,
   nil,   924,   924,   924,   931,   931,   931,   931,   931,   931,
   nil,   nil,   nil,   931,   931,   nil,   nil,   nil,   931,   nil,
   931,   931,   931,   931,   931,   931,   931,   nil,   nil,   nil,
   nil,   nil,   931,   931,   931,   931,   931,   931,   931,   nil,
   nil,   931,   nil,   nil,   nil,   nil,   nil,   931,   931,   931,
   931,   931,   931,   931,   931,   931,   931,   931,   931,   nil,
   931,   931,   931,   nil,   931,   931,   931,   931,   931,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   931,   nil,
   nil,   931,   nil,   nil,   931,   931,   nil,   nil,   931,   nil,
   931,   nil,   nil,   nil,   931,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   931,   nil,   nil,   nil,   nil,   931,   931,
   931,   931,   nil,   931,   931,   931,   931,   nil,   nil,   nil,
   nil,   931,   931,   nil,   nil,   nil,   nil,   nil,   nil,   931,
   nil,   931,   931,   931,   932,   932,   932,   932,   932,   932,
   nil,   nil,   nil,   932,   932,   nil,   nil,   nil,   932,   nil,
   932,   932,   932,   932,   932,   932,   932,   nil,   nil,   nil,
   nil,   nil,   932,   932,   932,   932,   932,   932,   932,   nil,
   nil,   932,   nil,   nil,   nil,   nil,   nil,   932,   932,   932,
   932,   932,   932,   932,   932,   932,   932,   932,   932,   nil,
   932,   932,   932,   nil,   932,   932,   932,   932,   932,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   932,   nil,
   nil,   932,   nil,   nil,   932,   932,   nil,   nil,   932,   nil,
   932,   nil,   nil,   nil,   932,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   932,   nil,   nil,   nil,   nil,   932,   932,
   932,   932,   nil,   932,   932,   932,   932,   nil,   nil,   nil,
   nil,   932,   932,   nil,   nil,   nil,   nil,   nil,   nil,   932,
   nil,   932,   932,   932,   949,   949,   949,   949,   949,   949,
   nil,   nil,   nil,   949,   949,   nil,   nil,   nil,   949,   nil,
   949,   949,   949,   949,   949,   949,   949,   nil,   nil,   nil,
   nil,   nil,   949,   949,   949,   949,   949,   949,   949,   nil,
   nil,   949,   nil,   nil,   nil,   nil,   nil,   949,   949,   949,
   949,   949,   949,   949,   949,   949,   949,   949,   949,   nil,
   949,   949,   949,   nil,   949,   949,   949,   949,   949,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   949,   nil,
   nil,   949,   nil,   nil,   949,   949,   nil,   nil,   949,   nil,
   949,   nil,   nil,   nil,   949,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   949,   nil,   nil,   nil,   nil,   949,   949,
   949,   949,   nil,   949,   949,   949,   949,   nil,   nil,   nil,
   nil,   949,   949,   nil,   nil,   nil,   nil,   nil,   nil,   949,
   nil,   949,   949,   949,   954,   954,   954,   954,   954,   954,
   nil,   nil,   nil,   954,   954,   nil,   nil,   nil,   954,   nil,
   954,   954,   954,   954,   954,   954,   954,   nil,   nil,   nil,
   nil,   nil,   954,   954,   954,   954,   954,   954,   954,   nil,
   nil,   954,   nil,   nil,   nil,   nil,   nil,   954,   954,   954,
   954,   954,   954,   954,   954,   954,   954,   954,   954,   nil,
   954,   954,   954,   nil,   954,   954,   954,   954,   954,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   954,   nil,
   nil,   954,   nil,   nil,   954,   954,   nil,   nil,   954,   nil,
   954,   nil,   nil,   nil,   954,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   954,   nil,   nil,   nil,   nil,   954,   954,
   954,   954,   nil,   954,   954,   954,   954,   nil,   nil,   nil,
   nil,   954,   954,   nil,   nil,   nil,   nil,   nil,   nil,   954,
   nil,   954,   954,   954,     5,     5,     5,     5,     5,   nil,
   nil,   nil,     5,     5,   nil,   nil,   nil,     5,   nil,     5,
     5,     5,     5,     5,     5,     5,   nil,   nil,   nil,   nil,
   nil,     5,     5,     5,     5,     5,     5,     5,   nil,   nil,
     5,   nil,   nil,   nil,   nil,   nil,     5,     5,     5,     5,
     5,     5,     5,     5,     5,     5,     5,     5,   nil,     5,
     5,     5,   nil,     5,     5,     5,     5,     5,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,     5,   nil,   nil,
     5,   nil,   nil,     5,     5,   nil,   nil,     5,   nil,     5,
   nil,   nil,   nil,     5,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,     5,   nil,   nil,   nil,   nil,     5,     5,     5,
     5,   nil,     5,     5,     5,     5,   nil,   nil,   nil,   nil,
     5,     5,   nil,   nil,   nil,    20,    20,    20,     5,    20,
     5,     5,     5,    20,    20,   nil,   nil,   nil,    20,   nil,
    20,    20,    20,    20,    20,    20,    20,   nil,   nil,   nil,
   nil,   nil,    20,    20,    20,    20,    20,    20,    20,   nil,
   nil,    20,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,    20,    20,    20,    20,    20,    20,    20,    20,   nil,
    20,    20,    20,   nil,    20,    20,    20,    20,    20,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,   nil,
   nil,    20,   nil,   nil,    20,    20,   nil,   nil,    20,   nil,
   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,    20,    20,
    20,    20,   nil,    20,    20,    20,    20,   nil,   nil,   nil,
   nil,    20,    20,   nil,   nil,   nil,    29,    29,    29,    20,
    29,    20,    20,    20,    29,    29,   nil,   nil,   nil,    29,
   nil,    29,    29,    29,    29,    29,    29,    29,   nil,   nil,
   nil,   nil,   nil,    29,    29,    29,    29,    29,    29,    29,
   nil,   nil,    29,   nil,   nil,   nil,   nil,   nil,   nil,    29,
   nil,   nil,    29,    29,    29,    29,    29,    29,    29,    29,
    29,    29,    29,    29,   nil,    29,    29,    29,    29,    29,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    29,
   nil,   nil,    29,   nil,   nil,    29,    29,   nil,   nil,    29,
   nil,    29,   nil,    29,   nil,    29,   nil,   nil,    29,   nil,
   nil,   nil,   nil,   nil,    29,   nil,   nil,   nil,   nil,    29,
    29,    29,    29,   nil,    29,    29,    29,    29,   nil,   nil,
   nil,   nil,    29,    29,   nil,   nil,   nil,    30,    30,    30,
    29,    30,    29,    29,    29,    30,    30,   nil,   nil,   nil,
    30,   nil,    30,    30,    30,    30,    30,    30,    30,   nil,
   nil,   nil,   nil,   nil,    30,    30,    30,    30,    30,    30,
    30,   nil,   nil,    30,   nil,   nil,   nil,   nil,   nil,   nil,
    30,   nil,   nil,    30,    30,    30,    30,    30,    30,    30,
    30,    30,    30,    30,    30,   nil,    30,    30,    30,    30,
    30,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    30,   nil,   nil,    30,   nil,   nil,    30,    30,   nil,   nil,
    30,   nil,    30,   nil,    30,   nil,    30,   nil,   nil,    30,
   nil,   nil,   nil,   nil,   nil,    30,   nil,   nil,   nil,   nil,
    30,    30,    30,    30,   nil,    30,    30,    30,    30,   nil,
   nil,   nil,   nil,    30,    30,   nil,   nil,   nil,    31,    31,
    31,    30,    31,    30,    30,    30,    31,    31,   nil,   nil,
   nil,    31,   nil,    31,    31,    31,    31,    31,    31,    31,
   nil,   nil,   nil,   nil,   nil,    31,    31,    31,    31,    31,
    31,    31,   nil,   nil,    31,   nil,   nil,   nil,   nil,   nil,
   nil,    31,   nil,   nil,    31,    31,    31,    31,    31,    31,
    31,    31,    31,    31,    31,    31,   nil,    31,    31,    31,
    31,    31,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    31,   nil,   nil,    31,   nil,   nil,    31,    31,   nil,
   nil,    31,   nil,    31,   nil,    31,   nil,    31,   nil,   nil,
    31,   nil,   nil,   nil,   nil,   nil,    31,   nil,   nil,   nil,
   nil,    31,    31,    31,    31,   nil,    31,    31,    31,    31,
   nil,   nil,   nil,   nil,    31,    31,   nil,   nil,   nil,    34,
    34,    34,    31,    34,    31,    31,    31,    34,    34,   nil,
   nil,   nil,    34,   nil,    34,    34,    34,    34,    34,    34,
    34,   nil,   nil,   nil,   nil,   nil,    34,    34,    34,    34,
    34,    34,    34,   nil,   nil,    34,   nil,   nil,   nil,   nil,
   nil,   nil,    34,   nil,   nil,    34,    34,    34,    34,    34,
    34,    34,    34,   nil,    34,    34,    34,   nil,    34,    34,
   nil,   nil,    34,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    34,   nil,   nil,    34,   nil,   nil,    34,    34,
   nil,   nil,    34,   nil,    34,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    34,    34,    34,    34,   nil,    34,    34,    34,
    34,   nil,   nil,   nil,   nil,    34,    34,   nil,   nil,   nil,
    35,    35,    35,    34,    35,    34,    34,    34,    35,    35,
   nil,   nil,   nil,    35,   nil,    35,    35,    35,    35,    35,
    35,    35,   nil,   nil,   nil,   nil,   nil,    35,    35,    35,
    35,    35,    35,    35,   nil,   nil,    35,   nil,   nil,   nil,
   nil,   nil,   nil,    35,   nil,   nil,    35,    35,    35,    35,
    35,    35,    35,    35,   nil,    35,    35,    35,   nil,    35,
    35,   nil,   nil,    35,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    35,   nil,   nil,    35,   nil,   nil,    35,
    35,   nil,   nil,    35,   nil,   nil,   915,   nil,   915,   915,
   915,   915,   915,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   915,   nil,    35,    35,    35,    35,   nil,    35,    35,
    35,    35,   nil,   nil,   nil,   nil,    35,    35,   nil,   nil,
   nil,    35,   nil,   915,    35,   nil,    35,    35,    35,    42,
    42,    42,   nil,    42,   915,   915,   nil,    42,    42,   915,
   nil,   nil,    42,   nil,    42,    42,    42,    42,    42,    42,
    42,   nil,   nil,   nil,   nil,   nil,    42,    42,    42,    42,
    42,    42,    42,   nil,   nil,    42,   nil,   nil,   nil,   nil,
   nil,   nil,    42,   nil,   nil,    42,    42,    42,    42,    42,
    42,    42,    42,   nil,    42,    42,    42,   nil,    42,    42,
    42,    42,    42,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    42,   nil,   nil,    42,   nil,   nil,    42,    42,
   nil,   nil,    42,   nil,   nil,   nil,   nil,   nil,    42,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    42,   nil,   nil,
   nil,   nil,    42,    42,    42,    42,   nil,    42,    42,    42,
    42,   nil,   nil,   nil,   nil,    42,    42,   nil,   nil,   nil,
    43,    43,    43,    42,    43,    42,    42,    42,    43,    43,
   nil,   nil,   nil,    43,   nil,    43,    43,    43,    43,    43,
    43,    43,   nil,   nil,   nil,   nil,   nil,    43,    43,    43,
    43,    43,    43,    43,   nil,   nil,    43,   nil,   nil,   nil,
   nil,   nil,   nil,    43,   nil,   nil,    43,    43,    43,    43,
    43,    43,    43,    43,   nil,    43,    43,    43,   nil,    43,
    43,    43,    43,    43,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    43,   nil,   nil,    43,   nil,   nil,    43,
    43,   nil,   nil,    43,   nil,   nil,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    43,   nil,
   nil,   nil,   nil,    43,    43,    43,    43,   nil,    43,    43,
    43,    43,   nil,   nil,   nil,   nil,    43,    43,   nil,   nil,
   nil,    45,    45,    45,    43,    45,    43,    43,    43,    45,
    45,   nil,   nil,   nil,    45,   nil,    45,    45,    45,    45,
    45,    45,    45,   nil,   nil,   nil,   nil,   nil,    45,    45,
    45,    45,    45,    45,    45,   nil,   nil,    45,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,    45,    45,    45,
    45,    45,    45,    45,    45,   nil,    45,    45,    45,   nil,
    45,    45,    45,    45,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    45,   nil,   nil,    45,   nil,   nil,
    45,    45,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
    45,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    45,
   nil,   nil,   nil,   nil,    45,    45,    45,    45,   nil,    45,
    45,    45,    45,   nil,   nil,   nil,   nil,    45,    45,   nil,
   nil,   nil,   nil,   nil,   nil,    45,   nil,    45,    45,    45,
    59,    59,    59,    59,    59,   nil,   nil,   nil,    59,    59,
   nil,   nil,   nil,    59,   nil,    59,    59,    59,    59,    59,
    59,    59,   nil,   nil,   nil,   nil,   nil,    59,    59,    59,
    59,    59,    59,    59,   nil,   nil,    59,   nil,   nil,   nil,
   nil,   nil,    59,    59,   nil,    59,    59,    59,    59,    59,
    59,    59,    59,    59,   nil,    59,    59,    59,   nil,    59,
    59,    59,    59,    59,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    59,   nil,   nil,    59,   nil,   nil,    59,
    59,   nil,   nil,    59,   nil,    59,   nil,   nil,   nil,    59,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    59,   nil,
   nil,   nil,   nil,    59,    59,    59,    59,   nil,    59,    59,
    59,    59,   nil,   nil,   nil,   nil,    59,    59,   nil,   nil,
   nil,    60,    60,    60,    59,    60,    59,    59,    59,    60,
    60,   nil,   nil,   nil,    60,   nil,    60,    60,    60,    60,
    60,    60,    60,   nil,   nil,   nil,   nil,   nil,    60,    60,
    60,    60,    60,    60,    60,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,   nil,    60,   nil,   nil,    60,    60,    60,
    60,    60,    60,    60,    60,    60,    60,    60,    60,   nil,
    60,    60,    60,    60,    60,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    60,   nil,   nil,    60,   nil,   nil,
    60,    60,   nil,   nil,    60,   nil,    60,   nil,   nil,   nil,
    60,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,    60,
   nil,   nil,   nil,   nil,    60,    60,    60,    60,   nil,    60,
    60,    60,    60,   nil,   nil,   nil,   nil,    60,    60,   nil,
   nil,   nil,    61,    61,    61,    60,    61,    60,    60,    60,
    61,    61,   nil,   nil,   nil,    61,   nil,    61,    61,    61,
    61,    61,    61,    61,   nil,   nil,   nil,   nil,   nil,    61,
    61,    61,    61,    61,    61,    61,   nil,   nil,    61,   nil,
   nil,   nil,   nil,   nil,   nil,    61,   nil,   nil,    61,    61,
    61,    61,    61,    61,    61,    61,    61,    61,    61,    61,
   nil,    61,    61,    61,    61,    61,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    61,   nil,   nil,    61,   nil,
   nil,    61,    61,   nil,   nil,    61,   nil,   nil,   nil,   nil,
   nil,    61,   nil,   nil,    61,   nil,   nil,   nil,   nil,   nil,
    61,   nil,   nil,   nil,   nil,    61,    61,    61,    61,   nil,
    61,    61,    61,    61,   nil,   nil,   nil,   nil,    61,    61,
   nil,   nil,   nil,    64,    64,    64,    61,    64,    61,    61,
    61,    64,    64,   nil,   nil,   nil,    64,   nil,    64,    64,
    64,    64,    64,    64,    64,   nil,   nil,   nil,   nil,   nil,
    64,    64,    64,    64,    64,    64,    64,   nil,   nil,    64,
   nil,   nil,   nil,   nil,   nil,   nil,    64,   nil,   nil,    64,
    64,    64,    64,    64,    64,    64,    64,   nil,    64,    64,
    64,   nil,    64,    64,    64,    64,    64,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    64,   nil,   nil,    64,
   nil,   nil,    64,    64,   nil,   nil,    64,   nil,   nil,   nil,
   nil,   nil,    64,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    64,   nil,   nil,   nil,   nil,    64,    64,    64,    64,
   nil,    64,    64,    64,    64,   nil,   nil,   nil,   nil,    64,
    64,   nil,   nil,   nil,    65,    65,    65,    64,    65,    64,
    64,    64,    65,    65,   nil,   nil,   nil,    65,   nil,    65,
    65,    65,    65,    65,    65,    65,   nil,   nil,   nil,   nil,
   nil,    65,    65,    65,    65,    65,    65,    65,   nil,   nil,
    65,   nil,   nil,   nil,   nil,   nil,   nil,    65,   nil,   nil,
    65,    65,    65,    65,    65,    65,    65,    65,   nil,    65,
    65,    65,   nil,    65,    65,    65,    65,    65,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    65,   nil,   nil,
    65,   nil,   nil,    65,    65,   nil,   nil,    65,   nil,   nil,
   nil,   nil,   nil,    65,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    65,   nil,   nil,   nil,   nil,    65,    65,    65,
    65,   nil,    65,    65,    65,    65,   nil,   nil,   nil,   nil,
    65,    65,   nil,   nil,   nil,    68,    68,    68,    65,    68,
    65,    65,    65,    68,    68,   nil,   nil,   nil,    68,   nil,
    68,    68,    68,    68,    68,    68,    68,   nil,   nil,   nil,
   nil,   nil,    68,    68,    68,    68,    68,    68,    68,   nil,
   nil,    68,   nil,   nil,   nil,   nil,   nil,   nil,    68,   nil,
   nil,    68,    68,    68,    68,    68,    68,    68,    68,   nil,
    68,    68,    68,   nil,    68,    68,    68,    68,    68,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    68,   nil,
   nil,    68,   nil,   nil,    68,    68,   nil,   nil,    68,   nil,
   nil,   nil,   nil,   nil,    68,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    68,   nil,   nil,   nil,   nil,    68,    68,
    68,    68,   nil,    68,    68,    68,    68,   nil,   nil,   nil,
   nil,    68,    68,    68,   nil,   nil,   nil,   nil,    68,    68,
   nil,    68,    68,    68,    69,    69,    69,   nil,    69,   nil,
   nil,   nil,    69,    69,   nil,   nil,   nil,    69,   nil,    69,
    69,    69,    69,    69,    69,    69,   nil,   nil,   nil,   nil,
   nil,    69,    69,    69,    69,    69,    69,    69,   nil,   nil,
    69,   nil,   nil,   nil,   nil,   nil,   nil,    69,   nil,   nil,
    69,    69,    69,    69,    69,    69,    69,    69,   nil,    69,
    69,    69,   nil,    69,    69,   nil,   nil,    69,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    69,   nil,   nil,
    69,   nil,   nil,    69,    69,   nil,   nil,    69,   nil,    69,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    69,    69,    69,
    69,   nil,    69,    69,    69,    69,   nil,   nil,   nil,   nil,
    69,    69,   nil,   nil,   nil,    70,    70,    70,    69,    70,
    69,    69,    69,    70,    70,   nil,   nil,   nil,    70,   nil,
    70,    70,    70,    70,    70,    70,    70,   nil,   nil,   nil,
   nil,   nil,    70,    70,    70,    70,    70,    70,    70,   nil,
   nil,    70,   nil,   nil,   nil,   nil,   nil,   nil,    70,   nil,
   nil,    70,    70,    70,    70,    70,    70,    70,    70,   nil,
    70,    70,    70,   nil,    70,    70,   nil,   nil,    70,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    70,   nil,   nil,    70,   nil,
   nil,    70,   nil,   nil,    70,    70,   nil,   nil,    70,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    70,    70,
    70,    70,   nil,    70,    70,    70,    70,   nil,   nil,   nil,
   nil,    70,    70,   nil,   nil,   nil,    71,    71,    71,    70,
    71,    70,    70,    70,    71,    71,   nil,   nil,   nil,    71,
   nil,    71,    71,    71,    71,    71,    71,    71,   nil,   nil,
   nil,   nil,   nil,    71,    71,    71,    71,    71,    71,    71,
   nil,   nil,    71,   nil,   nil,   nil,   nil,   nil,   nil,    71,
   nil,   nil,    71,    71,    71,    71,    71,    71,    71,    71,
   nil,    71,    71,    71,   nil,    71,    71,   nil,   nil,    71,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    71,
   nil,   nil,    71,   nil,   nil,    71,    71,   nil,   nil,    71,
   nil,   nil,   937,   nil,   937,   937,   937,   937,   937,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   937,   nil,    71,
    71,    71,    71,   nil,    71,    71,    71,    71,   nil,   nil,
   nil,   nil,    71,    71,   nil,   nil,   nil,   nil,   nil,   937,
    71,   nil,    71,    71,    71,   116,   116,   116,   116,   116,
   937,   937,   nil,   116,   116,   937,   nil,   nil,   116,   nil,
   116,   116,   116,   116,   116,   116,   116,   nil,   nil,   nil,
   nil,   nil,   116,   116,   116,   116,   116,   116,   116,   nil,
   nil,   116,   nil,   nil,   nil,   nil,   nil,   116,   116,   116,
   116,   116,   116,   116,   116,   116,   116,   116,   116,   nil,
   116,   116,   116,   nil,   116,   116,   116,   116,   116,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   116,   nil,
   nil,   116,   nil,   nil,   116,   116,   nil,   nil,   116,   nil,
   116,   nil,   nil,   nil,   116,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   116,   nil,   nil,   nil,   nil,   116,   116,
   116,   116,   nil,   116,   116,   116,   116,   nil,   nil,   nil,
   nil,   116,   116,   nil,   nil,   nil,   nil,   nil,   116,   116,
   nil,   116,   116,   116,   121,   121,   121,   nil,   121,   nil,
   nil,   nil,   121,   121,   nil,   nil,   nil,   121,   nil,   121,
   121,   121,   121,   121,   121,   121,   nil,   nil,   nil,   nil,
   nil,   121,   121,   121,   121,   121,   121,   121,   nil,   nil,
   121,   nil,   nil,   nil,   nil,   nil,   nil,   121,   nil,   nil,
   121,   121,   121,   121,   121,   121,   121,   121,   nil,   121,
   121,   121,   nil,   121,   121,   121,   121,   121,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   121,   nil,   nil,
   121,   nil,   nil,   121,   121,   nil,   nil,   121,   nil,   nil,
   nil,   nil,   nil,   121,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   121,   nil,   nil,   nil,   nil,   121,   121,   121,
   121,   nil,   121,   121,   121,   121,   nil,   nil,   nil,   nil,
   121,   121,   nil,   nil,   nil,   122,   122,   122,   121,   122,
   121,   121,   121,   122,   122,   nil,   nil,   nil,   122,   nil,
   122,   122,   122,   122,   122,   122,   122,   nil,   nil,   nil,
   nil,   nil,   122,   122,   122,   122,   122,   122,   122,   nil,
   nil,   122,   nil,   nil,   nil,   nil,   nil,   nil,   122,   nil,
   nil,   122,   122,   122,   122,   122,   122,   122,   122,   nil,
   122,   122,   122,   nil,   122,   122,   122,   122,   122,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   122,   nil,
   nil,   122,   nil,   nil,   122,   122,   nil,   nil,   122,   nil,
   nil,   nil,   nil,   nil,   122,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   122,   nil,   nil,   nil,   nil,   122,   122,
   122,   122,   nil,   122,   122,   122,   122,   nil,   nil,   nil,
   nil,   122,   122,   nil,   nil,   nil,   123,   123,   123,   122,
   123,   122,   122,   122,   123,   123,   nil,   nil,   nil,   123,
   nil,   123,   123,   123,   123,   123,   123,   123,   nil,   nil,
   nil,   nil,   nil,   123,   123,   123,   123,   123,   123,   123,
   nil,   nil,   123,   nil,   nil,   nil,   nil,   nil,   nil,   123,
   nil,   nil,   123,   123,   123,   123,   123,   123,   123,   123,
   nil,   123,   123,   123,   nil,   123,   123,   123,   123,   123,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   123,
   nil,   nil,   123,   nil,   nil,   123,   123,   nil,   nil,   123,
   nil,   nil,   nil,   nil,   nil,   123,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   123,   nil,   nil,   nil,   nil,   123,
   123,   123,   123,   nil,   123,   123,   123,   123,   nil,   nil,
   nil,   nil,   123,   123,   nil,   nil,   nil,   124,   124,   124,
   123,   124,   123,   123,   123,   124,   124,   nil,   nil,   nil,
   124,   nil,   124,   124,   124,   124,   124,   124,   124,   nil,
   nil,   nil,   nil,   nil,   124,   124,   124,   124,   124,   124,
   124,   nil,   nil,   124,   nil,   nil,   nil,   nil,   nil,   nil,
   124,   nil,   nil,   124,   124,   124,   124,   124,   124,   124,
   124,   nil,   124,   124,   124,   nil,   124,   124,   124,   124,
   124,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   124,   nil,   nil,   124,   nil,   nil,   124,   124,   nil,   nil,
   124,   nil,   nil,   nil,   nil,   nil,   124,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   124,   nil,   nil,   nil,   nil,
   124,   124,   124,   124,   nil,   124,   124,   124,   124,   nil,
   nil,   nil,   nil,   124,   124,   nil,   nil,   nil,   nil,   nil,
   nil,   124,   nil,   124,   124,   124,   125,   125,   125,   125,
   125,   nil,   nil,   nil,   125,   125,   nil,   nil,   nil,   125,
   nil,   125,   125,   125,   125,   125,   125,   125,   nil,   nil,
   nil,   nil,   nil,   125,   125,   125,   125,   125,   125,   125,
   nil,   nil,   125,   nil,   nil,   nil,   nil,   nil,   125,   125,
   nil,   125,   125,   125,   125,   125,   125,   125,   125,   125,
   nil,   125,   125,   125,   nil,   125,   125,   125,   125,   125,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   125,
   nil,   nil,   125,   nil,   nil,   125,   125,   nil,   nil,   125,
   nil,   125,   nil,   nil,   nil,   125,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   125,   nil,   nil,   nil,   nil,   125,
   125,   125,   125,   nil,   125,   125,   125,   125,   nil,   nil,
   nil,   nil,   125,   125,   nil,   nil,   nil,   213,   213,   213,
   125,   213,   125,   125,   125,   213,   213,   nil,   nil,   nil,
   213,   nil,   213,   213,   213,   213,   213,   213,   213,   nil,
   nil,   nil,   nil,   nil,   213,   213,   213,   213,   213,   213,
   213,   nil,   nil,   213,   nil,   nil,   nil,   nil,   nil,   nil,
   213,   nil,   nil,   213,   213,   213,   213,   213,   213,   213,
   213,   nil,   213,   213,   213,   nil,   213,   213,   213,   213,
   213,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   213,   nil,   nil,   213,   nil,   nil,   213,   213,   nil,   nil,
   213,   nil,   213,   nil,   nil,   nil,   213,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   213,   nil,   nil,   nil,   nil,
   213,   213,   213,   213,   nil,   213,   213,   213,   213,   nil,
   nil,   nil,   nil,   213,   213,   nil,   nil,   nil,   214,   214,
   214,   213,   214,   213,   213,   213,   214,   214,   nil,   nil,
   nil,   214,   nil,   214,   214,   214,   214,   214,   214,   214,
   nil,   nil,   nil,   nil,   nil,   214,   214,   214,   214,   214,
   214,   214,   nil,   nil,   214,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   214,   214,   214,   214,   214,   214,
   214,   214,   nil,   214,   214,   214,   nil,   214,   214,   214,
   214,   214,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   214,   nil,   nil,   214,   nil,   nil,   214,   214,   nil,
   nil,   214,   nil,   214,   nil,   nil,   nil,   214,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   214,   nil,   nil,   nil,
   nil,   214,   214,   214,   214,   nil,   214,   214,   214,   214,
   nil,   nil,   nil,   nil,   214,   214,   nil,   nil,   nil,   215,
   215,   215,   214,   215,   214,   214,   214,   215,   215,   nil,
   nil,   nil,   215,   nil,   215,   215,   215,   215,   215,   215,
   215,   nil,   nil,   nil,   nil,   nil,   215,   215,   215,   215,
   215,   215,   215,   nil,   nil,   215,   nil,   nil,   nil,   nil,
   nil,   nil,   215,   nil,   nil,   215,   215,   215,   215,   215,
   215,   215,   215,   nil,   215,   215,   215,   nil,   215,   215,
   215,   215,   215,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   215,   nil,   nil,   215,   nil,   nil,   215,   215,
   nil,   nil,   215,   nil,   nil,   nil,   nil,   nil,   215,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   215,   nil,   nil,
   nil,   nil,   215,   215,   215,   215,   nil,   215,   215,   215,
   215,   nil,   nil,   nil,   nil,   215,   215,   nil,   nil,   nil,
   216,   216,   216,   215,   216,   215,   215,   215,   216,   216,
   nil,   nil,   nil,   216,   nil,   216,   216,   216,   216,   216,
   216,   216,   nil,   nil,   nil,   nil,   nil,   216,   216,   216,
   216,   216,   216,   216,   nil,   nil,   216,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   216,   216,   216,   216,
   216,   216,   216,   216,   nil,   216,   216,   216,   nil,   216,
   216,   216,   216,   216,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,   nil,   216,   nil,   nil,   216,
   216,   nil,   nil,   216,   nil,   nil,   nil,   nil,   nil,   216,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,
   nil,   nil,   nil,   216,   216,   216,   216,   nil,   216,   216,
   216,   216,   nil,   nil,   nil,   nil,   216,   216,   nil,   nil,
   nil,   217,   217,   217,   216,   217,   216,   216,   216,   217,
   217,   nil,   nil,   nil,   217,   nil,   217,   217,   217,   217,
   217,   217,   217,   nil,   nil,   nil,   nil,   nil,   217,   217,
   217,   217,   217,   217,   217,   nil,   nil,   217,   nil,   nil,
   nil,   nil,   nil,   nil,   217,   nil,   nil,   217,   217,   217,
   217,   217,   217,   217,   217,   nil,   217,   217,   217,   nil,
   217,   217,   217,   217,   217,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   217,   nil,   nil,   217,   nil,   nil,
   217,   217,   nil,   nil,   217,   nil,   nil,   nil,   nil,   nil,
   217,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   217,
   nil,   nil,   nil,   nil,   217,   217,   217,   217,   nil,   217,
   217,   217,   217,   nil,   nil,   nil,   nil,   217,   217,   nil,
   nil,   nil,   218,   218,   218,   217,   218,   217,   217,   217,
   218,   218,   nil,   nil,   nil,   218,   nil,   218,   218,   218,
   218,   218,   218,   218,   nil,   nil,   nil,   nil,   nil,   218,
   218,   218,   218,   218,   218,   218,   nil,   nil,   218,   nil,
   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,   218,   218,
   218,   218,   218,   218,   218,   218,   218,   218,   218,   218,
   nil,   218,   218,   218,   218,   218,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   218,   nil,   nil,   218,   nil,
   nil,   218,   218,   nil,   nil,   218,   nil,   218,   nil,   218,
   nil,   218,   nil,   nil,   218,   nil,   nil,   nil,   nil,   nil,
   218,   nil,   nil,   nil,   nil,   218,   218,   218,   218,   nil,
   218,   218,   218,   218,   nil,   nil,   nil,   nil,   218,   218,
   nil,   nil,   nil,   223,   223,   223,   218,   223,   218,   218,
   218,   223,   223,   nil,   nil,   nil,   223,   nil,   223,   223,
   223,   223,   223,   223,   223,   nil,   nil,   nil,   nil,   nil,
   223,   223,   223,   223,   223,   223,   223,   nil,   nil,   223,
   nil,   nil,   nil,   nil,   nil,   nil,   223,   nil,   nil,   223,
   223,   223,   223,   223,   223,   223,   223,   nil,   223,   223,
   223,   nil,   223,   223,   223,   223,   223,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   223,   nil,   nil,   223,
   nil,   nil,   223,   223,   nil,   nil,   223,   nil,   nil,   nil,
   nil,   nil,   223,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   223,   nil,   nil,   nil,   nil,   223,   223,   223,   223,
   nil,   223,   223,   223,   223,   nil,   nil,   nil,   nil,   223,
   223,   nil,   nil,   nil,   224,   224,   224,   223,   224,   223,
   223,   223,   224,   224,   nil,   nil,   nil,   224,   nil,   224,
   224,   224,   224,   224,   224,   224,   nil,   nil,   nil,   nil,
   nil,   224,   224,   224,   224,   224,   224,   224,   nil,   nil,
   224,   nil,   nil,   nil,   nil,   nil,   nil,   224,   nil,   nil,
   224,   224,   224,   224,   224,   224,   224,   224,   nil,   224,
   224,   224,   nil,   224,   224,   224,   224,   224,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   224,   nil,   nil,
   224,   nil,   nil,   224,   224,   nil,   nil,   224,   nil,   nil,
   nil,   nil,   nil,   224,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   224,   nil,   nil,   nil,   nil,   224,   224,   224,
   224,   nil,   224,   224,   224,   224,   nil,   nil,   nil,   nil,
   224,   224,   nil,   nil,   nil,   225,   225,   225,   224,   225,
   224,   224,   224,   225,   225,   nil,   nil,   nil,   225,   nil,
   225,   225,   225,   225,   225,   225,   225,   nil,   nil,   nil,
   nil,   nil,   225,   225,   225,   225,   225,   225,   225,   nil,
   nil,   225,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   225,   225,   225,   225,   225,   225,   225,   225,   nil,
   225,   225,   225,   nil,   225,   225,   225,   225,   225,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   225,   nil,
   nil,   225,   nil,   nil,   225,   225,   nil,   nil,   225,   nil,
   nil,   nil,   nil,   nil,   225,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   225,   nil,   nil,   nil,   nil,   225,   225,
   225,   225,   nil,   225,   225,   225,   225,   nil,   nil,   nil,
   nil,   225,   225,   225,   nil,   nil,   236,   236,   236,   225,
   236,   225,   225,   225,   236,   236,   nil,   nil,   nil,   236,
   nil,   236,   236,   236,   236,   236,   236,   236,   nil,   nil,
   nil,   nil,   nil,   236,   236,   236,   236,   236,   236,   236,
   nil,   nil,   236,   nil,   nil,   nil,   nil,   nil,   nil,   236,
   nil,   nil,   236,   236,   236,   236,   236,   236,   236,   236,
   nil,   236,   236,   236,   nil,   236,   236,   236,   236,   236,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   236,
   nil,   nil,   236,   nil,   nil,   236,   236,   nil,   nil,   236,
   nil,   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   236,   nil,   nil,   nil,   nil,   236,
   236,   236,   236,   nil,   236,   236,   236,   236,   nil,   nil,
   nil,   nil,   236,   236,   nil,   nil,   nil,   239,   239,   239,
   236,   239,   236,   236,   236,   239,   239,   nil,   nil,   nil,
   239,   nil,   239,   239,   239,   239,   239,   239,   239,   nil,
   nil,   nil,   nil,   nil,   239,   239,   239,   239,   239,   239,
   239,   nil,   nil,   239,   nil,   nil,   nil,   nil,   nil,   nil,
   239,   nil,   nil,   239,   239,   239,   239,   239,   239,   239,
   239,   nil,   239,   239,   239,   nil,   239,   239,   239,   239,
   239,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   239,   nil,   nil,   239,   nil,   nil,   239,   239,   nil,   nil,
   239,   nil,   nil,   nil,   nil,   nil,   239,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   239,   nil,   nil,   nil,   nil,
   239,   239,   239,   239,   nil,   239,   239,   239,   239,   nil,
   nil,   nil,   nil,   239,   239,   nil,   nil,   nil,   240,   240,
   240,   239,   240,   239,   239,   239,   240,   240,   nil,   nil,
   nil,   240,   nil,   240,   240,   240,   240,   240,   240,   240,
   nil,   nil,   nil,   nil,   nil,   240,   240,   240,   240,   240,
   240,   240,   nil,   nil,   240,   nil,   nil,   nil,   nil,   nil,
   nil,   240,   nil,   nil,   240,   240,   240,   240,   240,   240,
   240,   240,   nil,   240,   240,   240,   nil,   240,   240,   240,
   240,   240,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   240,   nil,   nil,   240,   nil,   nil,   240,   240,   nil,
   nil,   240,   nil,   nil,   nil,   nil,   nil,   240,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   240,   nil,   nil,   nil,
   nil,   240,   240,   240,   240,   nil,   240,   240,   240,   240,
   nil,   nil,   nil,   nil,   240,   240,   nil,   nil,   nil,   241,
   241,   241,   240,   241,   240,   240,   240,   241,   241,   nil,
   nil,   nil,   241,   nil,   241,   241,   241,   241,   241,   241,
   241,   nil,   nil,   nil,   nil,   nil,   241,   241,   241,   241,
   241,   241,   241,   nil,   nil,   241,   nil,   nil,   nil,   nil,
   nil,   nil,   241,   nil,   nil,   241,   241,   241,   241,   241,
   241,   241,   241,   nil,   241,   241,   241,   nil,   241,   241,
   241,   241,   241,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   241,   nil,   nil,   241,   nil,   nil,   241,   241,
   nil,   nil,   241,   nil,   nil,   nil,   nil,   nil,   241,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   241,   nil,   nil,
   nil,   nil,   241,   241,   241,   241,   nil,   241,   241,   241,
   241,   nil,   nil,   nil,   nil,   241,   241,   nil,   nil,   nil,
   242,   242,   242,   241,   242,   241,   241,   241,   242,   242,
   nil,   nil,   nil,   242,   nil,   242,   242,   242,   242,   242,
   242,   242,   nil,   nil,   nil,   nil,   nil,   242,   242,   242,
   242,   242,   242,   242,   nil,   nil,   242,   nil,   nil,   nil,
   nil,   nil,   nil,   242,   nil,   nil,   242,   242,   242,   242,
   242,   242,   242,   242,   nil,   242,   242,   242,   nil,   242,
   242,   242,   242,   242,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   242,   nil,   nil,   242,   nil,   nil,   242,
   242,   nil,   nil,   242,   nil,   nil,   nil,   nil,   nil,   242,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   242,   nil,
   nil,   nil,   nil,   242,   242,   242,   242,   nil,   242,   242,
   242,   242,   nil,   nil,   nil,   nil,   242,   242,   nil,   nil,
   nil,   243,   243,   243,   242,   243,   242,   242,   242,   243,
   243,   nil,   nil,   nil,   243,   nil,   243,   243,   243,   243,
   243,   243,   243,   nil,   nil,   nil,   nil,   nil,   243,   243,
   243,   243,   243,   243,   243,   nil,   nil,   243,   nil,   nil,
   nil,   nil,   nil,   nil,   243,   nil,   nil,   243,   243,   243,
   243,   243,   243,   243,   243,   nil,   243,   243,   243,   nil,
   243,   243,   243,   243,   243,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   243,   nil,   nil,   243,   nil,   nil,
   243,   243,   nil,   nil,   243,   nil,   nil,   nil,   nil,   nil,
   243,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   243,
   nil,   nil,   nil,   nil,   243,   243,   243,   243,   nil,   243,
   243,   243,   243,   nil,   nil,   nil,   nil,   243,   243,   nil,
   nil,   nil,   244,   244,   244,   243,   244,   243,   243,   243,
   244,   244,   nil,   nil,   nil,   244,   nil,   244,   244,   244,
   244,   244,   244,   244,   nil,   nil,   nil,   nil,   nil,   244,
   244,   244,   244,   244,   244,   244,   nil,   nil,   244,   nil,
   nil,   nil,   nil,   nil,   nil,   244,   nil,   nil,   244,   244,
   244,   244,   244,   244,   244,   244,   nil,   244,   244,   244,
   nil,   244,   244,   244,   244,   244,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   244,   nil,   nil,   244,   nil,
   nil,   244,   244,   nil,   nil,   244,   nil,   nil,   nil,   nil,
   nil,   244,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   244,   nil,   nil,   nil,   nil,   244,   244,   244,   244,   nil,
   244,   244,   244,   244,   nil,   nil,   nil,   nil,   244,   244,
   nil,   nil,   nil,   245,   245,   245,   244,   245,   244,   244,
   244,   245,   245,   nil,   nil,   nil,   245,   nil,   245,   245,
   245,   245,   245,   245,   245,   nil,   nil,   nil,   nil,   nil,
   245,   245,   245,   245,   245,   245,   245,   nil,   nil,   245,
   nil,   nil,   nil,   nil,   nil,   nil,   245,   nil,   nil,   245,
   245,   245,   245,   245,   245,   245,   245,   nil,   245,   245,
   245,   nil,   245,   245,   245,   245,   245,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   245,   nil,   nil,   245,
   nil,   nil,   245,   245,   nil,   nil,   245,   nil,   nil,   nil,
   nil,   nil,   245,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   245,   nil,   nil,   nil,   nil,   245,   245,   245,   245,
   nil,   245,   245,   245,   245,   nil,   nil,   nil,   nil,   245,
   245,   nil,   nil,   nil,   246,   246,   246,   245,   246,   245,
   245,   245,   246,   246,   nil,   nil,   nil,   246,   nil,   246,
   246,   246,   246,   246,   246,   246,   nil,   nil,   nil,   nil,
   nil,   246,   246,   246,   246,   246,   246,   246,   nil,   nil,
   246,   nil,   nil,   nil,   nil,   nil,   nil,   246,   nil,   nil,
   246,   246,   246,   246,   246,   246,   246,   246,   nil,   246,
   246,   246,   nil,   246,   246,   246,   246,   246,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   246,   nil,   nil,
   246,   nil,   nil,   246,   246,   nil,   nil,   246,   nil,   nil,
   nil,   nil,   nil,   246,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   246,   nil,   nil,   nil,   nil,   246,   246,   246,
   246,   nil,   246,   246,   246,   246,   nil,   nil,   nil,   nil,
   246,   246,   nil,   nil,   nil,   247,   247,   247,   246,   247,
   246,   246,   246,   247,   247,   nil,   nil,   nil,   247,   nil,
   247,   247,   247,   247,   247,   247,   247,   nil,   nil,   nil,
   nil,   nil,   247,   247,   247,   247,   247,   247,   247,   nil,
   nil,   247,   nil,   nil,   nil,   nil,   nil,   nil,   247,   nil,
   nil,   247,   247,   247,   247,   247,   247,   247,   247,   nil,
   247,   247,   247,   nil,   247,   247,   247,   247,   247,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   247,   nil,
   nil,   247,   nil,   nil,   247,   247,   nil,   nil,   247,   nil,
   nil,   nil,   nil,   nil,   247,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   247,   nil,   nil,   nil,   nil,   247,   247,
   247,   247,   nil,   247,   247,   247,   247,   nil,   nil,   nil,
   nil,   247,   247,   nil,   nil,   nil,   248,   248,   248,   247,
   248,   247,   247,   247,   248,   248,   nil,   nil,   nil,   248,
   nil,   248,   248,   248,   248,   248,   248,   248,   nil,   nil,
   nil,   nil,   nil,   248,   248,   248,   248,   248,   248,   248,
   nil,   nil,   248,   nil,   nil,   nil,   nil,   nil,   nil,   248,
   nil,   nil,   248,   248,   248,   248,   248,   248,   248,   248,
   nil,   248,   248,   248,   nil,   248,   248,   248,   248,   248,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   248,
   nil,   nil,   248,   nil,   nil,   248,   248,   nil,   nil,   248,
   nil,   nil,   nil,   nil,   nil,   248,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   248,   nil,   nil,   nil,   nil,   248,
   248,   248,   248,   nil,   248,   248,   248,   248,   nil,   nil,
   nil,   nil,   248,   248,   nil,   nil,   nil,   249,   249,   249,
   248,   249,   248,   248,   248,   249,   249,   nil,   nil,   nil,
   249,   nil,   249,   249,   249,   249,   249,   249,   249,   nil,
   nil,   nil,   nil,   nil,   249,   249,   249,   249,   249,   249,
   249,   nil,   nil,   249,   nil,   nil,   nil,   nil,   nil,   nil,
   249,   nil,   nil,   249,   249,   249,   249,   249,   249,   249,
   249,   nil,   249,   249,   249,   nil,   249,   249,   249,   249,
   249,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   249,   nil,   nil,   249,   nil,   nil,   249,   249,   nil,   nil,
   249,   nil,   nil,   nil,   nil,   nil,   249,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   249,   nil,   nil,   nil,   nil,
   249,   249,   249,   249,   nil,   249,   249,   249,   249,   nil,
   nil,   nil,   nil,   249,   249,   nil,   nil,   nil,   250,   250,
   250,   249,   250,   249,   249,   249,   250,   250,   nil,   nil,
   nil,   250,   nil,   250,   250,   250,   250,   250,   250,   250,
   nil,   nil,   nil,   nil,   nil,   250,   250,   250,   250,   250,
   250,   250,   nil,   nil,   250,   nil,   nil,   nil,   nil,   nil,
   nil,   250,   nil,   nil,   250,   250,   250,   250,   250,   250,
   250,   250,   nil,   250,   250,   250,   nil,   250,   250,   250,
   250,   250,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   250,   nil,   nil,   250,   nil,   nil,   250,   250,   nil,
   nil,   250,   nil,   nil,   nil,   nil,   nil,   250,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   250,   nil,   nil,   nil,
   nil,   250,   250,   250,   250,   nil,   250,   250,   250,   250,
   nil,   nil,   nil,   nil,   250,   250,   nil,   nil,   nil,   251,
   251,   251,   250,   251,   250,   250,   250,   251,   251,   nil,
   nil,   nil,   251,   nil,   251,   251,   251,   251,   251,   251,
   251,   nil,   nil,   nil,   nil,   nil,   251,   251,   251,   251,
   251,   251,   251,   nil,   nil,   251,   nil,   nil,   nil,   nil,
   nil,   nil,   251,   nil,   nil,   251,   251,   251,   251,   251,
   251,   251,   251,   nil,   251,   251,   251,   nil,   251,   251,
   251,   251,   251,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   251,   nil,   nil,   251,   nil,   nil,   251,   251,
   nil,   nil,   251,   nil,   nil,   nil,   nil,   nil,   251,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   251,   nil,   nil,
   nil,   nil,   251,   251,   251,   251,   nil,   251,   251,   251,
   251,   nil,   nil,   nil,   nil,   251,   251,   nil,   nil,   nil,
   252,   252,   252,   251,   252,   251,   251,   251,   252,   252,
   nil,   nil,   nil,   252,   nil,   252,   252,   252,   252,   252,
   252,   252,   nil,   nil,   nil,   nil,   nil,   252,   252,   252,
   252,   252,   252,   252,   nil,   nil,   252,   nil,   nil,   nil,
   nil,   nil,   nil,   252,   nil,   nil,   252,   252,   252,   252,
   252,   252,   252,   252,   nil,   252,   252,   252,   nil,   252,
   252,   252,   252,   252,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   252,   nil,   nil,   252,   nil,   nil,   252,
   252,   nil,   nil,   252,   nil,   nil,   nil,   nil,   nil,   252,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   252,   nil,
   nil,   nil,   nil,   252,   252,   252,   252,   nil,   252,   252,
   252,   252,   nil,   nil,   nil,   nil,   252,   252,   nil,   nil,
   nil,   253,   253,   253,   252,   253,   252,   252,   252,   253,
   253,   nil,   nil,   nil,   253,   nil,   253,   253,   253,   253,
   253,   253,   253,   nil,   nil,   nil,   nil,   nil,   253,   253,
   253,   253,   253,   253,   253,   nil,   nil,   253,   nil,   nil,
   nil,   nil,   nil,   nil,   253,   nil,   nil,   253,   253,   253,
   253,   253,   253,   253,   253,   nil,   253,   253,   253,   nil,
   253,   253,   253,   253,   253,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   253,   nil,   nil,   253,   nil,   nil,
   253,   253,   nil,   nil,   253,   nil,   nil,   nil,   nil,   nil,
   253,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   253,
   nil,   nil,   nil,   nil,   253,   253,   253,   253,   nil,   253,
   253,   253,   253,   nil,   nil,   nil,   nil,   253,   253,   nil,
   nil,   nil,   254,   254,   254,   253,   254,   253,   253,   253,
   254,   254,   nil,   nil,   nil,   254,   nil,   254,   254,   254,
   254,   254,   254,   254,   nil,   nil,   nil,   nil,   nil,   254,
   254,   254,   254,   254,   254,   254,   nil,   nil,   254,   nil,
   nil,   nil,   nil,   nil,   nil,   254,   nil,   nil,   254,   254,
   254,   254,   254,   254,   254,   254,   nil,   254,   254,   254,
   nil,   254,   254,   254,   254,   254,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   254,   nil,   nil,   254,   nil,
   nil,   254,   254,   nil,   nil,   254,   nil,   nil,   nil,   nil,
   nil,   254,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   254,   nil,   nil,   nil,   nil,   254,   254,   254,   254,   nil,
   254,   254,   254,   254,   nil,   nil,   nil,   nil,   254,   254,
   nil,   nil,   nil,   255,   255,   255,   254,   255,   254,   254,
   254,   255,   255,   nil,   nil,   nil,   255,   nil,   255,   255,
   255,   255,   255,   255,   255,   nil,   nil,   nil,   nil,   nil,
   255,   255,   255,   255,   255,   255,   255,   nil,   nil,   255,
   nil,   nil,   nil,   nil,   nil,   nil,   255,   nil,   nil,   255,
   255,   255,   255,   255,   255,   255,   255,   nil,   255,   255,
   255,   nil,   255,   255,   255,   255,   255,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   255,   nil,   nil,   255,
   nil,   nil,   255,   255,   nil,   nil,   255,   nil,   nil,   nil,
   nil,   nil,   255,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   255,   nil,   nil,   nil,   nil,   255,   255,   255,   255,
   nil,   255,   255,   255,   255,   nil,   nil,   nil,   nil,   255,
   255,   nil,   nil,   nil,   256,   256,   256,   255,   256,   255,
   255,   255,   256,   256,   nil,   nil,   nil,   256,   nil,   256,
   256,   256,   256,   256,   256,   256,   nil,   nil,   nil,   nil,
   nil,   256,   256,   256,   256,   256,   256,   256,   nil,   nil,
   256,   nil,   nil,   nil,   nil,   nil,   nil,   256,   nil,   nil,
   256,   256,   256,   256,   256,   256,   256,   256,   nil,   256,
   256,   256,   nil,   256,   256,   256,   256,   256,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   256,   nil,   nil,
   256,   nil,   nil,   256,   256,   nil,   nil,   256,   nil,   nil,
   nil,   nil,   nil,   256,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   256,   nil,   nil,   nil,   nil,   256,   256,   256,
   256,   nil,   256,   256,   256,   256,   nil,   nil,   nil,   nil,
   256,   256,   nil,   nil,   nil,   257,   257,   257,   256,   257,
   256,   256,   256,   257,   257,   nil,   nil,   nil,   257,   nil,
   257,   257,   257,   257,   257,   257,   257,   nil,   nil,   nil,
   nil,   nil,   257,   257,   257,   257,   257,   257,   257,   nil,
   nil,   257,   nil,   nil,   nil,   nil,   nil,   nil,   257,   nil,
   nil,   257,   257,   257,   257,   257,   257,   257,   257,   nil,
   257,   257,   257,   nil,   257,   257,   257,   257,   257,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   257,   nil,
   nil,   257,   nil,   nil,   257,   257,   nil,   nil,   257,   nil,
   nil,   nil,   nil,   nil,   257,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   257,   nil,   nil,   nil,   nil,   257,   257,
   257,   257,   nil,   257,   257,   257,   257,   nil,   nil,   nil,
   nil,   257,   257,   nil,   nil,   nil,   258,   258,   258,   257,
   258,   257,   257,   257,   258,   258,   nil,   nil,   nil,   258,
   nil,   258,   258,   258,   258,   258,   258,   258,   nil,   nil,
   nil,   nil,   nil,   258,   258,   258,   258,   258,   258,   258,
   nil,   nil,   258,   nil,   nil,   nil,   nil,   nil,   nil,   258,
   nil,   nil,   258,   258,   258,   258,   258,   258,   258,   258,
   nil,   258,   258,   258,   nil,   258,   258,   258,   258,   258,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   258,
   nil,   nil,   258,   nil,   nil,   258,   258,   nil,   nil,   258,
   nil,   nil,   nil,   nil,   nil,   258,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   258,   nil,   nil,   nil,   nil,   258,
   258,   258,   258,   nil,   258,   258,   258,   258,   nil,   nil,
   nil,   nil,   258,   258,   nil,   nil,   nil,   259,   259,   259,
   258,   259,   258,   258,   258,   259,   259,   nil,   nil,   nil,
   259,   nil,   259,   259,   259,   259,   259,   259,   259,   nil,
   nil,   nil,   nil,   nil,   259,   259,   259,   259,   259,   259,
   259,   nil,   nil,   259,   nil,   nil,   nil,   nil,   nil,   nil,
   259,   nil,   nil,   259,   259,   259,   259,   259,   259,   259,
   259,   nil,   259,   259,   259,   nil,   259,   259,   259,   259,
   259,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   259,   nil,   nil,   259,   nil,   nil,   259,   259,   nil,   nil,
   259,   nil,   nil,   nil,   nil,   nil,   259,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   259,   nil,   nil,   nil,   nil,
   259,   259,   259,   259,   nil,   259,   259,   259,   259,   nil,
   nil,   nil,   nil,   259,   259,   nil,   nil,   nil,   260,   260,
   260,   259,   260,   259,   259,   259,   260,   260,   nil,   nil,
   nil,   260,   nil,   260,   260,   260,   260,   260,   260,   260,
   nil,   nil,   nil,   nil,   nil,   260,   260,   260,   260,   260,
   260,   260,   nil,   nil,   260,   nil,   nil,   nil,   nil,   nil,
   nil,   260,   nil,   nil,   260,   260,   260,   260,   260,   260,
   260,   260,   nil,   260,   260,   260,   nil,   260,   260,   260,
   260,   260,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   260,   nil,   nil,   260,   nil,   nil,   260,   260,   nil,
   nil,   260,   nil,   nil,   nil,   nil,   nil,   260,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   260,   nil,   nil,   nil,
   nil,   260,   260,   260,   260,   nil,   260,   260,   260,   260,
   nil,   nil,   nil,   nil,   260,   260,   nil,   nil,   nil,   265,
   265,   265,   260,   265,   260,   260,   260,   265,   265,   nil,
   nil,   nil,   265,   nil,   265,   265,   265,   265,   265,   265,
   265,   nil,   nil,   nil,   nil,   nil,   265,   265,   265,   265,
   265,   265,   265,   nil,   nil,   265,   nil,   nil,   nil,   nil,
   nil,   nil,   265,   nil,   nil,   265,   265,   265,   265,   265,
   265,   265,   265,   nil,   265,   265,   265,   nil,   265,   265,
   265,   265,   265,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   265,   nil,   nil,   265,   nil,   nil,   265,   265,
   nil,   nil,   265,   nil,   nil,   nil,   nil,   nil,   265,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   265,   nil,   nil,
   nil,   nil,   265,   265,   265,   265,   nil,   265,   265,   265,
   265,   nil,   nil,   nil,   nil,   265,   265,   nil,   nil,   nil,
   272,   272,   272,   265,   272,   265,   265,   265,   272,   272,
   nil,   nil,   nil,   272,   nil,   272,   272,   272,   272,   272,
   272,   272,   nil,   nil,   nil,   nil,   nil,   272,   272,   272,
   272,   272,   272,   272,   nil,   nil,   272,   nil,   nil,   nil,
   nil,   nil,   nil,   272,   nil,   nil,   272,   272,   272,   272,
   272,   272,   272,   272,   272,   272,   272,   272,   nil,   272,
   272,   272,   272,   272,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   272,   nil,   nil,   272,   nil,   nil,   272,
   272,   nil,   nil,   272,   nil,   272,   nil,   272,   nil,   272,
   nil,   nil,   272,   nil,   nil,   nil,   nil,   nil,   272,   nil,
   nil,   nil,   nil,   272,   272,   272,   272,   nil,   272,   272,
   272,   272,   nil,   nil,   nil,   nil,   272,   272,   nil,   nil,
   nil,   273,   273,   273,   272,   273,   272,   272,   272,   273,
   273,   nil,   nil,   nil,   273,   nil,   273,   273,   273,   273,
   273,   273,   273,   nil,   nil,   nil,   nil,   nil,   273,   273,
   273,   273,   273,   273,   273,   nil,   nil,   273,   nil,   nil,
   nil,   nil,   nil,   nil,   273,   nil,   nil,   273,   273,   273,
   273,   273,   273,   273,   273,   273,   273,   273,   273,   nil,
   273,   273,   273,   273,   273,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   273,   nil,   nil,   273,   nil,   nil,
   273,   273,   nil,   nil,   273,   nil,   273,   nil,   273,   nil,
   273,   nil,   nil,   273,   nil,   nil,   nil,   nil,   nil,   273,
   nil,   nil,   nil,   nil,   273,   273,   273,   273,   nil,   273,
   273,   273,   273,   nil,   nil,   nil,   nil,   273,   273,   nil,
   nil,   nil,   281,   281,   281,   273,   281,   273,   273,   273,
   281,   281,   nil,   nil,   nil,   281,   nil,   281,   281,   281,
   281,   281,   281,   281,   nil,   nil,   nil,   nil,   nil,   281,
   281,   281,   281,   281,   281,   281,   nil,   nil,   281,   nil,
   nil,   nil,   nil,   nil,   nil,   281,   nil,   nil,   281,   281,
   281,   281,   281,   281,   281,   281,   281,   281,   281,   281,
   nil,   281,   281,   281,   281,   281,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   281,   nil,   nil,   281,   nil,
   nil,   281,   281,   nil,   nil,   281,   nil,   281,   nil,   281,
   nil,   281,   nil,   nil,   281,   nil,   nil,   nil,   nil,   nil,
   281,   nil,   nil,   nil,   nil,   281,   281,   281,   281,   nil,
   281,   281,   281,   281,   nil,   nil,   nil,   nil,   281,   281,
   281,   nil,   nil,   288,   288,   288,   281,   288,   281,   281,
   281,   288,   288,   nil,   nil,   nil,   288,   nil,   288,   288,
   288,   288,   288,   288,   288,   nil,   nil,   nil,   nil,   nil,
   288,   288,   288,   288,   288,   288,   288,   nil,   nil,   288,
   nil,   nil,   nil,   nil,   nil,   nil,   288,   nil,   nil,   288,
   288,   288,   288,   288,   288,   288,   288,   nil,   288,   288,
   288,   nil,   288,   288,   288,   288,   288,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   288,   nil,   nil,   288,
   nil,   nil,   288,   288,   nil,   nil,   288,   nil,   nil,   nil,
   nil,   nil,   288,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   288,   nil,   nil,   nil,   nil,   288,   288,   288,   288,
   nil,   288,   288,   288,   288,   nil,   nil,   nil,   nil,   288,
   288,   nil,   nil,   nil,   290,   290,   290,   288,   290,   288,
   288,   288,   290,   290,   nil,   nil,   nil,   290,   nil,   290,
   290,   290,   290,   290,   290,   290,   nil,   nil,   nil,   nil,
   nil,   290,   290,   290,   290,   290,   290,   290,   nil,   nil,
   290,   nil,   nil,   nil,   nil,   nil,   nil,   290,   nil,   nil,
   290,   290,   290,   290,   290,   290,   290,   290,   nil,   290,
   290,   290,   nil,   290,   290,   290,   290,   290,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   290,   nil,   nil,
   290,   nil,   nil,   290,   290,   nil,   nil,   290,   nil,   nil,
   nil,   nil,   nil,   290,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   290,   nil,   nil,   nil,   nil,   290,   290,   290,
   290,   nil,   290,   290,   290,   290,   nil,   nil,   nil,   nil,
   290,   290,   nil,   nil,   nil,   293,   293,   293,   290,   293,
   290,   290,   290,   293,   293,   nil,   nil,   nil,   293,   nil,
   293,   293,   293,   293,   293,   293,   293,   nil,   nil,   nil,
   nil,   nil,   293,   293,   293,   293,   293,   293,   293,   nil,
   nil,   293,   nil,   nil,   nil,   nil,   nil,   nil,   293,   nil,
   nil,   293,   293,   293,   293,   293,   293,   293,   293,   nil,
   293,   293,   293,   nil,   293,   293,   293,   293,   293,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   293,   nil,
   nil,   293,   nil,   nil,   293,   293,   nil,   nil,   293,   nil,
   nil,   nil,   nil,   nil,   293,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   293,   nil,   nil,   nil,   nil,   293,   293,
   293,   293,   nil,   293,   293,   293,   293,   nil,   nil,   nil,
   nil,   293,   293,   nil,   nil,   nil,   294,   294,   294,   293,
   294,   293,   293,   293,   294,   294,   nil,   nil,   nil,   294,
   nil,   294,   294,   294,   294,   294,   294,   294,   nil,   nil,
   nil,   nil,   nil,   294,   294,   294,   294,   294,   294,   294,
   nil,   nil,   294,   nil,   nil,   nil,   nil,   nil,   nil,   294,
   nil,   nil,   294,   294,   294,   294,   294,   294,   294,   294,
   nil,   294,   294,   294,   nil,   294,   294,   294,   294,   294,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   294,
   nil,   nil,   294,   nil,   nil,   294,   294,   nil,   nil,   294,
   nil,   nil,   nil,   nil,   nil,   294,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   294,   nil,   nil,   nil,   nil,   294,
   294,   294,   294,   nil,   294,   294,   294,   294,   nil,   nil,
   nil,   nil,   294,   294,   nil,   nil,   nil,   nil,   nil,   nil,
   294,   nil,   294,   294,   294,   299,   299,   299,   299,   299,
   nil,   nil,   nil,   299,   299,   nil,   nil,   nil,   299,   nil,
   299,   299,   299,   299,   299,   299,   299,   nil,   nil,   nil,
   nil,   nil,   299,   299,   299,   299,   299,   299,   299,   nil,
   nil,   299,   nil,   nil,   nil,   nil,   nil,   299,   299,   nil,
   299,   299,   299,   299,   299,   299,   299,   299,   299,   nil,
   299,   299,   299,   nil,   299,   299,   299,   299,   299,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   299,   nil,
   nil,   299,   nil,   nil,   299,   299,   nil,   nil,   299,   nil,
   299,   nil,   nil,   nil,   299,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   299,   nil,   nil,   nil,   nil,   299,   299,
   299,   299,   nil,   299,   299,   299,   299,   nil,   nil,   nil,
   nil,   299,   299,   nil,   nil,   nil,   307,   307,   307,   299,
   307,   299,   299,   299,   307,   307,   nil,   nil,   nil,   307,
   nil,   307,   307,   307,   307,   307,   307,   307,   nil,   nil,
   nil,   nil,   nil,   307,   307,   307,   307,   307,   307,   307,
   nil,   nil,   307,   nil,   nil,   nil,   nil,   nil,   nil,   307,
   nil,   nil,   307,   307,   307,   307,   307,   307,   307,   307,
   nil,   307,   307,   307,   nil,   307,   307,   nil,   nil,   307,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   307,
   nil,   nil,   307,   nil,   nil,   307,   307,   nil,   nil,   307,
   nil,   nil,   939,   nil,   939,   939,   939,   939,   939,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   939,   nil,   307,
   307,   307,   307,   nil,   307,   307,   307,   307,   nil,   nil,
   nil,   nil,   307,   307,   nil,   nil,   nil,   307,   nil,   939,
   307,   nil,   307,   307,   307,   324,   324,   324,   nil,   324,
   939,   939,   nil,   324,   324,   939,   nil,   nil,   324,   nil,
   324,   324,   324,   324,   324,   324,   324,   nil,   nil,   nil,
   nil,   nil,   324,   324,   324,   324,   324,   324,   324,   nil,
   nil,   324,   nil,   nil,   nil,   nil,   nil,   nil,   324,   nil,
   nil,   324,   324,   324,   324,   324,   324,   324,   324,   nil,
   324,   324,   324,   nil,   324,   324,   nil,   nil,   324,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   324,   nil,
   nil,   324,   nil,   nil,   324,   324,   nil,   nil,   324,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   324,   324,
   324,   324,   nil,   324,   324,   324,   324,   nil,   nil,   nil,
   nil,   324,   324,   nil,   nil,   nil,   331,   331,   331,   324,
   331,   324,   324,   324,   331,   331,   nil,   nil,   nil,   331,
   nil,   331,   331,   331,   331,   331,   331,   331,   nil,   nil,
   nil,   nil,   nil,   331,   331,   331,   331,   331,   331,   331,
   nil,   nil,   331,   nil,   nil,   nil,   nil,   nil,   nil,   331,
   nil,   nil,   331,   331,   331,   331,   331,   331,   331,   331,
   nil,   331,   331,   331,   nil,   331,   331,   331,   331,   331,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   331,
   nil,   nil,   331,   nil,   nil,   331,   331,   nil,   nil,   331,
   nil,   nil,   nil,   nil,   nil,   331,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   331,   nil,   nil,   nil,   nil,   331,
   331,   331,   331,   nil,   331,   331,   331,   331,   nil,   nil,
   nil,   nil,   331,   331,   nil,   nil,   nil,   333,   333,   333,
   331,   333,   331,   331,   331,   333,   333,   nil,   nil,   nil,
   333,   nil,   333,   333,   333,   333,   333,   333,   333,   nil,
   nil,   nil,   nil,   nil,   333,   333,   333,   333,   333,   333,
   333,   nil,   nil,   333,   nil,   nil,   nil,   nil,   nil,   nil,
   333,   nil,   nil,   333,   333,   333,   333,   333,   333,   333,
   333,   nil,   333,   333,   333,   nil,   333,   333,   333,   333,
   333,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   333,   nil,   nil,   333,   333,   nil,   333,   333,   nil,   nil,
   333,   nil,   nil,   nil,   nil,   nil,   333,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   333,   nil,   nil,   nil,   nil,
   333,   333,   333,   333,   nil,   333,   333,   333,   333,   nil,
   nil,   nil,   nil,   333,   333,   nil,   nil,   nil,   349,   349,
   349,   333,   349,   333,   333,   333,   349,   349,   nil,   nil,
   nil,   349,   nil,   349,   349,   349,   349,   349,   349,   349,
   nil,   nil,   nil,   nil,   nil,   349,   349,   349,   349,   349,
   349,   349,   nil,   nil,   349,   nil,   nil,   nil,   nil,   nil,
   nil,   349,   nil,   nil,   349,   349,   349,   349,   349,   349,
   349,   349,   nil,   349,   349,   349,   nil,   349,   349,   349,
   349,   349,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   349,   nil,   nil,   349,   nil,   nil,   349,   349,   nil,
   nil,   349,   nil,   nil,   nil,   nil,   nil,   349,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   349,   nil,   nil,   nil,
   nil,   349,   349,   349,   349,   nil,   349,   349,   349,   349,
   nil,   nil,   nil,   nil,   349,   349,   nil,   nil,   nil,   370,
   370,   370,   349,   370,   349,   349,   349,   370,   370,   nil,
   nil,   nil,   370,   nil,   370,   370,   370,   370,   370,   370,
   370,   nil,   nil,   nil,   nil,   nil,   370,   370,   370,   370,
   370,   370,   370,   nil,   nil,   370,   nil,   nil,   nil,   nil,
   nil,   nil,   370,   nil,   nil,   370,   370,   370,   370,   370,
   370,   370,   370,   nil,   370,   370,   370,   nil,   370,   370,
   370,   370,   370,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   370,   nil,   nil,   370,   nil,   nil,   370,   370,
   nil,   nil,   370,   nil,   nil,   nil,   nil,   nil,   370,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   370,   nil,   nil,
   nil,   nil,   370,   370,   370,   370,   nil,   370,   370,   370,
   370,   nil,   nil,   nil,   nil,   370,   370,   nil,   nil,   nil,
   386,   386,   386,   370,   386,   370,   370,   370,   386,   386,
   nil,   nil,   nil,   386,   nil,   386,   386,   386,   386,   386,
   386,   386,   nil,   nil,   nil,   nil,   nil,   386,   386,   386,
   386,   386,   386,   386,   nil,   nil,   386,   nil,   nil,   nil,
   nil,   nil,   nil,   386,   nil,   nil,   386,   386,   386,   386,
   386,   386,   386,   386,   nil,   386,   386,   386,   nil,   386,
   386,   386,   386,   386,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   386,   nil,   nil,   386,   nil,   nil,   386,
   386,   nil,   nil,   386,   nil,   nil,   nil,   nil,   nil,   386,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   386,   nil,
   nil,   nil,   nil,   386,   386,   386,   386,   nil,   386,   386,
   386,   386,   nil,   nil,   nil,   nil,   386,   386,   nil,   nil,
   nil,   414,   414,   414,   386,   414,   386,   386,   386,   414,
   414,   nil,   nil,   nil,   414,   nil,   414,   414,   414,   414,
   414,   414,   414,   nil,   nil,   nil,   nil,   nil,   414,   414,
   414,   414,   414,   414,   414,   nil,   nil,   414,   nil,   nil,
   nil,   nil,   nil,   nil,   414,   nil,   nil,   414,   414,   414,
   414,   414,   414,   414,   414,   nil,   414,   414,   414,   nil,
   414,   414,   414,   414,   414,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   414,   nil,   nil,   414,   nil,   nil,
   414,   414,   nil,   nil,   414,   nil,   nil,   nil,   nil,   nil,
   414,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   414,
   nil,   nil,   nil,   nil,   414,   414,   414,   414,   nil,   414,
   414,   414,   414,   nil,   nil,   nil,   nil,   414,   414,   nil,
   nil,   nil,   457,   457,   457,   414,   457,   414,   414,   414,
   457,   457,   nil,   nil,   nil,   457,   nil,   457,   457,   457,
   457,   457,   457,   457,   nil,   nil,   nil,   nil,   nil,   457,
   457,   457,   457,   457,   457,   457,   nil,   nil,   457,   nil,
   nil,   nil,   nil,   nil,   nil,   457,   nil,   nil,   457,   457,
   457,   457,   457,   457,   457,   457,   457,   457,   457,   457,
   nil,   457,   457,   457,   457,   457,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   457,   nil,   nil,   457,   nil,
   nil,   457,   457,   nil,   nil,   457,   nil,   457,   nil,   457,
   nil,   457,   nil,   nil,   457,   nil,   nil,   nil,   nil,   nil,
   457,   nil,   nil,   nil,   nil,   457,   457,   457,   457,   nil,
   457,   457,   457,   457,   nil,   nil,   nil,   nil,   457,   457,
   nil,   nil,   nil,   459,   459,   459,   457,   459,   457,   457,
   457,   459,   459,   nil,   nil,   nil,   459,   nil,   459,   459,
   459,   459,   459,   459,   459,   nil,   nil,   nil,   nil,   nil,
   459,   459,   459,   459,   459,   459,   459,   nil,   nil,   459,
   nil,   nil,   nil,   nil,   nil,   nil,   459,   nil,   nil,   459,
   459,   459,   459,   459,   459,   459,   459,   nil,   459,   459,
   459,   nil,   459,   459,   459,   459,   459,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   459,   nil,   nil,   459,
   nil,   nil,   459,   459,   nil,   nil,   459,   nil,   nil,   nil,
   nil,   nil,   459,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   459,   nil,   nil,   nil,   nil,   459,   459,   459,   459,
   nil,   459,   459,   459,   459,   nil,   nil,   nil,   nil,   459,
   459,   nil,   nil,   nil,   460,   460,   460,   459,   460,   459,
   459,   459,   460,   460,   nil,   nil,   nil,   460,   nil,   460,
   460,   460,   460,   460,   460,   460,   nil,   nil,   nil,   nil,
   nil,   460,   460,   460,   460,   460,   460,   460,   nil,   nil,
   460,   nil,   nil,   nil,   nil,   nil,   nil,   460,   nil,   nil,
   460,   460,   460,   460,   460,   460,   460,   460,   nil,   460,
   460,   460,   nil,   460,   460,   460,   460,   460,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   460,   nil,   nil,
   460,   nil,   nil,   460,   460,   nil,   nil,   460,   nil,   nil,
   nil,   nil,   nil,   460,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   460,   nil,   nil,   nil,   nil,   460,   460,   460,
   460,   nil,   460,   460,   460,   460,   nil,   nil,   nil,   nil,
   460,   460,   nil,   nil,   nil,   461,   461,   461,   460,   461,
   460,   460,   460,   461,   461,   nil,   nil,   nil,   461,   nil,
   461,   461,   461,   461,   461,   461,   461,   nil,   nil,   nil,
   nil,   nil,   461,   461,   461,   461,   461,   461,   461,   nil,
   nil,   461,   nil,   nil,   nil,   nil,   nil,   nil,   461,   nil,
   nil,   461,   461,   461,   461,   461,   461,   461,   461,   nil,
   461,   461,   461,   nil,   461,   461,   461,   461,   461,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   461,   nil,
   nil,   461,   nil,   nil,   461,   461,   nil,   nil,   461,   nil,
   nil,   nil,   nil,   nil,   461,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   461,   nil,   nil,   nil,   nil,   461,   461,
   461,   461,   nil,   461,   461,   461,   461,   nil,   nil,   nil,
   nil,   461,   461,   nil,   nil,   nil,   498,   498,   498,   461,
   498,   461,   461,   461,   498,   498,   nil,   nil,   nil,   498,
   nil,   498,   498,   498,   498,   498,   498,   498,   nil,   nil,
   nil,   nil,   nil,   498,   498,   498,   498,   498,   498,   498,
   nil,   nil,   498,   nil,   nil,   nil,   nil,   nil,   nil,   498,
   nil,   nil,   498,   498,   498,   498,   498,   498,   498,   498,
   498,   498,   498,   498,   nil,   498,   498,   498,   498,   498,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   498,
   nil,   nil,   498,   nil,   nil,   498,   498,   nil,   nil,   498,
   nil,   498,   nil,   498,   nil,   498,   nil,   nil,   498,   nil,
   nil,   nil,   nil,   nil,   498,   nil,   nil,   nil,   nil,   498,
   498,   498,   498,   nil,   498,   498,   498,   498,   nil,   nil,
   nil,   nil,   498,   498,   nil,   nil,   nil,   500,   500,   500,
   498,   500,   498,   498,   498,   500,   500,   nil,   nil,   nil,
   500,   nil,   500,   500,   500,   500,   500,   500,   500,   nil,
   nil,   nil,   nil,   nil,   500,   500,   500,   500,   500,   500,
   500,   nil,   nil,   500,   nil,   nil,   nil,   nil,   nil,   nil,
   500,   nil,   nil,   500,   500,   500,   500,   500,   500,   500,
   500,   500,   500,   500,   500,   nil,   500,   500,   500,   500,
   500,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   500,   nil,   nil,   500,   nil,   nil,   500,   500,   nil,   nil,
   500,   nil,   nil,   nil,   500,   nil,   500,   nil,   nil,   500,
   nil,   nil,   nil,   nil,   nil,   500,   nil,   nil,   nil,   nil,
   500,   500,   500,   500,   nil,   500,   500,   500,   500,   nil,
   nil,   nil,   nil,   500,   500,   nil,   nil,   nil,   502,   502,
   502,   500,   502,   500,   500,   500,   502,   502,   nil,   nil,
   nil,   502,   nil,   502,   502,   502,   502,   502,   502,   502,
   nil,   nil,   nil,   nil,   nil,   502,   502,   502,   502,   502,
   502,   502,   nil,   nil,   502,   nil,   nil,   nil,   nil,   nil,
   nil,   502,   nil,   nil,   502,   502,   502,   502,   502,   502,
   502,   502,   nil,   502,   502,   502,   nil,   502,   502,   502,
   502,   502,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   502,   nil,   nil,   502,   nil,   nil,   502,   502,   nil,
   nil,   502,   nil,   nil,   nil,   nil,   nil,   502,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   502,   nil,   nil,   nil,
   nil,   502,   502,   502,   502,   nil,   502,   502,   502,   502,
   nil,   nil,   nil,   nil,   502,   502,   nil,   nil,   nil,   nil,
   nil,   nil,   502,   nil,   502,   502,   502,   508,   508,   508,
   508,   508,   nil,   nil,   nil,   508,   508,   nil,   nil,   nil,
   508,   nil,   508,   508,   508,   508,   508,   508,   508,   nil,
   nil,   nil,   nil,   nil,   508,   508,   508,   508,   508,   508,
   508,   nil,   nil,   508,   nil,   nil,   nil,   nil,   nil,   508,
   508,   508,   508,   508,   508,   508,   508,   508,   508,   508,
   508,   nil,   508,   508,   508,   nil,   508,   508,   508,   508,
   508,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   508,   nil,   nil,   508,   nil,   nil,   508,   508,   nil,   nil,
   508,   nil,   508,   nil,   nil,   nil,   508,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   508,   nil,   nil,   nil,   nil,
   508,   508,   508,   508,   nil,   508,   508,   508,   508,   nil,
   nil,   nil,   nil,   508,   508,   nil,   nil,   nil,   nil,   nil,
   508,   508,   nil,   508,   508,   508,   516,   516,   516,   nil,
   516,   nil,   nil,   nil,   516,   516,   nil,   nil,   nil,   516,
   nil,   516,   516,   516,   516,   516,   516,   516,   nil,   nil,
   nil,   nil,   nil,   516,   516,   516,   516,   516,   516,   516,
   nil,   nil,   516,   nil,   nil,   nil,   nil,   nil,   nil,   516,
   nil,   nil,   516,   516,   516,   516,   516,   516,   516,   516,
   nil,   516,   516,   516,   nil,   516,   516,   nil,   nil,   516,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   516,
   nil,   nil,   516,   nil,   nil,   516,   516,   nil,   nil,   516,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   516,
   516,   516,   516,   nil,   516,   516,   516,   516,   nil,   nil,
   nil,   nil,   516,   516,   nil,   nil,   nil,   518,   518,   518,
   516,   518,   516,   516,   516,   518,   518,   nil,   nil,   nil,
   518,   nil,   518,   518,   518,   518,   518,   518,   518,   nil,
   nil,   nil,   nil,   nil,   518,   518,   518,   518,   518,   518,
   518,   nil,   nil,   518,   nil,   nil,   nil,   nil,   nil,   nil,
   518,   nil,   nil,   518,   518,   518,   518,   518,   518,   518,
   518,   518,   518,   518,   518,   nil,   518,   518,   518,   518,
   518,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   518,   nil,   nil,   518,   nil,   nil,   518,   518,   nil,   nil,
   518,   nil,   518,   nil,   518,   nil,   518,   nil,   nil,   518,
   nil,   nil,   nil,   nil,   nil,   518,   nil,   nil,   nil,   nil,
   518,   518,   518,   518,   nil,   518,   518,   518,   518,   nil,
   nil,   nil,   nil,   518,   518,   nil,   nil,   nil,   524,   524,
   524,   518,   524,   518,   518,   518,   524,   524,   nil,   nil,
   nil,   524,   nil,   524,   524,   524,   524,   524,   524,   524,
   nil,   nil,   nil,   nil,   nil,   524,   524,   524,   524,   524,
   524,   524,   nil,   nil,   524,   nil,   nil,   nil,   nil,   nil,
   nil,   524,   nil,   nil,   524,   524,   524,   524,   524,   524,
   524,   524,   nil,   524,   524,   524,   nil,   524,   524,   nil,
   nil,   524,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   524,   nil,   nil,   524,   nil,   nil,   524,   524,   nil,
   nil,   524,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   524,   524,   524,   524,   nil,   524,   524,   524,   524,
   nil,   nil,   nil,   nil,   524,   524,   nil,   nil,   nil,   527,
   527,   527,   524,   527,   524,   524,   524,   527,   527,   nil,
   nil,   nil,   527,   nil,   527,   527,   527,   527,   527,   527,
   527,   nil,   nil,   nil,   nil,   nil,   527,   527,   527,   527,
   527,   527,   527,   nil,   nil,   527,   nil,   nil,   nil,   nil,
   nil,   nil,   527,   nil,   nil,   527,   527,   527,   527,   527,
   527,   527,   527,   nil,   527,   527,   527,   nil,   527,   527,
   527,   527,   527,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   527,   nil,   nil,   527,   nil,   nil,   527,   527,
   nil,   nil,   527,   nil,   nil,   nil,   nil,   nil,   527,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   527,   nil,   nil,
   nil,   nil,   527,   527,   527,   527,   nil,   527,   527,   527,
   527,   nil,   nil,   nil,   nil,   527,   527,   nil,   nil,   nil,
   528,   528,   528,   527,   528,   527,   527,   527,   528,   528,
   nil,   nil,   nil,   528,   nil,   528,   528,   528,   528,   528,
   528,   528,   nil,   nil,   nil,   nil,   nil,   528,   528,   528,
   528,   528,   528,   528,   nil,   nil,   528,   nil,   nil,   nil,
   nil,   nil,   nil,   528,   nil,   nil,   528,   528,   528,   528,
   528,   528,   528,   528,   nil,   528,   528,   528,   nil,   528,
   528,   528,   528,   528,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   528,   nil,   nil,   528,   nil,   nil,   528,
   528,   nil,   nil,   528,   nil,   nil,   nil,   nil,   nil,   528,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   528,   nil,
   nil,   nil,   nil,   528,   528,   528,   528,   nil,   528,   528,
   528,   528,   nil,   nil,   nil,   nil,   528,   528,   nil,   nil,
   nil,   533,   533,   533,   528,   533,   528,   528,   528,   533,
   533,   nil,   nil,   nil,   533,   nil,   533,   533,   533,   533,
   533,   533,   533,   nil,   nil,   nil,   nil,   nil,   533,   533,
   533,   533,   533,   533,   533,   nil,   nil,   533,   nil,   nil,
   nil,   nil,   nil,   nil,   533,   nil,   nil,   533,   533,   533,
   533,   533,   533,   533,   533,   nil,   533,   533,   533,   nil,
   533,   533,   533,   533,   533,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   533,   nil,   nil,   533,   nil,   nil,
   533,   533,   nil,   nil,   533,   nil,   nil,   nil,   nil,   nil,
   533,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   533,
   nil,   nil,   nil,   nil,   533,   533,   533,   533,   nil,   533,
   533,   533,   533,   nil,   nil,   nil,   nil,   533,   533,   nil,
   nil,   nil,   539,   539,   539,   533,   539,   533,   533,   533,
   539,   539,   nil,   nil,   nil,   539,   nil,   539,   539,   539,
   539,   539,   539,   539,   nil,   nil,   nil,   nil,   nil,   539,
   539,   539,   539,   539,   539,   539,   nil,   nil,   539,   nil,
   nil,   nil,   nil,   nil,   nil,   539,   nil,   nil,   539,   539,
   539,   539,   539,   539,   539,   539,   539,   539,   539,   539,
   nil,   539,   539,   539,   539,   539,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   539,   nil,   nil,   539,   nil,
   nil,   539,   539,   nil,   nil,   539,   nil,   539,   nil,   nil,
   nil,   539,   nil,   nil,   539,   nil,   nil,   nil,   nil,   nil,
   539,   nil,   nil,   nil,   nil,   539,   539,   539,   539,   nil,
   539,   539,   539,   539,   nil,   nil,   nil,   nil,   539,   539,
   nil,   nil,   nil,   542,   542,   542,   539,   542,   539,   539,
   539,   542,   542,   nil,   nil,   nil,   542,   nil,   542,   542,
   542,   542,   542,   542,   542,   nil,   nil,   nil,   nil,   nil,
   542,   542,   542,   542,   542,   542,   542,   nil,   nil,   542,
   nil,   nil,   nil,   nil,   nil,   nil,   542,   nil,   nil,   542,
   542,   542,   542,   542,   542,   542,   542,   542,   542,   542,
   542,   nil,   542,   542,   542,   542,   542,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   542,   nil,   nil,   542,
   nil,   nil,   542,   542,   nil,   nil,   542,   nil,   nil,   nil,
   nil,   nil,   542,   nil,   nil,   542,   nil,   nil,   nil,   nil,
   nil,   542,   nil,   nil,   nil,   nil,   542,   542,   542,   542,
   nil,   542,   542,   542,   542,   nil,   nil,   nil,   nil,   542,
   542,   nil,   nil,   nil,   557,   557,   557,   542,   557,   542,
   542,   542,   557,   557,   nil,   nil,   nil,   557,   nil,   557,
   557,   557,   557,   557,   557,   557,   nil,   nil,   nil,   nil,
   nil,   557,   557,   557,   557,   557,   557,   557,   nil,   nil,
   557,   nil,   nil,   nil,   nil,   nil,   nil,   557,   nil,   nil,
   557,   557,   557,   557,   557,   557,   557,   557,   nil,   557,
   557,   557,   nil,   557,   557,   557,   557,   557,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   557,   nil,   nil,
   557,   nil,   nil,   557,   557,   nil,   nil,   557,   nil,   557,
   nil,   nil,   nil,   557,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   557,   nil,   nil,   nil,   nil,   557,   557,   557,
   557,   nil,   557,   557,   557,   557,   nil,   nil,   nil,   nil,
   557,   557,   nil,   nil,   nil,   558,   558,   558,   557,   558,
   557,   557,   557,   558,   558,   nil,   nil,   nil,   558,   nil,
   558,   558,   558,   558,   558,   558,   558,   nil,   nil,   nil,
   nil,   nil,   558,   558,   558,   558,   558,   558,   558,   nil,
   nil,   558,   nil,   nil,   nil,   nil,   nil,   nil,   558,   nil,
   nil,   558,   558,   558,   558,   558,   558,   558,   558,   558,
   558,   558,   558,   nil,   558,   558,   558,   558,   558,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   558,   nil,
   nil,   558,   nil,   nil,   558,   558,   nil,   nil,   558,   nil,
   558,   nil,   558,   nil,   558,   nil,   nil,   558,   nil,   nil,
   nil,   nil,   nil,   558,   nil,   nil,   nil,   nil,   558,   558,
   558,   558,   nil,   558,   558,   558,   558,   nil,   nil,   nil,
   nil,   558,   558,   nil,   nil,   nil,   568,   568,   568,   558,
   568,   558,   558,   558,   568,   568,   nil,   nil,   nil,   568,
   nil,   568,   568,   568,   568,   568,   568,   568,   nil,   nil,
   nil,   nil,   nil,   568,   568,   568,   568,   568,   568,   568,
   nil,   nil,   568,   nil,   nil,   nil,   nil,   nil,   nil,   568,
   nil,   nil,   568,   568,   568,   568,   568,   568,   568,   568,
   568,   568,   568,   568,   nil,   568,   568,   568,   568,   568,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   568,
   nil,   nil,   568,   nil,   nil,   568,   568,   nil,   nil,   568,
   nil,   568,   nil,   568,   nil,   568,   nil,   nil,   568,   nil,
   nil,   nil,   nil,   nil,   568,   nil,   nil,   nil,   nil,   568,
   568,   568,   568,   nil,   568,   568,   568,   568,   nil,   nil,
   nil,   nil,   568,   568,   nil,   nil,   nil,   600,   600,   600,
   568,   600,   568,   568,   568,   600,   600,   nil,   nil,   nil,
   600,   nil,   600,   600,   600,   600,   600,   600,   600,   nil,
   nil,   nil,   nil,   nil,   600,   600,   600,   600,   600,   600,
   600,   nil,   nil,   600,   nil,   nil,   nil,   nil,   nil,   nil,
   600,   nil,   nil,   600,   600,   600,   600,   600,   600,   600,
   600,   nil,   600,   600,   600,   nil,   600,   600,   600,   600,
   600,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   600,   nil,   nil,   600,   nil,   nil,   600,   600,   nil,   nil,
   600,   nil,   600,   nil,   nil,   nil,   600,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   600,   nil,   nil,   nil,   nil,
   600,   600,   600,   600,   nil,   600,   600,   600,   600,   nil,
   nil,   nil,   nil,   600,   600,   nil,   nil,   nil,   601,   601,
   601,   600,   601,   600,   600,   600,   601,   601,   nil,   nil,
   nil,   601,   nil,   601,   601,   601,   601,   601,   601,   601,
   nil,   nil,   nil,   nil,   nil,   601,   601,   601,   601,   601,
   601,   601,   nil,   nil,   601,   nil,   nil,   nil,   nil,   nil,
   nil,   601,   nil,   nil,   601,   601,   601,   601,   601,   601,
   601,   601,   nil,   601,   601,   601,   nil,   601,   601,   601,
   601,   601,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   601,   nil,   nil,   601,   nil,   nil,   601,   601,   nil,
   nil,   601,   nil,   nil,   nil,   nil,   nil,   601,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   601,   nil,   nil,   nil,
   nil,   601,   601,   601,   601,   nil,   601,   601,   601,   601,
   nil,   nil,   nil,   nil,   601,   601,   nil,   nil,   nil,   602,
   602,   602,   601,   602,   601,   601,   601,   602,   602,   nil,
   nil,   nil,   602,   nil,   602,   602,   602,   602,   602,   602,
   602,   nil,   nil,   nil,   nil,   nil,   602,   602,   602,   602,
   602,   602,   602,   nil,   nil,   602,   nil,   nil,   nil,   nil,
   nil,   nil,   602,   nil,   nil,   602,   602,   602,   602,   602,
   602,   602,   602,   602,   602,   602,   602,   nil,   602,   602,
   602,   602,   602,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   602,   nil,   nil,   602,   nil,   nil,   602,   602,
   nil,   nil,   602,   nil,   602,   nil,   602,   nil,   602,   nil,
   nil,   602,   nil,   nil,   nil,   nil,   nil,   602,   nil,   nil,
   nil,   nil,   602,   602,   602,   602,   nil,   602,   602,   602,
   602,   nil,   nil,   nil,   nil,   602,   602,   nil,   nil,   nil,
   nil,   nil,   nil,   602,   nil,   602,   602,   602,   605,   605,
   605,   605,   605,   nil,   nil,   nil,   605,   605,   nil,   nil,
   nil,   605,   nil,   605,   605,   605,   605,   605,   605,   605,
   nil,   nil,   nil,   nil,   nil,   605,   605,   605,   605,   605,
   605,   605,   nil,   nil,   605,   nil,   nil,   nil,   nil,   nil,
   605,   605,   nil,   605,   605,   605,   605,   605,   605,   605,
   605,   605,   nil,   605,   605,   605,   nil,   605,   605,   605,
   605,   605,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   605,   nil,   nil,   605,   nil,   nil,   605,   605,   nil,
   nil,   605,   nil,   605,   nil,   nil,   nil,   605,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   605,   nil,   nil,   nil,
   nil,   605,   605,   605,   605,   nil,   605,   605,   605,   605,
   nil,   nil,   nil,   nil,   605,   605,   nil,   nil,   nil,   606,
   606,   606,   605,   606,   605,   605,   605,   606,   606,   nil,
   nil,   nil,   606,   nil,   606,   606,   606,   606,   606,   606,
   606,   nil,   nil,   nil,   nil,   nil,   606,   606,   606,   606,
   606,   606,   606,   nil,   nil,   606,   nil,   nil,   nil,   nil,
   nil,   nil,   606,   nil,   nil,   606,   606,   606,   606,   606,
   606,   606,   606,   nil,   606,   606,   606,   nil,   606,   606,
   606,   606,   606,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   606,   nil,   nil,   606,   nil,   nil,   606,   606,
   nil,   nil,   606,   nil,   nil,   nil,   nil,   nil,   606,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   606,   nil,   nil,
   nil,   nil,   606,   606,   606,   606,   nil,   606,   606,   606,
   606,   nil,   nil,   nil,   nil,   606,   606,   nil,   nil,   nil,
   609,   609,   609,   606,   609,   606,   606,   606,   609,   609,
   nil,   nil,   nil,   609,   nil,   609,   609,   609,   609,   609,
   609,   609,   nil,   nil,   nil,   nil,   nil,   609,   609,   609,
   609,   609,   609,   609,   nil,   nil,   609,   nil,   nil,   nil,
   nil,   nil,   nil,   609,   nil,   nil,   609,   609,   609,   609,
   609,   609,   609,   609,   609,   609,   609,   609,   nil,   609,
   609,   609,   609,   609,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   609,   nil,   nil,   609,   nil,   nil,   609,
   609,   nil,   nil,   609,   nil,   609,   nil,   609,   nil,   609,
   nil,   nil,   609,   nil,   nil,   nil,   nil,   nil,   609,   nil,
   nil,   nil,   nil,   609,   609,   609,   609,   nil,   609,   609,
   609,   609,   nil,   nil,   nil,   nil,   609,   609,   nil,   nil,
   nil,   610,   610,   610,   609,   610,   609,   609,   609,   610,
   610,   nil,   nil,   nil,   610,   nil,   610,   610,   610,   610,
   610,   610,   610,   nil,   nil,   nil,   nil,   nil,   610,   610,
   610,   610,   610,   610,   610,   nil,   nil,   610,   nil,   nil,
   nil,   nil,   nil,   nil,   610,   nil,   nil,   610,   610,   610,
   610,   610,   610,   610,   610,   610,   610,   610,   610,   nil,
   610,   610,   610,   610,   610,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   610,   nil,   nil,   610,   nil,   nil,
   610,   610,   nil,   nil,   610,   nil,   nil,   nil,   610,   nil,
   610,   nil,   nil,   610,   nil,   nil,   nil,   nil,   nil,   610,
   nil,   nil,   nil,   nil,   610,   610,   610,   610,   nil,   610,
   610,   610,   610,   nil,   nil,   nil,   nil,   610,   610,   nil,
   nil,   nil,   611,   611,   611,   610,   611,   610,   610,   610,
   611,   611,   nil,   nil,   nil,   611,   nil,   611,   611,   611,
   611,   611,   611,   611,   nil,   nil,   nil,   nil,   nil,   611,
   611,   611,   611,   611,   611,   611,   nil,   nil,   611,   nil,
   nil,   nil,   nil,   nil,   nil,   611,   nil,   nil,   611,   611,
   611,   611,   611,   611,   611,   611,   nil,   611,   611,   611,
   nil,   611,   611,   611,   611,   611,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   611,   nil,   nil,   611,   nil,
   nil,   611,   611,   nil,   nil,   611,   nil,   nil,   nil,   nil,
   nil,   611,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   611,   nil,   nil,   nil,   nil,   611,   611,   611,   611,   nil,
   611,   611,   611,   611,   nil,   nil,   nil,   nil,   611,   611,
   nil,   nil,   nil,   612,   612,   612,   611,   612,   611,   611,
   611,   612,   612,   nil,   nil,   nil,   612,   nil,   612,   612,
   612,   612,   612,   612,   612,   nil,   nil,   nil,   nil,   nil,
   612,   612,   612,   612,   612,   612,   612,   nil,   nil,   612,
   nil,   nil,   nil,   nil,   nil,   nil,   612,   nil,   nil,   612,
   612,   612,   612,   612,   612,   612,   612,   nil,   612,   612,
   612,   nil,   612,   612,   612,   612,   612,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   612,   nil,   nil,   612,
   nil,   nil,   612,   612,   nil,   nil,   612,   nil,   nil,   nil,
   nil,   nil,   612,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   612,   nil,   nil,   nil,   nil,   612,   612,   612,   612,
   nil,   612,   612,   612,   612,   nil,   nil,   nil,   nil,   612,
   612,   nil,   nil,   nil,   616,   616,   616,   612,   616,   612,
   612,   612,   616,   616,   nil,   nil,   nil,   616,   nil,   616,
   616,   616,   616,   616,   616,   616,   nil,   nil,   nil,   nil,
   nil,   616,   616,   616,   616,   616,   616,   616,   nil,   nil,
   616,   nil,   nil,   nil,   nil,   nil,   nil,   616,   nil,   nil,
   616,   616,   616,   616,   616,   616,   616,   616,   nil,   616,
   616,   616,   nil,   616,   616,   616,   616,   616,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   616,   nil,   nil,
   616,   nil,   nil,   616,   616,   nil,   nil,   616,   nil,   nil,
   nil,   nil,   nil,   616,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   616,   nil,   nil,   nil,   nil,   616,   616,   616,
   616,   nil,   616,   616,   616,   616,   nil,   nil,   nil,   nil,
   616,   616,   nil,   nil,   nil,   617,   617,   617,   616,   617,
   616,   616,   616,   617,   617,   nil,   nil,   nil,   617,   nil,
   617,   617,   617,   617,   617,   617,   617,   nil,   nil,   nil,
   nil,   nil,   617,   617,   617,   617,   617,   617,   617,   nil,
   nil,   617,   nil,   nil,   nil,   nil,   nil,   nil,   617,   nil,
   nil,   617,   617,   617,   617,   617,   617,   617,   617,   nil,
   617,   617,   617,   nil,   617,   617,   617,   617,   617,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   617,   nil,
   nil,   617,   nil,   nil,   617,   617,   nil,   nil,   617,   nil,
   nil,   nil,   nil,   nil,   617,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   617,   nil,   nil,   nil,   nil,   617,   617,
   617,   617,   nil,   617,   617,   617,   617,   nil,   nil,   nil,
   nil,   617,   617,   nil,   nil,   nil,   641,   641,   641,   617,
   641,   617,   617,   617,   641,   641,   nil,   nil,   nil,   641,
   nil,   641,   641,   641,   641,   641,   641,   641,   nil,   nil,
   nil,   nil,   nil,   641,   641,   641,   641,   641,   641,   641,
   nil,   nil,   641,   nil,   nil,   nil,   nil,   nil,   nil,   641,
   nil,   nil,   641,   641,   641,   641,   641,   641,   641,   641,
   nil,   641,   641,   641,   nil,   641,   641,   641,   641,   641,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   641,
   nil,   nil,   641,   nil,   nil,   641,   641,   nil,   nil,   641,
   nil,   nil,   nil,   nil,   nil,   641,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   641,   nil,   nil,   nil,   nil,   641,
   641,   641,   641,   nil,   641,   641,   641,   641,   nil,   nil,
   nil,   nil,   641,   641,   nil,   nil,   nil,   644,   644,   644,
   641,   644,   641,   641,   641,   644,   644,   nil,   nil,   nil,
   644,   nil,   644,   644,   644,   644,   644,   644,   644,   nil,
   nil,   nil,   nil,   nil,   644,   644,   644,   644,   644,   644,
   644,   nil,   nil,   644,   nil,   nil,   nil,   nil,   nil,   nil,
   644,   nil,   nil,   644,   644,   644,   644,   644,   644,   644,
   644,   nil,   644,   644,   644,   nil,   644,   644,   644,   644,
   644,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   644,   nil,   nil,   644,   nil,   nil,   644,   644,   nil,   nil,
   644,   nil,   nil,   nil,   nil,   nil,   644,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   644,   nil,   nil,   nil,   nil,
   644,   644,   644,   644,   nil,   644,   644,   644,   644,   nil,
   nil,   nil,   nil,   644,   644,   nil,   nil,   nil,   647,   647,
   647,   644,   647,   644,   644,   644,   647,   647,   nil,   nil,
   nil,   647,   nil,   647,   647,   647,   647,   647,   647,   647,
   nil,   nil,   nil,   nil,   nil,   647,   647,   647,   647,   647,
   647,   647,   nil,   nil,   647,   nil,   nil,   nil,   nil,   nil,
   nil,   647,   nil,   nil,   647,   647,   647,   647,   647,   647,
   647,   647,   nil,   647,   647,   647,   nil,   647,   647,   nil,
   nil,   647,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   647,   nil,   nil,   647,   nil,   nil,   647,   647,   nil,
   nil,   647,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   647,   647,   647,   647,   nil,   647,   647,   647,   647,
   nil,   nil,   nil,   nil,   647,   647,   nil,   nil,   nil,   658,
   658,   658,   647,   658,   647,   647,   647,   658,   658,   nil,
   nil,   nil,   658,   nil,   658,   658,   658,   658,   658,   658,
   658,   nil,   nil,   nil,   nil,   nil,   658,   658,   658,   658,
   658,   658,   658,   nil,   nil,   658,   nil,   nil,   nil,   nil,
   nil,   nil,   658,   nil,   nil,   658,   658,   658,   658,   658,
   658,   658,   658,   nil,   658,   658,   658,   nil,   658,   658,
   nil,   nil,   658,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   658,   nil,   nil,   658,   nil,   nil,   658,   658,
   nil,   nil,   658,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   658,   658,   658,   658,   nil,   658,   658,   658,
   658,   nil,   nil,   nil,   nil,   658,   658,   nil,   nil,   nil,
   663,   663,   663,   658,   663,   658,   658,   658,   663,   663,
   nil,   nil,   nil,   663,   nil,   663,   663,   663,   663,   663,
   663,   663,   nil,   nil,   nil,   nil,   nil,   663,   663,   663,
   663,   663,   663,   663,   nil,   nil,   663,   nil,   nil,   nil,
   nil,   nil,   nil,   663,   nil,   nil,   663,   663,   663,   663,
   663,   663,   663,   663,   nil,   663,   663,   663,   nil,   663,
   663,   663,   663,   663,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   663,   nil,   nil,   663,   nil,   nil,   663,
   663,   nil,   nil,   663,   nil,   663,   nil,   nil,   nil,   663,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   663,   nil,
   nil,   nil,   nil,   663,   663,   663,   663,   nil,   663,   663,
   663,   663,   nil,   nil,   nil,   nil,   663,   663,   nil,   nil,
   nil,   689,   689,   689,   663,   689,   663,   663,   663,   689,
   689,   nil,   nil,   nil,   689,   nil,   689,   689,   689,   689,
   689,   689,   689,   nil,   nil,   nil,   nil,   nil,   689,   689,
   689,   689,   689,   689,   689,   nil,   nil,   689,   nil,   nil,
   nil,   nil,   nil,   nil,   689,   nil,   nil,   689,   689,   689,
   689,   689,   689,   689,   689,   nil,   689,   689,   689,   nil,
   689,   689,   689,   689,   689,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   689,   nil,   nil,   689,   nil,   nil,
   689,   689,   nil,   nil,   689,   nil,   nil,   nil,   nil,   nil,
   689,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   689,
   nil,   nil,   nil,   nil,   689,   689,   689,   689,   nil,   689,
   689,   689,   689,   nil,   nil,   nil,   nil,   689,   689,   nil,
   nil,   nil,   723,   723,   723,   689,   723,   689,   689,   689,
   723,   723,   nil,   nil,   nil,   723,   nil,   723,   723,   723,
   723,   723,   723,   723,   nil,   nil,   nil,   nil,   nil,   723,
   723,   723,   723,   723,   723,   723,   nil,   nil,   723,   nil,
   nil,   nil,   nil,   nil,   nil,   723,   nil,   nil,   723,   723,
   723,   723,   723,   723,   723,   723,   nil,   723,   723,   723,
   nil,   723,   723,   723,   723,   723,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   723,   nil,   nil,   723,   nil,
   nil,   723,   723,   nil,   nil,   723,   nil,   nil,   nil,   nil,
   nil,   723,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   723,   nil,   nil,   nil,   nil,   723,   723,   723,   723,   nil,
   723,   723,   723,   723,   nil,   nil,   nil,   nil,   723,   723,
   nil,   nil,   nil,   745,   745,   745,   723,   745,   723,   723,
   723,   745,   745,   nil,   nil,   nil,   745,   nil,   745,   745,
   745,   745,   745,   745,   745,   nil,   nil,   nil,   nil,   nil,
   745,   745,   745,   745,   745,   745,   745,   nil,   nil,   745,
   nil,   nil,   nil,   nil,   nil,   nil,   745,   nil,   nil,   745,
   745,   745,   745,   745,   745,   745,   745,   nil,   745,   745,
   745,   nil,   745,   745,   745,   745,   745,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   745,   nil,   nil,   745,
   nil,   nil,   745,   745,   nil,   nil,   745,   nil,   nil,   nil,
   nil,   nil,   745,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   745,   nil,   nil,   nil,   nil,   745,   745,   745,   745,
   nil,   745,   745,   745,   745,   nil,   nil,   nil,   nil,   745,
   745,   nil,   nil,   nil,   753,   753,   753,   745,   753,   745,
   745,   745,   753,   753,   nil,   nil,   nil,   753,   nil,   753,
   753,   753,   753,   753,   753,   753,   nil,   nil,   nil,   nil,
   nil,   753,   753,   753,   753,   753,   753,   753,   nil,   nil,
   753,   nil,   nil,   nil,   nil,   nil,   nil,   753,   nil,   nil,
   753,   753,   753,   753,   753,   753,   753,   753,   nil,   753,
   753,   753,   nil,   753,   753,   753,   753,   753,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   753,   nil,   nil,
   753,   nil,   nil,   753,   753,   nil,   nil,   753,   nil,   nil,
   nil,   nil,   nil,   753,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   753,   nil,   nil,   nil,   nil,   753,   753,   753,
   753,   nil,   753,   753,   753,   753,   nil,   nil,   nil,   nil,
   753,   753,   nil,   nil,   nil,   766,   766,   766,   753,   766,
   753,   753,   753,   766,   766,   nil,   nil,   nil,   766,   nil,
   766,   766,   766,   766,   766,   766,   766,   nil,   nil,   nil,
   nil,   nil,   766,   766,   766,   766,   766,   766,   766,   nil,
   nil,   766,   nil,   nil,   nil,   nil,   nil,   nil,   766,   nil,
   nil,   766,   766,   766,   766,   766,   766,   766,   766,   nil,
   766,   766,   766,   nil,   766,   766,   766,   766,   766,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   766,   nil,
   nil,   766,   nil,   nil,   766,   766,   nil,   nil,   766,   nil,
   nil,   nil,   nil,   nil,   766,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   766,   nil,   nil,   nil,   nil,   766,   766,
   766,   766,   nil,   766,   766,   766,   766,   nil,   nil,   nil,
   nil,   766,   766,   nil,   nil,   nil,   767,   767,   767,   766,
   767,   766,   766,   766,   767,   767,   nil,   nil,   nil,   767,
   nil,   767,   767,   767,   767,   767,   767,   767,   nil,   nil,
   nil,   nil,   nil,   767,   767,   767,   767,   767,   767,   767,
   nil,   nil,   767,   nil,   nil,   nil,   nil,   nil,   nil,   767,
   nil,   nil,   767,   767,   767,   767,   767,   767,   767,   767,
   nil,   767,   767,   767,   nil,   767,   767,   767,   767,   767,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   767,
   nil,   nil,   767,   nil,   nil,   767,   767,   nil,   nil,   767,
   nil,   nil,   nil,   nil,   nil,   767,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   767,   nil,   nil,   nil,   nil,   767,
   767,   767,   767,   nil,   767,   767,   767,   767,   nil,   nil,
   nil,   nil,   767,   767,   nil,   nil,   nil,   768,   768,   768,
   767,   768,   767,   767,   767,   768,   768,   nil,   nil,   nil,
   768,   nil,   768,   768,   768,   768,   768,   768,   768,   nil,
   nil,   nil,   nil,   nil,   768,   768,   768,   768,   768,   768,
   768,   nil,   nil,   768,   nil,   nil,   nil,   nil,   nil,   nil,
   768,   nil,   nil,   768,   768,   768,   768,   768,   768,   768,
   768,   nil,   768,   768,   768,   nil,   768,   768,   768,   768,
   768,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   768,   nil,   nil,   768,   nil,   nil,   768,   768,   nil,   nil,
   768,   nil,   nil,   nil,   nil,   nil,   768,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   768,   nil,   nil,   nil,   nil,
   768,   768,   768,   768,   nil,   768,   768,   768,   768,   nil,
   nil,   nil,   nil,   768,   768,   nil,   nil,   nil,   769,   769,
   769,   768,   769,   768,   768,   768,   769,   769,   nil,   nil,
   nil,   769,   nil,   769,   769,   769,   769,   769,   769,   769,
   nil,   nil,   nil,   nil,   nil,   769,   769,   769,   769,   769,
   769,   769,   nil,   nil,   769,   nil,   nil,   nil,   nil,   nil,
   nil,   769,   nil,   nil,   769,   769,   769,   769,   769,   769,
   769,   769,   nil,   769,   769,   769,   nil,   769,   769,   769,
   769,   769,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   769,   nil,   nil,   769,   nil,   nil,   769,   769,   nil,
   nil,   769,   nil,   nil,   nil,   nil,   nil,   769,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   769,   nil,   nil,   nil,
   nil,   769,   769,   769,   769,   nil,   769,   769,   769,   769,
   nil,   nil,   nil,   nil,   769,   769,   nil,   nil,   nil,   771,
   771,   771,   769,   771,   769,   769,   769,   771,   771,   nil,
   nil,   nil,   771,   nil,   771,   771,   771,   771,   771,   771,
   771,   nil,   nil,   nil,   nil,   nil,   771,   771,   771,   771,
   771,   771,   771,   nil,   nil,   771,   nil,   nil,   nil,   nil,
   nil,   nil,   771,   nil,   nil,   771,   771,   771,   771,   771,
   771,   771,   771,   nil,   771,   771,   771,   nil,   771,   771,
   771,   771,   771,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   771,   nil,   nil,   771,   nil,   nil,   771,   771,
   nil,   nil,   771,   nil,   nil,   nil,   nil,   nil,   771,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   771,   nil,   nil,
   nil,   nil,   771,   771,   771,   771,   nil,   771,   771,   771,
   771,   nil,   nil,   nil,   nil,   771,   771,   nil,   nil,   nil,
   810,   810,   810,   771,   810,   771,   771,   771,   810,   810,
   nil,   nil,   nil,   810,   nil,   810,   810,   810,   810,   810,
   810,   810,   nil,   nil,   nil,   nil,   nil,   810,   810,   810,
   810,   810,   810,   810,   nil,   nil,   810,   nil,   nil,   nil,
   nil,   nil,   nil,   810,   nil,   nil,   810,   810,   810,   810,
   810,   810,   810,   810,   nil,   810,   810,   810,   nil,   810,
   810,   810,   810,   810,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   810,   nil,   nil,   810,   nil,   nil,   810,
   810,   nil,   nil,   810,   nil,   nil,   nil,   nil,   nil,   810,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   810,   nil,
   nil,   nil,   nil,   810,   810,   810,   810,   nil,   810,   810,
   810,   810,   nil,   nil,   nil,   nil,   810,   810,   nil,   nil,
   nil,   823,   823,   823,   810,   823,   810,   810,   810,   823,
   823,   nil,   nil,   nil,   823,   nil,   823,   823,   823,   823,
   823,   823,   823,   nil,   nil,   nil,   nil,   nil,   823,   823,
   823,   823,   823,   823,   823,   nil,   nil,   823,   nil,   nil,
   nil,   nil,   nil,   nil,   823,   nil,   nil,   823,   823,   823,
   823,   823,   823,   823,   823,   nil,   823,   823,   823,   nil,
   823,   823,   823,   823,   823,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   823,   nil,   nil,   823,   nil,   nil,
   823,   823,   nil,   nil,   823,   nil,   nil,   nil,   nil,   nil,
   823,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   823,
   nil,   nil,   nil,   nil,   823,   823,   823,   823,   nil,   823,
   823,   823,   823,   nil,   nil,   nil,   nil,   823,   823,   nil,
   nil,   nil,   826,   826,   826,   823,   826,   823,   823,   823,
   826,   826,   nil,   nil,   nil,   826,   nil,   826,   826,   826,
   826,   826,   826,   826,   nil,   nil,   nil,   nil,   nil,   826,
   826,   826,   826,   826,   826,   826,   nil,   nil,   826,   nil,
   nil,   nil,   nil,   nil,   nil,   826,   nil,   nil,   826,   826,
   826,   826,   826,   826,   826,   826,   nil,   826,   826,   826,
   nil,   826,   826,   826,   826,   826,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   826,   nil,   nil,   826,   nil,
   nil,   826,   826,   nil,   nil,   826,   nil,   826,   nil,   nil,
   nil,   826,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   826,   nil,   nil,   nil,   nil,   826,   826,   826,   826,   nil,
   826,   826,   826,   826,   nil,   nil,   nil,   nil,   826,   826,
   nil,   nil,   nil,   844,   844,   844,   826,   844,   826,   826,
   826,   844,   844,   nil,   nil,   nil,   844,   nil,   844,   844,
   844,   844,   844,   844,   844,   nil,   nil,   nil,   nil,   nil,
   844,   844,   844,   844,   844,   844,   844,   nil,   nil,   844,
   nil,   nil,   nil,   nil,   nil,   nil,   844,   nil,   nil,   844,
   844,   844,   844,   844,   844,   844,   844,   844,   844,   844,
   844,   nil,   844,   844,   844,   844,   844,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   844,   nil,   nil,   844,
   nil,   nil,   844,   844,   nil,   nil,   844,   nil,   nil,   nil,
   844,   nil,   844,   nil,   nil,   844,   nil,   nil,   nil,   nil,
   nil,   844,   nil,   nil,   nil,   nil,   844,   844,   844,   844,
   nil,   844,   844,   844,   844,   nil,   nil,   nil,   nil,   844,
   844,   nil,   nil,   nil,   845,   845,   845,   844,   845,   844,
   844,   844,   845,   845,   nil,   nil,   nil,   845,   nil,   845,
   845,   845,   845,   845,   845,   845,   nil,   nil,   nil,   nil,
   nil,   845,   845,   845,   845,   845,   845,   845,   nil,   nil,
   845,   nil,   nil,   nil,   nil,   nil,   nil,   845,   nil,   nil,
   845,   845,   845,   845,   845,   845,   845,   845,   nil,   845,
   845,   845,   nil,   845,   845,   845,   845,   845,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   845,   nil,   nil,
   845,   nil,   nil,   845,   845,   nil,   nil,   845,   nil,   nil,
   nil,   nil,   nil,   845,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   845,   nil,   nil,   nil,   nil,   845,   845,   845,
   845,   nil,   845,   845,   845,   845,   nil,   nil,   nil,   nil,
   845,   845,   nil,   nil,   nil,   860,   860,   860,   845,   860,
   845,   845,   845,   860,   860,   nil,   nil,   nil,   860,   nil,
   860,   860,   860,   860,   860,   860,   860,   nil,   nil,   nil,
   nil,   nil,   860,   860,   860,   860,   860,   860,   860,   nil,
   nil,   860,   nil,   nil,   nil,   nil,   nil,   nil,   860,   nil,
   nil,   860,   860,   860,   860,   860,   860,   860,   860,   nil,
   860,   860,   860,   nil,   860,   860,   nil,   nil,   860,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   860,   nil,
   nil,   860,   nil,   nil,   860,   860,   nil,   nil,   860,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   860,   860,
   860,   860,   nil,   860,   860,   860,   860,   nil,   nil,   nil,
   nil,   860,   860,   nil,   nil,   nil,   869,   869,   869,   860,
   869,   860,   860,   860,   869,   869,   nil,   nil,   nil,   869,
   nil,   869,   869,   869,   869,   869,   869,   869,   nil,   nil,
   nil,   nil,   nil,   869,   869,   869,   869,   869,   869,   869,
   nil,   nil,   869,   nil,   nil,   nil,   nil,   nil,   nil,   869,
   nil,   nil,   869,   869,   869,   869,   869,   869,   869,   869,
   nil,   869,   869,   869,   nil,   869,   869,   nil,   nil,   869,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   869,
   nil,   nil,   869,   nil,   nil,   869,   869,   nil,   nil,   869,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   869,
   869,   869,   869,   nil,   869,   869,   869,   869,   nil,   nil,
   nil,   nil,   869,   869,   nil,   nil,   nil,   920,   920,   920,
   869,   920,   869,   869,   869,   920,   920,   nil,   nil,   nil,
   920,   nil,   920,   920,   920,   920,   920,   920,   920,   nil,
   nil,   nil,   nil,   nil,   920,   920,   920,   920,   920,   920,
   920,   nil,   nil,   920,   nil,   nil,   nil,   nil,   nil,   nil,
   920,   nil,   nil,   920,   920,   920,   920,   920,   920,   920,
   920,   nil,   920,   920,   920,   nil,   920,   920,   nil,   nil,
   920,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   920,   nil,   nil,   920,   nil,   nil,   920,   920,   nil,   nil,
   920,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   920,   920,   920,   920,   nil,   920,   920,   920,   920,   nil,
   nil,   nil,   nil,   920,   920,   nil,   nil,   nil,   973,   973,
   973,   920,   973,   920,   920,   920,   973,   973,   nil,   nil,
   nil,   973,   nil,   973,   973,   973,   973,   973,   973,   973,
   nil,   nil,   nil,   nil,   nil,   973,   973,   973,   973,   973,
   973,   973,   nil,   nil,   973,   nil,   nil,   nil,   nil,   nil,
   nil,   973,   nil,   nil,   973,   973,   973,   973,   973,   973,
   973,   973,   973,   973,   973,   973,   nil,   973,   973,   973,
   973,   973,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   973,   nil,   nil,   973,   nil,   nil,   973,   973,   nil,
   nil,   973,   nil,   973,   nil,   973,   nil,   973,   nil,   nil,
   973,   nil,   nil,   nil,   nil,   nil,   973,   nil,   nil,   nil,
   nil,   973,   973,   973,   973,   nil,   973,   973,   973,   973,
   nil,   nil,   nil,   nil,   973,   973,   nil,   nil,   nil,   nil,
   nil,   nil,   973,   nil,   973,   973,   973,     8,     8,     8,
     8,     8,     8,     8,     8,     8,     8,     8,     8,     8,
     8,     8,     8,     8,     8,     8,     8,     8,     8,     8,
     8,   nil,   nil,   nil,     8,     8,     8,     8,     8,     8,
     8,     8,     8,     8,   nil,   nil,   nil,   nil,   nil,     8,
     8,     8,     8,     8,     8,     8,     8,     8,     8,   nil,
     8,   nil,   nil,   nil,   nil,   nil,   nil,   nil,     8,     8,
   nil,     8,     8,     8,     8,     8,     8,     8,   nil,   nil,
     8,     8,   nil,   nil,   nil,     8,     8,     8,     8,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,     8,     8,   nil,     8,     8,     8,     8,     8,
     8,     8,     8,     8,     8,     8,     8,   nil,   nil,     8,
     8,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,     8,     9,     9,     9,     9,     9,
     9,     9,     9,     9,     9,     9,     9,     9,     9,     9,
     9,     9,     9,     9,     9,     9,     9,     9,     9,   nil,
   nil,   nil,     9,     9,     9,     9,     9,     9,     9,     9,
     9,     9,   nil,   nil,   nil,   nil,   nil,     9,     9,     9,
     9,     9,     9,     9,     9,     9,   nil,   nil,     9,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,     9,     9,   nil,     9,
     9,     9,     9,     9,     9,     9,   nil,   nil,     9,     9,
   nil,   nil,   nil,     9,     9,     9,     9,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
     9,     9,   nil,     9,     9,     9,     9,     9,     9,     9,
     9,     9,     9,     9,     9,   nil,   nil,     9,     9,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,     9,   405,   405,   405,   405,   405,   405,   405,
   405,   405,   405,   405,   405,   405,   405,   405,   405,   405,
   405,   405,   405,   405,   405,   405,   405,   nil,   nil,   nil,
   405,   405,   405,   405,   405,   405,   405,   405,   405,   405,
   nil,   nil,   nil,   nil,   nil,   405,   405,   405,   405,   405,
   405,   405,   405,   405,   nil,   nil,   405,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   405,   405,   nil,   405,   405,   405,
   405,   405,   405,   405,   nil,   nil,   405,   405,   nil,   nil,
   nil,   405,   405,   405,   405,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   405,   405,
   nil,   405,   405,   405,   405,   405,   405,   405,   405,   405,
   405,   405,   405,   nil,   nil,   405,   405,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   405,   597,   597,   597,   597,   597,   597,   597,   597,   597,
   597,   597,   597,   597,   597,   597,   597,   597,   597,   597,
   597,   597,   597,   597,   597,   nil,   nil,   nil,   597,   597,
   597,   597,   597,   597,   597,   597,   597,   597,   nil,   nil,
   nil,   nil,   nil,   597,   597,   597,   597,   597,   597,   597,
   597,   597,   nil,   nil,   597,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   597,   597,   nil,   597,   597,   597,   597,   597,
   597,   597,   nil,   nil,   597,   597,   nil,   nil,   nil,   597,
   597,   597,   597,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   597,   597,   nil,   597,
   597,   597,   597,   597,   597,   597,   597,   597,   597,   597,
   597,   nil,   nil,   597,   597,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   597,    72,
    72,    72,    72,    72,    72,    72,    72,    72,    72,    72,
    72,    72,    72,    72,    72,    72,    72,    72,    72,    72,
    72,    72,    72,   nil,   nil,   nil,    72,    72,    72,    72,
    72,    72,    72,    72,    72,    72,   nil,   nil,   nil,   nil,
   nil,    72,    72,    72,    72,    72,    72,    72,    72,    72,
    72,    72,    72,   nil,    72,   nil,   nil,   nil,   nil,   nil,
    72,    72,   nil,    72,    72,    72,    72,    72,    72,    72,
   nil,   nil,    72,    72,   nil,   nil,   nil,    72,    72,    72,
    72,   nil,   nil,   nil,   nil,   nil,    72,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    72,    72,   nil,    72,    72,    72,
    72,    72,    72,    72,    72,    72,    72,    72,    72,   nil,
   nil,    72,   729,   729,   729,   729,   729,   729,   729,   729,
   729,   729,   729,   729,   729,   729,   729,   729,   729,   729,
   729,   729,   729,   729,   729,   729,   nil,   nil,   nil,   729,
   729,   729,   729,   729,   729,   729,   729,   729,   729,   nil,
   nil,   nil,   nil,   nil,   729,   729,   729,   729,   729,   729,
   729,   729,   729,   nil,   nil,   729,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   729,   729,   nil,   729,   729,   729,   729,
   729,   729,   729,   nil,   nil,   729,   729,   nil,   nil,   nil,
   729,   729,   729,   729,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   729,   729,   nil,
   729,   729,   729,   729,   729,   729,   729,   729,   729,   729,
   729,   729,   219,   219,   729,   nil,   219,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   219,   219,   nil,   219,   219,   219,
   219,   219,   219,   219,   nil,   nil,   219,   219,   nil,   nil,
   nil,   219,   219,   219,   219,   nil,   nil,   nil,   nil,   nil,
   219,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   219,   219,
   nil,   219,   219,   219,   219,   219,   219,   219,   219,   219,
   219,   219,   219,   220,   220,   219,   nil,   220,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   220,   220,   nil,   220,   220,
   220,   220,   220,   220,   220,   nil,   nil,   220,   220,   nil,
   nil,   nil,   220,   220,   220,   220,   nil,   nil,   nil,   nil,
   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   220,
   220,   nil,   220,   220,   220,   220,   220,   220,   220,   220,
   220,   220,   220,   220,   268,   268,   220,   nil,   268,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   268,   268,   nil,   268,
   268,   268,   268,   268,   268,   268,   nil,   nil,   268,   268,
   nil,   nil,   nil,   268,   268,   268,   268,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   268,   268,   nil,   268,   268,   268,   268,   268,   268,   268,
   268,   268,   268,   268,   268,   455,   455,   268,   nil,   455,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   455,   455,   nil,
   455,   455,   455,   455,   455,   455,   455,   nil,   nil,   455,
   455,   nil,   nil,   nil,   455,   455,   455,   455,   nil,   nil,
   nil,   nil,   nil,   455,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   455,   455,   nil,   455,   455,   455,   455,   455,   455,
   455,   455,   455,   455,   455,   455,   456,   456,   455,   nil,
   456,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   456,   456,
   nil,   456,   456,   456,   456,   456,   456,   456,   nil,   nil,
   456,   456,   nil,   nil,   nil,   456,   456,   456,   456,   nil,
   nil,   nil,   nil,   nil,   456,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   456,   456,   nil,   456,   456,   456,   456,   456,
   456,   456,   456,   456,   456,   456,   456,   519,   519,   456,
   nil,   519,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   519,
   519,   nil,   519,   519,   519,   519,   519,   519,   519,   nil,
   nil,   519,   519,   nil,   nil,   nil,   519,   519,   519,   519,
   nil,   nil,   nil,   nil,   nil,   519,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   519,   519,   nil,   519,   519,   519,   519,
   519,   519,   519,   519,   519,   519,   519,   519,   520,   520,
   519,   nil,   520,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   520,   520,   nil,   520,   520,   520,   520,   520,   520,   520,
   nil,   nil,   520,   520,   nil,   nil,   nil,   520,   520,   520,
   520,   nil,   nil,   nil,   nil,   nil,   520,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   520,   520,   nil,   520,   520,   520,
   520,   520,   520,   520,   520,   520,   520,   520,   520,   529,
   529,   520,   nil,   529,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   529,   529,   nil,   529,   529,   529,   529,   529,   529,
   529,   nil,   nil,   529,   529,   nil,   nil,   nil,   529,   529,
   529,   529,   nil,   nil,   nil,   nil,   nil,   529,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   529,   529,   nil,   529,   529,
   529,   529,   529,   529,   529,   529,   529,   529,   529,   529,
   530,   530,   529,   nil,   530,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   530,   530,   nil,   530,   530,   530,   530,   530,
   530,   530,   nil,   nil,   530,   530,   nil,   nil,   nil,   530,
   530,   530,   530,   nil,   nil,   nil,   nil,   nil,   530,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   530,   530,   nil,   530,
   530,   530,   530,   530,   530,   530,   530,   530,   530,   530,
   530,   559,   559,   530,   nil,   559,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   559,   559,   nil,   559,   559,   559,   559,
   559,   559,   559,   nil,   nil,   559,   559,   nil,   nil,   nil,
   559,   559,   559,   559,   nil,   nil,   nil,   nil,   nil,   559,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   559,   559,   nil,
   559,   559,   559,   559,   559,   559,   559,   559,   559,   559,
   559,   559,   560,   560,   559,   nil,   560,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   560,   560,   nil,   560,   560,   560,
   560,   560,   560,   560,   nil,   nil,   560,   560,   nil,   nil,
   nil,   560,   560,   560,   560,   nil,   nil,   nil,   nil,   nil,
   560,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   560,   560,
   nil,   560,   560,   560,   560,   560,   560,   560,   560,   560,
   560,   560,   560,   566,   566,   560,   nil,   566,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   566,   566,   nil,   566,   566,
   566,   566,   566,   566,   566,   nil,   nil,   566,   566,   nil,
   nil,   nil,   566,   566,   566,   566,   nil,   nil,   nil,   nil,
   nil,   566,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   566,
   566,   nil,   566,   566,   566,   566,   566,   566,   566,   566,
   566,   566,   566,   566,   567,   567,   566,   nil,   567,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   567,   567,   nil,   567,
   567,   567,   567,   567,   567,   567,   nil,   nil,   567,   567,
   nil,   nil,   nil,   567,   567,   567,   567,   nil,   nil,   nil,
   nil,   nil,   567,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   567,   567,   nil,   567,   567,   567,   567,   567,   567,   567,
   567,   567,   567,   567,   567,   603,   603,   567,   nil,   603,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   603,   603,   nil,
   603,   603,   603,   603,   603,   603,   603,   nil,   nil,   603,
   603,   nil,   nil,   nil,   603,   603,   603,   603,   nil,   nil,
   nil,   nil,   nil,   603,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   603,   603,   nil,   603,   603,   603,   603,   603,   603,
   603,   603,   603,   603,   603,   603,   604,   604,   603,   nil,
   604,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   604,   604,
   nil,   604,   604,   604,   604,   604,   604,   604,   nil,   nil,
   604,   604,   nil,   nil,   nil,   604,   604,   604,   604,   nil,
   nil,   nil,   nil,   nil,   604,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   604,   604,   nil,   604,   604,   604,   604,   604,
   604,   604,   604,   604,   604,   604,   604,   970,   970,   604,
   nil,   970,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   970,
   970,   nil,   970,   970,   970,   970,   970,   970,   970,   nil,
   nil,   970,   970,   nil,   nil,   nil,   970,   970,   970,   970,
   nil,   nil,   nil,   nil,   nil,   970,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   970,   970,   nil,   970,   970,   970,   970,
   970,   970,   970,   970,   970,   970,   970,   970,   974,   974,
   970,   nil,   974,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   974,   974,   nil,   974,   974,   974,   974,   974,   974,   974,
   nil,   nil,   974,   974,   nil,   nil,   nil,   974,   974,   974,
   974,   nil,   nil,   nil,   nil,   nil,   974,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   974,   974,   nil,   974,   974,   974,
   974,   974,   974,   974,   974,   974,   974,   974,   974,   975,
   975,   974,   nil,   975,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   975,   975,   nil,   975,   975,   975,   975,   975,   975,
   975,   nil,   nil,   975,   975,   nil,   nil,   nil,   975,   975,
   975,   975,   nil,   nil,   nil,   nil,   nil,   975,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   975,   975,   nil,   975,   975,
   975,   975,   975,   975,   975,   975,   975,   975,   975,   975,
   nil,   547,   975,   547,   547,   547,   547,   547,   nil,   669,
   nil,   669,   669,   669,   669,   669,   547,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   669,   nil,   727,   nil,   727,   727,
   727,   727,   727,   nil,   nil,   nil,   nil,   nil,   547,   547,
   nil,   727,   nil,   nil,   nil,   nil,   669,   547,   547,   547,
   547,   nil,   nil,   nil,   547,   669,   669,   669,   669,   nil,
   nil,   nil,   669,   727,   nil,   728,   nil,   728,   728,   728,
   728,   728,   727,   727,   727,   727,   nil,   nil,   nil,   727,
   728,   nil,   804,   nil,   804,   804,   804,   804,   804,   nil,
   806,   nil,   806,   806,   806,   806,   806,   804,   nil,   nil,
   nil,   nil,   728,   nil,   nil,   806,   nil,   nil,   nil,   nil,
   nil,   728,   728,   728,   728,   nil,   nil,   nil,   728,   804,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   806,   804,   804,
   804,   804,   nil,   nil,   nil,   804,   806,   806,   806,   806,
   nil,   nil,   911,   806,   911,   911,   911,   911,   911,   nil,
   913,   nil,   913,   913,   913,   913,   913,   911,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   913,   nil,   935,   nil,   935,
   935,   935,   935,   935,   nil,   nil,   nil,   nil,   nil,   911,
   nil,   nil,   935,   nil,   nil,   nil,   nil,   913,   911,   911,
   911,   911,   nil,   nil,   nil,   911,   913,   913,   913,   913,
   nil,   nil,   nil,   913,   935,   nil,   941,   nil,   941,   941,
   941,   941,   941,   935,   935,   935,   935,   nil,   nil,   nil,
   935,   941,   nil,   990,   nil,   990,   990,   990,   990,   990,
   992,   nil,   992,   992,   992,   992,   992,   nil,   990,   nil,
   nil,   nil,   nil,   941,   nil,   992,   nil,   994,   nil,   994,
   994,   994,   994,   994,   941,   941,   nil,   nil,   nil,   941,
   990,   nil,   994,   nil,   nil,   nil,   nil,   992,   nil,   990,
   990,   990,   990,   nil,   nil,   nil,   990,   nil,   992,   992,
   nil,   nil,   nil,   992,   994,   nil,   996,   nil,   996,   996,
   996,   996,   996,   nil,   nil,   994,   994,   nil,   nil,   nil,
   994,   996,   nil,  1011,   nil,  1011,  1011,  1011,  1011,  1011,
  1028,   nil,  1028,  1028,  1028,  1028,  1028,   nil,  1011,   nil,
   nil,   nil,   nil,   996,   nil,  1028,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   996,   996,   nil,   nil,   nil,   996,
  1011,   nil,   nil,   nil,   nil,   nil,   nil,  1028,   nil,   nil,
   nil,  1011,  1011,   nil,   nil,   nil,  1011,   nil,  1028,  1028,
   nil,   nil,   nil,  1028 ]

racc_action_pointer = [
  1853,    10,   nil,   221,   nil,  5772,   909,   -79, 22505, 22633,
   -51,   nil,   -80,   -44,   240,    15,   477,   -81,   nil,   -71,
  5903,  1711,   166,   nil,   -62,   nil,    -8,   958,  1068,  6034,
  6165,  6296,   nil,  1993,  6427,  6558,   nil,    70,   225,   352,
   152,   255,  6697,  6828,   -51,  6959,    86,   507,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,  1178,   nil,  7098,
  7229,  7360,     4,   nil,  7491,  7622,   nil,   nil,  7753,  7892,
  8023,  8154, 23017,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   624,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
     0,   nil,   nil,   112,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   224,   nil,  8293,   nil,   nil,   nil,
   nil,  8432,  8563,  8694,  8825,  8964,   nil,  2133,   nil,   287,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   115,   nil,  2273,  9095,  9226,  9357,  9488,  9619,  9750, 23191,
 23252,   nil,   nil,  9881, 10012, 10143,   nil,   nil,   574,   -54,
   166,   202,   217,   124,   216,   nil, 10274,  2413,   232, 10405,
 10536, 10667, 10798, 10929, 11060, 11191, 11322, 11453, 11584, 11715,
 11846, 11977, 12108, 12239, 12370, 12501, 12632, 12763, 12894, 13025,
 13156,   nil,   nil,   nil,   nil, 13287,   nil,   nil, 23313,   nil,
   nil,   258, 13418, 13549,   nil,   nil,   nil,   nil,   nil,   nil,
   nil, 13680,   nil,  2133,   nil,   249,   263,   nil, 13811,   328,
 13942,   nil,   nil, 14073, 14204,   nil,   nil,   295,   nil, 14343,
  1331,   337,   315,  2553,   340,   391,   350, 14474,  2693,   576,
   682,   686,   441,   718,   nil,   409,   387,    33,   nil,   nil,
   nil,   458,   360,   418, 14613,   nil,   424,   497,   771,   nil,
   528, 14744,   nil, 14875,  2833,  1396,   484,   nil,   398,   503,
   528,   511,   575,   550,   nil,   nil,   326,    -1,    11, 15006,
  2973,  3113,   298,   641,   533,   -18,    11,   794,   644,    25,
   677,   nil,   nil,   342,   434,   -21,   nil,   834,   nil,   596,
 15137,   nil,   nil,   nil,   194,   230,   255,   373,   413,   481,
   506,   508,   550,   nil,   551,   nil, 15268,   nil,   327,   388,
   395,   400,   456,   -41,   -35,   462,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   611, 22761,   nil,   nil,   nil,   nil,
   612,   nil,   nil,   600, 15399,   605,   nil,   nil,   600,   nil,
   837,   313,   701,   nil,   nil,  1853,   nil,   nil,   nil,   nil,
   nil,  1993,   615,   nil,   627,   632,   509,   521,  1314,   nil,
   nil,   nil,   222,   334,   678,   nil,   nil,  1446,  1582,   nil,
   nil,   nil,   -35,   nil,   683, 23374, 23435, 15530,   328, 15661,
 15792, 15923,  2833,  2973,   523,   563,   708,   710,   711,   712,
  1667,  4233,   666,  3113,  3253,  3393,  3533,  3673,  3813,   915,
  1465,  3953,  4093,  2273,  1397,   nil,  1718,   nil,   nil,   nil,
   nil,   669,   nil,   nil,   nil,   673,   nil,   nil, 16054,   nil,
 16185,   nil, 16316,   nil,   363,   nil,   nil,   nil, 16455,  1427,
   nil,   675,   675,   nil,   nil,   676, 16594,   683, 16725, 23496,
 23557,   870,   726,   nil, 16856,   685,   nil, 16987, 17118, 23618,
 23679,  1531,  2413, 17249,   823,   822,   702,   747,   nil, 17380,
   nil,   nil, 17511,   nil,   nil,   nil,   nil, 24290,  3253,   826,
   nil,  3393,    62,   834,   841,   836,   844, 17642, 17773, 23740,
 23801,    27,   nil,   nil,   930,   nil, 23862, 23923, 17904,   nil,
   nil,   250,  3533,   765,   nil,   -33,   nil,   nil,   nil,   832,
   nil,   nil,   nil,   739,   nil,   nil,   259,   nil,   338,   nil,
   nil,   727,   nil,   741,   nil,   nil,   nil, 22889,   nil,   744,
 18035, 18166, 18297, 23984, 24045, 18436, 18567,   552,   787, 18698,
 18829, 18960, 19091,   786,   nil,   nil, 19222, 19353,   787,   nil,
   nil,   nil,   343,   358,   466,   604,   758,   774,   906,   nil,
   890,     6,   nil,   nil,   807,   102,   913,   nil,   790,   nil,
   838, 19484,   nil,   nil, 19615,   nil,   -83, 19746,   808,   nil,
   830,   123,   180,   873,   248,  1038,   875,   840, 19877,   nil,
   909,   214,   964, 20008,   nil,   nil,   nil,   596,   nil, 24298,
   nil,   847,   849,   nil,   857,   858,   859,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   853,  1178,   nil,   nil, 20139,
   nil,   nil,   nil,   952,   nil,   nil,   nil,   958,   nil,   nil,
   959,   516,   nil,   997,   nil,   nil,   nil,   nil,   nil,  1006,
   nil,    26,   886,    40,    41,   151,   185,  3673,   717,  1040,
   nil,   892,  3813, 20270,   nil,  1029,  3953, 24315, 24354, 23130,
   nil,   nil,   nil,   nil,   nil,   nil,  4093,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   910, 20401,   914,   516,   519,   714,
   826,   nil,  2553, 20532,   nil,   913,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil, 20663, 20794, 20925, 21056,
   520, 21187,   nil,   160,   nil,   nil,   936,   nil,   nil,   889,
   nil,  4233,   nil,   nil,   nil,   nil,   917,   236,   nil,   nil,
  1045,   nil,  4373,   922,   970,   nil,   nil,   nil,    64,   929,
   780,   nil,   nil,   556, 24371,   nil, 24379,   nil,  1377,   nil,
 21318,   nil,  1241,   nil,   929,   228,   943,   nil,   nil,   nil,
   nil,  1065,   nil, 21449,  1069,   nil, 21580,  4513,    93,  1070,
   nil,  1077,   480,  4653,   nil,  1078,   962,   554,   nil,   969,
   964,   553,   nil,   nil, 21711, 21842,  2693,  4793,   nil,   965,
   968,   nil,   969,   970,   973,   nil,  1003,   983,   979,   973,
 21973,   nil,   nil,   nil,   nil,  4933,   nil,   nil,    32, 22104,
   nil,   nil,   nil,   nil,  1028,   986,   nil,   nil,   nil,   987,
   988,   nil,   990,   992,   nil,   993,   nil,   nil,   998,  1280,
   997,   819,   nil,   nil,    33,   nil,   nil,    39,   nil,   nil,
   nil,  1123,   nil,   nil,   nil,  1060,   nil,   nil,  1203,   nil,
   nil, 24431,   nil, 24439,   nil,  6605,   nil,   nil,  1044,  1171,
 22235,  1006,  1099,   nil,  5073,    34,    35,  1116,  1101,    36,
   nil,  5213,  5353,   nil,   nil, 24456,   nil,  8201,   nil, 14521,
   nil, 24495,   nil,   nil,   nil,   nil,   335,   951,  1026,  5493,
   nil,   nil,   nil,   nil,  5633,  1027,   nil,   nil,  1030,  1032,
  1034,  1036,   nil,  1039,   nil,   628,   nil,   nil,   nil,  1146,
 24106,   nil,  1176, 22366, 24167, 24228,    42,  1071,  1179,  1056,
  1063,  1064,  1069,  1074,  1291,  1075,  1307,   706,  1202,   nil,
 24512,   nil, 24519,   nil, 24536,   nil, 24575,   nil,   nil,   nil,
  1183,  1123,  1124,   nil,  1086,    98,   102,   111,   138,   nil,
   nil, 24592,   nil,   nil,   nil,   nil,  1312,  1094,   nil,   nil,
  1108,  1109,  1110,  1130,   nil,   145,  1131,  1134, 24599,   nil,
   nil,   nil,   nil,   nil,  1137,   nil ]

racc_action_default = [
    -3,  -611,    -1,  -597,    -4,  -611,    -7,  -611,  -611,  -611,
  -611,   -30,  -611,  -611,   -34,  -611,  -611,  -289,   -46,  -599,
  -611,   -51,   -55,   -56,   -57,   -61,  -266,  -266,  -266,  -302,
  -332,  -333,   -73,   -12,   -77,   -85,   -87,  -611,  -501,  -502,
  -611,  -611,  -611,  -611,  -225,  -611,  -599,  -239,  -280,  -281,
  -282,  -283,  -284,  -285,  -286,  -287,  -288,  -585,  -291,  -295,
  -610,  -575,  -310,  -312,  -611,  -611,   -53,   -53,  -597,  -611,
  -611,  -611,  -611,  -334,  -335,  -337,  -338,  -339,  -340,  -442,
  -443,  -444,  -445,  -446,  -467,  -449,  -450,  -469,  -471,  -454,
  -459,  -463,  -465,  -481,  -467,  -483,  -485,  -486,  -487,  -488,
  -583,  -490,  -491,  -584,  -493,  -494,  -495,  -496,  -497,  -498,
  -499,  -500,  -505,  -506,  -611,    -2,  -598,  -606,  -607,  -608,
    -6,  -611,  -611,  -611,  -611,  -611,    -8,    -3,   -18,  -611,
  -116,  -117,  -118,  -119,  -120,  -121,  -122,  -123,  -124,  -128,
  -129,  -130,  -131,  -132,  -133,  -134,  -135,  -136,  -137,  -138,
  -139,  -140,  -141,  -142,  -143,  -144,  -145,  -146,  -147,  -148,
  -149,  -150,  -151,  -152,  -153,  -154,  -155,  -156,  -157,  -158,
  -159,  -160,  -161,  -162,  -163,  -164,  -165,  -166,  -167,  -168,
  -169,  -170,  -171,  -172,  -173,  -174,  -175,  -176,  -177,  -178,
  -179,  -180,  -181,  -182,  -183,  -184,  -185,  -186,  -187,  -188,
  -189,  -190,  -191,  -192,  -193,  -194,  -195,  -196,  -197,  -198,
   -23,  -125,   -12,  -611,  -611,  -611,  -611,  -611,  -256,  -611,
  -611,  -595,  -596,  -611,  -611,  -599,  -600,   -50,  -611,  -501,
  -502,  -611,  -289,  -611,  -611,  -231,  -611,   -12,  -611,  -210,
  -211,  -611,  -611,  -611,  -611,  -611,  -611,  -611,  -611,  -611,
  -611,  -611,  -611,  -611,  -611,  -611,  -611,  -611,  -611,  -611,
  -611,  -240,  -241,  -242,  -243,  -611,  -406,  -408,  -611,  -593,
  -594,   -62,  -256,  -611,  -309,  -412,  -421,  -423,   -68,  -418,
   -69,  -599,   -70,  -246,  -261,  -270,  -270,  -265,  -611,  -271,
  -611,  -467,  -577,  -611,  -611,   -71,   -72,  -597,   -13,  -611,
   -16,  -611,   -75,   -12,  -599,  -611,   -78,   -81,   -12,   -93,
   -94,  -611,  -611,  -101,  -302,  -305,  -599,  -611,  -332,  -333,
  -336,  -419,  -611,   -83,  -611,   -89,  -299,  -484,  -611,  -219,
  -220,  -611,  -232,  -611,   -12,  -293,  -599,  -247,  -603,  -603,
  -611,  -611,  -603,  -611,  -311,  -397,   -52,  -611,  -611,  -611,
   -12,   -12,  -597,  -611,  -598,  -501,  -502,  -611,  -611,  -289,
  -611,  -350,  -351,  -111,  -112,  -611,  -114,  -611,  -289,  -509,
  -611,  -501,  -502,  -325,  -116,  -117,  -158,  -159,  -160,  -176,
  -181,  -188,  -191,  -327,  -611,  -573,  -611,  -447,  -611,  -611,
  -611,  -611,  -611,  -611,  -611,  -611,  1036,    -5,  -609,   -24,
   -25,   -26,   -27,   -28,  -611,  -611,   -20,   -21,   -22,  -126,
  -611,   -31,   -33,  -276,  -611,  -611,  -275,   -32,  -611,   -35,
  -611,  -289,   -43,   -45,  -199,  -251,  -271,   -47,   -48,   -36,
  -200,  -251,  -599,  -257,  -270,  -270,  -586,  -587,  -266,  -416,
  -588,  -589,  -587,  -586,  -266,  -415,  -417,  -588,  -589,   -42,
  -207,   -49,  -599,  -308,  -611,  -611,  -611,  -256,  -299,  -611,
  -611,  -611,  -208,  -209,  -212,  -213,  -214,  -215,  -216,  -217,
  -221,  -222,  -223,  -224,  -226,  -227,  -228,  -229,  -230,  -233,
  -234,  -235,  -236,  -599,  -244,  -427,  -266,  -586,  -587,   -59,
   -63,  -599,  -267,  -425,  -427,  -599,  -304,  -262,  -611,  -263,
  -611,  -268,  -611,  -272,  -611,  -580,  -582,   -11,  -598,   -15,
   -17,  -599,   -74,  -297,   -90,   -79,  -611,  -599,  -256,  -611,
  -611,  -100,  -611,  -484,  -611,   -86,   -91,  -611,  -611,  -611,
  -611,  -245,  -237,  -611,  -434,  -611,  -599,  -611,  -248,  -605,
  -604,  -250,  -605,  -300,  -301,  -576,  -313,  -533,   -12,  -341,
  -342,   -12,  -611,  -611,  -611,  -611,  -611,  -611,  -256,  -611,
  -611,  -299,   -53,  -111,  -112,  -113,  -611,  -611,  -256,  -321,
  -507,  -611,   -12,  -511,  -329,  -599,  -448,  -468,  -473,  -611,
  -475,  -451,  -470,  -611,  -472,  -453,  -611,  -456,  -611,  -458,
  -461,  -611,  -462,  -611,  -482,    -9,   -19,  -611,   -29,  -279,
  -611,  -611,  -256,  -611,  -611,  -611,  -611,  -420,  -611,  -258,
  -260,  -611,  -611,   -64,  -255,  -413,  -611,  -611,   -66,  -414,
  -307,  -601,  -586,  -587,  -586,  -587,  -599,  -611,  -611,  -428,
   -58,  -409,  -425,  -253,  -611,  -386,  -611,  -303,  -270,  -269,
  -273,  -611,  -578,  -579,  -611,   -14,   -76,  -611,   -82,   -88,
  -599,  -586,  -587,  -254,  -590,   -99,  -611,   -84,  -611,  -206,
  -218,  -599,  -610,  -610,  -292,  -294,  -296,  -603,  -398,  -533,
  -401,  -572,  -572,  -516,  -518,  -518,  -518,  -532,  -534,  -535,
  -536,  -537,  -538,  -539,  -540,  -541,  -611,  -543,  -545,  -547,
  -552,  -554,  -555,  -557,  -562,  -564,  -565,  -567,  -568,  -569,
  -611,  -610,  -343,  -610,   -54,  -344,  -345,  -316,  -317,  -611,
  -319,  -611,  -599,  -586,  -587,  -590,  -298,   -12,  -111,  -112,
  -115,  -599,   -12,  -611,  -323,  -611,   -12,  -533,  -533,  -611,
  -574,  -474,  -477,  -478,  -479,  -480,   -12,  -452,  -455,  -457,
  -460,  -464,  -466,  -127,  -277,  -611,  -599,  -586,  -587,  -587,
  -586,   -44,  -252,  -611,  -602,  -270,   -38,  -202,   -39,  -203,
   -65,   -40,  -205,   -41,  -204,   -67,  -611,  -611,  -611,  -611,
  -420,  -611,  -407,  -386,  -411,  -410,  -611,  -422,  -387,  -599,
  -389,   -12,  -424,  -264,  -274,  -581,   -80,  -420,   -92,  -306,
  -610,  -348,   -12,  -435,  -610,  -436,  -437,  -249,  -611,  -599,
  -611,  -514,  -515,  -611,  -611,  -525,  -611,  -528,  -611,  -530,
  -611,  -352,  -611,  -354,  -356,  -363,  -599,  -546,  -556,  -566,
  -570,  -611,  -346,  -611,  -611,  -318,  -611,   -12,  -420,  -611,
  -420,  -611,  -611,   -12,  -326,  -611,  -599,  -611,  -330,  -611,
  -278,  -420,   -37,  -201,  -259,  -611,  -238,   -12,   -60,  -572,
  -572,  -368,  -370,  -370,  -370,  -385,  -611,  -599,  -391,  -541,
  -549,  -550,  -560,  -426,   -10,   -12,  -441,  -349,  -611,  -611,
  -439,  -399,  -402,  -404,  -611,  -572,  -553,  -571,  -517,  -518,
  -518,  -544,  -518,  -518,  -563,  -518,  -541,  -558,  -599,  -611,
  -361,  -611,  -542,  -314,  -611,  -315,  -273,  -610,  -320,  -322,
  -508,  -611,  -328,  -510,  -512,  -511,  -476,  -429,  -611,  -366,
  -367,  -376,  -378,  -611,  -381,  -611,  -383,  -388,  -611,  -611,
  -611,  -548,  -611,  -440,   -12,  -501,  -502,  -611,  -611,  -289,
  -438,   -12,   -12,  -400,  -513,  -611,  -521,  -611,  -523,  -611,
  -526,  -611,  -529,  -531,  -353,  -355,  -359,  -611,  -364,   -12,
  -430,  -431,  -432,  -324,   -12,  -572,  -551,  -369,  -370,  -370,
  -370,  -370,  -561,  -370,  -390,  -599,  -393,  -395,  -396,  -559,
  -611,  -299,  -434,  -256,  -611,  -611,  -299,  -611,  -611,  -518,
  -518,  -518,  -518,  -357,  -611,  -362,  -611,  -610,  -611,  -365,
  -611,  -373,  -611,  -375,  -611,  -379,  -611,  -382,  -384,  -392,
  -611,  -298,  -590,  -433,  -599,  -586,  -587,  -590,  -298,  -403,
  -405,  -611,  -519,  -522,  -524,  -527,  -611,  -360,  -347,  -331,
  -370,  -370,  -370,  -370,  -394,  -420,  -518,  -358,  -611,  -371,
  -374,  -377,  -380,  -520,  -370,  -372 ]

racc_goto_table = [
   224,   133,   133,    16,   284,   284,   284,   337,    16,   383,
   267,   344,   416,   417,   285,   285,   285,   726,   548,   551,
   128,   211,   556,   228,   268,   119,   639,   333,   639,   275,
   279,   432,   228,   228,   228,   499,    16,   311,   311,   662,
   347,   348,     6,   429,   352,   338,   525,     6,   227,   449,
   607,   219,   350,   351,   821,   438,   444,   327,   305,   269,
   136,   136,    16,   116,   126,   133,   565,   228,   228,   490,
   306,   228,   357,   367,   367,   138,   138,   884,   642,   853,
   790,     2,   801,   802,   423,   491,   115,   423,   535,   323,
   119,   538,   541,   423,   120,   545,   634,   399,   400,   401,
   402,   335,   881,   816,   486,   781,   320,   320,   302,   966,
   945,   968,   271,   278,   280,     1,   304,   962,   864,    16,
   642,   824,   388,   645,   228,   228,   228,   228,    16,   210,
    16,   628,   395,   286,   286,   286,   362,   639,   639,   412,
   636,   320,   320,   320,   346,   346,   430,   405,   346,   369,
   373,   353,   450,   586,   588,   670,   948,   704,     6,   632,
   597,   331,   340,   677,   339,   342,   631,   403,   385,     6,
   341,   546,   360,   569,   282,   295,   296,   856,   384,   334,
   536,   336,   345,   722,   499,   833,   572,   573,   642,   729,
  1024,   905,   968,   284,   962,   855,   857,   874,   415,   415,
   965,   346,   346,   346,   346,   397,   884,   945,   404,   881,
   668,   958,   985,   448,   871,    16,   228,   420,   228,   228,
   420,   228,   439,   547,   709,   798,   420,   228,   228,   888,
   931,   582,   584,   587,   587,   776,   932,   410,   648,   881,
    16,   411,   422,   847,   770,   422,   657,   284,   284,  1017,
   485,   422,   493,   494,   773,   918,   284,   950,   285,   794,
   909,   910,   305,   455,   868,   387,   285,   720,   787,   389,
   626,   390,   391,   392,   393,   228,   228,   799,   394,   731,
   736,  1027,   723,    26,   228,   677,   934,   879,    26,   876,
   956,   438,   444,   nil,   881,   427,   428,   522,   nil,   nil,
   nil,   nil,    16,    26,   451,   452,    16,   nil,   nil,   nil,
   311,    16,    26,    26,    26,   951,    26,   537,   nil,   119,
   275,   nil,   nil,   nil,   279,   552,   725,   311,   305,   504,
   828,   650,   nil,   305,   nil,   836,   837,    16,   523,   830,
  1018,   509,    26,   677,   677,   nil,   519,    26,    26,   954,
   nil,    26,   228,    16,    16,   653,   nil,   508,   510,   nil,
   526,   515,   nil,   529,   841,   653,   989,   nil,   nil,   786,
   nil,   712,   639,   228,   119,   553,   554,   286,   302,   320,
   507,   721,   827,   302,   574,   286,   511,   783,   450,   228,
   430,   517,   559,   nil,   nil,   653,   320,   nil,   133,    26,
   nil,   nil,   nil,   653,    26,    26,    26,    26,    26,   nil,
    26,   760,   nil,   608,   nil,   746,   765,   596,   492,   269,
   797,   nil,   642,   nil,   nil,   nil,   495,   nil,   739,   346,
   739,   nil,   284,   nil,   nil,   555,   nil,   756,   758,   438,
   444,   614,   761,   763,   nil,   nil,   453,   619,   nil,   448,
   571,   nil,   nil,   nil,   nil,   603,   659,   136,   439,   nil,
   228,   nil,   795,   nil,   627,   nil,   575,   nil,   nil,   nil,
   nil,   423,   138,   nil,   nil,   nil,   831,  1003,   nil,   nil,
   835,   423,   423,   nil,   nil,   nil,   423,   423,   nil,   614,
   nil,   nil,   nil,   284,   nil,    26,    26,    26,    26,    26,
    26,    26,   496,   nil,   783,   nil,    26,    26,    26,   nil,
   nil,    16,   nil,   448,   nil,   nil,   nil,   nil,   nil,   311,
    26,   228,   439,   448,   613,   512,   nil,   311,   nil,   nil,
   618,   nil,   439,   284,   nil,   nil,   228,   nil,   860,   924,
   757,   759,   711,   284,   nil,   762,   764,   nil,   717,   nil,
   nil,    16,   526,   448,    16,    26,    26,   nil,   854,   448,
   526,   228,   439,   nil,    26,   949,   952,   nil,   nil,   nil,
   439,   228,   630,   701,   705,    16,   703,   284,   nil,   842,
   775,   nil,    26,   880,   nil,   882,    26,   901,   320,   nil,
   133,    26,   nil,   724,   nil,   nil,   320,   448,   nil,   nil,
   nil,   907,   638,   nil,   420,   228,   439,   608,    16,   743,
   796,   nil,   nil,   661,   420,   420,   nil,    26,   774,   420,
   420,   nil,  1025,   423,   nil,   nil,   nil,   nil,   nil,   422,
   nil,   608,    26,    26,    26,   nil,   nil,   nil,   nil,   422,
   422,   nil,   nil,   667,   422,   422,   nil,   751,   415,   136,
   311,   nil,    13,    26,   nil,   nil,   614,    13,   nil,   619,
   nil,   311,   nil,   nil,   138,   nil,   838,   860,   nil,    26,
   860,   nil,   860,   620,   860,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   843,   526,   nil,    13,   978,   nil,   nil,   nil,
   959,   nil,   960,   608,   788,   757,   759,   764,   762,   832,
   nil,   nil,   608,   nil,   nil,   nil,   nil,   nil,   988,   nil,
   nil,    13,   633,   755,   979,   nil,   637,   nil,   nil,   320,
    16,   361,   133,   nil,   nil,    16,   228,   608,   nil,    16,
   320,   nil,   646,   nil,   685,   nil,   nil,   866,   649,    16,
    26,   870,   829,   nil,   nil,   nil,   nil,   nil,   nil,   860,
   nil,   860,   nil,   860,   nil,   860,   420,   665,   nil,   nil,
   858,   839,   nil,   nil,   nil,   nil,   nil,   nil,    13,  1020,
   nil,   nil,   nil,   nil,   843,   nil,   nil,    13,   nil,    13,
   858,   422,   nil,   nil,    16,   nil,  1004,   860,   nil,   nil,
   nil,    26,   nil,   nil,   nil,    16,   730,   nil,   nil,   894,
   nil,    26,   nil,   346,   nil,   nil,   863,   nil,   nil,   nil,
   653,   nil,   nil,   nil,   nil,    29,    26,   867,   nil,   nil,
    29,   811,   nil,   nil,   nil,   nil,   228,   nil,   nil,   nil,
    16,    26,   nil,   nil,    26,    29,    16,   nil,   858,   nil,
   nil,    26,   nil,   nil,    29,    29,    29,   nil,    29,   nil,
    16,    26,   897,   nil,   900,    26,   685,   nil,   nil,   904,
   nil,   nil,   nil,   921,    13,   nil,   418,   nil,    16,   418,
   nil,   nil,   927,   nil,    29,   418,   nil,   nil,   nil,    29,
    29,   nil,   789,    29,    26,    26,   nil,   nil,    26,    13,
   923,   nil,   nil,   nil,    26,    26,   nil,   nil,   nil,    26,
    26,   nil,   nil,   346,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   685,   685,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   969,   nil,   nil,   nil,    16,   nil,   nil,
   nil,    29,   320,   nil,    16,    16,    29,    29,    29,    29,
    29,   320,    29,   nil,   850,   nil,   999,   811,   284,   972,
   890,    13,    16,   nil,   nil,    13,   977,    16,   nil,   nil,
    13,   nil,   974,   448,   nil,   875,   859,   nil,   448,   nil,
   805,   807,   809,   nil,   987,   nil,   228,   439,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   608,    13,   nil,   nil,   nil,
   nil,   685,   320,   685,   nil,   nil,   nil,   nil,   nil,   nil,
    26,   nil,    13,    13,   nil,    26,    26,   nil,    15,    26,
   nil,   nil,   nil,    15,   nil,   nil,   nil,   nil,   nil,    26,
   nil,   nil,   nil,   nil,   811,   nil,   811,    29,    29,    29,
    29,    29,    29,    29,   nil,   nil,    26,   892,    29,    29,
    29,    15,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    29,   nil,   nil,   nil,   nil,   903,   nil,   nil,
   nil,   nil,   nil,   nil,    26,   nil,   nil,    15,   nil,   nil,
   851,   nil,   nil,   955,   nil,    26,   850,   nil,   850,   nil,
   850,   983,   811,   nil,   nil,   nil,   nil,    29,    29,   nil,
   nil,   877,   nil,   nil,   877,   nil,    29,   nil,   859,   nil,
   859,   nil,   852,   nil,   nil,   nil,    26,   nil,   nil,   944,
    26,   nil,   nil,   nil,    29,   nil,    26,   nil,    29,   811,
   nil,   811,   685,    29,    15,   nil,   nil,   nil,   nil,   883,
    26,   885,   nil,    15,   nil,    15,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    26,    29,
   nil,   811,   nil,   nil,   nil,   850,   nil,   850,   nil,   850,
    13,   850,   nil,   nil,    29,    29,    29,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   936,   938,   859,   940,   942,
   nil,   943,   nil,   nil,   nil,    29,   912,   914,   916,   nil,
   nil,   nil,   nil,   850,   nil,   nil,   nil,   nil,   nil,   877,
    13,    29,   851,    13,   851,   nil,   851,    26,   nil,   nil,
   nil,   nil,   nil,   nil,    26,    26,   nil,   nil,   nil,   nil,
    15,   nil,    15,   nil,    13,    15,   nil,   nil,   nil,   nil,
   nil,    15,    26,   nil,   nil,   nil,   961,    26,   963,   nil,
   nil,   nil,   nil,   nil,   nil,    15,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   418,   nil,   nil,    26,    13,   980,   nil,
   981,   nil,   982,   418,   418,   nil,    17,   nil,   418,   418,
   nil,    17,    29,   nil,   nil,  1012,  1013,  1014,  1015,   nil,
   nil,   851,   nil,   851,   nil,   851,   nil,   851,   nil,   nil,
   nil,   nil,   991,   993,   995,   997,   nil,   998,   nil,    17,
   313,   313,   nil,   nil,   nil,   nil,   nil,    15,   nil,   nil,
   nil,    15,   nil,  1021,   nil,  1022,    15,  1023,   nil,   851,
   nil,   nil,  1033,    29,   nil,    17,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    29,  1026,   359,   368,   368,   nil,   nil,
   nil,   nil,    15,   nil,   nil,   nil,   nil,   nil,    29,   nil,
   nil,  1034,   nil,   nil,  1029,  1030,  1031,  1032,    15,    15,
   nil,   nil,   nil,    29,   nil,   nil,    29,   nil,  1035,    13,
   nil,   nil,   nil,    29,    13,   nil,   nil,   nil,    13,   nil,
   nil,   nil,    17,    29,   nil,   nil,   nil,    29,    13,   nil,
   nil,    17,   nil,    17,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    38,   nil,   nil,   418,   nil,    38,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    29,    29,   nil,   nil,
    29,   nil,   nil,   nil,   nil,   nil,    29,    29,   nil,   nil,
   nil,    29,    29,    13,   nil,    38,   309,   309,   nil,   nil,
   nil,   nil,   nil,   nil,    13,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   355,   371,   371,   371,   nil,   nil,   nil,    17,    13,
   421,   nil,   nil,   421,   nil,    13,   nil,   nil,   nil,   421,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    13,
   nil,   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    15,    13,    38,   nil,
   nil,   930,   nil,   nil,   nil,   nil,   nil,    38,   nil,    38,
   nil,   nil,    29,   nil,   nil,   nil,   nil,    29,    29,   nil,
   nil,    29,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    29,   nil,   nil,   nil,   nil,    15,   nil,   nil,    15,
   nil,   nil,   nil,   nil,   nil,    17,   nil,   nil,    29,    17,
   nil,   nil,   nil,   313,    17,   nil,    13,   nil,   nil,   nil,
    15,   nil,   nil,    13,    13,   nil,   nil,   nil,   nil,   nil,
   313,   nil,   nil,   nil,   nil,   nil,    29,   nil,   nil,   nil,
    17,    13,   nil,   nil,   nil,   nil,    13,    29,   nil,    15,
   nil,   nil,   nil,    15,    38,   nil,    17,    17,   nil,    15,
    15,   nil,   nil,   nil,    15,    15,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    29,    38,
   nil,   nil,    29,   nil,   nil,   nil,   nil,   nil,    29,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    29,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    29,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    38,   nil,   nil,   nil,    38,   nil,   nil,   nil,   309,
    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    15,   309,   nil,   nil,   nil,
    15,   nil,    39,   nil,    15,   nil,    38,    39,   nil,    29,
   nil,   nil,   nil,   nil,    15,   nil,    29,    29,   nil,   nil,
   nil,   nil,    38,    38,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    15,   nil,   nil,    29,    39,   310,   310,   nil,    29,
   nil,   nil,   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   313,   nil,   nil,   nil,   nil,   nil,    29,    15,
   313,    39,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    15,   356,   372,   372,   372,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    17,   nil,   nil,    17,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    15,   nil,   nil,    17,   nil,
   nil,    15,   nil,   nil,   nil,   735,   nil,   nil,    39,   nil,
   nil,   nil,   nil,   nil,   nil,    15,   nil,    39,   nil,    39,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   421,   nil,   nil,
   nil,    17,   nil,    15,   nil,   nil,   nil,   421,   421,   nil,
   nil,   nil,   421,   421,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    38,   nil,   nil,   313,   nil,   nil,   nil,   nil,   309,   nil,
   nil,   nil,   nil,   nil,   313,   nil,   309,   nil,   nil,   nil,
   nil,   nil,    15,   nil,   nil,   nil,   nil,   nil,   nil,    15,
    15,   nil,   nil,   nil,    39,   nil,   nil,   nil,   nil,   nil,
    38,   nil,   nil,    38,   nil,   nil,   nil,    15,   nil,   nil,
   nil,   nil,    15,   nil,   nil,   nil,   nil,   nil,   nil,    39,
   nil,   nil,   nil,   nil,    38,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    17,   nil,   nil,   nil,   nil,    17,   nil,
   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   343,    17,   nil,   nil,   nil,   nil,    38,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   421,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    39,   nil,   nil,   nil,    39,   nil,   nil,   nil,   310,
    39,   nil,   nil,   nil,   nil,   nil,   nil,    17,   nil,   309,
   nil,   nil,   nil,   nil,   nil,   nil,   310,   nil,    17,   nil,
   309,   nil,   nil,   nil,   nil,   nil,    39,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    39,    39,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,    17,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,    38,
   nil,   nil,   nil,   nil,    38,   nil,   368,   nil,    38,   nil,
   nil,    17,   nil,   nil,   nil,   929,   nil,   nil,    38,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   413,   426,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    38,   nil,   nil,   368,   nil,   nil,   nil,
    17,   235,   nil,   nil,    38,   nil,   nil,    17,    17,   nil,
   283,   283,   283,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   329,   330,    17,   332,   nil,   nil,   nil,
    17,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   501,    38,
   503,   283,   283,   505,   506,    38,   nil,   nil,   nil,   nil,
    39,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   310,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   310,   nil,   nil,   nil,
   nil,   nil,   371,   nil,   nil,   nil,   nil,    38,   nil,   nil,
   nil,   925,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    39,   nil,   nil,    39,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    39,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   371,   nil,   nil,   nil,    38,   nil,   nil,   nil,
   nil,   nil,   nil,    38,    38,   nil,   nil,    39,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    38,   nil,   nil,   599,   nil,    38,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   310,
   nil,   nil,   nil,   nil,   283,   425,   nil,   nil,   431,   283,
   310,   nil,   nil,   nil,   431,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   235,   nil,   nil,
   462,   463,   464,   465,   466,   467,   468,   469,   470,   471,
   472,   473,   474,   475,   476,   477,   478,   479,   480,   481,
   482,   483,   nil,   nil,   nil,   nil,   484,   nil,   640,   nil,
   343,   nil,   643,   283,   283,   nil,   nil,   nil,   nil,    39,
   nil,   nil,   283,   nil,    39,   nil,   nil,   nil,    39,   283,
   nil,   283,   nil,   nil,   283,   283,   nil,   nil,    39,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   640,
   nil,   nil,   343,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   426,   nil,   nil,
   nil,   nil,   531,   nil,   532,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    39,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    39,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   744,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   640,
   343,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    39,
   nil,   nil,   nil,   nil,   nil,    39,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    39,
   nil,   784,   nil,   nil,   785,   283,   nil,   nil,   nil,   nil,
   nil,   nil,   372,   nil,   nil,   nil,   nil,    39,   nil,   nil,
   nil,   926,   nil,   793,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   283,   817,
   431,   431,   431,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   372,   nil,   nil,   nil,    39,   nil,   nil,   nil,
   nil,   nil,   nil,    39,    39,   nil,   nil,   nil,   nil,   283,
   nil,   283,   nil,   283,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    39,   nil,   nil,   nil,   840,    39,   nil,   nil,   283,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   431,   660,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   283,   nil,   nil,   283,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   283,   283,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   283,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   887,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   896,   nil,   nil,   nil,
   nil,   283,   431,   283,   nil,   nil,   nil,   752,   nil,   nil,
   283,   283,   431,   431,   343,   nil,   nil,   431,   431,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   283,   nil,   nil,   283,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   283,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   283,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   283,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   431,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   431,   431,   431,
   431,   nil,   846,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   283,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   283,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   283,   431,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   283 ]

racc_goto_check = [
    34,    64,    64,    29,    39,    39,    39,    77,    29,    63,
   145,    96,    23,    23,    78,    78,    78,   108,    99,    99,
    16,    16,   101,    29,    42,   105,    84,    34,    84,    81,
    81,    30,    29,    29,    29,    83,    29,    29,    29,    11,
    18,    18,     7,    27,    18,    78,    58,     7,    21,    27,
    31,    32,    36,    36,   100,    43,    43,    71,    10,    32,
    67,    67,    29,     6,     8,    64,    61,    29,    29,    45,
    56,    29,    29,    29,    29,    68,    68,   185,   189,   132,
    12,     2,   127,   127,    19,    30,     4,    19,     9,    57,
   105,    79,    79,    19,     5,    79,    46,    18,    18,    18,
    18,     7,   180,   122,    43,   134,    73,    73,    52,   138,
   120,   139,    44,    44,    44,     1,    53,   186,    13,    29,
   189,    12,   162,    15,    29,    29,    29,    29,    29,    17,
    29,   148,   162,    80,    80,    80,    20,    84,    84,    24,
   148,    73,    73,    73,    25,    25,    70,    26,    25,    62,
    62,     4,    70,   166,   166,   144,   123,    37,     7,    48,
    69,    74,    76,   174,    80,    80,    82,     7,    94,     7,
    95,    98,   102,   104,    51,    51,    51,   136,   109,   110,
   111,   112,   113,   114,    83,   115,   116,   117,   189,   118,
   138,   119,   139,    39,   186,   124,   130,   136,    78,    78,
   137,    25,    25,    25,    25,     5,   185,   120,     2,   180,
   140,   132,   123,    64,   141,    29,    29,    29,    29,    29,
    29,    29,    81,   142,   101,   143,    29,    29,    29,   122,
   146,   167,   167,   167,   167,    46,   147,    10,    58,   180,
    29,    21,    21,   134,    31,    21,    58,    39,    39,   123,
   149,    21,   151,   152,   155,   136,    39,   156,    78,   157,
   127,   127,    10,    32,   158,   161,    78,    61,    31,   163,
    30,   164,   165,   168,   169,    29,    29,   144,   170,   171,
   172,   123,   173,    47,    29,   174,   127,   178,    47,   182,
   183,    43,    43,   nil,   180,    25,    25,    34,   nil,   nil,
   nil,   nil,    29,    47,    25,    25,    29,   nil,   nil,   nil,
    29,    29,    47,    47,    47,    12,    47,    34,   nil,   105,
    81,   nil,   nil,   nil,    81,    18,     9,    29,    10,   162,
    31,    30,   nil,    10,   nil,   144,   144,    29,    71,    31,
   100,     7,    47,   174,   174,   nil,    32,    47,    47,   108,
   nil,    47,    29,    29,    29,    43,   nil,     6,     8,   nil,
    56,    57,   nil,    32,    31,    43,   127,   nil,   nil,    58,
   nil,    30,    84,    29,   105,    10,    10,    80,    52,    73,
     4,    30,    99,    52,    42,    80,    53,    83,    70,    29,
    70,    53,    32,   nil,   nil,    43,    73,   nil,    64,    47,
   nil,   nil,   nil,    43,    47,    47,    47,    47,    47,   nil,
    47,    45,   nil,    34,   nil,    30,    45,    16,    51,    32,
    79,   nil,   189,   nil,   nil,   nil,    51,   nil,   167,    25,
   167,   nil,    39,   nil,   nil,     4,   nil,    27,    27,    43,
    43,    81,    27,    27,   nil,   nil,    54,    81,   nil,    64,
    25,   nil,   nil,   nil,   nil,    32,    70,    67,    81,   nil,
    29,   nil,    23,   nil,    34,   nil,    25,   nil,   nil,   nil,
   nil,    19,    68,   nil,   nil,   nil,     9,    11,   nil,   nil,
     9,    19,    19,   nil,   nil,   nil,    19,    19,   nil,    81,
   nil,   nil,   nil,    39,   nil,    47,    47,    47,    47,    47,
    47,    47,    54,   nil,    83,   nil,    47,    47,    47,   nil,
   nil,    29,   nil,    64,   nil,   nil,   nil,   nil,   nil,    29,
    47,    29,    81,    64,    44,    54,   nil,    29,   nil,   nil,
    44,   nil,    81,    39,   nil,   nil,    29,   nil,   181,    99,
    70,    70,    78,    39,   nil,    70,    70,   nil,    36,   nil,
   nil,    29,    56,    64,    29,    47,    47,   nil,   133,    64,
    56,    29,    81,   nil,    47,    99,   101,   nil,   nil,   nil,
    81,    29,    44,    10,   105,    29,    10,    39,   nil,    27,
    96,   nil,    47,   133,   nil,   133,    47,     9,    73,   nil,
    64,    47,   nil,   105,   nil,   nil,    73,    64,   nil,   nil,
   nil,     9,    80,   nil,    29,    29,    81,    34,    29,    16,
    77,   nil,   nil,    25,    29,    29,   nil,    47,   145,    29,
    29,   nil,    31,    19,   nil,   nil,   nil,   nil,   nil,    21,
   nil,    34,    47,    47,    47,   nil,   nil,   nil,   nil,    21,
    21,   nil,   nil,    80,    21,    21,   nil,     7,    78,    67,
    29,   nil,    22,    47,   nil,   nil,    81,    22,   nil,    81,
   nil,    29,   nil,   nil,    68,   nil,    63,   181,   nil,    47,
   181,   nil,   181,    54,   181,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    70,    56,   nil,    22,     9,   nil,   nil,   nil,
   133,   nil,   133,    34,    56,    70,    70,    70,    70,    18,
   nil,   nil,    34,   nil,   nil,   nil,   nil,   nil,     9,   nil,
   nil,    22,    54,    80,   133,   nil,    54,   nil,   nil,    73,
    29,    22,    64,   nil,   nil,    29,    29,    34,   nil,    29,
    73,   nil,    54,   nil,   179,   nil,   nil,    77,    54,    29,
    47,    77,    10,   nil,   nil,   nil,   nil,   nil,   nil,   181,
   nil,   181,   nil,   181,   nil,   181,    29,    54,   nil,   nil,
    34,    10,   nil,   nil,   nil,   nil,   nil,   nil,    22,   133,
   nil,   nil,   nil,   nil,    70,   nil,   nil,    22,   nil,    22,
    34,    21,   nil,   nil,    29,   nil,    30,   181,   nil,   nil,
   nil,    47,   nil,   nil,   nil,    29,    54,   nil,   nil,    18,
   nil,    47,   nil,    25,   nil,   nil,    10,   nil,   nil,   nil,
    43,   nil,   nil,   nil,   nil,    50,    47,    10,   nil,   nil,
    50,   121,   nil,   nil,   nil,   nil,    29,   nil,   nil,   nil,
    29,    47,   nil,   nil,    47,    50,    29,   nil,    34,   nil,
   nil,    47,   nil,   nil,    50,    50,    50,   nil,    50,   nil,
    29,    47,    10,   nil,   105,    47,   179,   nil,   nil,   105,
   nil,   nil,   nil,    29,    22,   nil,    22,   nil,    29,    22,
   nil,   nil,    29,   nil,    50,    22,   nil,   nil,   nil,    50,
    50,   nil,    54,    50,    47,    47,   nil,   nil,    47,    22,
    10,   nil,   nil,   nil,    47,    47,   nil,   nil,   nil,    47,
    47,   nil,   nil,    25,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   179,   179,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    29,   nil,   nil,   nil,    29,   nil,   nil,
   nil,    50,    73,   nil,    29,    29,    50,    50,    50,    50,
    50,    73,    50,   nil,   126,   nil,    34,   121,    39,    10,
   121,    22,    29,   nil,   nil,    22,    10,    29,   nil,   nil,
    22,   nil,    32,    64,   nil,   126,   179,   nil,    64,   nil,
   177,   177,   177,   nil,    10,   nil,    29,    81,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    34,    22,   nil,   nil,   nil,
   nil,   179,    73,   179,   nil,   nil,   nil,   nil,   nil,   nil,
    47,   nil,    22,    22,   nil,    47,    47,   nil,    28,    47,
   nil,   nil,   nil,    28,   nil,   nil,   nil,   nil,   nil,    47,
   nil,   nil,   nil,   nil,   121,   nil,   121,    50,    50,    50,
    50,    50,    50,    50,   nil,   nil,    47,    54,    50,    50,
    50,    28,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    50,   nil,   nil,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,   nil,    47,   nil,   nil,    28,   nil,   nil,
   128,   nil,   nil,   126,   nil,    47,   126,   nil,   126,   nil,
   126,   121,   121,   nil,   nil,   nil,   nil,    50,    50,   nil,
   nil,   128,   nil,   nil,   128,   nil,    50,   nil,   179,   nil,
   179,   nil,   131,   nil,   nil,   nil,    47,   nil,   nil,    54,
    47,   nil,   nil,   nil,    50,   nil,    47,   nil,    50,   121,
   nil,   121,   179,    50,    28,   nil,   nil,   nil,   nil,   131,
    47,   131,   nil,    28,   nil,    28,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    47,    50,
   nil,   121,   nil,   nil,   nil,   126,   nil,   126,   nil,   126,
    22,   126,   nil,   nil,    50,    50,    50,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   177,   177,   179,   177,   177,
   nil,   177,   nil,   nil,   nil,    50,   129,   129,   129,   nil,
   nil,   nil,   nil,   126,   nil,   nil,   nil,   nil,   nil,   128,
    22,    50,   128,    22,   128,   nil,   128,    47,   nil,   nil,
   nil,   nil,   nil,   nil,    47,    47,   nil,   nil,   nil,   nil,
    28,   nil,    28,   nil,    22,    28,   nil,   nil,   nil,   nil,
   nil,    28,    47,   nil,   nil,   nil,   131,    47,   131,   nil,
   nil,   nil,   nil,   nil,   nil,    28,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    22,   nil,   nil,    47,    22,   131,   nil,
   131,   nil,   131,    22,    22,   nil,    33,   nil,    22,    22,
   nil,    33,    50,   nil,   nil,   177,   177,   177,   177,   nil,
   nil,   128,   nil,   128,   nil,   128,   nil,   128,   nil,   nil,
   nil,   nil,   129,   129,   129,   129,   nil,   129,   nil,    33,
    33,    33,   nil,   nil,   nil,   nil,   nil,    28,   nil,   nil,
   nil,    28,   nil,   131,   nil,   131,    28,   131,   nil,   128,
   nil,   nil,   177,    50,   nil,    33,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    50,   131,    33,    33,    33,   nil,   nil,
   nil,   nil,    28,   nil,   nil,   nil,   nil,   nil,    50,   nil,
   nil,   131,   nil,   nil,   129,   129,   129,   129,    28,    28,
   nil,   nil,   nil,    50,   nil,   nil,    50,   nil,   129,    22,
   nil,   nil,   nil,    50,    22,   nil,   nil,   nil,    22,   nil,
   nil,   nil,    33,    50,   nil,   nil,   nil,    50,    22,   nil,
   nil,    33,   nil,    33,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    59,   nil,   nil,    22,   nil,    59,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    50,    50,   nil,   nil,
    50,   nil,   nil,   nil,   nil,   nil,    50,    50,   nil,   nil,
   nil,    50,    50,    22,   nil,    59,    59,    59,   nil,   nil,
   nil,   nil,   nil,   nil,    22,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    59,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    59,    59,    59,    59,   nil,   nil,   nil,    33,    22,
    33,   nil,   nil,    33,   nil,    22,   nil,   nil,   nil,    33,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    22,
   nil,   nil,   nil,    33,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    28,    22,    59,   nil,
   nil,    22,   nil,   nil,   nil,   nil,   nil,    59,   nil,    59,
   nil,   nil,    50,   nil,   nil,   nil,   nil,    50,    50,   nil,
   nil,    50,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    50,   nil,   nil,   nil,   nil,    28,   nil,   nil,    28,
   nil,   nil,   nil,   nil,   nil,    33,   nil,   nil,    50,    33,
   nil,   nil,   nil,    33,    33,   nil,    22,   nil,   nil,   nil,
    28,   nil,   nil,    22,    22,   nil,   nil,   nil,   nil,   nil,
    33,   nil,   nil,   nil,   nil,   nil,    50,   nil,   nil,   nil,
    33,    22,   nil,   nil,   nil,   nil,    22,    50,   nil,    28,
   nil,   nil,   nil,    28,    59,   nil,    33,    33,   nil,    28,
    28,   nil,   nil,   nil,    28,    28,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    50,    59,
   nil,   nil,    50,   nil,   nil,   nil,   nil,   nil,    50,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    50,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    50,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    59,   nil,   nil,   nil,    59,   nil,   nil,   nil,    59,
    59,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    28,    59,   nil,   nil,   nil,
    28,   nil,    60,   nil,    28,   nil,    59,    60,   nil,    50,
   nil,   nil,   nil,   nil,    28,   nil,    50,    50,   nil,   nil,
   nil,   nil,    59,    59,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    28,   nil,   nil,    50,    60,    60,    60,   nil,    50,
   nil,   nil,   nil,   nil,    33,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    33,   nil,   nil,   nil,   nil,   nil,    50,    28,
    33,    60,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    28,    60,    60,    60,    60,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    33,   nil,   nil,    33,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    28,   nil,   nil,    33,   nil,
   nil,    28,   nil,   nil,   nil,    33,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,   nil,    28,   nil,    60,   nil,    60,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    33,   nil,   nil,
   nil,    33,   nil,    28,   nil,   nil,   nil,    33,    33,   nil,
   nil,   nil,    33,    33,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    59,   nil,   nil,    33,   nil,   nil,   nil,   nil,    59,   nil,
   nil,   nil,   nil,   nil,    33,   nil,    59,   nil,   nil,   nil,
   nil,   nil,    28,   nil,   nil,   nil,   nil,   nil,   nil,    28,
    28,   nil,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
    59,   nil,   nil,    59,   nil,   nil,   nil,    28,   nil,   nil,
   nil,   nil,    28,   nil,   nil,   nil,   nil,   nil,   nil,    60,
   nil,   nil,   nil,   nil,    59,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    33,   nil,   nil,   nil,   nil,    33,   nil,
   nil,   nil,    33,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    75,    33,   nil,   nil,   nil,   nil,    59,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    33,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    60,   nil,   nil,   nil,    60,   nil,   nil,   nil,    60,
    60,   nil,   nil,   nil,   nil,   nil,   nil,    33,   nil,    59,
   nil,   nil,   nil,   nil,   nil,   nil,    60,   nil,    33,   nil,
    59,   nil,   nil,   nil,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    60,    60,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    33,   nil,   nil,   nil,   nil,   nil,    33,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    33,   nil,   nil,   nil,   nil,   nil,    59,
   nil,   nil,   nil,   nil,    59,   nil,    33,   nil,    59,   nil,
   nil,    33,   nil,   nil,   nil,    33,   nil,   nil,    59,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    75,    75,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    59,   nil,   nil,    33,   nil,   nil,   nil,
    33,    35,   nil,   nil,    59,   nil,   nil,    33,    33,   nil,
    35,    35,    35,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    35,    35,    33,    35,   nil,   nil,   nil,
    33,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    75,    59,
    75,    35,    35,    75,    75,    59,   nil,   nil,   nil,   nil,
    60,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    60,    59,
   nil,   nil,   nil,   nil,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,    59,   nil,   nil,   nil,   nil,    59,   nil,   nil,
   nil,    59,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    60,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    59,   nil,   nil,   nil,    59,   nil,   nil,   nil,
   nil,   nil,   nil,    59,    59,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    59,   nil,   nil,    75,   nil,    59,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    60,
   nil,   nil,   nil,   nil,    35,    35,   nil,   nil,    35,    35,
    60,   nil,   nil,   nil,    35,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,   nil,   nil,
    35,    35,    35,    35,    35,    35,    35,    35,    35,    35,
    35,    35,    35,    35,    35,    35,    35,    35,    35,    35,
    35,    35,   nil,   nil,   nil,   nil,    35,   nil,    75,   nil,
    75,   nil,    75,    35,    35,   nil,   nil,   nil,   nil,    60,
   nil,   nil,    35,   nil,    60,   nil,   nil,   nil,    60,    35,
   nil,    35,   nil,   nil,    35,    35,   nil,   nil,    60,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    75,
   nil,   nil,    75,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    75,   nil,   nil,
   nil,   nil,    35,   nil,    35,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    75,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    75,
    75,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    60,
   nil,   nil,   nil,   nil,   nil,    60,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    60,
   nil,    75,   nil,   nil,    75,    35,   nil,   nil,   nil,   nil,
   nil,   nil,    60,   nil,   nil,   nil,   nil,    60,   nil,   nil,
   nil,    60,   nil,    75,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,    75,
    35,    35,    35,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    60,   nil,   nil,   nil,    60,   nil,   nil,   nil,
   nil,   nil,   nil,    60,    60,   nil,   nil,   nil,   nil,    35,
   nil,    35,   nil,    35,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    60,   nil,   nil,   nil,    75,    60,   nil,   nil,    35,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,    35,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    35,   nil,   nil,    35,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,    35,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    75,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    75,   nil,   nil,   nil,
   nil,    35,    35,    35,   nil,   nil,   nil,    35,   nil,   nil,
    35,    35,    35,    35,    75,   nil,   nil,    35,    35,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    35,   nil,   nil,    35,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    35,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    35,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    35,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    35,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,    35,    35,
    35,   nil,    35,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    35,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    35,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    35,    35,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    35 ]

racc_goto_pointer = [
   nil,   115,    81,   nil,    83,    89,    60,    42,    57,  -246,
    25,  -495,  -582,  -672,   nil,  -385,    12,   120,   -24,  -130,
    67,    28,   652,  -201,   -74,    80,    19,  -174,  1008,     3,
  -187,  -382,    35,  1266,   -19,  2171,   -14,  -395,   nil,   -25,
   nil,   nil,     0,  -164,    86,  -202,  -397,   283,  -330,   nil,
   815,   145,    75,    83,   221,   nil,    36,    54,  -278,  1402,
  1732,  -299,    79,   -63,    -7,   nil,   nil,    52,    67,  -249,
   -71,    16,   nil,    72,   117,  1940,   102,   -53,   -15,  -247,
   104,     3,  -320,  -251,  -472,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    96,   109,   -51,   nil,  -174,  -329,
  -647,  -331,   103,   nil,  -196,    22,   nil,   nil,  -556,   106,
   121,  -155,   122,   119,  -386,  -539,  -187,  -196,  -385,  -647,
  -779,   135,  -583,  -735,  -584,   nil,   165,  -589,   291,   334,
  -583,   323,  -700,  -221,  -530,   nil,  -602,  -719,  -810,  -808,
  -337,  -584,  -122,  -443,  -392,   -12,  -642,  -637,  -354,   -16,
   nil,   -24,   -24,   nil,   nil,  -375,  -640,  -404,  -530,   nil,
   nil,   183,    38,   182,   183,   183,  -238,  -158,   183,   183,
   186,  -300,  -300,  -288,  -384,   nil,   nil,   296,  -517,   187,
  -702,  -241,  -511,  -618,   nil,  -729,  -796,   nil,   nil,  -422 ]

racc_goto_default = [
   nil,   nil,   nil,     3,   nil,     4,   354,   300,   nil,   nil,
   534,   nil,   822,   nil,   297,   298,   nil,   nil,   nil,    11,
    12,    18,   233,   nil,   nil,    14,   nil,   419,   234,   328,
   nil,   nil,   567,   232,   454,    21,   nil,   nil,   349,    22,
    23,    24,   nil,   656,   nil,   nil,   nil,   317,   nil,    25,
   314,   433,    32,   nil,   nil,    34,    37,    36,   nil,   229,
   230,   366,   nil,   135,   441,   134,   137,    80,    81,   nil,
   424,    95,    44,    47,   265,   289,   nil,   791,   434,   nil,
   435,   446,   615,   497,   287,   273,    48,    49,    50,    51,
    52,    53,    54,    55,    56,   nil,   274,    62,   nil,   nil,
   nil,   nil,   nil,    70,   nil,   549,    71,    72,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   813,   684,   nil,   814,   957,   849,   672,   nil,   673,   nil,
   nil,   674,   nil,   676,   nil,   778,   nil,   nil,   nil,   682,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   445,   nil,   nil,   635,   629,   nil,   nil,   nil,   nil,    79,
    82,    83,   nil,   nil,   nil,   nil,   nil,   577,   nil,   nil,
   nil,   nil,   nil,   nil,   878,   728,   671,   nil,   675,   886,
   687,   689,   690,   861,   693,   694,   862,   697,   700,   292 ]

racc_reduce_table = [
  0, 0, :racc_error,
  1, 146, :_reduce_none,
  2, 147, :_reduce_2,
  0, 148, :_reduce_3,
  1, 148, :_reduce_4,
  3, 148, :_reduce_5,
  2, 148, :_reduce_6,
  1, 150, :_reduce_none,
  2, 150, :_reduce_8,
  3, 153, :_reduce_9,
  4, 154, :_reduce_10,
  2, 155, :_reduce_11,
  0, 159, :_reduce_12,
  1, 159, :_reduce_13,
  3, 159, :_reduce_14,
  2, 159, :_reduce_15,
  1, 160, :_reduce_none,
  2, 160, :_reduce_17,
  0, 171, :_reduce_18,
  4, 152, :_reduce_19,
  3, 152, :_reduce_20,
  3, 152, :_reduce_21,
  3, 152, :_reduce_22,
  2, 152, :_reduce_23,
  3, 152, :_reduce_24,
  3, 152, :_reduce_25,
  3, 152, :_reduce_26,
  3, 152, :_reduce_27,
  3, 152, :_reduce_28,
  4, 152, :_reduce_29,
  1, 152, :_reduce_none,
  3, 152, :_reduce_31,
  3, 152, :_reduce_32,
  3, 152, :_reduce_33,
  1, 152, :_reduce_none,
  3, 164, :_reduce_35,
  3, 164, :_reduce_36,
  6, 164, :_reduce_37,
  5, 164, :_reduce_38,
  5, 164, :_reduce_39,
  5, 164, :_reduce_40,
  5, 164, :_reduce_41,
  3, 164, :_reduce_42,
  1, 172, :_reduce_none,
  3, 172, :_reduce_44,
  1, 172, :_reduce_none,
  1, 170, :_reduce_none,
  3, 170, :_reduce_47,
  3, 170, :_reduce_48,
  3, 170, :_reduce_49,
  2, 170, :_reduce_50,
  1, 170, :_reduce_none,
  1, 163, :_reduce_none,
  0, 183, :_reduce_53,
  3, 181, :_reduce_54,
  1, 166, :_reduce_none,
  1, 166, :_reduce_none,
  1, 185, :_reduce_none,
  4, 185, :_reduce_58,
  0, 193, :_reduce_59,
  4, 190, :_reduce_60,
  1, 192, :_reduce_none,
  2, 184, :_reduce_62,
  3, 184, :_reduce_63,
  4, 184, :_reduce_64,
  5, 184, :_reduce_65,
  4, 184, :_reduce_66,
  5, 184, :_reduce_67,
  2, 184, :_reduce_68,
  2, 184, :_reduce_69,
  2, 184, :_reduce_70,
  2, 184, :_reduce_71,
  2, 184, :_reduce_72,
  1, 165, :_reduce_73,
  3, 165, :_reduce_74,
  1, 198, :_reduce_75,
  3, 198, :_reduce_76,
  1, 197, :_reduce_none,
  2, 197, :_reduce_78,
  3, 197, :_reduce_79,
  5, 197, :_reduce_80,
  2, 197, :_reduce_81,
  4, 197, :_reduce_82,
  2, 197, :_reduce_83,
  4, 197, :_reduce_84,
  1, 197, :_reduce_85,
  3, 197, :_reduce_86,
  1, 201, :_reduce_none,
  3, 201, :_reduce_88,
  2, 200, :_reduce_89,
  3, 200, :_reduce_90,
  1, 203, :_reduce_91,
  3, 203, :_reduce_92,
  1, 202, :_reduce_93,
  1, 202, :_reduce_94,
  4, 202, :_reduce_95,
  3, 202, :_reduce_96,
  3, 202, :_reduce_97,
  3, 202, :_reduce_98,
  3, 202, :_reduce_99,
  2, 202, :_reduce_100,
  1, 202, :_reduce_101,
  1, 167, :_reduce_102,
  1, 167, :_reduce_103,
  4, 167, :_reduce_104,
  3, 167, :_reduce_105,
  3, 167, :_reduce_106,
  3, 167, :_reduce_107,
  3, 167, :_reduce_108,
  2, 167, :_reduce_109,
  1, 167, :_reduce_110,
  1, 206, :_reduce_111,
  1, 206, :_reduce_none,
  2, 207, :_reduce_113,
  1, 207, :_reduce_114,
  3, 207, :_reduce_115,
  1, 208, :_reduce_none,
  1, 208, :_reduce_none,
  1, 208, :_reduce_none,
  1, 208, :_reduce_none,
  1, 208, :_reduce_none,
  1, 211, :_reduce_121,
  1, 211, :_reduce_none,
  1, 161, :_reduce_none,
  1, 161, :_reduce_none,
  1, 162, :_reduce_125,
  0, 214, :_reduce_126,
  4, 162, :_reduce_127,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  3, 180, :_reduce_199,
  3, 180, :_reduce_200,
  6, 180, :_reduce_201,
  5, 180, :_reduce_202,
  5, 180, :_reduce_203,
  5, 180, :_reduce_204,
  5, 180, :_reduce_205,
  4, 180, :_reduce_206,
  3, 180, :_reduce_207,
  3, 180, :_reduce_208,
  3, 180, :_reduce_209,
  2, 180, :_reduce_210,
  2, 180, :_reduce_211,
  3, 180, :_reduce_212,
  3, 180, :_reduce_213,
  3, 180, :_reduce_214,
  3, 180, :_reduce_215,
  3, 180, :_reduce_216,
  3, 180, :_reduce_217,
  4, 180, :_reduce_218,
  2, 180, :_reduce_219,
  2, 180, :_reduce_220,
  3, 180, :_reduce_221,
  3, 180, :_reduce_222,
  3, 180, :_reduce_223,
  3, 180, :_reduce_224,
  1, 180, :_reduce_none,
  3, 180, :_reduce_226,
  3, 180, :_reduce_227,
  3, 180, :_reduce_228,
  3, 180, :_reduce_229,
  3, 180, :_reduce_230,
  2, 180, :_reduce_231,
  2, 180, :_reduce_232,
  3, 180, :_reduce_233,
  3, 180, :_reduce_234,
  3, 180, :_reduce_235,
  3, 180, :_reduce_236,
  3, 180, :_reduce_237,
  6, 180, :_reduce_238,
  1, 180, :_reduce_none,
  1, 219, :_reduce_none,
  1, 219, :_reduce_none,
  1, 219, :_reduce_none,
  1, 219, :_reduce_none,
  3, 217, :_reduce_244,
  3, 217, :_reduce_245,
  1, 220, :_reduce_none,
  1, 221, :_reduce_none,
  2, 221, :_reduce_none,
  4, 221, :_reduce_249,
  2, 221, :_reduce_250,
  1, 215, :_reduce_none,
  3, 215, :_reduce_252,
  3, 226, :_reduce_253,
  0, 227, :_reduce_254,
  1, 227, :_reduce_none,
  0, 175, :_reduce_256,
  1, 175, :_reduce_none,
  2, 175, :_reduce_none,
  4, 175, :_reduce_259,
  2, 175, :_reduce_260,
  1, 196, :_reduce_261,
  2, 196, :_reduce_262,
  2, 196, :_reduce_263,
  4, 196, :_reduce_264,
  1, 196, :_reduce_265,
  0, 230, :_reduce_266,
  2, 189, :_reduce_267,
  2, 229, :_reduce_268,
  2, 228, :_reduce_269,
  0, 228, :_reduce_270,
  1, 223, :_reduce_271,
  2, 223, :_reduce_272,
  3, 223, :_reduce_273,
  4, 223, :_reduce_274,
  1, 169, :_reduce_275,
  1, 169, :_reduce_none,
  3, 168, :_reduce_277,
  4, 168, :_reduce_278,
  2, 168, :_reduce_279,
  1, 218, :_reduce_none,
  1, 218, :_reduce_none,
  1, 218, :_reduce_none,
  1, 218, :_reduce_none,
  1, 218, :_reduce_none,
  1, 218, :_reduce_none,
  1, 218, :_reduce_none,
  1, 218, :_reduce_none,
  1, 218, :_reduce_none,
  1, 218, :_reduce_none,
  1, 218, :_reduce_290,
  0, 255, :_reduce_291,
  4, 218, :_reduce_292,
  0, 256, :_reduce_293,
  4, 218, :_reduce_294,
  0, 257, :_reduce_295,
  4, 218, :_reduce_296,
  3, 218, :_reduce_297,
  3, 218, :_reduce_298,
  2, 218, :_reduce_299,
  3, 218, :_reduce_300,
  3, 218, :_reduce_301,
  1, 218, :_reduce_302,
  4, 218, :_reduce_303,
  3, 218, :_reduce_304,
  1, 218, :_reduce_305,
  5, 218, :_reduce_306,
  4, 218, :_reduce_307,
  3, 218, :_reduce_308,
  2, 218, :_reduce_309,
  1, 218, :_reduce_none,
  2, 218, :_reduce_311,
  0, 258, :_reduce_312,
  3, 218, :_reduce_313,
  6, 218, :_reduce_314,
  6, 218, :_reduce_315,
  4, 218, :_reduce_316,
  4, 218, :_reduce_317,
  5, 218, :_reduce_318,
  4, 218, :_reduce_319,
  6, 218, :_reduce_320,
  0, 259, :_reduce_321,
  6, 218, :_reduce_322,
  0, 260, :_reduce_323,
  7, 218, :_reduce_324,
  0, 261, :_reduce_325,
  5, 218, :_reduce_326,
  0, 262, :_reduce_327,
  6, 218, :_reduce_328,
  0, 263, :_reduce_329,
  0, 264, :_reduce_330,
  9, 218, :_reduce_331,
  1, 218, :_reduce_332,
  1, 218, :_reduce_333,
  1, 218, :_reduce_334,
  1, 218, :_reduce_335,
  1, 174, :_reduce_none,
  1, 248, :_reduce_337,
  1, 251, :_reduce_338,
  1, 252, :_reduce_339,
  1, 195, :_reduce_340,
  1, 244, :_reduce_none,
  1, 244, :_reduce_none,
  2, 244, :_reduce_343,
  1, 182, :_reduce_none,
  1, 182, :_reduce_none,
  1, 245, :_reduce_none,
  5, 245, :_reduce_347,
  1, 157, :_reduce_none,
  2, 157, :_reduce_349,
  1, 247, :_reduce_none,
  1, 247, :_reduce_none,
  1, 265, :_reduce_352,
  3, 265, :_reduce_353,
  1, 268, :_reduce_354,
  3, 268, :_reduce_355,
  1, 267, :_reduce_none,
  4, 267, :_reduce_357,
  6, 267, :_reduce_358,
  3, 267, :_reduce_359,
  5, 267, :_reduce_360,
  2, 267, :_reduce_361,
  4, 267, :_reduce_362,
  1, 267, :_reduce_363,
  3, 267, :_reduce_364,
  4, 269, :_reduce_365,
  2, 269, :_reduce_366,
  2, 269, :_reduce_367,
  1, 269, :_reduce_368,
  2, 274, :_reduce_369,
  0, 274, :_reduce_370,
  6, 275, :_reduce_371,
  8, 275, :_reduce_372,
  4, 275, :_reduce_373,
  6, 275, :_reduce_374,
  4, 275, :_reduce_375,
  2, 275, :_reduce_none,
  6, 275, :_reduce_377,
  2, 275, :_reduce_378,
  4, 275, :_reduce_379,
  6, 275, :_reduce_380,
  2, 275, :_reduce_381,
  4, 275, :_reduce_382,
  2, 275, :_reduce_383,
  4, 275, :_reduce_384,
  1, 275, :_reduce_none,
  0, 279, :_reduce_386,
  1, 279, :_reduce_387,
  3, 280, :_reduce_388,
  1, 280, :_reduce_389,
  4, 280, :_reduce_390,
  1, 281, :_reduce_391,
  4, 281, :_reduce_392,
  1, 282, :_reduce_393,
  3, 282, :_reduce_394,
  1, 283, :_reduce_395,
  1, 283, :_reduce_none,
  0, 287, :_reduce_397,
  0, 288, :_reduce_398,
  4, 243, :_reduce_399,
  4, 285, :_reduce_400,
  1, 285, :_reduce_401,
  0, 291, :_reduce_402,
  4, 286, :_reduce_403,
  0, 292, :_reduce_404,
  4, 286, :_reduce_405,
  0, 294, :_reduce_406,
  4, 290, :_reduce_407,
  2, 186, :_reduce_408,
  4, 186, :_reduce_409,
  5, 186, :_reduce_410,
  5, 186, :_reduce_411,
  2, 242, :_reduce_412,
  4, 242, :_reduce_413,
  4, 242, :_reduce_414,
  3, 242, :_reduce_415,
  3, 242, :_reduce_416,
  3, 242, :_reduce_417,
  2, 242, :_reduce_418,
  1, 242, :_reduce_419,
  4, 242, :_reduce_420,
  0, 296, :_reduce_421,
  4, 241, :_reduce_422,
  0, 297, :_reduce_423,
  4, 241, :_reduce_424,
  0, 298, :_reduce_425,
  3, 191, :_reduce_426,
  0, 299, :_reduce_427,
  0, 300, :_reduce_428,
  4, 293, :_reduce_429,
  5, 246, :_reduce_430,
  1, 301, :_reduce_431,
  1, 301, :_reduce_none,
  6, 156, :_reduce_433,
  0, 156, :_reduce_434,
  1, 302, :_reduce_435,
  1, 302, :_reduce_none,
  1, 302, :_reduce_none,
  2, 303, :_reduce_438,
  1, 303, :_reduce_none,
  2, 158, :_reduce_440,
  1, 158, :_reduce_none,
  1, 231, :_reduce_none,
  1, 231, :_reduce_none,
  1, 231, :_reduce_none,
  1, 232, :_reduce_445,
  1, 305, :_reduce_446,
  2, 305, :_reduce_447,
  3, 306, :_reduce_448,
  1, 306, :_reduce_449,
  1, 306, :_reduce_450,
  3, 233, :_reduce_451,
  4, 234, :_reduce_452,
  3, 235, :_reduce_453,
  0, 310, :_reduce_454,
  3, 310, :_reduce_455,
  1, 311, :_reduce_456,
  2, 311, :_reduce_457,
  3, 237, :_reduce_458,
  0, 313, :_reduce_459,
  3, 313, :_reduce_460,
  3, 236, :_reduce_461,
  3, 238, :_reduce_462,
  0, 314, :_reduce_463,
  3, 314, :_reduce_464,
  0, 315, :_reduce_465,
  3, 315, :_reduce_466,
  0, 307, :_reduce_467,
  2, 307, :_reduce_468,
  0, 308, :_reduce_469,
  2, 308, :_reduce_470,
  0, 309, :_reduce_471,
  2, 309, :_reduce_472,
  1, 312, :_reduce_473,
  2, 312, :_reduce_474,
  0, 317, :_reduce_475,
  4, 312, :_reduce_476,
  1, 316, :_reduce_477,
  1, 316, :_reduce_478,
  1, 316, :_reduce_479,
  1, 316, :_reduce_none,
  1, 212, :_reduce_481,
  3, 213, :_reduce_482,
  1, 304, :_reduce_483,
  2, 304, :_reduce_484,
  1, 216, :_reduce_485,
  1, 216, :_reduce_486,
  1, 216, :_reduce_487,
  1, 216, :_reduce_488,
  1, 204, :_reduce_489,
  1, 204, :_reduce_490,
  1, 204, :_reduce_491,
  1, 204, :_reduce_492,
  1, 204, :_reduce_493,
  1, 205, :_reduce_494,
  1, 205, :_reduce_495,
  1, 205, :_reduce_496,
  1, 205, :_reduce_497,
  1, 205, :_reduce_498,
  1, 205, :_reduce_499,
  1, 205, :_reduce_500,
  1, 239, :_reduce_501,
  1, 239, :_reduce_502,
  1, 173, :_reduce_503,
  1, 173, :_reduce_504,
  1, 178, :_reduce_505,
  1, 178, :_reduce_506,
  0, 318, :_reduce_507,
  4, 249, :_reduce_508,
  0, 249, :_reduce_509,
  3, 253, :_reduce_510,
  0, 320, :_reduce_511,
  3, 253, :_reduce_512,
  4, 319, :_reduce_513,
  2, 319, :_reduce_514,
  2, 319, :_reduce_515,
  1, 319, :_reduce_516,
  2, 322, :_reduce_517,
  0, 322, :_reduce_518,
  6, 289, :_reduce_519,
  8, 289, :_reduce_520,
  4, 289, :_reduce_521,
  6, 289, :_reduce_522,
  4, 289, :_reduce_523,
  6, 289, :_reduce_524,
  2, 289, :_reduce_525,
  4, 289, :_reduce_526,
  6, 289, :_reduce_527,
  2, 289, :_reduce_528,
  4, 289, :_reduce_529,
  2, 289, :_reduce_530,
  4, 289, :_reduce_531,
  1, 289, :_reduce_532,
  0, 289, :_reduce_533,
  1, 284, :_reduce_534,
  1, 284, :_reduce_535,
  1, 284, :_reduce_536,
  1, 284, :_reduce_537,
  1, 266, :_reduce_none,
  1, 266, :_reduce_539,
  1, 324, :_reduce_540,
  1, 325, :_reduce_541,
  3, 325, :_reduce_542,
  1, 276, :_reduce_543,
  3, 276, :_reduce_544,
  1, 326, :_reduce_545,
  2, 327, :_reduce_546,
  1, 327, :_reduce_547,
  2, 328, :_reduce_548,
  1, 328, :_reduce_549,
  1, 270, :_reduce_550,
  3, 270, :_reduce_551,
  1, 321, :_reduce_552,
  3, 321, :_reduce_553,
  1, 329, :_reduce_none,
  1, 329, :_reduce_none,
  2, 271, :_reduce_556,
  1, 271, :_reduce_557,
  3, 330, :_reduce_558,
  3, 331, :_reduce_559,
  1, 277, :_reduce_560,
  3, 277, :_reduce_561,
  1, 323, :_reduce_562,
  3, 323, :_reduce_563,
  1, 332, :_reduce_none,
  1, 332, :_reduce_none,
  2, 278, :_reduce_566,
  1, 278, :_reduce_567,
  1, 333, :_reduce_none,
  1, 333, :_reduce_none,
  2, 273, :_reduce_570,
  2, 272, :_reduce_571,
  0, 272, :_reduce_572,
  1, 254, :_reduce_none,
  3, 254, :_reduce_574,
  0, 240, :_reduce_575,
  2, 240, :_reduce_none,
  1, 225, :_reduce_577,
  3, 225, :_reduce_578,
  3, 334, :_reduce_579,
  2, 334, :_reduce_580,
  4, 334, :_reduce_581,
  2, 334, :_reduce_582,
  1, 194, :_reduce_none,
  1, 194, :_reduce_none,
  1, 194, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 295, :_reduce_none,
  1, 295, :_reduce_none,
  1, 295, :_reduce_none,
  1, 187, :_reduce_none,
  1, 187, :_reduce_none,
  1, 177, :_reduce_595,
  1, 177, :_reduce_596,
  0, 149, :_reduce_none,
  1, 149, :_reduce_none,
  0, 179, :_reduce_none,
  1, 179, :_reduce_none,
  2, 199, :_reduce_601,
  2, 176, :_reduce_602,
  0, 224, :_reduce_none,
  1, 224, :_reduce_none,
  1, 224, :_reduce_none,
  1, 250, :_reduce_606,
  1, 250, :_reduce_none,
  1, 151, :_reduce_none,
  2, 151, :_reduce_none,
  0, 222, :_reduce_610 ]

racc_reduce_n = 611

racc_shift_n = 1036

racc_token_table = {
  false => 0,
  :error => 1,
  :kCLASS => 2,
  :kMODULE => 3,
  :kDEF => 4,
  :kUNDEF => 5,
  :kBEGIN => 6,
  :kRESCUE => 7,
  :kENSURE => 8,
  :kEND => 9,
  :kIF => 10,
  :kUNLESS => 11,
  :kTHEN => 12,
  :kELSIF => 13,
  :kELSE => 14,
  :kCASE => 15,
  :kWHEN => 16,
  :kWHILE => 17,
  :kUNTIL => 18,
  :kFOR => 19,
  :kBREAK => 20,
  :kNEXT => 21,
  :kREDO => 22,
  :kRETRY => 23,
  :kIN => 24,
  :kDO => 25,
  :kDO_COND => 26,
  :kDO_BLOCK => 27,
  :kDO_LAMBDA => 28,
  :kRETURN => 29,
  :kYIELD => 30,
  :kSUPER => 31,
  :kSELF => 32,
  :kNIL => 33,
  :kTRUE => 34,
  :kFALSE => 35,
  :kAND => 36,
  :kOR => 37,
  :kNOT => 38,
  :kIF_MOD => 39,
  :kUNLESS_MOD => 40,
  :kWHILE_MOD => 41,
  :kUNTIL_MOD => 42,
  :kRESCUE_MOD => 43,
  :kALIAS => 44,
  :kDEFINED => 45,
  :klBEGIN => 46,
  :klEND => 47,
  :k__LINE__ => 48,
  :k__FILE__ => 49,
  :k__ENCODING__ => 50,
  :tIDENTIFIER => 51,
  :tFID => 52,
  :tGVAR => 53,
  :tIVAR => 54,
  :tCONSTANT => 55,
  :tLABEL => 56,
  :tCVAR => 57,
  :tNTH_REF => 58,
  :tBACK_REF => 59,
  :tSTRING_CONTENT => 60,
  :tINTEGER => 61,
  :tFLOAT => 62,
  :tUPLUS => 63,
  :tUMINUS => 64,
  :tUNARY_NUM => 65,
  :tPOW => 66,
  :tCMP => 67,
  :tEQ => 68,
  :tEQQ => 69,
  :tNEQ => 70,
  :tGEQ => 71,
  :tLEQ => 72,
  :tANDOP => 73,
  :tOROP => 74,
  :tMATCH => 75,
  :tNMATCH => 76,
  :tDOT => 77,
  :tDOT2 => 78,
  :tDOT3 => 79,
  :tAREF => 80,
  :tASET => 81,
  :tLSHFT => 82,
  :tRSHFT => 83,
  :tCOLON2 => 84,
  :tCOLON3 => 85,
  :tOP_ASGN => 86,
  :tASSOC => 87,
  :tLPAREN => 88,
  :tLPAREN2 => 89,
  :tRPAREN => 90,
  :tLPAREN_ARG => 91,
  :tLBRACK => 92,
  :tLBRACK2 => 93,
  :tRBRACK => 94,
  :tLBRACE => 95,
  :tLBRACE_ARG => 96,
  :tSTAR => 97,
  :tSTAR2 => 98,
  :tAMPER => 99,
  :tAMPER2 => 100,
  :tTILDE => 101,
  :tPERCENT => 102,
  :tDIVIDE => 103,
  :tDSTAR => 104,
  :tPLUS => 105,
  :tMINUS => 106,
  :tLT => 107,
  :tGT => 108,
  :tPIPE => 109,
  :tBANG => 110,
  :tCARET => 111,
  :tLCURLY => 112,
  :tRCURLY => 113,
  :tBACK_REF2 => 114,
  :tSYMBEG => 115,
  :tSTRING_BEG => 116,
  :tXSTRING_BEG => 117,
  :tREGEXP_BEG => 118,
  :tREGEXP_OPT => 119,
  :tWORDS_BEG => 120,
  :tQWORDS_BEG => 121,
  :tSYMBOLS_BEG => 122,
  :tQSYMBOLS_BEG => 123,
  :tSTRING_DBEG => 124,
  :tSTRING_DVAR => 125,
  :tSTRING_END => 126,
  :tSTRING_DEND => 127,
  :tSTRING => 128,
  :tSYMBOL => 129,
  :tNL => 130,
  :tEH => 131,
  :tCOLON => 132,
  :tCOMMA => 133,
  :tSPACE => 134,
  :tSEMI => 135,
  :tLAMBDA => 136,
  :tLAMBEG => 137,
  :tCHARACTER => 138,
  :tRATIONAL => 139,
  :tIMAGINARY => 140,
  :tLABEL_END => 141,
  :tANDDOT => 142,
  :tEQL => 143,
  :tLOWEST => 144 }

racc_nt_base = 145

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]
Ractor.make_shareable(Racc_arg) if defined?(Ractor)

Racc_token_to_s_table = [
  "$end",
  "error",
  "kCLASS",
  "kMODULE",
  "kDEF",
  "kUNDEF",
  "kBEGIN",
  "kRESCUE",
  "kENSURE",
  "kEND",
  "kIF",
  "kUNLESS",
  "kTHEN",
  "kELSIF",
  "kELSE",
  "kCASE",
  "kWHEN",
  "kWHILE",
  "kUNTIL",
  "kFOR",
  "kBREAK",
  "kNEXT",
  "kREDO",
  "kRETRY",
  "kIN",
  "kDO",
  "kDO_COND",
  "kDO_BLOCK",
  "kDO_LAMBDA",
  "kRETURN",
  "kYIELD",
  "kSUPER",
  "kSELF",
  "kNIL",
  "kTRUE",
  "kFALSE",
  "kAND",
  "kOR",
  "kNOT",
  "kIF_MOD",
  "kUNLESS_MOD",
  "kWHILE_MOD",
  "kUNTIL_MOD",
  "kRESCUE_MOD",
  "kALIAS",
  "kDEFINED",
  "klBEGIN",
  "klEND",
  "k__LINE__",
  "k__FILE__",
  "k__ENCODING__",
  "tIDENTIFIER",
  "tFID",
  "tGVAR",
  "tIVAR",
  "tCONSTANT",
  "tLABEL",
  "tCVAR",
  "tNTH_REF",
  "tBACK_REF",
  "tSTRING_CONTENT",
  "tINTEGER",
  "tFLOAT",
  "tUPLUS",
  "tUMINUS",
  "tUNARY_NUM",
  "tPOW",
  "tCMP",
  "tEQ",
  "tEQQ",
  "tNEQ",
  "tGEQ",
  "tLEQ",
  "tANDOP",
  "tOROP",
  "tMATCH",
  "tNMATCH",
  "tDOT",
  "tDOT2",
  "tDOT3",
  "tAREF",
  "tASET",
  "tLSHFT",
  "tRSHFT",
  "tCOLON2",
  "tCOLON3",
  "tOP_ASGN",
  "tASSOC",
  "tLPAREN",
  "tLPAREN2",
  "tRPAREN",
  "tLPAREN_ARG",
  "tLBRACK",
  "tLBRACK2",
  "tRBRACK",
  "tLBRACE",
  "tLBRACE_ARG",
  "tSTAR",
  "tSTAR2",
  "tAMPER",
  "tAMPER2",
  "tTILDE",
  "tPERCENT",
  "tDIVIDE",
  "tDSTAR",
  "tPLUS",
  "tMINUS",
  "tLT",
  "tGT",
  "tPIPE",
  "tBANG",
  "tCARET",
  "tLCURLY",
  "tRCURLY",
  "tBACK_REF2",
  "tSYMBEG",
  "tSTRING_BEG",
  "tXSTRING_BEG",
  "tREGEXP_BEG",
  "tREGEXP_OPT",
  "tWORDS_BEG",
  "tQWORDS_BEG",
  "tSYMBOLS_BEG",
  "tQSYMBOLS_BEG",
  "tSTRING_DBEG",
  "tSTRING_DVAR",
  "tSTRING_END",
  "tSTRING_DEND",
  "tSTRING",
  "tSYMBOL",
  "tNL",
  "tEH",
  "tCOLON",
  "tCOMMA",
  "tSPACE",
  "tSEMI",
  "tLAMBDA",
  "tLAMBEG",
  "tCHARACTER",
  "tRATIONAL",
  "tIMAGINARY",
  "tLABEL_END",
  "tANDDOT",
  "tEQL",
  "tLOWEST",
  "$start",
  "program",
  "top_compstmt",
  "top_stmts",
  "opt_terms",
  "top_stmt",
  "terms",
  "stmt",
  "begin_block",
  "bodystmt",
  "compstmt",
  "opt_rescue",
  "opt_else",
  "opt_ensure",
  "stmts",
  "stmt_or_begin",
  "fitem",
  "undef_list",
  "expr_value",
  "command_asgn",
  "mlhs",
  "command_call",
  "lhs",
  "mrhs",
  "mrhs_arg",
  "expr",
  "@1",
  "command_rhs",
  "var_lhs",
  "primary_value",
  "opt_call_args",
  "rbracket",
  "call_op",
  "backref",
  "opt_nl",
  "arg",
  "expr_value_do",
  "do",
  "@2",
  "command",
  "block_command",
  "block_call",
  "dot_or_colon",
  "operation2",
  "command_args",
  "cmd_brace_block",
  "brace_body",
  "fcall",
  "@3",
  "operation",
  "k_return",
  "call_args",
  "mlhs_basic",
  "mlhs_inner",
  "rparen",
  "mlhs_head",
  "mlhs_item",
  "mlhs_node",
  "mlhs_post",
  "user_variable",
  "keyword_variable",
  "cname",
  "cpath",
  "fname",
  "op",
  "reswords",
  "fsym",
  "symbol",
  "dsym",
  "@4",
  "arg_rhs",
  "simple_numeric",
  "rel_expr",
  "primary",
  "relop",
  "arg_value",
  "aref_args",
  "none",
  "args",
  "trailer",
  "assocs",
  "paren_args",
  "opt_paren_args",
  "opt_block_arg",
  "block_arg",
  "@5",
  "literal",
  "strings",
  "xstring",
  "regexp",
  "words",
  "qwords",
  "symbols",
  "qsymbols",
  "var_ref",
  "assoc_list",
  "brace_block",
  "method_call",
  "lambda",
  "then",
  "if_tail",
  "case_body",
  "for_var",
  "k_class",
  "superclass",
  "term",
  "k_module",
  "k_def",
  "f_arglist",
  "singleton",
  "@6",
  "@7",
  "@8",
  "@9",
  "@10",
  "@11",
  "@12",
  "@13",
  "@14",
  "@15",
  "f_marg",
  "f_norm_arg",
  "f_margs",
  "f_marg_list",
  "block_args_tail",
  "f_block_kwarg",
  "f_kwrest",
  "opt_f_block_arg",
  "f_block_arg",
  "opt_block_args_tail",
  "block_param",
  "f_arg",
  "f_block_optarg",
  "f_rest_arg",
  "opt_block_param",
  "block_param_def",
  "opt_bv_decl",
  "bv_decls",
  "bvar",
  "f_bad_arg",
  "f_larglist",
  "lambda_body",
  "@16",
  "@17",
  "f_args",
  "do_block",
  "@18",
  "@19",
  "do_body",
  "@20",
  "operation3",
  "@21",
  "@22",
  "@23",
  "@24",
  "@25",
  "cases",
  "exc_list",
  "exc_var",
  "numeric",
  "string",
  "string1",
  "string_contents",
  "xstring_contents",
  "regexp_contents",
  "word_list",
  "word",
  "string_content",
  "symbol_list",
  "qword_list",
  "qsym_list",
  "string_dvar",
  "@26",
  "@27",
  "args_tail",
  "@28",
  "f_kwarg",
  "opt_args_tail",
  "f_optarg",
  "f_arg_asgn",
  "f_arg_item",
  "f_label",
  "f_kw",
  "f_block_kw",
  "kwrest_mark",
  "f_opt",
  "f_block_opt",
  "restarg_mark",
  "blkarg_mark",
  "assoc" ]
Ractor.make_shareable(Racc_token_to_s_table) if defined?(Ractor)

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

# reduce 1 omitted

def _reduce_2(val, _values, result)
                      result = @builder.compstmt(val[0])

    result
end

def _reduce_3(val, _values, result)
                      result = []

    result
end

def _reduce_4(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_5(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_6(val, _values, result)
                      result = [ val[1] ]

    result
end

# reduce 7 omitted

def _reduce_8(val, _values, result)
                      result = @builder.preexe(val[0], *val[1])

    result
end

def _reduce_9(val, _values, result)
                      result = val

    result
end

def _reduce_10(val, _values, result)
                      rescue_bodies     = val[1]
                      else_t,   else_   = val[2]
                      ensure_t, ensure_ = val[3]

                      if rescue_bodies.empty? && !else_t.nil?
                        diagnostic :error, :useless_else, nil, else_t
                      end

                      result = @builder.begin_body(val[0],
                                  rescue_bodies,
                                  else_t,   else_,
                                  ensure_t, ensure_)

    result
end

def _reduce_11(val, _values, result)
                      result = @builder.compstmt(val[0])

    result
end

def _reduce_12(val, _values, result)
                      result = []

    result
end

def _reduce_13(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_14(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_15(val, _values, result)
                      result = [ val[1] ]

    result
end

# reduce 16 omitted

def _reduce_17(val, _values, result)
                      diagnostic :error, :begin_in_method, nil, val[0]

    result
end

def _reduce_18(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_19(val, _values, result)
                      result = @builder.alias(val[0], val[1], val[3])

    result
end

def _reduce_20(val, _values, result)
                      result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.gvar(val[2]))

    result
end

def _reduce_21(val, _values, result)
                      result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.back_ref(val[2]))

    result
end

def _reduce_22(val, _values, result)
                      diagnostic :error, :nth_ref_alias, nil, val[2]

    result
end

def _reduce_23(val, _values, result)
                      result = @builder.undef_method(val[0], val[1])

    result
end

def _reduce_24(val, _values, result)
                      result = @builder.condition_mod(val[0], nil,
                                                      val[1], val[2])

    result
end

def _reduce_25(val, _values, result)
                      result = @builder.condition_mod(nil, val[0],
                                                      val[1], val[2])

    result
end

def _reduce_26(val, _values, result)
                      result = @builder.loop_mod(:while, val[0], val[1], val[2])

    result
end

def _reduce_27(val, _values, result)
                      result = @builder.loop_mod(:until, val[0], val[1], val[2])

    result
end

def _reduce_28(val, _values, result)
                      rescue_body = @builder.rescue_body(val[1],
                                        nil, nil, nil,
                                        nil, val[2])

                      result = @builder.begin_body(val[0], [ rescue_body ])

    result
end

def _reduce_29(val, _values, result)
                      result = @builder.postexe(val[0], val[1], val[2], val[3])

    result
end

# reduce 30 omitted

def _reduce_31(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1], val[2])

    result
end

def _reduce_32(val, _values, result)
                      result = @builder.assign(val[0], val[1],
                                  @builder.array(nil, val[2], nil))

    result
end

def _reduce_33(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1], val[2])

    result
end

# reduce 34 omitted

def _reduce_35(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

def _reduce_36(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_37(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])

    result
end

def _reduce_38(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_39(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_40(val, _values, result)
                      const  = @builder.const_op_assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))
                      result = @builder.op_assign(const, val[3], val[4])

    result
end

def _reduce_41(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_42(val, _values, result)
                      @builder.op_assign(val[0], val[1], val[2])

    result
end

# reduce 43 omitted

def _reduce_44(val, _values, result)
                      rescue_body = @builder.rescue_body(val[1],
                                        nil, nil, nil,
                                        nil, val[2])

                      result = @builder.begin_body(val[0], [ rescue_body ])

    result
end

# reduce 45 omitted

# reduce 46 omitted

def _reduce_47(val, _values, result)
                      result = @builder.logical_op(:and, val[0], val[1], val[2])

    result
end

def _reduce_48(val, _values, result)
                      result = @builder.logical_op(:or, val[0], val[1], val[2])

    result
end

def _reduce_49(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[2], nil)

    result
end

def _reduce_50(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[1], nil)

    result
end

# reduce 51 omitted

# reduce 52 omitted

def _reduce_53(val, _values, result)
                      @lexer.cond.push(true)

    result
end

def _reduce_54(val, _values, result)
                      @lexer.cond.pop
                      result = [ val[1], val[2] ]

    result
end

# reduce 55 omitted

# reduce 56 omitted

# reduce 57 omitted

def _reduce_58(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
end

def _reduce_59(val, _values, result)
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_60(val, _values, result)
                      result = [ val[0], *val[2], val[3] ]
                      @context.in_block = val[1].in_block

    result
end

# reduce 61 omitted

def _reduce_62(val, _values, result)
                      result = @builder.call_method(nil, nil, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_63(val, _values, result)
                      method_call = @builder.call_method(nil, nil, val[0],
                                        nil, val[1], nil)

                      begin_t, args, body, end_t = val[2]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_64(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
end

def _reduce_65(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                        nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_66(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
end

def _reduce_67(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                        nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_68(val, _values, result)
                      result = @builder.keyword_cmd(:super, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_69(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_70(val, _values, result)
                      result = @builder.keyword_cmd(:return, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_71(val, _values, result)
                      result = @builder.keyword_cmd(:break, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_72(val, _values, result)
                      result = @builder.keyword_cmd(:next, val[0],
                                  nil, val[1], nil)

    result
end

def _reduce_73(val, _values, result)
                      result = @builder.multi_lhs(nil, val[0], nil)

    result
end

def _reduce_74(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_75(val, _values, result)
                      result = @builder.multi_lhs(nil, val[0], nil)

    result
end

def _reduce_76(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

# reduce 77 omitted

def _reduce_78(val, _values, result)
                      result = val[0].
                                  push(val[1])

    result
end

def _reduce_79(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1], val[2]))

    result
end

def _reduce_80(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1], val[2])).
                                  concat(val[4])

    result
end

def _reduce_81(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1]))

    result
end

def _reduce_82(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1])).
                                  concat(val[3])

    result
end

def _reduce_83(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

def _reduce_84(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]),
                                 *val[3] ]

    result
end

def _reduce_85(val, _values, result)
                      result = [ @builder.splat(val[0]) ]

    result
end

def _reduce_86(val, _values, result)
                      result = [ @builder.splat(val[0]),
                                 *val[2] ]

    result
end

# reduce 87 omitted

def _reduce_88(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_89(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_90(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_91(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_92(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_93(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_94(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_95(val, _values, result)
                      result = @builder.index_asgn(val[0], val[1], val[2], val[3])

    result
end

def _reduce_96(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_97(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_98(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_99(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))

    result
end

def _reduce_100(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))

    result
end

def _reduce_101(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_102(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_103(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_104(val, _values, result)
                      result = @builder.index_asgn(val[0], val[1], val[2], val[3])

    result
end

def _reduce_105(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_106(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_107(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])

    result
end

def _reduce_108(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))

    result
end

def _reduce_109(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))

    result
end

def _reduce_110(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_111(val, _values, result)
                      diagnostic :error, :module_name_const, nil, val[0]

    result
end

# reduce 112 omitted

def _reduce_113(val, _values, result)
                      result = @builder.const_global(val[0], val[1])

    result
end

def _reduce_114(val, _values, result)
                      result = @builder.const(val[0])

    result
end

def _reduce_115(val, _values, result)
                      result = @builder.const_fetch(val[0], val[1], val[2])

    result
end

# reduce 116 omitted

# reduce 117 omitted

# reduce 118 omitted

# reduce 119 omitted

# reduce 120 omitted

def _reduce_121(val, _values, result)
                      result = @builder.symbol_internal(val[0])

    result
end

# reduce 122 omitted

# reduce 123 omitted

# reduce 124 omitted

def _reduce_125(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_126(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_127(val, _values, result)
                      result = val[0] << val[3]

    result
end

# reduce 128 omitted

# reduce 129 omitted

# reduce 130 omitted

# reduce 131 omitted

# reduce 132 omitted

# reduce 133 omitted

# reduce 134 omitted

# reduce 135 omitted

# reduce 136 omitted

# reduce 137 omitted

# reduce 138 omitted

# reduce 139 omitted

# reduce 140 omitted

# reduce 141 omitted

# reduce 142 omitted

# reduce 143 omitted

# reduce 144 omitted

# reduce 145 omitted

# reduce 146 omitted

# reduce 147 omitted

# reduce 148 omitted

# reduce 149 omitted

# reduce 150 omitted

# reduce 151 omitted

# reduce 152 omitted

# reduce 153 omitted

# reduce 154 omitted

# reduce 155 omitted

# reduce 156 omitted

# reduce 157 omitted

# reduce 158 omitted

# reduce 159 omitted

# reduce 160 omitted

# reduce 161 omitted

# reduce 162 omitted

# reduce 163 omitted

# reduce 164 omitted

# reduce 165 omitted

# reduce 166 omitted

# reduce 167 omitted

# reduce 168 omitted

# reduce 169 omitted

# reduce 170 omitted

# reduce 171 omitted

# reduce 172 omitted

# reduce 173 omitted

# reduce 174 omitted

# reduce 175 omitted

# reduce 176 omitted

# reduce 177 omitted

# reduce 178 omitted

# reduce 179 omitted

# reduce 180 omitted

# reduce 181 omitted

# reduce 182 omitted

# reduce 183 omitted

# reduce 184 omitted

# reduce 185 omitted

# reduce 186 omitted

# reduce 187 omitted

# reduce 188 omitted

# reduce 189 omitted

# reduce 190 omitted

# reduce 191 omitted

# reduce 192 omitted

# reduce 193 omitted

# reduce 194 omitted

# reduce 195 omitted

# reduce 196 omitted

# reduce 197 omitted

# reduce 198 omitted

def _reduce_199(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])

    result
end

def _reduce_200(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_201(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])

    result
end

def _reduce_202(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_203(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_204(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
end

def _reduce_205(val, _values, result)
                      const  = @builder.const_op_assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))
                      result = @builder.op_assign(const, val[3], val[4])

    result
end

def _reduce_206(val, _values, result)
                      const  = @builder.const_op_assignable(
                                  @builder.const_global(val[0], val[1]))
                      result = @builder.op_assign(const, val[2], val[3])

    result
end

def _reduce_207(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])

    result
end

def _reduce_208(val, _values, result)
                      result = @builder.range_inclusive(val[0], val[1], val[2])

    result
end

def _reduce_209(val, _values, result)
                      result = @builder.range_exclusive(val[0], val[1], val[2])

    result
end

def _reduce_210(val, _values, result)
                      result = @builder.range_inclusive(val[0], val[1], nil)

    result
end

def _reduce_211(val, _values, result)
                      result = @builder.range_exclusive(val[0], val[1], nil)

    result
end

def _reduce_212(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_213(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_214(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_215(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_216(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_217(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_218(val, _values, result)
                      result = @builder.unary_op(val[0],
                                  @builder.binary_op(
                                    val[1], val[2], val[3]))

    result
end

def _reduce_219(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_220(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_221(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_222(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_223(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_224(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

# reduce 225 omitted

def _reduce_226(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_227(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_228(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_229(val, _values, result)
                      result = @builder.match_op(val[0], val[1], val[2])

    result
end

def _reduce_230(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_231(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[1], nil)

    result
end

def _reduce_232(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])

    result
end

def _reduce_233(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_234(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_235(val, _values, result)
                      result = @builder.logical_op(:and, val[0], val[1], val[2])

    result
end

def _reduce_236(val, _values, result)
                      result = @builder.logical_op(:or, val[0], val[1], val[2])

    result
end

def _reduce_237(val, _values, result)
                      result = @builder.keyword_cmd(:defined?, val[0], nil, [ val[2] ], nil)

    result
end

def _reduce_238(val, _values, result)
                      result = @builder.ternary(val[0], val[1],
                                                val[2], val[4], val[5])

    result
end

# reduce 239 omitted

# reduce 240 omitted

# reduce 241 omitted

# reduce 242 omitted

# reduce 243 omitted

def _reduce_244(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

def _reduce_245(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])

    result
end

# reduce 246 omitted

# reduce 247 omitted

# reduce 248 omitted

def _reduce_249(val, _values, result)
                      result = val[0] << @builder.associate(nil, val[2], nil)

    result
end

def _reduce_250(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]

    result
end

# reduce 251 omitted

def _reduce_252(val, _values, result)
                      rescue_body = @builder.rescue_body(val[1],
                                        nil, nil, nil,
                                        nil, val[2])

                      result = @builder.begin_body(val[0], [ rescue_body ])

    result
end

def _reduce_253(val, _values, result)
                      result = val

    result
end

def _reduce_254(val, _values, result)
                      result = [ nil, [], nil ]

    result
end

# reduce 255 omitted

def _reduce_256(val, _values, result)
                      result = []

    result
end

# reduce 257 omitted

# reduce 258 omitted

def _reduce_259(val, _values, result)
                      result = val[0] << @builder.associate(nil, val[2], nil)

    result
end

def _reduce_260(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]

    result
end

def _reduce_261(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_262(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_263(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]
                      result.concat(val[1])

    result
end

def _reduce_264(val, _values, result)
                      assocs = @builder.associate(nil, val[2], nil)
                      result = val[0] << assocs
                      result.concat(val[3])

    result
end

def _reduce_265(val, _values, result)
                      result =  [ val[0] ]

    result
end

def _reduce_266(val, _values, result)
                      # When branch gets invoked by RACC's lookahead
                      # and command args start with '[' or '('
                      # we need to put `true` to the cmdarg stack
                      # **before** `false` pushed by lexer
                      #   m [], n
                      #     ^
                      # Right here we have cmdarg [...0] because
                      # lexer pushed it on '['
                      # We need to modify cmdarg stack to [...10]
                      #
                      # For all other cases (like `m n` or `m n, []`) we simply put 1 to the stack
                      # and later lexer pushes corresponding bits on top of it.
                      last_token = @last_token[0]
                      lookahead = last_token == :tLBRACK || last_token == :tLPAREN_ARG

                      if lookahead
                        top = @lexer.cmdarg.pop
                        @lexer.cmdarg.push(true)
                        @lexer.cmdarg.push(top)
                      else
                        @lexer.cmdarg.push(true)
                      end

    result
end

def _reduce_267(val, _values, result)
                      # call_args can be followed by tLBRACE_ARG (that does cmdarg.push(0) in the lexer)
                      # but the push must be done after cmdarg.pop() in the parser.
                      # So this code does cmdarg.pop() to pop 0 pushed by tLBRACE_ARG,
                      # cmdarg.pop() to pop 1 pushed by command_args,
                      # and cmdarg.push(0) to restore back the flag set by tLBRACE_ARG.
                      last_token = @last_token[0]
                      lookahead = last_token == :tLBRACE_ARG
                      if lookahead
                        top = @lexer.cmdarg.pop
                        @lexer.cmdarg.pop
                        @lexer.cmdarg.push(top)
                      else
                        @lexer.cmdarg.pop
                      end

                      result = val[1]

    result
end

def _reduce_268(val, _values, result)
                      result = @builder.block_pass(val[0], val[1])

    result
end

def _reduce_269(val, _values, result)
                      result = [ val[1] ]

    result
end

def _reduce_270(val, _values, result)
                      result = []

    result
end

def _reduce_271(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_272(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

def _reduce_273(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_274(val, _values, result)
                      result = val[0] << @builder.splat(val[2], val[3])

    result
end

def _reduce_275(val, _values, result)
                      result = @builder.array(nil, val[0], nil)

    result
end

# reduce 276 omitted

def _reduce_277(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_278(val, _values, result)
                      result = val[0] << @builder.splat(val[2], val[3])

    result
end

def _reduce_279(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]

    result
end

# reduce 280 omitted

# reduce 281 omitted

# reduce 282 omitted

# reduce 283 omitted

# reduce 284 omitted

# reduce 285 omitted

# reduce 286 omitted

# reduce 287 omitted

# reduce 288 omitted

# reduce 289 omitted

def _reduce_290(val, _values, result)
                      result = @builder.call_method(nil, nil, val[0])

    result
end

def _reduce_291(val, _values, result)
                      @lexer.cmdarg.push(false)

    result
end

def _reduce_292(val, _values, result)
                      @lexer.cmdarg.pop

                      result = @builder.begin_keyword(val[0], val[2], val[3])

    result
end

def _reduce_293(val, _values, result)
                      @lexer.state = :expr_endarg

    result
end

def _reduce_294(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[3])

    result
end

def _reduce_295(val, _values, result)
                      @lexer.state = :expr_endarg

    result
end

def _reduce_296(val, _values, result)
                      result = @builder.begin(val[0], nil, val[3])

    result
end

def _reduce_297(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])

    result
end

def _reduce_298(val, _values, result)
                      result = @builder.const_fetch(val[0], val[1], val[2])

    result
end

def _reduce_299(val, _values, result)
                      result = @builder.const_global(val[0], val[1])

    result
end

def _reduce_300(val, _values, result)
                      result = @builder.array(val[0], val[1], val[2])

    result
end

def _reduce_301(val, _values, result)
                      result = @builder.associate(val[0], val[1], val[2])

    result
end

def _reduce_302(val, _values, result)
                      result = @builder.keyword_cmd(:return, val[0])

    result
end

def _reduce_303(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0], val[1], val[2], val[3])

    result
end

def _reduce_304(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0], val[1], [], val[2])

    result
end

def _reduce_305(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0])

    result
end

def _reduce_306(val, _values, result)
                      result = @builder.keyword_cmd(:defined?, val[0],
                                                    val[2], [ val[3] ], val[4])

    result
end

def _reduce_307(val, _values, result)
                      result = @builder.not_op(val[0], val[1], val[2], val[3])

    result
end

def _reduce_308(val, _values, result)
                      result = @builder.not_op(val[0], val[1], nil, val[2])

    result
end

def _reduce_309(val, _values, result)
                      method_call = @builder.call_method(nil, nil, val[0])

                      begin_t, args, body, end_t = val[1]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

# reduce 310 omitted

def _reduce_311(val, _values, result)
                      begin_t, args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, args, body, end_t)

    result
end

def _reduce_312(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_313(val, _values, result)
                      lambda_call = @builder.call_lambda(val[0])

                      args, (begin_t, body, end_t) = val[2]
                      result      = @builder.block(lambda_call,
                                      begin_t, args, body, end_t)

                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_314(val, _values, result)
                      else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  val[3], else_t,
                                                  else_,  val[5])

    result
end

def _reduce_315(val, _values, result)
                      else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  else_,  else_t,
                                                  val[3], val[5])

    result
end

def _reduce_316(val, _values, result)
                      result = @builder.loop(:while, val[0], *val[1], val[2], val[3])

    result
end

def _reduce_317(val, _values, result)
                      result = @builder.loop(:until, val[0], *val[1], val[2], val[3])

    result
end

def _reduce_318(val, _values, result)
                      *when_bodies, (else_t, else_body) = *val[3]

                      result = @builder.case(val[0], val[1],
                                             when_bodies, else_t, else_body,
                                             val[4])

    result
end

def _reduce_319(val, _values, result)
                      *when_bodies, (else_t, else_body) = *val[2]

                      result = @builder.case(val[0], nil,
                                             when_bodies, else_t, else_body,
                                             val[3])

    result
end

def _reduce_320(val, _values, result)
                      result = @builder.for(val[0], val[1], val[2], *val[3], val[4], val[5])

    result
end

def _reduce_321(val, _values, result)
                      local_push
                      @context.in_class = true

    result
end

def _reduce_322(val, _values, result)
                      k_class, ctx = val[0]
                      if @context.in_def
                        diagnostic :error, :class_in_def, nil, k_class
                      end

                      lt_t, superclass = val[2]
                      result = @builder.def_class(k_class, val[1],
                                                  lt_t, superclass,
                                                  val[4], val[5])

                      local_pop
                      @context.in_class = ctx.in_class

    result
end

def _reduce_323(val, _values, result)
                      @context.in_def = false
                      @context.in_class = false
                      local_push

    result
end

def _reduce_324(val, _values, result)
                      k_class, ctx = val[0]
                      result = @builder.def_sclass(k_class, val[1], val[2],
                                                   val[5], val[6])

                      local_pop
                      @context.in_def = ctx.in_def
                      @context.in_class = ctx.in_class

    result
end

def _reduce_325(val, _values, result)
                      @context.in_class = true
                      local_push

    result
end

def _reduce_326(val, _values, result)
                      k_mod, ctx = val[0]
                      if @context.in_def
                        diagnostic :error, :module_in_def, nil, k_mod
                      end

                      result = @builder.def_module(k_mod, val[1],
                                                   val[3], val[4])

                      local_pop
                      @context.in_class = ctx.in_class

    result
end

def _reduce_327(val, _values, result)
                      local_push
                      result = context.dup
                      @context.in_def = true

    result
end

def _reduce_328(val, _values, result)
                      result = @builder.def_method(val[0], val[1],
                                  val[3], val[4], val[5])

                      local_pop
                      @context.in_def = val[2].in_def

    result
end

def _reduce_329(val, _values, result)
                      @lexer.state = :expr_fname

    result
end

def _reduce_330(val, _values, result)
                      local_push
                      result = context.dup
                      @context.in_def = true

    result
end

def _reduce_331(val, _values, result)
                      result = @builder.def_singleton(val[0], val[1], val[2],
                                  val[4], val[6], val[7], val[8])

                      local_pop
                      @context.in_def = val[5].in_def

    result
end

def _reduce_332(val, _values, result)
                      result = @builder.keyword_cmd(:break, val[0])

    result
end

def _reduce_333(val, _values, result)
                      result = @builder.keyword_cmd(:next, val[0])

    result
end

def _reduce_334(val, _values, result)
                      result = @builder.keyword_cmd(:redo, val[0])

    result
end

def _reduce_335(val, _values, result)
                      result = @builder.keyword_cmd(:retry, val[0])

    result
end

# reduce 336 omitted

def _reduce_337(val, _values, result)
                      result = [ val[0], @context.dup ]

    result
end

def _reduce_338(val, _values, result)
                      result = [ val[0], @context.dup ]

    result
end

def _reduce_339(val, _values, result)
                      result = val[0]

    result
end

def _reduce_340(val, _values, result)
                      if @context.in_class && !@context.in_def && !(context.in_block || context.in_lambda)
                        diagnostic :error, :invalid_return, nil, val[0]
                      end

    result
end

# reduce 341 omitted

# reduce 342 omitted

def _reduce_343(val, _values, result)
                      result = val[1]

    result
end

# reduce 344 omitted

# reduce 345 omitted

# reduce 346 omitted

def _reduce_347(val, _values, result)
                      else_t, else_ = val[4]
                      result = [ val[0],
                                 @builder.condition(val[0], val[1], val[2],
                                                    val[3], else_t,
                                                    else_,  nil),
                               ]

    result
end

# reduce 348 omitted

def _reduce_349(val, _values, result)
                      result = val

    result
end

# reduce 350 omitted

# reduce 351 omitted

def _reduce_352(val, _values, result)
                      result = @builder.arg(val[0])

    result
end

def _reduce_353(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

def _reduce_354(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_355(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 356 omitted

def _reduce_357(val, _values, result)
                      result = val[0].
                                  push(@builder.restarg(val[2], val[3]))

    result
end

def _reduce_358(val, _values, result)
                      result = val[0].
                                  push(@builder.restarg(val[2], val[3])).
                                  concat(val[5])

    result
end

def _reduce_359(val, _values, result)
                      result = val[0].
                                  push(@builder.restarg(val[2]))

    result
end

def _reduce_360(val, _values, result)
                      result = val[0].
                                  push(@builder.restarg(val[2])).
                                  concat(val[4])

    result
end

def _reduce_361(val, _values, result)
                      result = [ @builder.restarg(val[0], val[1]) ]

    result
end

def _reduce_362(val, _values, result)
                      result = [ @builder.restarg(val[0], val[1]),
                                 *val[3] ]

    result
end

def _reduce_363(val, _values, result)
                      result = [ @builder.restarg(val[0]) ]

    result
end

def _reduce_364(val, _values, result)
                      result = [ @builder.restarg(val[0]),
                                 *val[2] ]

    result
end

def _reduce_365(val, _values, result)
                      result = val[0].concat(val[2]).concat(val[3])

    result
end

def _reduce_366(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_367(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_368(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_369(val, _values, result)
                      result = val[1]

    result
end

def _reduce_370(val, _values, result)
                      result = []

    result
end

def _reduce_371(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_372(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[6]).
                                  concat(val[7])

    result
end

def _reduce_373(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_374(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_375(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

# reduce 376 omitted

def _reduce_377(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_378(val, _values, result)
                      if val[1].empty? && val[0].size == 1
                        result = [@builder.procarg0(val[0][0])]
                      else
                        result = val[0].concat(val[1])
                      end

    result
end

def _reduce_379(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_380(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_381(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_382(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_383(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_384(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

# reduce 385 omitted

def _reduce_386(val, _values, result)
                      result = @builder.args(nil, [], nil)

    result
end

def _reduce_387(val, _values, result)
                      @lexer.state = :expr_value

    result
end

def _reduce_388(val, _values, result)
                      result = @builder.args(val[0], val[1], val[2])

    result
end

def _reduce_389(val, _values, result)
                      result = @builder.args(val[0], [], val[0])

    result
end

def _reduce_390(val, _values, result)
                      result = @builder.args(val[0], val[1].concat(val[2]), val[3])

    result
end

def _reduce_391(val, _values, result)
                      result = []

    result
end

def _reduce_392(val, _values, result)
                      result = val[2]

    result
end

def _reduce_393(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_394(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_395(val, _values, result)
                      @static_env.declare val[0][0]
                      result = @builder.shadowarg(val[0])

    result
end

# reduce 396 omitted

def _reduce_397(val, _values, result)
                      @static_env.extend_dynamic

    result
end

def _reduce_398(val, _values, result)
                      @lexer.cmdarg.push(false)

    result
end

def _reduce_399(val, _values, result)
                      @lexer.cmdarg.pop

                      result = [ val[1], val[3] ]

                      @static_env.unextend

    result
end

def _reduce_400(val, _values, result)
                      result = @builder.args(val[0], val[1].concat(val[2]), val[3])

    result
end

def _reduce_401(val, _values, result)
                      result = @builder.args(nil, val[0], nil)

    result
end

def _reduce_402(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_403(val, _values, result)
                      result = [ val[0], val[2], val[3] ]
                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_404(val, _values, result)
                      result = @context.dup
                      @context.in_lambda = true

    result
end

def _reduce_405(val, _values, result)
                      result = [ val[0], val[2], val[3] ]
                      @context.in_lambda = val[1].in_lambda

    result
end

def _reduce_406(val, _values, result)
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_407(val, _values, result)
                      result = [ val[0], *val[2], val[3] ]
                      @context.in_block = val[1].in_block

    result
end

def _reduce_408(val, _values, result)
                      begin_t, block_args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, block_args, body, end_t)

    result
end

def _reduce_409(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_410(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                      lparen_t, args, rparen_t)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_411(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                      nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
end

def _reduce_412(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.call_method(nil, nil, val[0],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_413(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_414(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_415(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2])

    result
end

def _reduce_416(val, _values, result)
                      lparen_t, args, rparen_t = val[2]
                      result = @builder.call_method(val[0], val[1], nil,
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_417(val, _values, result)
                      lparen_t, args, rparen_t = val[2]
                      result = @builder.call_method(val[0], val[1], nil,
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_418(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.keyword_cmd(:super, val[0],
                                  lparen_t, args, rparen_t)

    result
end

def _reduce_419(val, _values, result)
                      result = @builder.keyword_cmd(:zsuper, val[0])

    result
end

def _reduce_420(val, _values, result)
                      result = @builder.index(val[0], val[1], val[2], val[3])

    result
end

def _reduce_421(val, _values, result)
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_422(val, _values, result)
                      result = [ val[0], *val[2], val[3] ]
                      @context.in_block = val[1].in_block

    result
end

def _reduce_423(val, _values, result)
                      result = @context.dup
                      @context.in_block = true

    result
end

def _reduce_424(val, _values, result)
                      result = [ val[0], *val[2], val[3] ]
                      @context.in_block = val[1].in_block

    result
end

def _reduce_425(val, _values, result)
                      @static_env.extend_dynamic

    result
end

def _reduce_426(val, _values, result)
                      result = [ val[1], val[2] ]

                      @static_env.unextend

    result
end

def _reduce_427(val, _values, result)
                      @static_env.extend_dynamic

    result
end

def _reduce_428(val, _values, result)
                      @lexer.cmdarg.push(false)

    result
end

def _reduce_429(val, _values, result)
                      result = [ val[2], val[3] ]

                      @static_env.unextend
                      @lexer.cmdarg.pop

    result
end

def _reduce_430(val, _values, result)
                      result = [ @builder.when(val[0], val[1], val[2], val[3]),
                                 *val[4] ]

    result
end

def _reduce_431(val, _values, result)
                      result = [ val[0] ]

    result
end

# reduce 432 omitted

def _reduce_433(val, _values, result)
                      assoc_t, exc_var = val[2]

                      if val[1]
                        exc_list = @builder.array(nil, val[1], nil)
                      end

                      result = [ @builder.rescue_body(val[0],
                                      exc_list, assoc_t, exc_var,
                                      val[3], val[4]),
                                 *val[5] ]

    result
end

def _reduce_434(val, _values, result)
                      result = []

    result
end

def _reduce_435(val, _values, result)
                      result = [ val[0] ]

    result
end

# reduce 436 omitted

# reduce 437 omitted

def _reduce_438(val, _values, result)
                      result = [ val[0], val[1] ]

    result
end

# reduce 439 omitted

def _reduce_440(val, _values, result)
                      result = [ val[0], val[1] ]

    result
end

# reduce 441 omitted

# reduce 442 omitted

# reduce 443 omitted

# reduce 444 omitted

def _reduce_445(val, _values, result)
                      result = @builder.string_compose(nil, val[0], nil)

    result
end

def _reduce_446(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_447(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_448(val, _values, result)
                      string = @builder.string_compose(val[0], val[1], val[2])
                      result = @builder.dedent_string(string, @lexer.dedent_level)

    result
end

def _reduce_449(val, _values, result)
                      string = @builder.string(val[0])
                      result = @builder.dedent_string(string, @lexer.dedent_level)

    result
end

def _reduce_450(val, _values, result)
                      result = @builder.character(val[0])

    result
end

def _reduce_451(val, _values, result)
                      string = @builder.xstring_compose(val[0], val[1], val[2])
                      result = @builder.dedent_string(string, @lexer.dedent_level)

    result
end

def _reduce_452(val, _values, result)
                      opts   = @builder.regexp_options(val[3])
                      result = @builder.regexp_compose(val[0], val[1], val[2], opts)

    result
end

def _reduce_453(val, _values, result)
                      result = @builder.words_compose(val[0], val[1], val[2])

    result
end

def _reduce_454(val, _values, result)
                      result = []

    result
end

def _reduce_455(val, _values, result)
                      result = val[0] << @builder.word(val[1])

    result
end

def _reduce_456(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_457(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_458(val, _values, result)
                      result = @builder.symbols_compose(val[0], val[1], val[2])

    result
end

def _reduce_459(val, _values, result)
                      result = []

    result
end

def _reduce_460(val, _values, result)
                      result = val[0] << @builder.word(val[1])

    result
end

def _reduce_461(val, _values, result)
                      result = @builder.words_compose(val[0], val[1], val[2])

    result
end

def _reduce_462(val, _values, result)
                      result = @builder.symbols_compose(val[0], val[1], val[2])

    result
end

def _reduce_463(val, _values, result)
                      result = []

    result
end

def _reduce_464(val, _values, result)
                      result = val[0] << @builder.string_internal(val[1])

    result
end

def _reduce_465(val, _values, result)
                      result = []

    result
end

def _reduce_466(val, _values, result)
                      result = val[0] << @builder.symbol_internal(val[1])

    result
end

def _reduce_467(val, _values, result)
                      result = []

    result
end

def _reduce_468(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_469(val, _values, result)
                      result = []

    result
end

def _reduce_470(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_471(val, _values, result)
                      result = []

    result
end

def _reduce_472(val, _values, result)
                      result = val[0] << val[1]

    result
end

def _reduce_473(val, _values, result)
                      result = @builder.string_internal(val[0])

    result
end

def _reduce_474(val, _values, result)
                      result = val[1]

    result
end

def _reduce_475(val, _values, result)
                      @lexer.cmdarg.push(false)
                      @lexer.cond.push(false)

    result
end

def _reduce_476(val, _values, result)
                      @lexer.cmdarg.pop
                      @lexer.cond.pop

                      result = @builder.begin(val[0], val[2], val[3])

    result
end

def _reduce_477(val, _values, result)
                      result = @builder.gvar(val[0])

    result
end

def _reduce_478(val, _values, result)
                      result = @builder.ivar(val[0])

    result
end

def _reduce_479(val, _values, result)
                      result = @builder.cvar(val[0])

    result
end

# reduce 480 omitted

def _reduce_481(val, _values, result)
                      @lexer.state = :expr_end
                      result = @builder.symbol(val[0])

    result
end

def _reduce_482(val, _values, result)
                      @lexer.state = :expr_end
                      result = @builder.symbol_compose(val[0], val[1], val[2])

    result
end

def _reduce_483(val, _values, result)
                      result = val[0]

    result
end

def _reduce_484(val, _values, result)
                      if @builder.respond_to? :negate
                        # AST builder interface compatibility
                        result = @builder.negate(val[0], val[1])
                      else
                        result = @builder.unary_num(val[0], val[1])
                      end

    result
end

def _reduce_485(val, _values, result)
                      @lexer.state = :expr_end
                      result = @builder.integer(val[0])

    result
end

def _reduce_486(val, _values, result)
                      @lexer.state = :expr_end
                      result = @builder.float(val[0])

    result
end

def _reduce_487(val, _values, result)
                      @lexer.state = :expr_end
                      result = @builder.rational(val[0])

    result
end

def _reduce_488(val, _values, result)
                      @lexer.state = :expr_end
                      result = @builder.complex(val[0])

    result
end

def _reduce_489(val, _values, result)
                      result = @builder.ident(val[0])

    result
end

def _reduce_490(val, _values, result)
                      result = @builder.ivar(val[0])

    result
end

def _reduce_491(val, _values, result)
                      result = @builder.gvar(val[0])

    result
end

def _reduce_492(val, _values, result)
                      result = @builder.const(val[0])

    result
end

def _reduce_493(val, _values, result)
                      result = @builder.cvar(val[0])

    result
end

def _reduce_494(val, _values, result)
                      result = @builder.nil(val[0])

    result
end

def _reduce_495(val, _values, result)
                      result = @builder.self(val[0])

    result
end

def _reduce_496(val, _values, result)
                      result = @builder.true(val[0])

    result
end

def _reduce_497(val, _values, result)
                      result = @builder.false(val[0])

    result
end

def _reduce_498(val, _values, result)
                      result = @builder.__FILE__(val[0])

    result
end

def _reduce_499(val, _values, result)
                      result = @builder.__LINE__(val[0])

    result
end

def _reduce_500(val, _values, result)
                      result = @builder.__ENCODING__(val[0])

    result
end

def _reduce_501(val, _values, result)
                      result = @builder.accessible(val[0])

    result
end

def _reduce_502(val, _values, result)
                      result = @builder.accessible(val[0])

    result
end

def _reduce_503(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_504(val, _values, result)
                      result = @builder.assignable(val[0])

    result
end

def _reduce_505(val, _values, result)
                      result = @builder.nth_ref(val[0])

    result
end

def _reduce_506(val, _values, result)
                      result = @builder.back_ref(val[0])

    result
end

def _reduce_507(val, _values, result)
                      @lexer.state = :expr_value

    result
end

def _reduce_508(val, _values, result)
                      result = [ val[0], val[2] ]

    result
end

def _reduce_509(val, _values, result)
                      result = nil

    result
end

def _reduce_510(val, _values, result)
                      result = @builder.args(val[0], val[1], val[2])

                      @lexer.state = :expr_value

    result
end

def _reduce_511(val, _values, result)
                      result = @context.in_kwarg
                      @context.in_kwarg = true

    result
end

def _reduce_512(val, _values, result)
                      @context.in_kwarg = val[0]
                      result = @builder.args(nil, val[1], nil)

    result
end

def _reduce_513(val, _values, result)
                      result = val[0].concat(val[2]).concat(val[3])

    result
end

def _reduce_514(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_515(val, _values, result)
                      result = val[0].concat(val[1])

    result
end

def _reduce_516(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_517(val, _values, result)
                      result = val[1]

    result
end

def _reduce_518(val, _values, result)
                      result = []

    result
end

def _reduce_519(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_520(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[6]).
                                  concat(val[7])

    result
end

def _reduce_521(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_522(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_523(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_524(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_525(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_526(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_527(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
end

def _reduce_528(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_529(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_530(val, _values, result)
                      result = val[0].
                                  concat(val[1])

    result
end

def _reduce_531(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
end

def _reduce_532(val, _values, result)
                      result = val[0]

    result
end

def _reduce_533(val, _values, result)
                      result = []

    result
end

def _reduce_534(val, _values, result)
                      diagnostic :error, :argument_const, nil, val[0]

    result
end

def _reduce_535(val, _values, result)
                      diagnostic :error, :argument_ivar, nil, val[0]

    result
end

def _reduce_536(val, _values, result)
                      diagnostic :error, :argument_gvar, nil, val[0]

    result
end

def _reduce_537(val, _values, result)
                      diagnostic :error, :argument_cvar, nil, val[0]

    result
end

# reduce 538 omitted

def _reduce_539(val, _values, result)
                      @static_env.declare val[0][0]

                      result = val[0]

    result
end

def _reduce_540(val, _values, result)
                      result = val[0]

    result
end

def _reduce_541(val, _values, result)
                      result = @builder.arg(val[0])

    result
end

def _reduce_542(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])

    result
end

def _reduce_543(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_544(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_545(val, _values, result)
                      check_kwarg_name(val[0])

                      @static_env.declare val[0][0]

                      result = val[0]

    result
end

def _reduce_546(val, _values, result)
                      result = @builder.kwoptarg(val[0], val[1])

    result
end

def _reduce_547(val, _values, result)
                      result = @builder.kwarg(val[0])

    result
end

def _reduce_548(val, _values, result)
                      result = @builder.kwoptarg(val[0], val[1])

    result
end

def _reduce_549(val, _values, result)
                      result = @builder.kwarg(val[0])

    result
end

def _reduce_550(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_551(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_552(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_553(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 554 omitted

# reduce 555 omitted

def _reduce_556(val, _values, result)
                      @static_env.declare val[1][0]

                      result = [ @builder.kwrestarg(val[0], val[1]) ]

    result
end

def _reduce_557(val, _values, result)
                      result = [ @builder.kwrestarg(val[0]) ]

    result
end

def _reduce_558(val, _values, result)
                      result = @builder.optarg(val[0], val[1], val[2])

    result
end

def _reduce_559(val, _values, result)
                      result = @builder.optarg(val[0], val[1], val[2])

    result
end

def _reduce_560(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_561(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_562(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_563(val, _values, result)
                      result = val[0] << val[2]

    result
end

# reduce 564 omitted

# reduce 565 omitted

def _reduce_566(val, _values, result)
                      @static_env.declare val[1][0]

                      result = [ @builder.restarg(val[0], val[1]) ]

    result
end

def _reduce_567(val, _values, result)
                      result = [ @builder.restarg(val[0]) ]

    result
end

# reduce 568 omitted

# reduce 569 omitted

def _reduce_570(val, _values, result)
                      @static_env.declare val[1][0]

                      result = @builder.blockarg(val[0], val[1])

    result
end

def _reduce_571(val, _values, result)
                      result = [ val[1] ]

    result
end

def _reduce_572(val, _values, result)
                      result = []

    result
end

# reduce 573 omitted

def _reduce_574(val, _values, result)
                      result = val[1]

    result
end

def _reduce_575(val, _values, result)
                      result = []

    result
end

# reduce 576 omitted

def _reduce_577(val, _values, result)
                      result = [ val[0] ]

    result
end

def _reduce_578(val, _values, result)
                      result = val[0] << val[2]

    result
end

def _reduce_579(val, _values, result)
                      result = @builder.pair(val[0], val[1], val[2])

    result
end

def _reduce_580(val, _values, result)
                      result = @builder.pair_keyword(val[0], val[1])

    result
end

def _reduce_581(val, _values, result)
                      result = @builder.pair_quoted(val[0], val[1], val[2], val[3])

    result
end

def _reduce_582(val, _values, result)
                      result = @builder.kwsplat(val[0], val[1])

    result
end

# reduce 583 omitted

# reduce 584 omitted

# reduce 585 omitted

# reduce 586 omitted

# reduce 587 omitted

# reduce 588 omitted

# reduce 589 omitted

# reduce 590 omitted

# reduce 591 omitted

# reduce 592 omitted

# reduce 593 omitted

# reduce 594 omitted

def _reduce_595(val, _values, result)
                      result = [:dot, val[0][1]]

    result
end

def _reduce_596(val, _values, result)
                      result = [:anddot, val[0][1]]

    result
end

# reduce 597 omitted

# reduce 598 omitted

# reduce 599 omitted

# reduce 600 omitted

def _reduce_601(val, _values, result)
                      result = val[1]

    result
end

def _reduce_602(val, _values, result)
                      result = val[1]

    result
end

# reduce 603 omitted

# reduce 604 omitted

# reduce 605 omitted

def _reduce_606(val, _values, result)
                    yyerrok

    result
end

# reduce 607 omitted

# reduce 608 omitted

# reduce 609 omitted

def _reduce_610(val, _values, result)
                    result = nil

    result
end

def _reduce_none(val, _values, result)
  val[0]
end

  end   # class Ruby26
end   # module Parser
