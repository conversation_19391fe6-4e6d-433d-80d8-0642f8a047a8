# -*- encoding:utf-8; warn-indent:false; frozen_string_literal: true  -*-

# line 1 "lib/parser/lexer.rl"

# line 3 "lib/parser/lexer.rl"
#
# === BEFORE YOU START ===
#
# Read the Ruby Hacking Guide chapter 11, available in English at
# http://whitequark.org/blog/2013/04/01/ruby-hacking-guide-ch-11-finite-state-lexer/
#
# Remember two things about Ragel scanners:
#
#   1) Longest match wins.
#
#   2) If two matches have the same length, the first
#      in source code wins.
#
# General rules of making <PERSON><PERSON> and <PERSON><PERSON> happy:
#
#  * `p` (position) and `@te` contain the index of the character
#    they're pointing to ("current"), plus one. `@ts` contains the index
#    of the corresponding character. The code for extracting matched token is:
#
#       @source_buffer.slice(@ts...@te)
#
#  * If your input is `foooooooobar` and the rule is:
#
#       'f' 'o'+
#
#    the result will be:
#
#       foooooooobar
#       ^ ts=0   ^ p=te=9
#
#  * A Ragel lexer action should not emit more than one token, unless
#    you know what you are doing.
#
#  * All Ragel commands (fnext, fgoto, ...) end with a semicolon.
#
#  * If an action emits the token and transitions to another state, use
#    these Ragel commands:
#
#       emit($whatever)
#       fnext $next_state; fbreak;
#
#    If you perform `fgoto` in an action which does not emit a token nor
#    rewinds the stream pointer, the parser's side-effectful,
#    context-sensitive lookahead actions will break in a hard to detect
#    and debug way.
#
#  * If an action does not emit a token:
#
#       fgoto $next_state;
#
#  * If an action features lookbehind, i.e. matches characters with the
#    intent of passing them to another action:
#
#       p = @ts - 1
#       fgoto $next_state;
#
#    or, if the lookbehind consists of a single character:
#
#       fhold; fgoto $next_state;
#
#  * Ragel merges actions. So, if you have `e_lparen = '(' %act` and
#    `c_lparen = '('` and a lexer action `e_lparen | c_lparen`, the result
#    _will_ invoke the action `act`.
#
#    e_something stands for "something with **e**mbedded action".
#
#  * EOF is explicit and is matched by `c_eof`. If you want to introspect
#    the state of the lexer, add this rule to the state:
#
#       c_eof => do_eof;
#
#  * If you proceed past EOF, the lexer will complain:
#
#       NoMethodError: undefined method `ord' for nil:NilClass
#

class Parser::Lexer

  
# line 85 "lib/parser/lexer-F0.rb"
class << self
	attr_accessor :_lex_actions
	private :_lex_actions, :_lex_actions=
end
self._lex_actions = [
	0, 1, 0, 1, 16, 1, 17, 1, 
	18, 1, 19, 1, 32, 1, 33, 1, 
	34, 1, 35, 1, 37, 1, 38, 1, 
	39, 1, 40, 1, 41, 1, 42, 1, 
	43, 1, 44, 1, 45, 1, 46, 1, 
	47, 1, 48, 1, 49, 1, 50, 1, 
	51, 1, 52, 1, 53, 1, 54, 1, 
	55, 1, 56, 1, 57, 1, 61, 1, 
	62, 1, 63, 1, 64, 1, 65, 1, 
	66, 1, 67, 1, 68, 1, 69, 1, 
	70, 1, 71, 1, 72, 1, 73, 1, 
	74, 1, 75, 1, 76, 1, 77, 1, 
	78, 1, 79, 1, 80, 1, 81, 1, 
	82, 1, 83, 1, 84, 1, 85, 1, 
	86, 1, 87, 1, 88, 1, 89, 1, 
	90, 1, 91, 1, 93, 1, 94, 1, 
	95, 1, 100, 1, 101, 1, 102, 1, 
	103, 1, 104, 1, 105, 1, 106, 1, 
	107, 1, 112, 1, 113, 1, 114, 1, 
	115, 1, 116, 1, 119, 1, 120, 1, 
	121, 1, 122, 1, 125, 1, 126, 1, 
	128, 1, 129, 1, 130, 1, 131, 1, 
	132, 1, 133, 1, 135, 1, 136, 1, 
	139, 1, 140, 1, 141, 1, 142, 1, 
	144, 1, 145, 1, 155, 1, 156, 1, 
	157, 1, 158, 1, 159, 1, 160, 1, 
	161, 1, 162, 1, 163, 1, 164, 1, 
	165, 1, 166, 1, 168, 1, 169, 1, 
	170, 1, 171, 1, 172, 1, 173, 1, 
	174, 1, 176, 1, 178, 1, 179, 1, 
	180, 1, 184, 1, 186, 1, 187, 1, 
	189, 1, 190, 1, 191, 1, 192, 1, 
	193, 1, 194, 1, 195, 1, 196, 1, 
	197, 1, 198, 1, 199, 1, 200, 1, 
	201, 1, 203, 1, 204, 1, 205, 1, 
	206, 1, 207, 1, 208, 1, 210, 1, 
	211, 1, 230, 1, 231, 1, 232, 1, 
	233, 1, 234, 1, 235, 1, 236, 1, 
	237, 1, 238, 1, 240, 1, 241, 1, 
	242, 1, 243, 1, 244, 1, 246, 1, 
	247, 1, 248, 1, 250, 1, 252, 1, 
	254, 1, 255, 1, 256, 1, 258, 1, 
	259, 1, 260, 1, 263, 1, 264, 1, 
	266, 1, 267, 1, 268, 1, 269, 1, 
	270, 1, 271, 1, 274, 1, 275, 1, 
	276, 1, 277, 1, 278, 1, 279, 1, 
	280, 1, 281, 1, 282, 1, 283, 1, 
	286, 1, 287, 1, 288, 1, 289, 1, 
	290, 1, 291, 1, 292, 1, 293, 1, 
	294, 1, 295, 1, 296, 2, 0, 18, 
	2, 0, 100, 2, 0, 104, 2, 0, 
	105, 2, 0, 167, 2, 0, 169, 2, 
	0, 239, 2, 0, 284, 2, 0, 285, 
	2, 0, 288, 2, 0, 289, 2, 2, 
	249, 2, 3, 249, 2, 4, 249, 2, 
	5, 249, 2, 6, 249, 2, 7, 249, 
	2, 9, 251, 2, 10, 251, 2, 11, 
	251, 2, 12, 251, 2, 13, 251, 2, 
	14, 111, 2, 14, 134, 2, 14, 181, 
	2, 14, 245, 2, 15, 261, 2, 16, 
	0, 2, 16, 32, 2, 16, 33, 2, 
	16, 34, 2, 16, 75, 2, 16, 84, 
	2, 16, 94, 2, 16, 105, 2, 16, 
	116, 2, 16, 118, 2, 16, 135, 2, 
	16, 142, 2, 16, 143, 2, 16, 155, 
	2, 16, 168, 2, 16, 190, 2, 16, 
	201, 2, 16, 202, 2, 16, 208, 2, 
	16, 209, 2, 16, 264, 2, 16, 265, 
	2, 16, 292, 2, 17, 18, 2, 18, 
	0, 2, 18, 75, 2, 18, 84, 2, 
	18, 94, 2, 18, 117, 2, 18, 135, 
	2, 18, 142, 2, 18, 190, 2, 18, 
	201, 2, 18, 208, 2, 18, 264, 2, 
	18, 292, 2, 19, 92, 2, 19, 177, 
	2, 19, 188, 2, 19, 271, 2, 20, 
	92, 2, 20, 177, 2, 20, 188, 2, 
	20, 257, 2, 21, 177, 2, 21, 188, 
	2, 22, 177, 2, 22, 188, 2, 23, 
	177, 2, 23, 188, 2, 24, 177, 2, 
	24, 198, 2, 25, 177, 2, 25, 188, 
	2, 26, 177, 2, 27, 253, 2, 28, 
	110, 2, 28, 182, 2, 28, 262, 2, 
	29, 261, 2, 30, 108, 2, 30, 109, 
	2, 30, 127, 2, 30, 183, 2, 30, 
	260, 2, 31, 261, 2, 35, 0, 2, 
	36, 175, 2, 37, 179, 2, 38, 179, 
	2, 39, 185, 2, 41, 47, 2, 42, 
	47, 2, 43, 47, 2, 44, 47, 2, 
	45, 47, 2, 46, 47, 2, 47, 1, 
	2, 53, 0, 2, 53, 49, 2, 53, 
	58, 2, 53, 59, 2, 53, 60, 2, 
	53, 96, 2, 53, 97, 2, 53, 98, 
	2, 53, 99, 2, 53, 123, 2, 53, 
	124, 2, 53, 137, 2, 53, 138, 2, 
	53, 147, 2, 53, 148, 2, 53, 149, 
	2, 53, 150, 2, 53, 151, 2, 53, 
	152, 2, 53, 153, 2, 53, 154, 2, 
	53, 212, 2, 53, 213, 2, 53, 215, 
	2, 53, 216, 2, 53, 217, 2, 53, 
	218, 2, 53, 219, 2, 53, 220, 2, 
	53, 221, 2, 53, 223, 2, 53, 224, 
	2, 53, 225, 2, 53, 226, 2, 53, 
	227, 2, 53, 228, 2, 53, 229, 2, 
	53, 272, 2, 53, 273, 3, 17, 18, 
	0, 3, 17, 18, 75, 3, 17, 18, 
	84, 3, 17, 18, 94, 3, 17, 18, 
	117, 3, 17, 18, 135, 3, 17, 18, 
	142, 3, 17, 18, 190, 3, 17, 18, 
	201, 3, 17, 18, 208, 3, 17, 18, 
	264, 3, 17, 18, 292, 3, 45, 47, 
	1, 3, 46, 47, 1, 3, 47, 1, 
	249, 3, 48, 8, 251, 3, 49, 8, 
	251, 3, 53, 0, 99, 3, 53, 16, 
	98, 3, 53, 16, 124, 3, 53, 16, 
	272, 3, 53, 18, 146, 3, 53, 18, 
	214, 3, 53, 18, 272, 3, 53, 33, 
	97, 3, 53, 39, 152, 3, 53, 39, 
	153, 3, 53, 45, 221, 4, 41, 47, 
	1, 249, 4, 42, 47, 1, 249, 4, 
	43, 47, 1, 249, 4, 44, 47, 1, 
	249, 4, 45, 47, 1, 249, 4, 46, 
	47, 1, 249, 4, 53, 16, 33, 97, 
	4, 53, 17, 18, 146, 4, 53, 17, 
	18, 272, 4, 53, 47, 1, 222, 5, 
	53, 45, 47, 1, 222, 5, 53, 46, 
	47, 1, 222
]

class << self
	attr_accessor :_lex_trans_keys
	private :_lex_trans_keys, :_lex_trans_keys=
end
self._lex_trans_keys = [
	0, 0, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	58, 58, 58, 58, 46, 46, 
	0, 127, 58, 58, 60, 
	60, 62, 62, 10, 10, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 115, 115, 
	99, 99, 117, 117, 101, 
	101, 108, 116, 101, 101, 
	115, 115, 115, 115, 105, 105, 
	108, 108, 105, 105, 108, 
	108, 58, 58, 0, 127, 
	10, 10, 0, 127, 9, 92, 
	10, 10, 9, 92, 58, 
	58, 98, 98, 101, 101, 
	103, 103, 105, 105, 110, 110, 
	0, 127, 61, 61, 9, 
	92, 9, 92, 9, 92, 
	9, 92, 9, 92, 10, 10, 
	0, 127, 0, 127, 61, 
	126, 93, 93, 0, 127, 
	0, 127, 10, 10, 34, 34, 
	10, 10, 39, 39, 0, 
	127, 10, 96, 96, 96, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 58, 58, 58, 58, 
	0, 127, 43, 57, 48, 57, 
	48, 57, 48, 57, 48, 
	57, 115, 115, 99, 99, 
	117, 117, 101, 101, 99, 99, 
	117, 117, 101, 101, 0, 
	127, 58, 58, 9, 92, 
	9, 92, 9, 92, 9, 92, 
	9, 92, 9, 92, 60, 
	60, 10, 10, 9, 92, 
	9, 92, 10, 10, 10, 10, 
	10, 10, 10, 10, 46, 
	46, 101, 101, 103, 103, 
	105, 105, 110, 110, 69, 69, 
	78, 78, 68, 68, 95, 
	95, 95, 95, 0, 26, 
	0, 0, 36, 64, 0, 127, 
	48, 57, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	9, 32, 0, 0, 61, 126, 
	10, 10, 10, 10, 0, 
	127, 0, 127, 48, 57, 
	115, 115, 38, 38, 42, 42, 
	64, 64, 58, 58, 60, 
	61, 62, 62, 61, 126, 
	61, 61, 61, 62, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 93, 93, 10, 10, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 124, 124, 0, 127, 
	0, 127, 9, 32, 10, 10, 
	10, 10, 46, 46, 10, 
	10, 0, 0, 0, 127, 
	0, 127, 61, 61, 0, 0, 
	9, 32, 0, 0, 61, 
	126, 10, 10, 10, 10, 
	38, 38, 42, 42, 64, 64, 
	60, 61, 62, 62, 61, 
	126, 61, 61, 61, 62, 
	0, 127, 93, 93, 10, 10, 
	124, 124, 0, 126, 0, 
	127, 0, 61, 9, 61, 
	9, 61, 0, 0, 9, 61, 
	9, 62, 46, 46, 46, 
	46, 58, 58, 9, 32, 
	0, 0, 0, 127, 0, 0, 
	9, 124, 0, 0, 10, 
	10, 10, 10, 0, 0, 
	9, 61, 58, 58, 60, 60, 
	62, 62, 9, 32, 10, 
	10, 0, 127, 102, 102, 
	101, 101, 110, 110, 104, 104, 
	0, 127, 0, 127, 0, 
	127, 0, 0, 0, 127, 
	10, 10, 0, 123, 9, 32, 
	10, 10, 10, 10, 10, 
	10, 0, 0, 111, 111, 
	0, 0, 0, 127, 0, 127, 
	9, 32, 0, 0, 10, 
	10, 10, 10, 10, 10, 
	0, 0, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	58, 61, 0, 0, 61, 
	126, 61, 61, 0, 0, 
	0, 0, 0, 0, 9, 32, 
	61, 61, 9, 32, 61, 
	126, 10, 10, 10, 10, 
	0, 127, 38, 61, 0, 0, 
	42, 61, 61, 61, 9, 
	92, 9, 92, 9, 92, 
	46, 46, 46, 46, 10, 10, 
	0, 26, 0, 127, 0, 
	127, 61, 61, 0, 0, 
	61, 126, 61, 62, 0, 0, 
	0, 0, 0, 0, 0, 
	0, 61, 126, 0, 127, 
	48, 57, 38, 38, 42, 42, 
	64, 64, 60, 61, 62, 
	62, 61, 61, 61, 62, 
	0, 127, 48, 57, 0, 127, 
	124, 124, 64, 64, 60, 
	61, 0, 0, 10, 34, 
	10, 39, 96, 96, 62, 62, 
	61, 126, 61, 62, 0, 
	26, 0, 127, 0, 127, 
	0, 127, 0, 0, 10, 10, 
	0, 0, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 61, 126, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 0, 61, 124, 0, 
	92, 9, 32, 0, 0, 
	10, 10, 10, 10, 10, 10, 
	0, 0, 0, 127, 0, 
	127, 9, 32, 0, 0, 
	10, 10, 10, 10, 10, 10, 
	0, 0, 0, 127, 0, 
	127, 61, 61, 0, 0, 
	9, 32, 0, 0, 61, 126, 
	10, 10, 10, 10, 0, 
	127, 0, 127, 48, 57, 
	61, 61, 38, 61, 0, 0, 
	0, 0, 42, 61, 61, 
	62, 46, 57, 46, 46, 
	10, 10, 48, 101, 48, 95, 
	46, 120, 48, 114, 43, 
	57, 48, 105, 102, 102, 
	0, 0, 101, 105, 0, 0, 
	0, 0, 48, 114, 48, 
	114, 48, 114, 48, 114, 
	105, 114, 102, 102, 0, 0, 
	101, 105, 115, 115, 0, 
	0, 0, 0, 48, 114, 
	48, 114, 48, 114, 48, 114, 
	48, 114, 48, 114, 48, 
	114, 48, 114, 46, 114, 
	48, 114, 46, 114, 48, 114, 
	58, 58, 60, 61, 62, 
	62, 61, 126, 61, 61, 
	61, 62, 0, 127, 0, 127, 
	0, 0, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 0, 
	10, 10, 0, 0, 0, 
	0, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	9, 92, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 127, 
	0, 127, 0, 127, 0, 
	0, 61, 124, 0, 0, 
	9, 92, 9, 92, 9, 92, 
	46, 46, 46, 46, 10, 
	10, 46, 46, 10, 10, 
	10, 61, 10, 10, 10, 101, 
	10, 110, 10, 100, 10, 
	10, 0, 95, 9, 32, 
	0, 0, 10, 10, 10, 10, 
	98, 98, 9, 32, 10, 
	10, 95, 95, 0
]

class << self
	attr_accessor :_lex_key_spans
	private :_lex_key_spans, :_lex_key_spans=
end
self._lex_key_spans = [
	0, 128, 128, 128, 128, 128, 128, 128, 
	1, 1, 1, 128, 1, 1, 1, 1, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 1, 1, 1, 1, 9, 1, 
	1, 1, 1, 1, 1, 1, 1, 128, 
	1, 128, 84, 1, 84, 1, 1, 1, 
	1, 1, 1, 128, 1, 84, 84, 84, 
	84, 84, 1, 128, 128, 66, 1, 128, 
	128, 1, 1, 1, 1, 128, 87, 1, 
	128, 128, 128, 128, 128, 128, 1, 1, 
	128, 15, 10, 10, 10, 10, 1, 1, 
	1, 1, 1, 1, 1, 128, 1, 84, 
	84, 84, 84, 84, 84, 1, 1, 84, 
	84, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 27, 
	0, 29, 128, 10, 128, 128, 128, 128, 
	24, 0, 66, 1, 1, 128, 128, 10, 
	1, 1, 1, 1, 1, 2, 1, 66, 
	1, 2, 128, 128, 128, 128, 128, 128, 
	128, 1, 1, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 1, 128, 
	128, 24, 1, 1, 1, 1, 0, 128, 
	128, 1, 0, 24, 0, 66, 1, 1, 
	1, 1, 1, 2, 1, 66, 1, 2, 
	128, 1, 1, 1, 127, 128, 62, 53, 
	53, 0, 53, 54, 1, 1, 1, 24, 
	0, 128, 0, 116, 0, 1, 1, 0, 
	53, 1, 1, 1, 24, 1, 128, 1, 
	1, 1, 1, 128, 128, 128, 0, 128, 
	1, 124, 24, 1, 1, 1, 0, 1, 
	0, 128, 128, 24, 0, 1, 1, 1, 
	0, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 4, 0, 66, 1, 0, 
	0, 0, 24, 1, 24, 66, 1, 1, 
	128, 24, 0, 20, 1, 84, 84, 84, 
	1, 1, 1, 27, 128, 128, 1, 0, 
	66, 2, 0, 0, 0, 0, 66, 128, 
	10, 1, 1, 1, 2, 1, 1, 2, 
	128, 10, 128, 1, 1, 2, 0, 25, 
	30, 1, 1, 66, 2, 27, 128, 128, 
	128, 0, 1, 0, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 66, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 0, 64, 93, 24, 0, 
	1, 1, 1, 0, 128, 128, 24, 0, 
	1, 1, 1, 0, 128, 128, 1, 0, 
	24, 0, 66, 1, 1, 128, 128, 10, 
	1, 24, 0, 0, 20, 2, 12, 1, 
	1, 54, 48, 75, 67, 15, 58, 1, 
	0, 5, 0, 0, 67, 67, 67, 67, 
	10, 1, 0, 5, 1, 0, 0, 67, 
	67, 67, 67, 67, 67, 67, 67, 69, 
	67, 69, 67, 1, 2, 1, 66, 1, 
	2, 128, 128, 0, 128, 128, 128, 128, 
	128, 128, 0, 1, 0, 0, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 84, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 0, 64, 0, 
	84, 84, 84, 1, 1, 1, 1, 1, 
	52, 1, 92, 101, 91, 1, 96, 24, 
	0, 1, 1, 1, 24, 1, 1
]

class << self
	attr_accessor :_lex_index_offsets
	private :_lex_index_offsets, :_lex_index_offsets=
end
self._lex_index_offsets = [
	0, 0, 129, 258, 387, 516, 645, 774, 
	903, 905, 907, 909, 1038, 1040, 1042, 1044, 
	1046, 1175, 1304, 1433, 1562, 1691, 1820, 1949, 
	2078, 2207, 2336, 2465, 2594, 2723, 2852, 2981, 
	3110, 3239, 3368, 3370, 3372, 3374, 3376, 3386, 
	3388, 3390, 3392, 3394, 3396, 3398, 3400, 3402, 
	3531, 3533, 3662, 3747, 3749, 3834, 3836, 3838, 
	3840, 3842, 3844, 3846, 3975, 3977, 4062, 4147, 
	4232, 4317, 4402, 4404, 4533, 4662, 4729, 4731, 
	4860, 4989, 4991, 4993, 4995, 4997, 5126, 5214, 
	5216, 5345, 5474, 5603, 5732, 5861, 5990, 5992, 
	5994, 6123, 6139, 6150, 6161, 6172, 6183, 6185, 
	6187, 6189, 6191, 6193, 6195, 6197, 6326, 6328, 
	6413, 6498, 6583, 6668, 6753, 6838, 6840, 6842, 
	6927, 7012, 7014, 7016, 7018, 7020, 7022, 7024, 
	7026, 7028, 7030, 7032, 7034, 7036, 7038, 7040, 
	7068, 7069, 7099, 7228, 7239, 7368, 7497, 7626, 
	7755, 7780, 7781, 7848, 7850, 7852, 7981, 8110, 
	8121, 8123, 8125, 8127, 8129, 8131, 8134, 8136, 
	8203, 8205, 8208, 8337, 8466, 8595, 8724, 8853, 
	8982, 9111, 9113, 9115, 9244, 9373, 9502, 9631, 
	9760, 9889, 10018, 10147, 10276, 10405, 10534, 10663, 
	10792, 10921, 11050, 11179, 11308, 11437, 11566, 11695, 
	11824, 11953, 12082, 12211, 12340, 12469, 12598, 12727, 
	12856, 12985, 13114, 13243, 13372, 13501, 13630, 13759, 
	13888, 14017, 14146, 14275, 14404, 14533, 14662, 14791, 
	14920, 15049, 15178, 15307, 15436, 15565, 15694, 15823, 
	15952, 16081, 16210, 16339, 16468, 16597, 16726, 16855, 
	16984, 17113, 17242, 17371, 17500, 17629, 17758, 17887, 
	18016, 18145, 18274, 18403, 18532, 18661, 18790, 18919, 
	19048, 19177, 19306, 19435, 19564, 19693, 19822, 19824, 
	19953, 20082, 20107, 20109, 20111, 20113, 20115, 20116, 
	20245, 20374, 20376, 20377, 20402, 20403, 20470, 20472, 
	20474, 20476, 20478, 20480, 20483, 20485, 20552, 20554, 
	20557, 20686, 20688, 20690, 20692, 20820, 20949, 21012, 
	21066, 21120, 21121, 21175, 21230, 21232, 21234, 21236, 
	21261, 21262, 21391, 21392, 21509, 21510, 21512, 21514, 
	21515, 21569, 21571, 21573, 21575, 21600, 21602, 21731, 
	21733, 21735, 21737, 21739, 21868, 21997, 22126, 22127, 
	22256, 22258, 22383, 22408, 22410, 22412, 22414, 22415, 
	22417, 22418, 22547, 22676, 22701, 22702, 22704, 22706, 
	22708, 22709, 22838, 22967, 23096, 23225, 23354, 23483, 
	23612, 23741, 23870, 23999, 24128, 24257, 24386, 24515, 
	24644, 24773, 24902, 25031, 25036, 25037, 25104, 25106, 
	25107, 25108, 25109, 25134, 25136, 25161, 25228, 25230, 
	25232, 25361, 25386, 25387, 25408, 25410, 25495, 25580, 
	25665, 25667, 25669, 25671, 25699, 25828, 25957, 25959, 
	25960, 26027, 26030, 26031, 26032, 26033, 26034, 26101, 
	26230, 26241, 26243, 26245, 26247, 26250, 26252, 26254, 
	26257, 26386, 26397, 26526, 26528, 26530, 26533, 26534, 
	26560, 26591, 26593, 26595, 26662, 26665, 26693, 26822, 
	26951, 27080, 27081, 27083, 27084, 27213, 27342, 27471, 
	27600, 27729, 27858, 27987, 28116, 28245, 28374, 28503, 
	28632, 28761, 28890, 29019, 29148, 29277, 29406, 29535, 
	29664, 29793, 29922, 30051, 30180, 30309, 30438, 30567, 
	30696, 30825, 30954, 31083, 31212, 31341, 31470, 31599, 
	31728, 31857, 31986, 32115, 32244, 32373, 32502, 32631, 
	32760, 32889, 33018, 33147, 33276, 33405, 33534, 33663, 
	33792, 33921, 34050, 34179, 34308, 34437, 34566, 34695, 
	34824, 34953, 35020, 35149, 35278, 35407, 35536, 35665, 
	35794, 35923, 36052, 36181, 36310, 36439, 36568, 36697, 
	36826, 36955, 37084, 37213, 37342, 37471, 37600, 37729, 
	37858, 37987, 38116, 38245, 38246, 38311, 38405, 38430, 
	38431, 38433, 38435, 38437, 38438, 38567, 38696, 38721, 
	38722, 38724, 38726, 38728, 38729, 38858, 38987, 38989, 
	38990, 39015, 39016, 39083, 39085, 39087, 39216, 39345, 
	39356, 39358, 39383, 39384, 39385, 39406, 39409, 39422, 
	39424, 39426, 39481, 39530, 39606, 39674, 39690, 39749, 
	39751, 39752, 39758, 39759, 39760, 39828, 39896, 39964, 
	40032, 40043, 40045, 40046, 40052, 40054, 40055, 40056, 
	40124, 40192, 40260, 40328, 40396, 40464, 40532, 40600, 
	40670, 40738, 40808, 40876, 40878, 40881, 40883, 40950, 
	40952, 40955, 41084, 41213, 41214, 41343, 41472, 41601, 
	41730, 41859, 41988, 41989, 41991, 41992, 41993, 42122, 
	42251, 42380, 42509, 42638, 42767, 42896, 43025, 43154, 
	43283, 43412, 43541, 43670, 43799, 43928, 44057, 44186, 
	44315, 44444, 44573, 44702, 44831, 44960, 45089, 45218, 
	45347, 45476, 45605, 45734, 45863, 45992, 46121, 46250, 
	46379, 46508, 46637, 46766, 46851, 46980, 47109, 47238, 
	47367, 47496, 47625, 47754, 47883, 48012, 48141, 48270, 
	48399, 48528, 48657, 48786, 48915, 49044, 49173, 49302, 
	49431, 49560, 49689, 49818, 49947, 50076, 50205, 50334, 
	50463, 50592, 50721, 50850, 50979, 51108, 51237, 51366, 
	51495, 51624, 51753, 51882, 52011, 52140, 52269, 52398, 
	52527, 52656, 52785, 52914, 53043, 53172, 53301, 53430, 
	53559, 53688, 53817, 53946, 54075, 54204, 54333, 54462, 
	54591, 54720, 54849, 54978, 55107, 55236, 55237, 55302, 
	55303, 55388, 55473, 55558, 55560, 55562, 55564, 55566, 
	55568, 55621, 55623, 55716, 55818, 55910, 55912, 56009, 
	56034, 56035, 56037, 56039, 56041, 56066, 56068
]

class << self
	attr_accessor :_lex_indicies
	private :_lex_indicies, :_lex_indicies=
end
self._lex_indicies = [
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 1, 1, 2, 1, 2, 1, 1, 
	2, 2, 1, 1, 1, 3, 1, 1, 
	4, 4, 4, 4, 4, 4, 4, 4, 
	4, 4, 1, 1, 1, 1, 1, 1, 
	1, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 2, 1, 2, 2, 0, 
	1, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 2, 2, 2, 1, 2, 
	0, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 2, 2, 2, 2, 2, 
	2, 2, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 2, 2, 2, 2, 
	1, 2, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 2, 2, 2, 2, 
	2, 1, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 5, 5, 5, 5, 5, 5, 
	5, 5, 5, 5, 2, 2, 2, 2, 
	2, 2, 6, 5, 5, 5, 5, 5, 
	5, 5, 5, 5, 5, 5, 5, 5, 
	5, 5, 5, 5, 5, 5, 5, 5, 
	5, 5, 5, 5, 5, 2, 2, 2, 
	2, 5, 2, 5, 5, 5, 5, 5, 
	5, 5, 5, 5, 5, 5, 5, 5, 
	5, 5, 5, 5, 5, 5, 5, 5, 
	5, 5, 5, 5, 5, 2, 2, 2, 
	2, 2, 5, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 7, 7, 7, 7, 7, 
	7, 7, 7, 7, 7, 2, 2, 2, 
	2, 2, 2, 2, 7, 7, 7, 7, 
	7, 7, 7, 7, 7, 7, 7, 7, 
	7, 7, 7, 7, 7, 7, 7, 7, 
	7, 7, 7, 7, 7, 7, 2, 2, 
	2, 2, 7, 2, 7, 7, 7, 7, 
	7, 7, 7, 7, 7, 7, 7, 7, 
	7, 7, 7, 7, 7, 7, 7, 7, 
	7, 7, 7, 7, 7, 7, 2, 2, 
	2, 2, 2, 7, 8, 8, 8, 8, 
	8, 8, 8, 8, 8, 8, 8, 8, 
	8, 8, 8, 8, 8, 8, 8, 8, 
	8, 8, 8, 8, 8, 8, 8, 8, 
	8, 8, 8, 8, 8, 8, 8, 8, 
	8, 8, 8, 8, 8, 8, 8, 8, 
	8, 8, 8, 8, 9, 9, 9, 9, 
	9, 9, 9, 9, 9, 9, 8, 8, 
	8, 8, 8, 8, 8, 9, 9, 9, 
	9, 9, 9, 9, 9, 9, 9, 9, 
	9, 9, 9, 9, 9, 9, 9, 9, 
	9, 9, 9, 9, 9, 9, 9, 8, 
	8, 8, 8, 9, 8, 9, 9, 9, 
	9, 9, 9, 9, 9, 9, 9, 9, 
	9, 9, 9, 9, 9, 9, 9, 9, 
	9, 9, 9, 9, 9, 9, 9, 8, 
	8, 8, 8, 8, 9, 11, 11, 11, 
	11, 11, 11, 11, 11, 11, 11, 11, 
	11, 11, 11, 11, 11, 11, 11, 11, 
	11, 11, 11, 11, 11, 11, 11, 11, 
	11, 11, 11, 11, 11, 11, 11, 11, 
	11, 11, 11, 11, 11, 11, 11, 11, 
	11, 11, 11, 11, 11, 10, 10, 10, 
	10, 10, 10, 10, 10, 10, 10, 11, 
	11, 11, 11, 11, 11, 11, 10, 10, 
	10, 10, 10, 10, 10, 10, 10, 10, 
	10, 10, 10, 10, 10, 10, 10, 10, 
	10, 10, 10, 10, 10, 10, 10, 10, 
	11, 11, 11, 11, 11, 11, 10, 10, 
	10, 10, 10, 10, 10, 10, 10, 10, 
	10, 10, 10, 10, 10, 10, 10, 10, 
	10, 10, 10, 10, 10, 10, 10, 10, 
	11, 11, 11, 11, 11, 10, 12, 12, 
	12, 12, 12, 12, 12, 12, 12, 12, 
	12, 12, 12, 12, 12, 12, 12, 12, 
	12, 12, 12, 12, 12, 12, 12, 12, 
	12, 12, 12, 12, 12, 12, 12, 14, 
	12, 12, 12, 12, 12, 12, 12, 12, 
	12, 12, 12, 12, 12, 12, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	15, 12, 12, 12, 12, 14, 12, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 12, 12, 12, 12, 13, 12, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 12, 12, 12, 12, 12, 13, 15, 
	12, 12, 16, 17, 12, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 20, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 21, 
	18, 18, 18, 18, 20, 18, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	18, 18, 18, 18, 19, 18, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	18, 18, 18, 18, 18, 19, 21, 18, 
	23, 22, 24, 22, 25, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 27, 
	22, 27, 27, 27, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 27, 22, 
	22, 22, 22, 28, 29, 22, 30, 22, 
	31, 32, 33, 34, 35, 28, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	36, 22, 37, 33, 38, 39, 22, 26, 
	26, 26, 26, 26, 26, 26, 26, 26, 
	26, 26, 26, 26, 26, 26, 26, 26, 
	26, 26, 26, 26, 26, 26, 26, 26, 
	26, 40, 41, 33, 42, 26, 22, 26, 
	26, 26, 26, 26, 26, 26, 26, 43, 
	26, 26, 26, 26, 26, 26, 26, 26, 
	44, 26, 26, 45, 26, 46, 26, 26, 
	26, 47, 48, 22, 42, 22, 26, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	49, 22, 49, 49, 49, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 49, 
	22, 22, 22, 22, 50, 51, 22, 52, 
	22, 53, 54, 55, 56, 57, 50, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 58, 22, 59, 55, 60, 61, 22, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 62, 63, 55, 24, 19, 22, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	64, 19, 19, 19, 19, 19, 19, 19, 
	19, 65, 19, 19, 66, 19, 67, 19, 
	19, 19, 68, 69, 22, 24, 22, 19, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 20, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 21, 22, 22, 22, 22, 20, 
	22, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 22, 22, 22, 22, 19, 
	22, 19, 19, 19, 19, 19, 70, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 22, 22, 22, 22, 22, 
	19, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 20, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 21, 22, 22, 22, 22, 
	20, 22, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 22, 22, 22, 22, 
	19, 22, 19, 19, 19, 19, 71, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 22, 22, 22, 22, 
	22, 19, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 20, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 21, 22, 22, 22, 
	22, 20, 22, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 22, 22, 22, 
	22, 19, 22, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 72, 19, 19, 
	19, 19, 19, 19, 19, 22, 22, 22, 
	22, 22, 19, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 20, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 21, 22, 22, 
	22, 22, 20, 22, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 22, 22, 
	22, 22, 19, 22, 19, 19, 73, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 22, 22, 
	22, 22, 22, 19, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 20, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 21, 22, 
	22, 22, 22, 20, 22, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 22, 
	22, 22, 22, 19, 22, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 74, 19, 19, 19, 19, 19, 22, 
	22, 22, 22, 22, 19, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 20, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 21, 
	22, 22, 22, 22, 20, 22, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	22, 22, 22, 22, 19, 22, 19, 19, 
	19, 19, 70, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	22, 22, 22, 22, 22, 19, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 20, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	21, 22, 22, 22, 22, 20, 22, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 22, 22, 22, 22, 19, 22, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 75, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 22, 22, 22, 22, 22, 19, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	20, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 21, 22, 22, 22, 22, 20, 22, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 22, 22, 22, 22, 19, 22, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 76, 19, 19, 19, 19, 
	19, 19, 19, 77, 19, 19, 19, 19, 
	19, 19, 22, 22, 22, 22, 22, 19, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 20, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 21, 22, 22, 22, 22, 20, 
	22, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 22, 22, 22, 22, 19, 
	22, 19, 19, 19, 19, 78, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 22, 22, 22, 22, 22, 
	19, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 20, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 21, 22, 22, 22, 22, 
	20, 22, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 22, 22, 22, 22, 
	19, 22, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 79, 19, 19, 19, 
	19, 19, 19, 19, 22, 22, 22, 22, 
	22, 19, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 20, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 21, 22, 22, 22, 
	22, 20, 22, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 22, 22, 22, 
	22, 19, 22, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 70, 19, 19, 
	19, 19, 19, 19, 19, 22, 22, 22, 
	22, 22, 19, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 20, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 21, 22, 22, 
	22, 22, 20, 22, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 22, 22, 
	22, 22, 19, 22, 19, 19, 19, 19, 
	19, 19, 19, 19, 80, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 22, 22, 
	22, 22, 22, 19, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 20, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 21, 22, 
	22, 22, 22, 20, 22, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 22, 
	22, 22, 22, 19, 22, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	70, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 22, 
	22, 22, 22, 22, 19, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 20, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 21, 
	22, 22, 22, 22, 20, 22, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	22, 22, 22, 22, 19, 22, 19, 19, 
	19, 19, 19, 19, 19, 81, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	22, 22, 22, 22, 22, 19, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 20, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	21, 22, 22, 22, 22, 20, 22, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 22, 22, 22, 22, 19, 22, 19, 
	19, 19, 19, 19, 19, 19, 19, 82, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 22, 22, 22, 22, 22, 19, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 22, 
	20, 22, 22, 22, 22, 22, 22, 22, 
	22, 22, 22, 22, 22, 22, 22, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 21, 22, 22, 22, 22, 20, 22, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 22, 22, 22, 22, 19, 22, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 74, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 22, 22, 22, 22, 22, 19, 
	84, 83, 85, 83, 86, 83, 55, 83, 
	87, 83, 83, 83, 83, 83, 83, 83, 
	88, 83, 89, 83, 90, 83, 55, 83, 
	91, 83, 55, 83, 92, 83, 86, 83, 
	94, 93, 95, 95, 95, 95, 95, 95, 
	95, 95, 95, 97, 95, 97, 97, 97, 
	95, 95, 95, 95, 95, 95, 95, 95, 
	95, 95, 95, 95, 95, 95, 95, 95, 
	95, 95, 97, 95, 95, 95, 95, 95, 
	95, 95, 98, 95, 95, 95, 95, 95, 
	95, 95, 95, 95, 95, 95, 95, 95, 
	95, 95, 95, 95, 95, 95, 95, 95, 
	95, 95, 95, 96, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 95, 99, 95, 
	95, 96, 95, 96, 96, 96, 100, 96, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 95, 95, 95, 
	95, 95, 96, 101, 95, 95, 95, 95, 
	95, 95, 95, 95, 95, 95, 103, 95, 
	103, 103, 103, 95, 95, 95, 95, 95, 
	95, 95, 95, 95, 95, 95, 95, 95, 
	95, 95, 95, 95, 95, 103, 95, 95, 
	95, 95, 95, 95, 95, 104, 95, 95, 
	95, 95, 95, 95, 95, 95, 95, 95, 
	95, 95, 95, 95, 95, 95, 95, 95, 
	95, 95, 95, 95, 95, 95, 102, 102, 
	102, 102, 102, 102, 102, 102, 102, 102, 
	102, 102, 102, 102, 102, 102, 102, 102, 
	102, 102, 102, 102, 102, 102, 102, 102, 
	95, 105, 95, 95, 102, 95, 102, 102, 
	102, 106, 102, 102, 102, 102, 102, 102, 
	102, 102, 102, 102, 102, 102, 102, 102, 
	102, 102, 102, 102, 102, 102, 102, 102, 
	95, 95, 95, 95, 95, 102, 108, 107, 
	108, 108, 108, 107, 107, 107, 107, 107, 
	107, 107, 107, 107, 107, 107, 107, 107, 
	107, 107, 107, 107, 107, 108, 107, 107, 
	107, 107, 107, 107, 107, 109, 107, 107, 
	107, 107, 107, 107, 107, 107, 107, 107, 
	107, 107, 107, 107, 107, 107, 107, 107, 
	107, 107, 107, 107, 107, 107, 107, 107, 
	107, 107, 107, 107, 107, 107, 107, 107, 
	107, 107, 107, 107, 107, 107, 107, 107, 
	107, 107, 107, 107, 107, 107, 107, 107, 
	107, 110, 107, 111, 107, 112, 107, 112, 
	112, 112, 107, 107, 107, 107, 107, 107, 
	107, 107, 107, 107, 107, 107, 107, 107, 
	107, 107, 107, 107, 112, 107, 107, 107, 
	107, 107, 107, 107, 113, 107, 107, 107, 
	107, 107, 107, 107, 107, 107, 107, 107, 
	107, 107, 107, 107, 107, 107, 107, 107, 
	107, 107, 107, 107, 107, 107, 107, 107, 
	107, 107, 107, 107, 107, 107, 107, 107, 
	107, 107, 107, 107, 107, 107, 107, 107, 
	107, 107, 107, 107, 107, 107, 107, 107, 
	114, 107, 115, 116, 118, 117, 119, 117, 
	120, 117, 121, 117, 122, 117, 124, 124, 
	124, 124, 124, 124, 124, 124, 124, 124, 
	124, 124, 124, 124, 124, 124, 124, 124, 
	124, 124, 124, 124, 124, 124, 124, 124, 
	124, 124, 124, 124, 124, 124, 124, 124, 
	124, 124, 124, 124, 124, 124, 124, 124, 
	124, 124, 124, 124, 124, 124, 123, 123, 
	123, 123, 123, 123, 123, 123, 123, 123, 
	124, 124, 124, 124, 124, 124, 124, 123, 
	123, 123, 123, 123, 123, 123, 123, 123, 
	123, 123, 123, 123, 123, 123, 123, 123, 
	123, 123, 123, 123, 123, 123, 123, 123, 
	123, 124, 124, 124, 124, 124, 124, 123, 
	123, 123, 123, 123, 123, 123, 123, 123, 
	123, 123, 123, 123, 123, 123, 123, 123, 
	123, 123, 123, 123, 123, 123, 123, 123, 
	123, 124, 124, 124, 124, 124, 123, 125, 
	115, 126, 127, 126, 126, 126, 115, 115, 
	115, 115, 115, 115, 115, 115, 115, 115, 
	115, 115, 115, 115, 115, 115, 115, 115, 
	126, 115, 115, 128, 115, 115, 115, 115, 
	115, 115, 115, 115, 115, 115, 115, 115, 
	129, 129, 129, 129, 129, 129, 129, 129, 
	129, 129, 115, 115, 115, 115, 115, 115, 
	115, 115, 115, 115, 115, 115, 115, 115, 
	115, 115, 115, 115, 115, 115, 115, 115, 
	115, 115, 115, 115, 115, 115, 115, 115, 
	115, 115, 115, 115, 130, 115, 131, 132, 
	131, 131, 131, 115, 115, 115, 115, 115, 
	115, 115, 115, 115, 115, 115, 115, 115, 
	115, 115, 115, 115, 115, 131, 115, 115, 
	133, 115, 115, 115, 115, 115, 115, 115, 
	115, 115, 115, 115, 115, 134, 134, 134, 
	134, 134, 134, 134, 134, 134, 134, 115, 
	115, 115, 115, 115, 115, 115, 115, 115, 
	115, 115, 115, 115, 115, 115, 115, 115, 
	115, 115, 115, 115, 115, 115, 115, 115, 
	115, 115, 115, 115, 115, 115, 115, 115, 
	115, 135, 115, 137, 138, 137, 137, 137, 
	136, 136, 136, 136, 136, 136, 136, 136, 
	136, 136, 136, 136, 136, 136, 136, 136, 
	136, 136, 137, 136, 136, 139, 136, 136, 
	136, 136, 136, 136, 136, 136, 136, 136, 
	136, 136, 140, 140, 140, 140, 140, 140, 
	140, 140, 140, 140, 136, 136, 136, 136, 
	136, 136, 136, 136, 136, 136, 136, 136, 
	136, 136, 136, 136, 136, 136, 136, 136, 
	136, 136, 136, 136, 136, 136, 136, 136, 
	136, 136, 136, 136, 136, 136, 141, 136, 
	143, 144, 143, 143, 143, 142, 142, 142, 
	142, 142, 142, 142, 142, 142, 142, 142, 
	142, 142, 142, 142, 142, 142, 142, 143, 
	142, 142, 145, 142, 142, 142, 142, 142, 
	142, 142, 142, 142, 142, 142, 142, 146, 
	146, 146, 146, 146, 146, 146, 146, 146, 
	146, 142, 142, 142, 142, 142, 142, 142, 
	142, 142, 142, 142, 142, 142, 142, 142, 
	142, 142, 142, 142, 142, 142, 142, 142, 
	142, 142, 142, 142, 142, 142, 142, 142, 
	142, 142, 142, 147, 142, 143, 148, 143, 
	143, 143, 142, 142, 142, 142, 142, 142, 
	142, 142, 142, 142, 142, 142, 142, 142, 
	142, 142, 142, 142, 143, 142, 142, 145, 
	142, 142, 142, 142, 142, 142, 142, 142, 
	142, 142, 142, 142, 146, 146, 146, 146, 
	146, 146, 146, 146, 146, 146, 142, 142, 
	142, 142, 142, 142, 142, 142, 142, 142, 
	142, 142, 142, 142, 142, 142, 142, 142, 
	142, 142, 142, 142, 142, 142, 142, 142, 
	142, 142, 142, 142, 142, 142, 142, 142, 
	147, 142, 127, 115, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 151, 151, 149, 
	151, 149, 151, 151, 149, 149, 151, 151, 
	151, 152, 151, 151, 153, 153, 153, 153, 
	153, 153, 153, 153, 153, 153, 151, 151, 
	151, 151, 151, 151, 151, 150, 150, 150, 
	150, 150, 150, 150, 150, 150, 150, 150, 
	150, 150, 150, 150, 150, 150, 150, 150, 
	150, 150, 150, 150, 150, 150, 150, 149, 
	151, 149, 149, 150, 151, 150, 150, 150, 
	150, 150, 150, 150, 150, 150, 150, 150, 
	150, 150, 150, 150, 150, 150, 150, 150, 
	150, 150, 150, 150, 150, 150, 150, 149, 
	149, 149, 151, 149, 150, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 151, 151, 151, 
	151, 151, 151, 151, 151, 151, 151, 149, 
	149, 149, 149, 149, 149, 149, 151, 151, 
	151, 151, 151, 151, 151, 151, 151, 151, 
	151, 151, 151, 151, 151, 151, 151, 151, 
	151, 151, 151, 151, 151, 151, 151, 151, 
	149, 149, 149, 149, 151, 149, 151, 151, 
	151, 151, 151, 151, 151, 151, 151, 151, 
	151, 151, 151, 151, 151, 151, 151, 151, 
	151, 151, 151, 151, 151, 151, 151, 151, 
	149, 149, 149, 149, 149, 151, 154, 151, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 151, 
	149, 154, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 156, 149, 149, 
	149, 149, 157, 149, 149, 149, 149, 149, 
	158, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	125, 149, 149, 149, 155, 155, 155, 155, 
	155, 155, 155, 155, 155, 155, 155, 155, 
	155, 155, 155, 155, 155, 155, 155, 155, 
	155, 155, 155, 155, 155, 155, 149, 149, 
	149, 149, 155, 159, 155, 155, 155, 155, 
	155, 155, 155, 155, 155, 155, 155, 155, 
	155, 155, 155, 155, 155, 155, 155, 155, 
	155, 155, 155, 155, 155, 155, 149, 149, 
	149, 158, 149, 155, 161, 161, 161, 161, 
	161, 161, 161, 161, 161, 161, 162, 161, 
	161, 161, 161, 161, 161, 161, 161, 161, 
	161, 161, 161, 161, 161, 161, 161, 161, 
	161, 161, 161, 161, 161, 161, 161, 161, 
	161, 161, 161, 161, 161, 161, 161, 161, 
	161, 161, 161, 161, 160, 160, 160, 160, 
	160, 160, 160, 160, 160, 160, 161, 161, 
	161, 161, 161, 161, 161, 160, 160, 160, 
	160, 160, 160, 160, 160, 160, 160, 160, 
	160, 160, 160, 160, 160, 160, 160, 160, 
	160, 160, 160, 160, 160, 160, 160, 161, 
	161, 161, 161, 160, 161, 160, 160, 160, 
	160, 160, 160, 160, 160, 160, 160, 160, 
	160, 160, 160, 160, 160, 160, 160, 160, 
	160, 160, 160, 160, 160, 160, 160, 161, 
	161, 161, 161, 161, 160, 164, 163, 167, 
	166, 162, 161, 167, 168, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 156, 
	149, 149, 149, 149, 157, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 155, 155, 
	155, 155, 155, 155, 155, 155, 155, 155, 
	155, 155, 155, 155, 155, 155, 155, 155, 
	155, 155, 155, 155, 155, 155, 155, 155, 
	149, 149, 149, 149, 155, 159, 155, 155, 
	155, 155, 155, 155, 155, 155, 155, 155, 
	155, 155, 155, 155, 155, 155, 155, 155, 
	155, 155, 155, 155, 155, 155, 155, 155, 
	149, 149, 149, 149, 149, 155, 170, 169, 
	169, 169, 169, 169, 169, 169, 169, 169, 
	169, 169, 169, 169, 169, 169, 169, 169, 
	169, 169, 169, 169, 169, 169, 169, 169, 
	169, 169, 169, 169, 169, 169, 169, 169, 
	169, 169, 169, 169, 169, 169, 169, 169, 
	169, 169, 169, 169, 169, 169, 169, 169, 
	169, 169, 169, 169, 169, 169, 169, 169, 
	169, 169, 169, 169, 169, 169, 169, 169, 
	169, 169, 169, 169, 169, 169, 169, 169, 
	169, 169, 169, 169, 169, 169, 169, 169, 
	169, 169, 169, 169, 167, 169, 167, 170, 
	115, 115, 115, 115, 115, 115, 115, 115, 
	115, 115, 115, 115, 115, 115, 115, 115, 
	115, 115, 115, 115, 115, 115, 115, 115, 
	115, 115, 115, 115, 115, 115, 115, 115, 
	115, 172, 115, 115, 115, 115, 115, 115, 
	115, 115, 115, 115, 115, 115, 115, 115, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 173, 115, 115, 174, 115, 172, 
	115, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 115, 115, 115, 115, 171, 
	115, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 115, 115, 115, 115, 115, 
	171, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 172, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 173, 149, 149, 174, 149, 
	172, 149, 171, 171, 171, 171, 171, 171, 
	175, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 149, 149, 149, 149, 
	171, 149, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 149, 149, 149, 149, 
	149, 171, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 172, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 173, 149, 149, 174, 
	149, 172, 149, 171, 171, 171, 171, 171, 
	171, 171, 171, 176, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 149, 149, 149, 
	149, 171, 149, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 149, 149, 149, 
	149, 149, 171, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 172, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 173, 149, 149, 
	174, 149, 172, 149, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 177, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 149, 149, 
	149, 149, 171, 149, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 149, 149, 
	149, 149, 149, 171, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 149, 172, 149, 149, 
	149, 149, 149, 149, 149, 149, 149, 149, 
	149, 149, 149, 149, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 173, 149, 
	149, 174, 149, 172, 149, 171, 171, 171, 
	177, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 149, 
	149, 149, 149, 171, 149, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 149, 
	149, 149, 149, 149, 171, 178, 178, 178, 
	178, 178, 178, 178, 178, 178, 178, 178, 
	178, 178, 178, 178, 178, 178, 178, 178, 
	178, 178, 178, 178, 178, 178, 178, 178, 
	178, 178, 178, 178, 178, 178, 180, 178, 
	178, 178, 178, 178, 178, 178, 178, 178, 
	178, 178, 178, 178, 178, 179, 179, 179, 
	179, 179, 179, 179, 179, 179, 179, 181, 
	178, 178, 178, 178, 180, 178, 179, 179, 
	179, 179, 179, 179, 179, 179, 179, 179, 
	179, 179, 179, 179, 179, 179, 179, 179, 
	179, 179, 179, 179, 179, 179, 179, 179, 
	178, 178, 178, 178, 179, 178, 179, 179, 
	179, 179, 179, 179, 179, 179, 179, 179, 
	179, 179, 179, 179, 179, 179, 179, 179, 
	179, 179, 179, 179, 179, 179, 179, 179, 
	178, 178, 178, 178, 178, 179, 181, 178, 
	178, 182, 183, 183, 183, 183, 183, 183, 
	183, 183, 183, 183, 183, 183, 183, 183, 
	183, 183, 183, 183, 183, 183, 183, 183, 
	183, 183, 183, 183, 183, 183, 183, 183, 
	183, 183, 183, 183, 183, 183, 183, 183, 
	183, 183, 183, 183, 183, 183, 183, 183, 
	183, 183, 184, 184, 184, 184, 184, 184, 
	184, 184, 184, 184, 183, 183, 183, 183, 
	183, 183, 183, 184, 184, 184, 184, 184, 
	184, 184, 184, 184, 184, 184, 184, 184, 
	184, 184, 184, 184, 184, 184, 184, 184, 
	184, 184, 184, 184, 184, 183, 183, 183, 
	183, 184, 183, 184, 184, 184, 184, 184, 
	184, 184, 184, 184, 184, 184, 184, 184, 
	184, 184, 184, 184, 184, 184, 184, 184, 
	184, 184, 184, 184, 184, 183, 183, 183, 
	183, 183, 184, 186, 185, 186, 185, 185, 
	187, 187, 187, 187, 187, 187, 187, 187, 
	187, 187, 185, 187, 187, 187, 187, 187, 
	187, 187, 187, 187, 187, 185, 188, 188, 
	188, 188, 188, 188, 188, 188, 188, 188, 
	185, 190, 190, 190, 190, 190, 190, 190, 
	190, 190, 190, 189, 191, 191, 191, 191, 
	191, 191, 191, 191, 191, 191, 189, 193, 
	192, 194, 192, 195, 192, 196, 192, 198, 
	197, 199, 197, 200, 197, 183, 183, 183, 
	183, 183, 183, 183, 183, 183, 183, 183, 
	183, 183, 183, 183, 183, 183, 183, 183, 
	183, 183, 183, 183, 183, 183, 183, 183, 
	183, 183, 183, 183, 183, 183, 183, 183, 
	183, 183, 183, 183, 183, 183, 183, 183, 
	183, 183, 183, 183, 183, 201, 201, 201, 
	201, 201, 201, 201, 201, 201, 201, 183, 
	183, 183, 183, 183, 183, 183, 201, 201, 
	201, 201, 201, 201, 201, 201, 201, 201, 
	201, 201, 201, 201, 201, 201, 201, 201, 
	201, 201, 201, 201, 201, 201, 201, 201, 
	183, 183, 183, 183, 201, 183, 201, 201, 
	201, 201, 201, 201, 201, 201, 201, 201, 
	201, 201, 201, 201, 201, 201, 201, 201, 
	201, 201, 201, 201, 201, 201, 201, 201, 
	183, 183, 183, 183, 183, 201, 202, 189, 
	203, 204, 203, 203, 203, 189, 189, 189, 
	189, 189, 189, 189, 189, 189, 189, 189, 
	189, 189, 189, 189, 189, 189, 189, 203, 
	189, 189, 205, 189, 189, 189, 189, 189, 
	189, 189, 189, 189, 189, 189, 189, 189, 
	189, 189, 189, 189, 189, 189, 189, 189, 
	189, 189, 189, 206, 189, 189, 189, 189, 
	189, 189, 189, 189, 189, 189, 189, 189, 
	189, 189, 189, 189, 189, 189, 189, 189, 
	189, 189, 189, 189, 189, 189, 189, 189, 
	189, 189, 189, 207, 189, 208, 209, 208, 
	208, 208, 189, 189, 189, 189, 189, 189, 
	189, 189, 189, 189, 189, 189, 189, 189, 
	189, 189, 189, 189, 208, 189, 189, 210, 
	189, 189, 189, 189, 189, 189, 189, 189, 
	189, 189, 189, 189, 189, 189, 189, 189, 
	189, 189, 189, 189, 189, 189, 189, 189, 
	211, 189, 189, 189, 189, 189, 189, 189, 
	189, 189, 189, 189, 189, 189, 189, 189, 
	189, 189, 189, 189, 189, 189, 189, 189, 
	189, 189, 189, 189, 189, 189, 189, 189, 
	212, 189, 214, 215, 214, 214, 214, 213, 
	213, 213, 213, 213, 213, 213, 213, 213, 
	213, 213, 213, 213, 213, 213, 213, 213, 
	213, 214, 213, 213, 216, 213, 213, 213, 
	213, 213, 213, 213, 213, 213, 213, 213, 
	213, 213, 213, 213, 213, 213, 213, 213, 
	213, 213, 213, 213, 213, 217, 213, 213, 
	213, 213, 213, 213, 213, 213, 213, 213, 
	213, 213, 213, 213, 213, 213, 213, 213, 
	213, 213, 213, 213, 213, 213, 213, 213, 
	213, 213, 213, 213, 213, 218, 213, 220, 
	221, 220, 220, 220, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 220, 219, 
	219, 222, 219, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	219, 219, 223, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	219, 219, 224, 219, 220, 221, 220, 220, 
	220, 219, 219, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	219, 219, 219, 220, 219, 219, 222, 219, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 225, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 224, 
	219, 220, 226, 220, 220, 220, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	220, 219, 219, 222, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 223, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 224, 219, 227, 189, 
	204, 189, 229, 230, 229, 229, 229, 228, 
	228, 228, 228, 228, 228, 228, 228, 228, 
	228, 228, 228, 228, 228, 228, 228, 228, 
	228, 229, 228, 228, 231, 228, 228, 232, 
	228, 228, 228, 228, 228, 228, 228, 233, 
	228, 228, 228, 228, 228, 228, 228, 228, 
	228, 228, 228, 228, 228, 228, 228, 228, 
	228, 228, 228, 228, 228, 228, 228, 228, 
	228, 228, 228, 228, 228, 228, 228, 228, 
	228, 228, 228, 228, 228, 228, 228, 228, 
	228, 228, 228, 228, 228, 234, 228, 236, 
	230, 236, 236, 236, 235, 235, 235, 235, 
	235, 235, 235, 235, 235, 235, 235, 235, 
	235, 235, 235, 235, 235, 235, 236, 235, 
	235, 231, 235, 235, 235, 235, 235, 235, 
	235, 235, 235, 235, 235, 235, 235, 235, 
	235, 235, 235, 235, 235, 235, 235, 235, 
	235, 235, 235, 235, 235, 235, 235, 235, 
	235, 235, 235, 235, 235, 235, 235, 235, 
	235, 235, 235, 235, 235, 235, 235, 235, 
	235, 235, 235, 235, 235, 235, 235, 235, 
	235, 235, 234, 235, 239, 238, 241, 240, 
	242, 237, 243, 237, 244, 228, 246, 245, 
	247, 245, 248, 245, 249, 245, 250, 245, 
	251, 245, 252, 245, 253, 245, 254, 245, 
	255, 245, 245, 245, 255, 245, 245, 245, 
	245, 245, 256, 245, 245, 245, 245, 245, 
	245, 245, 245, 245, 245, 245, 245, 245, 
	245, 245, 255, 245, 257, 258, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 259, 2, 260, 260, 260, 260, 260, 
	260, 260, 260, 260, 260, 260, 260, 260, 
	260, 260, 260, 260, 260, 260, 260, 260, 
	260, 260, 260, 260, 260, 260, 260, 260, 
	260, 260, 260, 260, 260, 260, 260, 260, 
	260, 260, 260, 260, 260, 260, 260, 260, 
	260, 260, 260, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 260, 260, 260, 
	260, 260, 260, 260, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 260, 260, 
	260, 260, 0, 260, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 260, 260, 
	260, 260, 260, 0, 4, 4, 4, 4, 
	4, 4, 4, 4, 4, 4, 260, 261, 
	261, 261, 261, 261, 261, 261, 261, 261, 
	261, 261, 261, 261, 261, 261, 261, 261, 
	261, 261, 261, 261, 261, 261, 261, 261, 
	261, 261, 261, 261, 261, 261, 261, 261, 
	261, 261, 261, 261, 261, 261, 261, 261, 
	261, 261, 261, 261, 261, 261, 261, 5, 
	5, 5, 5, 5, 5, 5, 5, 5, 
	5, 261, 261, 261, 261, 261, 261, 261, 
	5, 5, 5, 5, 5, 5, 5, 5, 
	5, 5, 5, 5, 5, 5, 5, 5, 
	5, 5, 5, 5, 5, 5, 5, 5, 
	5, 5, 261, 261, 261, 261, 5, 261, 
	5, 5, 5, 5, 5, 5, 5, 5, 
	5, 5, 5, 5, 5, 5, 5, 5, 
	5, 5, 5, 5, 5, 5, 5, 5, 
	5, 5, 261, 261, 261, 261, 261, 5, 
	262, 262, 262, 262, 262, 262, 262, 262, 
	262, 262, 262, 262, 262, 262, 262, 262, 
	262, 262, 262, 262, 262, 262, 262, 262, 
	262, 262, 262, 262, 262, 262, 262, 262, 
	262, 262, 262, 262, 262, 262, 262, 262, 
	262, 262, 262, 262, 262, 262, 262, 262, 
	7, 7, 7, 7, 7, 7, 7, 7, 
	7, 7, 262, 262, 262, 262, 262, 262, 
	262, 7, 7, 7, 7, 7, 7, 7, 
	7, 7, 7, 7, 7, 7, 7, 7, 
	7, 7, 7, 7, 7, 7, 7, 7, 
	7, 7, 7, 262, 262, 262, 262, 7, 
	262, 7, 7, 7, 7, 7, 7, 7, 
	7, 7, 7, 7, 7, 7, 7, 7, 
	7, 7, 7, 7, 7, 7, 7, 7, 
	7, 7, 7, 262, 262, 262, 262, 262, 
	7, 264, 265, 265, 265, 264, 265, 265, 
	265, 265, 266, 267, 266, 266, 266, 265, 
	265, 265, 265, 265, 265, 265, 265, 265, 
	265, 265, 265, 264, 265, 265, 265, 265, 
	265, 266, 268, 265, 269, 270, 271, 272, 
	265, 265, 265, 273, 274, 265, 274, 265, 
	275, 265, 265, 265, 265, 265, 265, 265, 
	265, 265, 265, 276, 265, 277, 278, 279, 
	265, 265, 280, 281, 280, 280, 282, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 283, 284, 265, 275, 
	285, 275, 286, 287, 288, 289, 290, 291, 
	263, 263, 292, 263, 263, 263, 293, 294, 
	295, 263, 263, 296, 297, 298, 299, 263, 
	300, 263, 301, 263, 265, 302, 265, 274, 
	265, 263, 303, 303, 303, 303, 303, 303, 
	303, 303, 303, 303, 303, 303, 303, 303, 
	303, 303, 303, 303, 303, 303, 303, 303, 
	303, 303, 303, 303, 303, 303, 303, 303, 
	303, 303, 303, 304, 303, 303, 303, 303, 
	303, 303, 303, 303, 303, 303, 303, 303, 
	303, 303, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 303, 303, 303, 304, 
	303, 304, 303, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 303, 303, 303, 
	303, 263, 303, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 303, 303, 303, 
	303, 303, 263, 266, 305, 266, 266, 266, 
	305, 305, 305, 305, 305, 305, 305, 305, 
	305, 305, 305, 305, 305, 305, 305, 305, 
	305, 305, 266, 305, 306, 275, 307, 307, 
	275, 307, 307, 307, 307, 307, 307, 307, 
	307, 307, 307, 307, 307, 307, 307, 307, 
	307, 307, 307, 307, 307, 307, 307, 307, 
	307, 307, 307, 307, 307, 307, 307, 307, 
	307, 307, 307, 307, 307, 307, 307, 307, 
	307, 307, 307, 307, 307, 307, 307, 307, 
	307, 307, 307, 307, 307, 307, 307, 307, 
	307, 307, 307, 307, 307, 307, 275, 307, 
	308, 309, 310, 311, 312, 312, 312, 312, 
	312, 312, 312, 312, 312, 312, 312, 312, 
	312, 312, 312, 312, 312, 312, 312, 312, 
	312, 312, 312, 312, 312, 312, 312, 312, 
	312, 312, 312, 312, 312, 9, 9, 312, 
	9, 312, 9, 9, 312, 312, 9, 9, 
	9, 314, 9, 9, 315, 315, 315, 315, 
	315, 315, 315, 315, 315, 315, 9, 9, 
	9, 9, 9, 9, 9, 313, 313, 313, 
	313, 313, 313, 313, 313, 313, 313, 313, 
	313, 313, 313, 313, 313, 313, 313, 313, 
	313, 313, 313, 313, 313, 313, 313, 312, 
	9, 312, 312, 313, 9, 313, 313, 313, 
	313, 313, 313, 313, 313, 313, 313, 313, 
	313, 313, 313, 313, 313, 313, 313, 313, 
	313, 313, 313, 313, 313, 313, 313, 312, 
	312, 312, 9, 312, 313, 316, 316, 316, 
	316, 316, 316, 316, 316, 316, 316, 316, 
	316, 316, 316, 316, 316, 316, 316, 316, 
	316, 316, 316, 316, 316, 316, 316, 316, 
	316, 316, 316, 316, 316, 316, 316, 316, 
	316, 316, 316, 316, 316, 316, 316, 316, 
	316, 316, 316, 316, 316, 313, 313, 313, 
	313, 313, 313, 313, 313, 313, 313, 316, 
	316, 316, 316, 316, 316, 316, 313, 313, 
	313, 313, 313, 313, 313, 313, 313, 313, 
	313, 313, 313, 313, 313, 313, 313, 313, 
	313, 313, 313, 313, 313, 313, 313, 313, 
	316, 316, 316, 316, 313, 316, 313, 313, 
	313, 313, 313, 313, 313, 313, 313, 313, 
	313, 313, 313, 313, 313, 313, 313, 313, 
	313, 313, 313, 313, 313, 313, 313, 313, 
	316, 316, 316, 316, 316, 313, 315, 315, 
	315, 315, 315, 315, 315, 315, 315, 315, 
	316, 317, 307, 275, 307, 275, 307, 275, 
	307, 319, 318, 275, 320, 307, 275, 307, 
	321, 275, 312, 312, 312, 312, 312, 312, 
	312, 312, 312, 312, 312, 312, 312, 312, 
	312, 312, 312, 312, 312, 312, 312, 312, 
	312, 312, 312, 312, 312, 312, 312, 312, 
	312, 312, 312, 312, 312, 312, 312, 312, 
	312, 312, 312, 312, 312, 312, 312, 312, 
	312, 312, 312, 312, 312, 312, 312, 312, 
	312, 312, 312, 312, 312, 312, 312, 312, 
	312, 275, 312, 275, 307, 275, 275, 307, 
	303, 303, 303, 303, 303, 303, 303, 303, 
	303, 303, 303, 303, 303, 303, 303, 303, 
	303, 303, 303, 303, 303, 303, 303, 303, 
	303, 303, 303, 303, 303, 303, 303, 303, 
	303, 304, 303, 303, 303, 303, 303, 303, 
	303, 303, 303, 303, 303, 303, 303, 303, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 303, 303, 303, 304, 303, 304, 
	303, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 303, 303, 303, 303, 280, 
	303, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 303, 303, 303, 303, 303, 
	280, 322, 322, 322, 322, 322, 322, 322, 
	322, 322, 322, 322, 322, 322, 322, 322, 
	322, 322, 322, 322, 322, 322, 322, 322, 
	322, 322, 322, 322, 322, 322, 322, 322, 
	322, 322, 304, 322, 322, 322, 322, 322, 
	322, 322, 322, 322, 322, 322, 322, 322, 
	322, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 322, 322, 322, 304, 322, 
	304, 322, 280, 280, 280, 280, 323, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 322, 322, 322, 322, 
	280, 322, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 322, 322, 322, 322, 
	322, 280, 322, 322, 322, 322, 322, 322, 
	322, 322, 322, 322, 322, 322, 322, 322, 
	322, 322, 322, 322, 322, 322, 322, 322, 
	322, 322, 322, 322, 322, 322, 322, 322, 
	322, 322, 322, 304, 322, 322, 322, 322, 
	322, 322, 322, 322, 322, 322, 322, 322, 
	322, 322, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 322, 322, 322, 304, 
	322, 304, 322, 280, 280, 280, 280, 280, 
	280, 324, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 322, 322, 322, 
	322, 280, 322, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 322, 322, 322, 
	322, 322, 280, 322, 322, 322, 322, 322, 
	322, 322, 322, 322, 322, 322, 322, 322, 
	322, 322, 322, 322, 322, 322, 322, 322, 
	322, 322, 322, 322, 322, 322, 322, 322, 
	322, 322, 322, 322, 304, 322, 322, 322, 
	322, 322, 322, 322, 322, 322, 322, 322, 
	322, 322, 322, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 322, 322, 322, 
	304, 322, 304, 322, 280, 280, 280, 280, 
	280, 280, 280, 280, 325, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 322, 322, 
	322, 322, 280, 322, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 322, 322, 
	322, 322, 322, 280, 322, 322, 322, 322, 
	322, 322, 322, 322, 322, 322, 322, 322, 
	322, 322, 322, 322, 322, 322, 322, 322, 
	322, 322, 322, 322, 322, 322, 322, 322, 
	322, 322, 322, 322, 322, 304, 322, 322, 
	322, 322, 322, 322, 322, 322, 322, 322, 
	322, 322, 322, 322, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 322, 322, 
	322, 304, 322, 304, 322, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 326, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 322, 
	322, 322, 322, 280, 322, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 322, 
	322, 322, 322, 322, 280, 322, 322, 322, 
	322, 322, 322, 322, 322, 322, 322, 322, 
	322, 322, 322, 322, 322, 322, 322, 322, 
	322, 322, 322, 322, 322, 322, 322, 322, 
	322, 322, 322, 322, 322, 322, 304, 322, 
	322, 322, 322, 322, 322, 322, 322, 322, 
	322, 322, 322, 322, 322, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 322, 
	322, 322, 304, 322, 304, 322, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 327, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	322, 322, 322, 322, 280, 322, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	322, 322, 322, 322, 322, 280, 322, 322, 
	322, 322, 322, 322, 322, 322, 322, 322, 
	322, 322, 322, 322, 322, 322, 322, 322, 
	322, 322, 322, 322, 322, 322, 322, 322, 
	322, 322, 322, 322, 322, 322, 322, 304, 
	322, 322, 322, 322, 322, 322, 322, 322, 
	322, 322, 322, 322, 322, 322, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	322, 322, 322, 304, 322, 304, 322, 280, 
	280, 280, 326, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 322, 322, 322, 322, 280, 322, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 280, 280, 280, 280, 280, 280, 280, 
	280, 322, 322, 322, 322, 322, 280, 321, 
	312, 267, 312, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 304, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	304, 328, 304, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 328, 329, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 328, 328, 263, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 304, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 304, 328, 304, 328, 263, 263, 263, 
	263, 330, 331, 263, 263, 263, 263, 263, 
	332, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 328, 263, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 328, 328, 263, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 304, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 304, 328, 304, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 333, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 328, 263, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 328, 328, 263, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 304, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 304, 328, 304, 328, 263, 
	263, 334, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 328, 263, 328, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 328, 328, 263, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	304, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 304, 328, 304, 328, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 335, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 328, 263, 328, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 328, 328, 263, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 304, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 304, 328, 304, 
	328, 263, 263, 263, 336, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 328, 263, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 328, 328, 
	263, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 304, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 304, 328, 
	304, 328, 263, 263, 263, 263, 263, 263, 
	263, 263, 337, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 328, 
	263, 328, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 328, 
	328, 263, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 304, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 304, 
	328, 304, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	338, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	328, 263, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	328, 328, 263, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 304, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	304, 328, 304, 328, 263, 263, 263, 263, 
	263, 263, 339, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 328, 263, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 328, 328, 263, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 304, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 304, 328, 304, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 328, 340, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 328, 328, 263, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 304, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 304, 328, 304, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 328, 341, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 328, 328, 263, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 304, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 304, 328, 304, 328, 263, 
	263, 263, 263, 263, 263, 263, 263, 342, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 328, 263, 328, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 328, 328, 263, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	304, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 304, 328, 304, 328, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 343, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 328, 263, 328, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 328, 328, 263, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 304, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 304, 328, 304, 
	328, 263, 263, 263, 263, 339, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 328, 263, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 328, 328, 
	263, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 304, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 304, 328, 
	304, 328, 263, 263, 263, 263, 263, 263, 
	263, 263, 344, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 328, 
	263, 328, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 328, 
	328, 263, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 304, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 304, 
	328, 304, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	343, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	328, 263, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	328, 328, 263, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 304, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	304, 328, 304, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 328, 263, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 345, 
	263, 346, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 328, 328, 263, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 304, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 304, 328, 304, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 328, 263, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 347, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 328, 328, 263, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 304, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 304, 328, 304, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 328, 263, 328, 348, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 328, 328, 263, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 304, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 304, 328, 304, 328, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 328, 263, 328, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 341, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 328, 328, 263, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	304, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 304, 328, 304, 328, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 328, 263, 328, 
	263, 263, 263, 341, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 328, 328, 263, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 304, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 304, 328, 304, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 328, 263, 
	328, 263, 263, 263, 263, 349, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 350, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 328, 328, 
	263, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 304, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 304, 328, 
	304, 328, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 328, 
	263, 328, 263, 263, 263, 263, 263, 263, 
	351, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 328, 
	328, 263, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 304, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 304, 
	328, 304, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	328, 263, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 352, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	328, 328, 263, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 304, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	304, 328, 304, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 328, 263, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 341, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 328, 328, 263, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 304, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 304, 328, 304, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 328, 263, 328, 263, 263, 263, 
	263, 353, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 328, 328, 263, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 304, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 304, 328, 304, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 328, 263, 328, 354, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 328, 328, 263, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 304, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 304, 328, 304, 328, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 328, 263, 328, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 341, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 328, 328, 263, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	304, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 304, 328, 304, 328, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 328, 263, 328, 
	355, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 356, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 328, 328, 263, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 304, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 304, 328, 304, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 328, 263, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 357, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 328, 328, 
	263, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 304, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 304, 328, 
	304, 328, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 328, 
	263, 328, 263, 263, 263, 263, 341, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 328, 
	328, 263, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 304, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 304, 
	328, 304, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	328, 263, 328, 358, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	328, 328, 263, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 304, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	304, 328, 304, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 328, 263, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 348, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 328, 328, 263, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 304, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 304, 328, 304, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 328, 263, 328, 263, 263, 263, 
	263, 359, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 341, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 328, 328, 263, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 304, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 304, 328, 304, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 328, 263, 328, 263, 263, 
	263, 263, 263, 360, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 328, 328, 263, 361, 361, 
	361, 361, 361, 361, 361, 361, 361, 361, 
	361, 361, 361, 361, 361, 361, 361, 361, 
	361, 361, 361, 361, 361, 361, 361, 361, 
	361, 361, 361, 361, 361, 361, 361, 304, 
	361, 361, 361, 361, 361, 361, 361, 361, 
	361, 361, 361, 361, 361, 361, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	361, 361, 361, 304, 361, 304, 361, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 361, 361, 361, 361, 263, 361, 263, 
	263, 263, 263, 263, 263, 263, 263, 362, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 361, 361, 361, 361, 361, 263, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	304, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 304, 328, 304, 328, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 328, 263, 328, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 363, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 328, 328, 263, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 304, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 304, 328, 304, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 328, 263, 
	328, 263, 263, 263, 263, 364, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 328, 328, 
	263, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 304, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 304, 328, 
	304, 328, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 328, 
	263, 328, 263, 263, 263, 365, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 328, 
	328, 263, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 304, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 304, 
	328, 366, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	328, 263, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	328, 328, 263, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 304, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	304, 328, 304, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 328, 263, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 367, 
	263, 368, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 328, 328, 263, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 304, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 304, 328, 304, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 328, 263, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 369, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 328, 328, 263, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 304, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 304, 328, 304, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 328, 263, 328, 263, 263, 
	263, 263, 341, 263, 263, 263, 370, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 328, 328, 263, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 304, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 304, 328, 304, 328, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 328, 263, 328, 263, 
	263, 263, 263, 263, 341, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 328, 328, 263, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	304, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 304, 328, 304, 328, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 328, 263, 328, 
	263, 263, 263, 341, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 371, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 328, 328, 263, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 304, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 304, 328, 304, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 328, 263, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 372, 263, 263, 
	263, 263, 263, 328, 328, 328, 328, 328, 
	263, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 304, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 304, 328, 
	304, 328, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 328, 
	263, 328, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 357, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 328, 
	328, 263, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 304, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 304, 
	328, 304, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	328, 263, 328, 373, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 295, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	328, 328, 263, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 304, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	304, 328, 304, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 328, 263, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 355, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 328, 328, 263, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 304, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 304, 328, 304, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 328, 263, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 341, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 328, 328, 263, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 304, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 304, 328, 304, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 328, 263, 328, 263, 263, 
	263, 263, 263, 341, 263, 263, 263, 263, 
	263, 263, 263, 341, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 328, 328, 263, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 304, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 304, 328, 304, 328, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 328, 263, 328, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 374, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 328, 328, 263, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	304, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 304, 328, 304, 328, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 328, 263, 328, 
	263, 263, 263, 375, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 328, 328, 263, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 304, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 304, 328, 304, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 328, 263, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 376, 263, 263, 
	263, 263, 263, 328, 328, 328, 328, 328, 
	263, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 304, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 304, 328, 
	304, 328, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 328, 
	263, 328, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 357, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 328, 
	328, 263, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 304, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 304, 
	328, 304, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	328, 263, 328, 263, 263, 263, 263, 377, 
	263, 263, 263, 378, 263, 263, 263, 263, 
	263, 379, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	328, 328, 263, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 304, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	304, 328, 304, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 328, 263, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 379, 263, 263, 328, 328, 
	328, 328, 328, 263, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 304, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 304, 328, 304, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 328, 263, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	341, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 328, 328, 263, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 304, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 304, 328, 304, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 328, 263, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 341, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 328, 328, 263, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 304, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 304, 328, 304, 328, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 328, 263, 328, 263, 
	263, 263, 263, 380, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 328, 328, 263, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	304, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 304, 328, 304, 328, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 328, 263, 328, 
	263, 263, 263, 381, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 382, 383, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 328, 328, 263, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 304, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 304, 328, 304, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 328, 263, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 341, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 328, 328, 
	263, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 304, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 304, 328, 
	304, 328, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 328, 
	263, 328, 263, 263, 384, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 328, 
	328, 263, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 304, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 304, 
	328, 304, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	328, 263, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 357, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	328, 328, 263, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 304, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	304, 328, 304, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 328, 263, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 385, 263, 263, 
	386, 263, 263, 263, 263, 263, 328, 328, 
	328, 328, 328, 263, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 304, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 304, 328, 304, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 328, 263, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 341, 263, 328, 
	328, 328, 328, 328, 263, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 304, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 304, 328, 304, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 328, 263, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 352, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 328, 328, 263, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 304, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 304, 328, 304, 328, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 328, 263, 328, 263, 
	263, 263, 263, 387, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 388, 263, 263, 263, 263, 
	263, 328, 328, 328, 328, 328, 263, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	304, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 304, 328, 304, 328, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 328, 263, 328, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 370, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 328, 328, 263, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 304, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 304, 328, 304, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 328, 263, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	389, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 328, 328, 
	263, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 304, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 304, 328, 
	304, 328, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 328, 
	263, 328, 263, 263, 263, 263, 295, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 328, 
	328, 263, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 304, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 304, 
	328, 304, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	328, 263, 328, 263, 263, 263, 263, 263, 
	263, 263, 390, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 384, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	328, 328, 263, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 304, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	304, 328, 304, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 328, 263, 328, 263, 263, 263, 263, 
	352, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 328, 328, 263, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 304, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 304, 328, 304, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 328, 263, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 391, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 328, 328, 263, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 304, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 304, 328, 304, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 328, 263, 328, 263, 263, 
	263, 392, 263, 263, 263, 263, 263, 263, 
	263, 393, 263, 263, 263, 263, 263, 263, 
	263, 394, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 328, 328, 263, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 304, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 304, 328, 304, 328, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 328, 263, 328, 263, 
	263, 263, 263, 370, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 328, 328, 263, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	304, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 328, 328, 328, 304, 328, 304, 328, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 328, 263, 328, 
	263, 263, 263, 263, 358, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 328, 328, 263, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 304, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 328, 328, 328, 304, 328, 304, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 328, 263, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 378, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 328, 328, 
	263, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 304, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 328, 328, 328, 304, 328, 
	304, 328, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 328, 
	263, 328, 263, 263, 263, 263, 263, 263, 
	263, 395, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 328, 
	328, 263, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 304, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 328, 328, 328, 304, 
	328, 304, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	328, 263, 328, 263, 263, 263, 263, 352, 
	263, 263, 263, 376, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	328, 328, 263, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 304, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 328, 328, 328, 
	304, 328, 304, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 328, 263, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 396, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 328, 328, 263, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 304, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 328, 328, 
	328, 304, 328, 304, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 328, 263, 328, 263, 263, 263, 
	263, 397, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 328, 328, 263, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 328, 304, 328, 
	328, 328, 328, 328, 328, 328, 328, 328, 
	328, 328, 328, 328, 328, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 328, 
	328, 328, 304, 328, 304, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 328, 263, 328, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	263, 346, 263, 263, 263, 263, 263, 263, 
	263, 263, 263, 263, 263, 263, 263, 263, 
	328, 328, 328, 328, 328, 263, 275, 307, 
	399, 400, 400, 400, 399, 400, 400, 400, 
	400, 401, 400, 401, 401, 401, 400, 400, 
	400, 400, 400, 400, 400, 400, 400, 400, 
	400, 400, 399, 400, 400, 400, 400, 400, 
	401, 400, 400, 402, 400, 400, 400, 400, 
	400, 400, 400, 400, 400, 400, 403, 400, 
	400, 400, 400, 400, 400, 400, 400, 400, 
	400, 400, 400, 400, 400, 400, 400, 400, 
	400, 398, 398, 398, 398, 398, 398, 398, 
	398, 398, 398, 398, 398, 398, 398, 398, 
	398, 398, 398, 398, 398, 398, 398, 398, 
	398, 398, 398, 400, 404, 400, 400, 398, 
	400, 398, 398, 398, 398, 398, 398, 398, 
	398, 398, 398, 398, 398, 398, 398, 398, 
	398, 398, 398, 398, 398, 398, 398, 398, 
	398, 398, 398, 400, 400, 400, 400, 400, 
	398, 405, 405, 405, 405, 405, 405, 405, 
	405, 405, 405, 405, 405, 405, 405, 405, 
	405, 405, 405, 405, 405, 405, 405, 405, 
	405, 405, 405, 405, 405, 405, 405, 405, 
	405, 405, 14, 405, 405, 405, 405, 405, 
	405, 405, 405, 405, 405, 405, 405, 405, 
	405, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 15, 405, 405, 405, 405, 
	14, 405, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 405, 405, 405, 405, 
	13, 405, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 13, 13, 13, 13, 
	13, 13, 13, 13, 405, 405, 405, 405, 
	405, 13, 401, 406, 401, 401, 401, 406, 
	406, 406, 406, 406, 406, 406, 406, 406, 
	406, 406, 406, 406, 406, 406, 406, 406, 
	406, 401, 406, 407, 408, 409, 410, 411, 
	405, 412, 405, 413, 415, 416, 416, 416, 
	415, 416, 416, 416, 416, 417, 418, 417, 
	417, 417, 416, 416, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 415, 416, 
	416, 416, 416, 416, 417, 419, 416, 420, 
	416, 421, 422, 416, 416, 416, 423, 424, 
	416, 424, 416, 421, 416, 416, 416, 416, 
	416, 416, 416, 416, 416, 416, 416, 416, 
	425, 426, 427, 416, 416, 428, 428, 428, 
	428, 428, 428, 428, 428, 428, 428, 428, 
	428, 428, 428, 428, 428, 428, 428, 428, 
	428, 428, 428, 428, 428, 428, 428, 429, 
	430, 416, 421, 414, 421, 414, 414, 414, 
	414, 414, 414, 414, 414, 414, 414, 414, 
	414, 414, 414, 414, 414, 414, 414, 414, 
	414, 414, 414, 414, 414, 414, 414, 416, 
	431, 416, 424, 416, 414, 432, 432, 432, 
	432, 432, 432, 432, 432, 432, 432, 432, 
	432, 432, 432, 432, 432, 432, 432, 432, 
	432, 432, 432, 432, 432, 432, 432, 432, 
	432, 432, 432, 432, 432, 432, 433, 432, 
	432, 432, 432, 432, 432, 432, 432, 432, 
	432, 432, 432, 432, 432, 414, 414, 414, 
	414, 414, 414, 414, 414, 414, 414, 432, 
	432, 432, 432, 432, 433, 432, 414, 414, 
	414, 414, 414, 414, 414, 414, 414, 414, 
	414, 414, 414, 414, 414, 414, 414, 414, 
	414, 414, 414, 414, 414, 414, 414, 414, 
	432, 432, 432, 432, 414, 432, 414, 414, 
	414, 414, 414, 414, 414, 414, 414, 414, 
	414, 414, 414, 414, 414, 414, 414, 414, 
	414, 414, 414, 414, 414, 414, 414, 414, 
	432, 432, 432, 432, 432, 414, 435, 434, 
	436, 417, 437, 417, 417, 417, 437, 437, 
	437, 437, 437, 437, 437, 437, 437, 437, 
	437, 437, 437, 437, 437, 437, 437, 437, 
	417, 437, 438, 421, 439, 439, 421, 439, 
	439, 439, 439, 439, 439, 439, 439, 439, 
	439, 439, 439, 439, 439, 439, 439, 439, 
	439, 439, 439, 439, 439, 439, 439, 439, 
	439, 439, 439, 439, 439, 439, 439, 439, 
	439, 439, 439, 439, 439, 439, 439, 439, 
	439, 439, 439, 439, 439, 439, 439, 439, 
	439, 439, 439, 439, 439, 439, 439, 439, 
	439, 439, 439, 439, 421, 439, 440, 441, 
	442, 443, 421, 439, 421, 439, 421, 439, 
	421, 444, 439, 421, 439, 446, 421, 445, 
	445, 445, 445, 445, 445, 445, 445, 445, 
	445, 445, 445, 445, 445, 445, 445, 445, 
	445, 445, 445, 445, 445, 445, 445, 445, 
	445, 445, 445, 445, 445, 445, 445, 445, 
	445, 445, 445, 445, 445, 445, 445, 445, 
	445, 445, 445, 445, 445, 445, 445, 445, 
	445, 445, 445, 445, 445, 445, 445, 445, 
	445, 445, 445, 445, 445, 445, 421, 445, 
	421, 439, 421, 421, 439, 447, 447, 447, 
	447, 447, 447, 447, 447, 447, 447, 447, 
	447, 447, 447, 447, 447, 447, 447, 447, 
	447, 447, 447, 447, 447, 447, 447, 447, 
	447, 447, 447, 447, 447, 447, 433, 447, 
	447, 447, 447, 447, 447, 447, 447, 447, 
	447, 447, 447, 447, 447, 428, 428, 428, 
	428, 428, 428, 428, 428, 428, 428, 447, 
	447, 447, 447, 447, 433, 447, 428, 428, 
	428, 428, 428, 428, 428, 428, 428, 428, 
	428, 428, 428, 428, 428, 428, 428, 428, 
	428, 428, 428, 428, 428, 428, 428, 428, 
	447, 447, 447, 447, 428, 447, 428, 428, 
	428, 428, 428, 428, 428, 428, 428, 428, 
	428, 428, 428, 428, 428, 428, 428, 428, 
	428, 428, 428, 428, 428, 428, 428, 428, 
	447, 447, 447, 447, 447, 428, 446, 445, 
	418, 445, 421, 439, 449, 448, 448, 448, 
	449, 448, 448, 448, 448, 450, 451, 450, 
	450, 450, 448, 448, 448, 448, 448, 448, 
	448, 448, 448, 448, 448, 448, 449, 448, 
	448, 448, 448, 448, 450, 448, 448, 452, 
	448, 24, 453, 448, 454, 448, 455, 24, 
	55, 456, 57, 24, 448, 448, 448, 448, 
	448, 448, 448, 448, 448, 448, 457, 448, 
	458, 55, 459, 460, 448, 448, 448, 448, 
	448, 448, 448, 448, 448, 448, 448, 448, 
	448, 448, 448, 448, 448, 448, 448, 448, 
	448, 448, 448, 448, 448, 448, 448, 55, 
	461, 55, 24, 448, 448, 448, 448, 448, 
	448, 448, 448, 448, 448, 462, 448, 448, 
	448, 448, 448, 448, 448, 448, 463, 448, 
	448, 464, 448, 465, 448, 448, 448, 68, 
	69, 448, 24, 448, 466, 466, 466, 466, 
	466, 466, 466, 466, 466, 450, 466, 450, 
	450, 450, 466, 466, 466, 466, 466, 466, 
	466, 466, 466, 466, 466, 466, 466, 466, 
	466, 466, 466, 466, 450, 466, 466, 466, 
	466, 50, 51, 466, 52, 466, 53, 54, 
	55, 56, 57, 50, 466, 466, 466, 466, 
	466, 466, 466, 466, 466, 466, 58, 466, 
	59, 55, 60, 61, 466, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 62, 
	63, 55, 24, 19, 466, 19, 19, 19, 
	19, 19, 19, 19, 19, 64, 19, 19, 
	19, 19, 19, 19, 19, 19, 65, 19, 
	19, 66, 19, 67, 19, 19, 19, 68, 
	69, 466, 24, 466, 19, 467, 468, 468, 
	468, 467, 468, 468, 468, 468, 55, 469, 
	55, 55, 55, 468, 468, 468, 468, 468, 
	468, 468, 468, 468, 468, 468, 468, 467, 
	468, 468, 468, 468, 468, 55, 468, 468, 
	468, 468, 468, 468, 468, 468, 468, 468, 
	468, 468, 468, 468, 468, 468, 468, 468, 
	468, 468, 468, 468, 468, 468, 468, 468, 
	468, 468, 55, 468, 55, 469, 55, 55, 
	55, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 55, 18, 18, 18, 18, 
	18, 24, 18, 18, 18, 18, 18, 18, 
	18, 55, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	55, 18, 55, 469, 55, 55, 55, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 55, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 55, 18, 
	470, 55, 469, 55, 55, 55, 471, 471, 
	471, 471, 471, 471, 471, 471, 471, 471, 
	471, 471, 471, 471, 471, 471, 471, 471, 
	55, 471, 471, 471, 471, 471, 471, 471, 
	471, 471, 472, 471, 471, 471, 471, 471, 
	471, 471, 471, 471, 471, 471, 471, 471, 
	471, 471, 471, 471, 471, 55, 471, 55, 
	469, 55, 55, 55, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 55, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 55, 55, 18, 473, 467, 
	55, 467, 475, 474, 477, 478, 477, 477, 
	477, 476, 476, 476, 476, 476, 476, 476, 
	476, 476, 476, 476, 476, 476, 476, 476, 
	476, 476, 476, 477, 476, 479, 467, 467, 
	467, 467, 467, 467, 467, 467, 467, 467, 
	467, 467, 467, 467, 467, 467, 467, 467, 
	467, 467, 467, 467, 467, 467, 467, 467, 
	467, 467, 467, 467, 467, 467, 467, 20, 
	467, 467, 467, 467, 467, 467, 467, 467, 
	467, 467, 467, 467, 467, 467, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	21, 467, 467, 467, 467, 20, 467, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 467, 467, 467, 467, 19, 467, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 19, 
	19, 467, 467, 467, 467, 467, 19, 480, 
	55, 469, 55, 55, 55, 467, 467, 467, 
	467, 467, 467, 467, 467, 467, 467, 467, 
	467, 467, 467, 467, 467, 467, 467, 55, 
	467, 467, 467, 467, 467, 467, 467, 467, 
	467, 467, 467, 467, 467, 467, 467, 467, 
	467, 467, 467, 467, 467, 467, 467, 467, 
	467, 467, 467, 467, 55, 467, 467, 467, 
	467, 467, 467, 467, 467, 467, 467, 467, 
	467, 467, 467, 467, 467, 467, 467, 467, 
	467, 467, 467, 467, 467, 467, 467, 467, 
	467, 467, 467, 467, 467, 467, 467, 467, 
	467, 467, 467, 467, 467, 467, 467, 467, 
	467, 467, 467, 467, 467, 467, 467, 467, 
	467, 467, 467, 467, 467, 467, 467, 467, 
	467, 467, 467, 24, 467, 481, 482, 483, 
	484, 485, 486, 55, 469, 55, 55, 55, 
	467, 467, 467, 467, 467, 467, 467, 467, 
	467, 467, 467, 467, 467, 467, 467, 467, 
	467, 467, 55, 467, 467, 467, 467, 467, 
	467, 467, 467, 467, 24, 467, 467, 467, 
	467, 467, 467, 467, 467, 467, 467, 467, 
	467, 467, 467, 467, 467, 467, 467, 55, 
	467, 55, 474, 24, 487, 24, 487, 488, 
	489, 488, 488, 488, 476, 476, 476, 476, 
	476, 476, 476, 476, 476, 476, 476, 476, 
	476, 476, 476, 476, 476, 476, 488, 476, 
	490, 487, 491, 491, 491, 491, 491, 491, 
	491, 491, 491, 27, 491, 27, 27, 27, 
	491, 491, 491, 491, 491, 491, 491, 491, 
	491, 491, 491, 491, 491, 491, 491, 491, 
	491, 491, 27, 491, 491, 491, 491, 28, 
	29, 491, 30, 491, 31, 32, 33, 34, 
	35, 28, 491, 491, 491, 491, 491, 491, 
	491, 491, 491, 491, 36, 491, 37, 33, 
	38, 39, 491, 26, 26, 26, 26, 26, 
	26, 26, 26, 26, 26, 26, 26, 26, 
	26, 26, 26, 26, 26, 26, 26, 26, 
	26, 26, 26, 26, 26, 40, 41, 33, 
	42, 26, 491, 26, 26, 26, 26, 26, 
	26, 26, 26, 43, 26, 26, 26, 26, 
	26, 26, 26, 26, 44, 26, 26, 45, 
	26, 46, 26, 26, 26, 47, 48, 491, 
	42, 491, 26, 55, 487, 492, 487, 493, 
	487, 494, 487, 495, 94, 94, 94, 495, 
	94, 94, 94, 94, 496, 94, 496, 496, 
	496, 94, 94, 94, 94, 94, 94, 94, 
	94, 94, 94, 94, 94, 495, 94, 94, 
	94, 94, 94, 496, 94, 94, 94, 94, 
	94, 94, 94, 94, 94, 94, 94, 94, 
	94, 94, 94, 94, 94, 94, 94, 94, 
	94, 94, 94, 94, 94, 94, 94, 94, 
	94, 94, 94, 94, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 94, 497, 
	94, 94, 96, 94, 96, 96, 96, 100, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 94, 94, 
	94, 94, 94, 96, 93, 93, 93, 93, 
	93, 93, 93, 93, 93, 93, 93, 93, 
	93, 93, 93, 93, 93, 93, 93, 93, 
	93, 93, 93, 93, 93, 93, 93, 93, 
	93, 93, 93, 93, 93, 498, 93, 93, 
	93, 93, 93, 93, 93, 93, 93, 93, 
	93, 93, 93, 93, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 94, 93, 
	93, 93, 93, 498, 93, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 96, 93, 
	93, 93, 93, 96, 93, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 96, 93, 
	93, 93, 93, 93, 96, 499, 499, 499, 
	499, 499, 499, 499, 499, 499, 97, 499, 
	97, 97, 97, 499, 499, 499, 499, 499, 
	499, 499, 499, 499, 499, 499, 499, 499, 
	499, 499, 499, 499, 499, 97, 499, 499, 
	499, 499, 499, 499, 499, 98, 499, 499, 
	499, 499, 499, 499, 499, 499, 499, 499, 
	499, 499, 499, 499, 499, 499, 499, 499, 
	499, 499, 499, 499, 499, 499, 96, 96, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	499, 99, 499, 499, 96, 499, 96, 96, 
	96, 100, 96, 96, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	499, 499, 499, 499, 499, 96, 500, 499, 
	499, 499, 499, 499, 499, 499, 499, 499, 
	499, 499, 499, 499, 499, 499, 499, 499, 
	499, 499, 499, 499, 499, 499, 499, 499, 
	499, 499, 499, 499, 499, 499, 499, 499, 
	498, 499, 499, 499, 499, 499, 499, 499, 
	499, 499, 499, 499, 499, 499, 499, 96, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 94, 499, 499, 499, 499, 498, 499, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 96, 499, 499, 499, 499, 96, 499, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 96, 96, 96, 96, 96, 501, 96, 
	96, 96, 96, 96, 96, 96, 96, 96, 
	96, 96, 499, 499, 499, 499, 499, 96, 
	101, 499, 503, 502, 502, 502, 503, 502, 
	502, 502, 502, 504, 502, 504, 504, 504, 
	502, 502, 502, 502, 502, 502, 502, 502, 
	502, 502, 502, 502, 503, 502, 502, 502, 
	502, 502, 504, 502, 502, 505, 502, 502, 
	502, 502, 502, 502, 502, 502, 502, 502, 
	502, 502, 502, 502, 502, 502, 502, 502, 
	502, 502, 502, 502, 502, 502, 502, 502, 
	502, 502, 502, 502, 502, 502, 502, 502, 
	502, 502, 502, 502, 502, 502, 502, 502, 
	502, 502, 502, 502, 502, 502, 502, 502, 
	502, 502, 502, 502, 502, 502, 506, 502, 
	502, 502, 502, 502, 502, 502, 507, 502, 
	502, 502, 502, 502, 502, 502, 502, 502, 
	502, 502, 502, 502, 502, 502, 502, 502, 
	502, 502, 502, 502, 502, 508, 502, 504, 
	509, 504, 504, 504, 509, 509, 509, 509, 
	509, 509, 509, 509, 509, 509, 509, 509, 
	509, 509, 509, 509, 509, 509, 504, 509, 
	510, 511, 512, 513, 515, 514, 516, 517, 
	514, 518, 520, 521, 521, 521, 520, 521, 
	521, 521, 521, 522, 523, 522, 522, 522, 
	521, 521, 521, 521, 521, 521, 521, 521, 
	521, 521, 521, 521, 520, 521, 521, 521, 
	521, 521, 522, 521, 521, 524, 521, 521, 
	521, 521, 521, 521, 521, 521, 521, 521, 
	521, 521, 521, 521, 521, 521, 521, 521, 
	521, 521, 521, 521, 521, 521, 521, 521, 
	521, 521, 521, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 521, 525, 521, 
	521, 519, 521, 519, 519, 519, 519, 519, 
	519, 519, 519, 526, 519, 519, 519, 519, 
	519, 519, 519, 519, 527, 519, 519, 528, 
	519, 529, 519, 519, 519, 521, 521, 521, 
	521, 521, 519, 530, 530, 530, 530, 530, 
	530, 530, 530, 530, 530, 530, 530, 530, 
	530, 530, 530, 530, 530, 530, 530, 530, 
	530, 530, 530, 530, 530, 530, 530, 530, 
	530, 530, 530, 530, 530, 530, 530, 530, 
	530, 530, 530, 530, 530, 530, 530, 530, 
	530, 530, 530, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 530, 530, 530, 
	530, 530, 530, 530, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 530, 530, 
	530, 530, 519, 530, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 530, 530, 
	530, 530, 530, 519, 522, 531, 522, 522, 
	522, 531, 531, 531, 531, 531, 531, 531, 
	531, 531, 531, 531, 531, 531, 531, 531, 
	531, 531, 531, 522, 531, 532, 533, 534, 
	535, 536, 538, 537, 539, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 540, 
	540, 540, 540, 540, 540, 540, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	540, 540, 540, 540, 519, 540, 519, 519, 
	519, 519, 519, 541, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	540, 540, 540, 540, 540, 519, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	540, 540, 540, 540, 540, 540, 540, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 540, 540, 540, 540, 519, 540, 519, 
	519, 519, 519, 542, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 540, 540, 540, 540, 540, 519, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 540, 540, 540, 540, 540, 540, 540, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 540, 540, 540, 540, 519, 540, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 543, 519, 519, 519, 519, 519, 
	519, 519, 540, 540, 540, 540, 540, 519, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 540, 540, 540, 540, 540, 540, 
	540, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 540, 540, 540, 540, 519, 
	540, 519, 519, 544, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 540, 540, 540, 540, 540, 
	519, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 540, 540, 540, 540, 540, 
	540, 540, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 540, 540, 540, 540, 
	519, 540, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 545, 519, 
	519, 519, 519, 519, 540, 540, 540, 540, 
	540, 519, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 540, 540, 540, 540, 
	540, 540, 540, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 540, 540, 540, 
	540, 519, 540, 519, 519, 519, 519, 541, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 540, 540, 540, 
	540, 540, 519, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 540, 540, 540, 
	540, 540, 540, 540, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 540, 540, 
	540, 540, 519, 540, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 546, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 540, 540, 
	540, 540, 540, 519, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 540, 540, 
	540, 540, 540, 540, 540, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 540, 
	540, 540, 540, 519, 540, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	547, 519, 519, 519, 519, 519, 519, 519, 
	548, 519, 519, 519, 519, 519, 519, 540, 
	540, 540, 540, 540, 519, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 540, 
	540, 540, 540, 540, 540, 540, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	540, 540, 540, 540, 519, 540, 519, 519, 
	519, 519, 549, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	540, 540, 540, 540, 540, 519, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	540, 540, 540, 540, 540, 540, 540, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 540, 540, 540, 540, 519, 540, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 550, 519, 519, 519, 519, 519, 519, 
	519, 540, 540, 540, 540, 540, 519, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 540, 540, 540, 540, 540, 540, 540, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 540, 540, 540, 540, 519, 540, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 541, 519, 519, 519, 519, 519, 
	519, 519, 540, 540, 540, 540, 540, 519, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 540, 540, 540, 540, 540, 540, 
	540, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 540, 540, 540, 540, 519, 
	540, 519, 519, 519, 519, 519, 519, 519, 
	519, 551, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 540, 540, 540, 540, 540, 
	519, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 540, 540, 540, 540, 540, 
	540, 540, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 540, 540, 540, 540, 
	519, 540, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 541, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 540, 540, 540, 540, 
	540, 519, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 540, 540, 540, 540, 
	540, 540, 540, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 540, 540, 540, 
	540, 519, 540, 519, 519, 519, 519, 519, 
	519, 519, 552, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 540, 540, 540, 
	540, 540, 519, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 540, 540, 540, 
	540, 540, 540, 540, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 540, 540, 
	540, 540, 519, 540, 519, 519, 519, 519, 
	519, 519, 519, 519, 553, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 540, 540, 
	540, 540, 540, 519, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 540, 540, 540, 540, 
	540, 540, 540, 540, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 540, 540, 
	540, 540, 540, 540, 540, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 540, 
	540, 540, 540, 519, 540, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 519, 
	545, 519, 519, 519, 519, 519, 519, 519, 
	519, 519, 519, 519, 519, 519, 519, 540, 
	540, 540, 540, 540, 519, 555, 125, 125, 
	125, 555, 125, 125, 125, 125, 556, 557, 
	556, 556, 556, 125, 125, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 555, 
	125, 125, 125, 125, 125, 556, 558, 125, 
	559, 125, 560, 561, 125, 562, 125, 563, 
	564, 125, 565, 566, 567, 125, 125, 125, 
	125, 125, 125, 125, 125, 125, 125, 568, 
	125, 569, 570, 571, 572, 125, 573, 574, 
	573, 573, 575, 573, 573, 573, 573, 573, 
	573, 573, 573, 573, 573, 573, 573, 573, 
	573, 573, 573, 573, 573, 573, 573, 573, 
	576, 577, 125, 578, 579, 125, 580, 581, 
	582, 583, 584, 585, 554, 554, 586, 554, 
	554, 554, 587, 588, 589, 554, 554, 590, 
	591, 592, 593, 554, 594, 554, 595, 554, 
	596, 597, 125, 578, 125, 554, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 599, 
	598, 599, 599, 599, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 599, 172, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	173, 598, 598, 174, 598, 172, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 598, 600, 598, 598, 554, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 598, 598, 598, 598, 598, 554, 602, 
	601, 601, 603, 601, 604, 606, 607, 605, 
	605, 605, 605, 605, 605, 605, 605, 605, 
	605, 605, 605, 605, 605, 605, 605, 605, 
	605, 605, 605, 605, 605, 605, 605, 605, 
	605, 605, 605, 605, 605, 605, 605, 605, 
	605, 605, 605, 605, 605, 605, 605, 605, 
	605, 605, 605, 605, 605, 605, 605, 605, 
	605, 605, 605, 605, 605, 605, 605, 605, 
	605, 605, 605, 605, 605, 605, 608, 605, 
	610, 609, 611, 612, 613, 556, 614, 556, 
	556, 556, 614, 614, 614, 614, 614, 614, 
	614, 614, 614, 614, 614, 614, 614, 614, 
	614, 614, 614, 614, 556, 614, 616, 615, 
	618, 619, 618, 618, 618, 617, 617, 617, 
	617, 617, 617, 617, 617, 617, 617, 617, 
	617, 617, 617, 617, 617, 617, 617, 618, 
	617, 125, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 125, 620, 621, 622, 623, 624, 
	626, 626, 626, 626, 626, 626, 626, 626, 
	626, 626, 626, 626, 626, 626, 626, 626, 
	626, 626, 626, 626, 626, 626, 626, 626, 
	626, 626, 626, 626, 626, 626, 626, 626, 
	626, 626, 626, 626, 626, 626, 626, 626, 
	626, 626, 626, 626, 626, 626, 626, 626, 
	625, 625, 625, 625, 625, 625, 625, 625, 
	625, 625, 626, 626, 626, 626, 626, 626, 
	626, 627, 627, 627, 627, 627, 627, 627, 
	627, 627, 627, 627, 627, 627, 627, 627, 
	627, 627, 627, 627, 627, 627, 627, 627, 
	627, 627, 627, 626, 626, 626, 626, 626, 
	626, 627, 627, 627, 627, 627, 627, 627, 
	627, 627, 627, 627, 627, 627, 627, 627, 
	627, 627, 627, 627, 627, 627, 627, 627, 
	627, 627, 627, 626, 626, 626, 626, 626, 
	625, 629, 628, 628, 628, 628, 628, 628, 
	628, 628, 628, 628, 628, 628, 628, 628, 
	628, 628, 628, 628, 628, 628, 628, 628, 
	125, 628, 630, 632, 631, 631, 631, 631, 
	631, 631, 631, 631, 631, 631, 631, 631, 
	631, 631, 631, 631, 631, 631, 125, 631, 
	125, 115, 126, 127, 126, 126, 126, 628, 
	628, 628, 628, 628, 628, 628, 628, 628, 
	628, 628, 628, 628, 628, 628, 628, 628, 
	628, 126, 628, 628, 128, 628, 628, 628, 
	628, 628, 628, 628, 628, 628, 628, 628, 
	628, 129, 129, 129, 129, 129, 129, 129, 
	129, 129, 129, 628, 628, 628, 125, 628, 
	628, 628, 628, 628, 628, 628, 628, 628, 
	628, 628, 628, 628, 628, 628, 628, 628, 
	628, 628, 628, 628, 628, 628, 628, 628, 
	628, 628, 628, 628, 628, 130, 628, 143, 
	144, 143, 143, 143, 142, 142, 142, 142, 
	142, 142, 142, 142, 142, 142, 142, 142, 
	142, 142, 142, 142, 142, 142, 143, 142, 
	142, 145, 142, 142, 142, 142, 142, 142, 
	142, 142, 142, 142, 142, 142, 146, 146, 
	146, 146, 146, 146, 146, 146, 146, 146, 
	142, 142, 142, 142, 142, 142, 142, 142, 
	142, 142, 142, 142, 142, 142, 142, 142, 
	142, 142, 142, 142, 142, 142, 142, 142, 
	142, 142, 142, 142, 142, 142, 142, 142, 
	142, 142, 147, 142, 126, 127, 126, 126, 
	126, 628, 628, 628, 628, 628, 628, 628, 
	628, 628, 628, 628, 628, 628, 628, 628, 
	628, 628, 628, 126, 628, 628, 128, 628, 
	628, 628, 628, 628, 628, 628, 628, 628, 
	628, 628, 628, 129, 129, 129, 129, 129, 
	129, 129, 129, 129, 129, 628, 628, 628, 
	125, 125, 628, 628, 628, 628, 628, 628, 
	628, 628, 628, 628, 628, 628, 628, 628, 
	628, 628, 628, 628, 628, 628, 628, 628, 
	628, 628, 628, 628, 628, 628, 628, 130, 
	628, 634, 620, 636, 635, 638, 637, 620, 
	639, 639, 639, 620, 639, 639, 639, 639, 
	639, 639, 639, 639, 639, 639, 639, 639, 
	639, 639, 639, 639, 639, 639, 639, 639, 
	639, 620, 639, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 641, 642, 620, 643, 
	151, 644, 642, 620, 620, 645, 646, 620, 
	646, 620, 151, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 647, 620, 648, 
	649, 650, 620, 651, 640, 640, 640, 640, 
	640, 640, 640, 640, 640, 640, 640, 640, 
	640, 640, 640, 640, 640, 640, 640, 640, 
	640, 640, 640, 640, 640, 640, 652, 620, 
	620, 151, 640, 151, 640, 640, 640, 640, 
	640, 640, 640, 640, 640, 640, 640, 640, 
	640, 640, 640, 640, 640, 640, 640, 640, 
	640, 640, 640, 640, 640, 640, 620, 653, 
	620, 654, 620, 640, 655, 655, 655, 655, 
	655, 655, 655, 655, 655, 655, 655, 655, 
	655, 655, 655, 655, 655, 655, 655, 655, 
	655, 655, 655, 655, 655, 655, 655, 655, 
	655, 655, 655, 655, 655, 656, 655, 655, 
	655, 655, 655, 655, 655, 655, 655, 655, 
	655, 655, 655, 655, 640, 640, 640, 640, 
	640, 640, 640, 640, 640, 640, 655, 655, 
	655, 657, 655, 656, 655, 640, 640, 640, 
	640, 640, 640, 640, 640, 640, 640, 640, 
	640, 640, 640, 640, 640, 640, 640, 640, 
	640, 640, 640, 640, 640, 640, 640, 655, 
	655, 655, 655, 640, 655, 640, 640, 640, 
	640, 640, 640, 640, 640, 640, 640, 640, 
	640, 640, 640, 640, 640, 640, 640, 640, 
	640, 640, 640, 640, 640, 640, 640, 655, 
	655, 655, 655, 655, 640, 659, 658, 660, 
	662, 663, 661, 661, 661, 661, 661, 661, 
	661, 661, 661, 661, 661, 661, 661, 661, 
	661, 661, 661, 661, 661, 661, 661, 661, 
	661, 661, 661, 661, 661, 661, 661, 661, 
	661, 661, 661, 661, 661, 661, 661, 661, 
	661, 661, 661, 661, 661, 661, 661, 661, 
	661, 661, 661, 661, 661, 661, 661, 661, 
	661, 661, 661, 661, 661, 661, 661, 661, 
	661, 664, 661, 666, 667, 665, 668, 669, 
	670, 671, 151, 655, 655, 672, 655, 655, 
	655, 655, 655, 655, 655, 655, 655, 655, 
	655, 655, 655, 655, 655, 655, 655, 655, 
	655, 655, 655, 655, 655, 655, 655, 655, 
	655, 655, 655, 655, 655, 655, 655, 655, 
	655, 655, 655, 655, 655, 655, 655, 655, 
	655, 655, 655, 655, 655, 655, 655, 655, 
	655, 655, 655, 655, 655, 655, 655, 655, 
	655, 655, 655, 151, 655, 655, 655, 655, 
	655, 655, 655, 655, 655, 655, 655, 655, 
	655, 655, 655, 655, 655, 655, 655, 655, 
	655, 655, 655, 655, 655, 655, 655, 655, 
	655, 655, 655, 655, 655, 655, 655, 655, 
	655, 655, 655, 655, 655, 655, 655, 655, 
	655, 655, 655, 655, 655, 150, 150, 150, 
	150, 150, 150, 150, 150, 150, 150, 655, 
	655, 655, 655, 655, 655, 655, 150, 150, 
	150, 150, 150, 150, 150, 150, 150, 150, 
	150, 150, 150, 150, 150, 150, 150, 150, 
	150, 150, 150, 150, 150, 150, 150, 150, 
	655, 655, 655, 655, 150, 655, 150, 150, 
	150, 150, 150, 150, 150, 150, 150, 150, 
	150, 150, 150, 150, 150, 150, 150, 150, 
	150, 150, 150, 150, 150, 150, 150, 150, 
	655, 655, 655, 655, 655, 150, 153, 153, 
	153, 153, 153, 153, 153, 153, 153, 153, 
	655, 673, 655, 151, 655, 151, 655, 151, 
	674, 655, 151, 655, 151, 655, 151, 151, 
	655, 675, 675, 675, 675, 675, 675, 675, 
	675, 675, 675, 675, 675, 675, 675, 675, 
	675, 675, 675, 675, 675, 675, 675, 675, 
	675, 675, 675, 675, 675, 675, 675, 675, 
	675, 675, 675, 675, 675, 675, 675, 675, 
	675, 675, 675, 675, 675, 675, 675, 675, 
	675, 677, 677, 677, 677, 677, 677, 677, 
	677, 677, 677, 675, 675, 675, 675, 675, 
	675, 678, 676, 676, 676, 676, 676, 676, 
	676, 676, 676, 676, 676, 676, 676, 676, 
	676, 676, 676, 676, 676, 676, 676, 676, 
	676, 676, 676, 676, 675, 675, 675, 675, 
	676, 675, 676, 676, 676, 676, 676, 676, 
	676, 676, 676, 676, 676, 676, 676, 676, 
	676, 676, 676, 676, 676, 676, 676, 676, 
	676, 676, 676, 676, 675, 675, 675, 675, 
	675, 676, 680, 680, 680, 680, 680, 680, 
	680, 680, 680, 680, 679, 681, 681, 681, 
	681, 681, 681, 681, 681, 681, 681, 681, 
	681, 681, 681, 681, 681, 681, 681, 681, 
	681, 681, 681, 681, 681, 681, 681, 681, 
	681, 681, 681, 681, 681, 681, 681, 681, 
	681, 681, 681, 681, 681, 681, 681, 681, 
	681, 681, 681, 681, 681, 683, 683, 683, 
	683, 683, 683, 683, 683, 683, 683, 681, 
	681, 681, 681, 681, 681, 681, 682, 682, 
	682, 682, 682, 682, 682, 682, 682, 682, 
	682, 682, 682, 682, 682, 682, 682, 682, 
	682, 682, 682, 682, 682, 682, 682, 682, 
	681, 681, 681, 681, 682, 681, 682, 682, 
	682, 682, 682, 682, 682, 682, 682, 682, 
	682, 682, 682, 682, 682, 682, 682, 682, 
	682, 682, 682, 682, 682, 682, 682, 682, 
	681, 681, 681, 681, 681, 682, 673, 655, 
	672, 655, 684, 685, 620, 686, 166, 156, 
	156, 156, 156, 156, 156, 156, 156, 156, 
	156, 156, 156, 156, 156, 156, 156, 156, 
	156, 156, 156, 156, 156, 156, 167, 156, 
	168, 157, 157, 157, 157, 157, 157, 157, 
	157, 157, 157, 157, 157, 157, 157, 157, 
	157, 157, 157, 157, 157, 157, 157, 157, 
	157, 157, 157, 157, 157, 167, 157, 167, 
	170, 125, 620, 578, 125, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 125, 620, 125, 629, 
	620, 690, 689, 689, 689, 690, 689, 689, 
	689, 689, 689, 689, 689, 689, 689, 689, 
	689, 689, 689, 689, 689, 689, 689, 689, 
	689, 689, 689, 690, 689, 115, 115, 115, 
	115, 115, 115, 115, 115, 115, 115, 115, 
	115, 115, 115, 115, 115, 115, 115, 115, 
	115, 115, 115, 115, 115, 115, 115, 115, 
	115, 115, 115, 115, 115, 115, 172, 115, 
	115, 115, 115, 115, 115, 115, 115, 115, 
	115, 115, 115, 115, 115, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 173, 
	115, 115, 174, 115, 172, 115, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	115, 115, 115, 115, 171, 115, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	115, 115, 115, 115, 115, 171, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 172, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	173, 620, 620, 174, 620, 172, 620, 171, 
	171, 171, 171, 691, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 620, 620, 620, 620, 171, 620, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 620, 620, 620, 620, 620, 171, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	172, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 173, 620, 620, 174, 620, 172, 620, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 692, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 620, 620, 620, 620, 171, 620, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 620, 620, 620, 620, 620, 171, 
	693, 694, 620, 615, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 599, 598, 599, 
	599, 599, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 599, 172, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 173, 598, 
	598, 174, 598, 172, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 598, 
	600, 598, 598, 695, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 598, 
	598, 598, 598, 598, 554, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 599, 598, 
	599, 599, 599, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 599, 172, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 173, 
	598, 598, 174, 598, 172, 598, 554, 554, 
	554, 554, 696, 697, 554, 554, 554, 554, 
	554, 698, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	598, 600, 598, 598, 554, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	598, 598, 598, 598, 598, 554, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 599, 
	598, 599, 599, 599, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 599, 172, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	173, 598, 598, 174, 598, 172, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 699, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 598, 600, 598, 598, 554, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 598, 598, 598, 598, 598, 554, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	599, 598, 599, 599, 599, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 599, 
	172, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 173, 598, 598, 174, 598, 172, 598, 
	554, 554, 700, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 598, 600, 598, 598, 554, 598, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 598, 598, 598, 598, 598, 554, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 599, 598, 599, 599, 599, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	599, 172, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 173, 598, 598, 174, 598, 172, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 701, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 598, 600, 598, 598, 554, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 598, 598, 598, 598, 598, 
	554, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 599, 598, 599, 599, 599, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 599, 172, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 173, 598, 598, 174, 598, 
	172, 598, 554, 554, 554, 702, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 598, 600, 598, 598, 
	554, 598, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 598, 598, 598, 598, 
	598, 554, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 599, 598, 599, 599, 599, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 599, 172, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 173, 598, 598, 174, 
	598, 172, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 703, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 598, 600, 598, 
	598, 554, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 598, 598, 598, 
	598, 598, 554, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 599, 598, 599, 599, 
	599, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 599, 172, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 173, 598, 598, 
	174, 598, 172, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 704, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 598, 600, 
	598, 598, 554, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 598, 598, 
	598, 598, 598, 554, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 599, 598, 599, 
	599, 599, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 599, 172, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 173, 598, 
	598, 174, 598, 172, 598, 554, 554, 554, 
	554, 554, 554, 705, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 598, 
	600, 598, 598, 554, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 598, 
	598, 598, 598, 598, 554, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 599, 598, 
	599, 599, 599, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 599, 172, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 173, 
	598, 598, 174, 598, 172, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	598, 600, 598, 598, 706, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	598, 598, 598, 598, 598, 554, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 599, 
	598, 599, 599, 599, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 599, 172, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	173, 598, 598, 174, 598, 172, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 598, 600, 598, 598, 707, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 598, 598, 598, 598, 598, 554, 115, 
	115, 115, 115, 115, 115, 115, 115, 115, 
	115, 115, 115, 115, 115, 115, 115, 115, 
	115, 115, 115, 115, 115, 115, 115, 115, 
	115, 115, 115, 115, 115, 115, 115, 115, 
	172, 115, 115, 115, 115, 115, 115, 115, 
	115, 115, 115, 115, 115, 115, 115, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 173, 115, 115, 174, 115, 172, 115, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 115, 115, 115, 115, 554, 115, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 115, 115, 115, 115, 115, 554, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 599, 598, 599, 599, 599, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	599, 172, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 173, 598, 598, 174, 598, 172, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 708, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 598, 600, 598, 598, 554, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 598, 598, 598, 598, 598, 
	554, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 599, 598, 599, 599, 599, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 599, 172, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 173, 598, 598, 174, 598, 
	172, 598, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 709, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 598, 600, 598, 598, 
	554, 598, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 598, 598, 598, 598, 
	598, 554, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 599, 598, 599, 599, 599, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 599, 172, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 173, 598, 598, 174, 
	598, 172, 598, 554, 554, 554, 554, 705, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 598, 600, 598, 
	598, 554, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 598, 598, 598, 
	598, 598, 554, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 599, 598, 599, 599, 
	599, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 599, 172, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 173, 598, 598, 
	174, 598, 172, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 710, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 598, 600, 
	598, 598, 554, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 598, 598, 
	598, 598, 598, 554, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 599, 598, 599, 
	599, 599, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 599, 172, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 173, 598, 
	598, 174, 598, 172, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 709, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 598, 
	600, 598, 598, 554, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 598, 
	598, 598, 598, 598, 554, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 599, 598, 
	599, 599, 599, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 599, 172, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 173, 
	598, 598, 174, 598, 172, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	598, 600, 598, 598, 554, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 711, 554, 712, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	598, 598, 598, 598, 598, 554, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 599, 
	598, 599, 599, 599, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 599, 172, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	173, 598, 598, 174, 598, 172, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 598, 600, 598, 598, 554, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 713, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 598, 598, 598, 598, 598, 554, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	599, 598, 599, 599, 599, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 599, 
	172, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 173, 598, 598, 174, 598, 172, 598, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 598, 600, 598, 598, 554, 598, 
	714, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 598, 598, 598, 598, 598, 554, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 599, 598, 599, 599, 599, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	599, 172, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 173, 598, 598, 174, 598, 172, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 598, 600, 598, 598, 554, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 707, 554, 554, 554, 554, 
	554, 554, 554, 598, 598, 598, 598, 598, 
	554, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 599, 598, 599, 599, 599, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 599, 172, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 173, 598, 598, 174, 598, 
	172, 598, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 598, 600, 598, 598, 
	554, 598, 554, 554, 554, 707, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 598, 598, 598, 598, 
	598, 554, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 599, 598, 599, 599, 599, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 599, 172, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 173, 598, 598, 174, 
	598, 172, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 598, 600, 598, 
	598, 554, 598, 554, 554, 554, 554, 715, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 716, 554, 554, 554, 
	554, 554, 554, 554, 554, 598, 598, 598, 
	598, 598, 554, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 599, 598, 599, 599, 
	599, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 599, 172, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 173, 598, 598, 
	174, 598, 172, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 598, 600, 
	598, 598, 554, 598, 554, 554, 554, 554, 
	554, 554, 717, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 598, 598, 
	598, 598, 598, 554, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 599, 598, 599, 
	599, 599, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 599, 172, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 173, 598, 
	598, 174, 598, 172, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 598, 
	600, 598, 598, 554, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 718, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 598, 
	598, 598, 598, 598, 554, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 599, 598, 
	599, 599, 599, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 599, 172, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 173, 
	598, 598, 174, 598, 172, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	598, 600, 598, 598, 554, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 707, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	598, 598, 598, 598, 598, 554, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 599, 
	598, 599, 599, 599, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 599, 172, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	173, 598, 598, 174, 598, 172, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 598, 600, 598, 598, 554, 598, 554, 
	554, 554, 554, 719, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 598, 598, 598, 598, 598, 554, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	599, 598, 599, 599, 599, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 599, 
	172, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 173, 598, 598, 174, 598, 172, 598, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 598, 600, 598, 598, 554, 598, 
	720, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 598, 598, 598, 598, 598, 554, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 599, 598, 599, 599, 599, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	599, 172, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 173, 598, 598, 174, 598, 172, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 598, 600, 598, 598, 554, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 707, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 598, 598, 598, 598, 598, 
	554, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 599, 598, 599, 599, 599, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 599, 172, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 173, 598, 598, 174, 598, 
	172, 598, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 598, 600, 598, 598, 
	554, 598, 721, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 722, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 598, 598, 598, 598, 
	598, 554, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 599, 598, 599, 599, 599, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 599, 172, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 173, 598, 598, 174, 
	598, 172, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 598, 600, 598, 
	598, 554, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 723, 554, 554, 
	554, 554, 554, 554, 554, 598, 598, 598, 
	598, 598, 554, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 599, 598, 599, 599, 
	599, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 599, 172, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 173, 598, 598, 
	174, 598, 172, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 598, 600, 
	598, 598, 554, 598, 554, 554, 554, 554, 
	707, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 598, 598, 
	598, 598, 598, 554, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 599, 598, 599, 
	599, 599, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 599, 172, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 173, 598, 
	598, 174, 598, 172, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 598, 
	600, 598, 598, 554, 598, 724, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 598, 
	598, 598, 598, 598, 554, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 599, 598, 
	599, 599, 599, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 599, 172, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 173, 
	598, 598, 174, 598, 172, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	598, 600, 598, 598, 554, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	714, 554, 554, 554, 554, 554, 554, 554, 
	598, 598, 598, 598, 598, 554, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 599, 
	598, 599, 599, 599, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 599, 172, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	173, 598, 598, 174, 598, 172, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 598, 600, 598, 598, 554, 598, 554, 
	554, 554, 554, 725, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 707, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 598, 598, 598, 598, 598, 554, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	599, 598, 599, 599, 599, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 599, 
	172, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 173, 598, 598, 174, 598, 172, 598, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 598, 600, 598, 598, 554, 598, 
	554, 554, 554, 554, 554, 707, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 598, 598, 598, 598, 598, 554, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 599, 598, 599, 599, 599, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	599, 172, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 173, 598, 598, 174, 598, 172, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 598, 600, 598, 598, 554, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 726, 554, 727, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 598, 598, 598, 598, 598, 
	554, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 599, 598, 599, 599, 599, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 599, 172, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 173, 598, 598, 174, 598, 
	172, 598, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 598, 600, 598, 598, 
	554, 598, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 728, 554, 554, 554, 
	554, 554, 554, 554, 598, 598, 598, 598, 
	598, 554, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 599, 598, 599, 599, 599, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 599, 172, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 173, 598, 598, 174, 
	598, 172, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 598, 600, 598, 
	598, 554, 598, 554, 554, 554, 554, 707, 
	554, 554, 554, 725, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 598, 598, 598, 
	598, 598, 554, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 599, 598, 599, 599, 
	599, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 599, 172, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 173, 598, 598, 
	174, 598, 172, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 598, 600, 
	598, 598, 554, 598, 554, 554, 554, 707, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 729, 554, 
	554, 554, 554, 554, 554, 554, 598, 598, 
	598, 598, 598, 554, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 599, 598, 599, 
	599, 599, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 599, 172, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 173, 598, 
	598, 174, 598, 172, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 598, 
	600, 598, 598, 554, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 730, 554, 554, 554, 554, 554, 598, 
	598, 598, 598, 598, 554, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 599, 598, 
	599, 599, 599, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 599, 172, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 173, 
	598, 598, 174, 598, 172, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	598, 600, 598, 598, 554, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 723, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	598, 598, 598, 598, 598, 554, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 599, 
	598, 599, 599, 599, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 599, 172, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	173, 598, 598, 174, 598, 172, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 598, 600, 598, 598, 554, 598, 731, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 589, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 598, 598, 598, 598, 598, 554, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	599, 598, 599, 599, 599, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 599, 
	172, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 173, 598, 598, 174, 598, 172, 598, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 598, 600, 598, 598, 554, 598, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 721, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 598, 598, 598, 598, 598, 554, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 599, 598, 599, 599, 599, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	599, 172, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 173, 598, 598, 174, 598, 172, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 598, 600, 598, 598, 554, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 707, 554, 554, 554, 554, 554, 
	554, 554, 554, 598, 598, 598, 598, 598, 
	554, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 599, 598, 599, 599, 599, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 599, 172, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 173, 598, 598, 174, 598, 
	172, 598, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 598, 600, 598, 598, 
	554, 598, 554, 554, 554, 554, 554, 732, 
	554, 554, 554, 554, 554, 554, 554, 707, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 598, 598, 598, 598, 
	598, 554, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 599, 598, 599, 599, 599, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 599, 172, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 173, 598, 598, 174, 
	598, 172, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 598, 600, 598, 
	598, 554, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 733, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 598, 598, 598, 
	598, 598, 554, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 599, 598, 599, 599, 
	599, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 599, 172, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 173, 598, 598, 
	174, 598, 172, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 598, 600, 
	598, 598, 554, 598, 554, 554, 554, 734, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 598, 598, 
	598, 598, 598, 554, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 599, 598, 599, 
	599, 599, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 599, 172, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 173, 598, 
	598, 174, 598, 172, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 598, 
	600, 598, 598, 554, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 735, 554, 554, 554, 554, 554, 598, 
	598, 598, 598, 598, 554, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 599, 598, 
	599, 599, 599, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 599, 172, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 173, 
	598, 598, 174, 598, 172, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	598, 600, 598, 598, 554, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 723, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	598, 598, 598, 598, 598, 554, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 599, 
	598, 599, 599, 599, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 599, 172, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	173, 598, 598, 174, 598, 172, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 598, 600, 598, 598, 554, 598, 554, 
	554, 554, 554, 736, 554, 554, 554, 737, 
	554, 554, 554, 554, 554, 738, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 598, 598, 598, 598, 598, 554, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	599, 598, 599, 599, 599, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 599, 
	172, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 173, 598, 598, 174, 598, 172, 598, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 598, 600, 598, 598, 554, 598, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 738, 
	554, 554, 598, 598, 598, 598, 598, 554, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 599, 598, 599, 599, 599, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	599, 172, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 173, 598, 598, 174, 598, 172, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 598, 600, 598, 598, 554, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 707, 554, 554, 554, 
	554, 554, 554, 598, 598, 598, 598, 598, 
	554, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 599, 598, 599, 599, 599, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 599, 172, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 173, 598, 598, 174, 598, 
	172, 598, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 598, 600, 598, 598, 
	554, 598, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 707, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 598, 598, 598, 598, 
	598, 554, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 599, 598, 599, 599, 599, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 599, 172, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 173, 598, 598, 174, 
	598, 172, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 598, 600, 598, 
	598, 554, 598, 554, 554, 554, 554, 739, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 598, 598, 598, 
	598, 598, 554, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 599, 598, 599, 599, 
	599, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 599, 172, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 173, 598, 598, 
	174, 598, 172, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 598, 600, 
	598, 598, 554, 598, 554, 554, 554, 740, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 741, 742, 
	554, 554, 554, 554, 554, 554, 598, 598, 
	598, 598, 598, 554, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 599, 598, 599, 
	599, 599, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 599, 172, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 173, 598, 
	598, 174, 598, 172, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 598, 
	600, 598, 598, 554, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 707, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 598, 
	598, 598, 598, 598, 554, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 599, 598, 
	599, 599, 599, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 599, 172, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 173, 
	598, 598, 174, 598, 172, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	598, 600, 598, 598, 554, 598, 554, 554, 
	743, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	598, 598, 598, 598, 598, 554, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 599, 
	598, 599, 599, 599, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 599, 172, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	173, 598, 598, 174, 598, 172, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 598, 600, 598, 598, 554, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 744, 554, 554, 554, 554, 
	554, 598, 598, 598, 598, 598, 554, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	599, 598, 599, 599, 599, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 599, 
	172, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 173, 598, 598, 174, 598, 172, 598, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 598, 600, 598, 598, 554, 598, 
	554, 554, 554, 554, 745, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 598, 598, 598, 598, 598, 554, 
	746, 746, 746, 746, 746, 746, 746, 746, 
	746, 746, 746, 746, 746, 746, 746, 746, 
	746, 746, 746, 746, 746, 746, 746, 746, 
	746, 746, 746, 746, 746, 746, 746, 746, 
	746, 748, 746, 746, 746, 746, 746, 746, 
	746, 746, 746, 746, 746, 746, 746, 746, 
	747, 747, 747, 747, 747, 747, 747, 747, 
	747, 747, 749, 746, 746, 750, 746, 748, 
	746, 747, 747, 747, 747, 747, 747, 747, 
	747, 747, 747, 747, 747, 747, 747, 747, 
	747, 747, 747, 747, 747, 747, 747, 747, 
	747, 747, 747, 746, 746, 746, 746, 747, 
	746, 747, 747, 747, 747, 747, 747, 747, 
	747, 747, 747, 747, 747, 747, 747, 747, 
	747, 747, 747, 747, 747, 747, 747, 747, 
	747, 747, 747, 746, 746, 746, 746, 746, 
	747, 606, 751, 605, 605, 605, 605, 605, 
	605, 605, 605, 605, 605, 605, 605, 605, 
	605, 605, 605, 605, 605, 605, 605, 605, 
	605, 605, 605, 605, 605, 605, 605, 605, 
	605, 605, 605, 605, 605, 605, 605, 605, 
	605, 605, 605, 605, 605, 605, 605, 605, 
	605, 605, 605, 605, 605, 605, 605, 605, 
	605, 605, 605, 605, 605, 605, 605, 605, 
	605, 605, 608, 605, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 599, 598, 599, 
	599, 599, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 599, 172, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 173, 598, 
	598, 174, 598, 172, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 598, 
	600, 598, 598, 554, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 752, 554, 
	554, 753, 554, 554, 554, 554, 554, 598, 
	598, 598, 598, 598, 554, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 599, 598, 
	599, 599, 599, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 599, 172, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 173, 
	598, 598, 174, 598, 172, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	598, 600, 598, 598, 554, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 707, 554, 
	598, 598, 598, 598, 598, 554, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 599, 
	598, 599, 599, 599, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 599, 172, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	173, 598, 598, 174, 598, 172, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 598, 600, 598, 598, 554, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	718, 554, 554, 554, 554, 554, 554, 554, 
	554, 598, 598, 598, 598, 598, 554, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	599, 598, 599, 599, 599, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 599, 
	172, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 173, 598, 598, 174, 598, 172, 598, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 598, 600, 598, 598, 554, 598, 
	554, 554, 554, 554, 754, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 755, 554, 554, 554, 
	554, 554, 598, 598, 598, 598, 598, 554, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 599, 598, 599, 599, 599, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	599, 172, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 173, 598, 598, 174, 598, 172, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 598, 600, 598, 598, 554, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 725, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 598, 598, 598, 598, 598, 
	554, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 599, 598, 599, 599, 599, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 599, 172, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 173, 598, 598, 174, 598, 
	172, 598, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 598, 600, 598, 598, 
	554, 598, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 756, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 598, 598, 598, 598, 
	598, 554, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 599, 598, 599, 599, 599, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 599, 172, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 173, 598, 598, 174, 
	598, 172, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 598, 600, 598, 
	598, 554, 598, 554, 554, 554, 554, 589, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 598, 598, 598, 
	598, 598, 554, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 599, 598, 599, 599, 
	599, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 599, 172, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 173, 598, 598, 
	174, 598, 172, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 598, 600, 
	598, 598, 554, 598, 554, 554, 554, 554, 
	554, 554, 554, 757, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 758, 554, 554, 
	554, 554, 554, 554, 554, 554, 598, 598, 
	598, 598, 598, 554, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 599, 598, 599, 
	599, 599, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 599, 172, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 173, 598, 
	598, 174, 598, 172, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 598, 
	600, 598, 598, 554, 598, 554, 554, 554, 
	554, 718, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 598, 
	598, 598, 598, 598, 554, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 599, 598, 
	599, 599, 599, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 599, 172, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 173, 
	598, 598, 174, 598, 172, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	598, 600, 598, 598, 554, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 723, 554, 554, 554, 554, 554, 
	598, 598, 598, 598, 598, 554, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 599, 
	598, 599, 599, 599, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 599, 172, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	173, 598, 598, 174, 598, 172, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 598, 600, 598, 598, 554, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 759, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 598, 598, 598, 598, 598, 554, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	599, 598, 599, 599, 599, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 599, 
	172, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 173, 598, 598, 174, 598, 172, 598, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 598, 600, 598, 598, 554, 598, 
	554, 554, 554, 760, 554, 554, 554, 554, 
	554, 554, 554, 761, 554, 554, 554, 554, 
	554, 554, 554, 762, 554, 554, 554, 554, 
	554, 554, 598, 598, 598, 598, 598, 554, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 599, 598, 599, 599, 599, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	599, 172, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 173, 598, 598, 174, 598, 172, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 598, 600, 598, 598, 554, 
	598, 554, 554, 554, 554, 725, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 598, 598, 598, 598, 598, 
	554, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 599, 598, 599, 599, 599, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 599, 172, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 173, 598, 598, 174, 598, 
	172, 598, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 598, 600, 598, 598, 
	554, 598, 554, 554, 554, 554, 763, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 598, 598, 598, 598, 
	598, 554, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 599, 598, 599, 599, 599, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 599, 172, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 173, 598, 598, 174, 
	598, 172, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 598, 600, 598, 
	598, 554, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 764, 554, 554, 
	554, 554, 554, 554, 554, 598, 598, 598, 
	598, 598, 554, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 599, 598, 599, 599, 
	599, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 599, 172, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 173, 598, 598, 
	174, 598, 172, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 598, 600, 
	598, 598, 554, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 732, 554, 
	554, 554, 554, 554, 554, 554, 598, 598, 
	598, 598, 598, 554, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 599, 598, 599, 
	599, 599, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 599, 172, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 173, 598, 
	598, 174, 598, 172, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 598, 
	600, 598, 598, 554, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 765, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 598, 
	598, 598, 598, 598, 554, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 599, 598, 
	599, 599, 599, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 599, 172, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 173, 
	598, 598, 174, 598, 172, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	598, 600, 598, 598, 554, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 732, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	598, 598, 598, 598, 598, 554, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 599, 
	598, 599, 599, 599, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 599, 172, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	173, 598, 598, 174, 598, 172, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 598, 600, 598, 598, 554, 598, 554, 
	554, 554, 554, 554, 554, 554, 766, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 598, 598, 598, 598, 598, 554, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	599, 598, 599, 599, 599, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 599, 
	172, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 173, 598, 598, 174, 598, 172, 598, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 598, 600, 598, 598, 554, 598, 
	554, 554, 554, 554, 718, 554, 554, 554, 
	767, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 598, 598, 598, 598, 598, 554, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 599, 598, 599, 599, 599, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	599, 172, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 173, 598, 598, 174, 598, 172, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 598, 600, 598, 598, 554, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 768, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 598, 598, 598, 598, 598, 
	554, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 599, 598, 599, 599, 599, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 599, 172, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 173, 598, 598, 174, 598, 
	172, 598, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 598, 600, 598, 598, 
	554, 598, 554, 554, 554, 554, 732, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 598, 598, 598, 598, 
	598, 554, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 599, 598, 599, 599, 599, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 599, 172, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 173, 598, 598, 174, 
	598, 172, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 598, 600, 598, 
	598, 554, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 769, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 598, 598, 598, 
	598, 598, 554, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 599, 598, 599, 599, 
	599, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 599, 172, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 173, 598, 598, 
	174, 598, 172, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 598, 600, 
	598, 598, 554, 598, 554, 554, 554, 554, 
	770, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 598, 598, 
	598, 598, 598, 554, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 599, 598, 599, 
	599, 599, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 599, 172, 598, 598, 
	598, 598, 598, 598, 598, 598, 598, 598, 
	598, 598, 598, 598, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 173, 598, 
	598, 174, 598, 172, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 598, 
	600, 598, 598, 554, 598, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 554, 
	712, 554, 554, 554, 554, 554, 554, 554, 
	554, 554, 554, 554, 554, 554, 554, 598, 
	598, 598, 598, 598, 554, 771, 125, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 620, 620, 620, 
	620, 620, 620, 620, 620, 772, 620, 774, 
	773, 773, 773, 774, 773, 773, 773, 773, 
	775, 776, 775, 775, 775, 773, 773, 773, 
	773, 773, 773, 773, 773, 773, 773, 773, 
	773, 774, 773, 773, 773, 773, 773, 775, 
	773, 773, 777, 773, 773, 773, 773, 773, 
	773, 773, 773, 773, 773, 773, 773, 773, 
	773, 773, 773, 773, 773, 773, 773, 773, 
	773, 773, 773, 773, 773, 773, 773, 773, 
	773, 773, 773, 773, 773, 773, 773, 773, 
	773, 773, 773, 773, 773, 773, 773, 773, 
	773, 773, 773, 773, 773, 773, 773, 773, 
	773, 773, 773, 778, 773, 775, 779, 775, 
	775, 775, 779, 779, 779, 779, 779, 779, 
	779, 779, 779, 779, 779, 779, 779, 779, 
	779, 779, 779, 779, 775, 779, 780, 781, 
	782, 783, 784, 786, 785, 787, 789, 790, 
	790, 790, 789, 790, 790, 790, 790, 791, 
	792, 791, 791, 791, 790, 790, 790, 790, 
	790, 790, 790, 790, 790, 790, 790, 790, 
	789, 790, 790, 790, 790, 790, 791, 790, 
	793, 794, 790, 790, 790, 793, 790, 790, 
	790, 790, 790, 790, 790, 790, 790, 790, 
	790, 790, 790, 790, 790, 790, 790, 790, 
	790, 790, 790, 790, 790, 790, 790, 788, 
	788, 788, 788, 788, 788, 788, 788, 788, 
	788, 788, 788, 788, 788, 788, 788, 788, 
	788, 788, 788, 788, 788, 788, 788, 788, 
	788, 790, 795, 790, 790, 788, 790, 788, 
	788, 788, 788, 788, 788, 788, 788, 788, 
	788, 788, 788, 788, 788, 788, 788, 788, 
	788, 788, 788, 788, 788, 788, 788, 788, 
	788, 790, 790, 790, 790, 790, 788, 796, 
	796, 796, 796, 796, 796, 796, 796, 796, 
	796, 796, 796, 796, 796, 796, 796, 796, 
	796, 796, 796, 796, 796, 796, 796, 796, 
	796, 796, 796, 796, 796, 796, 796, 796, 
	180, 796, 796, 796, 796, 796, 796, 796, 
	796, 796, 796, 796, 796, 796, 796, 179, 
	179, 179, 179, 179, 179, 179, 179, 179, 
	179, 181, 796, 796, 796, 796, 180, 796, 
	179, 179, 179, 179, 179, 179, 179, 179, 
	179, 179, 179, 179, 179, 179, 179, 179, 
	179, 179, 179, 179, 179, 179, 179, 179, 
	179, 179, 796, 796, 796, 796, 179, 796, 
	179, 179, 179, 179, 179, 179, 179, 179, 
	179, 179, 179, 179, 179, 179, 179, 179, 
	179, 179, 179, 179, 179, 179, 179, 179, 
	179, 179, 796, 796, 796, 796, 796, 179, 
	791, 797, 791, 791, 791, 797, 797, 797, 
	797, 797, 797, 797, 797, 797, 797, 797, 
	797, 797, 797, 797, 797, 797, 797, 791, 
	797, 798, 799, 800, 801, 802, 803, 796, 
	804, 806, 807, 807, 807, 806, 807, 807, 
	807, 807, 808, 809, 808, 808, 808, 807, 
	807, 807, 807, 807, 807, 807, 807, 807, 
	807, 807, 807, 806, 807, 807, 807, 807, 
	807, 808, 810, 811, 812, 813, 814, 815, 
	811, 816, 817, 818, 814, 819, 820, 821, 
	814, 822, 823, 823, 823, 823, 823, 823, 
	823, 823, 823, 824, 825, 826, 827, 828, 
	829, 830, 831, 832, 831, 831, 833, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 834, 835, 836, 814, 
	837, 811, 838, 839, 840, 841, 842, 843, 
	805, 805, 844, 805, 805, 805, 845, 846, 
	847, 805, 805, 848, 849, 850, 851, 805, 
	852, 805, 853, 805, 854, 855, 856, 857, 
	807, 805, 189, 189, 189, 189, 189, 189, 
	189, 189, 189, 189, 189, 189, 189, 189, 
	189, 189, 189, 189, 189, 189, 189, 189, 
	189, 189, 189, 189, 189, 189, 189, 189, 
	189, 189, 189, 858, 189, 189, 189, 189, 
	189, 189, 189, 189, 189, 189, 189, 189, 
	189, 189, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 189, 189, 189, 189, 
	189, 858, 189, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 189, 189, 189, 
	189, 805, 189, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 189, 189, 189, 
	189, 189, 805, 860, 859, 861, 808, 862, 
	808, 808, 808, 862, 862, 862, 862, 862, 
	862, 862, 862, 862, 862, 862, 862, 862, 
	862, 862, 862, 862, 862, 808, 862, 863, 
	865, 864, 864, 864, 864, 864, 864, 864, 
	864, 864, 864, 864, 864, 864, 864, 864, 
	864, 864, 864, 864, 864, 864, 864, 864, 
	864, 864, 864, 864, 864, 864, 864, 864, 
	864, 864, 864, 864, 864, 864, 864, 864, 
	864, 864, 864, 864, 864, 864, 864, 864, 
	864, 864, 864, 864, 864, 864, 864, 864, 
	864, 864, 864, 864, 864, 864, 864, 864, 
	864, 865, 864, 866, 867, 868, 869, 870, 
	870, 870, 870, 870, 870, 870, 870, 870, 
	870, 870, 870, 870, 870, 870, 870, 870, 
	870, 870, 870, 870, 870, 870, 870, 870, 
	870, 870, 870, 870, 870, 870, 870, 870, 
	184, 184, 870, 184, 870, 184, 184, 870, 
	870, 184, 184, 184, 871, 184, 184, 872, 
	872, 872, 872, 872, 872, 872, 872, 872, 
	872, 184, 184, 184, 184, 184, 184, 184, 
	201, 201, 201, 201, 201, 201, 201, 201, 
	201, 201, 201, 201, 201, 201, 201, 201, 
	201, 201, 201, 201, 201, 201, 201, 201, 
	201, 201, 870, 184, 870, 870, 201, 184, 
	201, 201, 201, 201, 201, 201, 201, 201, 
	201, 201, 201, 201, 201, 201, 201, 201, 
	201, 201, 201, 201, 201, 201, 201, 201, 
	201, 201, 870, 870, 870, 184, 870, 201, 
	873, 873, 873, 873, 873, 873, 873, 873, 
	873, 873, 873, 873, 873, 873, 873, 873, 
	873, 873, 873, 873, 873, 873, 873, 873, 
	873, 873, 873, 873, 873, 873, 873, 873, 
	873, 873, 873, 873, 873, 873, 873, 873, 
	873, 873, 873, 873, 873, 873, 873, 873, 
	201, 201, 201, 201, 201, 201, 201, 201, 
	201, 201, 873, 873, 873, 873, 873, 873, 
	873, 201, 201, 201, 201, 201, 201, 201, 
	201, 201, 201, 201, 201, 201, 201, 201, 
	201, 201, 201, 201, 201, 201, 201, 201, 
	201, 201, 201, 873, 873, 873, 873, 201, 
	873, 201, 201, 201, 201, 201, 201, 201, 
	201, 201, 201, 201, 201, 201, 201, 201, 
	201, 201, 201, 201, 201, 201, 201, 201, 
	201, 201, 201, 873, 873, 873, 873, 873, 
	201, 872, 872, 872, 872, 872, 872, 872, 
	872, 872, 872, 873, 874, 189, 814, 875, 
	875, 875, 875, 875, 875, 875, 876, 875, 
	875, 875, 875, 875, 875, 875, 875, 875, 
	875, 875, 875, 875, 875, 874, 875, 877, 
	878, 814, 879, 879, 879, 879, 879, 879, 
	879, 879, 879, 879, 879, 879, 879, 879, 
	879, 879, 879, 879, 874, 879, 874, 880, 
	875, 882, 881, 188, 188, 188, 188, 188, 
	188, 188, 188, 188, 188, 881, 884, 883, 
	885, 883, 188, 188, 188, 188, 188, 188, 
	188, 188, 188, 188, 886, 886, 886, 886, 
	886, 886, 886, 886, 886, 886, 886, 887, 
	886, 886, 886, 886, 886, 886, 886, 886, 
	886, 886, 886, 886, 886, 886, 886, 886, 
	886, 886, 886, 886, 886, 886, 886, 886, 
	886, 888, 886, 886, 886, 886, 886, 887, 
	886, 187, 187, 187, 187, 187, 187, 187, 
	187, 187, 187, 886, 886, 886, 886, 886, 
	886, 886, 886, 886, 886, 886, 886, 886, 
	886, 886, 886, 886, 886, 886, 886, 886, 
	886, 886, 886, 886, 886, 886, 886, 886, 
	886, 886, 886, 886, 886, 886, 886, 886, 
	186, 886, 890, 889, 891, 891, 891, 891, 
	891, 891, 891, 891, 891, 891, 889, 889, 
	889, 889, 889, 889, 889, 889, 892, 889, 
	893, 894, 889, 889, 889, 889, 889, 889, 
	889, 889, 889, 895, 889, 889, 889, 889, 
	889, 889, 889, 889, 896, 889, 889, 889, 
	889, 889, 889, 897, 889, 889, 892, 889, 
	893, 894, 889, 889, 889, 898, 889, 889, 
	889, 889, 889, 895, 889, 889, 899, 889, 
	889, 889, 889, 889, 896, 889, 190, 190, 
	190, 190, 190, 190, 190, 190, 190, 190, 
	900, 900, 900, 900, 900, 900, 900, 900, 
	900, 900, 900, 901, 900, 900, 900, 900, 
	900, 900, 900, 900, 900, 900, 900, 900, 
	900, 900, 900, 900, 900, 900, 900, 900, 
	900, 900, 900, 900, 900, 902, 900, 900, 
	900, 900, 900, 901, 900, 900, 900, 903, 
	900, 900, 900, 900, 900, 900, 900, 900, 
	904, 900, 905, 189, 905, 189, 189, 191, 
	191, 191, 191, 191, 191, 191, 191, 191, 
	191, 189, 191, 191, 191, 191, 191, 191, 
	191, 191, 191, 191, 906, 906, 906, 906, 
	906, 906, 906, 906, 906, 906, 906, 906, 
	906, 906, 906, 906, 906, 906, 906, 906, 
	906, 906, 906, 906, 906, 906, 906, 906, 
	906, 906, 906, 906, 906, 906, 906, 906, 
	906, 905, 906, 906, 906, 906, 906, 906, 
	906, 906, 906, 907, 906, 909, 908, 910, 
	912, 911, 911, 911, 913, 911, 914, 915, 
	891, 891, 891, 891, 891, 891, 891, 891, 
	891, 891, 889, 889, 889, 889, 889, 889, 
	889, 889, 889, 889, 889, 889, 889, 889, 
	889, 889, 889, 889, 889, 889, 889, 889, 
	889, 889, 889, 889, 889, 889, 889, 889, 
	889, 889, 889, 889, 889, 889, 889, 897, 
	889, 889, 889, 889, 889, 889, 889, 889, 
	889, 898, 889, 889, 889, 889, 889, 889, 
	889, 889, 899, 889, 916, 916, 916, 916, 
	916, 916, 916, 916, 916, 916, 889, 889, 
	889, 889, 889, 889, 889, 889, 889, 889, 
	889, 889, 889, 889, 889, 889, 889, 889, 
	889, 889, 889, 889, 889, 889, 889, 889, 
	889, 889, 889, 889, 889, 889, 889, 889, 
	889, 889, 889, 917, 889, 889, 889, 889, 
	889, 889, 889, 889, 889, 898, 889, 889, 
	889, 889, 889, 889, 889, 889, 899, 889, 
	919, 919, 919, 919, 919, 919, 919, 919, 
	919, 919, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 920, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 921, 918, 918, 918, 918, 918, 918, 
	918, 918, 922, 918, 919, 919, 919, 919, 
	919, 919, 919, 919, 919, 919, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 923, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 921, 918, 918, 
	918, 918, 918, 918, 918, 918, 922, 918, 
	921, 918, 918, 918, 918, 918, 918, 918, 
	918, 922, 918, 925, 924, 926, 928, 927, 
	927, 927, 929, 927, 931, 930, 932, 933, 
	935, 935, 934, 934, 934, 934, 934, 934, 
	934, 934, 934, 934, 934, 934, 934, 934, 
	934, 934, 934, 934, 934, 934, 934, 934, 
	934, 934, 934, 934, 934, 934, 934, 934, 
	934, 934, 934, 934, 934, 934, 934, 934, 
	934, 934, 934, 934, 934, 934, 934, 936, 
	934, 934, 934, 934, 934, 934, 934, 934, 
	934, 937, 934, 934, 934, 934, 934, 934, 
	934, 934, 938, 934, 939, 939, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 940, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 921, 918, 918, 
	918, 918, 918, 918, 918, 918, 922, 918, 
	939, 939, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 923, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 921, 918, 918, 918, 918, 918, 918, 
	918, 918, 922, 918, 942, 942, 942, 942, 
	942, 942, 942, 942, 942, 942, 941, 941, 
	941, 941, 941, 941, 941, 941, 941, 941, 
	941, 941, 941, 941, 941, 941, 941, 941, 
	941, 941, 941, 941, 941, 941, 941, 941, 
	941, 941, 941, 941, 941, 941, 941, 941, 
	941, 941, 941, 943, 941, 941, 941, 941, 
	941, 941, 941, 941, 941, 944, 941, 941, 
	941, 941, 941, 941, 941, 941, 945, 941, 
	947, 947, 947, 947, 947, 947, 947, 947, 
	947, 947, 946, 946, 946, 946, 946, 946, 
	946, 946, 946, 946, 946, 946, 946, 946, 
	946, 946, 946, 946, 946, 946, 946, 946, 
	946, 946, 946, 946, 946, 946, 946, 946, 
	946, 946, 946, 946, 946, 946, 946, 948, 
	946, 946, 946, 946, 946, 946, 946, 946, 
	946, 949, 946, 946, 946, 946, 946, 946, 
	946, 946, 950, 946, 952, 952, 952, 952, 
	952, 952, 952, 952, 952, 952, 951, 951, 
	951, 951, 951, 951, 951, 952, 952, 952, 
	952, 952, 952, 951, 951, 951, 951, 951, 
	951, 951, 951, 951, 951, 951, 951, 951, 
	951, 951, 951, 951, 951, 951, 951, 951, 
	951, 951, 951, 953, 951, 952, 952, 952, 
	952, 952, 952, 951, 951, 954, 951, 951, 
	951, 951, 951, 951, 951, 951, 955, 951, 
	956, 956, 956, 956, 956, 956, 956, 956, 
	956, 956, 918, 918, 918, 918, 918, 918, 
	918, 956, 956, 956, 956, 956, 956, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 957, 
	918, 956, 956, 956, 956, 956, 956, 918, 
	918, 921, 918, 918, 918, 918, 918, 918, 
	918, 918, 922, 918, 956, 956, 956, 956, 
	956, 956, 956, 956, 956, 956, 918, 918, 
	918, 918, 918, 918, 918, 956, 956, 956, 
	956, 956, 956, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 923, 918, 956, 956, 956, 
	956, 956, 956, 918, 918, 921, 918, 918, 
	918, 918, 918, 918, 918, 918, 922, 918, 
	959, 958, 960, 960, 960, 960, 960, 960, 
	960, 960, 960, 960, 958, 958, 958, 958, 
	958, 958, 958, 958, 958, 958, 958, 961, 
	958, 958, 958, 958, 958, 958, 958, 958, 
	958, 958, 958, 958, 958, 958, 958, 958, 
	958, 958, 958, 958, 958, 958, 958, 958, 
	958, 962, 958, 958, 958, 958, 958, 961, 
	958, 958, 958, 963, 958, 958, 958, 958, 
	958, 958, 958, 958, 964, 958, 965, 965, 
	965, 965, 965, 965, 965, 965, 965, 965, 
	958, 958, 958, 958, 958, 958, 958, 958, 
	958, 958, 958, 958, 958, 958, 958, 958, 
	958, 958, 958, 958, 958, 958, 958, 958, 
	958, 958, 958, 958, 958, 958, 958, 958, 
	958, 958, 958, 958, 958, 966, 958, 958, 
	958, 958, 958, 958, 958, 958, 958, 963, 
	958, 958, 958, 958, 958, 958, 958, 958, 
	964, 958, 967, 918, 968, 968, 968, 968, 
	968, 968, 968, 968, 968, 968, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 969, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 970, 918, 918, 918, 918, 
	918, 969, 918, 918, 918, 921, 918, 918, 
	918, 918, 918, 918, 918, 918, 922, 918, 
	968, 968, 968, 968, 968, 968, 968, 968, 
	968, 968, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 918, 918, 918, 918, 918, 918, 923, 
	918, 918, 918, 918, 918, 918, 918, 918, 
	918, 921, 918, 918, 918, 918, 918, 918, 
	918, 918, 922, 918, 876, 883, 814, 971, 
	875, 865, 875, 972, 973, 883, 883, 883, 
	883, 883, 883, 883, 883, 883, 883, 883, 
	883, 883, 883, 883, 883, 883, 883, 883, 
	883, 883, 883, 883, 883, 883, 883, 883, 
	883, 883, 883, 883, 883, 883, 883, 883, 
	883, 883, 883, 883, 883, 883, 883, 883, 
	883, 883, 883, 883, 883, 883, 883, 883, 
	883, 883, 883, 883, 883, 883, 883, 883, 
	883, 883, 883, 883, 865, 883, 865, 875, 
	865, 814, 875, 870, 870, 870, 870, 870, 
	870, 870, 870, 870, 870, 870, 870, 870, 
	870, 870, 870, 870, 870, 870, 870, 870, 
	870, 870, 870, 870, 870, 870, 870, 870, 
	870, 870, 870, 870, 870, 870, 870, 870, 
	870, 870, 870, 870, 870, 870, 870, 870, 
	870, 870, 870, 201, 201, 201, 201, 201, 
	201, 201, 201, 201, 201, 870, 870, 870, 
	870, 870, 870, 974, 201, 201, 201, 201, 
	201, 201, 201, 201, 201, 201, 201, 201, 
	201, 201, 201, 201, 201, 201, 201, 201, 
	201, 201, 201, 201, 201, 201, 870, 870, 
	870, 870, 201, 870, 201, 201, 201, 201, 
	201, 201, 201, 201, 201, 201, 201, 201, 
	201, 201, 201, 201, 201, 201, 201, 201, 
	201, 201, 201, 201, 201, 201, 870, 870, 
	870, 870, 870, 201, 189, 189, 189, 189, 
	189, 189, 189, 189, 189, 189, 189, 189, 
	189, 189, 189, 189, 189, 189, 189, 189, 
	189, 189, 189, 189, 189, 189, 189, 189, 
	189, 189, 189, 189, 189, 858, 189, 189, 
	189, 189, 189, 189, 189, 189, 189, 189, 
	189, 189, 189, 189, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 975, 189, 
	189, 189, 189, 858, 189, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 189, 
	189, 189, 189, 831, 189, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 189, 
	189, 189, 189, 189, 831, 976, 977, 977, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	977, 977, 977, 977, 977, 977, 977, 858, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	977, 977, 977, 977, 977, 977, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	975, 977, 977, 977, 977, 858, 977, 831, 
	831, 831, 831, 978, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 977, 977, 977, 977, 831, 977, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 977, 977, 977, 977, 977, 831, 977, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	858, 977, 977, 977, 977, 977, 977, 977, 
	977, 977, 977, 977, 977, 977, 977, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 975, 977, 977, 977, 977, 858, 977, 
	831, 831, 831, 831, 831, 831, 979, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 977, 977, 977, 977, 831, 977, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 977, 977, 977, 977, 977, 831, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	977, 858, 977, 977, 977, 977, 977, 977, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 975, 977, 977, 977, 977, 858, 
	977, 831, 831, 831, 831, 831, 831, 831, 
	831, 980, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 977, 977, 977, 977, 831, 
	977, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 977, 977, 977, 977, 977, 
	831, 977, 977, 977, 977, 977, 977, 977, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	977, 977, 858, 977, 977, 977, 977, 977, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	977, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 975, 977, 977, 977, 977, 
	858, 977, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 981, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 977, 977, 977, 977, 
	831, 977, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 977, 977, 977, 977, 
	977, 831, 977, 977, 977, 977, 977, 977, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	977, 977, 977, 858, 977, 977, 977, 977, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	977, 977, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 975, 977, 977, 977, 
	977, 858, 977, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	982, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 977, 977, 977, 
	977, 831, 977, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 977, 977, 977, 
	977, 977, 831, 977, 977, 977, 977, 977, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	977, 977, 977, 977, 858, 977, 977, 977, 
	977, 977, 977, 977, 977, 977, 977, 977, 
	977, 977, 977, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 975, 977, 977, 
	977, 977, 858, 977, 831, 831, 831, 981, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 977, 977, 
	977, 977, 831, 977, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 831, 831, 
	831, 831, 831, 831, 831, 831, 977, 977, 
	977, 977, 977, 831, 983, 985, 984, 986, 
	987, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 858, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	858, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	989, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 805, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 858, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 858, 988, 805, 805, 805, 805, 990, 
	991, 805, 805, 805, 805, 805, 992, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 805, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 805, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 858, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 858, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 993, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 805, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 805, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 858, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 858, 988, 805, 805, 994, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 805, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 988, 805, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 858, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 988, 858, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 995, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 805, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 988, 805, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 858, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 988, 858, 988, 805, 
	805, 805, 996, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 805, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 988, 805, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	858, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 988, 858, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	997, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 805, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 988, 805, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 858, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 988, 858, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 998, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 805, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	805, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 858, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	858, 988, 805, 805, 805, 805, 805, 805, 
	999, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	805, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 805, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 858, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 858, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 1000, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 805, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 858, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 858, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 1001, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 805, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 858, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 858, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 1002, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 805, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 988, 805, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 858, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 988, 858, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 1003, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 805, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 988, 805, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 858, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 988, 858, 988, 805, 
	805, 805, 805, 1004, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 805, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 988, 805, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	858, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 988, 858, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 1005, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 988, 805, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 858, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 988, 858, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 1006, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	805, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 858, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	858, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 1007, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	805, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 805, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 858, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 858, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	1003, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 805, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 805, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 858, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 858, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 805, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 1008, 
	805, 1009, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 805, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 858, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 858, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 805, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 1010, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 988, 805, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 858, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 988, 858, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 805, 988, 1011, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 988, 805, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 858, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 988, 858, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 805, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 1012, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 988, 805, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	858, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 988, 858, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 805, 988, 
	805, 805, 805, 1013, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 988, 805, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 858, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 988, 858, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 805, 
	988, 805, 805, 805, 805, 1014, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 1015, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	805, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 858, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	858, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	805, 988, 805, 805, 805, 805, 805, 805, 
	1016, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 805, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 858, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 858, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 805, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 1017, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 805, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 858, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 858, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 805, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 1013, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 805, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 858, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 858, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 805, 988, 805, 805, 805, 
	805, 1018, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 988, 805, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 858, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 988, 858, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 805, 988, 1019, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 988, 805, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 858, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 988, 858, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 805, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 1020, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 988, 805, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	858, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 988, 858, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 805, 988, 
	1021, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 1022, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 988, 805, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 858, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 988, 858, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 805, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 1023, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	805, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 858, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	858, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	805, 988, 805, 805, 805, 805, 1013, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 805, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 858, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 858, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 805, 988, 1024, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 805, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 858, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 858, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 805, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 1025, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 805, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 858, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 858, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 805, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 1026, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 988, 805, 1027, 1027, 1027, 
	1027, 1027, 1027, 1027, 1027, 1027, 203, 204, 
	203, 203, 203, 1027, 1027, 1027, 1027, 1027, 
	1027, 1027, 1027, 1027, 1027, 1027, 1027, 1027, 
	1027, 1027, 1027, 1027, 1027, 203, 858, 1027, 
	205, 1027, 1027, 1027, 1027, 1027, 1027, 1027, 
	1027, 1027, 1027, 1027, 1027, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 1027, 
	1027, 206, 1027, 1027, 858, 1027, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	1027, 207, 1027, 1027, 805, 1027, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	1027, 1027, 1027, 1027, 1027, 805, 220, 221, 
	220, 220, 220, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 220, 219, 219, 
	222, 219, 219, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	219, 225, 219, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	219, 219, 219, 219, 219, 219, 219, 219, 
	219, 224, 219, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 858, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 858, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 805, 988, 805, 805, 805, 805, 
	1029, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 1030, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 805, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 858, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 858, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 805, 988, 805, 805, 805, 
	805, 805, 1031, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 988, 805, 1032, 1032, 1032, 
	1032, 1032, 1032, 1032, 1032, 1032, 1032, 1032, 
	1032, 1032, 1032, 1032, 1032, 1032, 1032, 1032, 
	1032, 1032, 1032, 1032, 1032, 1032, 1032, 1032, 
	1032, 1032, 1032, 1032, 1032, 1032, 858, 1032, 
	1032, 1032, 1032, 1032, 1032, 1032, 1032, 1032, 
	1032, 1032, 1032, 1032, 1032, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 1032, 
	1032, 1032, 1032, 1032, 858, 1032, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	1032, 1032, 1032, 1032, 805, 1032, 805, 805, 
	805, 805, 805, 805, 805, 805, 1033, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	1032, 1032, 1032, 1032, 1032, 805, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 858, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 988, 858, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 805, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 1034, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 988, 805, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	858, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 988, 858, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 805, 988, 
	805, 805, 805, 805, 1035, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 988, 805, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 858, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 988, 858, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 805, 
	988, 805, 805, 805, 1036, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	805, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 858, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	1037, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	805, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 805, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 858, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 858, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 805, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 1038, 805, 
	1039, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 805, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 858, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 858, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 805, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 1040, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 805, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 858, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 858, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 805, 988, 805, 805, 805, 
	805, 1013, 805, 805, 805, 1041, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 988, 805, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 858, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 988, 858, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 805, 988, 805, 805, 
	805, 805, 805, 1013, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 988, 805, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 858, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 988, 858, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 805, 988, 805, 
	805, 805, 1006, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 1042, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 988, 805, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	858, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 988, 858, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 805, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 1043, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 988, 805, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 858, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 988, 858, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 805, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 1023, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	805, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 858, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	858, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	805, 988, 1044, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	847, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 805, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 858, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 858, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 805, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 1045, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 805, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 858, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 858, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 805, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 1046, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 805, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 858, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 858, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 805, 988, 805, 805, 805, 
	805, 1006, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 988, 805, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 858, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 988, 858, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 805, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 1013, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 988, 805, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 858, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 988, 858, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 805, 988, 805, 
	805, 805, 805, 805, 1047, 805, 805, 805, 
	805, 805, 805, 805, 1013, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 988, 805, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	858, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 988, 858, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 805, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 1048, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 988, 805, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 858, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 988, 858, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 805, 
	988, 805, 805, 805, 1049, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	805, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 858, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	858, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	805, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 1050, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 805, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 858, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 858, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 805, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 1023, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 805, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 858, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 858, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 805, 988, 805, 805, 805, 805, 
	1051, 805, 805, 805, 1052, 805, 805, 805, 
	805, 805, 1053, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 805, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 858, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 858, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 805, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 1054, 805, 805, 988, 
	988, 988, 988, 988, 805, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 858, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 988, 858, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 805, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 1020, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 988, 805, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 858, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 988, 858, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 805, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 1006, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 988, 805, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	858, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 988, 858, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 805, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 1055, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 988, 805, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 858, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 988, 858, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 805, 
	988, 805, 805, 805, 805, 1056, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	805, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 858, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	858, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	805, 988, 805, 805, 805, 1057, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 1058, 1059, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 805, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 858, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 858, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 805, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 1006, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 805, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 858, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 858, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 805, 988, 805, 805, 1060, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 805, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 858, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 858, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 805, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 1061, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 988, 805, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 858, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 988, 858, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 805, 988, 805, 805, 
	805, 805, 1047, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 988, 805, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 858, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 988, 858, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 805, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	1062, 805, 805, 1063, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 988, 805, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	858, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 988, 858, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 805, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	1006, 805, 988, 988, 988, 988, 988, 805, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 858, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 988, 858, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 805, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 1064, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	805, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 858, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	858, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	805, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 1020, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 805, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 858, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 858, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 805, 988, 805, 805, 805, 805, 1065, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 1066, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 805, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 858, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 858, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 805, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 1067, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 805, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 858, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 858, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 805, 988, 805, 805, 805, 
	805, 805, 1006, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 988, 805, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 858, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 988, 858, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 805, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 1068, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 988, 805, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 858, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 988, 858, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 805, 988, 805, 
	805, 805, 805, 1069, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 988, 805, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	858, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 988, 858, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 805, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 1055, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 988, 805, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 858, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 988, 858, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 805, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	1070, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 1071, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	805, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 858, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	858, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	805, 988, 805, 805, 805, 805, 1017, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 805, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 858, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 858, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 805, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 1046, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 805, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 858, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 858, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 805, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 1072, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 805, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 858, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 858, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 805, 988, 805, 805, 805, 
	1073, 805, 805, 805, 805, 805, 805, 805, 
	1074, 805, 805, 805, 805, 805, 805, 805, 
	1075, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 988, 805, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 858, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 988, 858, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 805, 988, 805, 805, 
	805, 805, 1076, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 988, 805, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 858, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 988, 858, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 805, 988, 805, 
	805, 805, 805, 805, 1012, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 988, 805, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	858, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 988, 858, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 805, 988, 
	805, 805, 805, 805, 1077, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 988, 805, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 858, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 988, 858, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 805, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 1078, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	805, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 858, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	858, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	805, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 1047, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 805, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 858, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 858, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 805, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 1079, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 805, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 858, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 858, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 805, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 1047, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 805, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 858, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 858, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 805, 988, 805, 805, 805, 
	805, 805, 805, 805, 1080, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 988, 805, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 858, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 988, 
	988, 988, 988, 988, 858, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 805, 988, 805, 805, 
	805, 805, 1017, 805, 805, 805, 1081, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 988, 805, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 858, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	988, 988, 988, 988, 988, 858, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 805, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 1082, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 988, 805, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	858, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 988, 988, 988, 988, 988, 858, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 805, 988, 
	805, 805, 805, 805, 1047, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 988, 805, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 858, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 988, 988, 988, 988, 988, 858, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 805, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 1083, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	805, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 858, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 988, 988, 988, 988, 988, 
	858, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	805, 988, 805, 805, 805, 805, 1084, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 805, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 858, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 988, 988, 988, 988, 
	988, 858, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 805, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 1085, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 805, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 988, 858, 988, 988, 988, 
	988, 988, 988, 988, 988, 988, 988, 988, 
	988, 988, 988, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 988, 988, 988, 
	988, 988, 858, 988, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 805, 988, 805, 805, 805, 1055, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 805, 805, 
	805, 805, 805, 805, 805, 805, 988, 988, 
	988, 988, 988, 805, 1086, 874, 864, 864, 
	864, 864, 864, 864, 864, 864, 864, 864, 
	864, 864, 864, 864, 864, 864, 864, 864, 
	864, 864, 864, 864, 864, 864, 864, 864, 
	864, 864, 864, 864, 864, 864, 864, 864, 
	864, 864, 864, 864, 864, 864, 864, 864, 
	864, 864, 864, 864, 864, 864, 864, 864, 
	864, 864, 864, 864, 864, 864, 864, 864, 
	864, 864, 864, 864, 814, 864, 1087, 1089, 
	1088, 1089, 1089, 1089, 1088, 1088, 1088, 1088, 
	1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 
	1088, 1088, 1088, 1088, 1088, 1088, 1089, 1088, 
	1088, 1090, 1088, 1088, 1091, 1088, 1088, 1088, 
	1088, 1088, 1088, 1088, 233, 1088, 1088, 1088, 
	1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 
	1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 
	1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 
	1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 
	1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 
	1088, 1088, 1092, 1088, 229, 230, 229, 229, 
	229, 1093, 1093, 1093, 1093, 1093, 1093, 1093, 
	1093, 1093, 1093, 1093, 1093, 1093, 1093, 1093, 
	1093, 1093, 1093, 229, 1093, 1093, 231, 1093, 
	1093, 232, 1093, 1093, 1093, 1093, 1093, 1093, 
	1093, 233, 1093, 1093, 1093, 1093, 1093, 1093, 
	1093, 1093, 1093, 1093, 1093, 1093, 1093, 1093, 
	1093, 1093, 1093, 1093, 1093, 1093, 1093, 1093, 
	1093, 1093, 1093, 1093, 1093, 1093, 1093, 1093, 
	1093, 1093, 1093, 1093, 1093, 1093, 1093, 1093, 
	1093, 1093, 1093, 1093, 1093, 1093, 1093, 234, 
	1093, 236, 1094, 236, 236, 236, 1094, 1094, 
	1094, 1094, 1094, 1094, 1094, 1094, 1094, 1094, 
	1094, 1094, 1094, 1094, 1094, 1094, 1094, 1094, 
	236, 1094, 1094, 231, 1094, 1094, 1094, 1094, 
	1094, 1094, 1094, 1094, 1094, 1094, 1094, 1094, 
	1094, 1094, 1094, 1094, 1094, 1094, 1094, 1094, 
	1094, 1094, 1094, 1094, 1094, 1094, 1094, 1094, 
	1094, 1094, 1094, 1094, 1094, 1094, 1094, 1094, 
	1094, 1094, 1094, 1094, 1094, 1094, 1094, 1094, 
	1094, 1094, 1094, 1094, 1094, 1094, 1094, 1094, 
	1094, 1094, 1094, 1094, 234, 1094, 1096, 1095, 
	1098, 1097, 239, 238, 244, 1093, 242, 1093, 
	1100, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1101, 1099, 1100, 1099, 1100, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1103, 1099, 1100, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1104, 1099, 1100, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1099, 1099, 1099, 1099, 
	1099, 1099, 1099, 1099, 1105, 1099, 1107, 1105, 
	1109, 1108, 1108, 1108, 1109, 1108, 1108, 1108, 
	1108, 1110, 1111, 1110, 1110, 1110, 1108, 1108, 
	1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 
	1108, 1108, 1109, 1108, 1108, 1108, 1108, 1108, 
	1110, 1108, 1108, 1112, 1108, 1108, 1108, 1108, 
	1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 
	1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 
	1108, 1108, 1108, 1108, 1108, 1113, 1108, 1108, 
	1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 
	1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 
	1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 
	1108, 1108, 1108, 1108, 1114, 1108, 1108, 1115, 
	1108, 1110, 1116, 1110, 1110, 1110, 1116, 1116, 
	1116, 1116, 1116, 1116, 1116, 1116, 1116, 1116, 
	1116, 1116, 1116, 1116, 1116, 1116, 1116, 1116, 
	1110, 1116, 1117, 1118, 1119, 1120, 1121, 1123, 
	1122, 1125, 1126, 1125, 1125, 1125, 1124, 1124, 
	1124, 1124, 1124, 1124, 1124, 1124, 1124, 1124, 
	1124, 1124, 1124, 1124, 1124, 1124, 1124, 1124, 
	1125, 1124, 1111, 1122, 1127, 1122, 0
]

class << self
	attr_accessor :_lex_trans_targs
	private :_lex_trans_targs, :_lex_trans_targs=
end
self._lex_trans_targs = [
	130, 129, 0, 2, 131, 132, 4, 133, 
	134, 134, 134, 134, 247, 7, 8, 9, 
	247, 247, 276, 11, 12, 276, 276, 280, 
	280, 16, 11, 17, 278, 279, 281, 282, 
	280, 276, 283, 284, 286, 13, 14, 287, 
	288, 15, 280, 18, 19, 24, 31, 290, 
	291, 17, 278, 279, 281, 282, 280, 276, 
	283, 284, 286, 13, 14, 287, 288, 15, 
	18, 19, 24, 31, 290, 291, 289, 20, 
	21, 22, 23, 25, 26, 29, 27, 28, 
	30, 32, 33, 276, 35, 36, 37, 39, 
	42, 40, 41, 43, 45, 307, 307, 307, 
	308, 47, 310, 48, 311, 49, 308, 47, 
	310, 48, 311, 345, 50, 345, 51, 52, 
	50, 345, 51, 345, 345, 345, 55, 56, 
	57, 58, 356, 345, 345, 345, 61, 62, 
	63, 345, 66, 61, 62, 63, 345, 66, 
	64, 64, 62, 63, 366, 65, 64, 64, 
	62, 63, 366, 65, 62, 345, 383, 345, 
	68, 384, 390, 72, 399, 400, 77, 78, 
	72, 73, 398, 73, 398, 345, 74, 75, 
	76, 401, 79, 80, 347, 53, 349, 82, 
	83, 406, 508, 85, 86, 87, 508, 516, 
	516, 516, 90, 538, 537, 516, 540, 542, 
	516, 95, 96, 97, 546, 516, 99, 100, 
	557, 526, 579, 103, 104, 105, 109, 110, 
	103, 104, 105, 109, 110, 106, 106, 104, 
	105, 107, 108, 106, 106, 104, 105, 107, 
	108, 627, 104, 516, 696, 111, 698, 113, 
	117, 699, 115, 696, 112, 696, 114, 698, 
	114, 698, 116, 698, 696, 710, 119, 120, 
	121, 716, 123, 124, 125, 126, 127, 710, 
	710, 128, 1, 3, 129, 129, 129, 135, 
	134, 134, 136, 137, 138, 139, 141, 144, 
	145, 146, 147, 134, 148, 149, 151, 153, 
	154, 155, 159, 161, 162, 163, 179, 184, 
	191, 196, 203, 210, 213, 214, 218, 212, 
	222, 230, 234, 236, 241, 243, 246, 134, 
	134, 134, 134, 134, 134, 140, 134, 140, 
	134, 142, 5, 143, 134, 6, 134, 134, 
	150, 152, 134, 156, 157, 158, 154, 160, 
	134, 164, 165, 174, 177, 166, 167, 168, 
	169, 170, 171, 172, 173, 135, 175, 176, 
	178, 180, 183, 181, 182, 185, 188, 186, 
	187, 189, 190, 192, 194, 193, 195, 197, 
	198, 134, 199, 200, 201, 202, 134, 204, 
	207, 205, 206, 208, 209, 211, 215, 216, 
	217, 219, 221, 220, 223, 224, 225, 227, 
	226, 228, 229, 231, 232, 233, 235, 237, 
	238, 239, 240, 242, 244, 245, 248, 247, 
	247, 249, 250, 252, 253, 247, 247, 247, 
	251, 247, 251, 10, 254, 247, 256, 255, 
	255, 259, 260, 261, 262, 255, 264, 265, 
	266, 267, 269, 271, 272, 273, 274, 275, 
	255, 257, 255, 258, 255, 255, 255, 255, 
	255, 263, 255, 263, 268, 255, 270, 255, 
	276, 276, 277, 292, 293, 279, 295, 296, 
	283, 297, 298, 299, 300, 301, 303, 304, 
	305, 306, 276, 276, 276, 276, 276, 276, 
	280, 285, 276, 276, 276, 276, 276, 276, 
	276, 276, 276, 294, 276, 294, 276, 276, 
	276, 276, 302, 276, 34, 38, 44, 307, 
	309, 312, 46, 307, 307, 308, 313, 313, 
	314, 315, 317, 319, 320, 313, 313, 316, 
	313, 316, 313, 318, 313, 313, 313, 322, 
	321, 321, 323, 324, 325, 327, 329, 330, 
	335, 342, 321, 321, 321, 321, 326, 321, 
	326, 321, 328, 321, 321, 322, 331, 332, 
	333, 334, 336, 337, 340, 338, 339, 341, 
	343, 344, 346, 345, 354, 355, 357, 358, 
	360, 361, 362, 363, 365, 367, 368, 371, 
	372, 397, 403, 404, 405, 406, 407, 408, 
	409, 410, 364, 412, 429, 434, 441, 446, 
	448, 454, 457, 458, 462, 456, 466, 477, 
	481, 484, 492, 496, 499, 500, 345, 50, 
	51, 345, 53, 348, 345, 345, 350, 352, 
	353, 345, 351, 345, 345, 345, 345, 345, 
	54, 345, 345, 345, 345, 345, 359, 345, 
	359, 345, 345, 59, 345, 60, 345, 345, 
	364, 345, 369, 345, 370, 345, 345, 345, 
	373, 382, 345, 67, 385, 386, 387, 345, 
	388, 69, 391, 392, 70, 395, 396, 345, 
	374, 376, 345, 375, 345, 345, 377, 380, 
	381, 345, 378, 379, 345, 345, 345, 345, 
	345, 345, 389, 345, 383, 393, 394, 345, 
	393, 345, 383, 393, 71, 402, 345, 345, 
	345, 345, 345, 81, 84, 345, 411, 413, 
	414, 424, 427, 415, 416, 417, 418, 419, 
	420, 421, 422, 423, 425, 426, 428, 430, 
	433, 431, 432, 435, 438, 436, 437, 439, 
	440, 442, 444, 443, 445, 447, 449, 451, 
	450, 452, 453, 455, 423, 459, 460, 461, 
	463, 465, 464, 467, 468, 469, 474, 470, 
	471, 472, 345, 346, 347, 53, 473, 352, 
	475, 476, 478, 479, 480, 482, 483, 485, 
	486, 487, 490, 488, 489, 491, 493, 494, 
	495, 497, 498, 345, 364, 501, 501, 502, 
	503, 504, 506, 501, 501, 501, 505, 501, 
	505, 501, 507, 501, 509, 508, 508, 510, 
	511, 508, 512, 514, 508, 508, 508, 508, 
	513, 508, 513, 515, 508, 517, 516, 516, 
	520, 521, 522, 516, 523, 525, 528, 529, 
	530, 531, 532, 516, 533, 534, 539, 567, 
	571, 516, 572, 574, 576, 516, 577, 578, 
	580, 584, 586, 587, 589, 590, 608, 613, 
	620, 628, 635, 642, 647, 648, 652, 646, 
	657, 667, 673, 676, 685, 689, 693, 694, 
	695, 528, 518, 516, 519, 516, 516, 516, 
	516, 516, 516, 524, 516, 524, 516, 88, 
	527, 516, 516, 516, 516, 516, 516, 516, 
	516, 516, 535, 516, 536, 516, 516, 89, 
	91, 516, 92, 548, 559, 562, 541, 563, 
	564, 549, 553, 555, 516, 541, 92, 543, 
	545, 93, 516, 543, 516, 544, 516, 516, 
	94, 547, 516, 516, 550, 552, 516, 550, 
	551, 553, 555, 552, 516, 554, 516, 516, 
	556, 558, 516, 98, 516, 516, 516, 560, 
	552, 553, 555, 560, 561, 516, 550, 552, 
	553, 555, 516, 550, 552, 553, 555, 516, 
	565, 552, 553, 555, 565, 566, 516, 92, 
	567, 541, 568, 553, 555, 569, 552, 92, 
	569, 541, 570, 573, 575, 516, 101, 102, 
	516, 516, 581, 582, 583, 578, 585, 516, 
	516, 588, 516, 516, 516, 591, 592, 601, 
	606, 593, 594, 595, 596, 597, 598, 599, 
	600, 517, 602, 603, 604, 605, 517, 607, 
	609, 612, 610, 611, 517, 517, 614, 617, 
	615, 616, 618, 619, 517, 621, 623, 622, 
	624, 625, 626, 516, 516, 629, 517, 630, 
	516, 631, 632, 633, 634, 518, 636, 639, 
	637, 638, 640, 641, 643, 644, 645, 517, 
	649, 650, 651, 653, 655, 656, 654, 517, 
	658, 659, 660, 663, 661, 662, 664, 665, 
	666, 668, 670, 669, 671, 672, 674, 675, 
	677, 678, 680, 683, 679, 681, 682, 684, 
	686, 687, 688, 690, 691, 692, 516, 516, 
	696, 697, 701, 702, 703, 696, 696, 696, 
	700, 696, 696, 705, 704, 706, 704, 707, 
	708, 709, 704, 704, 710, 710, 711, 712, 
	713, 715, 717, 718, 710, 710, 710, 714, 
	710, 714, 710, 118, 710, 710, 710, 122
]

class << self
	attr_accessor :_lex_trans_actions
	private :_lex_trans_actions, :_lex_trans_actions=
end
self._lex_trans_actions = [
	0, 53, 0, 0, 0, 0, 0, 0, 
	95, 65, 93, 71, 111, 0, 0, 0, 
	99, 101, 161, 0, 0, 135, 157, 727, 
	733, 1, 3, 3, 481, 971, 3, 484, 
	971, 499, 971, 3, 3, 481, 3, 487, 
	3, 3, 901, 3, 3, 3, 3, 3, 
	3, 0, 11, 925, 0, 13, 925, 139, 
	925, 0, 0, 11, 0, 15, 0, 0, 
	0, 0, 0, 0, 0, 0, 733, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 159, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 171, 163, 169, 
	742, 0, 0, 0, 742, 1, 905, 3, 
	3, 3, 905, 259, 0, 219, 0, 1, 
	3, 520, 3, 265, 217, 261, 0, 0, 
	0, 0, 0, 255, 201, 223, 0, 1, 
	0, 195, 0, 3, 478, 3, 517, 3, 
	5, 547, 829, 547, 976, 547, 0, 7, 
	550, 7, 913, 7, 397, 263, 0, 209, 
	0, 0, 0, 0, 751, 751, 0, 0, 
	17, 17, 676, 0, 1, 257, 0, 0, 
	0, 751, 0, 0, 766, 0, 0, 0, 
	0, 766, 287, 0, 0, 0, 275, 351, 
	295, 347, 0, 51, 51, 353, 805, 805, 
	349, 0, 0, 0, 0, 345, 0, 0, 
	0, 0, 0, 0, 1, 0, 0, 0, 
	3, 478, 3, 3, 3, 5, 547, 829, 
	547, 547, 547, 0, 7, 550, 7, 7, 
	7, 917, 397, 291, 371, 0, 823, 0, 
	45, 45, 0, 369, 0, 373, 5, 981, 
	0, 921, 1, 909, 357, 393, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 381, 
	427, 395, 0, 0, 55, 59, 57, 724, 
	75, 73, 0, 1, 0, 0, 51, 51, 
	0, 0, 0, 67, 0, 0, 0, 0, 
	721, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 97, 
	63, 89, 490, 85, 833, 5, 553, 0, 
	91, 0, 0, 0, 83, 0, 87, 69, 
	0, 0, 79, 0, 0, 0, 718, 0, 
	81, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 718, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 77, 0, 0, 0, 0, 61, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 51, 105, 
	103, 0, 0, 51, 0, 109, 107, 837, 
	5, 556, 0, 0, 1, 493, 0, 117, 
	115, 0, 1, 0, 0, 113, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	121, 0, 586, 0, 598, 125, 496, 123, 
	841, 5, 559, 0, 0, 127, 0, 119, 
	141, 143, 736, 1, 0, 733, 0, 0, 
	733, 0, 0, 0, 0, 0, 0, 51, 
	51, 51, 153, 151, 131, 406, 658, 147, 
	730, 0, 149, 133, 145, 137, 403, 646, 
	463, 505, 845, 5, 562, 0, 661, 155, 
	129, 400, 897, 502, 0, 0, 0, 165, 
	51, 51, 0, 167, 664, 739, 175, 177, 
	0, 0, 0, 0, 0, 179, 849, 5, 
	565, 0, 181, 1, 508, 173, 466, 748, 
	185, 183, 0, 1, 0, 0, 0, 0, 
	0, 0, 193, 189, 514, 853, 5, 568, 
	0, 191, 1, 511, 187, 745, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 769, 225, 0, 712, 0, 0, 
	51, 757, 0, 0, 757, 757, 0, 0, 
	51, 772, 0, 772, 0, 772, 772, 772, 
	0, 0, 772, 769, 769, 769, 769, 769, 
	769, 769, 769, 769, 769, 769, 769, 769, 
	769, 769, 769, 769, 0, 0, 247, 25, 
	25, 592, 9, 0, 604, 613, 0, 766, 
	0, 619, 0, 637, 631, 625, 249, 523, 
	3, 251, 221, 412, 253, 857, 5, 571, 
	0, 231, 199, 0, 241, 0, 667, 229, 
	757, 227, 0, 243, 0, 245, 409, 197, 
	0, 0, 205, 0, 0, 0, 0, 215, 
	0, 0, 0, 0, 0, 0, 0, 235, 
	0, 0, 589, 0, 601, 610, 0, 0, 
	0, 616, 0, 0, 634, 640, 628, 622, 
	207, 203, 0, 682, 19, 19, 0, 237, 
	0, 685, 21, 21, 0, 0, 679, 233, 
	239, 211, 213, 0, 0, 649, 1, 769, 
	769, 769, 769, 769, 769, 769, 769, 769, 
	769, 769, 769, 766, 769, 769, 769, 769, 
	769, 769, 769, 769, 769, 769, 769, 769, 
	769, 769, 769, 769, 769, 769, 769, 769, 
	769, 769, 769, 769, 763, 769, 769, 769, 
	769, 769, 769, 769, 769, 769, 769, 769, 
	769, 760, 688, 933, 929, 23, 23, 760, 
	769, 769, 769, 769, 769, 769, 769, 769, 
	769, 769, 769, 769, 769, 769, 769, 769, 
	769, 769, 769, 469, 754, 267, 269, 0, 
	1, 0, 0, 271, 529, 861, 5, 574, 
	0, 273, 1, 526, 51, 281, 279, 0, 
	1, 277, 0, 0, 285, 283, 535, 865, 
	5, 577, 0, 1, 532, 811, 315, 313, 
	0, 1, 0, 293, 0, 51, 817, 0, 
	0, 0, 0, 307, 0, 0, 799, 799, 
	0, 309, 0, 0, 0, 305, 51, 808, 
	808, 808, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 820, 814, 595, 0, 607, 341, 541, 
	337, 301, 869, 5, 580, 0, 343, 0, 
	0, 327, 303, 335, 297, 670, 673, 333, 
	289, 329, 0, 339, 0, 415, 323, 0, 
	0, 966, 881, 37, 0, 0, 997, 0, 
	0, 37, 706, 706, 893, 802, 0, 43, 
	715, 0, 889, 41, 448, 0, 451, 454, 
	0, 0, 460, 457, 37, 37, 885, 0, 
	0, 39, 39, 0, 433, 0, 442, 430, 
	51, 0, 439, 0, 445, 436, 956, 33, 
	33, 700, 700, 0, 0, 946, 29, 29, 
	694, 694, 951, 31, 31, 697, 697, 941, 
	27, 27, 691, 691, 0, 0, 961, 877, 
	937, 991, 35, 703, 703, 937, 35, 709, 
	799, 986, 0, 0, 0, 299, 0, 0, 
	643, 325, 808, 808, 808, 796, 808, 652, 
	311, 1, 538, 655, 331, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 793, 0, 0, 0, 0, 796, 0, 
	0, 0, 0, 0, 778, 784, 0, 0, 
	0, 0, 0, 0, 787, 0, 0, 0, 
	0, 0, 784, 321, 319, 0, 775, 0, 
	317, 0, 0, 0, 0, 790, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 781, 
	0, 0, 0, 0, 0, 0, 0, 790, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 472, 475, 
	359, 826, 826, 45, 826, 367, 361, 365, 
	0, 363, 355, 0, 421, 0, 377, 0, 
	0, 0, 375, 418, 383, 385, 0, 1, 
	0, 51, 0, 51, 387, 544, 873, 5, 
	583, 0, 391, 0, 389, 379, 424, 0
]

class << self
	attr_accessor :_lex_to_state_actions
	private :_lex_to_state_actions, :_lex_to_state_actions=
end
self._lex_to_state_actions = [
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	47, 47, 0, 0, 0, 0, 47, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 47, 
	0, 0, 0, 0, 0, 0, 0, 47, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 47, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 47, 0, 0, 0, 0, 
	0, 47, 0, 0, 0, 0, 0, 0, 
	0, 47, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 47, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 47, 0, 0, 
	0, 0, 0, 0, 47, 0, 0, 0, 
	0, 0, 0, 0, 47, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	47, 0, 0, 0, 0, 0, 0, 0, 
	47, 0, 0, 0, 0, 0, 47, 0, 
	0, 0, 0, 0, 0, 0, 0
]

class << self
	attr_accessor :_lex_from_state_actions
	private :_lex_from_state_actions, :_lex_from_state_actions=
end
self._lex_from_state_actions = [
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	49, 49, 0, 0, 0, 0, 49, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 49, 
	0, 0, 0, 0, 0, 0, 0, 49, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 49, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 49, 0, 0, 0, 0, 
	0, 49, 0, 0, 0, 0, 0, 0, 
	0, 49, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 49, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 49, 0, 0, 
	0, 0, 0, 0, 49, 0, 0, 0, 
	0, 0, 0, 0, 49, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	49, 0, 0, 0, 0, 0, 0, 0, 
	49, 0, 0, 0, 0, 0, 49, 0, 
	0, 0, 0, 0, 0, 0, 0
]

class << self
	attr_accessor :_lex_eof_trans
	private :_lex_eof_trans, :_lex_eof_trans=
end
self._lex_eof_trans = [
	0, 0, 0, 0, 0, 9, 11, 13, 
	13, 13, 13, 19, 19, 23, 23, 23, 
	23, 23, 23, 23, 23, 23, 23, 23, 
	23, 23, 23, 23, 23, 23, 23, 23, 
	23, 23, 84, 84, 84, 84, 84, 84, 
	84, 84, 84, 84, 84, 84, 94, 96, 
	96, 96, 108, 108, 108, 116, 118, 118, 
	118, 118, 118, 124, 116, 116, 116, 116, 
	116, 116, 116, 150, 150, 150, 150, 150, 
	150, 116, 166, 116, 166, 150, 150, 116, 
	116, 150, 150, 150, 150, 179, 179, 179, 
	184, 186, 186, 186, 190, 190, 193, 193, 
	193, 193, 198, 198, 198, 184, 190, 190, 
	190, 190, 190, 190, 190, 190, 190, 229, 
	236, 238, 238, 238, 238, 229, 246, 246, 
	246, 246, 246, 246, 246, 246, 246, 246, 
	0, 0, 261, 261, 262, 263, 0, 304, 
	306, 307, 308, 309, 311, 313, 317, 317, 
	308, 308, 308, 308, 319, 308, 308, 313, 
	308, 308, 304, 323, 323, 323, 323, 323, 
	323, 313, 313, 329, 329, 329, 329, 329, 
	329, 329, 329, 329, 329, 329, 329, 329, 
	329, 329, 329, 329, 329, 329, 329, 329, 
	329, 329, 329, 329, 329, 329, 329, 329, 
	329, 329, 329, 329, 329, 329, 362, 329, 
	329, 329, 329, 329, 329, 329, 329, 329, 
	329, 329, 329, 329, 329, 329, 329, 329, 
	329, 329, 329, 329, 329, 329, 329, 329, 
	329, 329, 329, 329, 329, 329, 329, 329, 
	329, 329, 329, 329, 329, 329, 329, 329, 
	329, 329, 329, 329, 329, 329, 308, 0, 
	406, 407, 408, 410, 406, 406, 414, 0, 
	433, 435, 437, 438, 439, 440, 441, 443, 
	440, 440, 440, 440, 440, 446, 440, 440, 
	448, 446, 446, 440, 0, 467, 468, 19, 
	19, 471, 472, 19, 468, 468, 475, 477, 
	480, 468, 481, 468, 482, 483, 485, 487, 
	468, 475, 488, 488, 477, 488, 492, 488, 
	488, 488, 488, 0, 94, 500, 501, 500, 
	500, 0, 510, 511, 513, 515, 517, 515, 
	519, 0, 531, 532, 533, 534, 536, 538, 
	540, 541, 541, 541, 541, 541, 541, 541, 
	541, 541, 541, 541, 541, 541, 541, 541, 
	541, 0, 599, 602, 605, 606, 610, 612, 
	613, 614, 615, 616, 618, 621, 622, 624, 
	626, 629, 631, 632, 116, 629, 634, 629, 
	621, 636, 638, 621, 621, 656, 659, 661, 
	662, 666, 669, 670, 671, 672, 656, 656, 
	656, 656, 656, 656, 656, 656, 656, 656, 
	676, 680, 682, 656, 656, 621, 687, 688, 
	688, 688, 621, 621, 621, 689, 116, 621, 
	621, 694, 621, 616, 599, 599, 599, 599, 
	599, 599, 599, 599, 599, 599, 599, 116, 
	599, 599, 599, 599, 599, 599, 599, 599, 
	599, 599, 599, 599, 599, 599, 599, 599, 
	599, 599, 599, 599, 599, 599, 599, 599, 
	599, 599, 599, 599, 599, 599, 599, 599, 
	599, 599, 599, 599, 599, 599, 599, 599, 
	599, 599, 599, 599, 599, 599, 599, 599, 
	747, 606, 599, 599, 599, 599, 599, 599, 
	599, 599, 599, 599, 599, 599, 599, 599, 
	599, 599, 599, 599, 599, 599, 599, 599, 
	599, 599, 599, 772, 621, 0, 780, 781, 
	782, 784, 786, 788, 0, 797, 798, 799, 
	800, 802, 797, 805, 0, 190, 860, 862, 
	863, 864, 865, 867, 869, 871, 874, 874, 
	190, 876, 878, 879, 880, 876, 882, 884, 
	884, 887, 887, 890, 901, 190, 907, 909, 
	911, 912, 915, 916, 890, 890, 919, 919, 
	919, 925, 927, 928, 931, 933, 934, 935, 
	919, 919, 942, 947, 952, 919, 919, 959, 
	959, 919, 919, 884, 876, 876, 884, 876, 
	876, 871, 190, 977, 978, 978, 978, 978, 
	978, 978, 984, 871, 987, 988, 989, 989, 
	989, 989, 989, 989, 989, 989, 989, 989, 
	989, 989, 989, 989, 989, 989, 989, 989, 
	989, 989, 989, 989, 989, 989, 989, 989, 
	989, 989, 989, 989, 989, 989, 989, 989, 
	989, 989, 1028, 1029, 989, 989, 1033, 989, 
	989, 989, 989, 989, 989, 989, 989, 989, 
	989, 989, 989, 989, 989, 989, 989, 989, 
	989, 989, 989, 989, 989, 989, 989, 989, 
	989, 989, 989, 989, 989, 989, 989, 989, 
	989, 989, 989, 989, 989, 989, 989, 989, 
	989, 989, 989, 989, 989, 989, 989, 989, 
	989, 989, 989, 989, 989, 989, 989, 989, 
	989, 989, 989, 989, 989, 1087, 865, 1088, 
	0, 1094, 1095, 1096, 1098, 1094, 1094, 1094, 
	0, 1103, 1103, 1103, 1103, 1107, 0, 1117, 
	1118, 1119, 1121, 1123, 1125, 1123, 1123
]

class << self
	attr_accessor :lex_start
end
self.lex_start = 128;
class << self
	attr_accessor :lex_error
end
self.lex_error = 0;

class << self
	attr_accessor :lex_en_expr_variable
end
self.lex_en_expr_variable = 129;
class << self
	attr_accessor :lex_en_expr_fname
end
self.lex_en_expr_fname = 134;
class << self
	attr_accessor :lex_en_expr_endfn
end
self.lex_en_expr_endfn = 247;
class << self
	attr_accessor :lex_en_expr_dot
end
self.lex_en_expr_dot = 255;
class << self
	attr_accessor :lex_en_expr_arg
end
self.lex_en_expr_arg = 276;
class << self
	attr_accessor :lex_en_expr_cmdarg
end
self.lex_en_expr_cmdarg = 307;
class << self
	attr_accessor :lex_en_expr_endarg
end
self.lex_en_expr_endarg = 313;
class << self
	attr_accessor :lex_en_expr_mid
end
self.lex_en_expr_mid = 321;
class << self
	attr_accessor :lex_en_expr_beg
end
self.lex_en_expr_beg = 345;
class << self
	attr_accessor :lex_en_expr_labelarg
end
self.lex_en_expr_labelarg = 501;
class << self
	attr_accessor :lex_en_expr_value
end
self.lex_en_expr_value = 508;
class << self
	attr_accessor :lex_en_expr_end
end
self.lex_en_expr_end = 516;
class << self
	attr_accessor :lex_en_leading_dot
end
self.lex_en_leading_dot = 696;
class << self
	attr_accessor :lex_en_line_comment
end
self.lex_en_line_comment = 704;
class << self
	attr_accessor :lex_en_line_begin
end
self.lex_en_line_begin = 710;
class << self
	attr_accessor :lex_en_inside_string
end
self.lex_en_inside_string = 128;


# line 82 "lib/parser/lexer.rl"
  # %

  attr_reader   :source_buffer

  attr_accessor :diagnostics
  attr_accessor :static_env
  attr_accessor :force_utf32

  attr_accessor :cond, :cmdarg, :context, :command_start

  attr_accessor :tokens, :comments

  attr_reader :paren_nest, :cmdarg_stack, :cond_stack, :lambda_stack, :version

  def initialize(version)
    @version    = version
    @static_env = nil
    @context    = nil

    @tokens     = nil
    @comments   = nil

    @_lex_actions =
      if self.class.respond_to?(:_lex_actions, true)
        self.class.send :_lex_actions
      else
        []
      end

    @emit_integer = lambda { |chars, p| emit(:tINTEGER,   chars); p }
    @emit_rational = lambda { |chars, p| emit(:tRATIONAL,  Rational(chars)); p }
    @emit_imaginary = lambda { |chars, p| emit(:tIMAGINARY, Complex(0, chars)); p }
    @emit_imaginary_rational = lambda { |chars, p| emit(:tIMAGINARY, Complex(0, Rational(chars))); p }
    @emit_integer_re = lambda { |chars, p| emit(:tINTEGER,   chars, @ts, @te - 2); p - 2 }
    @emit_integer_if = lambda { |chars, p| emit(:tINTEGER,   chars, @ts, @te - 2); p - 2 }
    @emit_integer_rescue = lambda { |chars, p| emit(:tINTEGER,   chars, @ts, @te - 6); p - 6 }

    @emit_float = lambda { |chars, p| emit(:tFLOAT,     Float(chars)); p }
    @emit_imaginary_float = lambda { |chars, p| emit(:tIMAGINARY, Complex(0, Float(chars))); p }
    @emit_float_if =     lambda { |chars, p| emit(:tFLOAT,     Float(chars), @ts, @te - 2); p - 2 }
    @emit_float_rescue = lambda { |chars, p| emit(:tFLOAT,     Float(chars), @ts, @te - 6); p - 6 }

    reset
  end

  def reset(reset_state=true)
    # Ragel state:
    if reset_state
      # Unit tests set state prior to resetting lexer.
      @cs     = self.class.lex_en_line_begin

      @cond   = StackState.new('cond')
      @cmdarg = StackState.new('cmdarg')
      @cond_stack   = []
      @cmdarg_stack = []
    end

    @force_utf32   = false # Set to true by some tests

    @source_pts    = nil # @source as a codepoint array

    @p             = 0   # stream position (saved manually in #advance)
    @ts            = nil # token start
    @te            = nil # token end
    @act           = 0   # next action

    @stack         = []  # state stack
    @top           = 0   # state stack top pointer

    # Lexer state:
    @token_queue   = []

    @eq_begin_s    = nil # location of last encountered =begin
    @sharp_s       = nil # location of last encountered #

    @newline_s     = nil # location of last encountered newline

    @num_base      = nil # last numeric base
    @num_digits_s  = nil # starting position of numeric digits
    @num_suffix_s  = nil # starting position of numeric suffix
    @num_xfrm      = nil # numeric suffix-induced transformation

    # Ruby 1.9 ->() lambdas emit a distinct token if do/{ is
    # encountered after a matching closing parenthesis.
    @paren_nest    = 0
    @lambda_stack  = []

    # If the lexer is in `command state' (aka expr_value)
    # at the entry to #advance, it will transition to expr_cmdarg
    # instead of expr_arg at certain points.
    @command_start = true

    # State before =begin / =end block comment
    @cs_before_block_comment = self.class.lex_en_line_begin

    @strings = Parser::LexerStrings.new(self, @version)
  end

  def source_buffer=(source_buffer)
    @source_buffer = source_buffer

    if @source_buffer
      source = @source_buffer.source

      if source.encoding == Encoding::UTF_8
        @source_pts = source.unpack('U*')
      else
        @source_pts = source.unpack('C*')
      end

      if @source_pts[0] == 0xfeff
        # Skip byte order mark.
        @p = 1
      end
    else
      @source_pts = nil
    end

    @strings.source_buffer = @source_buffer
    @strings.source_pts = @source_pts
  end

  def encoding
    @source_buffer.source.encoding
  end

  LEX_STATES = {
    :line_begin    => lex_en_line_begin,
    :expr_dot      => lex_en_expr_dot,
    :expr_fname    => lex_en_expr_fname,
    :expr_value    => lex_en_expr_value,
    :expr_beg      => lex_en_expr_beg,
    :expr_mid      => lex_en_expr_mid,
    :expr_arg      => lex_en_expr_arg,
    :expr_cmdarg   => lex_en_expr_cmdarg,
    :expr_end      => lex_en_expr_end,
    :expr_endarg   => lex_en_expr_endarg,
    :expr_endfn    => lex_en_expr_endfn,
    :expr_labelarg => lex_en_expr_labelarg,

    :inside_string => lex_en_inside_string
  }

  def state
    LEX_STATES.invert.fetch(@cs, @cs)
  end

  def state=(state)
    @cs = LEX_STATES.fetch(state)
  end

  def push_cmdarg
    @cmdarg_stack.push(@cmdarg)
    @cmdarg = StackState.new("cmdarg.#{@cmdarg_stack.count}")
  end

  def pop_cmdarg
    @cmdarg = @cmdarg_stack.pop
  end

  def push_cond
    @cond_stack.push(@cond)
    @cond = StackState.new("cond.#{@cond_stack.count}")
  end

  def pop_cond
    @cond = @cond_stack.pop
  end

  def dedent_level
    @strings.dedent_level
  end

  # Return next token: [type, value].
  def advance
    unless @token_queue.empty?
      return @token_queue.shift
    end

    # Ugly, but dependent on Ragel output. Consider refactoring it somehow.
    klass = self.class
    _lex_trans_keys         = klass.send :_lex_trans_keys
    _lex_key_spans          = klass.send :_lex_key_spans
    _lex_index_offsets      = klass.send :_lex_index_offsets
    _lex_indicies           = klass.send :_lex_indicies
    _lex_trans_targs        = klass.send :_lex_trans_targs
    _lex_trans_actions      = klass.send :_lex_trans_actions
    _lex_to_state_actions   = klass.send :_lex_to_state_actions
    _lex_from_state_actions = klass.send :_lex_from_state_actions
    _lex_eof_trans          = klass.send :_lex_eof_trans
    _lex_actions            = @_lex_actions

    pe = @source_pts.size + 2
    p, eof = @p, pe

    cmd_state = @command_start
    @command_start = false

    
# line 8568 "lib/parser/lexer-F0.rb"
begin # ragel flat
	testEof = false
	_slen, _trans, _keys, _inds, _acts, _nacts = nil
	_goto_level = 0
	_resume = 10
	_eof_trans = 15
	_again = 20
	_test_eof = 30
	_out = 40
	while true
	_trigger_goto = false
	if _goto_level <= 0
	if p == pe
		_goto_level = _test_eof
		next
	end
	if  @cs == 0
		_goto_level = _out
		next
	end
	end
	if _goto_level <= _resume
	_acts = _lex_from_state_actions[ @cs]
	_nacts = _lex_actions[_acts]
	_acts += 1
	while _nacts > 0
		_nacts -= 1
		_acts += 1
		case _lex_actions[_acts - 1]
	when 52 then
# line 1 "NONE"
		begin
 @ts = p
		end
# line 8603 "lib/parser/lexer-F0.rb"
		end # from state action switch
	end
	if _trigger_goto
		next
	end
	_keys =  @cs << 1
	_inds = _lex_index_offsets[ @cs]
	_slen = _lex_key_spans[ @cs]
	_wide = ( (@source_pts[p] || 0))
	_trans = if (   _slen > 0 && 
			_lex_trans_keys[_keys] <= _wide && 
			_wide <= _lex_trans_keys[_keys + 1] 
		    ) then
			_lex_indicies[ _inds + _wide - _lex_trans_keys[_keys] ] 
		 else 
			_lex_indicies[ _inds + _slen ]
		 end
	end
	if _goto_level <= _eof_trans
	 @cs = _lex_trans_targs[_trans]
	if _lex_trans_actions[_trans] != 0
		_acts = _lex_trans_actions[_trans]
		_nacts = _lex_actions[_acts]
		_acts += 1
		while _nacts > 0
			_nacts -= 1
			_acts += 1
			case _lex_actions[_acts - 1]
	when 0 then
# line 539 "lib/parser/lexer.rl"
		begin

    # Record position of a newline for precise location reporting on tNL
    # tokens.
    #
    # This action is embedded directly into c_nl, as it is idempotent and
    # there are no cases when we need to skip it.
    @newline_s = p
  		end
	when 1 then
# line 673 "lib/parser/lexer.rl"
		begin
 @num_xfrm = @emit_integer 		end
	when 2 then
# line 674 "lib/parser/lexer.rl"
		begin
 @num_xfrm = @emit_rational 		end
	when 3 then
# line 675 "lib/parser/lexer.rl"
		begin
 @num_xfrm = @emit_imaginary 		end
	when 4 then
# line 676 "lib/parser/lexer.rl"
		begin
 @num_xfrm = @emit_imaginary_rational 		end
	when 5 then
# line 677 "lib/parser/lexer.rl"
		begin
 @num_xfrm = @emit_integer_re 		end
	when 6 then
# line 678 "lib/parser/lexer.rl"
		begin
 @num_xfrm = @emit_integer_if 		end
	when 7 then
# line 679 "lib/parser/lexer.rl"
		begin
 @num_xfrm = @emit_integer_rescue 		end
	when 8 then
# line 682 "lib/parser/lexer.rl"
		begin
 @num_xfrm = @emit_float 		end
	when 9 then
# line 683 "lib/parser/lexer.rl"
		begin
 @num_xfrm = @emit_imaginary_float 		end
	when 10 then
# line 684 "lib/parser/lexer.rl"
		begin
 @num_xfrm = @emit_float_if 		end
	when 11 then
# line 688 "lib/parser/lexer.rl"
		begin
 @num_xfrm = @emit_rational 		end
	when 12 then
# line 689 "lib/parser/lexer.rl"
		begin
 @num_xfrm = @emit_imaginary_rational 		end
	when 13 then
# line 690 "lib/parser/lexer.rl"
		begin
 @num_xfrm = @emit_float_rescue 		end
	when 14 then
# line 696 "lib/parser/lexer.rl"
		begin

    e_lbrace
  		end
	when 15 then
# line 700 "lib/parser/lexer.rl"
		begin

    if @strings.close_interp_on_current_literal(p)
      p = p - 1;
       @cs = 128;
      	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    end

    @paren_nest -= 1
  		end
	when 16 then
# line 723 "lib/parser/lexer.rl"
		begin

    p = on_newline(p)
  		end
	when 17 then
# line 733 "lib/parser/lexer.rl"
		begin
 @sharp_s = p - 1 		end
	when 18 then
# line 736 "lib/parser/lexer.rl"
		begin
 emit_comment_from_range(p, pe) 		end
	when 19 then
# line 777 "lib/parser/lexer.rl"
		begin
 tm = p 		end
	when 20 then
# line 778 "lib/parser/lexer.rl"
		begin
 tm = p - 2 		end
	when 21 then
# line 783 "lib/parser/lexer.rl"
		begin
 tm = p 		end
	when 22 then
# line 784 "lib/parser/lexer.rl"
		begin
 tm = p - 2 		end
	when 23 then
# line 785 "lib/parser/lexer.rl"
		begin
 tm = p - 2 		end
	when 24 then
# line 786 "lib/parser/lexer.rl"
		begin
 tm = p - 2 		end
	when 25 then
# line 787 "lib/parser/lexer.rl"
		begin
 tm = p - 3 		end
	when 26 then
# line 792 "lib/parser/lexer.rl"
		begin
 tm = p - 2 		end
	when 27 then
# line 797 "lib/parser/lexer.rl"
		begin
 tm = p - 2 		end
	when 28 then
# line 803 "lib/parser/lexer.rl"
		begin

    @cond.push(false); @cmdarg.push(false)

    @paren_nest += 1
  		end
	when 29 then
# line 809 "lib/parser/lexer.rl"
		begin

    @paren_nest -= 1
  		end
	when 30 then
# line 816 "lib/parser/lexer.rl"
		begin

    @cond.push(false); @cmdarg.push(false)

    @paren_nest += 1

    if version?(18)
      @command_start = true
    end
  		end
	when 31 then
# line 826 "lib/parser/lexer.rl"
		begin

    @paren_nest -= 1
  		end
	when 32 then
# line 1061 "lib/parser/lexer.rl"
		begin
 tm = p 		end
	when 33 then
# line 1074 "lib/parser/lexer.rl"
		begin
 tm = p 		end
	when 34 then
# line 1102 "lib/parser/lexer.rl"
		begin
 tm = p 		end
	when 35 then
# line 1299 "lib/parser/lexer.rl"
		begin
 heredoc_e      = p 		end
	when 36 then
# line 1300 "lib/parser/lexer.rl"
		begin
 new_herebody_s = p 		end
	when 37 then
# line 1400 "lib/parser/lexer.rl"
		begin
 tm = p - 1; diag_msg = :ivar_name 		end
	when 38 then
# line 1401 "lib/parser/lexer.rl"
		begin
 tm = p - 2; diag_msg = :cvar_name 		end
	when 39 then
# line 1488 "lib/parser/lexer.rl"
		begin
 tm = p 		end
	when 40 then
# line 1595 "lib/parser/lexer.rl"
		begin
 ident_tok = tok; ident_ts = @ts; ident_te = @te; 		end
	when 41 then
# line 1781 "lib/parser/lexer.rl"
		begin
 @num_base = 16; @num_digits_s = p 		end
	when 42 then
# line 1782 "lib/parser/lexer.rl"
		begin
 @num_base = 10; @num_digits_s = p 		end
	when 43 then
# line 1783 "lib/parser/lexer.rl"
		begin
 @num_base = 8;  @num_digits_s = p 		end
	when 44 then
# line 1784 "lib/parser/lexer.rl"
		begin
 @num_base = 2;  @num_digits_s = p 		end
	when 45 then
# line 1785 "lib/parser/lexer.rl"
		begin
 @num_base = 10; @num_digits_s = @ts 		end
	when 46 then
# line 1786 "lib/parser/lexer.rl"
		begin
 @num_base = 8;  @num_digits_s = @ts 		end
	when 47 then
# line 1787 "lib/parser/lexer.rl"
		begin
 @num_suffix_s = p 		end
	when 48 then
# line 1830 "lib/parser/lexer.rl"
		begin
 @num_suffix_s = p 		end
	when 49 then
# line 1831 "lib/parser/lexer.rl"
		begin
 @num_suffix_s = p 		end
	when 50 then
# line 2034 "lib/parser/lexer.rl"
		begin
 tm = p 		end
	when 53 then
# line 1 "NONE"
		begin
 @te = p+1
		end
	when 54 then
# line 848 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        emit_global_var

         @cs = (stack_pop); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 55 then
# line 848 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        emit_global_var

         @cs = (stack_pop); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 56 then
# line 855 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        emit_class_var

         @cs = (stack_pop); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 57 then
# line 862 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        emit_instance_var

         @cs = (stack_pop); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 58 then
# line 880 "lib/parser/lexer.rl"
		begin
 @act = 4;		end
	when 59 then
# line 884 "lib/parser/lexer.rl"
		begin
 @act = 5;		end
	when 60 then
# line 888 "lib/parser/lexer.rl"
		begin
 @act = 6;		end
	when 61 then
# line 880 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  emit_table(KEYWORDS_BEGIN);
            @cs = 247; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 62 then
# line 888 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  emit(:tIDENTIFIER)
            @cs = 247; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 63 then
# line 892 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  p = @ts - 1
            @cs = 516; 	begin
		 @stack[ @top] =  @cs
		 @top+= 1
		 @cs = 129
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 64 then
# line 901 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  emit_table(PUNCTUATION)
            @cs = 247; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 65 then
# line 905 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  p = p - 1; p = p - 1; 	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 66 then
# line 911 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        if version?(23)
          type, delimiter = tok[0..-2], tok[-1].chr
          @strings.push_literal(type, delimiter, @ts)
          	begin
		 @cs = 128
		_trigger_goto = true
		_goto_level = _again
		break
	end

        else
          p = @ts - 1
          	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end

        end
       end
		end
	when 67 then
# line 925 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  p = p - 1; 	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 68 then
# line 566 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
    # Sit at EOF indefinitely. #advance would return $eof each time.
    # This allows to feed the lexer more data if needed; this is only used
    # in tests.
    #
    # Note that this action is not embedded into e_eof like e_nl and e_bs
    # below. This is due to the fact that scanner state at EOF is observed
    # by tests, and encapsulating it in a rule would break the introspection.
    p = p - 1; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

   end
		end
	when 69 then
# line 880 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit_table(KEYWORDS_BEGIN);
            @cs = 247; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 70 then
# line 884 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit(:tCONSTANT)
            @cs = 247; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 71 then
# line 888 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit(:tIDENTIFIER)
            @cs = 247; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 72 then
# line 892 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  p = @ts - 1
            @cs = 516; 	begin
		 @stack[ @top] =  @cs
		 @top+= 1
		 @cs = 129
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 73 then
# line 901 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit_table(PUNCTUATION)
            @cs = 247; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 74 then
# line 908 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  p = p - 1; 	begin
		 @cs = 345
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 75 then
# line 922 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1;		end
	when 76 then
# line 925 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  p = p - 1; 	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 77 then
# line 901 "lib/parser/lexer.rl"
		begin
 begin p = (( @te))-1; end
 begin  emit_table(PUNCTUATION)
            @cs = 247; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 78 then
# line 925 "lib/parser/lexer.rl"
		begin
 begin p = (( @te))-1; end
 begin  p = p - 1; 	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 79 then
# line 1 "NONE"
		begin
	case  @act
	when 4 then
	begin begin p = (( @te))-1; end
 emit_table(KEYWORDS_BEGIN);
            @cs = 247; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
 end
	when 5 then
	begin begin p = (( @te))-1; end
 emit(:tCONSTANT)
            @cs = 247; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
 end
	when 6 then
	begin begin p = (( @te))-1; end
 emit(:tIDENTIFIER)
            @cs = 247; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
 end
end 
			end
	when 80 then
# line 937 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  emit(:tLABEL, tok(@ts, @te - 2), @ts, @te - 1)
           p = p - 1;  @cs = 501; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 81 then
# line 941 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        if @version >= 31 && @context.in_argdef
          emit(:tBDOT3, '...'.freeze)
          # emit(:tNL, "\n".freeze, @te - 1, @te)
           @cs = 516; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

        else
          p -= 3;
          	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end

        end
       end
		end
	when 82 then
# line 955 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  p = p - 1; 	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 83 then
# line 566 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
    # Sit at EOF indefinitely. #advance would return $eof each time.
    # This allows to feed the lexer more data if needed; this is only used
    # in tests.
    #
    # Note that this action is not embedded into e_eof like e_nl and e_bs
    # below. This is due to the fact that scanner state at EOF is observed
    # by tests, and encapsulating it in a rule would break the introspection.
    p = p - 1; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

   end
		end
	when 84 then
# line 952 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1;		end
	when 85 then
# line 955 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  p = p - 1; 	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 86 then
# line 955 "lib/parser/lexer.rl"
		begin
 begin p = (( @te))-1; end
 begin  p = p - 1; 	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 87 then
# line 981 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  emit_table(PUNCTUATION)
            @cs = 276; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 88 then
# line 987 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  p = p - 1; 	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 89 then
# line 566 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
    # Sit at EOF indefinitely. #advance would return $eof each time.
    # This allows to feed the lexer more data if needed; this is only used
    # in tests.
    #
    # Note that this action is not embedded into e_eof like e_nl and e_bs
    # below. This is due to the fact that scanner state at EOF is observed
    # by tests, and encapsulating it in a rule would break the introspection.
    p = p - 1; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

   end
		end
	when 90 then
# line 966 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit(:tCONSTANT)
            @cs = (arg_or_cmdarg(cmd_state)); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 91 then
# line 970 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit(:tIDENTIFIER)
            @cs = (arg_or_cmdarg(cmd_state)); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 92 then
# line 974 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit(:tFID, tok(@ts, tm), @ts, tm)
            @cs = (arg_or_cmdarg(cmd_state)); p = tm - 1; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 93 then
# line 981 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit_table(PUNCTUATION)
            @cs = 276; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 94 then
# line 984 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1;		end
	when 95 then
# line 987 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  p = p - 1; 	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 96 then
# line 1065 "lib/parser/lexer.rl"
		begin
 @act = 33;		end
	when 97 then
# line 1075 "lib/parser/lexer.rl"
		begin
 @act = 34;		end
	when 98 then
# line 1114 "lib/parser/lexer.rl"
		begin
 @act = 39;		end
	when 99 then
# line 1119 "lib/parser/lexer.rl"
		begin
 @act = 40;		end
	when 100 then
# line 1047 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        # Unlike expr_beg as invoked in the next rule, do not warn
        p = @ts - 1
        	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end

       end
		end
	when 101 then
# line 1065 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        check_ambiguous_slash(tm)

        p = tm - 1
        	begin
		 @cs = 345
		_trigger_goto = true
		_goto_level = _again
		break
	end

       end
		end
	when 102 then
# line 1086 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  p = p - 1; p = p - 1; 	begin
		 @cs = 345
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 103 then
# line 1094 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  p = @ts - 1; 	begin
		 @cs = 345
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 104 then
# line 1103 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  p = tm - 1; 	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 105 then
# line 1114 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        p = @ts - 1
        	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end

       end
		end
	when 106 then
# line 1128 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  p = p - 1; 	begin
		 @cs = 345
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 107 then
# line 566 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
    # Sit at EOF indefinitely. #advance would return $eof each time.
    # This allows to feed the lexer more data if needed; this is only used
    # in tests.
    #
    # Note that this action is not embedded into e_eof like e_nl and e_bs
    # below. This is due to the fact that scanner state at EOF is observed
    # by tests, and encapsulating it in a rule would break the introspection.
    p = p - 1; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

   end
		end
	when 108 then
# line 1003 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        if version?(18)
          emit(:tLPAREN2, '('.freeze, @te - 1, @te)
           @cs = 508; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

        else
          emit(:tLPAREN_ARG, '('.freeze, @te - 1, @te)
           @cs = 345; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

        end
       end
		end
	when 109 then
# line 1016 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit(:tLPAREN2, '('.freeze)
            @cs = 345; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 110 then
# line 1022 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit(:tLBRACK, '['.freeze, @te - 1, @te)
            @cs = 345; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 111 then
# line 1028 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        if @lambda_stack.last == @paren_nest
          @lambda_stack.pop
          emit(:tLAMBEG, '{'.freeze, @te - 1, @te)
        else
          emit(:tLCURLY, '{'.freeze, @te - 1, @te)
        end
        @command_start = true
        @paren_nest += 1
         @cs = 508; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 112 then
# line 1056 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  p = p - 1; 	begin
		 @cs = 345
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 113 then
# line 1075 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        diagnostic :warning, :ambiguous_prefix, { :prefix => tok(tm, @te) },
                   range(tm, @te)

        p = tm - 1
        	begin
		 @cs = 345
		_trigger_goto = true
		_goto_level = _again
		break
	end

       end
		end
	when 114 then
# line 1091 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  p = p - 1; 	begin
		 @cs = 345
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 115 then
# line 1114 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        p = @ts - 1
        	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end

       end
		end
	when 116 then
# line 1119 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1;		end
	when 117 then
# line 1122 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 118 then
# line 1125 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  p = p - 1; 	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 119 then
# line 1128 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  p = p - 1; 	begin
		 @cs = 345
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 120 then
# line 1119 "lib/parser/lexer.rl"
		begin
 begin p = (( @te))-1; end
		end
	when 121 then
# line 1128 "lib/parser/lexer.rl"
		begin
 begin p = (( @te))-1; end
 begin  p = p - 1; 	begin
		 @cs = 345
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 122 then
# line 1 "NONE"
		begin
	case  @act
	when 33 then
	begin begin p = (( @te))-1; end

        check_ambiguous_slash(tm)

        p = tm - 1
        	begin
		 @cs = 345
		_trigger_goto = true
		_goto_level = _again
		break
	end

      end
	when 34 then
	begin begin p = (( @te))-1; end

        diagnostic :warning, :ambiguous_prefix, { :prefix => tok(tm, @te) },
                   range(tm, @te)

        p = tm - 1
        	begin
		 @cs = 345
		_trigger_goto = true
		_goto_level = _again
		break
	end

      end
	when 39 then
	begin begin p = (( @te))-1; end

        p = @ts - 1
        	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end

      end
	else
	begin begin p = (( @te))-1; end
end
end 
			end
	when 123 then
# line 1151 "lib/parser/lexer.rl"
		begin
 @act = 46;		end
	when 124 then
# line 1164 "lib/parser/lexer.rl"
		begin
 @act = 47;		end
	when 125 then
# line 1164 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  p = @ts - 1
           	begin
		 @cs = 276
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 126 then
# line 566 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
    # Sit at EOF indefinitely. #advance would return $eof each time.
    # This allows to feed the lexer more data if needed; this is only used
    # in tests.
    #
    # Note that this action is not embedded into e_eof like e_nl and e_bs
    # below. This is due to the fact that scanner state at EOF is observed
    # by tests, and encapsulating it in a rule would break the introspection.
    p = p - 1; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

   end
		end
	when 127 then
# line 1141 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        emit(:tLPAREN_ARG, '('.freeze, @te - 1, @te)
        if version?(18)
           @cs = 508; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

        else
           @cs = 345; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

        end
       end
		end
	when 128 then
# line 1164 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  p = @ts - 1
           	begin
		 @cs = 276
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 129 then
# line 1164 "lib/parser/lexer.rl"
		begin
 begin p = (( @te))-1; end
 begin  p = @ts - 1
           	begin
		 @cs = 276
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 130 then
# line 1 "NONE"
		begin
	case  @act
	when 46 then
	begin begin p = (( @te))-1; end

        if @cond.active?
          emit(:kDO_COND, 'do'.freeze, @te - 2, @te)
        else
          emit(:kDO, 'do'.freeze, @te - 2, @te)
        end
         @cs = 508; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      end
	when 47 then
	begin begin p = (( @te))-1; end
 p = @ts - 1
           	begin
		 @cs = 276
		_trigger_goto = true
		_goto_level = _again
		break
	end
 end
end 
			end
	when 131 then
# line 1200 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  emit_do(true)
            @cs = 508; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 132 then
# line 1206 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  p = p - 1; 	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 133 then
# line 566 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
    # Sit at EOF indefinitely. #advance would return $eof each time.
    # This allows to feed the lexer more data if needed; this is only used
    # in tests.
    #
    # Note that this action is not embedded into e_eof like e_nl and e_bs
    # below. This is due to the fact that scanner state at EOF is observed
    # by tests, and encapsulating it in a rule would break the introspection.
    p = p - 1; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

   end
		end
	when 134 then
# line 1187 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        if @lambda_stack.last == @paren_nest
          @lambda_stack.pop
          emit(:tLAMBEG, '{'.freeze)
        else
          emit(:tLBRACE_ARG, '{'.freeze)
        end
        @paren_nest += 1
        @command_start = true
         @cs = 508; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 135 then
# line 1203 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1;		end
	when 136 then
# line 1206 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  p = p - 1; 	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 137 then
# line 1218 "lib/parser/lexer.rl"
		begin
 @act = 54;		end
	when 138 then
# line 1222 "lib/parser/lexer.rl"
		begin
 @act = 55;		end
	when 139 then
# line 1230 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  p = p - 1; 	begin
		 @cs = 345
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 140 then
# line 566 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
    # Sit at EOF indefinitely. #advance would return $eof each time.
    # This allows to feed the lexer more data if needed; this is only used
    # in tests.
    #
    # Note that this action is not embedded into e_eof like e_nl and e_bs
    # below. This is due to the fact that scanner state at EOF is observed
    # by tests, and encapsulating it in a rule would break the introspection.
    p = p - 1; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

   end
		end
	when 141 then
# line 1222 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  p = @ts - 1; 	begin
		 @cs = 345
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 142 then
# line 1224 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1;		end
	when 143 then
# line 1227 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  p = p - 1; 	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 144 then
# line 1230 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  p = p - 1; 	begin
		 @cs = 345
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 145 then
# line 1 "NONE"
		begin
	case  @act
	when 54 then
	begin begin p = (( @te))-1; end
 emit_table(KEYWORDS)
            @cs = 345; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
 end
	when 55 then
	begin begin p = (( @te))-1; end
 p = @ts - 1; 	begin
		 @cs = 345
		_trigger_goto = true
		_goto_level = _again
		break
	end
 end
end 
			end
	when 146 then
# line 1245 "lib/parser/lexer.rl"
		begin
 @act = 60;		end
	when 147 then
# line 1348 "lib/parser/lexer.rl"
		begin
 @act = 67;		end
	when 148 then
# line 1442 "lib/parser/lexer.rl"
		begin
 @act = 76;		end
	when 149 then
# line 1483 "lib/parser/lexer.rl"
		begin
 @act = 80;		end
	when 150 then
# line 1489 "lib/parser/lexer.rl"
		begin
 @act = 81;		end
	when 151 then
# line 1495 "lib/parser/lexer.rl"
		begin
 @act = 82;		end
	when 152 then
# line 1586 "lib/parser/lexer.rl"
		begin
 @act = 86;		end
	when 153 then
# line 831 "lib/parser/lexer.rl"
		begin
 @act = 87;		end
	when 154 then
# line 1632 "lib/parser/lexer.rl"
		begin
 @act = 91;		end
	when 155 then
# line 1245 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        emit(:tUNARY_NUM, tok(@ts, @ts + 1), @ts, @ts + 1)
        p = p - 1;  @cs = 516; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 156 then
# line 1262 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        type = delimiter = tok[0].chr
        @strings.push_literal(type, delimiter, @ts)

        p = p - 1;
        	begin
		 @cs = 128
		_trigger_goto = true
		_goto_level = _again
		break
	end

       end
		end
	when 157 then
# line 1272 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        type, delimiter = @source_buffer.slice(@ts, 1).chr, tok[-1].chr
        @strings.push_literal(type, delimiter, @ts)
        	begin
		 @cs = 128
		_trigger_goto = true
		_goto_level = _again
		break
	end

       end
		end
	when 158 then
# line 1280 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        type, delimiter = tok[0..-2], tok[-1].chr
        @strings.push_literal(type, delimiter, @ts)
        	begin
		 @cs = 128
		_trigger_goto = true
		_goto_level = _again
		break
	end

       end
		end
	when 159 then
# line 1357 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        p = p - 1; p = p - 1;
        emit(:tSYMBEG, tok(@ts, @ts + 1), @ts, @ts + 1)
        	begin
		 @cs = 134
		_trigger_goto = true
		_goto_level = _again
		break
	end

       end
		end
	when 160 then
# line 1365 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        type, delimiter = tok, tok[-1].chr
        @strings.push_literal(type, delimiter, @ts);

        	begin
		 @cs = 128
		_trigger_goto = true
		_goto_level = _again
		break
	end

       end
		end
	when 161 then
# line 1375 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        emit(:tSYMBOL, tok(@ts + 1, @ts + 2))
         @cs = 516; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 162 then
# line 1389 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        gvar_name = tok(@ts + 1)

        if @version >= 33 && gvar_name.start_with?('$0') && gvar_name.length > 2
          diagnostic :error, :gvar_name, { :name => gvar_name }, range(@ts + 1, @te)
        end

        emit(:tSYMBOL, gvar_name, @ts)
         @cs = 516; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 163 then
# line 1416 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        p, next_state = @strings.read_character_constant(@ts)
        p = p - 1; # Ragel will do `p += 1` to consume input, prevent it

        # If strings lexer founds a character constant (?a) emit it,
        # otherwise read ternary operator
        if @token_queue.empty?
          	begin
		 @cs = (next_state)
		_trigger_goto = true
		_goto_level = _again
		break
	end

        else
           @cs = (next_state);
          	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

        end
       end
		end
	when 164 then
# line 1431 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        diagnostic :fatal, :incomplete_escape, nil, range(@ts, @ts + 1)
       end
		end
	when 165 then
# line 1483 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  emit_table(PUNCTUATION_BEGIN)
           	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 166 then
# line 1504 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        p = p - 1;

        if version?(18)
          ident = tok(@ts, @te - 2)

          emit((@source_buffer.slice(@ts, 1) =~ /[A-Z]/) ? :tCONSTANT : :tIDENTIFIER,
               ident, @ts, @te - 2)
          p = p - 1; # continue as a symbol

          if !@static_env.nil? && @static_env.declared?(ident)
             @cs = 516;
          else
             @cs = (arg_or_cmdarg(cmd_state));
          end
        else
          emit(:tLABEL, tok(@ts, @te - 2), @ts, @te - 1)
           @cs = 501;
        end

        	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 167 then
# line 1542 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        # Here we scan and conditionally emit "\n":
        # + if it's there
        #   + and emitted we do nothing
        #   + and not emitted we return `p` to "\n" to process it on the next scan
        # + if it's not there we do nothing
        followed_by_nl = @te - 1 == @newline_s
        nl_emitted = false
        dots_te = followed_by_nl ? @te - 1 : @te

        if @version >= 30
          if @lambda_stack.any? && @lambda_stack.last + 1 == @paren_nest
            # To reject `->(...)` like `->...`
            emit(:tDOT3, '...'.freeze, @ts, dots_te)
          else
            emit(:tBDOT3, '...'.freeze, @ts, dots_te)

            if @version >= 31 && followed_by_nl && @context.in_argdef
              emit(:tNL, @te - 1, @te)
              nl_emitted = true
            end
          end
        elsif @version >= 27
          emit(:tBDOT3, '...'.freeze, @ts, dots_te)
        else
          emit(:tDOT3, '...'.freeze, @ts, dots_te)
        end

        if followed_by_nl && !nl_emitted
          # return "\n" to process it on the next scan
          p = p - 1;
        end

         @cs = 345; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 168 then
# line 1597 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        emit(:tIDENTIFIER, ident_tok, ident_ts, ident_te)
        p = ident_te - 1

        if !@static_env.nil? && @static_env.declared?(ident_tok) && @version < 25
           @cs = 247;
        else
           @cs = 307;
        end
        	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 169 then
# line 1616 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        p = @ts - 1
        @cs_before_block_comment = @cs
        	begin
		 @cs = 710
		_trigger_goto = true
		_goto_level = _again
		break
	end

       end
		end
	when 170 then
# line 1632 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  p = @ts - 1; 	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 171 then
# line 566 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
    # Sit at EOF indefinitely. #advance would return $eof each time.
    # This allows to feed the lexer more data if needed; this is only used
    # in tests.
    #
    # Note that this action is not embedded into e_eof like e_nl and e_bs
    # below. This is due to the fact that scanner state at EOF is observed
    # by tests, and encapsulating it in a rule would break the introspection.
    p = p - 1; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

   end
		end
	when 172 then
# line 1245 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        emit(:tUNARY_NUM, tok(@ts, @ts + 1), @ts, @ts + 1)
        p = p - 1;  @cs = 516; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 173 then
# line 1252 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit(:tSTAR, '*'.freeze)
           	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 174 then
# line 1287 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        diagnostic :fatal, :string_eof, nil, range(@ts, @ts + 1)
       end
		end
	when 175 then
# line 1301 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        tok(@ts, heredoc_e) =~ /^<<(-?)(~?)(["'`]?)(.*)\3$/m

        indent      = !$1.empty? || !$2.empty?
        dedent_body = !$2.empty?
        type        =  $3.empty? ? '<<"'.freeze : ('<<'.freeze + $3)
        delimiter   =  $4

        if @version >= 27
          if delimiter.count("\n") > 0 || delimiter.count("\r") > 0
            diagnostic :error, :unterminated_heredoc_id, nil, range(@ts, @ts + 1)
          end
        elsif @version >= 24
          if delimiter.count("\n") > 0
            if delimiter.end_with?("\n")
              diagnostic :warning, :heredoc_id_ends_with_nl, nil, range(@ts, @ts + 1)
              delimiter = delimiter.rstrip
            else
              diagnostic :fatal, :heredoc_id_has_newline, nil, range(@ts, @ts + 1)
            end
          end
        end

        if dedent_body && version?(18, 19, 20, 21, 22)
          emit(:tLSHFT, '<<'.freeze, @ts, @ts + 2)
          p = @ts + 1
           @cs = 345; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

        else
          @strings.push_literal(type, delimiter, @ts, heredoc_e, indent, dedent_body);
          @strings.herebody_s ||= new_herebody_s

          p = @strings.herebody_s - 1
           @cs = 128;
        end
       end
		end
	when 176 then
# line 1348 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        diagnostic :error, :unterminated_heredoc_id, nil, range(@ts, @ts + 1)
       end
		end
	when 177 then
# line 1381 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        emit(:tSYMBOL, tok(@ts + 1, tm), @ts, tm)
        p = tm - 1
         @cs = 516; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 178 then
# line 1389 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        gvar_name = tok(@ts + 1)

        if @version >= 33 && gvar_name.start_with?('$0') && gvar_name.length > 2
          diagnostic :error, :gvar_name, { :name => gvar_name }, range(@ts + 1, @te)
        end

        emit(:tSYMBOL, gvar_name, @ts)
         @cs = 516; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 179 then
# line 1403 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        emit_colon_with_digits(p, tm, diag_msg)

         @cs = 516; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 180 then
# line 1431 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        diagnostic :fatal, :incomplete_escape, nil, range(@ts, @ts + 1)
       end
		end
	when 181 then
# line 1459 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        if @lambda_stack.last == @paren_nest
          @lambda_stack.pop
          @command_start = true
          emit(:tLAMBEG, '{'.freeze)
        else
          emit(:tLBRACE, '{'.freeze)
        end
        @paren_nest += 1
        	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 182 then
# line 1473 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit(:tLBRACK, '['.freeze)
           	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 183 then
# line 1478 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit(:tLPAREN, '('.freeze)
           	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 184 then
# line 1483 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit_table(PUNCTUATION_BEGIN)
           	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 185 then
# line 1489 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit(:kRESCUE, 'rescue'.freeze, @ts, tm)
           p = tm - 1
            @cs = 321; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 186 then
# line 1531 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        if @version >= 27
          emit(:tBDOT2)
        else
          emit(:tDOT2)
        end

         @cs = 345; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 187 then
# line 1542 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        # Here we scan and conditionally emit "\n":
        # + if it's there
        #   + and emitted we do nothing
        #   + and not emitted we return `p` to "\n" to process it on the next scan
        # + if it's not there we do nothing
        followed_by_nl = @te - 1 == @newline_s
        nl_emitted = false
        dots_te = followed_by_nl ? @te - 1 : @te

        if @version >= 30
          if @lambda_stack.any? && @lambda_stack.last + 1 == @paren_nest
            # To reject `->(...)` like `->...`
            emit(:tDOT3, '...'.freeze, @ts, dots_te)
          else
            emit(:tBDOT3, '...'.freeze, @ts, dots_te)

            if @version >= 31 && followed_by_nl && @context.in_argdef
              emit(:tNL, @te - 1, @te)
              nl_emitted = true
            end
          end
        elsif @version >= 27
          emit(:tBDOT3, '...'.freeze, @ts, dots_te)
        else
          emit(:tDOT3, '...'.freeze, @ts, dots_te)
        end

        if followed_by_nl && !nl_emitted
          # return "\n" to process it on the next scan
          p = p - 1;
        end

         @cs = 345; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 188 then
# line 1586 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  p = @ts - 1
           	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 189 then
# line 831 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
    emit(:tIDENTIFIER)

    if !@static_env.nil? && @static_env.declared?(tok)
       @cs = 247; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    elsif @version >= 32 && tok =~ /\A_[1-9]\z/
       @cs = 247; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    else
       @cs = (arg_or_cmdarg(cmd_state)); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    end
   end
		end
	when 190 then
# line 1613 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1;		end
	when 191 then
# line 1616 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        p = @ts - 1
        @cs_before_block_comment = @cs
        	begin
		 @cs = 710
		_trigger_goto = true
		_goto_level = _again
		break
	end

       end
		end
	when 192 then
# line 1632 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  p = @ts - 1; 	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 193 then
# line 1287 "lib/parser/lexer.rl"
		begin
 begin p = (( @te))-1; end
 begin 
        diagnostic :fatal, :string_eof, nil, range(@ts, @ts + 1)
       end
		end
	when 194 then
# line 1348 "lib/parser/lexer.rl"
		begin
 begin p = (( @te))-1; end
 begin 
        diagnostic :error, :unterminated_heredoc_id, nil, range(@ts, @ts + 1)
       end
		end
	when 195 then
# line 831 "lib/parser/lexer.rl"
		begin
 begin p = (( @te))-1; end
 begin 
    emit(:tIDENTIFIER)

    if !@static_env.nil? && @static_env.declared?(tok)
       @cs = 247; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    elsif @version >= 32 && tok =~ /\A_[1-9]\z/
       @cs = 247; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    else
       @cs = (arg_or_cmdarg(cmd_state)); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    end
   end
		end
	when 196 then
# line 1613 "lib/parser/lexer.rl"
		begin
 begin p = (( @te))-1; end
		end
	when 197 then
# line 1632 "lib/parser/lexer.rl"
		begin
 begin p = (( @te))-1; end
 begin  p = @ts - 1; 	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 198 then
# line 1 "NONE"
		begin
	case  @act
	when 60 then
	begin begin p = (( @te))-1; end

        emit(:tUNARY_NUM, tok(@ts, @ts + 1), @ts, @ts + 1)
        p = p - 1;  @cs = 516; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      end
	when 67 then
	begin begin p = (( @te))-1; end

        diagnostic :error, :unterminated_heredoc_id, nil, range(@ts, @ts + 1)
      end
	when 76 then
	begin begin p = (( @te))-1; end

        if @version >= 27
          emit(:tPIPE, tok(@ts, @ts + 1), @ts, @ts + 1)
          p = p - 1;
           @cs = 345; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

        else
          p -= 2
          	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end

        end
      end
	when 80 then
	begin begin p = (( @te))-1; end
 emit_table(PUNCTUATION_BEGIN)
           	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
 end
	when 81 then
	begin begin p = (( @te))-1; end
 emit(:kRESCUE, 'rescue'.freeze, @ts, tm)
           p = tm - 1
            @cs = 321; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
 end
	when 82 then
	begin begin p = (( @te))-1; end
 emit_table(KEYWORDS_BEGIN)
           @command_start = true
            @cs = 508; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
 end
	when 86 then
	begin begin p = (( @te))-1; end
 p = @ts - 1
           	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end
 end
	when 87 then
	begin begin p = (( @te))-1; end

    emit(:tIDENTIFIER)

    if !@static_env.nil? && @static_env.declared?(tok)
       @cs = 247; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    elsif @version >= 32 && tok =~ /\A_[1-9]\z/
       @cs = 247; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    else
       @cs = (arg_or_cmdarg(cmd_state)); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    end
  end
	when 91 then
	begin begin p = (( @te))-1; end
 p = @ts - 1; 	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end
 end
end 
			end
	when 199 then
# line 1652 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  p = p - 1; 	begin
		 @cs = 345
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 200 then
# line 566 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
    # Sit at EOF indefinitely. #advance would return $eof each time.
    # This allows to feed the lexer more data if needed; this is only used
    # in tests.
    #
    # Note that this action is not embedded into e_eof like e_nl and e_bs
    # below. This is due to the fact that scanner state at EOF is observed
    # by tests, and encapsulating it in a rule would break the introspection.
    p = p - 1; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

   end
		end
	when 201 then
# line 1640 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1;		end
	when 202 then
# line 1643 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
      if @context.in_kwarg
        p = p - 1; 	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end

      else
        	begin
		 @cs = 710
		_trigger_goto = true
		_goto_level = _again
		break
	end

      end
     end
		end
	when 203 then
# line 1652 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  p = p - 1; 	begin
		 @cs = 345
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 204 then
# line 1662 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  p = @ts - 1
           	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 205 then
# line 1667 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        @strings.push_literal(tok, tok, @ts)
        	begin
		 @cs = 128
		_trigger_goto = true
		_goto_level = _again
		break
	end

       end
		end
	when 206 then
# line 1678 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  p = p - 1; 	begin
		 @cs = 345
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 207 then
# line 566 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
    # Sit at EOF indefinitely. #advance would return $eof each time.
    # This allows to feed the lexer more data if needed; this is only used
    # in tests.
    #
    # Note that this action is not embedded into e_eof like e_nl and e_bs
    # below. This is due to the fact that scanner state at EOF is observed
    # by tests, and encapsulating it in a rule would break the introspection.
    p = p - 1; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

   end
		end
	when 208 then
# line 1672 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1;		end
	when 209 then
# line 1675 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  	begin
		 @cs = 710
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 210 then
# line 1678 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  p = p - 1; 	begin
		 @cs = 345
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 211 then
# line 1678 "lib/parser/lexer.rl"
		begin
 begin p = (( @te))-1; end
 begin  p = p - 1; 	begin
		 @cs = 345
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 212 then
# line 1697 "lib/parser/lexer.rl"
		begin
 @act = 104;		end
	when 213 then
# line 1726 "lib/parser/lexer.rl"
		begin
 @act = 105;		end
	when 214 then
# line 1730 "lib/parser/lexer.rl"
		begin
 @act = 106;		end
	when 215 then
# line 1735 "lib/parser/lexer.rl"
		begin
 @act = 107;		end
	when 216 then
# line 1740 "lib/parser/lexer.rl"
		begin
 @act = 108;		end
	when 217 then
# line 1745 "lib/parser/lexer.rl"
		begin
 @act = 109;		end
	when 218 then
# line 1749 "lib/parser/lexer.rl"
		begin
 @act = 110;		end
	when 219 then
# line 1760 "lib/parser/lexer.rl"
		begin
 @act = 111;		end
	when 220 then
# line 1774 "lib/parser/lexer.rl"
		begin
 @act = 112;		end
	when 221 then
# line 1788 "lib/parser/lexer.rl"
		begin
 @act = 113;		end
	when 222 then
# line 1806 "lib/parser/lexer.rl"
		begin
 @act = 115;		end
	when 223 then
# line 1818 "lib/parser/lexer.rl"
		begin
 @act = 116;		end
	when 224 then
# line 1833 "lib/parser/lexer.rl"
		begin
 @act = 117;		end
	when 225 then
# line 1862 "lib/parser/lexer.rl"
		begin
 @act = 119;		end
	when 226 then
# line 831 "lib/parser/lexer.rl"
		begin
 @act = 123;		end
	when 227 then
# line 1884 "lib/parser/lexer.rl"
		begin
 @act = 124;		end
	when 228 then
# line 1910 "lib/parser/lexer.rl"
		begin
 @act = 126;		end
	when 229 then
# line 1916 "lib/parser/lexer.rl"
		begin
 @act = 127;		end
	when 230 then
# line 1689 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        emit(:tLAMBDA, '->'.freeze, @ts, @ts + 2)

        @lambda_stack.push @paren_nest
         @cs = 247; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 231 then
# line 1730 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  emit_singleton_class
            @cs = 508; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 232 then
# line 1851 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        type, delimiter = tok, tok[-1].chr
        @strings.push_literal(type, delimiter, @ts, nil, false, false, true);
        	begin
		 @cs = 128
		_trigger_goto = true
		_goto_level = _again
		break
	end

       end
		end
	when 233 then
# line 1870 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  p = @ts - 1; 	begin
		 @stack[ @top] =  @cs
		 @top+= 1
		 @cs = 129
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 234 then
# line 1877 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  emit_table(PUNCTUATION)
            @cs = 255; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 235 then
# line 1901 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        emit_table(PUNCTUATION)
         @cs = 508; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 236 then
# line 1910 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        emit_table(PUNCTUATION);
         @cs = 508; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 237 then
# line 1937 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  emit(:tOP_ASGN, tok(@ts, @te - 1))
            @cs = 345; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 238 then
# line 1941 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  emit(:tEH, '?'.freeze)
            @cs = 508; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 239 then
# line 1949 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        if @paren_nest == 0
          diagnostic :warning, :triple_dot_at_eol, nil, range(@ts, @te - 1)
        end

        emit(:tDOT3, '...'.freeze, @ts, @te - 1)
        p = p - 1;
         @cs = 345; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 240 then
# line 1960 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  emit_table(PUNCTUATION)
            @cs = 345; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 241 then
# line 1973 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  emit(:tSEMI, ';'.freeze)
           @command_start = true
            @cs = 508; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 242 then
# line 1977 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        diagnostic :error, :bare_backslash, nil, range(@ts, @ts + 1)
        p = p - 1;
       end
		end
	when 243 then
# line 1983 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        diagnostic :fatal, :unexpected, { :character => tok.inspect[1..-2] }
       end
		end
	when 244 then
# line 566 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
    # Sit at EOF indefinitely. #advance would return $eof each time.
    # This allows to feed the lexer more data if needed; this is only used
    # in tests.
    #
    # Note that this action is not embedded into e_eof like e_nl and e_bs
    # below. This is due to the fact that scanner state at EOF is observed
    # by tests, and encapsulating it in a rule would break the introspection.
    p = p - 1; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

   end
		end
	when 245 then
# line 1697 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        if @lambda_stack.last == @paren_nest
          @lambda_stack.pop

          if tok == '{'.freeze
            emit(:tLAMBEG, '{'.freeze)
          else # 'do'
            emit(:kDO_LAMBDA, 'do'.freeze)
          end
        else
          if tok == '{'.freeze
            emit(:tLCURLY, '{'.freeze)
          else # 'do'
            emit_do
          end
        end
        if tok == '{'.freeze
          @paren_nest += 1
        end
        @command_start = true

         @cs = 508; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 246 then
# line 1726 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit_table(KEYWORDS)
            @cs = 134; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 247 then
# line 1730 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit_singleton_class
            @cs = 508; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 248 then
# line 1740 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit_table(KEYWORDS)
           @command_start = true
            @cs = 508; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 249 then
# line 1788 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        digits = numeric_literal_int

        if version?(18, 19, 20)
          emit(:tINTEGER, digits.to_i(@num_base), @ts, @num_suffix_s)
          p = @num_suffix_s - 1
        else
          p = @num_xfrm.call(digits.to_i(@num_base), p)
        end
        	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 250 then
# line 1801 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        diagnostic :error, :no_dot_digit_literal
       end
		end
	when 251 then
# line 1833 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        digits = tok(@ts, @num_suffix_s)

        if version?(18, 19, 20)
          emit(:tFLOAT, Float(digits), @ts, @num_suffix_s)
          p = @num_suffix_s - 1
        else
          p = @num_xfrm.call(digits, p)
        end
        	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 252 then
# line 1862 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit(:tCONSTANT)
            @cs = (arg_or_cmdarg(cmd_state)); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 253 then
# line 1866 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit(:tCONSTANT, tok(@ts, tm), @ts, tm)
           p = tm - 1; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 254 then
# line 1870 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  p = @ts - 1; 	begin
		 @stack[ @top] =  @cs
		 @top+= 1
		 @cs = 129
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 255 then
# line 1877 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit_table(PUNCTUATION)
            @cs = 255; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 256 then
# line 831 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
    emit(:tIDENTIFIER)

    if !@static_env.nil? && @static_env.declared?(tok)
       @cs = 247; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    elsif @version >= 32 && tok =~ /\A_[1-9]\z/
       @cs = 247; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    else
       @cs = (arg_or_cmdarg(cmd_state)); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    end
   end
		end
	when 257 then
# line 1884 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        if tm == @te
          # Suffix was consumed, e.g. foo!
          emit(:tFID)
        else
          # Suffix was not consumed, e.g. foo!=
          emit(:tIDENTIFIER, tok(@ts, tm), @ts, tm)
          p = tm - 1
        end
         @cs = 276; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 258 then
# line 1901 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        emit_table(PUNCTUATION)
         @cs = 508; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 259 then
# line 1910 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        emit_table(PUNCTUATION);
         @cs = 508; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 260 then
# line 1916 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit_table(PUNCTUATION)
            @cs = 345; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 261 then
# line 1920 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        emit_rbrace_rparen_rbrack

        if tok == '}'.freeze || tok == ']'.freeze
          if @version >= 25
             @cs = 516;
          else
             @cs = 313;
          end
        else # )
          # fnext expr_endfn; ?
        end

        	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 262 then
# line 1945 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit(:tLBRACK2, '['.freeze)
            @cs = 345; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 263 then
# line 1960 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit_table(PUNCTUATION)
            @cs = 345; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 264 then
# line 1967 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1;		end
	when 265 then
# line 1970 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  	begin
		 @cs = 696
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 266 then
# line 1983 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        diagnostic :fatal, :unexpected, { :character => tok.inspect[1..-2] }
       end
		end
	when 267 then
# line 1788 "lib/parser/lexer.rl"
		begin
 begin p = (( @te))-1; end
 begin 
        digits = numeric_literal_int

        if version?(18, 19, 20)
          emit(:tINTEGER, digits.to_i(@num_base), @ts, @num_suffix_s)
          p = @num_suffix_s - 1
        else
          p = @num_xfrm.call(digits.to_i(@num_base), p)
        end
        	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 268 then
# line 1801 "lib/parser/lexer.rl"
		begin
 begin p = (( @te))-1; end
 begin 
        diagnostic :error, :no_dot_digit_literal
       end
		end
	when 269 then
# line 1833 "lib/parser/lexer.rl"
		begin
 begin p = (( @te))-1; end
 begin 
        digits = tok(@ts, @num_suffix_s)

        if version?(18, 19, 20)
          emit(:tFLOAT, Float(digits), @ts, @num_suffix_s)
          p = @num_suffix_s - 1
        else
          p = @num_xfrm.call(digits, p)
        end
        	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
	when 270 then
# line 1983 "lib/parser/lexer.rl"
		begin
 begin p = (( @te))-1; end
 begin 
        diagnostic :fatal, :unexpected, { :character => tok.inspect[1..-2] }
       end
		end
	when 271 then
# line 1 "NONE"
		begin
	case  @act
	when 104 then
	begin begin p = (( @te))-1; end

        if @lambda_stack.last == @paren_nest
          @lambda_stack.pop

          if tok == '{'.freeze
            emit(:tLAMBEG, '{'.freeze)
          else # 'do'
            emit(:kDO_LAMBDA, 'do'.freeze)
          end
        else
          if tok == '{'.freeze
            emit(:tLCURLY, '{'.freeze)
          else # 'do'
            emit_do
          end
        end
        if tok == '{'.freeze
          @paren_nest += 1
        end
        @command_start = true

         @cs = 508; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      end
	when 105 then
	begin begin p = (( @te))-1; end
 emit_table(KEYWORDS)
            @cs = 134; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
 end
	when 106 then
	begin begin p = (( @te))-1; end
 emit_singleton_class
            @cs = 508; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
 end
	when 107 then
	begin begin p = (( @te))-1; end
 emit_table(KEYWORDS)
            @cs = 345; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
 end
	when 108 then
	begin begin p = (( @te))-1; end
 emit_table(KEYWORDS)
           @command_start = true
            @cs = 508; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
 end
	when 109 then
	begin begin p = (( @te))-1; end
 emit_table(KEYWORDS)
            @cs = 321; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
 end
	when 110 then
	begin begin p = (( @te))-1; end

        emit_table(KEYWORDS)

        if version?(18) && tok == 'not'.freeze
           @cs = 345; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

        else
           @cs = 276; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

        end
      end
	when 111 then
	begin begin p = (( @te))-1; end

        if version?(18)
          emit(:tIDENTIFIER)

          unless !@static_env.nil? && @static_env.declared?(tok)
             @cs = (arg_or_cmdarg(cmd_state));
          end
        else
          emit(:k__ENCODING__, '__ENCODING__'.freeze)
        end
        	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      end
	when 112 then
	begin begin p = (( @te))-1; end
 emit_table(KEYWORDS)
           	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
 end
	when 113 then
	begin begin p = (( @te))-1; end

        digits = numeric_literal_int

        if version?(18, 19, 20)
          emit(:tINTEGER, digits.to_i(@num_base), @ts, @num_suffix_s)
          p = @num_suffix_s - 1
        else
          p = @num_xfrm.call(digits.to_i(@num_base), p)
        end
        	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      end
	when 115 then
	begin begin p = (( @te))-1; end

        if version?(18, 19, 20)
          diagnostic :error,
                     :trailing_in_number, { :character => tok(@te - 1, @te) },
                     range(@te - 1, @te)
        else
          emit(:tINTEGER, tok(@ts, @te - 1).to_i, @ts, @te - 1)
          p = p - 1; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

        end
      end
	when 116 then
	begin begin p = (( @te))-1; end

        if version?(18, 19, 20)
          diagnostic :error,
                     :trailing_in_number, { :character => tok(@te - 1, @te) },
                     range(@te - 1, @te)
        else
          emit(:tFLOAT, tok(@ts, @te - 1).to_f, @ts, @te - 1)
          p = p - 1; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

        end
      end
	when 117 then
	begin begin p = (( @te))-1; end

        digits = tok(@ts, @num_suffix_s)

        if version?(18, 19, 20)
          emit(:tFLOAT, Float(digits), @ts, @num_suffix_s)
          p = @num_suffix_s - 1
        else
          p = @num_xfrm.call(digits, p)
        end
        	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      end
	when 119 then
	begin begin p = (( @te))-1; end
 emit(:tCONSTANT)
            @cs = (arg_or_cmdarg(cmd_state)); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
 end
	when 123 then
	begin begin p = (( @te))-1; end

    emit(:tIDENTIFIER)

    if !@static_env.nil? && @static_env.declared?(tok)
       @cs = 247; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    elsif @version >= 32 && tok =~ /\A_[1-9]\z/
       @cs = 247; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    else
       @cs = (arg_or_cmdarg(cmd_state)); 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

    end
  end
	when 124 then
	begin begin p = (( @te))-1; end

        if tm == @te
          # Suffix was consumed, e.g. foo!
          emit(:tFID)
        else
          # Suffix was not consumed, e.g. foo!=
          emit(:tIDENTIFIER, tok(@ts, tm), @ts, tm)
          p = tm - 1
        end
         @cs = 276; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      end
	when 126 then
	begin begin p = (( @te))-1; end

        emit_table(PUNCTUATION);
         @cs = 508; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

      end
	when 127 then
	begin begin p = (( @te))-1; end
 emit_table(PUNCTUATION)
            @cs = 345; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
 end
end 
			end
	when 272 then
# line 1999 "lib/parser/lexer.rl"
		begin
 @act = 140;		end
	when 273 then
# line 2038 "lib/parser/lexer.rl"
		begin
 @act = 144;		end
	when 274 then
# line 2024 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        emit(:tNL, nil, @newline_s, @newline_s + 1)
        if @version < 27
          p = p - 1;  @cs = 710; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

        else
          emit(:tBDOT3)
           @cs = 345; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

        end
       end
		end
	when 275 then
# line 2035 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  p = tm - 1; 	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 276 then
# line 2038 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  emit(:tNL, nil, @newline_s, @newline_s + 1)
           p = p - 1;  @cs = 710; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 277 then
# line 1999 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        if @version < 27
          # Ruby before 2.7 doesn't support comments before leading dot.
          # If a line after "a" starts with a comment then "a" is a self-contained statement.
          # So in that case we emit a special tNL token and start reading the
          # next line as a separate statement.
          #
          # Note: block comments before leading dot are not supported on any version of Ruby.
          emit(:tNL, nil, @newline_s, @newline_s + 1)
          p = p - 1;  @cs = 710; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

        end
       end
		end
	when 278 then
# line 2013 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        emit(:tNL, nil, @newline_s, @newline_s + 1)
        if @version < 27
          p = p - 1;  @cs = 710; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

        else
          emit(:tBDOT2)
           @cs = 345; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

        end
       end
		end
	when 279 then
# line 2035 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  p = tm - 1; 	begin
		 @cs = 516
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 280 then
# line 2038 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  emit(:tNL, nil, @newline_s, @newline_s + 1)
           p = p - 1;  @cs = 710; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 281 then
# line 1999 "lib/parser/lexer.rl"
		begin
 begin p = (( @te))-1; end
 begin 
        if @version < 27
          # Ruby before 2.7 doesn't support comments before leading dot.
          # If a line after "a" starts with a comment then "a" is a self-contained statement.
          # So in that case we emit a special tNL token and start reading the
          # next line as a separate statement.
          #
          # Note: block comments before leading dot are not supported on any version of Ruby.
          emit(:tNL, nil, @newline_s, @newline_s + 1)
          p = p - 1;  @cs = 710; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

        end
       end
		end
	when 282 then
# line 2038 "lib/parser/lexer.rl"
		begin
 begin p = (( @te))-1; end
 begin  emit(:tNL, nil, @newline_s, @newline_s + 1)
           p = p - 1;  @cs = 710; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
  end
		end
	when 283 then
# line 1 "NONE"
		begin
	case  @act
	when 140 then
	begin begin p = (( @te))-1; end

        if @version < 27
          # Ruby before 2.7 doesn't support comments before leading dot.
          # If a line after "a" starts with a comment then "a" is a self-contained statement.
          # So in that case we emit a special tNL token and start reading the
          # next line as a separate statement.
          #
          # Note: block comments before leading dot are not supported on any version of Ruby.
          emit(:tNL, nil, @newline_s, @newline_s + 1)
          p = p - 1;  @cs = 710; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

        end
      end
	when 144 then
	begin begin p = (( @te))-1; end
 emit(:tNL, nil, @newline_s, @newline_s + 1)
           p = p - 1;  @cs = 710; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end
 end
end 
			end
	when 284 then
# line 2048 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        emit_comment(@eq_begin_s, @te)
        	begin
		 @cs = (@cs_before_block_comment)
		_trigger_goto = true
		_goto_level = _again
		break
	end

       end
		end
	when 285 then
# line 2053 "lib/parser/lexer.rl"
		begin
 @te = p+1
		end
	when 286 then
# line 2048 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        emit_comment(@eq_begin_s, @te)
        	begin
		 @cs = (@cs_before_block_comment)
		_trigger_goto = true
		_goto_level = _again
		break
	end

       end
		end
	when 287 then
# line 2056 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin 
        diagnostic :fatal, :embedded_document, nil,
                   range(@eq_begin_s, @eq_begin_s + '=begin'.length)
       end
		end
	when 288 then
# line 2066 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  @eq_begin_s = @ts
           	begin
		 @cs = 704
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 289 then
# line 2070 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  p = pe - 3  end
		end
	when 290 then
# line 2073 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin  cmd_state = true; p = p - 1; 	begin
		 @cs = 508
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 291 then
# line 566 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
    # Sit at EOF indefinitely. #advance would return $eof each time.
    # This allows to feed the lexer more data if needed; this is only used
    # in tests.
    #
    # Note that this action is not embedded into e_eof like e_nl and e_bs
    # below. This is due to the fact that scanner state at EOF is observed
    # by tests, and encapsulating it in a rule would break the introspection.
    p = p - 1; 	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

   end
		end
	when 292 then
# line 2063 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1;		end
	when 293 then
# line 2066 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  @eq_begin_s = @ts
           	begin
		 @cs = 704
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 294 then
# line 2073 "lib/parser/lexer.rl"
		begin
 @te = p
p = p - 1; begin  cmd_state = true; p = p - 1; 	begin
		 @cs = 508
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 295 then
# line 2073 "lib/parser/lexer.rl"
		begin
 begin p = (( @te))-1; end
 begin  cmd_state = true; p = p - 1; 	begin
		 @cs = 508
		_trigger_goto = true
		_goto_level = _again
		break
	end
  end
		end
	when 296 then
# line 2080 "lib/parser/lexer.rl"
		begin
 @te = p+1
 begin 
        p, next_state = @strings.advance(p)

        p = p - 1; # Ragel will do `p += 1` to consume input, prevent it
         @cs = (next_state);
        	begin
		p += 1
		_trigger_goto = true
		_goto_level = _out
		break
	end

       end
		end
# line 12621 "lib/parser/lexer-F0.rb"
			end # action switch
		end
	end
	if _trigger_goto
		next
	end
	end
	if _goto_level <= _again
	_acts = _lex_to_state_actions[ @cs]
	_nacts = _lex_actions[_acts]
	_acts += 1
	while _nacts > 0
		_nacts -= 1
		_acts += 1
		case _lex_actions[_acts - 1]
	when 51 then
# line 1 "NONE"
		begin
 @ts = nil;		end
# line 12641 "lib/parser/lexer-F0.rb"
		end # to state action switch
	end
	if _trigger_goto
		next
	end
	if  @cs == 0
		_goto_level = _out
		next
	end
	p += 1
	if p != pe
		_goto_level = _resume
		next
	end
	end
	if _goto_level <= _test_eof
	if p == eof
	if _lex_eof_trans[ @cs] > 0
		_trans = _lex_eof_trans[ @cs] - 1;
		_goto_level = _eof_trans
		next;
	end
	end
	end
	if _goto_level <= _out
		break
	end
	end
	end

# line 281 "lib/parser/lexer.rl"
    # %

    # Ragel creates a local variable called `testEof` but it doesn't use
    # it in any assignment. This dead code is here to swallow the warning.
    # It has no runtime cost because Ruby doesn't produce any instructions from it.
    if false
      testEof
    end

    @p = p

    if @token_queue.any?
      @token_queue.shift
    elsif @cs == klass.lex_error
      [ false, [ '$error'.freeze, range(p - 1, p) ] ]
    else
      eof = @source_pts.size
      [ false, [ '$eof'.freeze,   range(eof, eof) ] ]
    end
  end

  protected

  def version?(*versions)
    versions.include?(@version)
  end

  def stack_pop
    @top -= 1
    @stack[@top]
  end

  def tok(s = @ts, e = @te)
    @source_buffer.slice(s, e - s)
  end

  def range(s = @ts, e = @te)
    Parser::Source::Range.new(@source_buffer, s, e)
  end

  def emit(type, value = tok, s = @ts, e = @te)
    token = [ type, [ value, range(s, e) ] ]

    @token_queue.push(token)

    @tokens.push(token) if @tokens

    token
  end

  def emit_table(table, s = @ts, e = @te)
    value = tok(s, e)

    emit(table[value], value, s, e)
  end

  def emit_do(do_block=false)
    if @cond.active?
      emit(:kDO_COND, 'do'.freeze)
    elsif @cmdarg.active? || do_block
      emit(:kDO_BLOCK, 'do'.freeze)
    else
      emit(:kDO, 'do'.freeze)
    end
  end

  def arg_or_cmdarg(cmd_state)
    if cmd_state
      self.class.lex_en_expr_cmdarg
    else
      self.class.lex_en_expr_arg
    end
  end

  def emit_comment(s = @ts, e = @te)
    if @comments
      @comments.push(Parser::Source::Comment.new(range(s, e)))
    end

    if @tokens
      @tokens.push([ :tCOMMENT, [ tok(s, e), range(s, e) ] ])
    end

    nil
  end

  def emit_comment_from_range(p, pe)
    emit_comment(@sharp_s, p == pe ? p - 2 : p)
  end

  def diagnostic(type, reason, arguments=nil, location=range, highlights=[])
    @diagnostics.process(
        Parser::Diagnostic.new(type, reason, arguments, location, highlights))
  end


  def e_lbrace
    @cond.push(false); @cmdarg.push(false)

    current_literal = @strings.literal
    if current_literal
      current_literal.start_interp_brace
    end
  end

  def numeric_literal_int
    digits = tok(@num_digits_s, @num_suffix_s)

    if digits.end_with? '_'.freeze
      diagnostic :error, :trailing_in_number, { :character => '_'.freeze },
                 range(@te - 1, @te)
    elsif digits.empty? && @num_base == 8 && version?(18)
      # 1.8 did not raise an error on 0o.
      digits = '0'.freeze
    elsif digits.empty?
      diagnostic :error, :empty_numeric
    elsif @num_base == 8 && (invalid_idx = digits.index(/[89]/))
      invalid_s = @num_digits_s + invalid_idx
      diagnostic :error, :invalid_octal, nil,
                 range(invalid_s, invalid_s + 1)
    end
    digits
  end

  def on_newline(p)
    @strings.on_newline(p)
  end

  def check_ambiguous_slash(tm)
    if tok(tm, tm + 1) == '/'.freeze
      # Ambiguous regexp literal.
      if @version < 30
        diagnostic :warning, :ambiguous_literal, nil, range(tm, tm + 1)
      else
        diagnostic :warning, :ambiguous_regexp, nil, range(tm, tm + 1)
      end
    end
  end

  def emit_global_var(ts = @ts, te = @te)
    if tok(ts, te) =~ /^\$([1-9][0-9]*)$/
      emit(:tNTH_REF, tok(ts + 1, te).to_i, ts, te)
    elsif tok =~ /^\$([&`'+])$/
      emit(:tBACK_REF, tok(ts, te), ts, te)
    else
      emit(:tGVAR, tok(ts, te), ts, te)
    end
  end

  def emit_class_var(ts = @ts, te = @te)
    if tok(ts, te) =~ /^@@[0-9]/
      diagnostic :error, :cvar_name, { :name => tok(ts, te) }
    end

    emit(:tCVAR, tok(ts, te), ts, te)
  end

  def emit_instance_var(ts = @ts, te = @te)
    if tok(ts, te) =~ /^@[0-9]/
      diagnostic :error, :ivar_name, { :name => tok(ts, te) }
    end

    emit(:tIVAR, tok(ts, te), ts, te)
  end

  def emit_rbrace_rparen_rbrack
    emit_table(PUNCTUATION)

    if @version < 24
      @cond.lexpop
      @cmdarg.lexpop
    else
      @cond.pop
      @cmdarg.pop
    end
  end

  def emit_colon_with_digits(p, tm, diag_msg)
    if @version >= 27
      diagnostic :error, diag_msg, { name: tok(tm, @te) }, range(tm, @te)
    else
      emit(:tCOLON, tok(@ts, @ts + 1), @ts, @ts + 1)
      p = @ts
    end
    p
  end

  def emit_singleton_class
    emit(:kCLASS, 'class'.freeze, @ts, @ts + 5)
    emit(:tLSHFT, '<<'.freeze,    @te - 2, @te)
  end

  # Mapping of strings to parser tokens.

  PUNCTUATION = {
    '='   => :tEQL,     '&'   => :tAMPER2,  '|'   => :tPIPE,
    '!'   => :tBANG,    '^'   => :tCARET,   '+'   => :tPLUS,
    '-'   => :tMINUS,   '*'   => :tSTAR2,   '/'   => :tDIVIDE,
    '%'   => :tPERCENT, '~'   => :tTILDE,   ','   => :tCOMMA,
    ';'   => :tSEMI,    '.'   => :tDOT,     '..'  => :tDOT2,
    '...' => :tDOT3,    '['   => :tLBRACK2, ']'   => :tRBRACK,
    '('   => :tLPAREN2, ')'   => :tRPAREN,  '?'   => :tEH,
    ':'   => :tCOLON,   '&&'  => :tANDOP,   '||'  => :tOROP,
    '-@'  => :tUMINUS,  '+@'  => :tUPLUS,   '~@'  => :tTILDE,
    '**'  => :tPOW,     '->'  => :tLAMBDA,  '=~'  => :tMATCH,
    '!~'  => :tNMATCH,  '=='  => :tEQ,      '!='  => :tNEQ,
    '>'   => :tGT,      '>>'  => :tRSHFT,   '>='  => :tGEQ,
    '<'   => :tLT,      '<<'  => :tLSHFT,   '<='  => :tLEQ,
    '=>'  => :tASSOC,   '::'  => :tCOLON2,  '===' => :tEQQ,
    '<=>' => :tCMP,     '[]'  => :tAREF,    '[]=' => :tASET,
    '{'   => :tLCURLY,  '}'   => :tRCURLY,  '`'   => :tBACK_REF2,
    '!@'  => :tBANG,    '&.'  => :tANDDOT,
  }

  PUNCTUATION_BEGIN = {
    '&'   => :tAMPER,   '*'   => :tSTAR,    '**'  => :tDSTAR,
    '+'   => :tUPLUS,   '-'   => :tUMINUS,  '::'  => :tCOLON3,
    '('   => :tLPAREN,  '{'   => :tLBRACE,  '['   => :tLBRACK,
  }

  KEYWORDS = {
    'if'     => :kIF_MOD,      'unless'   => :kUNLESS_MOD,
    'while'  => :kWHILE_MOD,   'until'    => :kUNTIL_MOD,
    'rescue' => :kRESCUE_MOD,  'defined?' => :kDEFINED,
    'BEGIN'  => :klBEGIN,      'END'      => :klEND,
  }

  KEYWORDS_BEGIN = {
    'if'     => :kIF,          'unless'   => :kUNLESS,
    'while'  => :kWHILE,       'until'    => :kUNTIL,
    'rescue' => :kRESCUE,      'defined?' => :kDEFINED,
    'BEGIN'  => :klBEGIN,      'END'      => :klEND,
  }

  ESCAPE_WHITESPACE = {
    " "  => '\s', "\r" => '\r', "\n" => '\n', "\t" => '\t',
    "\v" => '\v', "\f" => '\f'
  }

  %w(class module def undef begin end then elsif else ensure case when
     for break next redo retry in do return yield super self nil true
     false and or not alias __FILE__ __LINE__ __ENCODING__).each do |keyword|
    KEYWORDS_BEGIN[keyword] = KEYWORDS[keyword] = :"k#{keyword.upcase}"
  end

  
# line 2089 "lib/parser/lexer.rl"

  # %
end
