# -*- encoding:utf-8; warn-indent:false; frozen_string_literal: true  -*-
#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.4.15
# from Racc grammer file "".
#

require 'racc/parser.rb'


require 'parser'

module Parser
  class Ruby28 < Parser::Base


  def version
    28
  end

  def default_encoding
    Encoding::UTF_8
  end
##### State transition tables begin ###

clist = [
'-704,618,-119,-117,299,-122,965,-704,-704,-704,299,-120,870,-704,-704',
'1078,-704,1083,-121,659,-116,870,219,220,-704,-704,618,-603,805,362',
'299,299,-713,1077,-603,661,-704,-704,618,-704,-704,-704,-704,-704,788',
'-604,219,220,230,231,618,-114,-604,811,618,556,219,220,-500,962,219',
'220,789,974,231,618,-704,-704,-704,-704,-704,-704,-704,-704,-704,-704',
'-704,-704,-704,-704,-118,-115,-704,-704,-704,658,686,-704,-701,231,-704',
'298,-603,-704,294,294,625,298,-704,1084,-704,660,-704,-704,810,-704',
'-704,-704,-704,-704,-604,-704,-704,-704,-121,964,231,298,298,123,-119',
'-117,-116,-122,122,-119,-117,-704,-122,-120,-704,-704,123,-109,-120',
'-704,-121,122,-116,-704,231,-121,-704,-116,123,-118,-704,-704,-704,122',
'231,-704,-704,-704,123,-704,123,-700,-100,122,-612,122,231,-704,-704',
'-704,-704,-704,123,-114,-86,807,123,122,-114,-704,-704,122,-704,-704',
'-704,-704,-704,123,556,-291,954,-713,122,101,102,920,123,625,-613,481',
'646,122,-118,-115,681,101,102,-118,-115,-704,-704,-704,-704,-704,-704',
'-704,-704,-704,-704,-704,-704,-704,-704,-114,230,-704,-704,-704,680',
'867,-704,231,866,-704,646,-112,-704,-704,646,-704,646,-704,646,-704',
'231,-704,-704,-121,-704,-704,-704,-704,-704,-316,-704,-704,-704,-115',
'-122,865,-316,-316,-316,-119,648,647,-316,-316,806,-316,-704,103,104',
'-704,-704,-704,-704,-316,-704,-605,-704,1137,-432,103,104,-704,-605',
'-117,-118,-316,-316,-119,-316,-316,-316,-316,-316,-713,648,647,644,362',
'648,647,648,647,648,647,644,852,483,646,836,631,838,745,482,895,-117',
'-316,-316,-316,-316,-316,-316,-316,-316,-316,-316,-316,-316,-316,-316',
'231,-432,-316,-316,-316,228,685,-316,-432,920,-316,-605,646,-316,226',
'870,801,-432,-316,810,-316,673,-316,-316,646,-316,-316,-316,-316,-316',
'671,-316,-704,-316,681,-116,-432,228,294,-704,-704,-704,648,647,644',
'-704,-704,-316,-704,876,-316,-316,-113,-316,872,-316,-704,-704,873,1137',
'973,1124,-316,631,-122,-120,-432,895,-704,-704,480,-704,-704,-704,-704',
'-704,648,647,653,227,583,-110,580,579,578,588,581,226,648,647,651,553',
'123,-119,292,591,-122,122,-704,-704,-704,-704,-704,-704,-704,-704,-704',
'-704,-704,-704,-704,-704,798,680,-704,-704,-704,586,686,-704,631,231',
'-704,646,632,-704,596,595,599,598,-704,646,-704,592,-704,-704,646,-704',
'-704,-704,-704,-704,-598,-704,-704,-704,852,-612,123,-598,-598,-598',
'227,122,-598,-598,-598,226,-598,-704,-108,796,-704,-704,292,-704,-598',
'-704,-598,-598,-598,123,-117,577,-704,123,122,-118,-598,-598,122,-598',
'-598,-598,-598,-598,720,648,647,657,795,-316,-598,-107,-105,648,647',
'662,-316,-598,648,647,649,-701,793,-116,-114,-316,-598,-598,-598,-598',
'-598,-598,-598,-598,-598,-598,-598,-598,-598,-598,227,-601,-598,-598',
'-598,-316,-598,-598,-601,791,-598,231,-316,-598,-598,231,-598,-701,-598',
'790,-598,-316,-598,-598,1000,-598,-598,-598,-598,-598,-608,-598,-601',
'-598,-316,-598,1001,-608,1002,-601,-601,-601,269,270,-601,-601,-601',
'-598,-601,1005,-598,-598,-598,-598,779,-598,-601,-598,-601,-601,-601',
'-111,-598,777,610,-598,-601,609,-601,-601,-316,-601,-601,-601,-601,-601',
'986,-610,268,267,775,226,-607,-609,-610,-606,591,774,1152,-607,-609',
'610,-606,-610,612,-608,907,636,-601,-601,-601,-601,-601,-601,-601,-601',
'-601,-601,-601,-601,-601,-601,1011,226,-601,-601,-601,231,-601,-601',
'225,771,-601,231,592,-601,-601,759,-601,223,-601,768,-601,-105,-601',
'-601,1004,-601,-601,-601,-601,-601,-610,-601,-316,-601,227,-607,-609',
'-613,-606,-316,-316,-316,87,427,-316,-316,-316,-601,-316,1029,-601,-601',
'-601,-601,88,-601,-316,-601,-316,-316,-316,-289,-601,1033,89,-601,227',
'610,-316,-316,612,-316,-316,-316,-316,-316,231,424,1036,226,1038,226',
'426,425,-106,774,1157,583,1152,580,579,578,789,581,1041,1155,-115,636',
'-316,-316,-316,-316,-316,-316,-316,-316,-316,-316,-316,-316,-316,-316',
'1043,986,-316,-316,-316,231,868,-316,1174,1043,-316,591,781,-316,-316',
'231,-316,231,-316,414,-316,785,-316,-316,1051,-316,-316,-316,-316,-316',
'-316,-316,227,-316,227,1122,1123,-316,-316,-316,599,598,610,-316,-316',
'612,-316,-316,1053,592,-316,-316,-316,-316,-316,-316,766,-316,949,920',
'765,1004,-316,949,920,-120,-316,-316,559,-316,-316,-316,-316,-316,890',
'891,219,220,583,226,580,579,578,588,581,583,628,580,579,578,1186,581',
'1062,591,1063,626,-316,-316,-316,-316,-316,-316,-316,-316,-316,-316',
'-316,-316,-316,-316,219,220,-316,-316,-316,586,685,-316,362,1068,-316',
'1069,781,-316,596,595,599,598,-316,1070,-316,592,-316,-316,-292,-316',
'-316,-316,-316,-316,588,-316,252,-316,227,362,226,231,231,231,591,-317',
'-351,634,231,1074,759,-316,-317,-351,-316,-316,636,-111,231,-316,226',
'-317,-351,231,231,577,-316,558,870,-120,321,77,78,24,9,65,479,599,598',
'71,72,1082,592,563,75,-700,73,74,76,33,34,79,80,126,127,128,129,130',
'81,31,30,111,110,112,113,227,562,21,231,231,-317,-351,1091,8,51,323',
'10,115,114,116,105,64,107,106,108,227,109,117,118,226,101,102,47,48',
'46,-317,478,-700,774,-611,1094,1097,-317,1099,-700,479,-611,1101,231',
'-700,1103,-317,748,-700,43,-611,-100,325,350,231,66,67,-316,603,68,231',
'38,1118,1119,-316,50,231,-700,349,-701,231,830,831,-316,22,832,117,118',
'733,99,87,90,91,1128,92,94,93,95,231,227,-106,288,88,98,870,-317,613',
'-700,1138,-611,82,1140,89,103,104,252,1150,44,45,321,77,78,24,9,65,252',
'252,1153,71,72,252,1158,-316,75,-697,73,74,76,33,34,79,80,126,127,128',
'129,130,81,31,30,111,110,112,113,614,583,21,580,579,578,1159,581,8,51',
'323,10,115,114,116,105,64,107,106,108,1043,109,117,118,-307,101,102',
'47,48,46,226,-307,-598,1043,-307,1043,222,548,781,-598,-307,-307,221',
'1171,-697,1172,546,785,-598,43,-307,530,36,1176,774,66,67,-611,1180',
'68,1182,38,1184,1186,-611,50,1186,-697,690,252,765,218,294,-611,22,625',
'217,416,1119,99,87,90,91,216,92,94,93,95,1200,-307,-113,1176,88,98,679',
'227,678,-598,231,-307,82,249,89,103,104,251,250,44,45,321,77,78,24,9',
'65,674,670,-701,71,72,-700,231,-611,75,-698,73,74,76,33,34,79,80,126',
'127,128,129,130,81,31,30,111,110,112,113,215,583,21,580,579,578,132',
'581,8,51,323,10,115,114,116,105,64,107,106,108,588,109,117,118,-610',
'101,102,47,48,46,591,-610,-601,1043,-317,669,630,-289,781,-601,-610',
'-317,667,638,-698,629,666,785,-601,43,-317,663,36,871,894,66,67,897',
'870,68,899,38,288,599,598,50,231,-698,592,252,526,905,907,909,22,528',
'530,514,528,99,87,90,91,514,92,94,93,95,759,-610,-112,231,88,98,530',
'1212,294,-601,1186,-317,82,249,89,103,104,251,250,44,45,321,77,78,24',
'9,65,294,759,514,71,72,252,132,920,75,-701,73,74,76,33,34,79,80,126',
'127,128,129,130,81,31,30,111,110,112,113,-85,252,21,231,853,541,542',
'-290,8,51,323,10,115,114,116,105,64,107,106,108,549,109,117,118,941',
'101,102,47,48,46,942,231,-701,249,840,839,252,251,250,-701,247,248,920',
'951,-701,952,835,231,-701,43,1186,1186,36,119,303,66,67,1176,231,68',
'583,38,580,579,578,50,581,-701,249,231,,,251,250,22,247,248,,,99,87',
'90,91,,92,94,93,95,,,,,88,98,,,,-701,,,82,,89,103,104,,,44,45,321,77',
'78,24,9,65,,,,71,72,,,,75,-704,73,74,76,33,34,79,80,,,,,,81,31,30,111',
'110,112,113,,583,21,580,579,578,,581,8,51,323,10,115,114,116,105,64',
'107,106,108,,109,117,118,,101,102,47,48,46,,583,-704,580,579,578,,581',
'781,-704,,,,,-700,,,,-704,43,,,325,,,66,67,,,68,1133,38,580,579,578',
'50,581,-704,781,1133,,580,579,578,22,581,,,,99,87,90,91,,92,94,93,95',
',,,,88,98,,,,-704,,,82,,89,103,104,,,44,45,321,77,78,24,9,65,,,,71,72',
',,,75,-700,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,21,',
',,,,8,51,323,10,115,114,116,105,64,107,106,108,,109,117,118,,101,102',
'47,48,46,,,-700,,,,,,,-700,,,,,-700,,,,-700,43,,,36,,,66,67,,,68,,38',
',,,50,,-700,,,,,,,22,,,,,99,87,90,91,,92,94,93,95,-108,,,,88,98,,,,-700',
',,82,,89,103,104,,,44,45,321,77,78,24,9,65,,,,71,72,,,,75,-701,73,74',
'76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,21,,,,,,8,51,323,10,115',
'114,116,105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,-701,,,',
',,,-701,,,,,-701,,,,-701,43,,,36,,,66,67,,,68,,38,,,,50,,-701,,,,,,',
'22,,,,,99,87,90,91,,92,94,93,95,-110,,,,88,98,,,,-701,,,82,,89,103,104',
',,44,45,321,77,78,24,9,65,,,,71,72,,,,75,-704,73,74,76,33,34,79,80,',
',,,,81,31,30,111,110,112,113,,,21,,,,,,8,51,323,10,115,114,116,105,64',
'107,106,108,,109,117,118,,101,102,47,48,46,,,-704,,,,,,,-704,,,,,-700',
',,,-704,43,,,36,,,66,67,,,68,,38,,,,50,,-704,,,,,,,22,,,,,99,87,90,91',
',92,94,93,95,-109,,,,88,98,,,,-704,,,82,,89,103,104,,,44,45,321,77,78',
'24,9,65,,,,71,72,,,,75,-704,73,74,76,33,34,79,80,,,,,,81,31,30,111,110',
'112,113,,,21,,,,,,8,51,323,10,115,114,116,105,64,107,106,108,,109,117',
'118,,101,102,47,48,46,,,-704,,,,,,,-704,,,,,-700,,,,-704,43,,,36,,,66',
'67,,,68,,38,,,,50,,-704,,,,,,,22,,,,,99,87,90,91,,92,94,93,95,,,,,88',
'98,,,,-704,,,82,,89,103,104,,,44,45,321,77,78,24,9,65,,,,71,72,,,,75',
'-432,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,21,,,,,,8',
'51,323,10,115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48',
'46,,,-432,,,,,,,-432,,,,,,,,,-432,43,,,36,,,66,67,,,68,,38,,,,50,,-432',
',,,,,,22,,,,,99,87,90,91,,92,94,93,95,-107,,,,88,98,,,,-432,,,82,,89',
'103,104,,,44,45,5,77,78,24,9,65,,,,71,72,,,,75,,73,74,76,33,34,79,80',
'244,,,,,81,31,30,111,110,112,113,,,21,,,,,,8,51,7,10,115,114,116,105',
'64,107,106,108,,109,117,118,,101,102,47,48,46,252,256,257,258,259,269',
'270,264,265,260,261,,245,246,,,262,263,,43,,-262,36,,,66,67,,,68,,38',
'249,,255,50,251,250,,247,248,268,267,253,22,254,,,,99,87,90,91,,92,94',
'93,95,,,,,88,98,,266,,,,,82,,89,103,104,,,44,45,321,77,78,24,9,65,,',
',71,72,,,,75,,73,74,76,33,34,79,80,244,,,,,81,31,30,111,110,112,113',
',,21,,,,,,8,51,323,10,115,114,116,105,64,107,106,108,,109,117,118,,101',
'102,47,48,46,252,256,257,258,259,269,270,264,265,260,261,,245,246,,',
'262,263,,43,,,36,,,66,67,,,68,,38,249,,255,50,251,250,,247,248,268,267',
'253,22,254,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,266,,,,,82,,89,103',
'104,,,44,45,321,77,78,24,9,65,,,,71,72,,,,75,,73,74,76,33,34,79,80,',
',,,,81,31,30,111,110,112,113,,,21,,,,,675,8,51,323,10,115,114,116,105',
'64,107,106,108,,109,117,118,,101,102,47,48,46,252,256,257,258,259,269',
'270,264,265,260,261,,245,246,,,262,263,,43,,,36,,,66,67,,,68,,38,249',
',255,50,251,250,,247,248,268,267,253,22,254,,,,99,87,90,91,,92,94,93',
'95,,,,,88,98,,266,,-262,,,82,,89,103,104,,,44,45,321,77,78,24,9,65,',
',,71,72,,,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,',
'21,,,,,675,8,51,323,10,115,114,116,105,64,107,106,108,,109,117,118,',
'101,102,47,48,46,252,256,257,258,259,269,270,264,265,260,261,,245,246',
',,262,263,,43,,,36,,,66,67,,,68,,38,249,,255,50,251,250,,247,248,268',
'267,253,22,254,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,266,,,,,82,,89',
'103,104,,,44,45,321,77,78,24,9,65,,,,71,72,,,,75,,73,74,76,33,34,79',
'80,,,,,,81,31,30,111,110,112,113,,,21,,,,,917,8,51,323,10,115,114,116',
'105,64,107,106,108,,109,117,118,,101,102,47,48,46,252,256,257,258,259',
'269,270,264,265,260,261,,245,246,,,262,263,,43,,,36,,,66,67,,,68,,38',
'249,,255,50,251,250,,247,248,268,267,253,22,254,,,,99,87,90,91,,92,94',
'93,95,,,,,88,98,,266,,,,,82,,89,103,104,,,44,45,321,77,78,24,9,65,,',
',71,72,,,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,21',
',,,,945,8,51,323,10,115,114,116,105,64,107,106,108,,109,117,118,,101',
'102,47,48,46,252,256,257,258,259,269,270,264,265,260,261,,245,246,,',
'262,263,,43,,,36,,,66,67,,,68,,38,249,,255,50,251,250,,247,248,268,267',
'253,22,254,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,266,,,,,82,,89,103',
'104,,,44,45,321,77,78,24,9,65,,,,71,72,,,,75,,73,74,76,33,34,79,80,',
',,,,81,31,30,111,110,112,113,,,21,,,,,,8,51,323,10,115,114,116,105,64',
'107,106,108,,109,117,118,,101,102,47,48,46,252,256,257,258,259,269,270',
'264,265,260,261,,245,246,,,262,263,,43,,,36,,,66,67,,,68,,38,249,,255',
'50,251,250,,247,248,268,267,253,22,254,,,,99,87,90,91,,92,94,93,95,',
',,,88,98,,266,,,,,82,,89,103,104,,,44,45,321,77,78,24,9,65,,,,71,72',
',,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,21,,,,,',
'8,51,323,10,115,114,116,105,64,107,106,108,,109,117,118,,101,102,47',
'48,46,252,256,257,258,259,269,270,264,265,260,261,,245,246,,,262,263',
',43,,,36,,,66,67,,,68,,38,249,,255,50,251,250,,247,248,268,267,253,22',
'254,,,,99,87,90,91,,92,94,93,95,,,,,88,98,231,266,,,,,82,,89,103,104',
',,44,45,321,77,78,24,9,65,,,,71,72,,,,75,,73,74,76,33,34,79,80,,,,,',
'81,31,30,111,110,112,113,,,21,,,,,,8,51,323,10,115,114,116,105,64,107',
'106,108,,109,117,118,,101,102,47,48,46,252,256,257,258,259,269,270,264',
'265,260,261,,245,246,,,262,263,,43,,,325,,,66,67,,,68,,38,249,,255,50',
'251,250,,247,248,268,267,253,22,254,,,,99,87,90,91,,92,94,93,95,,,,',
'88,98,,266,,,,,82,,89,103,104,,,44,45,321,77,78,24,9,65,,,,71,72,,,',
'75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,21,,,,,,8,51',
'323,10,115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48,46',
'252,256,257,258,259,269,270,264,265,260,261,,245,246,,,262,263,,43,',
',36,,,66,67,,,68,,38,249,,255,50,251,250,,247,248,268,267,253,22,254',
',,,99,87,90,91,,92,94,93,95,,,,,88,98,,266,,,,,82,,89,103,104,,,44,45',
'5,77,78,24,9,65,,,,71,72,,,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111',
'110,112,113,,,21,,,,,,8,51,7,10,115,114,116,105,64,107,106,108,,109',
'117,118,,101,102,47,48,46,252,256,257,258,259,269,270,264,265,260,261',
',245,246,,,262,263,,43,,,36,,,66,67,,,68,,38,249,,255,50,251,250,,247',
'248,268,267,253,22,254,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,266,,',
',,82,,89,103,104,,,44,45,321,77,78,24,9,65,,,,71,72,,,,75,,73,74,76',
'33,34,79,80,,,,,,81,31,30,111,110,112,113,,,21,,,,,,8,51,323,10,115',
'114,116,105,64,107,106,108,,109,117,118,,101,102,47,48,46,252,256,257',
'258,259,269,270,264,265,260,261,,245,246,,,262,263,,43,,,36,,,66,67',
',,68,,38,249,,255,50,251,250,,247,248,268,267,253,22,254,,,,99,87,90',
'91,,92,94,93,95,,,,,88,98,,266,,,,,82,,89,103,104,,,44,45,321,77,78',
'24,9,65,,,,71,72,,,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112',
'113,,,21,,,,,,8,51,323,10,115,114,116,105,64,107,106,108,,109,117,118',
',101,102,47,48,46,252,256,257,258,259,269,270,264,265,260,261,,245,246',
',,262,263,,43,,,36,,,66,67,,,68,,38,249,,255,50,251,250,,247,248,268',
'267,253,22,254,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,266,,,,,82,,89',
'103,104,,,44,45,321,77,78,24,9,65,,,,71,72,,,,75,,73,74,76,33,34,79',
'80,,,,,,81,31,30,111,110,112,113,,,21,,,,,,8,51,323,10,115,114,116,105',
'64,107,106,108,,109,117,118,,101,102,47,48,46,252,-726,-726,-726,-726',
'269,270,,,-726,-726,,,,,,262,263,,43,,,36,,,66,67,,,68,,38,249,,255',
'50,251,250,,247,248,268,267,253,22,254,,,,99,87,90,91,,92,94,93,95,',
',,,88,98,,,,,,,82,,89,103,104,,,44,45,321,77,78,24,9,65,,,,71,72,,,',
'75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,21,,,,,,8,51',
'323,10,115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48,46',
'252,256,257,258,259,269,270,264,265,260,261,,-726,-726,,,262,263,,43',
',,36,,,66,67,,,68,,38,249,,255,50,251,250,,247,248,268,267,253,22,254',
',,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,,,,82,,89,103,104,,,44,45,321',
'77,78,24,9,65,,,,71,72,,,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111',
'110,112,113,,,21,,,,,,8,51,323,10,115,114,116,105,64,107,106,108,,109',
'117,118,,101,102,47,48,46,252,256,257,258,259,269,270,264,265,260,261',
',-726,-726,,,262,263,,43,,,36,,,66,67,,,68,,38,249,,255,50,251,250,',
'247,248,268,267,253,22,254,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,',
',,,82,,89,103,104,,,44,45,321,77,78,24,9,65,,,,71,72,,,,75,,73,74,76',
'33,34,79,80,,,,,,81,31,30,111,110,112,113,,,21,,,,,,8,51,323,10,115',
'114,116,105,64,107,106,108,,109,117,118,,101,102,47,48,46,252,-726,-726',
'-726,-726,269,270,,,-726,-726,,,,,,262,263,,43,,,36,,,66,67,,,68,,38',
'249,,255,50,251,250,,247,248,268,267,253,22,254,,,,99,87,90,91,,92,94',
'93,95,,,,,88,98,,,,,,,82,,89,103,104,,,44,45,321,77,78,24,9,65,,,,71',
'72,,,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,21,,',
',,,8,51,323,10,115,114,116,105,64,107,106,108,,109,117,118,,101,102',
'47,48,46,252,-726,-726,-726,-726,269,270,,,-726,-726,,,,,,262,263,,43',
',,36,,,66,67,,,68,,38,249,,255,50,251,250,,247,248,268,267,253,22,254',
',,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,,,,82,,89,103,104,,,44,45,321',
'77,78,24,9,65,,,,71,72,,,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111',
'110,112,113,,,21,,,,,,8,51,323,10,115,114,116,105,64,107,106,108,,109',
'117,118,,101,102,47,48,46,252,-726,-726,-726,-726,269,270,,,-726,-726',
',,,,,262,263,,43,,,36,,,66,67,,,68,,38,249,,255,50,251,250,,247,248',
'268,267,253,22,254,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24',
'82,65,89,103,104,71,72,44,45,,75,,73,74,76,33,34,79,80,,,,,,81,31,30',
'111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,315',
'109,117,118,,101,102,47,48,46,252,-726,-726,-726,-726,269,270,,,-726',
'-726,,,,,,262,263,,236,,,242,,,66,67,,,68,,312,249,310,255,50,251,250',
'316,247,248,268,267,253,241,254,,,,99,313,90,91,,92,94,93,95,,,,,88',
'98,,,,,,,82,,89,103,104,,,44,45,77,78,24,9,65,,,,71,72,,,,75,,73,74',
'76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,21,,,,,,8,51,7,10,115',
'114,116,105,64,107,106,108,,109,117,118,,101,102,47,48,46,252,-726,-726',
'-726,-726,269,270,,,-726,-726,,,,,,262,263,,43,,,36,,,66,67,,,68,,38',
'249,,255,50,251,250,,247,248,268,267,253,22,254,,,,99,87,90,91,,92,94',
'93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74',
'76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,243,,,,,,,51,,,115,114',
'116,105,64,107,106,108,315,109,117,118,,101,102,47,48,46,252,256,257',
'258,259,269,270,,,260,261,,,,,,262,263,,236,,,242,,,66,67,,,68,,312',
'249,310,255,50,251,250,316,247,248,268,267,253,241,254,,,,99,313,90',
'91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,',
'75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,21,,,,,,,51',
',,115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48,46,252',
'256,257,258,259,269,270,264,,260,261,,,,,,262,263,,236,,,242,,,66,67',
',,68,,,249,,255,50,251,250,,247,248,268,267,253,22,254,,,,99,87,90,91',
',92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75',
',73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,21,,,,,,,51,,',
'115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48,46,252,,',
',,,,,,,,,,,,,262,263,,236,,,242,,,66,67,,,68,,,249,,255,50,251,250,',
'247,248,,,253,22,254,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78',
'24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,33,34,79,80,,,,,,81,31',
'30,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,',
'109,117,118,,101,102,47,48,46,252,,,,,,,,,,,,,,,,262,263,,236,,,242',
',,66,67,,,68,,,249,,255,50,251,250,,247,248,,,253,241,254,,,,99,87,90',
'91,,92,94,93,95,,,,,88,98,,,,,,,82,,89,103,104,-431,,44,45,,,,-431,-431',
'-431,,,-431,-431,-431,252,-431,,,,,,,,-431,-431,-431,-431,,,,262,263',
',,,-431,-431,,-431,-431,-431,-431,-431,,,,249,,255,,251,250,,247,248',
',,,,,,,,,,-431,-431,-431,-431,-431,-431,-431,-431,-431,-431,-431,-431',
'-431,-431,,,-431,-431,-431,,,-431,,294,-431,,,-431,-431,,-431,,-431',
',-431,,-431,-431,,-431,-431,-431,-431,-431,-323,-431,-431,-431,,,,-323',
'-323,-323,,,-323,-323,-323,,-323,-431,252,,-431,-431,,-431,-323,-431',
'-323,-323,,,,,-431,,262,263,-323,-323,,-323,-323,-323,-323,-323,,,,',
',,249,,,,251,250,,247,248,,,,,,,,-323,-323,-323,-323,-323,-323,-323',
'-323,-323,-323,-323,-323,-323,-323,,,-323,-323,-323,,,-323,,303,-323',
',,-323,-323,,-323,,-323,,-323,,-323,-323,,-323,-323,-323,-323,-323,',
'-323,,-323,,,,,,,,,,,,,,-323,,,-323,-323,,-323,,-323,77,78,24,,65,,-323',
',71,72,,,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,243',
',,,,,,51,,,115,114,116,105,64,107,106,108,315,109,117,118,,101,102,47',
'48,46,252,256,257,258,259,269,270,264,265,260,261,,-726,-726,,,262,263',
',236,,,242,,,66,67,,,68,,312,249,310,255,50,251,250,316,247,248,268',
'267,253,241,254,,,,99,313,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82',
'65,89,103,104,71,72,44,45,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111',
'110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,315,109',
'117,118,,101,102,47,48,46,252,256,257,258,259,269,270,264,265,260,261',
',-726,-726,,,262,263,,236,,,242,,,66,67,,,68,,312,249,310,255,50,251',
'250,316,247,248,268,267,253,241,254,,,,99,313,90,91,,92,94,93,95,,,',
',88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,33,34',
'79,80,,,,,,81,31,30,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105',
'64,107,106,108,315,109,117,118,,101,102,47,48,46,252,,,,,,,,,,,,,,,',
'262,263,,236,,,242,,,66,67,,,68,,312,249,310,255,50,251,250,316,247',
'248,,,,241,,,,,99,313,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65',
'89,103,104,71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111',
'110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,,109,117',
'118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,',
',50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82',
'65,89,103,104,71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345',
'111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,,109',
'117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68',
',,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78',
'24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81',
'337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106',
'108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66',
'67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,',
',,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,342,343,79,80',
',,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64',
'107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,',
'242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,',
',,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,342,343',
'79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105',
'64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236',
',,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95',
',,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,342',
'343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116',
'105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,',
',236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93',
'95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76',
'342,343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114',
'116,105,64,107,106,108,315,109,117,118,,101,102,47,48,46,,,,,,,,,,,',
',,,,,,,,236,,,242,,,66,67,,,68,,,,310,,50,,,316,,,,,,241,,,,,99,313',
'90,91,,92,94,93,95,,,,,88,98,,,,,,,82,,89,103,104,-308,,44,45,,,,-308',
'-308,-308,,,-308,-308,-308,,-308,,,,,,,,-308,,-308,-308,-308,,,,111',
'110,112,113,-308,-308,,-308,-308,-308,-308,-308,,,,,115,114,116,,,,',
',,,,,,101,102,,,346,-308,-308,-308,-308,-308,-308,-308,-308,-308,-308',
'-308,-308,-308,-308,,,-308,-308,-308,,,-308,,,-308,,,-308,-308,,-308',
',-308,,-308,,-308,-308,,-308,-308,-308,-308,-308,,-308,,-308,,99,87',
'90,91,,92,94,93,95,,,,-308,88,98,-308,-308,-308,-308,,-308,82,-308,89',
'103,104,,-308,77,78,24,9,65,,,,71,72,,,,75,,73,74,76,33,34,79,80,,,',
',,81,31,30,111,110,112,113,,,21,,,,,,8,51,,10,115,114,116,105,64,107',
'106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,43,,,36,,',
'66,67,,,68,,38,,,,50,,,,,,,,,22,,,,,99,87,90,91,,92,94,93,95,,,,,88',
'98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,342,343,79',
'80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105',
'64,107,106,108,315,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,',
'236,,,242,,,66,67,,,68,,312,,,,50,,,316,,,,,,241,,,,,99,313,90,91,,92',
'94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73',
'74,76,342,343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,',
',115,114,116,105,64,107,106,108,315,109,117,118,,101,102,47,48,46,,',
',,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,316,,,,,,241,,,,,99',
'313,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72',
'44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110,112,113,,',
'243,,,,,,,51,,,115,114,116,105,64,107,106,108,,109,117,118,,101,102',
'47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,742,,,,50,,,,,,',
',,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103',
'104,71,72,44,45,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112',
'113,,,21,,,,,,,51,,,115,114,116,105,64,107,106,108,,109,117,118,,101',
'102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,',
',,,22,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103',
'104,71,72,44,45,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112',
'113,,,21,,,,,,,51,,,115,114,116,105,64,107,106,108,,109,117,118,,101',
'102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,',
',,,22,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103',
'104,71,72,44,45,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112',
'113,,,21,,,,,,,51,,,115,114,116,105,64,107,106,108,,109,117,118,,101',
'102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,',
',,,22,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,123,,,,,122,82,,89,103',
'104,,,44,45,77,78,24,,65,,,,71,72,,,,75,,73,74,76,33,34,79,80,,,,,,81',
'31,30,111,110,112,113,,,21,,,,,,,51,,,115,114,116,105,64,107,106,108',
',109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67',
',,68,,,,,,50,,,,,,,,,22,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77',
'78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,',
'81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106',
'108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66',
'67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,',
',,,,,82,,89,103,104,,,44,45,77,78,24,9,65,,,,71,72,,,,75,,73,74,76,33',
'34,79,80,,,,,,81,31,30,111,110,112,113,,,21,,,,,,8,51,7,10,115,114,116',
'105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,',
',43,,,36,,,66,67,,,68,,38,,,,50,,,,,,,,,22,,,,,99,87,90,91,,92,94,93',
'95,,,,,88,98,,,,,,416,82,,89,103,104,,,44,45,77,78,24,,65,,,,71,72,',
',,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,21,,,,,,',
'51,,,115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48,46,',
',,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,22,,,,,99,87',
'90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45',
',75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,21,,,,,,,51',
',,115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,,',
',,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,22,,,,,99,87,90',
'91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,',
'75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,21,,,,,,,51',
',,115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,,',
',,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,22,,,,,99,87,90',
'91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,',
'75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,21,,,,,,,51',
',,115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,,',
',,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,22,,,,,99,87,90',
'91,,92,94,93,95,,,,,88,98,,,,,,,82,,89,103,104,,,44,45,77,78,24,9,65',
',,,71,72,,,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,',
',21,,,,,,8,51,,10,115,114,116,105,64,107,106,108,,109,117,118,,101,102',
'47,48,46,,,,,,,,,,,,,,,,,,,,43,,,36,,,66,67,,,68,,38,,,,50,,,,,,,,,22',
',,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104',
'71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110,112',
'113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,,109,117,118,,101',
'102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,',
',,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103',
'104,71,72,44,45,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112',
'113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,,109,117,118,,101',
'102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,432,,,,50,,',
',,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89',
'103,104,71,72,44,45,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110',
'112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,,109,117,118',
',101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,432,,,',
'50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82',
'65,89,103,104,71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345',
'111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,,109',
'117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68',
',,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,,,,82',
',89,103,104,,,44,45,77,78,24,9,65,,,,71,72,,,,75,,73,74,76,33,34,79',
'80,,,,,,81,31,30,111,110,112,113,,,21,,,,,,8,51,,10,115,114,116,105',
'64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,43',
',,36,,,66,67,,,68,,38,,,,50,,,,,,,,,22,,,,,99,87,90,91,,92,94,93,95',
',,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,33',
'34,79,80,,,,,,81,31,30,111,110,112,113,,,243,,,,,,,51,,,115,114,116',
'105,64,107,106,108,315,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,',
',,,,236,,,242,,,66,67,,,68,,312,,310,,50,,,316,,,,,,241,,,,,99,313,90',
'91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,',
'75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,243,,,,,,,51',
',,115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,,',
',,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87',
'90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45',
',75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,',
',,,,51,,,115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48',
'46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,844,,,,50,,,,,,,,,241',
',,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,,,,82,,89,103,104,,,44,45',
'77,78,24,9,65,,,,71,72,,,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111',
'110,112,113,,,21,,,,,,8,51,,10,115,114,116,105,64,107,106,108,,109,117',
'118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,43,,,36,,,66,67,,,68,,38,,',
',50,,,,,,,,,22,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82',
'65,89,103,104,71,72,44,45,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111',
'110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,315,109',
'117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68',
',312,,310,,50,,,316,,,,,,241,,,,,99,313,90,91,,92,94,93,95,,,,,88,98',
',,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,33,34,79,80,',
',,,,81,31,30,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107',
'106,108,315,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242',
',,66,67,,,68,,312,,310,,50,,,316,,,,,,241,,,,,99,313,90,91,,92,94,93',
'95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76',
'342,343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114',
'116,105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,',
',,,,,236,,,242,,,66,67,,,68,,312,,,,50,,,,,,,,,241,,,,,99,87,90,91,',
'92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,',
'73,74,76,342,343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51',
',,115,114,116,105,64,107,106,108,315,109,117,118,,101,102,47,48,46,',
',,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,316,,,,,,241,,,,',
'99,313,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71',
'72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110,112,113',
',,243,,,,,,,51,,,115,114,116,105,64,107,106,108,315,109,117,118,,101',
'102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,742,,,,50,,',
'316,,,,,,241,,,,,99,313,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82',
'65,89,103,104,71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345',
'111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,,109',
'117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68',
',,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78',
'24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81',
'337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106',
'108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66',
'67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,',
',,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,342,343,79,80',
',,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64',
'107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,',
'242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,',
',,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,33,34',
'79,80,,,,,,81,31,30,111,110,112,113,,,21,,,,,,,51,,,115,114,116,105',
'64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236',
',,242,,,66,67,,,68,,,,,,50,,,,,,,,,22,,,,,99,87,90,91,,92,94,93,95,',
',,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,342',
'343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116',
'105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,',
',236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93',
'95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76',
'342,343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114',
'116,105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,',
',,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92',
'94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73',
'74,76,342,343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,',
',115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,',
',,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90',
'91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,',
'75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,',
',,,51,,,115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48,46',
',,,,,,,,,,,,,,,,,,,236,,,242,562,,66,67,,,68,,,,,,50,,,,,,,,,241,,,',
',99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71',
'72,44,45,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,21',
',,,,,,51,,,115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48',
'46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,22,,,,',
'99,87,90,91,,92,94,93,95,,,,,88,98,,,,,,,82,,89,103,104,,,44,45,77,78',
'24,9,65,,,,71,72,,,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112',
'113,,,21,,,,,,8,51,323,10,115,114,116,105,64,107,106,108,,109,117,118',
',101,102,47,48,46,,,,,,,,,,,,,,,,,,,,43,,,36,,,66,67,,,68,,38,,,,50',
',,,,,,,,22,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,,,416,82,,89,103',
'104,,,44,45,77,78,24,,65,,,,71,72,,,,75,,73,74,76,342,343,79,80,,,,',
',81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107',
'106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242',
',,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88',
'98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,342,343,79',
'80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105',
'64,107,106,108,315,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,',
'236,,,242,,,66,67,,,68,,,,310,,50,,,316,,,,,,241,,,,,99,313,90,91,,92',
'94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73',
'74,76,342,343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,',
',115,114,116,105,64,107,106,108,315,109,117,118,,101,102,47,48,46,,',
',,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,742,,310,,50,,,316,,,,,,241',
',,,,99,313,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104',
'71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110,112',
'113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,,109,117,118,,101',
'102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,',
',,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,,,,82,,89,103,104,-272',
',44,45,,,,-272,-272,-272,,,-272,-272,-272,583,-272,580,579,578,588,581',
',,-272,-272,-272,,,,591,,,,,,-272,-272,,-272,-272,-272,-272,-272,,,',
',,,,583,586,580,579,578,588,581,,,,596,595,599,598,,591,,592,,583,,580',
'579,578,588,581,-272,,,,,,,-272,591,,,586,294,-272,,,,231,,,596,595',
'599,598,,,,592,,,586,,,,,-272,-272,,,596,595,599,598,,,,592,,,,-272',
',,-272,,77,78,24,-272,65,,,,71,72,-272,,,75,,73,74,76,342,343,79,80',
',,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64',
'107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,',
'242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,',
',,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,342,343',
'79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105',
'64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236',
',,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95',
',,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,342',
'343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116',
'105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,',
',236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93',
'95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76',
'33,34,79,80,,,,,,81,31,30,111,110,112,113,,,243,,,,,,,51,,,115,114,116',
'105,64,107,106,108,315,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,',
',,,,236,,,242,,,66,67,,,68,,312,,310,,50,,,316,,,,,,241,,,,,99,313,90',
'91,,92,94,93,95,,,,,88,98,,,,,,,82,,89,103,104,-706,,44,45,,,,-706,-706',
'-706,,,-706,-706,-706,,-706,,,,,,,,-706,-706,-706,-706,-706,,,,,,,,-706',
'-706,,-706,-706,-706,-706,-706,,,,,,,,,,,,,,,,,,,,,,,-706,-706,-706',
'-706,-706,-706,-706,-706,-706,-706,-706,-706,-706,-706,,,-706,-706,-706',
',,-706,,,-706,,,-706,-706,,-706,,-706,,-706,,-706,-706,,-706,-706,-706',
'-706,-706,,-706,-706,-706,,,,,,,,,,,,,,-706,,,-706,-706,-706,-706,,-706',
',-706,,77,78,24,-706,65,,,,71,72,,,,75,,73,74,76,33,34,79,80,,,,,,81',
'31,30,111,110,112,113,,,21,,,,,,,51,,,115,114,116,105,64,107,106,108',
',109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67',
',,68,,,,,,50,,,,,,,,,22,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,,',
',82,,89,103,104,-705,,44,45,,,,-705,-705,-705,,,-705,-705,-705,,-705',
',,,,,,,-705,-705,-705,-705,-705,,,,,,,,-705,-705,,-705,-705,-705,-705',
'-705,,,,,,,,,,,,,,,,,,,,,,,-705,-705,-705,-705,-705,-705,-705,-705,-705',
'-705,-705,-705,-705,-705,,,-705,-705,-705,,,-705,,,-705,,,-705,-705',
',-705,,-705,,-705,,-705,-705,,-705,-705,-705,-705,-705,,-705,-705,-705',
',,,,,,,,,,,,,-705,,,-705,-705,-705,-705,,-705,-272,-705,,,,,-705,-272',
'-272,-272,,,-272,-272,-272,,-272,,,,,,,,-272,-272,-272,-272,,,,,,,,',
'-272,-272,,-272,-272,-272,-272,-272,,,,,,,,,,,,,,,,,,,,,,,-272,-272',
'-272,-272,-272,-272,-272,-272,-272,-272,-272,-272,-272,-272,,,-272,-272',
'-272,,,-272,,294,-272,,,-272,-272,,-272,,-272,,-272,,-272,-272,,-272',
'-272,-272,-272,-272,,-272,-272,-272,,,,,,,,,,,,,,-272,,,-272,-272,,-272',
',-272,77,78,24,,65,,-272,,71,72,,,,75,,73,74,76,342,343,79,80,,,,,,81',
'337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106',
'108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66',
'67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,',
',,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,33,34,79,80,,',
',,,81,31,30,111,110,112,113,,,21,,,,,,,51,,,115,114,116,105,64,107,106',
'108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66',
'67,,,68,,,,,,50,,,,,,,,,22,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,',
',77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,33,34,79,80,,,',
',,81,31,30,111,110,112,113,,,21,,,,,,,51,,,115,114,116,105,64,107,106',
'108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66',
'67,,,68,,,,,,50,,,,,,,,,22,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,',
',77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,33,34,79,80,,,',
',,81,31,30,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106',
'108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66',
'67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,',
',,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,33,34,79,80,,',
',,,81,31,30,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107',
'106,108,315,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242',
',,66,67,,,68,,312,,310,,50,,,316,,,,,,241,,,,,99,313,90,91,,92,94,93',
'95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76',
'342,343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114',
'116,105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,',
',,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92',
'94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73',
'74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,243,,,,,,,51,,,115',
'114,116,105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,',
',,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91',
',92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75',
',73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,21,,,,,,,51,,',
'115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,,',
',,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,22,,,,,99,87,90',
'91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,',
'75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,21,,,,,,,51',
',,115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,,',
',,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,22,,,,,99,87,90',
'91,,92,94,93,95,,,,,88,98,231,,,77,78,24,82,65,89,103,104,71,72,44,45',
',75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,',
',,,,51,,,115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48',
'46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,',
',99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71',
'72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110,112,113',
',,243,,,,,,,51,,,115,114,116,105,64,107,106,108,,109,117,118,,101,102',
'47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241',
',,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104',
'71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110,112',
'113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,,109,117,118,,101',
'102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,',
',,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103',
'104,71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110',
'112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,,109,117,118',
',101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50',
',,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65',
'89,103,104,71,72,44,45,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110',
'112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,,109,117,118',
',101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50',
',,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65',
'89,103,104,71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111',
'110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,,109,117',
'118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,',
',50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82',
'65,89,103,104,71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345',
'111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,,109',
'117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68',
',,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78',
'24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81',
'337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106',
'108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66',
'67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,',
',,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,342,343,79,80',
',,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64',
'107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,',
'242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,',
',,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,342,343',
'79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105',
'64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236',
',,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95',
',,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,342',
'343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116',
'105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,',
',236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93',
'95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76',
'342,343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114',
'116,105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,',
',,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92',
'94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73',
'74,76,342,343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,',
',115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,',
',,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90',
'91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,',
'75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,',
',,,51,,,115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48,46',
',,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99',
'87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44',
'45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110,112,113,,,243',
',,,,,,51,,,115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48',
'46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,',
',99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71',
'72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110,112,113',
',,243,,,,,,,51,,,115,114,116,105,64,107,106,108,,109,117,118,,101,102',
'47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241',
',,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104',
'71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110,112',
'113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,,109,117,118,,101',
'102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,',
',,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103',
'104,71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110',
'112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,,109,117,118',
',101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50',
',,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65',
'89,103,104,71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111',
'110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,,109,117',
'118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,',
',50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82',
'65,89,103,104,71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345',
'111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,,109',
'117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68',
',,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78',
'24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81',
'337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106',
'108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66',
'67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,',
',,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,342,343,79,80',
',,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64',
'107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,',
'242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,',
',,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,342,343',
'79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105',
'64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236',
',,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95',
',,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,342',
'343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116',
'105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,',
',236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93',
'95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76',
'342,343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114',
'116,105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,',
',,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92',
'94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73',
'74,76,342,343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,',
',115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,',
',,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90',
'91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,',
'75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,',
',,,51,,,115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48,46',
',,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99',
'87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44',
'45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110,112,113,,,243',
',,,,,,51,,,115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48',
'46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,',
',99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71',
'72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110,112,113',
',,243,,,,,,,51,,,115,114,116,105,64,107,106,108,,109,117,118,,101,102',
'47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241',
',,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104',
'71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110,112',
'113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,,109,117,118,,101',
'102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,',
',,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103',
'104,71,72,44,45,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112',
'113,,,21,,,,,,,51,,,115,114,116,105,64,107,106,108,,109,117,118,,101',
'102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,',
',,,22,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103',
'104,71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110',
'112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,,109,117,118',
',101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50',
',,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65',
'89,103,104,71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111',
'110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,,109,117',
'118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,432',
',,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24',
'82,65,89,103,104,71,72,44,45,,75,,73,74,76,33,34,79,80,,,,,,81,31,30',
'111,110,112,113,,,21,,,,,,,51,,,115,114,116,105,64,107,106,108,,109',
'117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68',
',,,,,50,,,,,,,,,22,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24',
'82,65,89,103,104,71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337',
'345,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108',
',109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67',
',,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77',
'78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,',
'81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106',
'108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66',
'67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,',
',,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,33,34,79,80,,',
',,,81,31,30,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107',
'106,108,315,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242',
',,66,67,,,68,,312,,310,,50,,,316,,,,,,241,,,,,99,313,90,91,,92,94,93',
'95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,516,,75,,73,74,76',
'33,34,79,80,,,,,,81,31,30,111,110,112,113,,,243,,,,,,,51,,,115,114,116',
'105,64,107,106,108,315,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,',
',,,,236,,,242,,,66,67,,,68,,312,,310,,50,,,316,,,,,,241,,,,,99,313,90',
'91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,',
'75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,243,,,,,,,51',
',,115,114,116,105,64,107,106,108,315,109,117,118,,101,102,47,48,46,',
',,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,312,,310,,50,,,316,,,,,,241',
',,,,99,313,90,91,,92,94,93,95,,,,,88,98,231,,,77,78,24,82,65,89,103',
'104,71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110',
'112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,315,109,117',
'118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,742',
',310,,50,,,316,,,,,,241,,,,,99,313,90,91,,92,94,93,95,,,,,88,98,,,,77',
'78,24,82,65,89,103,104,71,72,44,516,,75,,73,74,76,342,343,79,80,,,,',
',81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107',
'106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242',
',,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88',
'98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,33,34,79,80',
',,,,,81,31,30,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107',
'106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242',
',,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95,,,,,88',
'98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,342,343,79',
'80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116,105',
'64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236',
',,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93,95',
',,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,342',
'343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114,116',
'105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,',
',236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92,94,93',
'95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76',
'342,343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,51,,,115,114',
'116,105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,',
',,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91,,92',
'94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73',
'74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,243,,,,,,,51,,,115',
'114,116,105,64,107,106,108,,109,117,118,,101,102,47,48,46,,,,,,,,,,',
',,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,,,99,87,90,91',
',92,94,93,95,,,,,88,98,,,,,,,82,,89,103,104,,,44,45,77,78,24,9,65,,',
',71,72,,,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,21',
',,,,,8,51,,10,115,114,116,105,64,107,106,108,,109,117,118,,101,102,47',
'48,46,,,,,,,,,,,,,,,,,,,,43,,,36,,,66,67,,,68,,38,,,,50,,,,,,,,,22,',
',,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71',
'72,44,45,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,243',
',,,,,,51,,,115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48',
'46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,',
',99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71',
'72,44,45,,75,,73,74,76,33,34,79,80,,,,,,81,31,30,111,110,112,113,,,243',
',,,,,,51,,,115,114,116,105,64,107,106,108,,109,117,118,,101,102,47,48',
'46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,,,50,,,,,,,,,241,,,',
',99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71',
'72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110,112,113',
',,243,,,,,,,51,,,115,114,116,105,64,107,106,108,315,109,117,118,,101',
'102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,,,68,,,,310,,50,,',
'316,,,,,,241,,,,,99,313,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82',
'65,89,103,104,71,72,44,45,,75,,73,74,76,342,343,79,80,,,,,,81,337,345',
'111,110,112,113,,,243,,,,,,,51,,,115,114,116,105,64,107,106,108,315',
'109,117,118,,101,102,47,48,46,,,,,,,,,,,,,,,,,,,,236,,,242,,,66,67,',
',68,,742,,310,,50,,,316,,,,,,241,,,,,99,313,90,91,,92,94,93,95,,,,,88',
'98,,,,77,78,24,82,65,89,103,104,71,72,44,45,,75,,73,74,76,342,343,79',
'80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,338,,,115,114,116,105',
'64,107,106,108,,109,117,118,,101,102,,,346,,,,,,,,,,,,,,,,,,,,390,,',
'36,,,66,67,,,68,,38,,,,,,,,,,,,,,,,,,99,87,90,91,,92,94,93,95,,,,,88',
'98,,,,77,78,24,82,65,89,103,104,71,72,,,,75,,73,74,76,342,343,79,80',
',,,,,81,337,345,111,110,112,113,,,243,,,,,,,338,,,115,114,116,105,64',
'107,106,108,,109,117,118,,101,102,,,346,,,,,,,,,,,,,,,,,,,,390,,,36',
',,66,67,,,68,,38,,,,,,,,,,,,,,,,,,99,87,90,91,,92,94,93,95,,,,,88,98',
',,,77,78,24,82,65,89,103,104,71,72,,,,75,,73,74,76,342,343,79,80,,,',
',,81,337,345,111,110,112,113,,,243,,,,,,,338,,,115,114,116,105,64,107',
'106,108,,109,117,118,,101,102,,,346,,,,,,,,,,,,,,,,,,,,334,,,330,,,66',
'67,,,68,,,,,,,,,,,,,,,,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77',
'78,24,82,65,89,103,104,71,72,,,,75,,73,74,76,342,343,79,80,,,,,,81,337',
'345,111,110,112,113,,,243,,,,,,,338,,,115,114,116,395,64,107,106,396',
',109,117,118,,101,102,,,346,,,,,,,,,,,,,,,,,,,,397,,,242,,,66,67,,,68',
',,,,,,,,,,,,,,,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82',
'65,89,103,104,71,72,,,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111',
'110,112,113,,,243,,,,,,,338,,,115,114,116,105,64,107,106,108,,109,117',
'118,,101,102,,,346,,,,,,,,,,,,,,,,,,,,334,,,330,,,66,67,,,68,,,,,,,',
',,,,,,,,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89',
'103,104,71,72,,,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110',
'112,113,,,243,,,,,,,338,,,115,114,116,105,64,107,106,108,,109,117,118',
',101,102,,,346,,,,,,,,,,,,,,,,,,,,334,,,330,,,66,67,,,68,,,,,,,,,,,',
',,,,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103',
'104,71,72,,,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110,112',
'113,,,243,,,,,,,338,,,115,114,116,105,64,107,106,108,,109,117,118,,101',
'102,,,346,,,,,,,,,,,,,,,,,,,,1105,,,242,,,66,67,,,68,,,,,,,,,,,,,,,',
',,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104',
'71,72,,,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110,112,113',
',,243,,,,,,,338,,,115,114,116,105,64,107,106,108,,109,117,118,,101,102',
',,346,,,,,,,,,,,,,,,,,,,,1111,,,242,,,66,67,,,68,,,,,,,,,,,,,,,,,,,',
'99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72',
',,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110,112,113,,,243',
',,,,,,338,,,115,114,116,395,64,107,106,396,,109,117,118,,101,102,,,346',
',,,,,,,,,,,,,,,,402,,,397,,,242,,,66,67,,,68,,,,,,,,,,,,,,,,,,,,99,87',
'90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,,,,75',
',73,74,76,342,343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,',
'338,,,115,114,116,105,64,107,106,108,,109,117,118,,101,102,,,346,,,',
',,,,,,,,,,,,,,,,390,,,36,,,66,67,,,68,,38,,,,,,,,,,,,,,,,,,99,87,90',
'91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,,,,75,',
'73,74,76,342,343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,338',
',,115,114,116,105,64,107,106,108,,109,117,118,,101,102,,,346,,,,,,,',
',,,,,,,,,,,,1105,,,242,,,66,67,,,68,,,,,,,,,,,,,,,,,,,,99,87,90,91,',
'92,94,93,95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,,,,75,,73,74',
'76,342,343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,338,,,115',
'114,116,105,64,107,106,108,,109,117,118,,101,102,,,346,,,,,,,,,,,,,',
',,,,,,334,,,330,,,66,67,,,68,,,,,,,,,,,,,,,,,,,,99,87,90,91,,92,94,93',
'95,,,,,88,98,,,,77,78,24,82,65,89,103,104,71,72,,,,75,,73,74,76,342',
'343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,338,,,115,114',
'116,105,64,107,106,108,,109,117,118,,101,102,,,346,,,,,,,,,,,,,,,,,',
',,334,,,242,,,66,67,,,68,,,583,,580,579,578,588,581,,,,,,,,,591,,99',
'87,90,91,,92,94,93,95,,,,,88,98,,,,348,,586,82,,89,103,104,77,78,24',
',65,599,598,,71,72,592,,,75,,73,74,76,342,343,79,80,,,,,,81,337,345',
'111,110,112,113,,,243,,,,,,,338,,,115,114,116,105,64,107,106,108,,109',
'117,118,,101,102,,,346,,,,,,,,,,,,,,,,,,,,334,,,330,,,66,67,,,68,,,',
',,,,,,,,,,,,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,77,78,24,82,65',
'89,103,104,71,72,,,,75,,73,74,76,342,343,79,80,,,,,,81,337,345,111,110',
'112,113,,,243,,,,,,,338,,,115,114,116,105,64,107,106,108,,109,117,118',
',101,102,,,346,,,,,,,,,,,,,,,,,,,,334,,,242,,,66,67,,,68,,,583,,580',
'579,578,588,581,,,,,,,,,591,,99,87,90,91,,92,94,93,95,,,,,88,98,,,,544',
',586,82,,89,103,104,77,78,24,,65,599,598,,71,72,592,,,75,,73,74,76,342',
'343,79,80,,,,,,81,337,345,111,110,112,113,,,243,,,,,,,338,,,115,114',
'116,105,64,107,106,108,,109,117,118,,101,102,,,346,,,,,,,,,,,,,,,,,',
',,334,,,330,,,66,67,,,68,,329,,,,,,,,,,,,,,,,,,99,87,90,91,,92,94,93',
'95,,,,,88,98,,,,,,,82,,89,103,104,182,193,183,206,179,199,189,188,209',
'210,204,187,186,181,207,211,212,191,180,194,198,200,192,185,,,,201,208',
'203,202,195,205,190,178,197,196,,,,,,177,184,175,176,172,173,174,135',
'137,,,136,,,,,,,,166,167,,163,145,146,147,154,151,153,,,148,149,,,,168',
'169,155,156,,,,,,,,,,,,,,160,159,,144,165,162,161,170,157,158,152,150',
'142,164,143,,,171,99,,,,,,,,,,,,,,98,182,193,183,206,179,199,189,188',
'209,210,204,187,186,181,207,211,212,191,180,194,198,200,192,185,,,,201',
'208,203,202,195,205,190,178,197,196,,,,,,177,184,175,176,172,173,174',
'135,137,134,,136,,,,,,,,166,167,,163,145,146,147,154,151,153,,,148,149',
',,,168,169,155,156,,,,,,,,,,,,,,160,159,,144,165,162,161,170,157,158',
'152,150,142,164,143,,,171,99,,,,,,,,,,,,,,98,182,193,183,206,179,199',
'189,188,209,210,204,187,186,181,207,211,212,191,180,194,198,200,192',
'185,,,,201,208,203,202,195,205,190,178,197,196,,,,,,177,184,175,176',
'172,173,174,135,137,,,136,,,,,,,,166,167,,163,145,146,147,154,151,153',
',,148,149,,,,168,169,155,156,,,,,,,,,,,,,,160,159,,144,165,162,161,170',
'157,158,152,150,142,164,143,,,171,99,,,,,,,,,,,,,,98,182,193,183,206',
'179,199,189,188,209,210,204,187,186,181,207,211,212,191,180,194,198',
'200,192,185,,,,201,208,203,202,195,205,190,178,197,196,,,,,,177,184',
'175,176,172,173,174,135,137,,,136,,,,,,,,166,167,,163,145,146,147,154',
'151,153,,,148,149,,,,168,169,155,156,,,,,,,,,,,,,,160,159,,144,165,162',
'161,170,157,158,152,150,142,164,143,,,171,99,111,110,112,113,,,583,',
'580,579,578,588,581,98,,,115,114,116,720,,591,,723,986,,,,,101,102,',
',346,591,,,,,,,,,586,,,,,,,,,,722,599,598,704,,,592,702,,,703,,885,',
',,,,,592,,,,,,,721,,,,99,987,90,91,,92,94,93,95,,,,,88,98,111,110,112',
'113,,,82,,89,103,104,,,708,709,,115,114,116,720,,,583,723,580,579,578',
'588,581,101,102,,,346,,,,591,,,,,,,,,,,,,,,,722,,,704,,,586,702,,,703',
',,,,596,595,599,598,,,,592,,,,721,,,,99,87,90,91,,92,94,93,95,,,,,88',
'98,111,110,112,113,,,82,,89,103,104,,,708,709,,115,114,116,720,,,583',
'723,580,579,578,588,581,101,102,,,346,,,,591,,,,,,,,,,,,,,,,722,,,704',
',,586,702,,,703,,,,,596,595,599,598,,,,592,,,,721,,,,99,87,90,91,,92',
'94,93,95,,,,,88,98,,,,,,,82,,89,103,104,,,708,709,182,193,183,206,179',
'199,189,188,209,210,204,187,186,181,207,211,212,191,180,194,198,200',
'192,185,,,,201,208,203,202,195,205,190,178,197,196,,,,,,177,184,175',
'176,172,173,174,135,137,,,136,,,,,,,,166,167,,163,145,146,147,154,151',
'153,,,148,149,,,,168,169,155,156,,,,,,,,,,,,,,160,159,,144,165,162,161',
'170,157,158,152,150,142,164,143,,,171,111,110,112,113,,,583,,580,579',
'578,588,581,,,,115,114,116,720,,591,,723,,,,,,101,102,,,346,,,,,,,,',
',586,,,,,,,,,,722,599,598,704,,,592,702,,,703,,,,,,,,,,,,,,,,721,,,',
'99,87,90,91,,92,94,93,95,,,,,88,98,111,110,112,113,,,82,,89,103,104',
',,708,709,,115,114,116,720,,,583,723,580,579,578,588,581,101,102,,,346',
',,,591,,,,,,,,,,,,,,,,722,,,704,,,586,702,,,703,,885,,,596,595,599,598',
',,,592,,,,721,,,,99,87,90,91,,92,94,93,95,,,,,88,98,111,110,112,113',
',,82,,89,103,104,,,708,709,,115,114,116,720,,,583,723,580,579,578,588',
'581,101,102,,,346,,,,591,,,,,,,,,,,,,,,,722,,,704,,,586,702,,,703,,',
',,596,595,599,598,,,,592,,,,721,,,,99,87,90,91,,92,94,93,95,,,,,88,98',
'111,110,112,113,,,82,,89,103,104,,,708,709,,115,114,116,720,,,583,723',
'580,579,578,588,581,101,102,,,346,,,,591,,,,,,,,,,,,,,,,722,,,704,,',
'586,702,,,703,,885,,,,,599,598,,,,592,,,,721,,,,99,87,90,91,,92,94,93',
'95,,,,,88,98,111,110,112,113,,,82,,89,103,104,,,708,709,,115,114,116',
'720,,,583,723,580,579,578,588,581,101,102,,,346,,,,591,,,,,,,,,,,,,',
',,722,,,704,,,586,702,,,703,,,,,,,599,598,,,,592,,,,721,,,,99,87,90',
'91,,92,94,93,95,,,,,88,98,111,110,112,113,,,82,,89,103,104,,,708,709',
',115,114,116,720,,,583,723,580,579,578,588,581,101,102,,,346,,,,591',
',,,,,,,,,,,,,,,722,,,704,,,586,702,,,703,,,,,,,599,598,,,,592,,,,721',
',,,99,87,90,91,,92,94,93,95,,,,,88,98,111,110,112,113,,,82,,89,103,104',
',,708,709,,115,114,116,720,,,583,723,580,579,578,588,581,101,102,,,346',
',,,591,,,,,,,,,,,,,,,,722,,,704,,,586,702,,,703,,999,,,,,599,598,,,',
'592,,,,721,,,,99,87,90,91,,92,94,93,95,,,,,88,98,111,110,112,113,,,82',
',89,103,104,,,708,709,,115,114,116,720,,,583,723,580,579,578,588,581',
'101,102,,,346,,,,591,,,,,,,,,,,,,,,,722,,,704,,,586,702,,,703,,,,,596',
'595,599,598,,,,592,,,,721,,,,99,87,90,91,,92,94,93,95,,,,,88,98,111',
'110,112,113,,,82,,89,103,104,,,708,709,,115,114,116,720,,,,723,986,',
',,,101,102,,,346,591,,,,,,,,,,,,,,,,,,,722,,,704,,,,702,,,703,,885,',
',,,,,592,,,,,,,721,,,,99,987,90,91,,92,94,93,95,,,,,88,98,111,110,112',
'113,,,82,,89,103,104,,,708,709,,115,114,116,720,,,583,723,580,579,578',
'588,581,101,102,,,346,,,,591,,,,,,,,,,,,,,,,722,,,704,,,586,702,,,703',
',,,,,,599,598,,,,592,,,,721,,,,99,87,90,91,,92,94,93,95,,,,,88,98,111',
'110,112,113,,,82,,89,103,104,,,708,709,,115,114,116,720,,,583,723,580',
'579,578,588,581,101,102,,,346,,,,591,,,,,,,,,,,,,,,,722,,,704,,,586',
'702,,,703,,,,,,,599,598,,,,592,,,,721,,,,99,87,90,91,,92,94,93,95,,',
',,88,98,111,110,112,113,,,82,,89,103,104,,,708,709,,115,114,116,720',
',,583,723,580,579,578,588,581,101,102,,,346,,,,591,,,,,,,,,,,,,,,,722',
',,704,,,586,702,,,703,,885,,,596,595,599,598,,,,592,,,,721,,,,99,87',
'90,91,,92,94,93,95,,,,,88,98,231,111,110,112,113,,82,,89,103,104,,,708',
'709,,,115,114,116,720,,,,723,986,,,,,101,102,,,346,591,,,,,,,,,,,,,',
',,,,,722,,,704,,,,702,,,703,,885,,,,,,,592,,,,,,,721,,,,99,987,90,91',
',92,94,93,95,,,,,88,98,,,,,,,82,,89,103,104,,,708,709,182,193,183,206',
'179,199,189,188,209,210,204,187,186,181,207,211,212,191,180,194,198',
'200,192,185,,,,201,208,203,282,281,283,280,178,197,196,,,,,,177,184',
'175,176,277,278,279,275,137,107,106,276,,109,,,,,,166,167,,163,145,146',
'147,154,151,153,,,148,149,,,,168,169,155,156,,,,,,287,,,,,,,,160,159',
',144,165,162,161,170,157,158,152,150,142,164,143,,,171,111,110,112,113',
',,,583,,580,579,578,588,581,,,115,114,116,,,,591,,,,,,,101,102,,,346',
'111,110,112,113,,,,,,,586,642,,,,,115,114,116,596,595,599,598,,,,592',
',,101,102,,,346,,,,,,,,,,,,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,,',
',,,,82,,89,103,104,,,,,,,,,99,87,90,91,,92,94,93,95,,,,,88,98,111,110',
'112,113,,,82,,89,103,104,,,,,,115,114,116,1195,469,,,1196,,,,,,101,102',
'166,167,346,163,145,146,147,154,151,153,,,148,149,,,,168,169,155,156',
',,,,,294,,,,,,,,160,159,,144,165,162,161,170,157,158,152,150,142,164',
'143,,,171,99,87,90,91,,92,94,93,95,,,,,88,98,846,462,,,847,,82,,89,103',
'104,,166,167,,163,145,146,147,154,151,153,,,148,149,,,,168,169,155,156',
',,,,,294,,,,,,,,160,159,,144,165,162,161,170,157,158,152,150,142,164',
'143,512,462,171,,513,,,,,,,,166,167,,163,145,146,147,154,151,153,,,148',
'149,,,,168,169,155,156,,,,,,294,,,,,,,,160,159,,144,165,162,161,170',
'157,158,152,150,142,164,143,693,469,171,,694,,,,,,,,166,167,,163,145',
'146,147,154,151,153,,,148,149,,,,168,169,155,156,,,,,,294,,,,,,,,160',
'159,,144,165,162,161,170,157,158,152,150,142,164,143,819,469,171,,820',
',,,,,,,166,167,,163,145,146,147,154,151,153,,,148,149,,,,168,169,155',
'156,,,,,,294,,,,,,,,160,159,,144,165,162,161,170,157,158,152,150,142',
'164,143,691,462,171,,692,,,,,,,,166,167,,163,145,146,147,154,151,153',
',,148,149,,,,168,169,155,156,,,,,,294,,,,,,,,160,159,,144,165,162,161',
'170,157,158,152,150,142,164,143,693,469,171,,694,,,,,,,,166,167,,163',
'145,146,147,154,151,153,,,148,149,,,,168,169,155,156,,,,,,294,,,,,,',
',160,159,,144,165,162,161,170,157,158,152,150,142,164,143,755,469,171',
',756,,,,,,,,166,167,,163,145,146,147,154,151,153,,,148,149,,,,168,169',
'155,156,,,,,,294,,,,,,,,160,159,,144,165,162,161,170,157,158,152,150',
'142,164,143,816,469,171,,817,,,,,,,,166,167,,163,145,146,147,154,151',
'153,,,148,149,,,,168,169,155,156,,,,,,294,,,,,,,,160,159,,144,165,162',
'161,170,157,158,152,150,142,164,143,512,462,171,,513,,,,,,,,166,167',
',163,145,146,147,154,151,153,,,148,149,,,,168,169,155,156,,,,,,,,,,',
',,,160,159,,144,165,162,161,170,157,158,152,150,142,164,143,465,469',
'171,,464,,,,,,,,166,167,,163,145,146,147,154,151,153,,,148,149,,,,168',
'169,155,156,,,,,,294,,,,,,,,160,159,,144,165,162,161,170,157,158,152',
'150,142,164,143,814,462,171,,815,,,,,,,,166,167,,163,145,146,147,154',
'151,153,,,148,149,,,,168,169,155,156,,,,,,294,,,,,,,,160,159,,144,165',
'162,161,170,157,158,152,150,142,164,143,691,462,171,,692,,,,,,,,166',
'167,,163,145,146,147,154,151,153,,,148,149,,,,168,169,155,156,,,,,,294',
',,,,,,,160,159,,144,165,162,161,170,157,158,152,150,142,164,143,752',
'462,171,,753,,,,,,,,166,167,,163,145,146,147,154,151,153,,,148,149,',
',,168,169,155,156,,,,,,294,,,,,,,,160,159,,144,165,162,161,170,157,158',
'152,150,142,164,143,1190,469,171,,1189,,,,,,,,166,167,,163,145,146,147',
'154,151,153,,,148,149,,,,168,169,155,156,,,,,,294,,,,,,,,160,159,,144',
'165,162,161,170,157,158,152,150,142,164,143,1193,462,171,,1194,,,,,',
',,166,167,,163,145,146,147,154,151,153,,,148,149,,,,168,169,155,156',
',,,,,294,,,,,,,,160,159,,144,165,162,161,170,157,158,152,150,142,164',
'143,849,469,171,,848,,,,,,,,166,167,,163,145,146,147,154,151,153,,,148',
'149,,,,168,169,155,156,,,,,,294,,,,,,,,160,159,,144,165,162,161,170',
'157,158,152,150,142,164,143,458,462,171,,459,,,,,,,,166,167,,163,145',
'146,147,154,151,153,,,148,149,,,,168,169,155,156,,,,,,294,,,,,,,,160',
'159,,144,165,162,161,170,157,158,152,150,142,164,143,,,171' ]
        racc_action_table = arr = ::Array.new(28164, nil)
        idx = 0
        clist.each do |str|
          str.split(',', -1).each do |i|
            arr[idx] = i.to_i unless i.empty?
            idx += 1
          end
        end

clist = [
'465,380,1194,1193,730,1112,827,465,465,465,69,1196,877,465,465,988,465',
'999,1158,411,1206,1006,509,509,465,465,379,281,620,340,29,339,703,988',
'281,412,465,465,1032,465,465,465,465,465,593,282,474,474,21,703,1054',
'1108,282,624,1052,480,762,762,877,826,639,639,593,869,1006,812,465,465',
'465,465,465,465,465,465,465,465,465,465,465,465,1195,1109,465,465,465',
'411,465,465,1196,21,465,730,281,465,29,339,623,69,465,999,465,412,465',
'465,623,465,465,465,465,465,282,465,465,465,480,827,509,29,339,380,1194',
'1193,869,1112,380,1194,1193,465,1112,1196,465,465,620,465,1196,465,1158',
'620,1206,693,474,1158,465,1206,379,465,693,693,693,379,762,693,693,693',
'639,693,1032,1195,747,639,234,1032,828,693,693,693,693,693,1054,1108',
'747,622,1052,1054,1108,693,693,1052,693,693,693,693,693,812,350,843',
'812,872,812,46,46,1061,3,1061,235,237,1088,3,1195,1109,847,346,346,1195',
'1109,693,693,693,693,693,693,693,693,693,693,693,693,693,693,234,243',
'693,693,693,846,693,693,872,692,693,532,350,693,693,654,693,656,693',
'406,693,845,693,693,350,693,693,693,693,693,848,693,693,693,235,237',
'691,848,848,848,847,1088,1088,848,848,621,848,693,46,46,693,693,693',
'693,848,693,283,693,1088,914,346,346,693,283,846,693,848,848,692,848',
'848,848,848,848,873,532,532,532,341,654,654,656,656,406,406,406,970',
'239,1076,654,722,656,532,238,722,691,848,848,848,848,848,848,848,848',
'848,848,848,848,848,848,873,914,848,848,848,19,848,848,914,1199,848',
'283,409,848,438,1056,617,914,848,1199,848,438,848,848,408,848,848,848',
'848,848,438,848,849,848,459,970,914,439,345,849,849,849,1076,1076,1076',
'849,849,848,849,701,848,848,19,848,701,848,849,849,701,1076,854,1056',
'848,876,19,848,914,876,849,849,236,849,849,849,849,849,409,409,409,438',
'362,459,362,362,362,362,362,27,408,408,408,347,384,459,27,362,439,384',
'849,849,849,849,849,849,849,849,849,849,849,849,849,849,607,458,849',
'849,849,362,849,849,397,606,849,410,397,849,362,362,362,362,849,413',
'849,362,849,849,407,849,849,849,849,849,105,849,849,849,676,41,960,105',
'105,105,27,960,105,105,105,274,105,849,458,605,849,849,274,849,105,849',
'105,105,105,319,458,362,849,601,319,849,105,105,601,105,105,105,105',
'105,870,410,410,410,604,756,275,676,41,413,413,413,756,275,407,407,407',
'756,602,676,41,756,105,105,105,105,105,105,105,105,105,105,105,105,105',
'105,274,276,105,105,105,817,105,105,276,600,105,878,817,105,105,879',
'105,817,105,597,105,817,105,105,883,105,105,105,105,105,277,105,108',
'105,756,275,884,277,885,108,108,108,49,49,108,108,108,105,108,888,105',
'105,105,105,585,105,108,105,108,108,108,817,105,575,370,105,276,370',
'108,108,817,108,108,108,108,108,886,387,49,49,574,1151,278,279,387,280',
'886,572,1151,278,279,799,280,387,799,277,900,1151,108,108,108,108,108',
'108,108,108,108,108,108,108,108,108,903,18,108,108,108,904,108,108,18',
'571,108,570,886,108,108,913,108,18,108,569,108,387,108,108,886,108,108',
'108,108,108,387,108,694,108,1151,278,279,42,280,694,694,694,85,213,694',
'694,694,108,694,918,108,108,108,108,85,108,694,108,694,694,694,921,108',
'922,85,108,18,371,694,694,371,694,694,694,694,694,925,134,927,1110,928',
'1104,134,134,42,930,1110,586,1104,586,586,586,932,586,934,1110,42,1104',
'694,694,694,694,694,694,694,694,694,694,694,694,694,694,935,1074,694',
'694,694,1131,694,694,1131,937,694,1074,586,694,694,940,694,568,694,119',
'694,586,694,694,947,694,694,694,694,694,464,694,1110,694,1104,1055,1055',
'464,464,464,774,774,374,464,464,374,464,694,950,1074,694,694,694,694',
'464,694,565,694,800,800,564,1074,694,1165,1165,694,464,464,354,464,464',
'464,464,464,705,705,15,15,768,389,768,768,768,768,768,1118,389,1118',
'1118,1118,1217,1118,957,768,959,389,464,464,464,464,464,464,464,464',
'464,464,464,464,464,464,377,377,464,464,464,768,464,464,53,966,464,967',
'1118,464,768,768,768,768,464,968,464,768,464,464,969,464,464,464,464',
'464,1091,464,356,464,389,52,399,51,979,980,1091,549,54,399,981,982,554',
'464,549,54,464,464,399,464,991,464,351,549,54,992,993,768,464,351,997',
'464,330,330,330,330,330,330,351,1091,1091,330,330,998,1091,360,330,752',
'330,330,330,330,330,330,330,537,537,537,537,537,330,330,330,330,330',
'330,330,399,550,330,1003,545,549,54,1013,330,330,330,330,330,330,330',
'330,330,330,330,330,351,330,330,330,233,330,330,330,330,330,632,233',
'752,1014,388,1016,1017,632,1018,752,233,388,1020,1021,752,1022,632,543',
'752,330,388,540,330,43,539,330,330,820,364,330,368,330,1048,1049,820',
'330,523,752,40,820,519,647,647,820,330,647,647,647,518,330,330,330,330',
'1065,330,330,330,330,517,233,388,25,330,330,1075,632,372,752,1083,388',
'330,1085,330,330,330,492,1102,330,330,955,955,955,955,955,955,491,490',
'1105,955,955,489,1111,820,955,395,955,955,955,955,955,955,955,6,6,6',
'6,6,955,955,955,955,955,955,955,373,781,955,781,781,781,1114,781,955',
'955,955,955,955,955,955,955,955,955,955,955,1115,955,955,955,335,955',
'955,955,955,955,333,335,395,1116,391,1117,17,333,781,395,335,391,16',
'1129,395,1130,333,781,395,955,391,375,955,1139,1141,955,955,332,1144',
'955,1145,955,1146,1147,332,955,1149,395,476,487,1154,14,466,332,955',
'385,13,386,1164,955,955,955,955,12,955,955,955,955,1170,335,391,1175',
'955,955,457,333,456,395,454,391,955,487,955,955,955,487,487,955,955',
'382,382,382,382,382,382,440,436,1189,382,382,1190,1192,332,382,396,382',
'382,382,382,382,382,382,322,322,322,322,322,382,382,382,382,382,382',
'382,10,941,382,941,941,941,7,941,382,382,382,382,382,382,382,382,382',
'382,382,382,771,382,382,382,331,382,382,382,382,382,771,331,396,1197',
'629,433,392,431,941,396,331,629,430,401,396,390,428,941,396,382,629',
'422,382,698,721,382,382,724,696,382,727,382,729,771,771,382,695,396',
'771,488,307,735,736,737,382,739,741,293,308,382,382,382,382,687,382',
'382,382,382,749,331,629,751,382,382,311,1202,754,396,1203,629,382,488',
'382,382,382,488,488,382,382,1107,1107,1107,1107,1107,1107,757,758,682',
'1107,1107,761,323,764,1107,753,1107,1107,1107,1107,1107,1107,1107,367',
'367,367,367,367,1107,1107,1107,1107,1107,1107,1107,324,502,1107,326',
'677,327,328,668,1107,1107,1107,1107,1107,1107,1107,1107,1107,1107,1107',
'1107,334,1107,1107,1107,783,1107,1107,1107,1107,1107,784,786,753,502',
'661,659,503,502,502,753,502,502,802,808,753,809,651,813,753,1107,1204',
'1205,1107,1,337,1107,1107,1211,338,1107,785,1107,785,785,785,1107,785',
'753,503,822,,,503,503,1107,503,503,,,1107,1107,1107,1107,,1107,1107',
'1107,1107,,,,,1107,1107,,,,753,,,1107,,1107,1107,1107,,,1107,1107,325',
'325,325,325,325,325,,,,325,325,,,,325,755,325,325,325,325,325,325,325',
',,,,,325,325,325,325,325,325,325,,942,325,942,942,942,,942,325,325,325',
'325,325,325,325,325,325,325,325,325,,325,325,325,,325,325,325,325,325',
',1119,755,1119,1119,1119,,1119,942,755,,,,,755,,,,755,325,,,325,,,325',
'325,,,325,1068,325,1068,1068,1068,325,1068,755,1119,1174,,1174,1174',
'1174,325,1174,,,,325,325,325,325,,325,325,325,325,,,,,325,325,,,,755',
',,325,,325,325,325,,,325,325,1067,1067,1067,1067,1067,1067,,,,1067,1067',
',,,1067,814,1067,1067,1067,1067,1067,1067,1067,,,,,,1067,1067,1067,1067',
'1067,1067,1067,,,1067,,,,,,1067,1067,1067,1067,1067,1067,1067,1067,1067',
'1067,1067,1067,,1067,1067,1067,,1067,1067,1067,1067,1067,,,814,,,,,',
',814,,,,,814,,,,814,1067,,,1067,,,1067,1067,,,1067,,1067,,,,1067,,814',
',,,,,,1067,,,,,1067,1067,1067,1067,,1067,1067,1067,1067,814,,,,1067',
'1067,,,,814,,,1067,,1067,1067,1067,,,1067,1067,1066,1066,1066,1066,1066',
'1066,,,,1066,1066,,,,1066,815,1066,1066,1066,1066,1066,1066,1066,,,',
',,1066,1066,1066,1066,1066,1066,1066,,,1066,,,,,,1066,1066,1066,1066',
'1066,1066,1066,1066,1066,1066,1066,1066,,1066,1066,1066,,1066,1066,1066',
'1066,1066,,,815,,,,,,,815,,,,,815,,,,815,1066,,,1066,,,1066,1066,,,1066',
',1066,,,,1066,,815,,,,,,,1066,,,,,1066,1066,1066,1066,,1066,1066,1066',
'1066,815,,,,1066,1066,,,,815,,,1066,,1066,1066,1066,,,1066,1066,823',
'823,823,823,823,823,,,,823,823,,,,823,816,823,823,823,823,823,823,823',
',,,,,823,823,823,823,823,823,823,,,823,,,,,,823,823,823,823,823,823',
'823,823,823,823,823,823,,823,823,823,,823,823,823,823,823,,,816,,,,',
',,816,,,,,816,,,,816,823,,,823,,,823,823,,,823,,823,,,,823,,816,,,,',
',,823,,,,,823,823,823,823,,823,823,823,823,816,,,,823,823,,,,816,,,823',
',823,823,823,,,823,823,366,366,366,366,366,366,,,,366,366,,,,366,819',
'366,366,366,366,366,366,366,,,,,,366,366,366,366,366,366,366,,,366,',
',,,,366,366,366,366,366,366,366,366,366,366,366,366,,366,366,366,,366',
'366,366,366,366,,,819,,,,,,,819,,,,,819,,,,819,366,,,366,,,366,366,',
',366,,366,,,,366,,819,,,,,,,366,,,,,366,366,366,366,,366,366,366,366',
',,,,366,366,,,,819,,,366,,366,366,366,,,366,366,365,365,365,365,365',
'365,,,,365,365,,,,365,956,365,365,365,365,365,365,365,,,,,,365,365,365',
'365,365,365,365,,,365,,,,,,365,365,365,365,365,365,365,365,365,365,365',
'365,,365,365,365,,365,365,365,365,365,,,956,,,,,,,956,,,,,,,,,956,365',
',,365,,,365,365,,,365,,365,,,,365,,956,,,,,,,365,,,,,365,365,365,365',
',365,365,365,365,956,,,,365,365,,,,956,,,365,,365,365,365,,,365,365',
'132,132,132,132,132,132,,,,132,132,,,,132,,132,132,132,132,132,132,132',
'23,,,,,132,132,132,132,132,132,132,,,132,,,,,,132,132,132,132,132,132',
'132,132,132,132,132,132,,132,132,132,,132,132,132,132,132,23,23,23,23',
'23,23,23,23,23,23,23,,23,23,,,23,23,,132,,23,132,,,132,132,,,132,,132',
'23,,23,132,23,23,,23,23,23,23,23,132,23,,,,132,132,132,132,,132,132',
'132,132,,,,,132,132,,23,,,,,132,,132,132,132,,,132,132,920,920,920,920',
'920,920,,,,920,920,,,,920,,920,920,920,920,920,920,920,378,,,,,920,920',
'920,920,920,920,920,,,920,,,,,,920,920,920,920,920,920,920,920,920,920',
'920,920,,920,920,920,,920,920,920,920,920,378,378,378,378,378,378,378',
'378,378,378,378,,378,378,,,378,378,,920,,,920,,,920,920,,,920,,920,378',
',378,920,378,378,,378,378,378,378,378,920,378,,,,920,920,920,920,,920',
'920,920,920,,,,,920,920,,378,,,,,920,,920,920,920,,,920,920,908,908',
'908,908,908,908,,,,908,908,,,,908,,908,908,908,908,908,908,908,,,,,',
'908,908,908,908,908,908,908,,,908,,,,,443,908,908,908,908,908,908,908',
'908,908,908,908,908,,908,908,908,,908,908,908,908,908,443,443,443,443',
'443,443,443,443,443,443,443,,443,443,,,443,443,,908,,,908,,,908,908',
',,908,,908,443,,443,908,443,443,,443,443,443,443,443,908,443,,,,908',
'908,908,908,,908,908,908,908,,,,,908,908,,443,,443,,,908,,908,908,908',
',,908,908,361,361,361,361,361,361,,,,361,361,,,,361,,361,361,361,361',
'361,361,361,,,,,,361,361,361,361,361,361,361,,,361,,,,,453,361,361,361',
'361,361,361,361,361,361,361,361,361,,361,361,361,,361,361,361,361,361',
'453,453,453,453,453,453,453,453,453,453,453,,453,453,,,453,453,,361',
',,361,,,361,361,,,361,,361,453,,453,361,453,453,,453,453,453,453,453',
'361,453,,,,361,361,361,361,,361,361,361,361,,,,,361,361,,453,,,,,361',
',361,361,361,,,361,361,834,834,834,834,834,834,,,,834,834,,,,834,,834',
'834,834,834,834,834,834,,,,,,834,834,834,834,834,834,834,,,834,,,,,763',
'834,834,834,834,834,834,834,834,834,834,834,834,,834,834,834,,834,834',
'834,834,834,763,763,763,763,763,763,763,763,763,763,763,,763,763,,,763',
'763,,834,,,834,,,834,834,,,834,,834,763,,763,834,763,763,,763,763,763',
'763,763,834,763,,,,834,834,834,834,,834,834,834,834,,,,,834,834,,763',
',,,,834,,834,834,834,,,834,834,215,215,215,215,215,215,,,,215,215,,',
',215,,215,215,215,215,215,215,215,,,,,,215,215,215,215,215,215,215,',
',215,,,,,794,215,215,215,215,215,215,215,215,215,215,215,215,,215,215',
'215,,215,215,215,215,215,794,794,794,794,794,794,794,794,794,794,794',
',794,794,,,794,794,,215,,,215,,,215,215,,,215,,215,794,,794,215,794',
'794,,794,794,794,794,794,215,794,,,,215,215,215,215,,215,215,215,215',
',,,,215,215,,794,,,,,215,,215,215,215,,,215,215,1166,1166,1166,1166',
'1166,1166,,,,1166,1166,,,,1166,,1166,1166,1166,1166,1166,1166,1166,',
',,,,1166,1166,1166,1166,1166,1166,1166,,,1166,,,,,,1166,1166,1166,1166',
'1166,1166,1166,1166,1166,1166,1166,1166,,1166,1166,1166,,1166,1166,1166',
'1166,1166,305,305,305,305,305,305,305,305,305,305,305,,305,305,,,305',
'305,,1166,,,1166,,,1166,1166,,,1166,,1166,305,,305,1166,305,305,,305',
'305,305,305,305,1166,305,,,,1166,1166,1166,1166,,1166,1166,1166,1166',
',,,,1166,1166,,305,,,,,1166,,1166,1166,1166,,,1166,1166,616,616,616',
'616,616,616,,,,616,616,,,,616,,616,616,616,616,616,616,616,,,,,,616',
'616,616,616,616,616,616,,,616,,,,,,616,616,616,616,616,616,616,616,616',
'616,616,616,,616,616,616,,616,616,616,616,616,506,506,506,506,506,506',
'506,506,506,506,506,,506,506,,,506,506,,616,,,616,,,616,616,,,616,,616',
'506,,506,616,506,506,,506,506,506,506,506,616,506,,,,616,616,616,616',
',616,616,616,616,,,,,616,616,506,506,,,,,616,,616,616,616,,,616,616',
'36,36,36,36,36,36,,,,36,36,,,,36,,36,36,36,36,36,36,36,,,,,,36,36,36',
'36,36,36,36,,,36,,,,,,36,36,36,36,36,36,36,36,36,36,36,36,,36,36,36',
',36,36,36,36,36,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050',
',1050,1050,,,1050,1050,,36,,,36,,,36,36,,,36,,36,1050,,1050,36,1050',
'1050,,1050,1050,1050,1050,1050,36,1050,,,,36,36,36,36,,36,36,36,36,',
',,,36,36,,1050,,,,,36,,36,36,36,,,36,36,1029,1029,1029,1029,1029,1029',
',,,1029,1029,,,,1029,,1029,1029,1029,1029,1029,1029,1029,,,,,,1029,1029',
'1029,1029,1029,1029,1029,,,1029,,,,,,1029,1029,1029,1029,1029,1029,1029',
'1029,1029,1029,1029,1029,,1029,1029,1029,,1029,1029,1029,1029,1029,1027',
'1027,1027,1027,1027,1027,1027,1027,1027,1027,1027,,1027,1027,,,1027',
'1027,,1029,,,1029,,,1029,1029,,,1029,,1029,1027,,1027,1029,1027,1027',
',1027,1027,1027,1027,1027,1029,1027,,,,1029,1029,1029,1029,,1029,1029',
'1029,1029,,,,,1029,1029,,1027,,,,,1029,,1029,1029,1029,,,1029,1029,0',
'0,0,0,0,0,,,,0,0,,,,0,,0,0,0,0,0,0,0,,,,,,0,0,0,0,0,0,0,,,0,,,,,,0,0',
'0,0,0,0,0,0,0,0,0,0,,0,0,0,,0,0,0,0,0,1009,1009,1009,1009,1009,1009',
'1009,1009,1009,1009,1009,,1009,1009,,,1009,1009,,0,,,0,,,0,0,,,0,,0',
'1009,,1009,0,1009,1009,,1009,1009,1009,1009,1009,0,1009,,,,0,0,0,0,',
'0,0,0,0,,,,,0,0,,1009,,,,,0,,0,0,0,,,0,0,818,818,818,818,818,818,,,',
'818,818,,,,818,,818,818,818,818,818,818,818,,,,,,818,818,818,818,818',
'818,818,,,818,,,,,,818,818,818,818,818,818,818,818,818,818,818,818,',
'818,818,818,,818,818,818,818,818,561,561,561,561,561,561,561,561,561',
'561,561,,561,561,,,561,561,,818,,,818,,,818,818,,,818,,818,561,,561',
'818,561,561,,561,561,561,561,561,818,561,,,,818,818,818,818,,818,818',
'818,818,,,,,818,818,,561,,,,,818,,818,818,818,,,818,818,242,242,242',
'242,242,242,,,,242,242,,,,242,,242,242,242,242,242,242,242,,,,,,242',
'242,242,242,242,242,242,,,242,,,,,,242,242,242,242,242,242,242,242,242',
'242,242,242,,242,242,242,,242,242,242,242,242,851,851,851,851,851,851',
'851,851,851,851,851,,851,851,,,851,851,,242,,,242,,,242,242,,,242,,242',
'851,,851,242,851,851,,851,851,851,851,851,242,851,,,,242,242,242,242',
',242,242,242,242,,,,,242,242,,851,,,,,242,,242,242,242,,,242,242,383',
'383,383,383,383,383,,,,383,383,,,,383,,383,383,383,383,383,383,383,',
',,,,383,383,383,383,383,383,383,,,383,,,,,,383,383,383,383,383,383,383',
'383,383,383,383,383,,383,383,383,,383,383,383,383,383,500,500,500,500',
'500,500,500,,,500,500,,,,,,500,500,,383,,,383,,,383,383,,,383,,383,500',
',500,383,500,500,,500,500,500,500,500,383,500,,,,383,383,383,383,,383',
'383,383,383,,,,,383,383,,,,,,,383,,383,383,383,,,383,383,1120,1120,1120',
'1120,1120,1120,,,,1120,1120,,,,1120,,1120,1120,1120,1120,1120,1120,1120',
',,,,,1120,1120,1120,1120,1120,1120,1120,,,1120,,,,,,1120,1120,1120,1120',
'1120,1120,1120,1120,1120,1120,1120,1120,,1120,1120,1120,,1120,1120,1120',
'1120,1120,485,485,485,485,485,485,485,485,485,485,485,,485,485,,,485',
'485,,1120,,,1120,,,1120,1120,,,1120,,1120,485,,485,1120,485,485,,485',
'485,485,485,485,1120,485,,,,1120,1120,1120,1120,,1120,1120,1120,1120',
',,,,1120,1120,,,,,,,1120,,1120,1120,1120,,,1120,1120,1010,1010,1010',
'1010,1010,1010,,,,1010,1010,,,,1010,,1010,1010,1010,1010,1010,1010,1010',
',,,,,1010,1010,1010,1010,1010,1010,1010,,,1010,,,,,,1010,1010,1010,1010',
'1010,1010,1010,1010,1010,1010,1010,1010,,1010,1010,1010,,1010,1010,1010',
'1010,1010,486,486,486,486,486,486,486,486,486,486,486,,486,486,,,486',
'486,,1010,,,1010,,,1010,1010,,,1010,,1010,486,,486,1010,486,486,,486',
'486,486,486,486,1010,486,,,,1010,1010,1010,1010,,1010,1010,1010,1010',
',,,,1010,1010,,,,,,,1010,,1010,1010,1010,,,1010,1010,619,619,619,619',
'619,619,,,,619,619,,,,619,,619,619,619,619,619,619,619,,,,,,619,619',
'619,619,619,619,619,,,619,,,,,,619,619,619,619,619,619,619,619,619,619',
'619,619,,619,619,619,,619,619,619,619,619,496,496,496,496,496,496,496',
',,496,496,,,,,,496,496,,619,,,619,,,619,619,,,619,,619,496,,496,619',
'496,496,,496,496,496,496,496,619,496,,,,619,619,619,619,,619,619,619',
'619,,,,,619,619,,,,,,,619,,619,619,619,,,619,619,640,640,640,640,640',
'640,,,,640,640,,,,640,,640,640,640,640,640,640,640,,,,,,640,640,640',
'640,640,640,640,,,640,,,,,,640,640,640,640,640,640,640,640,640,640,640',
'640,,640,640,640,,640,640,640,640,640,497,497,497,497,497,497,497,,',
'497,497,,,,,,497,497,,640,,,640,,,640,640,,,640,,640,497,,497,640,497',
'497,,497,497,497,497,497,640,497,,,,640,640,640,640,,640,640,640,640',
',,,,640,640,,,,,,,640,,640,640,640,,,640,640,961,961,961,961,961,961',
',,,961,961,,,,961,,961,961,961,961,961,961,961,,,,,,961,961,961,961',
'961,961,961,,,961,,,,,,961,961,961,961,961,961,961,961,961,961,961,961',
',961,961,961,,961,961,961,961,961,498,498,498,498,498,498,498,,,498',
'498,,,,,,498,498,,961,,,961,,,961,961,,,961,,961,498,,498,961,498,498',
',498,498,498,498,498,961,498,,,,961,961,961,961,,961,961,961,961,,,',
',961,961,,,,546,546,546,961,546,961,961,961,546,546,961,961,,546,,546',
'546,546,546,546,546,546,,,,,,546,546,546,546,546,546,546,,,546,,,,,',
',546,,,546,546,546,546,546,546,546,546,546,546,546,546,,546,546,546',
'546,546,499,499,499,499,499,499,499,,,499,499,,,,,,499,499,,546,,,546',
',,546,546,,,546,,546,499,546,499,546,499,499,546,499,499,499,499,499',
'546,499,,,,546,546,546,546,,546,546,546,546,,,,,546,546,,,,,,,546,,546',
'546,546,,,546,546,5,5,5,5,5,,,,5,5,,,,5,,5,5,5,5,5,5,5,,,,,,5,5,5,5',
'5,5,5,,,5,,,,,,5,5,5,5,5,5,5,5,5,5,5,5,,5,5,5,,5,5,5,5,5,501,501,501',
'501,501,501,501,,,501,501,,,,,,501,501,,5,,,5,,,5,5,,,5,,5,501,,501',
'5,501,501,,501,501,501,501,501,5,501,,,,5,5,5,5,,5,5,5,5,,,,,5,5,,,',
'1155,1155,1155,5,1155,5,5,5,1155,1155,5,5,,1155,,1155,1155,1155,1155',
'1155,1155,1155,,,,,,1155,1155,1155,1155,1155,1155,1155,,,1155,,,,,,',
'1155,,,1155,1155,1155,1155,1155,1155,1155,1155,1155,1155,1155,1155,',
'1155,1155,1155,1155,1155,504,504,504,504,504,504,504,,,504,504,,,,,',
'504,504,,1155,,,1155,,,1155,1155,,,1155,,1155,504,1155,504,1155,504',
'504,1155,504,504,504,504,504,1155,504,,,,1155,1155,1155,1155,,1155,1155',
'1155,1155,,,,,1155,1155,,,,1123,1123,1123,1155,1123,1155,1155,1155,1123',
'1123,1155,1155,,1123,,1123,1123,1123,1123,1123,1123,1123,,,,,,1123,1123',
'1123,1123,1123,1123,1123,,,1123,,,,,,,1123,,,1123,1123,1123,1123,1123',
'1123,1123,1123,,1123,1123,1123,,1123,1123,1123,1123,1123,505,505,505',
'505,505,505,505,505,,505,505,,,,,,505,505,,1123,,,1123,,,1123,1123,',
',1123,,,505,,505,1123,505,505,,505,505,505,505,505,1123,505,,,,1123',
'1123,1123,1123,,1123,1123,1123,1123,,,,,1123,1123,,,,1122,1122,1122',
'1123,1122,1123,1123,1123,1122,1122,1123,1123,,1122,,1122,1122,1122,1122',
'1122,1122,1122,,,,,,1122,1122,1122,1122,1122,1122,1122,,,1122,,,,,,',
'1122,,,1122,1122,1122,1122,1122,1122,1122,1122,,1122,1122,1122,,1122',
'1122,1122,1122,1122,507,,,,,,,,,,,,,,,,507,507,,1122,,,1122,,,1122,1122',
',,1122,,,507,,507,1122,507,507,,507,507,,,507,1122,507,,,,1122,1122',
'1122,1122,,1122,1122,1122,1122,,,,,1122,1122,,,,22,22,22,1122,22,1122',
'1122,1122,22,22,1122,1122,,22,,22,22,22,22,22,22,22,,,,,,22,22,22,22',
'22,22,22,,,22,,,,,,,22,,,22,22,22,22,22,22,22,22,,22,22,22,,22,22,22',
'22,22,560,,,,,,,,,,,,,,,,560,560,,22,,,22,,,22,22,,,22,,,560,,560,22',
'560,560,,560,560,,,560,22,560,,,,22,22,22,22,,22,22,22,22,,,,,22,22',
',,,,,,22,,22,22,22,30,,22,22,,,,30,30,30,,,30,30,30,494,30,,,,,,,,30',
'30,30,30,,,,494,494,,,,30,30,,30,30,30,30,30,,,,494,,494,,494,494,,494',
'494,,,,,,,,,,,30,30,30,30,30,30,30,30,30,30,30,30,30,30,,,30,30,30,',
',30,,30,30,,,30,30,,30,,30,,30,,30,30,,30,30,30,30,30,31,30,30,30,,',
',31,31,31,,,31,31,31,,31,30,495,,30,30,,30,31,30,31,31,,,,,30,,495,495',
'31,31,,31,31,31,31,31,,,,,,,495,,,,495,495,,495,495,,,,,,,,31,31,31',
'31,31,31,31,31,31,31,31,31,31,31,,,31,31,31,,,31,,31,31,,,31,31,,31',
',31,,31,,31,31,,31,31,31,31,31,,31,,31,,,,,,,,,,,,,,31,,,31,31,,31,',
'31,32,32,32,,32,,31,,32,32,,,,32,,32,32,32,32,32,32,32,,,,,,32,32,32',
'32,32,32,32,,,32,,,,,,,32,,,32,32,32,32,32,32,32,32,32,32,32,32,,32',
'32,32,32,32,353,353,353,353,353,353,353,353,353,353,353,,353,353,,,353',
'353,,32,,,32,,,32,32,,,32,,32,353,32,353,32,353,353,32,353,353,353,353',
'353,32,353,,,,32,32,32,32,,32,32,32,32,,,,,32,32,,,,33,33,33,32,33,32',
'32,32,33,33,32,32,,33,,33,33,33,33,33,33,33,,,,,,33,33,33,33,33,33,33',
',,33,,,,,,,33,,,33,33,33,33,33,33,33,33,33,33,33,33,,33,33,33,33,33',
'352,352,352,352,352,352,352,352,352,352,352,,352,352,,,352,352,,33,',
',33,,,33,33,,,33,,33,352,33,352,33,352,352,33,352,352,352,352,352,33',
'352,,,,33,33,33,33,,33,33,33,33,,,,,33,33,,,,34,34,34,33,34,33,33,33',
'34,34,33,33,,34,,34,34,34,34,34,34,34,,,,,,34,34,34,34,34,34,34,,,34',
',,,,,,34,,,34,34,34,34,34,34,34,34,34,34,34,34,,34,34,34,34,34,493,',
',,,,,,,,,,,,,,493,493,,34,,,34,,,34,34,,,34,,34,493,34,493,34,493,493',
'34,493,493,,,,34,,,,,34,34,34,34,,34,34,34,34,,,,,34,34,,,,44,44,44',
'34,44,34,34,34,44,44,34,34,,44,,44,44,44,44,44,44,44,,,,,,44,44,44,44',
'44,44,44,,,44,,,,,,,44,,,44,44,44,44,44,44,44,44,,44,44,44,,44,44,44',
'44,44,,,,,,,,,,,,,,,,,,,,44,,,44,,,44,44,,,44,,,,,,44,,,,,,,,,44,,,',
',44,44,44,44,,44,44,44,44,,,,,44,44,,,,45,45,45,44,45,44,44,44,45,45',
'44,44,,45,,45,45,45,45,45,45,45,,,,,,45,45,45,45,45,45,45,,,45,,,,,',
',45,,,45,45,45,45,45,45,45,45,,45,45,45,,45,45,45,45,45,,,,,,,,,,,,',
',,,,,,,45,,,45,,,45,45,,,45,,,,,,45,,,,,,,,,45,,,,,45,45,45,45,,45,45',
'45,45,,,,,45,45,,,,47,47,47,45,47,45,45,45,47,47,45,45,,47,,47,47,47',
'47,47,47,47,,,,,,47,47,47,47,47,47,47,,,47,,,,,,,47,,,47,47,47,47,47',
'47,47,47,,47,47,47,,47,47,47,47,47,,,,,,,,,,,,,,,,,,,,47,,,47,,,47,47',
',,47,,,,,,47,,,,,,,,,47,,,,,47,47,47,47,,47,47,47,47,,,,,47,47,,,,48',
'48,48,47,48,47,47,47,48,48,47,47,,48,,48,48,48,48,48,48,48,,,,,,48,48',
'48,48,48,48,48,,,48,,,,,,,48,,,48,48,48,48,48,48,48,48,,48,48,48,,48',
'48,48,48,48,,,,,,,,,,,,,,,,,,,,48,,,48,,,48,48,,,48,,,,,,48,,,,,,,,',
'48,,,,,48,48,48,48,,48,48,48,48,,,,,48,48,,,,50,50,50,48,50,48,48,48',
'50,50,48,48,,50,,50,50,50,50,50,50,50,,,,,,50,50,50,50,50,50,50,,,50',
',,,,,,50,,,50,50,50,50,50,50,50,50,,50,50,50,,50,50,50,50,50,,,,,,,',
',,,,,,,,,,,,50,,,50,,,50,50,,,50,,,,,,50,,,,,,,,,50,,,,,50,50,50,50',
',50,50,50,50,,,,,50,50,,,,974,974,974,50,974,50,50,50,974,974,50,50',
',974,,974,974,974,974,974,974,974,,,,,,974,974,974,974,974,974,974,',
',974,,,,,,,974,,,974,974,974,974,974,974,974,974,,974,974,974,,974,974',
'974,974,974,,,,,,,,,,,,,,,,,,,,974,,,974,,,974,974,,,974,,,,,,974,,',
',,,,,,974,,,,,974,974,974,974,,974,974,974,974,,,,,974,974,,,,973,973',
'973,974,973,974,974,974,973,973,974,974,,973,,973,973,973,973,973,973',
'973,,,,,,973,973,973,973,973,973,973,,,973,,,,,,,973,,,973,973,973,973',
'973,973,973,973,973,973,973,973,,973,973,973,973,973,,,,,,,,,,,,,,,',
',,,,973,,,973,,,973,973,,,973,,,,973,,973,,,973,,,,,,973,,,,,973,973',
'973,973,,973,973,973,973,,,,,973,973,,,,,,,973,,973,973,973,64,,973',
'973,,,,64,64,64,,,64,64,64,,64,,,,,,,,64,,64,64,64,,,,709,709,709,709',
'64,64,,64,64,64,64,64,,,,,709,709,709,,,,,,,,,,,709,709,,,709,64,64',
'64,64,64,64,64,64,64,64,64,64,64,64,,,64,64,64,,,64,,,64,,,64,64,,64',
',64,,64,,64,64,,64,64,64,64,64,,64,,64,,709,709,709,709,,709,709,709',
'709,,,,64,709,709,64,64,64,64,,64,709,64,709,709,709,,64,66,66,66,66',
'66,,,,66,66,,,,66,,66,66,66,66,66,66,66,,,,,,66,66,66,66,66,66,66,,',
'66,,,,,,66,66,,66,66,66,66,66,66,66,66,66,,66,66,66,,66,66,66,66,66',
',,,,,,,,,,,,,,,,,,,66,,,66,,,66,66,,,66,,66,,,,66,,,,,,,,,66,,,,,66',
'66,66,66,,66,66,66,66,,,,,66,66,,,,67,67,67,66,67,66,66,66,67,67,66',
'66,,67,,67,67,67,67,67,67,67,,,,,,67,67,67,67,67,67,67,,,67,,,,,,,67',
',,67,67,67,67,67,67,67,67,67,67,67,67,,67,67,67,67,67,,,,,,,,,,,,,,',
',,,,,67,,,67,,,67,67,,,67,,67,,,,67,,,67,,,,,,67,,,,,67,67,67,67,,67',
'67,67,67,,,,,67,67,,,,68,68,68,67,68,67,67,67,68,68,67,67,,68,,68,68',
'68,68,68,68,68,,,,,,68,68,68,68,68,68,68,,,68,,,,,,,68,,,68,68,68,68',
'68,68,68,68,68,68,68,68,,68,68,68,68,68,,,,,,,,,,,,,,,,,,,,68,,,68,',
',68,68,,,68,,,,,,68,,,68,,,,,,68,,,,,68,68,68,68,,68,68,68,68,,,,,68',
'68,,,,954,954,954,68,954,68,68,68,954,954,68,68,,954,,954,954,954,954',
'954,954,954,,,,,,954,954,954,954,954,954,954,,,954,,,,,,,954,,,954,954',
'954,954,954,954,954,954,,954,954,954,,954,954,954,954,954,,,,,,,,,,',
',,,,,,,,,954,,,954,,,954,954,,,954,,954,,,,954,,,,,,,,,954,,,,,954,954',
'954,954,,954,954,954,954,,,,,954,954,,,,71,71,71,954,71,954,954,954',
'71,71,954,954,,71,,71,71,71,71,71,71,71,,,,,,71,71,71,71,71,71,71,,',
'71,,,,,,,71,,,71,71,71,71,71,71,71,71,,71,71,71,,71,71,71,71,71,,,,',
',,,,,,,,,,,,,,,71,,,71,,,71,71,,,71,,,,,,71,,,,,,,,,71,,,,,71,71,71',
'71,,71,71,71,71,,,,,71,71,,,,72,72,72,71,72,71,71,71,72,72,71,71,,72',
',72,72,72,72,72,72,72,,,,,,72,72,72,72,72,72,72,,,72,,,,,,,72,,,72,72',
'72,72,72,72,72,72,,72,72,72,,72,72,72,72,72,,,,,,,,,,,,,,,,,,,,72,,',
'72,,,72,72,,,72,,,,,,72,,,,,,,,,72,,,,,72,72,72,72,,72,72,72,72,,,,',
'72,72,,,,75,75,75,72,75,72,72,72,75,75,72,72,,75,,75,75,75,75,75,75',
'75,,,,,,75,75,75,75,75,75,75,,,75,,,,,,,75,,,75,75,75,75,75,75,75,75',
',75,75,75,,75,75,75,75,75,,,,,,,,,,,,,,,,,,,,75,,,75,,,75,75,,,75,,',
',,,75,,,,,,,,,75,,,,,75,75,75,75,,75,75,75,75,,,,,75,75,75,,,,,75,75',
',75,75,75,,,75,75,949,949,949,,949,,,,949,949,,,,949,,949,949,949,949',
'949,949,949,,,,,,949,949,949,949,949,949,949,,,949,,,,,,,949,,,949,949',
'949,949,949,949,949,949,,949,949,949,,949,949,949,949,949,,,,,,,,,,',
',,,,,,,,,949,,,949,,,949,949,,,949,,,,,,949,,,,,,,,,949,,,,,949,949',
'949,949,,949,949,949,949,,,,,949,949,,,,945,945,945,949,945,949,949',
'949,945,945,949,949,,945,,945,945,945,945,945,945,945,,,,,,945,945,945',
'945,945,945,945,,,945,,,,,,,945,,,945,945,945,945,945,945,945,945,,945',
'945,945,,945,945,945,945,945,,,,,,,,,,,,,,,,,,,,945,,,945,,,945,945',
',,945,,,,,,945,,,,,,,,,945,,,,,945,945,945,945,,945,945,945,945,,,,',
'945,945,,,,,,,945,,945,945,945,,,945,945,121,121,121,121,121,,,,121',
'121,,,,121,,121,121,121,121,121,121,121,,,,,,121,121,121,121,121,121',
'121,,,121,,,,,,121,121,121,121,121,121,121,121,121,121,121,121,,121',
'121,121,,121,121,121,121,121,,,,,,,,,,,,,,,,,,,,121,,,121,,,121,121',
',,121,,121,,,,121,,,,,,,,,121,,,,,121,121,121,121,,121,121,121,121,',
',,,121,121,,,,,,121,121,,121,121,121,,,121,121,126,126,126,,126,,,,126',
'126,,,,126,,126,126,126,126,126,126,126,,,,,,126,126,126,126,126,126',
'126,,,126,,,,,,,126,,,126,126,126,126,126,126,126,126,,126,126,126,',
'126,126,126,126,126,,,,,,,,,,,,,,,,,,,,126,,,126,,,126,126,,,126,,,',
',,126,,,,,,,,,126,,,,,126,126,126,126,,126,126,126,126,,,,,126,126,',
',,127,127,127,126,127,126,126,126,127,127,126,126,,127,,127,127,127',
'127,127,127,127,,,,,,127,127,127,127,127,127,127,,,127,,,,,,,127,,,127',
'127,127,127,127,127,127,127,,127,127,127,,127,127,127,127,127,,,,,,',
',,,,,,,,,,,,,127,,,127,,,127,127,,,127,,,,,,127,,,,,,,,,127,,,,,127',
'127,127,127,,127,127,127,127,,,,,127,127,,,,128,128,128,127,128,127',
'127,127,128,128,127,127,,128,,128,128,128,128,128,128,128,,,,,,128,128',
'128,128,128,128,128,,,128,,,,,,,128,,,128,128,128,128,128,128,128,128',
',128,128,128,,128,128,128,128,128,,,,,,,,,,,,,,,,,,,,128,,,128,,,128',
'128,,,128,,,,,,128,,,,,,,,,128,,,,,128,128,128,128,,128,128,128,128',
',,,,128,128,,,,129,129,129,128,129,128,128,128,129,129,128,128,,129',
',129,129,129,129,129,129,129,,,,,,129,129,129,129,129,129,129,,,129',
',,,,,,129,,,129,129,129,129,129,129,129,129,,129,129,129,,129,129,129',
'129,129,,,,,,,,,,,,,,,,,,,,129,,,129,,,129,129,,,129,,,,,,129,,,,,,',
',,129,,,,,129,129,129,129,,129,129,129,129,,,,,129,129,,,,,,,129,,129',
'129,129,,,129,129,130,130,130,130,130,,,,130,130,,,,130,,130,130,130',
'130,130,130,130,,,,,,130,130,130,130,130,130,130,,,130,,,,,,130,130',
',130,130,130,130,130,130,130,130,130,,130,130,130,,130,130,130,130,130',
',,,,,,,,,,,,,,,,,,,130,,,130,,,130,130,,,130,,130,,,,130,,,,,,,,,130',
',,,,130,130,130,130,,130,130,130,130,,,,,130,130,,,,917,917,917,130',
'917,130,130,130,917,917,130,130,,917,,917,917,917,917,917,917,917,,',
',,,917,917,917,917,917,917,917,,,917,,,,,,,917,,,917,917,917,917,917',
'917,917,917,,917,917,917,,917,917,917,917,917,,,,,,,,,,,,,,,,,,,,917',
',,917,,,917,917,,,917,,,,,,917,,,,,,,,,917,,,,,917,917,917,917,,917',
'917,917,917,,,,,917,917,,,,216,216,216,917,216,917,917,917,216,216,917',
'917,,216,,216,216,216,216,216,216,216,,,,,,216,216,216,216,216,216,216',
',,216,,,,,,,216,,,216,216,216,216,216,216,216,216,,216,216,216,,216',
'216,216,216,216,,,,,,,,,,,,,,,,,,,,216,,,216,,,216,216,,,216,,216,,',
',216,,,,,,,,,216,,,,,216,216,216,216,,216,216,216,216,,,,,216,216,,',
',217,217,217,216,217,216,216,216,217,217,216,216,,217,,217,217,217,217',
'217,217,217,,,,,,217,217,217,217,217,217,217,,,217,,,,,,,217,,,217,217',
'217,217,217,217,217,217,,217,217,217,,217,217,217,217,217,,,,,,,,,,',
',,,,,,,,,217,,,217,,,217,217,,,217,,217,,,,217,,,,,,,,,217,,,,,217,217',
'217,217,,217,217,217,217,,,,,217,217,,,,675,675,675,217,675,217,217',
'217,675,675,217,217,,675,,675,675,675,675,675,675,675,,,,,,675,675,675',
'675,675,675,675,,,675,,,,,,,675,,,675,675,675,675,675,675,675,675,,675',
'675,675,,675,675,675,675,675,,,,,,,,,,,,,,,,,,,,675,,,675,,,675,675',
',,675,,,,,,675,,,,,,,,,675,,,,,675,675,675,675,,675,675,675,675,,,,',
'675,675,,,,,,,675,,675,675,675,,,675,675,674,674,674,674,674,,,,674',
'674,,,,674,,674,674,674,674,674,674,674,,,,,,674,674,674,674,674,674',
'674,,,674,,,,,,674,674,,674,674,674,674,674,674,674,674,674,,674,674',
'674,,674,674,674,674,674,,,,,,,,,,,,,,,,,,,,674,,,674,,,674,674,,,674',
',674,,,,674,,,,,,,,,674,,,,,674,674,674,674,,674,674,674,674,,,,,674',
'674,,,,671,671,671,674,671,674,674,674,671,671,674,674,,671,,671,671',
'671,671,671,671,671,,,,,,671,671,671,671,671,671,671,,,671,,,,,,,671',
',,671,671,671,671,671,671,671,671,671,671,671,671,,671,671,671,671,671',
',,,,,,,,,,,,,,,,,,,671,,,671,,,671,671,,,671,,671,,671,,671,,,671,,',
',,,671,,,,,671,671,671,671,,671,671,671,671,,,,,671,671,,,,670,670,670',
'671,670,671,671,671,670,670,671,671,,670,,670,670,670,670,670,670,670',
',,,,,670,670,670,670,670,670,670,,,670,,,,,,,670,,,670,670,670,670,670',
'670,670,670,,670,670,670,,670,670,670,670,670,,,,,,,,,,,,,,,,,,,,670',
',,670,,,670,670,,,670,,,,,,670,,,,,,,,,670,,,,,670,670,670,670,,670',
'670,670,670,,,,,670,670,,,,669,669,669,670,669,670,670,670,669,669,670',
'670,,669,,669,669,669,669,669,669,669,,,,,,669,669,669,669,669,669,669',
',,669,,,,,,,669,,,669,669,669,669,669,669,669,669,,669,669,669,,669',
'669,669,669,669,,,,,,,,,,,,,,,,,,,,669,,,669,,,669,669,,,669,,669,,',
',669,,,,,,,,,669,,,,,669,669,669,669,,669,669,669,669,,,,,669,669,,',
',,,,669,,669,669,669,,,669,669,667,667,667,667,667,,,,667,667,,,,667',
',667,667,667,667,667,667,667,,,,,,667,667,667,667,667,667,667,,,667',
',,,,,667,667,,667,667,667,667,667,667,667,667,667,,667,667,667,,667',
'667,667,667,667,,,,,,,,,,,,,,,,,,,,667,,,667,,,667,667,,,667,,667,,',
',667,,,,,,,,,667,,,,,667,667,667,667,,667,667,667,667,,,,,667,667,,',
',636,636,636,667,636,667,667,667,636,636,667,667,,636,,636,636,636,636',
'636,636,636,,,,,,636,636,636,636,636,636,636,,,636,,,,,,,636,,,636,636',
'636,636,636,636,636,636,636,636,636,636,,636,636,636,636,636,,,,,,,',
',,,,,,,,,,,,636,,,636,,,636,636,,,636,,636,,636,,636,,,636,,,,,,636',
',,,,636,636,636,636,,636,636,636,636,,,,,636,636,,,,626,626,626,636',
'626,636,636,636,626,626,636,636,,626,,626,626,626,626,626,626,626,,',
',,,626,626,626,626,626,626,626,,,626,,,,,,,626,,,626,626,626,626,626',
'626,626,626,626,626,626,626,,626,626,626,626,626,,,,,,,,,,,,,,,,,,,',
'626,,,626,,,626,626,,,626,,626,,626,,626,,,626,,,,,,626,,,,,626,626',
'626,626,,626,626,626,626,,,,,626,626,,,,625,625,625,626,625,626,626',
'626,625,625,626,626,,625,,625,625,625,625,625,625,625,,,,,,625,625,625',
'625,625,625,625,,,625,,,,,,,625,,,625,625,625,625,625,625,625,625,,625',
'625,625,,625,625,625,625,625,,,,,,,,,,,,,,,,,,,,625,,,625,,,625,625',
',,625,,625,,,,625,,,,,,,,,625,,,,,625,625,625,625,,625,625,625,625,',
',,,625,625,,,,612,612,612,625,612,625,625,625,612,612,625,625,,612,',
'612,612,612,612,612,612,612,,,,,,612,612,612,612,612,612,612,,,612,',
',,,,,612,,,612,612,612,612,612,612,612,612,612,612,612,612,,612,612',
'612,612,612,,,,,,,,,,,,,,,,,,,,612,,,612,,,612,612,,,612,,,,,,612,,',
'612,,,,,,612,,,,,612,612,612,612,,612,612,612,612,,,,,612,612,,,,609',
'609,609,612,609,612,612,612,609,609,612,612,,609,,609,609,609,609,609',
'609,609,,,,,,609,609,609,609,609,609,609,,,609,,,,,,,609,,,609,609,609',
'609,609,609,609,609,609,609,609,609,,609,609,609,609,609,,,,,,,,,,,',
',,,,,,,,609,,,609,,,609,609,,,609,,609,,,,609,,,609,,,,,,609,,,,,609',
'609,609,609,,609,609,609,609,,,,,609,609,,,,603,603,603,609,603,609',
'609,609,603,603,609,609,,603,,603,603,603,603,603,603,603,,,,,,603,603',
'603,603,603,603,603,,,603,,,,,,,603,,,603,603,603,603,603,603,603,603',
',603,603,603,,603,603,603,603,603,,,,,,,,,,,,,,,,,,,,603,,,603,,,603',
'603,,,603,,,,,,603,,,,,,,,,603,,,,,603,603,603,603,,603,603,603,603',
',,,,603,603,,,,589,589,589,603,589,603,603,603,589,589,603,603,,589',
',589,589,589,589,589,589,589,,,,,,589,589,589,589,589,589,589,,,589',
',,,,,,589,,,589,589,589,589,589,589,589,589,,589,589,589,,589,589,589',
'589,589,,,,,,,,,,,,,,,,,,,,589,,,589,,,589,589,,,589,,,,,,589,,,,,,',
',,589,,,,,589,589,589,589,,589,589,589,589,,,,,589,589,,,,563,563,563',
'589,563,589,589,589,563,563,589,589,,563,,563,563,563,563,563,563,563',
',,,,,563,563,563,563,563,563,563,,,563,,,,,,,563,,,563,563,563,563,563',
'563,563,563,,563,563,563,,563,563,563,563,563,,,,,,,,,,,,,,,,,,,,563',
',,563,,,563,563,,,563,,,,,,563,,,,,,,,,563,,,,,563,563,563,563,,563',
'563,563,563,,,,,563,563,,,,562,562,562,563,562,563,563,563,562,562,563',
'563,,562,,562,562,562,562,562,562,562,,,,,,562,562,562,562,562,562,562',
',,562,,,,,,,562,,,562,562,562,562,562,562,562,562,,562,562,562,,562',
'562,562,562,562,,,,,,,,,,,,,,,,,,,,562,,,562,,,562,562,,,562,,,,,,562',
',,,,,,,,562,,,,,562,562,562,562,,562,562,562,562,,,,,562,562,,,,559',
'559,559,562,559,562,562,562,559,559,562,562,,559,,559,559,559,559,559',
'559,559,,,,,,559,559,559,559,559,559,559,,,559,,,,,,,559,,,559,559,559',
'559,559,559,559,559,,559,559,559,,559,559,559,559,559,,,,,,,,,,,,,,',
',,,,,559,,,559,,,559,559,,,559,,,,,,559,,,,,,,,,559,,,,,559,559,559',
'559,,559,559,559,559,,,,,559,559,,,,556,556,556,559,556,559,559,559',
'556,556,559,559,,556,,556,556,556,556,556,556,556,,,,,,556,556,556,556',
'556,556,556,,,556,,,,,,,556,,,556,556,556,556,556,556,556,556,,556,556',
'556,,556,556,556,556,556,,,,,,,,,,,,,,,,,,,,556,,,556,,,556,556,,,556',
',,,,,556,,,,,,,,,556,,,,,556,556,556,556,,556,556,556,556,,,,,556,556',
',,,357,357,357,556,357,556,556,556,357,357,556,556,,357,,357,357,357',
'357,357,357,357,,,,,,357,357,357,357,357,357,357,,,357,,,,,,,357,,,357',
'357,357,357,357,357,357,357,,357,357,357,,357,357,357,357,357,,,,,,',
',,,,,,,,,,,,,357,,,357,,,357,357,,,357,,,,,,357,,,,,,,,,357,,,,,357',
'357,357,357,,357,357,357,357,,,,,357,357,,,,359,359,359,357,359,357',
'357,357,359,359,357,357,,359,,359,359,359,359,359,359,359,,,,,,359,359',
'359,359,359,359,359,,,359,,,,,,,359,,,359,359,359,359,359,359,359,359',
',359,359,359,,359,359,359,359,359,,,,,,,,,,,,,,,,,,,,359,,,359,359,',
'359,359,,,359,,,,,,359,,,,,,,,,359,,,,,359,359,359,359,,359,359,359',
'359,,,,,359,359,,,,402,402,402,359,402,359,359,359,402,402,359,359,',
'402,,402,402,402,402,402,402,402,,,,,,402,402,402,402,402,402,402,,',
'402,,,,,,,402,,,402,402,402,402,402,402,402,402,,402,402,402,,402,402',
'402,402,402,,,,,,,,,,,,,,,,,,,,402,,,402,,,402,402,,,402,,,,,,402,,',
',,,,,,402,,,,,402,402,402,402,,402,402,402,402,,,,,402,402,,,,,,,402',
',402,402,402,,,402,402,536,536,536,536,536,,,,536,536,,,,536,,536,536',
'536,536,536,536,536,,,,,,536,536,536,536,536,536,536,,,536,,,,,,536',
'536,536,536,536,536,536,536,536,536,536,536,,536,536,536,,536,536,536',
'536,536,,,,,,,,,,,,,,,,,,,,536,,,536,,,536,536,,,536,,536,,,,536,,,',
',,,,,536,,,,,536,536,536,536,,536,536,536,536,,,,,536,536,,,,,,536,536',
',536,536,536,,,536,536,530,530,530,,530,,,,530,530,,,,530,,530,530,530',
'530,530,530,530,,,,,,530,530,530,530,530,530,530,,,530,,,,,,,530,,,530',
'530,530,530,530,530,530,530,,530,530,530,,530,530,530,530,530,,,,,,',
',,,,,,,,,,,,,530,,,530,,,530,530,,,530,,,,,,530,,,,,,,,,530,,,,,530',
'530,530,530,,530,530,530,530,,,,,530,530,,,,528,528,528,530,528,530',
'530,530,528,528,530,530,,528,,528,528,528,528,528,528,528,,,,,,528,528',
'528,528,528,528,528,,,528,,,,,,,528,,,528,528,528,528,528,528,528,528',
'528,528,528,528,,528,528,528,528,528,,,,,,,,,,,,,,,,,,,,528,,,528,,',
'528,528,,,528,,,,528,,528,,,528,,,,,,528,,,,,528,528,528,528,,528,528',
'528,528,,,,,528,528,,,,526,526,526,528,526,528,528,528,526,526,528,528',
',526,,526,526,526,526,526,526,526,,,,,,526,526,526,526,526,526,526,',
',526,,,,,,,526,,,526,526,526,526,526,526,526,526,526,526,526,526,,526',
'526,526,526,526,,,,,,,,,,,,,,,,,,,,526,,,526,,,526,526,,,526,,526,,526',
',526,,,526,,,,,,526,,,,,526,526,526,526,,526,526,526,526,,,,,526,526',
',,,516,516,516,526,516,526,526,526,516,516,526,526,,516,,516,516,516',
'516,516,516,516,,,,,,516,516,516,516,516,516,516,,,516,,,,,,,516,,,516',
'516,516,516,516,516,516,516,,516,516,516,,516,516,516,516,516,,,,,,',
',,,,,,,,,,,,,516,,,516,,,516,516,,,516,,,,,,516,,,,,,,,,516,,,,,516',
'516,516,516,,516,516,516,516,,,,,516,516,,,,,,,516,,516,516,516,511',
',516,516,,,,511,511,511,,,511,511,511,907,511,907,907,907,907,907,,',
'511,511,511,,,,907,,,,,,511,511,,511,511,511,511,511,,,,,,,,775,907',
'775,775,775,775,775,,,,907,907,907,907,,775,,907,,793,,793,793,793,793',
'793,511,,,,,,,511,793,,,775,511,511,,,,907,,,775,775,775,775,,,,775',
',,793,,,,,511,511,,,793,793,793,793,,,,793,,,,511,,,511,,483,483,483',
'511,483,,,,483,483,511,,,483,,483,483,483,483,483,483,483,,,,,,483,483',
'483,483,483,483,483,,,483,,,,,,,483,,,483,483,483,483,483,483,483,483',
',483,483,483,,483,483,483,483,483,,,,,,,,,,,,,,,,,,,,483,,,483,,,483',
'483,,,483,,,,,,483,,,,,,,,,483,,,,,483,483,483,483,,483,483,483,483',
',,,,483,483,,,,482,482,482,483,482,483,483,483,482,482,483,483,,482',
',482,482,482,482,482,482,482,,,,,,482,482,482,482,482,482,482,,,482',
',,,,,,482,,,482,482,482,482,482,482,482,482,,482,482,482,,482,482,482',
'482,482,,,,,,,,,,,,,,,,,,,,482,,,482,,,482,482,,,482,,,,,,482,,,,,,',
',,482,,,,,482,482,482,482,,482,482,482,482,,,,,482,482,,,,481,481,481',
'482,481,482,482,482,481,481,482,482,,481,,481,481,481,481,481,481,481',
',,,,,481,481,481,481,481,481,481,,,481,,,,,,,481,,,481,481,481,481,481',
'481,481,481,,481,481,481,,481,481,481,481,481,,,,,,,,,,,,,,,,,,,,481',
',,481,,,481,481,,,481,,,,,,481,,,,,,,,,481,,,,,481,481,481,481,,481',
'481,481,481,,,,,481,481,,,,479,479,479,481,479,481,481,481,479,479,481',
'481,,479,,479,479,479,479,479,479,479,,,,,,479,479,479,479,479,479,479',
',,479,,,,,,,479,,,479,479,479,479,479,479,479,479,479,479,479,479,,479',
'479,479,479,479,,,,,,,,,,,,,,,,,,,,479,,,479,,,479,479,,,479,,479,,479',
',479,,,479,,,,,,479,,,,,479,479,479,479,,479,479,479,479,,,,,479,479',
',,,,,,479,,479,479,479,470,,479,479,,,,470,470,470,,,470,470,470,,470',
',,,,,,,470,470,470,470,470,,,,,,,,470,470,,470,470,470,470,470,,,,,',
',,,,,,,,,,,,,,,,,470,470,470,470,470,470,470,470,470,470,470,470,470',
'470,,,470,470,470,,,470,,,470,,,470,470,,470,,470,,470,,470,470,,470',
'470,470,470,470,,470,470,470,,,,,,,,,,,,,,470,,,470,470,470,470,,470',
',470,,381,381,381,470,381,,,,381,381,,,,381,,381,381,381,381,381,381',
'381,,,,,,381,381,381,381,381,381,381,,,381,,,,,,,381,,,381,381,381,381',
'381,381,381,381,,381,381,381,,381,381,381,381,381,,,,,,,,,,,,,,,,,,',
',381,,,381,,,381,381,,,381,,,,,,381,,,,,,,,,381,,,,,381,381,381,381',
',381,381,381,381,,,,,381,381,,,,,,,381,,381,381,381,469,,381,381,,,',
'469,469,469,,,469,469,469,,469,,,,,,,,469,469,469,469,469,,,,,,,,469',
'469,,469,469,469,469,469,,,,,,,,,,,,,,,,,,,,,,,469,469,469,469,469,469',
'469,469,469,469,469,469,469,469,,,469,469,469,,,469,,,469,,,469,469',
',469,,469,,469,,469,469,,469,469,469,469,469,,469,469,469,,,,,,,,,,',
',,,469,,,469,469,469,469,,469,460,469,,,,,469,460,460,460,,,460,460',
'460,,460,,,,,,,,460,460,460,460,,,,,,,,,460,460,,460,460,460,460,460',
',,,,,,,,,,,,,,,,,,,,,,460,460,460,460,460,460,460,460,460,460,460,460',
'460,460,,,460,460,460,,,460,,460,460,,,460,460,,460,,460,,460,,460,460',
',460,460,460,460,460,,460,460,460,,,,,,,,,,,,,,460,,,460,460,,460,,460',
'432,432,432,,432,,460,,432,432,,,,432,,432,432,432,432,432,432,432,',
',,,,432,432,432,432,432,432,432,,,432,,,,,,,432,,,432,432,432,432,432',
'432,432,432,,432,432,432,,432,432,432,432,432,,,,,,,,,,,,,,,,,,,,432',
',,432,,,432,432,,,432,,,,,,432,,,,,,,,,432,,,,,432,432,432,432,,432',
'432,432,432,,,,,432,432,,,,219,219,219,432,219,432,432,432,219,219,432',
'432,,219,,219,219,219,219,219,219,219,,,,,,219,219,219,219,219,219,219',
',,219,,,,,,,219,,,219,219,219,219,219,219,219,219,,219,219,219,,219',
'219,219,219,219,,,,,,,,,,,,,,,,,,,,219,,,219,,,219,219,,,219,,,,,,219',
',,,,,,,,219,,,,,219,219,219,219,,219,219,219,219,,,,,219,219,,,,220',
'220,220,219,220,219,219,219,220,220,219,219,,220,,220,220,220,220,220',
'220,220,,,,,,220,220,220,220,220,220,220,,,220,,,,,,,220,,,220,220,220',
'220,220,220,220,220,,220,220,220,,220,220,220,220,220,,,,,,,,,,,,,,',
',,,,,220,,,220,,,220,220,,,220,,,,,,220,,,,,,,,,220,,,,,220,220,220',
'220,,220,220,220,220,,,,,220,220,,,,222,222,222,220,222,220,220,220',
'222,222,220,220,,222,,222,222,222,222,222,222,222,,,,,,222,222,222,222',
'222,222,222,,,222,,,,,,,222,,,222,222,222,222,222,222,222,222,,222,222',
'222,,222,222,222,222,222,,,,,,,,,,,,,,,,,,,,222,,,222,,,222,222,,,222',
',,,,,222,,,,,,,,,222,,,,,222,222,222,222,,222,222,222,222,,,,,222,222',
',,,223,223,223,222,223,222,222,222,223,223,222,222,,223,,223,223,223',
'223,223,223,223,,,,,,223,223,223,223,223,223,223,,,223,,,,,,,223,,,223',
'223,223,223,223,223,223,223,223,223,223,223,,223,223,223,223,223,,,',
',,,,,,,,,,,,,,,,223,,,223,,,223,223,,,223,,223,,223,,223,,,223,,,,,',
'223,,,,,223,223,223,223,,223,223,223,223,,,,,223,223,,,,897,897,897',
'223,897,223,223,223,897,897,223,223,,897,,897,897,897,897,897,897,897',
',,,,,897,897,897,897,897,897,897,,,897,,,,,,,897,,,897,897,897,897,897',
'897,897,897,,897,897,897,,897,897,897,897,897,,,,,,,,,,,,,,,,,,,,897',
',,897,,,897,897,,,897,,,,,,897,,,,,,,,,897,,,,,897,897,897,897,,897',
'897,897,897,,,,,897,897,,,,228,228,228,897,228,897,897,897,228,228,897',
'897,,228,,228,228,228,228,228,228,228,,,,,,228,228,228,228,228,228,228',
',,228,,,,,,,228,,,228,228,228,228,228,228,228,228,,228,228,228,,228',
'228,228,228,228,,,,,,,,,,,,,,,,,,,,228,,,228,,,228,228,,,228,,,,,,228',
',,,,,,,,228,,,,,228,228,228,228,,228,228,228,228,,,,,228,228,,,,229',
'229,229,228,229,228,228,228,229,229,228,228,,229,,229,229,229,229,229',
'229,229,,,,,,229,229,229,229,229,229,229,,,229,,,,,,,229,,,229,229,229',
'229,229,229,229,229,,229,229,229,,229,229,229,229,229,,,,,,,,,,,,,,',
',,,,,229,,,229,,,229,229,,,229,,,,,,229,,,,,,,,,229,,,,,229,229,229',
'229,,229,229,229,229,,,,,229,229,,,,230,230,230,229,230,229,229,229',
'230,230,229,229,,230,,230,230,230,230,230,230,230,,,,,,230,230,230,230',
'230,230,230,,,230,,,,,,,230,,,230,230,230,230,230,230,230,230,,230,230',
'230,,230,230,230,230,230,,,,,,,,,,,,,,,,,,,,230,,,230,,,230,230,,,230',
',,,,,230,,,,,,,,,230,,,,,230,230,230,230,,230,230,230,230,,,,,230,230',
'230,,,868,868,868,230,868,230,230,230,868,868,230,230,,868,,868,868',
'868,868,868,868,868,,,,,,868,868,868,868,868,868,868,,,868,,,,,,,868',
',,868,868,868,868,868,868,868,868,,868,868,868,,868,868,868,868,868',
',,,,,,,,,,,,,,,,,,,868,,,868,,,868,868,,,868,,,,,,868,,,,,,,,,868,,',
',,868,868,868,868,,868,868,868,868,,,,,868,868,,,,867,867,867,868,867',
'868,868,868,867,867,868,868,,867,,867,867,867,867,867,867,867,,,,,,867',
'867,867,867,867,867,867,,,867,,,,,,,867,,,867,867,867,867,867,867,867',
'867,,867,867,867,,867,867,867,867,867,,,,,,,,,,,,,,,,,,,,867,,,867,',
',867,867,,,867,,,,,,867,,,,,,,,,867,,,,,867,867,867,867,,867,867,867',
'867,,,,,867,867,,,,866,866,866,867,866,867,867,867,866,866,867,867,',
'866,,866,866,866,866,866,866,866,,,,,,866,866,866,866,866,866,866,,',
'866,,,,,,,866,,,866,866,866,866,866,866,866,866,,866,866,866,,866,866',
'866,866,866,,,,,,,,,,,,,,,,,,,,866,,,866,,,866,866,,,866,,,,,,866,,',
',,,,,,866,,,,,866,866,866,866,,866,866,866,866,,,,,866,866,,,,865,865',
'865,866,865,866,866,866,865,865,866,866,,865,,865,865,865,865,865,865',
'865,,,,,,865,865,865,865,865,865,865,,,865,,,,,,,865,,,865,865,865,865',
'865,865,865,865,,865,865,865,,865,865,865,865,865,,,,,,,,,,,,,,,,,,',
',865,,,865,,,865,865,,,865,,,,,,865,,,,,,,,,865,,,,,865,865,865,865',
',865,865,865,865,,,,,865,865,,,,852,852,852,865,852,865,865,865,852',
'852,865,865,,852,,852,852,852,852,852,852,852,,,,,,852,852,852,852,852',
'852,852,,,852,,,,,,,852,,,852,852,852,852,852,852,852,852,,852,852,852',
',852,852,852,852,852,,,,,,,,,,,,,,,,,,,,852,,,852,,,852,852,,,852,,',
',,,852,,,,,,,,,852,,,,,852,852,852,852,,852,852,852,852,,,,,852,852',
',,,241,241,241,852,241,852,852,852,241,241,852,852,,241,,241,241,241',
'241,241,241,241,,,,,,241,241,241,241,241,241,241,,,241,,,,,,,241,,,241',
'241,241,241,241,241,241,241,,241,241,241,,241,241,241,241,241,,,,,,',
',,,,,,,,,,,,,241,,,241,,,241,241,,,241,,,,,,241,,,,,,,,,241,,,,,241',
'241,241,241,,241,241,241,241,,,,,241,241,,,,844,844,844,241,844,241',
'241,241,844,844,241,241,,844,,844,844,844,844,844,844,844,,,,,,844,844',
'844,844,844,844,844,,,844,,,,,,,844,,,844,844,844,844,844,844,844,844',
',844,844,844,,844,844,844,844,844,,,,,,,,,,,,,,,,,,,,844,,,844,,,844',
'844,,,844,,,,,,844,,,,,,,,,844,,,,,844,844,844,844,,844,844,844,844',
',,,,844,844,,,,245,245,245,844,245,844,844,844,245,245,844,844,,245',
',245,245,245,245,245,245,245,,,,,,245,245,245,245,245,245,245,,,245',
',,,,,,245,,,245,245,245,245,245,245,245,245,,245,245,245,,245,245,245',
'245,245,,,,,,,,,,,,,,,,,,,,245,,,245,,,245,245,,,245,,,,,,245,,,,,,',
',,245,,,,,245,245,245,245,,245,245,245,245,,,,,245,245,,,,246,246,246',
'245,246,245,245,245,246,246,245,245,,246,,246,246,246,246,246,246,246',
',,,,,246,246,246,246,246,246,246,,,246,,,,,,,246,,,246,246,246,246,246',
'246,246,246,,246,246,246,,246,246,246,246,246,,,,,,,,,,,,,,,,,,,,246',
',,246,,,246,246,,,246,,,,,,246,,,,,,,,,246,,,,,246,246,246,246,,246',
'246,246,246,,,,,246,246,,,,247,247,247,246,247,246,246,246,247,247,246',
'246,,247,,247,247,247,247,247,247,247,,,,,,247,247,247,247,247,247,247',
',,247,,,,,,,247,,,247,247,247,247,247,247,247,247,,247,247,247,,247',
'247,247,247,247,,,,,,,,,,,,,,,,,,,,247,,,247,,,247,247,,,247,,,,,,247',
',,,,,,,,247,,,,,247,247,247,247,,247,247,247,247,,,,,247,247,,,,248',
'248,248,247,248,247,247,247,248,248,247,247,,248,,248,248,248,248,248',
'248,248,,,,,,248,248,248,248,248,248,248,,,248,,,,,,,248,,,248,248,248',
'248,248,248,248,248,,248,248,248,,248,248,248,248,248,,,,,,,,,,,,,,',
',,,,,248,,,248,,,248,248,,,248,,,,,,248,,,,,,,,,248,,,,,248,248,248',
'248,,248,248,248,248,,,,,248,248,,,,249,249,249,248,249,248,248,248',
'249,249,248,248,,249,,249,249,249,249,249,249,249,,,,,,249,249,249,249',
'249,249,249,,,249,,,,,,,249,,,249,249,249,249,249,249,249,249,,249,249',
'249,,249,249,249,249,249,,,,,,,,,,,,,,,,,,,,249,,,249,,,249,249,,,249',
',,,,,249,,,,,,,,,249,,,,,249,249,249,249,,249,249,249,249,,,,,249,249',
',,,250,250,250,249,250,249,249,249,250,250,249,249,,250,,250,250,250',
'250,250,250,250,,,,,,250,250,250,250,250,250,250,,,250,,,,,,,250,,,250',
'250,250,250,250,250,250,250,,250,250,250,,250,250,250,250,250,,,,,,',
',,,,,,,,,,,,,250,,,250,,,250,250,,,250,,,,,,250,,,,,,,,,250,,,,,250',
'250,250,250,,250,250,250,250,,,,,250,250,,,,251,251,251,250,251,250',
'250,250,251,251,250,250,,251,,251,251,251,251,251,251,251,,,,,,251,251',
'251,251,251,251,251,,,251,,,,,,,251,,,251,251,251,251,251,251,251,251',
',251,251,251,,251,251,251,251,251,,,,,,,,,,,,,,,,,,,,251,,,251,,,251',
'251,,,251,,,,,,251,,,,,,,,,251,,,,,251,251,251,251,,251,251,251,251',
',,,,251,251,,,,252,252,252,251,252,251,251,251,252,252,251,251,,252',
',252,252,252,252,252,252,252,,,,,,252,252,252,252,252,252,252,,,252',
',,,,,,252,,,252,252,252,252,252,252,252,252,,252,252,252,,252,252,252',
'252,252,,,,,,,,,,,,,,,,,,,,252,,,252,,,252,252,,,252,,,,,,252,,,,,,',
',,252,,,,,252,252,252,252,,252,252,252,252,,,,,252,252,,,,253,253,253',
'252,253,252,252,252,253,253,252,252,,253,,253,253,253,253,253,253,253',
',,,,,253,253,253,253,253,253,253,,,253,,,,,,,253,,,253,253,253,253,253',
'253,253,253,,253,253,253,,253,253,253,253,253,,,,,,,,,,,,,,,,,,,,253',
',,253,,,253,253,,,253,,,,,,253,,,,,,,,,253,,,,,253,253,253,253,,253',
'253,253,253,,,,,253,253,,,,254,254,254,253,254,253,253,253,254,254,253',
'253,,254,,254,254,254,254,254,254,254,,,,,,254,254,254,254,254,254,254',
',,254,,,,,,,254,,,254,254,254,254,254,254,254,254,,254,254,254,,254',
'254,254,254,254,,,,,,,,,,,,,,,,,,,,254,,,254,,,254,254,,,254,,,,,,254',
',,,,,,,,254,,,,,254,254,254,254,,254,254,254,254,,,,,254,254,,,,255',
'255,255,254,255,254,254,254,255,255,254,254,,255,,255,255,255,255,255',
'255,255,,,,,,255,255,255,255,255,255,255,,,255,,,,,,,255,,,255,255,255',
'255,255,255,255,255,,255,255,255,,255,255,255,255,255,,,,,,,,,,,,,,',
',,,,,255,,,255,,,255,255,,,255,,,,,,255,,,,,,,,,255,,,,,255,255,255',
'255,,255,255,255,255,,,,,255,255,,,,256,256,256,255,256,255,255,255',
'256,256,255,255,,256,,256,256,256,256,256,256,256,,,,,,256,256,256,256',
'256,256,256,,,256,,,,,,,256,,,256,256,256,256,256,256,256,256,,256,256',
'256,,256,256,256,256,256,,,,,,,,,,,,,,,,,,,,256,,,256,,,256,256,,,256',
',,,,,256,,,,,,,,,256,,,,,256,256,256,256,,256,256,256,256,,,,,256,256',
',,,257,257,257,256,257,256,256,256,257,257,256,256,,257,,257,257,257',
'257,257,257,257,,,,,,257,257,257,257,257,257,257,,,257,,,,,,,257,,,257',
'257,257,257,257,257,257,257,,257,257,257,,257,257,257,257,257,,,,,,',
',,,,,,,,,,,,,257,,,257,,,257,257,,,257,,,,,,257,,,,,,,,,257,,,,,257',
'257,257,257,,257,257,257,257,,,,,257,257,,,,258,258,258,257,258,257',
'257,257,258,258,257,257,,258,,258,258,258,258,258,258,258,,,,,,258,258',
'258,258,258,258,258,,,258,,,,,,,258,,,258,258,258,258,258,258,258,258',
',258,258,258,,258,258,258,258,258,,,,,,,,,,,,,,,,,,,,258,,,258,,,258',
'258,,,258,,,,,,258,,,,,,,,,258,,,,,258,258,258,258,,258,258,258,258',
',,,,258,258,,,,259,259,259,258,259,258,258,258,259,259,258,258,,259',
',259,259,259,259,259,259,259,,,,,,259,259,259,259,259,259,259,,,259',
',,,,,,259,,,259,259,259,259,259,259,259,259,,259,259,259,,259,259,259',
'259,259,,,,,,,,,,,,,,,,,,,,259,,,259,,,259,259,,,259,,,,,,259,,,,,,',
',,259,,,,,259,259,259,259,,259,259,259,259,,,,,259,259,,,,260,260,260',
'259,260,259,259,259,260,260,259,259,,260,,260,260,260,260,260,260,260',
',,,,,260,260,260,260,260,260,260,,,260,,,,,,,260,,,260,260,260,260,260',
'260,260,260,,260,260,260,,260,260,260,260,260,,,,,,,,,,,,,,,,,,,,260',
',,260,,,260,260,,,260,,,,,,260,,,,,,,,,260,,,,,260,260,260,260,,260',
'260,260,260,,,,,260,260,,,,261,261,261,260,261,260,260,260,261,261,260',
'260,,261,,261,261,261,261,261,261,261,,,,,,261,261,261,261,261,261,261',
',,261,,,,,,,261,,,261,261,261,261,261,261,261,261,,261,261,261,,261',
'261,261,261,261,,,,,,,,,,,,,,,,,,,,261,,,261,,,261,261,,,261,,,,,,261',
',,,,,,,,261,,,,,261,261,261,261,,261,261,261,261,,,,,261,261,,,,262',
'262,262,261,262,261,261,261,262,262,261,261,,262,,262,262,262,262,262',
'262,262,,,,,,262,262,262,262,262,262,262,,,262,,,,,,,262,,,262,262,262',
'262,262,262,262,262,,262,262,262,,262,262,262,262,262,,,,,,,,,,,,,,',
',,,,,262,,,262,,,262,262,,,262,,,,,,262,,,,,,,,,262,,,,,262,262,262',
'262,,262,262,262,262,,,,,262,262,,,,263,263,263,262,263,262,262,262',
'263,263,262,262,,263,,263,263,263,263,263,263,263,,,,,,263,263,263,263',
'263,263,263,,,263,,,,,,,263,,,263,263,263,263,263,263,263,263,,263,263',
'263,,263,263,263,263,263,,,,,,,,,,,,,,,,,,,,263,,,263,,,263,263,,,263',
',,,,,263,,,,,,,,,263,,,,,263,263,263,263,,263,263,263,263,,,,,263,263',
',,,264,264,264,263,264,263,263,263,264,264,263,263,,264,,264,264,264',
'264,264,264,264,,,,,,264,264,264,264,264,264,264,,,264,,,,,,,264,,,264',
'264,264,264,264,264,264,264,,264,264,264,,264,264,264,264,264,,,,,,',
',,,,,,,,,,,,,264,,,264,,,264,264,,,264,,,,,,264,,,,,,,,,264,,,,,264',
'264,264,264,,264,264,264,264,,,,,264,264,,,,265,265,265,264,265,264',
'264,264,265,265,264,264,,265,,265,265,265,265,265,265,265,,,,,,265,265',
'265,265,265,265,265,,,265,,,,,,,265,,,265,265,265,265,265,265,265,265',
',265,265,265,,265,265,265,265,265,,,,,,,,,,,,,,,,,,,,265,,,265,,,265',
'265,,,265,,,,,,265,,,,,,,,,265,,,,,265,265,265,265,,265,265,265,265',
',,,,265,265,,,,266,266,266,265,266,265,265,265,266,266,265,265,,266',
',266,266,266,266,266,266,266,,,,,,266,266,266,266,266,266,266,,,266',
',,,,,,266,,,266,266,266,266,266,266,266,266,,266,266,266,,266,266,266',
'266,266,,,,,,,,,,,,,,,,,,,,266,,,266,,,266,266,,,266,,,,,,266,,,,,,',
',,266,,,,,266,266,266,266,,266,266,266,266,,,,,266,266,,,,271,271,271',
'266,271,266,266,266,271,271,266,266,,271,,271,271,271,271,271,271,271',
',,,,,271,271,271,271,271,271,271,,,271,,,,,,,271,,,271,271,271,271,271',
'271,271,271,,271,271,271,,271,271,271,271,271,,,,,,,,,,,,,,,,,,,,271',
',,271,,,271,271,,,271,,,,,,271,,,,,,,,,271,,,,,271,271,271,271,,271',
'271,271,271,,,,,271,271,,,,824,824,824,271,824,271,271,271,824,824,271',
'271,,824,,824,824,824,824,824,824,824,,,,,,824,824,824,824,824,824,824',
',,824,,,,,,,824,,,824,824,824,824,824,824,824,824,,824,824,824,,824',
'824,824,824,824,,,,,,,,,,,,,,,,,,,,824,,,824,,,824,824,,,824,,,,,,824',
',,,,,,,,824,,,,,824,824,824,824,,824,824,824,824,,,,,824,824,,,,779',
'779,779,824,779,824,824,824,779,779,824,824,,779,,779,779,779,779,779',
'779,779,,,,,,779,779,779,779,779,779,779,,,779,,,,,,,779,,,779,779,779',
'779,779,779,779,779,,779,779,779,,779,779,779,779,779,,,,,,,,,,,,,,',
',,,,,779,,,779,,,779,779,,,779,,,,,,779,,,,,,,,,779,,,,,779,779,779',
'779,,779,779,779,779,,,,,779,779,,,,765,765,765,779,765,779,779,779',
'765,765,779,779,,765,,765,765,765,765,765,765,765,,,,,,765,765,765,765',
'765,765,765,,,765,,,,,,,765,,,765,765,765,765,765,765,765,765,,765,765',
'765,,765,765,765,765,765,,,,,,,,,,,,,,,,,,,,765,,,765,,,765,765,,,765',
',765,,,,765,,,,,,,,,765,,,,,765,765,765,765,,765,765,765,765,,,,,765',
'765,,,,287,287,287,765,287,765,765,765,287,287,765,765,,287,,287,287',
'287,287,287,287,287,,,,,,287,287,287,287,287,287,287,,,287,,,,,,,287',
',,287,287,287,287,287,287,287,287,,287,287,287,,287,287,287,287,287',
',,,,,,,,,,,,,,,,,,,287,,,287,,,287,287,,,287,,,,,,287,,,,,,,,,287,,',
',,287,287,287,287,,287,287,287,287,,,,,287,287,,,,745,745,745,287,745',
'287,287,287,745,745,287,287,,745,,745,745,745,745,745,745,745,,,,,,745',
'745,745,745,745,745,745,,,745,,,,,,,745,,,745,745,745,745,745,745,745',
'745,,745,745,745,,745,745,745,745,745,,,,,,,,,,,,,,,,,,,,745,,,745,',
',745,745,,,745,,,,,,745,,,,,,,,,745,,,,,745,745,745,745,,745,745,745',
'745,,,,,745,745,,,,742,742,742,745,742,745,745,745,742,742,745,745,',
'742,,742,742,742,742,742,742,742,,,,,,742,742,742,742,742,742,742,,',
'742,,,,,,,742,,,742,742,742,742,742,742,742,742,,742,742,742,,742,742',
'742,742,742,,,,,,,,,,,,,,,,,,,,742,,,742,,,742,742,,,742,,,,,,742,,',
',,,,,,742,,,,,742,742,742,742,,742,742,742,742,,,,,742,742,,,,294,294',
'294,742,294,742,742,742,294,294,742,742,,294,,294,294,294,294,294,294',
'294,,,,,,294,294,294,294,294,294,294,,,294,,,,,,,294,,,294,294,294,294',
'294,294,294,294,294,294,294,294,,294,294,294,294,294,,,,,,,,,,,,,,,',
',,,,294,,,294,,,294,294,,,294,,294,,294,,294,,,294,,,,,,294,,,,,294',
'294,294,294,,294,294,294,294,,,,,294,294,,,,295,295,295,294,295,294',
'294,294,295,295,294,294,,295,,295,295,295,295,295,295,295,,,,,,295,295',
'295,295,295,295,295,,,295,,,,,,,295,,,295,295,295,295,295,295,295,295',
'295,295,295,295,,295,295,295,295,295,,,,,,,,,,,,,,,,,,,,295,,,295,,',
'295,295,,,295,,295,,295,,295,,,295,,,,,,295,,,,,295,295,295,295,,295',
'295,295,295,,,,,295,295,,,,303,303,303,295,303,295,295,295,303,303,295',
'295,,303,,303,303,303,303,303,303,303,,,,,,303,303,303,303,303,303,303',
',,303,,,,,,,303,,,303,303,303,303,303,303,303,303,303,303,303,303,,303',
'303,303,303,303,,,,,,,,,,,,,,,,,,,,303,,,303,,,303,303,,,303,,303,,303',
',303,,,303,,,,,,303,,,,,303,303,303,303,,303,303,303,303,,,,,303,303',
'303,,,733,733,733,303,733,303,303,303,733,733,303,303,,733,,733,733',
'733,733,733,733,733,,,,,,733,733,733,733,733,733,733,,,733,,,,,,,733',
',,733,733,733,733,733,733,733,733,733,733,733,733,,733,733,733,733,733',
',,,,,,,,,,,,,,,,,,,733,,,733,,,733,733,,,733,,733,,733,,733,,,733,,',
',,,733,,,,,733,733,733,733,,733,733,733,733,,,,,733,733,,,,310,310,310',
'733,310,733,733,733,310,310,733,733,,310,,310,310,310,310,310,310,310',
',,,,,310,310,310,310,310,310,310,,,310,,,,,,,310,,,310,310,310,310,310',
'310,310,310,,310,310,310,,310,310,310,310,310,,,,,,,,,,,,,,,,,,,,310',
',,310,,,310,310,,,310,,,,,,310,,,,,,,,,310,,,,,310,310,310,310,,310',
'310,310,310,,,,,310,310,,,,686,686,686,310,686,310,310,310,686,686,310',
'310,,686,,686,686,686,686,686,686,686,,,,,,686,686,686,686,686,686,686',
',,686,,,,,,,686,,,686,686,686,686,686,686,686,686,,686,686,686,,686',
'686,686,686,686,,,,,,,,,,,,,,,,,,,,686,,,686,,,686,686,,,686,,,,,,686',
',,,,,,,,686,,,,,686,686,686,686,,686,686,686,686,,,,,686,686,,,,312',
'312,312,686,312,686,686,686,312,312,686,686,,312,,312,312,312,312,312',
'312,312,,,,,,312,312,312,312,312,312,312,,,312,,,,,,,312,,,312,312,312',
'312,312,312,312,312,,312,312,312,,312,312,312,312,312,,,,,,,,,,,,,,',
',,,,,312,,,312,,,312,312,,,312,,,,,,312,,,,,,,,,312,,,,,312,312,312',
'312,,312,312,312,312,,,,,312,312,,,,315,315,315,312,315,312,312,312',
'315,315,312,312,,315,,315,315,315,315,315,315,315,,,,,,315,315,315,315',
'315,315,315,,,315,,,,,,,315,,,315,315,315,315,315,315,315,315,,315,315',
'315,,315,315,315,315,315,,,,,,,,,,,,,,,,,,,,315,,,315,,,315,315,,,315',
',,,,,315,,,,,,,,,315,,,,,315,315,315,315,,315,315,315,315,,,,,315,315',
',,,316,316,316,315,316,315,315,315,316,316,315,315,,316,,316,316,316',
'316,316,316,316,,,,,,316,316,316,316,316,316,316,,,316,,,,,,,316,,,316',
'316,316,316,316,316,316,316,,316,316,316,,316,316,316,316,316,,,,,,',
',,,,,,,,,,,,,316,,,316,,,316,316,,,316,,,,,,316,,,,,,,,,316,,,,,316',
'316,316,316,,316,316,316,316,,,,,316,316,,,,685,685,685,316,685,316',
'316,316,685,685,316,316,,685,,685,685,685,685,685,685,685,,,,,,685,685',
'685,685,685,685,685,,,685,,,,,,,685,,,685,685,685,685,685,685,685,685',
',685,685,685,,685,685,685,685,685,,,,,,,,,,,,,,,,,,,,685,,,685,,,685',
'685,,,685,,,,,,685,,,,,,,,,685,,,,,685,685,685,685,,685,685,685,685',
',,,,685,685,,,,,,,685,,685,685,685,,,685,685,321,321,321,321,321,,,',
'321,321,,,,321,,321,321,321,321,321,321,321,,,,,,321,321,321,321,321',
'321,321,,,321,,,,,,321,321,,321,321,321,321,321,321,321,321,321,,321',
'321,321,,321,321,321,321,321,,,,,,,,,,,,,,,,,,,,321,,,321,,,321,321',
',,321,,321,,,,321,,,,,,,,,321,,,,,321,321,321,321,,321,321,321,321,',
',,,321,321,,,,681,681,681,321,681,321,321,321,681,681,321,321,,681,',
'681,681,681,681,681,681,681,,,,,,681,681,681,681,681,681,681,,,681,',
',,,,,681,,,681,681,681,681,681,681,681,681,,681,681,681,,681,681,681',
'681,681,,,,,,,,,,,,,,,,,,,,681,,,681,,,681,681,,,681,,,,,,681,,,,,,',
',,681,,,,,681,681,681,681,,681,681,681,681,,,,,681,681,,,,680,680,680',
'681,680,681,681,681,680,680,681,681,,680,,680,680,680,680,680,680,680',
',,,,,680,680,680,680,680,680,680,,,680,,,,,,,680,,,680,680,680,680,680',
'680,680,680,,680,680,680,,680,680,680,680,680,,,,,,,,,,,,,,,,,,,,680',
',,680,,,680,680,,,680,,,,,,680,,,,,,,,,680,,,,,680,680,680,680,,680',
'680,680,680,,,,,680,680,,,,679,679,679,680,679,680,680,680,679,679,680',
'680,,679,,679,679,679,679,679,679,679,,,,,,679,679,679,679,679,679,679',
',,679,,,,,,,679,,,679,679,679,679,679,679,679,679,679,679,679,679,,679',
'679,679,679,679,,,,,,,,,,,,,,,,,,,,679,,,679,,,679,679,,,679,,,,679',
',679,,,679,,,,,,679,,,,,679,679,679,679,,679,679,679,679,,,,,679,679',
',,,678,678,678,679,678,679,679,679,678,678,679,679,,678,,678,678,678',
'678,678,678,678,,,,,,678,678,678,678,678,678,678,,,678,,,,,,,678,,,678',
'678,678,678,678,678,678,678,678,678,678,678,,678,678,678,678,678,,,',
',,,,,,,,,,,,,,,,678,,,678,,,678,678,,,678,,678,,678,,678,,,678,,,,,',
'678,,,,,678,678,678,678,,678,678,678,678,,,,,678,678,,,,218,218,218',
'678,218,678,678,678,218,218,678,678,,218,,218,218,218,218,218,218,218',
',,,,,218,218,218,218,218,218,218,,,218,,,,,,,218,,,218,218,218,218,218',
'218,218,218,,218,218,218,,218,218,,,218,,,,,,,,,,,,,,,,,,,,218,,,218',
',,218,218,,,218,,218,,,,,,,,,,,,,,,,,,218,218,218,218,,218,218,218,218',
',,,,218,218,,,,221,221,221,218,221,218,218,218,221,221,,,,221,,221,221',
'221,221,221,221,221,,,,,,221,221,221,221,221,221,221,,,221,,,,,,,221',
',,221,221,221,221,221,221,221,221,,221,221,221,,221,221,,,221,,,,,,',
',,,,,,,,,,,,,221,,,221,,,221,221,,,221,,221,,,,,,,,,,,,,,,,,,221,221',
'221,221,,221,221,221,221,,,,,221,221,,,,553,553,553,221,553,221,221',
'221,553,553,,,,553,,553,553,553,553,553,553,553,,,,,,553,553,553,553',
'553,553,553,,,553,,,,,,,553,,,553,553,553,553,553,553,553,553,,553,553',
'553,,553,553,,,553,,,,,,,,,,,,,,,,,,,,553,,,553,,,553,553,,,553,,,,',
',,,,,,,,,,,,,,,553,553,553,553,,553,553,553,553,,,,,553,553,,,,78,78',
'78,553,78,553,553,553,78,78,,,,78,,78,78,78,78,78,78,78,,,,,,78,78,78',
'78,78,78,78,,,78,,,,,,,78,,,78,78,78,78,78,78,78,78,,78,78,78,,78,78',
',,78,,,,,,,,,,,,,,,,,,,,78,,,78,,,78,78,,,78,,,,,,,,,,,,,,,,,,,,78,78',
'78,78,,78,78,78,78,,,,,78,78,,,,544,544,544,78,544,78,78,78,544,544',
',,,544,,544,544,544,544,544,544,544,,,,,,544,544,544,544,544,544,544',
',,544,,,,,,,544,,,544,544,544,544,544,544,544,544,,544,544,544,,544',
'544,,,544,,,,,,,,,,,,,,,,,,,,544,,,544,,,544,544,,,544,,,,,,,,,,,,,',
',,,,,,544,544,544,544,,544,544,544,544,,,,,544,544,,,,348,348,348,544',
'348,544,544,544,348,348,,,,348,,348,348,348,348,348,348,348,,,,,,348',
'348,348,348,348,348,348,,,348,,,,,,,348,,,348,348,348,348,348,348,348',
'348,,348,348,348,,348,348,,,348,,,,,,,,,,,,,,,,,,,,348,,,348,,,348,348',
',,348,,,,,,,,,,,,,,,,,,,,348,348,348,348,,348,348,348,348,,,,,348,348',
',,,1103,1103,1103,348,1103,348,348,348,1103,1103,,,,1103,,1103,1103',
'1103,1103,1103,1103,1103,,,,,,1103,1103,1103,1103,1103,1103,1103,,,1103',
',,,,,,1103,,,1103,1103,1103,1103,1103,1103,1103,1103,,1103,1103,1103',
',1103,1103,,,1103,,,,,,,,,,,,,,,,,,,,1103,,,1103,,,1103,1103,,,1103',
',,,,,,,,,,,,,,,,,,,1103,1103,1103,1103,,1103,1103,1103,1103,,,,,1103',
'1103,,,,1033,1033,1033,1103,1033,1103,1103,1103,1033,1033,,,,1033,,1033',
'1033,1033,1033,1033,1033,1033,,,,,,1033,1033,1033,1033,1033,1033,1033',
',,1033,,,,,,,1033,,,1033,1033,1033,1033,1033,1033,1033,1033,,1033,1033',
'1033,,1033,1033,,,1033,,,,,,,,,,,,,,,,,,,,1033,,,1033,,,1033,1033,,',
'1033,,,,,,,,,,,,,,,,,,,,1033,1033,1033,1033,,1033,1033,1033,1033,,,',
',1033,1033,,,,77,77,77,1033,77,1033,1033,1033,77,77,,,,77,,77,77,77',
'77,77,77,77,,,,,,77,77,77,77,77,77,77,,,77,,,,,,,77,,,77,77,77,77,77',
'77,77,77,,77,77,77,,77,77,,,77,,,,,,,,,,,,,,,,,77,,,77,,,77,,,77,77',
',,77,,,,,,,,,,,,,,,,,,,,77,77,77,77,,77,77,77,77,,,,,77,77,,,,76,76',
'76,77,76,77,77,77,76,76,,,,76,,76,76,76,76,76,76,76,,,,,,76,76,76,76',
'76,76,76,,,76,,,,,,,76,,,76,76,76,76,76,76,76,76,,76,76,76,,76,76,,',
'76,,,,,,,,,,,,,,,,,,,,76,,,76,,,76,76,,,76,,76,,,,,,,,,,,,,,,,,,76,76',
'76,76,,76,76,76,76,,,,,76,76,,,,1023,1023,1023,76,1023,76,76,76,1023',
'1023,,,,1023,,1023,1023,1023,1023,1023,1023,1023,,,,,,1023,1023,1023',
'1023,1023,1023,1023,,,1023,,,,,,,1023,,,1023,1023,1023,1023,1023,1023',
'1023,1023,,1023,1023,1023,,1023,1023,,,1023,,,,,,,,,,,,,,,,,,,,1023',
',,1023,,,1023,1023,,,1023,,,,,,,,,,,,,,,,,,,,1023,1023,1023,1023,,1023',
'1023,1023,1023,,,,,1023,1023,,,,759,759,759,1023,759,1023,1023,1023',
'759,759,,,,759,,759,759,759,759,759,759,759,,,,,,759,759,759,759,759',
'759,759,,,759,,,,,,,759,,,759,759,759,759,759,759,759,759,,759,759,759',
',759,759,,,759,,,,,,,,,,,,,,,,,,,,759,,,759,,,759,759,,,759,,,,,,,,',
',,,,,,,,,,,759,759,759,759,,759,759,759,759,,,,,759,759,,,,38,38,38',
'759,38,759,759,759,38,38,,,,38,,38,38,38,38,38,38,38,,,,,,38,38,38,38',
'38,38,38,,,38,,,,,,,38,,,38,38,38,38,38,38,38,38,,38,38,38,,38,38,,',
'38,,,,,,,,,,,,,,,,,,,,38,,,38,,,38,38,,,38,,,1038,,1038,1038,1038,1038',
'1038,,,,,,,,,1038,,38,38,38,38,,38,38,38,38,,,,,38,38,,,,38,,1038,38',
',38,38,38,748,748,748,,748,1038,1038,,748,748,1038,,,748,,748,748,748',
'748,748,748,748,,,,,,748,748,748,748,748,748,748,,,748,,,,,,,748,,,748',
'748,748,748,748,748,748,748,,748,748,748,,748,748,,,748,,,,,,,,,,,,',
',,,,,,,748,,,748,,,748,748,,,748,,,,,,,,,,,,,,,,,,,,748,748,748,748',
',748,748,748,748,,,,,748,748,,,,329,329,329,748,329,748,748,748,329',
'329,,,,329,,329,329,329,329,329,329,329,,,,,,329,329,329,329,329,329',
'329,,,329,,,,,,,329,,,329,329,329,329,329,329,329,329,,329,329,329,',
'329,329,,,329,,,,,,,,,,,,,,,,,,,,329,,,329,,,329,329,,,329,,,777,,777',
'777,777,777,777,,,,,,,,,777,,329,329,329,329,,329,329,329,329,,,,,329',
'329,,,,329,,777,329,,329,329,329,37,37,37,,37,777,777,,37,37,777,,,37',
',37,37,37,37,37,37,37,,,,,,37,37,37,37,37,37,37,,,37,,,,,,,37,,,37,37',
'37,37,37,37,37,37,,37,37,37,,37,37,,,37,,,,,,,,,,,,,,,,,,,,37,,,37,',
',37,37,,,37,,37,,,,,,,,,,,,,,,,,,37,37,37,37,,37,37,37,37,,,,,37,37',
',,,,,,37,,37,37,37,423,423,423,423,423,423,423,423,423,423,423,423,423',
'423,423,423,423,423,423,423,423,423,423,423,,,,423,423,423,423,423,423',
'423,423,423,423,,,,,,423,423,423,423,423,423,423,423,423,,,423,,,,,',
',,423,423,,423,423,423,423,423,423,423,,,423,423,,,,423,423,423,423',
',,,,,,,,,,,,,423,423,,423,423,423,423,423,423,423,423,423,423,423,423',
',,423,423,,,,,,,,,,,,,,423,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8',
'8,8,8,,,,8,8,8,8,8,8,8,8,8,8,,,,,,8,8,8,8,8,8,8,8,8,8,,8,,,,,,,,8,8',
',8,8,8,8,8,8,8,,,8,8,,,,8,8,8,8,,,,,,,,,,,,,,8,8,,8,8,8,8,8,8,8,8,8',
'8,8,8,,,8,8,,,,,,,,,,,,,,8,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9',
'9,9,9,,,,9,9,9,9,9,9,9,9,9,9,,,,,,9,9,9,9,9,9,9,9,9,,,9,,,,,,,,9,9,',
'9,9,9,9,9,9,9,,,9,9,,,,9,9,9,9,,,,,,,,,,,,,,9,9,,9,9,9,9,9,9,9,9,9,9',
'9,9,,,9,9,,,,,,,,,,,,,,9,665,665,665,665,665,665,665,665,665,665,665',
'665,665,665,665,665,665,665,665,665,665,665,665,665,,,,665,665,665,665',
'665,665,665,665,665,665,,,,,,665,665,665,665,665,665,665,665,665,,,665',
',,,,,,,665,665,,665,665,665,665,665,665,665,,,665,665,,,,665,665,665',
'665,,,,,,,,,,,,,,665,665,,665,665,665,665,665,665,665,665,665,665,665',
'665,,,665,665,875,875,875,875,,,1182,,1182,1182,1182,1182,1182,665,',
',875,875,875,875,,1182,,875,875,,,,,875,875,,,875,875,,,,,,,,,1182,',
',,,,,,,,875,1182,1182,875,,,1182,875,,,875,,875,,,,,,,875,,,,,,,875',
',,,875,875,875,875,,875,875,875,875,,,,,875,875,1200,1200,1200,1200',
',,875,,875,875,875,,,875,875,,1200,1200,1200,1200,,,1036,1200,1036,1036',
'1036,1036,1036,1200,1200,,,1200,,,,1036,,,,,,,,,,,,,,,,1200,,,1200,',
',1036,1200,,,1200,,,,,1036,1036,1036,1036,,,,1036,,,,1200,,,,1200,1200',
'1200,1200,,1200,1200,1200,1200,,,,,1200,1200,1001,1001,1001,1001,,,1200',
',1200,1200,1200,,,1200,1200,,1001,1001,1001,1001,,,1094,1001,1094,1094',
'1094,1094,1094,1001,1001,,,1001,,,,1094,,,,,,,,,,,,,,,,1001,,,1001,',
',1094,1001,,,1001,,,,,1094,1094,1094,1094,,,,1094,,,,1001,,,,1001,1001',
'1001,1001,,1001,1001,1001,1001,,,,,1001,1001,,,,,,,1001,,1001,1001,1001',
',,1001,1001,725,725,725,725,725,725,725,725,725,725,725,725,725,725',
'725,725,725,725,725,725,725,725,725,725,,,,725,725,725,725,725,725,725',
'725,725,725,,,,,,725,725,725,725,725,725,725,725,725,,,725,,,,,,,,725',
'725,,725,725,725,725,725,725,725,,,725,725,,,,725,725,725,725,,,,,,',
',,,,,,,725,725,,725,725,725,725,725,725,725,725,725,725,725,725,,,725',
'484,484,484,484,,,1041,,1041,1041,1041,1041,1041,,,,484,484,484,484',
',1041,,484,,,,,,484,484,,,484,,,,,,,,,,1041,,,,,,,,,,484,1041,1041,484',
',,1041,484,,,484,,,,,,,,,,,,,,,,484,,,,484,484,484,484,,484,484,484',
'484,,,,,484,484,1124,1124,1124,1124,,,484,,484,484,484,,,484,484,,1124',
'1124,1124,1124,,,363,1124,363,363,363,363,363,1124,1124,,,1124,,,,363',
',,,,,,,,,,,,,,,1124,,,1124,,,363,1124,,,1124,,1124,,,363,363,363,363',
',,,363,,,,1124,,,,1124,1124,1124,1124,,1124,1124,1124,1124,,,,,1124',
'1124,1138,1138,1138,1138,,,1124,,1124,1124,1124,,,1124,1124,,1138,1138',
'1138,1138,,,1097,1138,1097,1097,1097,1097,1097,1138,1138,,,1138,,,,1097',
',,,,,,,,,,,,,,,1138,,,1138,,,1097,1138,,,1138,,,,,1097,1097,1097,1097',
',,,1097,,,,1138,,,,1138,1138,1138,1138,,1138,1138,1138,1138,,,,,1138',
'1138,1140,1140,1140,1140,,,1138,,1138,1138,1138,,,1138,1138,,1140,1140',
'1140,1140,,,1043,1140,1043,1043,1043,1043,1043,1140,1140,,,1140,,,,1043',
',,,,,,,,,,,,,,,1140,,,1140,,,1043,1140,,,1140,,1140,,,,,1043,1043,,',
',1043,,,,1140,,,,1140,1140,1140,1140,,1140,1140,1140,1140,,,,,1140,1140',
'1176,1176,1176,1176,,,1140,,1140,1140,1140,,,1140,1140,,1176,1176,1176',
'1176,,,1099,1176,1099,1099,1099,1099,1099,1176,1176,,,1176,,,,1099,',
',,,,,,,,,,,,,,1176,,,1176,,,1099,1176,,,1176,,,,,,,1099,1099,,,,1099',
',,,1176,,,,1176,1176,1176,1176,,1176,1176,1176,1176,,,,,1176,1176,889',
'889,889,889,,,1176,,1176,1176,1176,,,1176,1176,,889,889,889,889,,,1186',
'889,1186,1186,1186,1186,1186,889,889,,,889,,,,1186,,,,,,,,,,,,,,,,889',
',,889,,,1186,889,,,889,,,,,,,1186,1186,,,,1186,,,,889,,,,889,889,889',
'889,,889,889,889,889,,,,,889,889,881,881,881,881,,,889,,889,889,889',
',,889,889,,881,881,881,881,,,1212,881,1212,1212,1212,1212,1212,881,881',
',,881,,,,1212,,,,,,,,,,,,,,,,881,,,881,,,1212,881,,,881,,881,,,,,1212',
'1212,,,,1212,,,,881,,,,881,881,881,881,,881,881,881,881,,,,,881,881',
'871,871,871,871,,,881,,881,881,881,,,881,881,,871,871,871,871,,,1180',
'871,1180,1180,1180,1180,1180,871,871,,,871,,,,1180,,,,,,,,,,,,,,,,871',
',,871,,,1180,871,,,871,,,,,1180,1180,1180,1180,,,,1180,,,,871,,,,871',
'871,871,871,,871,871,871,871,,,,,871,871,874,874,874,874,,,871,,871',
'871,871,,,871,871,,874,874,874,874,,,,874,874,,,,,874,874,,,874,874',
',,,,,,,,,,,,,,,,,,874,,,874,,,,874,,,874,,874,,,,,,,874,,,,,,,874,,',
',874,874,874,874,,874,874,874,874,,,,,874,874,1084,1084,1084,1084,,',
'874,,874,874,874,,,874,874,,1084,1084,1084,1084,,,1184,1084,1184,1184',
'1184,1184,1184,1084,1084,,,1084,,,,1184,,,,,,,,,,,,,,,,1084,,,1084,',
',1184,1084,,,1084,,,,,,,1184,1184,,,,1184,,,,1084,,,,1084,1084,1084',
'1084,,1084,1084,1084,1084,,,,,1084,1084,985,985,985,985,,,1084,,1084',
'1084,1084,,,1084,1084,,985,985,985,985,,,1159,985,1159,1159,1159,1159',
'1159,985,985,,,985,,,,1159,,,,,,,,,,,,,,,,985,,,985,,,1159,985,,,985',
',,,,,,1159,1159,,,,1159,,,,985,,,,985,985,985,985,,985,985,985,985,',
',,,985,985,702,702,702,702,,,985,,985,985,985,,,985,985,,702,702,702',
'702,,,642,702,642,642,642,642,642,702,702,,,702,,,,642,,,,,,,,,,,,,',
',,702,,,702,,,642,702,,,702,,702,,,642,642,642,642,,,,642,,,,702,,,',
'702,702,702,702,,702,702,702,702,,,,,702,702,702,953,953,953,953,,702',
',702,702,702,,,702,702,,,953,953,953,953,,,,953,953,,,,,953,953,,,953',
'953,,,,,,,,,,,,,,,,,,,953,,,953,,,,953,,,953,,953,,,,,,,953,,,,,,,953',
',,,953,953,953,953,,953,953,953,953,,,,,953,953,,,,,,,953,,953,953,953',
',,953,953,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24',
'24,24,24,24,,,,24,24,24,24,24,24,24,24,24,24,,,,,,24,24,24,24,24,24',
'24,24,24,24,24,24,,24,,,,,,24,24,,24,24,24,24,24,24,24,,,24,24,,,,24',
'24,24,24,,,,,,24,,,,,,,,24,24,,24,24,24,24,24,24,24,24,24,24,24,24,',
',24,891,891,891,891,,,,404,,404,404,404,404,404,,,891,891,891,,,,404',
',,,,,,891,891,,,891,708,708,708,708,,,,,,,404,404,,,,,708,708,708,404',
'404,404,404,,,,404,,,708,708,,,708,,,,,,,,,,,,,,,,891,891,891,891,,891',
'891,891,891,,,,,891,891,,,,,,,891,,891,891,891,,,,,,,,,708,708,708,708',
',708,708,708,708,,,,,708,708,890,890,890,890,,,708,,708,708,708,,,,',
',890,890,890,1157,1157,,,1157,,,,,,890,890,1157,1157,890,1157,1157,1157',
'1157,1157,1157,1157,,,1157,1157,,,,1157,1157,1157,1157,,,,,,1157,,,',
',,,,1157,1157,,1157,1157,1157,1157,1157,1157,1157,1157,1157,1157,1157',
'1157,,,1157,890,890,890,890,,890,890,890,890,,,,,890,890,672,672,,,672',
',890,,890,890,890,,672,672,,672,672,672,672,672,672,672,,,672,672,,',
',672,672,672,672,,,,,,672,,,,,,,,672,672,,672,672,672,672,672,672,672',
'672,672,672,672,672,635,635,672,,635,,,,,,,,635,635,,635,635,635,635',
'635,635,635,,,635,635,,,,635,635,635,635,,,,,,635,,,,,,,,635,635,,635',
'635,635,635,635,635,635,635,635,635,635,635,558,558,635,,558,,,,,,,',
'558,558,,558,558,558,558,558,558,558,,,558,558,,,,558,558,558,558,,',
',,,558,,,,,,,,558,558,,558,558,558,558,558,558,558,558,558,558,558,558',
'634,634,558,,634,,,,,,,,634,634,,634,634,634,634,634,634,634,,,634,634',
',,,634,634,634,634,,,,,,634,,,,,,,,634,634,,634,634,634,634,634,634',
'634,634,634,634,634,634,557,557,634,,557,,,,,,,,557,557,,557,557,557',
'557,557,557,557,,,557,557,,,,557,557,557,557,,,,,,557,,,,,,,,557,557',
',557,557,557,557,557,557,557,557,557,557,557,557,478,478,557,,478,,',
',,,,,478,478,,478,478,478,478,478,478,478,,,478,478,,,,478,478,478,478',
',,,,,478,,,,,,,,478,478,,478,478,478,478,478,478,478,478,478,478,478',
'478,548,548,478,,548,,,,,,,,548,548,,548,548,548,548,548,548,548,,,548',
'548,,,,548,548,548,548,,,,,,548,,,,,,,,548,548,,548,548,548,548,548',
'548,548,548,548,548,548,548,628,628,548,,628,,,,,,,,628,628,,628,628',
'628,628,628,628,628,,,628,628,,,,628,628,628,628,,,,,,628,,,,,,,,628',
'628,,628,628,628,628,628,628,628,628,628,628,628,628,290,290,628,,290',
',,,,,,,290,290,,290,290,290,290,290,290,290,,,290,290,,,,290,290,290',
'290,,,,,,,,,,,,,,290,290,,290,290,290,290,290,290,290,290,290,290,290',
'290,225,225,290,,225,,,,,,,,225,225,,225,225,225,225,225,225,225,,,225',
'225,,,,225,225,225,225,,,,,,225,,,,,,,,225,225,,225,225,225,225,225',
'225,225,225,225,225,225,225,627,627,225,,627,,,,,,,,627,627,,627,627',
'627,627,627,627,627,,,627,627,,,,627,627,627,627,,,,,,627,,,,,,,,627',
'627,,627,627,627,627,627,627,627,627,627,627,627,627,477,477,627,,477',
',,,,,,,477,477,,477,477,477,477,477,477,477,,,477,477,,,,477,477,477',
'477,,,,,,477,,,,,,,,477,477,,477,477,477,477,477,477,477,477,477,477',
'477,477,547,547,477,,547,,,,,,,,547,547,,547,547,547,547,547,547,547',
',,547,547,,,,547,547,547,547,,,,,,547,,,,,,,,547,547,,547,547,547,547',
'547,547,547,547,547,547,547,547,1152,1152,547,,1152,,,,,,,,1152,1152',
',1152,1152,1152,1152,1152,1152,1152,,,1152,1152,,,,1152,1152,1152,1152',
',,,,,1152,,,,,,,,1152,1152,,1152,1152,1152,1152,1152,1152,1152,1152',
'1152,1152,1152,1152,1156,1156,1152,,1156,,,,,,,,1156,1156,,1156,1156',
'1156,1156,1156,1156,1156,,,1156,1156,,,,1156,1156,1156,1156,,,,,,1156',
',,,,,,,1156,1156,,1156,1156,1156,1156,1156,1156,1156,1156,1156,1156',
'1156,1156,673,673,1156,,673,,,,,,,,673,673,,673,673,673,673,673,673',
'673,,,673,673,,,,673,673,673,673,,,,,,673,,,,,,,,673,673,,673,673,673',
'673,673,673,673,673,673,673,673,673,224,224,673,,224,,,,,,,,224,224',
',224,224,224,224,224,224,224,,,224,224,,,,224,224,224,224,,,,,,224,',
',,,,,,224,224,,224,224,224,224,224,224,224,224,224,224,224,224,,,224' ]
        racc_action_check = arr = ::Array.new(28164, nil)
        idx = 0
        clist.each do |str|
          str.split(',', -1).each do |i|
            arr[idx] = i.to_i unless i.empty?
            idx += 1
          end
        end

racc_action_pointer = [
  3830,  1471,   nil,    62,   nil,  5256,  1079,  1168, 24697, 24825,
  1162,   nil,  1068,  1061,  1114,   815,  1085,  1080,   587,   248,
   nil,   -41,  5780,  2390, 26779,  1050,   nil,   339,   nil,     5,
  5921,  6031,  6165,  6296,  6427,   nil,  3542, 24430, 24021,   nil,
   921,   390,   614,   984,  6558,  6689,   128,  6820,  6951,   522,
  7082,   792,   831,   804,   850,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,  7485,   nil,  7626,  7757,  7888,   -15,
   nil,  8150,  8281,   nil,   nil,  8412, 23628, 23497, 22842,   nil,
   nil,   nil,   nil,   nil,   nil,   589,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   471,   nil,   nil,   583,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   794,
   nil,  8829,   nil,   nil,   nil,   nil,  8972,  9103,  9234,  9365,
  9508,   nil,  2390,   nil,   687,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   573,   nil,  3110,  9770,  9901, 22449, 14708,
 14839, 22580, 14970, 15101, 28049, 27622,   nil,   nil, 15363, 15494,
 15625,   nil,   nil,   933,    74,   108,   344,   109,   167,   220,
   nil, 16411,  4118,   131,   nil, 16673, 16804, 16935, 17066, 17197,
 17328, 17459, 17590, 17721, 17852, 17983, 18114, 18245, 18376, 18507,
 18638, 18769, 18900, 19031, 19162, 19293, 19424,   nil,   nil,   nil,
   nil, 19555,   nil,   nil,   409,   444,   475,   504,   556,   557,
   559,   -50,   -32,   198,   nil,   nil,   nil, 20079,   nil,   nil,
 27561,   nil,   nil,  1255, 20472, 20603,   nil,   nil,   nil,   nil,
   nil,   nil,   nil, 20734,   nil,  3254,   nil,  1211,  1219,   nil,
 20996,  1281, 21258,   nil,   nil, 21389, 21520,   nil,   nil,   370,
   nil, 21794,  1223,  1283,  1273,  1526,  1291,  1333,  1291, 24291,
   950,  1221,  1110,  1083,  1383,  1077,   nil,  1383,  1346,     6,
   -60,   208,   nil,   nil,   nil,   278,   140,   287, 23104,   nil,
    98,   864,  6296,  6165,   777,   nil,   851, 12283,   nil, 12414,
   819,  2822,   358, 25560,   899,  2246,  2102,  1367,   916,   nil,
   485,   600,   988,  1017,   687,  1094,   nil,   849,  2534,    14,
   -11, 14166,  1238,  4262,   291,  1189,  1072,   551,   943,   777,
  1264,  1087,  1286,   nil,   nil,  1085,  1229,   398,   nil,   844,
   nil,  1210, 12545,   nil, 26850,   nil,   178,   405,   293,   281,
   392,   -41,   -25,   400,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,  1212, 24569,   nil,   nil,   nil,   nil,  1207,   nil,
  1273,  1178, 14577,  1176,   nil,   nil,  1101,   nil,   266,   280,
  1202,   nil,   nil,  2678,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,  2822,  1098,   nil,  1093,  1091,   356,   277,
 14443,   nil,   nil,   nil,   805,     0,  1113,   nil,   nil, 14307,
 14029,   nil,   nil,   nil,    10,   nil,  1108, 27744, 27378, 13888,
   -31, 13757, 13626, 13495, 25459,  4406,  4550,  1133,  1277,  1040,
  1036,  1035,  1025,  6427,  5870,  5983,  4694,  4838,  4982,  5113,
  4262,  5256,  1353,  1388,  5387,  5518,  3398,  5649,   nil,   -14,
   nil, 13365,   nil,   nil,   nil,   nil, 13224,   944,   931,   926,
   nil,   nil,   nil,   922,   nil,   nil, 13093,   nil, 12962,   nil,
 12831,   nil,   170,   nil,   nil,   nil, 12688,   935,   nil,   910,
   904,   nil,   nil,   900, 22973,   860,  5113, 27805, 27439,   849,
   898,   nil,   nil, 22711,   798,   nil, 12152, 27317, 27195, 12021,
  5780,  3974, 11890, 11759,   828,   822,   nil,   nil,   662,   549,
   544,   539,   505,   nil,   498,   481,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   460,   699,   nil,   nil, 11628,
   nil,   nil,   nil,    11,   nil,   nil,   nil,   519,   nil,   nil,
   509,   374,   400, 11497,   510,   481,   320,   351,   nil, 11366,
   nil,   nil, 11235,   nil,   nil,   nil,  3398,   333,   nil,  4694,
     2,   255,   162,    80,    44, 11104, 10973, 27683, 27500,  1231,
   nil,   nil,   939,   nil, 27256, 27134, 10842,   nil,   nil,    24,
  4838,   nil, 26540,   nil,   nil,   nil,   nil,  1004,   nil,   nil,
   nil,  1345,   nil,   nil,   174,   nil,   176,   nil,   nil,  1319,
   nil,  1318,   nil,   nil,   nil, 24953,   nil, 10711,  1292, 10568,
 10437, 10306, 27073, 27988, 10175, 10032,   389,  1328, 22318, 22187,
 22056, 21925,  1295,   nil,   nil, 21651, 21127,  1261,   nil,   nil,
   nil,   169,   142,   139,   695,  1210,  1245,   nil,  1218,   nil,
   nil,   294, 26537,   -81,   nil,   771,   nil,   nil, 26896,  7485,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,  1277,   258,   nil,  1199, 25376,   nil,  1325,   nil,  1309,
   -21,   nil,   nil, 20865,   nil,  1232,  1237,  1338,   nil,  1216,
   nil,  1263, 20341,   nil,   nil, 20210,   nil,    25, 24160,  1229,
   nil,  1235,   941,  1373,  1281,  1517,   443,  1300,  1257, 23890,
   nil,  1328,    20,  2966,  1382, 19948,   nil,   nil,   802,   nil,
   nil,  1238,   nil,   nil,   716, 13365,   nil, 24338,   nil, 19817,
   nil,  1080,   nil,  1309,  1315,  1427,  1319,   nil,   nil,   nil,
   nil,   nil,   nil, 13384,  3110,   nil,   nil,   nil,   nil,   512,
   820,   nil,  1446,   nil,   nil,   nil,   nil,   nil,  1452,  1454,
   nil,   nil,    53,  1335,  1661,  1805,  1949,   479,  3974,  2093,
   966,   nil,  1357,  1958, 19686,   nil,    50,   -22,    32,   nil,
   nil,   nil,   nil,   nil,  2966,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    52, 16542,   110,   138,   114,   249,   361,
   nil,  4118, 16280,   nil,   256,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil, 16149, 16018, 15887, 15756,   -23,
   464, 26145,    97,   199, 26243, 25037,   341,   -75,   432,   436,
   nil, 26047,   nil,   442,   454,   538,   571,   nil,   487, 25949,
 26994, 26862,   nil,   nil,   nil,   nil,   nil, 15232,   nil,   nil,
   538,   nil,   nil,   550,   538,   nil,   nil, 13329,  2678,   nil,
   nil,   nil,   nil,   545,   253,   nil,   nil,  9639,   704,   nil,
  2534,   591,   639,   nil,   nil,   609,   nil,   608,   610,   nil,
   615,   nil,   704,   nil,   624,   642,   nil,   651,   nil,   nil,
   660,  1224,  1512,   nil,   nil,  8686,   nil,   790,   nil,  8555,
   814,   nil,   nil, 26636,  8019,  1094,  2237,   858,   nil,   860,
   347,  4982,   nil,   nil,   nil,   nil,   759,   806,   777,   776,
   219,   nil,   nil,  7344,  7213,   nil,   nil,   nil,   nil,   793,
   794,   799,   797,   nil,   nil, 26439,   nil,   nil,   -18,   nil,
   nil,   809,   814,   815,   nil,   nil,   nil,   862,   829,   -34,
   nil, 25233,   nil,   859,   nil,   nil,   -66,   nil,   nil,  3830,
  4550,   nil,   nil,   860,   886,   nil,   888,   889,   891,   nil,
   919,   899,   886, 23759,   nil,   nil,   nil,  3686,   nil,  3686,
   nil,   nil,    26, 23366,   nil,   nil, 25138,   nil, 24068,   nil,
   nil, 25446,   nil, 25756,   nil,   nil,   nil,   nil,   915,   916,
  3542,   nil,    42,   nil,    38,   771,   257,   nil,   nil,   nil,
   nil,   177,   nil,   nil,   nil,  1060,  1814,  1670,  1571,   nil,
   nil,   nil,   nil,   nil,   720,   993,   247,   nil,   nil,   nil,
   nil,   nil,   nil,   951, 26341,   954,   nil,   nil,   136,   nil,
   nil,   859,   nil,   nil, 25236,   nil,   nil, 25658,   nil, 25854,
   nil,   nil,   983, 23235,   667,  1048,   nil,  1382,    39,    69,
   665,  1052,    -7,   nil,  1003,  1017,  1030,  1032,   809,  1542,
  4406,   nil,  5649,  5518, 25557,   nil,   nil,   nil,   nil,  1060,
  1166,   650,   nil,   nil,   nil,   nil,   nil,   nil, 25655,  1050,
 25753,  1051,   nil,   nil,  1055,  1057,  1059,  1060,   nil,  1063,
   nil,   555, 27866,   nil,  1193,  5387, 27927, 26994,     6, 26442,
   nil,   nil,   nil,   nil,  1075,   825,  3254,   nil,   nil,   nil,
  1085,   nil,   nil,   nil,  1580,  1088, 25851,   nil,   nil,   nil,
 26148,   nil, 25024,   nil, 26344,   nil, 25952,   nil,   nil,  1158,
  1161,   nil,  1121,    -9,   -10,    68,    -1,  1174,   nil,   324,
 25135,   nil,  1236,  1239,  1335,  1336,     8,   nil,   nil,   nil,
   nil,  1342, 26050,   nil,   nil,   nil,   nil,   732,   nil ]

racc_action_default = [
    -3,  -726,    -1,  -711,    -4,  -726,    -7,  -726,  -726,  -726,
  -726,   -30,  -726,  -726,   -35,   -36,  -726,  -726,  -726,  -307,
   -52,  -713,  -726,   -59,  -726,   -67,   -68,   -69,   -73,  -284,
  -284,  -284,  -320,  -347,  -348,   -85,   -12,   -89,   -97,   -99,
  -726,  -610,  -611,  -726,  -726,  -726,  -726,  -726,  -726,  -237,
  -726,  -713,  -623,  -623,  -255,  -298,  -299,  -300,  -301,  -302,
  -303,  -304,  -305,  -306,  -699,  -309,  -313,  -725,  -689,  -328,
  -330,  -726,  -726,   -61,   -61,  -711,  -726,  -726,  -726,  -349,
  -350,  -352,  -409,  -550,  -551,  -552,  -553,  -574,  -556,  -557,
  -576,  -578,  -561,  -566,  -570,  -572,  -588,  -589,  -590,  -574,
  -592,  -594,  -595,  -596,  -597,  -697,  -599,  -600,  -698,  -602,
  -603,  -604,  -605,  -606,  -607,  -608,  -609,  -614,  -615,  -726,
    -2,  -712,  -721,  -722,  -723,    -6,  -726,  -726,  -726,  -726,
  -726,    -8,    -3,   -18,  -726,  -128,  -129,  -130,  -131,  -132,
  -133,  -134,  -138,  -139,  -140,  -141,  -142,  -143,  -144,  -145,
  -146,  -147,  -148,  -149,  -150,  -151,  -152,  -153,  -154,  -155,
  -156,  -157,  -158,  -159,  -160,  -161,  -162,  -163,  -164,  -165,
  -166,  -167,  -168,  -169,  -170,  -171,  -172,  -173,  -174,  -175,
  -176,  -177,  -178,  -179,  -180,  -181,  -182,  -183,  -184,  -185,
  -186,  -187,  -188,  -189,  -190,  -191,  -192,  -193,  -194,  -195,
  -196,  -197,  -198,  -199,  -200,  -201,  -202,  -203,  -204,  -205,
  -206,  -207,  -208,   -23,  -135,   -12,  -726,  -726,  -726,  -726,
  -726,  -726,  -726,  -274,  -726,  -726,  -709,  -710,  -726,  -726,
  -713,  -714,   -56,  -726,  -610,  -611,  -726,  -307,  -726,  -726,
  -243,  -726,   -12,  -726,   -57,  -220,  -221,  -726,  -726,  -726,
  -726,  -726,  -726,  -726,  -726,  -726,  -726,  -726,  -726,  -726,
  -726,  -726,  -726,  -726,  -726,  -726,  -726,  -256,  -257,  -258,
  -259,  -726,   -63,   -64,  -726,  -128,  -129,  -168,  -169,  -170,
  -186,  -191,  -198,  -201,  -610,  -611,  -687,  -726,  -418,  -420,
  -726,  -707,  -708,   -74,  -274,  -726,  -327,  -424,  -433,  -435,
   -80,  -430,   -81,  -713,   -82,  -262,  -279,  -288,  -288,  -283,
  -726,  -289,  -726,  -574,  -691,  -726,  -726,   -83,   -84,  -711,
   -13,  -726,   -16,  -726,   -87,   -12,  -713,  -726,   -90,   -93,
   -12,  -105,  -106,  -726,  -726,  -113,  -320,  -323,  -713,  -726,
  -623,  -623,  -347,  -348,  -351,  -431,  -726,   -95,  -726,  -101,
  -317,  -726,  -222,  -223,  -593,  -231,  -232,  -726,  -244,  -726,
  -622,   -12,  -645,  -645,  -622,   -12,   -12,  -311,  -713,  -263,
  -718,  -718,  -726,  -726,  -718,  -726,  -329,   -60,   -59,  -726,
  -726,  -726,   -12,   -12,  -711,  -726,  -712,  -114,  -115,  -726,
  -726,  -122,  -726,  -362,  -363,  -123,  -124,  -726,  -126,  -726,
  -307,  -618,  -726,  -343,  -645,  -554,  -726,  -726,  -726,  -726,
  -726,  -726,  -726,  -726,  1219,    -5,  -724,   -24,   -25,   -26,
   -27,   -28,  -726,  -726,   -20,   -21,   -22,  -136,  -726,   -31,
   -34,  -294,  -726,  -726,  -293,   -32,  -726,   -41,  -726,  -307,
   -49,   -51,  -209,  -267,  -289,   -39,   -40,   -53,   -54,   -37,
   -38,   -42,  -210,  -267,  -713,  -275,  -288,  -288,  -700,  -701,
  -284,  -428,  -702,  -703,  -701,  -700,  -284,  -427,  -429,  -702,
  -703,   -48,  -217,   -55,  -713,  -326,  -726,  -726,  -726,  -274,
  -317,  -726,  -726,  -726,  -726,  -218,  -219,  -224,  -225,  -226,
  -227,  -228,  -229,  -233,  -234,  -235,  -236,  -238,  -239,  -240,
  -241,  -242,  -245,  -246,  -247,  -248,  -713,  -260,   -65,  -713,
  -439,  -284,  -700,  -701,   -71,   -75,  -646,  -713,  -288,  -713,
  -285,  -437,  -439,  -713,  -322,  -280,  -726,  -281,  -726,  -286,
  -726,  -290,  -726,  -694,  -696,   -11,  -712,   -15,   -17,  -713,
   -86,  -315,  -102,   -91,  -726,  -713,  -274,  -726,  -726,  -112,
  -726,  -622,  -593,  -726,   -98,  -103,  -726,  -726,  -726,  -726,
  -261,  -249,  -726,  -726,  -542,  -726,  -375,  -376,  -713,  -630,
  -713,  -686,  -686,  -628,  -630,  -630,  -644,  -646,  -647,  -648,
  -649,  -650,  -651,  -652,  -653,  -654,  -726,  -656,  -658,  -660,
  -665,  -667,  -668,  -671,  -676,  -678,  -679,  -681,  -682,  -683,
  -726,  -726,  -630,  -726,  -726,  -726,  -713,  -726,  -264,  -720,
  -719,  -266,  -720,  -318,  -319,  -690,   -12,  -353,  -354,   -12,
  -726,  -726,  -726,  -726,  -726,  -726,  -274,  -726,  -726,  -121,
   -61,  -123,  -124,  -125,  -726,  -726,  -274,  -339,  -616,  -726,
   -12,  -410,  -645,  -413,  -555,  -575,  -580,  -726,  -582,  -558,
  -577,  -726,  -579,  -560,  -726,  -563,  -726,  -565,  -568,  -726,
  -569,  -726,  -591,    -9,   -19,  -726,   -29,  -726,  -297,  -726,
  -726,  -274,  -726,  -726,  -726,  -726,  -432,  -726,  -276,  -278,
  -726,  -726,   -76,  -273,  -425,  -726,  -726,   -78,  -426,  -325,
  -715,  -700,  -701,  -700,  -701,  -713,   -58,  -459,  -461,  -463,
  -466,  -523,  -713,  -478,  -481,  -516,  -521,  -522,  -726,  -726,
  -526,  -527,  -528,  -529,  -530,  -531,  -532,  -533,  -534,  -535,
  -536,  -726,  -726,  -540,  -726,  -726,  -688,  -726,  -440,   -70,
  -421,  -437,  -269,  -276,  -271,  -726,  -399,  -726,  -321,  -288,
  -287,  -291,  -726,  -692,  -693,  -726,   -14,   -88,  -726,   -94,
  -100,  -713,  -108,  -110,  -272,  -109,  -111,  -726,   -96,  -726,
  -216,  -230,  -713,  -251,  -725,  -725,  -345,  -619,  -726,  -637,
  -621,  -726,  -626,  -627,  -726,  -726,  -640,  -726,  -642,  -726,
  -364,  -726,  -366,  -368,  -371,  -374,  -713,  -659,  -669,  -670,
  -680,  -684,  -624,  -726,  -253,  -346,  -310,  -312,  -314,  -718,
  -725,  -355,  -725,   -62,  -356,  -357,  -333,  -334,  -726,  -726,
  -445,  -336,  -726,  -713,  -117,  -119,  -118,  -120,   -12,  -123,
  -124,  -127,  -713,   -12,  -726,  -341,  -726,  -726,  -713,  -581,
  -584,  -585,  -586,  -587,   -12,  -559,  -562,  -564,  -567,  -571,
  -573,  -137,   -33,  -295,  -726,  -713,  -700,  -701,  -701,  -700,
   -50,  -268,  -726,  -716,  -288,   -44,  -212,   -45,  -213,   -77,
   -46,  -215,   -47,  -214,   -79,  -726,  -726,  -726,  -726,  -432,
  -726,  -726,  -464,  -465,  -726,  -726,  -726,  -483,  -713,  -713,
  -477,  -484,  -490,  -726,  -493,  -497,  -726,  -480,  -726,  -726,
  -519,  -520,  -524,  -525,  -537,  -124,  -538,  -726,   -66,  -419,
  -399,  -423,  -422,  -726,  -713,  -434,  -400,  -713,   -12,  -436,
  -282,  -292,  -695,   -92,  -107,  -104,  -324,  -726,  -725,  -360,
   -12,  -543,  -725,  -544,  -545,  -713,  -629,  -630,  -630,  -657,
  -686,  -666,  -671,  -685,  -630,  -630,  -677,  -630,  -654,  -672,
  -713,  -726,  -726,  -373,  -655,  -726,  -265,  -726,  -358,  -726,
  -726,  -335,  -337,  -726,  -726,   -12,  -116,  -726,  -432,  -726,
  -726,   -12,  -344,  -411,  -414,  -416,  -403,  -726,  -726,  -296,
  -432,   -43,  -211,  -277,  -726,  -460,  -462,  -470,  -474,  -713,
  -713,  -713,  -502,  -504,  -505,  -508,  -509,  -574,  -512,  -514,
  -515,  -713,  -713,  -713,  -539,  -475,  -476,  -500,  -485,  -488,
  -491,  -726,  -496,  -713,  -574,  -717,  -713,  -517,  -518,  -250,
   -12,   -72,  -270,  -686,  -686,  -380,  -382,  -382,  -382,  -398,
  -726,  -713,  -654,  -662,  -663,  -674,  -438,  -252,   -10,   -12,
  -549,  -361,  -726,  -726,  -547,  -620,  -726,  -633,  -726,  -635,
  -625,  -726,  -638,  -726,  -641,  -643,  -365,  -367,  -369,  -372,
  -254,  -331,  -726,  -332,  -726,  -450,  -453,  -456,  -457,  -458,
  -291,  -725,  -338,  -340,  -617,  -726,   -12,   -12,  -726,  -412,
  -583,  -467,  -468,  -469,  -503,  -507,  -726,  -511,  -513,  -471,
  -472,  -473,  -492,  -486,  -726,  -494,  -498,  -479,  -726,  -482,
  -441,  -726,  -378,  -379,  -383,  -389,  -391,  -726,  -394,  -726,
  -396,  -401,  -726,  -726,  -661,  -726,  -548,   -12,  -610,  -611,
  -726,  -726,  -307,  -546,  -630,  -630,  -630,  -630,  -726,  -726,
   -12,  -446,  -726,  -726,  -454,  -442,  -443,  -444,  -342,  -726,
  -726,  -713,  -405,  -407,  -408,  -501,  -506,  -510,  -726,  -489,
  -726,  -686,  -664,  -381,  -382,  -382,  -382,  -382,  -675,  -382,
  -402,  -673,  -726,  -317,  -542,  -274,  -726,  -726,  -317,  -726,
  -631,  -634,  -636,  -639,  -370,  -725,   -12,  -451,  -452,  -455,
  -493,  -415,  -417,  -404,  -726,  -487,  -726,  -495,  -499,  -377,
  -726,  -386,  -726,  -388,  -726,  -392,  -726,  -395,  -397,  -316,
  -704,  -541,  -713,  -700,  -701,  -704,  -316,  -630,  -359,  -725,
  -726,  -406,  -382,  -382,  -382,  -382,  -432,  -632,  -447,  -448,
  -449,  -494,  -726,  -384,  -387,  -390,  -393,  -382,  -385 ]

clist = [
'42,224,229,289,272,42,306,306,306,406,291,328,138,138,376,624,764,297',
'301,124,273,413,369,809,285,585,585,743,138,565,633,454,359,604,605',
'527,42,332,332,519,141,141,947,452,382,383,460,466,930,472,676,878,232',
'344,344,133,214,131,121,879,340,340,434,435,327,347,42,585,735,6,354',
'379,380,515,6,384,388,285,285,1017,120,616,619,772,773,887,326,918,307',
'307,307,124,344,344,344,780,554,324,17,340,340,340,517,17,365,341,341',
'570,1085,608,611,743,511,615,568,601,377,377,576,576,377,42,786,370',
'908,950,417,418,419,420,42,290,42,451,17,367,1047,394,936,471,922,308',
'308,308,341,341,341,1049,967,696,929,1028,385,2,892,893,643,1148,360',
'364,576,304,317,318,17,1132,746,293,300,302,1134,377,377,377,377,929',
'371,374,743,1170,932,125,981,993,527,927,740,213,740,430,6,1139,423',
'441,1003,1177,727,306,441,421,484,6,654,656,441,401,403,803,737,274',
'927,725,461,731,665,42,477,357,388,17,372,388,730,979,991,286,373,1020',
'17,470,17,980,992,392,344,532,637,344,366,606,1148,340,42,428,340,1175',
'429,440,368,823,961,640,440,808,1048,1019,928,291,440,1095,1021,1059',
'1131,934,447,448,1144,821,306,306,327,1201,433,433,473,474,1134,306',
'903,446,641,928,450,988,988,422,341,695,1010,341,780,869,749,963,943',
'988,404,415,880,460,466,758,472,827,452,585,1066,1211,826,1067,1057',
'510,521,17,1047,17,547,940,522,550,17,42,555,1164,900,42,17,297,1125',
'332,42,1054,509,301,557,124,1007,1008,740,740,17,1102,1208,953,1166',
'344,1055,914,332,607,518,307,340,327,751,896,543,1058,327,307,975,42',
'988,976,344,42,42,877,1141,754,552,340,627,538,536,539,874,760,508,754',
'545,620,42,42,1126,1087,324,875,886,889,537,324,365,740,828,1018,535',
'341,576,936,124,1135,1136,1,308,1032,405,1198,407,956,621,622,308,408',
'409,410,341,411,958,17,412,672,829,17,520,929,377,138,17,834,824,585',
'523,931,813,677,998,1142,585,776,778,970,1040,,822,,,639,683,754,780',
'780,,306,688,141,585,754,,17,,623,859,17,17,461,910,864,,978,664,,743',
'1169,995,996,929,,904,845,17,17,470,988,718,,,724,,,,959,,,460,466,913',
',,683,932,856,858,,,,861,863,,994,,,,925,955,,,,555,,306,,1209,,1114',
'1092,1093,555,,933,,,933,,,461,42,,946,877,877,,,,332,461,,,,,,,470',
'332,1006,,1086,,,,344,,470,,,,,340,929,344,1022,,,,,,340,,,,,910,1145',
'569,,1146,,1079,1080,1081,,855,857,,,,860,862,682,1210,306,818,,,687',
',1191,762,,,306,923,341,,,461,42,792,1056,42,1023,341,,461,,,,780,780',
'1065,,,470,740,17,739,804,,470,1086,42,,,,800,306,441,802,,729,1075',
',,1179,,825,441,441,,,461,441,441,,,1015,1014,42,1202,138,,,,972,42',
'902,677,470,1090,,,812,,677,888,,856,858,863,861,,1127,,1086,,1178,',
'141,,585,440,,718,,,272,,901,718,718,440,440,841,,17,440,440,17,799',
',924,898,555,,,,,,,138,,1178,677,915,1107,1130,842,,17,,,,683,850,,688',
',,332,,650,652,655,655,1120,1086,1121,1022,,332,1022,,,971,344,17,,',
'17,,,340,17,,,344,,,17,17,,,340,17,17,,,854,1206,877,,,1037,1039,677',
'972,,,,1042,1044,,1045,677,1023,,,1023,,966,1023,,1023,,,,341,,42,,',
'433,,42,960,,677,341,441,,13,,,,42,13,,,,,,,854,1022,,,957,,,933,,,1015',
'1014,677,1015,1014,1015,1014,677,677,,968,,,,13,,,377,,718,,1030,718',
'718,,1034,,,713,718,440,,,,,,966,718,718,718,,1023,,1023,13,1023,,1023',
',,,,,,393,,42,1076,,,,,,,17,,,,42,17,,,,1023,1088,,,,,,17,,,,1026,,1015',
'1014,1015,1014,1015,1014,1015,1014,,,1031,1052,17,13,,718,712,42,,,',
',13,42,13,1192,,,,,,,1015,1014,677,677,677,,1064,,754,1160,1161,1162',
'1163,1061,888,718,,,,,,,,,377,,935,837,937,837,,718,966,,18,,17,,,18',
'42,,,,,,,,17,,,,,285,,,233,,,42,,,,1109,,,233,233,233,344,18,333,333',
',,13,340,436,445,344,,449,436,17,716,,340,1106,436,17,,,1207,,,,42,42',
',,18,,13,,,233,233,,,233,389,399,399,,718,,,,,,,341,,1156,1129,,,,713',
',,341,,285,713,713,,42,17,,,,1173,,,,,,,344,42,,1167,1168,718,18,340',
'17,1016,306,233,233,233,233,18,1154,18,,718,,718,,,,461,,,,1165,,,13',
',,,13,470,,,,13,470,,,17,17,42,377,377,475,,341,712,677,,,718,,712,712',
',,,,,,,,,,,13,,,1199,13,13,,,,718,,,,,17,,,,,,,13,13,,,,,17,18,233,438',
'389,233,233,389,438,233,,,,,438,233,233,,,,,,,,524,,,,18,,,,,,,,,,,1115',
',1116,,,1117,,17,540,,713,,,713,713,716,,,,,713,716,716,,,,,,713,713',
'713,1096,1098,1100,233,,,,,,,233,233,,,,,,,,233,710,,,,,,,,,,1147,,1149',
',,,,18,,,,18,,,,333,18,,,,,,,712,,,712,712,,,,713,,712,333,,,,,,19,712',
'712,712,,19,,18,13,,,18,18,,,,,,,,,,1197,713,,,,233,18,18,,,,,,,19,335',
'335,713,,,,1203,,1204,,1205,233,,,,,,,689,,,1181,1183,1185,1187,,1188',
',712,19,,,,,,,,,1217,391,400,400,,,,,716,,,716,716,13,,726,13,,716,',
',,712,732,,734,716,716,716,738,,,,,,,,13,712,,,1213,1214,1215,1216,747',
'19,713,,,233,750,,,,19,1218,19,,,,,,,13,,,436,,,,13,,,767,,770,436,436',
',,,436,436,,,,713,,,716,,,710,,,,,,710,710,713,,713,,,,18,,,,,797,,',
'333,,233,,,,712,716,,333,,,,,,,,,233,,,,,716,713,19,,439,391,,,391,439',
',,,,,439,,719,,,,,,712,,713,,,,19,,,,,,,,712,,712,,,,,,,,,,18,,,18,',
',,,,,233,,,,,,,,,,233,,,,18,,712,,13,,,,,13,716,,,,,,,,,,13,,,,,,712',
'18,,,438,233,,,18,19,,,436,19,438,438,,335,19,438,438,,,,716,710,,,710',
'710,,916,,,,710,335,,716,,716,,,710,710,710,,,,19,,,,19,19,944,,,,,',
',,,,,,,13,,19,19,,,,,716,,,,13,,333,,,,,,,,,,,333,,,,,,,716,,,,,,,710',
',,,,,,,13,,,,,,13,,711,,,,,,,,,,,,,719,,,710,,977,719,719,,,,,,,,,18',
',,710,,18,233,,,,,,715,,,,18,,13,,,1012,,,,,,,,,,,,,438,,,13,,,,1113',
'1035,,,,,,,,,,,,,,,1046,,,,,,,,,,,,,,,19,,13,13,,,,,335,,,,,,710,,,335',
'18,,,,,,1071,1072,1073,,,,18,,,,,,,,,,,29,,13,,,29,,,,,1089,,,710,,13',
',,233,,,,29,,18,,,710,,710,18,,29,29,29,,29,,,19,719,,19,719,719,,,',
',,719,,,,,,,,719,719,719,13,,19,,710,29,,,,833,29,29,,,29,711,,,18,',
',711,711,,,,,,710,19,,1104,439,,,,19,18,,,,1110,439,439,,,,439,439,',
',,,,,,715,,,,29,719,715,715,,29,29,29,29,29,,29,,,18,18,,,,,,,,,,,,',
',,,,,719,,,,,,,,,,,,,,,,719,335,1151,,717,,18,,,,,,335,,,,,,,18,,233',
'233,,,,,,,,,,,,311,311,311,,,,,,,,,29,29,29,,29,29,,29,29,233,,,,29',
'29,29,,,,,18,,,,311,375,19,29,,,711,19,,711,711,,,,719,,711,,19,,,,',
',711,711,711,,,,,,,,,,439,41,,,,,41,,,,,715,29,,715,715,,,719,29,29',
'715,,,,284,,,29,715,715,715,719,,719,,,41,331,331,,,,,,,29,,,,29,,711',
',,29,19,,,,,,,,,,,41,19,,719,,,714,,,,387,284,284,,,,,711,,29,,,,29',
'29,,715,719,,,,,,711,,,19,,,29,29,29,19,,,,431,444,,,717,,,311,,41,717',
'717,715,,29,,,,41,,41,,,,,,,,715,,,,,,,,,,,,,,,,,19,,,,,,,,,,,,,400',
',,,,,19,,,,1112,,711,,,,,,,,311,311,,,,,,,,311,,,,29,,,529,,531,,,533',
'534,19,19,41,,,387,,,387,711,715,,,,,,,,,,,,,711,,711,,,,41,,,,,,,,400',
',,,19,,,,,,29,,,715,,,,19,,,29,,,,711,,,715,,715,,,,,,717,29,,717,717',
'714,,,,,717,714,714,711,,,,,717,717,717,,,,,,,,19,,715,,,,,,41,,,,41',
',,668,331,41,,,,,,,,,715,29,,,29,,,,,331,,29,,,,,,,,,,29,41,,,29,41',
'41,717,,,,,,,,311,,,,,,,41,41,,,,,,,,29,,,29,29,,,29,717,,,,,29,29,',
',,29,29,,,,,717,,,,,,,741,,375,,744,,,,,,,,,,,,,,,,311,,,,,,,,,,,,,',
',,,714,,,714,714,,,,,,714,,,,,,,,714,714,714,,,,,,787,,,,,,,,,,,,,717',
',,,,,,741,,,375,,,,,,,,,,,,,444,311,,,,,,,,,,311,,,,41,,717,,,714,29',
',331,,,29,29,,,,717,331,717,,,,29,,,,,,,843,,311,,,,,,714,741,375,29',
'32,,,,,32,,,,,,,714,717,,,,,,,,,32,,,,,,,,,,32,32,32,,32,717,,41,,,41',
',,,,,,,,,741,,,29,,,,,,911,,41,912,,32,29,,,,32,32,,,32,,,,,,,,,921',
',,,,,41,,,,,714,29,41,939,,,,29,,,,,,29,,,,,,,,,,,,,32,,,,,32,32,32',
'32,32,,32,,,714,,,,,,,,,,,,,,714,,714,,,,,,29,,,,,,969,,,,,,,,331,,',
',,29,,,,,,331,,,,,714,,,,,,,,,,,,,,,,,,,,,,,,714,,29,29,32,32,32,,32',
'32,,32,32,,,,,32,32,32,,,,,,,,,,,41,32,,,,41,,,,,,,,29,,,41,,,,,,,,',
',29,,29,29,,,1060,,,,,,,,,,,,,,32,,,,,375,,32,32,,,,,,,29,32,,,,,,,',
',,29,,,,,,,,32,,,,32,,,,,32,41,,,,,,,,,,,,41,,,,,,,,,,,,,,,,,,32,,,',
'32,32,,,,,,,,,,,,41,,,32,32,32,41,,,,,,,,,,,,,,,,,,32,,,,,,,,,,,,,,',
',,,,,,,,,,,,,,,,41,,,,,,,,,,,,,284,,,,,,41,,,,1108,,,,,,,,,,,,,,,,,',
'311,,,,,32,,,,,,,,,,41,41,,,,,,,,,,,,,,,,,,,,,,,,,,23,,,,,23,,,,,284',
',,,41,,,,,,32,,240,,,,,41,,,32,,,,,,23,,,,,,,,352,353,32,355,356,,358',
',,,,,,,,,,,,,,,23,,,,,378,378,41,,378,,,,,,,,,,,,,,,,,,,,,,,,,32,,,32',
',,,,,,32,,,,,,,,,,32,23,,,32,,378,378,378,378,23,,23,,,,,,,,,,,,,,,',
',,,32,,,32,32,,,32,,,,,,32,32,,,,32,32,,,,,,,,,,,,,,,,,,,,,,,,,,,,,',
',,,,,,,,,,,,,,,23,,443,,378,378,,453,,,,,,453,378,378,,,,,,,,,,,240',
'23,,,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500',
'501,502,503,504,505,506,,,,,507,,,,,,,,,,,,,,,,378,,,,,,,,,,,,,,,32',
',,,,32,32,,,,,,,,,,32,,,23,,,,23,,,,,23,,,,,,32,,,,,,,,,,,,,,,,,,,,',
'560,,561,,23,,,,23,23,,,,,,,,,,,,,,,378,23,23,,,,,,,,,32,,,,,,,,,,378',
',32,,,,,,,,,,,,,,,,,,,,,,,,,,,,,32,,,,,,32,,,,,,32,,,,,,,,,,,,,,,,,',
',,,,,,,,,,,,,,,,,,453,453,453,,,,,,,,,,,32,,,,,,,,,,,,,,,,,,,32,,,353',
',,,,,,,,,,,,,,,,,,,23,,,,,,,,,,,,,,32,32,,,,,453,,,761,,,378,763,,,',
',,,,,,,,,,,,,,,,,,,,,,,,32,,,,,,,,,,,,794,32,,32,32,,,,,,,,,23,,,23',
',,,,,,,,,,,,,,,,,,,32,23,,,,,,,,,,32,,,,,,,,,,,,,,,,,23,,,453,,,,23',
'851,,,,,453,453,,,,453,453,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,',
',,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,',
',,,,,,,,,,,,,,,,,,,,,,23,,,,,23,378,,,,,,,,,,23,,,,,,,,,,,,,,,,,,453',
',,,,,,,,,,,,453,453,453,453,,,,,,,,,,,,,,,,,,,,,,,,,,,,,1009,,,,,,,',
',,,23,,,,,,,,,1027,,,23,,,,,,,,,,,,,,,,,,,,,,,,,1050,,,,378,,,,,,23',
',,,,,23,,,,,,,,,,,,,453,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,23,,,,,,',
',,,,,,,,,,,,23,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,23,23,,,,,,,,,,,',
',,,,,,,,,,,,,,,,,,,,,,,,,,,,23,,,,,,,,,,,,,23,,378,378,,,,,,,,,,,,,',
',,,,,,,,,,,,,,,,,,,,,,,,,,,,,23' ]
        racc_goto_table = arr = ::Array.new(4471, nil)
        idx = 0
        clist.each do |str|
          str.split(',', -1).each do |i|
            arr[idx] = i.to_i unless i.empty?
            idx += 1
          end
        end

clist = [
'70,34,36,150,44,70,50,50,50,190,34,66,73,73,104,109,11,88,88,113,42',
'190,84,110,70,219,219,228,73,9,71,32,36,9,9,91,70,70,70,89,75,75,108',
'77,40,40,53,53,127,77,33,167,21,81,81,16,16,8,6,168,45,45,23,23,10,67',
'70,219,56,7,78,18,18,55,7,18,70,70,70,137,4,107,107,131,131,179,63,12',
'85,85,85,113,81,81,81,122,68,62,30,45,45,45,32,30,114,46,46,89,184,86',
'86,228,53,86,149,149,26,26,214,214,26,70,123,85,139,12,18,18,18,18,70',
'48,70,29,30,7,121,20,224,29,196,87,87,87,46,46,46,124,141,38,220,13',
'4,2,194,194,149,225,80,80,214,61,61,61,30,143,15,54,54,54,144,26,26',
'26,26,220,87,87,228,185,192,5,170,170,91,218,92,17,92,24,7,184,27,19',
'170,185,153,50,19,7,39,7,204,204,19,72,72,41,153,47,218,49,88,58,76',
'70,34,82,70,30,83,70,90,167,167,102,103,141,30,73,30,168,168,111,81',
'190,112,81,115,116,225,45,70,10,45,184,21,21,117,118,119,120,21,109',
'125,129,138,34,21,134,135,170,142,138,26,26,137,71,50,50,10,143,85,85',
'26,26,144,50,56,20,145,138,20,192,192,2,46,32,139,46,122,33,68,146,122',
'192,147,5,33,53,53,68,77,148,77,219,151,184,9,152,168,154,156,30,121',
'30,34,123,157,36,30,70,66,124,160,70,30,88,161,70,70,162,26,88,34,113',
'194,194,92,92,30,141,163,164,165,81,166,33,70,36,85,85,45,10,32,71,67',
'169,10,85,172,70,192,174,81,70,70,38,127,53,78,45,34,8,6,63,175,77,48',
'53,63,18,70,70,12,179,62,176,180,181,7,62,114,92,149,138,4,46,214,224',
'113,187,188,1,87,197,200,108,201,33,10,10,87,202,203,206,46,207,33,30',
'208,34,209,30,61,220,26,73,30,210,213,219,61,222,32,36,183,223,219,217',
'217,33,131,,32,,,26,88,53,122,122,,50,88,75,219,53,,30,,4,55,30,30,88',
'91,55,,33,16,,228,167,33,33,220,,89,32,30,30,73,192,70,,,36,,,,9,,,53',
'53,68,,,88,192,77,77,,,,77,77,,71,,,,89,107,,,,66,,50,,12,,138,131,131',
'66,,132,,,132,,,88,70,,86,38,38,,,,70,88,,,,,,,73,70,38,,183,,,,81,',
'73,,,,,45,220,81,219,,,,,,45,,,,,91,138,136,,138,,33,33,33,,29,29,,',
',29,29,54,110,50,40,,,54,,11,26,,,50,23,46,,,88,70,113,38,70,221,46',
',88,,,,122,122,9,,,73,92,30,87,113,,73,183,70,,,,10,50,19,10,,54,38',
',,131,,113,19,19,,,88,19,19,,,132,126,70,138,73,,,,77,70,104,36,73,9',
',,85,,36,36,,77,77,77,77,,109,,183,,183,,75,,219,21,,70,,,44,,150,70',
'70,21,21,16,,30,21,21,30,87,,84,42,66,,,,,,,73,,183,36,66,107,9,7,,30',
',,,88,7,,88,,,70,,205,205,205,205,107,183,107,219,,70,219,,,29,81,30',
',,30,,,45,30,,,81,,,30,30,,,45,30,30,,,87,33,38,,,217,217,36,77,,,,217',
'217,,217,36,221,,,221,,36,221,,221,,,,46,,70,,,85,,70,18,,36,46,19,',
'22,,,,70,22,,,,,,,87,219,,,10,,,132,,,132,126,36,132,126,132,126,36',
'36,,10,,,,22,,,26,,70,,84,70,70,,84,,,97,70,21,,,,,,36,70,70,70,,221',
',221,22,221,,221,,,,,,,22,,70,190,,,,,,,30,,,,70,30,,,,221,190,,,,,',
'30,,,,10,,132,126,132,126,132,126,132,126,,,10,18,30,22,,70,96,70,,',
',,22,70,22,32,,,,,,,132,126,36,36,36,,113,,53,217,217,217,217,10,36',
'70,,,,,,,,,26,,136,205,136,205,,70,36,,31,,30,,,31,70,,,,,,,,30,,,,',
'70,,,31,,,70,,,,70,,,31,31,31,81,31,31,31,,,22,45,22,22,81,,22,22,30',
'100,,45,10,22,30,,,217,,,,70,70,,,31,,22,,,31,31,,,31,31,31,31,,70,',
',,,,,46,,34,10,,,,97,,,46,,70,97,97,,70,30,,,,36,,,,,,,81,70,,18,18',
'70,31,45,30,136,50,31,31,31,31,31,10,31,,70,,70,,,,88,,,,10,,,22,,,',
'22,73,,,,22,73,,,30,30,70,26,26,64,,46,96,36,,,70,,96,96,,,,,,,,,,,',
'22,,,10,22,22,,,,70,,,,,30,,,,,,,22,22,,,,,30,31,31,31,31,31,31,31,31',
'31,,,,,31,31,31,,,,,,,,64,,,,31,,,,,,,,,,,136,,136,,,136,,30,64,,97',
',,97,97,100,,,,,97,100,100,,,,,,97,97,97,133,133,133,31,,,,,,,31,31',
',,,,,,,31,94,,,,,,,,,,136,,136,,,,,31,,,,31,,,,31,31,,,,,,,96,,,96,96',
',,,97,,96,31,,,,,,35,96,96,96,,35,,31,22,,,31,31,,,,,,,,,,136,97,,,',
'31,31,31,,,,,,,35,35,35,97,,,,136,,136,,136,31,,,,,,,64,,,133,133,133',
'133,,133,,96,35,,,,,,,,,136,35,35,35,,,,,100,,,100,100,22,,64,22,,100',
',,,96,64,,64,100,100,100,64,,,,,,,,22,96,,,133,133,133,133,64,35,97',
',,31,64,,,,35,133,35,,,,,,,22,,,22,,,,22,,,64,,64,22,22,,,,22,22,,,',
'97,,,100,,,94,,,,,,94,94,97,,97,,,,31,,,,,64,,,31,,31,,,,96,100,,31',
',,,,,,,,31,,,,,100,97,35,,35,35,,,35,35,,,,,,35,,106,,,,,,96,,97,,,',
'35,,,,,,,,96,,96,,,,,,,,,,31,,,31,,,,,,,31,,,,,,,,,,31,,,,31,,96,,22',
',,,,22,100,,,,,,,,,,22,,,,,,96,31,,,31,31,,,31,35,,,22,35,31,31,,35',
'35,31,31,,,,100,94,,,94,94,,64,,,,94,35,,100,,100,,,94,94,94,,,,35,',
',,35,35,64,,,,,,,,,,,,,22,,35,35,,,,,100,,,,22,,31,,,,,,,,,,,31,,,,',
',,100,,,,,,,94,,,,,,,,22,,,,,,22,,95,,,,,,,,,,,,,106,,,94,,64,106,106',
',,,,,,,,31,,,94,,31,31,,,,,,99,,,,31,,22,,,64,,,,,,,,,,,,,31,,,22,,',
',22,64,,,,,,,,,,,,,,,64,,,,,,,,,,,,,,,35,,22,22,,,,,35,,,,,,94,,,35',
'31,,,,,,64,64,64,,,,31,,,,,,,,,,,57,,22,,,57,,,,,64,,,94,,22,,,31,,',
',57,,31,,,94,,94,31,,57,57,57,,57,,,35,106,,35,106,106,,,,,,106,,,,',
',,,106,106,106,22,,35,,94,57,,,,35,57,57,,,57,95,,,31,,,95,95,,,,,,94',
'35,,31,35,,,,35,31,,,,31,35,35,,,,35,35,,,,,,,,99,,,,57,106,99,99,,57',
'57,57,57,57,,57,,,31,31,,,,,,,,,,,,,,,,,,106,,,,,,,,,,,,,,,,106,35,31',
',101,,31,,,,,,35,,,,,,,31,,31,31,,,,,,,,,,,,28,28,28,,,,,,,,,57,57,57',
',57,57,,57,57,31,,,,57,57,57,,,,,31,,,,28,28,35,57,,,95,35,,95,95,,',
',106,,95,,35,,,,,,95,95,95,,,,,,,,,,35,69,,,,,69,,,,,99,57,,99,99,,',
'106,57,57,99,,,,69,,,57,99,99,99,106,,106,,,69,69,69,,,,,,,57,,,,57',
',95,,,57,35,,,,,,,,,,,69,35,,106,,,98,,,,69,69,69,,,,,95,,57,,,,57,57',
',99,106,,,,,,95,,,35,,,57,57,57,35,,,,28,28,,,101,,,28,,69,101,101,99',
',57,,,,69,,69,,,,,,,,99,,,,,,,,,,,,,,,,,35,,,,,,,,,,,,,35,,,,,,35,,',
',35,,95,,,,,,,,28,28,,,,,,,,28,,,,57,,,28,,28,,,28,28,35,35,69,,,69',
',,69,95,99,,,,,,,,,,,,,95,,95,,,,69,,,,,,,,35,,,,35,,,,,,57,,,99,,,',
'35,,,57,,,,95,,,99,,99,,,,,,101,57,,101,101,98,,,,,101,98,98,95,,,,',
'101,101,101,,,,,,,,35,,99,,,,,,69,,,,69,,,28,69,69,,,,,,,,,99,57,,,57',
',,,,69,,57,,,,,,,,,,57,69,,,57,69,69,101,,,,,,,,28,,,,,,,69,69,,,,,',
',,57,,,57,57,,,57,101,,,,,57,57,,,,57,57,,,,,101,,,,,,,28,,28,,28,,',
',,,,,,,,,,,,,28,,,,,,,,,,,,,,,,,98,,,98,98,,,,,,98,,,,,,,,98,98,98,',
',,,,28,,,,,,,,,,,,,101,,,,,,,28,,,28,,,,,,,,,,,,,28,28,,,,,,,,,,28,',
',,69,,101,,,98,57,,69,,,57,57,,,,101,69,101,,,,57,,,,,,,28,,28,,,,,',
'98,28,28,57,60,,,,,60,,,,,,,98,101,,,,,,,,,60,,,,,,,,,,60,60,60,,60',
'101,,69,,,69,,,,,,,,,,28,,,57,,,,,,28,,69,28,,60,57,,,,60,60,,,60,,',
',,,,,,28,,,,,,69,,,,,98,57,69,28,,,,57,,,,,,57,,,,,,,,,,,,,60,,,,,60',
'60,60,60,60,,60,,,98,,,,,,,,,,,,,,98,,98,,,,,,57,,,,,,28,,,,,,,,69,',
',,,57,,,,,,69,,,,,98,,,,,,,,,,,,,,,,,,,,,,,,98,,57,57,60,60,60,,60,60',
',60,60,,,,,60,60,60,,,,,,,,,,,69,60,,,,69,,,,,,,,57,,,69,,,,,,,,,,57',
',57,57,,,28,,,,,,,,,,,,,,60,,,,,28,,60,60,,,,,,,57,60,,,,,,,,,,57,,',
',,,,,60,,,,60,,,,,60,69,,,,,,,,,,,,69,,,,,,,,,,,,,,,,,,60,,,,60,60,',
',,,,,,,,,,69,,,60,60,60,69,,,,,,,,,,,,,,,,,,60,,,,,,,,,,,,,,,,,,,,,',
',,,,,,,,,69,,,,,,,,,,,,,69,,,,,,69,,,,69,,,,,,,,,,,,,,,,,,28,,,,,60',
',,,,,,,,,69,69,,,,,,,,,,,,,,,,,,,,,,,,,,37,,,,,37,,,,,69,,,,69,,,,,',
'60,,37,,,,,69,,,60,,,,,,37,,,,,,,,37,37,60,37,37,,37,,,,,,,,,,,,,,,',
'37,,,,,37,37,69,,37,,,,,,,,,,,,,,,,,,,,,,,,,60,,,60,,,,,,,60,,,,,,,',
',,60,37,,,60,,37,37,37,37,37,,37,,,,,,,,,,,,,,,,,,,60,,,60,60,,,60,',
',,,,60,60,,,,60,60,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37,,37',
',37,37,,37,,,,,,37,37,37,,,,,,,,,,,37,37,,,37,37,37,37,37,37,37,37,37',
'37,37,37,37,37,37,37,37,37,37,37,37,37,,,,,37,,,,,,,,,,,,,,,,37,,,,',
',,,,,,,,,,60,,,,,60,60,,,,,,,,,,60,,,37,,,,37,,,,,37,,,,,,60,,,,,,,',
',,,,,,,,,,,,,37,,37,,37,,,,37,37,,,,,,,,,,,,,,,37,37,37,,,,,,,,,60,',
',,,,,,,,37,,60,,,,,,,,,,,,,,,,,,,,,,,,,,,,,60,,,,,,60,,,,,,60,,,,,,',
',,,,,,,,,,,,,,,,,,,,,,,,,,,,,37,37,37,,,,,,,,,,,60,,,,,,,,,,,,,,,,,',
',60,,,37,,,,,,,,,,,,,,,,,,,,37,,,,,,,,,,,,,,60,60,,,,,37,,,37,,,37,37',
',,,,,,,,,,,,,,,,,,,,,,,,,,,60,,,,,,,,,,,,37,60,,60,60,,,,,,,,,37,,,37',
',,,,,,,,,,,,,,,,,,,60,37,,,,,,,,,,60,,,,,,,,,,,,,,,,,37,,,37,,,,37,37',
',,,,37,37,,,,37,37,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,',
',,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,',
',,,,,,,,,,,,,,37,,,,,37,37,,,,,,,,,,37,,,,,,,,,,,,,,,,,,37,,,,,,,,,',
',,,37,37,37,37,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37,,,,,,,,,,,37,,,,,,,,,37',
',,37,,,,,,,,,,,,,,,,,,,,,,,,,37,,,,37,,,,,,37,,,,,,37,,,,,,,,,,,,,37',
',,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37,,,,,,,,,,,,,,,,,,,37,,,,,,,,,',
',,,,,,,,,,,,,,,,,,,,,,,,,,,37,37,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,',
',,,,37,,,,,,,,,,,,,37,,37,37,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,',
',,,37' ]
        racc_goto_check = arr = ::Array.new(4471, nil)
        idx = 0
        clist.each do |str|
          str.split(',', -1).each do |i|
            arr[idx] = i.to_i unless i.empty?
            idx += 1
          end
        end

racc_goto_pointer = [
   nil,   403,   153,   nil,    77,   176,    55,    69,    50,  -332,
    28,  -548,  -677,  -767,   nil,  -370,    47,   178,     0,   -24,
    61,    30,   830,  -154,   -27,   nil,    45,    59,  2107,   -89,
    98,  1004,  -192,  -404,   -17,  1358,   -19,  3304,  -335,   -44,
   -29,  -413,    -4,   nil,   -20,    23,    68,   185,   104,  -297,
   -26,   nil,   nil,  -178,   138,  -220,  -453,  1935,  -301,   nil,
  2788,   129,    61,    50,   939,   nil,   -26,    27,  -252,  2211,
     0,  -367,   128,     4,   nil,    32,  -213,  -179,    24,   nil,
   106,    16,   168,   153,   -45,    56,  -261,   109,   -12,  -255,
  -289,  -273,  -340,   nil,   824,  1309,   470,   396,  1799,  1350,
   570,  1625,   201,   158,   -55,   nil,  1104,  -298,  -758,  -370,
  -600,   157,  -165,    16,    51,   173,  -128,   182,  -388,  -575,
  -152,  -805,  -491,  -464,  -795,  -687,  -241,  -723,   nil,  -652,
   nil,  -488,  -242,   272,  -757,  -647,   221,  -828,  -512,  -612,
   nil,  -680,  -806,  -903,  -898,  -124,  -534,   214,  -338,  -248,
   -22,  -658,  -656,  -314,    23,   nil,    14,    19,   nil,   nil,
  -404,  -733,  -622,  -857,  -467,  -777,  -607,  -651,  -643,  -596,
  -692,   nil,  -510,   nil,  -508,  -325,  -314,   nil,   nil,  -618,
  -315,  -315,   nil,  -445,  -893,  -945,   nil,  -673,  -672,   nil,
   -78,   nil,  -591,   nil,  -554,   nil,  -625,  -517,   nil,   nil,
   321,   318,   322,   322,  -207,   343,   322,   323,   325,  -225,
  -219,   nil,   nil,  -208,  -244,   nil,   nil,  -135,  -583,  -337,
  -618,  -287,  -338,  -654,  -637,  -940,   nil,   nil,  -501 ]

racc_goto_default = [
   nil,   nil,   nil,     3,   nil,     4,   386,   322,   nil,   nil,
   564,   nil,   948,   nil,   319,   320,   nil,   nil,   nil,    11,
    12,    20,   238,   nil,   nil,    14,    15,   nil,    16,   437,
   239,   351,   nil,   nil,   635,   237,   476,   305,   997,   nil,
   nil,   nil,   nil,   381,   140,    52,    53,   nil,   nil,   nil,
    25,    26,    27,   757,   nil,   nil,   nil,   339,   nil,    28,
   336,   455,    35,   nil,   nil,    37,    40,    39,   nil,   234,
   235,   398,   nil,   463,   139,    84,   nil,   442,   100,    49,
   551,    54,   271,   nil,   919,   456,   nil,   457,   468,   nil,
   684,   525,   309,   295,    55,    56,    57,    58,    59,    60,
    61,    62,    63,   nil,   296,    69,    70,   nil,   nil,   nil,
   nil,   nil,   nil,   617,   361,   nil,   nil,   nil,   nil,   nil,
   nil,   782,   584,   nil,   783,   784,   572,   566,   567,  1143,
  1013,   nil,   573,   nil,   nil,   nil,   602,   nil,   575,   nil,
   906,   nil,   nil,   nil,   582,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   467,   nil,   nil,   736,   728,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   882,
   nil,   697,   706,   698,   699,   nil,   nil,   700,   701,   nil,
   nil,   nil,   881,   883,   nil,   884,   982,   983,   984,   985,
   nil,   989,   593,   990,   705,   707,   nil,   nil,    83,    85,
    86,   nil,   nil,   nil,   nil,   645,   nil,   nil,   nil,   nil,
   nil,    96,    97,   nil,   926,   363,   571,   769,   574,   938,
   587,   589,   590,  1024,   594,  1025,   597,   600,   314 ]

racc_reduce_table = [
  0, 0, :racc_error,
  1, 148, :_reduce_none,
  2, 149, :_reduce_2,
  0, 150, :_reduce_3,
  1, 150, :_reduce_4,
  3, 150, :_reduce_5,
  2, 150, :_reduce_6,
  1, 152, :_reduce_none,
  2, 152, :_reduce_8,
  3, 155, :_reduce_9,
  4, 156, :_reduce_10,
  2, 157, :_reduce_11,
  0, 161, :_reduce_12,
  1, 161, :_reduce_13,
  3, 161, :_reduce_14,
  2, 161, :_reduce_15,
  1, 162, :_reduce_none,
  2, 162, :_reduce_17,
  0, 174, :_reduce_18,
  4, 154, :_reduce_19,
  3, 154, :_reduce_20,
  3, 154, :_reduce_21,
  3, 154, :_reduce_22,
  2, 154, :_reduce_23,
  3, 154, :_reduce_24,
  3, 154, :_reduce_25,
  3, 154, :_reduce_26,
  3, 154, :_reduce_27,
  3, 154, :_reduce_28,
  4, 154, :_reduce_29,
  1, 154, :_reduce_none,
  3, 154, :_reduce_31,
  3, 154, :_reduce_32,
  5, 154, :_reduce_33,
  3, 154, :_reduce_34,
  1, 154, :_reduce_none,
  1, 154, :_reduce_none,
  3, 172, :_reduce_37,
  3, 172, :_reduce_38,
  3, 172, :_reduce_39,
  3, 172, :_reduce_40,
  3, 166, :_reduce_41,
  3, 166, :_reduce_42,
  6, 166, :_reduce_43,
  5, 166, :_reduce_44,
  5, 166, :_reduce_45,
  5, 166, :_reduce_46,
  5, 166, :_reduce_47,
  3, 166, :_reduce_48,
  1, 176, :_reduce_none,
  3, 176, :_reduce_50,
  1, 176, :_reduce_none,
  1, 173, :_reduce_none,
  3, 173, :_reduce_53,
  3, 173, :_reduce_54,
  3, 173, :_reduce_55,
  2, 173, :_reduce_56,
  0, 186, :_reduce_57,
  4, 173, :_reduce_58,
  1, 173, :_reduce_none,
  1, 165, :_reduce_none,
  0, 190, :_reduce_61,
  3, 187, :_reduce_62,
  1, 189, :_reduce_63,
  2, 192, :_reduce_64,
  0, 196, :_reduce_65,
  5, 193, :_reduce_66,
  1, 168, :_reduce_none,
  1, 168, :_reduce_none,
  1, 198, :_reduce_none,
  4, 198, :_reduce_70,
  0, 205, :_reduce_71,
  4, 202, :_reduce_72,
  1, 204, :_reduce_none,
  2, 197, :_reduce_74,
  3, 197, :_reduce_75,
  4, 197, :_reduce_76,
  5, 197, :_reduce_77,
  4, 197, :_reduce_78,
  5, 197, :_reduce_79,
  2, 197, :_reduce_80,
  2, 197, :_reduce_81,
  2, 197, :_reduce_82,
  2, 197, :_reduce_83,
  2, 197, :_reduce_84,
  1, 167, :_reduce_85,
  3, 167, :_reduce_86,
  1, 210, :_reduce_87,
  3, 210, :_reduce_88,
  1, 209, :_reduce_none,
  2, 209, :_reduce_90,
  3, 209, :_reduce_91,
  5, 209, :_reduce_92,
  2, 209, :_reduce_93,
  4, 209, :_reduce_94,
  2, 209, :_reduce_95,
  4, 209, :_reduce_96,
  1, 209, :_reduce_97,
  3, 209, :_reduce_98,
  1, 213, :_reduce_none,
  3, 213, :_reduce_100,
  2, 212, :_reduce_101,
  3, 212, :_reduce_102,
  1, 215, :_reduce_103,
  3, 215, :_reduce_104,
  1, 214, :_reduce_105,
  1, 214, :_reduce_106,
  4, 214, :_reduce_107,
  3, 214, :_reduce_108,
  3, 214, :_reduce_109,
  3, 214, :_reduce_110,
  3, 214, :_reduce_111,
  2, 214, :_reduce_112,
  1, 214, :_reduce_113,
  1, 169, :_reduce_114,
  1, 169, :_reduce_115,
  4, 169, :_reduce_116,
  3, 169, :_reduce_117,
  3, 169, :_reduce_118,
  3, 169, :_reduce_119,
  3, 169, :_reduce_120,
  2, 169, :_reduce_121,
  1, 169, :_reduce_122,
  1, 218, :_reduce_123,
  1, 218, :_reduce_none,
  2, 219, :_reduce_125,
  1, 219, :_reduce_126,
  3, 219, :_reduce_127,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 163, :_reduce_133,
  1, 163, :_reduce_none,
  1, 164, :_reduce_135,
  0, 223, :_reduce_136,
  4, 164, :_reduce_137,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 220, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  3, 184, :_reduce_209,
  3, 184, :_reduce_210,
  6, 184, :_reduce_211,
  5, 184, :_reduce_212,
  5, 184, :_reduce_213,
  5, 184, :_reduce_214,
  5, 184, :_reduce_215,
  4, 184, :_reduce_216,
  3, 184, :_reduce_217,
  3, 184, :_reduce_218,
  3, 184, :_reduce_219,
  2, 184, :_reduce_220,
  2, 184, :_reduce_221,
  2, 184, :_reduce_222,
  2, 184, :_reduce_223,
  3, 184, :_reduce_224,
  3, 184, :_reduce_225,
  3, 184, :_reduce_226,
  3, 184, :_reduce_227,
  3, 184, :_reduce_228,
  3, 184, :_reduce_229,
  4, 184, :_reduce_230,
  2, 184, :_reduce_231,
  2, 184, :_reduce_232,
  3, 184, :_reduce_233,
  3, 184, :_reduce_234,
  3, 184, :_reduce_235,
  3, 184, :_reduce_236,
  1, 184, :_reduce_none,
  3, 184, :_reduce_238,
  3, 184, :_reduce_239,
  3, 184, :_reduce_240,
  3, 184, :_reduce_241,
  3, 184, :_reduce_242,
  2, 184, :_reduce_243,
  2, 184, :_reduce_244,
  3, 184, :_reduce_245,
  3, 184, :_reduce_246,
  3, 184, :_reduce_247,
  3, 184, :_reduce_248,
  3, 184, :_reduce_249,
  6, 184, :_reduce_250,
  4, 184, :_reduce_251,
  6, 184, :_reduce_252,
  4, 184, :_reduce_253,
  6, 184, :_reduce_254,
  1, 184, :_reduce_none,
  1, 229, :_reduce_none,
  1, 229, :_reduce_none,
  1, 229, :_reduce_none,
  1, 229, :_reduce_none,
  3, 226, :_reduce_260,
  3, 226, :_reduce_261,
  1, 175, :_reduce_none,
  1, 230, :_reduce_none,
  2, 230, :_reduce_none,
  4, 230, :_reduce_265,
  2, 230, :_reduce_266,
  1, 224, :_reduce_none,
  3, 224, :_reduce_268,
  3, 235, :_reduce_269,
  5, 235, :_reduce_270,
  3, 235, :_reduce_271,
  0, 237, :_reduce_272,
  1, 237, :_reduce_none,
  0, 179, :_reduce_274,
  1, 179, :_reduce_none,
  2, 179, :_reduce_none,
  4, 179, :_reduce_277,
  2, 179, :_reduce_278,
  1, 208, :_reduce_279,
  2, 208, :_reduce_280,
  2, 208, :_reduce_281,
  4, 208, :_reduce_282,
  1, 208, :_reduce_283,
  0, 240, :_reduce_284,
  2, 201, :_reduce_285,
  2, 239, :_reduce_286,
  2, 238, :_reduce_287,
  0, 238, :_reduce_288,
  1, 232, :_reduce_289,
  2, 232, :_reduce_290,
  3, 232, :_reduce_291,
  4, 232, :_reduce_292,
  1, 171, :_reduce_293,
  1, 171, :_reduce_none,
  3, 170, :_reduce_295,
  4, 170, :_reduce_296,
  2, 170, :_reduce_297,
  1, 228, :_reduce_none,
  1, 228, :_reduce_none,
  1, 228, :_reduce_none,
  1, 228, :_reduce_none,
  1, 228, :_reduce_none,
  1, 228, :_reduce_none,
  1, 228, :_reduce_none,
  1, 228, :_reduce_none,
  1, 228, :_reduce_none,
  1, 228, :_reduce_none,
  1, 228, :_reduce_308,
  0, 262, :_reduce_309,
  4, 228, :_reduce_310,
  0, 263, :_reduce_311,
  4, 228, :_reduce_312,
  0, 264, :_reduce_313,
  4, 228, :_reduce_314,
  3, 228, :_reduce_315,
  3, 228, :_reduce_316,
  2, 228, :_reduce_317,
  3, 228, :_reduce_318,
  3, 228, :_reduce_319,
  1, 228, :_reduce_320,
  4, 228, :_reduce_321,
  3, 228, :_reduce_322,
  1, 228, :_reduce_323,
  5, 228, :_reduce_324,
  4, 228, :_reduce_325,
  3, 228, :_reduce_326,
  2, 228, :_reduce_327,
  1, 228, :_reduce_none,
  2, 228, :_reduce_329,
  1, 228, :_reduce_none,
  6, 228, :_reduce_331,
  6, 228, :_reduce_332,
  4, 228, :_reduce_333,
  4, 228, :_reduce_334,
  5, 228, :_reduce_335,
  4, 228, :_reduce_336,
  5, 228, :_reduce_337,
  6, 228, :_reduce_338,
  0, 265, :_reduce_339,
  6, 228, :_reduce_340,
  0, 266, :_reduce_341,
  7, 228, :_reduce_342,
  0, 267, :_reduce_343,
  5, 228, :_reduce_344,
  4, 228, :_reduce_345,
  4, 228, :_reduce_346,
  1, 228, :_reduce_347,
  1, 228, :_reduce_348,
  1, 228, :_reduce_349,
  1, 228, :_reduce_350,
  1, 178, :_reduce_none,
  1, 207, :_reduce_352,
  1, 254, :_reduce_none,
  1, 254, :_reduce_none,
  2, 254, :_reduce_355,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 255, :_reduce_none,
  5, 255, :_reduce_359,
  1, 159, :_reduce_none,
  2, 159, :_reduce_361,
  1, 258, :_reduce_none,
  1, 258, :_reduce_none,
  1, 268, :_reduce_364,
  3, 268, :_reduce_365,
  1, 271, :_reduce_366,
  3, 271, :_reduce_367,
  1, 270, :_reduce_none,
  3, 270, :_reduce_369,
  5, 270, :_reduce_370,
  1, 270, :_reduce_371,
  3, 270, :_reduce_372,
  2, 272, :_reduce_373,
  1, 272, :_reduce_374,
  1, 273, :_reduce_none,
  1, 273, :_reduce_none,
  4, 276, :_reduce_377,
  2, 276, :_reduce_378,
  2, 276, :_reduce_379,
  1, 276, :_reduce_380,
  2, 280, :_reduce_381,
  0, 280, :_reduce_382,
  1, 281, :_reduce_none,
  6, 282, :_reduce_384,
  8, 282, :_reduce_385,
  4, 282, :_reduce_386,
  6, 282, :_reduce_387,
  4, 282, :_reduce_388,
  2, 282, :_reduce_none,
  6, 282, :_reduce_390,
  2, 282, :_reduce_391,
  4, 282, :_reduce_392,
  6, 282, :_reduce_393,
  2, 282, :_reduce_394,
  4, 282, :_reduce_395,
  2, 282, :_reduce_396,
  4, 282, :_reduce_397,
  1, 282, :_reduce_none,
  0, 286, :_reduce_399,
  1, 286, :_reduce_400,
  3, 287, :_reduce_401,
  4, 287, :_reduce_402,
  1, 288, :_reduce_403,
  4, 288, :_reduce_404,
  1, 289, :_reduce_405,
  3, 289, :_reduce_406,
  1, 290, :_reduce_407,
  1, 290, :_reduce_none,
  0, 294, :_reduce_409,
  0, 295, :_reduce_410,
  5, 253, :_reduce_411,
  4, 292, :_reduce_412,
  1, 292, :_reduce_413,
  0, 298, :_reduce_414,
  4, 293, :_reduce_415,
  0, 299, :_reduce_416,
  4, 293, :_reduce_417,
  0, 301, :_reduce_418,
  4, 297, :_reduce_419,
  2, 199, :_reduce_420,
  4, 199, :_reduce_421,
  5, 199, :_reduce_422,
  5, 199, :_reduce_423,
  2, 252, :_reduce_424,
  4, 252, :_reduce_425,
  4, 252, :_reduce_426,
  3, 252, :_reduce_427,
  3, 252, :_reduce_428,
  3, 252, :_reduce_429,
  2, 252, :_reduce_430,
  1, 252, :_reduce_431,
  4, 252, :_reduce_432,
  0, 303, :_reduce_433,
  4, 251, :_reduce_434,
  0, 304, :_reduce_435,
  4, 251, :_reduce_436,
  0, 305, :_reduce_437,
  3, 203, :_reduce_438,
  0, 306, :_reduce_439,
  0, 307, :_reduce_440,
  4, 300, :_reduce_441,
  5, 256, :_reduce_442,
  1, 308, :_reduce_443,
  1, 308, :_reduce_none,
  0, 311, :_reduce_445,
  0, 312, :_reduce_446,
  7, 257, :_reduce_447,
  1, 310, :_reduce_448,
  1, 310, :_reduce_none,
  1, 309, :_reduce_450,
  3, 309, :_reduce_451,
  3, 309, :_reduce_452,
  1, 313, :_reduce_none,
  2, 313, :_reduce_454,
  3, 313, :_reduce_455,
  1, 313, :_reduce_456,
  1, 313, :_reduce_457,
  1, 313, :_reduce_458,
  1, 185, :_reduce_none,
  3, 318, :_reduce_460,
  1, 318, :_reduce_none,
  3, 320, :_reduce_462,
  1, 320, :_reduce_none,
  1, 322, :_reduce_464,
  1, 323, :_reduce_465,
  1, 321, :_reduce_none,
  4, 321, :_reduce_467,
  4, 321, :_reduce_468,
  4, 321, :_reduce_469,
  3, 321, :_reduce_470,
  4, 321, :_reduce_471,
  4, 321, :_reduce_472,
  4, 321, :_reduce_473,
  3, 321, :_reduce_474,
  3, 321, :_reduce_475,
  3, 321, :_reduce_476,
  2, 321, :_reduce_477,
  0, 327, :_reduce_478,
  4, 321, :_reduce_479,
  2, 321, :_reduce_480,
  0, 328, :_reduce_481,
  4, 321, :_reduce_482,
  1, 314, :_reduce_483,
  1, 314, :_reduce_484,
  2, 314, :_reduce_485,
  3, 314, :_reduce_486,
  5, 314, :_reduce_487,
  2, 314, :_reduce_488,
  4, 314, :_reduce_489,
  1, 314, :_reduce_none,
  2, 329, :_reduce_491,
  3, 329, :_reduce_492,
  1, 316, :_reduce_493,
  3, 316, :_reduce_494,
  5, 315, :_reduce_495,
  2, 332, :_reduce_496,
  1, 332, :_reduce_497,
  1, 331, :_reduce_498,
  3, 331, :_reduce_499,
  1, 330, :_reduce_none,
  3, 317, :_reduce_501,
  1, 317, :_reduce_502,
  2, 317, :_reduce_503,
  1, 317, :_reduce_504,
  1, 333, :_reduce_505,
  3, 333, :_reduce_506,
  2, 335, :_reduce_507,
  1, 335, :_reduce_508,
  1, 336, :_reduce_509,
  3, 336, :_reduce_510,
  2, 338, :_reduce_511,
  1, 338, :_reduce_512,
  2, 340, :_reduce_513,
  1, 334, :_reduce_none,
  1, 334, :_reduce_none,
  1, 324, :_reduce_none,
  3, 324, :_reduce_517,
  3, 324, :_reduce_518,
  2, 324, :_reduce_519,
  2, 324, :_reduce_520,
  1, 324, :_reduce_none,
  1, 324, :_reduce_none,
  1, 324, :_reduce_none,
  2, 324, :_reduce_524,
  2, 324, :_reduce_525,
  1, 341, :_reduce_none,
  1, 341, :_reduce_none,
  1, 341, :_reduce_none,
  1, 341, :_reduce_none,
  1, 341, :_reduce_none,
  1, 341, :_reduce_none,
  1, 341, :_reduce_none,
  1, 341, :_reduce_none,
  1, 341, :_reduce_534,
  1, 341, :_reduce_none,
  1, 319, :_reduce_536,
  2, 342, :_reduce_537,
  2, 325, :_reduce_538,
  3, 325, :_reduce_539,
  1, 325, :_reduce_540,
  6, 158, :_reduce_541,
  0, 158, :_reduce_542,
  1, 343, :_reduce_543,
  1, 343, :_reduce_none,
  1, 343, :_reduce_none,
  2, 344, :_reduce_546,
  1, 344, :_reduce_none,
  2, 160, :_reduce_548,
  1, 160, :_reduce_none,
  1, 241, :_reduce_none,
  1, 241, :_reduce_none,
  1, 242, :_reduce_552,
  1, 346, :_reduce_553,
  2, 346, :_reduce_554,
  3, 347, :_reduce_555,
  1, 347, :_reduce_556,
  1, 347, :_reduce_557,
  3, 243, :_reduce_558,
  4, 244, :_reduce_559,
  3, 245, :_reduce_560,
  0, 350, :_reduce_561,
  3, 350, :_reduce_562,
  1, 351, :_reduce_563,
  2, 351, :_reduce_564,
  3, 247, :_reduce_565,
  0, 353, :_reduce_566,
  3, 353, :_reduce_567,
  3, 246, :_reduce_568,
  3, 248, :_reduce_569,
  0, 354, :_reduce_570,
  3, 354, :_reduce_571,
  0, 355, :_reduce_572,
  3, 355, :_reduce_573,
  0, 337, :_reduce_574,
  2, 337, :_reduce_575,
  0, 348, :_reduce_576,
  2, 348, :_reduce_577,
  0, 349, :_reduce_578,
  2, 349, :_reduce_579,
  1, 352, :_reduce_580,
  2, 352, :_reduce_581,
  0, 357, :_reduce_582,
  4, 352, :_reduce_583,
  1, 356, :_reduce_584,
  1, 356, :_reduce_585,
  1, 356, :_reduce_586,
  1, 356, :_reduce_none,
  1, 222, :_reduce_none,
  1, 222, :_reduce_none,
  1, 358, :_reduce_590,
  3, 359, :_reduce_591,
  1, 345, :_reduce_592,
  2, 345, :_reduce_593,
  1, 225, :_reduce_594,
  1, 225, :_reduce_595,
  1, 225, :_reduce_596,
  1, 225, :_reduce_597,
  1, 216, :_reduce_598,
  1, 216, :_reduce_599,
  1, 216, :_reduce_600,
  1, 216, :_reduce_601,
  1, 216, :_reduce_602,
  1, 217, :_reduce_603,
  1, 217, :_reduce_604,
  1, 217, :_reduce_605,
  1, 217, :_reduce_606,
  1, 217, :_reduce_607,
  1, 217, :_reduce_608,
  1, 217, :_reduce_609,
  1, 249, :_reduce_610,
  1, 249, :_reduce_611,
  1, 177, :_reduce_612,
  1, 177, :_reduce_613,
  1, 182, :_reduce_614,
  1, 182, :_reduce_615,
  0, 360, :_reduce_616,
  4, 259, :_reduce_617,
  0, 259, :_reduce_618,
  3, 227, :_reduce_619,
  5, 227, :_reduce_620,
  3, 227, :_reduce_621,
  1, 261, :_reduce_none,
  0, 362, :_reduce_623,
  3, 261, :_reduce_624,
  4, 361, :_reduce_625,
  2, 361, :_reduce_626,
  2, 361, :_reduce_627,
  1, 361, :_reduce_628,
  2, 364, :_reduce_629,
  0, 364, :_reduce_630,
  6, 296, :_reduce_631,
  8, 296, :_reduce_632,
  4, 296, :_reduce_633,
  6, 296, :_reduce_634,
  4, 296, :_reduce_635,
  6, 296, :_reduce_636,
  2, 296, :_reduce_637,
  4, 296, :_reduce_638,
  6, 296, :_reduce_639,
  2, 296, :_reduce_640,
  4, 296, :_reduce_641,
  2, 296, :_reduce_642,
  4, 296, :_reduce_643,
  1, 296, :_reduce_644,
  0, 296, :_reduce_645,
  1, 236, :_reduce_646,
  1, 291, :_reduce_647,
  1, 291, :_reduce_648,
  1, 291, :_reduce_649,
  1, 291, :_reduce_650,
  1, 269, :_reduce_none,
  1, 269, :_reduce_652,
  1, 366, :_reduce_653,
  1, 367, :_reduce_654,
  3, 367, :_reduce_655,
  1, 283, :_reduce_656,
  3, 283, :_reduce_657,
  1, 368, :_reduce_658,
  2, 369, :_reduce_659,
  1, 369, :_reduce_660,
  2, 370, :_reduce_661,
  1, 370, :_reduce_662,
  1, 277, :_reduce_663,
  3, 277, :_reduce_664,
  1, 363, :_reduce_665,
  3, 363, :_reduce_666,
  1, 339, :_reduce_none,
  1, 339, :_reduce_none,
  2, 275, :_reduce_669,
  2, 274, :_reduce_670,
  1, 274, :_reduce_671,
  3, 371, :_reduce_672,
  3, 372, :_reduce_673,
  1, 284, :_reduce_674,
  3, 284, :_reduce_675,
  1, 365, :_reduce_676,
  3, 365, :_reduce_677,
  1, 373, :_reduce_none,
  1, 373, :_reduce_none,
  2, 285, :_reduce_680,
  1, 285, :_reduce_681,
  1, 374, :_reduce_none,
  1, 374, :_reduce_none,
  2, 279, :_reduce_684,
  2, 278, :_reduce_685,
  0, 278, :_reduce_686,
  1, 194, :_reduce_none,
  3, 194, :_reduce_688,
  0, 250, :_reduce_689,
  2, 250, :_reduce_none,
  1, 234, :_reduce_691,
  3, 234, :_reduce_692,
  3, 375, :_reduce_693,
  2, 375, :_reduce_694,
  4, 375, :_reduce_695,
  2, 375, :_reduce_696,
  1, 206, :_reduce_none,
  1, 206, :_reduce_none,
  1, 206, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 200, :_reduce_none,
  1, 302, :_reduce_none,
  1, 302, :_reduce_none,
  1, 302, :_reduce_none,
  1, 195, :_reduce_none,
  1, 195, :_reduce_none,
  1, 181, :_reduce_709,
  1, 181, :_reduce_710,
  0, 151, :_reduce_none,
  1, 151, :_reduce_none,
  0, 183, :_reduce_none,
  1, 183, :_reduce_none,
  2, 211, :_reduce_715,
  2, 180, :_reduce_716,
  2, 326, :_reduce_717,
  0, 233, :_reduce_none,
  1, 233, :_reduce_none,
  1, 233, :_reduce_none,
  1, 260, :_reduce_721,
  1, 260, :_reduce_none,
  1, 153, :_reduce_none,
  2, 153, :_reduce_none,
  0, 231, :_reduce_725 ]

racc_reduce_n = 726

racc_shift_n = 1219

racc_token_table = {
  false => 0,
  :error => 1,
  :kCLASS => 2,
  :kMODULE => 3,
  :kDEF => 4,
  :kUNDEF => 5,
  :kBEGIN => 6,
  :kRESCUE => 7,
  :kENSURE => 8,
  :kEND => 9,
  :kIF => 10,
  :kUNLESS => 11,
  :kTHEN => 12,
  :kELSIF => 13,
  :kELSE => 14,
  :kCASE => 15,
  :kWHEN => 16,
  :kWHILE => 17,
  :kUNTIL => 18,
  :kFOR => 19,
  :kBREAK => 20,
  :kNEXT => 21,
  :kREDO => 22,
  :kRETRY => 23,
  :kIN => 24,
  :kDO => 25,
  :kDO_COND => 26,
  :kDO_BLOCK => 27,
  :kDO_LAMBDA => 28,
  :kRETURN => 29,
  :kYIELD => 30,
  :kSUPER => 31,
  :kSELF => 32,
  :kNIL => 33,
  :kTRUE => 34,
  :kFALSE => 35,
  :kAND => 36,
  :kOR => 37,
  :kNOT => 38,
  :kIF_MOD => 39,
  :kUNLESS_MOD => 40,
  :kWHILE_MOD => 41,
  :kUNTIL_MOD => 42,
  :kRESCUE_MOD => 43,
  :kALIAS => 44,
  :kDEFINED => 45,
  :klBEGIN => 46,
  :klEND => 47,
  :k__LINE__ => 48,
  :k__FILE__ => 49,
  :k__ENCODING__ => 50,
  :tIDENTIFIER => 51,
  :tFID => 52,
  :tGVAR => 53,
  :tIVAR => 54,
  :tCONSTANT => 55,
  :tLABEL => 56,
  :tCVAR => 57,
  :tNTH_REF => 58,
  :tBACK_REF => 59,
  :tSTRING_CONTENT => 60,
  :tINTEGER => 61,
  :tFLOAT => 62,
  :tUPLUS => 63,
  :tUMINUS => 64,
  :tUNARY_NUM => 65,
  :tPOW => 66,
  :tCMP => 67,
  :tEQ => 68,
  :tEQQ => 69,
  :tNEQ => 70,
  :tGEQ => 71,
  :tLEQ => 72,
  :tANDOP => 73,
  :tOROP => 74,
  :tMATCH => 75,
  :tNMATCH => 76,
  :tDOT => 77,
  :tDOT2 => 78,
  :tDOT3 => 79,
  :tAREF => 80,
  :tASET => 81,
  :tLSHFT => 82,
  :tRSHFT => 83,
  :tCOLON2 => 84,
  :tCOLON3 => 85,
  :tOP_ASGN => 86,
  :tASSOC => 87,
  :tLPAREN => 88,
  :tLPAREN2 => 89,
  :tRPAREN => 90,
  :tLPAREN_ARG => 91,
  :tLBRACK => 92,
  :tLBRACK2 => 93,
  :tRBRACK => 94,
  :tLBRACE => 95,
  :tLBRACE_ARG => 96,
  :tSTAR => 97,
  :tSTAR2 => 98,
  :tAMPER => 99,
  :tAMPER2 => 100,
  :tTILDE => 101,
  :tPERCENT => 102,
  :tDIVIDE => 103,
  :tDSTAR => 104,
  :tPLUS => 105,
  :tMINUS => 106,
  :tLT => 107,
  :tGT => 108,
  :tPIPE => 109,
  :tBANG => 110,
  :tCARET => 111,
  :tLCURLY => 112,
  :tRCURLY => 113,
  :tBACK_REF2 => 114,
  :tSYMBEG => 115,
  :tSTRING_BEG => 116,
  :tXSTRING_BEG => 117,
  :tREGEXP_BEG => 118,
  :tREGEXP_OPT => 119,
  :tWORDS_BEG => 120,
  :tQWORDS_BEG => 121,
  :tSYMBOLS_BEG => 122,
  :tQSYMBOLS_BEG => 123,
  :tSTRING_DBEG => 124,
  :tSTRING_DVAR => 125,
  :tSTRING_END => 126,
  :tSTRING_DEND => 127,
  :tSTRING => 128,
  :tSYMBOL => 129,
  :tNL => 130,
  :tEH => 131,
  :tCOLON => 132,
  :tCOMMA => 133,
  :tSPACE => 134,
  :tSEMI => 135,
  :tLAMBDA => 136,
  :tLAMBEG => 137,
  :tCHARACTER => 138,
  :tRATIONAL => 139,
  :tIMAGINARY => 140,
  :tLABEL_END => 141,
  :tANDDOT => 142,
  :tBDOT2 => 143,
  :tBDOT3 => 144,
  :tEQL => 145,
  :tLOWEST => 146 }

racc_nt_base = 147

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]

Racc_token_to_s_table = [
  "$end",
  "error",
  "kCLASS",
  "kMODULE",
  "kDEF",
  "kUNDEF",
  "kBEGIN",
  "kRESCUE",
  "kENSURE",
  "kEND",
  "kIF",
  "kUNLESS",
  "kTHEN",
  "kELSIF",
  "kELSE",
  "kCASE",
  "kWHEN",
  "kWHILE",
  "kUNTIL",
  "kFOR",
  "kBREAK",
  "kNEXT",
  "kREDO",
  "kRETRY",
  "kIN",
  "kDO",
  "kDO_COND",
  "kDO_BLOCK",
  "kDO_LAMBDA",
  "kRETURN",
  "kYIELD",
  "kSUPER",
  "kSELF",
  "kNIL",
  "kTRUE",
  "kFALSE",
  "kAND",
  "kOR",
  "kNOT",
  "kIF_MOD",
  "kUNLESS_MOD",
  "kWHILE_MOD",
  "kUNTIL_MOD",
  "kRESCUE_MOD",
  "kALIAS",
  "kDEFINED",
  "klBEGIN",
  "klEND",
  "k__LINE__",
  "k__FILE__",
  "k__ENCODING__",
  "tIDENTIFIER",
  "tFID",
  "tGVAR",
  "tIVAR",
  "tCONSTANT",
  "tLABEL",
  "tCVAR",
  "tNTH_REF",
  "tBACK_REF",
  "tSTRING_CONTENT",
  "tINTEGER",
  "tFLOAT",
  "tUPLUS",
  "tUMINUS",
  "tUNARY_NUM",
  "tPOW",
  "tCMP",
  "tEQ",
  "tEQQ",
  "tNEQ",
  "tGEQ",
  "tLEQ",
  "tANDOP",
  "tOROP",
  "tMATCH",
  "tNMATCH",
  "tDOT",
  "tDOT2",
  "tDOT3",
  "tAREF",
  "tASET",
  "tLSHFT",
  "tRSHFT",
  "tCOLON2",
  "tCOLON3",
  "tOP_ASGN",
  "tASSOC",
  "tLPAREN",
  "tLPAREN2",
  "tRPAREN",
  "tLPAREN_ARG",
  "tLBRACK",
  "tLBRACK2",
  "tRBRACK",
  "tLBRACE",
  "tLBRACE_ARG",
  "tSTAR",
  "tSTAR2",
  "tAMPER",
  "tAMPER2",
  "tTILDE",
  "tPERCENT",
  "tDIVIDE",
  "tDSTAR",
  "tPLUS",
  "tMINUS",
  "tLT",
  "tGT",
  "tPIPE",
  "tBANG",
  "tCARET",
  "tLCURLY",
  "tRCURLY",
  "tBACK_REF2",
  "tSYMBEG",
  "tSTRING_BEG",
  "tXSTRING_BEG",
  "tREGEXP_BEG",
  "tREGEXP_OPT",
  "tWORDS_BEG",
  "tQWORDS_BEG",
  "tSYMBOLS_BEG",
  "tQSYMBOLS_BEG",
  "tSTRING_DBEG",
  "tSTRING_DVAR",
  "tSTRING_END",
  "tSTRING_DEND",
  "tSTRING",
  "tSYMBOL",
  "tNL",
  "tEH",
  "tCOLON",
  "tCOMMA",
  "tSPACE",
  "tSEMI",
  "tLAMBDA",
  "tLAMBEG",
  "tCHARACTER",
  "tRATIONAL",
  "tIMAGINARY",
  "tLABEL_END",
  "tANDDOT",
  "tBDOT2",
  "tBDOT3",
  "tEQL",
  "tLOWEST",
  "$start",
  "program",
  "top_compstmt",
  "top_stmts",
  "opt_terms",
  "top_stmt",
  "terms",
  "stmt",
  "begin_block",
  "bodystmt",
  "compstmt",
  "opt_rescue",
  "opt_else",
  "opt_ensure",
  "stmts",
  "stmt_or_begin",
  "fitem",
  "undef_list",
  "expr_value",
  "command_asgn",
  "mlhs",
  "command_call",
  "lhs",
  "mrhs",
  "mrhs_arg",
  "rassign",
  "expr",
  "@1",
  "arg_value",
  "command_rhs",
  "var_lhs",
  "primary_value",
  "opt_call_args",
  "rbracket",
  "call_op",
  "backref",
  "opt_nl",
  "arg",
  "p_expr",
  "@2",
  "expr_value_do",
  "do",
  "def_name",
  "@3",
  "fname",
  "defn_head",
  "defs_head",
  "singleton",
  "dot_or_colon",
  "@4",
  "command",
  "block_command",
  "block_call",
  "operation2",
  "command_args",
  "cmd_brace_block",
  "brace_body",
  "fcall",
  "@5",
  "operation",
  "k_return",
  "call_args",
  "mlhs_basic",
  "mlhs_inner",
  "rparen",
  "mlhs_head",
  "mlhs_item",
  "mlhs_node",
  "mlhs_post",
  "user_variable",
  "keyword_variable",
  "cname",
  "cpath",
  "op",
  "reswords",
  "symbol",
  "@6",
  "arg_rhs",
  "simple_numeric",
  "rel_expr",
  "f_paren_args",
  "primary",
  "relop",
  "aref_args",
  "none",
  "args",
  "trailer",
  "assocs",
  "paren_args",
  "args_forward",
  "opt_paren_args",
  "opt_block_arg",
  "block_arg",
  "@7",
  "literal",
  "strings",
  "xstring",
  "regexp",
  "words",
  "qwords",
  "symbols",
  "qsymbols",
  "var_ref",
  "assoc_list",
  "brace_block",
  "method_call",
  "lambda",
  "then",
  "if_tail",
  "case_body",
  "p_case_body",
  "for_var",
  "superclass",
  "term",
  "f_arglist",
  "@8",
  "@9",
  "@10",
  "@11",
  "@12",
  "@13",
  "f_marg",
  "f_norm_arg",
  "f_margs",
  "f_marg_list",
  "f_rest_marg",
  "f_any_kwrest",
  "f_kwrest",
  "f_no_kwarg",
  "block_args_tail",
  "f_block_kwarg",
  "opt_f_block_arg",
  "f_block_arg",
  "opt_block_args_tail",
  "excessed_comma",
  "block_param",
  "f_arg",
  "f_block_optarg",
  "f_rest_arg",
  "opt_block_param",
  "block_param_def",
  "opt_bv_decl",
  "bv_decls",
  "bvar",
  "f_bad_arg",
  "f_larglist",
  "lambda_body",
  "@14",
  "@15",
  "f_args",
  "do_block",
  "@16",
  "@17",
  "do_body",
  "@18",
  "operation3",
  "@19",
  "@20",
  "@21",
  "@22",
  "@23",
  "cases",
  "p_top_expr",
  "p_cases",
  "@24",
  "@25",
  "p_top_expr_body",
  "p_args",
  "p_find",
  "p_args_tail",
  "p_kwargs",
  "p_as",
  "p_variable",
  "p_alt",
  "p_expr_basic",
  "p_lparen",
  "p_lbracket",
  "p_value",
  "p_const",
  "rbrace",
  "@26",
  "@27",
  "p_args_head",
  "p_arg",
  "p_args_post",
  "p_rest",
  "p_kwarg",
  "p_any_kwrest",
  "p_kw",
  "p_kw_label",
  "string_contents",
  "p_kwrest",
  "kwrest_mark",
  "p_kwnorest",
  "p_primitive",
  "p_var_ref",
  "exc_list",
  "exc_var",
  "numeric",
  "string",
  "string1",
  "xstring_contents",
  "regexp_contents",
  "word_list",
  "word",
  "string_content",
  "symbol_list",
  "qword_list",
  "qsym_list",
  "string_dvar",
  "@28",
  "ssym",
  "dsym",
  "@29",
  "args_tail",
  "@30",
  "f_kwarg",
  "opt_args_tail",
  "f_optarg",
  "f_arg_asgn",
  "f_arg_item",
  "f_label",
  "f_kw",
  "f_block_kw",
  "f_opt",
  "f_block_opt",
  "restarg_mark",
  "blkarg_mark",
  "assoc" ]

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

# reduce 1 omitted

def _reduce_2(val, _values, result)
                      result = @builder.compstmt(val[0])
                    
    result
end

def _reduce_3(val, _values, result)
                      result = []
                    
    result
end

def _reduce_4(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

def _reduce_5(val, _values, result)
                      result = val[0] << val[2]
                    
    result
end

def _reduce_6(val, _values, result)
                      result = [ val[1] ]
                    
    result
end

# reduce 7 omitted

def _reduce_8(val, _values, result)
                      result = @builder.preexe(val[0], *val[1])
                    
    result
end

def _reduce_9(val, _values, result)
                      result = val
                    
    result
end

def _reduce_10(val, _values, result)
                      rescue_bodies     = val[1]
                      else_t,   else_   = val[2]
                      ensure_t, ensure_ = val[3]

                      if rescue_bodies.empty? && !else_t.nil?
                        diagnostic :error, :useless_else, nil, else_t
                      end

                      result = @builder.begin_body(val[0],
                                  rescue_bodies,
                                  else_t,   else_,
                                  ensure_t, ensure_)
                    
    result
end

def _reduce_11(val, _values, result)
                      result = @builder.compstmt(val[0])
                    
    result
end

def _reduce_12(val, _values, result)
                      result = []
                    
    result
end

def _reduce_13(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

def _reduce_14(val, _values, result)
                      result = val[0] << val[2]
                    
    result
end

def _reduce_15(val, _values, result)
                      result = [ val[1] ]
                    
    result
end

# reduce 16 omitted

def _reduce_17(val, _values, result)
                      diagnostic :error, :begin_in_method, nil, val[0]
                    
    result
end

def _reduce_18(val, _values, result)
                      @lexer.state = :expr_fname
                    
    result
end

def _reduce_19(val, _values, result)
                      result = @builder.alias(val[0], val[1], val[3])
                    
    result
end

def _reduce_20(val, _values, result)
                      result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.gvar(val[2]))
                    
    result
end

def _reduce_21(val, _values, result)
                      result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.back_ref(val[2]))
                    
    result
end

def _reduce_22(val, _values, result)
                      diagnostic :error, :nth_ref_alias, nil, val[2]
                    
    result
end

def _reduce_23(val, _values, result)
                      result = @builder.undef_method(val[0], val[1])
                    
    result
end

def _reduce_24(val, _values, result)
                      result = @builder.condition_mod(val[0], nil,
                                                      val[1], val[2])
                    
    result
end

def _reduce_25(val, _values, result)
                      result = @builder.condition_mod(nil, val[0],
                                                      val[1], val[2])
                    
    result
end

def _reduce_26(val, _values, result)
                      result = @builder.loop_mod(:while, val[0], val[1], val[2])
                    
    result
end

def _reduce_27(val, _values, result)
                      result = @builder.loop_mod(:until, val[0], val[1], val[2])
                    
    result
end

def _reduce_28(val, _values, result)
                      rescue_body = @builder.rescue_body(val[1],
                                        nil, nil, nil,
                                        nil, val[2])

                      result = @builder.begin_body(val[0], [ rescue_body ])
                    
    result
end

def _reduce_29(val, _values, result)
                      result = @builder.postexe(val[0], val[1], val[2], val[3])
                    
    result
end

# reduce 30 omitted

def _reduce_31(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1], val[2])
                    
    result
end

def _reduce_32(val, _values, result)
                      result = @builder.assign(val[0], val[1],
                                  @builder.array(nil, val[2], nil))
                    
    result
end

def _reduce_33(val, _values, result)
                      rescue_body = @builder.rescue_body(val[3],
                                                         nil, nil, nil,
                                                         nil, val[4])
                      begin_body = @builder.begin_body(val[2], [ rescue_body ])

                      result = @builder.multi_assign(val[0], val[1], begin_body)
                    
    result
end

def _reduce_34(val, _values, result)
                      result = @builder.multi_assign(val[0], val[1], val[2])
                    
    result
end

# reduce 35 omitted

# reduce 36 omitted

def _reduce_37(val, _values, result)
                      result = @builder.rassign(val[0], val[1], val[2])
                    
    result
end

def _reduce_38(val, _values, result)
                      result = @builder.multi_rassign(val[0], val[1], val[2])
                    
    result
end

def _reduce_39(val, _values, result)
                      result = @builder.rassign(val[0], val[1], val[2])
                    
    result
end

def _reduce_40(val, _values, result)
                      result = @builder.multi_rassign(val[0], val[1], val[2])
                    
    result
end

def _reduce_41(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])
                    
    result
end

def _reduce_42(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])
                    
    result
end

def _reduce_43(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])
                    
    result
end

def _reduce_44(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])
                    
    result
end

def _reduce_45(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])
                    
    result
end

def _reduce_46(val, _values, result)
                      const  = @builder.const_op_assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))
                      result = @builder.op_assign(const, val[3], val[4])
                    
    result
end

def _reduce_47(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])
                    
    result
end

def _reduce_48(val, _values, result)
                      @builder.op_assign(val[0], val[1], val[2])
                    
    result
end

# reduce 49 omitted

def _reduce_50(val, _values, result)
                      rescue_body = @builder.rescue_body(val[1],
                                        nil, nil, nil,
                                        nil, val[2])

                      result = @builder.begin_body(val[0], [ rescue_body ])
                    
    result
end

# reduce 51 omitted

# reduce 52 omitted

def _reduce_53(val, _values, result)
                      result = @builder.logical_op(:and, val[0], val[1], val[2])
                    
    result
end

def _reduce_54(val, _values, result)
                      result = @builder.logical_op(:or, val[0], val[1], val[2])
                    
    result
end

def _reduce_55(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[2], nil)
                    
    result
end

def _reduce_56(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[1], nil)
                    
    result
end

def _reduce_57(val, _values, result)
                      @lexer.state = :expr_beg
                      @lexer.command_start = false
                      pattern_variables.push

                      result = @lexer.in_kwarg
                      @lexer.in_kwarg = true
                    
    result
end

def _reduce_58(val, _values, result)
                      @lexer.in_kwarg = val[2]
                      result = @builder.in_match(val[0], val[1], val[3])
                    
    result
end

# reduce 59 omitted

# reduce 60 omitted

def _reduce_61(val, _values, result)
                      @lexer.cond.push(true)
                    
    result
end

def _reduce_62(val, _values, result)
                      @lexer.cond.pop
                      result = [ val[1], val[2] ]
                    
    result
end

def _reduce_63(val, _values, result)
                      @static_env.extend_static
                      @lexer.cmdarg.push(false)
                      @lexer.cond.push(false)
                      @current_arg_stack.push(nil)

                      result = val[0]
                    
    result
end

def _reduce_64(val, _values, result)
                      @context.push(:def)

                      result = [ val[0], val[1] ]
                    
    result
end

def _reduce_65(val, _values, result)
                      @lexer.state = :expr_fname
                    
    result
end

def _reduce_66(val, _values, result)
                      @context.push(:defs)

                      result = [ val[0], val[1], val[2], val[4] ]
                    
    result
end

# reduce 67 omitted

# reduce 68 omitted

# reduce 69 omitted

def _reduce_70(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)
                    
    result
end

def _reduce_71(val, _values, result)
                      @context.push(:block)
                    
    result
end

def _reduce_72(val, _values, result)
                      result = [ val[0], *val[2], val[3] ]
                      @context.pop
                    
    result
end

# reduce 73 omitted

def _reduce_74(val, _values, result)
                      result = @builder.call_method(nil, nil, val[0],
                                  nil, val[1], nil)
                    
    result
end

def _reduce_75(val, _values, result)
                      method_call = @builder.call_method(nil, nil, val[0],
                                        nil, val[1], nil)

                      begin_t, args, body, end_t = val[2]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)
                    
    result
end

def _reduce_76(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)
                    
    result
end

def _reduce_77(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                        nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)
                    
    result
end

def _reduce_78(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)
                    
    result
end

def _reduce_79(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                        nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)
                    
    result
end

def _reduce_80(val, _values, result)
                      result = @builder.keyword_cmd(:super, val[0],
                                  nil, val[1], nil)
                    
    result
end

def _reduce_81(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0],
                                  nil, val[1], nil)
                    
    result
end

def _reduce_82(val, _values, result)
                      result = @builder.keyword_cmd(:return, val[0],
                                  nil, val[1], nil)
                    
    result
end

def _reduce_83(val, _values, result)
                      result = @builder.keyword_cmd(:break, val[0],
                                  nil, val[1], nil)
                    
    result
end

def _reduce_84(val, _values, result)
                      result = @builder.keyword_cmd(:next, val[0],
                                  nil, val[1], nil)
                    
    result
end

def _reduce_85(val, _values, result)
                      result = @builder.multi_lhs(nil, val[0], nil)
                    
    result
end

def _reduce_86(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])
                    
    result
end

def _reduce_87(val, _values, result)
                      result = @builder.multi_lhs(nil, val[0], nil)
                    
    result
end

def _reduce_88(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])
                    
    result
end

# reduce 89 omitted

def _reduce_90(val, _values, result)
                      result = val[0].
                                  push(val[1])
                    
    result
end

def _reduce_91(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1], val[2]))
                    
    result
end

def _reduce_92(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1], val[2])).
                                  concat(val[4])
                    
    result
end

def _reduce_93(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1]))
                    
    result
end

def _reduce_94(val, _values, result)
                      result = val[0].
                                  push(@builder.splat(val[1])).
                                  concat(val[3])
                    
    result
end

def _reduce_95(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]
                    
    result
end

def _reduce_96(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]),
                                 *val[3] ]
                    
    result
end

def _reduce_97(val, _values, result)
                      result = [ @builder.splat(val[0]) ]
                    
    result
end

def _reduce_98(val, _values, result)
                      result = [ @builder.splat(val[0]),
                                 *val[2] ]
                    
    result
end

# reduce 99 omitted

def _reduce_100(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])
                    
    result
end

def _reduce_101(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

def _reduce_102(val, _values, result)
                      result = val[0] << val[1]
                    
    result
end

def _reduce_103(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

def _reduce_104(val, _values, result)
                      result = val[0] << val[2]
                    
    result
end

def _reduce_105(val, _values, result)
                      result = @builder.assignable(val[0])
                    
    result
end

def _reduce_106(val, _values, result)
                      result = @builder.assignable(val[0])
                    
    result
end

def _reduce_107(val, _values, result)
                      result = @builder.index_asgn(val[0], val[1], val[2], val[3])
                    
    result
end

def _reduce_108(val, _values, result)
                      if (val[1][0] == :anddot)
                        diagnostic :error, :csend_in_lhs_of_masgn, nil, val[1]
                      end

                      result = @builder.attr_asgn(val[0], val[1], val[2])
                    
    result
end

def _reduce_109(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])
                    
    result
end

def _reduce_110(val, _values, result)
                      if (val[1][0] == :anddot)
                        diagnostic :error, :csend_in_lhs_of_masgn, nil, val[1]
                      end

                      result = @builder.attr_asgn(val[0], val[1], val[2])
                    
    result
end

def _reduce_111(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))
                    
    result
end

def _reduce_112(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))
                    
    result
end

def _reduce_113(val, _values, result)
                      result = @builder.assignable(val[0])
                    
    result
end

def _reduce_114(val, _values, result)
                      result = @builder.assignable(val[0])
                    
    result
end

def _reduce_115(val, _values, result)
                      result = @builder.assignable(val[0])
                    
    result
end

def _reduce_116(val, _values, result)
                      result = @builder.index_asgn(val[0], val[1], val[2], val[3])
                    
    result
end

def _reduce_117(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])
                    
    result
end

def _reduce_118(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])
                    
    result
end

def _reduce_119(val, _values, result)
                      result = @builder.attr_asgn(val[0], val[1], val[2])
                    
    result
end

def _reduce_120(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))
                    
    result
end

def _reduce_121(val, _values, result)
                      result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))
                    
    result
end

def _reduce_122(val, _values, result)
                      result = @builder.assignable(val[0])
                    
    result
end

def _reduce_123(val, _values, result)
                      diagnostic :error, :module_name_const, nil, val[0]
                    
    result
end

# reduce 124 omitted

def _reduce_125(val, _values, result)
                      result = @builder.const_global(val[0], val[1])
                    
    result
end

def _reduce_126(val, _values, result)
                      result = @builder.const(val[0])
                    
    result
end

def _reduce_127(val, _values, result)
                      result = @builder.const_fetch(val[0], val[1], val[2])
                    
    result
end

# reduce 128 omitted

# reduce 129 omitted

# reduce 130 omitted

# reduce 131 omitted

# reduce 132 omitted

def _reduce_133(val, _values, result)
                      result = @builder.symbol(val[0])
                    
    result
end

# reduce 134 omitted

def _reduce_135(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

def _reduce_136(val, _values, result)
                      @lexer.state = :expr_fname
                    
    result
end

def _reduce_137(val, _values, result)
                      result = val[0] << val[3]
                    
    result
end

# reduce 138 omitted

# reduce 139 omitted

# reduce 140 omitted

# reduce 141 omitted

# reduce 142 omitted

# reduce 143 omitted

# reduce 144 omitted

# reduce 145 omitted

# reduce 146 omitted

# reduce 147 omitted

# reduce 148 omitted

# reduce 149 omitted

# reduce 150 omitted

# reduce 151 omitted

# reduce 152 omitted

# reduce 153 omitted

# reduce 154 omitted

# reduce 155 omitted

# reduce 156 omitted

# reduce 157 omitted

# reduce 158 omitted

# reduce 159 omitted

# reduce 160 omitted

# reduce 161 omitted

# reduce 162 omitted

# reduce 163 omitted

# reduce 164 omitted

# reduce 165 omitted

# reduce 166 omitted

# reduce 167 omitted

# reduce 168 omitted

# reduce 169 omitted

# reduce 170 omitted

# reduce 171 omitted

# reduce 172 omitted

# reduce 173 omitted

# reduce 174 omitted

# reduce 175 omitted

# reduce 176 omitted

# reduce 177 omitted

# reduce 178 omitted

# reduce 179 omitted

# reduce 180 omitted

# reduce 181 omitted

# reduce 182 omitted

# reduce 183 omitted

# reduce 184 omitted

# reduce 185 omitted

# reduce 186 omitted

# reduce 187 omitted

# reduce 188 omitted

# reduce 189 omitted

# reduce 190 omitted

# reduce 191 omitted

# reduce 192 omitted

# reduce 193 omitted

# reduce 194 omitted

# reduce 195 omitted

# reduce 196 omitted

# reduce 197 omitted

# reduce 198 omitted

# reduce 199 omitted

# reduce 200 omitted

# reduce 201 omitted

# reduce 202 omitted

# reduce 203 omitted

# reduce 204 omitted

# reduce 205 omitted

# reduce 206 omitted

# reduce 207 omitted

# reduce 208 omitted

def _reduce_209(val, _values, result)
                      result = @builder.assign(val[0], val[1], val[2])
                    
    result
end

def _reduce_210(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])
                    
    result
end

def _reduce_211(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])
                    
    result
end

def _reduce_212(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])
                    
    result
end

def _reduce_213(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])
                    
    result
end

def _reduce_214(val, _values, result)
                      result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])
                    
    result
end

def _reduce_215(val, _values, result)
                      const  = @builder.const_op_assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))
                      result = @builder.op_assign(const, val[3], val[4])
                    
    result
end

def _reduce_216(val, _values, result)
                      const  = @builder.const_op_assignable(
                                  @builder.const_global(val[0], val[1]))
                      result = @builder.op_assign(const, val[2], val[3])
                    
    result
end

def _reduce_217(val, _values, result)
                      result = @builder.op_assign(val[0], val[1], val[2])
                    
    result
end

def _reduce_218(val, _values, result)
                      result = @builder.range_inclusive(val[0], val[1], val[2])
                    
    result
end

def _reduce_219(val, _values, result)
                      result = @builder.range_exclusive(val[0], val[1], val[2])
                    
    result
end

def _reduce_220(val, _values, result)
                      result = @builder.range_inclusive(val[0], val[1], nil)
                    
    result
end

def _reduce_221(val, _values, result)
                      result = @builder.range_exclusive(val[0], val[1], nil)
                    
    result
end

def _reduce_222(val, _values, result)
                      result = @builder.range_inclusive(nil, val[0], val[1])
                    
    result
end

def _reduce_223(val, _values, result)
                      result = @builder.range_exclusive(nil, val[0], val[1])
                    
    result
end

def _reduce_224(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])
                    
    result
end

def _reduce_225(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])
                    
    result
end

def _reduce_226(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])
                    
    result
end

def _reduce_227(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])
                    
    result
end

def _reduce_228(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])
                    
    result
end

def _reduce_229(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])
                    
    result
end

def _reduce_230(val, _values, result)
                      result = @builder.unary_op(val[0],
                                  @builder.binary_op(
                                    val[1], val[2], val[3]))
                    
    result
end

def _reduce_231(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])
                    
    result
end

def _reduce_232(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])
                    
    result
end

def _reduce_233(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])
                    
    result
end

def _reduce_234(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])
                    
    result
end

def _reduce_235(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])
                    
    result
end

def _reduce_236(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])
                    
    result
end

# reduce 237 omitted

def _reduce_238(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])
                    
    result
end

def _reduce_239(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])
                    
    result
end

def _reduce_240(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])
                    
    result
end

def _reduce_241(val, _values, result)
                      result = @builder.match_op(val[0], val[1], val[2])
                    
    result
end

def _reduce_242(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])
                    
    result
end

def _reduce_243(val, _values, result)
                      result = @builder.not_op(val[0], nil, val[1], nil)
                    
    result
end

def _reduce_244(val, _values, result)
                      result = @builder.unary_op(val[0], val[1])
                    
    result
end

def _reduce_245(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])
                    
    result
end

def _reduce_246(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])
                    
    result
end

def _reduce_247(val, _values, result)
                      result = @builder.logical_op(:and, val[0], val[1], val[2])
                    
    result
end

def _reduce_248(val, _values, result)
                      result = @builder.logical_op(:or, val[0], val[1], val[2])
                    
    result
end

def _reduce_249(val, _values, result)
                      result = @builder.keyword_cmd(:defined?, val[0], nil, [ val[2] ], nil)
                    
    result
end

def _reduce_250(val, _values, result)
                      result = @builder.ternary(val[0], val[1],
                                                val[2], val[4], val[5])
                    
    result
end

def _reduce_251(val, _values, result)
                      result = @builder.def_endless_method(*val[0],
                                 val[1], val[2], val[3])

                      @lexer.cmdarg.pop
                      @lexer.cond.pop
                      @static_env.unextend
                      @context.pop
                      @current_arg_stack.pop
                    
    result
end

def _reduce_252(val, _values, result)
                      rescue_body = @builder.rescue_body(val[4],
                                        nil, nil, nil,
                                        nil, val[5])

                      method_body = @builder.begin_body(val[3], [ rescue_body ])

                      result = @builder.def_endless_method(*val[0],
                                 val[1], val[2], method_body)

                      @lexer.cmdarg.pop
                      @lexer.cond.pop
                      @static_env.unextend
                      @context.pop
                      @current_arg_stack.pop
                    
    result
end

def _reduce_253(val, _values, result)
                      result = @builder.def_endless_singleton(*val[0],
                                 val[1], val[2], val[3])

                      @lexer.cmdarg.pop
                      @lexer.cond.pop
                      @static_env.unextend
                      @context.pop
                      @current_arg_stack.pop
                    
    result
end

def _reduce_254(val, _values, result)
                      rescue_body = @builder.rescue_body(val[4],
                                        nil, nil, nil,
                                        nil, val[5])

                      method_body = @builder.begin_body(val[3], [ rescue_body ])

                      result = @builder.def_endless_singleton(*val[0],
                                 val[1], val[2], method_body)

                      @lexer.cmdarg.pop
                      @lexer.cond.pop
                      @static_env.unextend
                      @context.pop
                      @current_arg_stack.pop
                    
    result
end

# reduce 255 omitted

# reduce 256 omitted

# reduce 257 omitted

# reduce 258 omitted

# reduce 259 omitted

def _reduce_260(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])
                    
    result
end

def _reduce_261(val, _values, result)
                      result = @builder.binary_op(val[0], val[1], val[2])
                    
    result
end

# reduce 262 omitted

# reduce 263 omitted

# reduce 264 omitted

def _reduce_265(val, _values, result)
                      result = val[0] << @builder.associate(nil, val[2], nil)
                    
    result
end

def _reduce_266(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]
                    
    result
end

# reduce 267 omitted

def _reduce_268(val, _values, result)
                      rescue_body = @builder.rescue_body(val[1],
                                        nil, nil, nil,
                                        nil, val[2])

                      result = @builder.begin_body(val[0], [ rescue_body ])
                    
    result
end

def _reduce_269(val, _values, result)
                      result = val
                    
    result
end

def _reduce_270(val, _values, result)
                      unless @static_env.declared_forward_args?
                        diagnostic :error, :unexpected_token, { :token => 'tBDOT3' } , val[3]
                      end

                      result = [val[0], [*val[1], @builder.forwarded_args(val[3])], val[4]]
                    
    result
end

def _reduce_271(val, _values, result)
                      unless @static_env.declared_forward_args?
                        diagnostic :error, :unexpected_token, { :token => 'tBDOT3' } , val[1]
                      end

                      result = [val[0], [@builder.forwarded_args(val[1])], val[2]]
                    
    result
end

def _reduce_272(val, _values, result)
                      result = [ nil, [], nil ]
                    
    result
end

# reduce 273 omitted

def _reduce_274(val, _values, result)
                      result = []
                    
    result
end

# reduce 275 omitted

# reduce 276 omitted

def _reduce_277(val, _values, result)
                      result = val[0] << @builder.associate(nil, val[2], nil)
                    
    result
end

def _reduce_278(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]
                    
    result
end

def _reduce_279(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

def _reduce_280(val, _values, result)
                      result = val[0].concat(val[1])
                    
    result
end

def _reduce_281(val, _values, result)
                      result = [ @builder.associate(nil, val[0], nil) ]
                      result.concat(val[1])
                    
    result
end

def _reduce_282(val, _values, result)
                      assocs = @builder.associate(nil, val[2], nil)
                      result = val[0] << assocs
                      result.concat(val[3])
                    
    result
end

def _reduce_283(val, _values, result)
                      result =  [ val[0] ]
                    
    result
end

def _reduce_284(val, _values, result)
                      # When branch gets invoked by RACC's lookahead
                      # and command args start with '[' or '('
                      # we need to put `true` to the cmdarg stack
                      # **before** `false` pushed by lexer
                      #   m [], n
                      #     ^
                      # Right here we have cmdarg [...0] because
                      # lexer pushed it on '['
                      # We need to modify cmdarg stack to [...10]
                      #
                      # For all other cases (like `m n` or `m n, []`) we simply put 1 to the stack
                      # and later lexer pushes corresponding bits on top of it.
                      last_token = @last_token[0]
                      lookahead = last_token == :tLBRACK || last_token == :tLPAREN_ARG

                      if lookahead
                        top = @lexer.cmdarg.pop
                        @lexer.cmdarg.push(true)
                        @lexer.cmdarg.push(top)
                      else
                        @lexer.cmdarg.push(true)
                      end
                    
    result
end

def _reduce_285(val, _values, result)
                      # call_args can be followed by tLBRACE_ARG (that does cmdarg.push(0) in the lexer)
                      # but the push must be done after cmdarg.pop() in the parser.
                      # So this code does cmdarg.pop() to pop 0 pushed by tLBRACE_ARG,
                      # cmdarg.pop() to pop 1 pushed by command_args,
                      # and cmdarg.push(0) to restore back the flag set by tLBRACE_ARG.
                      last_token = @last_token[0]
                      lookahead = last_token == :tLBRACE_ARG
                      if lookahead
                        top = @lexer.cmdarg.pop
                        @lexer.cmdarg.pop
                        @lexer.cmdarg.push(top)
                      else
                        @lexer.cmdarg.pop
                      end

                      result = val[1]
                    
    result
end

def _reduce_286(val, _values, result)
                      result = @builder.block_pass(val[0], val[1])
                    
    result
end

def _reduce_287(val, _values, result)
                      result = [ val[1] ]
                    
    result
end

def _reduce_288(val, _values, result)
                      result = []
                    
    result
end

def _reduce_289(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

def _reduce_290(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]
                    
    result
end

def _reduce_291(val, _values, result)
                      result = val[0] << val[2]
                    
    result
end

def _reduce_292(val, _values, result)
                      result = val[0] << @builder.splat(val[2], val[3])
                    
    result
end

def _reduce_293(val, _values, result)
                      result = @builder.array(nil, val[0], nil)
                    
    result
end

# reduce 294 omitted

def _reduce_295(val, _values, result)
                      result = val[0] << val[2]
                    
    result
end

def _reduce_296(val, _values, result)
                      result = val[0] << @builder.splat(val[2], val[3])
                    
    result
end

def _reduce_297(val, _values, result)
                      result = [ @builder.splat(val[0], val[1]) ]
                    
    result
end

# reduce 298 omitted

# reduce 299 omitted

# reduce 300 omitted

# reduce 301 omitted

# reduce 302 omitted

# reduce 303 omitted

# reduce 304 omitted

# reduce 305 omitted

# reduce 306 omitted

# reduce 307 omitted

def _reduce_308(val, _values, result)
                      result = @builder.call_method(nil, nil, val[0])
                    
    result
end

def _reduce_309(val, _values, result)
                      @lexer.cmdarg.push(false)
                    
    result
end

def _reduce_310(val, _values, result)
                      @lexer.cmdarg.pop

                      result = @builder.begin_keyword(val[0], val[2], val[3])
                    
    result
end

def _reduce_311(val, _values, result)
                      @lexer.state = :expr_endarg
                    
    result
end

def _reduce_312(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[3])
                    
    result
end

def _reduce_313(val, _values, result)
                      @lexer.state = :expr_endarg
                    
    result
end

def _reduce_314(val, _values, result)
                      result = @builder.begin(val[0], nil, val[3])
                    
    result
end

def _reduce_315(val, _values, result)
                      result = @builder.begin(val[0], val[1], val[2])
                    
    result
end

def _reduce_316(val, _values, result)
                      result = @builder.const_fetch(val[0], val[1], val[2])
                    
    result
end

def _reduce_317(val, _values, result)
                      result = @builder.const_global(val[0], val[1])
                    
    result
end

def _reduce_318(val, _values, result)
                      result = @builder.array(val[0], val[1], val[2])
                    
    result
end

def _reduce_319(val, _values, result)
                      result = @builder.associate(val[0], val[1], val[2])
                    
    result
end

def _reduce_320(val, _values, result)
                      result = @builder.keyword_cmd(:return, val[0])
                    
    result
end

def _reduce_321(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0], val[1], val[2], val[3])
                    
    result
end

def _reduce_322(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0], val[1], [], val[2])
                    
    result
end

def _reduce_323(val, _values, result)
                      result = @builder.keyword_cmd(:yield, val[0])
                    
    result
end

def _reduce_324(val, _values, result)
                      result = @builder.keyword_cmd(:defined?, val[0],
                                                    val[2], [ val[3] ], val[4])
                    
    result
end

def _reduce_325(val, _values, result)
                      result = @builder.not_op(val[0], val[1], val[2], val[3])
                    
    result
end

def _reduce_326(val, _values, result)
                      result = @builder.not_op(val[0], val[1], nil, val[2])
                    
    result
end

def _reduce_327(val, _values, result)
                      method_call = @builder.call_method(nil, nil, val[0])

                      begin_t, args, body, end_t = val[1]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)
                    
    result
end

# reduce 328 omitted

def _reduce_329(val, _values, result)
                      begin_t, args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, args, body, end_t)
                    
    result
end

# reduce 330 omitted

def _reduce_331(val, _values, result)
                      else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  val[3], else_t,
                                                  else_,  val[5])
                    
    result
end

def _reduce_332(val, _values, result)
                      else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  else_,  else_t,
                                                  val[3], val[5])
                    
    result
end

def _reduce_333(val, _values, result)
                      result = @builder.loop(:while, val[0], *val[1], val[2], val[3])
                    
    result
end

def _reduce_334(val, _values, result)
                      result = @builder.loop(:until, val[0], *val[1], val[2], val[3])
                    
    result
end

def _reduce_335(val, _values, result)
                      *when_bodies, (else_t, else_body) = *val[3]

                      result = @builder.case(val[0], val[1],
                                             when_bodies, else_t, else_body,
                                             val[4])
                    
    result
end

def _reduce_336(val, _values, result)
                      *when_bodies, (else_t, else_body) = *val[2]

                      result = @builder.case(val[0], nil,
                                             when_bodies, else_t, else_body,
                                             val[3])
                    
    result
end

def _reduce_337(val, _values, result)
                      *in_bodies, (else_t, else_body) = *val[3]

                      result = @builder.case_match(val[0], val[1],
                                             in_bodies, else_t, else_body,
                                             val[4])
                    
    result
end

def _reduce_338(val, _values, result)
                      result = @builder.for(val[0], val[1], val[2], *val[3], val[4], val[5])
                    
    result
end

def _reduce_339(val, _values, result)
                      @static_env.extend_static
                      @lexer.cmdarg.push(false)
                      @lexer.cond.push(false)
                      @context.push(:class)
                    
    result
end

def _reduce_340(val, _values, result)
                      unless @context.class_definition_allowed?
                        diagnostic :error, :class_in_def, nil, val[0]
                      end

                      lt_t, superclass = val[2]
                      result = @builder.def_class(val[0], val[1],
                                                  lt_t, superclass,
                                                  val[4], val[5])

                      @lexer.cmdarg.pop
                      @lexer.cond.pop
                      @static_env.unextend
                      @context.pop
                    
    result
end

def _reduce_341(val, _values, result)
                      @static_env.extend_static
                      @lexer.cmdarg.push(false)
                      @lexer.cond.push(false)
                      @context.push(:sclass)
                    
    result
end

def _reduce_342(val, _values, result)
                      result = @builder.def_sclass(val[0], val[1], val[2],
                                                   val[5], val[6])

                      @lexer.cmdarg.pop
                      @lexer.cond.pop
                      @static_env.unextend
                      @context.pop
                    
    result
end

def _reduce_343(val, _values, result)
                      @static_env.extend_static
                      @lexer.cmdarg.push(false)
                      @context.push(:module)
                    
    result
end

def _reduce_344(val, _values, result)
                      unless @context.module_definition_allowed?
                        diagnostic :error, :module_in_def, nil, val[0]
                      end

                      result = @builder.def_module(val[0], val[1],
                                                   val[3], val[4])

                      @lexer.cmdarg.pop
                      @static_env.unextend
                      @context.pop
                    
    result
end

def _reduce_345(val, _values, result)
                      result = @builder.def_method(*val[0], val[1],
                                  val[2], val[3])

                      @lexer.cmdarg.pop
                      @lexer.cond.pop
                      @static_env.unextend
                      @context.pop
                      @current_arg_stack.pop
                    
    result
end

def _reduce_346(val, _values, result)
                      result = @builder.def_singleton(*val[0], val[1],
                                  val[2], val[3])

                      @lexer.cmdarg.pop
                      @lexer.cond.pop
                      @static_env.unextend
                      @context.pop
                      @current_arg_stack.pop
                    
    result
end

def _reduce_347(val, _values, result)
                      result = @builder.keyword_cmd(:break, val[0])
                    
    result
end

def _reduce_348(val, _values, result)
                      result = @builder.keyword_cmd(:next, val[0])
                    
    result
end

def _reduce_349(val, _values, result)
                      result = @builder.keyword_cmd(:redo, val[0])
                    
    result
end

def _reduce_350(val, _values, result)
                      result = @builder.keyword_cmd(:retry, val[0])
                    
    result
end

# reduce 351 omitted

def _reduce_352(val, _values, result)
                      if @context.in_class?
                        diagnostic :error, :invalid_return, nil, val[0]
                      end
                    
    result
end

# reduce 353 omitted

# reduce 354 omitted

def _reduce_355(val, _values, result)
                      result = val[1]
                    
    result
end

# reduce 356 omitted

# reduce 357 omitted

# reduce 358 omitted

def _reduce_359(val, _values, result)
                      else_t, else_ = val[4]
                      result = [ val[0],
                                 @builder.condition(val[0], val[1], val[2],
                                                    val[3], else_t,
                                                    else_,  nil),
                               ]
                    
    result
end

# reduce 360 omitted

def _reduce_361(val, _values, result)
                      result = val
                    
    result
end

# reduce 362 omitted

# reduce 363 omitted

def _reduce_364(val, _values, result)
                      result = @builder.arg(val[0])
                    
    result
end

def _reduce_365(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])
                    
    result
end

def _reduce_366(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

def _reduce_367(val, _values, result)
                      result = val[0] << val[2]
                    
    result
end

# reduce 368 omitted

def _reduce_369(val, _values, result)
                      result = val[0].
                                  push(val[2])
                    
    result
end

def _reduce_370(val, _values, result)
                      result = val[0].
                                  push(val[2]).
                                  concat(val[4])
                    
    result
end

def _reduce_371(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

def _reduce_372(val, _values, result)
                      result = [ val[0], *val[2] ]
                    
    result
end

def _reduce_373(val, _values, result)
                      result = @builder.restarg(val[0], val[1])
                    
    result
end

def _reduce_374(val, _values, result)
                      result = @builder.restarg(val[0])
                    
    result
end

# reduce 375 omitted

# reduce 376 omitted

def _reduce_377(val, _values, result)
                      result = val[0].concat(val[2]).concat(val[3])
                    
    result
end

def _reduce_378(val, _values, result)
                      result = val[0].concat(val[1])
                    
    result
end

def _reduce_379(val, _values, result)
                      result = val[0].concat(val[1])
                    
    result
end

def _reduce_380(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

def _reduce_381(val, _values, result)
                      result = val[1]
                    
    result
end

def _reduce_382(val, _values, result)
                      result = []
                    
    result
end

# reduce 383 omitted

def _reduce_384(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])
                    
    result
end

def _reduce_385(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[6]).
                                  concat(val[7])
                    
    result
end

def _reduce_386(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])
                    
    result
end

def _reduce_387(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])
                    
    result
end

def _reduce_388(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])
                    
    result
end

# reduce 389 omitted

def _reduce_390(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])
                    
    result
end

def _reduce_391(val, _values, result)
                      if val[1].empty? && val[0].size == 1
                        result = [@builder.procarg0(val[0][0])]
                      else
                        result = val[0].concat(val[1])
                      end
                    
    result
end

def _reduce_392(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])
                    
    result
end

def _reduce_393(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])
                    
    result
end

def _reduce_394(val, _values, result)
                      result = val[0].
                                  concat(val[1])
                    
    result
end

def _reduce_395(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])
                    
    result
end

def _reduce_396(val, _values, result)
                      result = val[0].
                                  concat(val[1])
                    
    result
end

def _reduce_397(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])
                    
    result
end

# reduce 398 omitted

def _reduce_399(val, _values, result)
                      result = @builder.args(nil, [], nil)
                    
    result
end

def _reduce_400(val, _values, result)
                      @lexer.state = :expr_value
                    
    result
end

def _reduce_401(val, _values, result)
                      @max_numparam_stack.has_ordinary_params!
                      @current_arg_stack.set(nil)
                      result = @builder.args(val[0], val[1], val[2])
                    
    result
end

def _reduce_402(val, _values, result)
                      @max_numparam_stack.has_ordinary_params!
                      @current_arg_stack.set(nil)
                      result = @builder.args(val[0], val[1].concat(val[2]), val[3])
                    
    result
end

def _reduce_403(val, _values, result)
                      result = []
                    
    result
end

def _reduce_404(val, _values, result)
                      result = val[2]
                    
    result
end

def _reduce_405(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

def _reduce_406(val, _values, result)
                      result = val[0] << val[2]
                    
    result
end

def _reduce_407(val, _values, result)
                      @static_env.declare val[0][0]
                      result = @builder.shadowarg(val[0])
                    
    result
end

# reduce 408 omitted

def _reduce_409(val, _values, result)
                      @static_env.extend_dynamic
                      @max_numparam_stack.push
                      @context.push(:lambda)
                    
    result
end

def _reduce_410(val, _values, result)
                      @context.pop
                      @lexer.cmdarg.push(false)
                    
    result
end

def _reduce_411(val, _values, result)
                      lambda_call = @builder.call_lambda(val[0])
                      args = @max_numparam_stack.has_numparams? ? @builder.numargs(@max_numparam_stack.top) : val[2]
                      begin_t, body, end_t = val[4]

                      @max_numparam_stack.pop
                      @static_env.unextend
                      @lexer.cmdarg.pop

                      result      = @builder.block(lambda_call,
                                      begin_t, args, body, end_t)
                    
    result
end

def _reduce_412(val, _values, result)
                      @max_numparam_stack.has_ordinary_params!
                      result = @builder.args(val[0], val[1].concat(val[2]), val[3])
                    
    result
end

def _reduce_413(val, _values, result)
                      if val[0].any?
                        @max_numparam_stack.has_ordinary_params!
                      end
                      result = @builder.args(nil, val[0], nil)
                    
    result
end

def _reduce_414(val, _values, result)
                      @context.push(:lambda)
                    
    result
end

def _reduce_415(val, _values, result)
                      result = [ val[0], val[2], val[3] ]
                      @context.pop
                    
    result
end

def _reduce_416(val, _values, result)
                      @context.push(:lambda)
                    
    result
end

def _reduce_417(val, _values, result)
                      result = [ val[0], val[2], val[3] ]
                      @context.pop
                    
    result
end

def _reduce_418(val, _values, result)
                      @context.push(:block)
                    
    result
end

def _reduce_419(val, _values, result)
                      result = [ val[0], *val[2], val[3] ]
                      @context.pop
                    
    result
end

def _reduce_420(val, _values, result)
                      begin_t, block_args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, block_args, body, end_t)
                    
    result
end

def _reduce_421(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)
                    
    result
end

def _reduce_422(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                      lparen_t, args, rparen_t)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)
                    
    result
end

def _reduce_423(val, _values, result)
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                      nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)
                    
    result
end

def _reduce_424(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.call_method(nil, nil, val[0],
                                  lparen_t, args, rparen_t)
                    
    result
end

def _reduce_425(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)
                    
    result
end

def _reduce_426(val, _values, result)
                      lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)
                    
    result
end

def _reduce_427(val, _values, result)
                      result = @builder.call_method(val[0], val[1], val[2])
                    
    result
end

def _reduce_428(val, _values, result)
                      lparen_t, args, rparen_t = val[2]
                      result = @builder.call_method(val[0], val[1], nil,
                                  lparen_t, args, rparen_t)
                    
    result
end

def _reduce_429(val, _values, result)
                      lparen_t, args, rparen_t = val[2]
                      result = @builder.call_method(val[0], val[1], nil,
                                  lparen_t, args, rparen_t)
                    
    result
end

def _reduce_430(val, _values, result)
                      lparen_t, args, rparen_t = val[1]
                      result = @builder.keyword_cmd(:super, val[0],
                                  lparen_t, args, rparen_t)
                    
    result
end

def _reduce_431(val, _values, result)
                      result = @builder.keyword_cmd(:zsuper, val[0])
                    
    result
end

def _reduce_432(val, _values, result)
                      result = @builder.index(val[0], val[1], val[2], val[3])
                    
    result
end

def _reduce_433(val, _values, result)
                      @context.push(:block)
                    
    result
end

def _reduce_434(val, _values, result)
                      result = [ val[0], *val[2], val[3] ]
                      @context.pop
                    
    result
end

def _reduce_435(val, _values, result)
                      @context.push(:block)
                    
    result
end

def _reduce_436(val, _values, result)
                      result = [ val[0], *val[2], val[3] ]
                      @context.pop
                    
    result
end

def _reduce_437(val, _values, result)
                      @static_env.extend_dynamic
                      @max_numparam_stack.push
                    
    result
end

def _reduce_438(val, _values, result)
                      args = @max_numparam_stack.has_numparams? ? @builder.numargs(@max_numparam_stack.top) : val[1]
                      result = [ args, val[2] ]

                      @max_numparam_stack.pop
                      @static_env.unextend
                    
    result
end

def _reduce_439(val, _values, result)
                      @static_env.extend_dynamic
                      @max_numparam_stack.push
                    
    result
end

def _reduce_440(val, _values, result)
                      @lexer.cmdarg.push(false)
                    
    result
end

def _reduce_441(val, _values, result)
                      args = @max_numparam_stack.has_numparams? ? @builder.numargs(@max_numparam_stack.top) : val[2]
                      result = [ args, val[3] ]

                      @max_numparam_stack.pop
                      @static_env.unextend
                      @lexer.cmdarg.pop
                    
    result
end

def _reduce_442(val, _values, result)
                      result = [ @builder.when(val[0], val[1], val[2], val[3]),
                                 *val[4] ]
                    
    result
end

def _reduce_443(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

# reduce 444 omitted

def _reduce_445(val, _values, result)
                      @lexer.state = :expr_beg
                      @lexer.command_start = false
                      @pattern_variables.push
                      @pattern_hash_keys.push

                      result = @lexer.in_kwarg
                      @lexer.in_kwarg = true
                    
    result
end

def _reduce_446(val, _values, result)
                      @lexer.in_kwarg = val[1]
                    
    result
end

def _reduce_447(val, _values, result)
                      result = [ @builder.in_pattern(val[0], *val[2], val[3], val[5]),
                                 *val[6] ]
                    
    result
end

def _reduce_448(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

# reduce 449 omitted

def _reduce_450(val, _values, result)
                      result = [ val[0], nil ]
                    
    result
end

def _reduce_451(val, _values, result)
                      result = [ val[0], @builder.if_guard(val[1], val[2]) ]
                    
    result
end

def _reduce_452(val, _values, result)
                      result = [ val[0], @builder.unless_guard(val[1], val[2]) ]
                    
    result
end

# reduce 453 omitted

def _reduce_454(val, _values, result)
                      # array patterns that end with comma
                      # like 1, 2,
                      # must be emitted as `array_pattern_with_tail`
                      item = @builder.match_with_trailing_comma(val[0], val[1])
                      result = @builder.array_pattern(nil, [ item ], nil)
                    
    result
end

def _reduce_455(val, _values, result)
                      result = @builder.array_pattern(nil, [val[0]].concat(val[2]), nil)
                    
    result
end

def _reduce_456(val, _values, result)
                      result = @builder.find_pattern(nil, val[0], nil)
                    
    result
end

def _reduce_457(val, _values, result)
                      result = @builder.array_pattern(nil, val[0], nil)
                    
    result
end

def _reduce_458(val, _values, result)
                      result = @builder.hash_pattern(nil, val[0], nil)
                    
    result
end

# reduce 459 omitted

def _reduce_460(val, _values, result)
                      result = @builder.match_as(val[0], val[1], val[2])
                    
    result
end

# reduce 461 omitted

def _reduce_462(val, _values, result)
                      result = @builder.match_alt(val[0], val[1], val[2])
                    
    result
end

# reduce 463 omitted

def _reduce_464(val, _values, result)
                      result = val[0]
                      @pattern_hash_keys.push
                    
    result
end

def _reduce_465(val, _values, result)
                      result = val[0]
                      @pattern_hash_keys.push
                    
    result
end

# reduce 466 omitted

def _reduce_467(val, _values, result)
                      @pattern_hash_keys.pop
                      pattern = @builder.array_pattern(nil, val[2], nil)
                      result = @builder.const_pattern(val[0], val[1], pattern, val[3])
                    
    result
end

def _reduce_468(val, _values, result)
                      @pattern_hash_keys.pop
                      pattern = @builder.find_pattern(nil, val[2], nil)
                      result = @builder.const_pattern(val[0], val[1], pattern, val[3])
                    
    result
end

def _reduce_469(val, _values, result)
                      @pattern_hash_keys.pop
                      pattern = @builder.hash_pattern(nil, val[2], nil)
                      result = @builder.const_pattern(val[0], val[1], pattern, val[3])
                    
    result
end

def _reduce_470(val, _values, result)
                      pattern = @builder.array_pattern(val[1], nil, val[2])
                      result = @builder.const_pattern(val[0], val[1], pattern, val[2])
                    
    result
end

def _reduce_471(val, _values, result)
                      @pattern_hash_keys.pop
                      pattern = @builder.array_pattern(nil, val[2], nil)
                      result = @builder.const_pattern(val[0], val[1], pattern, val[3])
                    
    result
end

def _reduce_472(val, _values, result)
                      @pattern_hash_keys.pop
                      pattern = @builder.find_pattern(nil, val[2], nil)
                      result = @builder.const_pattern(val[0], val[1], pattern, val[3])
                    
    result
end

def _reduce_473(val, _values, result)
                      @pattern_hash_keys.pop
                      pattern = @builder.hash_pattern(nil, val[2], nil)
                      result = @builder.const_pattern(val[0], val[1], pattern, val[3])
                    
    result
end

def _reduce_474(val, _values, result)
                      pattern = @builder.array_pattern(val[1], nil, val[2])
                      result = @builder.const_pattern(val[0], val[1], pattern, val[2])
                    
    result
end

def _reduce_475(val, _values, result)
                      result = @builder.array_pattern(val[0], val[1], val[2])
                    
    result
end

def _reduce_476(val, _values, result)
                      result = @builder.find_pattern(val[0], val[1], val[2])
                    
    result
end

def _reduce_477(val, _values, result)
                      result = @builder.array_pattern(val[0], [], val[1])
                    
    result
end

def _reduce_478(val, _values, result)
                      @pattern_hash_keys.push
                      result = @lexer.in_kwarg
                      @lexer.in_kwarg = false
                    
    result
end

def _reduce_479(val, _values, result)
                      @pattern_hash_keys.pop
                      @lexer.in_kwarg = val[1]
                      result = @builder.hash_pattern(val[0], val[2], val[3])
                    
    result
end

def _reduce_480(val, _values, result)
                      result = @builder.hash_pattern(val[0], [], val[1])
                    
    result
end

def _reduce_481(val, _values, result)
                      @pattern_hash_keys.push
                    
    result
end

def _reduce_482(val, _values, result)
                      @pattern_hash_keys.pop
                      result = @builder.begin(val[0], val[2], val[3])
                    
    result
end

def _reduce_483(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

def _reduce_484(val, _values, result)
                      result = val[0]
                    
    result
end

def _reduce_485(val, _values, result)
                      result = [ *val[0], val[1] ]
                    
    result
end

def _reduce_486(val, _values, result)
                      match_rest = @builder.match_rest(val[1], val[2])
                      result = [ *val[0], match_rest ]
                    
    result
end

def _reduce_487(val, _values, result)
                      match_rest = @builder.match_rest(val[1], val[2])
                      result = [ *val[0], match_rest, *val[4] ]
                    
    result
end

def _reduce_488(val, _values, result)
                      result = [ *val[0], @builder.match_rest(val[1]) ]
                    
    result
end

def _reduce_489(val, _values, result)
                      result = [ *val[0], @builder.match_rest(val[1]), *val[3] ]
                    
    result
end

# reduce 490 omitted

def _reduce_491(val, _values, result)
                      # array patterns that end with comma
                      # like [1, 2,]
                      # must be emitted as `array_pattern_with_tail`
                      item = @builder.match_with_trailing_comma(val[0], val[1])
                      result = [ item ]
                    
    result
end

def _reduce_492(val, _values, result)
                      # array patterns that end with comma
                      # like [1, 2,]
                      # must be emitted as `array_pattern_with_tail`
                      last_item = @builder.match_with_trailing_comma(val[1], val[2])
                      result = [ *val[0], last_item ]
                    
    result
end

def _reduce_493(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

def _reduce_494(val, _values, result)
                      result = [ val[0], *val[2] ]
                    
    result
end

def _reduce_495(val, _values, result)
                      result = [ val[0], *val[2], val[4] ]
                    
    result
end

def _reduce_496(val, _values, result)
                      result = @builder.match_rest(val[0], val[1])
                    
    result
end

def _reduce_497(val, _values, result)
                      result = @builder.match_rest(val[0])
                    
    result
end

def _reduce_498(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

def _reduce_499(val, _values, result)
                      result = [ *val[0], val[2] ]
                    
    result
end

# reduce 500 omitted

def _reduce_501(val, _values, result)
                      result = [ *val[0], *val[2] ]
                    
    result
end

def _reduce_502(val, _values, result)
                      result = val[0]
                    
    result
end

def _reduce_503(val, _values, result)
                      result = val[0]
                    
    result
end

def _reduce_504(val, _values, result)
                      result = val[0]
                    
    result
end

def _reduce_505(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

def _reduce_506(val, _values, result)
                      result = [ *val[0], val[2] ]
                    
    result
end

def _reduce_507(val, _values, result)
                      result = @builder.match_pair(*val[0], val[1])
                    
    result
end

def _reduce_508(val, _values, result)
                      result = @builder.match_label(*val[0])
                    
    result
end

def _reduce_509(val, _values, result)
                    check_kwarg_name(val[0])
                    result = [:label, val[0]]
                  
    result
end

def _reduce_510(val, _values, result)
                    result = [:quoted, [val[0], val[1], val[2]]]
                  
    result
end

def _reduce_511(val, _values, result)
                      result = [ @builder.match_rest(val[0], val[1]) ]
                    
    result
end

def _reduce_512(val, _values, result)
                      result = [ @builder.match_rest(val[0], nil) ]
                    
    result
end

def _reduce_513(val, _values, result)
                      result = [ @builder.match_nil_pattern(val[0], val[1]) ]
                    
    result
end

# reduce 514 omitted

# reduce 515 omitted

# reduce 516 omitted

def _reduce_517(val, _values, result)
                      result = @builder.range_inclusive(val[0], val[1], val[2])
                    
    result
end

def _reduce_518(val, _values, result)
                      result = @builder.range_exclusive(val[0], val[1], val[2])
                    
    result
end

def _reduce_519(val, _values, result)
                      result = @builder.range_inclusive(val[0], val[1], nil)
                    
    result
end

def _reduce_520(val, _values, result)
                      result = @builder.range_exclusive(val[0], val[1], nil)
                    
    result
end

# reduce 521 omitted

# reduce 522 omitted

# reduce 523 omitted

def _reduce_524(val, _values, result)
                      result = @builder.range_inclusive(nil, val[0], val[1])
                    
    result
end

def _reduce_525(val, _values, result)
                      result = @builder.range_exclusive(nil, val[0], val[1])
                    
    result
end

# reduce 526 omitted

# reduce 527 omitted

# reduce 528 omitted

# reduce 529 omitted

# reduce 530 omitted

# reduce 531 omitted

# reduce 532 omitted

# reduce 533 omitted

def _reduce_534(val, _values, result)
                      result = @builder.accessible(val[0])
                    
    result
end

# reduce 535 omitted

def _reduce_536(val, _values, result)
                      result = @builder.match_var(val[0])
                    
    result
end

def _reduce_537(val, _values, result)
                      name = val[1][0]
                      unless static_env.declared?(name)
                        diagnostic :error, :undefined_lvar, { :name => name }, val[1]
                      end

                      lvar = @builder.accessible(@builder.ident(val[1]))
                      result = @builder.pin(val[0], lvar)
                    
    result
end

def _reduce_538(val, _values, result)
                      result = @builder.const_global(val[0], val[1])
                    
    result
end

def _reduce_539(val, _values, result)
                      result = @builder.const_fetch(val[0], val[1], val[2])
                    
    result
end

def _reduce_540(val, _values, result)
                      result = @builder.const(val[0])
                   
    result
end

def _reduce_541(val, _values, result)
                      assoc_t, exc_var = val[2]

                      if val[1]
                        exc_list = @builder.array(nil, val[1], nil)
                      end

                      result = [ @builder.rescue_body(val[0],
                                      exc_list, assoc_t, exc_var,
                                      val[3], val[4]),
                                 *val[5] ]
                    
    result
end

def _reduce_542(val, _values, result)
                      result = []
                    
    result
end

def _reduce_543(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

# reduce 544 omitted

# reduce 545 omitted

def _reduce_546(val, _values, result)
                      result = [ val[0], val[1] ]
                    
    result
end

# reduce 547 omitted

def _reduce_548(val, _values, result)
                      result = [ val[0], val[1] ]
                    
    result
end

# reduce 549 omitted

# reduce 550 omitted

# reduce 551 omitted

def _reduce_552(val, _values, result)
                      result = @builder.string_compose(nil, val[0], nil)
                    
    result
end

def _reduce_553(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

def _reduce_554(val, _values, result)
                      result = val[0] << val[1]
                    
    result
end

def _reduce_555(val, _values, result)
                      string = @builder.string_compose(val[0], val[1], val[2])
                      result = @builder.dedent_string(string, @lexer.dedent_level)
                    
    result
end

def _reduce_556(val, _values, result)
                      string = @builder.string(val[0])
                      result = @builder.dedent_string(string, @lexer.dedent_level)
                    
    result
end

def _reduce_557(val, _values, result)
                      result = @builder.character(val[0])
                    
    result
end

def _reduce_558(val, _values, result)
                      string = @builder.xstring_compose(val[0], val[1], val[2])
                      result = @builder.dedent_string(string, @lexer.dedent_level)
                    
    result
end

def _reduce_559(val, _values, result)
                      opts   = @builder.regexp_options(val[3])
                      result = @builder.regexp_compose(val[0], val[1], val[2], opts)
                    
    result
end

def _reduce_560(val, _values, result)
                      result = @builder.words_compose(val[0], val[1], val[2])
                    
    result
end

def _reduce_561(val, _values, result)
                      result = []
                    
    result
end

def _reduce_562(val, _values, result)
                      result = val[0] << @builder.word(val[1])
                    
    result
end

def _reduce_563(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

def _reduce_564(val, _values, result)
                      result = val[0] << val[1]
                    
    result
end

def _reduce_565(val, _values, result)
                      result = @builder.symbols_compose(val[0], val[1], val[2])
                    
    result
end

def _reduce_566(val, _values, result)
                      result = []
                    
    result
end

def _reduce_567(val, _values, result)
                      result = val[0] << @builder.word(val[1])
                    
    result
end

def _reduce_568(val, _values, result)
                      result = @builder.words_compose(val[0], val[1], val[2])
                    
    result
end

def _reduce_569(val, _values, result)
                      result = @builder.symbols_compose(val[0], val[1], val[2])
                    
    result
end

def _reduce_570(val, _values, result)
                      result = []
                    
    result
end

def _reduce_571(val, _values, result)
                      result = val[0] << @builder.string_internal(val[1])
                    
    result
end

def _reduce_572(val, _values, result)
                      result = []
                    
    result
end

def _reduce_573(val, _values, result)
                      result = val[0] << @builder.symbol_internal(val[1])
                    
    result
end

def _reduce_574(val, _values, result)
                      result = []
                    
    result
end

def _reduce_575(val, _values, result)
                      result = val[0] << val[1]
                    
    result
end

def _reduce_576(val, _values, result)
                      result = []
                    
    result
end

def _reduce_577(val, _values, result)
                      result = val[0] << val[1]
                    
    result
end

def _reduce_578(val, _values, result)
                      result = []
                    
    result
end

def _reduce_579(val, _values, result)
                      result = val[0] << val[1]
                    
    result
end

def _reduce_580(val, _values, result)
                      result = @builder.string_internal(val[0])
                    
    result
end

def _reduce_581(val, _values, result)
                      result = val[1]
                    
    result
end

def _reduce_582(val, _values, result)
                      @lexer.cmdarg.push(false)
                      @lexer.cond.push(false)
                    
    result
end

def _reduce_583(val, _values, result)
                      @lexer.cmdarg.pop
                      @lexer.cond.pop

                      result = @builder.begin(val[0], val[2], val[3])
                    
    result
end

def _reduce_584(val, _values, result)
                      result = @builder.gvar(val[0])
                    
    result
end

def _reduce_585(val, _values, result)
                      result = @builder.ivar(val[0])
                    
    result
end

def _reduce_586(val, _values, result)
                      result = @builder.cvar(val[0])
                    
    result
end

# reduce 587 omitted

# reduce 588 omitted

# reduce 589 omitted

def _reduce_590(val, _values, result)
                      @lexer.state = :expr_end
                      result = @builder.symbol(val[0])
                    
    result
end

def _reduce_591(val, _values, result)
                      @lexer.state = :expr_end
                      result = @builder.symbol_compose(val[0], val[1], val[2])
                    
    result
end

def _reduce_592(val, _values, result)
                      result = val[0]
                    
    result
end

def _reduce_593(val, _values, result)
                      if @builder.respond_to? :negate
                        # AST builder interface compatibility
                        result = @builder.negate(val[0], val[1])
                      else
                        result = @builder.unary_num(val[0], val[1])
                      end
                    
    result
end

def _reduce_594(val, _values, result)
                      @lexer.state = :expr_end
                      result = @builder.integer(val[0])
                    
    result
end

def _reduce_595(val, _values, result)
                      @lexer.state = :expr_end
                      result = @builder.float(val[0])
                    
    result
end

def _reduce_596(val, _values, result)
                      @lexer.state = :expr_end
                      result = @builder.rational(val[0])
                    
    result
end

def _reduce_597(val, _values, result)
                      @lexer.state = :expr_end
                      result = @builder.complex(val[0])
                    
    result
end

def _reduce_598(val, _values, result)
                      result = @builder.ident(val[0])
                    
    result
end

def _reduce_599(val, _values, result)
                      result = @builder.ivar(val[0])
                    
    result
end

def _reduce_600(val, _values, result)
                      result = @builder.gvar(val[0])
                    
    result
end

def _reduce_601(val, _values, result)
                      result = @builder.const(val[0])
                    
    result
end

def _reduce_602(val, _values, result)
                      result = @builder.cvar(val[0])
                    
    result
end

def _reduce_603(val, _values, result)
                      result = @builder.nil(val[0])
                    
    result
end

def _reduce_604(val, _values, result)
                      result = @builder.self(val[0])
                    
    result
end

def _reduce_605(val, _values, result)
                      result = @builder.true(val[0])
                    
    result
end

def _reduce_606(val, _values, result)
                      result = @builder.false(val[0])
                    
    result
end

def _reduce_607(val, _values, result)
                      result = @builder.__FILE__(val[0])
                    
    result
end

def _reduce_608(val, _values, result)
                      result = @builder.__LINE__(val[0])
                    
    result
end

def _reduce_609(val, _values, result)
                      result = @builder.__ENCODING__(val[0])
                    
    result
end

def _reduce_610(val, _values, result)
                      if (node = val[0]) && node.type == :ident
                        name = node.children[0]

                        if name =~ /\A_[1-9]\z/ && !static_env.declared?(name) && context.in_dynamic_block?
                          # definitely an implicit param
                          location = node.loc.expression

                          if max_numparam_stack.has_ordinary_params?
                            diagnostic :error, :ordinary_param_defined, nil, [nil, location]
                          end

                          raw_context = context.stack.dup
                          raw_max_numparam_stack = max_numparam_stack.stack.dup

                          # ignore current block scope
                          raw_context.pop
                          raw_max_numparam_stack.pop

                          raw_context.reverse_each do |outer_scope|
                            if outer_scope == :block || outer_scope == :lambda
                              outer_scope_has_numparams = raw_max_numparam_stack.pop > 0

                              if outer_scope_has_numparams
                                diagnostic :error, :numparam_used_in_outer_scope, nil, [nil, location]
                              else
                                # for now it's ok, but an outer scope can also be a block
                                # with numparams, so we need to continue
                              end
                            else
                              # found an outer scope that can't have numparams
                              # like def/class/etc
                              break
                            end
                          end

                          static_env.declare(name)
                          max_numparam_stack.register(name[1].to_i)
                        end
                      end

                      result = @builder.accessible(val[0])
                    
    result
end

def _reduce_611(val, _values, result)
                      result = @builder.accessible(val[0])
                    
    result
end

def _reduce_612(val, _values, result)
                      result = @builder.assignable(val[0])
                    
    result
end

def _reduce_613(val, _values, result)
                      result = @builder.assignable(val[0])
                    
    result
end

def _reduce_614(val, _values, result)
                      result = @builder.nth_ref(val[0])
                    
    result
end

def _reduce_615(val, _values, result)
                      result = @builder.back_ref(val[0])
                    
    result
end

def _reduce_616(val, _values, result)
                      @lexer.state = :expr_value
                    
    result
end

def _reduce_617(val, _values, result)
                      result = [ val[0], val[2] ]
                    
    result
end

def _reduce_618(val, _values, result)
                      result = nil
                    
    result
end

def _reduce_619(val, _values, result)
                      result = @builder.args(val[0], val[1], val[2])

                      @lexer.state = :expr_value
                    
    result
end

def _reduce_620(val, _values, result)
                      args = [ *val[1], @builder.forward_arg(val[3]) ]
                      result = @builder.args(val[0], args, val[4])

                      @static_env.declare_forward_args
                    
    result
end

def _reduce_621(val, _values, result)
                      result = @builder.forward_only_args(val[0], val[1], val[2])
                      @static_env.declare_forward_args

                      @lexer.state = :expr_value
                    
    result
end

# reduce 622 omitted

def _reduce_623(val, _values, result)
                      result = @lexer.in_kwarg
                      @lexer.in_kwarg = true
                    
    result
end

def _reduce_624(val, _values, result)
                      @lexer.in_kwarg = val[0]
                      result = @builder.args(nil, val[1], nil)
                    
    result
end

def _reduce_625(val, _values, result)
                      result = val[0].concat(val[2]).concat(val[3])
                    
    result
end

def _reduce_626(val, _values, result)
                      result = val[0].concat(val[1])
                    
    result
end

def _reduce_627(val, _values, result)
                      result = val[0].concat(val[1])
                    
    result
end

def _reduce_628(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

def _reduce_629(val, _values, result)
                      result = val[1]
                    
    result
end

def _reduce_630(val, _values, result)
                      result = []
                    
    result
end

def _reduce_631(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])
                    
    result
end

def _reduce_632(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[6]).
                                  concat(val[7])
                    
    result
end

def _reduce_633(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])
                    
    result
end

def _reduce_634(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])
                    
    result
end

def _reduce_635(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])
                    
    result
end

def _reduce_636(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])
                    
    result
end

def _reduce_637(val, _values, result)
                      result = val[0].
                                  concat(val[1])
                    
    result
end

def _reduce_638(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])
                    
    result
end

def _reduce_639(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])
                    
    result
end

def _reduce_640(val, _values, result)
                      result = val[0].
                                  concat(val[1])
                    
    result
end

def _reduce_641(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])
                    
    result
end

def _reduce_642(val, _values, result)
                      result = val[0].
                                  concat(val[1])
                    
    result
end

def _reduce_643(val, _values, result)
                      result = val[0].
                                  concat(val[2]).
                                  concat(val[3])
                    
    result
end

def _reduce_644(val, _values, result)
                      result = val[0]
                    
    result
end

def _reduce_645(val, _values, result)
                      result = []
                    
    result
end

def _reduce_646(val, _values, result)
                      result = val[0]
                    
    result
end

def _reduce_647(val, _values, result)
                      diagnostic :error, :argument_const, nil, val[0]
                    
    result
end

def _reduce_648(val, _values, result)
                      diagnostic :error, :argument_ivar, nil, val[0]
                    
    result
end

def _reduce_649(val, _values, result)
                      diagnostic :error, :argument_gvar, nil, val[0]
                    
    result
end

def _reduce_650(val, _values, result)
                      diagnostic :error, :argument_cvar, nil, val[0]
                    
    result
end

# reduce 651 omitted

def _reduce_652(val, _values, result)
                      @static_env.declare val[0][0]

                      @max_numparam_stack.has_ordinary_params!

                      result = val[0]
                    
    result
end

def _reduce_653(val, _values, result)
                      @current_arg_stack.set(val[0][0])
                      result = val[0]
                    
    result
end

def _reduce_654(val, _values, result)
                      @current_arg_stack.set(0)
                      result = @builder.arg(val[0])
                    
    result
end

def _reduce_655(val, _values, result)
                      result = @builder.multi_lhs(val[0], val[1], val[2])
                    
    result
end

def _reduce_656(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

def _reduce_657(val, _values, result)
                      result = val[0] << val[2]
                    
    result
end

def _reduce_658(val, _values, result)
                      check_kwarg_name(val[0])

                      @static_env.declare val[0][0]

                      @max_numparam_stack.has_ordinary_params!

                      @current_arg_stack.set(val[0][0])

                      result = val[0]
                    
    result
end

def _reduce_659(val, _values, result)
                      @current_arg_stack.set(nil)
                      result = @builder.kwoptarg(val[0], val[1])
                    
    result
end

def _reduce_660(val, _values, result)
                      @current_arg_stack.set(nil)
                      result = @builder.kwarg(val[0])
                    
    result
end

def _reduce_661(val, _values, result)
                      result = @builder.kwoptarg(val[0], val[1])
                    
    result
end

def _reduce_662(val, _values, result)
                      result = @builder.kwarg(val[0])
                    
    result
end

def _reduce_663(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

def _reduce_664(val, _values, result)
                      result = val[0] << val[2]
                    
    result
end

def _reduce_665(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

def _reduce_666(val, _values, result)
                      result = val[0] << val[2]
                    
    result
end

# reduce 667 omitted

# reduce 668 omitted

def _reduce_669(val, _values, result)
                      result = [ @builder.kwnilarg(val[0], val[1]) ]
                    
    result
end

def _reduce_670(val, _values, result)
                      @static_env.declare val[1][0]

                      result = [ @builder.kwrestarg(val[0], val[1]) ]
                    
    result
end

def _reduce_671(val, _values, result)
                      result = [ @builder.kwrestarg(val[0]) ]
                    
    result
end

def _reduce_672(val, _values, result)
                      @current_arg_stack.set(0)
                      result = @builder.optarg(val[0], val[1], val[2])
                    
    result
end

def _reduce_673(val, _values, result)
                      @current_arg_stack.set(0)
                      result = @builder.optarg(val[0], val[1], val[2])
                    
    result
end

def _reduce_674(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

def _reduce_675(val, _values, result)
                      result = val[0] << val[2]
                    
    result
end

def _reduce_676(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

def _reduce_677(val, _values, result)
                      result = val[0] << val[2]
                    
    result
end

# reduce 678 omitted

# reduce 679 omitted

def _reduce_680(val, _values, result)
                      @static_env.declare val[1][0]

                      result = [ @builder.restarg(val[0], val[1]) ]
                    
    result
end

def _reduce_681(val, _values, result)
                      result = [ @builder.restarg(val[0]) ]
                    
    result
end

# reduce 682 omitted

# reduce 683 omitted

def _reduce_684(val, _values, result)
                      @static_env.declare val[1][0]

                      result = @builder.blockarg(val[0], val[1])
                    
    result
end

def _reduce_685(val, _values, result)
                      result = [ val[1] ]
                    
    result
end

def _reduce_686(val, _values, result)
                      result = []
                    
    result
end

# reduce 687 omitted

def _reduce_688(val, _values, result)
                      result = val[1]
                    
    result
end

def _reduce_689(val, _values, result)
                      result = []
                    
    result
end

# reduce 690 omitted

def _reduce_691(val, _values, result)
                      result = [ val[0] ]
                    
    result
end

def _reduce_692(val, _values, result)
                      result = val[0] << val[2]
                    
    result
end

def _reduce_693(val, _values, result)
                      result = @builder.pair(val[0], val[1], val[2])
                    
    result
end

def _reduce_694(val, _values, result)
                      result = @builder.pair_keyword(val[0], val[1])
                    
    result
end

def _reduce_695(val, _values, result)
                      result = @builder.pair_quoted(val[0], val[1], val[2], val[3])
                    
    result
end

def _reduce_696(val, _values, result)
                      result = @builder.kwsplat(val[0], val[1])
                    
    result
end

# reduce 697 omitted

# reduce 698 omitted

# reduce 699 omitted

# reduce 700 omitted

# reduce 701 omitted

# reduce 702 omitted

# reduce 703 omitted

# reduce 704 omitted

# reduce 705 omitted

# reduce 706 omitted

# reduce 707 omitted

# reduce 708 omitted

def _reduce_709(val, _values, result)
                      result = [:dot, val[0][1]]
                    
    result
end

def _reduce_710(val, _values, result)
                      result = [:anddot, val[0][1]]
                    
    result
end

# reduce 711 omitted

# reduce 712 omitted

# reduce 713 omitted

# reduce 714 omitted

def _reduce_715(val, _values, result)
                      result = val[1]
                    
    result
end

def _reduce_716(val, _values, result)
                      result = val[1]
                    
    result
end

def _reduce_717(val, _values, result)
                      result = val[1]
                    
    result
end

# reduce 718 omitted

# reduce 719 omitted

# reduce 720 omitted

def _reduce_721(val, _values, result)
                    yyerrok
                  
    result
end

# reduce 722 omitted

# reduce 723 omitted

# reduce 724 omitted

def _reduce_725(val, _values, result)
                    result = nil
                  
    result
end

def _reduce_none(val, _values, result)
  val[0]
end

  end   # class Ruby28
  end   # module Parser
