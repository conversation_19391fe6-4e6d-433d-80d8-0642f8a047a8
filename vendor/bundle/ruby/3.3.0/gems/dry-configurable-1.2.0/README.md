<!--- this file is synced from dry-rb/template-gem project -->
[gem]: https://rubygems.org/gems/dry-configurable
[actions]: https://github.com/dry-rb/dry-configurable/actions

# dry-configurable [![Gem Version](https://badge.fury.io/rb/dry-configurable.svg)][gem] [![CI Status](https://github.com/dry-rb/dry-configurable/workflows/ci/badge.svg)][actions]

## Links

* [User documentation](https://dry-rb.org/gems/dry-configurable)
* [API documentation](http://rubydoc.info/gems/dry-configurable)
* [Forum](https://discourse.dry-rb.org)

## Supported Ruby versions

This library officially supports the following Ruby versions:

* MRI `>= 3.0.0`
* jruby `>= 9.4` (not tested on CI)

## License

See `LICENSE` file.
