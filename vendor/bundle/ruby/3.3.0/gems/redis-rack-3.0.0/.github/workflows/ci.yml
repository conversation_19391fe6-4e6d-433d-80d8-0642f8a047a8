name: CI
on:
  push:
    branches-ignore: [master]
jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        ruby-version: ["2.6", "2.7", "3.0", "3.1", "jruby"]

    steps:
      - uses: actions/checkout@v3
      - run: sudo apt-get install -y --no-install-recommends redis-server
      - uses: ruby/setup-ruby@v1
        with:
          ruby-version: ${{ matrix.ruby-version }}
          bundler-cache: true
      - run: bundle exec rake
