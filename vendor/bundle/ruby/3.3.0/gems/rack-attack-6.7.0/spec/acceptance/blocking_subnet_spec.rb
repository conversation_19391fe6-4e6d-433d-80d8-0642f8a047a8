# frozen_string_literal: true

require_relative "../spec_helper"

describe "Blocking an IP subnet" do
  before do
    Rack::Attack.blocklist_ip("*******/31")
  end

  it "forbids request if IP is inside the subnet" do
    get "/", {}, "REMOTE_ADDR" => "*******"

    assert_equal 403, last_response.status
  end

  it "forbids request for another IP in the subnet" do
    get "/", {}, "REMOTE_ADDR" => "*******"

    assert_equal 403, last_response.status
  end

  it "succeeds if IP is outside the subnet" do
    get "/", {}, "REMOTE_ADDR" => "*******"

    assert_equal 200, last_response.status
  end

  it "notifies when the request is blocked" do
    notified = false
    notification_type = nil

    ActiveSupport::Notifications.subscribe("blocklist.rack_attack") do |_name, _start, _finish, _id, payload|
      notified = true
      notification_type = payload[:request].env["rack.attack.match_type"]
    end

    get "/", {}, "REMOTE_ADDR" => "*******"

    refute notified

    get "/", {}, "REMOTE_ADDR" => "*******"

    assert notified
    assert_equal :blocklist, notification_type
  end
end
