# frozen_string_literal: true

require_relative "../spec_helper"

describe "#track" do
  it "notifies when track block returns true" do
    Rack::Attack.track("ip *******") do |request|
      request.ip == "*******"
    end

    notification_matched = nil
    notification_type = nil

    ActiveSupport::Notifications.subscribe("track.rack_attack") do |_name, _start, _finish, _id, payload|
      notification_matched = payload[:request].env["rack.attack.matched"]
      notification_type = payload[:request].env["rack.attack.match_type"]
    end

    get "/", {}, "REMOTE_ADDR" => "*******"

    assert_nil notification_matched
    assert_nil notification_type

    get "/", {}, "REMOTE_ADDR" => "*******"

    assert_equal "ip *******", notification_matched
    assert_equal :track, notification_type
  end
end
