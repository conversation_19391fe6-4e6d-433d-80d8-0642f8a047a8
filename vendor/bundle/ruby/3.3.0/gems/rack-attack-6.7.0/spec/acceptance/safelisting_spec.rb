# frozen_string_literal: true

require_relative "../spec_helper"

describe "#safelist" do
  before do
    Rack::Attack.blocklist do |request|
      request.ip == "*******"
    end

    Rack::Attack.safelist do |request|
      request.path == "/safe_space"
    end
  end

  it "forbids request if blocklist condition is true and safelist is false" do
    get "/", {}, "REMOTE_ADDR" => "*******"

    assert_equal 403, last_response.status
  end

  it "succeeds if blocklist condition is false and safelist is false" do
    get "/", {}, "REMOTE_ADDR" => "*******"

    assert_equal 200, last_response.status
  end

  it "succeeds request if blocklist condition is false and safelist is true" do
    get "/safe_space", {}, "REMOTE_ADDR" => "*******"

    assert_equal 200, last_response.status
  end

  it "succeeds request if both blocklist and safelist conditions are true" do
    get "/safe_space", {}, "REMOTE_ADDR" => "*******"

    assert_equal 200, last_response.status
  end

  it "notifies when the request is safe" do
    notification_matched = nil
    notification_type = nil

    ActiveSupport::Notifications.subscribe("rack.attack") do |_name, _start, _finish, _id, payload|
      notification_matched = payload[:request].env["rack.attack.matched"]
      notification_type = payload[:request].env["rack.attack.match_type"]
    end

    get "/safe_space", {}, "REMOTE_ADDR" => "*******"

    assert_equal 200, last_response.status
    assert_nil notification_matched
    assert_equal :safelist, notification_type
  end
end

describe "#safelist with name" do
  before do
    Rack::Attack.blocklist("block *******") do |request|
      request.ip == "*******"
    end

    Rack::Attack.safelist("safe path") do |request|
      request.path == "/safe_space"
    end
  end

  it "forbids request if blocklist condition is true and safelist is false" do
    get "/", {}, "REMOTE_ADDR" => "*******"

    assert_equal 403, last_response.status
  end

  it "succeeds if blocklist condition is false and safelist is false" do
    get "/", {}, "REMOTE_ADDR" => "*******"

    assert_equal 200, last_response.status
  end

  it "succeeds request if blocklist condition is false and safelist is true" do
    get "/safe_space", {}, "REMOTE_ADDR" => "*******"

    assert_equal 200, last_response.status
  end

  it "succeeds request if both blocklist and safelist conditions are true" do
    get "/safe_space", {}, "REMOTE_ADDR" => "*******"

    assert_equal 200, last_response.status
  end

  it "notifies when the request is safe" do
    notification_matched = nil
    notification_type = nil

    ActiveSupport::Notifications.subscribe("safelist.rack_attack") do |_name, _start, _finish, _id, payload|
      notification_matched = payload[:request].env["rack.attack.matched"]
      notification_type = payload[:request].env["rack.attack.match_type"]
    end

    get "/safe_space", {}, "REMOTE_ADDR" => "*******"

    assert_equal 200, last_response.status
    assert_equal "safe path", notification_matched
    assert_equal :safelist, notification_type
  end
end
