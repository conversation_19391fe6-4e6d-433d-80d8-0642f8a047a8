# elements like %{queue} are variables and should not be translated
zh-cn: # <---- change this to your locale code
  Dashboard: 信息板
  Status: 状态
  Time: 时间
  Namespace: 命名空间
  Realtime: 实时
  History: 历史记录
  Busy: 执行中
  Utilization: 利用率
  Processed: 已处理
  Failed: 已失败
  Scheduled: 已计划
  Retries: 重试
  Enqueued: 已进入队列
  Worker: 工人
  LivePoll: 实时轮询
  StopPolling: 停止轮询
  Queue: 队列
  Class: 类别
  Job: 任务
  Arguments: 参数
  Extras: 额外的
  Started: 已开始
  ShowAll: 显示全部
  CurrentMessagesInQueue: 目前在<span class='title'>%{queue}</span>的任务
  Delete: 删除
  AddToQueue: 添加至队列
  AreYouSureDeleteJob: 你确定要删除这个任务么？
  AreYouSureDeleteQueue: 你确定要删除%{queue}这个队列？
  Queues: 队列
  Size: 容量
  Actions: 动作
  NextRetry: 下次重试
  RetryCount: 重试次数
  RetryNow: 现在重试
  Kill: 终止
  LastRetry: 上次重试
  OriginallyFailed: 首次失败
  AreYouSure: 你确定？
  DeleteAll: 全部删除
  RetryAll: 全部重试
  KillAll: 全部终止
  NoRetriesFound: 没有发现可重试
  Error: 错误
  ErrorClass: 错误类别
  ErrorMessage: 错误消息
  ErrorBacktrace: 错误细节
  GoBack: ← 返回
  NoScheduledFound: 没有发现计划任务
  When: 当
  ScheduledJobs: 计划任务
  idle: 闲置
  active: 活动中
  Version: 版本
  Connections: 连接
  MemoryUsage: 内存占用
  PeakMemoryUsage: 内存占用峰值
  Uptime: 上线时间 (天数)
  OneWeek: 一周
  OneMonth: 一个月
  ThreeMonths: 三个月
  SixMonths: 六个月
  Failures: 失败
  DeadJobs: 已停滞任务
  NoDeadJobsFound: 没有发现任何已停滞的任务
  Dead: 已停滞
  Process: 进程
  Processes: 进程
  Name: 名称
  Thread: 线程
  Threads: 线程
  Jobs: 任务
  Paused: 已暂停
  Stop: 强制暂停
  Quiet: 暂停
  StopAll: 全部强制暂停
  QuietAll: 全部暂停
  PollingInterval: 轮询周期
  Plugins: 插件
  NotYetEnqueued: 尚未进入队列
  CreatedAt: 建立时间
  BackToApp: 回首頁
  Latency: 延迟
  Pause: 暂停
  Unpause: 取消暂停
  Metrics: 指标
  NoDataFound: 无数据
  TotalExecutionTime: 总执行时间
  AvgExecutionTime: 平均执行时间
  Context: 上下文
  NoJobMetricsFound: 无任务相关指标数据
  Success: 成功
  Failure: 失败
