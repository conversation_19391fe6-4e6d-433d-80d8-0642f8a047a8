# elements like %{queue} are variables and should not be translated
ko:
  Actions: 동작
  AddToQueue: 큐 추가
  AreYouSure: 정말입니까?
  AreYouSureDeleteJob: 이 작업을 삭제하시겠습니까?
  AreYouSureDeleteQueue: 이 %{queue} 큐를 삭제하시겠습니까?
  Arguments: 인자
  Batches: 배치
  Busy: 작동
  Class: 클래스
  Connections: 커넥션
  CurrentMessagesInQueue: <span class='title'>%{queue}</span>에 대기 중인 메시지
  Dashboard: 대시보드
  Dead: 죽음
  DeadJobs: 죽은 작업
  Delete: 삭제
  DeleteAll: 모두 삭제
  Enqueued: 대기 중
  Error: 에러
  ErrorBacktrace: 에러 Backtrace
  ErrorClass: 에러 클래스
  ErrorMessage: 에러 메시지
  Failed: 실패
  Failures: 실패
  GoBack: ← 뒤로
  History: 히스토리
  Job: 작업
  Jobs: 작업
  LastRetry: 최근 재시도
  LivePoll: 폴링 시작
  MemoryUsage: 메모리 사용량
  Namespace: 네임스페이스
  NextRetry: 다음 재시도
  NoDeadJobsFound: 죽은 작업이 없습니다
  NoRetriesFound: 재시도 내역이 없습니다
  NoScheduledFound: 예약된 작업이 없습니다
  OneMonth: 1 달
  OneWeek: 1 주
  OriginallyFailed: 실패
  PeakMemoryUsage: 최대 메모리 사용량
  Processed: 처리완료
  Processes: 프로세스
  Queue: 큐
  Queues: 큐
  Realtime: 실시간
  Retries: 재시도
  RetryAll: 모두 재시도
  RetryCount: 재시도 횟수
  RetryNow: 지금 재시도
  Scheduled: 예약
  ScheduledJobs: 예약된 작업
  ShowAll: 모두 보기
  SixMonths: 6 달
  Size: 크기
  Started: 시작
  Status: 상태
  StopPolling: 폴링 중단
  Thread: 스레드
  Threads: 스레드
  ThreeMonths: 3 달
  Time: 시간
  Uptime: 업타임 (일)
  Version: 버전
  When: 언제
  Worker: 워커
  active: 동작 중
  idle: 대기 중
