# elements like %{queue} are variables and should not be translated
zh-tw: # <---- change this to your locale code
  Dashboard: 資訊主頁
  Status: 狀態
  Time: 時間
  Namespace: 命名空間
  Realtime: 即時
  History: 歷史資料
  Busy: 忙碌
  Utilization: 使用率
  Processed: 已處理
  Failed: 已失敗
  Scheduled: 已排程
  Retries: 重試
  Enqueued: 已佇列
  Worker: 工人
  LivePoll: 即時輪詢
  StopPolling: 停止輪詢
  Queue: 佇列
  Class: 類別
  Job: 工作
  Arguments: 參數
  Extras: 額外的
  Started: 已開始
  ShowAll: 顯示全部
  CurrentMessagesInQueue: 目前在<span class='title'>%{queue}</span>的工作
  Delete: 刪除
  AddToQueue: 增加至佇列
  AreYouSureDeleteJob: 確定要刪除這個工作嗎？
  AreYouSureDeleteQueue: 確定要刪除%{queue}佇列？這會刪除佇列裡的所有工作，佇列將會在有新工作時重新出現。
  Queues: 佇列
  Size: 容量
  Actions: 動作
  NextRetry: 下次重試
  RetryCount: 重試次數
  RetryNow: 馬上重試
  Kill: 取消
  LastRetry: 最後一次重試
  OriginallyFailed: 原本已失敗
  AreYouSure: 你確定？
  DeleteAll: 全部刪除
  RetryAll: 全部重試
  KillAll: 全部取消
  NoRetriesFound: 找無可重試的工作
  Error: 錯誤
  ErrorBacktrace: 錯誤的回調追踨
  ErrorClass: 錯誤類別
  ErrorMessage: 錯誤訊息
  ErrorBacktrace: 詳細錯誤訊息
  GoBack: ← 返回
  NoScheduledFound: 找無已排程的工作
  When: 當
  ScheduledJobs: 已排程的工作
  idle: 閒置
  active: 活動中
  Version: 版本
  Connections: 連線
  MemoryUsage: 記憶體使用量
  PeakMemoryUsage: 尖峰記憶體使用量
  Uptime: 上線時間 (天數)
  OneWeek: 一週
  OneMonth: 一個月
  ThreeMonths: 三個月
  SixMonths: 六個月
  Failures: 失敗
  GoBack: ← 返回
  History: 歷史資料
  Job: 工作
  Jobs: 工作
  LastRetry: 最後一次重試
  LivePoll: 即時輪詢
  MemoryUsage: 記憶體使用量
  Namespace: 命名空間
  NextRetry: 下次重試
  NoDeadJobsFound: 沒有發現任何停滯的工作
  Dead: 停滯
  Process: 程序
  Processes: 處理中
  Name: 名稱
  Thread: 執行緒
  Threads: 執行緒
  Jobs: 工作
  Paused: 已暫停
  Stop: 強制暫停
  Quiet: 暫停
  StopAll: 全部強制暫停
  QuietAll: 全部暫停
  PollingInterval: 輪詢週期
  Plugins: 套件
  NotYetEnqueued: 尚未進入佇列
  CreatedAt: 建立時間
  BackToApp: 回首頁
  Latency: 延時
  Pause: 暫停
  Unpause: 取消暫停
  Metrics: 計量
  NoDataFound: 找無資料
  TotalExecutionTime: 總執行時間
  AvgExecutionTime: 平均執行時間
  Context: 上下文
  NoJobMetricsFound: 找無工作相關計量資料
