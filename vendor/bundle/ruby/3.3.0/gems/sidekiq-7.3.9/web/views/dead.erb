<%= erb :_job_info, locals: { job: @dead, type: :dead } %>

<h3><%= t('Error') %></h3>
<div class="table_container">
  <table class="error table table-bordered table-striped table-hover">
    <tbody>
      <tr>
        <th><%= t('ErrorClass') %></th>
        <td>
          <code><%= @dead['error_class'] %></code>
        </td>
      </tr>
      <tr>
        <th><%= t('ErrorMessage') %></th>
        <td><%= h(@dead['error_message']) %></td>
      </tr>
      <% if @dead.error_backtrace %>
        <tr>
          <th><%= t('ErrorBacktrace') %></th>
          <td>
            <code><%= @dead.error_backtrace.join("<br/>") %></code>
          </td>
        </tr>
      <% end %>
    </tbody>
  </table>
</div>

<form class="form-horizontal" action="<%= root_path %>morgue/<%= job_params(@dead, @dead.score) %>" method="post">
  <%= csrf_tag %>
  <a class="btn btn-default" href="<%= root_path %>morgue"><%= t('GoBack') %></a>
  <input class="btn btn-primary" type="submit" name="retry" value="<%= t('RetryNow') %>" />
  <input class="btn btn-danger" type="submit" name="delete" value="<%= t('Delete') %>" />
</form>
