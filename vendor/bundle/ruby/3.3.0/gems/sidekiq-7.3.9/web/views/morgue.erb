<div class="header-container">
  <h1><%= t('DeadJobs') %></h1>
  <% if @dead.size > 0 && @total_size > @count %>
    <%= erb :_paging, locals: { url: "#{root_path}morgue" } %>
  <% end %>
  <%= filtering('morgue') %>
</div>

<% if @dead.size > 0 %>
  <form action="<%= root_path %>morgue" method="post">
    <%= csrf_tag %>
    <div class="table_container">
      <table class="table table-striped table-bordered table-hover">
        <thead>
          <tr>
            <th class="table-checkbox checkbox-column">
              <label>
                <input type="checkbox" class="check_all" />
              </label>
            </th>
            <th><%= t('LastRetry') %></th>
            <th><%= t('Queue') %></th>
            <th><%= t('Job') %></th>
            <th><%= t('Arguments') %></th>
            <th><%= t('Error') %></th>
          </tr>
        </thead>
        <% @dead.each do |entry| %>
          <tr>
            <td class="table-checkbox">
              <label>
                <input type='checkbox' name='key[]' value='<%= job_params(entry.item, entry.score) %>' class='shift_clickable' />
              </label>
            </td>
            <td>
              <a href="<%= root_path %>morgue/<%= job_params(entry.item, entry.score) %>"><%= relative_time(entry.at) %></a>
            </td>
            <td>
              <a href="<%= root_path %>queues/<%= entry.queue %>"><%= entry.queue %></a>
            </td>
            <td>
              <%= entry.display_class %>
              <%= display_tags(entry, "morgue") %>
            </td>
            <td>
              <div class="args"><%= display_args(entry.display_args) %></div>
            </td>
            <td>
              <% if entry.error? %>
              <div><%= h truncate("#{entry['error_class']}: #{entry['error_message']}", 200) %></div>
              <% end %>
            </td>
          </tr>
        <% end %>
      </table>
    </div>
    <input class="btn btn-primary pull-left flip" type="submit" name="retry" value="<%= t('RetryNow') %>" />
    <input class="btn btn-danger pull-left flip" type="submit" name="delete" value="<%= t('Delete') %>" />
  </form>

  <% unfiltered? do %>
    <form action="<%= root_path %>morgue/all/delete" method="post">
      <%= csrf_tag %>
      <input class="btn btn-danger pull-right flip" type="submit" name="delete" value="<%= t('DeleteAll') %>" data-confirm="<%= t('AreYouSure') %>" />
    </form>
    <form action="<%= root_path %>morgue/all/retry" method="post">
      <%= csrf_tag %>
      <input class="btn btn-danger pull-right flip" type="submit" name="retry" value="<%= t('RetryAll') %>" data-confirm="<%= t('AreYouSure') %>" />
    </form>
  <% end %>

<% else %>
  <div class="alert alert-success"><%= t('NoDeadJobsFound') %></div>
<% end %>
