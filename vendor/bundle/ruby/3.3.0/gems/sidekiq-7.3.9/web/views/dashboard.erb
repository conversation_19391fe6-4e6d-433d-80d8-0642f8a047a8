<script type="text/javascript" src="<%= root_path %>javascripts/dashboard.js" nonce="<%= csp_nonce %>"></script>
<div class= "dashboard clearfix">
  <h3 >
    <%= t('Dashboard') %>
    <span id="beacon" class="beacon">
      <span class="ring"></span>
      <span class="dot"></span>
    </span>
  </h3>
  <div class="interval-slider ltr">
    <span class="interval-slider-label"><%= t('PollingInterval') %>:</span>
    <span id="sldr-text" class="current-interval">5 s</span>
    <br/>
    <input id="sldr" aria-label="<%= t("PollingIntervalMilliseconds") %>" type="range" min="2000" max="20000" step="1000" value="5000"/>
  </div>
</div>

<div class="row chart">
  <span id="sr-last-dashboard-update-template" hidden="hidden"><%= t("LastDashboardUpdateTemplateLiteral") %></span>
  <span id="sr-last-dashboard-update" class="sr-only" role="status"></span>

  <canvas id="realtime-chart">
    <%= to_json({
      processedLabel: t('Processed'),
      failedLabel: t('Failed'),
      labels: Array.new(50, ""),
      processed: Array.new(50),
      failed: Array.new(50),
      updateUrl: "#{root_path}stats",
    }) %>
  </canvas>

  <!-- start with a space in the legend so the height doesn't change when we add content dynamically -->
  <div id="realtime-legend">&nbsp;</div>
</div>

<div class="row header">
   <div class="col-sm-4 pull-left flip">
     <h3><%= t('History') %></h3>
   </div>
 </div>
 <div class="row chart">
   <div>
    <a href="<%= root_path %>?days=7" class="history-graph <%= "active" if params[:days] == "7" %>"><%= t('OneWeek') %></a>
    <a href="<%= root_path %>" class="history-graph <%= "active" if params[:days].nil? || params[:days] == "30" %>"><%= t('OneMonth') %></a>
    <a href="<%= root_path %>?days=90" class="history-graph <%= "active" if params[:days] == "90" %>"><%= t('ThreeMonths') %></a>
    <a href="<%= root_path %>?days=180" class="history-graph <%= "active" if params[:days] == "180" %>"><%= t('SixMonths') %></a>
  </div>

  <canvas id="history-chart">
    <%= to_json({
      processedLabel: t('Processed'),
      failedLabel: t('Failed'),
      processed: @processed_history.to_a.reverse,
      failed: @failed_history.to_a.reverse,
    }) %>
  </canvas>
</div>

<br/>
<div class="row header">
  <div class="col-sm-4 pull-left flip">
    <h3>Redis</h3>
  </div>
</div>
<div class="stats-wrapper">
  <div class="stats-container">
    <% if @redis_info.fetch("redis_version", nil) %>
      <div class="stat">
        <h3 id="redis_version"><%= @redis_info.fetch("redis_version") %></h3>
        <p><%= t('Version') %></p>
      </div>
    <% end %>

    <% if @redis_info.fetch("uptime_in_days", nil) %>
      <div class="stat">
        <h3 id="uptime_in_days"><%= @redis_info.fetch("uptime_in_days") %></h3>
        <p><%= t('Uptime') %></p>
      </div>
    <% end %>

    <% if @redis_info.fetch("connected_clients", nil) %>
      <div class="stat">
        <h3 id="connected_clients"><%= @redis_info.fetch("connected_clients") %></h3>
        <p><%= t('Connections') %></p>
      </div>
    <% end %>

    <% if @redis_info.fetch("used_memory_human", nil) %>
      <div class="stat">
        <h3 id="used_memory_human"><%= @redis_info.fetch("used_memory_human") %></h3>
        <p><%= t('MemoryUsage') %></p>
      </div>
    <% end %>

    <% if @redis_info.fetch("used_memory_peak_human", nil) %>
      <div class="stat">
        <h3 id="used_memory_peak_human"><%= @redis_info.fetch("used_memory_peak_human") %></h3>
        <p><%= t('PeakMemoryUsage') %></p>
      </div>
    <% end %>
  </div>
</div>

<script type="text/javascript" src="<%= root_path %>javascripts/chart.min.js" nonce="<%= csp_nonce %>"></script>
<script type="text/javascript" src="<%= root_path %>javascripts/chartjs-plugin-annotation.min.js" nonce="<%= csp_nonce %>"></script>
<script type="text/javascript" src="<%= root_path %>javascripts/base-charts.js" nonce="<%= csp_nonce %>"></script>
<script type="text/javascript" src="<%= root_path %>javascripts/dashboard-charts.js" nonce="<%= csp_nonce %>"></script>
