<div class="header-container">
  <h1><%= t('ScheduledJobs') %></h1>
  <% if @scheduled.size > 0 && @total_size > @count %>
    <%= erb :_paging, locals: { url: "#{root_path}scheduled" } %>
  <% end %>
  <%= filtering('scheduled') %>
</div>

<% if @scheduled.size > 0 %>
  <form action="<%= root_path %>scheduled" method="post">
    <%= csrf_tag %>
    <div class="table_container">
      <table class="table table-striped table-bordered table-hover">
        <thead>
          <tr>
            <th class="table-checkbox checkbox-column">
              <label>
                <input type="checkbox" class="check_all" />
              </label>
            </th>
            <th><%= t('When') %></th>
            <th><%= t('Queue') %></th>
            <th><%= t('Job') %></th>
            <th><%= t('Arguments') %></th>
          </tr>
        </thead>
        <% @scheduled.each do |entry| %>
          <tr>
            <td class="table-checkbox">
              <label>
                <input type='checkbox' name='key[]' value='<%= job_params(entry.item, entry.score) %>' class='shift_clickable' />
              </label>
            </td>
            <td>
               <a href="<%= root_path %>scheduled/<%= job_params(entry.item, entry.score) %>"><%= relative_time(entry.at) %></a>
            </td>
            <td>
              <a href="<%= root_path %>queues/<%= entry.queue %>"><%= entry.queue %></a>
            </td>
            <td>
              <%= entry.display_class %>
              <%= display_tags(entry, "scheduled") %>
            </td>
            <td>
               <div class="args"><%= display_args(entry.display_args) %></div>
            </td>
          </tr>
        <% end %>
      </table>
    </div>
    <input class="btn btn-danger pull-right flip" type="submit" name="delete" value="<%= t('Delete') %>" />
    <input class="btn btn-danger pull-right flip" type="submit" name="add_to_queue" value="<%= t('AddToQueue') %>" />
  </form>
<% else %>
  <div class="alert alert-success"><%= t('NoScheduledFound') %></div>
<% end %>
