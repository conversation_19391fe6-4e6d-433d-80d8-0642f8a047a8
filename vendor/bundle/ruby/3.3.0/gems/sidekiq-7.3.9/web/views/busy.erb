<div class="header-container">
  <h1><%= t('Status') %></h1>
</div>

<div class="stats-wrapper">
  <div class="stats-container">
    <div class="stat">
      <h3><%= s = processes.size; number_with_delimiter(s) %></h3>
      <p><%= t('Processes') %></p>
    </div>
    <div class="stat">
      <h3><%= x = processes.total_concurrency; number_with_delimiter(x) %></h3>
      <p><%= t('Threads') %></p>
    </div>
    <div class="stat">
      <h3><%= ws = workset.size; number_with_delimiter(ws) %></h3>
      <p><%= t('Busy') %></p>
    </div>
    <div class="stat">
      <h3><%= x == 0 ? 0 : ((ws / x.to_f) * 100).round(0) %>%</h3>
      <p><%= t('Utilization') %></p>
    </div>
    <div class="stat">
      <h3><%= format_memory(processes.total_rss) %></h3>
      <p><%= t('RSS') %></p>
    </div>
  </div>
</div>

<div class="header-container">
  <h1><%= t('Processes') %></h1>
  <div>
    <form method="POST" class="warning-messages">
      <%= csrf_tag %>
      <div class="btn-group pull-right flip">
        <input class="btn btn-warn" type="submit" name="quiet" value="<%= t('QuietAll') %>" data-confirm="<%= t('AreYouSure') %>"/>
        <input class="btn btn-danger" type="submit" name="stop" value="<%= t('StopAll') %>" data-confirm="<%= t('AreYouSure') %>"/>
      </div>
    </form>
  </div>
</div>

<div class="table_container">
  <table class="processes table table-hover table-bordered table-striped">
    <thead>
      <th><%= t('Name') %></th>
      <th><%= t('Started') %></th>
      <th class="col-sm-1"><%= t('RSS') %><a target="blank" href="https://github.com/sidekiq/sidekiq/wiki/Memory#rss"><span class="info-circle" title="Click to learn more about RSS">?</span></a></th>
      <th class="col-sm-1"><%= t('Threads') %></th>
      <th class="col-sm-1"><%= t('Busy') %></th>
      <th>&nbsp;</th>
    </thead>
    <% lead = processes.leader %>
    <% sorted_processes.each do |process| %>
      <tr>
        <td class="box">
          <%= "#{process['hostname']}:#{process['pid']}" %>
          <span class="label label-success"><%= process.tag %></span>
          <% process.labels.each do |label| %>
            <span class="label label-info"><%= label %></span>
          <% end %>
          <% if process.embedded? %>
            <span class="label label-default">embedded</span>
          <% end %>
          <% if process.stopping? %>
            <span class="label label-danger">quiet</span>
          <% end %>
          <% if process.identity == lead %>
            <span class="label label-warning">leader</span>
          <% end %>
          <br>
          <b><%= "#{t('Queues')}: " %></b>
          <% if process.weights %>
            <%= busy_weights(process.weights) %>
          <% else %>
            <%= process.queues.sort.join(", ") %>
          <% end %>
          <% if process.version != Sidekiq::VERSION %>
            <br>
            <b><%= "#{t('Version')}: " %></b>
            <% if process.version %>
              <%= process.version %>
            <% else %>
              <%= t('Unknown') %>
            <% end %>
          <% end %>
        </td>
        <td><%= relative_time(Time.at(process['started_at'])) %></td>
        <td class="num"><%= format_memory(process['rss'].to_i) %></td>
        <td class="num"><%= number_with_delimiter(process['concurrency']) %></td>
        <td class="num"><%= number_with_delimiter(process['busy']) %></td>
        <td>
          <% unless process.embedded? %>
            <form method="POST">
              <%= csrf_tag %>
              <input type="hidden" name="identity" value="<%= process['identity'] %>"/>

              <div class="btn-group pull-right flip">
                <% unless process.stopping? %><button class="btn btn-xs btn-warn" type="submit" name="quiet" value="1"><%= t('Quiet') %></button><% end %>
                <button class="btn btn-xs btn-danger" type="submit" name="stop" value="1"><%= t('Stop') %></button>
              </div>
            </form>
          <% end %>
        </td>
      </tr>
    <% end %>
  </table>
</div>

<div class="header-container">
  <h1><%= t('Jobs') %></h1>
  <% if @workset.size > 0 && @total_size > @count %>
    <%= erb :_paging, locals: { url: "#{root_path}busy" } %>
  <% end %>
</div>

<div class="table_container">
  <table class="workers table table-hover table-bordered table-striped">
    <thead>
      <th><%= t('Process') %></th>
      <th><%= t('TID') %></th>
      <th><%= t('JID') %></th>
      <th><%= t('Queue') %></th>
      <th><%= t('Job') %></th>
      <th><%= t('Arguments') %></th>
      <th><%= t('Started') %></th>
    </thead>
    <% @workset.each do |process, thread, work| %>
      <% job = work.job %>
      <tr>
        <td><%= process %></td>
        <td><%= thread %></td>
        <td><%= job.jid %></td>
        <td>
          <a href="<%= root_path %>queues/<%= work.queue %>"><%= work.queue %></a>
        </td>
        <td>
          <%= job.display_class %>
          <%= display_tags(job, nil) %>
        </td>
        <td>
          <div class="args"><%= display_args(job.display_args) %></div>
        </td>
        <td><%= relative_time(work.run_at) %></td>
      </tr>
    <% end %>
  </table>
</div>
