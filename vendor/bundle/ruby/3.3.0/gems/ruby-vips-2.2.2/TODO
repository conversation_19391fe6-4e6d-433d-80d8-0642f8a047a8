# Notes

- We should support complex constants, eg: 

	Complex(1, 2)
	=> (1+2i)

# Common operations

- Lint.

	bundle exec standardrb

- Reinstall local copy of gem after a change.

	bundle exec rake install

- Run test suite.

	bundle exec rake 

- Version bump.

	edit lib/vips/version.rb 
	edit VERSION

- Regenerate autodocs.

	cd lib/vips
	ruby > methods.rb
	require "vips"; Vips::Yard.generate
	^D

- Regenerate docs.

	bundle exec rake yard

- Push new gem to rubygems, tag repository with version.

	bundle exec rake release

  You'll be asked for a otp from authy / google authenticator / etc. 

