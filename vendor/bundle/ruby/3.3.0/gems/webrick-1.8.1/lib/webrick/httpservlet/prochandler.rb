# frozen_string_literal: true
#
# prochandler.rb -- ProcHandler Class
#
# Author: IPR -- Internet Programming with Ruby -- writers
# Copyright (c) 2001 TA<PERSON><PERSON><PERSON><PERSON><PERSON>, GOTOU Yuuzou
# Copyright (c) 2002 Internet Programming with Ruby writers. All rights
# reserved.
#
# $IPR: prochandler.rb,v 1.7 2002/09/21 12:23:42 gotoyuzo Exp $

require_relative 'abstract'

module WEBrick
  module HTTPServlet

    ##
    # Mounts a proc at a path that accepts a request and response.
    #
    # Instead of mounting this servlet with WEBrick::HTTPServer#mount use
    # WEBrick::HTTPServer#mount_proc:
    #
    #   server.mount_proc '/' do |req, res|
    #     res.body = 'it worked!'
    #     res.status = 200
    #   end

    class ProcHandler < AbstractServlet
      # :stopdoc:
      def get_instance(server, *options)
        self
      end

      def initialize(proc)
        @proc = proc
      end

      def do_GET(request, response)
        @proc.call(request, response)
      end

      alias do_POST do_GET
      alias do_PUT  do_GET
      # :startdoc:
    end

  end
end
