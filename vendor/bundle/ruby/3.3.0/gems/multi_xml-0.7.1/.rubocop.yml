require:
  - rubocop-performance
  - rubocop-rake
  - rubocop-rspec
  - standard
  - standard-performance

AllCops:
  NewCops: enable
  TargetRubyVersion: 3.1

Layout/ArgumentAlignment:
  EnforcedStyle: with_fixed_indentation
  IndentationWidth: 2

Layout/CaseIndentation:
  EnforcedStyle: end

Layout/EndAlignment:
  EnforcedStyleAlignWith: start_of_line

Layout/LineLength:
  Max: 140

Layout/ParameterAlignment:
  EnforcedStyle: with_fixed_indentation
  IndentationWidth: 2

Layout/SpaceInsideHashLiteralBraces:
  EnforcedStyle: no_space

Metrics/ParameterLists:
  CountKeywordArgs: false

Style/Alias:
  EnforcedStyle: prefer_alias_method

Style/Documentation:
  Enabled: false

Style/FrozenStringLiteralComment:
  EnforcedStyle: never

Style/OpenStructUse:
  Enabled: false

Style/StringLiterals:
  EnforcedStyle: double_quotes

Style/StringLiteralsInInterpolation:
  EnforcedStyle: double_quotes

Style/TernaryParentheses:
  EnforcedStyle: require_parentheses
