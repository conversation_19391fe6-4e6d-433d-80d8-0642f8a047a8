<% error_index = local_assigns[:error_index] || 0 %>

<% source_extracts.each_with_index do |source_extract, index| %>
  <% if source_extract[:code] %>
    <div class="source <%= "hidden" if show_source_idx != index %>" id="frame-source-<%= error_index %>-<%= index %>">
      <div class="info">
        Extracted source (around line <strong>#<%= source_extract[:line_number] %></strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
                <% source_extract[:code].each_key do |line_number| %>
<span><%= line_number -%></span>
                <% end %>
              </pre>
            </td>
<td width="100%">
<pre>
<% source_extract[:code].each do |line, source| -%>
<div class="line<%= " active" if line == source_extract[:line_number] -%>"><% if source.is_a?(Array) -%><%= source[0] -%><span class="error_highlight"><%= source[1] -%></span><%= source[2] -%>
<% else -%>
<%= source -%>
<% end -%></div><% end -%>
</pre>
</td>
          </tr>
        </table>
      </div>
      <%- unless self.error_highlight_available? -%>
        <p class="error_highlight_tip">Tip: You may want to add <code>gem 'error_highlight', '&gt;= 0.4.0'</code> into your Gemfile, which will display the fine-grained error location.</p>
      <%- end -%>
    </div>
  <% end %>
<% end %>
