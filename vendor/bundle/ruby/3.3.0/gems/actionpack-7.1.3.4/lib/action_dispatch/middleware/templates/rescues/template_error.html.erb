<header role="banner">
  <h1>
    <%= @exception_wrapper.exception_name %> in
    <%= @request.parameters["controller"].camelize if @request.parameters["controller"] %>#<%= @request.parameters["action"] %>
  </h1>
</header>

<main role="main" id="container">
  <p>
    Showing <i><%= @exception_wrapper.file_name %></i> where line <b>#<%= @exception_wrapper.line_number %></b> raised:
  </p>
  <pre><code><%= h @exception_wrapper.message %></code></pre>

  <%= render "rescues/source", source_extracts: @source_extracts, show_source_idx: @show_source_idx %>

  <p><%= @exception_wrapper.sub_template_message %></p>

  <%= render "rescues/trace", traces: @traces, trace_to_show: @trace_to_show %>
  <%= render template: "rescues/_request_and_response" %>
</main>
