Blocked hosts: <%= @hosts.join(", ") %>

To allow requests to these hosts, make sure they are valid hostnames (containing only numbers, letters, dashes and dots), then add the following to your environment configuration:

<% @hosts.each do |host| %>
  config.hosts << "<%= host %>"
<% end %>

For more details on host authorization view: https://guides.rubyonrails.org/configuring.html#actiondispatch-hostauthorization
