====== Option +unconverted_fields+

Specifies the boolean that determines whether unconverted field values are to be available.

Default value:
  CSV::DEFAULT_OPTIONS.fetch(:unconverted_fields) # => nil

The unconverted field values are those found in the source data,
prior to any conversions performed via option +converters+.

When option +unconverted_fields+ is +true+,
each returned row (\Array or \CSV::Row) has an added method,
+unconverted_fields+, that returns the unconverted field values:
  str = <<-EOT
  foo,0
  bar,1
  baz,2
  EOT
  # Without unconverted_fields
  csv = CSV.parse(str, converters: :integer)
  csv # => [["foo", 0], ["bar", 1], ["baz", 2]]
  csv.first.respond_to?(:unconverted_fields) # => false
  # With unconverted_fields
  csv = CSV.parse(str, converters: :integer, unconverted_fields: true)
  csv # => [["foo", 0], ["bar", 1], ["baz", 2]]
  csv.first.respond_to?(:unconverted_fields) # => true
  csv.first.unconverted_fields # => ["foo", "0"]
