====== Option +headers+

Specifies a boolean, \Symbol, \Array, or \String to be used
to define column headers.

Default value:
  CSV::DEFAULT_OPTIONS.fetch(:headers) # => false

---

Without +headers+:
  str = <<-EOT
  Name,Count
  foo,0
  bar,1
  bax,2
  EOT
  csv = CSV.new(str)
  csv # => #<CSV io_type:StringIO encoding:UTF-8 lineno:0 col_sep:"," row_sep:"\n" quote_char:"\"">
  csv.headers # => nil
  csv.shift # => ["Name", "Count"]

---

If set to +true+ or the \Symbol +:first_row+,
the first row of the data is treated as a row of headers:
  str = <<-EOT
  Name,Count
  foo,0
  bar,1
  bax,2
  EOT
  csv = CSV.new(str, headers: true)
  csv # => #<CSV io_type:StringIO encoding:UTF-8 lineno:2 col_sep:"," row_sep:"\n" quote_char:"\"" headers:["Name", "Count"]>
  csv.headers # => ["Name", "Count"]
  csv.shift # => #<CSV::Row "Name":"bar" "Count":"1">

---

If set to an \Array, the \Array elements are treated as headers:
  str = <<-EOT
  foo,0
  bar,1
  bax,2
  EOT
  csv = CSV.new(str, headers: ['Name', 'Count'])
  csv
  csv.headers # => ["Name", "Count"]
  csv.shift # => #<CSV::Row "Name":"bar" "Count":"1">

---

If set to a \String +str+, method <tt>CSV::parse_line(str, options)</tt> is called
with the current +options+, and the returned \Array is treated as headers:
  str = <<-EOT
  foo,0
  bar,1
  bax,2
  EOT
  csv = CSV.new(str, headers: 'Name,Count')
  csv
  csv.headers # => ["Name", "Count"]
  csv.shift # => #<CSV::Row "Name":"bar" "Count":"1">
