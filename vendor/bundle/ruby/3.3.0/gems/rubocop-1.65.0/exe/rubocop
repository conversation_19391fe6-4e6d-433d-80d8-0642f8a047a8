#!/usr/bin/env ruby
# frozen_string_literal: true

$LOAD_PATH.unshift("#{__dir__}/../lib")

require 'rubocop/server'
server_cli = RuboCop::Server::CLI.new
exit_status = server_cli.run
exit exit_status if server_cli.exit?

if RuboCop::Server.running?
  exit_status = RuboCop::Server::ClientCommand::Exec.new.run
else
  require 'benchmark'
  require 'rubocop'

  cli = RuboCop::CLI.new

  time = Benchmark.realtime { exit_status = cli.run }

  puts "Finished in #{time} seconds" if cli.options[:debug] || cli.options[:display_time]
end
exit exit_status
