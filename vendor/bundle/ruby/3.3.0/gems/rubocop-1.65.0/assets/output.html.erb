<!DOCTYPE html>
<html>
  <head>
    <meta charset='UTF-8' />
    <title>RuboCop Inspection Report</title>
    <style>
<%= render_css %>
    </style>
    <script>
    (function() {
      // floating headers. requires classList support.
      if (!('classList' in document.createElement("_"))) return;

      var loaded = false,
        boxes,
        boxPositions;

      window.onload = function() {
        var scrollY = window.scrollY;
        boxes = document.querySelectorAll('.offense-box');
        boxPositions = [];
        for (var i = 0; i < boxes.length; i++)
          // need to add scrollY because the page might be somewhere other than the top when loaded.
          boxPositions[i] = boxes[i].getBoundingClientRect().top + scrollY;
        loaded = true;
      };

      window.onscroll = function() {
        if (!loaded) return;
        var i,
          idx,
          scrollY = window.scrollY;
        for (i = 0; i < boxPositions.length; i++) {
          if (scrollY <= boxPositions[i] - 1) {
            idx = i;
            break;
          }
        }
        if (typeof idx == 'undefined') idx = boxes.length;
        if (idx > 0)
          boxes[idx - 1].classList.add('fixed');
        for (i = 0; i < boxes.length; i++) {
          if (i < idx) continue;
          boxes[i].classList.remove('fixed');
        }
      };
    })();
    </script>
  </head>
  <body>
    <div id="header">
      <img class="logo" src="data:image/png;base64,<%= base64_encoded_logo_image %>" alt="">
      <h1 class="title">RuboCop Inspection Report</h1>
    </div>
    <div class="information">
      <div class="infobox">
        <div class="total">
          <%= pluralize(files.count, 'file') %> inspected,
          <%= pluralize(summary.offense_count, 'offense', no_for_zero: true) %> detected:
        </div>
        <ul class="offenses-list">
          <% files.each do |file| %>
            <% next if file.offenses.none? %>
            <li>
              <a href="#offense_<%= relative_path(file.path) %>">
                <%= relative_path(file.path) %> - <%= pluralize(file.offenses.count, 'offense') %>
              </a>
            </li>
          <% end %>
        </ul>
      </div>
    </div>
    <div id="offenses">
      <% files.each do |file| %>
      <% if file.offenses.any? %>
      <div class="offense-box" id="offense_<%= relative_path(file.path) %>">
        <div class="box-title-placeholder"><h3>&nbsp;</h3></div>
        <div class="box-title"><h3><%= relative_path(file.path) %> - <%= pluralize(file.offenses.count, 'offense') %></h3></div>
        <div class="offense-reports">
          <% file.offenses.each do |offense| %>
          <div class="report">
            <div class="meta">
              <span class="location">Line #<%= offense.location.line %></span> –
              <span class="severity <%= offense.severity %>"><%= offense.severity %>:</span>
              <span class="message"><%= decorated_message(offense) %></span>
            </div>
            <% unless offense.location.source_line.strip.empty? %>
            <pre><code><%= highlighted_source_line(offense) %></code></pre>
            <% end %>
          </div>
          <% end %>
        </div>
      </div>
      <% end %>
      <% end %>
    </div>
    <footer>
      Generated by <a href="https://github.com/rubocop/rubocop">RuboCop</a>
      <span class="version"><%= RuboCop::Version::STRING %></span>
    </footer>
  </body>
</html>
