= Active Support -- Utility classes and Ruby extensions from \Rails

Active Support is a collection of utility classes and standard library
extensions that were found useful for the \Rails framework. These additions
reside in this package so they can be loaded as needed in Ruby projects
outside of \Rails.

You can read more about the extensions in the {Active Support Core Extensions}[https://guides.rubyonrails.org/active_support_core_extensions.html] guide.

== Download and installation

The latest version of Active Support can be installed with RubyGems:

  $ gem install activesupport

Source code can be downloaded as part of the \Rails project on GitHub:

* https://github.com/rails/rails/tree/main/activesupport


== License

Active Support is released under the MIT license:

* https://opensource.org/licenses/MIT


== Support

API documentation is at:

* https://api.rubyonrails.org

Bug reports for the Ruby on \Rails project can be filed here:

* https://github.com/rails/rails/issues

Feature requests should be discussed on the rails-core mailing list here:

* https://discuss.rubyonrails.org/c/rubyonrails-core
