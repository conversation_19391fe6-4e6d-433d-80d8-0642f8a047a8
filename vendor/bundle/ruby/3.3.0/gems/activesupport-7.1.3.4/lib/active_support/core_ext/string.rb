# frozen_string_literal: true

require "active_support/core_ext/string/conversions"
require "active_support/core_ext/string/filters"
require "active_support/core_ext/string/multibyte"
require "active_support/core_ext/string/starts_ends_with"
require "active_support/core_ext/string/inflections"
require "active_support/core_ext/string/access"
require "active_support/core_ext/string/behavior"
require "active_support/core_ext/string/output_safety"
require "active_support/core_ext/string/exclude"
require "active_support/core_ext/string/strip"
require "active_support/core_ext/string/inquiry"
require "active_support/core_ext/string/indent"
require "active_support/core_ext/string/zones"
