en:
  date:
    formats:
      # Use the strftime parameters for formats.
      # When no format has been given, it uses default.
      # You can provide other formats here if you like!
      default: "%Y-%m-%d"
      short: "%b %d"
      long: "%B %d, %Y"

    day_names: [Sunday, Monday, Tuesday, Wednesday, Thursday, Friday, Saturday]
    abbr_day_names: [<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>]

    # Don't forget the nil at the beginning; there's no such thing as a 0th month
    month_names: [~, January, February, March, April, May, June, July, August, September, October, November, December]
    abbr_month_names: [~, Jan, Feb, Mar, Apr, May, Jun, Jul, Aug, Sep, Oct, Nov, Dec]
    # Used in date_select and datetime_select.
    order:
      - year
      - month
      - day

  time:
    formats:
      default: "%a, %d %b %Y %H:%M:%S %z"
      short: "%d %b %H:%M"
      long: "%B %d, %Y %H:%M"
    am: "am"
    pm: "pm"

# Used in array.to_sentence.
  support:
    array:
      words_connector: ", "
      two_words_connector: " and "
      last_word_connector: ", and "
  number:
    # Used in NumberHelper.number_to_delimited()
    # These are also the defaults for 'currency', 'percentage', 'precision', and 'human'
    format:
      # Sets the separator between the units, for more precision (e.g. 1.0 / 2.0 == 0.5)
      separator: "."
      # Delimits thousands (e.g. 1,000,000 is a million) (always in groups of three)
      delimiter: ","
      # Number of decimals, behind the separator (the number 1 with a precision of 2 gives: 1.00)
      precision: 3
      # Determine how rounding is performed (see BigDecimal::mode)
      round_mode: default
      # If set to true, precision will mean the number of significant digits instead
      # of the number of decimal digits (1234 with precision 2 becomes 1200, 1.23543 becomes 1.2)
      significant: false
      # If set, the zeros after the decimal separator will always be stripped (e.g.: 1.200 will be 1.2)
      strip_insignificant_zeros: false

    # Used in NumberHelper.number_to_currency()
    currency:
      format:
        # Where is the currency sign? %u is the currency unit, %n is the number (default: $5.00)
        format: "%u%n"
        negative_format: "-%u%n"
        unit: "$"
        # These six are to override number.format and are optional
        separator: "."
        delimiter: ","
        precision: 2
        # round_mode:
        significant: false
        strip_insignificant_zeros: false

    # Used in NumberHelper.number_to_percentage()
    percentage:
      format:
        # These five are to override number.format and are optional
        # separator:
        delimiter: ""
        # precision:
        # significant: false
        # strip_insignificant_zeros: false
        format: "%n%"

    # Used in NumberHelper.number_to_rounded()
    precision:
      format:
        # These five are to override number.format and are optional
        # separator:
        delimiter: ""
        # precision:
        # significant: false
        # strip_insignificant_zeros: false

    # Used in NumberHelper.number_to_human_size() and NumberHelper.number_to_human()
    human:
      format:
        # These six are to override number.format and are optional
        # separator:
        delimiter: ""
        precision: 3
        # round_mode:
        significant: true
        strip_insignificant_zeros: true
      # Used in number_to_human_size()
      storage_units:
        # Storage units output formatting.
        # %u is the storage unit, %n is the number (default: 2 MB)
        format: "%n %u"
        units:
          byte:
            one:   "Byte"
            other: "Bytes"
          kb: "KB"
          mb: "MB"
          gb: "GB"
          tb: "TB"
          pb: "PB"
          eb: "EB"
          zb: "ZB"
      # Used in NumberHelper.number_to_human()
      decimal_units:
        format: "%n %u"
        # Decimal units output formatting
        # By default we will only quantify some of the exponents
        # but the commented ones might be defined or overridden
        # by the user.
        units:
          # femto: Quadrillionth
          # pico: Trillionth
          # nano: Billionth
          # micro: Millionth
          # mili: Thousandth
          # centi: Hundredth
          # deci: Tenth
          unit: ""
          # ten:
          #   one: Ten
          #   other: Tens
          # hundred: Hundred
          thousand: Thousand
          million: Million
          billion: Billion
          trillion: Trillion
          quadrillion: Quadrillion
