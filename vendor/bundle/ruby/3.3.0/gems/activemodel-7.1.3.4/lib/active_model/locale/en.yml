en:
  errors:
    # The default format to use in full error messages.
    format: "%{attribute} %{message}"

    # The values :model, :attribute and :value are always available for interpolation
    # The value :count is available when applicable. Can be used for pluralization.
    messages:
      model_invalid: "Validation failed: %{errors}"
      inclusion: "is not included in the list"
      exclusion: "is reserved"
      invalid: "is invalid"
      confirmation: "doesn't match %{attribute}"
      accepted: "must be accepted"
      empty: "can't be empty"
      blank: "can't be blank"
      present: "must be blank"
      too_long:
        one: "is too long (maximum is 1 character)"
        other: "is too long (maximum is %{count} characters)"
      password_too_long: "is too long"
      too_short:
        one: "is too short (minimum is 1 character)"
        other: "is too short (minimum is %{count} characters)"
      wrong_length:
        one: "is the wrong length (should be 1 character)"
        other: "is the wrong length (should be %{count} characters)"
      not_a_number: "is not a number"
      not_an_integer: "must be an integer"
      greater_than: "must be greater than %{count}"
      greater_than_or_equal_to: "must be greater than or equal to %{count}"
      equal_to: "must be equal to %{count}"
      less_than: "must be less than %{count}"
      less_than_or_equal_to: "must be less than or equal to %{count}"
      other_than: "must be other than %{count}"
      in: "must be in %{count}"
      odd: "must be odd"
      even: "must be even"
