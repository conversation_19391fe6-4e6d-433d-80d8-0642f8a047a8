08-08-2023
  - Version 1.1.5
  - Update mime types from upstream

08-08-2023
  - Version 1.1.4
  - Version 1.1.3 had issues on Windows which does not support pread, added a polyfill

04-08-2023
  - Version 1.1.3
  - Added fork safety by migrating from seek+read to pread

11-10-2021
  - Version 1.1.2
  - update mime types from upstream

23-08-2021
  - Version 1.1.1
  - update mime types from upstream

05-04-2021
  - Version 1.1.0
  - MiniMime.lookup_by_extension is now case insensitive

26-03-2021
  - Version 1.0.3
  - Update mime types from upstream

08-07-2019
  - Version 1.0.2
  - Update mime types from upstream

14-08-2018
  - Version 1.0.1
  - Update mime types from upstream
  - Add lookup_by_extension to the public API

08-11-2017
  - Version 1.0.0
  - Other than the version number, no difference from 0.1.4

11-08-2017
  - Version 0.1.4
  - Return preferred extension when looking up by content type


28-03-2016

  - Version 0.1.3
  - Prefer non-obsolete mime types to obsolete ones

14-12-2016

  - Version 0.1.2
  - Backwards compat with ancient Ruby to match mail gem

14-12-2016

  - Version 0.1.1
  - Adjusted API to be more consistent

14-12-2016

  - Version 0.1.0
  - Initial version
