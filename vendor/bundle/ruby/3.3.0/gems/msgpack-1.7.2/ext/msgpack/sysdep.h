/*
 * MessagePack for Ruby
 *
 * Copyright (C) 2008-2013 Sad<PERSON><PERSON>
 *
 *    Licensed under the Apache License, Version 2.0 (the "License");
 *    you may not use this file except in compliance with the License.
 *    You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *    Unless required by applicable law or agreed to in writing, software
 *    distributed under the License is distributed on an "AS IS" BASIS,
 *    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *    See the License for the specific language governing permissions and
 *    limitations under the License.
 */
#ifndef MSGPACK_RUBY_SYSDEP_H__
#define MSGPACK_RUBY_SYSDEP_H__

#include "sysdep_types.h"
#include "sysdep_endian.h"


#define UNUSED(var) ((void)var)


#ifdef __LITTLE_ENDIAN__

/* _msgpack_be16 */
#ifdef _WIN32
#  if defined(ntohs)
#    define _msgpack_be16(x) ntohs(x)
#  elif defined(_byteswap_ushort) || (defined(_MSC_VER) && _MSC_VER >= 1400)
#    define _msgpack_be16(x) ((uint16_t)_byteswap_ushort((unsigned short)x))
#  else
#    define _msgpack_be16(x) ( \
        ( \
          ((((uint16_t)x) <<  8) ) | \
          ((((uint16_t)x) >>  8) ) \
        ) \
          & 0x0000FFFF )
#  endif
#else
#  define _msgpack_be16(x) ntohs(x)
#endif

/* _msgpack_be32 */
#ifdef _WIN32
#  if defined(ntohl)
#    define _msgpack_be32(x) ntohl(x)
#  elif defined(_byteswap_ulong) || (defined(_MSC_VER) && _MSC_VER >= 1400)
#    define _msgpack_be32(x) ((uint32_t)_byteswap_ulong((unsigned long)x))
#  else
#    define _msgpack_be32(x) \
        ( ((((uint32_t)x) << 24)               ) | \
          ((((uint32_t)x) <<  8) & 0x00ff0000U ) | \
          ((((uint32_t)x) >>  8) & 0x0000ff00U ) | \
          ((((uint32_t)x) >> 24)               ) )
#  endif
#else
#  define _msgpack_be32(x) ntohl(x)
#endif

/* _msgpack_be64 */
#if defined(_byteswap_uint64) || (defined(_MSC_VER) && _MSC_VER >= 1400)
#  define _msgpack_be64(x) (_byteswap_uint64(x))
#elif defined(bswap_64)
#  define _msgpack_be64(x) bswap_64(x)
#elif defined(__DARWIN_OSSwapInt64)
#  define _msgpack_be64(x) __DARWIN_OSSwapInt64(x)
#else
#define _msgpack_be64(x) \
    ( ((((uint64_t)x) << 56)                         ) | \
      ((((uint64_t)x) << 40) & 0x00ff000000000000ULL ) | \
      ((((uint64_t)x) << 24) & 0x0000ff0000000000ULL ) | \
      ((((uint64_t)x) <<  8) & 0x000000ff00000000ULL ) | \
      ((((uint64_t)x) >>  8) & 0x00000000ff000000ULL ) | \
      ((((uint64_t)x) >> 24) & 0x0000000000ff0000ULL ) | \
      ((((uint64_t)x) >> 40) & 0x000000000000ff00ULL ) | \
      ((((uint64_t)x) >> 56)                         ) )
#endif

#else  /* big endian */
#define _msgpack_be16(x) (x)
#define _msgpack_be32(x) (x)
#define _msgpack_be64(x) (x)

#endif


/* _msgpack_be_float */
#define _msgpack_be_float(x) _msgpack_be32(x)

/* _msgpack_be_double */
#if defined(__arm__) && !(__ARM_EABI__)
/* ARM OABI */
#define _msgpack_be_double(x) \
    ( (((x) & 0xFFFFFFFFUL) << 32UL) | ((x) >> 32UL) )
#else
/* the other ABI */
#define _msgpack_be_double(x) _msgpack_be64(x)
#endif

/* _msgpack_bsp32 */
#if defined(_MSC_VER)
#define _msgpack_bsp32(name, val) \
    long name; \
    _BitScanForward(&name, val)
#else
#define _msgpack_bsp32(name, val) \
    int name = __builtin_ctz(val)
/* TODO default impl for _msgpack_bsp32 */
#endif


#endif

