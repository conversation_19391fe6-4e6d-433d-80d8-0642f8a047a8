/*
 * MessagePack for Ruby
 *
 * Copyright (C) 2008-2013 Saday<PERSON>
 *
 *    Licensed under the Apache License, Version 2.0 (the "License");
 *    you may not use this file except in compliance with the License.
 *    You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *    Unless required by applicable law or agreed to in writing, software
 *    distributed under the License is distributed on an "AS IS" BASIS,
 *    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *    See the License for the specific language governing permissions and
 *    limitations under the License.
 */
#ifndef MSGPACK_RUBY_SYSDEP_TYPES_H__
#define MSGPACK_RUBY_SYSDEP_TYPES_H__

#include <string.h>
#include <stdlib.h>

#include <stddef.h>

#if defined(_MSC_VER) && _MSC_VER < 1600
typedef __int8 int8_t;
typedef unsigned __int8 uint8_t;
typedef __int16 int16_t;
typedef unsigned __int16 uint16_t;
typedef __int32 int32_t;
typedef unsigned __int32 uint32_t;
typedef __int64 int64_t;
typedef unsigned __int64 uint64_t;

#elif defined(_MSC_VER)  // && _MSC_VER >= 1600
#include <stdint.h>

#else
#include <stdint.h>
#include <stdbool.h>
#endif


#endif

