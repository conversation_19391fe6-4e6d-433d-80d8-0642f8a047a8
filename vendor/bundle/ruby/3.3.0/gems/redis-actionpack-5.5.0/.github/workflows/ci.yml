name: CI

on:
  push:
    branches:
      - master
    tags-ignore: [v*]
  pull_request:
    branches:
      - master

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  test:
    name: "test (ruby: ${{ matrix.ruby }}, rails: ${{ matrix.rails }})"
    runs-on: ubuntu-latest
    continue-on-error: ${{ contains(matrix.ruby, 'head') }}
    strategy:
      fail-fast: false
      matrix:
        ruby:
          - "2.7"
          - "3.0"
          - "3.1"
          - "3.2"
          - "3.3"
          # - 'head'
          # - "jruby"
          # - 'jruby-head'
          # - "truffleruby"
          # - 'truffleruby-head'
        rails:
          - rails_8.0
          - rails_7.2
          - rails_7.1
          - rails_7.0
          - rails_6.1
          - rails_6.0
          - rails_5.2
          - rails_5.1
          - rails_5.0

        exclude:
          - ruby: '2.7'
            rails: 'rails_7.2'
          - ruby: '2.7'
            rails: 'rails_8.0'

          - ruby: '3.0'
            rails: 'rails_5.0'
          - ruby: '3.0'
            rails: 'rails_5.1'
          - ruby: '3.0'
            rails: 'rails_5.2'
          - ruby: '3.0'
            rails: 'rails_7.2'
          - ruby: '3.0'
            rails: 'rails_8.0'

          - ruby: '3.1'
            rails: 'rails_5.0'
          - ruby: '3.1'
            rails: 'rails_5.1'
          - ruby: '3.1'
            rails: 'rails_5.2'
          - ruby: '3.1'
            rails: 'rails_8.0'

          - ruby: '3.2'
            rails: 'rails_5.0'
          - ruby: '3.2'
            rails: 'rails_5.1'
          - ruby: '3.2'
            rails: 'rails_5.2'

          - ruby: '3.3'
            rails: 'rails_5.0'
          - ruby: '3.3'
            rails: 'rails_5.1'
          - ruby: '3.3'
            rails: 'rails_5.2'

    env:
      BUNDLE_GEMFILE: ${{ github.workspace }}/gemfiles/${{ matrix.rails }}.gemfile

    services:
      redis:
        image: redis
        ports:
          - 6379:6379

    steps:
      - uses: actions/checkout@v4
      - uses: ruby/setup-ruby@v1
        with:
          ruby-version: ${{ matrix.ruby }}
          bundler-cache: true
      - run: bundle exec rake
