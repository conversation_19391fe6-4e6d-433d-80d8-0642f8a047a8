# frozen_string_literal: true

appraise 'rails_5.0' do
  gem 'redis-store'
  gem 'redis-rack'
  gem 'actionpack', '~> 5.0.0'
  gem 'tzinfo', '~> 1.2'
end

appraise 'rails_5.1' do
  gem 'redis-store'
  gem 'redis-rack'
  gem 'actionpack', '~> 5.1.0'
  gem 'tzinfo', '~> 1.2'
end

appraise 'rails_5.2' do
  gem 'redis-store'
  gem 'redis-rack'
  gem 'actionpack', '~> 5.2.0'
  gem 'tzinfo', '~> 1.2'
end

appraise 'rails_6.0' do
  gem 'redis-store'
  gem 'redis-rack'
  gem 'actionpack', '~> 6.0.0'
  gem 'tzinfo', '~> 1.2'
end

appraise 'rails_6.1' do
  gem 'redis-store'
  gem 'redis-rack'
  gem 'actionpack', '~> 6.1.0'
end

appraise 'rails_7.0' do
  gem 'redis-store'
  gem 'redis-rack'
  gem 'actionpack', '~> 7.0.0'
end

appraise 'rails_7.1' do
  gem 'redis-store'
  gem 'redis-rack'
  gem 'actionpack', '~> 7.1.0'
end

appraise 'rails_7.2' do
  gem 'redis-store'
  gem 'redis-rack'
  gem 'actionpack', '~> 7.2.0'
end

appraise 'rails_8.0' do
  gem 'redis-store'
  gem 'redis-rack'
  gem 'actionpack', '~> 8.0.0'

  # TODO: remove me when one of this PR is merged:
  # * https://github.com/minitest/minitest-rails/pull/258
  # * https://github.com/minitest/minitest-rails/pull/259
  gem 'minitest-rails', git: 'https://github.com/n-rodriguez/minitest-rails.git', branch: 'wip/rails8'
end
