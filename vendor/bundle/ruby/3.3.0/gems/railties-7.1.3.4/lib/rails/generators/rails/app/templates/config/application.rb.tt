require_relative "boot"

<%= rails_require_statement %>

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module <%= app_const_base %>
  class Application < Rails::Application
<%- if !options.dummy_app? -%>
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults <%= build(:config_target_version) %>
<%- else -%>
    config.load_defaults Rails::VERSION::STRING.to_f
<%- end -%>

    # Please, add to the `ignore` list any other `lib` subdirectories that do
    # not contain `.rb` files, or that should not be reloaded or eager loaded.
    # Common ones are `templates`, `generators`, or `middleware`, for example.
    config.autoload_lib(ignore: %w(assets tasks))

    # Configuration for the application, engines, and railties goes here.
    #
    # These settings can be overridden in specific environments using the files
    # in config/environments, which are processed later.
    #
    # config.time_zone = "Central Time (US & Canada)"
    # config.eager_load_paths << Rails.root.join("extras")
<%- if options.api? -%>

    # Only loads a smaller set of middleware suitable for API only apps.
    # Middleware like session, flash, cookies can be added back manually.
    # Skip views, helpers and assets when generating a new resource.
    config.api_only = true
<%- elsif !depends_on_system_test? -%>

    # Don't generate system test files.
    config.generators.system_tests = nil
<%- end -%>
  end
end
