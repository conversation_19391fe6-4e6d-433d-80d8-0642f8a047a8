# Configure Rails Environment
ENV["RAILS_ENV"] = "test"

require_relative "<%= File.join("..", options[:dummy_path], "config/environment") -%>"
<% unless options[:skip_active_record] -%>
ActiveRecord::Migrator.migrations_paths = [File.expand_path("../<%= options[:dummy_path] -%>/db/migrate", __dir__)]
<% if options[:mountable] -%>
ActiveRecord::Migrator.migrations_paths << File.expand_path("../db/migrate", __dir__)
<% end -%>
<% end -%>
require "rails/test_help"

<% unless options[:skip_active_record] -%>
# Load fixtures from the engine
if ActiveSupport::TestCase.respond_to?(:fixture_paths=)
  ActiveSupport::TestCase.fixture_paths = [File.expand_path("fixtures", __dir__)]
  ActionDispatch::IntegrationTest.fixture_paths = ActiveSupport::TestCase.fixture_paths
  ActiveSupport::TestCase.file_fixture_path = File.expand_path("fixtures", __dir__) + "/files"
  ActiveSupport::TestCase.fixtures :all
end
<% end -%>
