# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

# Ignore bundler config.
/.bundle

# Ignore all environment files (except templates).
/.env*
!/.env*.erb

# Ignore all logfiles and tempfiles.
/log/*
/tmp/*
<% if keeps? -%>
!/log/.keep
!/tmp/.keep

# Ignore pidfiles, but keep the directory.
/tmp/pids/*
!/tmp/pids/
!/tmp/pids/.keep
<% end -%>

# Ignore storage (uploaded files in development and any SQLite databases).
/storage/*
<% if keeps? -%>
!/storage/.keep
/tmp/storage/*
!/tmp/storage/
!/tmp/storage/.keep
<% end -%>
<% unless options.api? -%>

/public/assets
<% end -%>
