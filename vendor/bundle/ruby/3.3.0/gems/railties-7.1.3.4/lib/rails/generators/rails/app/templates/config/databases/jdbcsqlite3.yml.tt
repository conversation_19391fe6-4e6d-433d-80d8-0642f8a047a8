# SQLite. Versions 3.8.0 and up are supported.
#   gem "activerecord-jdbcsqlite3-adapter"
#
# Configure Using Gemfile
# gem "activerecord-jdbcsqlite3-adapter"
#
default: &default
  adapter: sqlite3
  pool: <%%= ENV.fetch("RAILS_MAX_THREADS") { 5 } %>

development:
  <<: *default
  database: storage/development.sqlite3

# Warning: The database defined as "test" will be erased and
# re-generated from your development database when you run "rake".
# Do not set this db to the same as development or production.
test:
  <<: *default
  database: storage/test.sqlite3

production:
  <<: *default
  database: storage/production.sqlite3
