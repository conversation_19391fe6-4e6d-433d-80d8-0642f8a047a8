Description:
    Generates a new resource including an empty model and controller suitable
    for a RESTful, resource-oriented application. Pass the singular model name,
    either CamelCased or under_scored, as the first argument, and an optional
    list of attribute pairs.

    Attribute pairs are field:type arguments specifying the
    model's attributes. Timestamps are added by default, so you don't have to
    specify them by hand as 'created_at:datetime updated_at:datetime'.

    You don't have to think up every attribute up front, but it helps to
    sketch out a few so you can start working with the model immediately.

    This generator invokes your configured ORM and test framework, besides
    creating helpers and add routes to config/routes.rb.

    Unlike the scaffold generator, the resource generator does not create
    views or add any methods to the generated controller.

Examples:
    `bin/rails generate resource post` # no attributes
    `bin/rails generate resource post title:string body:text published:boolean`
    `bin/rails generate resource purchase order_id:integer amount:decimal`
