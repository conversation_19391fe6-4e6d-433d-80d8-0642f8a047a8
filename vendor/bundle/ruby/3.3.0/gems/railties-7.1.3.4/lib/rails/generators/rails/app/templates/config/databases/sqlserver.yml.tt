# SQL Server (2012 or higher required)
#
# Install the adapters and driver
#   gem install tiny_tds
#   gem install activerecord-sqlserver-adapter
#
# Ensure the activerecord adapter and db driver gems are defined in your Gemfile
#   gem "tiny_tds"
#   gem "activerecord-sqlserver-adapter"
#
default: &default
  adapter: sqlserver
  encoding: utf8
  username: sa
  password: <%%= ENV["SA_PASSWORD"] %>
  host: localhost

development:
  <<: *default
  database: <%= app_name %>_development

# Warning: The database defined as "test" will be erased and
# re-generated from your development database when you run "rake".
# Do not set this db to the same as development or production.
test:
  <<: *default
  database: <%= app_name %>_test

# As with config/credentials.yml, you never want to store sensitive information,
# like your database password, in your source code. If your source code is
# ever seen by anyone, they now have access to your database.
#
# Instead, provide the password or a full connection URL as an environment
# variable when you boot the app. For example:
#
#   DATABASE_URL="sqlserver://myuser:mypass@localhost/somedatabase"
#
# If the connection URL is provided in the special DATABASE_URL environment
# variable, Rails will automatically merge its configuration values on top of
# the values provided in this file. Alternatively, you can specify a connection
# URL environment variable explicitly:
#
#   production:
#     url: <%%= ENV["MY_APP_DATABASE_URL"] %>
#
# Read https://guides.rubyonrails.org/configuring.html#configuring-a-database
# for a full overview on how database connection configuration can be specified.
#
production:
  <<: *default
  database: <%= app_name %>_production
  username: <%= app_name %>
  password: <%%= ENV["<%= app_name.upcase %>_DATABASE_PASSWORD"] %>
