Description:
    The Rails `credentials` commands provide access to encrypted credentials,
    so you can safely store access tokens, database passwords, and the like
    safely inside the app without relying on a mess of ENVs.

    This also allows for atomic deploys: no need to coordinate key changes
    to get everything working as the keys are shipped with the code.

Setup:
    Applications after Rails 5.2 automatically have a basic credentials file generated
    that just contains the secret_key_base used by MessageVerifiers/MessageEncryptors, like the ones
    signing and encrypting cookies.

    For applications created prior to Rails 5.2, we'll automatically generate a new
    credentials file in `config/credentials.yml.enc` the first time you run `<%= executable(:edit) %>`.
    If you didn't have a master key saved in `config/master.key`, that'll be created too.

    Don't lose this master key! Put it in a password manager your team can access.
    Should you lose it no one, including you, will be able to access any encrypted
    credentials.

    Don't commit the key! Add `config/master.key` to your source control's
    ignore file. If you use Git, Rails handles this for you.

    <PERSON><PERSON> also looks for the master key in `ENV["RAILS_MASTER_KEY"]`, in case that
    is easier to manage. You could set `RAILS_MASTER_KEY` in a deployment
    configuration, or you could prepend it to your server's start command like so:

        RAILS_MASTER_KEY="very-secret-and-secure" bin/rails server

    If `ENV["RAILS_MASTER_KEY"]` is present, it takes precedence over
    `config/master.key`.

Set up Git to Diff Credentials:
    Rails provides `<%= executable(:diff) %> --enroll` to instruct Git to call
    `<%= executable(:diff) %>` when `git diff` is run on a credentials file.

    Running the command enrolls the project such that all credentials files use the
    "rails_credentials" diff driver in .gitattributes.

    Additionally since Git requires the driver itself to be set up in a config file
    that isn't tracked Rails automatically ensures it's configured when running
    `<%= executable(:edit) %>`.

    Otherwise each co-worker would have to run enable manually, including on each new
    repo clone.

    To disenroll from this feature, run `<%= executable(:diff) %> --disenroll`.

Editing Credentials:
    This will open a temporary file in `$VISUAL` or `$EDITOR` with the decrypted
    contents to edit the encrypted credentials.

    When the temporary file is next saved the contents are encrypted and written to
    `config/credentials.yml.enc` while the file itself is destroyed to prevent credentials
    from leaking.

Environment Specific Credentials:
    The `credentials` command supports passing an `--environment` option to create an
    environment specific override. That override will take precedence over the
    global `config/credentials.yml.enc` file when running in that environment. So:

        <%= executable(:edit) %> --environment development

    will create `config/credentials/development.yml.enc` with the corresponding
    encryption key in `config/credentials/development.key` if the credentials file
    doesn't exist.

    In addition to that, the default credentials lookup paths can be overridden through
    `config.credentials.content_path` and `config.credentials.key_path`.

    Just as with `config/master.key`, `ENV["RAILS_MASTER_KEY"]` takes precedence
    over any environment specific or specially configured key files.
