require "test_helper"

<% module_namespacing do -%>
class <%= class_name %>ControllerTest < ActionDispatch::IntegrationTest
<% if mountable_engine? -%>
  include Engine.routes.url_helpers

<% end -%>
<% if actions.empty? || options[:skip_routes] -%>
  # test "the truth" do
  #   assert true
  # end
<% else -%>
<% actions.each do |action| -%>
  test "should get <%= action %>" do
    get <%= url_helper_prefix %>_<%= action %>_url
    assert_response :success
  end
<%= "\n" unless action == actions.last -%>
<% end -%>
<% end -%>
end
<% end -%>
