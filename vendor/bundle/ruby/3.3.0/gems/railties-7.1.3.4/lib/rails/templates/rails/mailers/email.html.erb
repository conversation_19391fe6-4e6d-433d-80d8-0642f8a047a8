<!DOCTYPE html>
<html><head>
<title><%= @page_title %></title>
<meta name="viewport" content="width=device-width" />
<style type="text/css">
  html, body, iframe {
    height: 100%;
  }

  body {
    margin: 0;
  }

  header {
    width: 100%;
    padding: 10px 0 0 0;
    margin: 0;
    background: white;
    font: 12px "Lucida Grande", sans-serif;
    border-bottom: 1px solid #dedede;
    overflow: hidden;
  }

  dl {
    margin: 0 0 10px 0;
    padding: 0;
  }

  dt {
    width: 80px;
    padding: 1px;
    float: left;
    clear: left;
    text-align: right;
    color: #7f7f7f;
  }

  dd {
    margin-left: 90px; /* 80px + 10px */
    padding: 1px;
  }

  dd:empty:before {
    content: "\00a0"; // &nbsp;
  }

  th {
    font-weight: inherit;
    color: #7f7f7f;
    text-align: right;
    white-space: nowrap;
  }

  iframe {
    border: 0;
    width: 100%;
  }
</style>
</head>

<body>
<header>
  <dl>
    <% if @email.respond_to?(:smtp_envelope_from) && Array(@email.from) != Array(@email.smtp_envelope_from) %>
      <dt>SMTP-From:</dt>
      <dd id="smtp_from"><%= @email.smtp_envelope_from %></dd>
    <% end %>

    <% if @email.respond_to?(:smtp_envelope_to) && @email.to != @email.smtp_envelope_to %>
      <dt>SMTP-To:</dt>
      <dd id="smtp_to"><%= @email.smtp_envelope_to %></dd>
    <% end %>

    <dt>From:</dt>
    <dd id="from"><%= @email.header['from'] %></dd>

    <% if @email.reply_to %>
      <dt>Reply-To:</dt>
      <dd id="reply_to"><%= @email.header['reply-to'] %></dd>
    <% end %>

    <dt>To:</dt>
    <dd id="to"><%= @email.header['to'] %></dd>

    <% if @email.cc %>
      <dt>CC:</dt>
      <dd id="cc"><%= @email.header['cc'] %></dd>
    <% end %>

    <% if @email.bcc %>
      <dt>BCC:</dt>
      <dd id="bcc"><%= @email.header['bcc'] %></dd>
    <% end %>

    <dt>Date:</dt>
    <dd id="date"><%= Time.current.rfc2822 %></dd>

    <dt>Subject:</dt>
    <dd><strong id="subject"><%= @email.subject %></strong></dd>

    <% unless @email.attachments.nil? || @email.attachments.empty? %>
      <dt>Attachments:</dt>
      <dd>
        <% @email.attachments.each do |a| %>
          <% filename = a.respond_to?(:original_filename) ? a.original_filename : a.filename %>
          <%= link_to filename, "data:application/octet-stream;charset=utf-8;base64,#{Base64.encode64(a.body.to_s)}", download: filename %>
        <% end %>
      </dd>
    <% end %>

    <dt>Format:</dt>
    <% if @email.html_part && @email.text_part %>
      <dd>
        <select id="part" onchange="refreshBody(false);">
          <option <%= request.format == Mime[:html] ? 'selected' : '' %> value="<%= part_query('text/html') %>">View as HTML email</option>
          <option <%= request.format == Mime[:text] ? 'selected' : '' %> value="<%= part_query('text/plain') %>">View as plain-text email</option>
        </select>
      </dd>
    <% elsif @part %>
      <dd id="mime_type" data-mime-type="<%= part_query(@part.mime_type) %>"><%= @part.mime_type == 'text/html' ? 'HTML email' : 'plain-text email' %></dd>
    <% else %>
      <dd id="mime_type" data-mime-type=""></dd>
    <% end %>

    <% if I18n.available_locales.count > 1 %>
      <dt>Locale:</dt>
      <dd>
        <select id="locale" onchange="refreshBody(true);">
          <% I18n.available_locales.each do |locale| %>
            <option <%= I18n.locale == locale ? 'selected' : '' %> value="<%= locale_query(locale) %>"><%= locale %></option>
          <% end %>
        </select>
      </dd>
    <% end %>

    <% unless @email.header_fields.nil? || @email.header_fields.empty? %>
      <dt>Headers:</dt>
      <dd>
        <details>
          <summary>Show all headers</summary>
          <table>
          <% @email.header_fields.each do |field| %>
            <tr>
              <th><%= field.name %>:</th>
              <td><%= field.value %></td>
            </tr>
          <% end %>
          </table>
        </details>
      </dd>
    <% end %>

    <dt>EML File:</dt>
    <dd><%= link_to "Download", action: :download %></dd>
  </dl>
</header>

<% if @part && @part.mime_type %>
  <iframe name="messageBody" src="?<%= part_query(@part.mime_type) %>"></iframe>
<% else %>
  <p>
    You are trying to preview an email that does not have any content.
    This is probably because the <em>mail</em> method has not been called in <em><%= @preview.preview_name %>#<%= @email_action %></em>.
  </p>
<% end %>

<script>
  function refreshBody(reload) {
    var part_select = document.querySelector('select#part');
    var locale_select = document.querySelector('select#locale');
    var iframe = document.getElementsByName('messageBody')[0];
    var part_param = part_select ?
      part_select.options[part_select.selectedIndex].value :
      document.querySelector('#mime_type').dataset.mimeType;
    var locale_param = locale_select ? locale_select.options[locale_select.selectedIndex].value : null;
    var fresh_location;
    if (locale_param) {
      fresh_location = '?' + part_param + '&' + locale_param;
    } else {
      fresh_location = '?' + part_param;
    }
    iframe.contentWindow.location = fresh_location;

    var url = location.pathname.replace(/\.(txt|html)$/, '');
    var format = /html/.test(part_param) ? '.html' : '.txt';
    var state_to_replace = locale_param ? (url + format + '?' + locale_param) : (url + format);

    if (reload) {
      location.href = state_to_replace;
    } else if (history.replaceState) {
      window.history.replaceState({}, '', state_to_replace);
    }
  }
</script>

</body>
</html>
