Description: 
    The Rails `encrypted` commands provide access to encrypted files or configurations.
    See the `Rails.application.encrypted` documentation for using them in your app.

Encryption Keys:
    By default, <PERSON><PERSON> looks for the encryption key in `ENV["RAILS_MASTER_KEY"]` or
    `config/master.key`, but that lookup can be overridden with `--key`:

        <%= executable(:edit) %> config/encrypted_file.yml.enc --key config/encrypted_file.key

    Don't commit the key! Add it to your source control's ignore file. If you use
    Git, Rails handles this for you.

Examples:
    To edit or create an encrypted file use:

        <%= executable(:edit) %> config/encrypted_file.yml.enc

    This opens a temporary file in `$VISUAL` or `$EDITOR` with the decrypted contents for editing.

    To print the decrypted contents of an encrypted file use:

        <%= executable(:show) %> config/encrypted_file.yml.enc
