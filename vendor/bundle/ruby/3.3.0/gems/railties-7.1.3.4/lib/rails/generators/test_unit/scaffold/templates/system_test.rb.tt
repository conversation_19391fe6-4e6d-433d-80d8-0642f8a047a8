require "application_system_test_case"

<% module_namespacing do -%>
class <%= class_name.pluralize %>Test < ApplicationSystemTestCase
  setup do
    @<%= singular_table_name %> = <%= fixture_name %>(:one)
  end

  test "visiting the index" do
    visit <%= plural_table_name %>_url
    assert_selector "h1", text: "<%= human_name.pluralize %>"
  end

  test "should create <%= human_name.downcase %>" do
    visit <%= plural_table_name %>_url
    click_on "New <%= human_name.downcase %>"

    <%- attributes_hash.each do |attr, value| -%>
    <%- if boolean?(attr) -%>
    check "<%= attr.humanize %>" if <%= value %>
    <%- else -%>
    fill_in "<%= attr.humanize %>", with: <%= value %>
    <%- end -%>
    <%- end -%>
    click_on "Create <%= human_name %>"

    assert_text "<%= human_name %> was successfully created"
    click_on "Back"
  end

  test "should update <%= human_name %>" do
    visit <%= singular_table_name %>_url(@<%= singular_table_name %>)
    click_on "Edit this <%= human_name.downcase %>", match: :first

    <%- attributes_hash.each do |attr, value| -%>
    <%- if boolean?(attr) -%>
    check "<%= attr.humanize %>" if <%= value %>
    <%- else -%>
    fill_in "<%= attr.humanize %>", with: <%= value %>
    <%- end -%>
    <%- end -%>
    click_on "Update <%= human_name %>"

    assert_text "<%= human_name %> was successfully updated"
    click_on "Back"
  end

  test "should destroy <%= human_name %>" do
    visit <%= singular_table_name %>_url(@<%= singular_table_name %>)
    click_on "Destroy this <%= human_name.downcase %>", match: :first

    assert_text "<%= human_name %> was successfully destroyed"
  end
end
<% end -%>
