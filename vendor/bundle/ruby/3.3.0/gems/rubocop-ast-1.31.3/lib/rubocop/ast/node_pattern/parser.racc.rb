# frozen_string_literal: true
#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.7.1
# from Racc grammar file "".
#

require 'racc/parser.rb'
module RuboCop
  module AST
    class NodePattern
      class Parser < Racc::Parser
##### State transition tables begin ###

racc_action_table = [
    14,    15,    16,    21,    18,    17,    19,    10,    11,    12,
    60,    22,    20,     4,    24,     5,    40,     6,     7,     8,
    28,    23,    56,    50,    40,    61,    43,    66,    51,    51,
    58,    14,    15,    16,    21,    18,    17,    19,    10,    11,
    12,   nil,    22,    20,     4,   nil,     5,   nil,     6,     7,
     8,    28,    23,   nil,   nil,   -34,    14,    15,    16,    21,
    18,    17,    19,    10,    11,    12,   nil,    22,    20,     4,
   nil,     5,   nil,     6,     7,     8,     9,    23,    14,    15,
    16,    21,    18,    17,    19,    10,    11,    12,   nil,    22,
    20,     4,   nil,     5,   nil,     6,     7,     8,    28,    23,
    14,    15,    16,    21,    18,    17,    19,    10,    11,    12,
   nil,    22,    20,     4,   nil,     5,   nil,     6,     7,     8,
     9,    23,    14,    15,    16,    21,    18,    17,    19,    10,
    11,    12,   nil,    22,    20,     4,   nil,     5,   nil,     6,
     7,     8,     9,    23,    14,    15,    16,    21,    18,    17,
    19,    10,    11,    12,   nil,    22,    20,     4,   nil,     5,
   nil,     6,     7,     8,     9,    23,    14,    15,    16,    21,
    18,    17,    19,    10,    11,    12,   nil,    22,    20,     4,
   nil,     5,   nil,     6,     7,     8,     9,    23,    14,    15,
    16,    21,    18,    17,    19,    10,    11,    12,   nil,    22,
    20,     4,   nil,     5,   nil,     6,     7,     8,     9,    23,
    14,    15,    16,    21,    18,    17,    19,    10,    11,    12,
   nil,    22,    20,     4,    44,     5,   nil,     6,     7,     8,
    28,    23,    14,    15,    16,    21,    18,    17,    19,    10,
    11,    12,   nil,    22,    20,     4,   nil,     5,   nil,     6,
     7,     8,     9,    23,    14,    15,    16,    21,    18,    17,
    19,    10,    11,    12,   nil,    22,    20,     4,   nil,     5,
    52,     6,     7,     8,     9,    23,    14,    15,    16,    21,
    18,    17,    19,    10,    11,    12,   nil,    22,    20,     4,
   nil,     5,   nil,     6,     7,     8,     9,    23,    14,    15,
    16,    21,    18,    17,    19,    10,    11,    12,   nil,    22,
    20,     4,   nil,     5,   nil,     6,     7,     8,     9,    23,
    14,    15,    16,    21,    18,    17,    19,    10,    11,    12,
   nil,    22,    20,     4,   nil,     5,   nil,     6,     7,     8,
     9,    23,    -1,    -1,    -1,    -2,    -2,    -2,    47,    48,
    49 ]

racc_action_check = [
    42,    42,    42,    42,    42,    42,    42,    42,    42,    42,
    54,    42,    42,    42,     1,    42,    10,    42,    42,    42,
    42,    42,    42,    30,    11,    54,    24,    62,    30,    63,
    42,    59,    59,    59,    59,    59,    59,    59,    59,    59,
    59,   nil,    59,    59,    59,   nil,    59,   nil,    59,    59,
    59,    59,    59,   nil,   nil,    59,     0,     0,     0,     0,
     0,     0,     0,     0,     0,     0,   nil,     0,     0,     0,
   nil,     0,   nil,     0,     0,     0,     0,     0,     4,     4,
     4,     4,     4,     4,     4,     4,     4,     4,   nil,     4,
     4,     4,   nil,     4,   nil,     4,     4,     4,     4,     4,
     5,     5,     5,     5,     5,     5,     5,     5,     5,     5,
   nil,     5,     5,     5,   nil,     5,   nil,     5,     5,     5,
     5,     5,     6,     6,     6,     6,     6,     6,     6,     6,
     6,     6,   nil,     6,     6,     6,   nil,     6,   nil,     6,
     6,     6,     6,     6,     7,     7,     7,     7,     7,     7,
     7,     7,     7,     7,   nil,     7,     7,     7,   nil,     7,
   nil,     7,     7,     7,     7,     7,     8,     8,     8,     8,
     8,     8,     8,     8,     8,     8,   nil,     8,     8,     8,
   nil,     8,   nil,     8,     8,     8,     8,     8,     9,     9,
     9,     9,     9,     9,     9,     9,     9,     9,   nil,     9,
     9,     9,   nil,     9,   nil,     9,     9,     9,     9,     9,
    27,    27,    27,    27,    27,    27,    27,    27,    27,    27,
   nil,    27,    27,    27,    27,    27,   nil,    27,    27,    27,
    27,    27,    28,    28,    28,    28,    28,    28,    28,    28,
    28,    28,   nil,    28,    28,    28,   nil,    28,   nil,    28,
    28,    28,    28,    28,    33,    33,    33,    33,    33,    33,
    33,    33,    33,    33,   nil,    33,    33,    33,   nil,    33,
    33,    33,    33,    33,    33,    33,    40,    40,    40,    40,
    40,    40,    40,    40,    40,    40,   nil,    40,    40,    40,
   nil,    40,   nil,    40,    40,    40,    40,    40,    50,    50,
    50,    50,    50,    50,    50,    50,    50,    50,   nil,    50,
    50,    50,   nil,    50,   nil,    50,    50,    50,    50,    50,
    61,    61,    61,    61,    61,    61,    61,    61,    61,    61,
   nil,    61,    61,    61,   nil,    61,   nil,    61,    61,    61,
    61,    61,    25,    25,    25,    26,    26,    26,    29,    29,
    29 ]

racc_action_pointer = [
    54,    14,   nil,   nil,    76,    98,   120,   142,   164,   186,
     4,    12,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    26,   315,   318,   208,   230,   321,
    -2,   nil,   nil,   252,   nil,   nil,   nil,   nil,   nil,   nil,
   274,   nil,    -2,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   296,   nil,   nil,   nil,    -6,   nil,   nil,   nil,   nil,    29,
   nil,   318,     1,    -1,   nil,   nil,   nil ]

racc_action_default = [
   -47,   -47,    -1,    -2,   -31,   -47,   -47,   -47,   -47,   -47,
   -36,   -36,   -11,   -12,   -13,   -14,   -15,   -16,   -17,   -18,
   -19,   -20,   -21,   -44,   -47,   -23,   -24,   -31,   -32,   -47,
   -47,   -27,   -42,   -47,   -40,    -5,    -6,    -7,    -8,    -9,
   -47,   -10,   -31,    67,    -3,   -43,   -25,   -28,   -29,   -30,
   -47,   -33,    -4,   -41,   -47,   -38,   -22,   -45,   -46,   -31,
   -37,   -47,   -47,   -47,   -35,   -39,   -26 ]

racc_goto_table = [
     1,    33,    27,    25,    26,    34,    35,    36,    37,    38,
    42,    32,    39,    41,    46,    63,    62,    64,    54,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    25,    26,    38,   nil,
   nil,   nil,   nil,    53,    45,   nil,   nil,   nil,   nil,   nil,
    55,    25,    26,   nil,   nil,   nil,    59,   nil,   nil,    57,
    34,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    53,
   nil,    65 ]

racc_goto_check = [
     1,     5,     4,     2,     3,     1,     1,     1,     1,     1,
     8,     9,     6,     6,    10,    11,    12,    13,    14,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,     2,     3,     1,   nil,
   nil,   nil,   nil,     1,     9,   nil,   nil,   nil,   nil,   nil,
     1,     2,     3,   nil,   nil,   nil,     5,   nil,   nil,     9,
     1,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,     1,
   nil,     1 ]

racc_goto_pointer = [
   nil,     0,    -1,     0,    -2,    -4,     2,   nil,   -13,     7,
   -15,   -44,   -43,   -42,   -22 ]

racc_goto_default = [
   nil,    29,     2,     3,   nil,   nil,   nil,    13,   nil,   nil,
   nil,    30,   nil,    31,   nil ]

racc_reduce_table = [
  0, 0, :racc_error,
  1, 34, :_reduce_none,
  1, 34, :_reduce_2,
  3, 35, :_reduce_3,
  3, 35, :_reduce_4,
  2, 35, :_reduce_5,
  2, 35, :_reduce_6,
  2, 35, :_reduce_7,
  2, 35, :_reduce_8,
  2, 35, :_reduce_9,
  2, 35, :_reduce_10,
  1, 35, :_reduce_11,
  1, 35, :_reduce_none,
  1, 40, :_reduce_13,
  1, 40, :_reduce_14,
  1, 40, :_reduce_15,
  1, 40, :_reduce_16,
  1, 40, :_reduce_17,
  1, 40, :_reduce_18,
  1, 40, :_reduce_19,
  1, 40, :_reduce_20,
  1, 40, :_reduce_21,
  3, 36, :_reduce_22,
  1, 42, :_reduce_none,
  1, 42, :_reduce_none,
  2, 42, :_reduce_25,
  5, 42, :_reduce_26,
  1, 42, :_reduce_none,
  1, 43, :_reduce_none,
  1, 43, :_reduce_none,
  1, 43, :_reduce_none,
  0, 44, :_reduce_none,
  1, 44, :_reduce_none,
  2, 46, :_reduce_33,
  0, 45, :_reduce_none,
  1, 45, :_reduce_none,
  0, 39, :_reduce_none,
  3, 39, :_reduce_37,
  1, 47, :_reduce_38,
  3, 47, :_reduce_39,
  1, 38, :_reduce_40,
  2, 38, :_reduce_41,
  1, 37, :_reduce_42,
  2, 37, :_reduce_43,
  0, 41, :_reduce_44,
  2, 41, :_reduce_45,
  2, 41, :_reduce_46 ]

racc_reduce_n = 47

racc_shift_n = 67

racc_token_table = {
  false => 0,
  :error => 1,
  :tSYMBOL => 2,
  :tNUMBER => 3,
  :tSTRING => 4,
  :tWILDCARD => 5,
  :tPARAM_NAMED => 6,
  :tPARAM_CONST => 7,
  :tPARAM_NUMBER => 8,
  :tFUNCTION_CALL => 9,
  :tPREDICATE => 10,
  :tNODE_TYPE => 11,
  :tARG_LIST => 12,
  :tUNIFY => 13,
  :tREGEXP => 14,
  "(" => 15,
  ")" => 16,
  "[" => 17,
  "]" => 18,
  "!" => 19,
  "^" => 20,
  "`" => 21,
  "$" => 22,
  "{" => 23,
  "}" => 24,
  "<" => 25,
  ">" => 26,
  "?" => 27,
  "*" => 28,
  "+" => 29,
  "..." => 30,
  "," => 31,
  "|" => 32 }

racc_nt_base = 33

racc_use_result_var = false

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]
Ractor.make_shareable(Racc_arg) if defined?(Ractor)

Racc_token_to_s_table = [
  "$end",
  "error",
  "tSYMBOL",
  "tNUMBER",
  "tSTRING",
  "tWILDCARD",
  "tPARAM_NAMED",
  "tPARAM_CONST",
  "tPARAM_NUMBER",
  "tFUNCTION_CALL",
  "tPREDICATE",
  "tNODE_TYPE",
  "tARG_LIST",
  "tUNIFY",
  "tREGEXP",
  "\"(\"",
  "\")\"",
  "\"[\"",
  "\"]\"",
  "\"!\"",
  "\"^\"",
  "\"`\"",
  "\"$\"",
  "\"{\"",
  "\"}\"",
  "\"<\"",
  "\">\"",
  "\"?\"",
  "\"*\"",
  "\"+\"",
  "\"...\"",
  "\",\"",
  "\"|\"",
  "$start",
  "node_pattern",
  "node_pattern_no_union",
  "union",
  "variadic_pattern_list",
  "node_pattern_list",
  "args",
  "atom",
  "separated_variadic_patterns",
  "variadic_pattern",
  "repetition",
  "opt_capture",
  "opt_rest",
  "rest",
  "arg_list" ]
Ractor.make_shareable(Racc_token_to_s_table) if defined?(Ractor)

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

# reduce 1 omitted

def _reduce_2(val, _values)
 enforce_unary(val[0])
end

def _reduce_3(val, _values)
 emit_list :sequence, *val
end

def _reduce_4(val, _values)
 emit_list :intersection, *val
end

def _reduce_5(val, _values)
 emit_unary_op :negation, *val
end

def _reduce_6(val, _values)
 emit_unary_op :ascend, *val
end

def _reduce_7(val, _values)
 emit_unary_op :descend, *val
end

def _reduce_8(val, _values)
 emit_capture(*val)
end

def _reduce_9(val, _values)
 emit_call :function_call, *val
end

def _reduce_10(val, _values)
 emit_call :predicate, *val
end

def _reduce_11(val, _values)
 emit_call :node_type, *val
end

# reduce 12 omitted

def _reduce_13(val, _values)
 emit_atom :symbol, *val
end

def _reduce_14(val, _values)
 emit_atom :number, *val
end

def _reduce_15(val, _values)
 emit_atom :string, *val
end

def _reduce_16(val, _values)
 emit_atom :const, *val
end

def _reduce_17(val, _values)
 emit_atom :named_parameter, *val
end

def _reduce_18(val, _values)
 emit_atom :positional_parameter, *val
end

def _reduce_19(val, _values)
 emit_atom :regexp, *val
end

def _reduce_20(val, _values)
 emit_atom :wildcard, *val
end

def _reduce_21(val, _values)
 emit_atom :unify, *val
end

def _reduce_22(val, _values)
 emit_union(*val)
end

# reduce 23 omitted

# reduce 24 omitted

def _reduce_25(val, _values)
        main, repeat_t = val
        emit_unary_op(:repetition, repeat_t, main, repeat_t)

end

def _reduce_26(val, _values)
        opt_capture, bracket, node_pattern_list, opt_rest, close_bracket = val
        node_pattern_list << opt_rest if opt_rest
        main = emit_list :any_order, bracket, node_pattern_list, close_bracket
        emit_capture(opt_capture, main)

end

# reduce 27 omitted

# reduce 28 omitted

# reduce 29 omitted

# reduce 30 omitted

# reduce 31 omitted

# reduce 32 omitted

def _reduce_33(val, _values)
 emit_capture(val[0], emit_atom(:rest, val[1]))
end

# reduce 34 omitted

# reduce 35 omitted

# reduce 36 omitted

def _reduce_37(val, _values)
 val
end

def _reduce_38(val, _values)
 val
end

def _reduce_39(val, _values)
 val[0] << val[2]
end

def _reduce_40(val, _values)
 val
end

def _reduce_41(val, _values)
 val[0] << val[1]
end

def _reduce_42(val, _values)
 val
end

def _reduce_43(val, _values)
 val[0] << val[1]
end

def _reduce_44(val, _values)
 [[]]
end

def _reduce_45(val, _values)
 val[0].last << val[1]; val[0]
end

def _reduce_46(val, _values)
 val[0] << []
end

def _reduce_none(val, _values)
  val[0]
end

      end   # class Parser
    end   # class NodePattern
  end   # module AST
end   # module RuboCop
