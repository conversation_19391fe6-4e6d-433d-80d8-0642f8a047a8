require: rubocop-rspec
AllCops:
  TargetRubyVersion: 2.7
  Exclude:
    - Gemfile
    - warden-jwt_auth.gemspec
    - spec/support/shared_contexts/*rb
    - vendor/**/*
RSpec/NestedGroups:
  Max: 3
RSpec/MessageExpectation:
  EnforcedStyle: 'expect'
Metrics/BlockLength:
  Exclude:
    - "spec/**/*.rb"
Style/SafeNavigation:
  Enabled: false
Layout/EmptyLinesAroundAttributeAccessor:
  Enabled: true
Layout/SpaceAroundMethodCallOperator:
  Enabled: true
Lint/DeprecatedOpenSSLConstant:
  Enabled: true
Lint/MixedRegexpCaptureTypes:
  Enabled: true
Lint/RaiseException:
  Enabled: true
Lint/StructNewOverride:
  Enabled: true
Style/AccessorGrouping:
  Enabled: true
Style/BisectedAttrAccessor:
  Enabled: true
Style/ExponentialNotation:
  Enabled: true
Style/HashEachMethods:
  Enabled: true
Style/HashTransformKeys:
  Enabled: true
Style/HashTransformValues:
  Enabled: true
Style/RedundantAssignment:
  Enabled: true
Style/RedundantFetchBlock:
  Enabled: true
Style/RedundantRegexpCharacterClass:
  Enabled: true
Style/RedundantRegexpEscape:
  Enabled: true
Style/SlicingWithRange:
  Enabled: true
