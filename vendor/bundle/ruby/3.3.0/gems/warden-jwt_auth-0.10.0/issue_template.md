Please, for a bug report fill in the following template. Before that, make sure to read the whole [README](https://github.com/waiting-for-dev/warden-jwt_auth/blob/master/README.md).

Feature requests and questions about `warden-jwt_auth` are also accepted.

## Expected behavior

## Actual behavior

## Steps to Reproduce the Problem

1.
2.
3.

## Debugging information

Provide following information. Please, format pasted output as code. Feel free to remove the secret key value.

- Version of `warden-jwt_auth` in use
- Output of `Warden::JWTAuth.config`
- If your issue is related with not getting a JWT from the server:
  - Involved request path, method and request headers
  - Response headers for that request
- If your issue is related with not being able to revoke a JWT:
  - Involved request path, method and request headers
