language: ruby
cache: bundler
rvm:
  - 2.6
  - 2.7
  - 3.0
  - ruby-head
before_install:
  - gem update --system --no-doc
  - gem install bundler
script:
  - bundle exec rspec
  - bundle exec rubocop
  - bundle exec codeclimate-test-reporter
jobs:
  allow_failures:
    - rvm: ruby-head
addons:
  code_climate:
    repo_token:
      secure: neJ5LVLV6vgeCnerSQjUpLuQDvxEH87iW8swCSWl2hTtPcD/GuwYSeSXnhH72HVHi/9basHHhaYPcE2YeBwBCr39PhiHMNwS5GGGk/RGjKpU/Gt1KXV8KXTbNGT4v+ZMM3cdsdDfe8OnzGguNVsdxseHa3KE2pyuvo2a0swXwKa7BU9VB/3ZoZvvfI3Xr9im4eklWam5yCwVR0FOF7epzmNTKMXcUga2BOc9PV5aVELzLILLCHCJSCupe5Rx8mfcsRoRmZXKduF8Ke3eq8eULvLEo4EGfC107najOqrKt7x8uDVIsuGrP4LUQ4ainmNEb2jIvpjuqAxpusMjhpjCINF1Tn0OXK93OXAp4QKeIYoYEqKtzRxX0TWFNWHB8ombF9HTMF2DmloDZyFRiI40JSMImU0hc4MDxRgiTW5MDWGbohDaJ+9VV6+rIqtlEfLhgj1grFBAroaJce9BB7RQEmfsZPzhC2VXwGxHw/YkJgzBNGq1/9E1DoTY9RPSNTQfSRodhI3XW8LSQSHTBeXZvymVcjeOyYgjzJYviLHR8QS4cXpUALtlFXyaMkPHUBLUn8XsBa5Azfh5y3qPMGiJq1/qaHA4mKj5ls+ngFbzOq82sYGAKgQHj/ZDb+FZMQQanp4jyWADKcpXcmINb9jEQwkU0bjpuhUYtghASxH1Kl8=
