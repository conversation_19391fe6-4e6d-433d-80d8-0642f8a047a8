begin
  require_relative "lib/logger/version"
rescue LoadError # Fallback to load version file in ruby core repository
  require_relative "version"
end

Gem::Specification.new do |spec|
  spec.name          = "logger"
  spec.version       = Logger::VERSION
  spec.authors       = ["<PERSON><PERSON><PERSON>", "SHIBATA Hiroshi"]
  spec.email         = ["<EMAIL>", "<EMAIL>"]

  spec.summary       = %q{Provides a simple logging utility for outputting messages.}
  spec.description   = %q{Provides a simple logging utility for outputting messages.}
  spec.homepage      = "https://github.com/ruby/logger"
  spec.licenses      = ["Ruby", "BSD-2-Clause"]

  spec.files         = Dir.glob("lib/**/*.rb") + ["logger.gemspec"]
  spec.require_paths = ["lib"]

  spec.required_ruby_version = ">= 2.5.0"
end
