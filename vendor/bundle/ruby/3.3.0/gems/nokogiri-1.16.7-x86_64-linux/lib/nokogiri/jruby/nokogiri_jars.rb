# this is a generated file, to avoid over-writing it just delete this comment
begin
  require 'jar_dependencies'
rescue LoadError
  require 'xalan/serializer/2.7.3/serializer-2.7.3.jar'
  require 'net/sourceforge/htmlunit/neko-htmlunit/2.63.0/neko-htmlunit-2.63.0.jar'
  require 'nu/validator/jing/20200702VNU/jing-20200702VNU.jar'
  require 'xerces/xercesImpl/2.12.2/xercesImpl-2.12.2.jar'
  require 'net/sf/saxon/Saxon-HE/9.6.0-4/Saxon-HE-9.6.0-4.jar'
  require 'xalan/xalan/2.7.3/xalan-2.7.3.jar'
  require 'xml-apis/xml-apis/1.4.01/xml-apis-1.4.01.jar'
  require 'org/nokogiri/nekodtd/0.1.11.noko2/nekodtd-0.1.11.noko2.jar'
  require 'isorelax/isorelax/20030108/isorelax-20030108.jar'
end

if defined? Jars
  require_jar 'xalan', 'serializer', '2.7.3'
  require_jar 'net.sourceforge.htmlunit', 'neko-htmlunit', '2.63.0'
  require_jar 'nu.validator', 'jing', '20200702VNU'
  require_jar 'xerces', 'xercesImpl', '2.12.2'
  require_jar 'net.sf.saxon', 'Saxon-HE', '9.6.0-4'
  require_jar 'xalan', 'xalan', '2.7.3'
  require_jar 'xml-apis', 'xml-apis', '1.4.01'
  require_jar 'org.nokogiri', 'nekodtd', '0.1.11.noko2'
  require_jar 'isorelax', 'isorelax', '20030108'
end

module Nokogiri
  # generated by the :vendor_jars rake task
  JAR_DEPENDENCIES = {
    "isorelax:isorelax" => "20030108",
    "net.sf.saxon:Saxon-HE" => "9.6.0-4",
    "net.sourceforge.htmlunit:neko-htmlunit" => "2.63.0",
    "nu.validator:jing" => "20200702VNU",
    "org.nokogiri:nekodtd" => "0.1.11.noko2",
    "xalan:serializer" => "2.7.3",
    "xalan:xalan" => "2.7.3",
    "xerces:xercesImpl" => "2.12.2",
    "xml-apis:xml-apis" => "1.4.01",
  }.freeze
  XERCES_VERSION = JAR_DEPENDENCIES["xerces:xercesImpl"]
  NEKO_VERSION = JAR_DEPENDENCIES["net.sourceforge.htmlunit:neko-htmlunit"]
end
