# frozen_string_literal: true
#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.6.0
# from Racc grammar file "".
#

require 'racc/parser.rb'


require_relative "parser_extras"

module Nokogiri
  module CSS
    # :nodoc: all
    class Parser < Racc::Parser
    end
  end
end

module Nokogiri
  module CSS
    class Parser < Racc::Parser


def unescape_css_identifier(identifier)
  identifier.gsub(/\\(?:([^0-9a-fA-F])|([0-9a-fA-F]{1,6})\s?)/){ |m| $1 || [$2.hex].pack('U') }
end

def unescape_css_string(str)
  str.gsub(/\\(?:([^0-9a-fA-F])|([0-9a-fA-F]{1,6})\s?)/) do |m|
    if $1=="\n"
      ''
    else
      $1 || [$2.hex].pack('U')
    end
  end
end
##### State transition tables begin ###

racc_action_table = [
    27,    11,    38,    99,    36,    12,    40,    26,    48,    25,
    49,    27,   100,    12,    30,    36,   105,    99,   -26,    28,
    25,   -26,    26,    27,    29,    14,    21,    23,    80,    30,
    28,    36,    72,    26,   -26,    29,    14,    21,    23,    27,
    30,    91,    56,    36,    97,    96,    43,    29,    25,    26,
    27,    92,    94,    21,    36,    95,    30,    98,    28,    25,
   101,    26,   102,    29,    14,    21,    23,    96,    30,    28,
    36,    36,    26,   103,    29,    14,    21,    23,    27,    30,
   108,   107,    36,   109,   106,    43,    43,    25,    26,    26,
    27,   110,    21,    21,   111,    30,    30,    28,    99,    50,
    26,    53,    29,    14,    21,    23,    36,    30,    36,    56,
    61,    64,   113,    66,    29,    14,   116,    36,   118,    36,
   nil,    43,   nil,    43,    26,   nil,    26,    14,    21,    23,
    21,    30,    43,    30,    43,    26,   nil,    26,    36,    21,
    36,    21,    30,    25,    30,   nil,   nil,   nil,   nil,   nil,
   nil,    61,    62,    43,    60,    43,    26,   nil,    26,   nil,
    21,    23,    21,    30,    57,    30,    88,    89,    14,   nil,
   nil,    88,    89,   nil,   nil,   nil,   nil,    84,    85,    86,
   nil,    87,    84,    85,    86,    83,    87,   nil,    61,    93,
    83,    66,    61,    93,   nil,    66,    61,    93,   nil,    66,
    61,    93,   nil,    66,   nil,    14,   nil,    61,    93,    14,
    66,   nil,   nil,    14,   nil,   nil,   nil,    14,     4,     5,
    10,   nil,   nil,   nil,    14,     4,     5,    47,     6,   nil,
     8,     7,     4,     5,    10,     6,   nil,     8,     7,   nil,
   nil,   nil,     6,   nil,     8,     7 ]

racc_action_check = [
     3,     1,    11,    64,     3,    70,    14,    17,    21,     3,
    24,     9,    62,     1,    17,     9,    70,    62,    25,     3,
     9,    64,     3,    30,     3,     3,     3,     3,    49,     3,
     9,    16,    30,     9,    50,     9,     9,     9,     9,    12,
     9,    53,    30,    12,    60,    60,    16,    30,    12,    16,
    46,    54,    58,    16,    46,    59,    16,    61,    12,    46,
    63,    12,    65,    12,    12,    12,    12,    66,    12,    46,
    31,    32,    46,    67,    46,    46,    46,    46,    47,    46,
    82,    82,    47,    82,    81,    31,    32,    47,    31,    32,
    26,    90,    31,    32,    92,    31,    32,    47,    93,    26,
    47,    26,    47,    47,    47,    47,    28,    47,    33,    26,
    28,    28,    97,    28,    26,    26,   100,    34,   113,    35,
   nil,    28,   nil,    33,    28,   nil,    33,    28,    28,    28,
    33,    28,    34,    33,    35,    34,   nil,    35,    43,    34,
    68,    35,    34,    43,    35,   nil,   nil,   nil,   nil,   nil,
   nil,    27,    27,    43,    27,    68,    43,   nil,    68,   nil,
    43,    43,    68,    43,    27,    68,    51,    51,    27,   nil,
   nil,    52,    52,   nil,   nil,   nil,   nil,    51,    51,    51,
   nil,    51,    52,    52,    52,    51,    52,   nil,    56,    56,
    52,    56,    96,    96,   nil,    96,    98,    98,   nil,    98,
    99,    99,   nil,    99,   nil,    56,   nil,   101,   101,    96,
   101,   nil,   nil,    98,   nil,   nil,   nil,    99,     0,     0,
     0,   nil,   nil,   nil,   101,    20,    20,    20,     0,   nil,
     0,     0,    29,    29,    29,    20,   nil,    20,    20,   nil,
   nil,   nil,    29,   nil,    29,    29 ]

racc_action_pointer = [
   211,     1,   nil,    -2,   nil,   nil,   nil,   nil,   nil,     9,
   nil,     2,    37,   nil,    -5,   nil,    25,   -17,   nil,   nil,
   218,    -3,   nil,   nil,   -20,   -12,    88,   141,   100,   225,
    21,    64,    65,   102,   111,   113,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   132,   nil,   nil,    48,    76,   nil,    17,
     4,   163,   168,    16,    21,   nil,   178,   nil,    29,    32,
    33,    45,     5,    48,    -9,    39,    55,    50,   134,   nil,
    -7,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    59,    70,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    66,   nil,    83,    86,   nil,   nil,   182,   105,   186,   190,
   103,   197,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   105,   nil,   nil,   nil,   nil,   nil ]

racc_action_default = [
   -81,   -82,    -2,   -27,    -4,    -5,    -6,    -7,    -8,   -27,
   -80,   -82,   -27,    -3,   -82,   -10,   -53,   -12,   -15,   -16,
   -20,   -82,   -22,   -23,   -82,   -25,   -27,   -82,   -27,   -81,
   -82,   -59,   -60,   -61,   -62,   -63,   -64,   -17,   119,    -1,
    -9,   -11,   -52,   -27,   -13,   -14,   -27,   -27,   -21,   -82,
   -32,   -68,   -68,   -82,   -82,   -33,   -82,   -34,   -82,   -82,
   -43,   -44,   -45,   -46,   -25,   -82,   -43,   -82,   -77,   -79,
   -82,   -50,   -51,   -54,   -55,   -56,   -57,   -58,   -18,   -19,
   -24,   -82,   -82,   -69,   -70,   -71,   -72,   -73,   -74,   -75,
   -82,   -30,   -82,   -45,   -35,   -36,   -82,   -49,   -82,   -82,
   -82,   -82,   -37,   -76,   -78,   -38,   -28,   -65,   -66,   -67,
   -29,   -31,   -39,   -82,   -40,   -41,   -48,   -42,   -47 ]

racc_goto_table = [
    58,    42,    13,     1,    46,    52,    19,    68,    37,    71,
    41,    39,    19,    69,    44,    19,    73,    74,    75,    76,
    77,    45,    68,    81,    90,    54,    51,    59,    69,    55,
   nil,   nil,    70,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    78,    79,   nil,   nil,    19,
    19,   nil,   nil,   104,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   112,
   nil,   114,   115,   nil,   117 ]

racc_goto_check = [
    20,    14,     2,     1,     5,    11,     7,     9,     2,    11,
    10,     2,     7,    14,    12,     7,    14,    14,    14,    14,
    14,    13,     9,    19,    19,    17,    18,    21,    14,     7,
   nil,   nil,     1,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,     2,     2,   nil,   nil,     7,
     7,   nil,   nil,    14,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    20,
   nil,    20,    20,   nil,    20 ]

racc_goto_pointer = [
   nil,     3,    -1,   nil,   nil,   -16,   nil,     3,   nil,   -21,
    -6,   -21,    -3,     4,   -15,   nil,   nil,    -1,     0,   -28,
   -27,     0,   nil,   nil,   nil,   nil ]

racc_goto_default = [
   nil,   nil,   nil,     2,     3,     9,    15,    63,    20,    16,
   nil,    17,    34,    33,    18,    32,    22,    24,   nil,   nil,
    65,   nil,    31,    35,    82,    67 ]

racc_reduce_table = [
  0, 0, :racc_error,
  3, 33, :_reduce_1,
  1, 33, :_reduce_2,
  2, 33, :_reduce_3,
  1, 37, :_reduce_4,
  1, 37, :_reduce_5,
  1, 37, :_reduce_6,
  1, 37, :_reduce_7,
  1, 37, :_reduce_8,
  2, 38, :_reduce_9,
  1, 39, :_reduce_10,
  2, 40, :_reduce_11,
  1, 40, :_reduce_none,
  2, 40, :_reduce_13,
  2, 40, :_reduce_14,
  1, 40, :_reduce_15,
  1, 40, :_reduce_none,
  2, 35, :_reduce_17,
  3, 34, :_reduce_18,
  3, 34, :_reduce_19,
  1, 34, :_reduce_none,
  2, 47, :_reduce_21,
  1, 41, :_reduce_none,
  1, 41, :_reduce_23,
  3, 48, :_reduce_24,
  1, 48, :_reduce_25,
  1, 49, :_reduce_26,
  0, 49, :_reduce_none,
  4, 45, :_reduce_28,
  4, 45, :_reduce_29,
  3, 45, :_reduce_30,
  3, 50, :_reduce_31,
  1, 50, :_reduce_32,
  1, 50, :_reduce_none,
  2, 43, :_reduce_34,
  3, 43, :_reduce_35,
  3, 43, :_reduce_36,
  3, 43, :_reduce_37,
  3, 43, :_reduce_38,
  3, 52, :_reduce_39,
  3, 52, :_reduce_40,
  3, 52, :_reduce_41,
  3, 52, :_reduce_42,
  1, 52, :_reduce_none,
  1, 52, :_reduce_none,
  1, 52, :_reduce_45,
  1, 52, :_reduce_none,
  4, 53, :_reduce_47,
  3, 53, :_reduce_48,
  2, 53, :_reduce_49,
  2, 44, :_reduce_50,
  2, 44, :_reduce_51,
  1, 42, :_reduce_none,
  0, 42, :_reduce_none,
  2, 46, :_reduce_54,
  2, 46, :_reduce_55,
  2, 46, :_reduce_56,
  2, 46, :_reduce_57,
  2, 46, :_reduce_58,
  1, 46, :_reduce_none,
  1, 46, :_reduce_none,
  1, 46, :_reduce_none,
  1, 46, :_reduce_none,
  1, 46, :_reduce_none,
  1, 54, :_reduce_64,
  2, 51, :_reduce_65,
  2, 51, :_reduce_66,
  2, 51, :_reduce_67,
  0, 51, :_reduce_none,
  1, 56, :_reduce_69,
  1, 56, :_reduce_70,
  1, 56, :_reduce_71,
  1, 56, :_reduce_72,
  1, 56, :_reduce_73,
  1, 56, :_reduce_74,
  1, 56, :_reduce_75,
  3, 55, :_reduce_76,
  1, 57, :_reduce_none,
  2, 57, :_reduce_none,
  1, 57, :_reduce_none,
  1, 36, :_reduce_none,
  0, 36, :_reduce_none ]

racc_reduce_n = 82

racc_shift_n = 119

racc_token_table = {
  false => 0,
  :error => 1,
  :FUNCTION => 2,
  :INCLUDES => 3,
  :DASHMATCH => 4,
  :LBRACE => 5,
  :HASH => 6,
  :PLUS => 7,
  :GREATER => 8,
  :S => 9,
  :STRING => 10,
  :IDENT => 11,
  :COMMA => 12,
  :NUMBER => 13,
  :PREFIXMATCH => 14,
  :SUFFIXMATCH => 15,
  :SUBSTRINGMATCH => 16,
  :TILDE => 17,
  :NOT_EQUAL => 18,
  :SLASH => 19,
  :DOUBLESLASH => 20,
  :NOT => 21,
  :EQUAL => 22,
  :RPAREN => 23,
  :LSQUARE => 24,
  :RSQUARE => 25,
  :HAS => 26,
  "@" => 27,
  "." => 28,
  "*" => 29,
  "|" => 30,
  ":" => 31 }

racc_nt_base = 32

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]

Racc_token_to_s_table = [
  "$end",
  "error",
  "FUNCTION",
  "INCLUDES",
  "DASHMATCH",
  "LBRACE",
  "HASH",
  "PLUS",
  "GREATER",
  "S",
  "STRING",
  "IDENT",
  "COMMA",
  "NUMBER",
  "PREFIXMATCH",
  "SUFFIXMATCH",
  "SUBSTRINGMATCH",
  "TILDE",
  "NOT_EQUAL",
  "SLASH",
  "DOUBLESLASH",
  "NOT",
  "EQUAL",
  "RPAREN",
  "LSQUARE",
  "RSQUARE",
  "HAS",
  "\"@\"",
  "\".\"",
  "\"*\"",
  "\"|\"",
  "\":\"",
  "$start",
  "selector",
  "simple_selector_1toN",
  "prefixless_combinator_selector",
  "optional_S",
  "combinator",
  "xpath_attribute_name",
  "xpath_attribute",
  "simple_selector",
  "element_name",
  "hcap_0toN",
  "function",
  "pseudo",
  "attrib",
  "hcap_1toN",
  "class",
  "namespaced_ident",
  "namespace",
  "attrib_name",
  "attrib_val_0or1",
  "expr",
  "nth",
  "attribute_id",
  "negation",
  "eql_incl_dash",
  "negation_arg" ]

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

def _reduce_1(val, _values, result)
      result = [val[0], val[2]].flatten

    result
end

def _reduce_2(val, _values, result)
 result = val.flatten
    result
end

def _reduce_3(val, _values, result)
 result = [val[1]].flatten
    result
end

def _reduce_4(val, _values, result)
 result = :DIRECT_ADJACENT_SELECTOR
    result
end

def _reduce_5(val, _values, result)
 result = :CHILD_SELECTOR
    result
end

def _reduce_6(val, _values, result)
 result = :FOLLOWING_SELECTOR
    result
end

def _reduce_7(val, _values, result)
 result = :DESCENDANT_SELECTOR
    result
end

def _reduce_8(val, _values, result)
 result = :CHILD_SELECTOR
    result
end

def _reduce_9(val, _values, result)
 result = val[1]
    result
end

def _reduce_10(val, _values, result)
 result = Node.new(:ATTRIB_NAME, [val[0]])
    result
end

def _reduce_11(val, _values, result)
      result =  if val[1].nil?
                  val[0]
                else
                  Node.new(:CONDITIONAL_SELECTOR, [val[0], val[1]])
                end

    result
end

# reduce 12 omitted

def _reduce_13(val, _values, result)
 result = Node.new(:CONDITIONAL_SELECTOR, val)
    result
end

def _reduce_14(val, _values, result)
 result = Node.new(:CONDITIONAL_SELECTOR, val)
    result
end

def _reduce_15(val, _values, result)
 result = Node.new(:CONDITIONAL_SELECTOR, [Node.new(:ELEMENT_NAME, ['*']), val[0]])
    result
end

# reduce 16 omitted

def _reduce_17(val, _values, result)
 result = Node.new(val[0], [nil, val[1]])
    result
end

def _reduce_18(val, _values, result)
 result = Node.new(val[1], [val[0], val[2]])
    result
end

def _reduce_19(val, _values, result)
 result = Node.new(:DESCENDANT_SELECTOR, [val[0], val[2]])
    result
end

# reduce 20 omitted

def _reduce_21(val, _values, result)
 result = Node.new(:CLASS_CONDITION, [unescape_css_identifier(val[1])])
    result
end

# reduce 22 omitted

def _reduce_23(val, _values, result)
 result = Node.new(:ELEMENT_NAME, val)
    result
end

def _reduce_24(val, _values, result)
 result = Node.new(:ELEMENT_NAME, [[val[0], val[2]].compact.join(':')])
    result
end

def _reduce_25(val, _values, result)
      name = @namespaces.key?('xmlns') ? "xmlns:#{val[0]}" : val[0]
      result = Node.new(:ELEMENT_NAME, [name])

    result
end

def _reduce_26(val, _values, result)
 result = val[0]
    result
end

# reduce 27 omitted

def _reduce_28(val, _values, result)
      result = Node.new(:ATTRIBUTE_CONDITION, [val[1]] + (val[2] || []))

    result
end

def _reduce_29(val, _values, result)
      result = Node.new(:ATTRIBUTE_CONDITION, [val[1]] + (val[2] || []))

    result
end

def _reduce_30(val, _values, result)
      result = Node.new(:PSEUDO_CLASS, [Node.new(:FUNCTION, ['nth-child(', val[1]])])

    result
end

def _reduce_31(val, _values, result)
 result = Node.new(:ATTRIB_NAME, [[val[0], val[2]].compact.join(':')])
    result
end

def _reduce_32(val, _values, result)
 result = Node.new(:ATTRIB_NAME, [val[0]])
    result
end

# reduce 33 omitted

def _reduce_34(val, _values, result)
      result = Node.new(:FUNCTION, [val[0].strip])

    result
end

def _reduce_35(val, _values, result)
      result = Node.new(:FUNCTION, [val[0].strip, val[1]].flatten)

    result
end

def _reduce_36(val, _values, result)
      result = Node.new(:FUNCTION, [val[0].strip, val[1]].flatten)

    result
end

def _reduce_37(val, _values, result)
      result = Node.new(:FUNCTION, [val[0].strip, val[1]].flatten)

    result
end

def _reduce_38(val, _values, result)
      result = Node.new(:FUNCTION, [val[0].strip, val[1]].flatten)

    result
end

def _reduce_39(val, _values, result)
 result = [val[0], val[2]]
    result
end

def _reduce_40(val, _values, result)
 result = [val[0], val[2]]
    result
end

def _reduce_41(val, _values, result)
 result = [val[0], val[2]]
    result
end

def _reduce_42(val, _values, result)
 result = [val[0], val[2]]
    result
end

# reduce 43 omitted

# reduce 44 omitted

def _reduce_45(val, _values, result)
      case val[0]
      when 'even'
        result = Node.new(:NTH, ['2','n','+','0'])
      when 'odd'
        result = Node.new(:NTH, ['2','n','+','1'])
      when 'n'
        result = Node.new(:NTH, ['1','n','+','0'])
      else
        result = val
      end

    result
end

# reduce 46 omitted

def _reduce_47(val, _values, result)
      if val[1] == 'n'
        result = Node.new(:NTH, val)
      else
        raise Racc::ParseError, "parse error on IDENT '#{val[1]}'"
      end

    result
end

def _reduce_48(val, _values, result)
               # n+3, -n+3
      if val[0] == 'n'
        val.unshift("1")
        result = Node.new(:NTH, val)
      elsif val[0] == '-n'
        val[0] = 'n'
        val.unshift("-1")
        result = Node.new(:NTH, val)
      else
        raise Racc::ParseError, "parse error on IDENT '#{val[1]}'"
      end

    result
end

def _reduce_49(val, _values, result)
                    # 5n, -5n, 10n-1
      n = val[1]
      if n[0, 2] == 'n-'
        val[1] = 'n'
        val << "-"
        # b is contained in n as n is the string "n-b"
        val << n[2, n.size]
        result = Node.new(:NTH, val)
      elsif n == 'n'
        val << "+"
        val << "0"
        result = Node.new(:NTH, val)
      else
        raise Racc::ParseError, "parse error on IDENT '#{val[1]}'"
      end

    result
end

def _reduce_50(val, _values, result)
      result = Node.new(:PSEUDO_CLASS, [val[1]])

    result
end

def _reduce_51(val, _values, result)
 result = Node.new(:PSEUDO_CLASS, [val[1]])
    result
end

# reduce 52 omitted

# reduce 53 omitted

def _reduce_54(val, _values, result)
      result = Node.new(:COMBINATOR, val)

    result
end

def _reduce_55(val, _values, result)
      result = Node.new(:COMBINATOR, val)

    result
end

def _reduce_56(val, _values, result)
      result = Node.new(:COMBINATOR, val)

    result
end

def _reduce_57(val, _values, result)
      result = Node.new(:COMBINATOR, val)

    result
end

def _reduce_58(val, _values, result)
      result = Node.new(:COMBINATOR, val)

    result
end

# reduce 59 omitted

# reduce 60 omitted

# reduce 61 omitted

# reduce 62 omitted

# reduce 63 omitted

def _reduce_64(val, _values, result)
 result = Node.new(:ID, [unescape_css_identifier(val[0])])
    result
end

def _reduce_65(val, _values, result)
 result = [val[0], unescape_css_identifier(val[1])]
    result
end

def _reduce_66(val, _values, result)
 result = [val[0], unescape_css_string(val[1])]
    result
end

def _reduce_67(val, _values, result)
 result = [val[0], val[1]]
    result
end

# reduce 68 omitted

def _reduce_69(val, _values, result)
 result = :equal
    result
end

def _reduce_70(val, _values, result)
 result = :prefix_match
    result
end

def _reduce_71(val, _values, result)
 result = :suffix_match
    result
end

def _reduce_72(val, _values, result)
 result = :substring_match
    result
end

def _reduce_73(val, _values, result)
 result = :not_equal
    result
end

def _reduce_74(val, _values, result)
 result = :includes
    result
end

def _reduce_75(val, _values, result)
 result = :dash_match
    result
end

def _reduce_76(val, _values, result)
      result = Node.new(:NOT, [val[1]])

    result
end

# reduce 77 omitted

# reduce 78 omitted

# reduce 79 omitted

# reduce 80 omitted

# reduce 81 omitted

def _reduce_none(val, _values, result)
  val[0]
end

    end   # class Parser
  end   # module CSS
end   # module Nokogiri
