# -*-makefile-*-
# DO NOT DELETE

gumbo.o: $(srcdir)/nokogiri.h
html_document.o: $(srcdir)/nokogiri.h
html_element_description.o: $(srcdir)/nokogiri.h
html_entity_lookup.o: $(srcdir)/nokogiri.h
html_sax_parser_context.o: $(srcdir)/nokogiri.h
html_sax_push_parser.o: $(srcdir)/nokogiri.h
libxml2_backwards_compat.o: $(srcdir)/nokogiri.h
nokogiri.o: $(srcdir)/nokogiri.h
test_global_handlers.o: $(srcdir)/nokogiri.h
xml_attr.o: $(srcdir)/nokogiri.h
xml_attribute_decl.o: $(srcdir)/nokogiri.h
xml_cdata.o: $(srcdir)/nokogiri.h
xml_comment.o: $(srcdir)/nokogiri.h
xml_document.o: $(srcdir)/nokogiri.h
xml_document_fragment.o: $(srcdir)/nokogiri.h
xml_dtd.o: $(srcdir)/nokogiri.h
xml_element_content.o: $(srcdir)/nokogiri.h
xml_element_decl.o: $(srcdir)/nokogiri.h
xml_encoding_handler.o: $(srcdir)/nokogiri.h
xml_entity_decl.o: $(srcdir)/nokogiri.h
xml_entity_reference.o: $(srcdir)/nokogiri.h
xml_namespace.o: $(srcdir)/nokogiri.h
xml_node.o: $(srcdir)/nokogiri.h
xml_node_set.o: $(srcdir)/nokogiri.h
xml_processing_instruction.o: $(srcdir)/nokogiri.h
xml_reader.o: $(srcdir)/nokogiri.h
xml_relax_ng.o: $(srcdir)/nokogiri.h
xml_sax_parser.o: $(srcdir)/nokogiri.h
xml_sax_parser_context.o: $(srcdir)/nokogiri.h
xml_sax_push_parser.o: $(srcdir)/nokogiri.h
xml_schema.o: $(srcdir)/nokogiri.h
xml_syntax_error.o: $(srcdir)/nokogiri.h
xml_text.o: $(srcdir)/nokogiri.h
xml_xpath_context.o: $(srcdir)/nokogiri.h
xslt_stylesheet.o: $(srcdir)/nokogiri.h
