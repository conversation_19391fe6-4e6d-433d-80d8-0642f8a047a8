---
libxml2:
  version: "2.12.9"
  sha256: "59912db536ab56a3996489ea0299768c7bcffe57169f0235e7f962a91f483590"
  # sha-256 hash provided in https://download.gnome.org/sources/libxml2/2.12/libxml2-2.12.9.sha256sum

libxslt:
  version: "1.1.39"
  sha256: "2a20ad621148339b0759c4d4e96719362dee64c9a096dbba625ba053846349f0"
  # sha-256 hash provided in https://download.gnome.org/sources/libxslt/1.1/libxslt-1.1.39.sha256sum

zlib:
  version: "1.3.1"
  sha256: "9a93b2b7dfdac77ceba5a558a580e74667dd6fede4585b91eefb60f03b72df23"
  # SHA-256 hash provided on http://zlib.net/

libiconv:
  version: "1.17"
  sha256: "8f74213b56238c85a50a5329f77e06198771e70dd9a739779f4c02f65d971313"
  # signature verified by following this path:
  # - release announced at https://savannah.gnu.org/forum/forum.php?forum_id=10175
  # - which links to https://savannah.gnu.org/users/haible as the releaser
  # - which links to https://savannah.gnu.org/people/viewgpg.php?user_id=1871 as the gpg key
  #
  # So:
  # - wget -q -O - https://savannah.gnu.org/people/viewgpg.php?user_id=1871 | gpg --import
  #     gpg: key F5BE8B267C6A406D: 1 signature not checked due to a missing key
  # <AUTHOR> <EMAIL>" imported
  #     gpg: Total number processed: 1
  #     gpg:               imported: 1
  #     gpg: marginals needed: 3  completes needed: 1  trust model: pgp
  #     gpg: depth: 0  valid:   4  signed:   0  trust: 0-, 0q, 0n, 0m, 0f, 4u
  #     gpg: next trustdb check due at 2024-05-09
  # - gpg --verify libiconv-1.17.tar.gz.sig ports/archives/libiconv-1.17.tar.gz
  #     gpg: Signature made Sun 15 May 2022 11:26:42 AM EDT
  #     gpg:                using RSA key 9001B85AF9E1B83DF1BDA942F5BE8B267C6A406D
  # <AUTHOR> <EMAIL>" [unknown]
  #     gpg: WARNING: This key is not certified with a trusted signature!
  #     gpg:          There is no indication that the signature belongs to the owner.
  #     Primary key fingerprint: 9001 B85A F9E1 B83D F1BD  A942 F5BE 8B26 7C6A 406D
  #
  # And this sha256sum is calculated from that verified tarball.
