rbx.platform.typedef.__darwin_blkcnt_t = long_long
rbx.platform.typedef.__darwin_blksize_t = int
rbx.platform.typedef.__darwin_clock_t = ulong
rbx.platform.typedef.__darwin_ct_rune_t = int
rbx.platform.typedef.__darwin_dev_t = int
rbx.platform.typedef.__darwin_fsblkcnt_t = uint
rbx.platform.typedef.__darwin_fsfilcnt_t = uint
rbx.platform.typedef.__darwin_gid_t = uint
rbx.platform.typedef.__darwin_id_t = uint
rbx.platform.typedef.__darwin_ino64_t = ulong_long
rbx.platform.typedef.__darwin_ino_t = ulong_long
rbx.platform.typedef.__darwin_intptr_t = long
rbx.platform.typedef.__darwin_mach_port_name_t = uint
rbx.platform.typedef.__darwin_mach_port_t = uint
rbx.platform.typedef.__darwin_mode_t = ushort
rbx.platform.typedef.__darwin_natural_t = uint
rbx.platform.typedef.__darwin_off_t = long_long
rbx.platform.typedef.__darwin_pid_t = int
rbx.platform.typedef.__darwin_pthread_key_t = ulong
rbx.platform.typedef.__darwin_ptrdiff_t = long
rbx.platform.typedef.__darwin_rune_t = int
rbx.platform.typedef.__darwin_sigset_t = uint
rbx.platform.typedef.__darwin_size_t = ulong
rbx.platform.typedef.__darwin_socklen_t = uint
rbx.platform.typedef.__darwin_ssize_t = long
rbx.platform.typedef.__darwin_suseconds_t = int
rbx.platform.typedef.__darwin_time_t = long
rbx.platform.typedef.__darwin_uid_t = uint
rbx.platform.typedef.__darwin_useconds_t = uint
rbx.platform.typedef.__darwin_uuid_string_t[37] = char
rbx.platform.typedef.__darwin_uuid_t[16] = uchar
rbx.platform.typedef.__darwin_wchar_t = int
rbx.platform.typedef.__darwin_wint_t = int
rbx.platform.typedef.__int16_t = short
rbx.platform.typedef.__int32_t = int
rbx.platform.typedef.__int64_t = long_long
rbx.platform.typedef.__int8_t = char
rbx.platform.typedef.__uint16_t = ushort
rbx.platform.typedef.__uint32_t = uint
rbx.platform.typedef.__uint64_t = ulong_long
rbx.platform.typedef.__uint8_t = uchar
rbx.platform.typedef.blkcnt_t = long_long
rbx.platform.typedef.blksize_t = int
rbx.platform.typedef.caddr_t = string
rbx.platform.typedef.clock_t = ulong
rbx.platform.typedef.daddr_t = int
rbx.platform.typedef.dev_t = int
rbx.platform.typedef.errno_t = int
rbx.platform.typedef.fd_mask = int
rbx.platform.typedef.fixpt_t = uint
rbx.platform.typedef.fsblkcnt_t = uint
rbx.platform.typedef.fsfilcnt_t = uint
rbx.platform.typedef.gid_t = uint
rbx.platform.typedef.id_t = uint
rbx.platform.typedef.in_addr_t = uint
rbx.platform.typedef.in_port_t = ushort
rbx.platform.typedef.ino64_t = ulong_long
rbx.platform.typedef.ino_t = ulong_long
rbx.platform.typedef.int16_t = short
rbx.platform.typedef.int32_t = int
rbx.platform.typedef.int64_t = long_long
rbx.platform.typedef.int8_t = char
rbx.platform.typedef.int_fast16_t = short
rbx.platform.typedef.int_fast32_t = int
rbx.platform.typedef.int_fast64_t = long_long
rbx.platform.typedef.int_fast8_t = char
rbx.platform.typedef.int_least16_t = short
rbx.platform.typedef.int_least32_t = int
rbx.platform.typedef.int_least64_t = long_long
rbx.platform.typedef.int_least8_t = char
rbx.platform.typedef.intmax_t = long
rbx.platform.typedef.intptr_t = long
rbx.platform.typedef.key_t = int
rbx.platform.typedef.mode_t = ushort
rbx.platform.typedef.nlink_t = ushort
rbx.platform.typedef.off_t = long_long
rbx.platform.typedef.pid_t = int
rbx.platform.typedef.pthread_key_t = ulong
rbx.platform.typedef.ptrdiff_t = long
rbx.platform.typedef.qaddr_t = pointer
rbx.platform.typedef.quad_t = long_long
rbx.platform.typedef.register_t = long_long
rbx.platform.typedef.rlim_t = ulong_long
rbx.platform.typedef.rsize_t = ulong
rbx.platform.typedef.sa_family_t = uchar
rbx.platform.typedef.sae_associd_t = uint
rbx.platform.typedef.sae_connid_t = uint
rbx.platform.typedef.segsz_t = int
rbx.platform.typedef.size_t = ulong
rbx.platform.typedef.socklen_t = uint
rbx.platform.typedef.ssize_t = long
rbx.platform.typedef.suseconds_t = int
rbx.platform.typedef.swblk_t = int
rbx.platform.typedef.syscall_arg_t = ulong_long
rbx.platform.typedef.time_t = long
rbx.platform.typedef.u_char = uchar
rbx.platform.typedef.u_int = uint
rbx.platform.typedef.u_int16_t = ushort
rbx.platform.typedef.u_int32_t = uint
rbx.platform.typedef.u_int64_t = ulong_long
rbx.platform.typedef.u_int8_t = uchar
rbx.platform.typedef.u_long = ulong
rbx.platform.typedef.u_quad_t = ulong_long
rbx.platform.typedef.u_short = ushort
rbx.platform.typedef.uid_t = uint
rbx.platform.typedef.uint = uint
rbx.platform.typedef.uint16_t = ushort
rbx.platform.typedef.uint32_t = uint
rbx.platform.typedef.uint64_t = ulong_long
rbx.platform.typedef.uint8_t = uchar
rbx.platform.typedef.uint_fast16_t = ushort
rbx.platform.typedef.uint_fast32_t = uint
rbx.platform.typedef.uint_fast64_t = ulong_long
rbx.platform.typedef.uint_fast8_t = uchar
rbx.platform.typedef.uint_least16_t = ushort
rbx.platform.typedef.uint_least32_t = uint
rbx.platform.typedef.uint_least64_t = ulong_long
rbx.platform.typedef.uint_least8_t = uchar
rbx.platform.typedef.uintmax_t = ulong
rbx.platform.typedef.uintptr_t = ulong
rbx.platform.typedef.useconds_t = uint
rbx.platform.typedef.user_addr_t = ulong_long
rbx.platform.typedef.user_long_t = long_long
rbx.platform.typedef.user_off_t = long_long
rbx.platform.typedef.user_size_t = ulong_long
rbx.platform.typedef.user_ssize_t = long_long
rbx.platform.typedef.user_time_t = long_long
rbx.platform.typedef.user_ulong_t = ulong_long
rbx.platform.typedef.ushort = ushort
rbx.platform.typedef.wchar_t = int
