<!--- this file is synced from dry-rb/template-gem project -->
[gem]: https://rubygems.org/gems/dry-auto_inject
[actions]: https://github.com/dry-rb/dry-auto_inject/actions

# dry-auto_inject [![Gem Version](https://badge.fury.io/rb/dry-auto_inject.svg)][gem] [![CI Status](https://github.com/dry-rb/dry-auto_inject/workflows/ci/badge.svg)][actions]

## Links

* [User documentation](https://dry-rb.org/gems/dry-auto_inject)
* [API documentation](http://rubydoc.info/gems/dry-auto_inject)
* [Forum](https://discourse.dry-rb.org)

## Supported Ruby versions

This library officially supports the following Ruby versions:

* MRI `>= 2.7.0`
* jruby `>= 9.4` (not tested on CI)

## License

See `LICENSE` file.
