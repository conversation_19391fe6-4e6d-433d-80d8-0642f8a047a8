# -*- encoding: utf-8 -*-
# stub: rails ******* ruby lib

Gem::Specification.new do |s|
  s.name = "rails".freeze
  s.version = "*******".freeze

  s.required_rubygems_version = Gem::Requirement.new(">= 1.8.11".freeze) if s.respond_to? :required_rubygems_version=
  s.metadata = { "bug_tracker_uri" => "https://github.com/rails/rails/issues", "changelog_uri" => "https://github.com/rails/rails/releases/tag/v*******", "documentation_uri" => "https://api.rubyonrails.org/v*******/", "mailing_list_uri" => "https://discuss.rubyonrails.org/c/rubyonrails-talk", "rubygems_mfa_required" => "true", "source_code_uri" => "https://github.com/rails/rails/tree/v*******" } if s.respond_to? :metadata=
  s.require_paths = ["lib".freeze]
  s.authors = ["<PERSON>".freeze]
  s.date = "2024-06-04"
  s.description = "Ruby on Rails is a full-stack web framework optimized for programmer happiness and sustainable productivity. It encourages beautiful code by favoring convention over configuration.".freeze
  s.email = "<EMAIL>".freeze
  s.homepage = "https://rubyonrails.org".freeze
  s.licenses = ["MIT".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.7.0".freeze)
  s.rubygems_version = "3.5.3".freeze
  s.summary = "Full-stack web application framework.".freeze

  s.installed_by_version = "3.5.3".freeze if s.respond_to? :installed_by_version

  s.specification_version = 4

  s.add_runtime_dependency(%q<activesupport>.freeze, ["= *******".freeze])
  s.add_runtime_dependency(%q<actionpack>.freeze, ["= *******".freeze])
  s.add_runtime_dependency(%q<actionview>.freeze, ["= *******".freeze])
  s.add_runtime_dependency(%q<activemodel>.freeze, ["= *******".freeze])
  s.add_runtime_dependency(%q<activerecord>.freeze, ["= *******".freeze])
  s.add_runtime_dependency(%q<actionmailer>.freeze, ["= *******".freeze])
  s.add_runtime_dependency(%q<activejob>.freeze, ["= *******".freeze])
  s.add_runtime_dependency(%q<actioncable>.freeze, ["= *******".freeze])
  s.add_runtime_dependency(%q<activestorage>.freeze, ["= *******".freeze])
  s.add_runtime_dependency(%q<actionmailbox>.freeze, ["= *******".freeze])
  s.add_runtime_dependency(%q<actiontext>.freeze, ["= *******".freeze])
  s.add_runtime_dependency(%q<railties>.freeze, ["= *******".freeze])
  s.add_runtime_dependency(%q<bundler>.freeze, [">= 1.15.0".freeze])
end
