# -*- encoding: utf-8 -*-
# stub: logger 1.6.0 ruby lib

Gem::Specification.new do |s|
  s.name = "logger".freeze
  s.version = "1.6.0".freeze

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.require_paths = ["lib".freeze]
  s.authors = ["Naotoshi Seo".freeze, "SHIBATA Hiroshi".freeze]
  s.date = "2023-11-07"
  s.description = "Provides a simple logging utility for outputting messages.".freeze
  s.email = ["<EMAIL>".freeze, "<EMAIL>".freeze]
  s.homepage = "https://github.com/ruby/logger".freeze
  s.licenses = ["Ruby".freeze, "BSD-2-Clause".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.5.0".freeze)
  s.rubygems_version = "3.5.3".freeze
  s.summary = "Provides a simple logging utility for outputting messages.".freeze

  s.installed_by_version = "3.5.3".freeze if s.respond_to? :installed_by_version
end
