# -*- encoding: utf-8 -*-
# stub: webrick 1.8.1 ruby lib

Gem::Specification.new do |s|
  s.name = "webrick".freeze
  s.version = "1.8.1".freeze

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.metadata = { "bug_tracker_uri" => "https://github.com/ruby/webrick/issues" } if s.respond_to? :metadata=
  s.require_paths = ["lib".freeze]
  s.authors = ["TAKAHASHI Masayoshi".freeze, "GOTOU YUUZOU".freeze, "<PERSON>".freeze]
  s.date = "2023-01-27"
  s.description = "WEBrick is an HTTP server toolkit that can be configured as an HTTPS server, a proxy server, and a virtual-host server.".freeze
  s.email = [nil, nil, "<EMAIL>".freeze]
  s.homepage = "https://github.com/ruby/webrick".freeze
  s.licenses = ["Ruby".freeze, "BSD-2-Clause".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.4.0".freeze)
  s.rubygems_version = "3.5.3".freeze
  s.summary = "HTTP server toolkit".freeze

  s.installed_by_version = "3.5.3".freeze if s.respond_to? :installed_by_version
end
