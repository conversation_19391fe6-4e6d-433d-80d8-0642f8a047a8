# -*- encoding: utf-8 -*-
# stub: date 3.3.4 ruby lib
# stub: ext/date/extconf.rb

Gem::Specification.new do |s|
  s.name = "date".freeze
  s.version = "3.3.4".freeze

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.require_paths = ["lib".freeze]
  s.authors = ["<PERSON><PERSON><PERSON>".freeze]
  s.date = "2023-11-07"
  s.description = "A subclass of Object includes Comparable module for handling dates.".freeze
  s.email = [nil]
  s.extensions = ["ext/date/extconf.rb".freeze]
  s.files = ["ext/date/extconf.rb".freeze]
  s.homepage = "https://github.com/ruby/date".freeze
  s.licenses = ["Ruby".freeze, "BSD-2-Clause".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.6.0".freeze)
  s.rubygems_version = "3.5.3".freeze
  s.summary = "A subclass of Object includes Comparable module for handling dates.".freeze

  s.installed_by_version = "3.5.3".freeze if s.respond_to? :installed_by_version
end
