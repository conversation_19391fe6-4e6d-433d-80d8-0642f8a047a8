# -*- encoding: utf-8 -*-
# stub: responders 3.1.1 ruby lib

Gem::Specification.new do |s|
  s.name = "responders".freeze
  s.version = "3.1.1".freeze

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.metadata = { "bug_tracker_uri" => "https://github.com/heartcombo/responders/issues", "changelog_uri" => "https://github.com/heartcombo/responders/blob/main/CHANGELOG.md", "homepage_uri" => "https://github.com/heartcombo/responders", "source_code_uri" => "https://github.com/heartcombo/responders" } if s.respond_to? :metadata=
  s.require_paths = ["lib".freeze]
  s.authors = ["Jos\u00E9 Valim".freeze]
  s.date = "2023-10-11"
  s.description = "A set of Rails responders to dry up your application".freeze
  s.email = "<EMAIL>".freeze
  s.homepage = "https://github.com/heartcombo/responders".freeze
  s.licenses = ["MIT".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.5.0".freeze)
  s.rubygems_version = "3.5.3".freeze
  s.summary = "A set of Rails responders to dry up your application".freeze

  s.installed_by_version = "3.5.3".freeze if s.respond_to? :installed_by_version

  s.specification_version = 4

  s.add_runtime_dependency(%q<railties>.freeze, [">= 5.2".freeze])
  s.add_runtime_dependency(%q<actionpack>.freeze, [">= 5.2".freeze])
end
