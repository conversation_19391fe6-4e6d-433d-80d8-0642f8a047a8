# -*- encoding: utf-8 -*-
# stub: strscan 3.1.0 ruby lib
# stub: ext/strscan/extconf.rb

Gem::Specification.new do |s|
  s.name = "strscan".freeze
  s.version = "3.1.0".freeze

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.require_paths = ["lib".freeze]
  s.authors = ["Minero Aoki".freeze, "<PERSON><PERSON><PERSON>".freeze, "<PERSON>".freeze]
  s.date = "2024-02-04"
  s.description = "Provides lexical scanning operations on a String.".freeze
  s.email = [nil, "<EMAIL>".freeze, "<EMAIL>".freeze]
  s.extensions = ["ext/strscan/extconf.rb".freeze]
  s.files = ["ext/strscan/extconf.rb".freeze]
  s.homepage = "https://github.com/ruby/strscan".freeze
  s.licenses = ["Ruby".freeze, "BSD-2-Clause".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.4.0".freeze)
  s.rubygems_version = "3.5.3".freeze
  s.summary = "Provides lexical scanning operations on a String.".freeze

  s.installed_by_version = "3.5.3".freeze if s.respond_to? :installed_by_version
end
