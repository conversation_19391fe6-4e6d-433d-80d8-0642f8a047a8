# -*- encoding: utf-8 -*-
# stub: parallel 1.25.1 ruby lib

Gem::Specification.new do |s|
  s.name = "parallel".freeze
  s.version = "1.25.1".freeze

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.metadata = { "bug_tracker_uri" => "https://github.com/grosser/parallel/issues", "documentation_uri" => "https://github.com/grosser/parallel/blob/v1.25.1/Readme.md", "source_code_uri" => "https://github.com/grosser/parallel/tree/v1.25.1", "wiki_uri" => "https://github.com/grosser/parallel/wiki" } if s.respond_to? :metadata=
  s.require_paths = ["lib".freeze]
  s.authors = ["<PERSON> Grosser".freeze]
  s.date = "2024-06-08"
  s.email = "<EMAIL>".freeze
  s.homepage = "https://github.com/grosser/parallel".freeze
  s.licenses = ["MIT".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.7".freeze)
  s.rubygems_version = "3.5.3".freeze
  s.summary = "Run any kind of code in parallel processes".freeze

  s.installed_by_version = "3.5.3".freeze if s.respond_to? :installed_by_version
end
