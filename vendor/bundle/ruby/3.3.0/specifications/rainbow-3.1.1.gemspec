# -*- encoding: utf-8 -*-
# stub: rainbow 3.1.1 ruby lib

Gem::Specification.new do |s|
  s.name = "rainbow".freeze
  s.version = "3.1.1".freeze

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.require_paths = ["lib".freeze]
  s.authors = ["<PERSON><PERSON>".freeze, "<PERSON><PERSON>".freeze]
  s.date = "2022-01-11"
  s.description = "Colorize printed text on ANSI terminals".freeze
  s.email = ["<EMAIL>".freeze]
  s.homepage = "https://github.com/sickill/rainbow".freeze
  s.licenses = ["MIT".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.3.0".freeze)
  s.rubygems_version = "3.5.3".freeze
  s.summary = "Colorize printed text on ANSI terminals".freeze

  s.installed_by_version = "3.5.3".freeze if s.respond_to? :installed_by_version

  s.specification_version = 4

  s.add_development_dependency(%q<bundler>.freeze, [">= 1.3".freeze, "< 3".freeze])
end
