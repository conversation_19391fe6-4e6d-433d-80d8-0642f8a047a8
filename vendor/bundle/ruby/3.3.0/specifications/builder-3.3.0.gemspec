# -*- encoding: utf-8 -*-
# stub: builder 3.3.0 ruby lib

Gem::Specification.new do |s|
  s.name = "builder".freeze
  s.version = "3.3.0".freeze

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.metadata = { "bug_tracker_uri" => "https://github.com/rails/builder/issues", "changelog_uri" => "https://github.com/rails/builder/blob/master/CHANGES", "documentation_uri" => "https://www.rubydoc.info/gems/builder/3.3.0", "homepage_uri" => "https://github.com/rails/builder", "source_code_uri" => "https://github.com/rails/builder/tree/v3.3.0" } if s.respond_to? :metadata=
  s.require_paths = ["lib".freeze]
  s.authors = ["<PERSON>".freeze, "<PERSON>".freeze]
  s.date = "2024-06-06"
  s.description = "Builder provides a number of builder objects that make creating structured data\nsimple to do.  Currently the following builder objects are supported:\n\n* XML Markup\n* XML Events\n".freeze
  s.email = "<EMAIL>".freeze
  s.homepage = "https://github.com/rails/builder".freeze
  s.licenses = ["MIT".freeze]
  s.rdoc_options = ["--title".freeze, "Builder -- Easy XML Building".freeze, "--main".freeze, "README.rdoc".freeze, "--line-numbers".freeze]
  s.rubygems_version = "3.5.3".freeze
  s.summary = "Builders for MarkUp.".freeze

  s.installed_by_version = "3.5.3".freeze if s.respond_to? :installed_by_version
end
