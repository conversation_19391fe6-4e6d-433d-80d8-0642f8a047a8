# -*- encoding: utf-8 -*-
# stub: regexp_parser 2.9.2 ruby lib

Gem::Specification.new do |s|
  s.name = "regexp_parser".freeze
  s.version = "2.9.2".freeze

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.metadata = { "bug_tracker_uri" => "https://github.com/ammar/regexp_parser/issues", "changelog_uri" => "https://github.com/ammar/regexp_parser/blob/master/CHANGELOG.md", "homepage_uri" => "https://github.com/ammar/regexp_parser", "rubygems_mfa_required" => "true", "source_code_uri" => "https://github.com/ammar/regexp_parser", "wiki_uri" => "https://github.com/ammar/regexp_parser/wiki" } if s.respond_to? :metadata=
  s.require_paths = ["lib".freeze]
  s.authors = ["Ammar Ali".freeze, "<PERSON><PERSON><PERSON> M\u00FCller".freeze]
  s.date = "2024-05-15"
  s.description = "A library for tokenizing, lexing, and parsing Ruby regular expressions.".freeze
  s.email = ["<EMAIL>".freeze, "<EMAIL>".freeze]
  s.homepage = "https://github.com/ammar/regexp_parser".freeze
  s.licenses = ["MIT".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.0.0".freeze)
  s.rubygems_version = "3.5.3".freeze
  s.summary = "Scanner, lexer, parser for ruby's regular expressions".freeze

  s.installed_by_version = "3.5.3".freeze if s.respond_to? :installed_by_version
end
