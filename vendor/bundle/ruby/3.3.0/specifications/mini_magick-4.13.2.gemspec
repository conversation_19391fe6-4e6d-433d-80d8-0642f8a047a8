# -*- encoding: utf-8 -*-
# stub: mini_magick 4.13.2 ruby lib

Gem::Specification.new do |s|
  s.name = "mini_magick".freeze
  s.version = "4.13.2".freeze

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.require_paths = ["lib".freeze]
  s.authors = ["<PERSON>".freeze, "<PERSON>lin".freeze, "<PERSON>".freeze, "<PERSON>".freeze, "Thiago <PERSON>".freeze, "<PERSON><PERSON>\u0107".freeze]
  s.date = "2024-07-08"
  s.description = "Manipulate images with minimal use of memory via ImageMagick / GraphicsMagick".freeze
  s.email = ["<EMAIL>".freeze, "<EMAIL>".freeze, "<EMAIL>".freeze, "<EMAIL>".freeze, "<EMAIL>".freeze, "jan<PERSON>.<PERSON><PERSON><PERSON><PERSON>@gmail.com".freeze]
  s.homepage = "https://github.com/minimagick/minimagick".freeze
  s.licenses = ["MIT".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.3".freeze)
  s.requirements = ["You must have ImageMagick or GraphicsMagick installed".freeze]
  s.rubygems_version = "3.5.3".freeze
  s.summary = "Manipulate images with minimal use of memory via ImageMagick / GraphicsMagick".freeze

  s.installed_by_version = "3.5.3".freeze if s.respond_to? :installed_by_version

  s.specification_version = 4

  s.add_development_dependency(%q<rake>.freeze, [">= 0".freeze])
  s.add_development_dependency(%q<rspec>.freeze, ["~> 3.5.0".freeze])
  s.add_development_dependency(%q<webmock>.freeze, [">= 0".freeze])
end
