# -*- encoding: utf-8 -*-
# stub: actionmailbox ******* ruby lib

Gem::Specification.new do |s|
  s.name = "actionmailbox".freeze
  s.version = "*******".freeze

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.metadata = { "bug_tracker_uri" => "https://github.com/rails/rails/issues", "changelog_uri" => "https://github.com/rails/rails/blob/v*******/actionmailbox/CHANGELOG.md", "documentation_uri" => "https://api.rubyonrails.org/v*******/", "mailing_list_uri" => "https://discuss.rubyonrails.org/c/rubyonrails-talk", "rubygems_mfa_required" => "true", "source_code_uri" => "https://github.com/rails/rails/tree/v*******/actionmailbox" } if s.respond_to? :metadata=
  s.require_paths = ["lib".freeze]
  s.authors = ["<PERSON>".freeze, "<PERSON>".freeze]
  s.date = "2024-06-04"
  s.description = "Receive and process incoming emails in Rails applications.".freeze
  s.email = ["<EMAIL>".freeze, "<EMAIL>".freeze]
  s.homepage = "https://rubyonrails.org".freeze
  s.licenses = ["MIT".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.7.0".freeze)
  s.rubygems_version = "3.5.3".freeze
  s.summary = "Inbound email handling framework.".freeze

  s.installed_by_version = "3.5.3".freeze if s.respond_to? :installed_by_version

  s.specification_version = 4

  s.add_runtime_dependency(%q<activesupport>.freeze, ["= *******".freeze])
  s.add_runtime_dependency(%q<activerecord>.freeze, ["= *******".freeze])
  s.add_runtime_dependency(%q<activestorage>.freeze, ["= *******".freeze])
  s.add_runtime_dependency(%q<activejob>.freeze, ["= *******".freeze])
  s.add_runtime_dependency(%q<actionpack>.freeze, ["= *******".freeze])
  s.add_runtime_dependency(%q<mail>.freeze, [">= 2.7.1".freeze])
  s.add_runtime_dependency(%q<net-imap>.freeze, [">= 0".freeze])
  s.add_runtime_dependency(%q<net-pop>.freeze, [">= 0".freeze])
  s.add_runtime_dependency(%q<net-smtp>.freeze, [">= 0".freeze])
end
