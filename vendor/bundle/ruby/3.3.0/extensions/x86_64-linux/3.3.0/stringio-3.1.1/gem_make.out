current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/stringio-3.1.1/ext/stringio
/home/<USER>/.rbenv/versions/3.3.0/bin/ruby extconf.rb
creating Makefile

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/stringio-3.1.1/ext/stringio
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-8z81y2 sitelibdir\=./.gem.20250709-4286-8z81y2 clean

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/stringio-3.1.1/ext/stringio
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-8z81y2 sitelibdir\=./.gem.20250709-4286-8z81y2
compiling stringio.c
linking shared-object stringio.so

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/stringio-3.1.1/ext/stringio
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-8z81y2 sitelibdir\=./.gem.20250709-4286-8z81y2 install
/usr/bin/install -c -m 0755 stringio.so ./.gem.20250709-4286-8z81y2

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/stringio-3.1.1/ext/stringio
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-8z81y2 sitelibdir\=./.gem.20250709-4286-8z81y2 clean
