current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/bootsnap-1.18.3/ext/bootsnap
/home/<USER>/.rbenv/versions/3.3.0/bin/ruby extconf.rb
checking for fdatasync() in unistd.h... yes
checking for whether -D_GNU_SOURCE is accepted as CPPFLAGS... yes
checking for whether -O3 is accepted as CFLAGS... yes
checking for whether -std=c99 is accepted as CFLAGS... yes
creating Makefile

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/bootsnap-1.18.3/ext/bootsnap
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-7j146m sitelibdir\=./.gem.20250709-4286-7j146m clean

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/bootsnap-1.18.3/ext/bootsnap
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-7j146m sitelibdir\=./.gem.20250709-4286-7j146m
compiling bootsnap.c
linking shared-object bootsnap/bootsnap.so

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/bootsnap-1.18.3/ext/bootsnap
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-7j146m sitelibdir\=./.gem.20250709-4286-7j146m install
/usr/bin/install -c -m 0755 bootsnap.so ./.gem.20250709-4286-7j146m/bootsnap

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/bootsnap-1.18.3/ext/bootsnap
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-7j146m sitelibdir\=./.gem.20250709-4286-7j146m clean
