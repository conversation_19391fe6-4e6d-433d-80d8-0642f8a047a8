=== OpenSSL for Ruby configurator ===
have_func: checking for rb_io_descriptor()... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: int main(int argc, char **argv)
4: {
5:   return !!argv[argc];
6: }
/* end */

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:14:57: error: ‘rb_io_descriptor’ undeclared (first use in this function)
   14 | int t(void) { void ((*volatile p)()); p = (void ((*)()))rb_io_descriptor; return !p; }
      |                                                         ^~~~~~~~~~~~~~~~
conftest.c:14:57: note: each undeclared identifier is reported only once for each function it appears in
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: int t(void) { void ((*volatile p)()); p = (void ((*)()))rb_io_descriptor; return !p; }
/* end */

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: extern void rb_io_descriptor();
15: int t(void) { rb_io_descriptor(); return 0; }
/* end */

--------------------

have_func: checking for rb_io_maybe_wait(0, Qnil, Qnil, Qnil) in ruby/io.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <ruby/io.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { rb_io_maybe_wait(0, Qnil, Qnil, Qnil); return 0; }
/* end */

--------------------

have_func: checking for rb_io_timeout() in ruby/io.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <ruby/io.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: int t(void) { void ((*volatile p)()); p = (void ((*)()))rb_io_timeout; return !p; }
/* end */

--------------------

=== Checking for system dependent stuff... ===
have_library: checking for t_open() in -lnsl... -------------------- no

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lnsl  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:14:57: error: ‘t_open’ undeclared (first use in this function); did you mean ‘popen’?
   14 | int t(void) { void ((*volatile p)()); p = (void ((*)()))t_open; return !p; }
      |                                                         ^~~~~~
      |                                                         popen
conftest.c:14:57: note: each undeclared identifier is reported only once for each function it appears in
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: int t(void) { void ((*volatile p)()); p = (void ((*)()))t_open; return !p; }
/* end */

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lnsl  -lm -lpthread  -lc"
/usr/bin/ld: cannot find -lnsl: No such file or directory
collect2: error: ld returned 1 exit status
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: extern void t_open();
15: int t(void) { t_open(); return 0; }
/* end */

--------------------

have_library: checking for socket() in -lsocket... -------------------- no

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lsocket  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:14:57: error: ‘socket’ undeclared (first use in this function)
   14 | int t(void) { void ((*volatile p)()); p = (void ((*)()))socket; return !p; }
      |                                                         ^~~~~~
conftest.c:14:57: note: each undeclared identifier is reported only once for each function it appears in
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: int t(void) { void ((*volatile p)()); p = (void ((*)()))socket; return !p; }
/* end */

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lsocket  -lm -lpthread  -lc"
/usr/bin/ld: cannot find -lsocket: No such file or directory
collect2: error: ld returned 1 exit status
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: extern void socket();
15: int t(void) { socket(); return 0; }
/* end */

--------------------

=== Checking for required stuff... ===
pkg_config: checking for pkg-config for openssl... -------------------- not found

package configuration for openssl is not found
--------------------

have_header: checking for openssl/ssl.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC   -c conftest.c"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: #include <openssl/ssl.h>
/* end */

--------------------

have_library: checking for CRYPTO_malloc() in -lcrypto... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lcrypto  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:14:57: error: ‘CRYPTO_malloc’ undeclared (first use in this function)
   14 | int t(void) { void ((*volatile p)()); p = (void ((*)()))CRYPTO_malloc; return !p; }
      |                                                         ^~~~~~~~~~~~~
conftest.c:14:57: note: each undeclared identifier is reported only once for each function it appears in
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: int t(void) { void ((*volatile p)()); p = (void ((*)()))CRYPTO_malloc; return !p; }
/* end */

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: extern void CRYPTO_malloc();
15: int t(void) { CRYPTO_malloc(); return 0; }
/* end */

--------------------

have_library: checking for SSL_new() in -lssl... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:14:57: error: ‘SSL_new’ undeclared (first use in this function)
   14 | int t(void) { void ((*volatile p)()); p = (void ((*)()))SSL_new; return !p; }
      |                                                         ^~~~~~~
conftest.c:14:57: note: each undeclared identifier is reported only once for each function it appears in
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: int t(void) { void ((*volatile p)()); p = (void ((*)()))SSL_new; return !p; }
/* end */

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: extern void SSL_new();
15: int t(void) { SSL_new(); return 0; }
/* end */

--------------------

have_macro: checking for LIBRESSL_VERSION_NUMBER in openssl/opensslv.h... -------------------- no

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC   -c conftest.c"
conftest.c:6:3: error: #error 
    6 | # error
      |   ^~~~~
conftest.c:7:1: error: expected identifier or ‘(’ before ‘|’ token
    7 | |:/ === LIBRESSL_VERSION_NUMBER undefined === /:|
      | ^
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: #include <openssl/opensslv.h>
4: /*top*/
5: #ifndef LIBRESSL_VERSION_NUMBER
6: # error
7: |:/ === LIBRESSL_VERSION_NUMBER undefined === /:|
8: #endif
/* end */

--------------------

checking for OpenSSL version >= 1.0.2... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC   -c conftest.c"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: #include <openssl/opensslv.h>
4: 
5: /*top*/
6: int conftest_const[(OPENSSL_VERSION_NUMBER >= 0x10002000L) ? 1 : -1];
/* end */

--------------------

=== Checking for OpenSSL features... ===
have_func: checking for RAND_egd() in openssl/rand.h... -------------------- no

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:17:15: error: implicit declaration of function ‘RAND_egd’; did you mean ‘RAND_add’? [-Wimplicit-function-declaration]
   17 | int t(void) { RAND_egd(); return 0; }
      |               ^~~~~~~~
      |               RAND_add
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/rand.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { RAND_egd(); return 0; }
/* end */

--------------------

have_func: checking for ENGINE_load_dynamic() in openssl/engine.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/engine.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { ENGINE_load_dynamic(); return 0; }
/* end */

--------------------

have_func: checking for ENGINE_load_4758cca() in openssl/engine.h... -------------------- no

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:17:15: error: implicit declaration of function ‘ENGINE_load_4758cca’ [-Wimplicit-function-declaration]
   17 | int t(void) { ENGINE_load_4758cca(); return 0; }
      |               ^~~~~~~~~~~~~~~~~~~
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/engine.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { ENGINE_load_4758cca(); return 0; }
/* end */

--------------------

have_func: checking for ENGINE_load_aep() in openssl/engine.h... -------------------- no

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:17:15: error: implicit declaration of function ‘ENGINE_load_aep’; did you mean ‘ENGINE_load_rdrand’? [-Wimplicit-function-declaration]
   17 | int t(void) { ENGINE_load_aep(); return 0; }
      |               ^~~~~~~~~~~~~~~
      |               ENGINE_load_rdrand
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/engine.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { ENGINE_load_aep(); return 0; }
/* end */

--------------------

have_func: checking for ENGINE_load_atalla() in openssl/engine.h... -------------------- no

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:17:15: error: implicit declaration of function ‘ENGINE_load_atalla’; did you mean ‘ENGINE_load_dynamic’? [-Wimplicit-function-declaration]
   17 | int t(void) { ENGINE_load_atalla(); return 0; }
      |               ^~~~~~~~~~~~~~~~~~
      |               ENGINE_load_dynamic
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/engine.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { ENGINE_load_atalla(); return 0; }
/* end */

--------------------

have_func: checking for ENGINE_load_chil() in openssl/engine.h... -------------------- no

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:17:15: error: implicit declaration of function ‘ENGINE_load_chil’; did you mean ‘ENGINE_load_dynamic’? [-Wimplicit-function-declaration]
   17 | int t(void) { ENGINE_load_chil(); return 0; }
      |               ^~~~~~~~~~~~~~~~
      |               ENGINE_load_dynamic
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/engine.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { ENGINE_load_chil(); return 0; }
/* end */

--------------------

have_func: checking for ENGINE_load_cswift() in openssl/engine.h... -------------------- no

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:17:15: error: implicit declaration of function ‘ENGINE_load_cswift’; did you mean ‘ENGINE_load_rdrand’? [-Wimplicit-function-declaration]
   17 | int t(void) { ENGINE_load_cswift(); return 0; }
      |               ^~~~~~~~~~~~~~~~~~
      |               ENGINE_load_rdrand
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/engine.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { ENGINE_load_cswift(); return 0; }
/* end */

--------------------

have_func: checking for ENGINE_load_nuron() in openssl/engine.h... -------------------- no

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:17:15: error: implicit declaration of function ‘ENGINE_load_nuron’; did you mean ‘ENGINE_load_rdrand’? [-Wimplicit-function-declaration]
   17 | int t(void) { ENGINE_load_nuron(); return 0; }
      |               ^~~~~~~~~~~~~~~~~
      |               ENGINE_load_rdrand
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/engine.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { ENGINE_load_nuron(); return 0; }
/* end */

--------------------

have_func: checking for ENGINE_load_sureware() in openssl/engine.h... -------------------- no

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:17:15: error: implicit declaration of function ‘ENGINE_load_sureware’; did you mean ‘ENGINE_load_rdrand’? [-Wimplicit-function-declaration]
   17 | int t(void) { ENGINE_load_sureware(); return 0; }
      |               ^~~~~~~~~~~~~~~~~~~~
      |               ENGINE_load_rdrand
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/engine.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { ENGINE_load_sureware(); return 0; }
/* end */

--------------------

have_func: checking for ENGINE_load_ubsec() in openssl/engine.h... -------------------- no

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:17:15: error: implicit declaration of function ‘ENGINE_load_ubsec’; did you mean ‘ENGINE_load_dynamic’? [-Wimplicit-function-declaration]
   17 | int t(void) { ENGINE_load_ubsec(); return 0; }
      |               ^~~~~~~~~~~~~~~~~
      |               ENGINE_load_dynamic
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/engine.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { ENGINE_load_ubsec(); return 0; }
/* end */

--------------------

have_func: checking for ENGINE_load_padlock() in openssl/engine.h... -------------------- no

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:17:15: error: implicit declaration of function ‘ENGINE_load_padlock’; did you mean ‘ENGINE_load_public_key’? [-Wimplicit-function-declaration]
   17 | int t(void) { ENGINE_load_padlock(); return 0; }
      |               ^~~~~~~~~~~~~~~~~~~
      |               ENGINE_load_public_key
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/engine.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { ENGINE_load_padlock(); return 0; }
/* end */

--------------------

have_func: checking for ENGINE_load_capi() in openssl/engine.h... -------------------- no

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:17:15: error: implicit declaration of function ‘ENGINE_load_capi’; did you mean ‘ENGINE_load_dynamic’? [-Wimplicit-function-declaration]
   17 | int t(void) { ENGINE_load_capi(); return 0; }
      |               ^~~~~~~~~~~~~~~~
      |               ENGINE_load_dynamic
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/engine.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { ENGINE_load_capi(); return 0; }
/* end */

--------------------

have_func: checking for ENGINE_load_gmp() in openssl/engine.h... -------------------- no

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:17:15: error: implicit declaration of function ‘ENGINE_load_gmp’ [-Wimplicit-function-declaration]
   17 | int t(void) { ENGINE_load_gmp(); return 0; }
      |               ^~~~~~~~~~~~~~~
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/engine.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { ENGINE_load_gmp(); return 0; }
/* end */

--------------------

have_func: checking for ENGINE_load_gost() in openssl/engine.h... -------------------- no

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:17:15: error: implicit declaration of function ‘ENGINE_load_gost’ [-Wimplicit-function-declaration]
   17 | int t(void) { ENGINE_load_gost(); return 0; }
      |               ^~~~~~~~~~~~~~~~
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/engine.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { ENGINE_load_gost(); return 0; }
/* end */

--------------------

have_func: checking for ENGINE_load_cryptodev() in openssl/engine.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/engine.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { ENGINE_load_cryptodev(); return 0; }
/* end */

--------------------

have_func: checking for i2d_re_X509_tbs(NULL, NULL) in openssl/x509.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/x509.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { i2d_re_X509_tbs(NULL, NULL); return 0; }
/* end */

--------------------

have_struct_member: checking for SSL.ctx in openssl/ssl.h... -------------------- no

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC   -c conftest.c"
conftest.c:6:27: error: invalid use of incomplete typedef ‘SSL’ {aka ‘struct ssl_st’}
    6 | int s = (char *)&((SSL*)0)->ctx - (char *)0;
      |                           ^~
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/ssl.h>
 4: 
 5: /*top*/
 6: int s = (char *)&((SSL*)0)->ctx - (char *)0;
 7: int main(int argc, char **argv)
 8: {
 9:   return !!argv[argc];
10: }
/* end */

--------------------

have_func: checking for EVP_MD_CTX_new() in openssl/evp.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/evp.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { EVP_MD_CTX_new(); return 0; }
/* end */

--------------------

have_func: checking for EVP_MD_CTX_free(NULL) in openssl/evp.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/evp.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { EVP_MD_CTX_free(NULL); return 0; }
/* end */

--------------------

have_func: checking for EVP_MD_CTX_pkey_ctx(NULL) in openssl/evp.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/evp.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { EVP_MD_CTX_pkey_ctx(NULL); return 0; }
/* end */

--------------------

have_func: checking for X509_STORE_get_ex_data(NULL, 0) in openssl/x509.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/x509.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { X509_STORE_get_ex_data(NULL, 0); return 0; }
/* end */

--------------------

have_func: checking for X509_STORE_set_ex_data(NULL, 0, NULL) in openssl/x509.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/x509.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { X509_STORE_set_ex_data(NULL, 0, NULL); return 0; }
/* end */

--------------------

have_func: checking for X509_STORE_get_ex_new_index(0, NULL, NULL, NULL, NULL) in openssl/x509.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/x509.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { X509_STORE_get_ex_new_index(0, NULL, NULL, NULL, NULL); return 0; }
/* end */

--------------------

have_func: checking for X509_CRL_get0_signature(NULL, NULL, NULL) in openssl/x509.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/x509.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { X509_CRL_get0_signature(NULL, NULL, NULL); return 0; }
/* end */

--------------------

have_func: checking for X509_REQ_get0_signature(NULL, NULL, NULL) in openssl/x509.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/x509.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { X509_REQ_get0_signature(NULL, NULL, NULL); return 0; }
/* end */

--------------------

have_func: checking for X509_REVOKED_get0_serialNumber(NULL) in openssl/x509.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/x509.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { X509_REVOKED_get0_serialNumber(NULL); return 0; }
/* end */

--------------------

have_func: checking for X509_REVOKED_get0_revocationDate(NULL) in openssl/x509.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/x509.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { X509_REVOKED_get0_revocationDate(NULL); return 0; }
/* end */

--------------------

have_func: checking for X509_get0_tbs_sigalg(NULL) in openssl/x509.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/x509.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { X509_get0_tbs_sigalg(NULL); return 0; }
/* end */

--------------------

have_func: checking for X509_STORE_CTX_get0_untrusted(NULL) in openssl/x509.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/x509.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { X509_STORE_CTX_get0_untrusted(NULL); return 0; }
/* end */

--------------------

have_func: checking for X509_STORE_CTX_get0_cert(NULL) in openssl/x509.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/x509.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { X509_STORE_CTX_get0_cert(NULL); return 0; }
/* end */

--------------------

have_func: checking for X509_STORE_CTX_get0_chain(NULL) in openssl/x509.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/x509.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { X509_STORE_CTX_get0_chain(NULL); return 0; }
/* end */

--------------------

have_func: checking for OCSP_SINGLERESP_get0_id(NULL) in openssl/ocsp.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/ocsp.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { OCSP_SINGLERESP_get0_id(NULL); return 0; }
/* end */

--------------------

have_func: checking for SSL_CTX_get_ciphers(NULL) in openssl/ssl.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/ssl.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { SSL_CTX_get_ciphers(NULL); return 0; }
/* end */

--------------------

have_func: checking for X509_up_ref(NULL) in openssl/x509.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/x509.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { X509_up_ref(NULL); return 0; }
/* end */

--------------------

have_func: checking for X509_CRL_up_ref(NULL) in openssl/x509.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/x509.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { X509_CRL_up_ref(NULL); return 0; }
/* end */

--------------------

have_func: checking for X509_STORE_up_ref(NULL) in openssl/x509.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/x509.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { X509_STORE_up_ref(NULL); return 0; }
/* end */

--------------------

have_func: checking for SSL_SESSION_up_ref(NULL) in openssl/ssl.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/ssl.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { SSL_SESSION_up_ref(NULL); return 0; }
/* end */

--------------------

have_func: checking for EVP_PKEY_up_ref(NULL) in openssl/evp.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/evp.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { EVP_PKEY_up_ref(NULL); return 0; }
/* end */

--------------------

have_func: checking for SSL_CTX_set_min_proto_version(NULL, 0) in openssl/ssl.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/ssl.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { SSL_CTX_set_min_proto_version(NULL, 0); return 0; }
/* end */

--------------------

have_func: checking for SSL_CTX_get_security_level(NULL) in openssl/ssl.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/ssl.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { SSL_CTX_get_security_level(NULL); return 0; }
/* end */

--------------------

have_func: checking for X509_get0_notBefore(NULL) in openssl/x509.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/x509.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { X509_get0_notBefore(NULL); return 0; }
/* end */

--------------------

have_func: checking for SSL_SESSION_get_protocol_version(NULL) in openssl/ssl.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/ssl.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { SSL_SESSION_get_protocol_version(NULL); return 0; }
/* end */

--------------------

have_func: checking for TS_STATUS_INFO_get0_status(NULL) in openssl/ts.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/ts.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { TS_STATUS_INFO_get0_status(NULL); return 0; }
/* end */

--------------------

have_func: checking for TS_STATUS_INFO_get0_text(NULL) in openssl/ts.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/ts.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { TS_STATUS_INFO_get0_text(NULL); return 0; }
/* end */

--------------------

have_func: checking for TS_STATUS_INFO_get0_failure_info(NULL) in openssl/ts.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/ts.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { TS_STATUS_INFO_get0_failure_info(NULL); return 0; }
/* end */

--------------------

have_func: checking for TS_VERIFY_CTS_set_certs(NULL, NULL) in openssl/ts.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:17:1: warning: ‘TS_VERIFY_CTX_set_certs’ is deprecated: Since OpenSSL 3.4;Unclear semantics, replace with TS_VERIFY_CTX_set0_certs(). [-Wdeprecated-declarations]
   17 | int t(void) { TS_VERIFY_CTS_set_certs(NULL, NULL); return 0; }
      | ^~~
In file included from conftest.c:3:
/usr/include/openssl/ts.h:443:17: note: declared here
  443 | STACK_OF(X509) *TS_VERIFY_CTX_set_certs(TS_VERIFY_CTX *ctx, STACK_OF(X509) *certs);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/ts.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { TS_VERIFY_CTS_set_certs(NULL, NULL); return 0; }
/* end */

--------------------

have_func: checking for TS_VERIFY_CTX_set_store(NULL, NULL) in openssl/ts.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:17:1: warning: ‘TS_VERIFY_CTX_set_store’ is deprecated: Since OpenSSL 3.4;Unclear semantics, replace with TS_VERIFY_CTX_set0_store(). [-Wdeprecated-declarations]
   17 | int t(void) { TS_VERIFY_CTX_set_store(NULL, NULL); return 0; }
      | ^~~
In file included from conftest.c:3:
/usr/include/openssl/ts.h:435:13: note: declared here
  435 | X509_STORE *TS_VERIFY_CTX_set_store(TS_VERIFY_CTX *ctx, X509_STORE *s);
      |             ^~~~~~~~~~~~~~~~~~~~~~~
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/ts.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { TS_VERIFY_CTX_set_store(NULL, NULL); return 0; }
/* end */

--------------------

have_func: checking for TS_VERIFY_CTX_add_flags(NULL, 0) in openssl/ts.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/ts.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { TS_VERIFY_CTX_add_flags(NULL, 0); return 0; }
/* end */

--------------------

have_func: checking for TS_RESP_CTX_set_time_cb(NULL, NULL, NULL) in openssl/ts.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/ts.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { TS_RESP_CTX_set_time_cb(NULL, NULL, NULL); return 0; }
/* end */

--------------------

have_func: checking for EVP_PBE_scrypt("", 0, (unsigned char *)"", 0, 0, 0, 0, 0, NULL, 0) in openssl/evp.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:17:40: warning: ‘s1’ may be used uninitialized [-Wmaybe-uninitialized]
   17 | int t(void) { char s1[1024], s2[1024]; EVP_PBE_scrypt(s1, 0, (unsigned char *)s2, 0, 0, 0, 0, 0, NULL, 0); return 0; }
      |                                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
In file included from conftest.c:3:
/usr/include/openssl/evp.h:1555:5: note: by argument 1 of type ‘const char *’ to ‘EVP_PBE_scrypt’ declared here
 1555 | int EVP_PBE_scrypt(const char *pass, size_t passlen,
      |     ^~~~~~~~~~~~~~
conftest.c:17:20: note: ‘s1’ declared here
   17 | int t(void) { char s1[1024], s2[1024]; EVP_PBE_scrypt(s1, 0, (unsigned char *)s2, 0, 0, 0, 0, 0, NULL, 0); return 0; }
      |                    ^~
conftest.c:17:40: warning: ‘s2’ may be used uninitialized [-Wmaybe-uninitialized]
   17 | int t(void) { char s1[1024], s2[1024]; EVP_PBE_scrypt(s1, 0, (unsigned char *)s2, 0, 0, 0, 0, 0, NULL, 0); return 0; }
      |                                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/openssl/evp.h:1555:5: note: by argument 3 of type ‘const unsigned char *’ to ‘EVP_PBE_scrypt’ declared here
 1555 | int EVP_PBE_scrypt(const char *pass, size_t passlen,
      |     ^~~~~~~~~~~~~~
conftest.c:17:30: note: ‘s2’ declared here
   17 | int t(void) { char s1[1024], s2[1024]; EVP_PBE_scrypt(s1, 0, (unsigned char *)s2, 0, 0, 0, 0, 0, NULL, 0); return 0; }
      |                              ^~
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/evp.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { char s1[1024], s2[1024]; EVP_PBE_scrypt(s1, 0, (unsigned char *)s2, 0, 0, 0, 0, 0, NULL, 0); return 0; }
/* end */

--------------------

have_func: checking for SSL_CTX_set_post_handshake_auth(NULL, 0) in openssl/ssl.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/ssl.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { SSL_CTX_set_post_handshake_auth(NULL, 0); return 0; }
/* end */

--------------------

have_func: checking for X509_STORE_get0_param(NULL) in openssl/x509.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/x509.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { X509_STORE_get0_param(NULL); return 0; }
/* end */

--------------------

have_func: checking for EVP_PKEY_check(NULL) in openssl/evp.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/evp.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { EVP_PKEY_check(NULL); return 0; }
/* end */

--------------------

have_func: checking for EVP_PKEY_new_raw_private_key(0, NULL, (unsigned char *)"", 0) in openssl/evp.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:17:30: warning: ‘s1’ may be used uninitialized [-Wmaybe-uninitialized]
   17 | int t(void) { char s1[1024]; EVP_PKEY_new_raw_private_key(0, NULL, (unsigned char *)s1, 0); return 0; }
      |                              ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
In file included from conftest.c:3:
/usr/include/openssl/evp.h:1906:11: note: by argument 3 of type ‘const unsigned char *’ to ‘EVP_PKEY_new_raw_private_key’ declared here
 1906 | EVP_PKEY *EVP_PKEY_new_raw_private_key(int type, ENGINE *e,
      |           ^~~~~~~~~~~~~~~~~~~~~~~~~~~~
conftest.c:17:20: note: ‘s1’ declared here
   17 | int t(void) { char s1[1024]; EVP_PKEY_new_raw_private_key(0, NULL, (unsigned char *)s1, 0); return 0; }
      |                    ^~
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/evp.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { char s1[1024]; EVP_PKEY_new_raw_private_key(0, NULL, (unsigned char *)s1, 0); return 0; }
/* end */

--------------------

have_func: checking for SSL_CTX_set_ciphersuites(NULL, "") in openssl/ssl.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:17:30: warning: ‘s1’ may be used uninitialized [-Wmaybe-uninitialized]
   17 | int t(void) { char s1[1024]; SSL_CTX_set_ciphersuites(NULL, s1); return 0; }
      |                              ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
In file included from conftest.c:3:
/usr/include/openssl/ssl.h:1668:12: note: by argument 2 of type ‘const char *’ to ‘SSL_CTX_set_ciphersuites’ declared here
 1668 | __owur int SSL_CTX_set_ciphersuites(SSL_CTX *ctx, const char *str);
      |            ^~~~~~~~~~~~~~~~~~~~~~~~
conftest.c:17:20: note: ‘s1’ declared here
   17 | int t(void) { char s1[1024]; SSL_CTX_set_ciphersuites(NULL, s1); return 0; }
      |                    ^~
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/ssl.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { char s1[1024]; SSL_CTX_set_ciphersuites(NULL, s1); return 0; }
/* end */

--------------------

have_func: checking for SSL_set0_tmp_dh_pkey(NULL, NULL) in openssl/ssl.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/ssl.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { SSL_set0_tmp_dh_pkey(NULL, NULL); return 0; }
/* end */

--------------------

have_func: checking for ERR_get_error_all(NULL, NULL, NULL, NULL, NULL) in openssl/err.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/err.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { ERR_get_error_all(NULL, NULL, NULL, NULL, NULL); return 0; }
/* end */

--------------------

have_func: checking for TS_VERIFY_CTX_set_certs(NULL, NULL) in openssl/ts.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:17:1: warning: ‘TS_VERIFY_CTX_set_certs’ is deprecated: Since OpenSSL 3.4;Unclear semantics, replace with TS_VERIFY_CTX_set0_certs(). [-Wdeprecated-declarations]
   17 | int t(void) { TS_VERIFY_CTX_set_certs(NULL, NULL); return 0; }
      | ^~~
In file included from conftest.c:3:
/usr/include/openssl/ts.h:443:17: note: declared here
  443 | STACK_OF(X509) *TS_VERIFY_CTX_set_certs(TS_VERIFY_CTX *ctx, STACK_OF(X509) *certs);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/ts.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { TS_VERIFY_CTX_set_certs(NULL, NULL); return 0; }
/* end */

--------------------

have_func: checking for SSL_CTX_load_verify_file(NULL, "") in openssl/ssl.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:17:30: warning: ‘s1’ may be used uninitialized [-Wmaybe-uninitialized]
   17 | int t(void) { char s1[1024]; SSL_CTX_load_verify_file(NULL, s1); return 0; }
      |                              ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
In file included from conftest.c:3:
/usr/include/openssl/ssl.h:2179:12: note: by argument 2 of type ‘const char *’ to ‘SSL_CTX_load_verify_file’ declared here
 2179 | __owur int SSL_CTX_load_verify_file(SSL_CTX *ctx, const char *CAfile);
      |            ^~~~~~~~~~~~~~~~~~~~~~~~
conftest.c:17:20: note: ‘s1’ declared here
   17 | int t(void) { char s1[1024]; SSL_CTX_load_verify_file(NULL, s1); return 0; }
      |                    ^~
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/ssl.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { char s1[1024]; SSL_CTX_load_verify_file(NULL, s1); return 0; }
/* end */

--------------------

have_func: checking for BN_check_prime(NULL, NULL, NULL) in openssl/bn.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/bn.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { BN_check_prime(NULL, NULL, NULL); return 0; }
/* end */

--------------------

have_func: checking for EVP_MD_CTX_get0_md(NULL) in openssl/evp.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/evp.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { EVP_MD_CTX_get0_md(NULL); return 0; }
/* end */

--------------------

have_func: checking for EVP_MD_CTX_get_pkey_ctx(NULL) in openssl/evp.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/evp.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { EVP_MD_CTX_get_pkey_ctx(NULL); return 0; }
/* end */

--------------------

have_func: checking for EVP_PKEY_eq(NULL, NULL) in openssl/evp.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/evp.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { EVP_PKEY_eq(NULL, NULL); return 0; }
/* end */

--------------------

have_func: checking for EVP_PKEY_dup(NULL) in openssl/evp.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed    -lssl -lcrypto  -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby -lssl -lcrypto  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <openssl/evp.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: 
17: int t(void) { EVP_PKEY_dup(NULL); return 0; }
/* end */

--------------------

=== Checking done. ===
extconf.h is:
/* begin */
 1: #ifndef EXTCONF_H
 2: #define EXTCONF_H
 3: #define OPENSSL_SUPPRESS_DEPRECATED 1
 4: #define HAVE_RB_IO_DESCRIPTOR 1
 5: #define HAVE_RB_IO_MAYBE_WAIT 1
 6: #define HAVE_RB_IO_TIMEOUT 1
 7: #define HAVE_OPENSSL_SSL_H 1
 8: #define HAVE_ENGINE_LOAD_DYNAMIC 1
 9: #define HAVE_ENGINE_LOAD_CRYPTODEV 1
10: #define HAVE_I2D_RE_X509_TBS 1
11: #define HAVE_OPAQUE_OPENSSL 1
12: #define HAVE_EVP_MD_CTX_NEW 1
13: #define HAVE_EVP_MD_CTX_FREE 1
14: #define HAVE_EVP_MD_CTX_PKEY_CTX 1
15: #define HAVE_X509_STORE_GET_EX_DATA 1
16: #define HAVE_X509_STORE_SET_EX_DATA 1
17: #define HAVE_X509_STORE_GET_EX_NEW_INDEX 1
18: #define HAVE_X509_CRL_GET0_SIGNATURE 1
19: #define HAVE_X509_REQ_GET0_SIGNATURE 1
20: #define HAVE_X509_REVOKED_GET0_SERIALNUMBER 1
21: #define HAVE_X509_REVOKED_GET0_REVOCATIONDATE 1
22: #define HAVE_X509_GET0_TBS_SIGALG 1
23: #define HAVE_X509_STORE_CTX_GET0_UNTRUSTED 1
24: #define HAVE_X509_STORE_CTX_GET0_CERT 1
25: #define HAVE_X509_STORE_CTX_GET0_CHAIN 1
26: #define HAVE_OCSP_SINGLERESP_GET0_ID 1
27: #define HAVE_SSL_CTX_GET_CIPHERS 1
28: #define HAVE_X509_UP_REF 1
29: #define HAVE_X509_CRL_UP_REF 1
30: #define HAVE_X509_STORE_UP_REF 1
31: #define HAVE_SSL_SESSION_UP_REF 1
32: #define HAVE_EVP_PKEY_UP_REF 1
33: #define HAVE_SSL_CTX_SET_MIN_PROTO_VERSION 1
34: #define HAVE_SSL_CTX_GET_SECURITY_LEVEL 1
35: #define HAVE_X509_GET0_NOTBEFORE 1
36: #define HAVE_SSL_SESSION_GET_PROTOCOL_VERSION 1
37: #define HAVE_TS_STATUS_INFO_GET0_STATUS 1
38: #define HAVE_TS_STATUS_INFO_GET0_TEXT 1
39: #define HAVE_TS_STATUS_INFO_GET0_FAILURE_INFO 1
40: #define HAVE_TS_VERIFY_CTS_SET_CERTS 1
41: #define HAVE_TS_VERIFY_CTX_SET_STORE 1
42: #define HAVE_TS_VERIFY_CTX_ADD_FLAGS 1
43: #define HAVE_TS_RESP_CTX_SET_TIME_CB 1
44: #define HAVE_EVP_PBE_SCRYPT 1
45: #define HAVE_SSL_CTX_SET_POST_HANDSHAKE_AUTH 1
46: #define HAVE_X509_STORE_GET0_PARAM 1
47: #define HAVE_EVP_PKEY_CHECK 1
48: #define HAVE_EVP_PKEY_NEW_RAW_PRIVATE_KEY 1
49: #define HAVE_SSL_CTX_SET_CIPHERSUITES 1
50: #define HAVE_SSL_SET0_TMP_DH_PKEY 1
51: #define HAVE_ERR_GET_ERROR_ALL 1
52: #define HAVE_TS_VERIFY_CTX_SET_CERTS 1
53: #define HAVE_SSL_CTX_LOAD_VERIFY_FILE 1
54: #define HAVE_BN_CHECK_PRIME 1
55: #define HAVE_EVP_MD_CTX_GET0_MD 1
56: #define HAVE_EVP_MD_CTX_GET_PKEY_CTX 1
57: #define HAVE_EVP_PKEY_EQ 1
58: #define HAVE_EVP_PKEY_DUP 1
59: #endif
/* end */

Done.
