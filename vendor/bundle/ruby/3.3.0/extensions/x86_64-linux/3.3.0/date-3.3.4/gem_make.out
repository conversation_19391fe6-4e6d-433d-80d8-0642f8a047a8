current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/date-3.3.4/ext/date
/home/<USER>/.rbenv/versions/3.3.0/bin/ruby extconf.rb
checking for rb_category_warn()... yes
checking for timezone in time.h with  -Werror... yes
checking for altzone in time.h with  -Werror... no
creating Makefile

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/date-3.3.4/ext/date
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-qkrcov sitelibdir\=./.gem.20250709-4286-qkrcov clean

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/date-3.3.4/ext/date
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-qkrcov sitelibdir\=./.gem.20250709-4286-qkrcov
compiling date_core.c
compiling date_parse.c
compiling date_strftime.c
compiling date_strptime.c
linking shared-object date_core.so

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/date-3.3.4/ext/date
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-qkrcov sitelibdir\=./.gem.20250709-4286-qkrcov install
/usr/bin/install -c -m 0755 date_core.so ./.gem.20250709-4286-qkrcov

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/date-3.3.4/ext/date
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-qkrcov sitelibdir\=./.gem.20250709-4286-qkrcov clean
