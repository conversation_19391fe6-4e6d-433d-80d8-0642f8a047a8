current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/psych-5.1.2/ext/psych
/home/<USER>/.rbenv/versions/3.3.0/bin/ruby extconf.rb
checking for pkg-config for yaml-0.1... not found
checking for yaml.h... yes
checking for yaml_get_version() in -lyaml... yes
creating Makefile

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/psych-5.1.2/ext/psych
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-jnkr2i sitelibdir\=./.gem.20250709-4286-jnkr2i clean
cd libyaml && make clean
/bin/sh: 1: cd: can't cd to libyaml
make: [Makefile:283: clean-so] Error 2 (ignored)

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/psych-5.1.2/ext/psych
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-jnkr2i sitelibdir\=./.gem.20250709-4286-jnkr2i
compiling psych.c
compiling psych_emitter.c
compiling psych_parser.c
compiling psych_to_ruby.c
compiling psych_yaml_tree.c
linking shared-object psych.so

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/psych-5.1.2/ext/psych
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-jnkr2i sitelibdir\=./.gem.20250709-4286-jnkr2i install
/usr/bin/install -c -m 0755 psych.so ./.gem.20250709-4286-jnkr2i

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/psych-5.1.2/ext/psych
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-jnkr2i sitelibdir\=./.gem.20250709-4286-jnkr2i clean
cd libyaml && make clean
/bin/sh: 1: cd: can't cd to libyaml
make: [Makefile:283: clean-so] Error 2 (ignored)
