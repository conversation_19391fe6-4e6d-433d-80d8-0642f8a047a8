current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/racc-1.8.1/ext/racc/cparse
/home/<USER>/.rbenv/versions/3.3.0/bin/ruby extconf.rb
creating Makefile

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/racc-1.8.1/ext/racc/cparse
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-u54xl3 sitelibdir\=./.gem.20250709-4286-u54xl3 clean

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/racc-1.8.1/ext/racc/cparse
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-u54xl3 sitelibdir\=./.gem.20250709-4286-u54xl3
compiling cparse.c
linking shared-object racc/cparse.so

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/racc-1.8.1/ext/racc/cparse
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-u54xl3 sitelibdir\=./.gem.20250709-4286-u54xl3 install
/usr/bin/install -c -m 0755 cparse.so ./.gem.20250709-4286-u54xl3/racc

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/racc-1.8.1/ext/racc/cparse
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-u54xl3 sitelibdir\=./.gem.20250709-4286-u54xl3 clean
