current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/json-2.7.2/ext/json
/home/<USER>/.rbenv/versions/3.3.0/bin/ruby extconf.rb
creating Makefile

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/json-2.7.2/ext/json
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-3n12wa sitelibdir\=./.gem.20250709-4286-3n12wa clean

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/json-2.7.2/ext/json
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-3n12wa sitelibdir\=./.gem.20250709-4286-3n12wa
make: Nothing to be done for 'all'.

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/json-2.7.2/ext/json
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-3n12wa sitelibdir\=./.gem.20250709-4286-3n12wa install

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/json-2.7.2/ext/json
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-3n12wa sitelibdir\=./.gem.20250709-4286-3n12wa clean
