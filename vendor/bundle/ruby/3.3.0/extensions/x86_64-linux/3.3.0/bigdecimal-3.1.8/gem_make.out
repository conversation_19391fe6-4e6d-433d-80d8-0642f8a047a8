current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/bigdecimal-3.1.8/ext/bigdecimal
/home/<USER>/.rbenv/versions/3.3.0/bin/ruby extconf.rb
checking for __builtin_clz()... yes
checking for __builtin_clzl()... yes
checking for __builtin_clzll()... yes
checking for float.h... yes
checking for math.h... yes
checking for stdbool.h... yes
checking for stdlib.h... yes
checking for x86intrin.h... yes
checking for _lzcnt_u32() in x86intrin.h... no
checking for _lzcnt_u64() in x86intrin.h... no
checking for intrin.h... no
checking for __lzcnt() in intrin.h... no
checking for __lzcnt64() in intrin.h... no
checking for _BitScanReverse() in intrin.h... no
checking for _BitScanReverse64() in intrin.h... no
checking for labs() in stdlib.h... yes
checking for llabs() in stdlib.h... yes
checking for finite() in math.h... yes
checking for isfinite() in math.h... no
checking for ruby/atomic.h... yes
checking for ruby/internal/has/builtin.h... yes
checking for ruby/internal/static_assert.h... yes
checking for rb_rational_num() in ruby.h... yes
checking for rb_rational_den() in ruby.h... yes
checking for rb_complex_real() in ruby.h... yes
checking for rb_complex_imag() in ruby.h... yes
checking for rb_opts_exception_p() in ruby.h... yes
checking for rb_category_warn() in ruby.h... yes
checking for RB_WARN_CATEGORY_DEPRECATED in ruby.h... yes
creating Makefile

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/bigdecimal-3.1.8/ext/bigdecimal
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-4ev0xd sitelibdir\=./.gem.20250709-4286-4ev0xd clean

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/bigdecimal-3.1.8/ext/bigdecimal
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-4ev0xd sitelibdir\=./.gem.20250709-4286-4ev0xd
compiling bigdecimal.c
compiling missing.c
linking shared-object bigdecimal.so

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/bigdecimal-3.1.8/ext/bigdecimal
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-4ev0xd sitelibdir\=./.gem.20250709-4286-4ev0xd install
/usr/bin/install -c -m 0755 bigdecimal.so ./.gem.20250709-4286-4ev0xd

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/bigdecimal-3.1.8/ext/bigdecimal
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-4ev0xd sitelibdir\=./.gem.20250709-4286-4ev0xd clean
