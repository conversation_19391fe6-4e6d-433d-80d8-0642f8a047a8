current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/pg-1.5.7/ext
/home/<USER>/.rbenv/versions/3.3.0/bin/ruby extconf.rb
Calling libpq with GVL unlocked
checking for pg_config... yes
Using config values from /usr/bin/pg_config
Using libpq from /usr/lib/x86_64-linux-gnu
checking for libpq-fe.h... yes
checking for libpq/libpq-fs.h... yes
checking for pg_config_manual.h... yes
checking for PQconnectdb() in -lpq... yes
checking for PQconninfo() in libpq-fe.h... yes
checking for PQsslAttribute() in libpq-fe.h... yes
checking for PQresultVerboseErrorMessage() in libpq-fe.h... yes
checking for PQencryptPasswordConn() in libpq-fe.h... yes
checking for PQresultMemorySize() in libpq-fe.h... yes
checking for PQenterPipelineMode() in libpq-fe.h... yes
checking for timegm()... yes
checking for rb_gc_adjust_memory_usage()... yes
checking for rb_gc_mark_movable()... yes
checking for rb_io_wait()... yes
checking for rb_io_descriptor()... yes
checking for unistd.h... yes
checking for inttypes.h... yes
checking for C99 variable length arrays... yes
creating extconf.h
creating Makefile

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/pg-1.5.7/ext
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-dcdwzy sitelibdir\=./.gem.20250709-4286-dcdwzy clean

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/pg-1.5.7/ext
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-dcdwzy sitelibdir\=./.gem.20250709-4286-dcdwzy
compiling gvl_wrappers.c
compiling pg.c
compiling pg_binary_decoder.c
compiling pg_binary_encoder.c
compiling pg_coder.c
compiling pg_connection.c
compiling pg_copy_coder.c
compiling pg_errors.c
compiling pg_record_coder.c
compiling pg_result.c
compiling pg_text_decoder.c
compiling pg_text_encoder.c
compiling pg_tuple.c
compiling pg_type_map.c
compiling pg_type_map_all_strings.c
compiling pg_type_map_by_class.c
compiling pg_type_map_by_column.c
compiling pg_type_map_by_mri_type.c
compiling pg_type_map_by_oid.c
compiling pg_type_map_in_ruby.c
compiling pg_util.c
linking shared-object pg_ext.so

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/pg-1.5.7/ext
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-dcdwzy sitelibdir\=./.gem.20250709-4286-dcdwzy install
/usr/bin/install -c -m 0755 pg_ext.so ./.gem.20250709-4286-dcdwzy
installing pg_ext libraries

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/pg-1.5.7/ext
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-dcdwzy sitelibdir\=./.gem.20250709-4286-dcdwzy clean
