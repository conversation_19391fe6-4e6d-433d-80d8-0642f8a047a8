have_header: checking for unistd.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: int main(int argc, char **argv)
4: {
5:   return !!argv[argc];
6: }
/* end */

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC   -c conftest.c"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: #include <unistd.h>
/* end */

--------------------

have_func: checking for rb_io_descriptor()... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:14:57: error: ‘rb_io_descriptor’ undeclared (first use in this function)
   14 | int t(void) { void ((*volatile p)()); p = (void ((*)()))rb_io_descriptor; return !p; }
      |                                                         ^~~~~~~~~~~~~~~~
conftest.c:14:57: note: each undeclared identifier is reported only once for each function it appears in
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: int t(void) { void ((*volatile p)()); p = (void ((*)()))rb_io_descriptor; return !p; }
/* end */

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: extern void rb_io_descriptor();
15: int t(void) { rb_io_descriptor(); return 0; }
/* end */

--------------------

have_header: checking for linux/aio_abi.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC   -c conftest.c"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: #include <linux/aio_abi.h>
/* end */

--------------------

have_header: checking for linux/io_uring.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC   -c conftest.c"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: #include <linux/io_uring.h>
/* end */

--------------------

have_header: checking for sys/select.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC   -c conftest.c"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: #include <sys/select.h>
/* end */

--------------------

have_type: checking for port_event_t in poll.h... -------------------- no

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC   -c conftest.c"
conftest.c:6:9: error: unknown type name ‘port_event_t’
    6 | typedef port_event_t conftest_type;
      |         ^~~~~~~~~~~~
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: #include <poll.h>
4: 
5: /*top*/
6: typedef port_event_t conftest_type;
7: int conftestval[sizeof(conftest_type)?1:-1];
/* end */

--------------------

have_header: checking for sys/epoll.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC   -c conftest.c"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: #include <sys/epoll.h>
/* end */

--------------------

have_header: checking for sys/event.h... -------------------- no

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC   -c conftest.c"
conftest.c:3:10: fatal error: sys/event.h: No such file or directory
    3 | #include <sys/event.h>
      |          ^~~~~~~~~~~~~
compilation terminated.
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: #include <sys/event.h>
/* end */

--------------------

have_type: checking for port_event_t in port.h... -------------------- no

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC   -c conftest.c"
conftest.c:3:10: fatal error: port.h: No such file or directory
    3 | #include <port.h>
      |          ^~~~~~~~
compilation terminated.
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: #include <port.h>
4: 
5: /*top*/
6: typedef port_event_t conftest_type;
7: int conftestval[sizeof(conftest_type)?1:-1];
/* end */

--------------------

have_header: checking for sys/resource.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC   -c conftest.c"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: #include <sys/resource.h>
/* end */

--------------------

