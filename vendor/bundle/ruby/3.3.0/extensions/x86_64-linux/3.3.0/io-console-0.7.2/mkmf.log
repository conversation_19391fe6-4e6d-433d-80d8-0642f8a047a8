have_func: checking for rb_io_path()... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: int main(int argc, char **argv)
4: {
5:   return !!argv[argc];
6: }
/* end */

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:14:57: error: ‘rb_io_path’ undeclared (first use in this function); did you mean ‘rb_io_puts’?
   14 | int t(void) { void ((*volatile p)()); p = (void ((*)()))rb_io_path; return !p; }
      |                                                         ^~~~~~~~~~
      |                                                         rb_io_puts
conftest.c:14:57: note: each undeclared identifier is reported only once for each function it appears in
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: int t(void) { void ((*volatile p)()); p = (void ((*)()))rb_io_path; return !p; }
/* end */

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: extern void rb_io_path();
15: int t(void) { rb_io_path(); return 0; }
/* end */

--------------------

have_func: checking for rb_io_descriptor()... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:14:57: error: ‘rb_io_descriptor’ undeclared (first use in this function)
   14 | int t(void) { void ((*volatile p)()); p = (void ((*)()))rb_io_descriptor; return !p; }
      |                                                         ^~~~~~~~~~~~~~~~
conftest.c:14:57: note: each undeclared identifier is reported only once for each function it appears in
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: int t(void) { void ((*volatile p)()); p = (void ((*)()))rb_io_descriptor; return !p; }
/* end */

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: extern void rb_io_descriptor();
15: int t(void) { rb_io_descriptor(); return 0; }
/* end */

--------------------

have_func: checking for rb_io_get_write_io()... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:14:57: error: ‘rb_io_get_write_io’ undeclared (first use in this function)
   14 | int t(void) { void ((*volatile p)()); p = (void ((*)()))rb_io_get_write_io; return !p; }
      |                                                         ^~~~~~~~~~~~~~~~~~
conftest.c:14:57: note: each undeclared identifier is reported only once for each function it appears in
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: int t(void) { void ((*volatile p)()); p = (void ((*)()))rb_io_get_write_io; return !p; }
/* end */

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: extern void rb_io_get_write_io();
15: int t(void) { rb_io_get_write_io(); return 0; }
/* end */

--------------------

have_func: checking for rb_io_closed_p()... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:14:57: error: ‘rb_io_closed_p’ undeclared (first use in this function); did you mean ‘rb_io_close’?
   14 | int t(void) { void ((*volatile p)()); p = (void ((*)()))rb_io_closed_p; return !p; }
      |                                                         ^~~~~~~~~~~~~~
      |                                                         rb_io_close
conftest.c:14:57: note: each undeclared identifier is reported only once for each function it appears in
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: int t(void) { void ((*volatile p)()); p = (void ((*)()))rb_io_closed_p; return !p; }
/* end */

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: extern void rb_io_closed_p();
15: int t(void) { rb_io_closed_p(); return 0; }
/* end */

--------------------

have_func: checking for rb_io_open_descriptor()... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:14:57: error: ‘rb_io_open_descriptor’ undeclared (first use in this function)
   14 | int t(void) { void ((*volatile p)()); p = (void ((*)()))rb_io_open_descriptor; return !p; }
      |                                                         ^~~~~~~~~~~~~~~~~~~~~
conftest.c:14:57: note: each undeclared identifier is reported only once for each function it appears in
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: int t(void) { void ((*volatile p)()); p = (void ((*)()))rb_io_open_descriptor; return !p; }
/* end */

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: extern void rb_io_open_descriptor();
15: int t(void) { rb_io_open_descriptor(); return 0; }
/* end */

--------------------

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC   -c conftest.c"
conftest.c:5:3: error: #error 
    5 | # error
      |   ^~~~~
conftest.c:6:1: error: expected identifier or ‘(’ before ‘|’ token
    6 | |:/ === _WIN32 undefined === /:|
      | ^
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: /*top*/
4: #ifndef _WIN32
5: # error
6: |:/ === _WIN32 undefined === /:|
7: #endif
/* end */

have_header: checking for termios.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC   -c conftest.c"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: #include <termios.h>
/* end */

--------------------

have_func: checking for cfmakeraw() in termios.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <termios.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: int t(void) { void ((*volatile p)()); p = (void ((*)()))cfmakeraw; return !p; }
/* end */

--------------------

have_header: checking for sys/ioctl.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC   -c conftest.c"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: #include <sys/ioctl.h>
/* end */

--------------------

have_macro: checking for HAVE_RUBY_FIBER_SCHEDULER_H... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC   -c conftest.c"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: /*top*/
4: #ifndef HAVE_RUBY_FIBER_SCHEDULER_H
5: # error
6: |:/ === HAVE_RUBY_FIBER_SCHEDULER_H undefined === /:|
7: #endif
/* end */

--------------------

