current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/io-console-0.7.2/ext/io/console
/home/<USER>/.rbenv/versions/3.3.0/bin/ruby extconf.rb
checking for rb_io_path()... yes
checking for rb_io_descriptor()... yes
checking for rb_io_get_write_io()... yes
checking for rb_io_closed_p()... yes
checking for rb_io_open_descriptor()... yes
checking for termios.h... yes
checking for cfmakeraw() in termios.h... yes
checking for sys/ioctl.h... yes
checking for HAVE_RUBY_FIBER_SCHEDULER_H... yes
creating Makefile

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/io-console-0.7.2/ext/io/console
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-v0e1of sitelibdir\=./.gem.20250709-4286-v0e1of clean

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/io-console-0.7.2/ext/io/console
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-v0e1of sitelibdir\=./.gem.20250709-4286-v0e1of
compiling console.c
linking shared-object io/console.so

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/io-console-0.7.2/ext/io/console
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-v0e1of sitelibdir\=./.gem.20250709-4286-v0e1of install
/usr/bin/install -c -m 0755 console.so ./.gem.20250709-4286-v0e1of/io

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/io-console-0.7.2/ext/io/console
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-v0e1of sitelibdir\=./.gem.20250709-4286-v0e1of clean
