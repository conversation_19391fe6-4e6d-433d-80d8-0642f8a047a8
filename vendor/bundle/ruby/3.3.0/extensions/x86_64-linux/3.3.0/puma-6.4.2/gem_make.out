current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/puma-6.4.2/ext/puma_http11
/home/<USER>/.rbenv/versions/3.3.0/bin/ruby extconf.rb
checking for BIO_read() in -llibcrypto... no
checking for BIO_read() in -lcrypto... yes
checking for SSL_CTX_new() in -lssl... yes
checking for openssl/bio.h... yes
checking for DTLS_method() in openssl/ssl.h... yes
checking for SSL_CTX_set_session_cache_mode(NULL, 0) in openssl/ssl.h... yes
checking for TLS_server_method() in openssl/ssl.h... yes
checking for SSL_CTX_set_min_proto_version(NULL, 0) in openssl/ssl.h... yes
checking for X509_STORE_up_ref()... yes
checking for SSL_CTX_set_ecdh_auto(NULL, 0) in openssl/ssl.h... yes
checking for SSL_CTX_set_dh_auto(NULL, 0) in openssl/ssl.h... yes
checking for SSL_get1_peer_certificate() in openssl/ssl.h... yes
checking for Random.bytes... yes
creating Makefile

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/puma-6.4.2/ext/puma_http11
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-tb36g0 sitelibdir\=./.gem.20250709-4286-tb36g0 clean

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/puma-6.4.2/ext/puma_http11
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-tb36g0 sitelibdir\=./.gem.20250709-4286-tb36g0
compiling http11_parser.c
compiling mini_ssl.c
compiling puma_http11.c
linking shared-object puma/puma_http11.so

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/puma-6.4.2/ext/puma_http11
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-tb36g0 sitelibdir\=./.gem.20250709-4286-tb36g0 install
/usr/bin/install -c -m 0755 puma_http11.so ./.gem.20250709-4286-tb36g0/puma

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/puma-6.4.2/ext/puma_http11
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-tb36g0 sitelibdir\=./.gem.20250709-4286-tb36g0 clean
