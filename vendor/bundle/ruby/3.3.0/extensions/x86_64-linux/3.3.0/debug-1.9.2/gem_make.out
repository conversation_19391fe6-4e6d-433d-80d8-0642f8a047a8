current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/debug-1.9.2/ext/debug
/home/<USER>/.rbenv/versions/3.3.0/bin/ruby extconf.rb
creating Makefile

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/debug-1.9.2/ext/debug
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-tlijcj sitelibdir\=./.gem.20250709-4286-tlijcj clean

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/debug-1.9.2/ext/debug
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-tlijcj sitelibdir\=./.gem.20250709-4286-tlijcj
compiling debug.c
compiling iseq_collector.c
linking shared-object debug/debug.so

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/debug-1.9.2/ext/debug
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-tlijcj sitelibdir\=./.gem.20250709-4286-tlijcj install
/usr/bin/install -c -m 0755 debug.so ./.gem.20250709-4286-tlijcj/debug

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/debug-1.9.2/ext/debug
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-tlijcj sitelibdir\=./.gem.20250709-4286-tlijcj clean
