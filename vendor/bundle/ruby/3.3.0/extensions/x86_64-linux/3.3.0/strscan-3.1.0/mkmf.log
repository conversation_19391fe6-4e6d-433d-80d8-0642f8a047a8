have_func: checking for onig_region_memsize() in ruby.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: int main(int argc, char **argv)
4: {
5:   return !!argv[argc];
6: }
/* end */

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:16:57: error: ‘onig_region_memsize’ undeclared (first use in this function)
   16 | int t(void) { void ((*volatile p)()); p = (void ((*)()))onig_region_memsize; return !p; }
      |                                                         ^~~~~~~~~~~~~~~~~~~
conftest.c:16:57: note: each undeclared identifier is reported only once for each function it appears in
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <ruby.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: int t(void) { void ((*volatile p)()); p = (void ((*)()))onig_region_memsize; return !p; }
/* end */

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <ruby.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: extern void onig_region_memsize();
17: int t(void) { onig_region_memsize(); return 0; }
/* end */

--------------------

have_func: checking for rb_reg_onig_match() in ruby.h... -------------------- yes

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
conftest.c: In function ‘t’:
conftest.c:16:57: error: ‘rb_reg_onig_match’ undeclared (first use in this function); did you mean ‘rb_reg_nth_match’?
   16 | int t(void) { void ((*volatile p)()); p = (void ((*)()))rb_reg_onig_match; return !p; }
      |                                                         ^~~~~~~~~~~~~~~~~
      |                                                         rb_reg_nth_match
conftest.c:16:57: note: each undeclared identifier is reported only once for each function it appears in
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <ruby.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: int t(void) { void ((*volatile p)()); p = (void ((*)()))rb_reg_onig_match; return !p; }
/* end */

LD_LIBRARY_PATH=.:/home/<USER>/.rbenv/versions/3.3.0/lib "gcc -o conftest -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/x86_64-linux -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0/ruby/backward -I/home/<USER>/.rbenv/versions/3.3.0/include/ruby-3.3.0 -I.    -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wdiv-by-zero -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wpointer-arith -Wwrite-strings -Wold-style-definition -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable -Wmisleading-indentation -Wundef  -fPIC conftest.c  -L. -L/home/<USER>/.rbenv/versions/3.3.0/lib -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic -Wl,--no-as-needed     -Wl,-rpath,/home/<USER>/.rbenv/versions/3.3.0/lib -L/home/<USER>/.rbenv/versions/3.3.0/lib -lruby  -lm -lpthread  -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <ruby.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: extern void rb_reg_onig_match();
17: int t(void) { rb_reg_onig_match(); return 0; }
/* end */

--------------------

