current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/strscan-3.1.0/ext/strscan
/home/<USER>/.rbenv/versions/3.3.0/bin/ruby extconf.rb
checking for onig_region_memsize() in ruby.h... yes
checking for rb_reg_onig_match() in ruby.h... yes
creating Makefile

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/strscan-3.1.0/ext/strscan
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-xdidbi sitelibdir\=./.gem.20250709-4286-xdidbi clean

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/strscan-3.1.0/ext/strscan
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-xdidbi sitelibdir\=./.gem.20250709-4286-xdidbi
compiling strscan.c
linking shared-object strscan.so

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/strscan-3.1.0/ext/strscan
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-xdidbi sitelibdir\=./.gem.20250709-4286-xdidbi install
/usr/bin/install -c -m 0755 strscan.so ./.gem.20250709-4286-xdidbi

current directory: /home/<USER>/personal projects/EaseFood-Backend/vendor/bundle/ruby/3.3.0/gems/strscan-3.1.0/ext/strscan
make DESTDIR\= sitearchdir\=./.gem.20250709-4286-xdidbi sitelibdir\=./.gem.20250709-4286-xdidbi clean
