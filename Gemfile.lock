GEM
  remote: https://rubygems.org/
  specs:
    actioncable (7.1.3.4)
      actionpack (= 7.1.3.4)
      activesupport (= 7.1.3.4)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (7.1.3.4)
      actionpack (= 7.1.3.4)
      activejob (= 7.1.3.4)
      activerecord (= 7.1.3.4)
      activestorage (= 7.1.3.4)
      activesupport (= 7.1.3.4)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (7.1.3.4)
      actionpack (= 7.1.3.4)
      actionview (= 7.1.3.4)
      activejob (= 7.1.3.4)
      activesupport (= 7.1.3.4)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.2)
    actionpack (7.1.3.4)
      actionview (= 7.1.3.4)
      activesupport (= 7.1.3.4)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    actiontext (7.1.3.4)
      actionpack (= 7.1.3.4)
      activerecord (= 7.1.3.4)
      activestorage (= 7.1.3.4)
      activesupport (= 7.1.3.4)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (7.1.3.4)
      activesupport (= 7.1.3.4)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (7.1.3.4)
      activesupport (= 7.1.3.4)
      globalid (>= 0.3.6)
    activemodel (7.1.3.4)
      activesupport (= 7.1.3.4)
    activerecord (7.1.3.4)
      activemodel (= 7.1.3.4)
      activesupport (= 7.1.3.4)
      timeout (>= 0.4.0)
    activestorage (7.1.3.4)
      actionpack (= 7.1.3.4)
      activejob (= 7.1.3.4)
      activerecord (= 7.1.3.4)
      activesupport (= 7.1.3.4)
      marcel (~> 1.0)
    activesupport (7.1.3.4)
      base64
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      mutex_m
      tzinfo (~> 2.0)
    ast (2.4.2)
    base64 (0.2.0)
    bcrypt (3.1.20)
    bigdecimal (3.1.8)
    bootsnap (1.18.3)
      msgpack (~> 1.2)
    builder (3.3.0)
    concurrent-ruby (1.3.3)
    connection_pool (2.4.1)
    crass (1.0.6)
    csv (3.3.2)
    date (3.3.4)
    debug (1.9.2)
      irb (~> 1.10)
      reline (>= 0.3.8)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise-jwt (0.12.1)
      devise (~> 4.0)
      warden-jwt_auth (~> 0.10)
    dotenv (3.1.8)
    dotenv-rails (3.1.8)
      dotenv (= 3.1.8)
      railties (>= 6.1)
    drb (2.2.1)
    dry-auto_inject (1.0.1)
      dry-core (~> 1.0)
      zeitwerk (~> 2.6)
    dry-configurable (1.2.0)
      dry-core (~> 1.0, < 2)
      zeitwerk (~> 2.6)
    dry-core (1.0.1)
      concurrent-ruby (~> 1.0)
      zeitwerk (~> 2.6)
    erubi (1.13.0)
    faker (3.4.2)
      i18n (>= 1.8.11, < 2)
    ffi (1.17.0-aarch64-linux-gnu)
    ffi (1.17.0-arm-linux-gnu)
    ffi (1.17.0-arm64-darwin)
    ffi (1.17.0-x86-linux-gnu)
    ffi (1.17.0-x86_64-darwin)
    ffi (1.17.0-x86_64-linux-gnu)
    globalid (1.2.1)
      activesupport (>= 6.1)
    httparty (0.22.0)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    i18n (1.14.5)
      concurrent-ruby (~> 1.0)
    image_processing (1.13.0)
      mini_magick (>= 4.9.5, < 5)
      ruby-vips (>= 2.0.17, < 3)
    io-console (0.7.2)
    irb (1.14.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    json (2.7.2)
    jwt (2.8.2)
      base64
    language_server-protocol (********)
    logger (1.6.0)
    loofah (2.22.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    mini_magick (4.13.2)
    mini_mime (1.1.5)
    minitest (5.24.1)
    msgpack (1.7.2)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    mutex_m (0.2.0)
    net-imap (0.4.14)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.0)
      net-protocol
    nio4r (2.7.3)
    nokogiri (1.16.7-aarch64-linux)
      racc (~> 1.4)
    nokogiri (1.16.7-arm-linux)
      racc (~> 1.4)
    nokogiri (1.16.7-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.16.7-x86-linux)
      racc (~> 1.4)
    nokogiri (1.16.7-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.16.7-x86_64-linux)
      racc (~> 1.4)
    openssl (3.3.0)
    orm_adapter (0.5.0)
    parallel (1.25.1)
    parser (3.3.4.0)
      ast (~> 2.4.1)
      racc
    pg (1.5.7)
    psych (5.1.2)
      stringio
    puma (6.4.2)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (3.1.7)
    rack-attack (6.7.0)
      rack (>= 1.0, < 4)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-session (2.0.0)
      rack (>= 3.0.0)
    rack-test (2.1.0)
      rack (>= 1.3)
    rackup (2.1.0)
      rack (>= 3)
      webrick (~> 1.8)
    rails (7.1.3.4)
      actioncable (= 7.1.3.4)
      actionmailbox (= 7.1.3.4)
      actionmailer (= 7.1.3.4)
      actionpack (= 7.1.3.4)
      actiontext (= 7.1.3.4)
      actionview (= 7.1.3.4)
      activejob (= 7.1.3.4)
      activemodel (= 7.1.3.4)
      activerecord (= 7.1.3.4)
      activestorage (= 7.1.3.4)
      activesupport (= 7.1.3.4)
      bundler (>= 1.15.0)
      railties (= 7.1.3.4)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.0)
      loofah (~> 2.21)
      nokogiri (~> 1.14)
    railties (7.1.3.4)
      actionpack (= 7.1.3.4)
      activesupport (= 7.1.3.4)
      irb
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.2.1)
    rdoc (6.7.0)
      psych (>= 4.0.0)
    redis (5.4.0)
      redis-client (>= 0.22.0)
    redis-actionpack (5.5.0)
      actionpack (>= 5)
      redis-rack (>= 2.1.0, < 4)
      redis-store (>= 1.1.0, < 2)
    redis-activesupport (5.3.0)
      activesupport (>= 3, < 8)
      redis-store (>= 1.3, < 2)
    redis-client (0.24.0)
      connection_pool
    redis-rack (3.0.0)
      rack-session (>= 0.2.0)
      redis-store (>= 1.2, < 2)
    redis-rails (5.0.2)
      redis-actionpack (>= 5.0, < 6)
      redis-activesupport (>= 5.0, < 6)
      redis-store (>= 1.2, < 2)
    redis-store (1.11.0)
      redis (>= 4, < 6)
    regexp_parser (2.9.2)
    reline (0.5.9)
      io-console (~> 0.5)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rexml (3.3.2)
      strscan
    rubocop (1.65.0)
      json (~> 2.3)
      language_server-protocol (>= 3.17.0)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.4, < 3.0)
      rexml (>= 3.2.5, < 4.0)
      rubocop-ast (>= 1.31.1, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 3.0)
    rubocop-ast (1.31.3)
      parser (>= *******)
    ruby-progressbar (1.13.0)
    ruby-vips (2.2.2)
      ffi (~> 1.12)
      logger
    sidekiq (7.3.9)
      base64
      connection_pool (>= 2.3.0)
      logger
      rack (>= 2.2.4)
      redis-client (>= 0.22.2)
    stringio (3.1.1)
    strscan (3.1.0)
    thor (1.3.1)
    timeout (0.4.1)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (2.5.0)
    warden (1.2.9)
      rack (>= 2.0.9)
    warden-jwt_auth (0.10.0)
      dry-auto_inject (>= 0.8, < 2)
      dry-configurable (>= 0.13, < 2)
      jwt (~> 2.1)
      warden (~> 1.2)
    web-push (3.0.1)
      jwt (~> 2.0)
      openssl (~> 3.0)
    webrick (1.8.1)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.6.17)

PLATFORMS
  aarch64-linux
  arm-linux
  arm64-darwin
  x86-linux
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  bootsnap
  debug
  devise
  devise-jwt
  dotenv-rails
  faker
  httparty
  image_processing (~> 1.2)
  pg (~> 1.1)
  puma (>= 5.0)
  rack-attack
  rack-cors
  rails (~> 7.1.3, >= 7.1.3.4)
  redis
  redis-rails
  rubocop (>= 1.0, < 2.0)
  sidekiq
  tzinfo-data
  web-push

RUBY VERSION
   ruby 3.3.0p0

BUNDLED WITH
   2.5.14
