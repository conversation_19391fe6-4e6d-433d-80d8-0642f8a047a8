ENV['RAILS_ENV'] ||= 'test'
require_relative '../config/environment'
require 'rails/test_help'

class ActiveSupport::TestCase
  # Run tests in parallel with specified workers
  parallelize(workers: :number_of_processors)

  # Setup all fixtures in test/fixtures/*.yml for all tests in alphabetical order.
  fixtures :all

  # Add more helper methods to be used by all tests here...

  # Helper method for authentication in tests
  def sign_in_as(user)
    # For tests, we'll skip the actual authentication and just set the current user
    case user
    when Customer
      @current_customer = user
    when Vendor
      @current_vendor = user
    when Rider
      @current_rider = user
    when Admin
      @current_admin = user
    end

    # Generate a JWT token for the user
    payload = { id: user.id, exp: 24.hours.from_now.to_i }
    @token = JWT.encode(payload, Rails.application.credentials.devise_jwt_secret_key, 'HS256')
  end

  # Helper method for signing out in tests
  def sign_out(user_type)
    case user_type
    when :customer
      delete '/customers/sign_out'
    when :vendor
      delete '/vendors/sign_out'
    when :rider
      delete '/riders/sign_out'
    when :admin
      delete '/admins/sign_out'
    end
  end

  # Helper method to mock Paystack API responses
  def mock_paystack_service(&)
    mock_service = Minitest::Mock.new
    mock_response = {
      'status' => true,
      'data' => {
        'transfer_code' => 'TRF_123456789',
        'reference' => 'REF_123456789'
      }
    }

    mock_service.expect :initialize_transfer, mock_response, [Float, String, String]

    PaystackService.stub(:new, mock_service, &)

    mock_service.verify
  end
end
