require 'test_helper'

class ComplaintsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @complaint = complaints(:one)
    @customer = customers(:one)
    sign_in_as(@customer)
  end

  test 'should get index for authenticated customer' do
    get customer_complaints_url(@customer), headers: { Authorization: @token }
    assert_response :success
  end

  test 'should create complaint' do
    assert_difference('Complaint.count') do
      post complaints_url, params: {
        complaint: {
          name: 'Test User',
          email: '<EMAIL>',
          order_number: 'EF-2023-12-123',
          category: 'order',
          complaint: 'This is a test complaint with more than 10 characters'
        }
      }, headers: { Authorization: @token }
    end

    assert_response :created
  end

  test 'should create complaint without authentication' do
    sign_out(:customer)

    assert_difference('Complaint.count') do
      post complaints_url, params: {
        complaint: {
          name: 'Test User',
          email: '<EMAIL>',
          order_number: 'EF-2023-12-123',
          category: 'order',
          complaint: 'This is a test complaint with more than 10 characters'
        }
      }
    end

    assert_response :created
  end

  test 'should not create complaint with invalid data' do
    assert_no_difference('Complaint.count') do
      post complaints_url, params: {
        complaint: {
          name: '',
          email: 'invalid-email',
          order_number: '',
          category: '',
          complaint: 'Short'
        }
      }, headers: { Authorization: @token }
    end

    assert_response :unprocessable_entity
  end

  test 'should show complaint for authenticated customer' do
    get customer_complaint_url(@customer, @complaint), headers: { Authorization: @token }
    assert_response :success
  end
end
