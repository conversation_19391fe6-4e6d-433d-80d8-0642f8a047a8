require 'test_helper'

class SuggestionsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @suggestion = suggestions(:one)
    @customer = customers(:one)
    sign_in_as(@customer)
  end

  test 'should get index for authenticated customer' do
    get customer_suggestions_url(@customer), headers: { Authorization: @token }
    assert_response :success
  end

  test 'should create suggestion' do
    assert_difference('Suggestion.count') do
      post suggestions_url, params: {
        suggestion: {
          name: 'Test User',
          email: '<EMAIL>',
          category: 'menu',
          suggestion: 'This is a test suggestion with more than 10 characters',
          rating: 4
        }
      }, headers: { Authorization: @token }
    end

    assert_response :created
  end

  test 'should create suggestion without authentication' do
    sign_out(:customer)

    assert_difference('Suggestion.count') do
      post suggestions_url, params: {
        suggestion: {
          name: 'Test User',
          email: '<EMAIL>',
          category: 'menu',
          suggestion: 'This is a test suggestion with more than 10 characters',
          rating: 4
        }
      }
    end

    assert_response :created
  end

  test 'should not create suggestion with invalid data' do
    assert_no_difference('Suggestion.count') do
      post suggestions_url, params: {
        suggestion: {
          name: '',
          email: 'invalid-email',
          category: '',
          suggestion: 'Short',
          rating: 6
        }
      }, headers: { Authorization: @token }
    end

    assert_response :unprocessable_entity
  end

  test 'should show suggestion for authenticated customer' do
    get customer_suggestion_url(@customer, @suggestion), headers: { Authorization: @token }
    assert_response :success
  end
end
