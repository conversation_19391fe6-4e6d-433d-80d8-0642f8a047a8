require 'test_helper'
require 'minitest/mock'

class OrderDeliveriesSimplifiedTest < ActiveSupport::TestCase
  setup do
    @order = orders(:one)
    @customer = customers(:one)
    @vendor = vendors(:one)
    @rider = riders(:one)

    # Make sure vendor has required fields
    unless @vendor.operation_time.present? && @vendor.closing_time.present?
      @vendor.update!(
        operation_time: '08:00:00',
        closing_time: '20:00:00'
      )
    end

    # Set up order status
    @order.update!(
      status: Order::CONFIRMED,
      customer: @customer
    )

    # Set up payment details for vendor and rider
    @vendor.update!(
      account_name: 'Vendor Account',
      mobile_money_number: '**********',
      mobile_money_provider: 'MTN',
      paystack_recipient_code: 'RCP_123456789'
    )

    @rider.update!(
      account_name: 'Rider Account',
      mobile_money_number: '**********',
      mobile_money_provider: 'Vodafone',
      paystack_recipient_code: 'RCP_987654321'
    )
  end

  test 'should mark order as delivered and initiate transfers' do
    # Delete existing transfers
    Transfer.delete_all

    # Create a mock service
    mock_service = Object.new
    def mock_service.initialize_transfer(_amount, _recipient_code, _reason)
      {
        'status' => true,
        'data' => {
          'transfer_code' => "TRF_#{Time.now.to_i}_#{rand(1000)}",
          'reference' => "REF_#{Time.now.to_i}_#{rand(1000)}"
        }
      }
    end

    # Stub the PaystackService.new method
    PaystackService.stub :new, mock_service do
      # Mark order as delivered
      assert @order.mark_as_delivered!

      # Check order status
      assert_equal Order::DELIVERED, @order.status

      # Check transfers
      assert_equal 2, @order.transfers.count

      # Check vendor transfer
      vendor_transfer = @order.transfers.find_by(recipient: @vendor)
      assert_equal 400.0, vendor_transfer.amount
      assert_equal 20.0, vendor_transfer.service_fee
      assert_equal Transfer::PROCESSING, vendor_transfer.status
      assert_not_nil vendor_transfer.paystack_transfer_code

      # Check rider transfer
      rider_transfer = @order.transfers.find_by(recipient: @rider)
      assert_equal 10.0, rider_transfer.amount
      assert_equal 2.0, rider_transfer.service_fee
      assert_equal Transfer::PROCESSING, rider_transfer.status
      assert_not_nil rider_transfer.paystack_transfer_code
    end
  end

  test 'should not mark order as delivered if not in confirmed state' do
    # Delete existing transfers
    Transfer.delete_all

    # Change order status to pending
    @order.update!(status: Order::PENDING)

    # Try to mark as delivered
    assert_not @order.mark_as_delivered!

    # Check order status (should still be pending)
    assert_equal Order::PENDING, @order.status

    # Check transfers (should be none)
    assert_equal 0, @order.transfers.count
  end

  test 'should not allow another customer to mark order as delivered' do
    # Create another customer
    another_customer = Customer.create!(
      name: 'Another Customer',
      phone: '**********',
      username: 'anothercustomer3',
      email: '<EMAIL>',
      password: 'password',
      password_confirmation: 'password',
      address: 'Another Address'
    )

    # Change order customer
    original_customer = @order.customer
    @order.update!(customer: another_customer)

    # Create a controller instance
    controller = OrderDeliveriesController.new

    # Set the current customer
    def controller.current_customer
      @customer
    end
    controller.instance_variable_set(:@customer, original_customer)

    # Set the order
    controller.instance_variable_set(:@order, @order)

    # Check if the customer is authorized
    assert_not @order.customer_id == controller.current_customer.id
  end
end
