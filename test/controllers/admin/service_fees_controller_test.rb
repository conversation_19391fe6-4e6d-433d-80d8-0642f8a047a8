require 'test_helper'

class Admin::ServiceFeesControllerTest < ActionDispatch::IntegrationTest
  setup do
    @admin = admins(:one)
    @vendor = vendors(:one)
    @rider = riders(:one)
    @order = orders(:one)

    # Sign in as admin
    sign_in_as(@admin)

    # Create some successful transfers
    Transfer.create!(
      recipient: @vendor,
      order: @order,
      amount: 100.0,
      service_fee: 5.0,
      status: Transfer::SUCCESS
    )

    Transfer.create!(
      recipient: @rider,
      order: @order,
      amount: 20.0,
      service_fee: 2.0,
      status: Transfer::SUCCESS
    )

    # Create a pending transfer (should not be counted)
    Transfer.create!(
      recipient: @vendor,
      order: @order,
      amount: 50.0,
      service_fee: 2.5,
      status: Transfer::PENDING
    )
  end

  test 'should get index' do
    get admin_service_fees_url
    assert_response :success

    json_response = JSON.parse(response.body)
    assert_equal 7.0, json_response['total_service_fees']
    assert_equal 5.0, json_response['vendor_service_fees']
    assert_equal 2.0, json_response['rider_service_fees']
    assert_includes json_response, 'monthly_breakdown'
  end

  test 'should get vendors' do
    get vendors_admin_service_fees_url
    assert_response :success

    json_response = JSON.parse(response.body)
    assert_equal 1, json_response.length
    assert_equal @vendor.id, json_response[0]['id']
    assert_equal @vendor.name, json_response[0]['name']
    assert_equal '5.0', json_response[0]['total_fees']
  end

  test 'should get riders' do
    get riders_admin_service_fees_url
    assert_response :success

    json_response = JSON.parse(response.body)
    assert_equal 1, json_response.length
    assert_equal @rider.id, json_response[0]['id']
    assert_equal @rider.name, json_response[0]['name']
    assert_equal '2.0', json_response[0]['total_fees']
  end

  test 'should not allow non-admin access' do
    # Sign out admin
    sign_out_admin

    # Try to access service fees
    get admin_service_fees_url
    assert_response :unauthorized
  end

  # Using the sign_in_as and sign_out methods from test_helper.rb
  def sign_out_admin
    sign_out(:admin)
  end
end
