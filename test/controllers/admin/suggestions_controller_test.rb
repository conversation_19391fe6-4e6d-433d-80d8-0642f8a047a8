require 'test_helper'

class Admin::SuggestionsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @suggestion = suggestions(:one)
    @admin = admins(:one)
    sign_in_as(@admin)
  end

  test 'should get index' do
    get admin_suggestions_url, headers: { Authorization: @token }
    assert_response :success
  end

  test 'should show suggestion' do
    get admin_suggestion_url(@suggestion), headers: { Authorization: @token }
    assert_response :success
  end

  test 'should update suggestion status' do
    patch admin_suggestion_url(@suggestion), params: {
      suggestion: { status: 'under_review' }
    }, headers: { Authorization: @token }

    assert_response :success
    @suggestion.reload
    assert_equal 'under_review', @suggestion.status
  end

  test 'should get statistics' do
    get statistics_admin_suggestions_url, headers: { Authorization: @token }
    assert_response :success

    json_response = JSON.parse(response.body)
    assert_not_nil json_response['total_suggestions']
    assert_not_nil json_response['suggestions_by_status']
    assert_not_nil json_response['suggestions_by_category']
    assert_not_nil json_response['suggestions_by_rating']
  end
end
