require 'test_helper'

class Admin::ComplaintsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @complaint = complaints(:one)
    @admin = admins(:one)
    sign_in_as(@admin)
  end

  test 'should get index' do
    get admin_complaints_url, headers: { Authorization: @token }
    assert_response :success
  end

  test 'should show complaint' do
    get admin_complaint_url(@complaint), headers: { Authorization: @token }
    assert_response :success
  end

  test 'should update complaint status' do
    patch admin_complaint_url(@complaint), params: {
      complaint: { status: 'under_review' }
    }, headers: { Authorization: @token }

    assert_response :success
    @complaint.reload
    assert_equal 'under_review', @complaint.status
  end

  test 'should get statistics' do
    get statistics_admin_complaints_url, headers: { Authorization: @token }
    assert_response :success

    json_response = JSON.parse(response.body)
    assert_not_nil json_response['total_complaints']
    assert_not_nil json_response['complaints_by_status']
    assert_not_nil json_response['complaints_by_category']
  end
end
