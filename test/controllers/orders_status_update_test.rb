require 'test_helper'

class OrdersStatusUpdateTest < ActionDispatch::IntegrationTest
  setup do
    @order = orders(:one)
    @customer = customers(:one)
    @vendor = vendors(:one)
    @rider = riders(:one)

    # Make sure vendor has required fields
    unless @vendor.operation_time.present? && @vendor.closing_time.present?
      @vendor.update!(
        operation_time: '08:00:00',
        closing_time: '20:00:00'
      )
    end

    # Set up order status as delivered (rider has already marked it as delivered)
    @order.update!(
      status: Order::DELIVERED,
      customer: @customer,
      rider: @rider
    )

    # Sign in as customer
    sign_in_as(@customer)
  end

  test 'should update order status to received and initiate transfers' do
    # Delete existing transfers
    Transfer.delete_all

    # Create a mock OrderTransferService that always succeeds
    original_new = OrderTransferService.method(:new)
    OrderTransferService.define_singleton_method(:new) do |_order|
      mock_service = Minitest::Mock.new
      mock_service.expect :initiate_transfers, true
      mock_service
    end

    begin
      # Update order status to received
      patch "/customers/#{@customer.id}/orders/#{@order.id}", params: { order: { status: Order::RECEIVED } }

      # Check response
      assert_response :success

      # Reload order and check status
      @order.reload
      assert_equal Order::RECEIVED, @order.status
    ensure
      # Restore original method
      OrderTransferService.define_singleton_method(:new, original_new)
    end
  end

  test 'should update order status to not_received without initiating transfers' do
    # Delete existing transfers
    Transfer.delete_all

    # Update order status to not_received
    patch "/customers/#{@customer.id}/orders/#{@order.id}", params: { order: { status: Order::NOT_RECEIVED } }

    # Check response
    assert_response :success

    # Reload order and check status
    @order.reload
    assert_equal Order::NOT_RECEIVED, @order.status

    # Check transfers (should be none)
    assert_equal 0, @order.transfers.count
  end
end
