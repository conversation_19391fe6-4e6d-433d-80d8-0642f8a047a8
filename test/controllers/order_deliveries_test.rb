require 'test_helper'
require 'minitest/mock'

class OrderDeliveriesTest < ActiveSupport::TestCase
  setup do
    @order = orders(:one)
    @customer = customers(:one)
    @vendor = vendors(:one)
    @rider = riders(:one)

    # Make sure vendor has required fields
    unless @vendor.operation_time.present? && @vendor.closing_time.present?
      @vendor.update!(
        operation_time: '08:00:00',
        closing_time: '20:00:00'
      )
    end

    # Set up order status as delivered (rider has already marked it as delivered)
    @order.update!(
      status: Order::DELIVERED,
      customer: @customer
    )

    # Set up payment details for vendor and rider
    @vendor.update!(
      account_name: 'Vendor Account',
      mobile_money_number: '**********',
      mobile_money_provider: 'MTN',
      paystack_recipient_code: 'RCP_123456789'
    )

    @rider.update!(
      account_name: 'Rider Account',
      mobile_money_number: '**********',
      mobile_money_provider: 'Vodafone',
      paystack_recipient_code: 'RCP_987654321'
    )
  end

  test 'should mark order as delivered and initiate transfers' do
    # Delete existing transfers
    Transfer.delete_all

    # Create a mock service
    mock_service = Object.new
    def mock_service.initialize_transfer(_amount, _recipient_code, _reason)
      {
        'status' => true,
        'data' => {
          'transfer_code' => "TRF_#{Time.now.to_i}_#{rand(1000)}",
          'reference' => "REF_#{Time.now.to_i}_#{rand(1000)}"
        }
      }
    end

    # Stub the PaystackService.new method
    PaystackService.stub :new, mock_service do
      # Mark order as delivered with customer confirmation
      assert @order.mark_as_delivered!(customer_confirmed: true)

      # Check order status
      assert_equal Order::RECEIVED, @order.status

      # Check transfers
      assert_equal 2, @order.transfers.count

      # Check vendor transfer
      vendor_transfer = @order.transfers.find_by(recipient: @vendor)
      assert_equal 400.0, vendor_transfer.amount
      assert_equal 20.0, vendor_transfer.service_fee
      assert_equal Transfer::PROCESSING, vendor_transfer.status
      assert_not_nil vendor_transfer.paystack_transfer_code

      # Check rider transfer
      rider_transfer = @order.transfers.find_by(recipient: @rider)
      assert_equal 10.0, rider_transfer.amount
      assert_equal 2.0, rider_transfer.service_fee
      assert_equal Transfer::PROCESSING, rider_transfer.status
      assert_not_nil rider_transfer.paystack_transfer_code
    end
  end

  test 'should not initiate transfers if not customer confirmed' do
    # Delete existing transfers
    Transfer.delete_all

    # Try to mark as delivered without customer confirmation
    assert @order.mark_as_delivered!

    # Check order status (should be delivered)
    assert_equal Order::DELIVERED, @order.status

    # Check transfers (should be none since customer didn't confirm)
    assert_equal 0, @order.transfers.count
  end
end
