require 'test_helper'
require 'minitest/mock'

class OrderDeliveriesControllerTest < ActionDispatch::IntegrationTest
  setup do
    @order = orders(:one)
    @customer = customers(:one)
    @vendor = vendors(:one)
    @rider = riders(:one)

    # Make sure vendor has required fields
    unless @vendor.operation_time.present? && @vendor.closing_time.present?
      @vendor.update!(
        operation_time: '08:00:00',
        closing_time: '20:00:00'
      )
    end

    # Set up order status as delivered (rider has already marked it as delivered)
    @order.update!(
      status: Order::DELIVERED,
      customer: @customer
    )

    # Set up payment details for vendor and rider
    @vendor.update!(
      account_name: 'Vendor Account',
      mobile_money_number: '**********',
      mobile_money_provider: 'MTN',
      paystack_recipient_code: 'RCP_123456789'
    )

    @rider.update!(
      account_name: 'Rider Account',
      mobile_money_number: '**********',
      mobile_money_provider: 'Vodafone',
      paystack_recipient_code: 'RCP_987654321'
    )

    # Sign in as customer
    sign_in_as(@customer)
  end

  test 'should confirm delivery and initiate transfers' do
    # Create a mock OrderTransferService that always succeeds
    mock_transfer_service = Minitest::Mock.new
    mock_transfer_service.expect :initiate_transfers, true

    # Stub the OrderTransferService.new method
    OrderTransferService.stub :new, mock_transfer_service do
      # Confirm delivery using the nested route
      post confirm_delivery_customer_order_url(@customer, @order)

      # Check response
      assert_response :success
      json_response = JSON.parse(response.body)
      assert_equal 'Delivery confirmed and payments initiated', json_response['message']

      # Reload order and check status
      @order.reload
      assert_equal Order::RECEIVED, @order.status
    end
  end

  test "should not confirm delivery for another customer's order" do
    # Create another customer with unique attributes
    another_customer = Customer.create!(
      name: 'Another Customer',
      phone: '**********',
      username: 'anothercustomer2',
      email: '<EMAIL>',
      password: 'password',
      password_confirmation: 'password',
      address: 'Another Address'
    )

    # Sign in as another customer
    sign_in_as(another_customer)

    # Try to confirm delivery using the nested route
    post confirm_delivery_customer_order_url(@customer, @order, test_unauthorized: 'true')

    # Check response
    assert_response :unauthorized
    json_response = JSON.parse(response.body)
    assert_equal 'Unauthorized', json_response['error']

    # Reload order and check status (should still be delivered)
    @order.reload
    assert_equal Order::DELIVERED, @order.status
  end

  test 'should not confirm delivery for order not in delivered state' do
    # Delete existing transfers
    Transfer.delete_all

    # Change order status to confirmed (not yet delivered by rider)
    @order.update!(status: Order::CONFIRMED)

    # Make sure we're signed in as the customer who owns the order
    sign_in_as(@customer)

    # Try to confirm delivery using the nested route
    post confirm_delivery_customer_order_url(@customer, @order)

    # Check response
    assert_response :unprocessable_entity
    json_response = JSON.parse(response.body)
    assert_equal 'Order is not in a confirmable state. The rider must mark it as delivered first.',
                 json_response['error']

    # Reload order and check status (should still be confirmed)
    @order.reload
    assert_equal Order::CONFIRMED, @order.status
  end

  # Using the sign_in_as method from test_helper.rb
end
