require 'test_helper'
require 'minitest/mock'

class OrderTransferTest < ActiveSupport::TestCase
  setup do
    # Use fixtures
    @order = orders(:one)
    @vendor = vendors(:one)
    @rider = riders(:one)

    # Make sure vendor has required fields
    unless @vendor.operation_time.present? && @vendor.closing_time.present?
      @vendor.update!(
        operation_time: '08:00:00',
        closing_time: '20:00:00'
      )
    end

    # Create foods with service fees
    @food1 = Food.create!(
      name: 'Test Food 1',
      price: 100.0, # Will become 110.0 with 10% service fee
      vendor: @vendor
    )

    @food2 = Food.create!(
      name: 'Test Food 2',
      price: 200.0, # Will become 220.0 with 10% service fee
      vendor: @vendor
    )

    # Set up rider prices with service fees
    @rider.update!(
      within_dunkwa_price: 12.0, # 10.0 + 2.0 service fee
      outside_dunkwa_price: 24.0 # 20.0 + 4.0 service fee
    )

    # Set up order with foods
    @order.update!(
      food_ids: [@food1.id.to_s, @food2.id.to_s],
      quantities: [2, 1],
      delivery_address: 'Test Address, Dunkwa',
      delivery_price: 12.0, # Within Dunkwa price
      rider: @rider,
      status: Order::CONFIRMED
    )

    # Set up payment details for vendor and rider
    @vendor.update!(
      account_name: 'Vendor Account',
      mobile_money_number: '**********',
      mobile_money_provider: 'MTN',
      paystack_recipient_code: 'RCP_123456789'
    )

    @rider.update!(
      account_name: 'Rider Account',
      mobile_money_number: '**********',
      mobile_money_provider: 'Vodafone',
      paystack_recipient_code: 'RCP_987654321'
    )
  end

  test 'should calculate original food price' do
    # Original prices: 100.0 for food1, 200.0 for food2
    # Quantities: 2 for food1, 1 for food2
    # Total: (100.0 * 2) + (200.0 * 1) = 400.0
    assert_equal 400.0, @order.original_food_price
  end

  test 'should calculate food service fees' do
    # Service fees: 10.0 for food1, 20.0 for food2 (10% of original prices)
    # Quantities: 2 for food1, 1 for food2
    # Total: (10.0 * 2) + (20.0 * 1) = 40.0
    assert_equal 40.0, @order.food_service_fees
  end

  test 'should calculate original rider price for within Dunkwa' do
    # Order delivery address includes "Dunkwa"
    # Original within Dunkwa price: 10.0
    assert_equal 10.0, @order.original_rider_price
  end

  test 'should calculate original rider price for outside Dunkwa' do
    # Change delivery address to outside Dunkwa
    @order.update!(delivery_address: 'Test Address, Accra')

    # Original outside Dunkwa price: 20.0
    assert_equal 20.0, @order.original_rider_price
  end

  test 'should calculate rider service fee for within Dunkwa' do
    # Order delivery address includes "Dunkwa"
    # Within Dunkwa service fee: 2.0
    assert_equal 2.0, @order.rider_service_fee
  end

  test 'should calculate rider service fee for outside Dunkwa' do
    # Change delivery address to outside Dunkwa
    @order.update!(delivery_address: 'Test Address, Accra')

    # Outside Dunkwa service fee: 4.0
    assert_equal 4.0, @order.rider_service_fee
  end

  test 'should get vendors associated with order' do
    vendors = @order.vendors
    assert_includes vendors, @vendor
    assert_equal 1, vendors.count
  end

  test 'should calculate original vendor price' do
    # Original prices: 100.0 for food1, 200.0 for food2
    # Quantities: 2 for food1, 1 for food2
    # Total for vendor: (100.0 * 2) + (200.0 * 1) = 400.0
    assert_equal 400.0, @order.original_vendor_price(@vendor)
  end

  test 'should calculate vendor service fees' do
    # Service fees: 10.0 for food1, 20.0 for food2 (10% of original prices)
    # Quantities: 2 for food1, 1 for food2
    # Total for vendor: (10.0 * 2) + (20.0 * 1) = 40.0
    assert_equal 40.0, @order.vendor_service_fees(@vendor)
  end

  test 'should create transfers when order is delivered' do
    # Delete existing transfers
    Transfer.delete_all

    # Make sure the order is in CONFIRMED state
    @order.update!(status: Order::CONFIRMED)

    # Mock the PaystackService to avoid actual API calls

    # Create a mock service
    mock_service = Object.new
    # Use a counter to generate unique codes
    @counter = 0

    def mock_service.balance_sufficient?(_required_amount)
      {
        'status' => true,
        'sufficient' => true,
        'current_balance' => 10000.0,
        'message' => 'Balance is sufficient'
      }
    end

    def mock_service.initialize_transfer(_amount, _recipient_code, _reason)
      # Increment counter to generate unique codes
      @counter ||= 0
      @counter += 1

      {
        'status' => true,
        'data' => {
          'transfer_code' => "TRF_#{Time.now.to_i}_#{@counter}",
          'reference' => "REF_#{Time.now.to_i}_#{@counter}"
        }
      }
    end

    # Stub the PaystackService.new method
    PaystackService.stub :new, mock_service do
      # Directly call initiate_transfers instead of mark_as_delivered!
      @order.send(:initiate_transfers)

      # Update order status manually
      @order.update!(status: Order::DELIVERED)

      # Check transfers
      assert_equal 2, @order.transfers.count

      # Check vendor transfer
      vendor_transfer = @order.transfers.find_by(recipient: @vendor)
      assert_equal 400.0, vendor_transfer.amount
      assert_equal 40.0, vendor_transfer.service_fee
      assert_equal Transfer::PROCESSING, vendor_transfer.status
      assert_not_nil vendor_transfer.paystack_transfer_code
      assert_not_nil vendor_transfer.paystack_reference

      # Check rider transfer
      rider_transfer = @order.transfers.find_by(recipient: @rider)
      assert_equal 10.0, rider_transfer.amount
      assert_equal 2.0, rider_transfer.service_fee
      assert_equal Transfer::PROCESSING, rider_transfer.status
      assert_not_nil rider_transfer.paystack_transfer_code
      assert_not_nil rider_transfer.paystack_reference
    end
  end

  test 'should not mark order as delivered if not in confirmed state' do
    # Delete existing transfers
    Transfer.delete_all

    # Change order status to pending
    @order.update!(status: Order::PENDING)

    # Try to mark as delivered
    assert_not @order.mark_as_delivered!

    # Check order status (should still be pending)
    assert_equal Order::PENDING, @order.status

    # Check transfers (should be none)
    assert_equal 0, @order.transfers.count
  end
end
