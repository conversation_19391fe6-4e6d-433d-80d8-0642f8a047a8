require 'test_helper'

class TransferTest < ActiveSupport::TestCase
  setup do
    @order = orders(:one)
    @vendor = vendors(:one)
    @rider = riders(:one)
  end

  test 'should create a valid transfer' do
    transfer = Transfer.new(
      recipient: @vendor,
      order: @order,
      amount: 100.0,
      service_fee: 5.0,
      status: Transfer::PENDING
    )
    assert transfer.valid?
  end

  test 'should not create a transfer without an order' do
    transfer = Transfer.new(
      recipient: @vendor,
      amount: 100.0,
      service_fee: 5.0,
      status: Transfer::PENDING
    )
    assert_not transfer.valid?
    assert_includes transfer.errors.full_messages, 'Order must exist'
  end

  test 'should not create a transfer without a recipient' do
    transfer = Transfer.new(
      order: @order,
      amount: 100.0,
      service_fee: 5.0,
      status: Transfer::PENDING
    )
    assert_not transfer.valid?
    assert_includes transfer.errors.full_messages, 'Recipient must exist'
  end

  test 'should not create a transfer with negative amount' do
    transfer = Transfer.new(
      recipient: @vendor,
      order: @order,
      amount: -10.0,
      service_fee: 5.0,
      status: Transfer::PENDING
    )
    assert_not transfer.valid?
    assert_includes transfer.errors.full_messages, 'Amount must be greater than 0'
  end

  test 'should not create a transfer with negative service fee' do
    transfer = Transfer.new(
      recipient: @vendor,
      order: @order,
      amount: 100.0,
      service_fee: -5.0,
      status: Transfer::PENDING
    )
    assert_not transfer.valid?
    assert_includes transfer.errors.full_messages, 'Service fee must be greater than or equal to 0'
  end

  test 'should calculate total service fees' do
    # Delete existing transfers to start fresh
    Transfer.delete_all

    # Create some successful transfers
    Transfer.create!(
      recipient: @vendor,
      order: @order,
      amount: 100.0,
      service_fee: 5.0,
      status: Transfer::SUCCESS
    )
    Transfer.create!(
      recipient: @rider,
      order: @order,
      amount: 20.0,
      service_fee: 2.0,
      status: Transfer::SUCCESS
    )
    # Create a pending transfer (should not be counted)
    Transfer.create!(
      recipient: @vendor,
      order: @order,
      amount: 50.0,
      service_fee: 2.5,
      status: Transfer::PENDING
    )

    assert_equal 7.0, Transfer.total_service_fees
  end

  test 'should calculate vendor service fees' do
    # Delete existing transfers to start fresh
    Transfer.delete_all

    # Create vendor and rider transfers
    Transfer.create!(
      recipient: @vendor,
      order: @order,
      amount: 100.0,
      service_fee: 5.0,
      status: Transfer::SUCCESS
    )
    Transfer.create!(
      recipient: @rider,
      order: @order,
      amount: 20.0,
      service_fee: 2.0,
      status: Transfer::SUCCESS
    )

    assert_equal 5.0, Transfer.vendor_service_fees
  end

  test 'should calculate rider service fees' do
    # Delete existing transfers to start fresh
    Transfer.delete_all

    # Create vendor and rider transfers
    Transfer.create!(
      recipient: @vendor,
      order: @order,
      amount: 100.0,
      service_fee: 5.0,
      status: Transfer::SUCCESS
    )
    Transfer.create!(
      recipient: @rider,
      order: @order,
      amount: 20.0,
      service_fee: 2.0,
      status: Transfer::SUCCESS
    )

    assert_equal 2.0, Transfer.rider_service_fees
  end
end
