require 'test_helper'

class ComplaintTest < ActiveSupport::TestCase
  test 'should not save complaint without required fields' do
    complaint = Complaint.new
    assert_not complaint.save, 'Saved the complaint without required fields'
  end

  test 'should save valid complaint' do
    complaint = Complaint.new(
      name: 'Test User',
      email: '<EMAIL>',
      order_number: 'EF-2023-12-123',
      category: Complaint::ORDER,
      complaint: 'This is a test complaint with more than 10 characters'
    )
    assert complaint.save, 'Could not save valid complaint'
  end

  test 'should set default status to pending' do
    complaint = Complaint.new(
      name: 'Test User',
      email: '<EMAIL>',
      order_number: 'EF-2023-12-123',
      category: Complaint::ORDER,
      complaint: 'This is a test complaint with more than 10 characters'
    )
    complaint.save
    assert_equal Complaint::PENDING, complaint.status, 'Default status not set to pending'
  end

  test 'should validate email format' do
    complaint = Complaint.new(
      name: 'Test User',
      email: 'invalid-email',
      order_number: 'EF-2023-12-123',
      category: Complaint::ORDER,
      complaint: 'This is a test complaint with more than 10 characters'
    )
    assert_not complaint.save, 'Saved complaint with invalid email format'
  end

  test 'should validate complaint length' do
    complaint = Complaint.new(
      name: 'Test User',
      email: '<EMAIL>',
      order_number: 'EF-2023-12-123',
      category: Complaint::ORDER,
      complaint: 'Too short'
    )
    assert_not complaint.save, 'Saved complaint with too short complaint text'
  end
end
