require 'test_helper'

class RiderTest < ActiveSupport::TestCase
  test 'should add 2 Ghana cedis to within_dunkwa_price before saving' do
    # Use a unique timestamp to avoid conflicts
    timestamp = Time.now.to_i

    original_price = 10.0

    rider = Rider.new(
      name: 'Test Rider',
      phone: '1234567890',
      username: "testrider_#{timestamp}",
      email: "test_#{timestamp}@rider.com",
      password: 'password',
      password_confirmation: 'password',
      address: 'Test Address',
      ghana_card_number: '1234567890',
      within_dunkwa_price: original_price
    )
    rider.save!

    # Price should be increased by 2 Ghana cedis
    assert_equal 12.0, rider.within_dunkwa_price

    # Original price method should return the price without service fee
    assert_equal 10.0, rider.original_within_dunkwa_price

    # Service fee method should return 2 Ghana cedis
    assert_equal 2.0, rider.within_dunkwa_service_fee
  end

  test 'should add 4 Ghana cedis to outside_dunkwa_price before saving' do
    # Use a unique timestamp to avoid conflicts
    timestamp = Time.now.to_i

    original_price = 20.0

    rider = Rider.new(
      name: 'Test Rider 2',
      phone: '1234567891',
      username: "testrider2_#{timestamp}",
      email: "test2_#{timestamp}@rider.com",
      password: 'password',
      password_confirmation: 'password',
      address: 'Test Address 2',
      ghana_card_number: '1234567891',
      within_dunkwa_price: 10.0,
      outside_dunkwa_price: original_price
    )
    rider.save!

    # Price should be increased by 4 Ghana cedis
    assert_equal 24.0, rider.outside_dunkwa_price

    # Original price method should return the price without service fee
    assert_equal 20.0, rider.original_outside_dunkwa_price

    # Service fee method should return 4 Ghana cedis
    assert_equal 4.0, rider.outside_dunkwa_service_fee
  end

  test 'should not add service fees multiple times' do
    # Use a unique timestamp to avoid conflicts
    timestamp = Time.now.to_i

    rider = Rider.new(
      name: 'Test Rider 3',
      phone: '1234567892',
      username: "testrider3_#{timestamp}",
      email: "test3_#{timestamp}@rider.com",
      password: 'password',
      password_confirmation: 'password',
      address: 'Test Address 3',
      ghana_card_number: '1234567892',
      within_dunkwa_price: 10.0,
      outside_dunkwa_price: 20.0
    )
    rider.save!

    # Prices should be increased by service fees after first save
    assert_equal 12.0, rider.within_dunkwa_price
    assert_equal 24.0, rider.outside_dunkwa_price

    # Update the rider without changing the prices
    rider.name = 'Updated Test Rider'
    rider.save!

    # Prices should still be the same
    assert_equal 12.0, rider.within_dunkwa_price
    assert_equal 24.0, rider.outside_dunkwa_price

    # Update the rider with new prices
    # We need to reload the rider to simulate a real update scenario
    rider = Rider.find(rider.id)
    rider.within_dunkwa_price = 15.0
    rider.outside_dunkwa_price = 25.0
    rider.save!

    # Prices should be increased by service fees
    assert_equal 17.0, rider.within_dunkwa_price
    assert_equal 29.0, rider.outside_dunkwa_price
  end
end
