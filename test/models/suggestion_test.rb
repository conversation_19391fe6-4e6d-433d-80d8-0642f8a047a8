require 'test_helper'

class SuggestionTest < ActiveSupport::TestCase
  test 'should not save suggestion without required fields' do
    suggestion = Suggestion.new
    assert_not suggestion.save, 'Saved the suggestion without required fields'
  end

  test 'should save valid suggestion' do
    suggestion = Suggestion.new(
      name: 'Test User',
      email: '<EMAIL>',
      category: Suggestion::MENU,
      suggestion: 'This is a test suggestion with more than 10 characters',
      rating: 4
    )
    assert suggestion.save, 'Could not save valid suggestion'
  end

  test 'should set default status to pending' do
    suggestion = Suggestion.new(
      name: 'Test User',
      email: '<EMAIL>',
      category: Suggestion::MENU,
      suggestion: 'This is a test suggestion with more than 10 characters',
      rating: 4
    )
    suggestion.save
    assert_equal Suggestion::PENDING, suggestion.status, 'Default status not set to pending'
  end

  test 'should validate email format' do
    suggestion = Suggestion.new(
      name: 'Test User',
      email: 'invalid-email',
      category: Suggestion::MENU,
      suggestion: 'This is a test suggestion with more than 10 characters',
      rating: 4
    )
    assert_not suggestion.save, 'Saved suggestion with invalid email format'
  end

  test 'should validate suggestion length' do
    suggestion = Suggestion.new(
      name: 'Test User',
      email: '<EMAIL>',
      category: Suggestion::MENU,
      suggestion: 'Too short',
      rating: 4
    )
    assert_not suggestion.save, 'Saved suggestion with too short suggestion text'
  end

  test 'should validate rating range' do
    suggestion = Suggestion.new(
      name: 'Test User',
      email: '<EMAIL>',
      category: Suggestion::MENU,
      suggestion: 'This is a test suggestion with more than 10 characters',
      rating: 6
    )
    assert_not suggestion.save, 'Saved suggestion with rating outside valid range'
  end
end
