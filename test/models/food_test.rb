require 'test_helper'

class FoodTest < ActiveSupport::TestCase
  test 'should add 10% service fee to single price before saving' do
    vendor = vendors(:one)
    original_price = 100.0

    food = Food.new(name: 'Test Food', price: original_price, vendor:)
    food.save!

    # Price should be increased by 10%
    assert_equal 110.0, food.price

    # Original price method should return the price without service fee
    assert_equal 100.0, food.original_price

    # Service fee method should return the 10% amount
    assert_equal 10.0, food.service_fee

    # Should be stored as array
    assert_equal [110.0], food.prices_array
    assert_equal false, food.has_multiple_prices?
  end

  test 'should add 10% service fee to multiple prices before saving' do
    vendor = vendors(:one)
    original_prices = [50.0, 75.0, 100.0]

    food = Food.new(name: 'Test Food', prices_array: original_prices, vendor:)
    food.save!

    # Prices should be increased by 10%
    expected_prices = [55.0, 82.5, 110.0]
    assert_equal expected_prices, food.prices_array

    # Original prices method should return the prices without service fee
    assert_equal original_prices, food.original_prices

    # Service fees method should return the 10% amounts
    assert_equal [5.0, 7.5, 10.0], food.service_fees

    # Should detect multiple prices
    assert_equal true, food.has_multiple_prices?
    assert_equal 55.0, food.min_price
    assert_equal 110.0, food.max_price
    assert_equal 55.0, food.price # First price for backward compatibility
  end

  test 'should not add service fee multiple times' do
    vendor = vendors(:one)
    food = Food.new(name: 'Test Food', price: 100.0, vendor:)
    food.save!

    # Price should be 110.0 after first save
    assert_equal 110.0, food.price

    # Update the food without changing the price
    food.name = 'Updated Test Food'
    food.save!

    # Price should still be 110.0
    assert_equal 110.0, food.price

    # Update the food with a new price
    food.price = 200.0
    food.save!

    # Price should be increased by 10%
    assert_equal 220.0, food.price
  end

  test 'should validate prices array' do
    vendor = vendors(:one)

    # Test invalid JSON
    food = Food.new(name: 'Test Food', vendor:, prices: 'invalid json')
    assert_not food.valid?
    assert_includes food.errors[:prices], 'must be valid JSON array'

    # Test non-array
    food = Food.new(name: 'Test Food', vendor:, prices: '{"price": 100}')
    assert_not food.valid?
    assert_includes food.errors[:prices], 'must be an array of positive numbers'

    # Test negative prices
    food = Food.new(name: 'Test Food', vendor:, prices: '[-10, 20]')
    assert_not food.valid?
    assert_includes food.errors[:prices], 'must be an array of positive numbers'

    # Test valid prices
    food = Food.new(name: 'Test Food', vendor:, prices: '[10, 20, 30]')
    assert food.valid?
  end
end
