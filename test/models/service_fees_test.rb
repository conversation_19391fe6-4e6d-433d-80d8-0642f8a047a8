require 'test_helper'

class ServiceFeesTest < ActiveSupport::TestCase
  setup do
    @vendor = vendors(:one)
    @rider = riders(:one)
    @order = orders(:one)

    # Delete existing transfers
    Transfer.delete_all

    # Create some successful transfers
    Transfer.create!(
      recipient: @vendor,
      order: @order,
      amount: 100.0,
      service_fee: 5.0,
      status: Transfer::SUCCESS
    )

    Transfer.create!(
      recipient: @rider,
      order: @order,
      amount: 20.0,
      service_fee: 2.0,
      status: Transfer::SUCCESS
    )

    # Create a pending transfer (should not be counted)
    Transfer.create!(
      recipient: @vendor,
      order: @order,
      amount: 50.0,
      service_fee: 2.5,
      status: Transfer::PENDING
    )
  end

  test 'should calculate total service fees' do
    assert_equal 7.0, Transfer.total_service_fees
  end

  test 'should calculate vendor service fees' do
    assert_equal 5.0, Transfer.vendor_service_fees
  end

  test 'should calculate rider service fees' do
    assert_equal 2.0, Transfer.rider_service_fees
  end
end
