require 'test_helper'

class OrderCustomerConfirmationTest < ActiveSupport::TestCase
  setup do
    @order = orders(:one)
    @customer = customers(:one)
    @vendor = vendors(:one)
    @rider = riders(:one)

    # Make sure vendor has required fields
    unless @vendor.operation_time.present? && @vendor.closing_time.present?
      @vendor.update!(
        operation_time: '08:00:00',
        closing_time: '20:00:00'
      )
    end

    # Set up order status as delivered (rider has already marked it as delivered)
    @order.update!(
      status: Order::DELIVERED,
      customer: @customer
    )
  end

  test 'should initiate transfers when customer confirms delivery' do
    # Delete existing transfers
    Transfer.delete_all

    # Create a mock service
    mock_service = Object.new
    def mock_service.initialize_transfer(_amount, _recipient_code, _reason)
      {
        'status' => true,
        'data' => {
          'transfer_code' => "TRF_#{Time.now.to_i}_#{rand(1000)}",
          'reference' => "REF_#{Time.now.to_i}_#{rand(1000)}"
        }
      }
    end

    # Stub the PaystackService.new method
    PaystackService.stub :new, mock_service do
      # Customer confirms delivery
      assert @order.mark_as_delivered!(customer_confirmed: true)

      # Check order status
      assert_equal Order::RECEIVED, @order.status

      # Check transfers
      assert_equal 2, @order.transfers.count

      # Check vendor transfer
      vendor_transfer = @order.transfers.find_by(recipient: @vendor)
      assert_equal 400.0, vendor_transfer.amount
      assert_equal 20.0, vendor_transfer.service_fee
      assert_equal Transfer::PROCESSING, vendor_transfer.status
      assert_not_nil vendor_transfer.paystack_transfer_code

      # Check rider transfer
      rider_transfer = @order.transfers.find_by(recipient: @rider)
      assert_equal 10.0, rider_transfer.amount
      assert_equal 2.0, rider_transfer.service_fee
      assert_equal Transfer::PROCESSING, rider_transfer.status
      assert_not_nil rider_transfer.paystack_transfer_code
    end
  end
end
