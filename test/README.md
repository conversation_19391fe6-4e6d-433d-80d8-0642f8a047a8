# Testing the Transfer and Service Fee Features

This directory contains tests for the transfer and service fee features of the EaseFood-Backend application.

## Overview

The tests cover the following functionality:

1. **Transfer Model**: Tests for validations, associations, and service fee calculations
2. **Order Model**: Tests for calculating original prices and service fees
3. **Order Delivery**: Tests for confirming delivery and initiating transfers
4. **Service Fees**: Tests for retrieving service fee information for admins
5. **Integration Flow**: Tests for the complete flow from order creation to delivery and transfers

## Running the Tests

### Prerequisites

Make sure your test database is set up and migrated:

```bash
bin/rails db:test:prepare
```

### Running All Tests

To run all the tests:

```bash
bin/rails test
```

### Running Specific Test Files

To run specific test files:

```bash
bin/rails test test/models/transfer_test.rb
bin/rails test test/models/order_transfer_test.rb
bin/rails test test/controllers/order_deliveries_controller_test.rb
bin/rails test test/controllers/admin/service_fees_controller_test.rb
bin/rails test test/integration/order_delivery_transfer_flow_test.rb
```

### Running Specific Tests

To run a specific test:

```bash
bin/rails test test/models/transfer_test.rb:12  # Run the test at line 12
```

## Test Structure

- **Fixtures**: Located in `test/fixtures/` - Contains sample data for testing
- **Model Tests**: Tests for the Transfer model and Order model methods
- **Controller Tests**: Tests for the OrderDeliveriesController and ServiceFeesController
- **Integration Tests**: Tests for the complete order flow

## Mocking External Services

The tests use mocks for the Paystack API to avoid making actual API calls during testing. The `mock_paystack_service` helper method in `test_helper.rb` provides this functionality.

## Authentication in Tests

The tests include helper methods for authentication in `test_helper.rb`:

- `sign_in_as(user)`: Signs in as a customer, vendor, rider, or admin
- `sign_out(user_type)`: Signs out a user of the specified type

## Troubleshooting

If you encounter issues with the tests:

1. Make sure your test database is properly set up
2. Check that all required fixtures are loaded
3. Verify that the Paystack API credentials are properly configured in test mode
4. Ensure that all required gems are installed

## Adding More Tests

When adding new features, please add corresponding tests to maintain code quality and prevent regressions.
