require 'test_helper'
require 'minitest/mock'

class OrderDeliveryTransferFlowTest < ActiveSupport::TestCase
  setup do
    @customer = customers(:one)
    @vendor = vendors(:one)
    @rider = riders(:one)

    # Create foods with service fees
    @food1 = Food.create!(
      name: 'Test Food 1',
      price: 105.0, # Price with 5% service fee
      vendor: @vendor
    )

    @food2 = Food.create!(
      name: 'Test Food 2',
      price: 210.0, # Price with 5% service fee
      vendor: @vendor
    )

    # Set up rider prices with service fees
    @rider.update!(
      within_dunkwa_price: 12.0, # 10.0 + 2.0 service fee
      outside_dunkwa_price: 24.0 # 20.0 + 4.0 service fee
    )

    # Set up payment details for vendor and rider
    @vendor.update!(
      account_name: 'Vendor Account',
      mobile_money_number: '**********',
      mobile_money_provider: 'MTN',
      paystack_recipient_code: 'RCP_123456789',
      operation_time: '08:00:00',
      closing_time: '20:00:00'
    )

    @rider.update!(
      account_name: 'Rider Account',
      mobile_money_number: '**********',
      mobile_money_provider: 'Vodafone',
      paystack_recipient_code: 'RCP_987654321'
    )

    # Delete existing transfers
    Transfer.delete_all
  end

  test 'complete order flow from creation to delivery and transfers' do
    # Create a mock service
    mock_service = Object.new
    def mock_service.initialize_transfer(_amount, _recipient_code, _reason)
      {
        'status' => true,
        'data' => {
          'transfer_code' => "TRF_#{Time.now.to_i}_#{rand(1000)}",
          'reference' => "REF_#{Time.now.to_i}_#{rand(1000)}"
        }
      }
    end

    # Step 1: Create an order
    order = Order.create!(
      customer: @customer,
      rider: @rider,
      food_ids: [@food1.id.to_s, @food2.id.to_s],
      quantities: [2, 1],
      delivery_address: 'Test Address, Dunkwa',
      status: Order::PENDING,
      total_price: 420.0 # Total price with service fees
    )

    # Step 2: Create a payment for the order
    Payment.create!(
      order:,
      amount: 420.0, # Total price with service fees
      reference: 'REF_PAYMENT_123456789',
      status: 'success'
    )

    # Step 3: Update order status to confirmed
    order.update!(status: Order::CONFIRMED)
    assert_equal Order::CONFIRMED, order.status

    # Step 4: Mark as delivered and initiate transfers
    PaystackService.stub :new, mock_service do
      # Mark order as delivered
      assert order.mark_as_delivered!
    end

    # Step 5: Verify order status and transfers
    order.reload
    assert_equal Order::DELIVERED, order.status
    assert_equal 2, order.transfers.count

    # Verify vendor transfer
    vendor_transfer = order.transfers.find_by(recipient: @vendor)
    assert_equal 400.0, vendor_transfer.amount
    assert_equal 20.0, vendor_transfer.service_fee
    assert_equal Transfer::PROCESSING, vendor_transfer.status

    # Verify rider transfer
    rider_transfer = order.transfers.find_by(recipient: @rider)
    assert_equal 10.0, rider_transfer.amount
    assert_equal 2.0, rider_transfer.service_fee
    assert_equal Transfer::PROCESSING, rider_transfer.status

    # Step 6: Update transfer statuses to success
    vendor_transfer.update!(status: Transfer::SUCCESS)
    rider_transfer.update!(status: Transfer::SUCCESS)

    # Step 7: Verify service fee calculations
    assert_equal 22.0, Transfer.total_service_fees # 20.0 from vendor + 2.0 from rider
    assert_equal 20.0, Transfer.vendor_service_fees
    assert_equal 2.0, Transfer.rider_service_fees
  end
end
