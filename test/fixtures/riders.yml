# test/fixtures/riders.yml

one:
  id: 1
  name: Test Rider
  phone: "**********"
  username: testrider
  email: <EMAIL>
  encrypted_password: <%= Rider.new.send(:password_digest, 'password') %>
  address: "Rider Address"
  ghana_card_number: "**********"
  within_dunkwa_price: 12.0
  outside_dunkwa_price: 24.0
  role: rider
  account_name: "Rider Account"
  mobile_money_number: "**********"
  mobile_money_provider: "Vodafone"
  paystack_recipient_code: "RCP_987654321"

two:
  id: 2
  name: Another Rider
  phone: "**********"
  username: anotherrider
  email: <EMAIL>
  encrypted_password: <%= Rider.new.send(:password_digest, 'password') %>
  address: "Another Rider Address"
  ghana_card_number: "**********"
  within_dunkwa_price: 15.0
  outside_dunkwa_price: 30.0
  role: rider
