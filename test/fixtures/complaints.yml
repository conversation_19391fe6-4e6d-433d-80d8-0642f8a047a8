# test/fixtures/complaints.yml

one:
  id: 1
  customer_id: 1
  name: Test Customer
  email: <EMAIL>
  order_number: EF-2023-12-123
  category: order
  complaint: This is a test complaint about my order. The food was cold when it arrived.
  status: pending
  created_at: <%= 2.days.ago.iso8601 %>
  updated_at: <%= 2.days.ago.iso8601 %>

two:
  id: 2
  customer_id: 2
  name: Another Customer
  email: <EMAIL>
  order_number: EF-2023-12-124
  category: delivery
  complaint: This is a test complaint about delivery. The rider was very late.
  status: under_review
  created_at: <%= 1.day.ago.iso8601 %>
  updated_at: <%= 1.day.ago.iso8601 %>

three:
  id: 3
  customer_id: 1
  name: Test Customer
  email: <EMAIL>
  order_number: EF-2023-12-125
  category: food
  complaint: This is a test complaint about food quality. The food was not as described.
  status: resolved
  created_at: <%= 3.days.ago.iso8601 %>
  updated_at: <%= 1.day.ago.iso8601 %>
