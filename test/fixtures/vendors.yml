# test/fixtures/vendors.yml
one:
  id: 1
  name: Test Vendor
  address: Test Address
  phone: "**********"
  operation_time: "08:00:00"
  closing_time: "20:00:00"
  vendor_description: Test Description
  username: testvendor
  role: vendor
  email: <EMAIL>
  encrypted_password: <%= Vendor.new.send(:password_digest, 'password') %>
  digital_address: "TEST-123"
  ghana_card_number: "**********"
  account_name: "Vendor Account"
  mobile_money_number: "**********"
  mobile_money_provider: "MTN"
  paystack_recipient_code: "RCP_123456789"

two:
  id: 2
  name: Another Vendor
  address: Another Address
  phone: "**********"
  operation_time: "09:00:00"
  closing_time: "21:00:00"
  vendor_description: Another Description
  username: anothervendor
  role: vendor
  email: <EMAIL>
  encrypted_password: <%= Vendor.new.send(:password_digest, 'password') %>
  digital_address: "TEST-456"
  ghana_card_number: "**********"
