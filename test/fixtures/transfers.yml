# test/fixtures/transfers.yml

one:
  recipient_type: Vendor
  recipient_id: 1
  order_id: 1
  amount: 100.0
  service_fee: 5.0
  status: success
  paystack_transfer_code: TRF_123456789
  paystack_reference: REF_123456789

two:
  recipient_type: Rider
  recipient_id: 1
  order_id: 1
  amount: 20.0
  service_fee: 2.0
  status: success
  paystack_transfer_code: TRF_987654321
  paystack_reference: REF_987654321

pending:
  recipient_type: Vendor
  recipient_id: 1
  order_id: 2
  amount: 50.0
  service_fee: 2.5
  status: pending
  paystack_transfer_code: TRF_PENDING
  paystack_reference: REF_PENDING
