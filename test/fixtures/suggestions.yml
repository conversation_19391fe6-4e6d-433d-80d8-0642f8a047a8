# test/fixtures/suggestions.yml

one:
  id: 1
  customer_id: 1
  name: Test Customer
  email: <EMAIL>
  category: menu
  suggestion: This is a test suggestion about the menu. Please add more vegetarian options.
  rating: 4
  status: pending
  created_at: <%= 2.days.ago.iso8601 %>
  updated_at: <%= 2.days.ago.iso8601 %>

two:
  id: 2
  customer_id: 2
  name: Another Customer
  email: <EMAIL>
  category: delivery
  suggestion: This is a test suggestion about delivery. Please expand to Obuasi area.
  rating: 5
  status: under_review
  created_at: <%= 1.day.ago.iso8601 %>
  updated_at: <%= 1.day.ago.iso8601 %>

three:
  id: 3
  customer_id: 1
  name: Test Customer
  email: <EMAIL>
  category: app
  suggestion: This is a test suggestion about the app. Please add a filter for dietary restrictions.
  rating: 3
  status: implemented
  created_at: <%= 3.days.ago.iso8601 %>
  updated_at: <%= 1.day.ago.iso8601 %>
