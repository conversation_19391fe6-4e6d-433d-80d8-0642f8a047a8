name: Deploy <PERSON>aseFood Backend to Namecheap VPS

on:
  push:
    branches: [ development ]
  pull_request:
    branches: [ development ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    # Only deploy on pushes, not PRs
    if: github.event_name == 'push'

    services:
      # Spin up a Postgres service for CI, so tests run against Postgres.
      postgres:
        image: postgres:14
        env:
          POSTGRES_USER: postgres
          POSTGRES_DB: easefood_test
          POSTGRES_PASSWORD: postgres
        ports:
          - 5432:5432
        options: >-
          --health-cmd "pg_isready -U postgres"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Ruby
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: '3.3.0'
        bundler-cache: true

    # - name: Run tests (Postgres)
    #   env:
    #     RAILS_ENV: test
    #     DATABASE_URL: postgres://postgres:postgres@127.0.0.1:5432/ease_food_backend_test
    #   run: |
    #       # Prepare test database
    #       bin/rails db:test:prepare
    #       # Run tests using Rails' built-in test framework
    #       bin/rails test

    - name: Create deployment archive
      run: |
        # Create a clean staging directory
        mkdir -p staging
        
        # Copy essential Rails application files
        cp -r app staging/ 2>/dev/null || true
        cp -r config staging/ 2>/dev/null || true
        cp -r db staging/ 2>/dev/null || true
        cp -r lib staging/ 2>/dev/null || true
        cp -r public staging/ 2>/dev/null || true
        cp -r bin staging/ 2>/dev/null || true
        
        # Copy essential files
        cp Gemfile staging/ 2>/dev/null || true
        cp Gemfile.lock staging/ 2>/dev/null || true
        cp Rakefile staging/ 2>/dev/null || true
        cp config.ru staging/ 2>/dev/null || true
        cp README.md staging/ 2>/dev/null || true
        
        # List what we're archiving (for debugging)
        echo "Files to be archived:"
        ls -la staging/
        ls -la staging/config/
        
        # Create archive from staging directory
        tar -czf easefood-backend.tar.gz -C staging .
        
        # Verify archive was created
        ls -lh easefood-backend.tar.gz

    - name: Deploy to VPS
      uses: appleboy/scp-action@v0.1.7
      with:
        host: ${{ secrets.VPS_HOST }}
        username: ${{ secrets.VPS_USERNAME }}
        key: ${{ secrets.VPS_SSH_KEY }}
        port: ${{ secrets.VPS_PORT }}
        source: "easefood-backend.tar.gz"
        target: "/tmp/"

    - name: Deploy and setup on VPS
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.VPS_HOST }}
        username: ${{ secrets.VPS_USERNAME }}
        key: ${{ secrets.VPS_SSH_KEY }}
        port: ${{ secrets.VPS_PORT }}
        script: |
          set -e  # Exit on any error

          # make sure Rails sees the right environment.
          export RAILS_ENV=production
          # Set master key
          export RAILS_MASTER_KEY="${{ secrets.RAILS_MASTER_KEY }}"

          # Set database password.
          export EASE_FOOD_BACKEND_DATABASE_PASSWORD="${{ secrets.EASE_FOOD_BACKEND_DATABASE_PASSWORD }}"

          # Set deployment directory
          DEPLOY_DIR="/var/www/EaseFood-Backend"

          # Initialize rbenv properly
          export PATH="$HOME/.rbenv/bin:$PATH"
          eval "$(rbenv init -)" 2>/dev/null || true

          echo "Starting deployment..."
          
          # Create backup of current deployment
          if [ -d "$DEPLOY_DIR" ]; then
            echo "Creating backup..."
            sudo cp -r $DEPLOY_DIR ${DEPLOY_DIR}_backup_$(date +%Y%m%d_%H%M%S)
          fi
          
          # Create deployment directory
          sudo mkdir -p $DEPLOY_DIR
          
          # Extract application
          echo "Extracting application..."
          cd /tmp
          sudo tar -xzf easefood-backend.tar.gz -C $DEPLOY_DIR/
          
          # Change to app directory
          cd $DEPLOY_DIR
          
          # Set proper ownership
          sudo chown -R captain:captain $DEPLOY_DIR

          # Clean any existing bundle config and vendor directory
          rm -rf .bundle vendor/bundle

          # Configure bundler for production (replaces deprecated --deployment --without flags)
          bundle config set --local deployment false
          bundle config set --local without 'development test'
          bundle config set --local path 'vendor/bundle'
          
          # Install/update gems
          echo "Installing gems..."
          bundle install
          
          # Create necessary directories
          mkdir -p tmp/pids tmp/cache tmp/sockets log storage

          # Create the start script for systemd service
          sudo tee $DEPLOY_DIR/bin/start-puma.sh > /dev/null <<'SCRIPT'
          #!/bin/bash
          cd /var/www/EaseFood-Backend
          export RBENV_ROOT=/home/<USER>/.rbenv
          export PATH="$RBENV_ROOT/bin:$RBENV_ROOT/shims:$PATH"

          # Load environment variables if .env exists
          if [ -f .env ]; then
              set -a
              source .env
              set +a
          fi

          exec bundle exec puma -C config/puma.rb
          SCRIPT
          
          # Make start script executable
          sudo chmod +x $DEPLOY_DIR/bin/start-puma.sh
          
          # Setup minimal environment file (Rails credentials handle secrets)
          sudo tee /var/www/EaseFood-Backend/.env > /dev/null <<EOF
          RAILS_ENV=production
          RAILS_MASTER_KEY=${{ secrets.RAILS_MASTER_KEY }}
          EASE_FOOD_BACKEND_DATABASE_PASSWORD=${{ secrets.EASE_FOOD_BACKEND_DATABASE_PASSWORD }}
          EOF
          
          # Precompile assets (check if assets exist first)
          if [ -d "app/assets" ] || [ -f "config/application.rb" ] && grep -q "sprockets" Gemfile*; then
            echo "Precompiling assets..."
            RAILS_ENV=production bundle exec rails assets:precompile || echo "Asset precompilation failed, continuing..."
          else
            echo "No assets to precompile (API-only app)"
          fi

          # Instead of running migrations (which might be broken), load schema.rb
          # **Recommendation:** Once your production system is live,
          # switch this to `bundle exec rails db:migrate`, so you only run new migrations.
          # RAILS_ENV=production bundle exec rails db:schema:load
          # DISABLE_DATABASE_ENVIRONMENT_CHECK=1 RAILS_ENV=production bundle exec rails db:schema:load
          RAILS_ENV=production bundle exec rails db:migrate
          
          # Set proper permissions.
          sudo chown -R captain:captain $DEPLOY_DIR
          sudo chmod -R 755 $DEPLOY_DIR

          # Fix permissions for runtime directories.
          sudo mkdir -p tmp/{sockets,pids,cache}
          sudo chmod -R 755 tmp log storage
          
          # Restart application services.
          echo "Restarting services..."
          sudo systemctl restart easefood-backend || {
            echo "Warning: easefood-backend service not configured yet"
          }
          sudo systemctl reload nginx || sudo systemctl restart nginx
          
          # Clean up
          rm -f /tmp/easefood-backend.tar.gz
          
          echo "✅ EaseFood Backend deployment completed successfully!"

    - name: Health check
      run: |
        echo "Waiting 15 seconds for service to boot up..."
        sleep 15
        for i in {1..12}; do
            echo "Health check attempt $i/12..."
            if curl -fsSL --max-time 10 "${{ secrets.API_URL }}/up" --write-out "HTTP %{http_code}\n"; then
              echo "✅ Health check passed."
              exit 0
            fi
            echo "⏳ Service not ready yet, waiting 10 seconds..."
            sleep 10
          done
          echo "❌ Health check failed after 1 minute."
          echo "Checking service status..."
          echo "Please manually check: sudo systemctl status easefood-backend"
          exit 1