class OrdersController < ApplicationController
  require 'httparty'
  def index
    if params[:rider_id]
      @orders = Order.where(rider_id: params[:rider_id]).includes(:customer, :recipient)
    elsif params[:vendor_id]
      vendor_food_ids = Food.where(vendor_id: params[:vendor_id]).pluck(:id).map(&:to_s) # Convert IDs to strings

      @orders = if vendor_food_ids.empty?
                  [] # No food items found for this vendor.
                else
                  # Use ARRAY[...] syntax for text array comparison.
                  Order.where('food_ids && ARRAY[?]::text[]', vendor_food_ids)
                    .includes(:customer, :recipient)
                end

      # Get only the foods that belong to this vendor.
      vendor_foods = Food.where(id: vendor_food_ids)

      # For each order, log the food IDs and the filtered food IDs.
      @orders.each do |order|
        order_foods = vendor_foods.select { |food| order.food_ids.include?(food.id.to_s) }
        Rails.logger.info("Order #{order.id} filtered food IDs: #{order_foods.map(&:id)}")
      end

      # Render orders with only the foods that belong to this vendor
      render json: @orders.map { |order|
        order_foods = vendor_foods.select { |food| order.food_ids.include?(food.id.to_s) }

        # Get the current vendor details
        current_vendor = Vendor.find(params[:vendor_id])

        order.as_json(
          include: {
            customer: {
              only: %i[id name email phone]
            },
            rider: {
              only: %i[id name phone]
            },
            recipient: {
              only: %i[id recipient_name recipient_phone recipient_address]
            }
          }
        ).merge(
          # Use recipient details if available, otherwise use customer details
          display_recipient: get_display_recipient(order),
          foods: order_foods.map do |food|
            # Find the index of this food in the order to get the correct price
            food_index = order.food_ids.index(food.id.to_s)
            selected_price = get_selected_food_price(food, order, food_index)

            food.as_json(only: %i[id name description vendor_id]).merge(
              food_image_url: food.food_image_url,
              price: selected_price,
              vendor_name: current_vendor.name
            )
          end,
          vendor: {
            id: current_vendor.id,
            name: current_vendor.name,
            address: current_vendor.address,
            phone: current_vendor.phone,
            email: current_vendor.email
          }
        )
      }
      return
    elsif params[:customer_id]
      @orders = Order.where(customer_id: params[:customer_id]).includes(:rider, :recipient)
      foods = Food.where(id: @orders.flat_map(&:food_ids)).includes(:vendor)

      # Get all vendors for these orders
      vendor_ids = foods.pluck(:vendor_id).uniq
      vendors = Vendor.where(id: vendor_ids)

      render json: @orders.map { |order|
        # Get vendors for this specific order
        order_foods = foods.select { |food| order.food_ids.include?(food.id.to_s) }
        order_vendor_ids = order_foods.pluck(:vendor_id).uniq
        order_vendors = vendors.select { |v| order_vendor_ids.include?(v.id) }

        order.as_json(include: {
                        rider: { only: %i[id name phone] },
                        recipient: {
                          only: %i[id recipient_name recipient_phone recipient_address]
                        }
                      }).merge(
                        # Use recipient details if available, otherwise use customer details
                        display_recipient: get_display_recipient(order),
                        foods: order_foods.map do |food|
                          food.as_json(only: %i[id name description price vendor_id]).merge(
                            food_image_url: food.food_image_url,
                            price: food.price,
                            vendor_name: food.vendor.name
                          )
                        end,
                        vendors: order_vendors.map do |vendor|
                          {
                            id: vendor.id,
                            name: vendor.name,
                            address: vendor.address,
                            phone: vendor.phone,
                            email: vendor.email
                          }
                        end
                      )
      }
      return
    else
      render json: { error: 'Invalid request' }, status: :bad_request
      return
    end

    # Get all foods for these orders with their vendors
    foods = Food.where(id: @orders.flat_map(&:food_ids)).includes(:vendor)

    # Get all vendors for these orders
    vendor_ids = foods.pluck(:vendor_id).uniq
    vendors = Vendor.where(id: vendor_ids)

    # Render orders with their foods and vendors
    render json: @orders.map { |order|
      # Get foods for this specific order
      order_foods = foods.select { |food| order.food_ids.include?(food.id.to_s) }

      # Get vendors for this specific order
      order_vendor_ids = order_foods.pluck(:vendor_id).uniq
      order_vendors = vendors.select { |v| order_vendor_ids.include?(v.id) }

      order.as_json(include: {
                      customer: { only: %i[id name email phone] },
                      recipient: {
                        only: %i[id recipient_name recipient_phone recipient_address]
                      }
                    }).merge(
                      # Use recipient details if available, otherwise use customer details
                      display_recipient: get_display_recipient(order),
                      foods: order_foods.map do |food|
                        food.as_json(only: %i[id name description price vendor_id]).merge(
                          food_image_url: food.food_image_url,
                          price: food.price,
                          vendor_name: food.vendor.name
                        )
                      end,
                      vendors: order_vendors.map do |vendor|
                        {
                          id: vendor.id,
                          name: vendor.name,
                          address: vendor.address,
                          phone: vendor.phone,
                          email: vendor.email
                        }
                      end
                    )
    }
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Orders not found' }, status: :not_found
  end

  def show
    @order = Order.find(params[:id])

    # Get all vendors associated with this order
    vendors = @order.vendors

    # Get all foods for this order
    foods = @order.foods

    # Create a mapping of food to vendor
    food_vendor_map = {}
    foods.each do |food|
      food_vendor_map[food.id.to_s] = {
        vendor_id: food.vendor_id,
        vendor_name: food.vendor.name
      }
    end

    render json: @order.as_json(include: {
                                  customer: {
                                    only: %i[id name email phone]
                                  },
                                  rider: {
                                    only: %i[id name phone]
                                  },
                                  recipient: {
                                    only: %i[id recipient_name recipient_phone recipient_address]
                                  }
                                }).merge(
                                  # Use recipient details if available, otherwise use customer details
                                  display_recipient: get_display_recipient(@order),
                                  foods: foods.map do |food|
                                    food.as_json(only: %i[id name description price vendor_id]).merge(
                                      food_image_url: food.food_image_url,
                                      price: food.price,
                                      vendor_name: food.vendor.name
                                    )
                                  end,
                                  vendors: vendors.map do |vendor|
                                    {
                                      id: vendor.id,
                                      name: vendor.name,
                                      address: vendor.address,
                                      phone: vendor.phone,
                                      email: vendor.email
                                    }
                                  end
                                )
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Order not found' }, status: :not_found
  end

  # GET /orders/:id/transfer_debug
  # Debug endpoint to check transfer status and diagnose issues
  def transfer_debug
    @order = Order.find(params[:id])

    debug_info = {
      order_id: @order.id,
      order_status: @order.status,
      order_created_at: @order.created_at,
      order_updated_at: @order.updated_at,
      transfers: @order.transfers.map do |transfer|
        {
          id: transfer.id,
          recipient_type: transfer.recipient_type,
          recipient_id: transfer.recipient_id,
          recipient_name: transfer.recipient.name,
          amount: transfer.amount,
          service_fee: transfer.service_fee,
          status: transfer.status,
          paystack_transfer_code: transfer.paystack_transfer_code,
          paystack_reference: transfer.paystack_reference,
          created_at: transfer.created_at,
          updated_at: transfer.updated_at
        }
      end,
      transfer_summary: {
        total_transfers: @order.transfers.count,
        pending_transfers: @order.transfers.where(status: Transfer::PENDING).count,
        processing_transfers: @order.transfers.where(status: Transfer::PROCESSING).count,
        successful_transfers: @order.transfers.where(status: Transfer::SUCCESS).count,
        failed_transfers: @order.transfers.where(status: Transfer::FAILED).count
      },
      vendors: @order.vendors.map do |vendor|
        {
          id: vendor.id,
          name: vendor.name,
          account_name: vendor.account_name,
          paystack_recipient_code: vendor.paystack_recipient_code,
          mobile_money_number: vendor.mobile_money_number,
          mobile_money_provider: vendor.mobile_money_provider
        }
      end,
      rider: {
        id: @order.rider.id,
        name: @order.rider.name,
        account_name: @order.rider.account_name,
        paystack_recipient_code: @order.rider.paystack_recipient_code,
        mobile_money_number: @order.rider.mobile_money_number,
        mobile_money_provider: @order.rider.mobile_money_provider
      },
      price_calculations: {
        original_food_price: @order.original_food_price,
        original_rider_price: @order.original_rider_price,
        rider_service_fee: @order.rider_service_fee,
        vendor_prices: @order.vendors.map do |vendor|
          {
            vendor_id: vendor.id,
            vendor_name: vendor.name,
            original_price: @order.original_vendor_price(vendor),
            service_fees: @order.vendor_service_fees(vendor)
          }
        end
      }
    }

    render json: debug_info
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Order not found' }, status: :not_found
  end

  # POST /orders/:id/manual_transfer
  # Manual trigger to initiate transfers for orders that were missed due to Sidekiq not running
  def manual_transfer
    @order = Order.find(params[:id])

    # Check if order is in received status
    unless @order.status == Order::RECEIVED
      render json: { error: 'Order must be in received status to initiate transfers' }, status: :unprocessable_entity
      return
    end

    # Check if transfers already exist
    if @order.transfers.any?
      render json: {
        error: 'Transfers already exist for this order',
        transfers_count: @order.transfers.count,
        transfers: @order.transfers.map { |t| { id: t.id, status: t.status, recipient: t.recipient.name } }
      }, status: :unprocessable_entity
      return
    end

    # Manually trigger the transfer process
    Rails.logger.info("Manually triggering transfers for order #{@order.id}")

    begin
      # Check balance before proceeding
      transfer_service = OrderTransferService.new(@order)

      # Calculate total required amount
      calculator = OrderPriceCalculator.new(@order)
      total_required_amount = 0.0

      # Add vendor amounts
      @order.vendors.each do |vendor|
        vendor_amount = calculator.original_vendor_price(vendor)
        vendor_service_fee = calculator.vendor_service_fees(vendor)
        total_required_amount += vendor_amount + vendor_service_fee
      end

      # Add rider amount
      rider_amount = calculator.original_rider_price
      rider_service_fee = calculator.rider_service_fee
      total_required_amount += rider_amount + rider_service_fee

      # Check balance
      paystack_service = PaystackService.new
      balance_check = paystack_service.balance_sufficient?(total_required_amount)

      unless balance_check['status']
        render json: {
          error: 'Failed to check balance',
          details: balance_check['message']
        }, status: :unprocessable_entity
        return
      end

      unless balance_check['sufficient']
        render json: {
          error: 'Insufficient balance to initiate transfers',
          details: {
            required_amount: total_required_amount,
            current_balance: balance_check['current_balance'],
            message: 'Please ensure sufficient balance is available before retrying'
          }
        }, status: :unprocessable_entity
        return
      end

      # Proceed with transfers
      transfers = transfer_service.initiate_transfers

      render json: {
        message: 'Transfers initiated successfully',
        order_id: @order.id,
        transfers_created: transfers.count,
        balance_info: {
          required_amount: total_required_amount,
          current_balance: balance_check['current_balance']
        },
        transfers: transfers.map do |transfer|
          {
            id: transfer.id,
            recipient_type: transfer.recipient_type,
            recipient_name: transfer.recipient.name,
            amount: transfer.amount,
            service_fee: transfer.service_fee,
            status: transfer.status,
            paystack_transfer_code: transfer.paystack_transfer_code
          }
        end
      }, status: :ok
    rescue StandardError => e
      Rails.logger.error("Manual transfer failed for order #{@order.id}: #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))

      render json: {
        error: 'Failed to initiate transfers',
        details: e.message
      }, status: :unprocessable_entity
    end
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Order not found' }, status: :not_found
  end

  def create
    # Extract food items from parameters
    food_items = extract_food_items_from_params

    if food_items.empty?
      render json: { error: 'No food items provided' }, status: :unprocessable_entity
      return
    end

    # Get the customer and rider
    customer = Customer.find(params[:customer_id])
    rider = Rider.find(params[:order][:rider_id]) if params[:order][:rider_id].present?

    unless rider
      render json: { error: 'Rider is required' }, status: :unprocessable_entity
      return
    end

    # Prepare order parameters
    order_params_hash = {
      rider_id: rider.id,
      delivery_price: params[:order][:delivery_price],
      donation: params[:order][:donation],
      delivery_address: params[:order][:delivery_address],
      recipient_attributes: params[:order][:recipient_attributes]
    }

    # Use the multi-vendor order service
    service = MultiVendorOrderService.new(customer, food_items, order_params_hash)
    result = service.create_orders

    if result[:success]
      render json: {
        message: result[:message],
        orders: result[:orders].map do |order|
          {
            id: order.id,
            total_price: order.total_price,
            vendor_ids: order.vendor_ids,
            food_count: order.food_ids.size,
            rider_id: order.rider_id
          }
        end,
        payment_data: result[:payment_data]
      }, status: :ok
    else
      render json: { errors: result[:errors] }, status: :unprocessable_entity
    end
  rescue ActiveRecord::RecordNotFound => e
    if e.message.include?('Rider')
      render json: { error: 'Rider not found' }, status: :not_found
    else
      render json: { error: 'Customer not found' }, status: :not_found
    end
  rescue StandardError => e
    Rails.logger.error("Order creation error: #{e.message}")
    Rails.logger.error(e.backtrace.join("\n"))
    render json: { error: e.message }, status: :unprocessable_entity
  end

  def update
    ActiveRecord::Base.transaction do
      @order = Order.find(params[:id])

      # Store original food_ids to check if they've been modified
      original_food_ids = @order.food_ids.dup

      # Refactor status update logic into its own method
      update_order_status(@order)

      # Refactor rider and vendor logic into its own method
      update_order_associations(@order)

      # If only updating status (food_ids unchanged), don't modify food_ids
      if params[:order] && params[:order][:status].present? &&
         !params[:order][:food_ids].present? && @order.food_ids != original_food_ids
        @order.food_ids = original_food_ids
      end

      @order.save!

      # If this is a vendor, only return the foods that belong to this vendor
      if params[:vendor_id]
        vendor_food_ids = Food.where(vendor_id: params[:vendor_id]).pluck(:id).map(&:to_s)
        vendor_foods = Food.where(id: vendor_food_ids)

        # Only include foods that are both in the order AND belong to this vendor
        order_foods = vendor_foods.select { |food| @order.food_ids.include?(food.id.to_s) }

        # Get the current vendor details
        current_vendor = Vendor.find(params[:vendor_id])

        render json: @order.as_json(include: {
                                      customer: { only: %i[id name email phone] },
                                      rider: { only: %i[id name phone] },
                                      recipient: {
                                        only: %i[id recipient_name recipient_phone recipient_address]
                                      }
                                    }).merge(
                                      # Use recipient details if available, otherwise use customer details
                                      display_recipient: get_display_recipient(@order),
                                      foods: order_foods.map do |food|
                                        food.as_json(only: %i[id name description price vendor_id]).merge(
                                          food_image_url: food.food_image_url,
                                          vendor_name: current_vendor.name
                                        )
                                      end,
                                      vendor: {
                                        id: current_vendor.id,
                                        name: current_vendor.name,
                                        phone: current_vendor.phone,
                                        email: current_vendor.email
                                      }
                                    ), status: :ok
      else
        render json: @order, status: :ok
      end
    end
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Order not found' }, status: :not_found
  rescue ActiveRecord::RecordInvalid => e
    render json: { errors: e.record.errors.full_messages }, status: :unprocessable_entity
  rescue StandardError => e
    render json: { error: e.message }, status: :unprocessable_entity
  end

  # POST /orders/:id/fix_account_names
  # Fix account names and recreate Paystack recipients for vendors and riders
  def fix_account_names
    @order = Order.find(params[:id])

    results = {
      order_id: @order.id,
      vendors_fixed: [],
      rider_fixed: nil,
      errors: []
    }

    begin
      # Fix vendors
      @order.vendors.each do |vendor|
        vendor_result = {
          id: vendor.id,
          name: vendor.name,
          old_account_name: vendor.account_name,
          new_account_name: nil,
          paystack_recipient_code: vendor.paystack_recipient_code,
          status: 'unchanged'
        }

        # Check if account_name is missing
        if vendor.account_name.blank?
          # Set account_name to vendor name (you can modify this logic)
          new_account_name = vendor.name
          vendor.update_column(:account_name, new_account_name)
          vendor_result[:new_account_name] = new_account_name
          vendor_result[:status] = 'account_name_set'

          # Clear existing Paystack recipient code to force recreation
          if vendor.paystack_recipient_code.present?
            vendor.update_column(:paystack_recipient_code, nil)
            vendor_result[:paystack_recipient_code] = nil
            vendor_result[:status] = 'recipient_cleared'
          end
        end

        # Recreate Paystack recipient if needed
        if vendor.account_name.present? && vendor.mobile_money_number.present? && vendor.mobile_money_provider.present?
          if vendor.paystack_recipient_code.blank?
            service = PaystackService.new
            response = service.create_transfer_recipient(vendor.account_name,
                                                         vendor.mobile_money_number, vendor.mobile_money_provider)

            if response['status']
              vendor.update_column(:paystack_recipient_code, response['data']['recipient_code'])
              vendor_result[:paystack_recipient_code] = response['data']['recipient_code']
              vendor_result[:status] = 'recipient_created'
            else
              vendor_result[:status] = 'recipient_failed'
              vendor_result[:error] = response['message']
              results[:errors] << "Vendor #{vendor.name}: #{response['message']}"
            end
          end
        else
          vendor_result[:status] = 'missing_fields'
          vendor_result[:error] = 'Missing required fields for Paystack recipient'
          results[:errors] << "Vendor #{vendor.name}: Missing required fields"
        end

        results[:vendors_fixed] << vendor_result
      end

      # Fix rider
      if @order.rider
        rider_result = {
          id: @order.rider.id,
          name: @order.rider.name,
          old_account_name: @order.rider.account_name,
          new_account_name: nil,
          paystack_recipient_code: @order.rider.paystack_recipient_code,
          status: 'unchanged'
        }

        # Check if account_name is missing
        if @order.rider.account_name.blank?
          # Set account_name to rider name (you can modify this logic)
          new_account_name = @order.rider.name
          @order.rider.update_column(:account_name, new_account_name)
          rider_result[:new_account_name] = new_account_name
          rider_result[:status] = 'account_name_set'

          # Clear existing Paystack recipient code to force recreation
          if @order.rider.paystack_recipient_code.present?
            @order.rider.update_column(:paystack_recipient_code, nil)
            rider_result[:paystack_recipient_code] = nil
            rider_result[:status] = 'recipient_cleared'
          end
        end

        # Recreate Paystack recipient if needed
        if @order.rider.account_name.present? && @order.rider.mobile_money_number.present? && @order.rider.mobile_money_provider.present?
          if @order.rider.paystack_recipient_code.blank?
            service = PaystackService.new
            response = service.create_transfer_recipient(@order.rider.account_name,
                                                         @order.rider.mobile_money_number, @order.rider.mobile_money_provider)

            if response['status']
              @order.rider.update_column(:paystack_recipient_code, response['data']['recipient_code'])
              rider_result[:paystack_recipient_code] = response['data']['recipient_code']
              rider_result[:status] = 'recipient_created'
            else
              rider_result[:status] = 'recipient_failed'
              rider_result[:error] = response['message']
              results[:errors] << "Rider #{@order.rider.name}: #{response['message']}"
            end
          end
        else
          rider_result[:status] = 'missing_fields'
          rider_result[:error] = 'Missing required fields for Paystack recipient'
          results[:errors] << "Rider #{@order.rider.name}: Missing required fields"
        end

        results[:rider_fixed] = rider_result
      end

      render json: {
        message: 'Account names and Paystack recipients updated',
        results: results
      }, status: :ok
    rescue StandardError => e
      Rails.logger.error("Error fixing account names for order #{@order.id}: #{e.message}")
      render json: {
        error: 'Failed to fix account names',
        details: e.message
      }, status: :unprocessable_entity
    end
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Order not found' }, status: :not_found
  end

  private

  # Extract food items with quantities and price indexes from parameters
  def extract_food_items_from_params
    food_ids = params[:order][:food_ids] || []
    quantities = params[:order][:quantities] || []
    # Handle both 'price_indexes' and 'price_indices' for frontend compatibility
    price_indexes = params[:order][:price_indexes] || params[:order][:price_indices] || []

    food_items = []
    food_ids.each_with_index do |food_id, index|
      quantity = quantities[index] || 1
      price_index = price_indexes[index] || 0 # Default to first price option
      food_items << {
        food_id: food_id,
        quantity: quantity,
        price_index: price_index
      }
    end

    food_items
  end

  # Helper method to get the selected price for a food item in an order
  # Uses the price_indexes stored in the order to get the correct price
  def get_selected_food_price(food, order, food_index)
    # If no price indexes are set, use the first price (backward compatibility)
    if order.price_indexes.blank? || order.price_indexes[food_index].nil?
      return food.price
    end

    price_index = order.price_indexes[food_index]
    food.prices_array[price_index] || food.price
  end

  # Helper method to get display recipient details
  # Returns recipient details if available, otherwise customer details
  def get_display_recipient(order)
    if order.recipient.present?
      {
        id: order.recipient.id,
        name: order.recipient.recipient_name,
        phone: order.recipient.recipient_phone,
        address: order.recipient.recipient_address,
        is_recipient: true # Flag to indicate this is recipient data
      }
    else
      {
        id: order.customer.id,
        name: order.customer.name,
        phone: order.customer.phone,
        address: order.delivery_address, # Use delivery_address from order
        email: order.customer.email,
        is_recipient: false # Flag to indicate this is customer data
      }
    end
  rescue StandardError => e
    Rails.logger.error("Error getting display recipient: #{e.message}")
    # Return fallback customer data
    {
      id: order.customer&.id,
      name: order.customer&.name,
      phone: order.customer&.phone,
      address: order.delivery_address,
      email: order.customer&.email,
      is_recipient: false
    }
  end

  def initialize_paystack_transaction(payment)
    url = 'https://api.paystack.co/transaction/initialize'
    headers = {
      'Authorization' => "Bearer #{Rails.application.credentials.paystack[:secret_key]}",
      'Content-Type' => 'application/json'
    }
    # Assuming GHS currency; amount in pesewas (multiply by 100)
    body = {
      email: @order.customer.email, # Assuming Order belongs_to :customer
      amount: (payment.amount * 100).to_i
    }.to_json

    begin
      response = HTTParty.post(url, headers:, body:, timeout: 30)
      JSON.parse(response.body)
    rescue HTTParty::Error, JSON::ParserError, Net::OpenTimeout => e
      Rails.logger.error("Paystack API error: #{e.message}")
      { 'status' => false, 'message' => 'Failed to connect to payment gateway.' }
    end
  end

  def update_order_status(order)
    return unless params[:order] && params[:order][:status]

    new_status = params[:order][:status]
    old_status = order.status

    # Send push notification about status change
    send_status_notification(order, old_status, new_status)

    # If status is changed to 'received', initiate transfers
    if new_status == Order::RECEIVED
      # Update status and initiate transfers
      order.mark_as_delivered!(customer_confirmed: true)
    else
      # Just update the status for other status changes
      order.update!(status: new_status)
    end
  end

  def send_status_notification(order, old_status, new_status)
    # Skip if status hasn't changed.
    return if old_status == new_status

    Rails.logger.info("=== PUSH NOTIFICATION DEBUG ===")
    Rails.logger.info("Order ID: #{order.id}")
    Rails.logger.info("Old Status: #{old_status}")
    Rails.logger.info("New Status: #{new_status}")
    Rails.logger.info("Order::RECEIVED constant: #{Order::RECEIVED}")
    Rails.logger.info("Status comparison: #{new_status} == #{Order::RECEIVED} => #{new_status == Order::RECEIVED}")

    # Check push subscription counts before initializing service
    customer_subscriptions = PushSubscription.where(user: order.customer).count
    vendor_subscriptions = PushSubscription.where(user_type: 'Vendor').count
    rider_subscriptions = order.rider ? PushSubscription.where(user: order.rider).count : 0

    Rails.logger.info("Push subscription counts:")
    Rails.logger.info("  Customer #{order.customer.id}: #{customer_subscriptions} subscriptions")
    Rails.logger.info("  All vendors: #{vendor_subscriptions} subscriptions")
    Rails.logger.info("  Rider #{order.rider&.id}: #{rider_subscriptions} subscriptions")

    # Initialize push notification service
    begin
      push_service = PushNotificationService.new
      Rails.logger.info("✅ PushNotificationService initialized successfully")
    rescue => e
      Rails.logger.error("❌ Failed to initialize PushNotificationService: #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))
      return
    end

    # Send notifications based on status change
    case new_status
    when Order::CONFIRMED
      # Notify customer
      customer_title = "Order ##{order.id} Confirmed"
      customer_body = 'Your order has been confirmed and is being processed.'
      push_service.send_to_user(order.customer, customer_title, customer_body, order_data(order))

      # Notify vendors
      vendor_title = "New Order ##{order.id}"
      vendor_body = "You have a new order from #{order.customer.name}."
      push_service.send_to_vendors(order, vendor_title, vendor_body, order_data(order))

    when Order::PROCESSING
      # Notify customer
      customer_title = "Order ##{order.id} Being Prepared"
      customer_body = 'Your order is now being prepared by the vendor.'
      push_service.send_to_user(order.customer, customer_title, customer_body, order_data(order))

    when Order::READY_FOR_PICKUP
      # Notify customer
      customer_title = "Order ##{order.id} Ready for Pickup"
      customer_body = 'Your order is ready for pickup by the rider.'
      push_service.send_to_user(order.customer, customer_title, customer_body, order_data(order))

      # Notify rider
      if order.rider
        rider_title = "Order ##{order.id} Ready for Pickup"
        rider_body = "An order is ready for pickup at #{order.vendors.map(&:name).join(', ')}."
        rider_data = order_data(order).merge(url: '/riders-dashboard')
        push_service.send_to_user(order.rider, rider_title, rider_body, rider_data)
      end

    when Order::OUT_FOR_DELIVERY
      # Notify customer
      customer_title = "Order ##{order.id} Out for Delivery"
      customer_body = 'Your order is on the way! The rider has picked it up.'
      push_service.send_to_user(order.customer, customer_title, customer_body, order_data(order))

    when Order::DELIVERED
      # Notify customer
      customer_title = "Order ##{order.id} Delivered"
      customer_body = 'Your order has been delivered. Please confirm receipt.'
      push_service.send_to_user(order.customer, customer_title, customer_body, order_data(order))

    when Order::RECEIVED
      Rails.logger.info("=== PROCESSING RECEIVED STATUS ===")

      # Notify customer
      customer_title = "Order ##{order.id} Received"
      customer_body = 'Thank you for confirming receipt of your order.'
      Rails.logger.info("Sending notification to customer: #{order.customer.id} - #{customer_title}")

      begin
        push_service.send_to_user(order.customer, customer_title, customer_body, order_data(order))
        Rails.logger.info("Customer notification sent successfully")
      rescue => e
        Rails.logger.error("Failed to send customer notification: #{e.message}")
      end

      # Notify vendors
      vendor_title = "Order ##{order.id} Completed"
      vendor_body = 'Customer has confirmed receipt of their order. Payment will be processed.'
      vendor_data = order_data(order).merge(url: '/vendors-dashboard')
      Rails.logger.info("Sending notification to vendors for order: #{order.id}")

      begin
        push_service.send_to_vendors(order, vendor_title, vendor_body, vendor_data)
        Rails.logger.info("Vendor notifications sent successfully")
      rescue => e
        Rails.logger.error("Failed to send vendor notifications: #{e.message}")
      end

      # Notify rider
      if order.rider
        rider_title = "Order ##{order.id} Completed"
        rider_body = 'Customer has confirmed receipt of their order. Payment will be processed.'
        rider_data = order_data(order).merge(url: '/riders-dashboard')
        Rails.logger.info("Sending notification to rider: #{order.rider.id} - #{rider_title}")

        begin
          push_service.send_to_user(order.rider, rider_title, rider_body, rider_data)
          Rails.logger.info("Rider notification sent successfully")
        rescue => e
          Rails.logger.error("Failed to send rider notification: #{e.message}")
        end
      else
        Rails.logger.warn("No rider found for order #{order.id}")
      end
    end
  end

  # Helper method to notify vendors for an order using background jobs
  def notify_vendors_background(order, _title, _body)
    return unless order.foods.present?

    # Get unique vendors from the order's foods
    vendor_ids = order.foods.pluck(:vendor_id).uniq

    # Send notification to each vendor using background jobs.
    vendor_ids.each do |vendor_id|
      vendor = Vendor.find_by(id: vendor_id)
      if vendor
        order_data(order).merge(url: '/vendors-dashboard')
      end
    end
  end

  # Helper method to prepare order data for notifications
  def order_data(order)
    {
      order_id: order.id,
      status: order.status,
      created_at: order.created_at
    }
  end

  def update_order_associations(order)
    # Only update associations if explicitly provided in the params
    # Do NOT modify food_ids when just updating status
    if params[:vendor_id] && params[:order] && params[:order][:food_ids].present?
      vendor_food_ids = Food.where(vendor_id: params[:vendor_id]).pluck(:id).map(&:to_s) # Convert IDs to strings

      # Only allow food IDs that belong to this vendor
      new_food_ids = params[:order][:food_ids].select { |id| vendor_food_ids.include?(id.to_s) }

      # Only update if new food IDs are provided
      order.food_ids = new_food_ids if new_food_ids.present?
    elsif params[:rider_id]
      order.rider_id = params[:rider_id]
    elsif params[:customer_id]
      order.customer_id = params[:customer_id]
    end
  end

  def calculate_food_price(food_ids, quantities)
    total = 0.0
    foods = Food.where(id: food_ids)

    foods.each_with_index do |food, index|
      total += food.price * quantities[index] if quantities[index]
    end

    total
  end

  def order_params
    params.require(:order).permit(:customer_id, :rider_id, :status, :delivery_address,
                                  :delivery_price, :donation,
                                  food_ids: [],
                                  quantities: [],
                                  price_indexes: [],
                                  recipient_attributes:
                                  %i[recipient_name recipient_phone
                                     recipient_address])
  end
end
