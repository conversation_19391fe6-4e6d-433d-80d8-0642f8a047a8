class ComplaintsController < ApplicationController
  before_action :authenticate_user_from_token!, only: %i[index create]

  # GET /customers/:customer_id/complaints
  def index
    @customer = Customer.find(params[:customer_id])

    if @current_customer && @current_customer.id == @customer.id
      @complaints = Complaint.where(customer: @customer).order(created_at: :desc)
      render json: @complaints
    else
      render json: { error: 'Unauthorized' }, status: :unauthorized
    end
  end

  # POST /complaints
  def create
    @complaint = Complaint.new(complaint_params)
    # Associate with current customer if authenticated
    @complaint.customer = @current_customer if @current_customer

    # Handle file attachments
    if params[:attachments].present?
      # Handle both array and single attachment cases
      attachments = params[:attachments].is_a?(Array) ? params[:attachments] : [params[:attachments]]

      attachments.each do |attachment|
        @complaint.attachments.attach(attachment)
      end
    end

    if @complaint.save
      render json: @complaint, status: :created
    else
      render json: { errors: @complaint.errors.full_messages }, status: :unprocessable_entity
    end
  end

  private

  def set_complaint
    @complaint = Complaint.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Complaint not found' }, status: :not_found
  end

  def complaint_params
    params.require(:complaint).permit(:name, :email, :order_number, :category, :complaint)
  end
end
