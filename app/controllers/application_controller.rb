class ApplicationController < ActionController::API
  before_action :configure_permitted_parameters, if: :devise_controller?

  protected

  def configure_permitted_parameters
    devise_parameter_sanitizer.permit(:sign_in, keys: %i[username])
  end

  def authenticate_user_from_token!
    token = extract_token_from_request

    unless token
      Rails.logger.warn('No token provided')
      return render_unauthorized('No token provided')
    end

    payload = decode_jwt_token(token)

    return render_unauthorized('Invalid token') unless payload

    # Try to find user by 'sub' claim (standard JWT) or 'id' claim (custom)
    user_id = payload['sub'] || payload['id']

    # Try to find user in different user types
    @current_customer = Customer.find_by(id: user_id)
    @current_vendor = Vendor.find_by(id: user_id)
    @current_rider = Rider.find_by(id: user_id)
    @current_admin = Admin.find_by(id: user_id)

    # If no user found, render unauthorized
    return if @current_customer || @current_vendor || @current_rider || @current_admin

    render_unauthorized('User not found')
  end

  def extract_token_from_request
    # Try to get token from Authorization header
    auth_header = request.headers['Authorization']

    if auth_header
      # Check if the token already has 'Bearer ' prefix
      token = if auth_header.start_with?('Bearer ')
                auth_header.gsub('Bearer ', '')
              else
                # If not, use it as is
                auth_header
              end
      return token
    end

    # Try to get token from params
    return params[:token] if params[:token].present?

    nil
  end

  def decode_jwt_token(token)
    # Try to decode with HS256 algorithm
    begin
      decoded = JWT.decode(token, Rails.application.credentials.devise_jwt_secret_key, true, { algorithm: 'HS256' })[0]
      return decoded
    rescue JWT::ExpiredSignature => e
      Rails.logger.error("JWT token expired: #{e.message}")
      # For expired tokens, we could redirect to login or refresh the token
      # For now, just log it and return nil
    rescue JWT::DecodeError => e
      Rails.logger.error("JWT decode error with HS256: #{e.message}")
    rescue StandardError => e
      Rails.logger.error("Other error decoding JWT with HS256: #{e.class.name} - #{e.message}")
    end

    nil
  end

  def render_unauthorized(message = 'Unauthorized')
    render json: { error: message }, status: :unauthorized
  end
end
