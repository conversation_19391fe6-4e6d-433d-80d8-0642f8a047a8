class PaystackWebhooksController < ApplicationController
  skip_before_action :verify_authenticity_token, raise: false

  def create
    # Verify the webhook signature
    raw_body = request.body.read
    signature = request.headers['x-paystack-signature']
    secret_key = Rails.application.credentials.paystack[:secret_key]
    expected_signature = OpenSSL::HMAC.hexdigest('sha512', secret_key, raw_body)

    if signature == expected_signature
      event = JSON.parse(raw_body)
      case event['event']
      when 'charge.success'
        handle_charge_success(event['data'])
      when 'charge.failed'
        handle_charge_failed(event['data'])
      when 'transfer.success'
        handle_transfer_success(event['data'])
      when 'transfer.failed'
        handle_transfer_failed(event['data'])
      when 'transfer.reversed'
        handle_transfer_reversed(event['data'])
      end
      head :ok
    else
      head :unauthorized
    end
  end

  private

  def handle_charge_success(data)
    payment = Payment.find_by(reference: data['reference'])
    return unless payment && payment.status == 'pending' && (payment.amount * 100).to_i == data['amount'].to_i

    payment.update(status: 'success', transaction_id: data['id'])

    # Confirm all orders in the same group (multi-vendor fix)
    if payment.order&.order_group
      orders = payment.order.order_group.orders
      orders.each { |o| o.update(status: 'confirmed') }
    else
      # Fallback for single order
      payment.order.update(status: 'confirmed') if payment.order
    end
  end

  def handle_charge_failed(data)
    payment = Payment.find_by(reference: data['reference'])
    payment&.update(status: 'failed') if payment&.status == 'pending'
  end

  def handle_transfer_success(data)
    transfer = Transfer.find_by(paystack_transfer_code: data['transfer_code'])
    return unless transfer && transfer.status == Transfer::PROCESSING

    transfer.update!(status: Transfer::SUCCESS)
  end

  def handle_transfer_failed(data)
    transfer = Transfer.find_by(paystack_transfer_code: data['transfer_code'])
    return unless transfer && transfer.status == Transfer::PROCESSING

    transfer.update!(status: Transfer::FAILED)
  end

  def handle_transfer_reversed(data)
    transfer = Transfer.find_by(paystack_transfer_code: data['transfer_code'])
    return unless transfer && [Transfer::PROCESSING, Transfer::SUCCESS].include?(transfer.status)

    transfer.update!(status: Transfer::REVERSED)
  end
end
