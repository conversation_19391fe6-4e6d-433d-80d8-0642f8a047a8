class PayoutsController < ApplicationController
  before_action :authenticate_user_from_token!, only: [:index]
  before_action :set_recipient, only: [:index]

  # GET /vendors/:vendor_id/payouts
  # GET /riders/:rider_id/payouts
  def index
    @transfers = Transfer.where(recipient: @recipient)
      .order(created_at: :desc)

    render json: @transfers.map { |transfer|
      {
        id: transfer.id,
        order_id: transfer.order_id,
        amount: transfer.amount,
        status: transfer.status,
        created_at: transfer.created_at,
        updated_at: transfer.updated_at
      }
    }
  end

  private

  def set_recipient
    if params[:vendor_id]
      @recipient = @current_vendor
    elsif params[:rider_id]
      @recipient = @current_rider
    else
      render json: { error: 'Invalid recipient type' }, status: :bad_request
    end
  end

  def authenticate_user_from_token!
    token = extract_token_from_request
    return render_unauthorized('No token provided') unless token

    payload = decode_jwt_token(token)

    # Check if we got an error response from decode_jwt_token
    return render_unauthorized(payload) if payload.is_a?(Hash) && payload['error'] == 'token_expired'

    return render_unauthorized('Invalid token') unless payload

    # Try to find user by 'sub' claim (standard JWT) or 'id' claim (custom)
    user_id = payload['sub'] || payload['id']

    if params[:vendor_id]
      @current_vendor = Vendor.find_by(id: user_id)
      render_unauthorized('Vendor not found') unless @current_vendor
    elsif params[:rider_id]
      @current_rider = Rider.find_by(id: user_id)
      render_unauthorized('Rider not found') unless @current_rider
    else
      render_unauthorized('Invalid user type')
    end
  end

  def extract_token_from_request
    # Try to get token from Authorization header
    auth_header = request.headers['Authorization']

    if auth_header
      # Check if the token already has 'Bearer ' prefix
      token = if auth_header.start_with?('Bearer ')
                auth_header.gsub('Bearer ', '')
              else
                # If not, use it as is
                auth_header
              end
      return token
    end

    # Try to get token from params
    return params[:token] if params[:token].present?

    nil
  end

  def decode_jwt_token(token)
    # Try to decode with HS256 algorithm
    begin
      decoded = JWT.decode(token, Rails.application.credentials.devise_jwt_secret_key, true, { algorithm: 'HS256' })[0]
      return decoded
    rescue JWT::ExpiredSignature => e
      Rails.logger.error("JWT token expired: #{e.message}")
      # Return a specific error for expired tokens so frontend can handle it
      return { 'error' => 'token_expired', 'message' => 'Your session has expired. Please log in again.' }
    rescue JWT::DecodeError => e
      Rails.logger.error("JWT decode error with HS256: #{e.message}")
    rescue StandardError => e
      Rails.logger.error("Other error decoding JWT with HS256: #{e.class.name} - #{e.message}")
    end

    nil
  end

  def render_unauthorized(message = 'Unauthorized')
    if message.is_a?(Hash) && message['error'] == 'token_expired'
      render json: { error: message['error'], message: message['message'] }, status: :unauthorized
    else
      render json: { error: 'unauthorized', message: }, status: :unauthorized
    end
  end
end
