class Admin::RidersController < Admin::BaseController
  # GET /admin/riders
  def index
    @riders = Rider.all.order(created_at: :desc)

    render json: @riders.map { |rider|
      rider_json = rider.as_json(only: %i[id name email phone address within_dunkwa_price outside_dunkwa_price
                                          validation_status])

      # Add attachment URLs
      rider_json.merge(
        avatar_url: attachment_url(rider.avatar),
        front_drivers_license_url: attachment_url(rider.front_drivers_license),
        back_drivers_license_url: attachment_url(rider.back_drivers_license),
        front_ghana_card_url: attachment_url(rider.front_ghana_card),
        back_ghana_card_url: attachment_url(rider.back_ghana_card),
        moto_pic_url: attachment_url(rider.moto_pic),
        vehicle_registration_pic_url: attachment_url(rider.vehicle_registration_pic)
      )
    }
  end

  # GET /admin/riders/:id
  def show
    @rider = Rider.find(params[:id])

    rider_json = @rider.as_json(only: %i[id name email phone address within_dunkwa_price outside_dunkwa_price
                                         validation_status])

    # Add attachment URLs
    rider_json.merge!(
      avatar_url: attachment_url(@rider.avatar),
      front_drivers_license_url: attachment_url(@rider.front_drivers_license),
      back_drivers_license_url: attachment_url(@rider.back_drivers_license),
      front_ghana_card_url: attachment_url(@rider.front_ghana_card),
      back_ghana_card_url: attachment_url(@rider.back_ghana_card),
      moto_pic_url: attachment_url(@rider.moto_pic),
      vehicle_registration_pic_url: attachment_url(@rider.vehicle_registration_pic)
    )

    # Get rider's orders
    orders = @rider.orders.order(created_at: :desc).limit(10)

    # Get rider's transfers (payouts)
    transfers = @rider.transfers.order(created_at: :desc).limit(10)

    render json: rider_json.merge(
      recent_orders: orders.map do |order|
        order.as_json(only: %i[id status total_price created_at])
      end,
      recent_payouts: transfers.map do |transfer|
        transfer.as_json(only: %i[id amount service_fee status created_at])
      end
    )
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Rider not found' }, status: :not_found
  end

  # PATCH/PUT /admin/riders/:id/approve
  def approve
    @rider = Rider.find(params[:id])

    # Set a default address if it's blank
    @rider.address = 'Default Address' if @rider.address.blank?

    if @rider.update(validation_status: 'verified')
      render json: {
        message: 'Rider approved successfully',
        rider: @rider.as_json(only: %i[id name validation_status])
      }, status: :ok
    else
      render json: { errors: @rider.errors.full_messages }, status: :unprocessable_entity
    end
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Rider not found' }, status: :not_found
  end

  # PATCH/PUT /admin/riders/:id/reject
  def reject
    @rider = Rider.find(params[:id])

    # Set a default address if it's blank
    @rider.address = 'Default Address' if @rider.address.blank?

    if @rider.update(validation_status: 'rejected')
      render json: {
        message: 'Rider rejected',
        rider: @rider.as_json(only: %i[id name validation_status])
      }, status: :ok
    else
      render json: { errors: @rider.errors.full_messages }, status: :unprocessable_entity
    end
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Rider not found' }, status: :not_found
  end

  # GET /admin/riders/pending
  def pending
    @riders = Rider.where(validation_status: 'not-verified').order(created_at: :desc)

    render json: @riders.map { |rider|
      rider_json = rider.as_json(only: %i[id name email phone address within_dunkwa_price outside_dunkwa_price
                                          validation_status])

      # Add attachment URLs
      rider_json.merge(
        avatar_url: attachment_url(rider.avatar),
        front_drivers_license_url: attachment_url(rider.front_drivers_license),
        back_drivers_license_url: attachment_url(rider.back_drivers_license),
        front_ghana_card_url: attachment_url(rider.front_ghana_card),
        back_ghana_card_url: attachment_url(rider.back_ghana_card),
        moto_pic_url: attachment_url(rider.moto_pic),
        vehicle_registration_pic_url: attachment_url(rider.vehicle_registration_pic)
      )
    }
  end

  # GET /admin/riders/statistics
  def statistics
    # Riders by validation status
    riders_by_status = {
      verified: Rider.where(validation_status: 'verified').count,
      pending: Rider.where(validation_status: 'not-verified').count,
      rejected: Rider.where(validation_status: 'rejected').count
    }

    # New riders this month
    new_riders_this_month = Rider.where('created_at >= ?', Time.zone.now.beginning_of_month).count

    # Top riders by revenue
    top_riders_by_revenue = Rider.joins(:transfers)
      .where(transfers: { status: 'success' })
      .select('riders.id, riders.name, SUM(transfers.amount) as total_revenue')
      .group('riders.id, riders.name')
      .order('total_revenue DESC')
      .limit(5)

    render json: {
      riders_by_status:,
      new_riders_this_month:,
      top_riders_by_revenue:
    }
  end

  private

  def attachment_url(attachment)
    attachment.attached? ? url_for(attachment) : nil
  end
end
