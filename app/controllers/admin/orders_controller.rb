class Admin::OrdersController < Admin::BaseController
  # GET /admin/orders
  def index
    @orders = Order.includes(:customer, :rider, :payment)
      .order(created_at: :desc)

    render json: @orders.map { |order|
      # Use existing foods method or query directly
      foods = order.foods.includes(:vendor, food_image_attachment: :blob)

      # Get vendor information from the foods
      vendors = foods.map(&:vendor).uniq.compact.map do |vendor|
        { id: vendor.id, name: vendor.name }
      end

      order.as_json(include: {
                      customer: { only: %i[id name email phone] },
                      rider: { only: %i[id name phone] },
                      payment: { only: %i[id status amount reference] }
                    }).merge(
                      vendor: vendors.first,
                      vendors:
                    )
    }
  end

  # GET /admin/orders/:id
  def show
    @order = Order.includes(
      :customer,
      :rider,
      :payment,
      :transfers,
      :recipient
    ).find(params[:id])

    # Get foods for this order with eager loading
    foods = Food.includes(:vendor, food_image_attachment: :blob)
      .where(id: @order.food_ids)

    # Get vendor information from the foods
    vendors = foods.map(&:vendor).uniq.compact.map { |vendor| { id: vendor.id, name: vendor.name } }

    render json: @order.as_json(include: {
                                  customer: { only: %i[id name email phone] },
                                  rider: { only: %i[id name phone] },
                                  payment: { only: %i[id status amount reference] },
                                  transfers: {
                                    only: %i[id recipient_type recipient_id amount service_fee status],
                                    methods: [:recipient_name]
                                  }
                                }).merge(
                                  vendor: vendors.first, # For backward compatibility
                                  vendors:,
                                  foods: foods.map do |food|
                                    food.as_json(only: %i[id name description], methods: [:prices_array, :has_multiple_prices?, :min_price, :max_price, :price]).merge(
                                      food_image_url: food.food_image_url
                                    )
                                  end
                                )
  end
end
