class Admin::ServiceFeesController < Admin::BaseController
  # GET /admin/service_fees
  # Returns summary of all service fees
  def index
    # Get total service fees
    total_fees = Transfer.total_service_fees
    vendor_fees = Transfer.vendor_service_fees
    rider_fees = Transfer.rider_service_fees

    # Get monthly breakdown
    monthly_fees = monthly_service_fees

    # Get service fee percentages from the database
    service_fees = {
      total_service_fees: total_fees,
      vendor_service_fees: vendor_fees,
      rider_service_fees: rider_fees,
      monthly_breakdown: monthly_fees,
      vendorFeePercentage: ServiceFeeCalculator.vendor_fee_percentage,
      riderFeePercentage: ServiceFeeCalculator.rider_within_dunkwa_fee,
      platformFeePercentage: ServiceFeeCalculator.platform_fee_percentage
    }

    render json: service_fees, status: :ok
  end

  # GET /admin/service_fees/vendors
  # Returns service fees from vendors
  def vendors
    # Get all vendors with their service fees
    vendors_with_fees = Vendor.joins(:transfers)
      .where(transfers: { status: Transfer::SUCCESS })
      .select('vendors.id, vendors.name, SUM(transfers.service_fee) as total_fees')
      .group('vendors.id, vendors.name')
      .order('total_fees DESC')

    render json: vendors_with_fees, status: :ok
  end

  # GET /admin/service_fees/riders
  # Returns service fees from riders
  def riders
    # Get all riders with their service fees
    riders_with_fees = Rider.joins(:transfers)
      .where(transfers: { status: Transfer::SUCCESS })
      .select('riders.id, riders.name, SUM(transfers.service_fee) as total_fees')
      .group('riders.id, riders.name')
      .order('total_fees DESC')

    render json: riders_with_fees, status: :ok
  end

  # PUT /admin/service_fees
  # Updates service fee percentages
  def update
    if params[:service_fee].present?
      # Update service fee configurations in the database
      vendor_fee = params[:service_fee][:vendorFeePercentage].to_f
      rider_fee = params[:service_fee][:riderFeePercentage].to_f
      platform_fee = params[:service_fee][:platformFeePercentage].to_f

      # Update the service fee configurations
      ServiceFeeConfig.set(ServiceFeeConfig::VENDOR_FEE_PERCENTAGE, vendor_fee)
      ServiceFeeConfig.set(ServiceFeeConfig::RIDER_WITHIN_DUNKWA_FEE, rider_fee)
      ServiceFeeConfig.set(ServiceFeeConfig::PLATFORM_FEE_PERCENTAGE, platform_fee)

      # Return the updated values
      service_fees = {
        vendorFeePercentage: ServiceFeeCalculator.vendor_fee_percentage,
        riderFeePercentage: ServiceFeeCalculator.rider_within_dunkwa_fee,
        platformFeePercentage: ServiceFeeCalculator.platform_fee_percentage
      }

      render json: service_fees, status: :ok
    else
      render json: { error: 'Invalid service fee parameters' }, status: :unprocessable_entity
    end
  end

  private

  def monthly_service_fees
    # Get service fees grouped by month
    Transfer.successful
      .select("DATE_TRUNC('month', created_at) as month, SUM(service_fee) as total_fees")
      .group("DATE_TRUNC('month', created_at)")
      .order('month DESC')
      .map do |result|
      {
        month: result.month.strftime('%B %Y'),
        total_fees: result.total_fees
      }
    end
  end
end
