class Admin::CategoriesController < Admin::BaseController
  # GET /admin/categories
  def index
    @categories = Category.all.order(name: :asc)

    render json: @categories
  end

  # GET /admin/categories/:id
  def show
    @category = Category.find(params[:id])

    # Get vendors in this category
    vendors = @category.vendors

    # Get foods in this category
    foods = Food.joins(vendor: :categories)
      .where(categories: { id: @category.id })
      .distinct

    render json: @category.as_json.merge(
      vendors: vendors.map { |vendor| vendor.as_json(only: %i[id name]) },
      foods: foods.map do |food|
        food.as_json(only: %i[id name price description vendor_id]).merge(
          food_image_url: food.food_image_url,
          vendor_name: food.vendor.name
        )
      end
    )
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Category not found' }, status: :not_found
  end

  # POST /admin/categories
  def create
    @category = Category.new(category_params)

    if @category.save
      render json: @category, status: :created
    else
      render json: { errors: @category.errors.full_messages }, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /admin/categories/:id
  def update
    @category = Category.find(params[:id])

    if @category.update(category_params)
      render json: @category
    else
      render json: { errors: @category.errors.full_messages }, status: :unprocessable_entity
    end
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Category not found' }, status: :not_found
  end

  # DELETE /admin/categories/:id
  def destroy
    @category = Category.find(params[:id])
    @category.destroy

    head :no_content
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Category not found' }, status: :not_found
  end

  private

  def category_params
    params.require(:category).permit(:name)
  end
end
