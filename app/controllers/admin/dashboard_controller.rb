class Admin::DashboardController < Admin::BaseController
  # GET /admin/dashboard
  def index
    render json: {
      counts: dashboard_counts,
      revenue_stats:,
      recent_orders:,
      recent_payouts:,
      orders_by_status:,
      monthly_orders:,
      monthly_revenue:,
      recent_complaints:,
      recent_suggestions:,
      complaints_by_status:,
      complaints_by_category:,
      suggestions_by_status:,
      suggestions_by_category:,
      top_vendors_by_sales:,
      top_riders_by_deliveries:
    }
  end

  private

  def dashboard_counts
    # Use a single query to get counts by validation status for vendors
    vendors_by_status = Vendor.group(:validation_status).count

    # Use a single query to get counts by validation status for riders
    riders_by_status = Rider.group(:validation_status).count

    # Get counts for complaints and suggestions
    complaints_by_status = Complaint.group(:status).count
    suggestions_by_status = Suggestion.group(:status).count

    {
      total_orders: Order.count,
      total_customers: Customer.count,
      total_vendors: vendors_by_status.values.sum,
      total_riders: riders_by_status.values.sum,
      pending_vendors: vendors_by_status['not-verified'] || 0,
      pending_riders: riders_by_status['not-verified'] || 0,
      total_complaints: Complaint.count,
      pending_complaints: complaints_by_status['pending'] || 0,
      total_suggestions: Suggestion.count,
      pending_suggestions: suggestions_by_status['pending'] || 0
    }
  end

  def revenue_stats
    # Use a single query for order revenue
    order_revenue = Order.where(status: [Order::CONFIRMED, Order::DELIVERED, Order::RECEIVED]).sum(:total_price)

    # Use a single query with grouping for transfer fees
    transfer_fees = Transfer.group(:recipient_type).sum(:service_fee)

    {
      total_revenue: order_revenue,
      total_service_fees: transfer_fees.values.sum,
      vendor_service_fees: transfer_fees['Vendor'] || 0,
      rider_service_fees: transfer_fees['Rider'] || 0
    }
  end

  def recent_orders
    # Eager load associations to avoid N+1 queries
    Order.includes(:customer, :rider)
      .order(created_at: :desc)
      .limit(5)
      .map do |order|
        order.as_json(only: %i[id status total_price created_at]).merge(
          customer_name: order.customer&.name,
          rider_name: order.rider&.name
        )
      end
  end

  def recent_payouts
    # Eager load recipients to avoid N+1 queries
    Transfer.includes(:recipient)
      .order(created_at: :desc)
      .limit(5)
      .map do |transfer|
        recipient_name = transfer.recipient&.name || 'Unknown'

        transfer.as_json(only: %i[id amount service_fee status created_at]).merge(
          recipient_name:,
          recipient_type: transfer.recipient_type
        )
      end
  end

  def orders_by_status
    # Use a single query with grouping
    status_counts = Order.group(:status).count

    {
      pending: status_counts[Order::PENDING] || 0,
      confirmed: status_counts[Order::CONFIRMED] || 0,
      processing: status_counts[Order::PROCESSING] || 0,
      ready_for_pickup: status_counts[Order::READY_FOR_PICKUP] || 0,
      out_for_delivery: status_counts[Order::OUT_FOR_DELIVERY] || 0,
      delivered: status_counts[Order::DELIVERED] || 0,
      received: status_counts[Order::RECEIVED] || 0,
      canceled: status_counts[Order::CANCELED] || 0
    }
  end

  def monthly_orders
    Order.where('created_at >= ?', 6.months.ago)
      .select("DATE_TRUNC('month', created_at) as month, COUNT(*) as order_count")
      .group("DATE_TRUNC('month', created_at)")
      .order('month DESC')
      .map do |result|
        {
          month: result.month.strftime('%B %Y'),
          order_count: result.order_count
        }
      end
  end

  def monthly_revenue
    Order.where(status: [Order::CONFIRMED, Order::DELIVERED, Order::RECEIVED])
      .where('created_at >= ?', 6.months.ago)
      .select("DATE_TRUNC('month', created_at) as month, SUM(total_price) as total_revenue")
      .group("DATE_TRUNC('month', created_at)")
      .order('month DESC')
      .map do |result|
        {
          month: result.month.strftime('%B %Y'),
          total_revenue: result.total_revenue
        }
      end
  end

  def recent_complaints
    # Eager load associations to avoid N+1 queries
    Complaint.includes(:customer)
      .order(created_at: :desc)
      .limit(5)
      .map do |complaint|
        complaint.as_json(only: %i[id category status created_at]).merge(
          customer_name: complaint.customer&.name || complaint.name,
          complaint_summary: complaint.complaint.truncate(100)
        )
      end
  end

  def recent_suggestions
    # Eager load associations to avoid N+1 queries
    Suggestion.includes(:customer)
      .order(created_at: :desc)
      .limit(5)
      .map do |suggestion|
        suggestion.as_json(only: %i[id category status rating created_at]).merge(
          customer_name: suggestion.customer&.name || suggestion.name,
          suggestion_summary: suggestion.suggestion.truncate(100)
        )
      end
  end

  def complaints_by_status
    Complaint.group(:status).count
  end

  def complaints_by_category
    Complaint.group(:category).count
  end

  def suggestions_by_status
    Suggestion.group(:status).count
  end

  def suggestions_by_category
    Suggestion.group(:category).count
  end

  # Get top vendors by sales amount, grouped by month
  def top_vendors_by_sales
    # Get only orders with received status from the last 6 months
    start_date = 6.months.ago.beginning_of_month

    # Use SQL with JSON parsing for better performance
    vendor_sales = ActiveRecord::Base.connection.execute(<<-SQL
      WITH order_foods AS (
        SELECT
          o.id AS order_id,
          o.created_at,
          o.status,
          f.id AS food_id,
          -- Use the first price from the JSON array (simplified for admin stats)
          (f.prices::json->>0)::float AS selected_price,
          f.vendor_id
        FROM orders o
        CROSS JOIN UNNEST(o.food_ids) AS food_id
        JOIN foods f ON f.id = food_id::integer
        WHERE o.status = 'received'
        AND o.created_at >= '#{start_date}'
      )
      SELECT
        v.id AS vendor_id,
        v.name AS vendor_name,
        DATE_TRUNC('month', of.created_at) AS month,
        SUM(of.selected_price) AS total_sales,
        COUNT(DISTINCT of.order_id) AS order_count
      FROM order_foods of
      JOIN vendors v ON v.id = of.vendor_id
      GROUP BY v.id, v.name, DATE_TRUNC('month', of.created_at)
      ORDER BY month DESC, total_sales DESC
    SQL
                                                        )

    # Transform the results into a structured format
    results = {}

    vendor_sales.each do |row|
      # Convert timestamp to formatted month string
      month_value = row['month']
      month = if month_value.is_a?(Time) || month_value.is_a?(DateTime)
                month_value.strftime('%B %Y')
              elsif month_value.nil?
                'Unknown Date'
              else
                begin
                  Date.parse(month_value.to_s).strftime('%B %Y')
                rescue StandardError
                  'Unknown Date'
                end
              end

      # Initialize the month if it doesn't exist
      results[month] ||= []

      # Add the vendor data to the month
      results[month] << {
        vendor_id: row['vendor_id'],
        vendor_name: row['vendor_name'],
        total_sales: row['total_sales'],
        order_count: row['order_count']
      }
    end

    # Convert the hash to an array of objects with month and vendors
    results.map do |month, vendors|
      {
        month:,
        vendors: vendors.sort_by { |v| -v[:total_sales].to_f }.take(5) # Top 5 vendors per month
      }
    end.sort_by { |item| Date.parse(item[:month]) }.reverse # Sort by month descending
  end

  # Get top riders by delivery count and earnings, grouped by month
  def top_riders_by_deliveries
    # Get only orders with received status from the last 6 months
    start_date = 6.months.ago.beginning_of_month

    # This query gets the total deliveries and earnings for each rider by month
    # Since orders table doesn't have a delivery_fee column, we need to use the rider's pricing
    rider_deliveries = ActiveRecord::Base.connection.execute(<<-SQL
      SELECT
        r.id AS rider_id,
        r.name AS rider_name,
        DATE_TRUNC('month', o.created_at) AS month,
        COUNT(o.id) AS delivery_count,
        SUM(CASE
            WHEN o.delivery_address ILIKE '%dunkwa%' OR o.delivery_address ILIKE '%dunkwa-%' OR o.delivery_address ILIKE '%dunkwa on ofin%' THEN r.within_dunkwa_price
            WHEN o.delivery_address IS NULL THEN r.within_dunkwa_price -- Default to within_dunkwa_price if address is missing
            ELSE r.outside_dunkwa_price
        END) AS total_earnings
      FROM orders o
      JOIN riders r ON r.id = o.rider_id
      WHERE o.status = 'received'
      AND o.created_at >= '#{start_date}'
      GROUP BY r.id, r.name, DATE_TRUNC('month', o.created_at)
      ORDER BY month DESC, total_earnings DESC
    SQL
                                                            )

    # Transform the results into a structured format
    results = {}

    rider_deliveries.each do |row|
      # Convert timestamp to formatted month string
      month_value = row['month']

      # Handle different types of month values
      month = if month_value.is_a?(Time) || month_value.is_a?(DateTime)
                month_value.strftime('%B %Y')
              elsif month_value.nil?
                'Unknown Date'
              else
                begin
                  Date.parse(month_value.to_s).strftime('%B %Y')
                rescue StandardError
                  'Unknown Date'
                end
              end

      # Initialize the month if it doesn't exist
      results[month] ||= []

      # Add the rider data to the month
      results[month] << {
        rider_id: row['rider_id'],
        rider_name: row['rider_name'],
        delivery_count: row['delivery_count'],
        total_earnings: row['total_earnings']
      }
    end

    # Convert the hash to an array of objects with month and riders
    results.map do |month, riders|
      {
        month:,
        riders: riders.sort_by { |r| -r[:total_earnings].to_f }.take(5) # Top 5 riders per month
      }
    end.sort_by { |item| Date.parse(item[:month]) }.reverse # Sort by month descending
  end
end
