class Admin::ComplaintsController < Admin::BaseController
  before_action :set_complaint, only: %i[show update]

  # GET /admin/complaints
  def index
    @complaints = Complaint.all.order(created_at: :desc)

    render json: @complaints.map { |complaint|
      complaint_json = complaint.as_json(
        include: { customer: { only: %i[id name email] } }
      )

      # Add attachment URLs
      complaint_json.merge(
        attachments: complaint.attachments.map do |attachment|
          {
            id: attachment.id,
            filename: attachment.filename.to_s,
            url: url_for(attachment)
          }
        end
      )
    }
  end

  # GET /admin/complaints/:id
  def show
    render json: @complaint.as_json(
      include: { customer: { only: %i[id name email phone] } }
    ).merge(
      attachments: @complaint.attachments.map do |attachment|
        {
          id: attachment.id,
          filename: attachment.filename.to_s,
          url: url_for(attachment)
        }
      end
    )
  end

  # PATCH/PUT /admin/complaints/:id
  def update
    if @complaint.update(complaint_update_params)
      render json: @complaint
    else
      render json: { errors: @complaint.errors.full_messages }, status: :unprocessable_entity
    end
  end

  # GET /admin/complaints/statistics
  def statistics
    # Total complaints
    total_complaints = Complaint.count

    # Complaints by status
    complaints_by_status = {
      pending: Complaint.where(status: Complaint::PENDING).count,
      under_review: Complaint.where(status: Complaint::UNDER_REVIEW).count,
      resolved: Complaint.where(status: Complaint::RESOLVED).count,
      rejected: Complaint.where(status: Complaint::REJECTED).count
    }

    # Complaints by category
    complaints_by_category = {
      order: Complaint.where(category: Complaint::ORDER).count,
      delivery: Complaint.where(category: Complaint::DELIVERY).count,
      food: Complaint.where(category: Complaint::FOOD).count,
      app: Complaint.where(category: Complaint::APP).count,
      payment: Complaint.where(category: Complaint::PAYMENT).count,
      other: Complaint.where(category: Complaint::OTHER).count
    }

    # New complaints this month
    new_complaints_this_month = Complaint.where('created_at >= ?', Time.zone.now.beginning_of_month).count

    # Monthly complaints
    monthly_complaints = Complaint.where('created_at >= ?', 6.months.ago)
      .select("DATE_TRUNC('month', created_at) as month, COUNT(*) as complaint_count")
      .group("DATE_TRUNC('month', created_at)")
      .order('month DESC')
      .map do |result|
        {
          month: result.month.strftime('%B %Y'),
          complaint_count: result.complaint_count
        }
      end

    render json: {
      total_complaints:,
      complaints_by_status:,
      complaints_by_category:,
      new_complaints_this_month:,
      monthly_complaints:
    }
  end

  private

  def set_complaint
    @complaint = Complaint.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Complaint not found' }, status: :not_found
  end

  def complaint_update_params
    params.require(:complaint).permit(:status)
  end
end
