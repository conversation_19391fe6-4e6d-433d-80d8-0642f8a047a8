class Admin::CustomersController < Admin::BaseController
  # GET /admin/customers
  def index
    @customers = Customer.all.order(created_at: :desc)

    render json: @customers.as_json(only: %i[id name email phone address created_at])
  end

  # GET /admin/customers/:id
  def show
    @customer = Customer.find(params[:id])

    # Get customer's orders
    orders = @customer.orders.order(created_at: :desc)

    render json: @customer.as_json(only: %i[id name email phone address created_at]).merge(
      orders: orders.map do |order|
        order_json = order.as_json(only: %i[id status total_price created_at])

        # Get vendor information for this order
        vendors = order.vendors.map { |vendor| { id: vendor.id, name: vendor.name } }

        # Get rider information
        rider = order.rider ? { id: order.rider.id, name: order.rider.name } : nil

        order_json.merge(
          vendors:,
          rider:
        )
      end
    )
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Customer not found' }, status: :not_found
  end

  # GET /admin/customers/statistics
  def statistics
    # Total customers
    total_customers = Customer.count

    # New customers this month
    new_customers_this_month = Customer.where('created_at >= ?', Time.zone.now.beginning_of_month).count

    # Top customers by order count
    top_customers_by_orders = Customer.joins(:orders)
      .select('customers.id, customers.name, COUNT(orders.id) as order_count')
      .group('customers.id, customers.name')
      .order('order_count DESC')
      .limit(5)

    # Top customers by spending
    top_customers_by_spending = Customer.joins(:orders)
      .where(orders: { status: [Order::CONFIRMED, Order::DELIVERED, Order::RECEIVED] })
      .select('customers.id, customers.name, SUM(orders.total_price) as total_spent')
      .group('customers.id, customers.name')
      .order('total_spent DESC')
      .limit(5)

    # Monthly new customer signups
    monthly_signups = Customer.where('created_at >= ?', 6.months.ago)
      .select("DATE_TRUNC('month', created_at) as month, COUNT(*) as signup_count")
      .group("DATE_TRUNC('month', created_at)")
      .order('month DESC')
      .map do |result|
        {
          month: result.month.strftime('%B %Y'),
          signup_count: result.signup_count
        }
      end

    render json: {
      total_customers:,
      new_customers_this_month:,
      top_customers_by_orders:,
      top_customers_by_spending:,
      monthly_signups:
    }
  end
end
