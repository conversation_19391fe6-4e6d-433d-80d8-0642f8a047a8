class Admin::SuggestionsController < Admin::BaseController
  before_action :set_suggestion, only: %i[show update]

  # GET /admin/suggestions
  def index
    @suggestions = Suggestion.all.order(created_at: :desc)

    render json: @suggestions.as_json(
      include: { customer: { only: %i[id name email] } }
    )
  end

  # GET /admin/suggestions/:id
  def show
    render json: @suggestion.as_json(
      include: { customer: { only: %i[id name email phone] } }
    )
  end

  # PATCH/PUT /admin/suggestions/:id
  def update
    if @suggestion.update(suggestion_update_params)
      render json: @suggestion
    else
      render json: { errors: @suggestion.errors.full_messages }, status: :unprocessable_entity
    end
  end

  # GET /admin/suggestions/statistics
  def statistics
    # Total suggestions
    total_suggestions = Suggestion.count

    # Suggestions by status
    suggestions_by_status = {
      pending: Suggestion.where(status: Suggestion::PENDING).count,
      under_review: Suggestion.where(status: Suggestion::UNDER_REVIEW).count,
      implemented: Suggestion.where(status: Suggestion::IMPLEMENTED).count,
      rejected: Suggestion.where(status: Suggestion::REJECTED).count
    }

    # Suggestions by category
    suggestions_by_category = {
      menu: Suggestion.where(category: Suggestion::MENU).count,
      delivery: Suggestion.where(category: Suggestion::DELIVERY).count,
      app: Suggestion.where(category: Suggestion::APP).count,
      pricing: Suggestion.where(category: Suggestion::PRICING).count,
      timing: Suggestion.where(category: Suggestion::TIMING).count,
      other: Suggestion.where(category: Suggestion::OTHER).count
    }

    # Suggestions by rating
    suggestions_by_rating = {
      '1': Suggestion.where(rating: 1).count,
      '2': Suggestion.where(rating: 2).count,
      '3': Suggestion.where(rating: 3).count,
      '4': Suggestion.where(rating: 4).count,
      '5': Suggestion.where(rating: 5).count
    }

    # New suggestions this month
    new_suggestions_this_month = Suggestion.where('created_at >= ?', Time.zone.now.beginning_of_month).count

    # Monthly suggestions
    monthly_suggestions = Suggestion.where('created_at >= ?', 6.months.ago)
      .select("DATE_TRUNC('month', created_at) as month, COUNT(*) as suggestion_count")
      .group("DATE_TRUNC('month', created_at)")
      .order('month DESC')
      .map do |result|
        {
          month: result.month.strftime('%B %Y'),
          suggestion_count: result.suggestion_count
        }
      end

    render json: {
      total_suggestions:,
      suggestions_by_status:,
      suggestions_by_category:,
      suggestions_by_rating:,
      new_suggestions_this_month:,
      monthly_suggestions:
    }
  end

  private

  def set_suggestion
    @suggestion = Suggestion.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Suggestion not found' }, status: :not_found
  end

  def suggestion_update_params
    params.require(:suggestion).permit(:status)
  end
end
