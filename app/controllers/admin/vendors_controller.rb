class Admin::VendorsController < Admin::BaseController
  # GET /admin/vendors
  def index
    # Use includes to eager load attachments
    @vendors = Vendor.includes(
      :front_ghana_card_attachment, :front_ghana_card_blob,
      :back_ghana_card_attachment, :back_ghana_card_blob,
      :health_certificate_pic_attachment, :health_certificate_pic_blob,
      :food_certificate_pic_attachment, :food_certificate_pic_blob,
      :operation_license_pic_attachment, :operation_license_pic_blob
    ).order(created_at: :desc)

    render json: @vendors.map { |vendor|
      vendor_json = vendor.as_json(only: %i[id name email phone address operation_time closing_time validation_status])

      # Add attachment URLs
      vendor_json.merge(
        front_ghana_card_url: attachment_url(vendor.front_ghana_card),
        back_ghana_card_url: attachment_url(vendor.back_ghana_card),
        health_certificate_url: attachment_url(vendor.health_certificate_pic),
        food_certificate_url: attachment_url(vendor.food_certificate_pic),
        operation_license_url: attachment_url(vendor.operation_license_pic)
      )
    }
  end

  # GET /admin/vendors/:id
  def show
    # Eager load attachments and associations
    @vendor = Vendor.includes(
      :front_ghana_card_attachment, :front_ghana_card_blob,
      :back_ghana_card_attachment, :back_ghana_card_blob,
      :health_certificate_pic_attachment, :health_certificate_pic_blob,
      :food_certificate_pic_attachment, :food_certificate_pic_blob,
      :operation_license_pic_attachment, :operation_license_pic_blob,
      :foods, :transfers
    ).find(params[:id])

    vendor_json = @vendor.as_json(only: %i[id name email phone address operation_time closing_time validation_status])

    # Add attachment URLs
    vendor_json.merge!(
      front_ghana_card_url: attachment_url(@vendor.front_ghana_card),
      back_ghana_card_url: attachment_url(@vendor.back_ghana_card),
      health_certificate_url: attachment_url(@vendor.health_certificate_pic),
      food_certificate_url: attachment_url(@vendor.food_certificate_pic),
      operation_license_url: attachment_url(@vendor.operation_license_pic)
    )

    # Get vendor's foods - already eager loaded
    foods = @vendor.foods

    # Get vendor's orders - optimize the query
    vendor_food_ids = foods.map(&:id) # Use the already loaded foods

    # Use a more efficient query with proper indexing
    orders = Order.where('food_ids && ARRAY[?]::integer[]', vendor_food_ids)
      .distinct
      .order(created_at: :desc)
      .limit(10)

    # Get vendor's transfers (payouts) - already eager loaded
    transfers = @vendor.transfers.order(created_at: :desc).limit(10)

    render json: vendor_json.merge(
      foods: foods.map do |food|
        food.as_json(only: %i[id name description], methods: [:prices_array, :has_multiple_prices?, :min_price, :max_price, :price]).merge(
          food_image_url: food.food_image_url
        )
      end,
      recent_orders: orders.map do |order|
        order.as_json(only: %i[id status total_price created_at])
      end,
      recent_payouts: transfers.map do |transfer|
        transfer.as_json(only: %i[id amount service_fee status created_at])
      end
    )
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Vendor not found' }, status: :not_found
  end

  # PATCH/PUT /admin/vendors/:id/approve
  def approve
    @vendor = Vendor.find(params[:id])

    if @vendor.update(validation_status: 'verified')
      render json: {
        message: 'Vendor approved successfully',
        vendor: @vendor.as_json(only: %i[id name validation_status])
      }, status: :ok
    else
      render json: { errors: @vendor.errors.full_messages }, status: :unprocessable_entity
    end
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Vendor not found' }, status: :not_found
  end

  # PATCH/PUT /admin/vendors/:id/reject
  def reject
    @vendor = Vendor.find(params[:id])

    if @vendor.update(validation_status: 'rejected')
      render json: {
        message: 'Vendor rejected',
        vendor: @vendor.as_json(only: %i[id name validation_status])
      }, status: :ok
    else
      render json: { errors: @vendor.errors.full_messages }, status: :unprocessable_entity
    end
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Vendor not found' }, status: :not_found
  end

  # GET /admin/vendors/pending
  def pending
    # Use includes to eager load attachments
    @vendors = Vendor.includes(
      :front_ghana_card_attachment, :front_ghana_card_blob,
      :back_ghana_card_attachment, :back_ghana_card_blob,
      :health_certificate_pic_attachment, :health_certificate_pic_blob,
      :food_certificate_pic_attachment, :food_certificate_pic_blob,
      :operation_license_pic_attachment, :operation_license_pic_blob
    ).where(validation_status: 'not-verified').order(created_at: :desc)

    render json: @vendors.map { |vendor|
      vendor_json = vendor.as_json(only: %i[id name email phone address operation_time closing_time validation_status])

      # Add attachment URLs
      vendor_json.merge(
        front_ghana_card_url: attachment_url(vendor.front_ghana_card),
        back_ghana_card_url: attachment_url(vendor.back_ghana_card),
        health_certificate_url: attachment_url(vendor.health_certificate_pic),
        food_certificate_url: attachment_url(vendor.food_certificate_pic),
        operation_license_url: attachment_url(vendor.operation_license_pic)
      )
    }
  end

  # GET /admin/vendors/statistics
  def statistics
    # Use a single query to get counts by validation status
    vendors_counts = Vendor.group(:validation_status).count

    # Vendors by validation status
    vendors_by_status = {
      verified: vendors_counts['verified'] || 0,
      pending: vendors_counts['not-verified'] || 0,
      rejected: vendors_counts['rejected'] || 0
    }

    # New vendors this month
    new_vendors_this_month = Vendor.where('created_at >= ?', Time.zone.now.beginning_of_month).count

    # Top vendors by revenue
    top_vendors_by_revenue = Vendor.joins(:transfers)
      .where(transfers: { status: 'success' })
      .select('vendors.id, vendors.name, SUM(transfers.amount) as total_revenue')
      .group('vendors.id, vendors.name')
      .order('total_revenue DESC')
      .limit(5)

    render json: {
      vendors_by_status:,
      new_vendors_this_month:,
      top_vendors_by_revenue:
    }
  end

  private

  def attachment_url(attachment)
    attachment.attached? ? url_for(attachment) : nil
  end
end
