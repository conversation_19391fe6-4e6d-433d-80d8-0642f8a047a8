class Admin::PayoutsController < Admin::BaseController
  # GET /admin/payouts
  def index
    @transfers = Transfer.all.order(created_at: :desc)

    render json: @transfers.map { |transfer|
      recipient_name = if transfer.recipient_type == 'Vendor'
                         Vendor.find_by(id: transfer.recipient_id)&.name
                       elsif transfer.recipient_type == 'Rider'
                         Rider.find_by(id: transfer.recipient_id)&.name
                       else
                         'Unknown'
                       end

      {
        id: transfer.id,
        order_id: transfer.order_id,
        recipient_id: transfer.recipient_id,
        recipient_type: transfer.recipient_type,
        recipient_name:,
        amount: transfer.amount,
        service_fee: transfer.service_fee,
        status: transfer.status,
        created_at: transfer.created_at,
        updated_at: transfer.updated_at
      }
    }
  end
end
