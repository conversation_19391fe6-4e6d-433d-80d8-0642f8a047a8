class Admin::StatisticsController < Admin::BaseController
  # GET /admin/statistics/vendor_sales
  def vendor_sales
    render json: top_vendors_by_sales
  end

  # GET /admin/statistics/rider_deliveries
  def rider_deliveries
    render json: top_riders_by_deliveries
  end

  private

  # Get top vendors by sales amount, grouped by month
  def top_vendors_by_sales
    # Get only orders with received status from the last 6 months
    start_date = 6.months.ago.beginning_of_month

    # This query gets the total sales for each vendor by month
    # We join orders with foods and vendors to get the vendor information
    # Then we group by vendor and month to get the total sales
    vendor_sales = ActiveRecord::Base.connection.execute(<<-SQL
      WITH order_foods AS (
        SELECT#{' '}
          o.id AS order_id,
          o.created_at,
          o.status,
          f.id AS food_id,
          -- Use the first price from the JSON array (backward compatibility)
          (f.prices::json->>0)::float AS selected_price,
          f.vendor_id
        FROM orders o
        CROSS JOIN UNNEST(o.food_ids) AS food_id
        JOIN foods f ON f.id = food_id::integer
        WHERE o.status = 'received'
        AND o.created_at >= '#{start_date}'
      )
      SELECT#{' '}
        v.id AS vendor_id,
        v.name AS vendor_name,
        DATE_TRUNC('month', of.created_at) AS month,
        SUM(of.selected_price) AS total_sales,
        COUNT(DISTINCT of.order_id) AS order_count
      FROM order_foods of
      JOIN vendors v ON v.id = of.vendor_id
      GROUP BY v.id, v.name, DATE_TRUNC('month', of.created_at)
      ORDER BY month DESC, total_sales DESC
    SQL
                                                        )

    # Transform the results into a structured format
    results = {}

    vendor_sales.each do |row|
      # Convert timestamp to formatted month string
      month_value = row['month']

      # Handle different types of month values
      month = if month_value.is_a?(Time) || month_value.is_a?(DateTime)
                month_value.strftime('%B %Y')
              elsif month_value.nil?
                'Unknown Date'
              else
                begin
                  Date.parse(month_value.to_s).strftime('%B %Y')
                rescue StandardError
                  'Unknown Date'
                end
              end

      # Initialize the month if it doesn't exist
      results[month] ||= []

      # Add the vendor data to the month
      results[month] << {
        vendor_id: row['vendor_id'],
        vendor_name: row['vendor_name'],
        total_sales: row['total_sales'],
        order_count: row['order_count']
      }
    end

    # Convert the hash to an array of objects with month and vendors
    formatted_results = results.map do |month, vendors|
      {
        month:,
        vendors: vendors.sort_by { |v| -v[:total_sales].to_f }.take(10) # Top 10 vendors per month
      }
    end

    # Sort by month descending
    formatted_results.sort_by do |item|
      Date.parse(item[:month])
    rescue StandardError
      # If date parsing fails, put it at the end
      Date.new(1900, 1, 1)
    end.reverse
  end

  # Get top riders by delivery count and earnings, grouped by month
  def top_riders_by_deliveries
    # Get only orders with received status from the last 6 months
    start_date = 6.months.ago.beginning_of_month

    # This query gets the total deliveries and earnings for each rider by month
    # Since orders table doesn't have a delivery_fee column, we need to use the rider's pricing
    rider_deliveries = ActiveRecord::Base.connection.execute(<<-SQL
      SELECT#{' '}
        r.id AS rider_id,
        r.name AS rider_name,
        DATE_TRUNC('month', o.created_at) AS month,
        COUNT(o.id) AS delivery_count,
        SUM(CASE#{' '}
            WHEN o.delivery_address ILIKE '%dunkwa%' OR o.delivery_address ILIKE '%dunkwa-%' OR o.delivery_address ILIKE '%dunkwa on offin%' THEN r.within_dunkwa_price#{' '}
            WHEN o.delivery_address IS NULL THEN r.within_dunkwa_price -- Default to within_dunkwa_price if address is missing
            ELSE r.outside_dunkwa_price#{' '}
        END) AS total_earnings
      FROM orders o
      JOIN riders r ON r.id = o.rider_id
      WHERE o.status = 'received'
      AND o.created_at >= '#{start_date}'
      GROUP BY r.id, r.name, DATE_TRUNC('month', o.created_at)
      ORDER BY month DESC, total_earnings DESC
    SQL
                                                            )

    # Transform the results into a structured format
    results = {}

    rider_deliveries.each do |row|
      # Convert timestamp to formatted month string
      month_value = row['month']

      # Handle different types of month values
      month = if month_value.is_a?(Time) || month_value.is_a?(DateTime)
                month_value.strftime('%B %Y')
              elsif month_value.nil?
                'Unknown Date'
              else
                begin
                  Date.parse(month_value.to_s).strftime('%B %Y')
                rescue StandardError
                  'Unknown Date'
                end
              end

      # Initialize the month if it doesn't exist
      results[month] ||= []

      # Add the rider data to the month
      results[month] << {
        rider_id: row['rider_id'],
        rider_name: row['rider_name'],
        delivery_count: row['delivery_count'],
        total_earnings: row['total_earnings']
      }
    end

    # Convert the hash to an array of objects with month and riders
    formatted_results = results.map do |month, riders|
      {
        month:,
        riders: riders.sort_by { |r| -r[:total_earnings].to_f }.take(10) # Top 10 riders per month
      }
    end

    # Sort by month descending
    formatted_results.sort_by do |item|
      Date.parse(item[:month])
    rescue StandardError
      # If date parsing fails, put it at the end
      Date.new(1900, 1, 1)
    end.reverse
  end
end
