class PushSubscriptionsController < ApplicationController
  before_action :authenticate_user!, except: [:vapid_public_key]

  # GET /push_subscriptions/vapid_public_key
  def vapid_public_key
    public_key = Rails.application.credentials.vapid[:public_key]

    if public_key.blank?
      render json: { error: 'VAPID public key not configured' }, status: :service_unavailable
      return
    end

    render json: { vapid_public_key: public_key }
  end

  # POST /push_subscriptions
  def create
    # Validate required parameters
    unless subscription_params[:endpoint].present? && subscription_params[:p256dh].present? && subscription_params[:auth].present?
      render json: { error: 'Missing required subscription parameters' }, status: :bad_request
      return
    end

    # Find existing subscription with the same endpoint
    existing_subscription = PushSubscription.find_by(endpoint: subscription_params[:endpoint])

    if existing_subscription
      # Update existing subscription and reassign to current user
      existing_subscription.user = current_user
      if existing_subscription.update(subscription_params)
        render json: { message: 'Subscription updated successfully' }, status: :ok
      else
        render json: { error: existing_subscription.errors.full_messages.join(', ') }, status: :unprocessable_entity
      end
    else
      # Create new subscription
      subscription = PushSubscription.new(subscription_params)
      subscription.user = current_user

      if subscription.save
        render json: { message: 'Subscription created successfully' }, status: :created
      else
        render json: { error: subscription.errors.full_messages.join(', ') }, status: :unprocessable_entity
      end
    end
  rescue StandardError => e
    Rails.logger.error("Error creating push subscription: #{e.message}")
    render json: { error: 'Internal server error' }, status: :internal_server_error
  end

  # DELETE /push_subscriptions
  def destroy
    unless params[:endpoint].present?
      render json: { error: 'Endpoint parameter is required' }, status: :bad_request
      return
    end

    subscription = PushSubscription.find_by(endpoint: params[:endpoint])

    if subscription && subscription.user == current_user
      subscription.destroy
      render json: { message: 'Subscription deleted successfully' }, status: :ok
    else
      render json: { error: 'Subscription not found' }, status: :not_found
    end
  rescue StandardError => e
    Rails.logger.error("Error deleting push subscription: #{e.message}")
    render json: { error: 'Internal server error' }, status: :internal_server_error
  end

  private

  def subscription_params
    params.require(:subscription).permit(:endpoint, :p256dh, :auth)
  end

  def authenticate_user!
    # Use the same token extraction logic as in ApplicationController
    auth_token = extract_token_from_request

    # Log the headers for debugging
    Rails.logger.info("Request headers: #{request.headers.to_h.select { |k, _| k.start_with?('HTTP_') }.inspect}")
    Rails.logger.info("Authorization header: #{request.headers['Authorization'].inspect}")

    unless auth_token
      render json: { error: 'No authorization token provided' }, status: :unauthorized
      return
    end

    # Check for undefined or null token
    if %w[undefined null].include?(auth_token)
      Rails.logger.warn("Invalid token value: #{auth_token}")
      render json: { error: 'Invalid token value' }, status: :unauthorized
      return
    end

    # Use the same token decoding logic as in ApplicationController
    payload = decode_jwt_token(auth_token)

    unless payload
      render json: { error: 'Invalid token' }, status: :unauthorized
      return
    end

    # Try to find user by 'sub' claim (standard JWT) or 'id' claim (custom)
    user_id = payload['sub'] || payload['id']
    user_scope = payload['scp'] # Get the scope to determine user type

    # Find user based on JWT scope to avoid ID collisions between user types
    @current_user = case user_scope
                   when 'customer'
                     Customer.find_by(id: user_id)
                   when 'vendor'
                     Vendor.find_by(id: user_id)
                   when 'rider'
                     Rider.find_by(id: user_id)
                   when 'admin'
                     Admin.find_by(id: user_id)
                   else
                     # Fallback to old method if scope is missing (backward compatibility)
                     Rails.logger.warn("No scope found in JWT, using fallback method")
                     Customer.find_by(id: user_id) ||
                     Vendor.find_by(id: user_id) ||
                     Rider.find_by(id: user_id) ||
                     Admin.find_by(id: user_id)
                   end

    # If no user found, render unauthorized
    unless @current_user
      render json: { error: "#{user_scope&.capitalize || 'User'} not found with ID #{user_id}" }, status: :unauthorized
      return
    end

    Rails.logger.info("✅ User authenticated: #{@current_user.class.name} ##{@current_user.id} (#{@current_user.name})")
  end

  attr_reader :current_user

  def extract_token_from_request
    # Try to get token from Authorization header
    auth_header = request.headers['Authorization']

    if auth_header
      # Check if the token already has 'Bearer ' prefix
      token = if auth_header.start_with?('Bearer ')
                auth_header.gsub('Bearer ', '')
              else
                # If not, use it as is
                auth_header
              end
      return token
    end

    # Try to get token from params
    return params[:token] if params[:token].present?

    nil
  end

  def decode_jwt_token(token)
    # Try to decode with HS256 algorithm
    begin
      decoded = JWT.decode(token, Rails.application.credentials.devise_jwt_secret_key, true, { algorithm: 'HS256' })[0]
      return decoded
    rescue JWT::ExpiredSignature => e
      Rails.logger.error("JWT token expired: #{e.message}")
      # For expired tokens, we could redirect to login or refresh the token
      # For now, just log it and return nil
    rescue JWT::DecodeError => e
      Rails.logger.error("JWT decode error with HS256: #{e.message}")
    rescue StandardError => e
      Rails.logger.error("Other error decoding JWT with HS256: #{e.class.name} - #{e.message}")
    end

    nil
  end
end
