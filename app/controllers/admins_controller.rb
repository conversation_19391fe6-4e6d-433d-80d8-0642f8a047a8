class AdminsController < ApplicationController
  def index
    @admins = Admin.all
    render json: admin_with_avatar(@admins)
  end

  def show
    @admin = Admin.find(params[:id])
    render json: admin_with_avatar(@admin)
  rescue ActiveRecord::RecordInvalid => e
    render json: { errors: e.record.errors.full_messages }, status: :unprocessable_entity
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Admin not found' }, status: :not_found
  end

  def update
    @admin = Admin.find(params[:id])
    @admin.update!(admin_params)
    render json: admin_with_avatar(@admin)
  rescue ActiveRecord::RecordInvalid => e
    render json: { errors: e.record.errors.full_messages }, status: :unprocessable_entity
  rescue ActiveRecord::RecordNotFound
    render json: { errors: ['Admin not found'] }, status: :not_found
  end

  private

  def admin_with_avatar(admins)
    if admins.respond_to?(:each)
      admins.map do |admin|
        admin.as_json.merge(avatar_url: admin.avatar.attached? ? url_for(admin.avatar) : nil)
      end
    else
      admins.as_json.merge(avatar_url: admins.avatar.attached? ? url_for(admins.avatar) : nil)
    end
  end

  def admins_params
    params.require(:admin).permit(:name, :phone, :username, :role, :avatar)
  end
end
