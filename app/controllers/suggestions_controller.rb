class SuggestionsController < ApplicationController
  before_action :authenticate_user_from_token!, only: %i[index create]
  # GET /customers/:customer_id/suggestions
  def index
    @customer = Customer.find(params[:customer_id])

    if @current_customer && @current_customer.id == @customer.id
      @suggestions = Suggestion.where(customer: @customer).order(created_at: :desc)
      render json: @suggestions
    else
      render json: { error: 'Unauthorized' }, status: :unauthorized
    end
  end

  # POST /suggestions
  def create
    @suggestion = Suggestion.new(suggestion_params)
    # Associate with current customer if authenticated.
    @suggestion.customer = @current_customer if @current_customer

    if @suggestion.save
      render json: @suggestion, status: :created
    else
      render json: { errors: @suggestion.errors.full_messages }, status: :unprocessable_entity
    end
  end

  private

  def set_suggestion
    @suggestion = Suggestion.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Suggestion not found' }, status: :not_found
  end

  def suggestion_params
    params.require(:suggestion).permit(:name, :email, :category, :suggestion, :rating)
  end
end
