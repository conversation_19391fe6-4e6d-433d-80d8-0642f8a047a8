class RidersController < ApplicationController
  def index
    @riders = Rider.all
    render json: rider_with_attachments(@riders)
  end

  def show
    @rider = Rider.find(params[:id])
    render json: rider_with_attachments(@rider)
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Rider not found.' }, status: :not_found
  rescue ActiveRecord::RecordInvalid => e
    render json: { errors: e.record.errors.full_messages }, status: :unprocessable_entity
  end

  def update
    @rider = Rider.find(params[:id])
    @rider.update!(rider_params)
    render json: rider_with_attachments(@rider)
  rescue ActiveRecord::RecordNotFound
    render json: { errors: ['Rider not found'] }, status: :not_found
  rescue ActiveRecord::RecordInvalid => e
    render json: { errors: e.record.errors.full_messages }, status: :unprocessable_entity
  end

  # GET /riders/:id/statistics
  def statistics
    @rider = Rider.find(params[:id])

    # Get current date for calculations
    today = Date.today
    current_month_start = today.beginning_of_month
    current_month_end = today.end_of_month

    # Only include orders with 'received' status for revenue calculations
    completed_orders = Order.where(status: 'received', rider_id: @rider.id)

    # Monthly statistics
    monthly_deliveries = completed_orders.where(created_at: current_month_start..current_month_end).count

    # Calculate monthly revenue
    monthly_revenue = calculate_rider_revenue(@rider,
                                              completed_orders.where(created_at: current_month_start..current_month_end))

    # Calculate monthly unique customers
    monthly_customers = completed_orders.where(created_at: current_month_start..current_month_end)
      .distinct.pluck(:customer_id).count

    # Daily statistics
    daily_deliveries = completed_orders.where(created_at: today.beginning_of_day..today.end_of_day).count
    daily_revenue = calculate_rider_revenue(@rider,
                                            completed_orders.where(created_at: today.beginning_of_day..today.end_of_day))
    daily_customers = completed_orders.where(created_at: today.beginning_of_day..today.end_of_day)
      .distinct.pluck(:customer_id).count

    # Last 6 months revenue trend
    monthly_revenue_trend = []

    (0..5).each do |i|
      month_start = (today - i.months).beginning_of_month
      month_end = (today - i.months).end_of_month
      month_orders = completed_orders.where(created_at: month_start..month_end)
      revenue = calculate_rider_revenue(@rider, month_orders)

      monthly_revenue_trend.unshift({
                                      month: month_start.strftime('%B %Y'),
                                      revenue:
                                    })
    end

    render json: {
      monthly_statistics: {
        deliveries: monthly_deliveries,
        revenue: monthly_revenue,
        customers: monthly_customers
      },
      daily_statistics: {
        deliveries: daily_deliveries,
        revenue: daily_revenue,
        customers: daily_customers
      },
      revenue_trend: monthly_revenue_trend
    }
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Rider not found' }, status: :not_found
  end

  private

  # Calculate rider's revenue from orders
  def calculate_rider_revenue(rider, orders)
    total_revenue = 0

    orders.each do |order|
      # Determine if the delivery is within Dunkwa or outside
      is_within_dunkwa = order.delivery_address.to_s.downcase.include?('dunkwa')

      # Get the original delivery price (without service fee)
      original_price = if is_within_dunkwa
                         # Original price without service fee (2 cedis service fee)
                         rider.within_dunkwa_price - 2
                       else
                         # Original price without service fee (4 cedis service fee)
                         rider.outside_dunkwa_price - 4
                       end

      total_revenue += original_price
    end

    total_revenue
  end

  def rider_with_attachments(riders)
    if riders.respond_to?(:each)
      riders.map { |rider| rider_as_json(rider) }
    else
      rider_as_json(riders)
    end
  end

  def rider_as_json(rider)
    rider.as_json.merge(
      avatar_url: attachment_url(rider.avatar),
      drivers_license_front_url: attachment_url(rider.front_drivers_license),
      drivers_license_back_url: attachment_url(rider.back_drivers_license),
      ghana_card_front_url: attachment_url(rider.front_ghana_card),
      ghana_card_back_url: attachment_url(rider.back_ghana_card),
      moto_pic_url: attachment_url(rider.moto_pic),
      vehicle_registration_pic_url: attachment_url(rider.vehicle_registration_pic)
    )
  end

  def attachment_url(attachment)
    attachment.attached? ? url_for(attachment) : nil
  end

  def rider_params
    params.require(:rider).permit(:name, :phone, :username, :address, :vehicle_details, :within_dunkwa_price,
                                  :outside_dunkwa_price, :rider_status, :role, :avatar, :dob, :drivers_license_number,
                                  :ghana_card_number, :vehicle_registration_number, :validation_status,
                                  :account_name, :mobile_money_provider, :mobile_money_number, :paystack_recipient_code,
                                  :front_drivers_license, :back_drivers_license,
                                  :front_ghana_card, :back_ghana_card,
                                  :moto_pic, :vehicle_registration_pic)
  end
end
