class PaystackRecipientsController < ApplicationController
  before_action :authenticate_admin!

  def create_for_vendor
    vendor = Vendor.find(params[:vendor_id])
    create_recipient_for(vendor, 'Vendor')
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Vendor not found' }, status: :not_found
  end

  def create_for_rider
    rider = Rider.find(params[:rider_id])
    create_recipient_for(rider, 'Rider')
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Rider not found' }, status: :not_found
  end

  private

  def create_recipient_for(recipient, recipient_type)
    if recipient.paystack_recipient_code.present?
      render json: {
        message: "#{recipient_type} already has a Paystack recipient code",
        recipient_code: recipient.paystack_recipient_code
      }, status: :ok
      return
    end

    if missing_required_params?
      render json: { error: 'Account name, mobile money provider, and mobile money number are required' },
             status: :unprocessable_entity
      return
    end

    # Update recipient with payment details
    recipient.update(recipient_params)

    # Create Paystack recipient
    service = PaystackService.new
    response = service.create_transfer_recipient(
      recipient.account_name,
      recipient.mobile_money_number,
      recipient.mobile_money_provider
    )

    handle_paystack_response(recipient, response)
  end

  def handle_paystack_response(recipient, response)
    if response['status']
      recipient.update(paystack_recipient_code: response['data']['recipient_code'])
      render json: {
        message: 'Paystack recipient created successfully',
        recipient_code: recipient.paystack_recipient_code
      }, status: :ok
    else
      render json: { error: response['message'] }, status: :unprocessable_entity
    end
  end

  def missing_required_params?
    params[:account_name].blank? ||
      params[:mobile_money_provider].blank? ||
      params[:mobile_money_number].blank?
  end

  def recipient_params
    params.permit(:account_name, :mobile_money_provider, :mobile_money_number)
  end
end
