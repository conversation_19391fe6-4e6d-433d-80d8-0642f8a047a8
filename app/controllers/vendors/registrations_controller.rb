class Vendors::RegistrationsController < Devise::RegistrationsController
  include RackSessionsFix
  respond_to :json

  before_action :map_mobile_money_name_to_account_name, only: :create
  before_action :normalize_mobile_provider, only: :create

  private

  def respond_with(resource, _opts = {})
    if request.method == 'POST' && resource.persisted?

      # Associate categories if category_ids are provided
      if params[:vendor][:category_names].present?
        category_names = params[:vendor][:category_names].reject(&:blank?)
        categories = category_names.map do |name|
          # Find or create category
          Category.find_or_create_by(name: name.strip)
        end
        resource.categories = categories
      end

      render json: {
        status: { code: 200, message: 'Signed up successfully.' },
        data: resource.as_json(include: :categories)
      }
    else
      render json: {
        status: { code: 422, message: 'Validation failed.' },
        errors: resource.errors.full_messages
      }, status: :unprocessable_entity
    end
  end

  def map_mobile_money_name_to_account_name
    return unless params[:vendor] && params[:vendor][:mobile_money_name].present?

    params[:vendor][:account_name] = params[:vendor].delete(:mobile_money_name)
  end

  def normalize_mobile_provider
    # Handle mobile_money_provider case sensitivity
    return unless params[:vendor] && params[:vendor][:mobile_money_provider].present?

    # Convert to uppercase for validation
    case params[:vendor][:mobile_money_provider].downcase
    when 'mtn'
      params[:vendor][:mobile_money_provider] = 'MTN'
    when 'vodafone'
      params[:vendor][:mobile_money_provider] = 'Vodafone'
    when 'airteltigo'
      params[:vendor][:mobile_money_provider] = 'AirtelTigo'
    end
  end

  def sign_up_params
    params.require(:vendor).permit(:name, :phone, :username, :operation_time, :vendor_description, :closing_time,
                                   :address, :email, :password, :password_confirmation, :health_certificate_pic,
                                   :back_ghana_card, :front_ghana_card, :digital_address, :account_name,
                                   :mobile_money_provider, :food_certificate_pic, :avatar, :ghana_card_number,
                                   :operation_license_pic, :mobile_money_number, category_names: [])
  end
end
