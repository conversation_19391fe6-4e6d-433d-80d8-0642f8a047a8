class Riders::RegistrationsController < Devise::RegistrationsController
  include RackSessionsFix
  respond_to :json

  before_action :map_mobile_money_name_to_account_name, only: :create
  before_action :normalize_mobile_provider, only: :create

  private

  def respond_with(resource, _opts = {})
    if request.method == 'POST' && resource.persisted?
      render json: {
        status: { code: 200, message: 'Signed up successfully.' },
        data: resource.as_json
      }
    else
      render json: {
        status: { code: 422, message: 'Validation failed.' },
        errors: resource.errors.full_messages
      }, status: :unprocessable_entity
    end
  end

  def map_mobile_money_name_to_account_name
    return unless params[:rider] && params[:rider][:mobile_money_name].present?

    params[:rider][:account_name] = params[:rider].delete(:mobile_money_name)
  end

  def normalize_mobile_provider
    # Handle mobile_money_provider case sensitivity
    return unless params[:rider] && params[:rider][:mobile_money_provider].present?

    # Convert to uppercase for validation
    case params[:rider][:mobile_money_provider].downcase
    when 'mtn'
      params[:rider][:mobile_money_provider] = 'MTN'
    when 'vodafone'
      params[:rider][:mobile_money_provider] = 'Vodafone'
    when 'airteltigo'
      params[:rider][:mobile_money_provider] = 'AirtelTigo'
    end
  end

  def sign_up_params
    params.require(:rider).permit(:name, :phone, :categories, :username, :address, :vehicle_details,
                                  :outside_dunkwa_price, :rider_status, :email, :ghana_card_number,
                                  :password, :password_confirmation, :account_name, :mobile_money_provider,
                                  :mobile_money_number, :front_drivers_license, :back_drivers_license, :avatar,
                                  :front_ghana_card, :back_ghana_card, :moto_pic, :vehicle_registration_pic,
                                  :dob, :drivers_license_number, :vehicle_registration_number,
                                  :within_dunkwa_price, :validation_status)
  end
end
