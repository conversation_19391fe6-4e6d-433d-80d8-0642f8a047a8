class CartsController < ApplicationController
  def index
    @cart_items = Cart.where(customer_id: params[:customer_id]).includes(:food)

    if @cart_items.empty?
      render json: { message: 'Cart is empty' }, status: :ok
    else
      # Format cart items with consistent food data structure
      formatted_cart_items = @cart_items.map do |cart_item|
        cart_json = cart_item.as_json(methods: [:selected_price, :total_price, :price_option_label])

        # Format food data to match the standalone food endpoint structure
        food = cart_item.food
        cart_json['food'] = {
          id: food.id,
          name: food.name,
          description: food.description,
          vendor_id: food.vendor_id,
          created_at: food.created_at,
          updated_at: food.updated_at,
          prices: food.prices_array,                    # ✅ Consistent key name
          food_image_url: food.food_image.attached? ? url_for(food.food_image) : nil,
          has_multiple_prices: food.has_multiple_prices?, # ✅ Consistent key name (no ?)
          min_price: food.min_price,
          max_price: food.max_price,
          price: food.price                             # ✅ Backward compatibility
        }

        cart_json
      end

      render json: formatted_cart_items
    end
  end

  def create
    @cart_items = Cart.new(cart_params)

    if @cart_items.save
      render json: @cart_items, status: :created
    else
      render json: { errors: @cart_items.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def destroy
    @cart_item = Cart.find_by(id: params[:id], customer_id: params[:customer_id])

    if @cart_item
      @cart_item.destroy
      render json: { message: 'Cart item deleted successfully' }, status: :ok
    else
      render json: { message: 'Cart item not found' }, status: :not_found
    end
  end

  def update
    @cart_item = Cart.find_by(id: params[:id], customer_id: params[:customer_id])

    if @cart_item&.update(cart_params)
      render json: @cart_item, status: :ok
    else
      render json: { errors: @cart_item ? @cart_item.errors.full_messages : ['Cart item not found'] },
             status: :unprocessable_entity
    end
  end

  def clear
    @cart_items = Cart.where(customer_id: params[:customer_id])

    if @cart_items.destroy_all
      render json: { message: 'All cart items cleared successfully' }, status: :ok
    else
      render json: { message: 'Failed to clear cart items' }, status: :unprocessable_entity
    end
  end

  private

  def cart_params
    # Handle the case where food_id is sent as a nested object
    if params[:cart][:food_id].is_a?(Hash) && params[:cart][:food_id][:id].present?
      params[:cart][:food_id] = params[:cart][:food_id][:id]
    end

    params.require(:cart).permit(:customer_id, :food_id, :quantity, :selected_price_index)
  end
end
