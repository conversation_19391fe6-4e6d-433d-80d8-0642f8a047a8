class VendorsController < ApplicationController
  def index
    @vendors = Vendor.includes(:foods, :categories).all
    render json: vendor_with_attachments(@vendors)
  end

  def show
    @vendor = Vendor.includes(:foods).find(params[:id])
    render json: vendor_with_attachments(@vendor)
  rescue ActiveRecord::RecordInvalid => e
    render json: { errors: e.record.errors.full_messages }, status: :unprocessable_entity
  end

  def update
    @vendor = Vendor.includes(:foods).find(params[:id])

    # Handle mobile_money_name if it's sent from frontend
    if params[:vendor] && params[:vendor][:mobile_money_name].present?
      # Use mobile_money_name as account_name
      params[:vendor][:account_name] = params[:vendor][:mobile_money_name]
    end

    # Handle mobile_money_provider case sensitivity
    if params[:vendor] && params[:vendor][:mobile_money_provider].present?
      # Convert to uppercase for validation
      case params[:vendor][:mobile_money_provider].downcase
      when 'mtn'
        params[:vendor][:mobile_money_provider] = 'MTN'
      when 'vodafone'
        params[:vendor][:mobile_money_provider] = 'Vodafone'
      when 'airteltigo'
        params[:vendor][:mobile_money_provider] = 'AirtelTigo'
      end
    end

    # Get clean parameters
    clean_params = vendor_params

    # Try to update the vendor
    if @vendor.update(clean_params)
      render json: vendor_with_attachments(@vendor)
    else
      render json: { errors: @vendor.errors.full_messages }, status: :unprocessable_entity
    end
  rescue ActiveRecord::RecordNotFound
    render json: { errors: ['Vendor not found'] }, status: :not_found
  rescue StandardError => e
    render json: { errors: [e.message] }, status: :unprocessable_entity
  end

  # GET /vendors/:id/statistics
  def statistics
    @vendor = Vendor.find(params[:id])

    # Get current date for calculations
    today = Date.today
    current_month_start = today.beginning_of_month
    current_month_end = today.end_of_month

    # Get vendor's food IDs
    vendor_food_ids = @vendor.foods.pluck(:id)

    # Only include orders with 'received' status for revenue calculations
    completed_orders = Order.where(status: 'received')
      .where('food_ids::integer[] && ARRAY[?]::integer[]', vendor_food_ids)

    # Monthly statistics
    monthly_orders = completed_orders.where(created_at: current_month_start..current_month_end).count

    # Calculate monthly revenue
    monthly_revenue = calculate_vendor_revenue(@vendor,
                                               completed_orders.where(created_at: current_month_start..current_month_end))

    # Calculate monthly unique customers
    monthly_customers = completed_orders.where(created_at: current_month_start..current_month_end)
      .distinct.pluck(:customer_id).count

    # Daily statistics
    daily_orders = completed_orders.where(created_at: today.beginning_of_day..today.end_of_day).count
    daily_revenue = calculate_vendor_revenue(@vendor,
                                             completed_orders.where(created_at: today.beginning_of_day..today.end_of_day))
    daily_customers = completed_orders.where(created_at: today.beginning_of_day..today.end_of_day)
      .distinct.pluck(:customer_id).count

    # Last 6 months revenue trend
    monthly_revenue_trend = []

    (0..5).each do |i|
      month_start = (today - i.months).beginning_of_month
      month_end = (today - i.months).end_of_month
      month_orders = completed_orders.where(created_at: month_start..month_end)
      revenue = calculate_vendor_revenue(@vendor, month_orders)

      monthly_revenue_trend.unshift({
                                      month: month_start.strftime('%B %Y'),
                                      revenue:
                                    })
    end

    render json: {
      monthly_statistics: {
        orders: monthly_orders,
        revenue: monthly_revenue,
        customers: monthly_customers
      },
      daily_statistics: {
        orders: daily_orders,
        revenue: daily_revenue,
        customers: daily_customers
      },
      revenue_trend: monthly_revenue_trend
    }
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Vendor not found' }, status: :not_found
  end

  private

  # Calculate vendor's revenue from orders
  def calculate_vendor_revenue(vendor, orders)
    total_revenue = 0

    orders.each do |order|
      # Get the vendor's food IDs as strings to match the food_ids format
      vendor_food_ids = vendor.foods.pluck(:id).map(&:to_s)

      # Calculate revenue for foods in this order that belong to this vendor
      order.food_ids.each_with_index do |food_id, index|
        # Convert food_id to string for comparison if it's not already
        food_id_str = food_id.to_s

        # Skip if this food doesn't belong to the vendor
        next unless vendor_food_ids.include?(food_id_str)

        begin
          # Convert back to integer for Food.find
          food = Food.find(food_id_str.to_i)
          quantity = order.quantities[index] || 1

          # Double-check that the food belongs to this vendor
          next unless food.vendor_id == vendor.id

          # Original price without service fee
          original_price = food.price / 1.05 # Remove 5% service fee
          item_revenue = original_price * quantity
          total_revenue += item_revenue
        rescue ActiveRecord::RecordNotFound
          next
        end
      end
    end

    total_revenue
  end

  def vendor_with_attachments(vendors)
    if vendors.respond_to?(:each)
      vendors.map { |vendor| vendor_as_json(vendor) }
    else
      vendor_as_json(vendors)
    end
  end

  def vendor_as_json(vendor)
    vendor_json = vendor.as_json(
      include: {
        foods: {
          methods: [:food_image_url],
          except: %i[created_at updated_at]
        }
      }
    ).merge(
      vendor_url: attachment_url(vendor.avatar)
    )

    # Format each food with price information like FoodsController does
    if vendor_json['foods']
      vendor_json['foods'] = vendor_json['foods'].map do |food_json|
        food = vendor.foods.find(food_json['id'])
        food_json.merge!(
          food_image_url: food.food_image.attached? ? url_for(food.food_image) : nil,
          prices: food.prices_array,
          has_multiple_prices: food.has_multiple_prices?,
          min_price: food.min_price,
          max_price: food.max_price,
          # Keep backward compatibility
          price: food.price
        )
      end
    end

    vendor_json
  end

  def attachment_url(attachment)
    attachment.attached? ? url_for(attachment) : nil
  end

  def vendor_params
    # Parse time fields before permitting parameters
    parse_time_fields

    params.require(:vendor).permit(
      :name, :phone, :username, :address, :email, :operation_time, :closing_time,
      :digital_address, :ghana_card_number, :vendor_description, :validation_status, :account_name,
      :mobile_money_provider, :mobile_money_number, :paystack_recipient_code,
      :role, :avatar, :front_ghana_card, :back_ghana_card, :health_certificate_pic,
      :food_certificate_pic, :operation_license_pic, :avatar
    )
  end

  # Parse time strings from frontend to time objects in Ghana time zone (UTC)
  def parse_time_fields
    # Parse operation_time
    parse_time_field(:operation_time)

    # Parse closing_time
    parse_time_field(:closing_time)
  end

  # Helper method to parse a single time field
  def parse_time_field(field_name)
    return unless params[:vendor][field_name].present? && params[:vendor][field_name].is_a?(String)

    begin
      # Parse time string in format "HH:MM" or "HH:MM:SS"
      time_str = params[:vendor][field_name]

      # Parse the time string and store it in UTC (Ghana is UTC+0)
      # If the time string includes a date part, extract just the time
      if time_str.include?('T')
        # Handle ISO format like "2000-01-01T10:00:00.000+00:00"
        datetime = Time.parse(time_str)
        # Create a new time object with just the time component
        params[:vendor][field_name] = Time.utc(2000, 1, 1, datetime.hour, datetime.min, datetime.sec)
      else
        # Handle simple time format like "10:00"
        hour, minute = time_str.split(':').map(&:to_i)
        params[:vendor][field_name] = Time.utc(2000, 1, 1, hour, minute, 0)
      end
    rescue ArgumentError => e
      Rails.logger.error("Error parsing #{field_name}: #{e.message}")
    end
  end
end
