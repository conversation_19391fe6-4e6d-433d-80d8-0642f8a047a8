class FoodsController < ApplicationController
  def index
    @foods = Food.where(vendor_id: params[:vendor_id])
    render json: food_with_attachments(@foods), status: :ok
  end

  def create
    @food = Food.new(food_params)

    @food.save!
    render json: @food, status: :created
  rescue ActiveRecord::RecordInvalid => e
    render json: { errors: e.record.errors.full_messages }, status: :unprocessable_entity
  end

  def update
    @food = Food.find(params[:id])
    @food.update!(food_params)
    render json: food_with_attachments(@food)
  rescue ActiveRecord::RecordNotFound
    render json: { errors: ['Food not found'] }, status: :not_found
  rescue ActiveRecord::RecordInvalid => e
    render json: { errors: e.record.errors.full_messages }, status: :unprocessable_entity
  end

  def destroy
    # scope deletion to the vendor so a food can't be deleted across vendors by id alone
    @food = Food.where(vendor_id: params[:vendor_id]).find(params[:id])

    # optionally remove the attached image (runs asynchronously)
    @food.food_image.purge_later if @food.food_image.attached?

    @food.destroy!
    head :no_content
  rescue ActiveRecord::RecordNotFound
    render json: { errors: ['Food not found'] }, status: :not_found
  rescue StandardError => e
    render json: { errors: [e.message] }, status: :unprocessable_entity
  end

  private

  def food_with_attachments(foods)
    if foods.respond_to?(:each)
      foods.map do |food|
        food_json = food.as_json
        food_json.merge!(
          food_image_url: food.food_image.attached? ? url_for(food.food_image) : nil,
          prices: food.prices_array,
          has_multiple_prices: food.has_multiple_prices?,
          min_price: food.min_price,
          max_price: food.max_price,
          # Keep backward compatibility
          price: food.price
        )
      end
    else
      food_json = foods.as_json
      food_json.merge!(
        food_image_url: foods.food_image.attached? ? url_for(foods.food_image) : nil,
        prices: foods.prices_array,
        has_multiple_prices: foods.has_multiple_prices?,
        min_price: foods.min_price,
        max_price: foods.max_price,
        # Keep backward compatibility
        price: foods.price
      )
    end
  end

  def food_params
    permitted_params = params.require(:food).permit(:name, :description, :food_image, :vendor_id, prices: [])

    # Handle both single price and array prices
    if params[:food][:price].present?
      # Single price provided - convert to array
      permitted_params[:prices] = [params[:food][:price].to_f]
    elsif params[:food][:prices].present?
      # Array prices provided
      permitted_params[:prices] = params[:food][:prices].map(&:to_f)
    end

    permitted_params
  end
end
