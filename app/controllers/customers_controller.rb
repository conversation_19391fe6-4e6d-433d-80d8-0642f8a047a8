class CustomersController < ApplicationController
  def index
    @customers = Customer.all
    render json: customer_with_avatar(@customers)
  end

  def show
    @customer = Customer.find(params[:id])
    render json: customer_with_avatar(@customer)
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Customer not found' }, status: :not_found
  rescue ActiveRecord::RecordInvalid => e
    render json: { errors: e.record.errors.full_messages }, status: :unprocessable_entity
  end

  def update
    @customer = Customer.find(params[:id])
    @customer.update!(customer_params)
    render json: customer_with_avatar(@customer)
  rescue ActiveRecord::RecordNotFound
    render json: { errors: ['Customer not found'] }, status: :not_found
  rescue ActiveRecord::RecordInvalid => e
    render json: { errors: e.record.errors.full_messages }, status: :unprocessable_entity
  end

  # GET /customers/:id/statistics
  def statistics
    @customer = Customer.find(params[:id])

    # Get current date for calculations
    today = Date.today
    current_month_start = today.beginning_of_month
    current_month_end = today.end_of_month

    # Get all orders for this customer
    all_orders = @customer.orders

    # Get orders with 'received' status (completed orders)
    completed_orders = all_orders.where(status: Order::RECEIVED)

    # Calculate statistics
    total_orders = all_orders.count
    completed_order_count = completed_orders.count
    pending_order_count = all_orders.where(status: Order::PENDING).count
    in_progress_order_count = all_orders.where.not(status: [Order::PENDING, Order::RECEIVED, Order::CANCELED,
                                                            Order::NOT_RECEIVED]).count

    # Calculate total spent
    total_spent = completed_orders.sum(:total_price)

    # Calculate monthly statistics
    monthly_orders = all_orders.where(created_at: current_month_start..current_month_end).count
    monthly_spent = completed_orders.where(created_at: current_month_start..current_month_end).sum(:total_price)

    # Get order history with pagination
    page = (params[:page] || 1).to_i
    per_page = (params[:per_page] || 10).to_i

    # Get paginated orders
    paginated_orders = all_orders.order(created_at: :desc).offset((page - 1) * per_page).limit(per_page)

    # Get all foods for these orders with their vendors
    food_ids = paginated_orders.flat_map(&:food_ids)
    foods = Food.where(id: food_ids).includes(:vendor)

    # Get all vendors for these orders
    vendor_ids = foods.pluck(:vendor_id).uniq
    vendors = Vendor.where(id: vendor_ids)

    # Format order history
    order_history = paginated_orders.map do |order|
      # Get foods for this specific order
      order_foods = foods.select { |food| order.food_ids.include?(food.id.to_s) }

      # Get vendors for this specific order
      order_vendor_ids = order_foods.pluck(:vendor_id).uniq
      order_vendors = vendors.select { |v| order_vendor_ids.include?(v.id) }

      order.as_json(include: {
                      rider: { only: %i[id name phone] }
                    }).merge(
                      foods: order_foods.map do |food|
                        food.as_json(only: %i[id name description price vendor_id]).merge(
                          food_image_url: food.food_image_url,
                          vendor_name: food.vendor.name
                        )
                      end,
                      vendors: order_vendors.map do |vendor|
                        {
                          id: vendor.id,
                          name: vendor.name,
                          address: vendor.address,
                          phone: vendor.phone,
                          email: vendor.email
                        }
                      end
                    )
    end

    # Calculate total pages
    total_pages = (all_orders.count.to_f / per_page).ceil

    # Return statistics and order history
    render json: {
      statistics: {
        total_orders:,
        completed_orders: completed_order_count,
        pending_orders: pending_order_count,
        in_progress_orders: in_progress_order_count,
        total_spent:,
        monthly_orders:,
        monthly_spent:
      },
      order_history: {
        orders: order_history,
        pagination: {
          current_page: page,
          per_page:,
          total_pages:,
          total_orders: all_orders.count
        }
      }
    }
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Customer not found' }, status: :not_found
  rescue StandardError => e
    render json: { error: e.message }, status: :unprocessable_entity
  end

  private

  def customer_with_avatar(customers)
    if customers.respond_to?(:each)
      customers.map do |customer|
        customer.as_json.merge(avatar_url: customer.avatar.attached? ? url_for(customer.avatar) : nil)
      end
    else
      customers.as_json.merge(avatar_url: customers.avatar.attached? ? url_for(customers.avatar) : nil)
    end
  end

  def customer_params
    params.require(:customer).permit(:name, :phone, :username, :address, :role, :avatar)
  end
end
