class Admins::PasswordsController < Devise::PasswordsController
  include RackSessionsFix
  respond_to :json

  # POST /admin/password
  def create
    self.resource = resource_class.send_reset_password_instructions(resource_params)
    yield resource if block_given?

    if successfully_sent?(resource)
      render json: {
        status: { code: 200, message: 'Password reset instructions sent successfully.' },
        data: { email: resource.email }
      }
    else
      render json: {
        status: { code: 422, message: 'Failed to send password reset instructions.' },
        errors: resource.errors.full_messages
      }, status: :unprocessable_entity
    end
  end

  # PUT /admin/password
  def update
    self.resource = resource_class.reset_password_by_token(resource_params)
    yield resource if block_given?

    if resource.errors.empty?
      resource.unlock_access! if unlockable?(resource)
      if Devise.sign_in_after_reset_password
        flash_message = resource.active_for_authentication? ? :updated : :updated_not_active
        set_flash_message!(:notice, flash_message)
        resource.after_database_authentication
        sign_in(resource_name, resource)
      end
      render json: {
        status: { code: 200, message: 'Password reset successfully.' },
        data: resource.as_json
      }
    else
      set_minimum_password_length
      render json: {
        status: { code: 422, message: 'Failed to reset password.' },
        errors: resource.errors.full_messages
      }, status: :unprocessable_entity
    end
  end

  protected

  def after_resetting_password_path_for(_resource)
    # Return a path or URL where the user should be redirected after resetting password
    # For API, this might not be used, but it's required by Devise
    root_path
  end

  def after_sending_reset_password_instructions_path_for(_resource_name)
    # Return a path or URL where the user should be redirected after requesting password reset
    # For API, this might not be used, but it's required by Devise
    root_path
  end

  private

  def resource_params
    params.require(:admin).permit(:email, :password, :password_confirmation, :reset_password_token)
  end
end
