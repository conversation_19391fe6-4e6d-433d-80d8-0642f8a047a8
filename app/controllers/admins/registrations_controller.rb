class Admins::RegistrationsController < Devise::RegistrationsController
  include RackSessionsFix
  respond_to :json

  private

  def respond_with(resource, _opts = {})
    if request.method == 'POST' && resource.persisted?
      render json: {
        status: { code: 200, message: 'Signed up successfully.' },
        data: resource.as_json
      }
    else
      render json: {
        status: { code: 422, message: 'Validation failed.' },
        errors: resource.errors.full_messages
      }, status: :unprocessable_entity
    end
  end

  def sign_up_params
    params.require(:admin).permit(:name, :phone, :username, :email, :password, :password_confirmation)
  end
end
