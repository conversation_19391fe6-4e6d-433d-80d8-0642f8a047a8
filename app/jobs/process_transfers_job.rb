class ProcessTransfersJob < ApplicationJob
  queue_as :default

  # Maximum number of retry attempts for delayed transfers
  MAX_RETRY_ATTEMPTS = 10
  # Base delay between retries (in hours) - longer intervals for settlement waiting
  BASE_RETRY_DELAY = 2

  def perform(order_id, retry_attempt = 0)
    Rails.logger.info("ProcessTransfers<PERSON>ob started for order #{order_id} (attempt #{retry_attempt + 1})")

    order = Order.find(order_id)
    Rails.logger.info("Found order #{order_id} with status: #{order.status}")

    # Check if transfers already exist for this order
    existing_transfers = order.transfers.count
    Rails.logger.info("Order #{order_id} already has #{existing_transfers} existing transfers")

    if existing_transfers.positive?
      Rails.logger.warn("Order #{order_id} already has transfers. Skipping transfer initiation.")
      return
    end

    # Check if vendor and rider have paystack recipient codes
    validate_recipients(order)

    # Calculate total required amount for all transfers
    total_required_amount = calculate_total_required_amount(order)
    Rails.logger.info("Total required amount for order #{order_id}: #{total_required_amount} GHS")

    # Check if balance is sufficient
    balance_check = check_balance_sufficiency(total_required_amount)

    unless balance_check['status']
      Rails.logger.error("Failed to check balance for order #{order_id}: #{balance_check['message']}")
      schedule_retry(order_id, retry_attempt, 'balance_check_failed')
      return
    end

    if balance_check['sufficient']
      Rails.logger.info("Balance is sufficient for order #{order_id}. Proceeding with transfers.")
      initiate_transfers(order)
    else
      Rails.logger.warn("Insufficient balance for order #{order_id}. Current balance: #{balance_check['current_balance']} GHS, Required: #{balance_check['required_amount']} GHS")

      if retry_attempt < MAX_RETRY_ATTEMPTS
        schedule_retry(order_id, retry_attempt, 'insufficient_balance')
      else
        Rails.logger.error("Maximum retry attempts reached for order #{order_id}. Balance still insufficient after #{MAX_RETRY_ATTEMPTS} attempts.")
        # Create failed transfers to track the issue
        create_failed_transfers_for_insufficient_balance(order, balance_check)
      end
    end
  rescue ActiveRecord::RecordNotFound
    Rails.logger.error("ProcessTransfersJob failed for order #{order_id}: Order not found")
  rescue StandardError => e
    Rails.logger.error("ProcessTransfersJob failed for order #{order_id}: #{e.message}")
    Rails.logger.error(e.backtrace.join("\n"))

    schedule_retry(order_id, retry_attempt, 'general_error') if retry_attempt < MAX_RETRY_ATTEMPTS
  end

  private

  def validate_recipients(order)
    order.vendors.each do |vendor|
      if vendor.paystack_recipient_code.blank?
        Rails.logger.warn("Vendor #{vendor.id} (#{vendor.name}) has no paystack_recipient_code")
      else
        Rails.logger.info("Vendor #{vendor.id} (#{vendor.name})
        has paystack_recipient_code: #{vendor.paystack_recipient_code}")
      end
    end

    if order.rider.paystack_recipient_code.blank?
      Rails.logger.warn("Rider #{order.rider.id} (#{order.rider.name}) has no paystack_recipient_code")
    else
      Rails.logger.info("Rider #{order.rider.id} (#{order.rider.name}) has paystack_recipient_code: #{order.rider.paystack_recipient_code}")
    end
  end

  def calculate_total_required_amount(order)
    calculator = OrderPriceCalculator.new(order)
    total_amount = 0.0

    # Add vendor amounts
    order.vendors.each do |vendor|
      vendor_amount = calculator.original_vendor_price(vendor)
      vendor_service_fee = calculator.vendor_service_fees(vendor)
      total_amount += vendor_amount + vendor_service_fee
    end

    # Add rider amount
    rider_amount = calculator.original_rider_price
    rider_service_fee = calculator.rider_service_fee
    total_amount += rider_amount + rider_service_fee

    total_amount
  end

  def check_balance_sufficiency(required_amount)
    service = PaystackService.new
    service.balance_sufficient?(required_amount)
  end

  def initiate_transfers(order)
    Rails.logger.info("Initiating transfers for order #{order.id}")
    result = order.initiate_transfers
    Rails.logger.info("ProcessTransfersJob completed for order #{order.id}. Result: #{result.inspect}")
  end

  def schedule_retry(order_id, current_attempt, _reason)
    # Calculate delay with exponential backoff
    delay_hours = BASE_RETRY_DELAY * (2**current_attempt)
    # Cap the delay at 24 hours
    delay_hours = [delay_hours, 24].min

    Rails.logger.info("Scheduling retry for order #{order_id} in #{delay_hours} hours (attempt #{current_attempt + 2})")

    ProcessTransfersJob
      .set(wait: delay_hours.hours)
      .perform_later(order_id, current_attempt + 1)
  end

  def create_failed_transfers_for_insufficient_balance(order, _balance_check)
    Rails.logger.error("Creating failed transfers for order #{order.id} due to insufficient balance")

    calculator = OrderPriceCalculator.new(order)

    # Create failed transfers for vendors
    order.vendors.each do |vendor|
      amount = calculator.original_vendor_price(vendor)
      service_fee = calculator.vendor_service_fees(vendor)

      order.transfers.create!(
        recipient: vendor,
        amount:,
        service_fee:,
        status: Transfer::FAILED
      )
    end

    # Create failed transfer for rider
    if order.rider
      amount = calculator.original_rider_price
      service_fee = calculator.rider_service_fee

      order.transfers.create!(
        recipient: order.rider,
        amount:,
        service_fee:,
        status: Transfer::FAILED
      )
    end

    Rails.logger.error("Created #{order.transfers.count} failed transfers for order #{order.id}
    due to insufficient balance")
  end
end
