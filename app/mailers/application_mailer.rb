class ApplicationMailer < ActionMailer::Base
  default from: Rails.application.credentials.dig(:email, :from_address) || '<EMAIL>'
  layout 'mailer'

  # Set default email settings
  default reply_to: Rails.application.credentials.dig(:email, :reply_to) || '<EMAIL>'

  private

  # Helper method to get the application name
  def app_name
    'EaseFood'
  end

  # Helper method to get support email
  def support_email
    Rails.application.credentials.dig(:email, :support) || '<EMAIL>'
  end
end
