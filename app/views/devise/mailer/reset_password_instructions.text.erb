EaseFood - Password Reset Request
=====================================

Hello <%= @resource.name || @resource.username %>,

We received a request to reset your password for your EaseFood account.

If you made this request, please visit the following link to reset your password:

<%= edit_password_url(@resource, reset_password_token: @token) %>

IMPORTANT SECURITY INFORMATION:
- This link will expire in 6 hours for security reasons
- If you didn't request this password reset, please ignore this email
- Your password won't change until you access the link above and create a new one

If you have any questions or need assistance, please contact our support team.

Best regards,
The EaseFood Team

---
This email was sent to <%= @resource.email %>
© <%= Date.current.year %> EaseFood. All rights reserved.

If you're having trouble with the link above, copy and paste the URL into your web browser.
