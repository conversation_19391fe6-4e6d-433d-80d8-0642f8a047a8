class Order < ApplicationRecord
  belongs_to :customer
  belongs_to :rider
  belongs_to :order_group, optional: true
  has_one :recipient, dependent: :destroy
  has_one :payment, dependent: :destroy
  has_many :transfers, dependent: :destroy
  accepts_nested_attributes_for :recipient

  validate :food_ids_must_exist
  validates :food_ids, presence: true
  validate :price_indexes_must_match_food_ids

  # Status values
  PENDING = 'pending'.freeze
  CONFIRMED = 'confirmed'.freeze
  DELIVERED = 'delivered'.freeze
  RECEIVED = 'received'.freeze
  NOT_RECEIVED = 'not_received'.freeze
  CANCELED = 'canceled'.freeze

  # Additional status values used in the frontend
  PROCESSING = 'processing'.freeze
  READY_FOR_PICKUP = 'ready_for_pickup'.freeze
  OUT_FOR_DELIVERY = 'out_for_delivery'.freeze
  DELAYED = 'delayed'.freeze

  # This method retrieves all associated food items
  def foods
    Food.where(id: food_ids)
  end

  # Set default status to "pending" before creating the order
  before_create :set_default_status

  # Calculate the original food price (without service fees)
  def original_food_price
    calculator.original_food_price
  end

  # Calculate the food service fees
  def food_service_fees
    calculator.food_service_fees
  end

  # Get the original rider price (without service fee)
  def original_rider_price
    calculator.original_rider_price
  end

  # Get the rider service fee
  def rider_service_fee
    calculator.rider_service_fee
  end

  # Get a price calculator for this order
  def calculator
    @calculator ||= OrderPriceCalculator.new(self)
  end

  # Get vendors associated with this order
  def vendors
    Vendor.joins(:foods).where(foods: { id: food_ids }).distinct
  end

  # Get vendor IDs associated with this order
  def vendor_ids
    foods.pluck(:vendor_id).uniq
  end

  # Check if a specific vendor is associated with this order
  def has_vendor?(vendor_id)
    vendor_ids.include?(vendor_id.to_i)
  end

  # Calculate the original price for a specific vendor (without service fees)
  def original_vendor_price(vendor)
    calculator.original_vendor_price(vendor)
  end

  # Calculate service fees for a specific vendor
  def vendor_service_fees(vendor)
    calculator.vendor_service_fees(vendor)
  end

  # Mark order as delivered and schedule transfers for next day if customer confirmed
  def mark_as_delivered!(customer_confirmed: false)
    # If this is a customer confirmation, schedule transfers for next day
    if customer_confirmed && [DELIVERED, RECEIVED].include?(status)
      transaction do
        # If the status is already RECEIVED, we don't need to update it again
        update!(status: RECEIVED) if status == DELIVERED

        # Schedule transfer for next day at 5:00 AM Ghana time
        schedule_transfer_for_next_day
      end
      return true
    end

    # For rider updates, only allow marking as delivered if order is confirmed
    unless status == CONFIRMED
      Rails.logger.warn("Cannot mark order #{id} as delivered - not in confirmed state (current: #{status})")
      return false
    end

    # Mark as delivered (for rider updates)
    transaction do
      update!(status: DELIVERED)
    end
    true
  rescue StandardError => e
    Rails.logger.error("Error marking order as delivered: #{e.message}")
    false
  end

  # Schedule transfer for next day at 5:00 AM Ghana time
  def schedule_transfer_for_next_day
    # Calculate next day at 5:00 AM Ghana time
    ghana_timezone = ActiveSupport::TimeZone['Africa/Accra']
    now = ghana_timezone.now
    
    # Always schedule for tomorrow at 5:00 AM, regardless of current time
    # This ensures we wait for Paystack's next-day settlement
    scheduled_time = ghana_timezone.local(now.year, now.month, now.day + 1, 5, 0, 0)

    Rails.logger.info("Scheduling transfer for order #{id} at #{scheduled_time} (Ghana time) - next day settlement")
    
    # Check if Sidekiq is available and use it, otherwise fallback to immediate processing
    if Order.sidekiq_available?
      ProcessTransfersJob.set(wait_until: scheduled_time).perform_later(id)
      Rails.logger.info("Scheduled ProcessTransfersJob for order #{id} at #{scheduled_time}")
    else
      Rails.logger.warn("Sidekiq not available. Processing transfer immediately for order #{id}")
      # Fallback: process immediately if Sidekiq is not available
      # This ensures the transfer still happens, even if not at the optimal time
      ProcessTransfersJob.new.perform(id)
    end
  end

  # Initiate transfers to vendors and rider
  def initiate_transfers
    transfer_service = OrderTransferService.new(self)
    transfer_service.initiate_transfers
  end

  # Check if Sidekiq is available
  def self.sidekiq_available?
    # Try to connect to Redis and test if we can enqueue jobs
    client = Sidekiq::Client.new
    # Test if we can connect to Redis by trying to get the Redis connection
    client.redis { |conn| conn.ping }
    true
  rescue StandardError => e
    Rails.logger.warn("Sidekiq not available: #{e.message}")
    false
  end

  private

  def set_default_status
    self.status ||= PENDING
  end

  def food_ids_must_exist
    invalid_food_ids = food_ids.reject { |id| Food.exists?(id) }
    return unless invalid_food_ids.any?

    errors.add(:food_ids, "contains invalid food IDs: #{invalid_food_ids.join(', ')}")
  end

  def price_indexes_must_match_food_ids
    return if price_indexes.blank? || food_ids.blank?

    if price_indexes.length != food_ids.length
      errors.add(:price_indexes, "must have the same length as food_ids")
    end

    # Validate that each price index is valid for its corresponding food
    food_ids.each_with_index do |food_id, index|
      next if price_indexes[index].nil?

      food = Food.find_by(id: food_id)
      next unless food

      price_index = price_indexes[index]
      if price_index < 0 || price_index >= food.prices_array.length
        errors.add(:price_indexes, "invalid price index #{price_index} for food #{food_id}")
      end
    end
  end
end
