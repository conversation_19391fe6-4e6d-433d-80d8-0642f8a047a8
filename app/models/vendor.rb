class Vendor < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable,
         :jwt_authenticatable, jwt_revocation_strategy: JwtBlacklist,
                               authentication_keys: [:username]

  include PaystackRecipient

  has_many :foods, dependent: :destroy
  has_many :vendor_categories, dependent: :destroy
  has_many :categories, through: :vendor_categories
  has_many :orders, through: :foods
  has_many :transfers, as: :recipient, dependent: :destroy
  has_many :push_subscriptions, as: :user, dependent: :destroy

  before_create :set_default_role
  validates :username, presence: true, uniqueness: true
  validates :name, :phone, :operation_time, :closing_time, presence: true

  has_one_attached :front_ghana_card
  has_one_attached :back_ghana_card
  has_one_attached :health_certificate_pic
  has_one_attached :food_certificate_pic
  has_one_attached :operation_license_pic
  has_one_attached :avatar

  # Check if the vendor is currently open
  def open?
    return false unless operation_time && closing_time

    # Get current time in Ghana time zone (UTC+0)
    # Ghana is in UTC+0, so we can use UTC time directly
    current_time = Time.now.utc

    # Create Time objects for today with the operation and closing times
    # This ensures we're comparing times on the same day
    today = Date.today

    # Extract hours and minutes from the stored time objects
    opening_hour = operation_time.hour
    opening_min = operation_time.min
    closing_hour = closing_time.hour
    closing_min = closing_time.min

    # Create Time objects for today with the operation and closing times
    today_opening = Time.utc(today.year, today.month, today.day, opening_hour, opening_min, 0)
    today_closing = Time.utc(today.year, today.month, today.day, closing_hour, closing_min, 0)

    # Handle cases where closing time is on the next day (e.g., open until 2 AM)
    if closing_hour < opening_hour || (closing_hour == opening_hour && closing_min < opening_min)
      # Vendor closes after midnight
      tomorrow_closing = today_closing + 1.day

      # Check if current time is between opening time today and closing time tomorrow
      is_open = current_time.between?(today_opening, tomorrow_closing)
    else
      # Normal case: vendor opens and closes on the same day
      is_open = current_time.between?(today_opening, today_closing)
    end

    is_open
  end

  # Serialize the vendor object
  def as_json(options = {})
    is_open_now = open?

    # Merge with base JSON
    super.merge(
      is_open: is_open_now
    )
  end

  private

  def set_default_role
    self.role ||= 'vendor'
  end
end
