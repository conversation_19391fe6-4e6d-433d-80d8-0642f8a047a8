class ServiceFeeConfig < ApplicationRecord
  validates :key, presence: true, uniqueness: true
  validates :value, presence: true, numericality: { greater_than_or_equal_to: 0 }

  # Define constants for fee keys
  VENDOR_FEE_PERCENTAGE = 'vendor_fee_percentage'.freeze
  RIDER_WITHIN_DUNKWA_FEE = 'rider_within_dunkwa_fee'.freeze
  RIDER_OUTSIDE_DUNKWA_FEE = 'rider_outside_dunkwa_fee'.freeze
  PLATFORM_FEE_PERCENTAGE = 'platform_fee_percentage'.freeze

  # Get a service fee by key
  def self.get(key)
    config = find_by(key:)
    config&.value
  end

  # Set a service fee by key
  def self.set(key, value, description = nil)
    config = find_or_initialize_by(key:)
    config.value = value
    config.description = description if description.present?
    config.save
  end

  # Get all service fees as a hash
  def self.all_fees
    all.each_with_object({}) do |config, hash|
      hash[config.key] = config.value
    end
  end
end
