class Rider < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable,
         :jwt_authenticatable, jwt_revocation_strategy: JwtBlacklist,
                               authentication_keys: [:username]

  include PaystackRecipient

  has_many :orders
  has_many :transfers, as: :recipient, dependent: :destroy
  has_many :push_subscriptions, as: :user, dependent: :destroy

  before_create :set_default_role
  before_validation :add_service_fees

  validates :username, presence: true, uniqueness: true
  validates :name, :phone, :within_dunkwa_price, :ghana_card_number, presence: true

  has_one_attached :avatar
  has_one_attached :front_drivers_license
  has_one_attached :back_drivers_license
  has_one_attached :front_ghana_card
  has_one_attached :back_ghana_card
  has_one_attached :moto_pic
  has_one_attached :vehicle_registration_pic

  # Get the original within Dunkwa price (without service fee) for rider payments
  def original_within_dunkwa_price
    (within_dunkwa_price - ServiceFees::RIDER_WITHIN_DUNKWA).round(2) if within_dunkwa_price.present?
  end

  # Get the original outside Dunkwa price (without service fee) for rider payments
  def original_outside_dunkwa_price
    (outside_dunkwa_price - ServiceFees::RIDER_OUTSIDE_DUNKWA).round(2) if outside_dunkwa_price.present?
  end

  # Get the service fee amount for within Dunkwa
  def within_dunkwa_service_fee
    ServiceFees::RIDER_WITHIN_DUNKWA
  end

  # Get the service fee amount for outside Dunkwa
  def outside_dunkwa_service_fee
    ServiceFees::RIDER_OUTSIDE_DUNKWA
  end

  private

  def set_default_role
    self.role ||= 'rider'
  end

  # Add service fees to delivery prices
  def add_service_fees
    # For new records, always add the service fees
    if new_record?
      add_service_fee_to_within_dunkwa_price
      add_service_fee_to_outside_dunkwa_price
    else
      # For existing records, only add fees if prices changed
      add_service_fee_to_within_dunkwa_price if within_dunkwa_price_changed?
      add_service_fee_to_outside_dunkwa_price if outside_dunkwa_price_changed?
    end
  end

  # Add service fee to within_dunkwa_price
  def add_service_fee_to_within_dunkwa_price
    return unless within_dunkwa_price.present?

    self.within_dunkwa_price = (within_dunkwa_price + ServiceFees::RIDER_WITHIN_DUNKWA).round(2)
  end

  # Add service fee to outside_dunkwa_price
  def add_service_fee_to_outside_dunkwa_price
    return unless outside_dunkwa_price.present?

    self.outside_dunkwa_price = (outside_dunkwa_price + ServiceFees::RIDER_OUTSIDE_DUNKWA).round(2)
  end
end
