class PushSubscription < ApplicationRecord
  # Associations
  belongs_to :user, polymorphic: true

  # Validations
  validates :endpoint, presence: true, uniqueness: true
  validates :p256dh, presence: true
  validates :auth, presence: true
  validates :user_type, presence: true
  validates :user_id, presence: true

  # Convert to web-push subscription format
  def to_web_push_subscription
    {
      endpoint:,
      keys: {
        p256dh:,
        auth:
      }
    }
  end
end
