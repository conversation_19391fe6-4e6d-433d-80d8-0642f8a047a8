class Food < ApplicationRecord
  include Rails.application.routes.url_helpers

  belongs_to :vendor
  has_many :carts

  validates :name, presence: true
  validates :prices, presence: true
  validate :prices_must_be_valid_array

  has_one_attached :food_image

  # Add 10% service fee to the prices before saving
  before_save :add_service_fee, if: :prices_changed?

  # This method retrieves all orders that include this food item
  def orders
    Order.where('food_ids @> ARRAY[?]::bigint[]', id)
  end

  def food_image_url
    return nil unless food_image.attached?

    Rails.application.routes.url_helpers.rails_blob_url(food_image, host: 'https://server1.easefood.org')
  end

  # Get prices as array
  def prices_array
    return [] if prices.blank?
    JSON.parse(prices)
  rescue JSON::ParserError
    []
  end

  # Set prices from array
  def prices_array=(array)
    self.prices = array.to_json
  end

  # Get the original prices (without service fee) for vendor payments
  def original_prices
    prices_array.map { |price| (price / (1 + ServiceFees::VENDOR_PERCENTAGE)).round(2) }
  end

  # Get the service fee amounts
  def service_fees
    prices_array.zip(original_prices).map { |price, original| (price - original).round(2) }
  end

  # Backward compatibility: get first price as single price
  def price
    prices_array.first || 0.0
  end

  # Backward compatibility: set single price (converts to array)
  def price=(value)
    self.prices_array = [value.to_f]
  end

  # Get the original price (without service fee) for vendor payments - backward compatibility
  def original_price
    original_prices.first || 0.0
  end

  # Get the service fee amount - backward compatibility
  def service_fee
    service_fees.first || 0.0
  end

  # Check if food has multiple price options
  def has_multiple_prices?
    prices_array.length > 1
  end

  # Get minimum price
  def min_price
    prices_array.min || 0.0
  end

  # Get maximum price
  def max_price
    prices_array.max || 0.0
  end

  private

  # Validate that prices is a valid array of numbers
  def prices_must_be_valid_array
    return if prices.blank?

    begin
      parsed_prices = JSON.parse(prices)
      unless parsed_prices.is_a?(Array) && parsed_prices.all? { |p| p.is_a?(Numeric) && p > 0 }
        errors.add(:prices, 'must be an array of positive numbers')
      end
    rescue JSON::ParserError
      errors.add(:prices, 'must be valid JSON array')
    end
  end

  # Add service fee to all prices
  def add_service_fee
    return if prices.blank?

    begin
      current_prices = JSON.parse(prices)

      # Apply service fee to each price if not already applied
      updated_prices = current_prices.map do |price|
        fee_multiplier = (1 + ServiceFees::VENDOR_PERCENTAGE) * 100
        # Check if price already includes fee
        if ((price * 100).to_i % fee_multiplier.to_i).zero?
          price # Already has fee
        else
          (price * (1 + ServiceFees::VENDOR_PERCENTAGE)).round(2)
        end
      end

      self.prices = updated_prices.to_json
    rescue JSON::ParserError
      # If prices is not valid JSON, treat as single price and convert
      if prices.is_a?(Numeric)
        self.prices = [(prices * (1 + ServiceFees::VENDOR_PERCENTAGE)).round(2)].to_json
      end
    end
  end
end
