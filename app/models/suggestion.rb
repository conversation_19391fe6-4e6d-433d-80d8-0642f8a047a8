class Suggestion < ApplicationRecord
  belongs_to :customer, optional: true

  validates :name, presence: true
  validates :email, presence: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :category, presence: true
  validates :suggestion, presence: true, length: { minimum: 10 }
  validates :rating, presence: true, numericality: { only_integer: true, greater_than: 0, less_than_or_equal_to: 5 }

  # Status values
  PENDING = 'pending'.freeze
  UNDER_REVIEW = 'under_review'.freeze
  IMPLEMENTED = 'implemented'.freeze
  REJECTED = 'rejected'.freeze

  # Category values
  MENU = 'menu'.freeze
  DELIVERY = 'delivery'.freeze
  APP = 'app'.freeze
  PRICING = 'pricing'.freeze
  TIMING = 'timing'.freeze
  OTHER = 'other'.freeze

  # Set default status to "pending" before creating the suggestion
  before_create :set_default_status

  private

  def set_default_status
    self.status ||= PENDING
  end
end
