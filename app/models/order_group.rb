class OrderGroup < ApplicationRecord
  belongs_to :customer
  has_many :orders, dependent: :destroy

  validates :payment_reference, presence: true, uniqueness: true
  validates :total_amount, presence: true, numericality: { greater_than: 0 }

  # Check if this is a multi-vendor order group
  def multi_vendor?
    orders.joins(:foods).select('DISTINCT foods.vendor_id').count > 1
  end

  # Get all vendors involved in this order group
  def vendors
    Vendor.joins(:foods).where(foods: { id: orders.flat_map(&:food_ids) }).distinct
  end
end
