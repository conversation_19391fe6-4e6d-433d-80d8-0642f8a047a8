class Customer < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable,
         :jwt_authenticatable, jwt_revocation_strategy: JwtBlacklist,
                               authentication_keys: [:username]

  has_one :cart
  has_many :orders
  has_many :complaints
  has_many :suggestions
  has_many :push_subscriptions, as: :user, dependent: :destroy

  before_create :set_default_role

  validates :name, presence: true
  validates :phone, presence: true
  validates :username, presence: true, uniqueness: true

  has_one_attached :avatar

  private

  def set_default_role
    self.role ||= 'customer'
  end
end
