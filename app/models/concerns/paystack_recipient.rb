module PaystackRecipient
  extend ActiveSupport::Concern

  included do
    after_create :create_paystack_recipient, if: lambda {
      account_name.present? && mobile_money_provider.present? && mobile_money_number.present?
    }

    validates :mobile_money_provider, inclusion: { in: %w[MTN Vodafone AirtelTigo], allow_blank: true }
    validates :mobile_money_number, format: { with: /\A\+?[0-9]{10,15}\z/, message: 'must be a valid phone number' },
                                    allow_blank: true
  end

  private

  def create_paystack_recipient
    return if paystack_recipient_code.present?

    begin
      service = PaystackService.new
      response = service.create_transfer_recipient(account_name, mobile_money_number, mobile_money_provider)

      if response['status']
        update_column(:paystack_recipient_code, response['data']['recipient_code'])
      else
        Rails.logger.error("Failed to create Paystack recipient for #{self.class.name.downcase} #{id}: #{response['message']}")
      end
    rescue StandardError => e
      Rails.logger.error("Error creating Paystack recipient for #{self.class.name.downcase} #{id}: #{e.message}")
    end
  end
end
