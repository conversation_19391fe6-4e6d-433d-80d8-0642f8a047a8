class Cart < ApplicationRecord
  belongs_to :customer
  belongs_to :food

  validates :customer_id, presence: true
  validates :food_id, presence: true
  validates :quantity, presence: true, numericality: { greater_than: 0 }
  validates :selected_price_index, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validate :selected_price_index_must_be_valid

  # Get the selected price based on the price index
  def selected_price
    return food.price if food.prices_array.empty?

    food.prices_array[selected_price_index] || food.price
  end

  # Get the total price for this cart item
  def total_price
    selected_price * quantity
  end

  # Get a label for the selected price option (e.g., "Small", "Medium", "Large")
  def price_option_label
    return nil unless food.has_multiple_prices?

    size_labels = ['Small', 'Medium', 'Large', 'XL', 'XXL']
    size_labels[selected_price_index] || "Option #{selected_price_index + 1}"
  end

  private

  def selected_price_index_must_be_valid
    return unless food && selected_price_index

    if selected_price_index >= food.prices_array.length
      errors.add(:selected_price_index, "is invalid for this food item")
    end
  end
end
