class ServiceFees
  # Default values if not set in the database
  DEFAULT_VENDOR_FEE_PERCENTAGE = 10.0
  DEFAULT_RIDER_WITHIN_DUNKWA = 2.0
  DEFAULT_RIDER_OUTSIDE_DUNKWA = 4.0
  DEFAULT_PLATFORM_FEE_PERCENTAGE = 1.0

  # Get vendor fee percentage
  def self.vendor_fee_percentage
    ServiceFeeConfig.get(ServiceFeeConfig::VENDOR_FEE_PERCENTAGE) || DEFAULT_VENDOR_FEE_PERCENTAGE
  end

  # Get rider within Dunkwa fee
  def self.rider_within_dunkwa_fee
    ServiceFeeConfig.get(ServiceFeeConfig::RIDER_WITHIN_DUNKWA_FEE) || DEFAULT_RIDER_WITHIN_DUNKWA
  end

  # Get rider outside Dunkwa fee
  def self.rider_outside_dunkwa_fee
    ServiceFeeConfig.get(ServiceFeeConfig::RIDER_OUTSIDE_DUNKWA_FEE) || DEFAULT_RIDER_OUTSIDE_DUNKWA
  end

  # Get platform fee percentage
  def self.platform_fee_percentage
    ServiceFeeConfig.get(ServiceFeeConfig::PLATFORM_FEE_PERCENTAGE) || DEFAULT_PLATFORM_FEE_PERCENTAGE
  end

  # Calculate vendor service fee for a given price
  def self.calculate_vendor_fee(price)
    (price * vendor_fee_percentage / 100.0).round(2)
  end

  # Calculate platform fee for a given price
  def self.calculate_platform_fee(price)
    (price * platform_fee_percentage / 100.0).round(2)
  end

  # For backward compatibility
  VENDOR_FEE_PERCENTAGE = DEFAULT_VENDOR_FEE_PERCENTAGE
  RIDER_WITHIN_DUNKWA = DEFAULT_RIDER_WITHIN_DUNKWA
  RIDER_OUTSIDE_DUNKWA = DEFAULT_RIDER_OUTSIDE_DUNKWA
end
