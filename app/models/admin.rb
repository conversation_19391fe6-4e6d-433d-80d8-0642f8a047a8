class Admin < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable,
         :jwt_authenticatable, jwt_revocation_strategy: JwtBlacklist,
                               authentication_keys: [:username]

  validates :name, presence: true
  validates :phone, presence: true
  validates :username, presence: true, uniqueness: true

  before_create :set_default_role

  has_one_attached :avatar

  private

  def set_default_role
    self.role ||= 'admin'
  end
end
