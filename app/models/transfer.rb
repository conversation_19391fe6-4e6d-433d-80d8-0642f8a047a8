class Transfer < ApplicationRecord
  belongs_to :order
  belongs_to :recipient, polymorphic: true

  validates :amount, presence: true, numericality: { greater_than: 0 }
  validates :service_fee, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :status, presence: true
  validates :paystack_transfer_code, uniqueness: true, allow_blank: true
  validates :paystack_reference, uniqueness: true, allow_blank: true

  # Status values
  PENDING = 'pending'.freeze
  PROCESSING = 'processing'.freeze
  SUCCESS = 'success'.freeze
  FAILED = 'failed'.freeze
  REVERSED = 'reversed'.freeze

  # Scopes
  scope :pending, -> { where(status: PENDING) }
  scope :processing, -> { where(status: PROCESSING) }
  scope :successful, -> { where(status: SUCCESS) }
  scope :failed, -> { where(status: FAILED) }
  scope :reversed, -> { where(status: REVERSED) }
  scope :for_vendor, -> { where(recipient_type: 'Vendor') }
  scope :for_rider, -> { where(recipient_type: 'Rider') }

  # Calculate total service fees
  def self.total_service_fees
    successful.sum(:service_fee)
  end

  # Calculate service fees by recipient type
  def self.vendor_service_fees
    successful.for_vendor.sum(:service_fee)
  end

  def self.rider_service_fees
    successful.for_rider.sum(:service_fee)
  end

  # Get recipient name for API responses
  def recipient_name
    if recipient_type == 'Vendor'
      Vendor.find_by(id: recipient_id)&.name
    elsif recipient_type == 'Rider'
      Rider.find_by(id: recipient_id)&.name
    else
      'Unknown'
    end
  end
end
