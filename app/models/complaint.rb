class Complaint < ApplicationRecord
  belongs_to :customer, optional: true

  validates :name, presence: true
  validates :email, presence: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :order_number, presence: true
  validates :category, presence: true
  validates :complaint, presence: true, length: { minimum: 10 }

  # Status values
  PENDING = 'pending'.freeze
  UNDER_REVIEW = 'under_review'.freeze
  RESOLVED = 'resolved'.freeze
  REJECTED = 'rejected'.freeze

  # Category values
  ORDER = 'order'.freeze
  DELIVERY = 'delivery'.freeze
  FOOD = 'food'.freeze
  APP = 'app'.freeze
  PAYMENT = 'payment'.freeze
  OTHER = 'other'.freeze

  # Set default status to "pending" before creating the complaint
  before_create :set_default_status

  # Attachments
  has_many_attached :attachments

  private

  def set_default_status
    self.status ||= PENDING
  end
end
