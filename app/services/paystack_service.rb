class PaystackService
  require 'httparty'

  BASE_URL = 'https://api.paystack.co'.freeze

  def initialize
    @api_key = Rails.application.credentials.paystack[:secret_key]
    @headers = {
      'Authorization' => "Bearer #{@api_key}",
      'Content-Type' => 'application/json'
    }
  end

  # Create a transfer recipient for mobile money
  # @param account_name [String] The name associated with the mobile money account
  # @param phone [String] The mobile money phone number
  # @param provider [String] The mobile money provider (MTN, Vodafone, AirtelTigo)
  # @return [Hash] The response from the Paystack API
  def create_transfer_recipient(account_name, phone, provider)
    url = "#{BASE_URL}/transferrecipient"

    formatted_phone = format_phone_number(phone)
    provider_code = map_provider_code(provider)

    body = {
      type: 'mobile_money',
      name: account_name,
      account_number: formatted_phone,
      bank_code: provider_code,
      currency: 'GHS'
    }.to_json

    Rails.logger.info("Creating Paystack recipient - Account Name: #{account_name},
    Phone: #{formatted_phone}, Provider: #{provider_code}")
    Rails.logger.info("Paystack create recipient request body: #{body}")

    begin
      response = HTTParty.post(url, headers: @headers, body:, timeout: 30)
      response_data = JSON.parse(response.body)

      # Log the full response from Paystack
      Rails.logger.info("Paystack create recipient response: #{response_data.inspect}")

      # Log success/failure specifically
      if response_data['status']
        Rails.logger.info("Successfully created Paystack recipient. Code: #{response_data.dig('data',
                                                                                              'recipient_code')}")
      else
        Rails.logger.error("Failed to create Paystack recipient: #{response_data['message']}")
      end

      # Return the parsed response
      response_data
    rescue HTTParty::Error, JSON::ParserError, Net::OpenTimeout => e
      Rails.logger.error("Paystack API error: #{e.message}")
      { 'status' => false, 'message' => 'Failed to connect to payment gateway' }
    end
  end

  # Verify a transfer recipient
  # @param recipient_code [String] The recipient code to verify
  # @return [Hash] The response from the Paystack API
  def verify_transfer_recipient(recipient_code)
    url = "#{BASE_URL}/transferrecipient/#{recipient_code}"

    begin
      response = HTTParty.get(url, headers: @headers, timeout: 30)
      JSON.parse(response.body)
    rescue HTTParty::Error, JSON::ParserError, Net::OpenTimeout => e
      Rails.logger.error("Paystack API error: #{e.message}")
      { 'status' => false, 'message' => 'Failed to connect to payment gateway' }
    end
  end

  # Initialize a transfer
  # @param amount [Float] The amount to transfer in Ghana cedis
  # @param recipient_code [String] The recipient code to transfer to
  # @param reason [String, nil] The reason for the transfer
  # @return [Hash] The response from the Paystack API
  def initialize_transfer(amount, recipient_code, reason = nil)
    url = "#{BASE_URL}/transfer"

    # Convert amount to kobo/pesewas (multiply by 100)
    amount_in_pesewas = (amount * 100).to_i

    body = {
      source: 'balance',
      amount: amount_in_pesewas,
      recipient: recipient_code,
      currency: 'GHS',
      reason: reason || 'Payment for services'
    }.to_json

    Rails.logger.info("Paystack transfer request - URL: #{url}")
    Rails.logger.info("Paystack transfer request body: #{body}")
    Rails.logger.info("Amount: #{amount} GHS (#{amount_in_pesewas} pesewas), Recipient: #{recipient_code}")

    begin
      response = HTTParty.post(url, headers: @headers, body:, timeout: 30)
      response_data = JSON.parse(response.body)

      Rails.logger.info("Paystack transfer response: #{response_data.inspect}")

      response_data
    rescue HTTParty::Error, JSON::ParserError, Net::OpenTimeout => e
      Rails.logger.error("Paystack API error: #{e.message}")
      { 'status' => false, 'message' => 'Failed to connect to payment gateway' }
    end
  end

  # Check Paystack balance
  # @return [Hash] The response from the Paystack API with balance information
  def check_balance
    url = "#{BASE_URL}/balance"

    Rails.logger.info("Checking Paystack balance - URL: #{url}")
    Rails.logger.info("Using API key: #{@api_key[0..10]}...") # Log first 10 chars of API key

    begin
      response = HTTParty.get(url, headers: @headers, timeout: 30)
      response_data = JSON.parse(response.body)

      Rails.logger.info("Paystack balance response: #{response_data.inspect}")
      Rails.logger.info("Response status code: #{response.code}")

      if response_data['status']
        # Fix: data is an array, not a hash
        balances = response_data['data'] || []
        Rails.logger.info("All available balances: #{balances.inspect}")

        ghs_balance_hash = balances.find { |b| b['currency'] == 'GHS' }
        balance = ghs_balance_hash ? ghs_balance_hash['balance'] : 0
        currency = ghs_balance_hash ? ghs_balance_hash['currency'] : 'GHS'

        Rails.logger.info("Found GHS balance: #{balance} #{currency}")
        Rails.logger.info("Current Paystack balance: #{balance} #{currency}")
        # Attach the extracted balance and currency for downstream use
        response_data['extracted_balance'] = balance
        response_data['extracted_currency'] = currency
      else
        Rails.logger.error("Failed to check Paystack balance: #{response_data['message']}")
      end

      response_data
    rescue HTTParty::Error, JSON::ParserError, Net::OpenTimeout => e
      Rails.logger.error("Paystack API error checking balance: #{e.message}")
      { 'status' => false, 'message' => 'Failed to connect to payment gateway' }
    end
  end

  # Check if balance is sufficient for a given amount
  # @param required_amount [Float] The amount required in Ghana cedis
  # @return [Hash] The response with balance check result
  def balance_sufficient?(required_amount)
    balance_response = check_balance

    unless balance_response['status']
      return {
        'status' => false,
        'sufficient' => false,
        'message' => "Failed to check balance: #{balance_response['message']}",
        'error' => 'balance_check_failed'
      }
    end

    # Use the extracted balance if available, otherwise fallback to old logic
    current_balance = balance_response['extracted_balance'] || 0
    # Convert balance from pesewas to cedis (divide by 100)
    current_balance_cedis = current_balance / 100.0

    # Convert required amount to pesewas for comparison
    required_amount_pesewas = (required_amount * 100).to_i

    sufficient = current_balance >= required_amount_pesewas

    Rails.logger.info("Balance check - Required: #{required_amount} GHS (#{required_amount_pesewas}
    pesewas), Available: #{current_balance_cedis} GHS (#{current_balance} pesewas), Sufficient: #{sufficient}")

    {
      'status' => true,
      'sufficient' => sufficient,
      'current_balance' => current_balance_cedis,
      'required_amount' => required_amount,
      'message' => sufficient ? 'Balance is sufficient' : 'Insufficient balance'
    }
  end

  private

  # Format the phone number for Paystack API
  def format_phone_number(phone)
    # Remove any non-numeric characters
    clean_phone = phone.gsub(/\D/, '')

    # If it's already in Ghana format (starts with 0), return as-is
    unless clean_phone.start_with?('0') && clean_phone.length == 10
      raise "Invalid phone number format: #{phone}. Expected format: 0XXXXXXXXX"
    end

    clean_phone
  end

  # Map provider name to Paystack's expected code
  def map_provider_code(provider)
    case provider.to_s.downcase
    when 'mtn'
      'MTN'
    when 'vodafone'
      'VOD'
    when 'airteltigo', 'airtel', 'tigo'
      'ATL'
    else
      raise "Unsupported Mobile Money provider: #{provider}"
    end
  end
end
