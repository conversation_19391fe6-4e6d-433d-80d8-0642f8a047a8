class OrderPriceCalculator
  # Initialize with an order
  # @param order [Order] The order to calculate prices for
  def initialize(order)
    @order = order
  end

  # Calculate the original food price (without service fees)
  # @return [Float] The total original price of all food items
  def original_food_price
    calculate_total(:original_price)
  end

  # Calculate the food service fees
  # @return [Float] The total service fees for all food items
  def food_service_fees
    calculate_total(:service_fee)
  end

  # Calculate the original price for a specific vendor (without service fees)
  # @param vendor [Vendor] The vendor to calculate the price for
  # @return [Float] The total original price of all food items from this vendor
  def original_vendor_price(vendor)
    calculate_vendor_total(vendor, :original_price)
  end

  # Calculate service fees for a specific vendor
  # @param vendor [Vendor] The vendor to calculate service fees for
  # @return [Float] The total service fees for all food items from this vendor
  def vendor_service_fees(vendor)
    calculate_vendor_total(vendor, :service_fee)
  end

  # Get the original rider price (without service fee)
  # @return [Float] The original delivery price without service fee
  def original_rider_price
    return 0.0 unless @order.rider

    within_dunkwa? ? @order.rider.original_within_dunkwa_price : @order.rider.original_outside_dunkwa_price
  end

  # Get the rider service fee
  # @return [Float] The service fee for the rider
  def rider_service_fee
    return 0.0 unless @order.rider

    within_dunkwa? ? @order.rider.within_dunkwa_service_fee : @order.rider.outside_dunkwa_service_fee
  end

  private

  # Calculate total for a specific price method across all foods
  def calculate_total(price_method)
    return 0.0 if @order.foods.empty? || @order.quantities.empty?

    total = @order.foods.each_with_index.sum do |food, index|
      quantity = @order.quantities[index]
      next 0 unless quantity&.positive?

      # Get the price based on the selected price index
      price = get_food_price(food, index, price_method)
      next 0 unless price&.positive?

      price * quantity
    end

    total.round(2)
  end

  # Calculate vendor-specific total for a price method
  def calculate_vendor_total(vendor, price_method)
    vendor_foods = foods_by_vendor[vendor.id] || []

    total = vendor_foods.sum do |food|
      index = food_id_to_index[food.id.to_s]
      next 0 unless index && @order.quantities[index]&.positive?

      # Get the price based on the selected price index
      price = get_food_price(food, index, price_method)
      next 0 unless price&.positive?

      price * @order.quantities[index]
    end

    total.round(2)
  end

  # Get the appropriate price for a food item based on the selected price index
  def get_food_price(food, food_index, price_method)
    # If no price indexes are set, use the first price (backward compatibility)
    if @order.price_indexes.blank? || @order.price_indexes[food_index].nil?
      return food.send(price_method) if food.respond_to?(price_method)
      return 0.0
    end

    price_index = @order.price_indexes[food_index]

    case price_method
    when :original_price
      food.original_prices[price_index] || 0.0
    when :service_fee
      food.service_fees[price_index] || 0.0
    when :price
      food.prices_array[price_index] || 0.0
    else
      # Fallback to the old method for backward compatibility
      food.send(price_method) if food.respond_to?(price_method)
    end
  end

  # Memoized foods grouped by vendor to avoid N+1 queries
  def foods_by_vendor
    @foods_by_vendor ||= @order.foods.group_by(&:vendor_id)
  end

  # Memoized food ID to index mapping for efficient lookups
  def food_id_to_index
    @food_id_to_index ||= @order.food_ids.each_with_index.to_h
  end

  # Determine if delivery is within Dunkwa area
  def within_dunkwa?
    return false if @order.delivery_address.blank?

    address = @order.delivery_address.to_s.downcase.strip

    # Define exact Dunkwa areas - customize based on your business logic
    dunkwa_areas = [
      'dunkwa',
      'dunkwa,'
    ]

    dunkwa_areas.any? { |area| address.include?(area) }
  end
end
