class OrderTransferService
  # Initialize with an order
  # @param order [Order] The order to initiate transfers for
  def initialize(order)
    @order = order
    @calculator = OrderPriceCalculator.new(order)
  end

  # Initiate transfers to vendors and rider
  # @return [Array<Transfer>] The created transfers
  def initiate_transfers
    Rails.logger.info("Starting transfer initiation for order #{@order.id}")
    transfers = []

    # Check if transfers already exist for this order to prevent duplicates
    existing_transfers_count = @order.transfers.count
    if existing_transfers_count > 0
      Rails.logger.warn("Order #{@order.id} already has #{existing_transfers_count} transfers. Skipping transfer initiation to prevent duplicates.")
      return @order.transfers.to_a
    end

    # Calculate total required amount for balance check
    total_required_amount = calculate_total_required_amount
    Rails.logger.info("Total required amount for order #{@order.id}: #{total_required_amount} GHS")

    # Check balance before proceeding
    balance_check = check_balance_sufficiency(total_required_amount)
    raise StandardError, "Failed to check balance: #{balance_check['message']}" unless balance_check['status']

    unless balance_check['sufficient']
      raise StandardError, "Insufficient balance. Required: #{total_required_amount}
      GHS, Available: #{balance_check['current_balance']} GHS"
    end

    Rails.logger.info("Balance is sufficient. Proceeding with transfers for order #{@order.id}")

    ActiveRecord::Base.transaction do
      # Get unique vendor IDs from this order's foods to prevent duplicate transfers
      vendor_ids = @order.foods.pluck(:vendor_id).uniq
      Rails.logger.info("Processing transfers for vendors: #{vendor_ids} for order #{@order.id}")

      vendor_ids.each do |vendor_id|
        vendor = Vendor.find_by(id: vendor_id)
        if vendor
          Rails.logger.info("Processing vendor transfer for vendor #{vendor.id} (#{vendor.name})")
          transfer = create_vendor_transfer(vendor)
          transfers << transfer if transfer
        else
          Rails.logger.error("Vendor not found for ID: #{vendor_id} in order #{@order.id}")
        end
      end

      # Only create rider transfer if this order has delivery fee (first order in multi-vendor group)
      if (@order.delivery_price || 0) > 0 && @order.rider
        Rails.logger.info("Processing rider transfer for rider #{@order.rider.id} (#{@order.rider.name})")
        rider_transfer = create_rider_transfer
        transfers << rider_transfer if rider_transfer
      else
        Rails.logger.info("Skipping rider transfer for order #{@order.id} - delivery_price: #{@order.delivery_price}")
      end
    end

    Rails.logger.info("Completed transfer initiation for order #{@order.id}. Created #{transfers.count} transfers.")
    transfers
  end

  private

  # Calculate total required amount for all transfers
  def calculate_total_required_amount
    total_amount = 0.0

    # Add vendor amounts
    @order.vendors.each do |vendor|
      vendor_amount = @calculator.original_vendor_price(vendor)
      vendor_service_fee = @calculator.vendor_service_fees(vendor)
      total_amount += vendor_amount + vendor_service_fee
    end

    # Add rider amount
    rider_amount = @calculator.original_rider_price
    rider_service_fee = @calculator.rider_service_fee
    total_amount += rider_amount + rider_service_fee

    total_amount
  end

  # Check if balance is sufficient for the required amount
  def check_balance_sufficiency(required_amount)
    service = PaystackService.new
    service.balance_sufficient?(required_amount)
  end

  # Create a transfer for a vendor
  # @param vendor [Vendor] The vendor to create a transfer for
  # @return [Transfer, nil] The created transfer or nil if no transfer was created
  def create_vendor_transfer(vendor)
    # Check if transfer already exists for this vendor on this order
    existing_transfer = @order.transfers.find_by(
      recipient_type: 'Vendor',
      recipient_id: vendor.id
    )

    if existing_transfer
      Rails.logger.warn("Transfer already exists for vendor #{vendor.id} (#{vendor.name}) on order #{@order.id}. Transfer ID: #{existing_transfer.id}")
      return existing_transfer
    end

    if vendor.paystack_recipient_code.blank?
      Rails.logger.warn("Vendor #{vendor.id} (#{vendor.name}) has no paystack_recipient_code. Skipping transfer.")
      return nil
    end

    amount = @calculator.original_vendor_price(vendor)
    service_fee = @calculator.vendor_service_fees(vendor)

    Rails.logger.info("Vendor #{vendor.id} transfer - Amount: #{amount}, Service Fee: #{service_fee}")

    # Validate amount
    if amount <= 0
      Rails.logger.warn("Vendor #{vendor.id} transfer amount is #{amount} (<= 0). Skipping transfer.")
      return nil
    end

    create_transfer(vendor, amount, service_fee)
  end

  # Create a transfer for the rider
  # @return [Transfer, nil] The created transfer or nil if no transfer was created
  def create_rider_transfer
    # Check if transfer already exists for this rider on this order
    existing_transfer = @order.transfers.find_by(
      recipient_type: 'Rider',
      recipient_id: @order.rider.id
    )

    if existing_transfer
      Rails.logger.warn("Transfer already exists for rider #{@order.rider.id} (#{@order.rider.name}) on order #{@order.id}. Transfer ID: #{existing_transfer.id}")
      return existing_transfer
    end

    if @order.rider.paystack_recipient_code.blank?
      Rails.logger.warn("Rider #{@order.rider.id} (#{@order.rider.name})
      has no paystack_recipient_code. Skipping transfer.")
      return nil
    end

    amount = @calculator.original_rider_price
    service_fee = @calculator.rider_service_fee

    Rails.logger.info("Rider #{@order.rider.id} transfer - Amount: #{amount}, Service Fee: #{service_fee}")

    create_transfer(@order.rider, amount, service_fee)
  end

  # Create a transfer record and initiate the Paystack transfer
  # @param recipient [Vendor, Rider] The recipient of the transfer
  # @param amount [Float] The amount to transfer
  # @param service_fee [Float] The service fee
  # @return [Transfer] The created transfer
  def create_transfer(recipient, amount, service_fee)
    Rails.logger.info("Creating transfer for #{recipient.class.name} #{recipient.id}
    - Amount: #{amount}, Service Fee: #{service_fee}")

    transfer = @order.transfers.create!(
      recipient:,
      amount:,
      service_fee:,
      status: Transfer::PENDING
    )

    Rails.logger.info("Created transfer record #{transfer.id} with status: #{transfer.status}")

    begin
      # Initiate the transfer via Paystack
      Rails.logger.info("Initiating Paystack transfer for transfer #{transfer.id}")
      initiate_paystack_transfer(transfer)
    rescue StandardError => e
      transfer.update!(status: Transfer::FAILED)
      Rails.logger.error("Transfer creation failed for order #{@order.id}, transfer #{transfer.id}: #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))
    end

    transfer
  end

  # Initiate a transfer via Paystack
  # @param transfer [Transfer] The transfer to initiate
  def initiate_paystack_transfer(transfer)
    service = PaystackService.new
    recipient_code = transfer.recipient.paystack_recipient_code
    reason = "EaseFood payment for order ##{@order.id}"

    Rails.logger.info("Calling Paystack API for transfer #{transfer.id}
    - Amount: #{transfer.amount}, Recipient: #{recipient_code}")

    response = service.initialize_transfer(transfer.amount, recipient_code, reason)

    Rails.logger.info("Paystack API response for transfer #{transfer.id}: #{response.inspect}")

    if response['status']
      transfer.update!(
        status: Transfer::PROCESSING,
        paystack_transfer_code: response['data']['transfer_code'],
        paystack_reference: response['data']['reference']
      )
      Rails.logger.info("Successfully initiated Paystack transfer #{transfer.id}
      - Transfer Code: #{response['data']['transfer_code']}")
    else
      transfer.update!(status: Transfer::FAILED)
      Rails.logger.error("Failed to initiate transfer for order #{@order.id},
      recipient #{transfer.recipient.class.name} #{transfer.recipient.id}: #{response['message']}")
    end
  end
end
