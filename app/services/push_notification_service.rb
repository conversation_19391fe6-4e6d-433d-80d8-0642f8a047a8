class PushNotificationService
  # Initialize with VAPID details
  def initialize
    @vapid_public_key = Rails.application.credentials.vapid[:public_key]
    @vapid_private_key = Rails.application.credentials.vapid[:private_key]
    @vapid_subject = "mailto:#{Rails.application.credentials.vapid[:email]}"

    # Validate VAPID credentials
    validate_vapid_credentials

    # We'll use these VAPID details directly in the send_notification method
    @vapid_details = {
      subject: @vapid_subject,
      public_key: @vapid_public_key,
      private_key: @vapid_private_key
    }
  end

  # Send notification to a specific user with order context
  def send_to_user(user, title, body, data = {})
    return if user.nil?
    # Get all subscriptions for this user
    subscriptions = PushSubscription.where(user: user)
    Rails.logger.info("Found #{subscriptions.count} subscriptions for user")

    if subscriptions.empty?
      Rails.logger.warn("No push subscriptions found for #{user.class.name} ##{user.id}")
      return
    end

    # Calculate badge count for this user
    badge_count = NotificationService.calculate_badge_count(user)
    Rails.logger.info("Badge count: #{badge_count}")

    # Strategy: Send to ONLY ONE subscription per user to prevent spam
    # Choose the most recently created subscription (likely the most active device/browser)
    # This ensures user gets exactly ONE notification with order-specific data
    selected_subscription = subscriptions.order(created_at: :desc).first

    begin
      send_notification(selected_subscription, title, body, data, badge_count)
    rescue => e
      # Fallback: Try the second most recent subscription if the first fails
      fallback_subscriptions = subscriptions.where.not(id: selected_subscription.id).order(created_at: :desc).limit(2)

      if fallback_subscriptions.any?
        fallback_subscription = fallback_subscriptions.first

        begin
          send_notification(fallback_subscription, title, body, data, badge_count)
        rescue => fallback_error
        end
      end
    end
  end

  # Send notification to users of a specific type
  def send_to_user_type(user_type, title, body, data = {})
    # Get all subscriptions for this user type
    subscriptions = PushSubscription.where(user_type: user_type)

    # Send notification to each subscription
    subscriptions.each do |subscription|
      send_notification(subscription, title, body, data)
    end
  end

  # Send notification for an order to all relevant parties
  def send_order_notification(order, title, body, custom_data = {})
    # Prepare order data
    data = {
      order_id: order.id,
      status: order.status,
      created_at: order.created_at,
      url: '/customer-orders'
    }.merge(custom_data)

    # Send to customer
    send_to_user(order.customer, title, body, data) if order.customer

    # Send to vendor if order has foods
    if order.foods.present?
      # Get unique vendors from the order's foods
      vendor_ids = order.foods.pluck(:vendor_id).uniq

      # Send notification to each vendor
      vendor_ids.each do |vendor_id|
        vendor = Vendor.find_by(id: vendor_id)
        vendor_data = data.merge(url: '/vendors-dashboard')
        send_to_user(vendor, title, body, vendor_data) if vendor
      end
    end

    # Send to rider
    return unless order.rider

    rider_data = data.merge(url: '/riders-dashboard')
    send_to_user(order.rider, title, body, rider_data)
  end

  # Send notification to all vendors for an order
  def send_to_vendors(order, title, body, custom_data = {})
    Rails.logger.info("=== SENDING TO VENDORS ===")
    Rails.logger.info("Order ID: #{order.id}")
    Rails.logger.info("Order has foods: #{order.foods.present?}")

    return unless order.foods.present?

    # Prepare vendor-specific data
    data = {
      order_id: order.id,
      status: order.status,
      created_at: order.created_at,
      url: '/vendors-dashboard'
    }.merge(custom_data)

    # Get unique vendors from the order's foods
    vendor_ids = order.foods.pluck(:vendor_id).uniq
    Rails.logger.info("Found vendor IDs: #{vendor_ids}")

    # Send notification to each vendor
    vendor_ids.each do |vendor_id|
      vendor = Vendor.find_by(id: vendor_id)
      if vendor
        Rails.logger.info("Sending notification to vendor: #{vendor.name} (ID: #{vendor.id})")
        send_to_user(vendor, title, body, data)
      else
        Rails.logger.warn("Vendor not found for ID: #{vendor_id}")
      end
    end
  end

  private

  # Validate VAPID credentials
  def validate_vapid_credentials
    return unless @vapid_public_key.blank? || @vapid_private_key.blank? || @vapid_subject.blank?

    Rails.logger.error('VAPID credentials are missing. Push notifications will not work.')
    Rails.logger.error('Please ensure vapid[:public_key], vapid[:private_key], and vapid[:email] are set in credentials.yml.enc')
  end

  # Send notification to a specific subscription
  def send_notification(subscription, title, body, data, badge_count = nil)
    unless subscription.endpoint.present? && subscription.p256dh.present? && subscription.auth.present?
      Rails.logger.error("❌ Invalid subscription data for #{subscription.user_type} ##{subscription.user_id}")
      return
    end

    # Prepare notification payload
    payload = {
      title: title,
      body: body,
      data: data
    }
    payload[:badgeCount] = badge_count if badge_count
    payload = payload.to_json

    begin
      # Send the notification
      result = WebPush.payload_send(
        message: payload,
        endpoint: subscription.endpoint,
        p256dh: subscription.p256dh,
        auth: subscription.auth,
        vapid: @vapid_details
      )

      # Log success
      Rails.logger.info("WebPush result: #{result.inspect}")
      puts "✅ Push notification sent successfully to #{subscription.user_type} ##{subscription.user_id}"

    rescue WebPush::InvalidSubscription => e
      # Subscription is no longer valid, remove it
      Rails.logger.error("❌ Invalid subscription: #{e.message}")
      Rails.logger.error("Destroying invalid subscription #{subscription.id}")
      subscription.destroy
      puts "❌ Invalid subscription destroyed: #{subscription.id}"

    rescue WebPush::ExpiredSubscription => e
      # Subscription has expired, remove it
      Rails.logger.error("❌ Expired subscription: #{e.message}")
      Rails.logger.error("Destroying expired subscription #{subscription.id}")
      subscription.destroy
      puts "❌ Expired subscription destroyed: #{subscription.id}"

    rescue WebPush::Unauthorized => e
      # Unauthorized (usually wrong VAPID keys), log and keep subscription
      Rails.logger.error("❌ Unauthorized push notification: #{e.message}")

    rescue StandardError => e
      # Log other errors
      Rails.logger.error("❌ Push notification error: #{e.class} - #{e.message}")
      Rails.logger.error("Backtrace: #{e.backtrace.first(3).join("\n")}")
      puts "❌ Push notification failed: #{e.message}"
    end
  end
end

# NotificationService for badge count logic
class NotificationService
  def self.calculate_badge_count(user)
    case user.role
    when 'vendor'
      # Find all orders that include foods belonging to this vendor and are confirmed
      vendor_food_ids = Food.where(vendor_id: user.id).pluck(:id).map(&:to_s)
      Order.where("food_ids && ARRAY[?]::text[]", vendor_food_ids)
           .where(status: 'confirmed')
           .count
    when 'rider'
      Order.where(rider_id: user.id, status: 'ready_for_pickup').count
    when 'customer'
      Order.where(customer_id: user.id, status: 'delivered').count
    else
      0
    end
  end
end
