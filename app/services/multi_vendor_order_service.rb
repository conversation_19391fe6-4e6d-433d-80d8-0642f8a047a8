class MultiVendorOrderService
  require 'httparty'

  attr_reader :customer, :food_items, :order_params, :errors

  def initialize(customer, food_items, order_params)
    @customer = customer
    @food_items = food_items # Array of { food_id: id, quantity: qty, price_index: index }
    @order_params = order_params
    @errors = []
  end

  # Main method to create orders
  def create_orders
    return { success: false, errors: ['No food items provided'] } if food_items.empty?

    # Group food items by vendor
    vendor_groups = group_food_items_by_vendor

    return { success: false, errors: ['Invalid food items'] } if vendor_groups.empty?

    # If only one vendor, create a single order
    if vendor_groups.size == 1
      create_single_order(vendor_groups.values.first)
    else
      create_multiple_orders(vendor_groups)
    end
  rescue StandardError => e
    Rails.logger.error("MultiVendorOrderService error: #{e.message}")
    Rails.logger.error(e.backtrace.join("\n"))
    { success: false, errors: [e.message] }
  end

  private

  # Group food items by their vendor
  def group_food_items_by_vendor
    vendor_groups = {}
    
    food_items.each do |item|
      food = Food.find_by(id: item[:food_id])
      next unless food

      vendor_id = food.vendor_id
      vendor_groups[vendor_id] ||= []
      vendor_groups[vendor_id] << {
        food_id: food.id.to_s,
        quantity: item[:quantity].to_i,
        price_index: item[:price_index].to_i,
        food: food
      }
    end

    vendor_groups
  end

  # Create a single order for one vendor
  def create_single_order(vendor_food_items)
    result = nil

    ActiveRecord::Base.transaction do
      food_ids = vendor_food_items.map { |item| item[:food_id] }
      quantities = vendor_food_items.map { |item| item[:quantity] }
      price_indexes = vendor_food_items.map { |item| item[:price_index] }

      # Calculate prices
      food_price = calculate_food_price_with_indexes(food_ids, quantities, price_indexes)
      delivery_price = order_params[:delivery_price].to_f
      donation = order_params[:donation].to_f
      total_price = food_price + delivery_price + donation

      # Generate payment reference
      payment_reference = generate_payment_reference

      # Create order group
      order_group = OrderGroup.create!(
        customer: customer,
        payment_reference: payment_reference,
        total_amount: total_price
      )

      # Create the order
      # For single vendor, the full delivery fee goes to this order
      # Donation is a system fee, so it goes to the order but not to vendor
      order = Order.new(
        customer: customer,
        rider_id: order_params[:rider_id],
        order_group: order_group,
        food_ids: food_ids,
        quantities: quantities,
        price_indexes: price_indexes,
        delivery_price: delivery_price,  # Full delivery fee for single vendor
        donation: donation,              # System fee
        total_price: total_price,
        status: Order::PENDING,
        delivery_address: order_params[:delivery_address]
      )

      # Handle recipient if provided
      if order_params[:recipient_attributes].present?
        recipient_attrs = order_params[:recipient_attributes]
        order.build_recipient(
          recipient_name: recipient_attrs[:recipient_name],
          recipient_phone: recipient_attrs[:recipient_phone],
          recipient_address: recipient_attrs[:recipient_address]
        )
      end

      order.save!

      # Create payment
      payment = create_payment_for_order(order)
      payment.update!(reference: payment_reference)

      # Initialize Paystack transaction
      paystack_response = initialize_paystack_transaction(payment)

      unless paystack_response['status']
        result = {
          success: false,
          errors: [paystack_response['message'] || 'Payment initialization failed'],
          message: 'Failed to initialize payment'
        }
        raise ActiveRecord::Rollback
      end

      result = {
        success: true,
        orders: [order],
        order_group: order_group,
        payment_data: {
          access_code: paystack_response['data']['access_code'],
          reference: paystack_response['data']['reference']
        },
        message: 'Single order created successfully'
      }
    end

    # Return the result (either success or failure)
    result || { success: false, errors: ['Transaction failed'], message: 'Order creation failed' }
  end

  # Create multiple orders for multiple vendors
  def create_multiple_orders(vendor_groups)
    result = nil

    ActiveRecord::Base.transaction do
      orders = []
      total_amount = 0

      # Delivery fee and donation are system fees, not split among vendors
      delivery_price = order_params[:delivery_price].to_f
      donation_amount = order_params[:donation].to_f

      # Generate payment reference for the group
      payment_reference = generate_payment_reference

      # Calculate total amount first
      vendor_groups.each do |vendor_id, vendor_food_items|
        food_ids = vendor_food_items.map { |item| item[:food_id] }
        quantities = vendor_food_items.map { |item| item[:quantity] }
        price_indexes = vendor_food_items.map { |item| item[:price_index] }
        food_price = calculate_food_price_with_indexes(food_ids, quantities, price_indexes)
        total_amount += food_price
      end

      # Add delivery fee and donation to total (paid once by customer)
      total_amount += delivery_price + donation_amount

      # Create order group
      order_group = OrderGroup.create!(
        customer: customer,
        payment_reference: payment_reference,
        total_amount: total_amount
      )

      # Create orders for each vendor
      vendor_groups.each_with_index do |(vendor_id, vendor_food_items), index|
        food_ids = vendor_food_items.map { |item| item[:food_id] }
        quantities = vendor_food_items.map { |item| item[:quantity] }
        price_indexes = vendor_food_items.map { |item| item[:price_index] }

        # Calculate prices for this vendor
        food_price = calculate_food_price_with_indexes(food_ids, quantities, price_indexes)

        # For multi-vendor orders:
        # - Only the first order gets the delivery fee (rider will be assigned to first order)
        # - Only the first order gets the donation (system fee)
        # - Other orders only have food price
        current_delivery_price = index == 0 ? delivery_price : 0
        current_donation = index == 0 ? donation_amount : 0
        order_total = food_price + current_delivery_price + current_donation

        # Create order for this vendor
        # All orders in a multi-vendor group share the same rider
        order = Order.new(
          customer: customer,
          rider_id: order_params[:rider_id],
          order_group: order_group,
          food_ids: food_ids,
          quantities: quantities,
          price_indexes: price_indexes,
          delivery_price: current_delivery_price,
          donation: current_donation,
          total_price: order_total,
          status: Order::PENDING,
          delivery_address: order_params[:delivery_address]
        )

        # Handle recipient if provided (same recipient for all orders)
        if order_params[:recipient_attributes].present?
          recipient_attrs = order_params[:recipient_attributes]
          order.build_recipient(
            recipient_name: recipient_attrs[:recipient_name],
            recipient_phone: recipient_attrs[:recipient_phone],
            recipient_address: recipient_attrs[:recipient_address]
          )
        end

        order.save!
        orders << order
      end

      # Create a single payment for all orders combined
      # We'll use the first order as the primary order for payment
      primary_order = orders.first
      payment = create_payment_for_order(primary_order, total_amount)
      payment.update!(reference: payment_reference)

      # Initialize Paystack transaction for the total amount
      paystack_response = initialize_paystack_transaction(payment)

      unless paystack_response['status']
        result = {
          success: false,
          errors: [paystack_response['message'] || 'Payment initialization failed'],
          message: 'Failed to initialize payment'
        }
        raise ActiveRecord::Rollback
      end

      result = {
        success: true,
        orders: orders,
        order_group: order_group,
        payment_data: {
          access_code: paystack_response['data']['access_code'],
          reference: paystack_response['data']['reference']
        },
        message: "#{orders.size} orders created for different vendors"
      }
    end

    # Return the result (either success or failure)
    result || { success: false, errors: ['Transaction failed'], message: 'Order creation failed' }
  end

  # Calculate food price for given food IDs and quantities (backward compatibility)
  def calculate_food_price(food_ids, quantities)
    total_price = 0
    food_ids.each_with_index do |food_id, index|
      food = Food.find(food_id)
      quantity = quantities[index]
      total_price += food.price * quantity
    end
    total_price
  end

  # Calculate food price for given food IDs, quantities, and price indexes
  def calculate_food_price_with_indexes(food_ids, quantities, price_indexes)
    total_price = 0
    food_ids.each_with_index do |food_id, index|
      food = Food.find(food_id)
      quantity = quantities[index]
      price_index = price_indexes[index] || 0

      # Get the specific price based on the price index
      selected_price = food.prices_array[price_index] || food.price
      total_price += selected_price * quantity
    end
    total_price
  end

  # Create payment record for an order
  def create_payment_for_order(order, custom_amount = nil)
    amount = custom_amount || order.total_price
    
    payment = order.build_payment(
      status: 'pending',
      amount: amount
    )
    payment.save!
    payment
  end

  # Initialize Paystack transaction
  def initialize_paystack_transaction(payment)
    url = 'https://api.paystack.co/transaction/initialize'
    headers = {
      'Authorization' => "Bearer #{Rails.application.credentials.paystack[:secret_key]}",
      'Content-Type' => 'application/json'
    }

    body = {
      email: customer.email,
      amount: (payment.amount * 100).to_i, # Convert to pesewas
      reference: payment.reference || generate_payment_reference
    }.to_json

    begin
      response = HTTParty.post(url, headers: headers, body: body, timeout: 30)
      JSON.parse(response.body)
    rescue HTTParty::Error, JSON::ParserError, Net::OpenTimeout => e
      Rails.logger.error("Paystack API error: #{e.message}")
      { 'status' => false, 'message' => 'Failed to connect to payment gateway.' }
    end
  end

  # Generate a unique payment reference
  def generate_payment_reference
    "EF_#{Time.current.to_i}_#{SecureRandom.hex(4)}"
  end
end
